
.env
.trae

# 编译的 key
prd_keys
secrets.yaml
encembedded
zencembed.go

quanter/backtest/output/*

*.exe
licenses.json

test_build
copier_server
balancer.yaml
balance_plans.json
balance_plans.json.*
wallet_subaccount_mappings.yaml
wallet_subaccount_mappings.yaml.*

*/dist/*
pgate/dist/*

.zig-cache
dist/*

.idea
*/output/*
conf.go
data.json
main
.vscode
.ipynb_checkpoints
__debug_bin*
main*.exe
main*.exe~
main_linux
main_osx
main_osx_as
main_osx_arm64
main_osx_amd64
genkeys_linux
genkeys_osx_arm64
genkeys_osx_amd64
genkeys_win.exe
conf.toml
apikeys.go
fabfile.py
*.pyc
go.sum
go.work.sum
bin
build
__debug_bin

releases
logs

*/contrib/*.yaml
fastur
wintur
.DS_Store
firebase.json
*.toml
encryptsalt.go
*.data.json
.cache
simu_result
*.arbi_storage
*.turtle_storage
*.grid_storage
*.order_storage
*.trader_storage
*.stack
proxy_storage.json
proxy.log
*.log

# goctp

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

.idea*/
log/
logs/
pkg*/
data*/
releases/


goctp/cmds/gateway/*.so
goctp/cmds/gateway/*.dylib
goctp/demo/*
goctp/ctp/tests/*.so
goctp/ctp/tests/*.dylib
*.maker_storage
*.maker_orders
*.position_snapshots
*.account_snapshots
*.funding_history
*storage.bak
*.snapshots
*.open_conditional_orders
*.finished_conditional_orders
*.open_conditional_orders.bak

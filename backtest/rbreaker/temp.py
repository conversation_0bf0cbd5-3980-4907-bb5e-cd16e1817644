
from operator import itemgetter


H = 50828.13
L = 48665.43
C = 50089.64
O = 49424.47

P = (H + L + C) /3 

sSetup = P + (H - L)
sEnter = 2*P - L
sBreak = L - 2*(H-P)

bSetup = P - (H - L)
bEnter = 2*P - H
bBreak = H + 2*(P-L)

priceMap = {"sSetup": sSetup,
"sEnter": sEnter,
"sBreak": sBreak,
"bSetup": bSetup,
"bEnter": bEnter,
"bBreak": bBreak,
"H": H,
"L": L,
"C": C,
# "O": O,
"P": P,
}

key_values = priceMap.items()

key_values = sorted(key_values, key=itemgetter(1), reverse=True)

for k, v in key_values:
    print(k, "\t\t%.2f"%v)
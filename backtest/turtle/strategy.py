import argparse
import os
import string
import random
from datetime import datetime, timedelta

import backtrader as bt

Buy = "buy"
Sell = "sell"

Long = "long"
Short = "short"

# OPEN_CLOSE_AT_THE_SAME_BAR = False
OPEN_CLOSE_AT_THE_SAME_BAR = True

def id_generator(prefix="", size=6, chars=string.ascii_uppercase + string.digits):
    return prefix + ''.join(random.choice(chars) for _ in range(size))


# 创建的订单
class Order(object):
    def __init__(self, **kwargs):
        self.is_close = kwargs.pop('is_close', False)
        self.side = kwargs.pop('side', "")
        self.price = kwargs.pop('price', 0.0)
        self.qty = kwargs.pop('qty', 0.0)
        self.time = kwargs.pop('time', None)
        self.current_position = kwargs.pop('current_position', 0.0)
        self.last_price = kwargs.pop('last_price', 0.0)
        self.position_value = kwargs.pop('position_value', 0.0)
        self.cash = kwargs.pop('cash', 0.0)
        self.worth = kwargs.pop('worth', 0.0)
        self.total_pnl = kwargs.pop('total_pnl', 0.0)
        self.total_pnl_ratio = kwargs.pop('total_pnl_ratio', 0.0)
        self.id = kwargs.pop('id', '')
        self.ref_id = kwargs.pop('ref_id', '')
        self.submit_time = kwargs.pop('submit_time', None)
        self.stop_loss = kwargs.pop('stop_loss', False)
        self.unit = kwargs.pop('unit', 0)
        self.margin_leverage = kwargs.pop('margin_leverage', 0)


# 已成交交易
class Trade(Order):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class VegasStrategy(bt.Strategy):
    params = (
        ("atr_period", 20),
        ("breakout_period", 40),
        ("secondary_breakout_period", 0),
        ("exit_period", 20),
        ("max_open_units", 4),
        ("open_size_percent", 0.01),
    )

    def log(self, txt, dt=None):
        dt = dt or self.datas[0].datetime.datetime()
        print('[%s] %s' % (dt.isoformat(), txt))

    def __init__(self):
        self.orders = []
        self.trades = []
        self.daily_pnls = []
        self.daily_positions = []
        self.init_cash = self.broker.getvalue()
        self.stop_loss_count = 0
        self.take_profit_count = 0
        self.max_drawdown = -1
        self.max_drawdown_time = None

        self.atr = bt.indicators.ATR(period=self.p.atr_period)
        self.bar_index = 0
        self.position_units = 0
        self.open_atr = 0 # 开仓时 ART
        self.open_price = 0 # 开仓时突破价
        self.open_size = 0 # 开仓时单位数量
        self.breakout_period = self.p.breakout_period

    def start(self):
        pass

    def record_order(self, order):
        side = Buy if order.isbuy() else Sell
        is_close = order.info.is_close == True
        total_pnl = self.broker.getvalue() - self.init_cash
        last_price = self.data.close[0]
        margin_leverage = 0

        if self.position.size != 0:
            # 有持仓，需要减去当前持仓盈亏
            total_pnl -= (last_price - self.position.price) * self.position.size

            if not is_close:
                exit_price = self.get_exit_price(Long if self.position.size > 0 else Short, self.position.price, self.position_units)
                margin = abs((exit_price - self.position.price) * self.position.size)
                margin_leverage = self.position.price * abs(self.position.size) / margin

        if order.status == order.Completed:
            self.trades.append(Trade(
                side=side,
                price=order.executed.price,
                qty=order.executed.size,
                time=self.data0.datetime.datetime(),
                is_close=is_close,
                stop_loss=order.info.stop_loss,
                current_position=self.position.size,
                total_pnl=total_pnl,
                total_pnl_ratio=total_pnl / self.init_cash,
                id=order.info.id,
                unit=order.info.unit,
                ref_id=order.info.ref_id or "",
                last_price=last_price,
                position_value=self.position.size * last_price,
                cash=self.broker.getcash(),
                worth=self.broker.getvalue(),
                margin_leverage=margin_leverage,
            ))

            self.orders.append(Order(
                side=side,
                price=order.price,
                qty=order.size,
                time=self.data0.datetime.datetime(),
                submit_time=order.info.submit_time,
                is_close=is_close,
                stop_loss=order.info.stop_loss,
                current_position=self.position.size,
                total_pnl=total_pnl,
                total_pnl_ratio=total_pnl / self.init_cash,
                id=order.info.id,
                unit=order.info.unit,
                ref_id=order.info.ref_id or "",
                last_price=last_price,
                position_value=self.position.size * last_price,
                cash=self.broker.getcash(),
                worth=self.broker.getvalue(),
                margin_leverage=margin_leverage,
            ))

    def notify_order(self, order):
        if order.status in [order.Completed]:
            if order.info.is_close == True:
                self.log("close order[{}] traded: {:.4f}@{:.2f}".format(order.info.id, order.executed.size, order.executed.price))
            else:
                self.log("open order[{}] traded: {:.4f}@{:.2f}".format(order.info.id, order.executed.size, order.executed.price))
            self.record_order(order)

        elif order.status in [order.Margin, order.Rejected]:
            self.log('{} Order Margin({})/Rejected({}) status: {}'.format(
                     "buy" if order.isbuy() else "sell",
                     order.Margin, order.Rejected,
                     order.status,
                     ))
            exit()

    def notify_trade(self, trade):
        if not trade.isclosed:
            return

        value = self.broker.getvalue()
        pct = trade.pnlcomm / (value - trade.pnlcomm) * 100
        self.log('OPERATION PROFIT, GROSS %.4f(%.2f%%)' % (trade.pnlcomm, pct))

    def next(self):
        dt = self.datas[0].datetime.datetime()

        # if dt.year == 2022 and dt.month == 12 and dt.day == 13:
        #     print("debug")

        if dt.hour == 0 and dt.minute == 0:
            self.daily_pnls.append((
                dt.date(),
                self.broker.getvalue() - self.init_cash
            ))

        if self.stats.drawdown.maxdrawdown[-1] > self.max_drawdown:
            self.max_drawdown = self.stats.drawdown.maxdrawdown[-1]
            self.max_drawdown_time = dt

        self.bar_index += 1
        if self.bar_index < max(self.p.atr_period, self.p.breakout_period, self.p.secondary_breakout_period):
            return

        try:
            if not self.position or self.position.size == 0:
                self.open_handle()
            else:
                self.close_handle()
        except IndexError as e:
            self.log("handle index error: {}".format(e))


    def get_dc(self, period):
        price_high = self.data.high[0]
        price_low = self.data.low[0]
        for i in range(period):
            price_high = max(price_high, self.data.high[-i])
            price_low = min(price_low, self.data.low[-i])
        return price_high, price_low

    def open_handle(self, after_close=False, close_side=Long):
        self.open_order_id = id_generator()
        self.open_atr = self.atr[0]
        price_high, price_low = self.get_dc(self.breakout_period)
        # self.log("open dc[{}]: {}, {}".format(self.breakout_period, price_high, price_low))
        # 如果下一根 K 线突破则挂单
        open_long = False
        open_short = False
        open_size = 0
        if self.data.high[1] >= price_high:
            self.open_price = price_high
            open_long = True
            open_value = price_high * self.broker.getvalue() * self.p.open_size_percent / self.atr[0]
            open_size = open_value / price_high
        elif self.data.low[1] <= price_low:
            self.open_price = price_low
            open_short = True
            open_value = price_low * self.broker.getvalue() * self.p.open_size_percent / self.atr[0]
            open_size = open_value / price_low
        else:
            return

        if after_close:
            # 平仓后立即开仓只做反方向，更接近真实情况
            if close_side == Long and open_long:
                return
            if close_side == Short and open_short:
                return

        open_size = round(open_size, 4)
        self.open_size = open_size
        open_value = 0
        for i in range(self.p.max_open_units):
            if open_long:
                price = price_high + i*0.5*self.atr[0]
                if self.data.high[1] >= price:
                    self.log("will break out long[{}] @{}, atr: {}".format(i, price, self.atr[0]))
                    open_order = self.buy(exectype=bt.Order.StopLimit, size=open_size, price=price, plimit=price)
                    open_order.addinfo(submit_time=self.datas[0].datetime.datetime())
                    open_order.addinfo(id="Long_{}_{}".format(i, self.open_order_id))
                    open_order.addinfo(unit=i)
                    self.position_units += 1
                    open_value += price * open_size
            elif open_short:
                price = price_low - i*0.5*self.atr[0]
                if self.data.low[1] <= price:
                    self.log("will break out short[{}] @{}, atr: {}".format(i, price, self.atr[0]))
                    open_order = self.sell(exectype=bt.Order.StopLimit, size=open_size, price=price, plimit=price)
                    open_order.addinfo(submit_time=self.datas[0].datetime.datetime())
                    open_order.addinfo(id="Short_{}_{}".format(i, self.open_order_id))
                    open_order.addinfo(unit=i)
                    self.position_units += 1
                    open_value += price * open_size

        if after_close:
            return

        if not OPEN_CLOSE_AT_THE_SAME_BAR:
            return

        # 开仓即止损的情况
        position_size = self.position_units * open_size
        open_price = open_value / position_size
        close_order = None
        # 因为 K 线内走势是不确定的，使用收盘价作为判断是否止损
        if open_long:
            exit_price = self.get_exit_price(Long, open_price, self.position_units)
            if self.data.close[1] <= exit_price:
                close_order = self.sell(exectype=bt.Order.StopLimit, size=position_size, price=exit_price, plimit=exit_price)
        else:
            exit_price = self.get_exit_price(Short, open_price, self.position_units)
            if self.data.close[1] >= exit_price:
                close_order = self.buy(exectype=bt.Order.StopLimit, size=position_size, price=exit_price, plimit=exit_price)
        if close_order is not None:
            self.position_units = 0

            self.log("will stop loss in the same bar @{}".format(exit_price))
            close_order.addinfo(submit_time=self.datas[0].datetime.datetime())
            close_order.addinfo(is_close=True)
            close_order.addinfo(id="StopLoss_{}".format(self.open_order_id))
            close_order.addinfo(stop_loss=True)
            self.stop_loss_count += 1

    def get_exit_price(self, position_side, position_price, position_units):
        price_high, price_low = self.get_dc(self.p.exit_period)
        if position_side == Long:
            dc_exit_price = price_low
            # 一个头寸时止损价=持仓价-2ATR，2个时=持仓价-1.75ATR，3个时=持仓价-1.5ATR，4个时=持仓价-1.25ATR
            atr_exit_price = position_price - self.open_atr*1.25 - (4-position_units)*0.25*self.open_atr
            exit_price = max(dc_exit_price, atr_exit_price)
        else:
            dc_exit_price = price_high
            atr_exit_price = position_price + self.open_atr*1.25 + (4-position_units)*0.25*self.open_atr
            exit_price = min(dc_exit_price, atr_exit_price)
        return exit_price

    def close_handle(self):
        close_order = None
        if self.position.size > 0:
            exit_price = self.get_exit_price(Long, self.position.price, self.position_units)
            if self.data.low[1] <= exit_price:
                if self.data.high[1] <= exit_price:
                    exit_price = self.data.open[1] # 说明当前 K 线已触发退出了，改用开盘价
                close_order = self.sell(exectype=bt.Order.StopLimit, size=self.position.size, price=exit_price, plimit=exit_price)
        else:
            exit_price = self.get_exit_price(Short, self.position.price, self.position_units)
            if self.data.high[1] >= exit_price:
                if self.data.low[1] >= exit_price:
                    exit_price = self.data.open[1] # 说明当前 K 线已触发退出了，改用开盘价
                close_order = self.buy(exectype=bt.Order.StopLimit, size=abs(self.position.size), price=exit_price, plimit=exit_price)

        if close_order is not None:
            self.position_units = 0

            close_order.addinfo(submit_time=self.datas[0].datetime.datetime())
            close_order.addinfo(is_close=True)

            is_take_profit = False
            if self.position.size > 0 and self.position.price < exit_price:
                is_take_profit = True
            elif self.position.size < 0 and self.position.price > exit_price:
                is_take_profit = True

            if is_take_profit:
                close_order.addinfo(id="TakeProfit_{}".format(self.open_order_id))
                self.take_profit_count += 1
                self.log("will take profit @{}".format(exit_price))

                # 如果是止盈，下次突破使用 secondary_breakout_period
                if self.p.secondary_breakout_period != 0:
                    self.breakout_period = self.p.secondary_breakout_period
            else:
                close_order.addinfo(id="StopLoss_{}".format(self.open_order_id))
                close_order.addinfo(stop_loss=True)
                self.stop_loss_count += 1
                self.breakout_period = self.p.breakout_period
                self.log("will stop loss @{}".format(exit_price))

            if OPEN_CLOSE_AT_THE_SAME_BAR:
                self.open_handle(True, Long if self.position.size > 0 else Short) # 如果平仓后会反向突破，也要提前挂单

            return

        # 如果没有触发平仓，则判断是否加仓
        for i in range(self.p.max_open_units):
            if i < self.position_units:
                continue

            if self.position.size > 0:
                price = self.open_price + i*0.5*self.open_atr
                if self.data.high[1] >= price:
                    self.log("will break out long[{}] @{}, atr: {}".format(i, price, self.open_atr))
                    open_order = self.buy(exectype=bt.Order.StopLimit, size=self.open_size, price=price, plimit=price)
                    open_order.addinfo(submit_time=self.datas[0].datetime.datetime())
                    open_order.addinfo(id="Long_{}_{}".format(i, self.open_order_id))
                    open_order.addinfo(unit=i)
                    self.position_units += 1
            else:
                price = self.open_price - i*0.5*self.open_atr
                if self.data.low[1] <= price:
                    self.log("will break out short[{}] @{}, atr: {}".format(i, price, self.open_atr))
                    open_order = self.sell(exectype=bt.Order.StopLimit, size=self.open_size, price=price, plimit=price)
                    open_order.addinfo(submit_time=self.datas[0].datetime.datetime())
                    open_order.addinfo(id="Short_{}_{}".format(i, self.open_order_id))
                    open_order.addinfo(unit=i)
                    self.position_units += 1


def save_trades(file_name, trades, symbol, init_cash):
    f = open(file_name + ".csv", 'w+')
    f.write(
        "Time,Symbol,Side,Price,Qty,ID,CurrentPosition,LastPrice,PositionValue,MarginLeverage,TradePNL,PNLRatio,Cash,Worth,TotalPnl,TotalPnlRatio\n")
    last_total_pnl = 0
    for trade in trades:
        trade_pnl = 0
        pnl_ratio = 0
        if trade.is_close:
            trade_pnl = trade.total_pnl - last_total_pnl
            pnl_ratio = trade_pnl / (init_cash + last_total_pnl)
            last_total_pnl = trade.total_pnl

        f.write("{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{}\n".format(
            trade.time.strftime('%Y-%m-%d %H:%M:%S'),
            symbol,
            trade.side,
            "{:.5f}".format(trade.price),
            trade.qty,
            trade.id,
            "{:.2f}".format(trade.current_position),
            "{:.5f}".format(trade.last_price),
            "{:.2f}".format(trade.position_value),
            "{:.2f}".format(trade.margin_leverage),
            "{:.2f}".format(trade_pnl),
            "{:.4f}".format(pnl_ratio),
            "{:.2f}".format(trade.cash),
            "{:.2f}".format(trade.worth),
            "{:.2f}".format(trade.total_pnl),
            "{:.6f}".format(trade.total_pnl_ratio),
        ))
    f.close()


def save_pines(output_dir, file_name, orders, cash, fee_rate):
    f = open(output_dir + "/" + file_name + ".pine", 'w+')
    f.write(f'//@version=4\nstrategy("{file_name}", default_qty_type=strategy.cash, initial_capital={cash}, commission_type=strategy.commission.percent, commission_value={fee_rate*100}, calc_on_order_fills=true, close_entries_rule="ANY")\n')

    open_time_afrer_close = None
    close_time = None
    closed = False
    last_open_time = None
    for order in orders:
        time = order.submit_time

        long_short = "long"
        if order.side == Sell:
            long_short = "short"
        # 注意 pine 脚本严格要求格式和对齐，不要随意加空白字符
        # 成交后下一根 K 线的挂单，要加上 position_size 判断条件，否则可能触发两次
        if order.is_close:
            open_time_afrer_close = None
            close_time = time
            closed = True
            # 用 strategy.exit 时如果同一 K 线同时有开仓会无视数量也平仓，改用 strategy.order, 但开平仓数量需要精确
            if order.stop_loss:
                if last_open_time != time:
                    script = f"""\nif year == {time.year} and month == {time.month} and dayofmonth == {time.day} and hour == {time.hour}\n    strategy.order("{order.id}", strategy.{long_short}, qty={abs(order.qty):.4f}, stop={order.price:.5f}, when=strategy.position_size != 0)\n"""
                else:
                    # 同一 K 线止损用 limit
                    script = f"""\nif year == {time.year} and month == {time.month} and dayofmonth == {time.day} and hour == {time.hour}\n    strategy.order("{order.id}", strategy.{long_short}, qty={abs(order.qty):.4f}, limit={order.price:.5f})\n"""
            else:
                script = f"""\nif year == {time.year} and month == {time.month} and dayofmonth == {time.day} and hour == {time.hour}\n    strategy.order("{order.id}", strategy.{long_short}, qty={abs(order.qty):.4f}, stop={order.price:.5f}, when=strategy.position_size != 0)\n"""
        else:
            # strategy.entry 如果是 stop 类型，成交之后再挂同类型订单则无法触发，用 strategy.order 没这个问题
            if open_time_afrer_close is None and close_time != time and closed:
                open_time_afrer_close = time
            if open_time_afrer_close == time:
                script = f"""\nif year == {time.year} and month == {time.month} and dayofmonth == {time.day} and hour == {time.hour}\n    strategy.order("{order.id}", strategy.{long_short}, qty={abs(order.qty):.4f}, stop={order.price:.5f}, when=strategy.position_size == 0)\n"""
            else:
                script = f"""\nif year == {time.year} and month == {time.month} and dayofmonth == {time.day} and hour == {time.hour}\n    strategy.order("{order.id}", strategy.{long_short}, qty={abs(order.qty):.4f}, stop={order.price:.5f})\n"""
            closed = False
            last_open_time = time

        f.write(script)

    f.write('\nplot(strategy.equity)')
    f.close()


def run_strategy(atr_period: int, breakout_period: int, secondary_breakout_period: int, exit_period: int, max_open_units: int, open_size_percent: float, datapath: str, cash: float, fee_rate: float,  from_date: datetime, to_date: datetime, output_dir: str):
    cerebro = bt.Cerebro()

    # Add a strategy
    cerebro.addstrategy(
        VegasStrategy,
        atr_period=atr_period,
        breakout_period=breakout_period,
        secondary_breakout_period=secondary_breakout_period,
        exit_period=exit_period,
        max_open_units=max_open_units,
        open_size_percent=open_size_percent,
    )

    # Create a Data Feed
    data = bt.feeds.GenericCSVData(
        dataname=datapath,
        fromdate=from_date,
        todate=to_date,
        nullvalue=0.0,
        timeframe=bt.TimeFrame.Minutes,
        dtformat=('%Y-%m-%d %H:%M:%S'),
        datetime=0,
        open=1,
        high=2,
        low=3,
        close=4,
        volume=5,
        openinterest=-1
    )
    data_name_strs = data._name.split('_')
    source = data_name_strs[0]
    symbol = data_name_strs[1]
    period = data_name_strs[2]

    # Add the Data Feed to Cerebro
    cerebro.adddata(data)

    cerebro.addobserver(bt.observers.DrawDown)
    cerebro.addanalyzer(bt.analyzers.TimeDrawDown)
    cerebro.addanalyzer(bt.analyzers.TimeReturn, timeframe=bt.TimeFrame.Years)

    # Set our desired cash start
    cerebro.broker.setcash(cash)

    # Set the commission - 0.1% ... divide by 100 to remove the %
    cerebro.broker.setcommission(commission=fee_rate, leverage=100)

    # Run over everything
    results = cerebro.run()
    strat0 = results[0]
    tret_analyzer = strat0.analyzers.getbyname('timereturn')
    timereturns = tret_analyzer.get_analysis()
    timedrawdowns = strat0.analyzers.getbyname('timedrawdown').get_analysis()
    maxdrawdownperiod = timedrawdowns.get("maxdrawdownperiod")

    if period.find("h") != -1:
        strat0.maxdrawdown_start_time = strat0.max_drawdown_time - timedelta(hours=maxdrawdownperiod)
    elif period.find("m") != -1:
        strat0.maxdrawdown_start_time = strat0.max_drawdown_time - timedelta(minutes=maxdrawdownperiod)

    file_name = "{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}-{}".format(
        source, symbol, period, cash,
        atr_period, breakout_period, secondary_breakout_period, exit_period, max_open_units, open_size_percent,
        from_date.strftime("%Y-%m-%d"), to_date.strftime("%Y-%m-%d"))

    print("\n*** %s ***" % file_name)
    print('Starting Portfolio Value: %.2f' % cash)
    print('Final Portfolio Value: %.2f' % cerebro.broker.getvalue())
    print("TimeReturns:")
    for d in timereturns:
        print("{} {:.2f}%".format(d, timereturns[d] * 100))
    print('Total return: %.2f%%' %
          ((cerebro.broker.getvalue() - cash) * 100 / cash))
    print('MaxDrawDown: %.2f%%' % strat0.stats.drawdown.maxdrawdown[0])
    print('MaxDrawDown time: %s ~ %s' % (strat0.maxdrawdown_start_time, strat0.max_drawdown_time))

    if strat0.take_profit_count > 0 or strat0.stop_loss_count > 0:
        print('Win/Loss: %d/%d, win ratio: %.2f%%' % (strat0.take_profit_count, strat0.stop_loss_count, strat0.take_profit_count / (strat0.take_profit_count + strat0.stop_loss_count) * 100))

    trades_stat(from_date, to_date, strat0.trades, True)
    pnl_stat(strat0.daily_pnls, True)

    output_dir = output_dir + "/turtle"
    if not os.path.isdir(output_dir):
        os.mkdir(output_dir)

    save_trades(output_dir + "/" + file_name, strat0.trades, symbol, cash)
    save_pines(output_dir, file_name, strat0.orders, cash, fee_rate)

    return cerebro, strat0


def pnl_stat(pnls, print_out=False):
    max_pnl = None
    max_pnl_date = None
    min_pnl = None
    min_pnl_date = None
    positive_pnl_days = 0
    nagative_pnl_days = 0
    for (date, pnl) in pnls:
        if max_pnl is None:
            max_pnl = pnl
            max_pnl_date = date
        if min_pnl is None:
            min_pnl = pnl
            min_pnl_date = date

        if pnl > 0:
            positive_pnl_days += 1
        else:
            nagative_pnl_days += 1

        if pnl > max_pnl:
            max_pnl = pnl
            max_pnl_date = date

        if pnl < min_pnl:
            min_pnl = pnl
            min_pnl_date = date

    if len(pnls) == 0:
        return

    if print_out:
        print("\n*** PNL Stat ***")
        print("max pnl: {:.2f} @ {}".format(max_pnl, max_pnl_date))
        print("min pnl: {:.2f} @ {}".format(min_pnl, min_pnl_date))
        print("positive pnl days: {}".format(positive_pnl_days))
        print("nagative pnl days: {}".format(nagative_pnl_days))
    return max_pnl, min_pnl, positive_pnl_days, nagative_pnl_days


def trades_stat(from_date, to_date, trades, print_out=False):
    if len(trades) == 0:
        return

    duration = to_date - from_date
    avg_trade_time = duration / len(trades)
    max_no_trade_time = trades[0].time - from_date
    last_trade_time = trades[0].time
    for trade in trades[1:]:
        max_no_trade_time = max(
            max_no_trade_time, trade.time - last_trade_time)
        last_trade_time = trade.time

    max_no_trade_time = max(
        max_no_trade_time, to_date - last_trade_time)

    if print_out:
        print("\n*** Trade Stat ***")
        print("{} ~ {}, {} days".format(
            from_date.date(), to_date.date(), duration.days))
        print("trade count: ", len(trades))
        print("avg_trade_time: ", avg_trade_time)
        print("max_no_trade_time: ", max_no_trade_time)

    return avg_trade_time, max_no_trade_time


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    parser.add_argument('--datapath', required=True, help="csv data path")

    parser.add_argument('--from_date', required=True,
                        type=lambda s: datetime.strptime(s, '%Y-%m-%d'), help="from date")

    parser.add_argument('--to_date', required=True,
                        type=lambda s: datetime.strptime(s, '%Y-%m-%d'), help="to date")

    parser.add_argument('--cash', default=100000, required=False,
                        type=int, help="init cash")

    parser.add_argument('--fee_rate', default=0.0, required=False, type=float)

    parser.add_argument('--atr_period', default=20, required=False, type=int)
    parser.add_argument('--breakout_period', required=True, type=int)
    parser.add_argument('--secondary_breakout_period', default=0, required=False, type=int)
    parser.add_argument('--exit_period', required=True, type=int)
    parser.add_argument('--max_open_units', default=4, required=False, type=int)
    parser.add_argument('--open_size_percent', default=0.01, required=False, type=float)

    parser.add_argument('--plot', default=False, required=False, type=bool, help='show plot')

    parser.add_argument('--output_dir', default='.')

    return parser.parse_args()


if __name__ == '__main__':
    args = parse_args()

    cerebro, strat = run_strategy(args.atr_period, args.breakout_period, args.secondary_breakout_period,
                                  args.exit_period, args.max_open_units, args.open_size_percent,
                                  args.datapath, args.cash, args.fee_rate, args.from_date, args.to_date, args.output_dir)

    if args.plot:
        cerebro.plot()

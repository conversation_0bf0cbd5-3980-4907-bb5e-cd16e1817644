
import os

from datetime import datetime, timedelta
from strategy import run_strategy, trades_stat, pnl_stat


class BackTestCase(object):

    def __init__(self, **kwargs):
        self.symbol = kwargs.pop('symbol')
        self.period = kwargs.pop('period')

        self.atr_period = kwargs.pop('atr_period')
        self.breakout_period = kwargs.pop('breakout_period')
        self.secondary_breakout_period = kwargs.pop('secondary_breakout_period')
        self.exit_period = kwargs.pop('exit_period')
        self.max_open_units = kwargs.pop('max_open_units')
        self.open_size_percent = kwargs.pop('open_size_percent')

        self.from_date = kwargs.pop('from_date')
        self.to_date = kwargs.pop('to_date')
        self.data_file = kwargs.pop('data_file')

    def set_results(self, **kwargs):
        self.days = kwargs.pop('days')
        self.trade_count = kwargs.pop('trade_count')
        self.return_rate = kwargs.pop('return_rate')
        self.annualized_rate = kwargs.pop('annualized_rate', 0)
        self.avg_trade_time = kwargs.pop('avg_trade_time')
        self.max_no_trade_time = kwargs.pop('max_no_trade_time')
        self.max_loss_rate = kwargs.pop('max_loss_rate')
        self.max_drawdown = kwargs.pop('max_drawdown')
        self.max_drawdown_start_time = kwargs.pop('max_drawdown_start_time')
        self.max_drawdown_end_time = kwargs.pop('max_drawdown_end_time')
        self.positive_pnl_days = kwargs.pop('positive_pnl_days')
        self.nagative_pnl_days = kwargs.pop('nagative_pnl_days')
        self.stop_loss_count = kwargs.pop('stop_loss_count')
        self.take_profit_count = kwargs.pop('take_profit_count')

if __name__ == "__main__":
    cash = 10000
    fee_rate = 0.0006

    data_files = [
        "binance_BTCUSDT_1h_2017-10-01-2022-12-10.csv",
        "binance_BTCUSDT_4h_2017-10-01-2022-12-10.csv",
        "binance_BTCUSDT_12h_2017-10-01-2023-01-10.csv",
    ]

    # parser.add_argument('--secondary_breakout_period', default=0, required=False, type=int)
    # parser.add_argument('--exit_period', required=True, type=int)
    # parser.add_argument('--max_open_units', default=4, required=False, type=int)
    # parser.add_argument('--open_size_percent', default=0.01, required=False, type=float)

    atr_periods = [20]
    breakout_periods = [20, 40, 60]
    use_secondary_breakout_periods = [False, True]
    max_open_units = 4
    open_size_percent = 0.01
    from_date = "2020-01-01"
    to_date = "2022-12-09"

    tests = []
    for data_file in data_files:
        for atr_period in atr_periods:
            for breakout_period in breakout_periods:
                for use_secondary_breakout_period in use_secondary_breakout_periods:
                    if use_secondary_breakout_period:
                        secondary_breakout_period = breakout_period * 2
                    else:
                        secondary_breakout_period = breakout_period
                    symbol = data_file.split("_")[1]
                    period = data_file.split("_")[2]
                    tests.append(BackTestCase(
                        symbol=symbol,
                        period=period,
                        atr_period=atr_period,
                        breakout_period=breakout_period,
                        secondary_breakout_period=secondary_breakout_period,
                        exit_period=int(breakout_period/2),
                        max_open_units=max_open_units,
                        open_size_percent=open_size_percent,
                        from_date=from_date,
                        to_date=to_date,
                        data_file=data_file,
                    ))

    output_dir = "../output"
    results = []
    for case in tests:
        datapath = os.path.join("../data", case.data_file)
        from_date_time = datetime.strptime(case.from_date, '%Y-%m-%d')
        to_date_time = datetime.strptime(case.to_date, '%Y-%m-%d')

        cerebro, strat = run_strategy(case.atr_period, case.breakout_period, case.secondary_breakout_period,
                                      case.exit_period, case.max_open_units, case.open_size_percent,
                                      datapath, cash, fee_rate, from_date_time, to_date_time, output_dir)

        days = (to_date_time - from_date_time).days
        return_rate = (strat.broker.getvalue() - cash) / cash
        annualized_rate = return_rate / (days / 365)
        avg_trade_time, max_no_trade_time = trades_stat(from_date_time, to_date_time, strat.trades, False)

        # 填充缺失的日期
        time = from_date_time
        pnls = []
        last_pnl = 0
        i = 0
        while time <= to_date_time:
            date, current_pnl = strat.daily_pnls[i]
            if date == time.date():
                last_pnl = current_pnl
                i += 1
            pnls.append((time.date(), last_pnl))
            time += timedelta(hours=24)

        max_pnl, min_pnl, positive_pnl_days, nagative_pnl_days = pnl_stat(pnls, "orders")

        case.set_results(
            days=days,
            trade_count=len(strat.trades),
            return_rate=return_rate,
            annualized_rate=annualized_rate,
            avg_trade_time=avg_trade_time,
            max_no_trade_time=max_no_trade_time,
            max_loss_rate=min_pnl/cash,
            positive_pnl_days=positive_pnl_days,
            nagative_pnl_days=nagative_pnl_days,
            max_drawdown=strat.stats.drawdown.maxdrawdown[0],
            max_drawdown_start_time=strat.maxdrawdown_start_time,
            max_drawdown_end_time=strat.max_drawdown_time,
            stop_loss_count=strat.stop_loss_count,
            take_profit_count=strat.take_profit_count,
        )
        results.append(case)

    f = open(output_dir + "/turtle/turtle_results.csv", 'w+')
    f.write(
        "品种,K线,ATR,突破周期,盈利后突破周期,退出周期,单位头寸数量,开仓比例,时间,天数,成交次数,胜率,总收益率,净年平均收益率,平均成交时间,最长无成交时间,最大损失,最大回撤,最大回撤时间,正收益天数,负收益天数\n")
    for r in results:
        f.write("{},{},{},{},{},{},{},{},{},{},{},{:.2f}%,{:.2f}%,{:.2f}%,{},{},{:.2f}%,{:.2f}%,{},{},{}\n".format(
            r.symbol,
            r.period,
            r.atr_period,
            r.breakout_period,
            r.secondary_breakout_period,
            r.exit_period,
            r.max_open_units,
            r.open_size_percent,
            "{}~{}".format(r.from_date, r.to_date),
            r.days,
            r.trade_count,
            r.take_profit_count / (r.take_profit_count + r.stop_loss_count) * 100,
            r.return_rate * 100,
            r.annualized_rate * 100,
            "{}".format(r.avg_trade_time).replace(",", ""),
            "{}".format(r.max_no_trade_time).replace(",", ""),
            r.max_loss_rate * 100,
            r.max_drawdown,
            "{}~{}".format(r.max_drawdown_start_time.strftime("%Y-%m-%d"), r.max_drawdown_end_time.strftime("%Y-%m-%d")),
            r.positive_pnl_days,
            r.nagative_pnl_days,
        ))
    f.close()

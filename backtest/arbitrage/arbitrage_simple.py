from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import argparse
import datetime  # For datetime objects
import os.path  # To manage paths
import sys  # To find out the script name (in argv[0])

# Import the backtrader platform
import backtrader as bt


class GenericCSV_PE(bt.feeds.GenericCSVData):
    lines = ('symbol',)
    params = (('symbol', 6),)


class SimpleArbiStrategy(bt.Strategy):
    params = (
        ('minopenprice', 1.01),
        ('triggercloseprice', 1),
    )

    def log(self, txt, dt=None):
        ''' Logging function fot this strategy'''
        dt = dt or self.datas[0].datetime.datetime()
        symbol = "{:.0f}".format(self.data.symbol[0])
        print('[%s] %s, %s' % (symbol, dt.isoformat(), txt))

    def __init__(self):
        # Keep a reference to the "close" line in the data[0] dataseries
        self.dataclose = self.datas[0].close

        # To keep track of pending orders and buy price/commission
        self.order = None
        self.buyprice = None
        self.buycomm = None

    def notify_order(self, order):
        if order.status in [order.Submitted, order.Accepted]:
            # Buy/Sell order submitted/accepted to/by broker - Nothing to do
            return

        # Check if an order has been completed
        # Attention: broker could reject order if not enough cash
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(
                    'BUY EXECUTED, Price: %.4f, Cost: %.4f, Comm %.4f' %
                    (order.executed.price,
                     order.executed.value,
                     order.executed.comm))

                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm
            else:  # Sell
                self.log('SELL EXECUTED, Price: %.4f, Cost: %.4f, Comm %.4f' %
                         (order.executed.price,
                          order.executed.value,
                          order.executed.comm))

            self.bar_executed = len(self)

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('Order Canceled/Margin/Rejected')

        self.order = None

    def notify_trade(self, trade):
        if not trade.isclosed:
            return

        self.log('OPERATION PROFIT, GROSS %.4f, NET %.4f' %
                 (trade.pnl, trade.pnlcomm))

    def next(self):
        # Simply log the closing price of the series from the reference
        # self.log('Close: %.4f' % self.dataclose[0])

        symbol = "{:.0f}".format(self.data.symbol[0])
        # self.log("{} close {}".format(symbol, self.data.close[0]))

        if self.order:
            return

        dt = self.datas[0].datetime.datetime()
        dtStr = dt.isoformat()[2:10].replace("-", "")
        close = self.dataclose[0]

        if dtStr == symbol and dt.hour == 16:
            self.log("*** end of season ***")
            self.log('DrawDown: %.2f' % self.stats.drawdown.drawdown[-1])
            self.log('MaxDrawDown: %.2f' % self.stats.drawdown.maxdrawdown[-1])

        # Check if we are in the market
        if not self.position:
            if dtStr == symbol:
                # 最后一天不开仓
                return

            if close < self.p.minopenprice:
                # 价差太小也不开仓
                return

            self.log('sell order create')
            self.order = self.sell(size=self.broker.get_cash())
        else:
            if dtStr == symbol and dt.hour == 15:
                self.log('buy order create @ end of season')
                self.order = self.buy(size=0-self.position.size)
            elif close < self.p.triggercloseprice:
                # 负溢价，提前平仓
                self.log('buy order create')
                self.order = self.buy(size=0-self.position.size)


def parse_args(pargs=None):
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    parser.add_argument('--datapath', required=True, help="csv data path")

    parser.add_argument('--minopenprice', default=1.01,
                        required=False, type=float, help="min open price")

    parser.add_argument('--triggercloseprice', default=1, required=False,
                        type=float, help="close price before deliveried")

    parser.add_argument('--plot', default=False, required=False, type=bool,
                        help='show plot')

    if pargs is not None:
        return parser.parse_args(pargs)

    return parser.parse_args()


if __name__ == '__main__':
    args = parse_args()

    # Create a cerebro entity
    cerebro = bt.Cerebro()
    # cerebro = bt.Cerebro(cheat_on_open=True)

    # Add a strategy
    cerebro.addstrategy(
        SimpleArbiStrategy,
        minopenprice=args.minopenprice,
        triggercloseprice=args.triggercloseprice,
    )

    # Create a Data Feed
    data = GenericCSV_PE(
        dataname=args.datapath,
        fromdate=datetime.datetime(2019, 1, 1),
        todate=datetime.datetime(2021, 6, 25, 16),
        nullvalue=0.0,
        timeframe=bt.TimeFrame.Minutes,
        compression=60,
        dtformat=('%Y-%m-%d %H:%M:%S'),
        datetime=0,
        open=1,
        high=2,
        low=3,
        close=4,
        volume=5,
        symbol=6,
        openinterest=-1
    )

    # Add the Data Feed to Cerebro
    cerebro.adddata(data)
    cerebro.addobserver(bt.observers.DrawDown)
    cerebro.addanalyzer(bt.analyzers.TimeReturn, timeframe=bt.TimeFrame.Years)

    # Set our desired cash start
    cerebro.broker.setcash(10000.0)

    # cheat on close 以收盘价成交
    cerebro.broker.set_coc(True)

    # Set the commission - 0.1% ... divide by 100 to remove the %
    cerebro.broker.setcommission(commission=0.0015)

    # Print out the starting conditions
    print('Starting Portfolio Value: %.2f' % cerebro.broker.getvalue())

    # Run over everything
    results = cerebro.run()
    strat0 = results[0]
    tret_analyzer = strat0.analyzers.getbyname('timereturn')
    print(tret_analyzer.get_analysis())

    # Print out the final result
    print('Final Portfolio Value: %.2f' % cerebro.broker.getvalue())

    if args.plot:
        cerebro.plot()

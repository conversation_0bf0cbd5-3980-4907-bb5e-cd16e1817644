from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import argparse
import datetime
import math
from decimal import Decimal

import backtrader as bt
from backtrader import position
from backtrader.utils.py3 import values


class GenericCSV_PE(bt.feeds.GenericCSVData):
    lines = ('symbol',)
    params = (('symbol', 6),)


def roundPrice(price):
    return round(price * 100000) / 100000


class GridArbiStrategy(bt.Strategy):
    params = (
        ("high", 1.03),
        ("low", 1.003),
        ("num", 10)
    )

    def log(self, txt, dt=None):
        dt = dt or self.datas[0].datetime.datetime()
        symbol = "{:.0f}".format(self.data.symbol[0])
        print('[%s] %s %s' % (symbol, dt.isoformat(), txt))

    def __init__(self):
        self.reset_grid_size()

        # self.gridNum = math.ceil(
        #     (Decimal(str(self.p.high)) - Decimal(str(self.p.low))) / Decimal(str(self.p.step))) + 1

        self.gridStep = roundPrice(
            (self.p.high - self.p.low) / (self.p.num - 1))
        self.gridPrices = []
        # num + 1 个价格，0 为第 1 格的平仓价
        for i in range(self.p.num + 1):
            self.gridPrices.append(roundPrice(
                self.p.low + (i - 1) * self.gridStep))

        self.lastTradeIdx = 0  # 上次交易网格位置
        self.order = None

    def reset_grid_size(self):
        '''按当前价值分配每个网格持仓价值'''
        self.gridSize = int(self.broker.getvalue() / self.p.num)

    def notify_order(self, order):
        if order.status in [order.Submitted, order.Accepted]:
            # Buy/Sell order submitted/accepted to/by broker - Nothing to do
            return

        # Check if an order has been completed
        # Attention: broker could reject order if not enough cash
        if order.status in [order.Completed]:
            positionValue = -self.position.size * order.executed.price
            if order.isbuy():
                self.log(
                    'BUY EXECUTED, Price: %.4f, Size: %.0f, Cost: %.2f, Comm: %.2f, position size: %.0f(%.0f%%), portfolio value: %.2f' %
                    (order.executed.price,
                     order.executed.size,
                     order.executed.value,
                     order.executed.comm,
                     self.position.size,
                     -self.position.size * 100 / (self.gridSize * self.p.num),
                     self.broker.getvalue()))

                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm
            else:  # Sell
                self.log('SELL EXECUTED, Price: %.4f, Size: %.0f, Cost: %.2f, Comm: %.2f, position size: %.0f(%.0f%%), portfolio value: %.2f' %
                         (order.executed.price,
                          order.executed.size,
                          order.executed.value,
                          order.executed.comm,
                          self.position.size,
                          -self.position.size * 100 /
                          (self.gridSize * self.p.num),
                          self.broker.getvalue()))

            self.bar_executed = len(self)

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('Order Canceled/Margin/Rejected')

        self.order = None

    def notify_trade(self, trade):
        if not trade.isclosed:
            return

        self.log('OPERATION PROFIT, GROSS %.4f, NET %.4f' %
                 (trade.pnl, trade.pnlcomm))

    def get_grid_index(self, price):
        '''
        根据价格返回当前所在网格位置
        网格位置按从最低价到最高价 1 ~ N, 每格价差 step
        '''
        idx = 0
        while 1:
            if price < (self.p.low + idx * self.p.step):
                break
            if idx == self.gridNum:
                break
            idx += 1
        return idx

    def next(self):
        symbol = "{:.0f}".format(self.data.symbol[0])
        # self.log("{} close {}".format(symbol, self.data.close[0]))

        if self.order:
            return

        close = self.data.close[0]
        orderUnits = 0
        while 1:
            lastTradeGridPrice = self.gridPrices[self.lastTradeIdx]
            if (lastTradeGridPrice + self.gridStep) < close:
                # 价格向上
                if self.lastTradeIdx < self.p.num:
                    # 还未满仓
                    self.lastTradeIdx += 1
                    # 加仓（卖出）
                    orderUnits += 1
                    self.log("price {:.4f} rised above {:.4f}, sell one".format(close,
                                                                                lastTradeGridPrice + self.gridStep))
                    continue
            elif (lastTradeGridPrice - self.gridStep) > close:
                # 价格向下
                if self.lastTradeIdx > 0:
                    # 还未空仓
                    self.lastTradeIdx -= 1
                    # 减仓（买入）
                    orderUnits -= 1
                    self.log("price {:.4f} fell below {:.4f}, buy one".format(close,
                                                                              lastTradeGridPrice - self.gridStep))
                    continue

            break

        dt = self.datas[0].datetime.datetime()
        dtStr = dt.isoformat()[2:10].replace("-", "")

        positionUnits = round(abs(self.position.size) / self.gridSize)
        if positionUnits > 0 and dtStr == symbol and dt.hour == 15:
            # 即将到期，全部平仓
            self.log('end of season, close all')
            orderUnits = -positionUnits

        orderSize = round(self.gridSize) * abs(orderUnits)
        if orderUnits > 0:
            # 加仓（卖出）
            if dtStr == symbol:
                # if dtStr == symbol and (dt.hour == 15 or dt.hour == 16):
                # 最后一天不再加仓
                pass
            else:
                self.order = self.sell(size=orderSize)
        elif orderUnits < 0:
            # 减仓（买入）
            self.order = self.buy(size=orderSize)

        if dtStr == symbol and dt.hour == 16:
            self.log("*** END OF SEASON ***")

            # 到期重新计算网格价值大小
            self.reset_grid_size()
            # 重置位置
            self.lastTradeIdx = 0

            self.log('DrawDown: %.2f' % self.stats.drawdown.drawdown[-1])
            self.log('MaxDrawDown: %.2f' % self.stats.drawdown.maxdrawdown[-1])


def parse_args(pargs=None):
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    parser.add_argument('--datapath', required=True, help="csv data path")

    parser.add_argument('--high', default=1.03,
                        required=False, type=float, help="the highest price to arbi")

    parser.add_argument('--low', default=1.003, required=False,
                        type=float, help="the lowest price to arbi")

    parser.add_argument('--num', default=10, required=False,
                        type=int, help="grid num")

    parser.add_argument('--plot', default=False, required=False, type=bool,
                        help='show plot')

    if pargs is not None:
        return parser.parse_args(pargs)

    return parser.parse_args()


if __name__ == '__main__':
    args = parse_args()

    cerebro = bt.Cerebro()

    # Add a strategy
    cerebro.addstrategy(
        GridArbiStrategy,
        high=args.high,
        low=args.low,
        num=args.num,
    )

    # Create a Data Feed
    data = GenericCSV_PE(
        dataname=args.datapath,
        fromdate=datetime.datetime(2019, 1, 1),
        # todate=datetime.datetime(2019, 8, 1),
        # todate=datetime.datetime(2019, 10, 1),
        todate=datetime.datetime(2021, 6, 25, 16),
        nullvalue=0.0,
        timeframe=bt.TimeFrame.Minutes,
        compression=60,
        dtformat=('%Y-%m-%d %H:%M:%S'),
        datetime=0,
        open=1,
        high=2,
        low=3,
        close=4,
        volume=5,
        symbol=6,
        openinterest=-1
    )

    # Add the Data Feed to Cerebro
    cerebro.adddata(data)

    cerebro.addobserver(bt.observers.DrawDown)
    cerebro.addanalyzer(bt.analyzers.TimeReturn, timeframe=bt.TimeFrame.Years)

    # Set our desired cash start
    cerebro.broker.setcash(10000.0)

    # cheat on close 以收盘价成交
    cerebro.broker.set_coc(True)

    # Set the commission - 0.1% ... divide by 100 to remove the %
    cerebro.broker.setcommission(commission=0.0015)

    # Print out the starting conditions
    print('Starting Portfolio Value: %.2f' % cerebro.broker.getvalue())

    # Run over everything
    results = cerebro.run()
    strat0 = results[0]
    tret_analyzer = strat0.analyzers.getbyname('timereturn')
    print(tret_analyzer.get_analysis())

    # Print out the final result
    print('Final Portfolio Value: %.2f' % cerebro.broker.getvalue())

    if args.plot:
        cerebro.plot()

import yfinance as yf
import matplotlib.pyplot as plt

symbol = 'ETH-USD'
start_date = '2020-01-01'
end_date = '2024-09-25'

df = yf.download(symbol, start=start_date, end=end_date)
df['signal'] = 0
df.loc[df['Close'] >= df['Close'].rolling(10).max().shift(), 'signal'] = 1
df['cumulative_return'] = (1+ df['Close'].pct_change() * df['signal'].shift(2)).cumprod()

def calculate_max_drawdown(returns):
    cumulative_returns = returns.cummax()
    drawdowns = (cumulative_returns - returns) / cumulative_returns
    max_drawdown = drawdowns.max()
    return max_drawdown

max_drawdown = calculate_max_drawdown(df['cumulative_return'])
print(f"Maximum Drawdown: {max_drawdown:.2%}")

plt.figure(figsize=(12, 6))
plt.plot(df.index, df['cumulative_return'])
plt.title(f'Cumulative Returns of {symbol} Trading Strategy')
plt.xlabel('Date')
plt.ylabel('Cumulative Return')
plt.grid(True)
plt.show()
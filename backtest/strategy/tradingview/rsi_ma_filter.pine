//@version=5
strategy("RSI with MA Filter Strategy (Long/Short)", overlay=true, initial_capital=10000, default_qty_type=strategy.percent_of_equity, default_qty_value=100)

// Input parameters
riskPerTrade = input(1.0, title="Risk Percentage") // Risk 1% of account per trade
rsiLength = input(7, title="RSI Length")
atrPeriod = input(14, title="ATR period")
rsiOverbought = input(80, title="RSI Overbought Level")
rsiOversold = input(30, title="RSI Oversold Level")
maLength = input(50, title="MA Length")

// Date range inputs
startDate = input.time(timestamp("2022-06-01"), title="Start Date")
endDate = input.time(timestamp("2024-09-31"), title="End Date")

// Calculate indicators
rsiValue = ta.rsi(close, rsiLength)
maValue = ta.sma(close, maLength)

// Entry and exit conditions with date range filter
validTimeRange = time >= startDate and time <= endDate
longCondition = validTimeRange and rsiValue > rsiOverbought and close > maValue
exitLongCondition = validTimeRange and rsiValue < rsiOversold

// Position sizing and risk management
atrMultiplier = 2.0

atr = ta.atr(atrPeriod)
stopLoss = atrMultiplier * atr

// Calculate position size based on risk
entryPrice = close
longStopPrice = entryPrice - stopLoss
positionSize = (strategy.equity * (riskPerTrade / 100)) / stopLoss

// Plot indicators
plot(maValue, color=color.blue, title="MA")

// Execute strategy
if (longCondition and strategy.position_size <= 0)
    strategy.entry("Long", strategy.long, qty=positionSize)

if (exitLongCondition and strategy.position_size > 0)
    strategy.close("Long")

// Add stop loss
strategy.exit("Long Stop", "Long", stop=longStopPrice)


// Plot entry and exit signals
plotshape(longCondition and strategy.position_size <= 0, title="Long", text="Buy", style=shape.triangleup, location=location.belowbar, color=color.green, size=size.small)
plotshape(exitLongCondition and strategy.position_size > 0, title="Exit Long", text="Exit Long", style=shape.triangledown, location=location.abovebar, color=color.yellow, size=size.small)

// Display current position and equity
var label equityLabel = na
label.delete(equityLabel)
equityLabel := label.new(bar_index, high, text="Equity: $" + str.tostring(strategy.equity, "#.##") + "\nPosition: " + str.tostring(strategy.position_size), color=color.new(color.white, 100), textcolor=color.black, size=size.small)
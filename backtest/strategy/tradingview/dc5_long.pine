//@version=5
strategy("Close > 10 Bars High Strategy with Date Range and Commission", overlay=true, commission_type=strategy.commission.percent, commission_value=0.05)

// Input parameters
riskPercentage = input.float(1, "Risk Percentage", minval=0.1, maxval=10, step=0.1)
stopLossATR = input.float(2, "Stop Loss ATR Multiplier", minval=0.5, maxval=5, step=0.1)
takeProfitATR = input.float(3, "Take Profit ATR Multiplier", minval=1, maxval=10, step=0.1)

// Date range inputs
startDate = input.time(timestamp("2023-01-01"), "Start Date")
endDate = input.time(timestamp("9999-12-31"), "End Date")

// Calculate highest high of last 10 bars
highestHigh = ta.highest(high, 10)

// Calculate ATR for position sizing and risk management
atrPeriod = 14
atr = ta.atr(atrPeriod)

// Entry condition
longCondition = close > highestHigh[1]

// Date range condition
inDateRange = time >= startDate and time <= endDate

// Position sizing
riskAmount = strategy.equity * (riskPercentage / 100)
stopLossDistance = atr * stopLossATR
positionSize = riskAmount / stopLossDistance

// Execute trades
if (longCondition and inDateRange)
    stopLossPrice = close - stopLossDistance
    takeProfitPrice = close + (atr * takeProfitATR)
    strategy.entry("Long", strategy.long, qty=positionSize)
    strategy.exit("Exit", "Long", stop=stopLossPrice, limit=takeProfitPrice)

// Plot signals
plotshape(longCondition and inDateRange, title="Buy Signal", location=location.belowbar, color=color.green, style=shape.triangleup, size=size.small)

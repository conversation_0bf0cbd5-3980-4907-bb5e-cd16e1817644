import argparse
from datetime import datetime
import time
import requests

LIMIT = 1000


def request_klines(type, symbol, period, start_time, retries=3):
    base_url = 'https://api.binance.com/api/v3/klines'
    if type == "ufutures":
        base_url = 'https://fapi.binance.com/fapi/v1/klines'

    for i in range(0, retries):
        url = '{}?symbol={}&interval={}&startTime={}&limit={}'.format(
            base_url, symbol, period, start_time, LIMIT
        )
        try:
            res = requests.get(url, timeout=10)
        except Exception as e:
            print("request error: {}".format(e))
            continue

        if res.status_code != 200:
            print("request klines err: {}".format(res.status_code))
            return []
        return res.json()

    print("{} times tried".format(retries))
    return []


def get_klines(type, symbol, period, from_date, to_date):
    from_ts = int(time.mktime(from_date.timetuple())) * 1000
    to_ts = int(time.mktime(to_date.timetuple())) * 1000

    klines = []
    while 1:
        tmp_klines = request_klines(type, symbol, period, from_ts)
        print("got {} klines since {}".format(len(tmp_klines), datetime.utcfromtimestamp(from_ts / 1000)))
        if len(tmp_klines) == 0:
            break
        last_ts = tmp_klines[len(tmp_klines) - 1][0]
        if last_ts < to_ts and len(tmp_klines) == LIMIT:
            klines.extend(tmp_klines[0:len(tmp_klines) - 1])
            from_ts = last_ts
        else:
            klines.extend(tmp_klines)
            break

    return klines


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    parser.add_argument('--type', required=False, default="spot", choices=["spot", "ufutures"])
    parser.add_argument('--symbol', required=True, type=str)
    parser.add_argument('--from_date', required=True,
                        type=lambda s: datetime.strptime(s, '%Y-%m-%d'))
    parser.add_argument('--to_date', required=False, default=datetime.now(),
                        type=lambda s: datetime.strptime(s, '%Y-%m-%d'))
    parser.add_argument('--period', default='1h', choices=['5m', '15m', '1h', '2h', '4h', '6h', '8h', '12h', '1d'])
    parser.add_argument('--output_dir', default='.')

    return parser.parse_args()


if __name__ == '__main__':
    args = parse_args()
    klines = get_klines(args.type, args.symbol, args.period,
                        args.from_date, args.to_date)
    file_name = "{}/binance-{}_{}_{}_{}-{}.csv".format(
        args.output_dir, args.type,
        args.symbol, args.period, args.from_date.strftime('%Y-%m-%d'), args.to_date.strftime('%Y-%m-%d'))
    f = open(file_name, 'w+')
    f.write("Time,Open,High,Low,Close,Volume\n")
    for k in klines:
        f.write("{},{},{},{},{},{}\n".format(
            datetime.utcfromtimestamp(k[0] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
            k[1],
            k[2],
            k[3],
            k[4],
            k[5],
        ))
    f.close()

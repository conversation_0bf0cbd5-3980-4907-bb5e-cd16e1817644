import argparse
from datetime import datetime
import time
import requests


API_PORT = 8888


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    parser.add_argument('--api_port', default=API_PORT,
                        required=False, help="mt5 api port")
    parser.add_argument('--symbol',  required=True, type=str)
    parser.add_argument('--from_date', required=True,
                        type=lambda s: datetime.strptime(s, '%Y-%m-%d'))
    parser.add_argument('--to_date', required=False, default=datetime.now(),
                        type=lambda s: datetime.strptime(s, '%Y-%m-%d'))
    parser.add_argument('--period', default='1h', choices=['1h', '1d'])
    parser.add_argument('--output_dir', default='.')

    return parser.parse_args()


def fetch_klines(symbol, period, from_date, to_date):
    if period == '1h':
        period = 1 | 0x4000
    elif period == '1d':
        period = 24 | 0x4000
    else:
        print("unsupported period")
        exit(0)

    from_ts = int(time.mktime(from_date.timetuple()))
    to_ts = int(time.mktime(to_date.timetuple()))

    path = "http://localhost:{}/mt5/klines?symbol={}&timeframe={}&date_from={}&date_to={}".format(
        API_PORT, symbol, period, from_ts, to_ts
    )
    res = requests.get(path)
    return res.json()


if __name__ == '__main__':
    args = parse_args()
    API_PORT = args.api_port

    klines = fetch_klines(args.symbol, args.period,
                          args.from_date, args.to_date)
    file_name = "{}/mt_{}_{}_{}-{}.csv".format(
        args.output_dir,
        args.symbol, args.period, args.from_date.strftime('%Y-%m-%d'), args.to_date.strftime('%Y-%m-%d'))
    f = open(file_name, 'w+')
    f.write("Date,Open,High,Low,Close,Volume\n")
    for k in klines:
        f.write("{},{},{},{},{},{}\n".format(
            datetime.utcfromtimestamp(k["time"]).strftime('%Y-%m-%d %H:%M:%S'),
            k["open"],
            k["high"],
            k["low"],
            k["close"],
            k["tick_volume"],
        ))
    f.close()

import argparse
import os
import string
import random
from datetime import datetime, timedelta

import backtrader as bt

Long = "long"
Short = "short"
Both = "both"

Buy = "buy"
Sell = "sell"

ENABLE_ORDER_LOG = False


def PriceOpen1(EMA144, EMA169):
    return (EMA144 + EMA169) / 2


def PriceOpen2(EMA144, EMA169):
    return 2 * max(EMA144, EMA169) - (EMA144 + EMA169) / 2


def PriceOpen3(EMA144, EMA169):
    return 2 * min(EMA144, EMA169) - (EMA144 + EMA169) / 2


def PriceOpen4(EMA144, EMA169):
    return EMA144


def PriceOpen5(EMA144, EMA169):
    return EMA169


def id_generator(prefix="", size=6, chars=string.ascii_uppercase + string.digits):
    return prefix + ''.join(random.choice(chars) for _ in range(size))


# 创建的订单
class Order(object):
    def __init__(self, **kwargs):
        self.is_close = kwargs.pop('is_close', False)
        self.side = kwargs.pop('side', "")
        self.price = kwargs.pop('price', 0.0)
        self.qty = kwargs.pop('qty', 0.0)
        self.time = kwargs.pop('time', None)
        self.current_position = kwargs.pop('current_position', 0.0)
        self.last_price = kwargs.pop('last_price', 0.0)
        self.position_value = kwargs.pop('position_value', 0.0)
        self.cash = kwargs.pop('cash', 0.0)
        self.worth = kwargs.pop('worth', 0.0)
        self.total_pnl = kwargs.pop('total_pnl', 0.0)
        self.total_pnl_ratio = kwargs.pop('total_pnl_ratio', 0.0)
        self.id = kwargs.pop('id', '')
        self.ref_id = kwargs.pop('ref_id', '')
        self.submit_time = kwargs.pop('submit_time', None)
        self.stop_loss = kwargs.pop('stop_loss', False)

    def to_pine_order(self, timezone_offset=0):
        time = self.submit_time
        if timezone_offset != 0:
            time = time + timedelta(hours=timezone_offset)

        long_short = "long"
        if self.side == Sell:
            long_short = "short"
        # 注意 pine 脚本严格要求格式和对齐，不要随意加空白字符
        if self.is_close:
            if self.stop_loss:
                return f"""\nif year == {time.year} and month == {time.month} and dayofmonth == {time.day} and hour == {time.hour}\n    strategy.exit("{self.id}", "{self.ref_id}", qty={abs(self.qty):.4f}, stop={self.price:.5f})\n"""
            else:
                return f"""\nif year == {time.year} and month == {time.month} and dayofmonth == {time.day} and hour == {time.hour}\n    strategy.exit("{self.id}", "{self.ref_id}", qty={abs(self.qty):.4f}, limit={self.price:.5f})\n"""
        else:
            return f"""\nif year == {time.year} and month == {time.month} and dayofmonth == {time.day} and hour == {time.hour}\n    strategy.entry("{self.id}", strategy.{long_short}, qty={abs(self.qty):.4f}, limit={self.price:.5f})\n"""


# 已成交交易
class Trade(Order):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class VegasStrategy(bt.Strategy):
    params = (
        ("risk_ratio", 0.05),
        ("price_open_method", 1),
        ("signal_filter_method", "a"),
        ("profit_ratio", 3),
        ("side", Long),
    )

    def log(self, txt, dt=None):
        dt = dt or self.datas[0].datetime.datetime()
        print('[%s] %s' % (dt.isoformat(), txt))

    def __init__(self):
        self.orders = []
        self.trades = []
        self.daily_pnls = []
        self.daily_positions = []
        self.open_order = None
        self.open_order_id = ""
        self.stop_price = 0
        self.take_profit_price = 0
        self.init_cash = self.broker.getvalue()
        self.max_leverage = 0
        self.stop_loss_count = 0
        self.take_profit_count = 0
        self.max_drawdown = 0
        self.max_drawdown_time = None

        self.ema12 = bt.indicators.EMA(period=12)
        self.ema144 = bt.indicators.EMA(period=144)
        self.ema169 = bt.indicators.EMA(period=169)
        self.ema576 = bt.indicators.EMA(period=576)
        self.ema676 = bt.indicators.EMA(period=676)
        if self.p.signal_filter_method == "b":
            self.atr = bt.indicators.ATR(period=20)

    def start(self):
        pass

    def record_order(self, order):
        side = Buy if order.isbuy() else Sell
        is_close = self.position.size == 0
        total_pnl = self.broker.getvalue() - self.init_cash
        last_price = self.data.close[0]

        if order.status == order.Completed:
            self.trades.append(Trade(
                side=side,
                price=order.executed.price,
                qty=order.executed.size,
                time=self.data0.datetime.datetime(),
                is_close=is_close,
                stop_loss=order.info.stop_loss,
                current_position=self.position.size,
                total_pnl=total_pnl,
                total_pnl_ratio=total_pnl / self.init_cash,
                id=order.info.id,
                ref_id=order.info.ref_id or "",
                last_price=last_price,
                position_value=self.position.size * last_price,
                cash=self.broker.getcash(),
                worth=self.broker.getvalue()
            ))

            self.orders.append(Order(
                side=side,
                price=order.price,
                qty=order.size,
                time=self.data0.datetime.datetime(),
                submit_time=order.info.submit_time,
                is_close=is_close,
                stop_loss=order.info.stop_loss,
                current_position=self.position.size,
                total_pnl=total_pnl,
                total_pnl_ratio=total_pnl / self.init_cash,
                id=order.info.id,
                ref_id=order.info.ref_id or "",
                last_price=last_price,
                position_value=self.position.size * last_price,
                cash=self.broker.getcash(),
                worth=self.broker.getvalue()
            ))

    def notify_order(self, order):
        if order.status in [order.Completed]:
            if self.position.size == 0:
                self.log("close order traded: {:.4f}@{:.2f}".format(order.executed.size, order.executed.price))
            else:
                self.log("open order traded: {:.4f}@{:.2f}".format(order.executed.size, order.executed.price))
                self.open_order_id = order.info.id
                # 止盈价开仓后即固定
                self.stop_price = self.ema676[0]
                if self.position.size > 0:
                    self.take_profit_price = self.position.price + self.p.profit_ratio * (self.position.price - self.stop_price)
                if self.position.size < 0:
                    self.take_profit_price = self.position.price - self.p.profit_ratio * (self.stop_price - self.position.price)

                leverage = abs(self.position.size * self.position.price) / self.broker.getvalue()
                self.max_leverage = max(leverage, self.max_leverage)

            self.record_order(order)
            self.open_order = None
        elif order.status in [order.Margin, order.Rejected]:
            self.log('{} Order Margin({})/Rejected({}) status: {}'.format(
                     "buy" if order.isbuy() else "sell",
                     order.Margin, order.Rejected,
                     order.status,
                     ))
            exit()
        elif order.status == order.Canceled:
            pass
        elif order.status == order.Submitted:
            pass

    def notify_trade(self, trade):
        if not trade.isclosed:
            return

        value = self.broker.getvalue()
        pct = trade.pnlcomm / (value - trade.pnlcomm) * 100
        self.log('OPERATION PROFIT, GROSS %.4f(%.2f%%)' % (trade.pnlcomm, pct))

    def next(self):
        dt = self.datas[0].datetime.datetime()

        if dt.hour == 0 and dt.minute == 0:
            self.daily_pnls.append((
                dt.date(),
                self.broker.getvalue() - self.init_cash
            ))

        if self.stats.drawdown.maxdrawdown[-1] > self.max_drawdown:
            self.max_drawdown = self.stats.drawdown.maxdrawdown[-1]
            self.max_drawdown_time = dt

        # self.log("EMA12: {}".format(self.ema12[0]))
        # self.log("EMA144: {}".format(self.ema144[0]))
        # self.log("EMA676: {}".format(self.ema676[0]))
        # self.log("ATR20: {}".format(self.atr[0]))

        if not self.position or self.position.size == 0:
            if self.is_long_signal():
                open_price = self.get_open_price()
                stop_price = self.ema676[0]
                size = self.broker.getcash() * self.p.risk_ratio / (open_price - stop_price)
                # self.log("buy {:.4f}@{:.2f}".format(size, open_price))
                if self.open_order is not None:
                    self.broker.cancel(self.open_order)
                self.open_order = self.buy(exectype=bt.Order.Limit, size=size, price=open_price)
                self.open_order.addinfo(submit_time=dt)
                self.open_order.addinfo(id=id_generator("Open_"))

            elif self.is_short_signal():
                open_price = self.get_open_price()
                stop_price = self.ema676[0]
                size = self.broker.getcash() * self.p.risk_ratio / (stop_price - open_price)
                # self.log("buy {:.4f}@{:.2f}".format(size, open_price))
                if self.open_order is not None:
                    self.broker.cancel(self.open_order)
                self.open_order = self.sell(exectype=bt.Order.Limit, size=size, price=open_price)
                self.open_order.addinfo(submit_time=dt)
                self.open_order.addinfo(id=id_generator("Open_"))

        else:
            try:
                self.close_handle()
            except IndexError as e:
                self.log("close handle index error: {}".format(e))

    def is_long_signal(self):
        if self.p.side == Short:
            return False

        if self.ema12[0] > self.ema144[0] and self.ema12[0] > self.ema169[0]:
            pass
        else:
            return False

        if min(self.ema144[0], self.ema169[0]) > max(self.ema576[0], self.ema676[0]):
            pass
        else:
            return False

        if self.ema169[0] > self.ema169[-1] > self.ema169[-2]:
            pass
        else:
            return False

        if self.ema676[0] > self.ema676[-1] > self.ema676[-2]:
            pass
        else:
            return False

        return self.filter_signal()

    def filter_signal(self):
        pd = abs(self.ema676[0] - (self.ema144[0]+self.ema169[0]) / 2) # 通道间距
        if pd > (self.ema676[0] * 0.02):
            pass
        else:
            return False

        if self.p.signal_filter_method == "a":
            if pd < (self.ema676[0] * 0.1):
                pass
            else:
                return False

        elif self.p.signal_filter_method == "b":
            if pd > (4 * self.atr[0]):
                pass
            else:
                return False

            if pd < (0.1 * self.ema676[0]):
                pass
            else:
                return False

        elif self.p.signal_filter_method == "c":
            return True

        return True

    def is_short_signal(self):
        if self.p.side == Long:
            return False

        if self.ema12[0] < self.ema144[0] and self.ema12[0] < self.ema169[0]:
            pass
        else:
            return False

        if max(self.ema144[0], self.ema169[0]) < min(self.ema576[0], self.ema676[0]):
            pass
        else:
            return False

        if self.ema169[0] < self.ema169[-1] < self.ema169[-2]:
            pass
        else:
            return False

        if self.ema676[0] < self.ema676[-1] < self.ema676[-2]:
            pass
        else:
            return False

        return self.filter_signal()

    def close_handle(self):
        stop_order = None
        # 止损价随 676 移动，价格如果更差则不变
        if self.position.size > 0:
            stop_price = max(self.stop_price, self.ema676[0])
            self.stop_price = stop_price
            if self.data.low[1] <= stop_price:
                if self.data.high[1] <= stop_price:
                    stop_price = self.data.open[1] # 说明当前 K 线已触发止损了，改用开盘价止损
                stop_order = self.sell(exectype=bt.Order.StopLimit, size=self.position.size, price=stop_price, plimit=stop_price)
        if self.position.size < 0:
            stop_price = min(self.stop_price, self.ema676[0])
            self.stop_price = stop_price
            if self.data.high[1] >= stop_price:
                if self.data.low[1] >= stop_price:
                    stop_price = self.data.open[1] # 说明当前 K 线已触发止损了，改用开盘价止损
                stop_order = self.buy(exectype=bt.Order.StopLimit, size=abs(self.position.size), price=stop_price, plimit=stop_price)
        if stop_order is not None:
            stop_order.addinfo(submit_time=self.datas[0].datetime.datetime())
            stop_order.addinfo(ref_id=self.open_order_id)

            is_acturely_take_profit = False
            if self.position.size > 0 and self.position.price < stop_price:
                is_acturely_take_profit = True
            elif self.position.size < 0 and self.position.price > stop_price:
                is_acturely_take_profit = True

            if is_acturely_take_profit:
                stop_order.addinfo(id=self.open_order_id.replace("Open_", "TakeProfit_"))
                self.take_profit_count += 1
                self.log("stop loss signal in next kline(acturely take profit)")
            else:
                stop_order.addinfo(id=self.open_order_id.replace("Open_", "StopLoss_"))
                stop_order.addinfo(stop_loss=True)
                self.stop_loss_count += 1
                self.log("stop loss signal in next kline")

            # dt = self.datas[0].datetime.datetime()
            # if dt.year == 2018 and dt.month == 12 and dt.day == 28 and dt.hour == 15:
            #     print("debug")
            return

        take_profit_order = None
        if self.position.size > 0:
            if self.data.high[1] >= self.take_profit_price:
                take_profit_order = self.sell(exectype=bt.Order.Limit, size=self.position.size, price=self.take_profit_price)
        if self.position.size < 0:
            if self.data.low[1] <= self.take_profit_price:
                take_profit_order = self.buy(exectype=bt.Order.Limit, size=abs(self.position.size), price=self.take_profit_price)
        if take_profit_order is not None:
            take_profit_order.addinfo(submit_time=self.datas[0].datetime.datetime())
            take_profit_order.addinfo(id=self.open_order_id.replace("Open_", "TakeProfit_"))
            take_profit_order.addinfo(ref_id=self.open_order_id)
            self.take_profit_count += 1
            self.log("take profit signal in next kline")

    def get_open_price(self):
        if self.p.price_open_method == 1:
            return PriceOpen1(self.ema144[0], self.ema169[0])
        elif self.p.price_open_method == 2:
            return PriceOpen2(self.ema144[0], self.ema169[0])
        elif self.p.price_open_method == 3:
            return PriceOpen3(self.ema144[0], self.ema169[0])
        elif self.p.price_open_method == 4:
            return PriceOpen4(self.ema144[0], self.ema169[0])
        elif self.p.price_open_method == 5:
            return PriceOpen5(self.ema144[0], self.ema169[0])


def save_trades(file_name, trades, symbol):
    f = open(file_name + ".csv", 'w+')
    f.write(
        "Time,Symbol,Side,Price,Qty,ID,RefID,CurrentPosition,LastPrice,PositionValue,EntryValue,Leverage,TradePNL,Cash,Worth,TotalPnl,TotalPnlRatio\n")
    position_trades = []
    last_position = 0
    last_total_pnl = 0
    for trade in trades:
        if abs(trade.current_position) > abs(last_position):  # 加仓
            position_trades.append(trade)
        else:  # 减仓，先进后出
            position_trades = position_trades[:-1]
        last_position = trade.current_position
        position_entry_value = 0
        for t in position_trades:
            position_entry_value += t.price * t.qty
        trade_pnl = 0
        if trade.is_close:
            trade_pnl = trade.total_pnl - last_total_pnl
            last_total_pnl = trade.total_pnl

        f.write("{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{}\n".format(
            trade.time.strftime('%Y-%m-%d %H:%M:%S'),
            symbol,
            trade.side,
            "{:.5f}".format(trade.price),
            trade.qty,
            trade.id,
            trade.ref_id,
            "{:.2f}".format(trade.current_position),
            "{:.5f}".format(trade.last_price),
            "{:.2f}".format(trade.position_value),
            "{:.2f}".format(position_entry_value),
            "{:.2f}".format(abs(trade.position_value) / trade.worth),
            # "{:.2f}".format(trade.position_value - position_entry_value),
            "{:.2f}".format(trade_pnl),
            "{:.2f}".format(trade.cash),
            "{:.2f}".format(trade.worth),
            "{:.2f}".format(trade.total_pnl),
            "{:.6f}".format(trade.total_pnl_ratio),
        ))
    f.close()


def save_pines(output_dir, file_name, orders, cash, fee_rate):
    f = open(output_dir + "/" + file_name + ".pine", 'w+')
    f.write(f'//@version=4\nstrategy("{file_name}", default_qty_type=strategy.cash, initial_capital={cash}, commission_type=strategy.commission.percent, commission_value={fee_rate*100}, calc_on_order_fills=true, close_entries_rule="ANY")\n')
    for order in orders:
        f.write(order.to_pine_order())
    f.write('\nplot(strategy.equity)')
    f.close()


def run_strategy(signal_filter_method: str, risk_ratio: float, price_open_method: int, profit_ratio: float, side: str, datapath: str, cash: float, fee_rate: float,  from_date: datetime, to_date: datetime, output_dir: str):
    cerebro = bt.Cerebro()

    # Add a strategy
    cerebro.addstrategy(
        VegasStrategy,
        signal_filter_method=signal_filter_method,
        risk_ratio=risk_ratio,
        price_open_method=price_open_method,
        profit_ratio=profit_ratio,
        side=side,
    )

    # Create a Data Feed
    data = bt.feeds.GenericCSVData(
        dataname=datapath,
        fromdate=from_date,
        todate=to_date,
        nullvalue=0.0,
        timeframe=bt.TimeFrame.Minutes,
        dtformat=('%Y-%m-%d %H:%M:%S'),
        datetime=0,
        open=1,
        high=2,
        low=3,
        close=4,
        volume=5,
        openinterest=-1
    )
    data_name_strs = data._name.split('_')
    source = data_name_strs[0]
    symbol = data_name_strs[1]
    period = data_name_strs[2]

    # Add the Data Feed to Cerebro
    cerebro.adddata(data)

    cerebro.addobserver(bt.observers.DrawDown)
    cerebro.addanalyzer(bt.analyzers.TimeDrawDown)
    cerebro.addanalyzer(bt.analyzers.TimeReturn, timeframe=bt.TimeFrame.Years)

    # Set our desired cash start
    cerebro.broker.setcash(cash)

    # Set the commission - 0.1% ... divide by 100 to remove the %
    leverage = 1 / risk_ratio
    cerebro.broker.setcommission(commission=fee_rate, leverage=leverage)

    # Run over everything
    results = cerebro.run()
    strat0 = results[0]
    tret_analyzer = strat0.analyzers.getbyname('timereturn')
    timereturns = tret_analyzer.get_analysis()
    timedrawdowns = strat0.analyzers.getbyname('timedrawdown').get_analysis()
    maxdrawdownperiod = timedrawdowns.get("maxdrawdownperiod")

    if period == "1h":
        strat0.maxdrawdown_start_time = strat0.max_drawdown_time - timedelta(hours=maxdrawdownperiod)
    elif period == "4h":
        strat0.maxdrawdown_start_time = strat0.max_drawdown_time - timedelta(hours=maxdrawdownperiod)
    elif period == "5m":
        strat0.maxdrawdown_start_time = strat0.max_drawdown_time - timedelta(minutes=maxdrawdownperiod)
    elif period == "15m":
        strat0.maxdrawdown_start_time = strat0.max_drawdown_time - timedelta(minutes=maxdrawdownperiod)

    file_name = "{}_{}_{}_{}_{}_{}_{}_{}_{}_{}-{}".format(
        source, symbol, period, side, cash, signal_filter_method, risk_ratio, price_open_method, profit_ratio,
        from_date.strftime("%Y-%m-%d"), to_date.strftime("%Y-%m-%d"))

    print("\n*** %s ***" % file_name)
    print('Starting Portfolio Value: %.2f' % cash)
    print('Final Portfolio Value: %.2f' % cerebro.broker.getvalue())
    print("TimeReturns:")
    for d in timereturns:
        print("{} {:.2f}%".format(d, timereturns[d] * 100))
    print('Total return: %.2f%%' %
          ((cerebro.broker.getvalue() - cash) * 100 / cash))
    print('MaxDrawDown: %.2f%%' % strat0.stats.drawdown.maxdrawdown[0])
    print('MaxDrawDown time: %s ~ %s' % (strat0.maxdrawdown_start_time, strat0.max_drawdown_time))
    print('Max Leverage: %.2f' % strat0.max_leverage)
    print('Win/Loss: %d/%d, win ratio: %.2f%%' % (strat0.take_profit_count, strat0.stop_loss_count, strat0.take_profit_count / (strat0.take_profit_count + strat0.stop_loss_count) * 100))

    trades_stat(from_date, to_date, strat0.trades, side, True)
    pnl_stat(strat0.daily_pnls, side, True)

    output_dir = output_dir + "/vegas"
    if not os.path.isdir(output_dir):
        os.mkdir(output_dir)

    save_trades(output_dir + "/" + file_name, strat0.trades, symbol)
    save_pines(output_dir, file_name, strat0.orders, cash, fee_rate)

    return cerebro, strat0


def pnl_stat(pnls, name, print_out=False):
    max_pnl = None
    max_pnl_date = None
    min_pnl = None
    min_pnl_date = None
    positive_pnl_days = 0
    nagative_pnl_days = 0
    for (date, pnl) in pnls:
        if max_pnl is None:
            max_pnl = pnl
            max_pnl_date = date
        if min_pnl is None:
            min_pnl = pnl
            min_pnl_date = date

        if pnl > 0:
            positive_pnl_days += 1
        else:
            nagative_pnl_days += 1

        if pnl > max_pnl:
            max_pnl = pnl
            max_pnl_date = date

        if pnl < min_pnl:
            min_pnl = pnl
            min_pnl_date = date

    if len(pnls) == 0:
        return

    if print_out:
        print("\n*** PNL Stat {} ***".format(name))
        print("max pnl: {:.2f} @ {}".format(max_pnl, max_pnl_date))
        print("min pnl: {:.2f} @ {}".format(min_pnl, min_pnl_date))
        print("positive pnl days: {}".format(positive_pnl_days))
        print("nagative pnl days: {}".format(nagative_pnl_days))
    return max_pnl, min_pnl, positive_pnl_days, nagative_pnl_days


def trades_stat(from_date, to_date, trades, name, print_out=False):
    if len(trades) == 0:
        return

    duration = to_date - from_date
    avg_trade_time = duration / len(trades)
    max_no_trade_time = trades[0].time - from_date
    last_trade_time = trades[0].time
    for trade in trades[1:]:
        max_no_trade_time = max(
            max_no_trade_time, trade.time - last_trade_time)
        last_trade_time = trade.time

    max_no_trade_time = max(
        max_no_trade_time, to_date - last_trade_time)

    if print_out:
        print("\n*** Trade Stat %s ***" % name)
        print("{} ~ {}, {} days".format(
            from_date.date(), to_date.date(), duration.days))
        print("trade count: ", len(trades))
        print("avg_trade_time: ", avg_trade_time)
        print("max_no_trade_time: ", max_no_trade_time)

    return avg_trade_time, max_no_trade_time


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    parser.add_argument('--datapath', required=True, help="csv data path")

    parser.add_argument('--from_date', required=True,
                        type=lambda s: datetime.strptime(s, '%Y-%m-%d'), help="from date")

    parser.add_argument('--to_date', required=True,
                        type=lambda s: datetime.strptime(s, '%Y-%m-%d'), help="to date")

    parser.add_argument('--cash', default=100000, required=False,
                        type=int, help="init cash")

    parser.add_argument('--side', default=Long,
                        choices=[Long, Short, Both], required=False, help='side')

    parser.add_argument('--signal_filter_method', default="a", required=False, choices=["a", "b", "c"], type=str)

    parser.add_argument('--risk_ratio', default=0.05, required=False, type=float)

    parser.add_argument('--price_open_method', default=1, required=False, choices=[1,2,3,4,5], type=int)

    parser.add_argument('--profit_ratio', default=3, required=False, type=float)

    parser.add_argument('--fee_rate', default=0.0, required=False, type=float)

    parser.add_argument('--plot', default=False, required=False, type=bool,
                        help='show plot')

    parser.add_argument('--output_dir', default='.')

    return parser.parse_args()


if __name__ == '__main__':
    args = parse_args()

    cerebro, strat = run_strategy(args.signal_filter_method, args.risk_ratio, args.price_open_method, args.profit_ratio,
                                  args.side, args.datapath, args.cash, args.fee_rate, args.from_date, args.to_date, args.output_dir)

    if args.plot:
        cerebro.plot()

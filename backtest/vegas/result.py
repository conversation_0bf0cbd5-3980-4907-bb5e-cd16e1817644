
import os

from datetime import datetime, timedelta
from strategy import run_strategy, trades_stat, pnl_stat, Both, Long, Short


class BackTestCase(object):

    def __init__(self, **kwargs):
        self.symbol = kwargs.pop('symbol')
        self.period = kwargs.pop('period')
        self.signal_filter_method = kwargs.pop('signal_filter_method')
        self.risk_ratio = kwargs.pop('risk_ratio')
        self.price_open_method = kwargs.pop('price_open_method')
        self.profit_ratio = kwargs.pop('profit_ratio')
        self.side = kwargs.pop('side')
        self.from_date = kwargs.pop('from_date')
        self.to_date = kwargs.pop('to_date')
        self.data_file = kwargs.pop('data_file')

    def set_results(self, **kwargs):
        self.days = kwargs.pop('days')
        self.trade_count = kwargs.pop('trade_count')
        self.return_rate = kwargs.pop('return_rate')
        self.annualized_rate = kwargs.pop('annualized_rate', 0)
        self.avg_trade_time = kwargs.pop('avg_trade_time')
        self.max_no_trade_time = kwargs.pop('max_no_trade_time')
        self.max_loss_rate = kwargs.pop('max_loss_rate')
        self.max_drawdown = kwargs.pop('max_drawdown')
        self.max_drawdown_start_time = kwargs.pop('max_drawdown_start_time')
        self.max_drawdown_end_time = kwargs.pop('max_drawdown_end_time')
        self.positive_pnl_days = kwargs.pop('positive_pnl_days')
        self.nagative_pnl_days = kwargs.pop('nagative_pnl_days')
        self.stop_loss_count = kwargs.pop('stop_loss_count')
        self.take_profit_count = kwargs.pop('take_profit_count')

if __name__ == "__main__":
    cash = 10000
    fee_rate = 0.0006

    data_files = [
        "binance_BTCUSDT_5m_2017-10-01-2022-12-10.csv",
        "binance_BTCUSDT_15m_2017-10-01-2022-12-10.csv",
        "binance_BTCUSDT_1h_2017-10-01-2022-12-10.csv",
        "binance_BTCUSDT_4h_2017-10-01-2022-12-10.csv",

        # "binance_ETHUSDT_5m_2017-10-01-2022-12-10.csv",
        # "binance_ETHUSDT_15m_2017-10-01-2022-12-10.csv",
        # "binance_ETHUSDT_1h_2017-10-01-2022-12-10.csv",
        # "binance_ETHUSDT_4h_2017-10-01-2022-12-10.csv",

        # "mt_USDCNH_1h_2015-01-01-2022-12-15.csv",
        # "mt_USDCNH_1d_2015-01-01-2022-12-15.csv",
        # "mt_EURUSD_1h_2015-01-01-2022-12-15.csv",
    ]
    # risk_ratios = [0.025, 0.05]
    risk_ratios = [0.05]
    price_open_methods = [1, 2, 3, 4, 5]
    # price_open_methods = [1]
    # profit_ratios = [1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5]
    profit_ratios = [1, 3, 5]
    # sides = [Long, Short, Both]
    sides = [Both]
    from_date = "2020-01-01"
    # from_date = "2016-01-01"
    to_date = "2022-12-09"
    signal_filter_methods = ["a", "b", "c"]
    # signal_filter_methods = ["a"]

    tests = []
    for data_file in data_files:
        for signal_filter_method in signal_filter_methods:
            for risk_ratio in risk_ratios:
                for price_open_method in price_open_methods:
                    for profit_ratio in profit_ratios:
                        for side in sides:
                            symbol = data_file.split("_")[1]
                            period = data_file.split("_")[2]
                            tests.append(BackTestCase(
                                symbol=symbol,
                                period=period,
                                signal_filter_method=signal_filter_method,
                                risk_ratio=risk_ratio,
                                price_open_method=price_open_method,
                                profit_ratio=profit_ratio,
                                side=side,
                                from_date=from_date,
                                to_date=to_date,
                                data_file=data_file,
                            ))

    output_dir = "../output"
    results = []
    for case in tests:
        datapath = os.path.join("../data", case.data_file)
        from_date_time = datetime.strptime(case.from_date, '%Y-%m-%d')
        to_date_time = datetime.strptime(case.to_date, '%Y-%m-%d')

        cerebro, strat = run_strategy(case.signal_filter_method, case.risk_ratio, case.price_open_method, case.profit_ratio,
                                  case.side, datapath, cash, fee_rate, from_date_time, to_date_time, output_dir)

        days = (to_date_time - from_date_time).days
        return_rate = (strat.broker.getvalue() - cash) / cash
        annualized_rate = return_rate / (days / 365)
        avg_trade_time, max_no_trade_time = trades_stat(
                from_date_time, to_date_time, strat.trades, "", False)

        # 填充缺失的日期
        time = from_date_time
        pnls = []
        last_pnl = 0
        i = 0
        while time <= to_date_time:
            date, current_pnl = strat.daily_pnls[i]
            if date == time.date():
                last_pnl = current_pnl
                i += 1
            pnls.append((time.date(), last_pnl))
            time += timedelta(hours=24)

        max_pnl, min_pnl, positive_pnl_days, nagative_pnl_days = pnl_stat(pnls, "orders")

        case.set_results(
            days=days,
            trade_count=len(strat.trades),
            return_rate=return_rate,
            annualized_rate=annualized_rate,
            avg_trade_time=avg_trade_time,
            max_no_trade_time=max_no_trade_time,
            max_loss_rate=min_pnl/cash,
            positive_pnl_days=positive_pnl_days,
            nagative_pnl_days=nagative_pnl_days,
            max_drawdown=strat.stats.drawdown.maxdrawdown[0],
            max_drawdown_start_time=strat.maxdrawdown_start_time,
            max_drawdown_end_time=strat.max_drawdown_time,
            stop_loss_count=strat.stop_loss_count,
            take_profit_count=strat.take_profit_count,
        )
        results.append(case)

    f = open(output_dir + "/vegas/vegas_results.csv", 'w+')
    f.write(
        "品种,K线,方向,过滤方案,风险率,开仓价算法,盈亏比,时间,天数,成交次数,胜率,总收益率,净年平均收益率,平均成交时间,最长无成交时间,最大损失,最大回撤,最大回撤时间,正收益天数,负收益天数\n")
    for r in results:
        f.write("{},{},{},{},{},{},{},{},{},{},{:.2f}%,{:.2f}%,{:.2f}%,{},{},{:.2f}%,{:.2f}%,{},{},{}\n".format(
            r.symbol,
            r.period,
            r.side,
            r.signal_filter_method,
            r.risk_ratio,
            r.price_open_method,
            r.profit_ratio,
            "{}~{}".format(r.from_date, r.to_date),
            r.days,
            r.trade_count,
            r.take_profit_count / (r.take_profit_count + r.stop_loss_count) * 100,
            r.return_rate * 100,
            r.annualized_rate * 100,
            "{}".format(r.avg_trade_time).replace(",", ""),
            "{}".format(r.max_no_trade_time).replace(",", ""),
            r.max_loss_rate * 100,
            r.max_drawdown,
            "{}~{}".format(r.max_drawdown_start_time.strftime("%Y-%m-%d"), r.max_drawdown_end_time.strftime("%Y-%m-%d")),
            r.positive_pnl_days,
            r.nagative_pnl_days,
        ))
    f.close()

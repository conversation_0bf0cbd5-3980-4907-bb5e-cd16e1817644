import argparse
from array import array
import math
import string
import random
from datetime import datetime, timedelta

import backtrader as bt

from strategy import Order, Trade, save_trades

Long = "long"
Short = "short"
Both = "both"

Buy = "buy"
Sell = "sell"

MT = "MT"  # mt5 download
TV = "TV"  # tradingview export


def id_generator(size=6, chars=string.ascii_uppercase + string.digits):
    return ''.join(random.choice(chars) for _ in range(size))


class OrderStrategy(bt.Strategy):
    params = (
        ("orders", []),
    )

    def log(self, txt, dt=None):
        dt = dt or self.datas[0].datetime.datetime()
        print('[%s] %s' % (dt.isoformat(), txt))

    def __init__(self):
        self.cash = self.broker.getvalue()

        self.current_position = 0
        self.orders = self.p.orders
        self.orderIndex = 0
        self.trades = []
        self.daily_pnls = []
        self.daily_positions = []

        print("OrderStrategy init done")

    # 记录订单/成交
    def record_order(self, order):
        if order.status == order.Completed:
            direction = Buy if order.isbuy() else Sell
            pnl = self.broker.getvalue() - self.cash
            last_price = self.data.close[0]
            self.current_position += order.executed.size

            self.trades.append(Trade(
                side=direction,
                price=order.executed.price,
                qty=order.executed.size,
                index=order.info.gridIndex,
                time=self.data0.datetime.datetime(),
                is_close=order.info.is_close,
                current_position=self.current_position,
                total_pnl=pnl,
                total_pnl_ratio=pnl / self.cash,
                id=order.info.id,
                ref_id=order.info.ref_id or "",
                last_price=last_price,
                position_value=self.current_position * last_price,
                cash=self.broker.getcash(),
                worth=self.broker.getvalue()
            ))

        else:
            return

    def notify_order(self, order):
        if order.status in [order.Completed]:
            self.record_order(order)

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('{} Order[{} - {}] Canceled({})/Margin({})/Rejected({}) status: {}'.format(
                     "buy" if order.isbuy() else "sell",
                     order.info.id,
                     order.info.gridIndex,
                     order.Canceled, order.Margin, order.Rejected,
                     order.status,
                     ))
            exit()

    def next(self):
        dt = self.datas[0].datetime.datetime()

        if dt.hour == 0:
            self.daily_pnls.append((
                dt.date(),
                self.broker.getvalue() - self.cash
            ))

            self.daily_positions.append((
                dt.date(),
                self.current_position,
                self.data.close[0],
            ))

        while 1:
            if self.orderIndex >= len(self.orders):
                break
            sim_order = self.orders[self.orderIndex]
            if sim_order.submit_time <= dt:
                if sim_order.side == Sell:
                    order = self.sell(exectype=bt.Order.Limit,
                                      size=abs(sim_order.qty), price=sim_order.price)
                else:
                    order = self.buy(exectype=bt.Order.Limit,
                                     size=sim_order.qty, price=sim_order.price)
                order.addinfo(id=sim_order.id)
                order.addinfo(gridIndex=sim_order.index)
                order.addinfo(ref_id=sim_order.ref_id)
                self.orderIndex += 1
            else:
                break


def run_orders(orders: array, source: str, datapath: str, testname: str, cash: float, fee_rate: float, from_date: datetime, to_date: datetime, output_dir: str):
    cerebro = bt.Cerebro()

    # Add a strategy
    cerebro.addstrategy(
        OrderStrategy,
        orders=orders,
    )

    # Create a Data Feed
    data = bt.feeds.GenericCSVData(
        dataname=datapath,
        fromdate=from_date,
        todate=to_date,
        nullvalue=0.0,
        timeframe=bt.TimeFrame.Minutes,
        compression=60,
        dtformat=('%Y-%m-%d %H:%M:%S'),
        datetime=0,
        open=1,
        high=2,
        low=3,
        close=4,
        volume=5,
        openinterest=-1
    )

    data_name_strs = data._name.split('_')
    symbol = data_name_strs[0]
    period = data_name_strs[1]

    if source == TV:
        data = bt.feeds.GenericCSVData(
            dataname=datapath,
            fromdate=from_date,
            todate=to_date,
            nullvalue=0.0,
            timeframe=bt.TimeFrame.Minutes,
            compression=60,
            dtformat=('%Y-%m-%dT%H:%M:%SZ'),
            datetime=0,
            open=1,
            high=2,
            low=3,
            close=4,
        )
        symbol = data_name_strs[1]
        period = data_name_strs[2]

    # Add the Data Feed to Cerebro
    cerebro.adddata(data)

    cerebro.addobserver(bt.observers.DrawDown)
    cerebro.addanalyzer(bt.analyzers.TimeReturn, timeframe=bt.TimeFrame.Years)

    # Set our desired cash start
    cerebro.broker.setcash(cash)

    # Set the commission - 0.1% ... divide by 100 to remove the %
    cerebro.broker.setcommission(commission=fee_rate)
    cerebro.broker.set_checksubmit(False)  # 不检查当前资产是否满足挂单价值，相当于可用杠杆

    # Run over everything
    results = cerebro.run()
    strat0 = results[0]
    tret_analyzer = strat0.analyzers.getbyname('timereturn')
    timereturns = tret_analyzer.get_analysis()

    file_name = "{}_{}_{}_{}_{}_{}-{}".format(
        source, symbol, period, testname, cash, from_date.strftime("%Y-%m-%d"), to_date.strftime("%Y-%m-%d"))

    print("\n*** %s ***" % file_name)
    print('Starting Portfolio Value: %.2f' % cash)
    print('Final Portfolio Value: %.2f' % cerebro.broker.getvalue())
    print("TimeReturns:")
    for d in timereturns:
        print("{} {:.2f}%".format(d, timereturns[d] * 100))
    print('Total return: %.2f%%' %
          ((cerebro.broker.getvalue() - cash) * 100 / cash))
    print('MaxDrawDown: %.2f%%' % strat0.stats.drawdown.maxdrawdown[0])

    save_trades(output_dir + "/" + file_name, strat0.trades, symbol)

    return cerebro, strat0

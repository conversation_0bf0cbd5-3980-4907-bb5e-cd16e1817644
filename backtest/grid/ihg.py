
import os

from datetime import datetime, timedelta
from strategy import run_strategy, print_trades_stat, print_pnl_stat, get_swap_fee, Long, Short, Trade, TV, MT, Pyramid, Dumbbel, Arithmetic, Geometric
from order import run_orders


class BackResult(object):

    def __init__(self, **kwargs):
        self.strategy_name = kwargs.pop('strategy_name')
        self.symbol = kwargs.pop('symbol')
        self.num = kwargs.pop('num')
        self.high = kwargs.pop('high')
        self.low = kwargs.pop('low')
        self.from_date = kwargs.pop('from_date')
        self.to_date = kwargs.pop('to_date')
        self.days = kwargs.pop('days')
        self.trade_count = kwargs.pop('trade_count')
        self.return_rate = kwargs.pop('return_rate')
        self.swap_rate = kwargs.pop('swap_rate')
        self.annualized_rate = kwargs.pop('annualized_rate', 0)
        self.avg_trade_time = kwargs.pop('avg_trade_time')
        self.max_no_trade_time = kwargs.pop('max_no_trade_time')
        self.max_loss_rate = kwargs.pop('max_loss_rate')
        self.max_drawdown = kwargs.pop('max_drawdown')
        self.positive_pnl_days = kwargs.pop('positive_pnl_days')
        self.nagative_pnl_days = kwargs.pop('nagative_pnl_days')


if __name__ == "__main__":
    output_dir = "../output"

    cash = 5000
    fee_rate = 0.0
    swap_rate = 0.0001
    timezone_offset = -4

    tests = [
        # [MT, "EURGBP", 0.8, 0.93, 30, "2016-07-16", "2022-07-20", "EURGBP_1h_2016-01-01-2022-07-21.csv"],
        [MT, "EURGBP", 0.8, 0.93, 60, "2016-07-16", "2022-07-20", "EURGBP_1h_2016-01-01-2022-07-21.csv"],
        [MT, "EURGBP", 0.8, 0.93, 40, "2016-07-16", "2022-07-20", "EURGBP_1h_2016-01-01-2022-07-21.csv"],
        [MT, "EURGBP", 0.8, 0.93, 30, "2016-07-16", "2022-07-20", "EURGBP_1h_2016-01-01-2022-07-21.csv"],
        [MT, "EURGBP", 0.8, 0.93, 16, "2016-07-16", "2022-07-20", "EURGBP_1h_2016-01-01-2022-07-21.csv"],
        [MT, "EURGBP", 0.8, 0.93, 10, "2016-07-16", "2022-07-20", "EURGBP_1h_2016-01-01-2022-07-21.csv"],
        #[MT, "EURGBP", 0.8, 0.93, 16, "2017-01-01", "2022-07-20", "EURGBP_1h_2016-01-01-2022-07-21.csv"],
        #[MT, "EURGBP", 0.8, 0.93, 16, "2018-01-01", "2022-07-20", "EURGBP_1h_2016-01-01-2022-07-21.csv"],
        #[MT, "EURGBP", 0.8, 0.93, 16, "2019-01-01", "2022-07-20", "EURGBP_1h_2016-01-01-2022-07-21.csv"],
        #[MT, "EURGBP", 0.8, 0.93, 16, "2020-01-01", "2022-07-20", "EURGBP_1h_2016-01-01-2022-07-21.csv"],
        #[MT, "EURGBP", 0.8, 0.93, 16, "2021-01-01", "2022-07-20", "EURGBP_1h_2016-01-01-2022-07-21.csv"],
        
        [MT, "AUDNZD", 0.99937, 1.14738, 60, "2014-06-18", "2022-07-20", "AUDNZD_1h_2014-01-01-2022-07-22.csv"],
        [MT, "AUDNZD", 0.99937, 1.14738, 40, "2014-06-18", "2022-07-20", "AUDNZD_1h_2014-01-01-2022-07-22.csv"],
        [MT, "AUDNZD", 0.99937, 1.14738, 30, "2014-06-18", "2022-07-20", "AUDNZD_1h_2014-01-01-2022-07-22.csv"],
        [MT, "AUDNZD", 0.99937, 1.14738, 16, "2014-06-18", "2022-07-20", "AUDNZD_1h_2014-01-01-2022-07-22.csv"],
        [MT, "AUDNZD", 0.99937, 1.14738, 10, "2014-06-18", "2022-07-20", "AUDNZD_1h_2014-01-01-2022-07-22.csv"],
        
        [MT, "USDCHF", 0.90078, 1.04341, 60, "2015-02-20", "2022-07-20", "USDCHF_1h_2015-01-01-2022-07-22.csv"],
        [MT, "USDCHF", 0.90078, 1.04341, 40, "2015-02-20", "2022-07-20", "USDCHF_1h_2015-01-01-2022-07-22.csv"],
        [MT, "USDCHF", 0.90078, 1.04341, 30, "2015-02-20", "2022-07-20", "USDCHF_1h_2015-01-01-2022-07-22.csv"],
        [MT, "USDCHF", 0.90078, 1.04341, 16, "2015-02-20", "2022-07-20", "USDCHF_1h_2015-01-01-2022-07-22.csv"],
        [MT, "USDCHF", 0.90078, 1.04341, 10, "2015-02-20", "2022-07-20", "USDCHF_1h_2015-01-01-2022-07-22.csv"],
        # Ken's settings
        # [TV, "EURGBP", 0.8, 0.93, 16, "2020-01-01", "2022-06-30", "FOREXCOM_EURGBP_1h_2020-01-01-2022-07-21.csv"],
        # [TV, "EURGBP", 0.8, 0.93, 30, "2020-01-01", "2022-06-30",
        #     "FOREXCOM_EURGBP_1h_2020-01-01-2022-07-21.csv"],
        # Seven's settings
        # [TV, "EURGBP", 0.8, 0.93, 16, "2021-01-15", "2022-06-30",
        #     "FOREXCOM_EURGBP_1h_2020-01-01-2022-07-21.csv"],
    ]

    strategies = [Pyramid, Dumbbel, Arithmetic, Geometric]

    results = []

    for strategy_name in strategies:
        for test in tests:
            source = test[0]
            symbol = test[1]
            low = test[2]
            high = test[3]
            middle = (low + high) / 2.0
            num = int(test[4]/2)
            from_date = test[5]
            to_date = test[6]
            datapath = os.path.join("../data", test[7])

            from_date_time = datetime.strptime(from_date, '%Y-%m-%d')
            to_date_time = datetime.strptime(to_date, '%Y-%m-%d')
            cerebro_long, strat_long = run_strategy(
                strategy_name, source, datapath, cash, fee_rate, Long, low, middle, num, from_date_time, to_date_time, output_dir)
            cerebro_short, strat_short = run_strategy(
                strategy_name, source, datapath, cash, fee_rate, Short, middle, high, num, from_date_time, to_date_time, output_dir)

            print(
                f"\n*** {source}_{symbol}_{low:.05f}_{high:.05f}_{num}_{from_date}_{to_date} ihg ***")
            total_return = cerebro_long.broker.getvalue() + \
                cerebro_short.broker.getvalue() - cash * 2
            print("total return: %.2f(%.2f%%)" %
                  (total_return, total_return * 100 / cash))

            trades, orders = strat_long.trades, strat_long.orders
            short_trades, short_orders = strat_short.trades, strat_short.orders

            trades.extend(short_trades)
            trades.sort(key=lambda x: x.time)
            print_trades_stat(from_date_time, to_date_time,
                              trades, symbol + " ihg")

            # 合并 pnls 并填充缺失的日期
            pnls_long = strat_long.daily_pnls
            pnls_short = strat_short.daily_pnls
            long_i, short_i = 0, 0
            time = from_date_time
            pnls = []
            last_pnl = 0
            while time <= to_date_time:
                current_pnl = 0
                date_long, pnl = pnls_long[long_i]
                if date_long == time.date():
                    current_pnl += pnl
                    long_i += 1
                date_short, pnl = pnls_short[short_i]
                if date_short == time.date():
                    current_pnl += pnl
                    short_i += 1

                if current_pnl == 0:
                    current_pnl = last_pnl

                last_pnl = current_pnl
                pnls.append((time.date(), last_pnl))

                time += timedelta(hours=24)

            print_pnl_stat(pnls, symbol + " ihg")

            swap_fee = get_swap_fee(strat_long.daily_positions, swap_rate)
            print("\nlong swap fee: %.2f(%.2f%%)" %
                  (swap_fee, swap_fee / cash * 100))
            swap_fee = get_swap_fee(strat_short.daily_positions, swap_rate)
            print("short swap fee: %.2f(%.2f%%)" %
                  (swap_fee, swap_fee / cash * 100))

            orders.extend(short_orders)
            orders.sort(key=lambda x: x.time)

            pine_output = os.path.join(
                output_dir, f"{strategy_name}_{source}_{symbol}_{low:.05f}_{high:.05f}_{num}_{from_date}_{to_date}.pine")

            f = open(pine_output, "w")

            f.write(f'//@version=4\nstrategy("ihg-{symbol}-{low:.05f}-{high:.05f}-{num}-{from_date}-{to_date}", default_qty_type=strategy.cash, pyramiding={num}, initial_capital={cash}, commission_type=strategy.commission.percent, commission_value={fee_rate*100}, calc_on_order_fills=true, close_entries_rule="ANY")\n')
            for order in orders:
                f.write(order.to_pine_order(timezone_offset))
            to_date_time_exchange = to_date_time + \
                timedelta(hours=timezone_offset)
            f.write(
                f"""\nif time > timestamp({to_date_time_exchange.year}, {to_date_time_exchange.month}, {to_date_time_exchange.day}, {to_date_time_exchange.hour})\n    strategy.cancel_all()\n""")
            f.write('\nplot(strategy.equity)')
            f.close()

            grids_line_pine_name = f"{strategy_name}_{source}_{symbol}_{low:.05f}_{high:.05f}_{num}"
            f = open(os.path.join(
                output_dir, grids_line_pine_name + "_grid.pine"), "w")
            f.write(
                f'//@version=5\nindicator("grids {grids_line_pine_name}", overlay=true)\n')
            long_prices = strat_long.prices[:-1]
            short_prices = strat_short.prices[1:]
            for i, p in enumerate(long_prices):
                p = f'{p:.05f}'
                label_id = f'long_{i}'
                scripts = f'\nhline({p}, title="{p}", color=color.blue, linestyle=hline.style_dotted, linewidth=2)\nvar label {label_id}=label.new(bar_index, {p}, "{p}")\nlabel.set_textcolor({label_id}, color.white)\nlabel.set_x({label_id}, bar_index)'
                f.write(scripts)

            p = f'{strat_short.prices[0]:.05f}'
            label_id = f'center'
            scripts = f'\nhline({p}, title="{p}", color=color.green, linestyle=hline.style_dotted, linewidth=2)\nvar label {label_id}=label.new(bar_index, {p}, "{p}")\nlabel.set_textcolor({label_id}, color.white)\nlabel.set_x({label_id}, bar_index)'
            f.write(scripts)

            counter = len(long_prices)
            for i, p in enumerate(short_prices):
                p = f'{p:.05f}'
                label_id = f'short_{i}'
                scripts = f'\nhline({p}, title="{p}", color=color.red, linestyle=hline.style_dotted, linewidth=2)\nvar label {label_id}=label.new(bar_index, {p}, "{p}")\nlabel.set_textcolor({label_id}, color.white)\nlabel.set_x({label_id}, bar_index)'
                f.write(scripts)

            f.close()

            cerebro_orders, strat_orders = run_orders(orders, source, datapath, f"sim_orders_{strategy_name}_{low:.05f}_{high:.05f}_{num}", cash,
                                                      fee_rate, from_date_time, to_date_time, output_dir)

            swap_fee = get_swap_fee(strat_orders.daily_positions, swap_rate)
            days = (to_date_time - from_date_time).days
            avg_trade_time, max_no_trade_time = print_trades_stat(
                from_date_time, to_date_time, strat_orders.trades, symbol + " orders")

            # 填充缺失的日期
            time = from_date_time
            pnls = []
            last_pnl = 0
            i = 0
            while time <= to_date_time:
                date, current_pnl = strat_orders.daily_pnls[i]
                if date == time.date():
                    last_pnl = current_pnl
                    i += 1
                pnls.append((time.date(), last_pnl))
                time += timedelta(hours=24)

            max_pnl, min_pnl, positive_pnl_days, nagative_pnl_days = print_pnl_stat(
                pnls, "orders")
            back_result = BackResult(
                strategy_name=strategy_name,
                symbol=symbol,
                num=num,
                high=high,
                low=low,
                from_date=from_date_time,
                to_date=to_date_time,
                days=days,
                trade_count=len(strat_orders.trades),
                return_rate=(cerebro_orders.broker.getvalue() - cash) / cash,
                swap_rate=swap_fee/cash,
                avg_trade_time=avg_trade_time,
                max_no_trade_time=max_no_trade_time,
                max_loss_rate=min_pnl/cash,
                positive_pnl_days=positive_pnl_days,
                nagative_pnl_days=nagative_pnl_days,
                max_drawdown=strat_orders.stats.drawdown.maxdrawdown[0],
            )
            back_result.annualized_rate = (
                back_result.return_rate - back_result.swap_rate) / (days / 365)

            results.append(back_result)

    f = open(output_dir + "/ihg_results.csv", 'w+')
    f.write(
        "品种,策略,单边格数,价格,时间,天数,成交次数,总收益率,swap 费率,净年平均收益率,平均成交时间,最长无成交时间,最大损失,最大回撤,正收益天数,负收益天数\n")
    for r in results:
        f.write("{},{},{},{},{},{},{},{:.2f}%,{:.2f}%,{:.2f}%,{},{},{:.2f}%,{:.2f}%,{},{}\n".format(
            r.symbol,
            r.strategy_name,
            r.num,
            "{}~{}".format(r.low, r.high),
            "{}~{}".format(r.from_date.strftime("%Y-%m-%d"),
                           r.to_date.strftime("%Y-%m-%d")),
            r.days,
            r.trade_count,
            r.return_rate * 100,
            r.swap_rate * 100,
            r.annualized_rate * 100,
            "{}".format(r.avg_trade_time).replace(",", ""),
            "{}".format(r.max_no_trade_time).replace(",", ""),
            r.max_loss_rate * 100,
            r.max_drawdown,
            r.positive_pnl_days,
            r.nagative_pnl_days,
        ))
    f.close()

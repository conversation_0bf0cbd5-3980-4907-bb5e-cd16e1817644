
import pandas as pd
import os
from io import StringIO


def price_correlation():
    files = (
        # ("EURGBP", "EURGBP_1h_2016-01-01-2022-07-21.csv"),
        # ("AUDNZD", "AUDNZD_1h_2014-01-01-2022-07-22.csv"),
        # ("USDCHF", "USDCHF_1h_2015-01-01-2022-07-22.csv"),
        ("EURGBP", "EURGBP_1day_2016-01-01-2022-07-21.csv"),
        ("AUDNZD", "AUDNZD_1day_2014-01-01-2022-08-17.csv"),
        ("USDCHF", "USDCHF_1day_2015-01-01-2022-08-17.csv"),
        ("EURCAD", "EURCAD_1day_2016-01-01-2022-08-18.csv"),
        ("USDCAD", "USDCAD_1day_2016-01-01-2022-08-18.csv"),
        ("USDJPY", "USDJPY_1day_2016-01-01-2022-08-18.csv"),
    )

    price_df = pd.DataFrame()
    for f in files:
        symbol = f[0]
        path = f[1]
        df = pd.read_csv(os.path.join("../data", path), index_col=0)
        df["Change"] = df["Close"].diff()
        price_df[symbol] = df["Change"]

    print(price_df)
    corr = price_df.corr("pearson")
    print(corr)


def simu_correlation(column):
    files = (
        ("AUDNZD", "MT_AUDNZD_1h_sim_orders_0.99937_1.14738_8_5000_2014-06-18-2022-07-20.csv"),
        ("EURGBP", "MT_EURGBP_1h_sim_orders_0.80000_0.93000_8_5000_2016-07-16-2022-07-20.csv"),
        ("USDCHF", "MT_USDCHF_1h_sim_orders_0.90078_1.04341_8_5000_2015-02-20-2022-07-20.csv"),
    )

    pnl_df = None

    for f in files:
        symbol = f[0]
        path = os.path.join("../output", f[1])

        with open(path, "r") as f:
            lines = f.readlines()
            lines = remove_duplicate_lines(lines)
            df = pd.read_csv(StringIO("\n".join(lines)), index_col=0)

            if pnl_df is None:
                pnl_df = pd.DataFrame()
                pnl_df[symbol] = df[column]
            else:
                symbol_df = pd.DataFrame()
                symbol_df[symbol] = df[column]
                pnl_df = pd.merge(pnl_df, symbol_df, on=["Time"], how="outer")

            pnl_df = pnl_df.sort_index()
            pnl_df = pnl_df.fillna(method="pad")

    print(pnl_df)
    corr = pnl_df.corr("pearson")
    print(corr)

    pnl_df.to_csv(os.path.join("../output", f"correllation_{column}.csv"))


def remove_duplicate_lines(lines):
    exist_times = set()
    new_lines = []
    for l in lines:
        time = l.split()[0]
        if time not in exist_times:
            exist_times.add(time)
            new_lines.append(l)
    return new_lines


if __name__ == "__main__":
    price_correlation()
    # simu_correlation("PositionPNL")

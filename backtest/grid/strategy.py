import argparse
import math
import string
import random
from datetime import datetime, timedelta

import backtrader as bt

Long = "long"
Short = "short"
Both = "both"

Buy = "buy"
Sell = "sell"

MT = "MT"  # mt5 download
TV = "TV"  # tradingview export

Pyramid = "Pyramid"  # 等额，等差
Dumbbel = "Dumbbel"  # 等额，等比
Arithmetic = "Arithmetic"  # 等差，等数量
Geometric = "Geometric"  # 等比，等数量

ENABLE_ORDER_LOG = False


def id_generator(size=6, chars=string.ascii_uppercase + string.digits):
    return ''.join(random.choice(chars) for _ in range(size))


# 创建的订单
class Order(object):
    def __init__(self, **kwargs):
        self.is_close = kwargs.pop('is_close', False)
        self.side = kwargs.pop('side', "")
        self.price = kwargs.pop('price', 0.0)
        self.qty = kwargs.pop('qty', 0.0)
        self.index = kwargs.pop('index', 0)
        self.time = kwargs.pop('time', None)
        self.current_position = kwargs.pop('current_position', 0.0)
        self.last_price = kwargs.pop('last_price', 0.0)
        self.position_value = kwargs.pop('position_value', 0.0)
        self.cash = kwargs.pop('cash', 0.0)
        self.worth = kwargs.pop('worth', 0.0)
        self.total_pnl = kwargs.pop('total_pnl', 0.0)
        self.total_pnl_ratio = kwargs.pop('total_pnl_ratio', 0.0)
        self.id = kwargs.pop('id', '')
        self.ref_id = kwargs.pop('ref_id', '')
        self.submit_time = kwargs.pop('submit_time', None)

    def to_pine_order(self, timezone_offset=0):
        time = self.time
        if timezone_offset != 0:
            time = time + timedelta(hours=timezone_offset)

        long_short = "long"
        if self.side == Sell:
            long_short = "short"
        # 注意 pine 脚本严格要求格式和对齐，不要随意加空白字符
        p = f"""\nif year == {time.year} and month == {time.month} and dayofmonth == {time.day} and hour == {time.hour}\n    strategy.entry("G_{self.index}_{self.id}", strategy.{long_short}, qty={abs(self.qty):.0f}, limit={self.price:.5f})\n"""
        if self.is_close:
            p = f"""\nif year == {time.year} and month == {time.month} and dayofmonth == {time.day} and hour == {time.hour}\n    strategy.exit("Close_G_{self.index}_{self.id}", "G_{self.index}_{self.ref_id}", qty={abs(self.qty):.0f}, limit={self.price:.5f})\n"""
        return p


# 已成交交易
class Trade(Order):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class GridStrategy(bt.Strategy):
    params = (
        ("high", 0),
        ("low", 0),
        ("num", 0),
        ("side", Long),
        ("name", Pyramid),
    )

    def log(self, txt, dt=None):
        dt = dt or self.datas[0].datetime.datetime()
        print('[%s] %s' % (dt.isoformat(), txt))

    def __init__(self):
        equalQty = False
        equalValue = False
        if self.p.name == Pyramid:
            deltaPrice = (self.p.high - self.p.low) / self.p.num
            self.prices = [self.p.low]
            for i in range(1, self.p.num+1):
                self.prices.append(self.prices[i-1] + deltaPrice)
            equalValue = True
        elif self.p.name == Dumbbel:
            self.prices = [self.p.low]
            ratio = math.pow(self.p.high/self.p.low, 1.0/float(self.p.num))
            for i in range(1, self.p.num+1):
                self.prices.append(self.prices[i-1] * ratio)
            equalValue = True
        elif self.p.name == Arithmetic:
            deltaPrice = (self.p.high - self.p.low) / self.p.num
            self.prices = [self.p.low]
            for i in range(1, self.p.num+1):
                self.prices.append(self.prices[i-1] + deltaPrice)
            equalQty = True
        elif self.p.name == Geometric:
            self.prices = [self.p.low]
            ratio = math.pow(self.p.high/self.p.low, 1.0/float(self.p.num))
            for i in range(1, self.p.num+1):
                self.prices.append(self.prices[i-1] * ratio)
            equalQty = True
        else:
            print("strategy %s not supported yet", self.p.name)
            exit()

        self.buyQtys = []
        self.saleQtys = []
        self.cash = self.broker.getvalue()

        if equalValue:
            value = int(self.broker.getvalue() / self.p.num)
            for i in range(0, self.p.num):
                qty = value / self.prices[i]
                if self.p.side == Short:
                    qty = value / self.prices[i+1]

                qty = math.floor(qty * 100) / 100
                qty = float("{:.2f}".format(qty))

                self.buyQtys.append(qty)
                self.saleQtys.append(qty)
        elif equalQty:
            priceSum = sum(self.prices[:self.p.num])
            if self.p.side == Short:
                priceSum = sum(self.prices[1:])
            qty = self.broker.getvalue() / priceSum
            for i in range(0, self.p.num):
                qty = math.floor(qty * 100) / 100
                qty = float("{:.2f}".format(qty))
                self.buyQtys.append(qty)
                self.saleQtys.append(qty)

        for i, p in enumerate(self.prices):
            self.prices[i] = float("{:.5f}".format(p))

        print("\n*** %s grid prices & qtys ***" % self.p.name)
        print("prices: ", self.prices)
        print("buyQtys: ", self.buyQtys)
        print("saleQtys: ", self.saleQtys, "\n")

        self.current_position = 0
        self.orders = []
        self.trades = []
        self.daily_pnls = []
        self.daily_positions = []

    def start(self):
        i = self.p.num - 1
        while i >= 0:
            if self.p.side == Short:
                order = self.sell(exectype=bt.Order.Limit,
                                  size=self.saleQtys[i], price=self.prices[i+1])
            else:
                order = self.buy(exectype=bt.Order.Limit,
                                 size=self.buyQtys[i], price=self.prices[i])
            order.addinfo(id=id_generator())
            order.addinfo(gridIndex=i)
            order.addinfo(initOrder=True)
            i -= 1

    # 记录订单/成交
    def record_order(self, order):
        direction = Buy if order.isbuy() else Sell
        is_close = False
        if (self.p.side == Long and direction == Sell) or (self.p.side == Short and direction == Buy):
            is_close = True
        pnl = self.broker.getvalue() - self.cash
        last_price = self.data.close[0]

        if order.status == order.Completed:
            if ENABLE_ORDER_LOG:
                self.log("{} order[{}] traded, qty: {}, price: {}".format(
                    direction,
                    order.info.gridIndex,
                    order.executed.size,
                    order.executed.price,
                ))

            self.current_position += order.executed.size
            self.trades.append(Trade(
                side=direction,
                price=order.executed.price,
                qty=order.executed.size,
                index=order.info.gridIndex,
                time=self.data0.datetime.datetime(),
                is_close=is_close,
                current_position=self.current_position,
                total_pnl=pnl,
                total_pnl_ratio=pnl / self.cash,
                id=order.info.id,
                ref_id=order.info.ref_id or "",
                last_price=last_price,
                position_value=self.current_position * last_price,
                cash=self.broker.getcash(),
                worth=self.broker.getvalue()
            ))

        elif order.status == order.Submitted:
            time = self.data0.datetime.datetime()
            submit_time = order.info.submit_time
            if order.info.initOrder:  # 让初始挂单时间前移
                time = time - timedelta(hours=1)
                submit_time = time

            if ENABLE_ORDER_LOG:
                self.log("{} order[{}] submitted, qty: {} price: {}".format(
                    direction,
                    order.info.gridIndex,
                    order.size,
                    order.price,
                ), time)

            self.orders.append(Order(
                side=direction,
                price=order.price,
                qty=order.size,
                index=order.info.gridIndex,
                time=time,
                submit_time=submit_time,
                is_close=is_close,
                current_position=self.current_position,
                total_pnl=pnl,
                total_pnl_ratio=pnl / self.cash,
                id=order.info.id,
                ref_id=order.info.ref_id or "",
                last_price=last_price,
                position_value=self.current_position * last_price,
                cash=self.broker.getcash(),
                worth=self.broker.getvalue()
            ))
        else:
            return

    def notify_order(self, order):
        orderIndex = order.info.gridIndex
        if order.status in [order.Completed]:
            self.record_order(order)

            if order.isbuy():
                # self.log(
                #     'BUY EXECUTED, Price: %.5f, Size: %.0f, Cost: %.2f, Comm: %.2f, position size: %.0f, portfolio value: %.2f' %
                #     (order.executed.price,
                #      order.executed.size,
                #      order.executed.value,
                #      order.executed.comm,
                #      self.position.size,
                #      self.broker.getvalue()))

                newOrder = self.sell(exectype=bt.Order.Limit,
                                     size=self.saleQtys[orderIndex], price=self.prices[orderIndex+1])

            else:  # Sell
                # self.log('SELL EXECUTED, Price: %.5f, Size: %.0f, Cost: %.2f, Comm: %.2f, position size: %.0f, portfolio value: %.2f' %
                #          (order.executed.price,
                #           order.executed.size,
                #           order.executed.value,
                #           order.executed.comm,
                #           self.position.size,
                #           self.broker.getvalue()))

                newOrder = self.buy(exectype=bt.Order.Limit,
                                    size=self.buyQtys[orderIndex], price=self.prices[orderIndex])

            newOrder.addinfo(gridIndex=orderIndex)
            newOrder.addinfo(id=id_generator())
            newOrder.addinfo(ref_id=order.info.id)
            newOrder.addinfo(submit_time=self.datas[0].datetime.datetime())

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('{} Order[{}] Canceled({})/Margin({})/Rejected({}) status: {}'.format(
                     "buy" if order.isbuy() else "sell",
                     orderIndex,
                     order.Canceled, order.Margin, order.Rejected,
                     order.status,
                     ))
            exit()
        elif order.status == order.Submitted:
            self.record_order(order)

    def notify_trade(self, trade):
        if not trade.isclosed:
            return

        # self.log('OPERATION PROFIT, GROSS %.4f, NET %.4f' %
        #          (trade.pnl, trade.pnlcomm))

    def next(self):
        dt = self.datas[0].datetime.datetime()

        if dt.hour == 0:
            self.daily_pnls.append((
                dt.date(),
                self.broker.getvalue() - self.cash
            ))

            self.daily_positions.append((
                dt.date(),
                self.current_position,
                self.data.close[0],
            ))

        if dt.month == 12 and dt.day == 31 and dt.hour == 16:
            print("\n*** END OF YEAR %d ***" % dt.year)
            print('DrawDown: %.2f%%' % self.stats.drawdown.drawdown[-1])
            print('MaxDrawDown: %.2f%%\n' %
                  self.stats.drawdown.maxdrawdown[-1])


def save_trades(file_name, trades, symbol):
    f = open(file_name + ".csv", 'w+')
    f.write(
        "Time,Symbol,Side,Price,Qty,Index,ID,RefID,CurrentPosition,LastPrice,PositionValue,EntryValue,PositionPNL,Cash,Worth,TotalPnl,TotalPnlRatio\n")
    position_trades = []
    last_position = 0
    for trade in trades:
        if abs(trade.current_position) > abs(last_position):  # 加仓
            position_trades.append(trade)
        else:  # 减仓，先进后出
            position_trades = position_trades[:-1]
        last_position = trade.current_position
        position_entry_value = 0
        for t in position_trades:
            position_entry_value += t.price * t.qty

        f.write("{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{}\n".format(
            trade.time.strftime('%Y-%m-%d %H:%M:%S'),
            symbol,
            trade.side,
            "{:.5f}".format(trade.price),
            trade.qty,
            trade.index,
            trade.id,
            trade.ref_id,
            "{:.2f}".format(trade.current_position),
            "{:.5f}".format(trade.last_price),
            "{:.2f}".format(trade.position_value),
            "{:.2f}".format(position_entry_value),
            "{:.2f}".format(trade.position_value - position_entry_value),
            "{:.2f}".format(trade.cash),
            "{:.2f}".format(trade.worth),
            "{:.2f}".format(trade.total_pnl),
            "{:.6f}".format(trade.total_pnl_ratio),
        ))
    f.close()


def run_strategy(strategy: str, source: str, datapath: str, cash: float, fee_rate: float, side: str, low: float, high: float, num: int, from_date: datetime, to_date: datetime, output_dir: str):
    cerebro = bt.Cerebro()

    # Add a strategy
    cerebro.addstrategy(
        GridStrategy,
        high=high,
        low=low,
        num=num,
        side=side,
        name=strategy,
    )

    # Create a Data Feed
    data = bt.feeds.GenericCSVData(
        dataname=datapath,
        fromdate=from_date,
        todate=to_date,
        nullvalue=0.0,
        timeframe=bt.TimeFrame.Minutes,
        compression=60,
        dtformat=('%Y-%m-%d %H:%M:%S'),
        datetime=0,
        open=1,
        high=2,
        low=3,
        close=4,
        volume=5,
        openinterest=-1
    )
    data_name_strs = data._name.split('_')
    symbol = data_name_strs[0]
    period = data_name_strs[1]

    if source == TV:
        data = bt.feeds.GenericCSVData(
            dataname=datapath,
            fromdate=from_date,
            todate=to_date,
            nullvalue=0.0,
            timeframe=bt.TimeFrame.Minutes,
            compression=60,
            dtformat=('%Y-%m-%dT%H:%M:%SZ'),
            datetime=0,
            open=1,
            high=2,
            low=3,
            close=4,
        )
        symbol = data_name_strs[1]
        period = data_name_strs[2]

    # Add the Data Feed to Cerebro
    cerebro.adddata(data)

    cerebro.addobserver(bt.observers.DrawDown)
    cerebro.addanalyzer(bt.analyzers.TimeReturn, timeframe=bt.TimeFrame.Years)

    # Set our desired cash start
    cerebro.broker.setcash(cash)

    # cheat on close 以收盘价成交
    # cerebro.broker.set_coc(True)

    # Set the commission - 0.1% ... divide by 100 to remove the %
    cerebro.broker.setcommission(commission=fee_rate)

    # Run over everything
    results = cerebro.run()
    strat0 = results[0]
    tret_analyzer = strat0.analyzers.getbyname('timereturn')
    timereturns = tret_analyzer.get_analysis()

    file_name = "{}_{}_{}_{}_{}_{}_{}_{}-{}_{}-{}".format(
        strategy, source, symbol, period, side, cash, num,
        low, high, from_date.strftime("%Y-%m-%d"), to_date.strftime("%Y-%m-%d"))

    print("\n*** %s ***" % file_name)
    print('Starting Portfolio Value: %.2f' % cash)
    print('Final Portfolio Value: %.2f' % cerebro.broker.getvalue())
    print("TimeReturns:")
    for d in timereturns:
        print("{} {:.2f}%".format(d, timereturns[d] * 100))
    print('Total return: %.2f%%' %
          ((cerebro.broker.getvalue() - cash) * 100 / cash))
    print('MaxDrawDown: %.2f%%' % strat0.stats.drawdown.maxdrawdown[0])

    print_trades_stat(from_date, to_date, strat0.trades, side)
    print_pnl_stat(strat0.daily_pnls, side)

    save_trades(output_dir + "/" + file_name, strat0.trades, symbol)

    return cerebro, strat0


def print_pnl_stat(pnls, name):
    max_pnl = None
    max_pnl_date = None
    min_pnl = None
    min_pnl_date = None
    positive_pnl_days = 0
    nagative_pnl_days = 0
    for (date, pnl) in pnls:
        if max_pnl is None:
            max_pnl = pnl
            max_pnl_date = date
        if min_pnl is None:
            min_pnl = pnl
            min_pnl_date = date

        if pnl > 0:
            positive_pnl_days += 1
        else:
            nagative_pnl_days += 1

        if pnl > max_pnl:
            max_pnl = pnl
            max_pnl_date = date

        if pnl < min_pnl:
            min_pnl = pnl
            min_pnl_date = date

    print("\n*** PNL Stat {} ***".format(name))
    print("max pnl: {:.2f} @ {}".format(max_pnl, max_pnl_date))
    print("min pnl: {:.2f} @ {}".format(min_pnl, min_pnl_date))
    print("positive pnl days: {}".format(positive_pnl_days))
    print("nagative pnl days: {}".format(nagative_pnl_days))
    return max_pnl, min_pnl, positive_pnl_days, nagative_pnl_days


def get_swap_fee(daily_positions, rate):
    position_night_sum = 0
    for (date, pos, price) in daily_positions:
        # if abs(pos) > 0.01:
        #     print("{} pos: {:.2f} price: {}".format(date, pos, price))
        position_night_sum += abs(pos) * price
    return position_night_sum * rate


def print_trades_stat(from_date, to_date, trades, name):
    if len(trades) == 0:
        return

    duration = to_date - from_date
    avg_trade_time = duration / len(trades)
    max_no_trade_time = trades[0].time - from_date
    last_trade_time = trades[0].time
    for trade in trades[1:]:
        max_no_trade_time = max(
            max_no_trade_time, trade.time - last_trade_time)
        last_trade_time = trade.time

    max_no_trade_time = max(
        max_no_trade_time, to_date - last_trade_time)

    if "ihg" in name:
        print("\n*** Trade Summary %s ***" % name)
    else:
        print("\n*** Trade Stat %s ***" % name)
    print("{} ~ {}, {} days".format(
        from_date.date(), to_date.date(), duration.days))
    print("trade count: ", len(trades))
    print("avg_trade_time: ", avg_trade_time)
    print("max_no_trade_time: ", max_no_trade_time)

    return avg_trade_time, max_no_trade_time


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    parser.add_argument('--datapath', required=True, help="csv data path")

    parser.add_argument('--from_date', required=True,
                        type=lambda s: datetime.strptime(s, '%Y-%m-%d'), help="from date")

    parser.add_argument('--to_date', required=True,
                        type=lambda s: datetime.strptime(s, '%Y-%m-%d'), help="to date")

    parser.add_argument('--high', required=True, type=float,
                        help="the highest price")

    parser.add_argument('--low', required=True, type=float,
                        help="the lowest price")

    parser.add_argument('--num', required=True,
                        type=int, help="grid num")

    parser.add_argument('--cash', default=100000, required=False,
                        type=int, help="init cash")

    parser.add_argument('--side', default=Long,
                        choices=[Long, Short, Both], required=False, help='grid side')

    parser.add_argument('--fee_rate', default=0.0, required=False, type=float)

    parser.add_argument('--plot', default=False, required=False, type=bool,
                        help='show plot')

    parser.add_argument('--output_dir', default='.')

    parser.add_argument('--source', default=MT,
                        choices=[MT, TV], required=False, help='data source')

    return parser.parse_args()


if __name__ == '__main__':
    args = parse_args()

    if args.side == Both:
        print("side both not supported yet")
        exit()

    cerebro, strat = run_strategy(Pyramid, args.source, args.datapath, args.cash, args.fee_rate,
                                  args.side, args.low, args.high, args.num, args.from_date, args.to_date, args.output_dir)

    if args.plot:
        cerebro.plot()

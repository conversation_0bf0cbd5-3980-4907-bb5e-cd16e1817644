package license

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/AlecAivazis/survey/v2"
	_ "github.com/wizhodl/encembed/pkg/encembed" // 防止 go mod tidy 删除依赖
)

//go:generate go run github.com/wizhodl/encembed -i licenses.json -decvarname licenseStr -pkgname license

type SecretString string

// custom stringer to hide the secret
func (this SecretString) String() string {
	length := len(this)
	b := []byte(this)
	if length > 14 {
		return fmt.Sprintf("%s***%s", b[:5], b[len(this)-5:])
	}
	if length > 8 {
		return fmt.Sprintf("%s***%s", b[:3], b[len(this)-3:])
	}
	if length > 4 {
		return fmt.Sprintf("%s***%s", b[:1], b[len(this)-1:])
	}
	return "***"
}

// custom json marshaler to reveal the secret
func (this SecretString) MarshalJSON() ([]byte, error) {
	return json.Marshal(string(this))
}

type LicenseProject string

const (
	LicenseProjectQuickShare LicenseProject = "QuickShare"
	LicenseProjectCopier     LicenseProject = "copier"
	LicenseProjectPgate      LicenseProject = "pgate"
)

type UserLicense struct {
	Email string         `json:"email"`
	HwIDs []SecretString `json:"hwids"`
	Keys  []SecretString `json:"keys"` // keys
}

var _licenses map[LicenseProject][]UserLicense

func init() {
	err := json.Unmarshal(licenseStr, &_licenses)
	if err != nil {
		log.Panicf("unmarsal licenses failed")
	}
}

type TimeLockLicenseManager struct {
	BuildTime     *time.Time
	project       LicenseProject
	maxExpireTime *time.Time
	expireTime    *time.Time
	userLicense   *UserLicense
	lockDuration  time.Duration
	licensePath   string
	hwID          SecretString
}

func NewTimeLockLicenseManager(project LicenseProject, buildTimeStr string, maxExpireTime *time.Time, lockDuration time.Duration, licensePath string) (*TimeLockLicenseManager, error) {
	var buildTime *time.Time
	if buildTimeStr != "" {
		loc, err := time.LoadLocation("Asia/Shanghai") // 设置为上海时区，即东八区
		if err != nil {
			// Fallback to fixed +08:00 offset if Asia/Shanghai location loading fails (common on Windows)
			loc = time.FixedZone("CST", 8*3600) // CST = China Standard Time, +8 hours
		}
		t, err := time.ParseInLocation("2006-01-02.15:04:05", buildTimeStr, loc)
		if err != nil {
			fmt.Printf("failed to parse build time: %s", err)
			return nil, fmt.Errorf("failed to parse build time: %w", err)
		}
		buildTime = &t
	}

	_, ok := _licenses[project]
	if !ok {
		return nil, fmt.Errorf("project %s not found", project)
	}

	hwID, err := GetHwid(project)
	if err != nil {
		log.Panicf("get hwid failed, project: %s, error: %s", project, err)
	}

	manager := &TimeLockLicenseManager{
		project:       project,
		BuildTime:     buildTime,
		expireTime:    buildTime, // 初始状态下，过期时间和构建时间相同
		maxExpireTime: maxExpireTime,

		lockDuration: lockDuration,
		licensePath:  licensePath,
		hwID:         hwID,
	}
	manager.updateExpireTimeFromLicenseFile()
	return manager, nil
}

func (this *TimeLockLicenseManager) ExpireTime() *time.Time {
	return this.expireTime
}

func (this *TimeLockLicenseManager) PromptForLicense() {
	if this.licensePath == "" {
		return
	}

	if this.userLicense == nil {
		this.userLicense = &UserLicense{}
	}

	if this.userLicense.Email == "" {
		this.promptForEmail()
	}

	this.promptForKeys()

	file, err := os.OpenFile(this.licensePath, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
	if err != nil {
		log.Printf("open license file failed: %s", err)
		return
	}
	defer file.Close()

	jsonData, err := json.MarshalIndent(this.userLicense, "", "    ")
	if err != nil {
		log.Printf("marshal license failed: %s", err)
		return
	}
	file.Write(jsonData)

	this.updateExpireTimeFromLicenseFile()
	log.Printf("new expire time: %s\n", this.expireTime)
}

func (this *TimeLockLicenseManager) promptForEmail() {
	var email string
	for {
		err := survey.AskOne(&survey.Input{
			Message: "Please input your email address:",
		}, &email)
		if err != nil {
			log.Printf("invalid email address: %s", err)
			continue
		}
		break
	}
	this.userLicense.Email = email
}

func (this *TimeLockLicenseManager) promptForKeys() {
	var keys []SecretString
	for {
		var key string
		prompt := &survey.Password{
			Message: fmt.Sprintf("Please input your license key %d (Empty to finish):", len(keys)+1),
		}
		err := survey.AskOne(prompt, &key)
		if err != nil {
			fmt.Println("error:", err)
			break
		}

		if key == "" && len(keys) > 0 {
			break
		} else if key == "" && len(keys) == 0 {
			fmt.Println("Please input at least one license key")
			continue
		}

		keys = append(keys, SecretString(key))
	}
	this.userLicense.Keys = append(this.userLicense.Keys, keys...)
}

func (this *TimeLockLicenseManager) updateExpireTimeFromLicenseFile() {
	if this.licensePath == "" {
		return
	}

	file, err := os.Open(this.licensePath)
	if err != nil {
		log.Printf("open license file failed: %s", err)
		return
	}
	defer file.Close()

	license := &UserLicense{}
	err = json.NewDecoder(file).Decode(license)
	if err != nil {
		log.Printf("decode license failed: %s", err)
		return
	}

	this.userLicense = license

	if len(license.Keys) == 0 {
		return
	}

	// 去除 license keys 中的重复项
	email := license.Email

	for _, key := range license.Keys {
		exist := false
		for _, k := range license.Keys {
			if k == key {
				exist = true
				break
			}
		}
		if !exist {
			license.Keys = append(license.Keys, key)
		}
	}

	// 累加所有的 key 的 lockDuration
	var lockDuration time.Duration
	for _, key := range license.Keys {
		if err := this.isLicenseKeyValid(email, this.hwID, key); err == nil {
			lockDuration += this.lockDuration
		} else {
			log.Printf("license key not valid: %s, error: %s", key, err)
		}
	}

	// 计算过期时间
	if lockDuration > 0 && this.BuildTime != nil {
		expireTime := this.BuildTime.Add(lockDuration)
		if this.maxExpireTime != nil && expireTime.After(*this.maxExpireTime) {
			expireTime = *this.maxExpireTime
		}
		this.expireTime = &expireTime
	}
}

func (this *TimeLockLicenseManager) isLicenseKeyValid(email string, hwID, key SecretString) error {
	licenses, ok := _licenses[this.project]
	if !ok {
		return fmt.Errorf("project %s not found in licenses", this.project)
	}

	// Check if email exists
	emailFound := false
	var userLicense *UserLicense
	for _, license := range licenses {
		if license.Email == email {
			emailFound = true
			userLicense = &license
			break
		}
	}
	if !emailFound {
		return fmt.Errorf("email %s not found in licenses", email)
	}

	// Check if hwID exists for this email
	hwIDFound := false
	for _, hwid := range userLicense.HwIDs {
		if hwid == hwID {
			hwIDFound = true
			break
		}
	}
	if !hwIDFound {
		return fmt.Errorf("hwID %s not found for email %s", hwID, email)
	}

	// Check if key exists for this email/hwID combination
	for _, k := range userLicense.Keys {
		if k == key {
			// log.Printf("license key valid: %s, email: %s, hwid: %s", key, email, hwID)
			return nil // Valid license
		}
	}

	return fmt.Errorf("license key %s not found for email %s and hwID %s", key, email, hwID)
}

func (this *TimeLockLicenseManager) Check() error {
	now := time.Now()
	// log.Printf("build time: %s, expire time: %s, max expire time: %s, now: (%s)", this.buildTime, this.expireTime, this.maxExpireTime, now)

	if this.BuildTime == nil || this.expireTime == nil {
		return fmt.Errorf("build time or expire time is nil")
	}

	if now.Before(*this.BuildTime) {
		log.Printf("license before build time: %s, now: (%s)", *this.BuildTime, now)
		return fmt.Errorf("license before build time: %s, now: (%s)", *this.BuildTime, now)
	}

	if now.After(*this.expireTime) {
		log.Printf("license after expire time: %s, now: (%s)", *this.expireTime, now)
		return fmt.Errorf("license after expire time: %s, now: (%s)", *this.expireTime, now)
	}

	if this.maxExpireTime != nil && now.After(*this.maxExpireTime) {
		log.Printf("license after max expire time: %s, now: (%s)", *this.maxExpireTime, now)
		return fmt.Errorf("license after max expire time: %s, now: (%s)", *this.maxExpireTime, now)
	}

	return nil
}

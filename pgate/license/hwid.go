package license

import (
	"errors"

	"github.com/denisbrodbeck/machineid"
)

func GetHwid(appName LicenseProject) (SecretString, error) {
	appKeys := map[LicenseProject]string{
		LicenseProjectQuickShare: "328BE402-8090-461F-9A5F-949F26B23595",
		LicenseProjectPgate:      "e8e9b822-61a4-4c5c-8f9a-7f9ac36ff163",
		LicenseProjectCopier:     "ba5abef2-a6a3-4cc1-ab2e-e7934f05f2ee"}
	if appKey, found := appKeys[appName]; !found {
		return "", errors.New("invalid app name")
	} else {
		appKey += appKey
		id, err := machineid.ProtectedID(appKey)
		return SecretString(id), err
	}
}

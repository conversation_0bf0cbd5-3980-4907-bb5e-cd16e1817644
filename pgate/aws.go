package pgate

import (
	"context"
	"fmt"
	"io"
	"os"
	"sort"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/costexplorer"
	ceTypes "github.com/aws/aws-sdk-go-v2/service/costexplorer/types"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

const DEFAULT_REGION = "ap-northeast-2" // 所有接口都需要指定 Region，即使实际上不需要，如查询所有 Regions. 如果未设置会从环境变量、~/.aws/config 中读取

func convertAWSInstanceState(state types.InstanceStateName) InstanceState {
	switch state {
	case types.InstanceStateNamePending:
		return InstanceStatePending
	case types.InstanceStateNameRunning:
		return InstanceStateRunning
	case types.InstanceStateNameShuttingDown:
		return InstanceStateShutting
	case types.InstanceStateNameTerminated:
		return InstanceStateTerminated
	case types.InstanceStateNameStopping:
		return InstanceStateStopping
	case types.InstanceStateNameStopped:
		return InstanceStateStopped
	default:
		return InstanceStateUnknown
	}
}

func (a *AWSAccount) GetEC2Client(region string) (*ec2.Client, error) {
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(a.AccessKeyID, string(a.SecretAccessKey), "")),
	)
	if err != nil {
		return nil, fmt.Errorf("加载 AWS SDK 配置失败: %v", err)
	}

	return ec2.NewFromConfig(cfg), nil
}

func (p *Proxy) createAWSInstance() error {
	awsTemplate := p.template.MachineTemplate.(*AWSTemplate)

	ec2Client, err := awsTemplate.Account.GetEC2Client(awsTemplate.Region)
	if err != nil {
		return err
	}

	// 创建 EC2 实例的请求参数
	runInstancesInput := &ec2.RunInstancesInput{
		ImageId:      aws.String(awsTemplate.AMI),                  // 替换为你选择的 AMI ID
		InstanceType: types.InstanceType(awsTemplate.InstanceType), // 实例类型
		MinCount:     aws.Int32(1),                                 // 最小实例数
		MaxCount:     aws.Int32(1),                                 // 最大实例数
		KeyName:      aws.String(awsTemplate.SSHKey.KeyName),       // 指定密钥对名称
		SecurityGroupIds: []string{
			awsTemplate.SecurityGroupID, // 替换为你的安全组ID
		},
		TagSpecifications: []types.TagSpecification{
			{
				ResourceType: types.ResourceTypeInstance,
				Tags: []types.Tag{
					{
						Key:   aws.String("Name"),
						Value: aws.String(fmt.Sprintf("Proxy_%s", p.Name)), // 实例的名称
					},
				},
			},
		},
	}

	// 调用 RunInstances 创建实例
	result, err := ec2Client.RunInstances(context.TODO(), runInstancesInput)
	if err != nil {
		return err
	}

	instanceId := *result.Instances[0].InstanceId

	// 创建 DescribeInstances 的输入
	describeInput := &ec2.DescribeInstancesInput{
		InstanceIds: []string{instanceId},
	}

	// 等待实例状态变为 "running"
	// 注意：这里使用了 DescribeInstanceStatus 而不是 DescribeInstances
	waiter := ec2.NewInstanceRunningWaiter(ec2Client)
	waitErr := waiter.Wait(context.TODO(), &ec2.DescribeInstancesInput{
		InstanceIds: []string{instanceId},
	}, 5*time.Minute) // 等待时间上限为 5 分钟
	if waitErr != nil {
		return waitErr
	}

	// 查询实例
	describeResult, err := ec2Client.DescribeInstances(context.TODO(), describeInput)
	if err != nil {
		return err
	}

	// 遍历查询结果，获取公有 IP 地址
	for _, reservation := range describeResult.Reservations {
		for _, instance := range reservation.Instances {
			if *instance.InstanceId == instanceId {
				p.IP = *instance.PublicIpAddress
				p.InstanceIP = p.IP
				p.InstanceState = convertAWSInstanceState(instance.State.Name)
				break
			}
		}
	}

	if p.IP == "" {
		return fmt.Errorf("can't get public IP")
	}

	p.InstanceID = instanceId

	return nil
}

func (p *Proxy) deleteAWSInstance() error {
	if p.InstanceID == "" {
		return nil
	}

	if p.InstanceState == InstanceStateTerminated || p.InstanceState == InstanceStateNotFound {
		return nil
	}

	awsTemplate := p.template.MachineTemplate.(*AWSTemplate)

	ec2Client, err := awsTemplate.Account.GetEC2Client(awsTemplate.Region)
	if err != nil {
		return err
	}

	// 创建 TerminateInstances 的输入
	terminateInput := &ec2.TerminateInstancesInput{
		InstanceIds: []string{p.InstanceID},
	}

	// 调用 TerminateInstances 终止实例
	_, err = ec2Client.TerminateInstances(context.TODO(), terminateInput)
	if err != nil {
		return err
	}

	// 等待实例状态变为 "terminated"
	// 注意：这里使用了 DescribeInstanceStatus 而不是 DescribeInstances
	waiter := ec2.NewInstanceTerminatedWaiter(ec2Client)
	waitErr := waiter.Wait(context.TODO(), &ec2.DescribeInstancesInput{
		InstanceIds: []string{p.InstanceID},
	}, 5*time.Minute) // 等待时间上限为 5 分钟
	if waitErr != nil {
		return waitErr
	}

	p.InstanceState = InstanceStateTerminated
	p.InstanceIP = ""
	p.IP = ""

	return nil
}

func (t *AWSTemplate) Validate() error {
	ec2Client, err := t.Account.GetEC2Client(t.Region)
	if err != nil {
		return err
	}

	// 验证 Region 通过检查是否能列出可用区
	_, err = ec2Client.DescribeAvailabilityZones(context.TODO(), &ec2.DescribeAvailabilityZonesInput{})
	if err != nil {
		return fmt.Errorf("验证 Region 失败: %v", err)
	}

	// 验证 AMIID 通过尝试获取AMI信息
	_, err = ec2Client.DescribeImages(context.TODO(), &ec2.DescribeImagesInput{
		ImageIds: []string{t.AMI},
	})
	if err != nil {
		return fmt.Errorf("验证 AMIID 失败: %v", err)
	}

	// 验证 KeyName 通过尝试获取密钥对信息
	_, err = ec2Client.DescribeKeyPairs(context.TODO(), &ec2.DescribeKeyPairsInput{
		KeyNames: []string{t.SSHKey.KeyName},
	})
	if err != nil {
		return fmt.Errorf("验证 KeyName 失败: %v", err)
	}

	// 验证 SecurityGroupID 通过尝试获取安全组信息
	_, err = ec2Client.DescribeSecurityGroups(context.TODO(), &ec2.DescribeSecurityGroupsInput{
		GroupIds: []string{t.SecurityGroupID},
	})
	if err != nil {
		return fmt.Errorf("验证 SecurityGroupID 失败: %v", err)
	}

	return nil
}

// ListProxyInstances 获取 Region 下所有以 "Proxy_" 开头的实例
func (t *AWSTemplate) ListProxyInstances() ([]types.Instance, error) {
	ec2Client, err := t.Account.GetEC2Client(t.Region)
	if err != nil {
		return nil, err
	}

	// 调用 DescribeInstances 来获取所有实例
	resp, err := ec2Client.DescribeInstances(context.TODO(), &ec2.DescribeInstancesInput{
		Filters: []types.Filter{
			{
				Name:   aws.String("tag:Name"),
				Values: []string{"Proxy_*"},
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("获取实例列表失败: %v", err)
	}

	// 筛选出名称以 "Proxy_" 开头的实例
	var proxyInstances []types.Instance
	for _, reservation := range resp.Reservations {
		for _, instance := range reservation.Instances {
			for _, tag := range instance.Tags {
				if *tag.Key == "Name" && len(*tag.Value) > 6 && (*tag.Value)[0:6] == "Proxy_" {
					proxyInstances = append(proxyInstances, instance)
					break
				}
			}
		}
	}

	return proxyInstances, nil
}

func (a *AWSAccount) GetReservedInstances(region string) ([]types.ReservedInstances, error) {
	ec2Client, err := a.GetEC2Client(region)
	if err != nil {
		return nil, err
	}

	resp, err := ec2Client.DescribeReservedInstances(context.TODO(), &ec2.DescribeReservedInstancesInput{
		Filters: []types.Filter{
			{
				Name:   aws.String("state"),
				Values: []string{"active"},
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("获取预留实例列表失败: %v", err)
	}

	return resp.ReservedInstances, nil
}

type AWSReservedInstancesWithRegion struct {
	Region string
	types.ReservedInstances
}

func (a *AWSAccount) GetAllReservedInstances() ([]AWSReservedInstancesWithRegion, error) {
	ec2Client, err := a.GetEC2Client(DEFAULT_REGION)
	if err != nil {
		return nil, err
	}

	result, err := ec2Client.DescribeRegions(context.TODO(), &ec2.DescribeRegionsInput{})
	if err != nil {
		return nil, err
	}

	regions := make([]string, 0, len(result.Regions))
	for _, region := range result.Regions {
		regions = append(regions, *region.RegionName)
	}

	var allInstances []AWSReservedInstancesWithRegion
	for _, region := range regions {
		instances, err := a.GetReservedInstances(region)
		if err != nil {
			return nil, err
		}

		if len(instances) == 0 {
			continue
		}

		// 按到期时间排序
		sort.Slice(instances, func(i, j int) bool {
			return instances[i].End == nil || (instances[j].End != nil && instances[i].End.Before(*instances[j].End))
		})

		for _, instance := range instances {
			allInstances = append(allInstances, AWSReservedInstancesWithRegion{
				Region:            region,
				ReservedInstances: instance,
			})
		}
	}

	return allInstances, nil
}

func (a *AWSAccount) UploadFileToS3(region, bucket, key, filePath string) error {
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(a.AccessKeyID, string(a.SecretAccessKey), "")),
	)
	if err != nil {
		return err
	}

	s3Client := s3.NewFromConfig(cfg)

	// 检查 Bucket 是否存在
	_, err = s3Client.HeadBucket(context.TODO(), &s3.HeadBucketInput{
		Bucket: aws.String(bucket),
	})
	if err != nil {
		return fmt.Errorf("bucket %s not exists or not accessible: %v", bucket, err)
	}

	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = s3Client.PutObject(context.TODO(), &s3.PutObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
		Body:   file,
	})
	return err
}

func (a *AWSAccount) DownloadFileFromS3(region, bucket, key string) ([]byte, error) {
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(a.AccessKeyID, string(a.SecretAccessKey), "")),
	)
	if err != nil {
		return nil, err
	}

	s3Client := s3.NewFromConfig(cfg)

	resp, err := s3Client.GetObject(context.TODO(), &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	return io.ReadAll(resp.Body)
}

func (m *ProxyManager) GetAWSReservedInstances() (map[string][]AWSReservedInstancesWithRegion, error) {
	reservedInstances := make(map[string][]AWSReservedInstancesWithRegion)
	for _, account := range m.Options.AWSAccounts {
		instances, err := account.GetAllReservedInstances()
		if err != nil {
			return nil, err
		}

		reservedInstances[account.ID] = instances
	}
	return reservedInstances, nil
}

type AWSReservationCoverage = ceTypes.ReservationCoverageGroup

func (m *ProxyManager) GetAWSReservationCoverages(days int) (map[string][]AWSReservationCoverage, error) {
	if days == 0 {
		days = 30
	}
	end := time.Now()
	start := end.AddDate(0, 0, -days)
	coverages := make(map[string][]AWSReservationCoverage)
	for _, a := range m.Options.AWSAccounts {
		coverage, err := a.GetReservationCoverage(start, end)
		if err != nil {
			return nil, err
		}
		coverages[a.ID] = coverage
	}
	return coverages, nil
}

func (a *AWSAccount) GetReservationCoverage(start, end time.Time) ([]AWSReservationCoverage, error) {
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(DEFAULT_REGION),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(a.AccessKeyID, string(a.SecretAccessKey), "")),
	)
	if err != nil {
		return nil, err
	}

	ceClient := costexplorer.NewFromConfig(cfg)

	input := &costexplorer.GetReservationCoverageInput{
		TimePeriod: &ceTypes.DateInterval{
			Start: aws.String(start.Format("2006-01-02")),
			End:   aws.String(end.Format("2006-01-02")),
		},
		GroupBy: []ceTypes.GroupDefinition{
			{
				Type: ceTypes.GroupDefinitionTypeDimension,
				Key:  aws.String("INSTANCE_TYPE"),
			},
			{
				Type: ceTypes.GroupDefinitionTypeDimension,
				Key:  aws.String("PLATFORM"),
			},
			{
				Type: ceTypes.GroupDefinitionTypeDimension,
				Key:  aws.String("REGION"),
			},
		},
	}

	result, err := ceClient.GetReservationCoverage(context.TODO(), input)
	if err != nil {
		return nil, err
	}

	var coverages []AWSReservationCoverage
	for _, group := range result.CoveragesByTime {
		coverages = append(coverages, group.Groups...)
	}

	sort.Slice(coverages, func(i, j int) bool {
		return coverages[i].Attributes["region"] < coverages[j].Attributes["region"]
	})

	return coverages, nil
}

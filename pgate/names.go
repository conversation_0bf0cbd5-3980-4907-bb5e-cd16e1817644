package pgate

import (
	"fmt"
	"math/rand"
	"strings"
)

var NameNouns = []string{
	"time", "person", "year", "way", "day", "thing", "man", "world", "life", "hand", "part", "child",
	"eye", "woman", "place", "work", "week", "case", "point", "government", "company", "number", "group",
	"problem", "fact", "night", "question", "home", "money", "business", "body", "water", "family", "school",
	"state", "country", "system", "program", "community", "area", "book", "food", "power", "person", "story",
	"research", "office", "door", "education", "market", "class", "health", "game", "music", "video", "computer",
	"language", "money", "month", "one", "truth", "idea", "friend", "child", "company", "government", "party",
	"country", "life", "home", "hour", "work", "industry", "girl", "job", "moment", "word", "news", "fact", "air",
	"right", "boy", "restaurant", "water", "service", "war", "area", "history", "information", "map", "hotel",
	"player", "phone", "television", "film", "teacher", "bus", "train", "plane", "university", "city", "town",
	"village", "street", "road", "car", "bike", "house", "room", "name", "problem", "question", "idea", "man",
	"woman", "boy", "girl", "mother", "father", "brother", "sister", "son", "daughter", "baby", "child", "friend",
	"aunt", "uncle", "cousin", "grandmother", "grandfather", "husband", "wife", "lover", "partner", "neighbor",
	"stranger", "doctor", "nurse", "police", "soldier", "artist", "singer", "musician", "actor", "writer", "director",
	"manager", "boss", "employee", "student", "teacher", "professor", "scientist", "engineer", "designer", "architect",
	"athlete", "champion", "leader", "politician", "president", "king", "queen", "prince", "princess", "hero", "villain",
	"monster", "ghost", "vampire", "witch", "wizard", "fairy", "angel", "demon", "god", "goddess", "spirit", "ghost", "alien",
	"robot", "dinosaur", "dragon", "superhero", "villain", "animal", "bird", "fish", "cat", "dog", "horse", "cow", "pig", "sheep",
	"lion", "tiger", "bear", "monkey", "elephant", "snake", "rabbit", "fox", "wolf", "deer", "panda", "koala", "kangaroo", "dolphin",
	"whale", "shark", "octopus", "butterfly", "bee", "ant", "spider", "mosquito", "fly", "cockroach", "beetle", "worm", "snail", "turtle",
	"frog", "lizard", "dinosaur", "bear", "rabbit", "fox", "wolf", "deer", "panda", "koala", "kangaroo", "dolphin", "whale", "shark",
	"octopus", "butterfly", "bee", "ant", "spider", "mosquito", "fly", "cockroach", "beetle", "worm", "snail", "turtle", "frog", "lizard",
	"dinosaur",
}

func GenerateRandomName(length int, onlyLower bool) string {
	// Generate a random name
	chars := "abcdefghijklmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ23456789"
	name := ""
	for i := 0; i < length; i++ {
		name += string(chars[rand.Intn(len(chars))])
	}
	if onlyLower {
		return strings.ToLower(name)
	}
	return name
}

func isValidName(name string) bool {
	if len(name) < 3 {
		return false
	}
	// Check if the name is valid
	chars := "abcdefghijklmnopqrstuvwxyz0123456789-"
	for _, c := range name {
		if !strings.Contains(chars, string(c)) {
			return false
		}
	}
	return true
}

func CheckGroupName(group string) error {
	if group == "" {
		return nil
	}
	if len(group) > 15 {
		return fmt.Errorf("group name is too long (max 10 characters)")
	}
	// Check if the name is valid
	chars := "abcdefghijklmnopqrstuvwxyz0123456789"
	for _, c := range group {
		if !strings.Contains(chars, string(c)) {
			return fmt.Errorf("group name must be lower letters and numbers")
		}
	}
	return nil
}

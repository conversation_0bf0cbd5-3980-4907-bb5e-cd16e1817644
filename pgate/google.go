package pgate

import (
	"context"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"
	"strings"
	"time"

	compute "cloud.google.com/go/compute/apiv1"
	"cloud.google.com/go/compute/apiv1/computepb"
	"github.com/googleapis/gax-go/v2/apierror"
	"github.com/wizhodl/quanter/common/zlog"
	"golang.org/x/crypto/ssh"
	"google.golang.org/api/googleapi"
	"google.golang.org/api/iterator"
	"google.golang.org/api/option"
)

const ProxyNamePrefix = "proxy-" // 需满足表达式 (a-z](?:[-a-z0-9]{0,61}[a-z0-9])?)

func (t *GoogleTemplate) Validate() error {
	_, err := t.ListProxyInstances()
	return err
}

func (t *GoogleTemplate) ListProxyInstances() ([]*computepb.Instance, error) {
	ctx := context.Background()
	client, err := compute.NewInstancesRESTClient(ctx, option.WithCredentialsJSON([]byte(t.Account.ServiceAccountJson)))
	if err != nil {
		return nil, err
	}
	defer client.Close()

	req := &computepb.ListInstancesRequest{
		Project: t.Account.ProjectID,
		Zone:    t.Zone,
	}

	it := client.List(ctx, req)
	instances := make([]*computepb.Instance, 0)
	for {
		instance, err := it.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return nil, err
		}

		name := instance.GetName()
		if len(name) > 6 && name[0:6] == ProxyNamePrefix {
			instances = append(instances, instance)
		}
	}

	return instances, nil
}

func toPtr[T any](v T) *T {
	return &v
}

func getInstanceName(name string) string {
	return fmt.Sprintf("%s%s", ProxyNamePrefix, name)
}

func (p *Proxy) createGoogleInstance() error {
	template := p.template.MachineTemplate.(*GoogleTemplate)

	ctx := context.Background()
	client, err := compute.NewInstancesRESTClient(ctx, option.WithCredentialsJSON([]byte(template.Account.ServiceAccountJson)))
	if err != nil {
		return err
	}
	defer client.Close()

	firewallClient, err := compute.NewFirewallsRESTClient(ctx, option.WithCredentialsJSON([]byte(template.Account.ServiceAccountJson)))
	if err != nil {
		return err
	}
	defer firewallClient.Close()

	// 创建防火墙规则（如果不存在）
	if err := ensureFirewallRule(ctx, firewallClient, template.Account.ProjectID, "default-allow-http", "tcp", []string{"80"}, "http-server"); err != nil {
		return err
	}
	if err := ensureFirewallRule(ctx, firewallClient, template.Account.ProjectID, "default-allow-https", "tcp", []string{"443"}, "https-server"); err != nil {
		return err
	}

	_, publicKey, err := getSSHKeyPairFromPEM(p.template.GetKeyPath())
	if err != nil {
		return err
	}

	sshKeyString := fmt.Sprintf("ubuntu:%s", publicKey)

	instance := &computepb.Instance{
		Name:        toPtr(getInstanceName(p.Name)),
		MachineType: toPtr(fmt.Sprintf("zones/%s/machineTypes/%s", template.Zone, template.MachineType)),
		Disks: []*computepb.AttachedDisk{
			{
				Boot: toPtr(true),
				InitializeParams: &computepb.AttachedDiskInitializeParams{
					SourceImage: &template.SourceImage,
				},
			},
		},
		NetworkInterfaces: []*computepb.NetworkInterface{
			{
				Name: toPtr("global/networks/default"),
				AccessConfigs: []*computepb.AccessConfig{
					{
						Name: toPtr("External NAT"),
						Type: toPtr("ONE_TO_ONE_NAT"),
					},
				},
			},
		},
		Tags: &computepb.Tags{
			Items: []string{"http-server", "https-server"},
		},
		Metadata: &computepb.Metadata{
			Items: []*computepb.Items{
				{
					Key:   toPtr("ssh-keys"),
					Value: toPtr(sshKeyString),
				},
			},
		},
	}

	op, err := client.Insert(ctx, &computepb.InsertInstanceRequest{
		Project:          template.Account.ProjectID,
		Zone:             template.Zone,
		InstanceResource: instance,
	})
	if err != nil {
		return err
	}

	err = op.Wait(ctx)
	if err != nil {
		return err
	}

	// 等待实例启动并分配外部 IP
	time.Sleep(60 * time.Second)

	// 获取实例信息
	instanceInfo, err := client.Get(ctx, &computepb.GetInstanceRequest{
		Project:  template.Account.ProjectID,
		Zone:     template.Zone,
		Instance: getInstanceName(p.Name),
	})
	if err != nil {
		return err
	}

	zlog.Infof("created google instance %s, id: %d, status: %s", p.Name, instanceInfo.GetId(), instanceInfo.GetStatus())

	// 获取外部 IP 地址
	var externalIP string
	for _, ni := range instanceInfo.GetNetworkInterfaces() {
		for _, ac := range ni.GetAccessConfigs() {
			if ac.GetNatIP() != "" {
				externalIP = ac.GetNatIP()
				break
			}
		}
	}

	if externalIP == "" {
		return fmt.Errorf("can't get external IP")
	}

	p.IP = externalIP
	p.InstanceIP = p.IP
	p.InstanceState = convertGoogleInstanceState(instanceInfo.GetStatus())
	p.InstanceID = fmt.Sprintf("%d", instanceInfo.GetId())

	// 安装 docker & warp.sh
	keyPath := p.template.GetKeyPath()
	commands := []string{
		"curl -sSL https://get.docker.com/ | sh",
		"sudo docker ps",
		"wget git.io/warp.sh",
		"chmod +x warp.sh",
	}
	_, err = ExecuteSSHCommands(p.IP, "ubuntu", keyPath, commands)
	if err != nil {
		return err
	}

	zlog.Infof("google instance %s initialized successfully", p.Name)

	return nil
}

func ensureFirewallRule(ctx context.Context, client *compute.FirewallsClient, projectID, name, protocol string, ports []string, targetTag string) error {
	// 检查防火墙规则是否存在
	req := &computepb.GetFirewallRequest{
		Project:  projectID,
		Firewall: name,
	}
	_, err := client.Get(ctx, req)
	if err == nil {
		// 防火墙规则已经存在
		return nil
	}

	// 如果防火墙规则不存在，创建它
	firewall := &computepb.Firewall{
		Name:    &name,
		Network: toPtr(fmt.Sprintf("projects/%s/global/networks/default", projectID)),
		Allowed: []*computepb.Allowed{
			{
				IPProtocol: toPtr(protocol),
				Ports:      ports,
			},
		},
		Direction: toPtr("INGRESS"),
		Priority:  toPtr(int32(1000)),
		TargetTags: []string{
			targetTag,
		},
	}

	op, err := client.Insert(ctx, &computepb.InsertFirewallRequest{
		Project:          projectID,
		FirewallResource: firewall,
	})
	if err != nil {
		return fmt.Errorf("failed to create firewall rule %s: %v", name, err)
	}

	err = op.Wait(ctx)
	if err != nil {
		return fmt.Errorf("failed to wait for the operation: %v", err)
	}

	zlog.Infof("Firewall rule %s created successfully", name)
	return nil
}

func convertGoogleInstanceState(status string) InstanceState {
	// PROVISIONING, STAGING, RUNNING, STOPPING, SUSPENDING, SUSPENDED, REPAIRING, and TERMINATED
	switch status {
	case "PROVISIONING", "STAGING":
		return InstanceStatePending
	case "RUNNING":
		return InstanceStateRunning
	case "STOPPING":
		return InstanceStateShutting
	case "TERMINATED":
		return InstanceStateTerminated
	case "SUSPENDING":
		return InstanceStateStopping
	case "SUSPENDED":
		return InstanceStateStopped
	default:
		return InstanceStateUnknown
	}
}

func getSSHKeyPairFromPEM(privateKeyPath string) ([]byte, []byte, error) {
	// 读取 PEM 格式的私钥文件
	privateKeyPEM, err := os.ReadFile(privateKeyPath)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to read private key file: %v", err)
	}

	// 解码 PEM 块
	block, _ := pem.Decode(privateKeyPEM)
	if block == nil || block.Type != "RSA PRIVATE KEY" {
		return nil, nil, fmt.Errorf("failed to decode PEM block containing private key")
	}

	// 解析 RSA 私钥
	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	// 生成公钥
	publicKey, err := ssh.NewPublicKey(&privateKey.PublicKey)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to generate public key: %v", err)
	}

	// 编码公钥为 OpenSSH 格式
	publicKeyBytes := ssh.MarshalAuthorizedKey(publicKey)

	return privateKeyPEM, publicKeyBytes, nil
}

func (p *Proxy) deleteGoogleInstance() error {
	if p.InstanceID == "" {
		return nil
	}

	if p.InstanceState == InstanceStateTerminated || p.InstanceState == InstanceStateNotFound {
		return nil
	}

	template := p.template.MachineTemplate.(*GoogleTemplate)

	ctx := context.Background()
	client, err := compute.NewInstancesRESTClient(ctx, option.WithCredentialsJSON([]byte(template.Account.ServiceAccountJson)))
	if err != nil {
		return err
	}
	defer client.Close()

	op, err := client.Delete(ctx, &computepb.DeleteInstanceRequest{
		Project:  template.Account.ProjectID,
		Zone:     template.Zone,
		Instance: getInstanceName(p.Name),
	})
	if err != nil {
		return err
	}

	err = op.Wait(ctx)
	if err != nil {
		return err
	}

	// 确认已删除
	_, err = client.Get(ctx, &computepb.GetInstanceRequest{
		Project:  template.Account.ProjectID,
		Zone:     template.Zone,
		Instance: getInstanceName(p.Name),
	})
	if err != nil {
		if isNotFoundError(err) {
			p.InstanceState = InstanceStateTerminated
			p.InstanceIP = ""
			p.IP = ""
			return nil
		}
		return err
	}

	return fmt.Errorf("instance still exists after delete operation")
}

func isNotFoundError(err error) bool {
	apiErr, ok := err.(*apierror.APIError)
	if ok {
		if gErr, ok := apiErr.Unwrap().(*googleapi.Error); ok && gErr.Code == 404 {
			return true
		}
	}
	return false
}

func (g *GoogleAccount) listRegions() ([]string, error) {
	req := &computepb.ListRegionsRequest{
		Project: g.ProjectID,
	}

	ctx := context.Background()
	regionClient, err := compute.NewRegionsRESTClient(ctx, option.WithCredentialsJSON([]byte(g.ServiceAccountJson)))
	if err != nil {
		return nil, err
	}
	defer regionClient.Close()

	var regions []string
	it := regionClient.List(ctx, req)
	for {
		region, err := it.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return nil, err
		}
		regions = append(regions, *region.Name)
	}
	return regions, nil
}

type GoogleCommitment struct {
	Region string
	*computepb.Commitment
}

func (g *GoogleAccount) GetCommitments(regions []string) ([]*GoogleCommitment, error) {
	ctx := context.Background()
	client, err := compute.NewRegionCommitmentsRESTClient(ctx, option.WithCredentialsJSON([]byte(g.ServiceAccountJson)))
	if err != nil {
		return nil, err
	}
	defer client.Close()

	commits := make([]*GoogleCommitment, 0)
	for _, region := range regions {
		req := &computepb.ListRegionCommitmentsRequest{
			Project: g.ProjectID,
			Region:  region,
		}

		it := client.List(ctx, req)
		for {
			commitment, err := it.Next()
			if err == iterator.Done {
				break
			}
			if err != nil {
				return nil, err
			}
			commits = append(commits, &GoogleCommitment{
				Region:     region,
				Commitment: commitment,
			})
		}
	}

	return commits, nil
}

func (m *ProxyManager) GetGoogleCommitments() (map[string][]*GoogleCommitment, error) {
	commits := make(map[string][]*GoogleCommitment)
	for _, account := range m.Options.GoogleAccounts {

		regions, err := account.listRegions()
		if err != nil {
			return nil, err
		}

		l, err := account.GetCommitments(regions)
		if err != nil {
			return nil, err
		}

		commits[account.ID] = l
	}
	return commits, nil
}

type GoogleCommitmentCoverage struct {
	Region string
	// MachineType     string // 实例类型和折扣类型对上不，无法合并统计 如 n1-standard-1 和 GENERAL_PURPOSE
	UsedCPUs        int64
	UsedMemory      int64
	CommittedCPUs   int64
	CommittedMemory int64
}

func (g *GoogleAccount) getInstancesUsageByRegion() (map[string]int64, map[string]int64, error) {
	req := &computepb.AggregatedListInstancesRequest{
		Project: g.ProjectID,
	}

	ctx := context.Background()
	client, err := compute.NewInstancesRESTClient(ctx, option.WithCredentialsJSON([]byte(g.ServiceAccountJson)))
	if err != nil {
		return nil, nil, err
	}
	defer client.Close()

	it := client.AggregatedList(ctx, req)
	usedCpus := make(map[string]int64)
	usedMemory := make(map[string]int64)
	for {
		item, err := it.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return nil, nil, err
		}

		for _, instance := range item.Value.Instances {
			zoneURL := instance.GetZone()
			zone := zoneURL[strings.LastIndex(zoneURL, "/")+1:]
			region := zone[:strings.LastIndex(zone, "-")]
			machineType := getMachineType(zone, instance.GetMachineType())

			cpus, memory, err := g.getMachineTypeDetails(zone, machineType)
			if err != nil {
				return nil, nil, err
			}

			usedCpus[region] += cpus
			usedMemory[region] += memory
		}
	}

	return usedCpus, usedMemory, nil
}

func (g *GoogleAccount) GetCommitmentsCoverage() ([]*GoogleCommitmentCoverage, error) {
	usedCpus, usedMemory, err := g.getInstancesUsageByRegion()
	if err != nil {
		return nil, err
	}

	regions, err := g.listRegions()
	if err != nil {
		return nil, err
	}

	coverages := make([]*GoogleCommitmentCoverage, 0)
	for _, region := range regions {
		usedCpu := usedCpus[region]
		usedMem := usedMemory[region]
		if usedCpu == 0 {
			continue
		}

		var committedCpus int64
		var committedMemory int64

		commitments, err := g.GetCommitments([]string{region})
		if err != nil {
			return nil, err
		}

		for _, c := range commitments {
			for _, r := range c.Resources {
				if *r.Type == "VCPU" {
					committedCpus += *r.Amount
				} else if *r.Type == "MEMORY" {
					committedMemory += *r.Amount
				}
			}
		}

		coverages = append(coverages, &GoogleCommitmentCoverage{
			Region:          region,
			UsedCPUs:        usedCpu,
			UsedMemory:      usedMem,
			CommittedCPUs:   committedCpus,
			CommittedMemory: committedMemory,
		})
	}

	return coverages, nil
}

func getMachineType(zone, machineType string) string {
	// e.g. "https://www.googleapis.com/compute/v1/projects/wizhodl-233708/zones/asia-east2-a/machineTypes/n1-standard-1"
	if strings.Contains(machineType, "zones/"+zone+"/machineTypes/") {
		// 获取这段字符串后面的内容
		index := strings.Index(machineType, "zones/"+zone+"/machineTypes/") + len("zones/"+zone+"/machineTypes/")
		machineType = machineType[index:]
	}
	return machineType
}

func (g *GoogleAccount) getMachineTypeDetails(zone, machineType string) (int64, int64, error) {
	ctx := context.Background()
	machineTypeClient, err := compute.NewMachineTypesRESTClient(ctx, option.WithCredentialsJSON([]byte(g.ServiceAccountJson)))
	if err != nil {
		return 0, 0, err
	}
	defer machineTypeClient.Close()

	req := &computepb.GetMachineTypeRequest{
		Project:     g.ProjectID,
		Zone:        zone,
		MachineType: machineType,
	}
	machineTypeDetails, err := machineTypeClient.Get(ctx, req)
	if err != nil {
		return 0, 0, err
	}
	return int64(*machineTypeDetails.GuestCpus), int64(*machineTypeDetails.MemoryMb), nil
}

func (m *ProxyManager) GetGoogleCommitmentCoverages() (map[string][]*GoogleCommitmentCoverage, error) {
	coverags := make(map[string][]*GoogleCommitmentCoverage)
	for _, account := range m.Options.GoogleAccounts {
		c, err := account.GetCommitmentsCoverage()
		if err != nil {
			return nil, err
		}

		coverags[account.ID] = c
	}
	return coverags, nil
}

package main

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"flag"
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/ssh"
)

func main() {
	keyPath := flag.String("path", "", "Path to save the generated SSH key")
	flag.Parse()

	privateKy, _, err := generateSSHKeyPair()
	if err != nil {
		log.Fatalf("Failed to generate SSH key pair: %v", err)
	}

	if *keyPath == "" {
		path := "tmp_private_key.pem"
		keyPath = &path
	}

	err = os.WriteFile(*keyPath, privateKy, 0600)
	if err != nil {
		log.Fatalf("Failed to save private key: %v", err)
	}
	fmt.Printf("\nPrivate key saved to %s\n", *keyPath)
}

func generateSSHKeyPair() ([]byte, []byte, error) {
	// 生成 RSA 私钥
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to generate private key: %v", err)
	}

	// 编码私钥为 PEM 格式
	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: x509.MarshalPKCS1PrivateKey(privateKey),
	})

	// 生成公钥
	publicKey, err := ssh.NewPublicKey(&privateKey.PublicKey)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to generate public key: %v", err)
	}

	// 编码公钥为 OpenSSH 格式
	publicKeyBytes := ssh.MarshalAuthorizedKey(publicKey)

	return privateKeyPEM, publicKeyBytes, nil
}

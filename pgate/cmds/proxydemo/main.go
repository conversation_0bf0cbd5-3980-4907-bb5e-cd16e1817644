package main

import (
	"io"
	"log"
	"math/rand"
	"net"
	"time"
)

var trojanClients = []string{
	"127.0.0.1:57410",
	"127.0.0.1:52828",
}

func main() {
	listenAddr := "127.0.0.1:7000"
	listener, err := net.Listen("tcp", listenAddr)
	if err != nil {
		log.Fatalf("Failed to listen on %s: %v", listenAddr, err)
	}
	defer listener.Close()

	log.Printf("Socks5 load balancer running on %s", listenAddr)

	for {
		clientConn, err := listener.Accept()
		if err != nil {
			log.Printf("Failed to accept connection: %v", err)
			continue
		}
		go handleConnection(clientConn)
	}
}

func handleConnection(clientConn net.Conn) {
	defer clientConn.Close()

	// 随机选择一个 Trojan-Go 客户端实例
	rand.Seed(time.Now().UnixNano())
	selectedClient := trojanClients[rand.Intn(len(trojanClients))]

	log.Printf("Forwarding request to %s", selectedClient)

	// 连接到选定的 Trojan-Go 客户端
	serverConn, err := net.Dial("tcp", selectedClient)
	if err != nil {
		log.Printf("Failed to connect to Trojan-Go client at %s: %v", selectedClient, err)
		return
	}
	defer serverConn.Close()

	// 启动双向数据拷贝（双向流量转发）
	done := make(chan struct{})

	go func() {
		_, err := io.Copy(clientConn, serverConn)
		if err != nil && !isClosedNetworkError(err) {
			log.Printf("Error copying from server to client: %v", err)
		}
		done <- struct{}{}
	}()

	_, err = io.Copy(serverConn, clientConn)
	if err != nil && !isClosedNetworkError(err) {
		log.Printf("Error copying from client to server: %v", err)
	}

	<-done // 等待双向拷贝完成
}

// 检查是否是已关闭连接的错误
func isClosedNetworkError(err error) bool {
	opErr, ok := err.(*net.OpError)
	return ok && opErr.Err.Error() == "use of closed network connection"
}

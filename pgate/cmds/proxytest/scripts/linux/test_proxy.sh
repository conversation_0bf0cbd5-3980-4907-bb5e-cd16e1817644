#!/bin/bash

# Proxy Testing Script for Linux/macOS
# This script provides multiple methods to test HTTP, HTTPS, and SOCKS5 proxies

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default values
PROXY_URL=""
TARGET_URL="https://httpbin.org/ip"
TEST_TYPE="all"
SHOW_HELP=false

# Function to show help
show_help() {
    cat << EOF
Proxy Testing Script for Linux/macOS

Usage:
    ./test_proxy.sh "http://127.0.0.1:8080"
    ./test_proxy.sh "socks5://127.0.0.1:1080" "https://api.ipify.org"
    ./test_proxy.sh "http://127.0.0.1:8080" "https://httpbin.org/ip" "curl"

Parameters:
    PROXY_URL    Proxy URL to test (e.g., http://127.0.0.1:8080, socks5://127.0.0.1:1080)
    TARGET_URL   Target URL to test (default: https://httpbin.org/ip)
    TEST_TYPE    Test method: all, curl, wget, go, connectivity

Examples:
    # Test HTTP proxy with curl
    ./test_proxy.sh "http://127.0.0.1:8080" "https://httpbin.org/ip" "curl"
    
    # Test SOCKS5 proxy with wget
    ./test_proxy.sh "socks5://127.0.0.1:1080" "https://api.ipify.org" "wget"
    
    # Test all methods
    ./test_proxy.sh "http://127.0.0.1:8080" "https://httpbin.org/ip" "all"
EOF
}

# Function to test proxy connectivity
test_connectivity() {
    local proxy_url="$1"
    
    echo -e "${CYAN}=== Testing Proxy Connectivity ===${NC}"
    echo "Proxy URL: $proxy_url"
    
    # Extract host and port
    if [[ $proxy_url =~ ^(http|https|socks5)://([^:]+):([0-9]+) ]]; then
        local host="${BASH_REMATCH[2]}"
        local port="${BASH_REMATCH[3]}"
        
        echo "Testing connection to $host:$port..."
        
        if timeout 5 bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
            echo -e "${GREEN}✓ Connectivity test PASSED${NC}"
            return 0
        else
            echo -e "${RED}✗ Connectivity test FAILED${NC}"
            return 1
        fi
    else
        echo -e "${RED}✗ Invalid proxy URL format${NC}"
        return 1
    fi
}

# Function to test proxy with curl
test_curl() {
    local proxy_url="$1"
    local target_url="$2"
    
    echo -e "${CYAN}=== Testing Proxy with cURL ===${NC}"
    echo "Proxy URL: $proxy_url"
    echo "Target URL: $target_url"
    
    if command -v curl >/dev/null 2>&1; then
        if [[ $proxy_url =~ ^socks5:// ]]; then
            # SOCKS5 proxy
            local socks_host=$(echo "$proxy_url" | sed 's|socks5://||' | cut -d: -f1)
            local socks_port=$(echo "$proxy_url" | sed 's|socks5://||' | cut -d: -f2)
            
            if curl --socks5 "$socks_host:$socks_port" --connect-timeout 10 --max-time 30 --silent "$target_url"; then
                echo -e "${GREEN}✓ cURL test PASSED${NC}"
            else
                echo -e "${RED}✗ cURL test FAILED${NC}"
            fi
        else
            # HTTP proxy
            if curl --proxy "$proxy_url" --connect-timeout 10 --max-time 30 --silent "$target_url"; then
                echo -e "${GREEN}✓ cURL test PASSED${NC}"
            else
                echo -e "${RED}✗ cURL test FAILED${NC}"
            fi
        fi
    else
        echo -e "${YELLOW}⚠ cURL not found, skipping test${NC}"
    fi
}

# Function to test proxy with wget
test_wget() {
    local proxy_url="$1"
    local target_url="$2"
    
    echo -e "${CYAN}=== Testing Proxy with wget ===${NC}"
    echo "Proxy URL: $proxy_url"
    echo "Target URL: $target_url"
    
    if command -v wget >/dev/null 2>&1; then
        if [[ $proxy_url =~ ^socks5:// ]]; then
            # SOCKS5 proxy (wget doesn't support SOCKS5 natively, but some versions do)
            local socks_host=$(echo "$proxy_url" | sed 's|socks5://||' | cut -d: -f1)
            local socks_port=$(echo "$proxy_url" | sed 's|socks5://||' | cut -d: -f2)
            
            if wget --timeout=10 --tries=1 --quiet --output-document=- "$target_url" 2>/dev/null; then
                echo -e "${GREEN}✓ wget test PASSED${NC}"
            else
                echo -e "${RED}✗ wget test FAILED${NC}"
            fi
        else
            # HTTP proxy
            if wget --proxy="$proxy_url" --timeout=10 --tries=1 --quiet --output-document=- "$target_url" 2>/dev/null; then
                echo -e "${GREEN}✓ wget test PASSED${NC}"
            else
                echo -e "${RED}✗ wget test FAILED${NC}"
            fi
        fi
    else
        echo -e "${YELLOW}⚠ wget not found, skipping test${NC}"
    fi
}

# Function to test proxy with Go tool
test_go() {
    local proxy_url="$1"
    local target_url="$2"
    
    echo -e "${CYAN}=== Testing Proxy with Go Tool ===${NC}"
    echo "Proxy URL: $proxy_url"
    echo "Target URL: $target_url"
    
    # Look for the appropriate binary
    local go_tool=""
    
    # Check current directory first
    if [[ -f "proxytest-linux-amd64" ]]; then
        go_tool="./proxytest-linux-amd64"
    elif [[ -f "proxytest-linux-386" ]]; then
        go_tool="./proxytest-linux-386"
    elif [[ -f "proxytest-linux-arm64" ]]; then
        go_tool="./proxytest-linux-arm64"
    elif [[ -f "proxytest-linux-armv7" ]]; then
        go_tool="./proxytest-linux-armv7"
    elif [[ -f "proxytest-darwin-amd64" ]]; then
        go_tool="./proxytest-darwin-amd64"
    elif [[ -f "proxytest-darwin-arm64" ]]; then
        go_tool="./proxytest-darwin-arm64"
    elif [[ -f "proxytest" ]]; then
        go_tool="./proxytest"
    fi
    
    # Check build directory
    if [[ -z "$go_tool" ]]; then
        if [[ -f "../build/proxytest-linux-amd64" ]]; then
            go_tool="../build/proxytest-linux-amd64"
        elif [[ -f "../build/proxytest-linux-386" ]]; then
            go_tool="../build/proxytest-linux-386"
        elif [[ -f "../build/proxytest-linux-arm64" ]]; then
            go_tool="../build/proxytest-linux-arm64"
        elif [[ -f "../build/proxytest-linux-armv7" ]]; then
            go_tool="../build/proxytest-linux-armv7"
        elif [[ -f "../build/proxytest-darwin-amd64" ]]; then
            go_tool="../build/proxytest-darwin-amd64"
        elif [[ -f "../build/proxytest-darwin-arm64" ]]; then
            go_tool="../build/proxytest-darwin-arm64"
        fi
    fi
    
    if [[ -n "$go_tool" ]]; then
        if "$go_tool" -proxy "$proxy_url" -target "$target_url" -type "auto" >/dev/null 2>&1; then
            echo -e "${GREEN}✓ Go test PASSED${NC}"
        else
            echo -e "${RED}✗ Go test FAILED${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ Go test tool not found. Please build it first.${NC}"
    fi
}

# Function to test with netcat
test_netcat() {
    local proxy_url="$1"
    
    echo -e "${CYAN}=== Testing Proxy with Netcat ===${NC}"
    echo "Proxy URL: $proxy_url"
    
    if [[ $proxy_url =~ ^(http|https|socks5)://([^:]+):([0-9]+) ]]; then
        local host="${BASH_REMATCH[2]}"
        local port="${BASH_REMATCH[3]}"
        
        if command -v nc >/dev/null 2>&1; then
            if timeout 5 nc -zv "$host" "$port" 2>/dev/null; then
                echo -e "${GREEN}✓ Netcat connectivity test PASSED${NC}"
            else
                echo -e "${RED}✗ Netcat connectivity test FAILED${NC}"
            fi
        else
            echo -e "${YELLOW}⚠ netcat not found, skipping test${NC}"
        fi
    fi
}

# Function to test with telnet
test_telnet() {
    local proxy_url="$1"
    
    echo -e "${CYAN}=== Testing Proxy with Telnet ===${NC}"
    echo "Proxy URL: $proxy_url"
    
    if [[ $proxy_url =~ ^(http|https|socks5)://([^:]+):([0-9]+) ]]; then
        local host="${BASH_REMATCH[2]}"
        local port="${BASH_REMATCH[3]}"
        
        if command -v telnet >/dev/null 2>&1; then
            if timeout 5 bash -c "echo quit | telnet $host $port" 2>/dev/null | grep -q "Connected"; then
                echo -e "${GREEN}✓ Telnet connectivity test PASSED${NC}"
            else
                echo -e "${RED}✗ Telnet connectivity test FAILED${NC}"
            fi
        else
            echo -e "${YELLOW}⚠ telnet not found, skipping test${NC}"
        fi
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            SHOW_HELP=true
            shift
            ;;
        *)
            if [[ -z "$PROXY_URL" ]]; then
                PROXY_URL="$1"
            elif [[ -z "$TARGET_URL" ]]; then
                TARGET_URL="$1"
            elif [[ -z "$TEST_TYPE" ]]; then
                TEST_TYPE="$1"
            fi
            shift
            ;;
    esac
done

# Show help if requested
if [[ "$SHOW_HELP" == true ]]; then
    show_help
    exit 0
fi

# Check if proxy URL is provided
if [[ -z "$PROXY_URL" ]]; then
    echo -e "${RED}Error: Proxy URL is required${NC}"
    show_help
    exit 1
fi

# Validate proxy URL format
if [[ ! "$PROXY_URL" =~ ^(http|https|socks5):// ]]; then
    echo -e "${RED}Error: Invalid proxy URL format. Must start with http://, https://, or socks5://${NC}"
    exit 1
fi

echo -e "${BLUE}Proxy Testing Script for Linux/macOS${NC}"
echo -e "${BLUE}=====================================${NC}"
echo ""

# Run tests based on TEST_TYPE
case "$TEST_TYPE" in
    "connectivity")
        test_connectivity "$PROXY_URL"
        ;;
    "curl")
        test_curl "$PROXY_URL" "$TARGET_URL"
        ;;
    "wget")
        test_wget "$PROXY_URL" "$TARGET_URL"
        ;;
    "go")
        test_go "$PROXY_URL" "$TARGET_URL"
        ;;
    "netcat")
        test_netcat "$PROXY_URL"
        ;;
    "telnet")
        test_telnet "$PROXY_URL"
        ;;
    "all")
        echo -e "${YELLOW}Running all proxy tests...${NC}"
        echo ""
        
        test_connectivity "$PROXY_URL"
        echo ""
        
        test_curl "$PROXY_URL" "$TARGET_URL"
        echo ""
        
        test_wget "$PROXY_URL" "$TARGET_URL"
        echo ""
        
        test_go "$PROXY_URL" "$TARGET_URL"
        echo ""
        
        test_netcat "$PROXY_URL"
        echo ""
        
        test_telnet "$PROXY_URL"
        ;;
    *)
        echo -e "${RED}Error: Unknown test type '$TEST_TYPE'${NC}"
        show_help
        exit 1
        ;;
esac

echo ""
echo -e "${CYAN}Testing completed!${NC}" 
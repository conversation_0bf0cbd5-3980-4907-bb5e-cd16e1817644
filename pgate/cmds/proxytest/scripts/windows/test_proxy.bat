@echo off
setlocal enabledelayedexpansion

REM Proxy Testing Batch Script for Windows
REM This script provides multiple methods to test HTTP, HTTPS, and SOCKS5 proxies

set "PROXY_URL=%1"
set "TARGET_URL=%2"
set "TEST_TYPE=%3"

if "%PROXY_URL%"=="" (
    echo Error: Proxy URL is required
    echo.
    echo Usage: test_proxy.bat ^<proxy_url^> [target_url] [test_type]
    echo.
    echo Examples:
    echo   test_proxy.bat http://127.0.0.1:8080
    echo   test_proxy.bat socks5://127.0.0.1:1080 https://api.ipify.org
    echo   test_proxy.bat http://127.0.0.1:8080 https://httpbin.org/ip curl
    echo.
    echo Test types: curl, powershell, go, connectivity, all
    exit /b 1
)

if "%TARGET_URL%"=="" set "TARGET_URL=https://httpbin.org/ip"
if "%TEST_TYPE%"=="" set "TEST_TYPE=all"

echo Proxy Testing Script for Windows
echo =================================
echo Proxy URL: %PROXY_URL%
echo Target URL: %TARGET_URL%
echo Test Type: %TEST_TYPE%
echo.

REM Validate proxy URL format
echo %PROXY_URL% | findstr /r "^http://" >nul
if %errorlevel% equ 0 goto :valid_url

echo %PROXY_URL% | findstr /r "^https://" >nul
if %errorlevel% equ 0 goto :valid_url

echo %PROXY_URL% | findstr /r "^socks5://" >nul
if %errorlevel% equ 0 goto :valid_url

echo Error: Invalid proxy URL format. Must start with http://, https://, or socks5://
exit /b 1

:valid_url

REM Run tests based on TEST_TYPE
if /i "%TEST_TYPE%"=="connectivity" goto :test_connectivity
if /i "%TEST_TYPE%"=="curl" goto :test_curl
if /i "%TEST_TYPE%"=="powershell" goto :test_powershell
if /i "%TEST_TYPE%"=="go" goto :test_go
if /i "%TEST_TYPE%"=="all" goto :test_all

echo Error: Unknown test type '%TEST_TYPE%'
echo Available test types: curl, powershell, go, connectivity, all
exit /b 1

:test_connectivity
echo === Testing Proxy Connectivity ===
echo Proxy URL: %PROXY_URL%
call :extract_host_port
echo Testing connection to %HOST%:%PORT%...
powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.ConnectAsync('%HOST%', %PORT%).Wait(5000); if ($tcp.Connected) { Write-Host '✓ Connectivity test PASSED' -ForegroundColor Green; $tcp.Close() } else { Write-Host '✗ Connectivity test FAILED' -ForegroundColor Red } } catch { Write-Host '✗ Connectivity test FAILED:' $_.Exception.Message -ForegroundColor Red }"
goto :end

:test_curl
echo === Testing Proxy with cURL ===
echo Proxy URL: %PROXY_URL%
echo Target URL: %TARGET_URL%

echo %PROXY_URL% | findstr /r "^socks5://" >nul
if %errorlevel% equ 0 (
    REM SOCKS5 proxy
    call :extract_host_port
    curl.exe --socks5 "%HOST%:%PORT%" --connect-timeout 10 --max-time 30 "%TARGET_URL%"
) else (
    REM HTTP proxy
    curl.exe --proxy "%PROXY_URL%" --connect-timeout 10 --max-time 30 "%TARGET_URL%"
)

if %errorlevel% equ 0 (
    echo ✓ cURL test PASSED
) else (
    echo ✗ cURL test FAILED
)
goto :end

:test_powershell
echo === Testing Proxy with PowerShell ===
echo Proxy URL: %PROXY_URL%
echo Target URL: %TARGET_URL%

powershell -Command "try { $env:HTTP_PROXY='%PROXY_URL%'; $env:HTTPS_PROXY='%PROXY_URL%'; $request = [System.Net.WebRequest]::Create('%TARGET_URL%'); $request.Timeout = 10000; $request.Proxy = [System.Net.WebRequest]::GetSystemWebProxy(); $request.Proxy.Credentials = [System.Net.CredentialCache]::DefaultCredentials; $response = $request.GetResponse(); $stream = $response.GetResponseStream(); $reader = New-Object System.IO.StreamReader($stream); $content = $reader.ReadToEnd(); Write-Host '✓ PowerShell test PASSED' -ForegroundColor Green; Write-Host 'Status Code:' $response.StatusCode -ForegroundColor Yellow; Write-Host 'Response:' -ForegroundColor Yellow; Write-Host $content; $reader.Close(); $stream.Close(); $response.Close() } catch { Write-Host '✗ PowerShell test FAILED:' $_.Exception.Message -ForegroundColor Red } finally { Remove-Item Env:HTTP_PROXY -ErrorAction SilentlyContinue; Remove-Item Env:HTTPS_PROXY -ErrorAction SilentlyContinue }"
goto :end

:test_go
echo === Testing Proxy with Go Tool ===
echo Proxy URL: %PROXY_URL%
echo Target URL: %TARGET_URL%

REM Look for the appropriate binary
set "GO_TOOL="
if exist "proxytest-windows-amd64.exe" set "GO_TOOL=proxytest-windows-amd64.exe"
if exist "proxytest-windows-386.exe" set "GO_TOOL=proxytest-windows-386.exe"
if exist "proxytest.exe" set "GO_TOOL=proxytest.exe"

if "%GO_TOOL%"=="" (
    echo Go test tool not found. Looking for cross-compiled binaries...
    if exist "..\build\proxytest-windows-amd64.exe" (
        set "GO_TOOL=..\build\proxytest-windows-amd64.exe"
    ) else if exist "..\build\proxytest-windows-386.exe" (
        set "GO_TOOL=..\build\proxytest-windows-386.exe"
    ) else (
        echo ✗ Go test tool not found. Please build it first.
        goto :end
    )
)

%GO_TOOL% -proxy "%PROXY_URL%" -target "%TARGET_URL%" -type "auto"
if %errorlevel% equ 0 (
    echo ✓ Go test PASSED
) else (
    echo ✗ Go test FAILED
)
goto :end

:test_all
echo Running all proxy tests...
echo.

call :test_connectivity
echo.

call :test_curl
echo.

call :test_powershell
echo.

call :test_go
echo.

echo === Testing with Netcat ===
call :extract_host_port
echo Testing connection to %HOST%:%PORT%...
nc.exe -zv %HOST% %PORT% 2>nul
if %errorlevel% equ 0 (
    echo ✓ Netcat connectivity test PASSED
) else (
    echo ✗ Netcat connectivity test FAILED
)
echo.

echo === Testing with Telnet ===
echo Testing connection to %HOST%:%PORT%...
timeout /t 3 >nul & telnet.exe %HOST% %PORT% 2>nul
if %errorlevel% equ 0 (
    echo ✓ Telnet connectivity test PASSED
) else (
    echo ✗ Telnet connectivity test FAILED
)
goto :end

:extract_host_port
REM Extract host and port from proxy URL
set "HOST="
set "PORT="

echo %PROXY_URL% | findstr /r "^http://" >nul
if %errorlevel% equ 0 (
    set "HOST_PORT=%PROXY_URL:http://=%"
    for /f "tokens=1,2 delims=:" %%a in ("%HOST_PORT%") do (
        set "HOST=%%a"
        set "PORT=%%b"
    )
    goto :eof
)

echo %PROXY_URL% | findstr /r "^https://" >nul
if %errorlevel% equ 0 (
    set "HOST_PORT=%PROXY_URL:https://=%"
    for /f "tokens=1,2 delims=:" %%a in ("%HOST_PORT%") do (
        set "HOST=%%a"
        set "PORT=%%b"
    )
    goto :eof
)

echo %PROXY_URL% | findstr /r "^socks5://" >nul
if %errorlevel% equ 0 (
    set "HOST_PORT=%PROXY_URL:socks5://=%"
    for /f "tokens=1,2 delims=:" %%a in ("%HOST_PORT%") do (
        set "HOST=%%a"
        set "PORT=%%b"
    )
    goto :eof
)

:end
echo.
echo Testing completed! 
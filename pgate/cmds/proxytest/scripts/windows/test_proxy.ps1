# Proxy Testing Script for Windows
# This script provides multiple methods to test HTTP, HTTPS, and SOCKS5 proxies

param(
    [string]$ProxyURL = "",
    [string]$TargetURL = "https://httpbin.org/ip",
    [string]$TestType = "all",
    [switch]$Help
)

function Show-Help {
    Write-Host @"
Proxy Testing Script for Windows

Usage:
    .\test_proxy.ps1 -ProxyURL "http://127.0.0.1:8080"
    .\test_proxy.ps1 -ProxyURL "socks5://127.0.0.1:1080" -TargetURL "https://api.ipify.org"
    .\test_proxy.ps1 -ProxyURL "http://127.0.0.1:8080" -TestType "curl"

Parameters:
    -ProxyURL    Proxy URL to test (e.g., http://127.0.0.1:8080, socks5://127.0.0.1:1080)
    -TargetURL   Target URL to test (default: https://httpbin.org/ip)
    -TestType    Test method: all, curl, powershell, go, connectivity
    -Help        Show this help message

Examples:
    # Test HTTP proxy with curl
    .\test_proxy.ps1 -ProxyURL "http://127.0.0.1:8080" -TestType "curl"
    
    # Test SOCKS5 proxy with PowerShell
    .\test_proxy.ps1 -ProxyURL "socks5://127.0.0.1:1080" -TestType "powershell"
    
    # Test all methods
    .\test_proxy.ps1 -ProxyURL "http://127.0.0.1:8080" -TestType "all"
"@
}

function Test-ProxyConnectivity {
    param([string]$ProxyURL)
    
    Write-Host "=== Testing Proxy Connectivity ===" -ForegroundColor Green
    Write-Host "Proxy URL: $ProxyURL"
    
    try {
        $uri = [System.Uri]$ProxyURL
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $tcpClient.ConnectAsync($uri.Host, $uri.Port).Wait(5000)
        
        if ($tcpClient.Connected) {
            Write-Host "✓ Connectivity test PASSED" -ForegroundColor Green
            $tcpClient.Close()
            return $true
        } else {
            Write-Host "✗ Connectivity test FAILED" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ Connectivity test FAILED: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-ProxyWithCurl {
    param([string]$ProxyURL, [string]$TargetURL)
    
    Write-Host "=== Testing Proxy with cURL ===" -ForegroundColor Green
    Write-Host "Proxy URL: $ProxyURL"
    Write-Host "Target URL: $TargetURL"
    
    try {
        if ($ProxyURL.StartsWith("socks5://")) {
            # SOCKS5 proxy
            $socksHost = ($ProxyURL -replace "socks5://", "").Split(":")[0]
            $socksPort = ($ProxyURL -replace "socks5://", "").Split(":")[1]
            
            $result = curl.exe --socks5 "$socksHost`:$socksPort" --connect-timeout 10 --max-time 30 $TargetURL 2>&1
        } else {
            # HTTP proxy
            $result = curl.exe --proxy $ProxyURL --connect-timeout 10 --max-time 30 $TargetURL 2>&1
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ cURL test PASSED" -ForegroundColor Green
            Write-Host "Response:" -ForegroundColor Yellow
            Write-Host $result
        } else {
            Write-Host "✗ cURL test FAILED" -ForegroundColor Red
            Write-Host "Error: $result" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ cURL test FAILED: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Test-ProxyWithPowerShell {
    param([string]$ProxyURL, [string]$TargetURL)
    
    Write-Host "=== Testing Proxy with PowerShell ===" -ForegroundColor Green
    Write-Host "Proxy URL: $ProxyURL"
    Write-Host "Target URL: $TargetURL"
    
    try {
        # Set proxy environment variables
        $env:HTTP_PROXY = $ProxyURL
        $env:HTTPS_PROXY = $ProxyURL
        
        # Create web request
        $request = [System.Net.WebRequest]::Create($TargetURL)
        $request.Timeout = 10000
        $request.Proxy = [System.Net.WebRequest]::GetSystemWebProxy()
        $request.Proxy.Credentials = [System.Net.CredentialCache]::DefaultCredentials
        
        # Get response
        $response = $request.GetResponse()
        $stream = $response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $content = $reader.ReadToEnd()
        
        Write-Host "✓ PowerShell test PASSED" -ForegroundColor Green
        Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Yellow
        Write-Host "Response:" -ForegroundColor Yellow
        Write-Host $content
        
        $reader.Close()
        $stream.Close()
        $response.Close()
    }
    catch {
        Write-Host "✗ PowerShell test FAILED: $($_.Exception.Message)" -ForegroundColor Red
    }
    finally {
        # Clear proxy environment variables
        Remove-Item Env:HTTP_PROXY -ErrorAction SilentlyContinue
        Remove-Item Env:HTTPS_PROXY -ErrorAction SilentlyContinue
    }
}

function Test-ProxyWithGo {
    param([string]$ProxyURL, [string]$TargetURL)
    
    Write-Host "=== Testing Proxy with Go Tool ===" -ForegroundColor Green
    Write-Host "Proxy URL: $ProxyURL"
    Write-Host "Target URL: $TargetURL"
    
    try {
        # Look for the appropriate binary
        $goTool = $null
        
        # Check current directory first
        if (Test-Path "proxytest-windows-amd64.exe") {
            $goTool = ".\proxytest-windows-amd64.exe"
        } elseif (Test-Path "proxytest-windows-386.exe") {
            $goTool = ".\proxytest-windows-386.exe"
        } elseif (Test-Path "proxytest.exe") {
            $goTool = ".\proxytest.exe"
        }
        
        # Check build directory
        if (-not $goTool) {
            if (Test-Path "..\build\proxytest-windows-amd64.exe") {
                $goTool = "..\build\proxytest-windows-amd64.exe"
            } elseif (Test-Path "..\build\proxytest-windows-386.exe") {
                $goTool = "..\build\proxytest-windows-386.exe"
            }
        }
        
        if (-not $goTool) {
            Write-Host "✗ Go test tool not found. Please build it first." -ForegroundColor Red
            return
        }
        
        # Run the Go test tool
        $result = & $goTool -proxy $ProxyURL -target $TargetURL -type "auto" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Go test PASSED" -ForegroundColor Green
            Write-Host "Result:" -ForegroundColor Yellow
            Write-Host $result
        } else {
            Write-Host "✗ Go test FAILED" -ForegroundColor Red
            Write-Host "Error: $result" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ Go test FAILED: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Test-ProxyWithNetcat {
    param([string]$ProxyURL)
    
    Write-Host "=== Testing Proxy with Netcat ===" -ForegroundColor Green
    Write-Host "Proxy URL: $ProxyURL"
    
    try {
        $uri = [System.Uri]$ProxyURL
        
        # Test basic TCP connectivity
        $result = nc.exe -zv $uri.Host $uri.Port 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Netcat connectivity test PASSED" -ForegroundColor Green
            Write-Host "Result: $result" -ForegroundColor Yellow
        } else {
            Write-Host "✗ Netcat connectivity test FAILED" -ForegroundColor Red
            Write-Host "Error: $result" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ Netcat test FAILED: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Test-ProxyWithTelnet {
    param([string]$ProxyURL)
    
    Write-Host "=== Testing Proxy with Telnet ===" -ForegroundColor Green
    Write-Host "Proxy URL: $ProxyURL"
    
    try {
        $uri = [System.Uri]$ProxyURL
        
        # Test basic TCP connectivity
        $result = telnet.exe $uri.Host $uri.Port 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Telnet connectivity test PASSED" -ForegroundColor Green
        } else {
            Write-Host "✗ Telnet connectivity test FAILED" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ Telnet test FAILED: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Main script logic
if ($Help) {
    Show-Help
    exit 0
}

if (-not $ProxyURL) {
    Write-Host "Error: Proxy URL is required" -ForegroundColor Red
    Show-Help
    exit 1
}

Write-Host "Proxy Testing Script for Windows" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

# Validate proxy URL format
if (-not ($ProxyURL.StartsWith("http://") -or $ProxyURL.StartsWith("https://") -or $ProxyURL.StartsWith("socks5://"))) {
    Write-Host "Error: Invalid proxy URL format. Must start with http://, https://, or socks5://" -ForegroundColor Red
    exit 1
}

# Run tests based on TestType
switch ($TestType.ToLower()) {
    "connectivity" {
        Test-ProxyConnectivity -ProxyURL $ProxyURL
    }
    "curl" {
        Test-ProxyWithCurl -ProxyURL $ProxyURL -TargetURL $TargetURL
    }
    "powershell" {
        Test-ProxyWithPowerShell -ProxyURL $ProxyURL -TargetURL $TargetURL
    }
    "go" {
        Test-ProxyWithGo -ProxyURL $ProxyURL -TargetURL $TargetURL
    }
    "netcat" {
        Test-ProxyWithNetcat -ProxyURL $ProxyURL
    }
    "telnet" {
        Test-ProxyWithTelnet -ProxyURL $ProxyURL
    }
    "all" {
        Write-Host "Running all proxy tests..." -ForegroundColor Yellow
        Write-Host ""
        
        # Test connectivity first
        Test-ProxyConnectivity -ProxyURL $ProxyURL
        Write-Host ""
        
        # Test with different tools
        Test-ProxyWithCurl -ProxyURL $ProxyURL -TargetURL $TargetURL
        Write-Host ""
        
        Test-ProxyWithPowerShell -ProxyURL $ProxyURL -TargetURL $TargetURL
        Write-Host ""
        
        Test-ProxyWithGo -ProxyURL $ProxyURL -TargetURL $TargetURL
        Write-Host ""
        
        Test-ProxyWithNetcat -ProxyURL $ProxyURL
        Write-Host ""
        
        Test-ProxyWithTelnet -ProxyURL $ProxyURL
    }
    default {
        Write-Host "Error: Unknown test type '$TestType'" -ForegroundColor Red
        Show-Help
        exit 1
    }
}

Write-Host ""
Write-Host "Testing completed!" -ForegroundColor Cyan 
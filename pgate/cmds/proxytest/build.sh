#!/bin/bash

# Proxy Testing Tool Cross-Compilation Script
# This script builds the proxy testing tool for multiple platforms

set -e

echo "Building Proxy Testing Tool (Cross-Compilation)..."
echo

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "Error: Go is not installed or not in PATH"
    echo "Please install Go from https://golang.org/"
    exit 1
fi

echo "Go version:"
go version
echo

# Set build parameters
BUILD_DIR="build"
VERSION="1.0.0"

# Create build directory
mkdir -p "$BUILD_DIR"

echo "Building for multiple platforms..."
echo

# Function to build for a specific platform
build_for_platform() {
    local os=$1
    local arch=$2
    local arm_version=$3
    local output_name=$4
    
    echo "Building for $os ($arch)..."
    
    # Set environment variables
    export GOOS=$os
    export GOARCH=$arch
    if [ -n "$arm_version" ]; then
        export GOARM=$arm_version
    fi
    
    # Build command
    local build_cmd="go build -ldflags \"-s -w -X main.Version=$VERSION\" -o \"$BUILD_DIR/$output_name\" main.go"
    
    if eval $build_cmd; then
        echo "✓ $os ($arch) build successful!"
    else
        echo "✗ $os ($arch) build failed!"
        return 1
    fi
    
    echo
}

# Build for different platforms
build_for_platform "windows" "amd64" "" "proxytest-windows-amd64.exe"
build_for_platform "darwin" "arm64" "" "proxytest-darwin-arm64"

# Reset environment variables
unset GOOS GOARCH GOARM

echo "Build Summary:"
echo "=============="
ls -la "$BUILD_DIR/"
echo

echo "✓ Cross-compilation completed!"
echo
echo "Usage examples:"
echo "  Windows: ./proxytest-windows-amd64.exe -proxy \"http://127.0.0.1:8080\""
echo "  macOS:   ./proxytest-darwin-arm64 -proxy \"http://127.0.0.1:8080\""
echo
echo "For more options, run: ./proxytest -help"

# Make Linux and macOS binaries executable
chmod +x "$BUILD_DIR"/proxytest-windows-*
chmod +x "$BUILD_DIR"/proxytest-darwin-* 
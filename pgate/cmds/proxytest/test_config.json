{"test_scenarios": [{"name": "HTTP Proxy Test", "proxy_url": "http://127.0.0.1:8080", "target_urls": ["https://httpbin.org/ip", "https://api.ipify.org", "https://ifconfig.me", "https://www.google.com"], "description": "Test HTTP proxy with common IP check services"}, {"name": "SOCKS5 Proxy Test", "proxy_url": "socks5://127.0.0.1:1080", "target_urls": ["https://httpbin.org/ip", "https://api.ipify.org", "https://ifconfig.me"], "description": "Test SOCKS5 proxy with IP check services"}, {"name": "Unified Proxy Test", "proxy_url": "http://127.0.0.1:8080", "socks5_url": "socks5://127.0.0.1:8080", "target_urls": ["https://httpbin.org/ip", "https://httpbin.org/user-agent", "https://httpbin.org/headers"], "description": "Test unified proxy (HTTP/HTTPS/SOCKS5 on same port)"}, {"name": "Performance Test", "proxy_url": "http://127.0.0.1:8080", "target_urls": ["https://httpbin.org/bytes/1000", "https://httpbin.org/bytes/10000", "https://httpbin.org/delay/1", "https://httpbin.org/delay/3"], "description": "Test proxy performance with different payload sizes and delays"}, {"name": "HTTPS Sites Test", "proxy_url": "http://127.0.0.1:8080", "target_urls": ["https://github.com", "https://stackoverflow.com", "https://www.reddit.com", "https://www.youtube.com"], "description": "Test proxy with popular HTTPS websites"}], "test_methods": ["connectivity", "curl", "powershell", "go"], "timeout": "10s", "output_format": "text"}
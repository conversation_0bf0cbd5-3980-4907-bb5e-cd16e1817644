package main

import (
	"bufio"
	"context"
	"crypto/tls"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"golang.org/x/net/proxy"
)

// Version will be set during build
var Version = "dev"

type TestResult struct {
	TestType   string        `json:"test_type"`
	ProxyURL   string        `json:"proxy_url"`
	TargetURL  string        `json:"target_url"`
	Success    bool          `json:"success"`
	Duration   time.Duration `json:"duration"`
	Error      string        `json:"error,omitempty"`
	StatusCode int           `json:"status_code,omitempty"`
	ResponseIP string        `json:"response_ip,omitempty"`
	Headers    http.Header   `json:"headers,omitempty"`
}

type ProxyTester struct {
	timeout time.Duration
}

func NewProxyTester(timeout time.Duration) *ProxyTester {
	return &ProxyTester{
		timeout: timeout,
	}
}

// TestHTTPProxy tests HTTP proxy functionality
func (pt *ProxyTester) TestHTTPProxy(proxyURL, targetURL string) TestResult {
	result := TestResult{
		TestType:  "HTTP",
		ProxyURL:  proxyURL,
		TargetURL: targetURL,
	}

	start := time.Now()
	defer func() {
		result.Duration = time.Since(start)
	}()

	// Parse proxy URL
	proxyURLParsed, err := url.Parse(proxyURL)
	if err != nil {
		result.Error = fmt.Sprintf("invalid proxy URL: %v", err)
		return result
	}

	// First test basic connectivity
	conn, err := net.DialTimeout("tcp", proxyURLParsed.Host, 5*time.Second)
	if err != nil {
		result.Error = fmt.Sprintf("failed to connect to proxy: %v", err)
		return result
	}
	conn.Close()

	// Create HTTP client with proxy
	client := &http.Client{
		Timeout: pt.timeout,
		Transport: &http.Transport{
			Proxy: http.ProxyURL(proxyURLParsed),
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true, // For testing purposes
			},
			// Add more detailed error handling
			DisableKeepAlives: false,
			MaxIdleConns:      10,
			IdleConnTimeout:   30 * time.Second,
		},
	}

	// Make request with more detailed error handling
	req, err := http.NewRequest("GET", targetURL, nil)
	if err != nil {
		result.Error = fmt.Sprintf("failed to create request: %v", err)
		return result
	}

	// Add some headers to help with debugging
	req.Header.Set("User-Agent", "ProxyTest/1.0")
	req.Header.Set("Accept", "*/*")

	resp, err := client.Do(req)
	if err != nil {
		// Try to get more detailed error information
		if netErr, ok := err.(net.Error); ok {
			if netErr.Timeout() {
				result.Error = fmt.Sprintf("HTTP request timeout: %v", err)
			} else {
				result.Error = fmt.Sprintf("HTTP network error: %v", err)
			}
		} else {
			result.Error = fmt.Sprintf("HTTP request failed: %v", err)
		}
		return result
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		result.Error = fmt.Sprintf("failed to read response body: %v", err)
		return result
	}

	result.Success = true
	result.StatusCode = resp.StatusCode
	result.Headers = resp.Header

	// Try to extract IP from response headers or body
	if ip := pt.extractIPFromResponse(resp, body); ip != "" {
		result.ResponseIP = ip
	}

	return result
}

// TestSOCKS5Proxy tests SOCKS5 proxy functionality
func (pt *ProxyTester) TestSOCKS5Proxy(proxyURL, targetURL string) TestResult {
	result := TestResult{
		TestType:  "SOCKS5",
		ProxyURL:  proxyURL,
		TargetURL: targetURL,
	}

	start := time.Now()
	defer func() {
		result.Duration = time.Since(start)
	}()

	// Parse proxy URL
	proxyURLParsed, err := url.Parse(proxyURL)
	if err != nil {
		result.Error = fmt.Sprintf("invalid proxy URL: %v", err)
		return result
	}

	// Create SOCKS5 dialer
	dialer, err := proxy.SOCKS5("tcp", proxyURLParsed.Host, nil, proxy.Direct)
	if err != nil {
		result.Error = fmt.Sprintf("failed to create SOCKS5 dialer: %v", err)
		return result
	}

	// Create HTTP client with SOCKS5 proxy
	client := &http.Client{
		Timeout: pt.timeout,
		Transport: &http.Transport{
			DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
				return dialer.(proxy.ContextDialer).DialContext(ctx, network, addr)
			},
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true, // For testing purposes
			},
		},
	}

	// Make request
	resp, err := client.Get(targetURL)
	if err != nil {
		result.Error = fmt.Sprintf("SOCKS5 request failed: %v", err)
		return result
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		result.Error = fmt.Sprintf("failed to read response body: %v", err)
		return result
	}

	result.Success = true
	result.StatusCode = resp.StatusCode
	result.Headers = resp.Header

	// Try to extract IP from response headers or body
	if ip := pt.extractIPFromResponse(resp, body); ip != "" {
		result.ResponseIP = ip
	}

	return result
}

// TestDirectConnection tests direct connection without proxy
func (pt *ProxyTester) TestDirectConnection(targetURL string) TestResult {
	result := TestResult{
		TestType:  "Direct",
		ProxyURL:  "none",
		TargetURL: targetURL,
	}

	start := time.Now()
	defer func() {
		result.Duration = time.Since(start)
	}()

	// Create HTTP client without proxy
	client := &http.Client{
		Timeout: pt.timeout,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true, // For testing purposes
			},
		},
	}

	// Make request
	resp, err := client.Get(targetURL)
	if err != nil {
		result.Error = fmt.Sprintf("direct request failed: %v", err)
		return result
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		result.Error = fmt.Sprintf("failed to read response body: %v", err)
		return result
	}

	result.Success = true
	result.StatusCode = resp.StatusCode
	result.Headers = resp.Header

	// Try to extract IP from response headers or body
	if ip := pt.extractIPFromResponse(resp, body); ip != "" {
		result.ResponseIP = ip
	}

	return result
}

// TestHTTPSProxy tests HTTPS proxy functionality (CONNECT method)
func (pt *ProxyTester) TestHTTPSProxy(proxyURL, targetURL string) TestResult {
	result := TestResult{
		TestType:  "HTTPS",
		ProxyURL:  proxyURL,
		TargetURL: targetURL,
	}

	start := time.Now()
	defer func() {
		result.Duration = time.Since(start)
	}()

	// Parse proxy URL
	proxyURLParsed, err := url.Parse(proxyURL)
	if err != nil {
		result.Error = fmt.Sprintf("invalid proxy URL: %v", err)
		return result
	}

	// First test basic connectivity
	conn, err := net.DialTimeout("tcp", proxyURLParsed.Host, 5*time.Second)
	if err != nil {
		result.Error = fmt.Sprintf("failed to connect to proxy: %v", err)
		return result
	}
	conn.Close()

	// Create HTTP client with proxy
	client := &http.Client{
		Timeout: pt.timeout,
		Transport: &http.Transport{
			Proxy: http.ProxyURL(proxyURLParsed),
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true, // For testing purposes
			},
			// Add more detailed error handling
			DisableKeepAlives: false,
			MaxIdleConns:      10,
			IdleConnTimeout:   30 * time.Second,
		},
	}

	// Make request with more detailed error handling
	req, err := http.NewRequest("GET", targetURL, nil)
	if err != nil {
		result.Error = fmt.Sprintf("failed to create request: %v", err)
		return result
	}

	// Add some headers to help with debugging
	req.Header.Set("User-Agent", "ProxyTest/1.0")
	req.Header.Set("Accept", "*/*")

	resp, err := client.Do(req)
	if err != nil {
		// Try to get more detailed error information
		if netErr, ok := err.(net.Error); ok {
			if netErr.Timeout() {
				result.Error = fmt.Sprintf("HTTPS request timeout: %v", err)
			} else {
				result.Error = fmt.Sprintf("HTTPS network error: %v", err)
			}
		} else {
			result.Error = fmt.Sprintf("HTTPS request failed: %v", err)
		}
		return result
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		result.Error = fmt.Sprintf("failed to read response body: %v", err)
		return result
	}

	result.Success = true
	result.StatusCode = resp.StatusCode
	result.Headers = resp.Header

	// Try to extract IP from response headers or body
	if ip := pt.extractIPFromResponse(resp, body); ip != "" {
		result.ResponseIP = ip
	}

	return result
}

// extractIPFromResponse tries to extract the IP address from response headers or body
func (pt *ProxyTester) extractIPFromResponse(resp *http.Response, body []byte) string {
	// Check common headers that might contain IP
	ipHeaders := []string{
		"X-Forwarded-For",
		"X-Real-IP",
		"CF-Connecting-IP", // Cloudflare
		"X-Client-IP",
		"X-Forwarded",
	}

	for _, header := range ipHeaders {
		if ip := resp.Header.Get(header); ip != "" {
			// Take the first IP if there are multiple
			if commaIdx := strings.Index(ip, ","); commaIdx > 0 {
				return strings.TrimSpace(ip[:commaIdx])
			}
			return strings.TrimSpace(ip)
		}
	}

	// Try to extract from response body (common IP check services)
	bodyStr := string(body)
	if strings.Contains(bodyStr, "ip") || strings.Contains(bodyStr, "IP") {
		// Simple string search for IP patterns
		if strings.Contains(bodyStr, "ip") {
			// This is a simplified version - in a real implementation you'd use regex
			// For now, just return a placeholder
			return "extracted_from_body"
		}
	}

	return ""
}

// TestProxyConnectivity tests basic connectivity to proxy server
func (pt *ProxyTester) TestProxyConnectivity(proxyURL string) TestResult {
	result := TestResult{
		TestType: "Connectivity",
		ProxyURL: proxyURL,
	}

	start := time.Now()
	defer func() {
		result.Duration = time.Since(start)
	}()

	// Parse proxy URL
	proxyURLParsed, err := url.Parse(proxyURL)
	if err != nil {
		result.Error = fmt.Sprintf("invalid proxy URL: %v", err)
		return result
	}

	// Test basic TCP connectivity
	conn, err := net.DialTimeout("tcp", proxyURLParsed.Host, pt.timeout)
	if err != nil {
		result.Error = fmt.Sprintf("failed to connect to proxy: %v", err)
		return result
	}
	defer conn.Close()

	result.Success = true
	return result
}

func main() {
	var (
		proxyURL     = flag.String("proxy", "", "Proxy URL (e.g., http://127.0.0.1:8080 or socks5://127.0.0.1:1080)")
		targetURL    = flag.String("target", "https://httpbin.org/ip", "Target URL to test")
		timeout      = flag.Duration("timeout", 10*time.Second, "Request timeout")
		outputFormat = flag.String("format", "text", "Output format: text, json")
		testType     = flag.String("type", "auto", "Test type: http, https, socks5, connectivity, auto, all")
		interactive  = flag.Bool("interactive", false, "Interactive mode")
		showVersion  = flag.Bool("version", false, "Show version information")
	)
	flag.Parse()

	if *showVersion {
		fmt.Printf("Proxy Testing Tool v%s\n", Version)
		fmt.Printf("Built for: %s/%s\n", os.Getenv("GOOS"), os.Getenv("GOARCH"))
		return
	}

	tester := NewProxyTester(*timeout)

	if *interactive {
		runInteractiveMode(tester)
		return
	}

	if *proxyURL == "" {
		log.Fatal("Proxy URL is required. Use -proxy flag or -interactive mode.")
	}

	var results []TestResult

	switch *testType {
	case "connectivity":
		results = append(results, tester.TestProxyConnectivity(*proxyURL))
	case "http":
		results = append(results, tester.TestHTTPProxy(*proxyURL, *targetURL))
	case "https":
		results = append(results, tester.TestHTTPSProxy(*proxyURL, *targetURL))
	case "socks5":
		results = append(results, tester.TestSOCKS5Proxy(*proxyURL, *targetURL))
	case "auto":
		// Auto-detect proxy type and test accordingly
		if strings.HasPrefix(*proxyURL, "socks5://") {
			results = append(results, tester.TestSOCKS5Proxy(*proxyURL, *targetURL))
		} else {
			// For unified proxy, test connectivity first, then HTTP and HTTPS
			results = append(results, tester.TestProxyConnectivity(*proxyURL))
			if results[0].Success {
				results = append(results, tester.TestHTTPProxy(*proxyURL, *targetURL))
				results = append(results, tester.TestHTTPSProxy(*proxyURL, *targetURL))
			}
		}
	case "all":
		// Test all types
		results = append(results, tester.TestProxyConnectivity(*proxyURL))
		results = append(results, tester.TestHTTPProxy(*proxyURL, *targetURL))
		results = append(results, tester.TestHTTPSProxy(*proxyURL, *targetURL))
		results = append(results, tester.TestSOCKS5Proxy(*proxyURL, *targetURL))
	default:
		log.Fatalf("Unknown test type: %s", *testType)
	}

	// Output results
	outputResults(results, *outputFormat)
}

func runInteractiveMode(tester *ProxyTester) {
	fmt.Println("=== Proxy Testing Tool (Interactive Mode) ===")
	fmt.Println("Available commands:")
	fmt.Println("  test <proxy_url> [target_url] - Test proxy")
	fmt.Println("  direct <target_url> - Test direct connection")
	fmt.Println("  connectivity <proxy_url> - Test proxy connectivity")
	fmt.Println("  quit - Exit")
	fmt.Println()

	scanner := bufio.NewScanner(os.Stdin)
	for {
		fmt.Print("> ")
		if !scanner.Scan() {
			break
		}

		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) == 0 {
			continue
		}

		command := parts[0]
		switch command {
		case "quit", "exit":
			fmt.Println("Goodbye!")
			return
		case "test":
			if len(parts) < 2 {
				fmt.Println("Usage: test <proxy_url> [target_url]")
				continue
			}
			proxyURL := parts[1]
			targetURL := "https://httpbin.org/ip"
			if len(parts) >= 3 {
				targetURL = parts[2]
			}

			var results []TestResult
			if strings.HasPrefix(proxyURL, "socks5://") {
				results = append(results, tester.TestSOCKS5Proxy(proxyURL, targetURL))
			} else {
				results = append(results, tester.TestHTTPProxy(proxyURL, targetURL))
			}
			outputResults(results, "text")

		case "direct":
			if len(parts) < 2 {
				fmt.Println("Usage: direct <target_url>")
				continue
			}
			targetURL := parts[1]
			results := []TestResult{tester.TestDirectConnection(targetURL)}
			outputResults(results, "text")

		case "connectivity":
			if len(parts) < 2 {
				fmt.Println("Usage: connectivity <proxy_url>")
				continue
			}
			proxyURL := parts[1]
			results := []TestResult{tester.TestProxyConnectivity(proxyURL)}
			outputResults(results, "text")

		default:
			fmt.Printf("Unknown command: %s\n", command)
		}
	}
}

func outputResults(results []TestResult, format string) {
	switch format {
	case "json":
		jsonData, err := json.MarshalIndent(results, "", "  ")
		if err != nil {
			log.Printf("Failed to marshal results: %v", err)
			return
		}
		fmt.Println(string(jsonData))

	case "text":
		for _, result := range results {
			fmt.Printf("\n=== %s Test ===\n", result.TestType)
			fmt.Printf("Proxy URL: %s\n", result.ProxyURL)
			fmt.Printf("Target URL: %s\n", result.TargetURL)
			fmt.Printf("Success: %t\n", result.Success)
			fmt.Printf("Duration: %v\n", result.Duration)

			if result.Error != "" {
				fmt.Printf("Error: %s\n", result.Error)
			}

			if result.StatusCode > 0 {
				fmt.Printf("Status Code: %d\n", result.StatusCode)
			}

			if result.ResponseIP != "" {
				fmt.Printf("Response IP: %s\n", result.ResponseIP)
			}

			if len(result.Headers) > 0 {
				fmt.Printf("Response Headers:\n")
				for key, values := range result.Headers {
					for _, value := range values {
						fmt.Printf("  %s: %s\n", key, value)
					}
				}
			}
		}

	default:
		log.Printf("Unknown output format: %s", format)
	}
}

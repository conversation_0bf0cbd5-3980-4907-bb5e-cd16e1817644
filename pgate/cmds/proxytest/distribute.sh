#!/bin/bash

# Proxy Testing Tool Distribution Script
# This script creates platform-specific distribution packages

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
VERSION=${VERSION:-"1.0.0"}
BUILD_DIR="build"
DIST_DIR="dist"
SCRIPT_DIR="scripts"

echo -e "${BLUE}Proxy Testing Tool Distribution Script${NC}"
echo -e "${BLUE}=======================================${NC}"
echo ""

# Check if build directory exists
if [[ ! -d "$BUILD_DIR" ]]; then
    echo -e "${RED}Error: Build directory '$BUILD_DIR' not found${NC}"
    echo "Please run the build script first:"
    echo "  make build"
    echo "  or"
    echo "  ./build.sh"
    exit 1
fi

# Create distribution directory
mkdir -p "$DIST_DIR"

echo -e "${YELLOW}Creating distribution packages...${NC}"
echo ""

# Function to create Windows distribution
create_windows_dist() {
    local arch="$1"
    local dist_name="proxytest-windows-${arch}-${VERSION}"
    local dist_path="$DIST_DIR/$dist_name"
    
    echo -e "${CYAN}Creating Windows ${arch} distribution...${NC}"
    
    # Create distribution directory
    mkdir -p "$dist_path"
    
    # Copy binary
    cp "$BUILD_DIR/proxytest-windows-${arch}.exe" "$dist_path/proxytest.exe"
    
    # Copy Windows scripts
    cp "$SCRIPT_DIR/windows/test_proxy.bat" "$dist_path/"
    cp "$SCRIPT_DIR/windows/test_proxy.ps1" "$dist_path/"
    
    # Copy documentation
    cp README.md "$dist_path/"
    cp test_config.json "$dist_path/"
    
    # Create Windows-specific README
    cat > "$dist_path/README-Windows.md" << EOF
# Proxy Testing Tool for Windows ${arch}

## Quick Start

### Using Batch File (Recommended)
\`\`\`cmd
test_proxy.bat http://127.0.0.1:8080
test_proxy.bat socks5://127.0.0.1:1080
\`\`\`

### Using PowerShell
\`\`\`powershell
.\\test_proxy.ps1 -ProxyURL "http://127.0.0.1:8080"
.\\test_proxy.ps1 -ProxyURL "socks5://127.0.0.1:1080"
\`\`\`

### Using Go Tool
\`\`\`cmd
proxytest.exe -proxy "http://127.0.0.1:8080"
proxytest.exe -interactive
\`\`\`

## Requirements
- Windows 7 or later
- PowerShell 2.0 or later (for PowerShell tests)
- cURL (usually pre-installed on Windows 10/11)

## Files
- \`proxytest.exe\` - Main testing tool
- \`test_proxy.bat\` - Batch script for testing
- \`test_proxy.ps1\` - PowerShell script for testing
- \`README.md\` - Full documentation
- \`test_config.json\` - Sample configuration

## Version
v${VERSION} (${arch})
EOF
    
    # Create ZIP archive
    cd "$DIST_DIR"
    zip -r "${dist_name}.zip" "$dist_name" > /dev/null
    cd - > /dev/null
    
    echo -e "${GREEN}✓ Created: $DIST_DIR/${dist_name}.zip${NC}"
    echo ""
}

# Function to create Linux distribution
create_linux_dist() {
    local arch="$1"
    local dist_name="proxytest-linux-${arch}-${VERSION}"
    local dist_path="$DIST_DIR/$dist_name"
    
    echo -e "${CYAN}Creating Linux ${arch} distribution...${NC}"
    
    # Create distribution directory
    mkdir -p "$dist_path"
    
    # Copy binary
    cp "$BUILD_DIR/proxytest-linux-${arch}" "$dist_path/proxytest"
    chmod +x "$dist_path/proxytest"
    
    # Copy Linux script
    cp "$SCRIPT_DIR/linux/test_proxy.sh" "$dist_path/"
    chmod +x "$dist_path/test_proxy.sh"
    
    # Copy documentation
    cp README.md "$dist_path/"
    cp test_config.json "$dist_path/"
    
    # Create Linux-specific README
    cat > "$dist_path/README-Linux.md" << EOF
# Proxy Testing Tool for Linux ${arch}

## Quick Start

### Using Shell Script (Recommended)
\`\`\`bash
chmod +x test_proxy.sh
./test_proxy.sh "http://127.0.0.1:8080"
./test_proxy.sh "socks5://127.0.0.1:1080"
\`\`\`

### Using Go Tool
\`\`\`bash
chmod +x proxytest
./proxytest -proxy "http://127.0.0.1:8080"
./proxytest -interactive
\`\`\`

## Requirements
- Linux (glibc-based)
- bash
- curl (for curl tests)
- wget (for wget tests)
- netcat (optional, for connectivity tests)
- telnet (optional, for connectivity tests)

## Files
- \`proxytest\` - Main testing tool
- \`test_proxy.sh\` - Shell script for testing
- \`README.md\` - Full documentation
- \`test_config.json\` - Sample configuration

## Installation
\`\`\`bash
# Extract the archive
tar -xzf ${dist_name}.tar.gz

# Make executable
chmod +x proxytest test_proxy.sh

# Run tests
./test_proxy.sh "http://127.0.0.1:8080"
\`\`\`

## Version
v${VERSION} (${arch})
EOF
    
    # Create tar.gz archive
    cd "$DIST_DIR"
    tar -czf "${dist_name}.tar.gz" "$dist_name" > /dev/null
    cd - > /dev/null
    
    echo -e "${GREEN}✓ Created: $DIST_DIR/${dist_name}.tar.gz${NC}"
    echo ""
}

# Function to create macOS distribution
create_darwin_dist() {
    local arch="$1"
    local dist_name="proxytest-darwin-${arch}-${VERSION}"
    local dist_path="$DIST_DIR/$dist_name"
    
    echo -e "${CYAN}Creating macOS ${arch} distribution...${NC}"
    
    # Create distribution directory
    mkdir -p "$dist_path"
    
    # Copy binary
    cp "$BUILD_DIR/proxytest-darwin-${arch}" "$dist_path/proxytest"
    chmod +x "$dist_path/proxytest"
    
    # Copy Linux script (works on macOS too)
    cp "$SCRIPT_DIR/linux/test_proxy.sh" "$dist_path/"
    chmod +x "$dist_path/test_proxy.sh"
    
    # Copy documentation
    cp README.md "$dist_path/"
    cp test_config.json "$dist_path/"
    
    # Create macOS-specific README
    cat > "$dist_path/README-macOS.md" << EOF
# Proxy Testing Tool for macOS ${arch}

## Quick Start

### Using Shell Script (Recommended)
\`\`\`bash
chmod +x test_proxy.sh
./test_proxy.sh "http://127.0.0.1:8080"
./test_proxy.sh "socks5://127.0.0.1:1080"
\`\`\`

### Using Go Tool
\`\`\`bash
chmod +x proxytest
./proxytest -proxy "http://127.0.0.1:8080"
./proxytest -interactive
\`\`\`

## Requirements
- macOS 10.12 or later
- bash
- curl (pre-installed)
- wget (install via Homebrew: \`brew install wget\`)
- netcat (optional, install via Homebrew: \`brew install netcat\`)

## Files
- \`proxytest\` - Main testing tool
- \`test_proxy.sh\` - Shell script for testing
- \`README.md\` - Full documentation
- \`test_config.json\` - Sample configuration

## Installation
\`\`\`bash
# Extract the archive
tar -xzf ${dist_name}.tar.gz

# Make executable
chmod +x proxytest test_proxy.sh

# Run tests
./test_proxy.sh "http://127.0.0.1:8080"
\`\`\`

## Homebrew Installation (Optional)
\`\`\`bash
# Install additional tools
brew install wget netcat

# Or install all tools
brew install curl wget netcat telnet
\`\`\`

## Version
v${VERSION} (${arch})
EOF
    
    # Create tar.gz archive
    cd "$DIST_DIR"
    tar -czf "${dist_name}.tar.gz" "$dist_name" > /dev/null
    cd - > /dev/null
    
    echo -e "${GREEN}✓ Created: $DIST_DIR/${dist_name}.tar.gz${NC}"
    echo ""
}

# Create distributions for all platforms
create_windows_dist "amd64"
create_windows_dist "386"
create_linux_dist "amd64"
create_linux_dist "386"
create_linux_dist "arm64"
create_linux_dist "armv7"
create_darwin_dist "amd64"
create_darwin_dist "arm64"

# Create a universal distribution with all binaries
echo -e "${CYAN}Creating universal distribution...${NC}"
universal_dist_name="proxytest-universal-${VERSION}"
universal_dist_path="$DIST_DIR/$universal_dist_name"

mkdir -p "$universal_dist_path"

# Copy all binaries
cp "$BUILD_DIR"/* "$universal_dist_path/"

# Copy all scripts
mkdir -p "$universal_dist_path/scripts/windows"
mkdir -p "$universal_dist_path/scripts/linux"
cp "$SCRIPT_DIR/windows"/* "$universal_dist_path/scripts/windows/"
cp "$SCRIPT_DIR/linux"/* "$universal_dist_path/scripts/linux/"

# Copy documentation
cp README.md "$universal_dist_path/"
cp test_config.json "$universal_dist_path/"

# Create universal README
cat > "$universal_dist_path/README-Universal.md" << EOF
# Proxy Testing Tool - Universal Distribution

This package contains binaries and scripts for all supported platforms.

## Supported Platforms

### Windows
- \`proxytest-windows-amd64.exe\` - Windows 64-bit
- \`proxytest-windows-386.exe\` - Windows 32-bit
- \`scripts/windows/test_proxy.bat\` - Windows batch script
- \`scripts/windows/test_proxy.ps1\` - Windows PowerShell script

### Linux
- \`proxytest-linux-amd64\` - Linux x86_64
- \`proxytest-linux-386\` - Linux x86
- \`proxytest-linux-arm64\` - Linux ARM64
- \`proxytest-linux-armv7\` - Linux ARMv7
- \`scripts/linux/test_proxy.sh\` - Linux/macOS shell script

### macOS
- \`proxytest-darwin-amd64\` - macOS Intel
- \`proxytest-darwin-arm64\` - macOS Apple Silicon
- \`scripts/linux/test_proxy.sh\` - Linux/macOS shell script

## Usage

### Windows
\`\`\`cmd
# Use the appropriate binary
proxytest-windows-amd64.exe -proxy "http://127.0.0.1:8080"

# Or use the batch script
scripts\\windows\\test_proxy.bat http://127.0.0.1:8080
\`\`\`

### Linux
\`\`\`bash
# Use the appropriate binary
chmod +x proxytest-linux-amd64
./proxytest-linux-amd64 -proxy "http://127.0.0.1:8080"

# Or use the shell script
chmod +x scripts/linux/test_proxy.sh
./scripts/linux/test_proxy.sh "http://127.0.0.1:8080"
\`\`\`

### macOS
\`\`\`bash
# Use the appropriate binary
chmod +x proxytest-darwin-amd64
./proxytest-darwin-amd64 -proxy "http://127.0.0.1:8080"

# Or use the shell script
chmod +x scripts/linux/test_proxy.sh
./scripts/linux/test_proxy.sh "http://127.0.0.1:8080"
\`\`\`

## Version
v${VERSION}
EOF

# Create universal archive
cd "$DIST_DIR"
tar -czf "${universal_dist_name}.tar.gz" "$universal_dist_name" > /dev/null
cd - > /dev/null

echo -e "${GREEN}✓ Created: $DIST_DIR/${universal_dist_name}.tar.gz${NC}"
echo ""

# Show summary
echo -e "${BLUE}Distribution Summary:${NC}"
echo "========================"
echo ""
echo -e "${GREEN}Windows distributions:${NC}"
ls -la "$DIST_DIR"/*windows*.zip 2>/dev/null || echo "No Windows distributions found"
echo ""
echo -e "${GREEN}Linux distributions:${NC}"
ls -la "$DIST_DIR"/*linux*.tar.gz 2>/dev/null || echo "No Linux distributions found"
echo ""
echo -e "${GREEN}macOS distributions:${NC}"
ls -la "$DIST_DIR"/*darwin*.tar.gz 2>/dev/null || echo "No macOS distributions found"
echo ""
echo -e "${GREEN}Universal distribution:${NC}"
ls -la "$DIST_DIR"/*universal*.tar.gz 2>/dev/null || echo "No universal distribution found"
echo ""

echo -e "${CYAN}✓ Distribution completed!${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Test the distributions on target platforms"
echo "2. Upload to your distribution platform"
echo "3. Update documentation with download links" 
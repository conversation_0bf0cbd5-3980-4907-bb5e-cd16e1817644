// This file contains a fix for the unified proxy server to properly handle HTTP CONNECT requests
// The issue is that httputil.ReverseProxy doesn't handle CONNECT requests for HTTPS tunneling

package main

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/http/httputil"
	"strings"
	"sync"
	"time"

	"github.com/armon/go-socks5"
	"golang.org/x/net/proxy"
)

// FixedUnifiedServer is a corrected version of the unified server
type FixedUnifiedServer struct {
	port         int
	socks5Server *socks5.Server
	dialer       proxy.Dialer
}

// NewFixedUnifiedServer creates a new fixed unified server
func NewFixedUnifiedServer(port int, dialer proxy.Dialer) *FixedUnifiedServer {
	return &FixedUnifiedServer{
		port:   port,
		dialer: dialer,
	}
}

// Start starts the fixed unified server
func (f *FixedUnifiedServer) Start() error {
	// Create SOCKS5 server
	socks5Conf := &socks5.Config{
		Dial: func(ctx context.Context, network, addr string) (net.Conn, error) {
			return f.dialer.Dial(network, addr)
		},
	}
	var err error
	f.socks5Server, err = socks5.New(socks5Conf)
	if err != nil {
		return fmt.Errorf("failed to create SOCKS5 server: %v", err)
	}

	// HTTP handler is created inline in handleConnection

	// Start the server
	go func() {
		fmt.Printf("starting fixed unified server on port %d\n", f.port)

		listener, err := net.Listen("tcp", fmt.Sprintf("127.0.0.1:%d", f.port))
		if err != nil {
			fmt.Printf("failed to create listener: %v\n", err)
			return
		}
		defer listener.Close()

		for {
			conn, err := listener.Accept()
			if err != nil {
				if !strings.Contains(err.Error(), "use of closed network connection") {
					fmt.Printf("accept error: %v\n", err)
				}
				continue
			}

			go f.handleConnection(conn)
		}
	}()

	return nil
}

// handleConnection handles incoming connections with protocol detection
func (f *FixedUnifiedServer) handleConnection(clientConn net.Conn) {
	defer clientConn.Close()

	// Use a buffered reader to peek at the first byte
	br := bufio.NewReader(clientConn)
	peeked, err := br.Peek(1)
	if err != nil {
		fmt.Printf("failed to peek: %v\n", err)
		return
	}

	// Create a new connection that includes the buffered data
	bufferedConn := &bufferedConn{br, clientConn}

	// Check if it's SOCKS5 (first byte is 0x05)
	if peeked[0] == 0x05 {
		fmt.Printf("SOCKS5 protocol detected\n")
		// Handle SOCKS5 request
		if err := f.socks5Server.ServeConn(bufferedConn); err != nil {
			fmt.Printf("SOCKS5 serve error: %v\n", err)
		}
	} else {
		fmt.Printf("HTTP protocol detected\n")
		// Handle HTTP request with our fixed handler
		http.Serve(&oneShotListener{conn: bufferedConn}, &FixedHTTPHandler{dialer: f.dialer})
	}
}

// FixedHTTPHandler handles HTTP requests including CONNECT
type FixedHTTPHandler struct {
	dialer proxy.Dialer
}

// ServeHTTP handles HTTP requests
func (h *FixedHTTPHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	fmt.Printf("HTTP request: %s %s\n", r.Method, r.URL.String())

	if r.Method == "CONNECT" {
		h.handleCONNECT(w, r)
		return
	}

	// Handle regular HTTP requests
	h.handleHTTP(w, r)
}

// handleCONNECT handles CONNECT requests for HTTPS tunneling
func (h *FixedHTTPHandler) handleCONNECT(w http.ResponseWriter, r *http.Request) {
	// Extract target host and port
	target := r.URL.Host
	if r.URL.Port() == "" {
		target = r.URL.Host + ":443" // Default HTTPS port
	}

	fmt.Printf("CONNECT request to: %s\n", target)

	// Hijack the connection
	hijacker, ok := w.(http.Hijacker)
	if !ok {
		http.Error(w, "Hijacking not supported", http.StatusInternalServerError)
		return
	}

	clientConn, _, err := hijacker.Hijack()
	if err != nil {
		http.Error(w, err.Error(), http.StatusServiceUnavailable)
		return
	}
	defer clientConn.Close()

	// Send 200 OK to client
	clientConn.Write([]byte("HTTP/1.1 200 Connection established\r\n\r\n"))

	// Connect to target
	targetConn, err := h.dialer.Dial("tcp", target)
	if err != nil {
		fmt.Printf("failed to connect to target %s: %v\n", target, err)
		clientConn.Close()
		return
	}
	defer targetConn.Close()

	// Start bidirectional data transfer
	go func() {
		io.Copy(targetConn, clientConn)
	}()
	io.Copy(clientConn, targetConn)
}

// handleHTTP handles regular HTTP requests
func (h *FixedHTTPHandler) handleHTTP(w http.ResponseWriter, r *http.Request) {
	// Create transport with our dialer
	transport := &http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			return h.dialer.Dial(network, addr)
		},
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	// Create reverse proxy
	reverseProxy := &httputil.ReverseProxy{
		Director: func(req *http.Request) {
			// Preserve original request
			fmt.Printf("HTTP request: %s %s\n", req.Method, req.URL.String())
		},
		Transport: transport,
		ErrorHandler: func(w http.ResponseWriter, r *http.Request, err error) {
			fmt.Printf("HTTP error: %v\n", err)
			http.Error(w, fmt.Sprintf("proxy error: %v", err), http.StatusBadGateway)
		},
	}

	reverseProxy.ServeHTTP(w, r)
}

// bufferedConn wraps a net.Conn and a bufio.Reader
type bufferedConn struct {
	r *bufio.Reader
	net.Conn
}

func (b *bufferedConn) Read(data []byte) (int, error) {
	return b.r.Read(data)
}

// oneShotListener implements net.Listener but only returns a single connection.
type oneShotListener struct {
	conn net.Conn
	once sync.Once
}

func (l *oneShotListener) Accept() (net.Conn, error) {
	var conn net.Conn
	l.once.Do(func() {
		conn = l.conn
	})
	if conn != nil {
		return conn, nil
	}
	return nil, io.EOF
}

func (l *oneShotListener) Close() error {
	return l.conn.Close()
}

func (l *oneShotListener) Addr() net.Addr {
	return l.conn.LocalAddr()
}

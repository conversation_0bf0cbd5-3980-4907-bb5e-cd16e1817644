# Proxy Testing Tools for Windows

This directory contains comprehensive tools for testing HTTP, HTTPS, and SOCKS5 proxies on Windows systems.

## Overview

The proxy testing tools include:
- **Go-based test tool** (`main.go`) - Comprehensive proxy testing with multiple protocols
- **PowerShell script** (`test_proxy.ps1`) - Native Windows testing with multiple methods
- **Batch file** (`test_proxy.bat`) - Simple command-line testing
- **Interactive mode** - Built into the Go tool for interactive testing

## Quick Start

### 1. Using the Batch File (Easiest)

```cmd
# Test HTTP proxy
test_proxy.bat http://127.0.0.1:8080

# Test SOCKS5 proxy
test_proxy.bat socks5://127.0.0.1:1080

# Test with custom target URL
test_proxy.bat http://127.0.0.1:8080 https://api.ipify.org

# Test specific method only
test_proxy.bat http://127.0.0.1:8080 https://httpbin.org/ip curl
```

### 2. Using PowerShell Script

```powershell
# Test all methods
.\test_proxy.ps1 -ProxyURL "http://127.0.0.1:8080"

# Test specific method
.\test_proxy.ps1 -ProxyURL "socks5://127.0.0.1:1080" -TestType "curl"

# Test with custom target
.\test_proxy.ps1 -ProxyURL "http://127.0.0.1:8080" -TargetURL "https://api.ipify.org"
```

### 3. Using Go Tool

#### Building for Current Platform
```cmd
# Build the tool
go build -o proxytest.exe main.go

# Test HTTP proxy
proxytest.exe -proxy "http://127.0.0.1:8080"

# Test SOCKS5 proxy
proxytest.exe -proxy "socks5://127.0.0.1:1080"

# Interactive mode
proxytest.exe -interactive

# Show version
proxytest.exe -version
```

#### Cross-Compilation Options

**Using Makefile (Recommended):**
```bash
# Build for all platforms
make

# Build for specific platform
make windows
make linux
make darwin

# Build for specific architecture
make windows-amd64
make linux-arm64

# Show help
make help
```

**Using Build Scripts:**
```bash
# Windows (PowerShell/CMD)
build.bat

# Linux/macOS
chmod +x build.sh
./build.sh
```

**Manual Cross-Compilation:**
```bash
# Windows amd64
GOOS=windows GOARCH=amd64 go build -o proxytest-windows-amd64.exe main.go

# Linux amd64
GOOS=linux GOARCH=amd64 go build -o proxytest-linux-amd64 main.go

# macOS amd64
GOOS=darwin GOARCH=amd64 go build -o proxytest-darwin-amd64 main.go

# Linux ARM64
GOOS=linux GOARCH=arm64 go build -o proxytest-linux-arm64 main.go
```

## Testing Methods

### 1. Connectivity Testing
Tests basic TCP connectivity to the proxy server.

**Tools that support this:**
- PowerShell script
- Batch file
- Go tool
- Netcat (if available)
- Telnet (if available)

### 2. HTTP/HTTPS Proxy Testing
Tests HTTP and HTTPS proxy functionality.

**Tools that support this:**
- cURL
- PowerShell
- Go tool

### 3. SOCKS5 Proxy Testing
Tests SOCKS5 proxy functionality.

**Tools that support this:**
- cURL (with --socks5 flag)
- Go tool
- PowerShell (limited support)

## Detailed Testing Instructions

### Prerequisites

1. **cURL** - Usually pre-installed on Windows 10/11
   ```cmd
   curl --version
   ```

2. **PowerShell** - Built into Windows
   ```powershell
   $PSVersionTable.PSVersion
   ```

3. **Go** (for Go tool) - Download from https://golang.org/
   ```cmd
   go version
   ```

4. **Optional tools:**
   - Netcat (nc.exe) - Download from https://eternallybored.org/misc/netcat/
   - Telnet - Enable via Windows Features

### Testing Your Unified Proxy Server

If you're using the unified proxy server (HTTP/HTTPS/SOCKS5 on same port):

```cmd
# Test HTTP functionality
test_proxy.bat http://127.0.0.1:8080

# Test SOCKS5 functionality  
test_proxy.bat socks5://127.0.0.1:8080

# Test both on the same port
test_proxy.bat http://127.0.0.1:8080
test_proxy.bat socks5://127.0.0.1:8080
```

### Testing Different Target URLs

```cmd
# Test with IP check services
test_proxy.bat http://127.0.0.1:8080 https://httpbin.org/ip
test_proxy.bat http://127.0.0.1:8080 https://api.ipify.org
test_proxy.bat http://127.0.0.1:8080 https://ifconfig.me

# Test with Google
test_proxy.bat http://127.0.0.1:8080 https://www.google.com

# Test with HTTPS sites
test_proxy.bat http://127.0.0.1:8080 https://github.com
```

### Manual Testing with cURL

```cmd
# Test HTTP proxy
curl.exe --proxy http://127.0.0.1:8080 https://httpbin.org/ip

# Test SOCKS5 proxy
curl.exe --socks5 127.0.0.1:1080 https://httpbin.org/ip

# Test with verbose output
curl.exe --proxy http://127.0.0.1:8080 -v https://httpbin.org/ip

# Test with custom headers
curl.exe --proxy http://127.0.0.1:8080 -H "User-Agent: TestBot" https://httpbin.org/user-agent
```

### Manual Testing with PowerShell

```powershell
# Set proxy environment variables
$env:HTTP_PROXY = "http://127.0.0.1:8080"
$env:HTTPS_PROXY = "http://127.0.0.1:8080"

# Test HTTP request
Invoke-WebRequest -Uri "https://httpbin.org/ip" -UseBasicParsing

# Test with custom headers
$headers = @{
    "User-Agent" = "PowerShell-Test"
}
Invoke-WebRequest -Uri "https://httpbin.org/user-agent" -Headers $headers -UseBasicParsing

# Clear proxy settings
Remove-Item Env:HTTP_PROXY
Remove-Item Env:HTTPS_PROXY
```

### Manual Testing with Browser

1. **Chrome/Edge:**
   - Install a proxy extension like "Proxy SwitchyOmega"
   - Configure proxy settings
   - Visit https://httpbin.org/ip to see your IP

2. **Firefox:**
   - Go to Settings > Network Settings
   - Configure proxy manually
   - Visit https://httpbin.org/ip

### Testing Network Connectivity

```cmd
# Test basic connectivity
telnet 127.0.0.1 8080

# Test with netcat (if available)
nc -zv 127.0.0.1 8080

# Test with PowerShell
powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.ConnectAsync('127.0.0.1', 8080).Wait(5000); if ($tcp.Connected) { Write-Host 'Connected'; $tcp.Close() } else { Write-Host 'Failed' } } catch { Write-Host 'Error:' $_.Exception.Message }"
```

## Troubleshooting

### Common Issues

1. **"Connection refused"**
   - Proxy server is not running
   - Wrong port number
   - Firewall blocking connection

2. **"Timeout"**
   - Proxy server is overloaded
   - Network issues
   - Target server is slow

3. **"Authentication required"**
   - Proxy requires username/password
   - Add credentials to proxy URL: `*******************************`

4. **"SOCKS5 not supported"**
   - Some tools only support HTTP proxies
   - Use cURL with `--socks5` flag for SOCKS5 testing

### Debug Steps

1. **Check if proxy server is running:**
   ```cmd
   netstat -an | findstr :8080
   ```

2. **Check Windows Firewall:**
   ```cmd
   netsh advfirewall firewall show rule name=all | findstr "Proxy"
   ```

3. **Test with different tools:**
   ```cmd
   # Try multiple testing methods
   test_proxy.bat http://127.0.0.1:8080
   curl.exe --proxy http://127.0.0.1:8080 https://httpbin.org/ip
   ```

4. **Check proxy server logs:**
   - Look for connection attempts in your proxy server logs
   - Check for error messages

### Performance Testing

```cmd
# Test response time
powershell -Command "Measure-Command { curl.exe --proxy http://127.0.0.1:8080 https://httpbin.org/ip }"

# Test throughput
curl.exe --proxy http://127.0.0.1:8080 -o nul -s -w "Time: %{time_total}s\n" https://httpbin.org/bytes/1000000
```

## Advanced Usage

### Go Tool Interactive Mode

```cmd
proxytest.exe -interactive
```

Available commands:
- `test <proxy_url> [target_url]` - Test proxy
- `direct <target_url>` - Test direct connection
- `connectivity <proxy_url>` - Test proxy connectivity
- `quit` - Exit

### JSON Output

```cmd
proxytest.exe -proxy "http://127.0.0.1:8080" -format json
```

### Custom Timeouts

```cmd
proxytest.exe -proxy "http://127.0.0.1:8080" -timeout 30s
```

## Cross-Compilation

The proxy testing tool supports cross-compilation for multiple platforms and architectures.

### Supported Platforms

- **Windows**: amd64, 386
- **Linux**: amd64, 386, arm64, armv7
- **macOS**: amd64, arm64

### Build Artifacts

After cross-compilation, you'll find binaries in the `build/` directory:

```
build/
├── proxytest-windows-amd64.exe
├── proxytest-windows-386.exe
├── proxytest-linux-amd64
├── proxytest-linux-386
├── proxytest-linux-arm64
├── proxytest-linux-armv7
├── proxytest-darwin-amd64
└── proxytest-darwin-arm64
```

### Distribution

You can distribute the appropriate binary for each target platform:

- **Windows users**: `proxytest-windows-amd64.exe`
- **Linux x86_64**: `proxytest-linux-amd64`
- **Linux ARM64**: `proxytest-linux-arm64`
- **macOS Intel**: `proxytest-darwin-amd64`
- **macOS Apple Silicon**: `proxytest-darwin-arm64`

### Version Information

Each binary includes version information:
```bash
./proxytest-linux-amd64 -version
# Output: Proxy Testing Tool v1.0.0
#         Built for: linux/amd64
```

## Integration with Your Proxy Gateway

If you're testing the proxy gateway from this codebase:

1. **Start the proxy gateway:**
   ```cmd
   cd ../../quanter
   go run main.go
   ```

2. **Login to get a forwarder:**
   ```cmd
   curl -X POST "http://localhost:8080/login?proxy_group=your_group&uid=test&project=test"
   ```

3. **Test the forwarder:**
   ```cmd
   test_proxy.bat http://127.0.0.1:PORT
   ```

## Security Considerations

- **Never test with sensitive data** over untrusted proxies
- **Use HTTPS targets** when possible
- **Be aware of proxy logging** - your requests may be logged
- **Test in isolated environment** when possible

## Contributing

To add new testing methods:

1. Add new test function to `main.go`
2. Update the command-line interface
3. Add corresponding PowerShell/batch support
4. Update this README

## License

This testing tool is part of the quant project and follows the same license terms. 
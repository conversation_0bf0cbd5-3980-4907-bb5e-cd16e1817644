# Proxy Testing Tool Makefile
# Supports cross-compilation for multiple platforms

# Build configuration
VERSION ?= 1.0.0
BUILD_DIR = build
MAIN_FILE = main.go

# Platforms to build for
PLATFORMS = \
	windows-amd64 \
	windows-386 \
	linux-amd64 \
	linux-386 \
	linux-arm64 \
	linux-armv7 \
	darwin-amd64 \
	darwin-arm64

# Default target
.PHONY: all
all: clean build

# Build for all platforms
.PHONY: build
build: $(PLATFORMS)

# Build for specific platform
.PHONY: $(PLATFORMS)
$(PLATFORMS): $(BUILD_DIR)
	@echo "Building for $@..."
	@$(eval OS = $(word 1,$(subst -, ,$@)))
	@$(eval ARCH = $(word 2,$(subst -, ,$@)))
	@$(eval ARM = $(word 3,$(subst -, ,$@)))
	@$(eval EXT = $(if $(findstring windows,$(OS)),.exe,))
	@$(eval ARM_FLAG = $(if $(ARM),GOARM=$(ARM),))
	@GOOS=$(OS) GOARCH=$(ARCH) $(ARM_FLAG) go build \
		-ldflags "-s -w -X main.Version=$(VERSION)" \
		-o $(BUILD_DIR)/proxytest-$@$(EXT) \
		$(MAIN_FILE)
	@echo "✓ Built: proxytest-$@$(EXT)"

# Create build directory
$(BUILD_DIR):
	@mkdir -p $(BUILD_DIR)

# Build for current platform only
.PHONY: local
local: $(BUILD_DIR)
	@echo "Building for current platform..."
	@go build -ldflags "-s -w -X main.Version=$(VERSION)" -o $(BUILD_DIR)/proxytest-local $(MAIN_FILE)
	@echo "✓ Built: proxytest-local"

# Build for Windows only
.PHONY: windows
windows: windows-amd64 windows-386

# Build for Linux only
.PHONY: linux
linux: linux-amd64 linux-386 linux-arm64 linux-armv7

# Build for macOS only
.PHONY: darwin
darwin: darwin-amd64 darwin-arm64

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build directory..."
	@rm -rf $(BUILD_DIR)
	@echo "✓ Cleaned"

# Install dependencies
.PHONY: deps
deps:
	@echo "Installing dependencies..."
	@go mod tidy
	@go mod download
	@echo "✓ Dependencies installed"

# Run tests
.PHONY: test
test:
	@echo "Running tests..."
	@go test -v ./...
	@echo "✓ Tests completed"

# Show help
.PHONY: help
help:
	@echo "Proxy Testing Tool - Available targets:"
	@echo ""
	@echo "  all          - Build for all platforms (default)"
	@echo "  build        - Build for all platforms"
	@echo "  local        - Build for current platform only"
	@echo "  windows      - Build for Windows (amd64, 386)"
	@echo "  linux        - Build for Linux (amd64, 386, arm64, armv7)"
	@echo "  darwin       - Build for macOS (amd64, arm64)"
	@echo "  clean        - Remove build artifacts"
	@echo "  deps         - Install dependencies"
	@echo "  test         - Run tests"
	@echo "  distribute   - Create distribution packages"
	@echo "  help         - Show this help"
	@echo ""
	@echo "Platform-specific targets:"
	@echo "  windows-amd64, windows-386"
	@echo "  linux-amd64, linux-386, linux-arm64, linux-armv7"
	@echo "  darwin-amd64, darwin-arm64"
	@echo ""
	@echo "Examples:"
	@echo "  make                    # Build all platforms"
	@echo "  make windows            # Build Windows versions"
	@echo "  make linux-amd64        # Build Linux amd64 only"
	@echo "  make clean && make all  # Clean and rebuild all"
	@echo "  make distribute         # Create distribution packages"

# Show version
.PHONY: version
version:
	@echo "Proxy Testing Tool v$(VERSION)"
	@echo "Build directory: $(BUILD_DIR)"
	@echo "Main file: $(MAIN_FILE)"

# List built binaries
.PHONY: list
list:
	@echo "Built binaries:"
	@ls -la $(BUILD_DIR)/ 2>/dev/null || echo "No binaries found. Run 'make build' first."

# Create distribution packages
.PHONY: distribute
distribute: build
	@echo "Creating distribution packages..."
	@chmod +x distribute.sh
	@./distribute.sh
	@echo "✓ Distribution packages created in dist/ directory" 
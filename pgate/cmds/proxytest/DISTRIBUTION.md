# Proxy Testing Tool - Distribution Guide

This guide explains how to handle platform-specific scripts (batch files and PowerShell) when cross-compiling the proxy testing tool.

## Problem

When cross-compiling Go applications, you need to handle platform-specific scripts:
- **Windows**: `.bat` and `.ps1` files
- **Linux/macOS**: `.sh` files

These scripts are not portable across platforms and need to be distributed with the appropriate binaries.

## Solution: Platform-Specific Distribution

### Directory Structure

```
pgate/cmds/proxytest/
├── main.go                    # Go source code
├── build.bat                  # Windows build script
├── build.sh                   # Linux/macOS build script
├── Makefile                   # Universal build system
├── distribute.sh              # Distribution script
├── README.md                  # Main documentation
├── test_config.json           # Sample configuration
├── scripts/                   # Platform-specific scripts
│   ├── windows/
│   │   ├── test_proxy.bat     # Windows batch script
│   │   └── test_proxy.ps1     # Windows PowerShell script
│   └── linux/
│       └── test_proxy.sh      # Linux/macOS shell script
└── dist/                      # Distribution packages (generated)
    ├── proxytest-windows-amd64-v1.0.0.zip
    ├── proxytest-linux-amd64-v1.0.0.tar.gz
    ├── proxytest-darwin-amd64-v1.0.0.tar.gz
    └── proxytest-universal-v1.0.0.tar.gz
```

## Build and Distribution Process

### 1. Cross-Compilation

```bash
# Build for all platforms
make build

# Or use individual scripts
./build.sh          # Linux/macOS
./build.bat         # Windows
```

This creates binaries in the `build/` directory:
```
build/
├── proxytest-windows-amd64.exe
├── proxytest-windows-386.exe
├── proxytest-linux-amd64
├── proxytest-linux-386
├── proxytest-linux-arm64
├── proxytest-linux-armv7
├── proxytest-darwin-amd64
└── proxytest-darwin-arm64
```

### 2. Distribution Packaging

```bash
# Create distribution packages
make distribute

# Or run directly
./distribute.sh
```

This creates platform-specific packages in the `dist/` directory.

## Distribution Packages

### Windows Packages

**File**: `proxytest-windows-amd64-v1.0.0.zip`
```
proxytest-windows-amd64-v1.0.0/
├── proxytest.exe              # Main binary
├── test_proxy.bat             # Batch script
├── test_proxy.ps1             # PowerShell script
├── README.md                  # Documentation
├── README-Windows.md          # Windows-specific guide
└── test_config.json           # Sample configuration
```

**Usage**:
```cmd
# Extract and run
unzip proxytest-windows-amd64-v1.0.0.zip
cd proxytest-windows-amd64-v1.0.0
test_proxy.bat http://127.0.0.1:8080
```

### Linux Packages

**File**: `proxytest-linux-amd64-v1.0.0.tar.gz`
```
proxytest-linux-amd64-v1.0.0/
├── proxytest                  # Main binary
├── test_proxy.sh              # Shell script
├── README.md                  # Documentation
├── README-Linux.md            # Linux-specific guide
└── test_config.json           # Sample configuration
```

**Usage**:
```bash
# Extract and run
tar -xzf proxytest-linux-amd64-v1.0.0.tar.gz
cd proxytest-linux-amd64-v1.0.0
chmod +x proxytest test_proxy.sh
./test_proxy.sh "http://127.0.0.1:8080"
```

### macOS Packages

**File**: `proxytest-darwin-amd64-v1.0.0.tar.gz`
```
proxytest-darwin-amd64-v1.0.0/
├── proxytest                  # Main binary
├── test_proxy.sh              # Shell script (same as Linux)
├── README.md                  # Documentation
├── README-macOS.md            # macOS-specific guide
└── test_config.json           # Sample configuration
```

**Usage**:
```bash
# Extract and run
tar -xzf proxytest-darwin-amd64-v1.0.0.tar.gz
cd proxytest-darwin-amd64-v1.0.0
chmod +x proxytest test_proxy.sh
./test_proxy.sh "http://127.0.0.1:8080"
```

### Universal Package

**File**: `proxytest-universal-v1.0.0.tar.gz`
```
proxytest-universal-v1.0.0/
├── proxytest-windows-amd64.exe
├── proxytest-windows-386.exe
├── proxytest-linux-amd64
├── proxytest-linux-386
├── proxytest-linux-arm64
├── proxytest-linux-armv7
├── proxytest-darwin-amd64
├── proxytest-darwin-arm64
├── scripts/
│   ├── windows/
│   │   ├── test_proxy.bat
│   │   └── test_proxy.ps1
│   └── linux/
│       └── test_proxy.sh
├── README.md
├── README-Universal.md
└── test_config.json
```

## Script Compatibility

### Windows Scripts

**`test_proxy.bat`**:
- Uses Windows batch commands
- Calls PowerShell for advanced features
- Uses `curl.exe` for HTTP testing
- Uses `nc.exe` and `telnet.exe` for connectivity

**`test_proxy.ps1`**:
- Uses PowerShell cmdlets
- Sets environment variables for proxy testing
- Uses .NET classes for HTTP requests
- Provides colored output

### Linux/macOS Scripts

**`test_proxy.sh`**:
- Uses bash shell commands
- Uses `curl` and `wget` for HTTP testing
- Uses `nc` and `telnet` for connectivity
- Provides colored output with ANSI codes
- Works on both Linux and macOS

## Cross-Platform Considerations

### Binary Detection

All scripts automatically detect the appropriate binary:

**Windows**:
```batch
if exist "proxytest-windows-amd64.exe" set "GO_TOOL=proxytest-windows-amd64.exe"
if exist "proxytest-windows-386.exe" set "GO_TOOL=proxytest-windows-386.exe"
```

**Linux/macOS**:
```bash
if [[ -f "proxytest-linux-amd64" ]]; then
    go_tool="./proxytest-linux-amd64"
elif [[ -f "proxytest-darwin-amd64" ]]; then
    go_tool="./proxytest-darwin-amd64"
fi
```

### Tool Availability

Scripts check for available tools and skip tests if not found:

```bash
if command -v curl >/dev/null 2>&1; then
    # Run curl test
else
    echo "⚠ cURL not found, skipping test"
fi
```

## Distribution Workflow

### 1. Development

```bash
# Build for current platform
make local

# Test locally
./build/proxytest-local -proxy "http://127.0.0.1:8080"
```

### 2. Cross-Compilation

```bash
# Build for all platforms
make build

# Verify binaries
make list
```

### 3. Distribution

```bash
# Create distribution packages
make distribute

# Check results
ls -la dist/
```

### 4. Testing

```bash
# Test Windows package
unzip dist/proxytest-windows-amd64-v1.0.0.zip
cd proxytest-windows-amd64-v1.0.0
test_proxy.bat http://127.0.0.1:8080

# Test Linux package
tar -xzf dist/proxytest-linux-amd64-v1.0.0.tar.gz
cd proxytest-linux-amd64-v1.0.0
./test_proxy.sh "http://127.0.0.1:8080"
```

## Version Management

### Setting Version

```bash
# Set version for build
VERSION=1.1.0 make build

# Set version for distribution
VERSION=1.1.0 make distribute
```

### Version Information

Each binary includes version info:
```bash
./proxytest-linux-amd64 -version
# Output: Proxy Testing Tool v1.0.0
#         Built for: linux/amd64
```

## Best Practices

### 1. Script Organization
- Keep platform-specific scripts in separate directories
- Use consistent naming conventions
- Include platform-specific documentation

### 2. Binary Detection
- Always check for the appropriate binary
- Provide fallback options
- Give clear error messages

### 3. Tool Availability
- Check for required tools before using them
- Provide helpful installation instructions
- Skip tests gracefully when tools are missing

### 4. Documentation
- Include platform-specific README files
- Provide clear usage examples
- Document requirements and dependencies

### 5. Testing
- Test packages on target platforms
- Verify script functionality
- Check binary compatibility

## Troubleshooting

### Common Issues

1. **Script not found**: Ensure scripts are in the correct platform directory
2. **Permission denied**: Make scripts executable (`chmod +x`)
3. **Binary not found**: Check that cross-compilation completed successfully
4. **Tool not available**: Install required tools or skip those tests

### Debug Commands

```bash
# Check build results
make list

# Test specific platform
make windows-amd64

# Clean and rebuild
make clean && make build

# Check distribution
ls -la dist/
```

This approach ensures that users get the appropriate scripts for their platform while maintaining a clean, organized distribution structure. 
#!/bin/bash

# Debug script for proxy testing
# This script helps troubleshoot proxy connection issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

PROXY_URL="${1:-http://127.0.0.1:61596}"
TARGET_URL="${2:-https://httpbin.org/ip}"

echo -e "${BLUE}Proxy Debug Script${NC}"
echo -e "${BLUE}==================${NC}"
echo "Proxy URL: $PROXY_URL"
echo "Target URL: $TARGET_URL"
echo ""

# Extract host and port
if [[ $PROXY_URL =~ ^(http|https|socks5)://([^:]+):([0-9]+) ]]; then
    PROTOCOL="${BASH_REMATCH[1]}"
    HOST="${BASH_REMATCH[2]}"
    PORT="${BASH_REMATCH[3]}"
else
    echo -e "${RED}Error: Invalid proxy URL format${NC}"
    exit 1
fi

echo -e "${CYAN}1. Testing basic TCP connectivity...${NC}"
echo "Connecting to $HOST:$PORT..."

if timeout 5 bash -c "</dev/tcp/$HOST/$PORT" 2>/dev/null; then
    echo -e "${GREEN}✓ TCP connectivity PASSED${NC}"
else
    echo -e "${RED}✗ TCP connectivity FAILED${NC}"
    echo "This means the proxy server is not running or not accessible."
    exit 1
fi

echo ""

echo -e "${CYAN}2. Testing with netcat...${NC}"
if command -v nc >/dev/null 2>&1; then
    if timeout 5 nc -zv "$HOST" "$PORT" 2>/dev/null; then
        echo -e "${GREEN}✓ Netcat test PASSED${NC}"
    else
        echo -e "${RED}✗ Netcat test FAILED${NC}"
    fi
else
    echo -e "${YELLOW}⚠ netcat not available${NC}"
fi

echo ""

echo -e "${CYAN}3. Testing with telnet...${NC}"
if command -v telnet >/dev/null 2>&1; then
    if timeout 5 bash -c "echo quit | telnet $HOST $PORT" 2>/dev/null | grep -q "Connected"; then
        echo -e "${GREEN}✓ Telnet test PASSED${NC}"
    else
        echo -e "${RED}✗ Telnet test FAILED${NC}"
    fi
else
    echo -e "${YELLOW}⚠ telnet not available${NC}"
fi

echo ""

echo -e "${CYAN}4. Testing with curl (direct connection)...${NC}"
if command -v curl >/dev/null 2>&1; then
    if curl --connect-timeout 10 --max-time 30 --silent "$TARGET_URL" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Direct curl test PASSED${NC}"
    else
        echo -e "${RED}✗ Direct curl test FAILED${NC}"
    fi
else
    echo -e "${YELLOW}⚠ curl not available${NC}"
fi

echo ""

echo -e "${CYAN}5. Testing with curl through proxy...${NC}"
if command -v curl >/dev/null 2>&1; then
    echo "Testing HTTP proxy..."
    if curl --proxy "$PROXY_URL" --connect-timeout 10 --max-time 30 --verbose "$TARGET_URL" 2>&1 | head -20; then
        echo -e "${GREEN}✓ Proxy curl test PASSED${NC}"
    else
        echo -e "${RED}✗ Proxy curl test FAILED${NC}"
    fi
else
    echo -e "${YELLOW}⚠ curl not available${NC}"
fi

echo ""

echo -e "${CYAN}6. Testing with Go tool...${NC}"
if [[ -f "proxytest" ]]; then
    echo "Testing with Go tool..."
    if ./proxytest -proxy "$PROXY_URL" -target "$TARGET_URL" -type "connectivity"; then
        echo -e "${GREEN}✓ Go connectivity test PASSED${NC}"
        echo ""
        echo "Testing HTTP proxy..."
        if ./proxytest -proxy "$PROXY_URL" -target "$TARGET_URL" -type "http"; then
            echo -e "${GREEN}✓ Go HTTP test PASSED${NC}"
        else
            echo -e "${RED}✗ Go HTTP test FAILED${NC}"
        fi
    else
        echo -e "${RED}✗ Go connectivity test FAILED${NC}"
    fi
else
    echo -e "${YELLOW}⚠ Go tool not found${NC}"
fi

echo ""

echo -e "${CYAN}7. Testing SOCKS5 protocol...${NC}"
if [[ "$PROTOCOL" == "socks5" ]] || [[ "$PROTOCOL" == "http" ]]; then
    # Test SOCKS5 on the same port (for unified proxy)
    SOCKS5_URL="socks5://$HOST:$PORT"
    echo "Testing SOCKS5 on $SOCKS5_URL..."
    
    if command -v curl >/dev/null 2>&1; then
        if curl --socks5 "$HOST:$PORT" --connect-timeout 10 --max-time 30 --silent "$TARGET_URL" >/dev/null 2>&1; then
            echo -e "${GREEN}✓ SOCKS5 curl test PASSED${NC}"
        else
            echo -e "${RED}✗ SOCKS5 curl test FAILED${NC}"
        fi
    fi
    
    if [[ -f "proxytest" ]]; then
        if ./proxytest -proxy "$SOCKS5_URL" -target "$TARGET_URL" -type "socks5"; then
            echo -e "${GREEN}✓ Go SOCKS5 test PASSED${NC}"
        else
            echo -e "${RED}✗ Go SOCKS5 test FAILED${NC}"
        fi
    fi
fi

echo ""

echo -e "${CYAN}8. Network information...${NC}"
echo "Local IP addresses:"
ip addr show | grep "inet " | grep -v "127.0.0.1" | awk '{print "  " $2}' || echo "  Unable to get IP addresses"

echo ""
echo "Active connections to $HOST:$PORT:"
netstat -an | grep "$HOST:$PORT" | head -5 || echo "  No active connections found"

echo ""

echo -e "${CYAN}9. Proxy server check...${NC}"
echo "Checking if proxy server is running on port $PORT:"
lsof -i :$PORT 2>/dev/null || echo "  No process found on port $PORT"

echo ""

echo -e "${BLUE}Debug Summary:${NC}"
echo "================"
echo "If TCP connectivity passes but HTTP/SOCKS5 tests fail, the issue is likely:"
echo "1. Proxy server protocol handling (unified proxy implementation)"
echo "2. Proxy server configuration"
echo "3. Network/firewall rules"
echo ""
echo "If TCP connectivity fails, the issue is:"
echo "1. Proxy server not running"
echo "2. Wrong port number"
echo "3. Network connectivity issues" 
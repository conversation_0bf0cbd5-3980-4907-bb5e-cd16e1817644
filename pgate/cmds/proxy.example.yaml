aws_accounts:
    - id: "1604"
      access_key_id: "YOUR_AWS_ACCESS_KEY_ID"
      secret_access_key: "YOUR_AWS_SECRET"

ssh_keys:
    - id: "aws_kr"
      key_name: "aws_kr"
      key_path: "/Users/<USER>/.ssh/aws_kr.pem"

aws_templates:
    - id: "awstmp1"
      account_id: "1604"
      ami: "YOUR_AMI_ID"
      region: "ap-northeast-2"
      instance_type: "t3.micro"
      security_group_id: "YOUR_SECURITY_GROUP_ID"
      ssh_key_id: "aws_kr"

google_accounts:
    - id: "wiz"
      project_id: "PROJECT_ID"
      service_account_json: "SERVICE_ACCOUNT_JSON"
      service_account_json_encrypted: "SERVICE_ACCOUNT_JSON_ENCRYPTED"

google_templates:
    - id: "gchk"
      account_id: "wiz"
      source_image: "projects/ubuntu-os-cloud/global/images/family/ubuntu-2004-lts"
      zone: "asia-east2-a"
      machine_type: "n1-standard-1"
      ssh_key_id: "gc_hk"

cloudflare_accounts:
    - id: "transferstart"
      api_token: "YOUR_CLOUDFLARE_API_TOKEN"
      domains:
          - domain: ""
            zone_id: ""
          - domain: ""
            zone_id: ""
          - domain: ""
            zone_id: ""

templates:
    - name: "tmp1"
      service: "aws"
      machine_template_id: "awstmp1"
      cloudflare_account_id: "transferstart"
      domains:
        - ""
        - ""
        - ""
      enable_warp: true

gateway_server:
    enabled: true
    port: 6979
    enable_surge: false,
    surge_config_path: "/Users/<USER>/Library/Application Support/Surge/Profiles/Default.conf" # 直接修改这个文件内容，先备份
    surge_reload_cmd: "/Applications/Surge.app/Contents/Applications/surge-cli reload"

sync_storage:
    account_id: "1604"
    region: "ap-northeast-2"
    bucket: "sv-proxy-dev"
    from_pgate_id: "proxy_dev_x"

proxy_auto_restart_interval_minutes: 0
data_dir: "/path/to/your/data_dir"
debug: true
id: "proxy_dev"


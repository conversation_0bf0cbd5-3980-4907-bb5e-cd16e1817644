package main

import (
	"flag"
	"fmt"
	"os"

	"github.com/wizhodl/pgate/license"
)

func main() {
	// Define command line flags
	projectPtr := flag.String("project", "", "Project name (required)")

	// Parse command line arguments
	flag.Parse()

	// Check if project argument is provided
	if *projectPtr == "" {
		fmt.Fprintf(os.Stderr, "Error: -project argument is required\n")
		flag.Usage()
		os.Exit(1)
	}

	// 获取机器码
	hwid, err := license.GetHwid(license.LicenseProject(*projectPtr))
	if err != nil {
		panic(err)
	}
	// 打印机器码
	fmt.Printf("HWID: %s\n", string(hwid))
}

package main

import (
	"context"
	"crypto/x509"
	"encoding/pem"
	"flag"
	"fmt"
	"log"
	"os"
	"os/exec"
	"time"

	"golang.org/x/crypto/ssh"

	compute "cloud.google.com/go/compute/apiv1"
	"cloud.google.com/go/compute/apiv1/computepb"
	"google.golang.org/api/iterator"
	"google.golang.org/api/option"
)

func main() {
	// 从环境变量获取配置
	projectID := os.Getenv("GCP_PROJECT_ID")
	if projectID == "" {
		log.Fatalf("GCP_PROJECT_ID environment variable is not set")
	}

	zone := os.Getenv("GCP_ZONE")
	if zone == "" {
		log.Fatalf("GCP_ZONE environment variable is not set")
	}

	sourceImage := os.Getenv("GCP_SOURCE_IMAGE")
	if sourceImage == "" {
		log.Fatalf("GCP_SOURCE_IMAGE environment variable is not set")
	}

	network := os.Getenv("GCP_NETWORK")
	if network == "" {
		log.Fatalf("GCP_NETWORK environment variable is not set")
	}

	// 解析命令行参数
	operation := flag.String("operation", "list", "Operation to perform: list, add, remove")
	instanceName := flag.String("name", "", "Instance name for add or remove operation")
	flag.Parse()

	// 创建上下文
	ctx := context.Background()

	var opt option.ClientOption
	serviceAccountFile := os.Getenv("GCP_SERVICE_ACCOUNT_FILE")
	if serviceAccountFile != "" {
		opt = option.WithCredentialsFile(serviceAccountFile)
	} else {
		serviceAccountJSON := os.Getenv("GCP_SERVICE_ACCOUNT_JSON")
		if serviceAccountJSON == "" {
			log.Fatalf("Service account credentials must be provided")
		}
		opt = option.WithCredentialsJSON([]byte(serviceAccountJSON))
	}

	// 使用服务账户密钥文件创建 Compute Engine 客户端
	client, err := compute.NewInstancesRESTClient(ctx, opt)
	if err != nil {
		log.Fatalf("Failed to create client: %v", err)
	}
	defer client.Close()

	switch *operation {
	case "list":
		listInstances(ctx, client, projectID, zone)
	case "add":
		if *instanceName == "" {
			log.Fatalf("Instance name must be provided for add operation")
		}
		addInstance(ctx, client, projectID, zone, *instanceName, sourceImage, network)
	case "remove":
		if *instanceName == "" {
			log.Fatalf("Instance name must be provided for remove operation")
		}
		removeInstance(ctx, client, projectID, zone, *instanceName)
	default:
		log.Fatalf("Unknown operation: %s", *operation)
	}
}

func listInstances(ctx context.Context, client *compute.InstancesClient, projectID, zone string) {
	req := &computepb.ListInstancesRequest{
		Project: projectID,
		Zone:    zone,
	}

	it := client.List(ctx, req)
	fmt.Println("Instances in zone:", zone)

	for {
		instance, err := it.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			log.Fatalf("Failed to list instances: %v", err)
		}

		// 打印实例详细信息
		fmt.Printf("Name: %s\n", instance.GetName())
		fmt.Printf("Status: %s\n", instance.GetStatus())
		if len(instance.GetNetworkInterfaces()) > 0 {
			fmt.Printf("Network Interfaces:\n")
			for _, ni := range instance.GetNetworkInterfaces() {
				fmt.Printf(" - Network: %s\n", ni.GetName())
				fmt.Printf(" - Network IP: %s\n", ni.GetNetworkIP())
				if len(ni.GetAccessConfigs()) > 0 {
					for _, ac := range ni.GetAccessConfigs() {
						fmt.Printf("   - External IP: %s\n", ac.GetNatIP())
					}
				}
			}
		}
		fmt.Println()
	}
}

func addInstance(ctx context.Context, client *compute.InstancesClient, projectID, zone, instanceName, sourceImage, network string) {
	// 读取 SSH 私钥文件
	sshPrivateKeyPath := os.Getenv("SSH_PRIVATE_KEY_PATH")
	if sshPrivateKeyPath == "" {
		log.Fatalf("SSH_PRIVATE_KEY_PATH environment variable is not set")
	}

	_, publicKey, err := getSSHKeyPairFromPEM(sshPrivateKeyPath)
	if err != nil {
		log.Fatalf("Failed to get SSH key pair: %v", err)
	}

	sshKeyString := fmt.Sprintf("ubuntu:%s", publicKey)

	instance := &computepb.Instance{
		Name:        &instanceName,
		MachineType: toPtr(fmt.Sprintf("zones/%s/machineTypes/%s", zone, "n1-standard-1")),
		Disks: []*computepb.AttachedDisk{
			{
				Boot: toPtr(true),
				InitializeParams: &computepb.AttachedDiskInitializeParams{
					SourceImage: toPtr(sourceImage),
				},
			},
		},
		NetworkInterfaces: []*computepb.NetworkInterface{
			{
				Name: toPtr(network),
				AccessConfigs: []*computepb.AccessConfig{
					{
						Name: toPtr("External NAT"),
						Type: toPtr("ONE_TO_ONE_NAT"),
					},
				},
			},
		},
		Metadata: &computepb.Metadata{
			Items: []*computepb.Items{
				{
					Key:   toPtr("ssh-keys"),
					Value: toPtr(sshKeyString),
				},
			},
		},
	}

	op, err := client.Insert(ctx, &computepb.InsertInstanceRequest{
		Project:          projectID,
		Zone:             zone,
		InstanceResource: instance,
	})
	if err != nil {
		log.Fatalf("Failed to create instance: %v", err)
	}

	err = op.Wait(ctx)
	if err != nil {
		log.Fatalf("Failed to wait for the operation: %v", err)
	}

	fmt.Printf("Instance %s created successfully\n", instanceName)

	// 等待实例启动并分配外部 IP
	time.Sleep(60 * time.Second)

	// 获取实例信息
	instanceInfo, err := client.Get(ctx, &computepb.GetInstanceRequest{
		Project:  projectID,
		Zone:     zone,
		Instance: instanceName,
	})
	if err != nil {
		log.Fatalf("Failed to get instance info: %v", err)
	}

	// 获取外部 IP 地址
	var externalIP string
	for _, ni := range instanceInfo.GetNetworkInterfaces() {
		for _, ac := range ni.GetAccessConfigs() {
			if ac.GetNatIP() != "" {
				externalIP = ac.GetNatIP()
				break
			}
		}
	}

	if externalIP == "" {
		log.Fatalf("Failed to get external IP address for instance %s", instanceName)
	}

	// 使用 SSH 登录实例并运行命令
	sshCommand := fmt.Sprintf("ssh -i %s -o StrictHostKeyChecking=no ubuntu@%s 'echo Hello, world!'", sshPrivateKeyPath, externalIP)

	cmd := exec.Command("sh", "-c", sshCommand)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	err = cmd.Run()
	if err != nil {
		log.Fatalf("Failed to execute command on instance: %v", err)
	}
}

func removeInstance(ctx context.Context, client *compute.InstancesClient, projectID, zone, instanceName string) {
	op, err := client.Delete(ctx, &computepb.DeleteInstanceRequest{
		Project:  projectID,
		Zone:     zone,
		Instance: instanceName,
	})
	if err != nil {
		log.Fatalf("Failed to delete instance: %v", err)
	}

	err = op.Wait(ctx)
	if err != nil {
		log.Fatalf("Failed to wait for the operation: %v", err)
	}

	fmt.Printf("Instance %s deleted successfully\n", instanceName)
}

func toPtr[T any](v T) *T {
	return &v
}

func getSSHKeyPairFromPEM(privateKeyPath string) ([]byte, []byte, error) {
	// 读取 PEM 格式的私钥文件
	privateKeyPEM, err := os.ReadFile(privateKeyPath)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to read private key file: %v", err)
	}

	// 解码 PEM 块
	block, _ := pem.Decode(privateKeyPEM)
	if block == nil || block.Type != "RSA PRIVATE KEY" {
		return nil, nil, fmt.Errorf("failed to decode PEM block containing private key")
	}

	// 解析 RSA 私钥
	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	// 生成公钥
	publicKey, err := ssh.NewPublicKey(&privateKey.PublicKey)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to generate public key: %v", err)
	}

	// 编码公钥为 OpenSSH 格式
	publicKeyBytes := ssh.MarshalAuthorizedKey(publicKey)

	return privateKeyPEM, publicKeyBytes, nil
}

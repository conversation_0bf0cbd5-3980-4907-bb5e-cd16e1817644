package pgate

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/utils"
)

type PgateClient struct {
	Host string
}

func NewPgateClient(host string) *PgateClient {
	return &PgateClient{Host: host}
}

func (this *PgateClient) CreateProxy(name, template string, lifeHour int, group string) (resultProxy *Proxy, er error) {
	result, err := this.Get("/proxy/create", map[string]interface{}{"name": name, "template": template, "life_hour": lifeHour, "group": group})
	if err != nil {
		er = fmt.Errorf("create proxy failed: %v", err)
		return
	}
	resultProxy = &Proxy{}
	err = json.Unmarshal([]byte(result), resultProxy)
	if err != nil {
		er = fmt.Errorf("unmarshal proxy failed: %v", err)
	}
	return
}

func (this *PgateClient) CreateSocks5Proxy(name string, ip string, port int, username string, password string, lifeHour int, group string) (resultProxy *Proxy, er error) {
	result, err := this.Get("/proxy/create/socks5", map[string]interface{}{
		"name":      name,
		"ip":        ip,
		"port":      port,
		"username":  username,
		"password":  password,
		"life_hour": lifeHour,
		"group":     group,
	})
	if err != nil {
		er = fmt.Errorf("create SOCKS5 proxy failed: %v", err)
		return
	}
	resultProxy = &Proxy{}
	err = json.Unmarshal([]byte(result), resultProxy)
	if err != nil {
		er = fmt.Errorf("unmarshal SOCKS5 proxy failed: %v", err)
	}
	return
}

func (this *PgateClient) ListProxyTemplates() (templates []string, er error) {
	result, err := this.Get("/proxy/templates", map[string]interface{}{})
	if err != nil {
		er = fmt.Errorf("get proxy templates failed: %v", err)
		return
	}
	err = json.Unmarshal([]byte(result), &templates)
	if err != nil {
		er = fmt.Errorf("unmarshal proxy templates failed: %v", err)
	}
	return
}

func (this *PgateClient) ListProxies() (proxies []*Proxy, er error) {
	result, err := this.Get("/proxies", map[string]interface{}{})
	if err != nil {
		er = fmt.Errorf("get proxies failed: %v", err)
		return
	}
	if result == "" {
		return
	}
	err = json.Unmarshal([]byte(result), &proxies)
	if err != nil {
		er = fmt.Errorf("unmarshal proxies failed: %v, res: %s", err, result)
	}
	return
}

func (this *PgateClient) TestAllProxies() (er error) {
	_, err := this.Get("/proxy/test", map[string]interface{}{})
	if err != nil {
		er = fmt.Errorf("test proxy failed: %v", err)
	}
	return
}

func (this *PgateClient) DeleteProxy(name string) (er error) {
	_, err := this.Get("/proxy/delete", map[string]interface{}{"name": name})
	if err != nil {
		er = fmt.Errorf("delete proxy failed: %v", err)
	}
	return
}

func (this *PgateClient) RepairProxy(name string) (er error) {
	_, err := this.Get("/proxy/repair", map[string]interface{}{"name": name})
	if err != nil {
		er = fmt.Errorf("repair proxy failed: %v", err)
	}
	return
}

func (this *PgateClient) StopProxy(name string) (er error) {
	_, err := this.Get("/proxy/stop", map[string]interface{}{"name": name})
	if err != nil {
		er = fmt.Errorf("stop proxy failed: %v", err)
	}
	return
}

func (this *PgateClient) RemoveLogForProgram(name string) (er error) {
	_, err := this.Get("/proxy/remove/log", map[string]interface{}{"name": name})
	if err != nil {
		er = fmt.Errorf("remove log for program failed: %v", err)
	}
	return
}

func (this *PgateClient) StartProxy(name string) (er error) {
	_, err := this.Get("/proxy/start", map[string]interface{}{"name": name})
	if err != nil {
		er = fmt.Errorf("start proxy failed: %v", err)
	}
	return
}

func (this *PgateClient) IncreaseLifeHour(name string, lifeHour int) (er error) {
	_, err := this.Get("/proxy/extend", map[string]interface{}{"name": name, "life_hour": lifeHour})
	if err != nil {
		er = fmt.Errorf("increase life hour failed: %v", err)
	}
	return
}

func (this *PgateClient) ProxyLogins() (logins map[string]*ProxyLoginBook, er error) {
	logins = map[string]*ProxyLoginBook{}
	loginBookStr, err := this.Get("/proxy/logins", map[string]interface{}{})
	if err != nil {
		er = fmt.Errorf("get proxy logins failed: %v", err)
		return
	}
	err = json.Unmarshal([]byte(loginBookStr), &logins)
	if err != nil {
		er = fmt.Errorf("unmarshal proxy logins failed: %v", err)
	}
	return
}

func (this *PgateClient) GroupForwarders() (forwarders map[string]*GroupForwarder, er error) {
	forwarders = map[string]*GroupForwarder{}
	forwardersStr, err := this.Get("/group/forwarders", map[string]interface{}{})
	if err != nil {
		er = fmt.Errorf("get group forwarders failed: %v", err)
		return
	}
	err = json.Unmarshal([]byte(forwardersStr), &forwarders)
	if err != nil {
		er = fmt.Errorf("unmarshal group forwarders failed: %v", err)
	}
	return
}

func (this *PgateClient) SuggestName(template string) (suggestedName string) {
	result, err := this.Get("/proxy/name/suggest", map[string]interface{}{"template": template})
	if err != nil {
		zlog.Errorf("suggest name failed: %v", err)
	}
	if result != "" {
		suggestedName = result
	}
	return
}

func (this *PgateClient) CheckName(name string) (er error) {
	_, err := this.Get("/proxy/name/check", map[string]interface{}{"name": name})
	if err != nil {
		er = fmt.Errorf("check name failed: %v", err)
	}
	return
}

func (this *PgateClient) GetForwarderByPort(port int) (forwarder *Forwarder, er error) {
	result, err := this.Get("/forwarder", map[string]interface{}{"port": port})
	if err != nil {
		er = fmt.Errorf("get forwarder failed: %v", err)
		return
	}
	forwarder = &Forwarder{}
	err = json.Unmarshal([]byte(result), forwarder)
	if err != nil {
		er = fmt.Errorf("unmarshal forwarder failed: %v", err)
	}
	return
}

func (this *PgateClient) Get(url string, data map[string]interface{}) (dataStr string, er error) {
	args := []string{}
	for k, v := range data {
		args = append(args, fmt.Sprintf("%s=%v", k, v))
	}
	allArgs := strings.Join(args, "&")
	if !strings.HasPrefix(url, "/") {
		url = "/" + url
	}
	response, err := httpGet(fmt.Sprintf("http://%s%s?%s", this.Host, url, allArgs))
	if err != nil {
		er = fmt.Errorf("http.Get => %v", err.Error())
		return
	}
	// zlog.Debugf("response: %s", response)
	result := gjson.Parse(response)
	if !result.Get("ok").Bool() {
		er = fmt.Errorf("rest get failed: %s", result.Get("message").String())
		return
	}
	dataStr = result.Get("data").String()
	return
}

func httpGet(url string) (bodyStr string, er error) {
	// 发送 GET 请求
	resp, err := http.Get(url)
	if err != nil {
		er = fmt.Errorf("http.Get => %v", err.Error())
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		er = fmt.Errorf("ioutil.ReadAll => %v", err.Error())
		return
	}

	bodyStr = string(body)
	// 返回请求结果
	return
}

func (this *PgateClient) GetAWSReservedInstances() (instances map[string][]AWSReservedInstancesWithRegion, er error) {
	result, err := this.Get("/aws/reserved/instances", map[string]interface{}{})
	if err != nil {
		er = fmt.Errorf("get aws reserved instances failed: %v", err)
		return
	}

	instances = map[string][]AWSReservedInstancesWithRegion{}
	err = json.Unmarshal([]byte(result), &instances)
	if err != nil {
		er = fmt.Errorf("unmarshal aws reserved instances failed: %v", err)
	}
	return
}

func (this *PgateClient) GetAWSReservationCoverage(days int) (coverage map[string][]AWSReservationCoverage, er error) {
	result, err := this.Get("/aws/reservation/coverage", map[string]interface{}{"days": days})
	if err != nil {
		er = fmt.Errorf("get aws reservation coverage failed: %v", err)
		return
	}

	coverage = map[string][]AWSReservationCoverage{}
	err = json.Unmarshal([]byte(result), &coverage)
	if err != nil {
		er = fmt.Errorf("unmarshal aws reservation coverage failed: %v", err)
	}
	return
}

func (this *PgateClient) GetGoogleCommitments() (commitments map[string][]*GoogleCommitment, er error) {
	result, err := this.Get("/google/commitments", map[string]interface{}{})
	if err != nil {
		er = fmt.Errorf("get google commitments failed: %v", err)
		return
	}

	commitments = map[string][]*GoogleCommitment{}
	err = json.Unmarshal([]byte(result), &commitments)
	if err != nil {
		er = fmt.Errorf("unmarshal google commitments failed: %v", err)
	}
	return
}

func (this *PgateClient) GetGoogleCommitmentsConverage() (commitments map[string][]*GoogleCommitmentCoverage, er error) {
	result, err := this.Get("/google/commitments/coverage", map[string]interface{}{})
	if err != nil {
		er = fmt.Errorf("get google commitments coverage failed: %v", err)
		return
	}

	commitments = map[string][]*GoogleCommitmentCoverage{}
	err = json.Unmarshal([]byte(result), &commitments)
	if err != nil {
		er = fmt.Errorf("unmarshal google commitments coverage failed: %v", err)
	}
	return
}

func (this *PgateClient) CheckSyncProxies(from_pgate_id string) (data map[string]string, er error) {
	result, err := this.Get("/proxies/sync/check", map[string]interface{}{"from_pgate_id": from_pgate_id})
	if err != nil {
		er = fmt.Errorf("sync proxies failed: %v", err)
		return
	}
	err = json.Unmarshal([]byte(result), &data)
	if err != nil {
		er = fmt.Errorf("unmarshal check sync proxies failed: %v", err)
	}
	return
}

func (this *PgateClient) SyncProxies(from_pgate_id string) (er error) {
	_, err := this.Get("/proxies/sync", map[string]interface{}{"from_pgate_id": from_pgate_id})
	if err != nil {
		er = fmt.Errorf("sync proxies failed: %v", err)
	}
	return
}

func (this *PgateClient) ClearAccountsForProxy(proxyName string) (er error) {
	_, err := this.Get("/proxy/clear/accounts", map[string]interface{}{"proxy_name": proxyName})
	if err != nil {
		er = fmt.Errorf("clear accounts for proxy failed: %v", err)
	}
	return
}

func (this *PgateClient) SetAccountLimit(project string, proxyGroup string, limit int) (er error) {
	_, err := this.Get("/proxy/set/account/limit", map[string]interface{}{"project": project, "proxy_group": proxyGroup, "limit": limit})
	if err != nil {
		er = fmt.Errorf("set account limit failed: %v", err)
	}
	return
}

func (this *PgateClient) GetProjectAccountLimits() (limits map[string]int, er error) {
	result, err := this.Get("/proxy/account/limits", map[string]interface{}{})
	if err != nil {
		er = fmt.Errorf("get project account limits failed: %v", err)
		return
	}
	err = json.Unmarshal([]byte(result), &limits)
	if err != nil {
		er = fmt.Errorf("unmarshal project account limits failed: %v", err)
	}
	return
}

func FormatProjectAccountLimits(limits map[string]int) (str string) {
	table := utils.NewTable()
	table.SetHeader([]string{"Project", "Proxy Group", "Limit"})
	for key, limit := range limits {
		parts := strings.Split(key, "::")
		project := parts[0]
		proxyGroup := parts[1]
		table.AddRow([]string{project, proxyGroup, strconv.Itoa(limit)})
	}
	if len(table.Rows) > 1 {
		return table.Render()
	}
	return "[No account limits]"
}

package pgate

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/http/httputil"
	"strings"
	"time"

	"github.com/armon/go-socks5"
	"github.com/wizhodl/quanter/common/zlog"
	goproxy "golang.org/x/net/proxy"
)

// StartUnifiedServer starts a unified HTTP/HTTPS/SOCKS5 server on the same port
func (f *Forwarder) StartUnifiedServer(dialer goproxy.Dialer) error {
	// Create HTTP transport with SOCKS5 proxy
	transport := &http.Transport{
		Proxy: http.ProxyFromEnvironment,
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			return dialer.Dial(network, addr)
		},
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	// Create SOCKS5 server for SOCKS5 requests
	socks5Conf := &socks5.Config{
		Dial: func(ctx context.Context, network, addr string) (net.Conn, error) {
			zlog.Debugf("unified proxy SOCKS5 dialing: %s %s", network, addr)
			conn, err := dialer.Dial(network, addr)
			if err != nil {
				zlog.Errorf("unified proxy SOCKS5 dial failed: %s %s - %v", network, addr, err)
			} else {
				zlog.Debugf("unified proxy SOCKS5 dial success: %s %s", network, addr)
			}
			return conn, err
		},
	}
	var err error
	f.socks5Server, err = socks5.New(socks5Conf)
	if err != nil {
		return fmt.Errorf("failed to create SOCKS5 server: %v", err)
	}

	// Create custom HTTP handler that can handle both HTTP and CONNECT requests
	httpHandler := &unifiedHTTPHandler{
		dialer:    dialer,
		transport: transport,
	}

	// Create unified server that can handle both HTTP and SOCKS5
	f.unifiedServer = &http.Server{
		Addr:    fmt.Sprintf("0.0.0.0:%d", f.Port),
		Handler: httpHandler, // Use custom handler that supports CONNECT
	}

	// Start the unified server with protocol detection
	go func() {
		zlog.Infof("starting unified HTTP/HTTPS/SOCKS5 server on port %d", f.Port)

		// Create a custom listener that can detect protocol
		listener, err := net.Listen("tcp", f.unifiedServer.Addr)
		if err != nil {
			zlog.Errorf("failed to create unified server listener: %v", err)
			f.Status = ForwarderStatusError
			return
		}
		defer listener.Close()

		for {
			conn, err := listener.Accept()
			if err != nil {
				if !strings.Contains(err.Error(), "use of closed network connection") {
					zlog.Errorf("unified server accept error: %v", err)
				}
				continue
			}

			go func(clientConn net.Conn) {
				// Use a buffered reader to peek at the first byte
				br := bufio.NewReader(clientConn)
				peeked, err := br.Peek(1)
				if err != nil {
					zlog.Errorf("failed to peek protocol detection bytes: %v", err)
					clientConn.Close()
					return
				}

				// Create a new connection that includes the buffered data
				bufferedConn := &bufferedConn{br, clientConn}

				// Check if it's SOCKS5 (first byte is 0x05)
				if peeked[0] == 0x05 {
					zlog.Debugf("unified server: SOCKS5 protocol detected")
					// Handle SOCKS5 request
					if err := f.socks5Server.ServeConn(bufferedConn); err != nil {
						zlog.Errorf("SOCKS5 serve error: %v", err)
					}
				} else {
					zlog.Debugf("unified server: HTTP protocol detected")
					// Handle HTTP request directly by parsing the request
					// and creating a response writer
					req, err := http.ReadRequest(br)
					if err != nil {
						zlog.Errorf("failed to read HTTP request: %v", err)
						clientConn.Close()
						return
					}

					// Check if it's a CONNECT request (needs special handling)
					if req.Method == "CONNECT" {
						// For CONNECT requests, we need to handle them directly
						// since our custom response writer doesn't support hijacking
						httpHandler := &unifiedHTTPHandler{
							dialer:    dialer,
							transport: transport,
						}
						httpHandler.handleCONNECT(&httpResponseWriter{conn: clientConn, req: req}, req)
					} else {
						// For regular HTTP requests, use our custom response writer
						respWriter := &httpResponseWriter{
							conn: clientConn,
							req:  req,
						}

						// Handle the request
						f.unifiedServer.Handler.ServeHTTP(respWriter, req)
					}
				}
			}(conn)
		}
	}()

	zlog.Infof("unified HTTP/HTTPS/SOCKS5 server started on port %d", f.Port)
	return nil
}

// unifiedHTTPHandler handles HTTP requests including CONNECT for HTTPS tunneling
type unifiedHTTPHandler struct {
	dialer    goproxy.Dialer
	transport *http.Transport
}

// ServeHTTP handles HTTP requests
func (h *unifiedHTTPHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	zlog.Debugf("unified proxy HTTP request: %s %s", r.Method, r.URL.String())

	if r.Method == "CONNECT" {
		h.handleCONNECT(w, r)
		return
	}

	// Handle regular HTTP requests
	h.handleHTTP(w, r)
}

// handleCONNECT handles CONNECT requests for HTTPS tunneling
func (h *unifiedHTTPHandler) handleCONNECT(w http.ResponseWriter, r *http.Request) {
	// Extract target host and port
	target := r.URL.Host
	if r.URL.Port() == "" {
		target = r.URL.Host + ":443" // Default HTTPS port
	}

	zlog.Debugf("unified proxy CONNECT request to: %s", target)

	// Hijack the connection
	hijacker, ok := w.(http.Hijacker)
	if !ok {
		zlog.Errorf("Hijacking not supported")
		http.Error(w, "Hijacking not supported", http.StatusInternalServerError)
		return
	}

	clientConn, _, err := hijacker.Hijack()
	if err != nil {
		zlog.Errorf("failed to hijack connection: %v", err)
		http.Error(w, err.Error(), http.StatusServiceUnavailable)
		return
	}
	defer clientConn.Close()

	// Send 200 OK to client
	clientConn.Write([]byte("HTTP/1.1 200 Connection established\r\n\r\n"))

	// Connect to target
	targetConn, err := h.dialer.Dial("tcp", target)
	if err != nil {
		zlog.Errorf("failed to connect to target %s: %v", target, err)
		clientConn.Close()
		return
	}
	defer targetConn.Close()

	// Start bidirectional data transfer
	zlog.Debugf("unified proxy starting data transfer for CONNECT to %s", target)
	go func() {
		written, err := io.Copy(targetConn, clientConn)
		zlog.Debugf("unified proxy client->target transfer complete: %d bytes, err: %v", written, err)
	}()
	written, err := io.Copy(clientConn, targetConn)
	zlog.Debugf("unified proxy target->client transfer complete: %d bytes, err: %v", written, err)
}

// handleHTTP handles regular HTTP requests
func (h *unifiedHTTPHandler) handleHTTP(w http.ResponseWriter, r *http.Request) {
	// Create reverse proxy for HTTP requests
	reverseProxy := &httputil.ReverseProxy{
		Director: func(req *http.Request) {
			// Preserve original request
			zlog.Debugf("unified proxy HTTP request: %s %s", req.Method, req.URL.String())
		},
		Transport: h.transport,
		ErrorHandler: func(w http.ResponseWriter, r *http.Request, err error) {
			zlog.Errorf("unified proxy HTTP error: %v", err)
			http.Error(w, fmt.Sprintf("unified proxy error: %v", err), http.StatusBadGateway)
		},
	}

	reverseProxy.ServeHTTP(w, r)
}

// bufferedConn wraps a net.Conn and a bufio.Reader
type bufferedConn struct {
	r *bufio.Reader
	net.Conn
}

func (b *bufferedConn) Read(data []byte) (int, error) {
	return b.r.Read(data)
}

// Implement other net.Conn methods to delegate to the underlying connection
func (b *bufferedConn) Write(data []byte) (int, error) {
	return b.Conn.Write(data)
}

func (b *bufferedConn) Close() error {
	return b.Conn.Close()
}

func (b *bufferedConn) LocalAddr() net.Addr {
	return b.Conn.LocalAddr()
}

func (b *bufferedConn) RemoteAddr() net.Addr {
	return b.Conn.RemoteAddr()
}

func (b *bufferedConn) SetDeadline(t time.Time) error {
	return b.Conn.SetDeadline(t)
}

func (b *bufferedConn) SetReadDeadline(t time.Time) error {
	return b.Conn.SetReadDeadline(t)
}

func (b *bufferedConn) SetWriteDeadline(t time.Time) error {
	return b.Conn.SetWriteDeadline(t)
}

// httpResponseWriter implements http.ResponseWriter for direct connection handling
type httpResponseWriter struct {
	conn       net.Conn
	req        *http.Request
	header     http.Header
	written    bool
	statusCode int
}

func (w *httpResponseWriter) Header() http.Header {
	if w.header == nil {
		w.header = make(http.Header)
	}
	return w.header
}

func (w *httpResponseWriter) Write(data []byte) (int, error) {
	if !w.written {
		w.WriteHeader(http.StatusOK)
	}
	return w.conn.Write(data)
}

func (w *httpResponseWriter) WriteHeader(statusCode int) {
	if w.written {
		return
	}
	w.statusCode = statusCode
	w.written = true

	// Write the HTTP response line
	statusText := http.StatusText(statusCode)
	if statusText == "" {
		statusText = "Unknown"
	}

	responseLine := fmt.Sprintf("HTTP/1.1 %d %s\r\n", statusCode, statusText)
	w.conn.Write([]byte(responseLine))

	// Write headers
	for key, values := range w.header {
		for _, value := range values {
			headerLine := fmt.Sprintf("%s: %s\r\n", key, value)
			w.conn.Write([]byte(headerLine))
		}
	}

	// Write end of headers
	w.conn.Write([]byte("\r\n"))
}

// Hijack implements http.Hijacker interface for CONNECT requests
func (w *httpResponseWriter) Hijack() (net.Conn, *bufio.ReadWriter, error) {
	if w.written {
		return nil, nil, fmt.Errorf("connection already written to")
	}

	// Create a bufio.ReadWriter for the connection
	br := bufio.NewReader(w.conn)
	bw := bufio.NewWriter(w.conn)
	rw := &bufio.ReadWriter{Reader: br, Writer: bw}

	return w.conn, rw, nil
}

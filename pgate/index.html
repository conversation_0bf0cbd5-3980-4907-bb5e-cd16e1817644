<!DOCTYPE html>
<html>
    <head>
        <title>Login Page</title>
        <style>
            #info {
                font-size: 24px;
                margin-top: 30px;
            }
            .error {
                color: red;
                font-size: 24px;
            }
            input {
                margin-bottom: 20px;
                display: block;
            } /* Added space at the bottom */
        </style>
    </head>
    <body>
        <input id="groupInput" type="text" placeholder="Proxy Group" />
        <input id="projectInput" type="text" placeholder="Project ID" />
        <input id="uidInput" type="text" placeholder="UID" />
        <button onclick="submitUID()">Login</button>
        <button onclick="generateAndSaveUID()">Generate Random UID</button>
        <button onclick="logout()" style="margin-left: 30px">logout</button>
        <div id="info"></div>
        <div id="error" class="error"></div>

        <script>
            function generateUID() {
                const chars = "123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ";
                let uid = "";
                for (let i = 0; i < 4; i++) {
                    uid += chars[Math.floor(Math.random() * chars.length)];
                }
                return uid;
            }

            function generateAndSaveUID() {
                let uid = generateUID();
                localStorage.setItem("uid", uid);
                document.getElementById("uidInput").value = uid;
                login();
            }

            function submitUID() {
                let uid = document.getElementById("uidInput").value;
                localStorage.setItem("uid", uid);
                login();
            }

            function login() {
                let uid = localStorage.getItem("uid");
                let group = document.getElementById("groupInput").value;
                let project = document.getElementById("projectInput").value;

                if (project === "" || uid === "") {
                    document.getElementById("error").innerText = "Error: Project ID and UID are required.";
                    return;
                }

                fetch(`/login?proxy_group=${group}&uid=${uid}&project=${project}`)
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.ok) {
                            message = data.data;
                            if (data.data.forwarder != undefined) {
                                message = data.data.forwarder;
                            }
                            document.getElementById("info").innerText = JSON.stringify(message);
                            document.getElementById("error").innerText = "";
                            setInterval(() => {
                                fetch(`/ping?uid=${uid}&project=${project}`).catch((error) => console.error("Error:", error));
                                checkForwarderStatus(uid);
                            }, 3000);
                        }
                    })
                    .catch((error) => console.error("Error:", error));
            }

            function logout() {
                let uid = document.getElementById("uidInput").value || localStorage.getItem("uid");
                if (!uid) {
                    document.getElementById("error").innerText = "Error: UID is required.";
                    return;
                }
                localStorage.setItem("uid", uid);
                let project = document.getElementById("projectInput").value;
                if (project === "" || uid === "") {
                    document.getElementById("error").innerText = "Error: Project ID and UID are required.";
                    return;
                }
                fetch(`/logout?uid=${uid}&project=${project}`)
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.ok) {
                            document.getElementById("info").innerText = "";
                        }
                    })
                    .catch((error) => console.error("Error:", error));
            }

            function checkForwarderStatus(uid) {
                let project = document.getElementById("projectInput").value;
                fetch(`/forwarders?project=${project}`)
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.ok) {
                            if (!data.data.hasOwnProperty(uid)) {
                                document.getElementById("info").innerText = "";
                                document.getElementById("error").innerText = "Error: Forwarder not found for this UID.";
                            }
                        }
                    })
                    .catch((error) => console.error("Error:", error));
            }

            window.onload = function () {
                let uid = localStorage.getItem("uid");
                if (uid) {
                    document.getElementById("uidInput").value = uid;
                }
            };
        </script>
    </body>
</html>
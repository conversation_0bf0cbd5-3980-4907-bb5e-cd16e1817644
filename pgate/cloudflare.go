package pgate

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

func (c *CloudFlareAccount) GetZoneID(domain string) (string, error) {
	for _, d := range c.Domains {
		if d.Domain == domain {
			return d.ZoneID, nil
		}
	}
	return "", fmt.<PERSON>rrorf("domain not found in Cloudflare account")
}

func (c *CloudFlareAccount) Validate() error {
	if c.Domains == nil || len(c.Domains) == 0 {
		return fmt.Errorf("no domain found in Cloudflare account")
	}
	client := &http.Client{}
	req, _ := http.NewRequest("GET", "https://api.cloudflare.com/client/v4/zones", nil)
	req.Header.Add("Authorization", "Bearer "+c.APIToken)
	req.Header.Add("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("verify Cloudflare API failed: %v", err)
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	res := map[string]interface{}{}
	json.Unmarshal(body, &res)

	if res["success"] != true {
		return fmt.Errorf("verify Cloudflare API failed, res: %v", res)
	}

	// 检查 Domains 是否存在
	result, ok := res["result"].([]interface{})
	if !ok {
		return fmt.Errorf("can not parse API response: %v", res)
	}

	for _, domain := range c.Domains {
		found := false
		for _, zone := range result {
			zoneInfo, ok := zone.(map[string]interface{})
			if !ok {
				return fmt.Errorf("can not parse API response: %v", res)
			}
			if zoneInfo["id"] == domain.ZoneID && zoneInfo["name"] == domain.Domain {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("domain not found in Cloudflare account: %s", domain.Domain)
		}
	}

	return nil
}

// GetDNSRecords 返回所有域名的 DNS 记录
func (c *CloudFlareAccount) GetDNSRecords() ([]CloudFlareDNSRecord, error) {
	var records []CloudFlareDNSRecord
	for _, domain := range c.Domains {
		dnsRecords, err := c.getDNSRecords(domain.ZoneID)
		if err != nil {
			return nil, err
		}
		records = append(records, dnsRecords...)
	}
	return records, nil
}

// GetDNSRecords 返回当前 Zone 的所有 DNS 记录
func (c *CloudFlareAccount) getDNSRecords(zoneID string) ([]CloudFlareDNSRecord, error) {
	client := &http.Client{}
	req, _ := http.NewRequest("GET", fmt.Sprintf("https://api.cloudflare.com/client/v4/zones/%s/dns_records", zoneID), nil)
	req.Header.Add("Authorization", "Bearer "+c.APIToken)
	req.Header.Add("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("获取 DNS 记录失败: %v", err)
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	var res struct {
		Success bool                  `json:"success"`
		Result  []CloudFlareDNSRecord `json:"result"`
	}
	if err := json.Unmarshal(body, &res); err != nil {
		return nil, fmt.Errorf("解析 JSON 响应失败: %v", err)
	}

	if !res.Success {
		return nil, fmt.Errorf("API 请求失败或 ZoneID 无效")
	}

	return res.Result, nil
}

// DNSRecordExists 检查特定名称的 DNS 记录是否存在
func (c *CloudFlareAccount) DNSRecordExists(names []string) (bool, error) {
	records, err := c.GetDNSRecords()
	if err != nil {
		return false, err
	}

	for _, record := range records {
		for _, name := range names {
			if record.Name == name {
				return true, nil
			}
		}
	}

	return false, nil
}

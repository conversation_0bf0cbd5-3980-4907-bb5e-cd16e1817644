package pgate

import (
	"crypto/md5"
	"encoding/hex"
	"io"
	"os"
	"regexp"
	"strings"
)

func TrimColor(s string) string {
	re := regexp.MustCompile(`\x1b\[[0-9;]*m`)
	s = re.ReplaceAllString(s, "")
	return s
}

func ReplaceMultipleSpaces(s string) string {
	re := regexp.MustCompile(`\s+`)
	return re.ReplaceAllString(s, " ")
}

// TextEqual 比较两个字符串是否相等，忽略前后空格和颜色
func TextEqual(a, b string) bool {
	a = ReplaceMultipleSpaces(TrimColor(strings.TrimSpace(a)))
	b = ReplaceMultipleSpaces(TrimColor(strings.TrimSpace(b)))
	return strings.EqualFold(a, b)
}

func calculateMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hasher := md5.New()
	if _, err := io.Copy(hasher, file); err != nil {
		return "", err
	}

	return hex.EncodeToString(hasher.Sum(nil)), nil
}

#! /usr/bin/env python3

# Convert pchrome JSON files to pgate accounts format

import json
import glob
import os
import sys
from typing import Dict, List


def read_pchrome_json(file_path: str) -> dict:
    """Read and parse a pchrome JSON file."""
    with open(file_path, 'r') as f:
        return json.load(f)

def convert_to_accounts_format(pchrome_data: dict) -> Dict[str, List[dict]]:
    """Convert pchrome data to accounts format."""
    accounts = {}
    
    # Process forwarders to create account entries
    for uid, forwarder_info in pchrome_data.get('forwarders', {}).items():
        proxy_name = forwarder_info.get('proxy')
        if not proxy_name:
            continue
            
        # Create account entry
        account_entry = {
            "uid": uid,
            "project": pchrome_data.get('project', ''),
            "last_active": None,
            "ip_addr": "",
            "script_ip_addr": ""
        }
        
        # Add to accounts dictionary
        if proxy_name not in accounts:
            accounts[proxy_name] = []
        accounts[proxy_name].append(account_entry)
    
    return accounts

def merge_accounts(accounts_list: List[Dict[str, List[dict]]]) -> Dict[str, List[dict]]:
    """Merge multiple accounts dictionaries, removing duplicates."""
    merged = {}
    
    for accounts in accounts_list:
        for proxy_name, client_list in accounts.items():
            if proxy_name not in merged:
                merged[proxy_name] = []
            
            # Add only unique UIDs
            existing_uids = {client['uid'] for client in merged[proxy_name]}
            for client in client_list:
                if client['uid'] not in existing_uids:
                    merged[proxy_name].append(client)
                    existing_uids.add(client['uid'])
    
    return merged

def main(data_dir: str):
    # Get all pchrome JSON files in the current directory
    pchrome_files = glob.glob(os.path.join(data_dir, '*.json'))
    
    # Read and convert each file
    all_accounts = []
    for file_path in pchrome_files:
        try:
            pchrome_data = read_pchrome_json(file_path)
            accounts = convert_to_accounts_format(pchrome_data)
            all_accounts.append(accounts)
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
    
    # Merge all accounts and remove duplicates
    final_accounts = merge_accounts(all_accounts)
    
    # Write to accounts.json
    with open(os.path.join(data_dir, 'accounts.json'), 'w') as f:
        json.dump(final_accounts, f, indent=4)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        data_dir = sys.argv[1]
        main(data_dir)
    else:
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        main(data_dir)

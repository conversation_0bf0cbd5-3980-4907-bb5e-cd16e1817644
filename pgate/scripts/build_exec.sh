#!/bin/bash
set -o nounset errexit

PROOT=$(pwd)
export PROOT
rm -r dist && mkdir dist && mkdir dist/pchrome & mkdir dist/hwid

go generate $PROOT/license/time_lock_license.go

# set this for builders behind GFW...
go env -w GOPROXY=https://goproxy.cn,direct
go install github.com/mitchellh/gox@v1.0.1
PATH=$PATH:$HOME/go/bin

cd $PROOT/cmds/pgate
# CC=x86_64-linux-musl-gcc CXX=x86_64-linux-musl-g++ GOARCH=amd64 GOOS=linux CGO_ENABLED=1 go build -ldflags "-linkmode external -extldflags -static -X main.buildTime=$(date +%Y-%m-%d.%H:%M:%S) -X main.commitHash=$(git rev-parse --short HEAD)" -v -o "$PROOT/dist/pgate_linux" main.go
CGO_ENABLED=1 GOOS=darwin GOARCH=arm64 go build  -ldflags "-X main.buildTime=$(date +%Y-%m-%d.%H:%M:%S) -X main.commitHash=$(git rev-parse --short HEAD)" -v -o "$PROOT/dist/pgate" main.go

cd $PROOT/cmds/pchrome
GOOS=windows GOARCH=arm64 go build  -ldflags "-X main.buildTime=$(date +%Y-%m-%d.%H:%M:%S) -X main.commitHash=$(git rev-parse --short HEAD)" -v -o $PROOT/dist/pchrome/pchrome_arm64_$(date +%Y%m%d%H%M%S).exe main.go

cd $PROOT/cmds/hwid
# gox \
#     -osarch="linux/amd64" \
#     -output "$PROOT/dist/hwid/hwid_linux"

gox \
    -osarch="darwin/arm64" \
    -output "$PROOT/dist/hwid/hwid_macos_arm64"

echo "Done"

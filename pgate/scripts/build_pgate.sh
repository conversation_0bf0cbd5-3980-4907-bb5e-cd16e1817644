#!/bin/bash
set -o nounset errexit

PROOT=$(pwd)
export PROOT
rm dist/pgate* 

go generate $PROOT/license/time_lock_license.go

# set this for builders behind GFW...
go env -w GOPROXY=https://goproxy.cn,direct
go install github.com/mitchellh/gox@v1.0.1
PATH=$PATH:$HOME/go/bin

cd $PROOT/cmds/pgate
CGO_ENABLED=1 GOOS=darwin GOARCH=arm64 go build  -ldflags "-X main.buildTime=$(date +%Y-%m-%d.%H:%M:%S) -X main.commitHash=$(git rev-parse --short HEAD)" -v -o "$PROOT/dist/pgate" main.go

echo "Done"

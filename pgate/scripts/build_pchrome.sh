#!/bin/bash
set -o nounset errexit

PROOT=$(pwd)
export PROOT
rm -r dist/pchrome && mkdir dist/pchrome

go generate $PROOT/license/time_lock_license.go

# set this for builders behind GFW...
go env -w GOPROXY=https://goproxy.cn,direct
go install github.com/mitchellh/gox@v1.0.1
PATH=$PATH:$HOME/go/bin

cd $PROOT/cmds/pchrome
GOOS=windows GOARCH=arm64 go build  -ldflags "-X main.buildTime=$(date +%Y-%m-%d.%H:%M:%S) -X main.commitHash=$(git rev-parse --short HEAD)" -v -o $PROOT/dist/pchrome/pchrome_arm64_$(date +%Y%m%d%H%M%S).exe main.go
GOOS=windows GOARCH=amd64 go build  -ldflags "-X main.buildTime=$(date +%Y-%m-%d.%H:%M:%S) -X main.commitHash=$(git rev-parse --short HEAD)" -v -o $PROOT/dist/pchrome/pchrome_amd64_$(date +%Y%m%d%H%M%S).exe main.go
echo "Done"

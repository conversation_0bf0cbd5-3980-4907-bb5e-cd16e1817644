#!/bin/bash
set -o nounset errexit

PROOT=$(pwd)
export PROOT

rm -r dist/hwid && mkdir dist/hwid

go generate $PROOT/license/time_lock_license.go

# set this for builders behind GFW...
go env -w GOPROXY=https://goproxy.cn,direct
# go install github.com/mitchellh/gox@v1.0.1
PATH=$PATH:$HOME/go/bin

# Compile hwid
cd cmds/hwid
GOARCH=amd64 GOOS=linux go build -v -o "$PROOT/dist/hwid/hwid_linux" main.go
GOOS=darwin GOARCH=arm64 go build -v -o "$PROOT/dist/hwid/hwid_macos_arm64" main.go
GOOS=windows GOARCH=amd64 go build -v -o "$PROOT/dist/hwid/hwid_windows_amd64.exe" main.go

echo "Done"

### 创建 AWS API 访问 ID 和密钥

1. 打开 IAM 控制台: https://console.aws.amazon.com/iam/home
2. 在导航菜单上，选择用户
3. 点击您的 IAM 用户名（不是复选框）
4. 打开安全凭证选项卡，然后选择创建访问密钥
5. 查看新的访问密钥，请选择“显示”。类似于以下内容：
Access key ID: AKIAIOSFODNN7EXAMPLE
Secret access key: wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
6. 妥善保存访问密钥

### Cloudflare API Token 创建

1. 打开个人资料页：https://dash.cloudflare.com/profile
2. 点击菜单 API Tokens
3. 点击按钮 Create Token
4. 选择模板 Edit zone DNS
5. 在 Zone Resources 选择对应的域名
6. 确认创建
7. 妥善保存 Token


### Google Cloud ServiceAccountJson 使用

明文模式：
1. 使用脚本 python scripts/single_line_json.py {path_to_json_file} true 获得转义后的 json 字符串
2. 在配置文件 service_account_json 字段使用该字符串

加密模式:
1. 使用脚本 python scripts/single_line_json.py {path_to_json_file} 获得 json 字符串
2. 使用 go run genkeys/genkeys.go 加密 json 字符串
3. 在配置文件 service_account_json_encrypted 字段使用加密后的字符串

# This file is autogenerated, do not edit; changes may be undone by the next 'dep ensure'.


[[projects]]
  branch = "master"
  name = "github.com/aristanetworks/goarista"
  packages = ["monotime"]
  revision = "2c5933638c5ef1bc320b01486100788c81d57b99"

[[projects]]
  branch = "master"
  name = "github.com/btcsuite/btcd"
  packages = [
    "btcec",
    "chaincfg",
    "chaincfg/chainhash",
    "wire"
  ]
  revision = "86fed781132ac890ee03e906e4ecd5d6fa180c64"

[[projects]]
  branch = "master"
  name = "github.com/btcsuite/btcutil"
  packages = [
    ".",
    "base58",
    "bech32",
    "hdkeychain"
  ]
  revision = "d4cc87b860166d00d6b5b9e0d3b3d71d6088d4d4"

[[projects]]
  name = "github.com/davecgh/go-spew"
  packages = ["spew"]
  revision = "346938d642f2ec3594ed81d874461961cd0faa76"
  version = "v1.1.0"

[[projects]]
  name = "github.com/ethereum/go-ethereum"
  packages = [
    ".",
    "accounts",
    "common",
    "common/hexutil",
    "common/math",
    "common/mclock",
    "core/types",
    "crypto",
    "crypto/secp256k1",
    "crypto/sha3",
    "ethdb",
    "event",
    "log",
    "metrics",
    "params",
    "rlp",
    "trie"
  ]
  revision = "dea1ce052a10cd7d401a5c04f83f371a06fe293c"
  version = "v1.8.11"

[[projects]]
  name = "github.com/go-stack/stack"
  packages = ["."]
  revision = "259ab82a6cad3992b4e21ff5cac294ccb06474bc"
  version = "v1.7.0"

[[projects]]
  branch = "master"
  name = "github.com/golang/snappy"
  packages = ["."]
  revision = "2e65f85255dbc3072edf28d6b5b8efc472979f5a"

[[projects]]
  branch = "master"
  name = "github.com/syndtr/goleveldb"
  packages = [
    "leveldb",
    "leveldb/cache",
    "leveldb/comparer",
    "leveldb/errors",
    "leveldb/filter",
    "leveldb/iterator",
    "leveldb/journal",
    "leveldb/memdb",
    "leveldb/opt",
    "leveldb/storage",
    "leveldb/table",
    "leveldb/util"
  ]
  revision = "0d5a0ceb10cf9ab89fdd744cc8c50a83134f6697"

[[projects]]
  branch = "master"
  name = "github.com/tyler-smith/go-bip39"
  packages = ["."]
  revision = "52158e4697b87de16ed390e1bdaf813e581008fa"

[[projects]]
  branch = "master"
  name = "golang.org/x/crypto"
  packages = [
    "pbkdf2",
    "ripemd160"
  ]
  revision = "a49355c7e3f8fe157a85be2f77e6e269a0f89602"

[[projects]]
  branch = "v2"
  name = "gopkg.in/karalabe/cookiejar.v2"
  packages = ["collections/prque"]
  revision = "8dcd6a7f4951f6ff3ee9cbb919a06d8925822e57"

[solve-meta]
  analyzer-name = "dep"
  analyzer-version = 1
  inputs-digest = "6ad4b2174ce7a75c33a183c8db5474e703deb5f8940416965fc4038652304c55"
  solver-name = "gps-cdcl"
  solver-version = 1

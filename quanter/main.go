package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/wizhodl/quanter/arbitrage"
	"github.com/wizhodl/quanter/common/stack"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/grid"
	"github.com/wizhodl/quanter/manager"
	"github.com/wizhodl/quanter/marketmaker"
	"github.com/wizhodl/quanter/trader"
	"github.com/wizhodl/quanter/turtle"
	"github.com/wizhodl/quanter/utils"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
)

/*

*名词及公式*
- 指标: ATR, DC84H, DC84L, DC42H, DC42L
- 多头开仓每单位数量 = DC84H * BTC数量 * 1% / ATR
- 多头开仓价依次为 DC84H, DC84H + 0.5 * ATR, DC84H + 1 * ATR, DC84H + 1.5 * ATR
- 多头退出价 = DC42L, 随 K 线更新
- 多头止损价 = 最后开仓价 - 2 * ATR
- 多头实际止损价 = 退出价 < 开仓均价 ? max(退出价, 止损价) : 止损价
- 多头平仓止损订单: 按多头实际止损价挂单；
- 多头平仓退出订单: 仅退出价 > 开仓均价时挂单；
- 空头开仓单位数量 = DC84L * BTC数量 * 1% / ATR
- 空头开仓价依次为 DC84L, DC84L - 0.5 * ATR, DC84L - 1 * ATR, DC84L - 1.5 * ATR
- 空头退出价 = DC42H, 随 K 线更新
- 空头止损价 = 最后开仓价 + 2 * ATR
- 空头实际止损价 = 退出价 > 开仓均价 ? min(退出价, 止损价) : 止损价
- 空头平仓止损订单: 按空头实际止损价挂单；
- 空头平仓退出订单: 仅退出价 < 开仓均价时挂单；
- 挂单类型: Stop Limit
- 平仓数量: 当前持仓数量
- 杠杆率 = 实际止损价 / (abs(开仓均价 - 实际止损价) + 0.005 * 开仓均价), 且不得小于固定值 15
- 挂单滑点: 多单限价 = 限价 * (1 + 滑点率)，空单限价 = 限价 * (1 - 滑点率)
- 杠杠率的止损价滑点: 多头实际止损价 = 实际止损价 * (1 + 滑点率)，空头实际止损价 = 实际止损价 * (1 - 滑点率)

*基本流程*
- 如果当前无持仓，则创建或修改 long1, long2, long3, long4, short1, short2, short3, short4 开仓订单；
- 有任一开仓订单成交时，取消对立方向开仓订单；
- 开仓订单部分成交或完成成交后，按当前持仓创建或修改平仓止损、退出订单；
- 如果当前有持仓，推测当前持仓状态后，按需创建或修改加仓单、平仓单；
- 平仓订单触发或成交后，取消其他挂单；
- K 线更新时，根据最新数据修改挂单；

*/

var (
	buildTime  string // 编译时间
	commitHash string // git commit hash
)

func main() {

	var trenderID string
	var arbitragerID string
	var griderID string
	var managerID string
	var traderID string
	var makerID string
	var configPath string
	var version bool
	var httpPort int
	var debug bool
	var logDirPath string
	var releaseBinaryDirPath string

	flag.BoolVar(&version, "version", false, "获取版本号，编译时必须由脚本提供 GitHash 和 BuildTime，否则为空")
	flag.StringVar(&trenderID, "trenderID", "", "Trender 名称【可选】，指定后可以独立运行某个趋势机")
	flag.StringVar(&arbitragerID, "arbitragerID", "", "Arbitrager 名称【可选】，指定后可以独立运行某个套利机")
	flag.StringVar(&griderID, "griderID", "", "Grider 名称【可选】，指定后可以独立运行某个网格机")
	flag.StringVar(&traderID, "traderID", "", "Trader 名称【可选】，指定后可以独立运行某个交易机")
	flag.StringVar(&makerID, "makerID", "", "MarketMaker 名称【可选】，指定后可以独立运行某个做市机")
	flag.StringVar(&managerID, "managerID", "quanters", "量化程序名字，会在 configPath 下寻找 managerID.toml 的配置")
	flag.StringVar(&configPath, "configPath", ".", "配置文件路径【必填】")
	flag.IntVar(&httpPort, "httpPort", 7070, "HTTP 服务端口，localhost:httpPort")
	flag.BoolVar(&debug, "debug", false, "以 Debug 模式运行程序")
	flag.StringVar(&logDirPath, "logDirPath", "", "日志所在目录，turtleID.log 或 managerID.log（仅用于读取日志，不输出日志）")
	flag.StringVar(&releaseBinaryDirPath, "releaseBinaryDirPath", "", "升级程序二进制所在目录")
	flag.Parse()

	if version {
		fmt.Printf("Build: %s/(%s)\n", commitHash, buildTime)
		os.Exit(0)
	}

	if debug {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// TODO: 检查 SetDebug 替换 zlog.logger 之后，会不会导致 ginzap 获取的 logger 为 nil，从而导致崩溃
	router := gin.Default()
	pprof.Register(router)

	// 如果传递了 turtleID，直接以 standalone 模式运行 TurtleController；
	// 如果传递了 arbiID，直接运行 arbiController；
	// 否则运行 QuanterManager
	logLevel := "INFO"
	if debug {
		logLevel = "DEBUG"
	}
	zlog.SetLogger(zlog.NewRotateLogger(logLevel, "", nil))
	zlog.Infof("config path: %s", configPath)

	var stackHttpHandler *utils.StackHttpHandler

	var exitFunc func()
	var id string
	if trenderID != "" {
		id = trenderID
		controller, err := turtle.NewTurtleController(trenderID, debug, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath, nil, "")
		if err != nil {
			zlog.Panicf(err.Error())
		}
		stackHttpHandler = utils.NewStackHttpHandler(controller)
		// 开始运行策略
		controller.Run()
		exitFunc = controller.Exit
	} else if arbitragerID != "" {
		id = arbitragerID
		arbitrager, err := arbitrage.NewArbitragerController(arbitragerID, debug, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath, nil, "")
		if err != nil {
			zlog.Panicf(err.Error())
		}
		stackHttpHandler = utils.NewStackHttpHandler(arbitrager)
		arbitrager.Run()
		exitFunc = arbitrager.Exit
	} else if griderID != "" {
		id = griderID
		controller, err := grid.NewGridController(griderID, debug, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath, nil, "", nil)
		if err != nil {
			zlog.Panicf(err.Error())
		}
		stackHttpHandler = utils.NewStackHttpHandler(controller)
		controller.Run()
		exitFunc = controller.Exit
	} else if traderID != "" {
		id = traderID
		controller, err := trader.NewTraderController(traderID, debug, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath, nil, "")
		if err != nil {
			zlog.Panicf(err.Error())
		}
		stackHttpHandler = utils.NewStackHttpHandler(controller)
		controller.Run()
		exitFunc = controller.Exit
	} else if makerID != "" {
		id = makerID
		controller, err := marketmaker.NewMarketMakerController(makerID, debug, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath, nil, "")
		if err != nil {
			zlog.Panicf(err.Error())
		}
		stackHttpHandler = utils.NewStackHttpHandler(controller)
		controller.Run()
		exitFunc = controller.Exit
	} else if managerID != "" {
		id = managerID
		manager := manager.NewQuanterManager(managerID, debug, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath)
		stackHttpHandler = utils.NewStackHttpHandler(manager)
		exitFunc = manager.Exit
	}

	stackHttpHandler.RegisterRoutes(router)
	stack.StartStorage(id, logDirPath, 15)

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		zlog.Debugf("run router at: %d", httpPort)
		router.Run(fmt.Sprintf(":%d", httpPort))
	}()

	sig := <-sigChan
	zlog.Infof("got signal: %s", sig)
	if exitFunc != nil {
		exitFunc()
	}
	zlog.Infof("quanter exit")
}

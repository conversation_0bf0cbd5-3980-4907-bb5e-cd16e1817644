# 新建文件 {arbiID}.arbi.toml 复制以下内容，修改对应配置项

Debug = false
ExchangeName = "Binance"
AllowedSymbolPrefixs = ["BTC", "ETH"]
APIKey = "YOUR_API_KEY"
APISecret = "YOUR_API_SECRET"
IsTestnet = false
ProxyURL = "http://127.0.0.1:1087"
ReleaseBinaryDirPath = "/home/<USER>/turtle_releases"
LogDirPath = ""

Splits = 27
SplitWait = 200
BasisTolerance = 0.1
BasisWait = 5
SpotSlippage = 0.001
FutureSlippage = 0.0015
SplitSpotQtyTolerance = 0.001
SplitFutureQtyTolerance = 0.001
SplitBasisRatioWarningTolerance = 0.15
SplitBasisRatioErrorTolerance = 0.2
SpotRetryInterval = 300
TransferRetryInterval = 300
FutureRetryInterval = 300
GetBasisRetryInterval = 300
Timeout = 15

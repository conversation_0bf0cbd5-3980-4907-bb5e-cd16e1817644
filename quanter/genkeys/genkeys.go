package main

import (
	"fmt"
	"math/rand"
	"os"
	"strings"
	"time"

	"github.com/AlecAivazis/survey/v2"
	qrterminal "github.com/mdp/qrterminal/v3"
	"github.com/mitchellh/go-homedir"

	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/binance"
	"github.com/wizhodl/quanter/exchange/bybit"
	"github.com/wizhodl/quanter/exchange/gateway"
	"github.com/wizhodl/quanter/exchange/hyperliquid"
	"github.com/wizhodl/quanter/exchange/okex"
	"github.com/wizhodl/quanter/messenger"
	"github.com/wizhodl/quanter/secrets"
)

type SlackConfigInfo struct {
	RobotToken   string
	CleanerToken string
	ChannelID    string
	Commander    string
}

func main() {

	if !secrets.CheckEncryptSalt() {
		surveyYes("EncryptSalt 为空，按回车结束")
		return
	}

	selected, _ := surveySelect("请选择一个命令：", []string{
		"用新密码生成一个配置",
		"使用已有密码加密 “交易所API“",
		"加密一个: 字符串",
		"加密一个: 文件",
		"解密一个: 字符串",
	})

	switch selected {
	case 0:
		newSecret()
	case 1:
		encryptAPIKey()
	case 2:
		encryptText()
	case 3:
		encryptFile()
	case 4:
		decryptGeneral()
	}

}

func newSecret() {
	authSecret := strings.ToUpper(randKey(16, false))
	mainKey := surveyPassword("请输入一个新的密码: ")
	if mainKey == "" {
		mainKey = randKey(8, true)
	}
	fmt.Printf("\n主密码: %s...%s (%d位)\n", mainKey[:1], mainKey[len(mainKey)-1:], len(mainKey))
	fmt.Printf("两步验证秘钥: %s...%s (%d位)\n\n", authSecret[:2], authSecret[len(authSecret)-2:], len(authSecret))
	slackConfig := confirmSlackConfig()

	if slackConfig == nil {
		return
	}

	fmt.Printf("\n*** 请确认 slack 配置信息 *** \n")
	fmt.Printf("\n用户ID: %s", slackConfig.Commander)

	SLACK_ROBOT_TOKEN, err := secrets.EncryptForCommander(slackConfig.Commander, fmt.Sprintf("%s|||%s", slackConfig.RobotToken, slackConfig.CleanerToken), "")
	if err != nil {
		zlog.Panicf("加密 slack robot token 失败: %s", err)
	}
	encryptedAuthSecret, err := secrets.EncryptForCommander(slackConfig.Commander, authSecret, mainKey)
	if err != nil {
		zlog.Panicf("加密 auth secret 失败: %s", err)
	}

	fileContent := fmt.Sprintf(`ENCRYPTED_AUTH_SECRET : %s`, encryptedAuthSecret) + "\n" +
		`AUTH_SECRET : ` + "\n" +
		fmt.Sprintf(`SLACK_ROBOT_TOKEN : %s`, SLACK_ROBOT_TOKEN) + "\n" +
		fmt.Sprintf(`SLACK_COMMANDER : %s`, slackConfig.Commander) + "\n"

	fmt.Printf("\n============================\n\n%s\n\n============================\n", fileContent)
	fmt.Println("\n请将以上内容拷贝到 secrets.yaml 中")

	qrcodeStr := fmt.Sprintf("otpauth://totp/quant-%s?secret=%s", mainKey, authSecret)
	fmt.Printf("\n*** 请记录 Slack 指令的谷歌验证 ***\n")
	fmt.Printf("\n%s\n", qrcodeStr)

	config := qrterminal.Config{
		Level:          qrterminal.M,
		Writer:         os.Stdout,
		HalfBlocks:     true,
		BlackChar:      qrterminal.BLACK_BLACK,
		WhiteBlackChar: qrterminal.WHITE_BLACK,
		WhiteChar:      qrterminal.WHITE_WHITE,
		BlackWhiteChar: qrterminal.BLACK_WHITE,
		QuietZone:      1,
	}

	// 生成 AUTH_SECRET 的二维码
	qrterminal.GenerateWithConfig(qrcodeStr, config)

	fmt.Printf("\n*** 请记录启动程序的密码 ***\n")
	fmt.Printf("\n%s\n", mainKey)
	fmt.Printf("\n")

	surveyYes("\n按回车结束")
}

func surveyYes(tips string) bool {
	result := false
	prompt := &survey.Confirm{
		Message: tips,
	}
	survey.AskOne(prompt, &result)
	return result
}

func surveyPassword(message string) string {
	s := ""
	survey.AskOne(&survey.Password{
		Message: message,
	}, &s)
	return strings.TrimSpace(s)
}

func surveyInput(message string) string {
	s := ""
	survey.AskOne(&survey.Input{
		Message: message,
	}, &s)
	return strings.TrimSpace(s)
}

func surveySelect(message string, choices []string) (int, string) {
	if len(choices) == 0 {
		return -1, ""
	}
	s := ""
	survey.AskOne(&survey.Select{
		Message:  message,
		Options:  choices,
		PageSize: 10,
	}, &s)
	for idx, choice := range choices {
		if strings.EqualFold(s, choice) {
			return idx, choice
		}
	}
	return 0, choices[0]
}

func confirmSlackConfig() (slackConfig *SlackConfigInfo) {
	slackRobotToken := surveyPassword("请输入 Slack Robot Token: ")
	slackCleanerToken := surveyPassword("请输入 Slack Cleaner Token: ")
	if !strings.HasPrefix(slackRobotToken, "xoxb-") {
		fmt.Printf("请确认 Slack Robot Token 的格式以 \"xoxb-\" 开头。\n")
		return
	}
	if slackCleanerToken != "" && !strings.HasPrefix(slackCleanerToken, "xoxp-") {
		fmt.Printf("请确认 Slack Cleaner Token 的格式以 \"xoxp-\" 开头。\n")
		return
	}
	slackRobot := messenger.NewSlackMessenger(secrets.SecretString(slackRobotToken), "", secrets.SecretString(slackCleanerToken))

	channels, err := slackRobot.GetChannels()

	if err != nil {
		fmt.Printf("获取频道列表失败: %s\n", err)
		return
	}

	channelChoices := []string{}
	for _, channel := range channels {
		channelChoices = append(channelChoices, fmt.Sprintf("Name: %s, ID: %s, Creator: %s, IsPrivate: %t", channel.Name, channel.ID, channel.Creator, channel.IsPrivate))
	}

	channelIndex, _ := surveySelect("请任意选择一个您所在的频道：", channelChoices)
	if channelIndex < 0 {
		zlog.Panicf("频道列表为空，请确保机器人已加入到频道中。")
	}
	channel := channels[channelIndex]
	zlog.Debugf("channel: %v", channel)
	if slackCleanerToken != "" {
		err2 := slackRobot.TestCleaner(channel.ID)
		if err2 != nil {
			fmt.Printf("测试 Cleaner Token 失败，error: %s", err2)
			return
		}
	}

	return &SlackConfigInfo{
		RobotToken:   slackRobotToken,
		CleanerToken: slackCleanerToken,
		ChannelID:    channel.ID,
		Commander:    channel.Creator,
	}
}

func randKey(n int, includedNum bool) string {
	rand.Seed(time.Now().UnixNano())
	randStr := "abcdefghijkmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	if includedNum {
		randStr = "1234567890abcdefghijkmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	}
	var letterRunes = []rune(randStr)

	b := make([]rune, n)
	for i := range b {
		b[i] = letterRunes[rand.Intn(len(letterRunes))]
	}
	return string(b)
}

func encryptAPIKey() {
	exchanges := []string{exchange.BitMEX, exchange.Binance, exchange.OKEx, exchange.Hyperliquid, exchange.Bybit, exchange.CTP, exchange.MetaTrader, exchange.InteractiveBrokers}
	_, exchangeName := surveySelect("请选择交易所：", exchanges)
	var api exchange.Secretable
	switch exchangeName {
	case exchange.CTP:
		api = gateway.NewEmptyCTPAPISecret()
	case exchange.MetaTrader:
		api = gateway.NewEmptyMetaTraderAPISecret()
	case exchange.Hyperliquid:
		api = hyperliquid.NewEmptyAPISecret()
	case exchange.OKEx:
		api = okex.NewEmptyAPISecret()
	case exchange.Binance:
		api = binance.NewEmptyAPISecret()
	case exchange.Bybit:
		api = bybit.NewEmptyAPISecret()
	default:
		api = exchange.NewEmptyAPISecret(exchangeName)
	}
	apiString, err := api.Generate()
	if err == nil {
		fmt.Println("请将以下输出拷贝到配置文件:")
		fmt.Println("============================")
		fmt.Println(apiString)
		fmt.Println("============================")
	}
}

func decryptGeneral() {
	apiSecretEncrypted := surveyInput("请输入加密后的 Secret: ")

	password := surveyPassword("请输入主密码: ")
	authCode := surveyInput("请输入 2FA: ")
	apiSecret, err := decryptWithPassword(apiSecretEncrypted, password, authCode)
	if err != nil {
		fmt.Printf("解密失败: %s\n", err)
		return
	}
	showFullValue := surveyYes("是否显示完整的 secret ？")

	fmt.Printf("\n解密后的 Secret:\n\n")
	if showFullValue {
		fmt.Printf(`Secret = "%s"`, string(apiSecret))
	} else {
		fmt.Printf(`Secret = "%s"`, apiSecret)
	}
	fmt.Printf("\n")
}

func decryptWithPassword(encryptedApiSecret string, password string, authCode string) (apiSecret secrets.SecretString, err error) {
	err = secrets.CheckPassword(password, authCode)
	if err != nil {
		return "", err
	}
	apiSecret, err = secrets.Decrypt(encryptedApiSecret)
	return
}

func encryptText() {
	apiSecret := surveyPassword("请输入 Secret: ")
	fmt.Printf("\n\n*** 请确认输入的API *** \n\nSecret: %s******%s (%d位)\n\n",
		apiSecret[:2],
		apiSecret[len(apiSecret)-2:],
		len(apiSecret),
	)

	password := surveyPassword("请输入主密码: ")
	authCode := surveyInput("请输入 2FA: ")
	encryptedApiSecret, err := exchange.EncryptWithPassword(apiSecret, password, authCode)
	if err != nil {
		fmt.Printf("加密失败: %s\n", err)
		return
	}

	fmt.Printf("\n请将以下输出拷贝到配置文件:\n\n")
	fmt.Printf(`EncryptedSecret = "%s"`, encryptedApiSecret)
	fmt.Printf("\n")
}

func encryptFile() {
	// 读取文件字符串加密
	filePath := surveyInput("请输入文件路径: ")
	filePath, _ = homedir.Expand(filePath)
	fileContent, err := os.ReadFile(filePath)
	if err != nil {
		fmt.Printf("读取文件失败: %s\n", err)
		return
	}

	password := surveyPassword("请输入程序主密码: ")
	authCode := surveyInput("请输入 2FA: ")

	encryptedFileContent, err := exchange.EncryptWithPassword(string(fileContent), password, authCode)
	if err != nil {
		fmt.Printf("加密失败: %s\n", err)
		return
	}

	fmt.Printf("\n请将以下输出拷贝到配置文件:\n\n")
	fmt.Printf(`ApiSecretEncrypted: "%s"`, encryptedFileContent)
	fmt.Printf("\n")
}

#!/bin/bash

echo "编译中..."

go generate secrets/secrets.go

GOOS=linux GOARCH=amd64 go build -ldflags "-X main.buildTime=$(date +%Y-%m-%d.%H:%M:%S) -X main.commitHash=$(git rev-parse --short HEAD)" -v -o main_linux main.go
GOOS=darwin GOARCH=arm64 go build  -ldflags "-X main.buildTime=$(date +%Y-%m-%d.%H:%M:%S) -X main.commitHash=$(git rev-parse --short HEAD)" -v -o main_osx_arm64 main.go

echo "编译完成"

cd genkeys
GOOS=darwin GOARCH=arm64 go build -v -o genkeys_osx_arm64 genkeys.go

echo "genkeys 编译完成"



package zlog

import (
	"fmt"
	"net/url"
	"os"
	"runtime"
	"strings"

	"github.com/wizhodl/quanter/common/stack"
	"gopkg.in/natefinch/lumberjack.v2"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var logger *zap.SugaredLogger

func init() {
	logger = NewRotateLogger("DEBUG", "", nil)
}

func SetLogger(zlogger *zap.SugaredLogger) {
	logger = zlogger
}

func GetLogger() *zap.SugaredLogger {
	return logger
}

// 打印 Level 的同时，打印 StackHash，并且将日志等级打印为彩色的，用于 Debug 模式
func ColoredStackLevelEncoder(level zapcore.Level, enc zapcore.PrimitiveArrayEncoder) {
	levelStr := level.CapitalString()
	coloredLevelStr := levelStr

	red := "\u001b[31m"
	green := "\u001b[32m"
	yellow := "\u001b[33m"
	blue := "\u001b[34m"
	magenta := "\u001b[35m"
	// cyan := "\u001b[36m"
	// reset := "\u001b[0m"

	switch levelStr {
	case "DEBUG":
		coloredLevelStr = fmt.Sprintf("%s[%s]\u001b[0m", blue, levelStr)
	case "INFO":
		coloredLevelStr = fmt.Sprintf("%s[%s]\u001b[0m", green, levelStr)
	case "WARN":
		coloredLevelStr = fmt.Sprintf("%s[%s]\u001b[0m", yellow, levelStr)
	case "ERROR":
		coloredLevelStr = fmt.Sprintf("%s[%s]\u001b[0m", red, levelStr)
	case "DPANIC":
		coloredLevelStr = fmt.Sprintf("%s[%s]\u001b[0m", magenta, levelStr)
	case "PANIC":
		coloredLevelStr = fmt.Sprintf("%s[%s]\u001b[0m", magenta, levelStr)
	case "FATAL":
		coloredLevelStr = fmt.Sprintf("%s[%s]\u001b[0m", magenta, levelStr)
	}
	logCallerEnv := os.Getenv("QUANTER_LOG_CALLER")
	if logCallerEnv == "1" {
		enc.AppendString(coloredLevelStr)
	} else {
		enc.AppendString(coloredLevelStr + fmt.Sprintf("@%s", stack.GetStackHash()))
	}
}

// 打印 Level 的同时，打印 StackHash，非彩色；用于非 Debug 模式
func StackLevelEncoder(level zapcore.Level, enc zapcore.PrimitiveArrayEncoder) {
	levelStr := fmt.Sprintf("[%s]", level.CapitalString())
	logCallerEnv := os.Getenv("QUANTER_LOG_CALLER")
	if logCallerEnv == "1" {
		enc.AppendString(levelStr)
	} else {
		enc.AppendString(levelStr + fmt.Sprintf("@%s", stack.GetStackHash()))
	}
}

func newWinFileSink(u *url.URL) (zap.Sink, error) {
	// Remove leading slash left by url.Parse()
	return os.OpenFile(u.Path[1:], os.O_WRONLY|os.O_APPEND|os.O_CREATE, 0644)
}

type lumberjackSink struct {
	*lumberjack.Logger
}

func (lumberjackSink) Sync() error {
	return nil
}

type LogRotation struct {
	MaxSize    int // MB
	MaxBackups int
	MaxAge     int // days
}

// 可 rotation 的 logger，windows 下默认使用 rotation
func NewRotateLogger(level string, logPath string, rotation *LogRotation) *zap.SugaredLogger {
	return newLogger(level, logPath, rotation, false)
}

// 无 rotation 的 logger
func NewLogger(level string, logPath string) *zap.SugaredLogger {
	return newLogger(level, logPath, nil, true)
}

// 设置 Logger ，如果 Controller.Exchange 初始化了，将其同步设置为新的 Logger
// 设置日志等级为终端彩色颜色： export TURTLE_LOG_COLORED_LEVEL=1
// 设置日志打印文件和行号：export TURTLE_LOG_CALLER=1
// 如果 logPath 为空，则不输出日志到文件
func newLogger(level string, logPath string, rotation *LogRotation, skipRotation bool) *zap.SugaredLogger {
	var cfg zap.Config
	loggerExist := logger != nil

	if !strings.Contains("DEBUG/INFO/WARN/ERROR/FATAL", level) {
		level = "DEBUG"
	}

	atomicLevel := zap.NewAtomicLevelAt(zapcore.InfoLevel)
	switch level {
	case "DEBUG":
		atomicLevel = zap.NewAtomicLevelAt(zapcore.DebugLevel)
	case "ERROR":
		atomicLevel = zap.NewAtomicLevelAt(zapcore.ErrorLevel)
	case "WARN":
		atomicLevel = zap.NewAtomicLevelAt(zapcore.WarnLevel)
	}

	outputPaths := []string{"stdout"}
	if logPath != "" {
		if runtime.GOOS == "windows" && rotation == nil {
			rotation = &LogRotation{
				MaxSize:    100,
				MaxBackups: 5,
				MaxAge:     30,
			}
		}

		if rotation != nil && !skipRotation {
			if rotation.MaxBackups <= 0 {
				rotation.MaxBackups = 5
			}
			// lumberjack 用于处理日志文件切割
			ll := lumberjack.Logger{
				Filename:   logPath,
				MaxSize:    rotation.MaxSize,
				MaxBackups: rotation.MaxBackups,
				MaxAge:     rotation.MaxAge,
			}
			zap.RegisterSink("lumberjack", func(u *url.URL) (zap.Sink, error) {
				s := lumberjackSink{
					Logger: &ll,
				}
				return s, nil
			})
			// lumberjack 可以支持绝对路径，就不需要在 outputPaths 中增加 hack 的 winfile:/// 协议的路径了
			// 同时通过 winfile 和 lumberjack 两种方式输出日志到同一个文件可能有竞争问题
			logPath = fmt.Sprintf("lumberjack:%s", logPath)
		}
		outputPaths = append(outputPaths, logPath)
	}

	cfg = zap.Config{
		Level:             atomicLevel,
		Encoding:          "console",
		OutputPaths:       outputPaths,
		DisableStacktrace: true,
		EncoderConfig: zapcore.EncoderConfig{
			MessageKey:  "message",
			TimeKey:     "time",
			EncodeTime:  zapcore.TimeEncoderOfLayout("2006-01-02 15:04:05"),
			LevelKey:    "level",
			EncodeLevel: StackLevelEncoder,
		}}

	coloredLevelEnv := os.Getenv("QUANTER_LOG_COLORED_LEVEL")
	if coloredLevelEnv == "1" {
		cfg.EncoderConfig.EncodeLevel = ColoredStackLevelEncoder
	}

	logCallerEnv := os.Getenv("QUANTER_LOG_CALLER")
	if logCallerEnv == "1" {
		cfg.EncoderConfig.CallerKey = "caller"
		cfg.EncoderConfig.EncodeCaller = zapcore.ShortCallerEncoder
	}

	_logger, err := cfg.Build()
	if err != nil {
		if loggerExist {
			logger.Errorf("setup logger failed. %s", err)
		} else {
			panic(fmt.Sprintf("init logger failed. %s", err))
		}
	} else {
		_logger = _logger.WithOptions(zap.AddCallerSkip(1)) // 因为我们自己封装了一层 zlog，如果不设置这个，所有的 caller 都会打印 zlog.Errorf... 这些函数，没有啥意义
		logger = _logger.Sugar()
		if loggerExist {
			logger.Infof("setup logger success.")
		}
	}

	return logger
}

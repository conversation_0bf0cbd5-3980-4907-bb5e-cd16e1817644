package extstruct

import "errors"

// 使用时按如下方式定义 tags
// type OrderTag = uint
// const (
// 	OrderTagTakeProfit OrderTag = 1 << iota
// 	OrderTagStopLoss
//  OrderTagManualClose
// )

func IsPowerOf2(x uint) bool {
	return (x > 0) && (x&(x-1) == 0)
}

func (s *ExtStruct) AddTag(tag uint) (tags uint, er error) {
	if !IsPowerOf2(tag) {
		return s.Tags, errors.New("tags is not power of 2")
	}
	if s.Tags&tag != tag {
		s.Tags |= tag
	}
	return s.Tags, nil
}

// tags 用或操作连接：SetTags(OrderTagTakeProfit|OrderTagManualClose)
func (s *ExtStruct) SetTags(tags uint) (er error) {
	if !IsPowerOf2(tags) {
		return errors.New("tags is not power of 2")
	}
	s.Tags = tags
	return nil
}

func (s *ExtStruct) HasTag(tag uint) bool {
	return s.Tags&tag == tag
}

package extstruct

import "time"

type ExtStructable interface {
	GetExtFloat64() map[string]float64
	GetExtInt() map[string]int
	GetExtBool() map[string]bool
	GetExtString() map[string]string
	GetExtTime() map[string]*time.Time
}

func (s *ExtStruct) SetExtStruct(s2 ExtStructable) {
	s.ExtFloat64 = s2.GetExtFloat64()
	s.ExtInt = s2.GetExtInt()
	s.ExtBool = s2.GetExtBool()
	s.ExtString = s2.GetExtString()
	// 不太确认 ExtTime 中的 *time.Time 是否可以拷贝，解析了重新设置一次
	for key, value := range s2.GetExtTime() {
		if newTime, err := time.Parse(time.RFC3339Nano, value.Format(time.RFC3339Nano)); err != nil {
			s.SetTime(key, &newTime)
		}
	}
}

func (s *ExtStruct) GetExtFloat64() map[string]float64 {
	return s.ExtFloat64
}

func (s *ExtStruct) GetExtInt() map[string]int {
	return s.ExtInt
}

func (s *ExtStruct) GetExtBool() map[string]bool {
	return s.ExtBool
}

func (s *ExtStruct) GetExtString() map[string]string {
	return s.ExtString
}

func (s *ExtStruct) GetExtTime() map[string]*time.Time {
	return s.ExtTime
}

func (s *ExtStruct) SetFloat64(key string, value float64) (replacedValue *float64) {
	if s.ExtFloat64 == nil {
		s.ExtFloat64 = map[string]float64{}
	}
	if v, ok := s.ExtFloat64[key]; ok {
		replacedValue = &v
	}
	s.ExtFloat64[key] = value
	return
}

func (s *ExtStruct) GetFloat64(key string) (value float64) {
	if s.ExtFloat64 == nil {
		return 0
	}
	if v, ok := s.ExtFloat64[key]; ok {
		value = v
	}
	return
}

func (s *ExtStruct) DeleteFloat64(key string) (deletedValue *float64) {
	if v, ok := s.ExtFloat64[key]; ok {
		deletedValue = &v
		delete(s.ExtFloat64, key)
	}
	return
}

// 清理结构中的扩展字段，用于存储时批量去除不需要的字段
// 比如：Bitmex 交易所底层可能使用了 PosCost 的扩展字段，但是 Trender 应用中并不需要保留这个扩展字段
//      如果使用 DeleteXXX 函数，就需要确切知道所有底层用的扩展字段，而这种情况可能是变动的，也就是所可能不知道哪些需要清除
//      因此，可以通过白名单(keepKeys = true)清理掉所有不想要的字段，仅保留想要的 keys
func (s *ExtStruct) CleanFloat64(keys []string, keepKeys bool) (cleanedKeys []string) {
	cleanedKeys = []string{}
	for k := range s.ExtFloat64 {
		keep := !keepKeys
		for _, key := range keys {
			if k == key {
				keep = keepKeys
				break
			}
		}
		if !keep {
			delete(s.ExtFloat64, k)
			cleanedKeys = append(cleanedKeys, k)
		}
	}
	return
}

func (s *ExtStruct) SetInt(key string, value int) (replacedValue *int) {
	if s.ExtInt == nil {
		s.ExtInt = map[string]int{}
	}
	if v, ok := s.ExtInt[key]; ok {
		replacedValue = &v
	}
	s.ExtInt[key] = value
	return
}

func (s *ExtStruct) GetInt(key string) (value int) {
	if s.ExtInt == nil {
		return 0
	}
	if v, ok := s.ExtInt[key]; ok {
		value = v
	}
	return
}

func (s *ExtStruct) DeleteInt(key string) (deletedValue *int) {
	if v, ok := s.ExtInt[key]; ok {
		deletedValue = &v
		delete(s.ExtInt, key)
	}
	return
}

func (s *ExtStruct) CleanInt(keys []string, keepKeys bool) (cleanedKeys []string) {
	cleanedKeys = []string{}
	for k := range s.ExtInt {
		keep := !keepKeys
		for _, key := range keys {
			if k == key {
				keep = keepKeys
				break
			}
		}
		if !keep {
			delete(s.ExtInt, k)
			cleanedKeys = append(cleanedKeys, k)
		}
	}
	return
}

func (s *ExtStruct) SetBool(key string, value bool) (replacedValue *bool) {
	if s.ExtBool == nil {
		s.ExtBool = map[string]bool{}
	}
	if v, ok := s.ExtBool[key]; ok {
		replacedValue = &v
	}
	s.ExtBool[key] = value
	return
}

func (s *ExtStruct) GetBool(key string) (value bool) {
	if s.ExtBool == nil {
		return false
	}
	if v, ok := s.ExtBool[key]; ok {
		value = v
	}
	return
}

func (s *ExtStruct) DeleteBool(key string) (deletedValue *bool) {
	if v, ok := s.ExtBool[key]; ok {
		deletedValue = &v
		delete(s.ExtBool, key)
	}
	return
}

func (s *ExtStruct) CleanBool(keys []string, keepKeys bool) (cleanedKeys []string) {
	cleanedKeys = []string{}
	for k := range s.ExtBool {
		keep := !keepKeys
		for _, key := range keys {
			if k == key {
				keep = keepKeys
				break
			}
		}
		if !keep {
			delete(s.ExtBool, k)
			cleanedKeys = append(cleanedKeys, k)
		}
	}
	return
}

func (s *ExtStruct) SetString(key string, value string) (replacedValue *string) {
	if s.ExtString == nil {
		s.ExtString = map[string]string{}
	}
	if v, ok := s.ExtString[key]; ok {
		replacedValue = &v
	}
	s.ExtString[key] = value
	return
}

func (s *ExtStruct) GetString(key string) (value string) {
	if s.ExtString == nil {
		return ""
	}
	if v, ok := s.ExtString[key]; ok {
		value = v
	}
	return
}

func (s *ExtStruct) DeleteString(key string) (deletedValue *string) {
	if v, ok := s.ExtString[key]; ok {
		deletedValue = &v
		delete(s.ExtString, key)
	}
	return
}

func (s *ExtStruct) CleanString(keys []string, keepKeys bool) (cleanedKeys []string) {
	cleanedKeys = []string{}
	for k := range s.ExtString {
		keep := !keepKeys
		for _, key := range keys {
			if k == key {
				keep = keepKeys
				break
			}
		}
		if !keep {
			delete(s.ExtString, k)
			cleanedKeys = append(cleanedKeys, k)
		}
	}
	return
}

func (s *ExtStruct) SetTime(key string, value *time.Time) (replacedValue *time.Time) {
	if s.ExtTime == nil {
		s.ExtTime = map[string]*time.Time{}
	}
	if v, ok := s.ExtTime[key]; ok {
		replacedValue = v
	}
	s.ExtTime[key] = value
	return
}

func (s *ExtStruct) GetTime(key string) (value *time.Time) {
	if s.ExtTime == nil {
		return nil
	}
	if v, ok := s.ExtTime[key]; ok {
		value = v
	}
	return
}

func (s *ExtStruct) DeleteTime(key string) (deletedValue *time.Time) {
	if v, ok := s.ExtTime[key]; ok {
		deletedValue = v
		delete(s.ExtTime, key)
	}
	return
}

func (s *ExtStruct) CleanTime(keys []string, keepKeys bool) (cleanedKeys []string) {
	cleanedKeys = []string{}
	for k := range s.ExtTime {
		keep := !keepKeys
		for _, key := range keys {
			if k == key {
				keep = keepKeys
				break
			}
		}
		if !keep {
			delete(s.ExtTime, k)
			cleanedKeys = append(cleanedKeys, k)
		}
	}
	return
}

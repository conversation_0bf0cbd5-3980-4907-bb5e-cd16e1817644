package stack

import (
	"bytes"
	"encoding/json"
	"fmt"
	"hash/fnv"
	"io"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"runtime/debug"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

var stackHashPrefix string
var keyCounter int
var keyCountHardLimit = 30000
var storageEnabled atomic.Bool
var storageStartedAt *time.Time

// how many function calls to skip when generating stack hash
// stack hash is generated by skip*2 lines, both function call and file path lines
// for usage of zlog.XXX(), config skip to 6 to remove all unrelated function calls
// IMPORTANT: if module zap changed lib structure, this value may need change, you can change it by SetSkip()
var skip = 6

const hardLimitKey = "0000000000000000"
const hardLimitValue = ""

var currentStacker *Stacker
var storageStackers []*Stacker

type Stacker struct {
	Stacks     map[string][]byte
	stacksLock sync.RWMutex

	Counts     map[string]int64
	countsLock sync.RWMutex
}

func init() {
	var s Stacker
	s.Stacks = make(map[string][]byte)
	s.Stacks[hardLimitKey] = []byte(hardLimitValue)
	s.Counts = make(map[string]int64)
	s.Counts[hardLimitKey] = 0
	currentStacker = &s
}

func SetSkip(s int) {
	skip = s
}

func SetStackKeyPrefix(pre string) {
	stackHashPrefix = pre
}

func SetHardLimit(limit int) {
	keyCountHardLimit = limit
}

func (s *Stacker) StoreStack(stack []byte) string {
	// md5 算法和 maphash 算法的速度差不了太多，但是 md5 算法的 hash 值是固定的，可以用来做 key
	// var h = md5.New()
	// h.Write(stack)
	// var key = stackHashPrefix + hex.EncodeToString(h.Sum(nil))

	// fnv 算法看起来是稳定的，可能比 md5 算法快一点，并且短一点
	// 暂时还不知道防重复性怎么样
	hasher := fnv.New32a()
	hasher.Write(stack)
	var key = fmt.Sprintf("%s%d", stackHashPrefix, hasher.Sum32())

	s.stacksLock.Lock()
	_, stackFound := s.Stacks[key]
	if !stackFound {
		s.Stacks[key] = stack
		keyCounter += 1
	}
	s.stacksLock.Unlock()

	s.countsLock.Lock()
	var count int64
	if _, ok := s.Counts[key]; ok {
		count = s.Counts[key]
		atomic.AddInt64(&count, 1)
	}
	s.Counts[key] = count
	s.countsLock.Unlock()

	return key
}

func (s *Stacker) QueryStack(key string) (int64, []byte) {
	s.stacksLock.RLock()
	var st, ok = s.Stacks[key]
	s.stacksLock.RUnlock()

	if ok {
		s.countsLock.RLock()
		var count = s.Counts[key]
		s.countsLock.RUnlock()
		return count, st
	}

	return int64(len(s.Stacks)), nil
}

func (s *Stacker) ListStacks() map[string]int64 {
	var count = make(map[string]int64)
	s.countsLock.RLock()
	for k, v := range s.Counts {
		count[k] = v
	}
	s.countsLock.RUnlock()
	return count
}

func getStackHash(stack []byte) string {
	if keyCounter >= keyCountHardLimit {
		return hardLimitKey
	}

	// remove first line from stack, which contains goroutine numbers, which is not stable
	// remove lines that skip, *2 for both function calls and file path lines
	// fmt.Printf("stack is: \n%s", string(stack))
	var lines = bytes.Split(stack, []byte("\n"))
	stack = bytes.Join(lines[1+skip*2:], []byte("\n"))

	// remove all the address from the function call of the stack
	// so that the stack hash is stable
	var reg = regexp.MustCompile(`\((0x[^)]+)\)`)
	var reg2 = regexp.MustCompile(`\({0x.+\)`)
	stack = reg.ReplaceAll(stack, []byte("()"))
	stack = reg2.ReplaceAll(stack, []byte("()"))

	// 下面是 copilot 推荐的算法，但是耗时竟然更长，大概从 160us 增加到 240us；可能是因为替换的次数更多
	// 在上面的替换完成后，再替换一次，把 0x 开头的地址替换掉，速度比单纯替换 0x 开头的地址更快
	// 增加这个之后，stack 会更加稳定，即使有 storage 的功能，如果 stack 更稳定，更方便使用
	var re3 = regexp.MustCompile(`0x[0-9a-f]+`)
	stack = re3.ReplaceAll(stack, []byte("0x0"))

	// fmt.Printf("new stack is: \n%s", string(stack))
	return currentStacker.StoreStack(stack)
}

var getStackHashLock sync.Mutex = sync.Mutex{}

func GetStackHash() string {
	getStackHashLock.Lock()
	defer getStackHashLock.Unlock()

	var stack = debug.Stack()
	return getStackHash(stack)
}

func QueryStack(key string) (int64, []byte) {
	key = strings.TrimPrefix(key, "@")
	// if storage is enabled, query both currentStacker and storageStackers
	if storageEnabled.Load() {
		var count, stack = currentStacker.QueryStack(key)
		if stack != nil {
			return count, stack
		}
		for _, s := range storageStackers {
			count, stack = s.QueryStack(key)
			if stack != nil {
				return count, stack
			}
		}
		return 0, nil
	}
	return currentStacker.QueryStack(key)
}

func ListStacksCount() map[string]int64 {
	return currentStacker.ListStacks()
}

var startStorageMutex sync.Mutex

func StartStorage(name string, dataDir string, maxNum int) (er error) {
	// prevent concurrent call
	startStorageMutex.Lock()
	defer startStorageMutex.Unlock()

	// save files under {dataDir}/stacks
	stacksDir := filepath.Join(dataDir, "stacks")
	os.MkdirAll(stacksDir, 0755)

	if !storageEnabled.Load() {
		storageStackers = []*Stacker{}
		// walk folder stacksDir, list files with .stack extension
		// for each file, extract datetime from the filename
		// sort filenames by datetime in ascending order, delete the oldest files if there are more than maxNum
		type FileWithDatetime struct {
			Path     string
			Datetime time.Time
		}
		var files []FileWithDatetime
		// walk folder stacksDir, list files with .stack extension
		filepath.Walk(stacksDir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return fmt.Errorf("walk %s error: %v", path, err)
			}
			if info.IsDir() || filepath.Ext(path) != ".stack" {
				return nil
			}
			var filename = filepath.Base(path)
			prefix := fmt.Sprintf("%s___", name)
			if !strings.HasPrefix(filename, prefix) {
				return nil
			}
			var datetime = filename[len(prefix) : len(filename)-len(".stack")]
			var t, err2 = time.Parse("20060102_150405", datetime)
			if err2 != nil {
				log.Printf("parse datetime %s error: %v\n", datetime, err2)
				return nil
			}
			files = append(files, FileWithDatetime{Path: path, Datetime: t})
			return nil
		})
		// sort files by datetime in ascending order
		sort.Slice(files, func(i, j int) bool {
			return files[i].Datetime.Before(files[j].Datetime)
		})

		// delete the oldest files if there are more than maxNum
		for i := 0; i < len(files)-maxNum; i++ {
			os.Remove(files[i].Path)
		}

		// remove files that are removed
		var keepedFiles []FileWithDatetime
		for _, f := range files {
			if _, err := os.Stat(f.Path); err == nil {
				keepedFiles = append(keepedFiles, f)
			}
		}

		// iterate files, read file content, json unmarshal to a struct of Stacker
		// append it to var storageStackers
		for _, f := range keepedFiles {
			file, err3 := os.OpenFile(f.Path, os.O_RDONLY, 0644)
			if err3 != nil {
				log.Printf("open file failed, path:%s error: %v\n", f.Path, err3)
				return nil
			}
			defer file.Close()
			var s Stacker
			// read all bytes from var file into var data
			data, err4 := io.ReadAll(file)

			if err4 != nil {
				log.Printf("read stack file failed, path: %s, error: %s\n", f.Path, err4)
				return nil
			}
			err5 := json.Unmarshal(data, &s)
			if err5 != nil {
				log.Printf("unmarshal stacker failed, path: %s, error: %s", f.Path, err5)
				return nil
			}
			storageStackers = append(storageStackers, &s)
		}

		// start a goroutine to save var currentStacker every 10 mintues
		// save var currentStacker to a file with .stack extension
		// the file name is the datetime of var storageStartedAt if it's not nil, or use var nowTime
		go periodicalSaveStacker(stacksDir, name)
		storageEnabled.Store(true)
	} else {
		log.Printf("storage already started\n")
	}
	return
}

func periodicalSaveStacker(stacksDir, name string) {
	for {
		saveCurrentStacker(stacksDir, name)
		time.Sleep(time.Minute * 1)
	}
}

func saveCurrentStacker(stacksDir, name string) {
	// save var currentStacker to a file with .stack extension
	if storageStartedAt == nil {
		nowTime := time.Now()
		storageStartedAt = &nowTime
	}
	var filename = fmt.Sprintf("%s___%s.stack", name, storageStartedAt.Format("20060102_150405"))
	var file, err = os.OpenFile(filepath.Join(stacksDir, filename), os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Printf("open file %s error: %v\n", filename, err)
		return
	}
	defer file.Close()

	currentStacker.stacksLock.Lock()
	defer currentStacker.stacksLock.Unlock()

	data, err := json.Marshal(currentStacker)
	if err != nil {
		fmt.Printf("marshal currentStacker error: %v\n", err)
		return
	}
	file.Write(data)
}

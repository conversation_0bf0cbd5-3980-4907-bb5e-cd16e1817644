package stack

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestDefaultStack(t *testing.T) {
	var h = GetStackHash()
	fmt.Printf("stack hash: %v\n", h)
	var count, s = QueryStack(h)
	fmt.Printf("count: %d\tstack:\n%s\n", count, s)

	count, s = QueryStack("")
	fmt.Printf("count: %d\tstack:\n%s\n", count, s)
}

func TestStoreStack(t *testing.T) {
	var stack = `goroutine 359 [running]:
runtime/debug.Stack(0x10, 0x12a67c0, 0x13e1680)
	/usr/local/Cellar/go/1.12.7/libexec/src/runtime/debug/stack.go:24 +0x9d
github.com/wizhodl/wisebook/bookd.wiseBookInterceptor.func3(0x19492a0, 0xc00a1bb2a0)
	/Users/<USER>/projects/wizhodl/wisebook/bookd/service.go:273 +0x46
github.com/wizhodl/go-utils/functionCaller.(*FunctionCaller).Call.func1(0xc00dcddb28)
	/Users/<USER>/projects/go/pkg/mod/github.com/wizhodl/go-utils@v0.0.0-20200219072209-480aba760b07/functionCaller/caller.go:60 +0xf8
panic(0x12274a0, 0x1924ae0)
	/usr/local/Cellar/go/1.12.7/libexec/src/runtime/panic.go:522 +0x1b5
github.com/wizhodl/orm.(*sqlSentence).insert(0xc0103f8e10, 0x1378220, 0xc01198e4e0, 0x0, 0x0, 0x0)
	/Users/<USER>/projects/go/pkg/mod/github.com/wizhodl/orm@v0.0.0-20200305064531-c9cc19b4170e/insert.go:152 +0x689
github.com/wizhodl/orm.(*DB).insert(0xc0000fc0a0, 0x1378220, 0xc01198e4e0, 0x0, 0x0, 0x0, 0xc0137819e0)
	/Users/<USER>/projects/go/pkg/mod/github.com/wizhodl/orm@v0.0.0-20200305064531-c9cc19b4170e/insert.go:177 +0x11e
github.com/wizhodl/orm.(*DB).Insert(...)
	/Users/<USER>/projects/go/pkg/mod/github.com/wizhodl/orm@v0.0.0-20200305064531-c9cc19b4170e/orm.go:72
github.com/wizhodl/wisebook/bookd.(*BookServer).ReportOnlineStatus(0xc0000fe5a0, 0x196e0a0, 0xc0108af320, 0xc0108af290, 0xc0000fe5a0, 0x7f318ed736d0, 0x0)
	/Users/<USER>/projects/wizhodl/wisebook/bookd/rpc.go:938 +0x3a1
github.com/wizhodl/wisebook/api/proto._BookService_ReportOnlineStatus_Handler.func1(0x196e0a0, 0xc0108af320, 0x1370f20, 0xc0108af290, 0x0, 0x0, 0x0, 0x0)
	/Users/<USER>/projects/wizhodl/wisebook/api/proto/bookservice.pb.go:26818 +0x89
reflect.Value.call(0x1285b20, 0xc0034874c0, 0x13, 0x14182fe, 0x4, 0xc0108af380, 0x2, 0x2, 0xc000233200, 0xc0108af290, ...)
	/usr/local/Cellar/go/1.12.7/libexec/src/reflect/value.go:447 +0x461
reflect.Value.Call(0x1285b20, 0xc0034874c0, 0x13, 0xc0108af380, 0x2, 0x2, 0x2, 0x2, 0xc0108af350)
	/usr/local/Cellar/go/1.12.7/libexec/src/reflect/value.go:308 +0xa4
github.com/wizhodl/go-utils/functionCaller.(*FunctionCaller).Call(0xc0108af350, 0xc003487401, 0xc00dcddb28, 0x0, 0x0, 0x0)
	/Users/<USER>/projects/go/pkg/mod/github.com/wizhodl/go-utils@v0.0.0-20200219072209-480aba760b07/functionCaller/caller.go:66 +0x82
github.com/wizhodl/wisebook/bookd.wiseBookInterceptor(0x196e0a0, 0xc0108af260, 0x1370f20, 0xc0108af290, 0xc0034874a0, 0xc0034874c0, 0x0, 0x0, 0x0, 0x0)
	/Users/<USER>/projects/wizhodl/wisebook/bookd/service.go:272 +0xd34
github.com/wizhodl/wisebook/api/proto._BookService_ReportOnlineStatus_Handler(0x1415700, 0xc0000fe5a0, 0x196e0a0, 0xc0108af260, 0xc0025b8de0, 0x14b7b68, 0x196e0a0, 0xc0108af260, 0xc010397848, 0x8)
	/Users/<USER>/projects/wizhodl/wisebook/api/proto/bookservice.pb.go:26820 +0x158
google.golang.org/grpc.(*Server).processUnaryRPC(0xc00a896d80, 0x197ffa0, 0xc001980000, 0xc0108d4100, 0xc002937fb0, 0x2406148, 0x0, 0x0, 0x0)
	/Users/<USER>/projects/go/pkg/mod/google.golang.org/grpc@v1.25.1/server.go:1007 +0x466
google.golang.org/grpc.(*Server).handleStream(0xc00a896d80, 0x197ffa0, 0xc001980000, 0xc0108d4100, 0x0)
	/Users/<USER>/projects/go/pkg/mod/google.golang.org/grpc@v1.25.1/server.go:1287 +0xda6
google.golang.org/grpc.(*Server).serveStreams.func1.1(0xc011f73f00, 0xc00a896d80, 0x197ffa0, 0xc001980000, 0xc0108d4100)
	/Users/<USER>/projects/go/pkg/mod/google.golang.org/grpc@v1.25.1/server.go:722 +0x9f
created by google.golang.org/grpc.(*Server).serveStreams.func1
	/Users/<USER>/projects/go/pkg/mod/google.golang.org/grpc@v1.25.1/server.go:720 +0xa1`
	var x = make([]string, 1)
	var start = time.Now()
	for range x {
		var s = getStackHash([]byte(stack))
		fmt.Printf("hash: %v\n", s)
	}
	fmt.Printf("cost: %v", time.Since(start))
}

func TestStoreStackGo117(t *testing.T) {
	var stack = `goroutine 13 [running]:
	runtime/debug.Stack()
		/usr/local/go/src/runtime/debug/stack.go:24 +0x7c
	github.com/wizhodl/go-utils/stack.GetStackHash()
		/Users/<USER>/Documents/workspace/go-utils/stack/stack.go:113 +0x24
	quanter/common/zlog.ColoredStackLevelEncoder(0x0, {0x105b8e028, 0x140004b0000})
		/Users/<USER>/Documents/workspace/quanter/common/zlog/logger.go:51 +0xd54
	go.uber.org/zap/zapcore.consoleEncoder.EncodeEntry({0x140004046c0}, {0x0, {0xc071fd6db406b5d8, 0x71243bea, 0x10622b880}, {0x0, 0x0}, {0x140003f2090, 0x2e}, {0x1, ...}, ...}, ...)
		/Users/<USER>/go/pkg/mod/go.uber.org/zap@v1.16.0/zapcore/console_encoder.go:84 +0x194
	go.uber.org/zap/zapcore.(*ioCore).Write(0x140004046f0, {0x0, {0xc071fd6db406b5d8, 0x71243bea, 0x10622b880}, {0x0, 0x0}, {0x140003f2090, 0x2e}, {0x1, ...}, ...}, ...)
		/Users/<USER>/go/pkg/mod/go.uber.org/zap@v1.16.0/zapcore/core.go:86 +0x84
	go.uber.org/zap/zapcore.(*CheckedEntry).Write(0x140012b2000, {0x0, 0x0, 0x0})
		/Users/<USER>/go/pkg/mod/go.uber.org/zap@v1.16.0/zapcore/entry.go:220 +0x2f4
	go.uber.org/zap.(*SugaredLogger).log(0x140000ba930, 0x0, {0x105887d3b, 0x7}, {0x140005cdb88, 0x2, 0x2}, {0x0, 0x0, 0x0})
		/Users/<USER>/go/pkg/mod/go.uber.org/zap@v1.16.0/sugar.go:234 +0x248
	go.uber.org/zap.(*SugaredLogger).Infof(0x140000ba930, {0x105887d3b, 0x7}, {0x140005cdb88, 0x2, 0x2})
		/Users/<USER>/go/pkg/mod/go.uber.org/zap@v1.16.0/sugar.go:138 +0x5c
	quanter/common/zlog.Infof({0x105887d3b, 0x7}, {0x140005cdb88, 0x2, 0x2})
		/Users/<USER>/Documents/workspace/quanter/common/zlog/log.go:12 +0x58
	quanter/command.(*BaseResponder).SendMsgf(0x140005a3d40, {0x140003f2060, 0x24}, {0x0, 0x0, 0x0})
		/Users/<USER>/Documents/workspace/quanter/command/command.go:522 +0x168
	quanter/arbitrage.(*ArbitragerController).AskForLaunch(0x140005a3d40)
		/Users/<USER>/Documents/workspace/quanter/arbitrage/arbitrager.go:447 +0x100
	quanter/arbitrage.(*ArbitragerController).MessengerConnectedCallback(0x140005a3d40)
		/Users/<USER>/Documents/workspace/quanter/arbitrage/arbitrager.go:477 +0x78
	quanter/messenger.(*SlackMessenger).RTMConnect(0x1400015e2a0)
		/Users/<USER>/Documents/workspace/quanter/messenger/slackrobot.go:222 +0x684
	created by quanter/arbitrage.NewArbitragerController
		/Users/<USER>/Documents/workspace/quanter/arbitrage/arbitrager.go:174 +0x1598`
	var x = make([]string, 1)
	var start = time.Now()
	for range x {
		var s = getStackHash([]byte(stack))
		fmt.Printf("hash: %v\n", s)
	}
	fmt.Printf("cost: %v", time.Since(start))
}

func TestStoreStackCounter(t *testing.T) {
	var stack1 = "a\na"
	var stack2 = "a\nb"
	var stack3 = "a\nc"
	assert := assert.New(t)

	// assert keyCounter equals 1
	getStackHash([]byte(stack1))
	assert.Equal(1, keyCounter)
	getStackHash([]byte(stack2))
	assert.Equal(2, keyCounter)
	getStackHash([]byte(stack3))
	assert.Equal(3, keyCounter)
	getStackHash([]byte(stack2))
	assert.Equal(3, keyCounter)
}

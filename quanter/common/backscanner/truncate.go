package backscanner

import (
	"bufio"
	"bytes"
	"encoding/json"
	"io"
	"os"
	"time"

	"github.com/wizhodl/quanter/common/zlog"
)

func TruncateJsonFileByCreateTime(path string, retentionPeriod time.Duration) error {
	cutoffTime := time.Now().Add(-retentionPeriod)

	// Open original file for reading
	file, err := os.Open(path)
	if err != nil {
		return err
	}
	defer file.Close()

	// Create a temporary file
	tempFile, err := os.CreateTemp(os.TempDir(), "truncate-*")
	if err != nil {
		return err
	}
	tempPath := tempFile.Name()
	defer os.Remove(tempPath) // Clean up temp file in case of failure

	reader := bufio.NewReader(file)
	var offset int64 = 0
	foundValid := false

	// Skip entries until we find first valid entry
	for {
		line, err := reader.ReadBytes('\n')
		if err != nil && err != io.EOF {
			return err
		}

		if len(line) == 0 {
			break
		}

		var entry struct {
			CreateTime time.Time `json:"createTime"`
		}

		if err := json.Unmarshal(bytes.TrimSpace(line), &entry); err != nil {
			offset += int64(len(line))
			continue
		}

		if entry.CreateTime.After(cutoffTime) {
			// Write this line and copy the rest
			if _, err := tempFile.Write(line); err != nil {
				return err
			}
			stat, err := file.Stat()
			if err != nil {
				return err
			}
			zlog.Debugf("truncating file, offset: %d, remainingBytes: %d", offset, stat.Size()-offset)
			foundValid = true
			break
		}

		offset += int64(len(line))
	}

	if foundValid {
		// Copy the rest of the file
		if _, err := io.Copy(tempFile, reader); err != nil {
			return err
		}
	}

	// Close both files
	tempFile.Close()
	file.Close()

	// Replace original with temp file
	return os.Rename(tempPath, path)
}

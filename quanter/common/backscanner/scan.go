package backscanner

import (
	"fmt"
	"os"
)

func BackScan(filePath string, lineCallback func(line []byte) (continueLoop bool)) error {
	f, err := os.OpenFile(filePath, os.O_RDONLY, os.ModePerm)
	if err != nil {
		return fmt.Errorf("open file error: %s", err)
	}
	defer f.Close()

	fi, err := os.Stat(filePath)
	if err != nil {
		return fmt.Errorf("get file stat err: %s", err)
	}
	size := fi.Size()

	sc := New(f, int(size))
	for {
		line, _, err := sc.LineBytes() // GET the line string
		if err != nil {
			break
		}
		continueLoop := lineCallback(line)
		if !continueLoop {
			break
		}
	}
	return nil
}

package rate

import (
	"sync"
	"time"
)

type DurationLimiter struct {
	events   []time.Time
	Limit    int
	Duration time.Duration
	mu       sync.Mutex
}

// 限制在 duration 内最多 limit 次事件，如果不允许(allow)，返回需要等待的时间
// 如果 resetOnAllow 为 true，则每次允许时重置计数器
func NewDurationLimiter(limit int, duration time.Duration) *DurationLimiter {
	if limit <= 0 || duration <= 0 {
		panic("limit and duration must be greater than 0")
	}
	return &DurationLimiter{
		events:   make([]time.Time, 0),
		Limit:    limit,
		Duration: duration,
	}
}

// 记录当前事件，并返回是否允许当前操作
func (t *DurationLimiter) Allow() (waitTime time.Duration, ok bool) {
	t.mu.Lock()
	defer t.mu.Unlock()

	now := time.Now()
	t.events = append(t.events, now)
	if len(t.events) > t.Limit {
		t.events = t.events[len(t.events)-t.Limit:]
	}
	if len(t.events) == t.Limit {
		oldest := t.events[0]
		if now.Sub(oldest) < t.Duration {
			waitTime = t.Duration - now.Sub(oldest)
			return waitTime, false
		}
	}
	return 0, true
}

// 获取当前计数
func (t *DurationLimiter) GetCount() int {
	t.mu.Lock()
	defer t.mu.Unlock()
	return len(t.events)
}

// 获取 events
func (t *DurationLimiter) GetEvents() []time.Time {
	return t.events
}

func (t *DurationLimiter) Reset() {
	t.mu.Lock()
	defer t.mu.Unlock()
	t.events = make([]time.Time, 0)
}

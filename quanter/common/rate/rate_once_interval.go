package rate

import (
	"sync"
	"time"
)

type OnceIntervalLimiter struct {
	events    []time.Time
	Duration  time.Duration
	HardLimit int
	mu        sync.Mutex
	lastAllow time.Time
}

// 每隔 duration 允许(allow)一次操作，hardLimit 是允许的最大事件数量
// 如果 hardLimit 为 0，则 hardLimit 为 1000
// 不允许时，返回从上次允许到现在的事件个数
// 主要用在对 slack sendMsg 做限速处理
func NewOnceIntervalLimiter(interval time.Duration, hardLimit int) *OnceIntervalLimiter {
	if interval <= 0 {
		panic("duration must be greater than 0")
	}
	// 用于保证清理 events slice
	if hardLimit <= 0 {
		hardLimit = 1000
	}
	return &OnceIntervalLimiter{
		events:    make([]time.Time, 0),
		Duration:  interval,
		HardLimit: hardLimit,
	}
}

// duration 内发生 n 次事件，只要上次允许的事件到当前时间不超过 duration，都可以继续添加 event，但是 notAllow
// 因此有 n 个 notAllow，然后再一次 Allow
func (t *OnceIntervalLimiter) Allow() (count int, allow bool) {
	t.mu.Lock()
	defer func() {
		t.mu.Unlock()
		if allow {
			t.lastAllow = time.Now()
		}
	}()

	now := time.Now()
	t.events = append(t.events, now)
	count = 0
	// 初始化的情况
	if t.lastAllow.IsZero() {
		allow = true
		count = 1
		return
	}
	length := len(t.events)
	// 如果事件数量超过 hard limit，限制事件数量
	if length > t.HardLimit {
		t.events = t.events[length-t.HardLimit:]
	}
	// 统计从上次 allow 到现在的事件数量
	for _, event := range t.events {
		if event.After(t.lastAllow) {
			count += 1
		}
	}
	// 只要上次 allow 的事件离现在还不到 duration，都可以继续添加 event，但是 allow 为 false
	if now.Sub(t.lastAllow) < t.Duration {
		allow = false
		return
	}
	return count, true
}

// 获取当前计数
func (t *OnceIntervalLimiter) GetCount() int {
	t.mu.Lock()
	defer t.mu.Unlock()
	return len(t.events)
}

// 获取 events
func (t *OnceIntervalLimiter) GetEvents() []time.Time {
	return t.events
}

func (t *OnceIntervalLimiter) Reset() {
	t.mu.Lock()
	defer t.mu.Unlock()
	t.events = make([]time.Time, 0)
}

package option

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

const (
	// Float64Precision 是 float64 的精度，保留 12 位小数，支持的最大小数位数
	// 因为 float64 的精度是 15-17 位，保留 12 位小数可以避免打印太多 0
	Float64Precision = 12
)

const (
	Int     = reflect.Int
	Float64 = reflect.Float64
	String  = reflect.String
	Bool    = reflect.Bool
)

func SupportedTypes() []string {
	return []string{Int.String(), Float64.String(), String.String(), Bool.String()}
}

func IsValidType(kind reflect.Kind) bool {
	switch kind {
	case Int, Float64, String, Bool:
		return true
	}
	return false
}

type TypedOption struct {
	Name      string
	Type      reflect.Kind
	Value     any
	Default   any
	Unit      string
	IsPercent bool
}

func (this *TypedOption) Get() any {
	return this.Value
}

func (this *TypedOption) FormatValue() string {
	valueStr := ""
	if this.Type == Float64 {
		if this.IsPercent {
			valueStr = fmt.Sprintf("%s%%", strconv.FormatFloat(this.Value.(float64)*100, 'g', Float64Precision, 64))
		} else {
			valueStr = strconv.FormatFloat(this.Value.(float64), 'g', Float64Precision, 64)
		}
	} else {
		valueStr = fmt.Sprintf("%v", this.Value)
	}
	if this.Unit != "" {
		valueStr = fmt.Sprintf("%s %s", valueStr, this.Unit)
	}
	return valueStr
}

type TypedOptions []*TypedOption

func NewTypedOption(name string, kind reflect.Kind, defaultValue any, unit string, isPercent bool) *TypedOption {
	option := &TypedOption{
		Name:      name,
		Type:      kind,
		Unit:      unit,
		Default:   defaultValue,
		IsPercent: isPercent,
	}
	// 这里必须显式设置 Value == Default，如果不直接设置 Default，生成的 option 就会使用 Value 的默认值，比如： 0， false
	// 在已经设置了 Default 的情况下，后续会将此默认值作为有效的值，这样 Default 的设置就无效了
	if option.Default == nil {
		option.Value = nil
	} else {
		option.Value = option.Default
	}
	if option.IsPercent && option.Type != Float64 {
		panic(fmt.Sprintf("option %s is percent but type is not float64", option.Name))
	}
	return option
}

type Options struct {
	typedOpts []*TypedOption
	_raw      string
}

func (this *Options) GetOption(name string) *TypedOption {
	if this.typedOpts == nil {
		panic("options not loaded, typedOpts is nil")
	}
	for _, option := range this.typedOpts {
		if strings.EqualFold(option.Name, name) {
			return option
		}
	}
	return nil
}

func (this *Options) Get(name string) any {
	if this.typedOpts == nil {
		panic("options not loaded, typedOpts is nil")
	}
	for _, option := range this.typedOpts {
		if strings.EqualFold(option.Name, name) {
			if option.Value != nil {
				return option.Value
			} else {
				switch option.Type {
				case Int:
					return option.Default.(int)
				case Float64:
					return option.Default.(float64)
				case String:
					return option.Default.(string)
				case Bool:
					return option.Default.(bool)
				}
			}
		}
	}
	return nil
}

func (this *Options) Default(name string) any {
	if this.typedOpts == nil {
		panic("options not inited or loaded, typedOpts is nil")
	}
	for _, option := range this.typedOpts {
		if option.Name == name {
			return option.Default
		}
	}
	return nil
}

func (this *Options) Set(name string, value any) {
	if this.typedOpts == nil {
		panic("options not inited or loaded, typedOpts is nil")
	}
	for _, option := range this.typedOpts {
		if option.Name == name {
			option.Value = value
		}
	}
	this._raw = this.String()
}

func NewOptions(raw string, fromOptions ...*TypedOption) (opts *Options, err error) {
	var options []*TypedOption
	copier.Copy(&options, fromOptions)
	for _, option := range options {
		if !IsValidType(option.Type) {
			return nil, fmt.Errorf("unsupported option type: %s", option.Type)
		}
	}
	opts = &Options{typedOpts: options, _raw: raw}
	for _, option := range opts.typedOpts {
		if option.Default != nil && option.Value == nil {
			// get reflect type of option.Default and check type
			typ := reflect.TypeOf(option.Default)
			if typ.Kind() != option.Type {
				return nil, fmt.Errorf("default value type mismatch for option %s, expect %s, got %s", option.Name, option.Type, typ.Kind())
			}
			option.Value = option.Default
		}
	}
	if raw != "" {
		_, err = opts.UpdateFromString(raw)
		if err != nil {
			return nil, fmt.Errorf("parse options failed: %s", err)
		}
	}
	return opts, nil
}

// Load options from a list of TypedOption, can be called multiple times but only the first call will take effect
func (this *Options) Load(fromOptions ...*TypedOption) error {
	var options []*TypedOption
	copier.Copy(&options, fromOptions)
	if this._raw == "" {
		return fmt.Errorf("options is empty")
	}
	for _, option := range options {
		if !IsValidType(option.Type) {
			return fmt.Errorf("unsupported option type: %s", option.Type)
		}
	}
	this.typedOpts = append(this.typedOpts, options...)
	this.UpdateFromString(this._raw)
	return nil
}

func (this *Options) IsZero() bool {
	return len(this.typedOpts) == 0
}

func (this *Options) Copy() *Options {
	opts := &Options{}
	for _, option := range this.typedOpts {
		opts.typedOpts = append(opts.typedOpts, &TypedOption{
			Name:      option.Name,
			Type:      option.Type,
			Value:     option.Value,
			Default:   option.Default,
			Unit:      option.Unit,
			IsPercent: option.IsPercent,
		})
	}
	opts._raw = this._raw
	return opts
}

// 更新选项，允许部分更新，未被更新的选项会保留之前的值
func (this *Options) UpdateFromString(csvStr string) (updatedNames []string, err error) {
	if this.typedOpts == nil {
		return nil, fmt.Errorf("options is nil")
	}
	parts := strings.Split(csvStr, ",")
	for _, part := range parts {
		parts := strings.Split(part, "=")
		if len(parts) != 2 {
			continue
		}
		name := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])
		for _, option := range this.typedOpts {
			if strings.EqualFold(option.Name, name) {
				if !IsValidType(option.Type) {
					return nil, fmt.Errorf("unsupported option type: %s", option.Type)
				}
				if option.Type == Int {
					if v, err := cast.ToIntE(value); err == nil {
						option.Value = v
					} else {
						option.Value = 0
					}
				} else if option.Type == Float64 {
					if v, _, err := utils.ParseFloatOrPercentage(value, false, true); err == nil {
						option.Value = v
					} else {
						option.Value = 0.0
					}
				} else if option.Type == String {
					option.Value = value
				} else if option.Type == Bool {
					if v, err := cast.ToBoolE(value); err == nil {
						option.Value = v
					} else {
						option.Value = false
					}
				}
				zlog.Debugf("set option %s to %v", option.Name, option.Value)
				updatedNames = append(updatedNames, option.Name)
			}
		}
	}
	// 更新完选项后，重新组装 raw 字符串
	updatedItems := []string{}
	for _, option := range this.typedOpts {
		if option.Value == nil {
			updatedItems = append(updatedItems, fmt.Sprintf("%s=%v", option.Name, option.Default))
		} else {
			updatedItems = append(updatedItems, fmt.Sprintf("%s=%v", option.Name, option.Value))
		}
	}
	this._raw = strings.Join(updatedItems, ",")
	return
}

func (this *Options) GetDefinitions() []*TypedOption {
	return this.typedOpts
}

func (this *Options) String() string {
	if this.typedOpts == nil {
		return ""
	}
	parts := []string{}
	for _, option := range this.typedOpts {
		valueStr := fmt.Sprintf("%v", option.Value)
		if option.Value == nil {
			valueStr = ""
		}
		parts = append(parts, fmt.Sprintf("%s=%s", option.Name, valueStr))
	}
	return strings.Join(parts, ",")
}

func (this *Options) Rows() [][]string {
	rows := [][]string{}
	if this.typedOpts == nil {
		return rows
	}
	for _, option := range this.typedOpts {
		rows = append(rows, []string{option.Name, option.FormatValue()})
	}
	return rows
}

func (this *Options) ToTable() string {
	t := exchange.NewTable()
	t.SetHeader([]string{"Name", "Value"})
	for _, row := range this.Rows() {
		t.AddRow(row)
	}
	return t.Render()
}

// 获取两个选项的差异，只返回包含在 includeNames 中的选项
// 如果 includeNames 为 nil，则返回所有选项的差异
func (this *Options) DiffRows(other *Options, includeNames []string) [][]string {
	rows := [][]string{}
	for _, option := range this.typedOpts {
		include := false
		if includeNames != nil {
			if utils.SliceContains(includeNames, option.Name) {
				include = true
			}
		} else {
			include = true
		}
		if include {
			rows = append(rows, []string{option.Name, option.FormatValue(), "->", other.GetOption(option.Name).FormatValue()})
		}
	}
	return rows
}

// 获取两个选项的差异，只返回包含在 includeNames 中的选项
// 如果 includeNames 为 nil，则返回所有选项的差异
func (this *Options) DiffToTable(other *Options, includeNames []string) string {
	t := exchange.NewTable()
	t.SetHeader([]string{"Name", "Value", "    ", "New"})
	for _, row := range this.DiffRows(other, includeNames) {
		t.AddRow(row)
	}
	return t.Render()
}

// MarshalJSON

func (this *Options) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf("\"%s\"", this.String())), nil
}

func (this *Options) UnmarshalJSON(data []byte) error {
	this._raw = strings.TrimPrefix(string(data), "\"")
	this._raw = strings.TrimSuffix(this._raw, "\"")
	return nil
}

func (this *Options) MarshalText() ([]byte, error) {
	return this.MarshalJSON()
}

func (this *Options) UnmarshalText(data []byte) error {
	return this.UnmarshalJSON(data)
}

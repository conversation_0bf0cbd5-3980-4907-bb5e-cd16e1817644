package option

import (
	"fmt"
	"strings"
	"sync"

	"github.com/wizhodl/quanter/utils"
)

type NamedList struct {
	List  []string
	mutex sync.Mutex
}

func (this *NamedList) Add(name string, values []string) {
	this.mutex.Lock()
	defer this.mutex.Unlock()

	// check existing name
	existingIndex := -1
	existingValues := []string{}
	for i, g := range this.List {
		if strings.HasPrefix(g, name+"|") {
			existingIndex = i
			existingValues = strings.Split(g[len(name)+1:], ",")
		}
	}
	if existingIndex != -1 {
		// prevent duplicate values
		for _, v := range values {
			if !utils.SliceContains(existingValues, v) {
				existingValues = append(existingValues, v)
			}
		}
		this.List[existingIndex] = name + "|" + strings.Join(existingValues, ",")
	} else {
		this.List = append(this.List, name+"|"+strings.Join(values, ","))
	}
}

func (this *NamedList) Set(name string, values []string) {
	this.mutex.Lock()
	defer this.mutex.Unlock()

	// check existing name
	existingIndex := -1
	for i, g := range this.List {
		if strings.HasPrefix(g, name+"|") {
			existingIndex = i
			break
		}
	}
	if existingIndex != -1 {
		this.List[existingIndex] = name + "|" + strings.Join(values, ",")
	} else {
		this.List = append(this.List, name+"|"+strings.Join(values, ","))
	}
}

// 不要在 Add/RemoveFrom 中使用 Delete，会死锁
func (this *NamedList) Delete(name string) {
	this.mutex.Lock()
	defer this.mutex.Unlock()

	for i, g := range this.List {
		if strings.HasPrefix(g, name+"|") {
			this.List = append((this.List)[:i], (this.List)[i+1:]...)
			return
		}
	}
}

func (this *NamedList) RemoveFrom(name string, values []string) {
	this.mutex.Lock()
	defer this.mutex.Unlock()

	existingValues := this.Get(name)
	newValues := []string{}
	for _, v := range existingValues {
		if !utils.SliceContains(values, v) {
			newValues = append(newValues, v)
		}
	}
	existingIndex := -1
	for i, g := range this.List {
		if strings.HasPrefix(g, name+"|") {
			existingIndex = i
			this.List[existingIndex] = name + "|" + strings.Join(newValues, ",")
			break
		}
	}
}

func (this *NamedList) Get(name string) []string {
	for _, g := range this.List {
		if strings.HasPrefix(g, name+"|") {
			return strings.Split(g[len(name)+1:], ",")
		}
	}
	return nil
}

func (this *NamedList) ToTable(nameTitle, valueTitle string) string {
	table := utils.NewTable()
	table.SetHeader([]string{nameTitle, valueTitle})
	for _, g := range this.List {
		name := strings.Split(g, "|")[0]
		table.AddRow([]string{name, strings.Join(this.Get(name), ", ")})
	}
	if len(table.Rows) > 1 {
		return table.Render()
	}
	return fmt.Sprintf("[no %s list]", nameTitle)
}

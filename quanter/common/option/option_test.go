package option

import (
	"encoding/json"
	"testing"

	"github.com/pelletier/go-toml/v2"
)

func TestOption(t *testing.T) {
	opts, err := NewOptions("a=1,b=2,c=3",
		&TypedOption{Name: "a", Type: String, Default: "0"},
		&TypedOption{Name: "b", Type: String, Default: "0"},
		&TypedOption{Name: "c", Type: String, Default: "0"},
	)
	if err != nil {
		t.Fatalf("new options error: %s", err)
	}
	t.Logf("options: %s", opts.String())
}

func TestUnmarshalJSON(t *testing.T) {
	s := &struct {
		A           string            `json:"A"`
		Options     *Options          `json:"Options"`
		ConfigSaves *NamedOptionsList `json:"ConfigSaves"`
	}{}
	err := json.Unmarshal([]byte(`{
		"A": "1",
		"Options": "a=1,b=2,c=3",
		"ConfigSaves": ["1day|a=1,b=2,c=3", "1min|a=1,b=2,c=3"]
	}`), s)
	if err != nil {
		t.Fatalf("unmarshal json error: %s", err)
	}
	t.Logf("struct: %+v", s)
	s.Options.Load(
		&TypedOption{Name: "a", Type: String, Default: "0"},
		&TypedOption{Name: "b", Type: String, Default: "0"},
		&TypedOption{Name: "c", Type: String, Default: "0"},
	)
	s.ConfigSaves.Load(
		&TypedOption{Name: "a", Type: String, Default: "0"},
		&TypedOption{Name: "b", Type: String, Default: "0"},
		&TypedOption{Name: "c", Type: String, Default: "0"},
	)
	t.Logf("struct: %+v", s)
}

func TestMarshalJSON(t *testing.T) {
	opts, err := NewOptions("a=1,b=2,c=3",
		&TypedOption{Name: "a", Type: String, Default: "0"},
		&TypedOption{Name: "b", Type: String, Default: "0"},
		&TypedOption{Name: "d", Type: String, Default: "0"},
	)
	if err != nil {
		t.Fatalf("new options error: %s", err)
	}
	configSaves, err := NewNamedOptionsList(`["1day|a=1,b=2,c=3", "1min|a=1,b=2,c=3"]`,
		&TypedOption{Name: "a", Type: String, Default: "0"},
		&TypedOption{Name: "b", Type: String, Default: "0"},
		&TypedOption{Name: "c", Type: String, Default: "0"},
	)
	if err != nil {
		t.Fatalf("new named options list error: %s", err)
	}
	s := &struct {
		A           string            `json:"A"`
		Options     *Options          `json:"Options"`
		ConfigSaves *NamedOptionsList `json:"ConfigSaves"`
	}{
		A:           "1",
		Options:     opts,
		ConfigSaves: configSaves,
	}
	bs, err := json.Marshal(s)
	if err != nil {
		t.Fatalf("marshal json error: %s", err)
	}
	bsStr := string(bs)
	t.Logf("json: %s", bsStr)
}

func TestUnmarshalToml(t *testing.T) {
	s := &struct {
		Options *Options `toml:"Options"`
	}{}
	err := toml.Unmarshal([]byte(`Options = "a=1,b=2,c=3"`), s)
	if err != nil {
		t.Fatalf("unmarshal toml error: %s", err)
	}
	s.Options.Load(
		&TypedOption{Name: "a", Type: String, Default: "0"},
		&TypedOption{Name: "b", Type: String, Default: "0"},
		&TypedOption{Name: "c", Type: String, Default: "0"},
	)
	t.Logf("struct: %+v", s)

	input := `ConfigSaves = "1day|a=1,b=2,c=3 1min|a=1,b=2,c=3"`

	// Original test
	s2 := &struct {
		ConfigSaves *NamedOptionsList `toml:"ConfigSaves"`
	}{}
	err = toml.Unmarshal([]byte(input), s2)
	if err != nil {
		t.Fatalf("unmarshal toml error: %s", err)
	}
	s2.ConfigSaves.Load(
		&TypedOption{Name: "a", Type: String, Default: "0"},
		&TypedOption{Name: "b", Type: String, Default: "0"},
		&TypedOption{Name: "c", Type: String, Default: "0"},
	)
	t.Logf("struct: %+v", s2)
}

func TestUnmarshalToml2(t *testing.T) {
	input := `ConfigSaves = ["1day|a=1,b=2,c=3", "1min|a=1,b=2,c=3"]`

	// Original test
	s2 := &struct {
		ConfigSaves *NamedOptionsList `toml:"ConfigSaves"`
	}{}
	err := toml.Unmarshal([]byte(input), s2)
	if err != nil {
		t.Fatalf("unmarshal toml error: %s", err)
	}
	s2.ConfigSaves.Load(
		&TypedOption{Name: "a", Type: String, Default: "0"},
		&TypedOption{Name: "b", Type: String, Default: "0"},
		&TypedOption{Name: "c", Type: String, Default: "0"},
	)
	t.Logf("struct: %+v", s2)
}

func TestMarshalToml(t *testing.T) {
	opts, err := NewOptions("a=1,b=2,c=3",
		&TypedOption{Name: "a", Type: String, Default: "0"},
		&TypedOption{Name: "b", Type: String, Default: "0"},
		&TypedOption{Name: "c", Type: String, Default: "0"},
	)
	if err != nil {
		t.Fatalf("new options error: %s", err)
	}
	t.Logf("opts: %s", opts.String())

	configSaves, err := NewNamedOptionsList(`1day|a=1,b=2,c=3 1min|a=1,b=2,c=3`,
		&TypedOption{Name: "a", Type: String, Default: "0"},
		&TypedOption{Name: "b", Type: String, Default: "0"},
		&TypedOption{Name: "c", Type: String, Default: "0"},
	)
	t.Logf("toml: %s", configSaves.RenderTable())

	s := &struct {
		Options     *Options          `toml:"Options"`
		ConfigSaves *NamedOptionsList `toml:"ConfigSaves"`
	}{
		Options:     opts,
		ConfigSaves: configSaves,
	}
	if err != nil {
		t.Fatalf("new named options list error: %s", err)
	}
	bs, err := toml.Marshal(s)
	if err != nil {
		t.Fatalf("marshal toml error: %s", err)
	}
	t.Logf("toml: %s", string(bs))
}

package option

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"

	"github.com/wizhodl/quanter/exchange"
)

type NamedOptions struct {
	opts *Options
	Name string
}

type NamedOptionsList struct {
	namedOpts []*NamedOptions
	mutex     sync.Mutex
}

// operations on NamedOptionsList

func NewNamedOptionsList(str string, options ...*TypedOption) (*NamedOptionsList, error) {
	l := &NamedOptionsList{namedOpts: []*NamedOptions{}}
	str = strings.TrimSpace(str)
	if strings.HasPrefix(str, "[") && strings.HasSuffix(str, "]") {
		if err := l.UnmarshalJSON([]byte(str)); err != nil {
			return nil, err
		}
	} else {
		if err := l.UnmarshalText([]byte(str)); err != nil {
			return nil, err
		}
	}
	err := l.Load(options...)
	if err != nil {
		return nil, err
	}
	return l, nil
}

func (this *NamedOptionsList) Get(name string) *Options {
	if this.namedOpts == nil {
		panic("namedOpts is not loaded")
	}
	for _, opt := range this.namedOpts {
		if opt.Name == name {
			return opt.opts
		}
	}
	return nil
}

func (this *NamedOptionsList) Set(name string, opts *Options) {
	if this.namedOpts == nil {
		panic("namedOpts is not loaded")
	}
	this.mutex.Lock()
	defer this.mutex.Unlock()
	for _, opt := range this.namedOpts {
		if opt.Name == name {
			opt.opts = opts
			return
		}
	}
	this.namedOpts = append(this.namedOpts, &NamedOptions{Name: name, opts: opts})
}

func (this *NamedOptionsList) Delete(name string) {
	if this.namedOpts == nil {
		panic("namedOpts is not loaded")
	}
	this.mutex.Lock()
	defer this.mutex.Unlock()
	for i, opt := range this.namedOpts {
		if opt.Name == name {
			this.namedOpts = append(this.namedOpts[:i], this.namedOpts[i+1:]...)
			return
		}
	}
}

func (this *NamedOptionsList) Len() int {
	this.mutex.Lock()
	defer this.mutex.Unlock()
	return len(this.namedOpts)
}

func (this *NamedOptionsList) Rows() [][]string {
	rows := [][]string{}
	for _, opt := range this.namedOpts {
		valueStr := opt.opts.String()
		rows = append(rows, []string{opt.Name, valueStr})
	}
	return rows
}

func (this *NamedOptionsList) Load(options ...*TypedOption) error {
	for _, option := range this.namedOpts {
		if err := option.opts.Load(options...); err != nil {
			return err
		}
	}
	return nil
}

// MarshalJSON for NamedOptionsList

func (this *NamedOptionsList) MarshalJSON() ([]byte, error) {
	result := []string{}
	for _, opt := range this.namedOpts {
		result = append(result, fmt.Sprintf("%s|%s", opt.Name, opt.opts.String()))
	}
	return json.Marshal(result)
}

func (this *NamedOptionsList) UnmarshalJSON(data []byte) error {
	if len(data) == 0 {
		return nil
	}

	lines := []string{}
	if err := json.Unmarshal(data, &lines); err != nil {
		return err
	}
	for _, line := range lines {
		parts := strings.Split(line, "|")
		if len(parts) != 2 {
			continue
		}
		name := strings.TrimSpace(parts[0])
		opts := &Options{}
		if err := opts.UnmarshalJSON([]byte(fmt.Sprintf("\"%s\"", strings.TrimSpace(parts[1])))); err != nil {
			return err
		}
		this.namedOpts = append(this.namedOpts, &NamedOptions{Name: name, opts: opts})
	}
	return nil
}

// marshal/unmarshal toml
func (this *NamedOptionsList) MarshalText() ([]byte, error) {
	result := []string{}
	for _, opt := range this.namedOpts {
		result = append(result, fmt.Sprintf("%s|%s", opt.Name, opt.opts.String()))
	}
	return []byte(strings.Join(result, " ")), nil
}

func (this *NamedOptionsList) UnmarshalText(data []byte) error {
	if len(data) == 0 {
		return nil
	}

	line := string(data)
	lines := strings.Split(line, " ")
	for _, line := range lines {
		parts := strings.Split(line, "|")
		if len(parts) != 2 {
			continue
		}
		name := strings.TrimSpace(parts[0])
		opts := &Options{}
		if err := opts.UnmarshalJSON([]byte(fmt.Sprintf("\"%s\"", strings.TrimSpace(parts[1])))); err != nil {
			return err
		}
		this.namedOpts = append(this.namedOpts, &NamedOptions{Name: name, opts: opts})
	}
	return nil
}

// maybe need to call encoder.EnableUnmarshalerInterface() to enable this function
// func (this *NamedOptionsList) UnmarshalTOML(node *unstable.Node) error {
// 	return this.UnmarshalJSON(node.Data)
// }

func (this *NamedOptionsList) RenderTable() string {
	t := exchange.NewTable()
	t.SetHeader([]string{"Name", "Options"})
	for _, row := range this.Rows() {
		t.AddRow(row)
	}
	return t.Render()
}

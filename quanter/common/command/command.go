package command

import (
	"crypto/md5"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/quanter/common/rate"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"
)

const SLACK_MESSAGE_LIMIT = 3500

// 所有自定义命令都继承这个类
type Commander interface {
	// 以下方法用于操作 Command 类中的属性，不需要 override
	GetName() string
	Is(name string) (result bool)
	GetNameAndAlias() []string
	GetSubcommand() string
	GetArgs() []string
	IsMore() bool // 是否需要打印更多信息
	SetID(id int)
	GetID() int
	SetArgs(args []string, validate bool) (bool, error)
	IsID(id int) bool
	IsRequiresConfirm() bool
	GetInstructions() string       // 返回所有指令的列表，用以打印帮助信息
	GetCurrentInstruction() string // 返回当前指令，用以打印帮助信息
	Validate() (bool, error)
	SetProcessor(processor *CommandProcessor)
	PromptRequiresConfirm()
	SetPrepared(prepared bool)
	IsPrepared() bool
	SetConfirmed(confirmed bool)
	IsConfirmed() bool
	IsRequiresFile() bool // 是否需要上传文件
	IsSensitive() bool

	// Commander 不再对外暴露 processor，因此都不能再通过 processor.responder 调用 CommandProcessorResponder 的接口
	// 而是以更加简化的形式实现在以下接口中
	// 这些函数签名和 CommandProcessorResponder 虽然完全一样，但是目的并非是实现 CommandProcessorResponder 接口
	// 而是提供一些更加方便的调用方法
	SendMsgf(msg string, args ...any)
	WarnMsgf(msg string, args ...any)
	ErrorMsgf(msg string, args ...any)
	AlertMsgf(msg string, args ...any)
	BuildInfo() string
	ValidateAuthCode(authCode string) bool
	GetFile(url string) ([]byte, error)
	SendFileMessage(title, content, comment string)
	SendFileWithType(title, content, ftype string)
	SetDebug(debug bool)

	// 以下方法需要在各种 Command 子类中实现
	PrePrepare() bool
	Prepare() bool
	PostPrepare() bool
	PreDo() bool
	Do() bool
	PostDo() bool
}

// 为了共用部分代码，将部分业务逻辑放在 Command 的默认实现上，需要传入此接口的实现
// 具体的，Turtle 和 TurtleController 实现该接口
type CommandProcessorResponder interface {
	GetChannelName() string
	IsMuted() bool
	SendMsgf(msg string, args ...any)
	WarnMsgf(msg string, args ...any)
	ErrorMsgf(msg string, args ...any)
	AlertMsgf(msg string, args ...any)
	BuildInfo() string
	ValidateAuthCode(authCode string) bool
	GetFile(url string) ([]byte, error)
	SendMessage(asFile bool, msg string) // 发送消息，根据 slack 长度限制自动判断是否需要截断 msg ，如果 asFile 为 true，则将 msg 作为文件发送
	SendFileMessage(title, content, comment string)
	SendFileWithType(title, content, ftype string)
	RegisterCommandRequiresFile(commandName string) // 注册需要上传文件的命令
	SetDebug(debug bool)
	Mute(minutes int64) (expireTime time.Time)
	IsDebug() bool
	DeleteMsgs(queries []string, beforeTime time.Time, after *time.Time) (count int, err error)
}

type CommandProcessor struct {
	idCounter          int // ID 游标
	okCounter          int // .ok 命令的 ID 游标
	Commands           []Commander
	Responder          CommandProcessorResponder
	SensitiveCommands  []string
	commandLocks       *xsync.MapOf[string, *sync.Mutex]
	processingCount    int
	additionalHelpText string
}

// 传入 除 ConfirmCommand 和 HelpCommand 的 commands
func NewCommandProcessor(responder CommandProcessorResponder, commands []Commander) *CommandProcessor {
	processor := &CommandProcessor{
		Responder:    responder,
		Commands:     []Commander{},
		idCounter:    1000,
		okCounter:    50000000,
		commandLocks: xsync.NewMapOf[*sync.Mutex](),
	}
	processor.AddCommands(commands)
	// ConfirmCommand 和 HelpCommand 对于 TurtleController 和 Turtle 都是公用的，用不着在上层传进来
	// 直接在内部设置上
	processor.AddCommands([]Commander{NewConfirmCommand(), NewHelpCommand()})
	return processor
}

func (this *CommandProcessor) AddCommands(commands []Commander) {
	for _, cmd := range commands {
		cmd.SetProcessor(this)
		if cmd.IsRequiresFile() {
			this.Responder.RegisterCommandRequiresFile(cmd.GetName())
		}
		if cmd.IsSensitive() {
			this.SensitiveCommands = append(this.SensitiveCommands, cmd.GetNameAndAlias()...)
		}
	}
	this.Commands = append(this.Commands, commands...)
}

func (this *CommandProcessor) SetAdditionalHelpText(text string) {
	this.additionalHelpText = text
}

func (this *CommandProcessor) TryLock(commandName string, subcommand string) (er error) {
	if commandName == "ok" {
		return nil // ok 命令不加锁，没有冲突问题
	}
	if !this.checkCommandName(commandName) {
		er = fmt.Errorf("try to lock command (%s), but command with such name not existed", commandName)
		return
	}
	key := commandName
	if subcommand != "" {
		return nil // 子命令不加锁，加锁导致其他子命令无法执行
	}
	mutex, _ := this.commandLocks.LoadOrStore(key, &sync.Mutex{})
	if !mutex.TryLock() {
		er = fmt.Errorf("try acquire lock failed, command is processing")
		return
	}
	this.processingCount += 1
	return
}

func (this *CommandProcessor) Unlock(commandName string, subcommand string) {
	if commandName == "ok" {
		return
	}
	if !this.checkCommandName(commandName) {
		zlog.Errorf("try to lock command (%s), but command with such name not existed", commandName)
		return
	}
	key := commandName
	if subcommand != "" {
		key = fmt.Sprintf("%s#%s", commandName, subcommand)
	}
	mutex, ok := this.commandLocks.Load(key)
	if !ok {
		zlog.Errorf("command mutex not found, please lock before unlock")
		return
	}
	this.processingCount -= 1
	mutex.Unlock()
}

func (this *CommandProcessor) checkCommandName(commandName string) bool {
	nameExist := false
	for _, cmd := range this.Commands {
		if cmd.GetName() == commandName {
			nameExist = true
			break
		}
	}
	return nameExist
}

func (this *CommandProcessor) GetProcessingCount() int {
	return this.processingCount
}

func (this *CommandProcessor) Process(commandName string, commandArgs []string) bool {
	// 处理当前命令，包括 ok 和 help 命令
	cmdFound := false
	for _, command := range this.Commands {
		cmdFound = command.Is(commandName)
		fullName := command.GetName()
		subCommand := command.GetSubcommand()
		if cmdFound {
			if err := this.TryLock(fullName, subCommand); err != nil {
				if strings.Contains(err.Error(), "command is processing") {
					if subCommand == "" {
						this.Responder.ErrorMsgf("指令 `.%s` 正在执行，请稍后再试。", fullName)
					} else {
						this.Responder.ErrorMsgf("指令 `.%s %s` 正在执行，请稍后再试。", fullName, subCommand)
					}
				} else {
					this.Responder.ErrorMsgf("系统错误，准备指令时发现错误：%s", err)
				}
			} else {
				// 设置命令的参数，包括基础的 Validate
				if ok, err := this.SetupCommand(command, commandArgs); ok {
					if command.IsRequiresConfirm() {
						// 仅有 prepare 成功的命令才可以被 .ok 命令二次验证执行
						if command.PrePrepare() {
							prepared := command.Prepare()
							if prepared && command.PostPrepare() {
								command.SetPrepared(true)
								command.PromptRequiresConfirm()
							} else {
								zlog.Errorf("command %s#%d prepare failed.\n", command.GetName(), command.GetID())
								this.Unlock(fullName, subCommand)
								return false
							}
						}
					} else {
						// 仅有 prepare 成功的命令才可以被 .ok 命令二次验证执行
						if command.PrePrepare() {
							prepared := command.Prepare()
							if prepared && command.PostPrepare() {
								command.SetPrepared(true)
								if command.PreDo() {
									command.Do()
									command.PostDo()
								}
							}
						}
					}
				} else {
					if err.Error() == "google authcode error" {
						this.Responder.ErrorMsgf("谷歌验证码错误")
					} else if err.Error() == "arguments length error" {
						this.Responder.ErrorMsgf("参数长度错误，请输入： %s", command.GetCurrentInstruction())
					} else {
						this.Responder.ErrorMsgf("参数错误，error: %s, 请输入： %s", err, command.GetCurrentInstruction())
					}
					this.Unlock(fullName, subCommand)
					return false
				}
				this.Unlock(fullName, subCommand)
			}
			break
		}
	}
	if !cmdFound {
		this.Responder.SendMsgf("无法识别的命令，输入 .? 查看帮助")
		return false
	}
	return true
}

func (this *CommandProcessor) SetupCommand(command Commander, args []string) (bool, error) {
	// 分别设置 ok 命令和其他命令的 ID，这样可以保证非 ok 命令的 ID 是连续的，并且保证 ok 命令也有 ID，可以追溯和调试
	if command.GetName() == "ok" {
		this.okCounter++
		command.SetID(this.okCounter)
	} else {
		this.idCounter++
		command.SetID(this.idCounter)
	}
	command.SetPrepared(false)  // 确保 prepared 默认是 false，以免不小心从 struct 赋值
	command.SetConfirmed(false) // 确保 prepared 默认是 false，以免不小心从 struct 赋值
	return command.SetArgs(args[:], true)
}

type Command struct {
	ID                     int      // 由 CommandProcessor 自动管理
	Name                   string   // 命令的名字
	Alias                  []string // 命令的别名
	RequiresConfirm        bool     // 是否需要二次验证
	SkipConfirmSubcommands []string // 跳过两次验证的子命令（第一个参数）
	DefaultSubcommand      string   // 默认子命令（如果没有输入任何参数，填入该字段作为第一个参数）
	ArgMin                 int      // 包含该值
	ArgMax                 int      // 包含该值
	AuthcodePos            int      // 命令名以后的参数，从1开始计数
	RequiresFile           bool     // 命令是否需要上传文件
	ConfirmPrompt          string   // 二次确认提示
	Sensitive              bool     // 是否有敏感信息，如果是，会在消息中隐藏信息，并稍后删除命令

	isMore    bool // 是否需要使用 file 发送回复
	prepared  bool // Prepare() 是否成功，内部变量
	confirmed bool // 已经二次确认

	Instruction  string   // 命令指令
	Instructions []string // 命令指令列表

	Args      []string
	Processor *CommandProcessor
}

func (this *Command) GetName() string {
	return this.Name
}

func (this *Command) Is(nameOrAlias string) (result bool) {
	if strings.HasSuffix(nameOrAlias, "+") {
		this.isMore = true
		nameOrAlias = strings.ReplaceAll(nameOrAlias, "+", "")
	} else {
		this.isMore = false
	}
	if strings.EqualFold(this.Name, nameOrAlias) {
		return true
	}
	for _, v := range this.Alias {
		if strings.EqualFold(v, nameOrAlias) {
			return true
		}
	}
	return false
}

func (this *Command) IsMore() bool {
	return this.isMore
}

func (this *Command) SetID(id int) {
	this.ID = id
}

func (this *Command) GetID() int {
	return this.ID
}

// 非特别原因，不建议使用 validate = false 二次设置参数
// 原有代码逻辑中，有在 Prepare() 中处理原始参数，然后将处理过的参数调用 SetArgs()，在 Do() 中使用处理过的参数
// 因为这种操作的代码量都比较少，已经改写了这种逻辑，在 Do() 函数中始终使用原始数据
func (this *Command) SetArgs(args []string, validate bool) (bool, error) {
	this.SetPrepared(false)  // 确保 prepared 默认是 false，以免不小心从 struct 赋值
	this.SetConfirmed(false) // 确保 prepared 默认是 false，以免不小心从 struct 赋值
	this.Args = []string{}
	// 如果没有输入任何参数，但是设置了默认参数，将默认参数填进去
	if len(args) == 0 && this.DefaultSubcommand != "" {
		args = []string{this.DefaultSubcommand}
	}
	for _, arg := range args {
		this.Args = append(this.Args, strings.TrimSpace(arg))
	}

	if validate {
		result, err := this.Validate()
		if result {
			return true, nil
		} else {
			// 虽然数据不合法，但是也不要清理 Args 数据，因为还需要 subcommand 给出正确的命令 instruction
			return false, err
		}
	}
	return true, nil
}

// Commands 初始化的时候内部调用，Commander 的实现中不用调用或设置属性
func (this *Command) SetProcessor(processor *CommandProcessor) {
	this.Processor = processor
}

func (this *Command) IsID(id int) bool {
	return id == this.ID
}

func (this *Command) IsRequiresConfirm() bool {
	skipConfirm := false
	if len(this.SkipConfirmSubcommands) > 0 && len(this.Args) > 0 && utils.SliceContains(this.SkipConfirmSubcommands, this.Args[0]) {
		skipConfirm = true
	}
	return this.RequiresConfirm && !skipConfirm
}

func (this *Command) GetInstructions() string {
	var instruction string
	aliasStrList := []string{}
	for _, alias := range this.Alias {
		aliasStrList = append(aliasStrList, fmt.Sprintf("`.%s`", alias))
	}
	if this.Instruction != "" {
		if len(aliasStrList) > 0 {
			instruction = fmt.Sprintf("%s 或 %s", strings.Join(aliasStrList, " 或 "), this.Instruction)
		} else {
			instruction = this.Instruction
		}
	} else if len(this.Instructions) > 0 {
		instructionList := []string{}
		for _, inst := range this.Instructions {
			if len(aliasStrList) > 0 {
				inst = fmt.Sprintf("%s 或 %s", strings.Join(aliasStrList, " 或 "), inst)
			}
			instructionList = append(instructionList, inst)
		}
		instruction = strings.Join(instructionList, "\n")
	}
	return instruction
}

func (this *Command) GetCurrentInstruction() string {
	for _, inst := range this.Instructions {
		subcommands, _, _, _, _, _, err := this.ParseInstruction(inst, true)
		if err != nil {
			continue
		}
		currentSubcommand := this.GetSubcommand()
		if utils.SliceContainsEqualFold(subcommands, currentSubcommand) {
			return inst
		}
	}
	return this.GetInstructions()
}

func (this *Command) Prepare() bool {
	return true
}

func (this *Command) PrePrepare() bool {
	return true
}

func (this *Command) PostPrepare() bool {
	return true
}

func (this *Command) Do() bool {
	return true
}

func (this *Command) PreDo() bool {
	return true
}

func (this *Command) PostDo() bool {
	return true
}

func (this *Command) IsSensitive() bool {
	return this.Sensitive
}

func (this *Command) GetNameAndAlias() []string {
	result := []string{this.Name}
	result = append(result, this.Alias...)
	return result
}

// 在 prepared 后调用，用于设置内部的 prepared 状态
// 不在 prepare() 函数中直接设置的原因是，不希望 Commander 的实现去处理这个问题，如果漏处理的话，合法性检查就没用了。
func (this *Command) SetPrepared(prepared bool) {
	this.prepared = prepared
}

func (this *Command) IsPrepared() bool {
	return this.prepared
}

// 命令通过 .ok 命令二次确认完成后，设置为已经确认

func (this *Command) SetConfirmed(confirmed bool) {
	this.confirmed = confirmed
}

func (this *Command) IsConfirmed() bool {
	return this.confirmed
}

func (this *Command) IsRequiresFile() bool {
	return this.RequiresFile
}

func (this *Command) GetSubcommand() string {
	if !this.HasSubcommand() {
		return ""
	}
	if len(this.Args) > 0 {
		return this.Args[0]
	}
	return ""
}

func (this *Command) GetArgs() []string {
	if !this.HasSubcommand() {
		return this.Args
	}
	if len(this.Args) > 1 {
		return this.Args[1:]
	}
	return []string{}
}

func (this *Command) ValidateArgLength() bool {
	// 检查参数长度
	// 如果设置了 instructions，不再使用 argMin 和 argMax 配置
	// 而是解析 instruction 中的 subcommand 和 authcode 的位置
	// 要求 instruction 按指定的格式编写，否则可能出现错误
	argMin := this.ArgMin
	authCodePos := this.AuthcodePos
	argMax := this.ArgMax
	if this.IsRequiresFile() {
		argMax = this.ArgMax + 1
	}

	argLen := len(this.GetArgs())

	if argLen < argMin || argLen > argMax || argLen < authCodePos {
		return false
	}
	return true
}

func (this *Command) ParseInstruction(inst string, isSubcommand bool) (subcommands []string, argMin, argMax, authCodePos int, needConfirm bool, requiresFile bool, err error) {
	// cmd subcommands arg1 arg2 ... [argN] AuthCode __file__`, description
	inst = strings.TrimPrefix(inst, fmt.Sprintf("`.%s ", this.Name))
	// subcommands arg1 arg2 ... [argN] AuthCode __file__
	instBody := strings.Split(inst, "`")[0]
	// subcommands, arg1, arg2, ..., [argN], AuthCode, __file__
	parts := strings.Split(instBody, " ")
	if len(parts) > 0 {
		args := []string{}
		if isSubcommand {
			// subcommand 可以有不同别名，因此需要解析为多个 subcommand
			subcommands = strings.Split(parts[0], "/")
			if len(parts) > 1 {
				args = parts[1:]
			}
		} else {
			args = parts
		}

		if len(args) > 0 {
			if args[len(args)-1] == "？" {
				return []string{}, 0, 0, 0, false, false, fmt.Errorf("instruction end with chinese '？' error: %s", inst)
			}

			if args[len(args)-1] == "?" {
				needConfirm = true
				args = args[:len(args)-1]
			}
		}
		argMax = len(args)

		optionalCount := 0
		for _, part := range args {
			if strings.HasPrefix(part, "[") && strings.HasSuffix(part, "]") {
				optionalCount++
				break
			}
		}
		argMin = argMax - optionalCount

		for i, arg := range args {
			if strings.EqualFold(arg, "AuthCode") {
				if needConfirm {
					err = fmt.Errorf("authcodePos can't be set when needConfirm is true, instruction: %s", inst)
					return
				}
				authCodePos = i + 1
			}
			if strings.EqualFold(arg, "__file__") {
				requiresFile = true
				break
			}
		}
	}
	return
}

func (this *Command) HasSubcommand() bool {
	return len(this.Instructions) > 0
}

func (this *Command) ValidateInstructions() (bool, error) {
	//  通过 格式化的 instructions 来设置 subcommand 的 ArgMin, ArgMax, AuthcodePos, RequiresFile
	// TODO: 如果 command.Instruction 的格式满足要求，也可以用上述方法来自动设置 ArgMin, ArgMax, AuthcodePos, RequiresFile
	// inst must be like "`.cmdName subcommand arg1 arg2 ...`, description"
	if len(this.Instructions) > 0 {
		this.AuthcodePos = 0
		this.RequiresConfirm = false
		this.RequiresFile = false
		for _, inst := range this.Instructions {
			subcommands, argMin, argMax, authCodePos, needConfirm, requiresFile, err := this.ParseInstruction(inst, true)
			if err != nil {
				return false, err
			}
			currentSubcommand := this.GetSubcommand()
			if utils.SliceContainsEqualFold(subcommands, currentSubcommand) {
				this.ArgMin = argMin
				this.ArgMax = argMax
				if authCodePos > 0 {
					this.AuthcodePos = authCodePos
				}
				this.RequiresConfirm = needConfirm
				this.RequiresFile = requiresFile
			}

			if !strings.HasPrefix(inst, fmt.Sprintf("`.%s ", this.Name)) {
				return false, fmt.Errorf("instructions format error: %s", inst)
			}
			if strings.Count(inst, "`") != 2 {
				return false, fmt.Errorf("instructions format error: %s", inst)
			}
		}
	}

	return true, nil
}

// 仅做基本的参数检查，包含：
// 1、参数长度的检查；
// 2、requiresConfirm 和 authcodePos 的合法性检查
// 3、GoogleAuthCode 的检查
func (this *Command) Validate() (bool, error) {
	// 检查 instructions 格式是否正确，并且设置 ArgMin, ArgMax, AuthcodePos
	if ok, err := this.ValidateInstructions(); !ok {
		return false, err
	}
	// 检查参数长度是否合法，必须在 validateInstructions 之后调用，因为 validateInstructions 会设置 ArgMin, ArgMax, AuthcodePos
	if !this.ValidateArgLength() {
		return false, errors.New("arguments length error")
	}
	// 如果需要两次验证，不能设置 authcodePos 的值
	if this.RequiresConfirm && this.AuthcodePos > 0 {
		return false, errors.New("system inner error, improper authcodePos config")
	}
	// 如果不需要两次验证，直接检查验证码是否正确
	if !this.RequiresConfirm && this.AuthcodePos > 0 {
		args := this.GetArgs()
		authCode := args[this.AuthcodePos-1]
		if strings.EqualFold(authCode[:1], "g") {
			// 兼容支持验证码前面带一个g
			authCode = authCode[1:]
		}
		if !this.ValidateAuthCode(authCode) {
			return false, errors.New("google authcode error")
		}
	}
	return true, nil
}

func (this *Command) PromptRequiresConfirm() {
	args := this.Args
	if this.RequiresFile {
		args = this.Args[:len(this.Args)-1]
	}
	argStr := strings.Join(args, " ")
	if this.Sensitive {
		argStr = "***"
	}
	if this.ConfirmPrompt != "" {
		this.SendMsgf("``` *** %s *** ```\n命令: %s, 参数: %s\n请输入 `.ok %d GoogleAuthCode` 二次确认", this.ConfirmPrompt, this.Name, argStr, this.ID)
	} else {
		this.SendMsgf("\n命令: %s, 参数: %s\n请输入 `.ok %d GoogleAuthCode` 二次确认", this.Name, argStr, this.ID)
	}
}
func (this *Command) SendMsgf(msg string, args ...any) {
	msg = fmt.Sprintf(msg, args...)
	zlog.Infof("[%s] %s", this.Processor.Responder.GetChannelName(), msg)
	if this.IsMore() {
		this.Processor.Responder.SendMessage(true, msg)
	} else {
		this.Processor.Responder.SendMessage(false, msg)
	}
}

func (this *Command) ErrorMsgf(msg string, args ...any) {
	msg = fmt.Sprintf(msg, args...)
	msg = fmt.Sprintf("`错误:` %s", msg)
	zlog.Warnf("[%s] %s", this.Processor.Responder.GetChannelName(), msg)
	if this.IsMore() {
		this.Processor.Responder.SendMessage(true, msg)
	} else {
		this.Processor.Responder.SendMessage(false, msg)
	}
}

func (this *Command) WarnMsgf(msg string, args ...any) {
	msg = fmt.Sprintf(msg, args...)
	msg = fmt.Sprintf("`警告:` %s", msg)
	zlog.Warnf("[%s] %s", this.Processor.Responder.GetChannelName(), msg)
	if this.IsMore() {
		this.Processor.Responder.SendMessage(true, msg)
	} else {
		this.Processor.Responder.SendMessage(false, msg)
	}
}

func (this *Command) AlertMsgf(msg string, args ...any) {
	if !this.Processor.Responder.IsMuted() {
		msg = fmt.Sprintf(msg, args...)
		msg = fmt.Sprintf("`运行错误:` %s", msg)
		zlog.Errorf("[%s] %s", this.Processor.Responder.GetChannelName(), msg)
		if strings.Contains(msg, "err[51030]") {
			// OKEx 结算中，不报警
			return
		}
		if this.IsMore() {
			this.Processor.Responder.SendMessage(true, msg)
		} else {
			this.Processor.Responder.SendMessage(false, msg)
		}
	} else {
		zlog.Warnf("[%s] alert message muted, msg: %s", this.Processor.Responder.GetChannelName(), msg)
	}
}

func (this *Command) BuildInfo() string {
	return this.Processor.Responder.BuildInfo()
}

func (this *Command) ValidateAuthCode(authCode string) bool {
	return this.Processor.Responder.ValidateAuthCode(authCode)
}

func (this *Command) GetFile(url string) ([]byte, error) {
	return this.Processor.Responder.GetFile(url)
}

func (this *Command) SendFileMessage(title, content, comment string) {
	this.Processor.Responder.SendFileMessage(title, content, comment)
}

func (this *Command) SendFileWithType(title, content, ftype string) {
	this.Processor.Responder.SendFileWithType(title, content, ftype)
}

func (this *Command) SetDebug(debug bool) {
	this.Processor.Responder.SetDebug(debug)
}

type Messagable interface {
	SendRTMMessage(channelName, msg string) (bool, error)
	SendFileMessage(channelName, title, msg, comment, ftype string)
	GetFile(url string) ([]byte, error)
	RegisterCommandRequiresFile(channelName, commandName string)
	GetChannelID(channelName string) string
	DeleteMsgs(channelID string, queries []string, before time.Time, after *time.Time) (count int, err error)
}

// BaseResponder 可以嵌入到其他 struct 中，用以提供 CommandProcessorResponder 的接口
// 现在 QuanterManager, TurtleController, Turtle 都嵌入了该 struct，因此自动实现了 CommandProcessorResponder 接口
// 而不用重复单独实现这些该接口，消除了一些重复的代码
type BaseResponder struct {
	CommitHash         string       `json:"-"`
	BuildTime          string       `json:"-"`
	Messenger          Messagable   `json:"-"` // slack 机器人客户端
	ChannelName        string       `json:"-"`
	Launched           *atomic.Bool `json:"-"`
	LaunchTime         *time.Time   `json:"-"`
	Debug              bool         `json:"-"`
	muteExpireTime     time.Time    `json:"-"` // 禁声过期时间
	LogPath            string       `json:"-"`
	messageRateLimiter exchange.SyncMapOf[string, *rate.OnceIntervalLimiter]
}

func NewBaseResponder(debug bool, commitHash, buildTime, channelName string, messenger Messagable, logPath string) BaseResponder {
	l := &atomic.Bool{}
	l.Store(false)
	responder := BaseResponder{
		CommitHash:  commitHash,
		BuildTime:   buildTime,
		Messenger:   messenger,
		ChannelName: channelName,
		Launched:    l,
		Debug:       debug,
		LogPath:     logPath,
	}
	// 如果是 debug 模式，跳过 auth code 验证
	// 设置后不可更改，中途修改 debug 模式的时候，也不再有效
	if debug {
		// 仅在 commitHash 为空时，才可以跳过 authcode 验证
		// release 版本，通常 commitHash 都不是空，防止因为错误配置 debug 选项跳过 authcode 验证
		// 因为 license 模块可能要求必须设置 buildTime，因此不用 buildTime 而是用 commitHash 为空作为判断 binary 是否为 release 的条件
		if commitHash == "" {
			secrets.SkipAuthCode(true)
			zlog.Infof("debug is on and commitHash is empty, skip auth code.")
		} else {
			zlog.Errorf("CommitHash is not empty, can't skip auth code in debug mode.")
		}
	} else {
		secrets.SkipAuthCode(false)
		zlog.Infof("debug is off, auth code is required.")
	}
	return responder
}

func (this *BaseResponder) Mute(minutes int64) (expireTime time.Time) {
	this.muteExpireTime = time.Now().Add(time.Minute * time.Duration(minutes))
	return this.muteExpireTime
}

func (this *BaseResponder) GetLaunchTime() *time.Time {
	return this.LaunchTime
}

// 实际是 channelName
func (this *BaseResponder) GetChannelName() string {
	return this.ChannelName
}

func (this *BaseResponder) DeleteMsgs(queries []string, before time.Time, after *time.Time) (count int, err error) {
	channelID := this.Messenger.GetChannelID(this.ChannelName)
	if channelID == "" {
		return 0, fmt.Errorf("get channel id failed, channel name: %s", this.ChannelName)
	}
	return this.Messenger.DeleteMsgs(channelID, queries, before, after)
}

func (this *BaseResponder) IsMuted() bool {
	if this.muteExpireTime.IsZero() {
		return false
	} else {
		return time.Now().Before(this.muteExpireTime)
	}
}

func (this *BaseResponder) IsDebug() bool {
	return this.Debug
}

func (this *BaseResponder) SendMsgf(msg string, args ...any) {
	msg = fmt.Sprintf(msg, args...)
	zlog.Infof("[%s] %s", this.ChannelName, msg)
	this.SendMessage(false, msg)
}

func (this *BaseResponder) ErrorMsgf(msg string, args ...any) {
	msg = fmt.Sprintf(msg, args...)
	msg = fmt.Sprintf("`错误:` %s", msg)
	zlog.Warnf("[%s] %s", this.ChannelName, msg)
	this.SendMessage(false, msg)
}

func (this *BaseResponder) WarnMsgf(msg string, args ...any) {
	msg = fmt.Sprintf(msg, args...)
	msg = fmt.Sprintf("`警告:` %s", msg)
	zlog.Warnf("[%s] %s", this.ChannelName, msg)
	this.SendMessage(false, msg)
}

func (this *BaseResponder) AlertMsgf(msg string, args ...any) {
	if !this.IsMuted() {
		msg = fmt.Sprintf(msg, args...)
		msg = fmt.Sprintf("`运行错误:` %s", msg)
		zlog.Errorf("[%s] %s", this.ChannelName, msg)
		if strings.Contains(msg, "err[51030]") {
			// OKEx 结算中，不报警
			return
		}
		this.SendMessage(false, msg)
	}
}

func (this *BaseResponder) SendMessage(asFile bool, msg string) {
	if this.Messenger == nil {
		zlog.Errorf("send message failed, messenger not initialized.")
		return
	}

	// repeated message check, counting how many times the same message has been sent, output the count and the message every 1 minutes
	// hash msg to md5
	errTags := []string{"`错误:`", "`运行错误:`", "`警告:`", "`运行错误`:"}
	isErrorMsg := false
	for _, tag := range errTags {
		if strings.Contains(msg, tag) {
			isErrorMsg = true
			break
		}
	}
	if isErrorMsg {
		md5 := fmt.Sprintf("%x", md5.Sum([]byte(msg)))
		duration := 1 * time.Minute
		if this.Debug {
			duration = 5 * time.Second
		}
		rateLimiter, _ := this.messageRateLimiter.LoadOrStore(md5, rate.NewOnceIntervalLimiter(duration, 100))
		count, allow := rateLimiter.Allow()
		if !allow {
			zlog.Debugf("message repeated, count: %d", count)
			return
		}

		if count > 1 {
			this.Messenger.SendRTMMessage(this.ChannelName, fmt.Sprintf("`警告:` <repeated message, count: %d in %s>", count, duration))
		}
	}

	if asFile {
		msg = strings.ReplaceAll(msg, "```", "")
		this.Messenger.SendFileMessage(this.ChannelName, "", msg, "", "")
	} else {
		limit := math.Min(float64(len(msg)), SLACK_MESSAGE_LIMIT)
		msg = msg[:int(limit)]
		if strings.Count(msg, "```") == 1 {
			msg += "...```"
		}
		this.Messenger.SendRTMMessage(this.ChannelName, msg)
	}
}

func (this *BaseResponder) SendFileMessage(title, msg, comment string) {
	this.Messenger.SendFileMessage(this.ChannelName, title, msg, comment, "")
}

func (this *BaseResponder) SendFileWithType(title, msg, ftype string) {
	this.Messenger.SendFileMessage(this.ChannelName, title, msg, "", ftype)
}

func (this *BaseResponder) GetFile(url string) ([]byte, error) {
	return this.Messenger.GetFile(url)
}

func (this *BaseResponder) BuildInfo() string {
	return fmt.Sprintf("%s/(%s)", this.CommitHash, this.BuildTime)
}

func (this *BaseResponder) ValidateAuthCode(authCode string) bool {
	if !this.Launched.Load() {
		this.AlertMsgf("程序未启动，请使用 launch 命令启动")
		return false
	}
	return secrets.ValidateAuthCode(authCode)
}

func (this *BaseResponder) RegisterCommandRequiresFile(commandName string) {
	if this.Messenger != nil {
		this.Messenger.RegisterCommandRequiresFile(this.ChannelName, commandName)
	} else {
		zlog.Panicf("manager.messenger not initialized.")
	}
}

// 二次确认命令，TurtleController 和 Turtle 可以共用，因此统一实现
type ConfirmCommand struct {
	Command
}

func NewConfirmCommand() *ConfirmCommand {
	cmd := &ConfirmCommand{
		Command: Command{
			Name:            "ok",
			Instruction:     "`.ok ${ID} GoogleAuthCode` 二次确认命令",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
	}
	return cmd
}

func (this *ConfirmCommand) Do() bool {
	confirmID, err := strconv.Atoi(this.Command.Args[0])
	if err == nil {
		for _, cmd := range this.Processor.Commands {
			if cmd.IsID(confirmID) {
				if !cmd.IsRequiresConfirm() {
					this.ErrorMsgf("命令 %s 不需要二次确认。", cmd.GetName())
					return false
				} else {
					if cmd.IsPrepared() {
						if cmd.IsConfirmed() {
							this.ErrorMsgf("命令 #%d 已经二次确认过了。", confirmID)
						} else {
							cmdName := cmd.GetName()
							subCommand := cmd.GetSubcommand()
							if err := this.Processor.TryLock(cmdName, subCommand); err != nil {
								if strings.Contains(err.Error(), "command is processing") {
									if subCommand == "" {
										this.ErrorMsgf("指令 `.%s` 正在执行，请稍后再试。", cmdName)
									} else {
										this.ErrorMsgf("指令 `.%s %s` 正在执行，请稍后再试。", cmdName, subCommand)
									}
								} else {
									this.ErrorMsgf("系统错误，准备指令时发现错误：%s", err)
								}
							} else {
								cmd.SetConfirmed(true)
								cmd.Do()
								this.Processor.Unlock(cmdName, subCommand)
							}
						}
					}
				}
			}
		}
	} else {
		this.ErrorMsgf("命令格式错误，请输入：`.ok 1xxx authCode`")
		return false
	}
	return true
}

// 打印帮助，TurtleController 和 Turtle 可以共用，因此统一实现
type HelpCommand struct {
	Command
}

func NewHelpCommand() *HelpCommand {
	cmd := &HelpCommand{
		Command: Command{
			Name:            "help",
			Alias:           []string{"?"},
			Instruction:     "`.help` 获取帮助",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
	}
	return cmd
}

func (this *HelpCommand) Do() bool {
	helpStr := fmt.Sprintf("%s, 可用命令\n", this.BuildInfo())
	for _, cmd := range this.Processor.Commands {
		if strings.EqualFold(cmd.GetName(), "pager") {
			this.SendMsgf(helpStr)
			helpStr = ""
		} else {
			helpStr += cmd.GetInstructions() + "\n"
		}

	}
	if this.Processor.additionalHelpText != "" {
		helpStr += "\n_说明:_\n" + this.Processor.additionalHelpText
	}
	this.SendMsgf(helpStr)
	return true
}

package marketmaker

import (
	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/exchange"
)

type Status string

const Running Status = "Running"
const Paused Status = "Paused"
const Closed Status = "Closed"

type FinishReason string

const FinishReasonTakeProfit FinishReason = "TakeProfit"
const FinishReasonStopLoss FinishReason = "StopLoss"
const FinishReasonManual FinishReason = "Manual"
const FinishReasonAuto FinishReason = "Auto"

type MarketMaker interface {
	GetName() string
	GetRefID() string
	GetSymbolCode() *exchange.SymbolCode
	GetSymbol() string
	GetStatus() Status
	Start() error
	Run() error
	GetOptions() *option.Options
	ValidateOptions(opts *option.Options) error
	UpdateOptions(opts *option.Options)
	RenderPreview() string
	QueryOrders()
	UpdateReport()
	IsFinished() bool
	GetStatusRow(simple bool, detail bool) []string
	GetHoldingQty() float64
	UpdateQty() (holdingQty float64, quoteQty float64, err error)
	Close(reason FinishReason, sellBaseCoin bool, force bool)
	Pause()
	Resume()
}

type MarketMakerReport struct {
	BuyFilledCount  int
	SaleFilledCount int
	PosQty          float64
	PNL             float64 // 总收益
	MarketMakerPNL  float64 // 做市利润
	HoldingPNL      float64 // 现有持仓收益，即浮动盈亏
	TradedValue     float64 // 总交易额
	Fee             float64 // 总手续费
}

type CreateMarketMakerOptions struct {
	SymbolCode *exchange.SymbolCode
	Symbol     string
	Name       string
	OptStr     string
}

type PrepareOrder struct {
	Price float64
	Qty   float64
	Side  exchange.OrderSide
	Index int
}

const ExtKeyPNL = "PNL"
const ExtKeyPosQty = "PosQty"

package marketmaker

import (
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type SimpleMarketMaker struct {
	BaseMarketMaker

	startBuyPrice   float64           // 初始买单价格，每次根据市场价格调整
	startSellPrice  float64           // 初始卖单价格，每次根据市场价格调整
	highestBuyPrice float64           // 当前挂单最高买单价格
	lowestSellPrice float64           // 当前挂单最低卖单价格
	openOrders      []*exchange.Order // 记录当前挂单

	// 以下参数在 Options 中保存和加载，不参与序列化
	OrderPairs           int     `json:"-"`
	OrderPriceInterval   float64 `json:"-"`
	OrderStartSize       float64 `json:"-"`
	OrderStepSize        float64 `json:"-"`
	MinPosition          float64 `json:"-"`
	MaxPosition          float64 `json:"-"`
	LoopInterval         int     `json:"-"` // 订单检查间隔，单位秒
	MinSpread            float64 `json:"-"` // 买卖价差最小值
	RelistPriceTolerance float64 `json:"-"` // 订单价格容差，如 0.001 表示价差在 0.1% 以内不需要更新订单
	UseRemoteOpenOrders  bool    `json:"-"` // 流程中是否获取远程订单
}

type SimpleMarketMakerOption string

const (
	SimpleMarketMakerOptionOrderPairs           SimpleMarketMakerOption = "OrderPairs"
	SimpleMarketMakerOptionOrderPriceInterval   SimpleMarketMakerOption = "OrderPriceInterval"
	SimpleMarketMakerOptionOrderStartSize       SimpleMarketMakerOption = "OrderStartSize"
	SimpleMarketMakerOptionOrderStepSize        SimpleMarketMakerOption = "OrderStepSize"
	SimpleMarketMakerOptionMinPosition          SimpleMarketMakerOption = "MinPosition"
	SimpleMarketMakerOptionMaxPosition          SimpleMarketMakerOption = "MaxPosition"
	SimpleMarketMakerOptionLoopInterval         SimpleMarketMakerOption = "LoopInterval"
	SimpleMarketMakerOptionMinSpread            SimpleMarketMakerOption = "MinSpread"
	SimpleMarketMakerOptionRelistPriceTolerance SimpleMarketMakerOption = "RelistPriceTolerance"
	SimpleMarketMakerOptionUseRemoteOpenOrders  SimpleMarketMakerOption = "UseRemoteOpenOrders"
)

func getSimpleMarketMakerOptionDefinitions() []*option.TypedOption {
	return []*option.TypedOption{
		option.NewTypedOption(string(SimpleMarketMakerOptionOrderPairs), option.Int, nil, "#", false),
		option.NewTypedOption(string(SimpleMarketMakerOptionOrderPriceInterval), option.Float64, nil, "", false),
		option.NewTypedOption(string(SimpleMarketMakerOptionOrderStartSize), option.Float64, nil, "", false),
		option.NewTypedOption(string(SimpleMarketMakerOptionOrderStepSize), option.Float64, nil, "", false),
		option.NewTypedOption(string(SimpleMarketMakerOptionMinPosition), option.Float64, nil, "", false),
		option.NewTypedOption(string(SimpleMarketMakerOptionMaxPosition), option.Float64, nil, "", false),
		option.NewTypedOption(string(SimpleMarketMakerOptionLoopInterval), option.Int, 5, "s", false),
		option.NewTypedOption(string(SimpleMarketMakerOptionMinSpread), option.Float64, 0.001, "", true),
		option.NewTypedOption(string(SimpleMarketMakerOptionRelistPriceTolerance), option.Float64, 0.001, "", true),
		option.NewTypedOption(string(SimpleMarketMakerOptionUseRemoteOpenOrders), option.Bool, false, "", false),
	}
}

func NewSimpleMarketMakerOptions(optStr string) (*option.Options, error) {
	opts, err := option.NewOptions(optStr, getSimpleMarketMakerOptionDefinitions()...)
	if err != nil {
		return nil, err
	}
	return opts, nil
}

func NewSimpleMarketMaker(baseMM BaseMarketMaker, optStr string) (MarketMaker, error) {
	opts, err := NewSimpleMarketMakerOptions(optStr)
	if err != nil {
		return nil, err
	}

	baseMM.Options = opts

	mm := &SimpleMarketMaker{
		BaseMarketMaker:      baseMM,
		OrderPairs:           opts.Get(string(SimpleMarketMakerOptionOrderPairs)).(int),
		OrderPriceInterval:   opts.Get(string(SimpleMarketMakerOptionOrderPriceInterval)).(float64),
		OrderStartSize:       opts.Get(string(SimpleMarketMakerOptionOrderStartSize)).(float64),
		OrderStepSize:        opts.Get(string(SimpleMarketMakerOptionOrderStepSize)).(float64),
		MinPosition:          opts.Get(string(SimpleMarketMakerOptionMinPosition)).(float64),
		MaxPosition:          opts.Get(string(SimpleMarketMakerOptionMaxPosition)).(float64),
		LoopInterval:         opts.Get(string(SimpleMarketMakerOptionLoopInterval)).(int),
		MinSpread:            opts.Get(string(SimpleMarketMakerOptionMinSpread)).(float64),
		RelistPriceTolerance: opts.Get(string(SimpleMarketMakerOptionRelistPriceTolerance)).(float64),
		UseRemoteOpenOrders:  opts.Get(string(SimpleMarketMakerOptionUseRemoteOpenOrders)).(bool),
	}

	err = mm.ValidateOptions(opts)
	if err != nil {
		return nil, err
	}

	mm.MarketMaker = mm
	return mm, nil
}

func (this *SimpleMarketMaker) ValidateOptions(opts *option.Options) error {
	if opts.Get(string(SimpleMarketMakerOptionOrderPairs)).(int) <= 0 {
		return fmt.Errorf("OrderPairs must be greater than 0")
	}

	if opts.Get(string(SimpleMarketMakerOptionOrderPriceInterval)).(float64) <= 0 {
		return fmt.Errorf("OrderPriceInterval must be greater than 0")
	}

	if opts.Get(string(SimpleMarketMakerOptionOrderStartSize)).(float64) <= 0 {
		return fmt.Errorf("OrderStartSize must be greater than 0")
	}

	if opts.Get(string(SimpleMarketMakerOptionOrderStepSize)).(float64) < 0 {
		return fmt.Errorf("OrderStepSize must be greater than or equal to 0")
	}

	if opts.Get(string(SimpleMarketMakerOptionMinPosition)).(float64) < 0 && this.SymbolCode.IsSpot() {
		return fmt.Errorf("In spot mode, MinPosition must be greater than or equal to 0")
	}

	if opts.Get(string(SimpleMarketMakerOptionMaxPosition)).(float64) < 0 && this.SymbolCode.IsSpot() {
		return fmt.Errorf("In spot mode, MaxPosition must be greater than or equal to 0")
	}

	if opts.Get(string(SimpleMarketMakerOptionLoopInterval)).(int) <= 0 {
		return fmt.Errorf("LoopInterval must be greater than 0")
	}

	if opts.Get(string(SimpleMarketMakerOptionMinSpread)).(float64) < 0 {
		return fmt.Errorf("MinSpread must be greater than or equal to 0")
	}

	if opts.Get(string(SimpleMarketMakerOptionRelistPriceTolerance)).(float64) < 0 {
		return fmt.Errorf("RelistPriceTolerance must be greater than or equal to 0")
	}
	return nil
}

func (this *SimpleMarketMaker) setLeverageToMax() {
	maxLeverage := this.controller.Exchange.MaxLeverage(this.SymbolCode.InstrumentType(), this.Symbol, 0)
	if err := this.controller.Exchange.SetLeverage(this.SymbolCode.InstrumentType(), this.Symbol, exchange.Cross, "", maxLeverage); err != nil {
		this.controller.AlertMsgf("设置杠杠率失败: %s", err)
	}
}

func (this *SimpleMarketMaker) Start() error {
	lock, ok := this.controller.handleMutex.Load(this.Symbol)
	if !ok {
		return fmt.Errorf("MarketMaker controller handle mutex improperly initialized")
	}
	lock.Lock()
	defer lock.Unlock()

	this.Status = Running

	if this.SymbolCode.InstrumentType().IsFuture() {
		// 杠杠率设到最大
		this.setLeverageToMax()

		if err := this.controller.Exchange.SetMarginMode(this.SymbolCode.InstrumentType(), this.Symbol, exchange.Cross); err != nil {
			if !strings.Contains(err.Error(), "No need to change") {
				this.controller.AlertMsgf("设置全仓模式失败: %s", err)
			}
		}
	}

	go this.Run()
	this.controller.storage.Save()
	return nil
}

func (this *SimpleMarketMaker) Run() error {
	this.controller.Exchange.SubscribeOrderBook(this.SymbolCode.InstrumentType(), this.Symbol)

	runCount := 0

	_, _, err := this.UpdateQty()
	if err != nil {
		this.controller.AlertMsgf("update holding qty failed: %s", err)
	}

	this.SyncOpenOrders()

	for {
		time.Sleep(time.Duration(this.LoopInterval) * time.Second)

		if this.Status != Running {
			return nil
		}

		if this.controller.IsClosed() {
			return nil
		}

		if runCount%10 == 0 {
			// 每 10 次打印一次状态
			this.Infof("Status:\n%s", RenderStatus([]MarketMaker{this}, false))
			balances, err := this.controller.GetAccountBalances(true)
			if err != nil {
				this.Errorf("get account balances failed, error: %s", err)
				continue
			}

			this.Infof("Holdings:\n%s", balances.RenderTotalCombined())
		}
		runCount++

		go this.UpdateQty()
		this.handleOrders()

		if runCount%10 == 0 && !this.UseRemoteOpenOrders {
			// 每 10 次同步一次远程订单，防止本地数据异常导致远程订单一直无法更新
			go this.SyncOpenOrders()
		}
	}
}

func (this *SimpleMarketMaker) handleOrders() {
	lock, ok := this.controller.handleMutex.Load(this.Symbol)
	if !ok {
		this.Errorf("handle orders mutex improperly initialized")
		return
	}
	lock.Lock()
	defer lock.Unlock()

	if this.Status != Running {
		return
	}

	defer this.checkLastSuccessTime()
	defer this.orderBookTimeCheck()

	err := this.UpdateMakerPrice()
	if err != nil {
		this.Errorf("get maker price failed: %s", err)
		return
	}

	buyOrders, sellOrders, err := this.PrepareOrders()
	if err != nil {
		this.Errorf("prepare orders failed: %s", err)
		return
	}

	this.convergeOrders(buyOrders, sellOrders)

	now := time.Now()
	this.LastHandleSuccessAt = &now
	this.controller.storage.Save()
}

func (this *SimpleMarketMaker) checkLastSuccessTime() {
	if this.Status != Running {
		return
	}

	if this.LastHandleSuccessAt != nil && this.LastHandleSuccessAt.Add(time.Minute*time.Duration(15)).Before(time.Now()) {
		this.controller.AlertMsgf("%s 已超过 15 分钟未成功执行完整检测流程", this.RefID)
	}
}

func (this *SimpleMarketMaker) orderBookTimeCheck() {
	now := time.Now()
	dataDelayed := false
	if this._orderBookTime == nil {
		dataDelayed = true
	} else if this._orderBookTimeDelay > time.Minute*1 {
		// 数据时间超过 1 分钟没有更新，标记为异常
		dataDelayed = true
	}

	if !dataDelayed {
		this._orderBookTimeErrorAt = nil
		return
	}

	if this._orderBookTimeErrorAt == nil {
		if dataDelayed {
			this._orderBookTimeErrorAt = &now
		}
	} else {
		if now.Sub(this._orderBookTimeErrorAt.Add(time.Minute*5)) > 0 {
			// 异常超过 5 分钟，发警报
			this.controller.AlertMsgf("%s 实时数据时间异常超过 5 分钟，将断开 ws 重连，最近数据时间: %s", this.RefID, utils.FormatShortTimeStr(this._orderBookTime, false))
			this.controller.Exchange.CloseWebsocket(false)
			this._orderBookTimeErrorAt = nil
		}
	}
}

func (this *SimpleMarketMaker) UpdateOrderBookTime(t *time.Time) {
	this._orderBookTime = t
	this._orderBookTimeDelay = time.Since(*t)
}

func (this *SimpleMarketMaker) convergeOrders(buyOrders []*PrepareOrder, sellOrders []*PrepareOrder) {
	if len(buyOrders) > 0 {
		this.highestBuyPrice = buyOrders[0].Price
	} else {
		this.highestBuyPrice = 0.0
	}

	if len(sellOrders) > 0 {
		this.lowestSellPrice = sellOrders[0].Price
	} else {
		this.lowestSellPrice = math.MaxFloat64
	}

	var openOrders []*exchange.Order
	if this.UseRemoteOpenOrders {
		openOrders = this.SyncOpenOrders()
	} else {
		openOrders = this.openOrders
	}

	// openOrders 排序: 买单按价格从高到低，卖单按价格从低到高
	sort.Slice(openOrders, func(i, j int) bool {
		if openOrders[i].Side == exchange.OrderSideBuy {
			return openOrders[i].Price > openOrders[j].Price
		}
		return openOrders[i].Price < openOrders[j].Price
	})

	buysMatched := 0
	sellsMatched := 0

	ordersToCreate := make([]*PrepareOrder, 0)
	ordersToCancel := make([]*exchange.Order, 0)

	if this.UseRemoteOpenOrders {
		// 使用远程订单，根据实际挂单情况调整
		for _, order := range openOrders {
			var desiredOrder *PrepareOrder
			if order.Side == exchange.OrderSideBuy {
				if buysMatched < len(buyOrders) {
					desiredOrder = buyOrders[buysMatched]
					buysMatched++
				}
			} else {
				if sellsMatched < len(sellOrders) {
					desiredOrder = sellOrders[sellsMatched]
					sellsMatched++
				}
			}

			if desiredOrder == nil {
				// 订单无匹配，取消订单
				ordersToCancel = append(ordersToCancel, order)
				continue
			}

			// 订单部分成交 或 价格偏差大于 RelistPriceTolerance，需要重新挂单
			if order.ExecQty > 0 ||
				(order.Price != desiredOrder.Price && math.Abs(desiredOrder.Price/order.Price-1) > this.RelistPriceTolerance) {
				this.Infof("relist order: %#v => %#v", order, desiredOrder)
				// 先取消，再重新挂单
				ordersToCancel = append(ordersToCancel, order)
				ordersToCreate = append(ordersToCreate, desiredOrder)
			}
		}
	} else {
		// 不使用远程订单，无法知道是否已成交，直接取消所有挂单
		ordersToCancel = openOrders
	}

	type cancelResult struct {
		order *exchange.Order
		err   error
	}

	// 并发取消订单
	if len(ordersToCancel) > 0 {
		cancelChan := make(chan cancelResult, len(ordersToCancel))
		for _, order := range ordersToCancel {
			go func(order *exchange.Order) {
				this.Infof("cancel order: %#v", order)
				err := this.controller.Exchange.CancelOrder(this.SymbolCode.InstrumentType(), order.Type, this.Symbol, order.OrderID)
				cancelChan <- cancelResult{order: order, err: err}
			}(order)
		}

		// 等待所有取消操作完成
		for i := 0; i < len(ordersToCancel); i++ {
			result := <-cancelChan
			if result.err != nil {
				if this.UseRemoteOpenOrders {
					this.controller.AlertMsgf("convergeOrders failed, cancel order failed: %s", result.err)
					return
				} else {
					// 不使用远程订单，取消失败则可能是订单已成交，忽略
					this.Errorf("cancel order failed: %s", result.err)
					continue
				}
			}
		}
	}

	for {
		if buysMatched < len(buyOrders) {
			order := buyOrders[buysMatched]
			ordersToCreate = append(ordersToCreate, order)
			buysMatched++
		} else {
			break
		}
	}

	for {
		if sellsMatched < len(sellOrders) {
			order := sellOrders[sellsMatched]
			ordersToCreate = append(ordersToCreate, order)
			sellsMatched++
		} else {
			break
		}
	}

	// orderToCreate 按 index 从低到高排序，保证最优价格优先创建
	sort.Slice(ordersToCreate, func(i, j int) bool {
		return ordersToCreate[i].Index < ordersToCreate[j].Index
	})

	type orderResult struct {
		order *exchange.Order
		err   error
		index int // 保存原始顺序
	}

	createOrder := func(order *PrepareOrder, index int, resultChan chan<- orderResult) {
		this.Infof("create order: %#v", order)
		exOrder, err := this.controller.Exchange.CreateOrder(exchange.CreateOrderArgs{
			InstrumentType: this.SymbolCode.InstrumentType(),
			Symbol:         this.Symbol,
			Side:           order.Side,
			Price:          order.Price,
			Qty:            order.Qty,
			Type:           exchange.Limit,
		})
		if exOrder != nil {
			// 订单创建时的持仓和浮动盈亏
			exOrder.SetFloat64(ExtKeyPosQty, this.HoldingQty)
			exOrder.SetFloat64(ExtKeyPNL, this.Report.PNL)
		}
		resultChan <- orderResult{order: exOrder, err: err, index: index}
	}

	resultChan := make(chan orderResult, len(ordersToCreate))
	newOrders := make([]*exchange.Order, len(ordersToCreate))

	// 前两个订单优先创建
	if len(ordersToCreate) > 0 {
		for i := 0; i < min(2, len(ordersToCreate)); i++ {
			go createOrder(ordersToCreate[i], i, resultChan)
		}
	}

	// 稍后创建剩余订单
	time.Sleep(10 * time.Millisecond) // 给优先订单一个小延迟
	if len(ordersToCreate) > 2 {
		for i := 2; i < len(ordersToCreate); i++ {
			go createOrder(ordersToCreate[i], i, resultChan)
		}
	}

	// 收集所有订单创建结果
	successCount := 0
	for i := 0; i < len(ordersToCreate); i++ {
		result := <-resultChan
		if result.err != nil {
			this.controller.AlertMsgf("convergeOrders failed, create order[%#v] failed: %s", ordersToCreate[result.index], result.err)
			continue
		}
		newOrders[result.index] = result.order
		successCount++
	}

	// 过滤掉空值（创建失败的订单），并保持顺序
	finalOrders := make([]*exchange.Order, 0, successCount)
	for _, order := range newOrders {
		if order != nil {
			finalOrders = append(finalOrders, order)
		}
	}

	if !this.UseRemoteOpenOrders {
		this.openOrders = finalOrders
	}

	this.controller.addMakerOrders(this.RefID, finalOrders)
}

func (this *SimpleMarketMaker) GetName() string {
	return "simple"
}

// 根据索引返回该侧的价格
func (this *SimpleMarketMaker) getPriceOffset(side exchange.OrderSide, index int) float64 {
	var startPrice float64

	// 根据买卖方向选择起始价格
	if side == exchange.OrderSideBuy {
		startPrice = this.startBuyPrice
	} else {
		startPrice = this.startSellPrice
	}

	if side == exchange.OrderSideBuy {
		index = -index
	}

	// 计算最终价格并确保符合最小价格单位
	price := startPrice * math.Pow(1+this.OrderPriceInterval, float64(index))
	price = this.controller.Exchange.RoundPrice(this.SymbolCode.InstrumentType(), this.Symbol, price)
	return price
}

func (this *SimpleMarketMaker) prepareOrder(side exchange.OrderSide, index int) *PrepareOrder {
	price := this.getPriceOffset(side, index)
	qty := this.OrderStartSize + (float64(index) * this.OrderStepSize)
	return &PrepareOrder{Price: price, Qty: qty, Side: side, Index: index}
}

func (this *SimpleMarketMaker) PrepareOrders() (buyOrders []*PrepareOrder, sellOrders []*PrepareOrder, err error) {
	buyOrders = make([]*PrepareOrder, 0)
	sellOrders = make([]*PrepareOrder, 0)
	prepareBuyPos := this.HoldingQty
	prepareSellPos := this.HoldingQty
	for i := 0; i < this.OrderPairs; i++ {
		if prepareBuyPos < this.MaxPosition {
			order := this.prepareOrder(exchange.OrderSideBuy, i)
			if ok, err := this.checkOrderQty(order.Qty, order.Price); err != nil {
				this.Debugf("check order qty failed: %s", err)
				continue
			} else if !ok {
				continue
			}
			buyOrders = append(buyOrders, order)
			prepareBuyPos += order.Qty
		}

		if prepareSellPos > this.MinPosition {
			order := this.prepareOrder(exchange.OrderSideSell, i)
			if this.SymbolCode.IsSpot() && prepareSellPos < order.Qty {
				order.Qty = this.controller.Exchange.FloorQty(this.SymbolCode.InstrumentType(), this.Symbol, prepareSellPos-this.MinPosition)
				prepareSellPos = this.MinPosition
			} else {
				prepareSellPos -= order.Qty
			}
			if ok, err := this.checkOrderQty(order.Qty, order.Price); err != nil {
				this.Debugf("check order qty failed: %s", err)
				continue
			} else if !ok {
				continue
			}
			sellOrders = append(sellOrders, order)
		}
	}
	return
}

// 检查订单数量是否满足最小下单价值&数量
func (this *SimpleMarketMaker) checkOrderQty(qty float64, price float64) (valid bool, err error) {
	minNotional := 0.0
	minQty := 0.0
	instrument, err := this.controller.Exchange.GetInstrument(this.SymbolCode.InstrumentType(), this.Symbol)
	if err != nil {
		return false, fmt.Errorf("get instrument failed: %s", err)
	}
	minNotional = instrument.MinNotional
	minQty = instrument.MinSize

	if qty < minQty {
		return false, nil
	}

	notional := this.Qty2Size(price, qty)
	if notional < minNotional {
		return false, nil
	}
	return true, nil
}

func (this *SimpleMarketMaker) GetOptions() *option.Options {
	return this.Options
}

func (this *SimpleMarketMaker) LoadOptions() error {
	err := this.Options.Load(getSimpleMarketMakerOptionDefinitions()...)
	if err != nil {
		return fmt.Errorf("load options failed: %s", err)
	}
	this.UpdateOptions(this.Options)
	return nil
}

func (this *SimpleMarketMaker) UpdateOptions(opts *option.Options) {
	this.Options = opts

	this.OrderPairs = this.Options.Get(string(SimpleMarketMakerOptionOrderPairs)).(int)
	this.OrderPriceInterval = this.Options.Get(string(SimpleMarketMakerOptionOrderPriceInterval)).(float64)
	this.OrderStartSize = this.Options.Get(string(SimpleMarketMakerOptionOrderStartSize)).(float64)
	this.OrderStepSize = this.Options.Get(string(SimpleMarketMakerOptionOrderStepSize)).(float64)
	this.MinPosition = this.Options.Get(string(SimpleMarketMakerOptionMinPosition)).(float64)
	this.MaxPosition = this.Options.Get(string(SimpleMarketMakerOptionMaxPosition)).(float64)
	this.LoopInterval = this.Options.Get(string(SimpleMarketMakerOptionLoopInterval)).(int)
	this.MinSpread = this.Options.Get(string(SimpleMarketMakerOptionMinSpread)).(float64)
	this.RelistPriceTolerance = this.Options.Get(string(SimpleMarketMakerOptionRelistPriceTolerance)).(float64)
	this.UseRemoteOpenOrders = this.Options.Get(string(SimpleMarketMakerOptionUseRemoteOpenOrders)).(bool)
}

func (this *SimpleMarketMaker) UpdateMakerPrice() error {
	orderBook, err := this.controller.Exchange.GetOrderBook(this.SymbolCode.InstrumentType(), this.Symbol)
	if err != nil {
		return fmt.Errorf("get order book failed: %s", err)
	}

	// this.Debugf("order book: %#v", orderBook)

	if len(orderBook.Bids) == 0 || len(orderBook.Asks) == 0 {
		return fmt.Errorf("order book is empty")
	}

	this.UpdateOrderBookTime(orderBook.Time)

	instrument, err := this.controller.Exchange.GetInstrument(this.SymbolCode.InstrumentType(), this.Symbol)
	if err != nil {
		return fmt.Errorf("get instrument failed: %s", err)
	}

	orderBookBuy := orderBook.Bids[0].Price
	orderBookSell := orderBook.Asks[0].Price

	// 设置基准买卖价格，在当前市场买卖价格的基础上各偏移一个最小价格单位
	this.startBuyPrice = orderBookBuy + instrument.TickSize
	this.startSellPrice = orderBookSell - instrument.TickSize

	// 如果已经是市场最优价格，则不再偏移
	if orderBookBuy == this.highestBuyPrice {
		this.startBuyPrice = orderBookBuy
		this.Debugf("got the best buy price: %f, no need to offset", orderBookBuy)
	}
	if orderBookSell == this.lowestSellPrice {
		this.startSellPrice = orderBookSell
		this.Debugf("got the best sell price: %f, no need to offset", orderBookSell)
	}

	if (this.startBuyPrice * (1 + this.MinSpread)) > this.startSellPrice {
		this.Infof("Spread too small, adjusting: buy price %f, sell price %f", this.startBuyPrice, this.startSellPrice)
		this.startBuyPrice *= (1 - (this.MinSpread / 2))
		this.startSellPrice *= (1 + (this.MinSpread / 2))
	}

	this.startBuyPrice = this.controller.Exchange.RoundPrice(this.SymbolCode.InstrumentType(), this.Symbol, this.startBuyPrice)
	this.startSellPrice = this.controller.Exchange.RoundPrice(this.SymbolCode.InstrumentType(), this.Symbol, this.startSellPrice)

	this.Debugf("order book: buy: %f, sell: %f", orderBookBuy, orderBookSell)
	this.Debugf("start buy price: %f, start sell price: %f", this.startBuyPrice, this.startSellPrice)

	if this.getPriceOffset(exchange.OrderSideBuy, 0) >= orderBookSell || this.getPriceOffset(exchange.OrderSideSell, 0) <= orderBookBuy {
		return fmt.Errorf("数据检查异常, 交易所买价 %f, 卖价 %f, 起始买价 %f, 起始卖价 %f. ", orderBookBuy, orderBookSell, this.getPriceOffset(exchange.OrderSideBuy, 0), this.getPriceOffset(exchange.OrderSideSell, 0))
	}

	return nil
}

func (this *SimpleMarketMaker) RenderPreview() string {
	t := NewTable()
	rows := [][]string{}
	rows = append(rows, []string{"SymbolCode", this.SymbolCode.String()})
	rows = append(rows, []string{"Symbol", this.Symbol})
	options := this.GetOptions()
	rows = append(rows, options.Rows()...)
	rows = append(rows, []string{"---", ""})
	rows = append(rows, []string{"Init Price", this.controller.Exchange.FormatPrice(this.SymbolCode.InstrumentType(), this.Symbol, this.InitPrice)})
	for _, row := range rows {
		t.AddRow(row)
	}
	return fmt.Sprintf("```%s```", t.Render())
}

func (this *SimpleMarketMaker) SyncOpenOrders() []*exchange.Order {
	remoteOrders, err := this.controller.Exchange.GetOpenOrders(this.SymbolCode.InstrumentType(), exchange.Limit, this.Symbol)
	if err != nil {
		this.controller.AlertMsgf("sync open orders failed: %s", err)
		return nil
	}
	this.openOrders = remoteOrders
	return remoteOrders
}

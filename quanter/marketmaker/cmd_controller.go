package marketmaker

import (
	"strings"

	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
)

// 设置配置项命令

type MarketMakerCommand struct {
	command.Command
	controller *MarketMakerController
}

// 需组合使用才能调用到 PrePrepare, PreDo
type CheckExchangeCommand MarketMakerCommand

func (this *CheckExchangeCommand) PrePrepare() bool {
	return this.controller.CheckExchangeReady(true)
}

func (this *CheckExchangeCommand) PreDo() bool {
	return this.controller.CheckExchangeReady(true)
}

// 启动程序
type LaunchMarketMakerCommand MarketMakerCommand

func NewLaunchMarketMakerCommand(controller *MarketMakerController) *LaunchMarketMakerCommand {
	cmd := &LaunchMarketMakerCommand{
		Command: command.Command{
			Name:            "launch",
			Instruction:     "`.launch Password GoogleAuthCode ` 启动程序",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          3,
			AuthcodePos:     -1, // launch 命令不能在解密主密码前校验 authcode，所以把 authCode 当成是普通参数处理，跳过统一的校验流程
			Sensitive:       true,
		},
		controller: controller,
	}
	return cmd
}

func (this *LaunchMarketMakerCommand) Do() bool {
	password := this.Args[0]
	authCode := this.Args[1]
	success := this.controller.Launch(password, authCode)
	if success {
		this.controller.Init()
		this.SendMsgf("启动成功。")
	}
	return true
}

// 当前程序运行状态
type StatusMarketMakerCommand MarketMakerCommand

func NewStatusMarketMakerCommand(controller *MarketMakerController) *StatusMarketMakerCommand {
	cmd := &StatusMarketMakerCommand{
		Command: command.Command{
			Name:            "status",
			Alias:           []string{"s"},
			Instruction:     "`.status(+) all/SymbolCode/ID1,ID2…[可选]` 查看运行状态",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *StatusMarketMakerCommand) Do() bool {
	var mms []MarketMaker
	if len(this.Args) == 0 || strings.EqualFold(this.Args[0], "all") {
		mms = this.controller.MarketMakers
	} else if len(this.Args) > 0 {
		isSymbolCode := false
		if symbolCode, err := exchange.NewSymbolCode(this.Args[0], ""); err == nil {
			for _, mm := range this.controller.MarketMakers {
				if mm.GetSymbolCode().Code == symbolCode.Code {
					mms = append(mms, mm)
					isSymbolCode = true
				}
			}
		}

		if !isSymbolCode {
			ids := strings.Split(this.Args[0], ",")
			for _, id := range ids {
				gs := this.controller.GetMarketMakersByID(id, false)
				if len(gs) == 0 {
					this.ErrorMsgf("没有找到网格 %s", id)
					continue
				}
				mms = append(mms, gs...)
			}
		}
	}

	this.SendMsgf("```%s```", RenderStatus(mms, this.IsMore()))
	return true
}

var SimpleStatusHeader = []string{"Symbol", "ID", "Status", "Buys", "Sells", "Traded Value", "Position", "PNL", "Fee", "Holdings", "Curr. Price", "CreateTime", "RunningTime"}
var DetailStatusHeader = []string{"Symbol", "ID", "Status", "Buys", "Sells", "Traded Value", "Position", "PNL", "Fee", "Holdings", "Curr. Price", "Create Time", "Running Time", "Options", "Init Price", "Finish Time", "Finish Reason"}

func RenderStatus(marketMakers []MarketMaker, detail bool) string {
	t := NewTable()
	if detail {
		t.SetHeader(DetailStatusHeader)
	} else {
		t.SetHeader(SimpleStatusHeader)
	}

	for _, mm := range marketMakers {
		if !mm.IsFinished() {
			row := mm.GetStatusRow(false, detail)
			t.AddRow(row)
		}
	}
	if len(t.Rows) == 1 {
		return "无运行中的网格"
	}
	return t.Render()
}

// 打印程序运行参数
type ConfigMarketMakerCommand MarketMakerCommand

func NewConfigMarketMakerCommand(controller *MarketMakerController) *ConfigMarketMakerCommand {
	cmd := &ConfigMarketMakerCommand{
		Command: command.Command{
			Name:            "config",
			Alias:           []string{"cfg"},
			Instruction:     "`.config` 查看当前运行参数",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ConfigMarketMakerCommand) Do() bool {
	this.SendMsgf("Build: %s, 运行参数\n```%s```", this.controller.BuildInfo(), this.controller.Config.ToTable())
	return true
}

type SetConfigMarketMakerCommand MarketMakerCommand

func NewSetConfigMarketMakerCommand(controller *MarketMakerController) *SetConfigMarketMakerCommand {
	cmd := &SetConfigMarketMakerCommand{
		Command: command.Command{
			Name:            "setConfig",
			Alias:           []string{"sc"},
			Instruction:     "`.setConfig field1=value1,field2=value2` 设置配置 Field 字段值",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
		},
		controller: controller,
	}
	return cmd
}

func (this *SetConfigMarketMakerCommand) Prepare() bool {
	configStr := this.Args[0]
	if !strings.Contains(configStr, "=") {
		this.ErrorMsgf("请按 field=value 设置配置。")
		return false
	}

	if _, correctedConfigStr, err := baseconfig.ParseConfigsFromString(this.controller.Config, configStr); err != nil {
		this.ErrorMsgf("解析配置错误：%s", err)
		return false
	} else {
		this.SendMsgf("设置配置 %s", correctedConfigStr)
		return true
	}
}

func (this *SetConfigMarketMakerCommand) Do() bool {
	configStr := this.Args[0]
	if correctedConfigStr, err := this.controller.SaveConfig(configStr); err != nil {
		this.ErrorMsgf("设置配置错误：%s", err)
		return false
	} else {
		this.SendMsgf("设置配置成功：%s", correctedConfigStr)
		return true
	}
}

type SetCoinsMarketMakerCommand struct {
	CheckExchangeCommand
}

func NewSetCoinsMarketMakerCommand(controller *MarketMakerController) *SetCoinsMarketMakerCommand {
	cmd := &SetCoinsMarketMakerCommand{
		CheckExchangeCommand: CheckExchangeCommand{
			Command: command.Command{
				Name:            "setCoins",
				Instruction:     "`.setCoins coin1,coin2` 设置允许的币种",
				RequiresConfirm: true,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *SetCoinsMarketMakerCommand) Prepare() bool {
	coins := strings.Split(this.Args[0], ",")
	if _, invalidCoins, err := this.controller.Exchange.CheckValidCoins(coins); err != nil {
		this.ErrorMsgf("检查币种请求错误：%s", err)
		return false
	} else {
		if len(invalidCoins) != 0 {
			this.ErrorMsgf("交易所 %s 不支持币种 %s ", this.controller.Exchange.GetName(), strings.Join(invalidCoins, ","))
			return false
		}
		return true
	}
}

func (this *SetCoinsMarketMakerCommand) Do() bool {
	coins := strings.Split(this.Args[0], ",")
	if validCoins, invalidCoins, err := this.controller.Exchange.CheckValidCoins(coins); err != nil {
		this.ErrorMsgf("检查币种请求错误：%s", err)
		return false
	} else {
		if len(invalidCoins) != 0 {
			this.ErrorMsgf("交易所 %s 不支持币种 %s ", this.controller.Exchange.GetName(), strings.Join(invalidCoins, ","))
			return false
		}
		this.controller.Config.AllowedSymbolPrefixs = validCoins
		this.controller.Config.Save()
		this.SendMsgf("允许的币种已设为：%s，可以通过 `.config` 查询。", strings.Join(validCoins, ","))
		return true
	}
}

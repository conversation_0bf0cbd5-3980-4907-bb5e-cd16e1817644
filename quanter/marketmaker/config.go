package marketmaker

import (
	"fmt"
	"os"
	"path"
	"strings"

	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/utils"

	"github.com/spf13/viper"
	"github.com/stevedomin/termtable"
)

var ConfigWithoutDiffHeader = []string{"Config", "Value"}

type MarketMakerConfig struct {
	SpotFeeRate             float64
	FutureFeeRate           float64
	OrderBookDelayTolerance int // 允许的实时数据最大延迟时间，单位秒
}

type MarketMakerControllerConfig struct {
	baseconfig.BaseConfig
	MarketMakerConfig
	controller *MarketMakerController
}

var MarketMakerConfigOptions = &baseconfig.ConfigOptions{
	"SpotFeeRate":             {IsPercent: true},
	"FutureFeeRate":           {IsPercent: true},
	"OrderBookDelayTolerance": {Unit: "s"},
}

func SetupMarketMakerControllerConfig(controller *MarketMakerController) (*MarketMakerControllerConfig, error) {
	if config, err := LoadConfig(controller.ConfigPath, controller.ID); err != nil {
		return nil, err
	} else {
		config.controller = controller
		controller.Config = config
		return config, nil
	}
}

func LoadConfig(configPath, controllerID string) (config *MarketMakerControllerConfig, er error) {
	config = &MarketMakerControllerConfig{}
	configFilePath := path.Join(configPath, controllerID+".maker.toml")
	if _, err := os.Stat(configFilePath); !os.IsNotExist(err) {
		viper.SetConfigName(controllerID + ".maker")
		viper.AddConfigPath(configPath)
		err := viper.ReadInConfig()
		if err == nil {
			err := viper.Unmarshal(&config)
			if err != nil {
				zlog.Errorf("[%s] unable to decode marketmaker config into struct, %v", controllerID, err)
				return nil, err
			}
			if err := config.Validate(); err != nil {
				zlog.Errorf("[%s] marketmaker config validate error: %s, %v", controllerID, err, config)
				return nil, err
			}
			zlog.Infof("[%s] load config from local file", controllerID)
			return config, nil
		} else { // 如果配置文件存在，但是解析出现错误，报错退出
			zlog.Panicf("[%s] read config file error：%s", controllerID, err)
			return nil, fmt.Errorf("read config error: %v", err)
		}
	} else {
		return nil, err
	}
}

func (g *MarketMakerControllerConfig) snapshotMarketMakerConfig() *MarketMakerConfig {
	s := &MarketMakerConfig{
		SpotFeeRate:             g.SpotFeeRate,
		FutureFeeRate:           g.FutureFeeRate,
		OrderBookDelayTolerance: g.OrderBookDelayTolerance,
	}
	return s
}

func (g *MarketMakerControllerConfig) Validate() error {
	if err := g.BaseConfig.Validate(); err != nil {
		return err
	}
	return g.MarketMakerConfig.Validate()
}

func (g *MarketMakerControllerConfig) GetConfigOptions() *baseconfig.ConfigOptions {
	return MarketMakerConfigOptions
}

func (g *MarketMakerControllerConfig) GetChildValidatableConfig() baseconfig.ValidatableConfig {
	return &g.MarketMakerConfig
}

func (g *MarketMakerControllerConfig) GetBaseConfig() *baseconfig.BaseConfig {
	return &g.BaseConfig
}

func (g *MarketMakerControllerConfig) ToTomlContent(hideSecret bool) string {
	result := "[BaseConfig]\n" +
		g.BaseConfig.ToTomlContent(hideSecret) +
		"\n[MarketMakerConfig]\n" +
		fmt.Sprintf("SpotFeeRate = %v\n", g.SpotFeeRate) +
		fmt.Sprintf("FutureFeeRate = %v\n", g.FutureFeeRate) +
		fmt.Sprintf("OrderBookDelayTolerance = %v\n", g.OrderBookDelayTolerance)
	return result
}

func NewTable() *termtable.Table {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	return t
}

func (this *MarketMakerControllerConfig) ToTable() string {
	apiKey := this.ApiKey
	apiSecret := this.ApiSecret
	apiKey = utils.HideSecret(apiKey)
	apiSecret = utils.HideSecret(apiSecret)

	ts := NewTable()
	ts.SetHeader([]string{"AllowedSymbolPrefixs"})
	ts.AddRow([]string{strings.Join(this.AllowedSymbolPrefixs, ",")})

	t := NewTable()
	t.SetHeader(ConfigWithoutDiffHeader)
	debugStr := "false"
	if this.controller != nil && this.controller.Debug {
		debugStr = "true"
	}
	t.AddRow([]string{"Debug*", debugStr})
	t.AddRow([]string{"", ""})
	t.AddRow([]string{"ExchangeName", fmt.Sprintf(`%v`, this.ExchangeName)})
	t.AddRow([]string{"Host", fmt.Sprintf(`%v`, this.Host)})
	t.AddRow([]string{"ApiKey", fmt.Sprintf(`%v`, apiKey)})
	t.AddRow([]string{"ApiSecret", fmt.Sprintf(`%v`, apiSecret)})

	if exp, _ := this.controller.GetAPIExpireTime(); exp != nil {
		t.AddRow([]string{"ApiKeyExpire", utils.FormatShortTimeStr(exp, true)})
	}

	t.AddRow([]string{"ReleaseBinaryDirPath", fmt.Sprintf(`%v`, this.ReleaseBinaryDirPath)})
	t.AddRow([]string{"LogDirPath", fmt.Sprintf(`%v`, this.LogDirPath)})
	t.AddRow([]string{"IsTestnet", fmt.Sprintf(`%v`, this.IsTestnet)})
	t.AddRow([]string{"ProxyUrl", this.ProxyUrl})
	t.AddRow([]string{"ShowFutureQtyAsValue", fmt.Sprintf(`%v`, this.ShowFutureQtyAsValue)})
	t.AddRow([]string{"MinMarginRatio", fmt.Sprintf(`%v`, this.MinMarginRatio)})
	t.AddRow([]string{"EnableRealtimePrice", fmt.Sprintf(`%v`, this.EnableRealtimePrice)})
	t.AddRow([]string{"USDXSymbol", this.USDXSymbol})
	t.AddRow([]string{"MarginMode", string(this.MarginMode)})
	t.AddRow([]string{"-------------------------", ""})

	for _, row := range this.GetMarketMakerConfigRows() {
		t.AddRow(row)
	}
	return fmt.Sprintf("%s\n\n%s", ts.Render(), t.Render())
}

func (this *MarketMakerControllerConfig) GetMarketMakerConfigRows() (rows [][]string) {
	if baseConfigRows, err := baseconfig.GetTableRows(this.snapshotMarketMakerConfig()); err != nil {
		rows = append(rows, []string{"ERROR", fmt.Sprintf("[ERROR!>base config table error: %s]", err)})
		return
	} else {
		rows = append(rows, baseConfigRows...)
	}
	return
}

func (g *MarketMakerControllerConfig) SaveTo(configPath string, id string, overwrite bool) error {
	if configPath == "" {
		return fmt.Errorf("configPath is empty")
	}
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".maker.toml")
	if _, err := os.Stat(path); !os.IsNotExist(err) && !overwrite {
		return fmt.Errorf("[%s] %s already exist, can not overwrite", g.controller.ID, path)
	}
	if err := os.WriteFile(path, []byte(g.ToTomlContent(false)), 0755); err != nil {
		zlog.Errorf("[%s] write marketmaker config %s error: %v", g.controller.ID, id, err)
		return err
	}
	return nil
}

func (g *MarketMakerControllerConfig) Save() {
	err := g.SaveTo(g.controller.ConfigPath, g.controller.ID, true)
	if err != nil {
		g.controller.AlertMsgf("保存配置文件失败: %s", err)
	}
}

func (g *MarketMakerControllerConfig) Delete() {
	// 删除本地配置
	if err := os.Remove(path.Join(g.controller.ConfigPath, g.controller.ID+".maker.toml")); err != nil {
		g.controller.AlertMsgf("本地配置文件删除失败: %s", err)
		return
	}
}

func (g *MarketMakerControllerConfig) GetExchangeName() string {
	return g.ExchangeName
}

func (g *MarketMakerControllerConfig) GetHost() string {
	return g.Host
}

func (g *MarketMakerControllerConfig) GetApiKey() string {
	return g.ApiKey
}

func (g *MarketMakerControllerConfig) GetApiSecret() string {
	return g.ApiSecret
}

func (g *MarketMakerConfig) Validate() error {
	if g.OrderBookDelayTolerance <= 0 {
		g.OrderBookDelayTolerance = 10
	}
	return nil
}

func (g *MarketMakerConfig) GetConfigOptions() *baseconfig.ConfigOptions {
	return MarketMakerConfigOptions
}

func (g *MarketMakerConfig) GetChildValidatableConfig() baseconfig.ValidatableConfig {
	return nil
}

func (g *MarketMakerConfig) GetBaseConfig() *baseconfig.BaseConfig {
	return nil
}

package marketmaker

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path"
	"sync"

	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

type MarketMakerStorage struct {
	base.BaseStorage
	MarketMakers       []MarketMaker `json:"-"`
	SimpleMarketMakers []*SimpleMarketMaker
}

func SetupMarketMakerStroage(controller *MarketMakerController) {
	storage := &MarketMakerStorage{
		BaseStorage: base.BaseStorage{
			Controller: controller,
		},
	}
	// 紧接着赋值给 Storager，防止没有赋值前调用 Storager 相关的方法导致崩溃
	storage.Storager = storage

	if ok := storage.ReadFrom(controller.ConfigPath, controller.ID); !ok {
		storage.Assets = map[int64]float64{}
		storage.MarketMakers = []MarketMaker{}
		storage.SimpleMarketMakers = []*SimpleMarketMaker{}
	} else {
		for _, smm := range storage.SimpleMarketMakers {
			smm.MarketMaker = smm
			smm.controller = controller
			smm.LoadOptions()
			storage.MarketMakers = append(storage.MarketMakers, smm)
			controller.orderMutex.LoadOrStore(smm.GetRefID(), &sync.Mutex{})
		}
	}

	if storage.Assets == nil {
		storage.Assets = map[int64]float64{}
	}

	loadOrders(controller, controller.ConfigPath, controller.ID)

	controller.storage = storage
}

func (s *MarketMakerStorage) ReadFrom(configPath, id string) bool {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".maker_storage")
	f, _ := os.OpenFile(path, os.O_RDWR|os.O_CREATE, 0755)
	defer f.Close()
	file, err := io.ReadAll(f)
	if err != nil {
		zlog.Errorf("[%s] read storage file error: %s", s.Controller.GetID(), err.Error())
		return false
	}
	zlog.Infof("[%s] local storage file (%s) loaded", s.Controller.GetID(), path)
	err = json.Unmarshal(file, s)
	if err != nil {
		zlog.Debugf("[%s] json.Unmarshal err: %#v", s.Controller.GetID(), err)
		zlog.Errorf("[%s] read json file error", s.Controller.GetID())
		return false
	}
	return true
}

func loadOrders(controller *MarketMakerController, configPath, id string) bool {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	ordersPath := path.Join(configPath, id+".maker_orders")
	ordersFile, err := os.OpenFile(ordersPath, os.O_RDWR|os.O_CREATE, 0755)
	if err != nil {
		zlog.Errorf("[%s] open orders file error: %s", controller.GetID(), err)
		return false
	}
	defer ordersFile.Close()
	orders, err := io.ReadAll(ordersFile)
	if err != nil {
		zlog.Errorf("[%s] read orders file error: %s", controller.GetID(), err)
		return false
	}

	ordersMap := map[string][]*exchange.Order{}
	err = json.Unmarshal(orders, &ordersMap)
	if err != nil {
		zlog.Errorf("[%s] json.Unmarshal orders error: %s", controller.GetID(), err)
		return false
	}

	for mmID, orders := range ordersMap {
		controller.loadMakerOrders(mmID, orders)
	}

	return true
}

// 写入本地存储到文件
func (s *MarketMakerStorage) SaveTo(configPath string, id string, overwrite bool) error {
	mmIDs := []string{}
	s.SimpleMarketMakers = []*SimpleMarketMaker{}
	for _, mm := range s.MarketMakers {
		if mm.GetName() == "simple" {
			rmm := mm.(*SimpleMarketMaker)
			s.SimpleMarketMakers = append(s.SimpleMarketMakers, rmm)
		}
		mmIDs = append(mmIDs, mm.GetRefID())
	}

	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	storagePath := path.Join(configPath, id+".maker_storage")
	if _, err := os.Stat(storagePath); !os.IsNotExist(err) && !overwrite {
		return fmt.Errorf("[%s] %s already exist, can not overwrite", s.Controller.GetID(), storagePath)
	}
	// Wrap marshal in a single recover
	// marshal 的过程中，如果其中的子结构中的 slices 可能发生变化，导致 crash
	// 因此，需要手工 recover 忽略错误
	data, err := func() (data []byte, err error) {
		defer func() {
			if r := recover(); r != nil {
				err = fmt.Errorf("[%s] panic during storage marshal: %v", s.Controller.GetID(), r)
			}
		}()

		data, err = json.MarshalIndent(s, "", "    ")
		if err != nil {
			return nil, fmt.Errorf("marshal error: %s", err)
		}
		return data, nil
	}()

	if err != nil {
		zlog.Errorf("[%s] marshal storage error: %s", s.Controller.GetID(), err)
		return err
	}

	if err := os.WriteFile(storagePath, data, 0755); err != nil {
		zlog.Errorf("[%s] save storage to file, error: %s", s.Controller.GetID(), err)
		return err
	}

	ordersPath := path.Join(configPath, id+".maker_orders")
	if _, err := os.Stat(ordersPath); !os.IsNotExist(err) && !overwrite {
		return fmt.Errorf("[%s] %s already exist, can not overwrite", s.Controller.GetID(), ordersPath)
	}

	ctrl := s.Controller.(*MarketMakerController)
	orders := map[string][]*exchange.Order{}
	for _, mmID := range mmIDs {
		orders[mmID] = ctrl.getMakerOrders(mmID)
	}
	// Wrap marshal in a single recover
	// marshal 的过程中，如果其中的子结构中的 slices 可能发生变化，导致 crash
	// 因此，需要手工 recover 忽略错误
	data, err = func() (data []byte, err error) {
		defer func() {
			if r := recover(); r != nil {
				err = fmt.Errorf("[%s] panic during storage marshal: %v", s.Controller.GetID(), r)
			}
		}()

		data, err = json.MarshalIndent(orders, "", "    ")
		if err != nil {
			return nil, fmt.Errorf("marshal error: %s", err)
		}
		return data, nil
	}()

	if err != nil {
		zlog.Errorf("[%s] marshal orders storage error: %s", s.Controller.GetID(), err)
		return err
	}

	if err := os.WriteFile(ordersPath, data, 0755); err != nil {
		zlog.Errorf("[%s] save orders storage to file, error: %s", s.Controller.GetID(), err)
		return err
	}
	return nil
}

package marketmaker

import (
	"fmt"
	"math"
	"sync"
	"time"

	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type BaseMarketMaker struct {
	MarketMaker `json:"-"`

	controller *MarketMakerController
	Options    *option.Options

	SymbolCode          *exchange.SymbolCode
	Symbol              string
	RefID               string
	InitPrice           float64
	Name                string
	Status              Status
	CreateTime          *time.Time
	FinishTime          *time.Time
	FinishReason        FinishReason
	LastHandleSuccessAt *time.Time
	Report              *MarketMakerReport
	HoldingQty          float64
	QuoteQty            float64

	_orderBookTime        *time.Time
	_orderBookTimeDelay   time.Duration
	_orderBookTimeErrorAt *time.Time
}

func (this *BaseMarketMaker) GetOptions() *option.Options {
	return this.Options
}

func (this *BaseMarketMaker) GetRefID() string {
	return this.RefID
}

func (this *BaseMarketMaker) GetStatus() Status {
	return this.Status
}

func (this *BaseMarketMaker) GetSymbol() string {
	return this.Symbol
}

func (this *BaseMarketMaker) GetSymbolCode() *exchange.SymbolCode {
	return this.SymbolCode
}

func (this *BaseMarketMaker) GetHoldingQty() float64 {
	return this.HoldingQty
}

func (this *BaseMarketMaker) Debugf(format string, args ...any) {
	symbolRefID := fmt.Sprintf("%s-%s", this.SymbolCode.Code, this.RefID)
	this.controller.Debugf("(%s) %s", symbolRefID, fmt.Sprintf(format, args...))
}

func (this *BaseMarketMaker) Infof(format string, args ...any) {
	symbolRefID := fmt.Sprintf("%s-%s", this.SymbolCode.Code, this.RefID)
	this.controller.Infof("(%s) %s", symbolRefID, fmt.Sprintf(format, args...))
}

func (this *BaseMarketMaker) Warnf(format string, args ...any) {
	symbolRefID := fmt.Sprintf("%s-%s", this.SymbolCode.Code, this.RefID)
	this.controller.Warnf("(%s) %s", symbolRefID, fmt.Sprintf(format, args...))
}

func (this *BaseMarketMaker) Errorf(format string, args ...any) {
	symbolRefID := fmt.Sprintf("%s-%s", this.SymbolCode.Code, this.RefID)
	this.controller.Errorf("(%s) %s", symbolRefID, fmt.Sprintf(format, args...))
}

func NewMarketMaker(controller *MarketMakerController, options *CreateMarketMakerOptions, copyMM MarketMaker) (mm MarketMaker, er error) {
	if copyMM == nil {
		now := time.Now()
		baseMM := BaseMarketMaker{
			controller: controller,
			RefID:      exchange.NewRandomID(),
			SymbolCode: options.SymbolCode,
			Symbol:     options.Symbol,
			CreateTime: &now,
			Name:       options.Name,
			Report:     &MarketMakerReport{},
		}

		if p, err := controller.Exchange.GetLastPrice(baseMM.SymbolCode.InstrumentType(), baseMM.Symbol, false); err != nil {
			er = err
			return
		} else {
			baseMM.InitPrice = p
		}

		if options.Name == "simple" {
			mm, er = NewSimpleMarketMaker(baseMM, options.OptStr)
		} else {
			return nil, fmt.Errorf("unsupported strategy: %s", options.Name)
		}
	} else {
		mm = copyMM
	}

	controller.handleMutex.LoadOrStore(mm.GetSymbol(), &sync.Mutex{})
	controller.orderMutex.LoadOrStore(mm.GetRefID(), &sync.Mutex{})
	controller.MakerOrders.LoadOrStore(mm.GetRefID(), []*exchange.Order{})

	return
}

func (this *BaseMarketMaker) Qty2Size(price float64, qty float64) (size float64) {
	if this.SymbolCode.IsSpot() {
		return price * qty
	}

	if size, err := this.controller.Exchange.Qty2Size(this.SymbolCode.InstrumentType(), this.Symbol, price, qty); err != nil {
		this.controller.ErrorMsgf("convert qty 2 size error: %s", err)
		return 0
	} else {
		return size
	}
}

func (this *BaseMarketMaker) CalcPrice(qty, size float64) (price float64) {
	if this.SymbolCode.IsSpot() {
		return size / qty
	}

	if price, err := this.controller.Exchange.CalcPrice(this.SymbolCode.InstrumentType(), this.Symbol, qty, size); err != nil {
		this.controller.ErrorMsgf("calc price error: %s", err)
		return 0
	} else {
		return price
	}
}

func (this *BaseMarketMaker) CancelOpenOrders() error {
	openOrders, err := this.controller.Exchange.GetOpenOrders(this.SymbolCode.InstrumentType(), exchange.Limit, this.Symbol)
	if err != nil {
		return fmt.Errorf("get open orders failed: %s", err)
	}

	for _, order := range openOrders {
		if err := this.controller.Exchange.CancelOrder(this.SymbolCode.InstrumentType(), exchange.Limit, this.Symbol, order.OrderID); err != nil {
			return fmt.Errorf("cancel order[%s] failed: %s", order.OrderID, err)
		}
	}

	return nil
}

func (this *BaseMarketMaker) Close(reason FinishReason, sellBaseCoin bool, force bool) {
	if this.Status == Closed {
		return
	}

	lock, ok := this.controller.handleMutex.Load(this.Symbol)
	if !ok {
		this.Errorf("handle orders mutex improperly initialized")
		return
	}
	lock.Lock()
	defer lock.Unlock()

	if sellBaseCoin {
		this.controller.SendMsgf("执行停止做市机 %s，将依次执行取消订单、平仓操作", this.RefID)
	}

	if err := this.CancelOpenOrders(); err != nil {
		this.controller.ErrorMsgf("取消订单失败: %s", err)
		if !force {
			this.controller.SendMsgf("停止操作已中断")
			return
		}
	}

	if sellBaseCoin {
		holdingQtyBefore := 0.0

		coinOrSymbol := this.SymbolCode.Coin()
		if this.SymbolCode.IsFuture() {
			coinOrSymbol = this.Symbol
		}
		pos, _, err := this.controller.Exchange.GetHoldingQty(this.SymbolCode.InstrumentType(), coinOrSymbol)
		if err != nil {
			this.controller.ErrorMsgf("获取当前持有 %s 数量失败: %s", this.Symbol, err)
		} else {
			holdingQtyBefore = pos
		}

		if pos != 0 {
			order, err := this.ClosePosition()
			if err != nil {
				if err == exchange.ErrNoPosition {
					this.controller.WarnMsgf("没有持仓，无需平仓")
				} else {
					this.controller.ErrorMsgf("清仓 %s 失败: %s", this.Symbol, err)
					if !force {
						this.controller.SendMsgf("停止操作已中断，请人工确认是否成功平仓")
						return
					}
				}
			} else {
				holdingQtyAfter := 0.0
				if total, _, err := this.controller.Exchange.GetHoldingQty(this.SymbolCode.InstrumentType(), coinOrSymbol); err != nil {
					this.controller.ErrorMsgf("获取当前持有 %s 数量失败: %s", this.Symbol, err)
				} else {
					holdingQtyAfter = total
				}

				t := NewTable()
				t.SetHeader([]string{"Direction", "Exe. Price", "Exe. Qty", "Holding Before", "Holding After"})
				t.AddRow([]string{
					string(order.Side),
					this.controller.Exchange.FormatPrice(this.SymbolCode.InstrumentType(), this.Symbol, order.ExecPrice),
					this.controller.Exchange.FormatQty(this.SymbolCode.InstrumentType(), this.Symbol, order.ExecQty),
					this.controller.Exchange.FormatQty(this.SymbolCode.InstrumentType(), this.Symbol, holdingQtyBefore),
					this.controller.Exchange.FormatQty(this.SymbolCode.InstrumentType(), this.Symbol, holdingQtyAfter),
				})
				this.controller.SendMsgf("做市机平仓成功:\n```%s```", t.Render())
			}
		}
	}

	this.Finish(reason)
	this.controller.SendMsgf("做市机 %s 已停止。", this.RefID)
}

func (this *BaseMarketMaker) Pause() {
	if this.Status != Running {
		this.controller.WarnMsgf("做市机 %s 不是运行状态，无法暂停。", this.RefID)
		return
	}

	lock, ok := this.controller.handleMutex.Load(this.Symbol)
	if !ok {
		this.Errorf("handle orders mutex improperly initialized")
		return
	}
	lock.Lock()
	defer lock.Unlock()

	if err := this.CancelOpenOrders(); err != nil {
		this.controller.ErrorMsgf("取消订单失败: %s", err)
	}

	// 期货平仓
	if this.SymbolCode.IsFuture() {
		if _, err := this.ClosePosition(); err != nil {
			if err == exchange.ErrNoPosition {
				this.controller.WarnMsgf("没有持仓，无需平仓")
			} else {
				this.controller.ErrorMsgf("平仓失败: %s", err)
			}
		}
	}

	this.Status = Paused
	this.controller.storage.Save()
	this.controller.SendMsgf("暂停做市机 %s", this.RefID)
}

func (this *BaseMarketMaker) Resume() {
	if this.Status != Paused {
		this.controller.WarnMsgf("做市机 %s 不是暂停状态，无法恢复。", this.RefID)
		return
	}

	this.Status = Running

	go this.Run()

	this.controller.storage.Save()
	this.controller.SendMsgf("恢复做市机 %s", this.RefID)
}

func (this *BaseMarketMaker) ClosePosition() (*exchange.Order, error) {
	coinOrSymbol := this.SymbolCode.Coin()
	if this.SymbolCode.IsFuture() {
		coinOrSymbol = this.Symbol
	}
	pos, _, err := this.controller.Exchange.GetHoldingQty(this.SymbolCode.InstrumentType(), coinOrSymbol)
	if err != nil {
		return nil, fmt.Errorf("get holding qty error: %s", err)
	}

	if pos == 0 {
		return nil, exchange.ErrNoPosition
	}

	args := exchange.CreateOrderArgs{
		InstrumentType: this.SymbolCode.InstrumentType(),
		Symbol:         this.Symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCash,
		Qty:            pos,
		TimeInForce:    exchange.GTC,
		Side:           exchange.OrderSideSell,
	}
	if this.SymbolCode.IsFuture() {
		args.TradeMode = exchange.TradeModeCross
		args.ReduceOnly = true
	}
	if pos < 0 {
		args.Qty = -pos
		args.Side = exchange.OrderSideBuy
	}
	order, err := this.controller.Exchange.CreateOrder(args)
	if err != nil {
		return nil, err
	}

	orderID := order.OrderID

	order, err = this.controller.Exchange.GetOrderByOrig(*order)
	if err != nil {
		this.controller.ErrorMsgf("[%s] 查询平仓订单[%s]失败: %s", this.Symbol, orderID, err)
		return nil, err
	}

	this.controller.addMakerOrders(this.RefID, []*exchange.Order{order})

	return order, nil
}

func (this *BaseMarketMaker) Finish(reason FinishReason) {
	nowTime := time.Now()
	this.FinishTime = &nowTime
	this.FinishReason = reason
	this.Status = Closed
	for i, mm := range this.controller.MarketMakers {
		if mm.GetRefID() == this.RefID {
			this.controller.MarketMakers = append(this.controller.MarketMakers[:i], this.controller.MarketMakers[i+1:]...) // 删除结束 grid
			break
		}
	}

	this.controller.storage.Save()

	go func() {
		time.Sleep(time.Second * 2)
		this.QueryOrders()
		this.UpdateReport()
	}()
}

// 查询本地记录的订单状态并更新
func (this *BaseMarketMaker) QueryOrders() {
	openOrders, err := this.controller.Exchange.GetOpenOrders(this.SymbolCode.InstrumentType(), exchange.Limit, this.Symbol)
	if err != nil {
		this.Errorf("get open orders failed: %s", err)
		return
	}

	openOrderIDs := make([]string, 0)
	for _, order := range openOrders {
		openOrderIDs = append(openOrderIDs, order.OrderID)
	}

	orders := this.controller.getMakerOrders(this.RefID)
	for _, order := range orders {
		if utils.SliceContains(openOrderIDs, order.OrderID) {
			continue
		}

		if !order.IsOpen() {
			continue
		}

		exOrder, err := this.controller.Exchange.GetOrderByOrig(*order)
		if err != nil {
			this.Errorf("failed to get order by orig: %s", err)
			continue
		}
		if exOrder.Status == order.Status && exOrder.ExecQty == order.ExecQty {
			continue
		}
		order.Status = exOrder.Status
		order.ExecQty = exOrder.ExecQty
		order.ExecPrice = exOrder.ExecPrice
		order.UpdateTime = exOrder.UpdateTime

		this.controller.storage.Save()
	}
}

func (this *BaseMarketMaker) UpdateReport() {
	// 结束超过 1h 不再更新
	if this.Status == Closed && this.FinishTime.Add(time.Hour).Before(time.Now()) {
		return
	}

	report := &MarketMakerReport{}

	buyQty := 0.0
	sellQty := 0.0
	buySize := 0.0
	sellSize := 0.0

	orders := this.controller.getMakerOrders(this.RefID)
	for _, order := range orders {
		if !order.IsFilled() {
			continue
		}

		value := this.Qty2Size(order.ExecPrice, order.ExecQty)
		report.TradedValue += value

		if order.Side == exchange.OrderSideBuy {
			report.BuyFilledCount++
			buyQty += order.ExecQty
			buySize += value
		} else {
			report.SaleFilledCount++
			sellQty += order.ExecQty
			sellSize += value
		}
	}

	// 做市收益 = 已平仓订单（买单数量 == 卖单数量）的收益
	// 持仓收益 = 当前持仓的浮动盈亏
	// 总收益 = 做市收益 + 持仓收益

	pairedQty := math.Min(buyQty, sellQty)
	if pairedQty > 0 {
		avgBuyPrice := this.CalcPrice(buyQty, buySize)
		avgSellPrice := this.CalcPrice(sellQty, sellSize)
		report.MarketMakerPNL = this.Qty2Size(avgSellPrice, pairedQty) - this.Qty2Size(avgBuyPrice, pairedQty)
	}

	lastPrice, err := this.controller.Exchange.GetLastPrice(this.SymbolCode.InstrumentType(), this.Symbol, true)
	if err != nil {
		this.Errorf("get last price failed: %s", err)
		return
	}

	posQty := buyQty - sellQty
	if posQty > 0 {
		avgBuyPrice := this.CalcPrice(buyQty, buySize)
		report.HoldingPNL = this.Qty2Size(lastPrice, posQty) - this.Qty2Size(avgBuyPrice, posQty)
	} else if posQty < 0 {
		avgSellPrice := this.CalcPrice(sellQty, sellSize)
		report.HoldingPNL = this.Qty2Size(avgSellPrice, -posQty) - this.Qty2Size(lastPrice, -posQty)
	}
	report.PosQty = posQty

	report.PNL = report.MarketMakerPNL + report.HoldingPNL

	fee := 0.0
	if this.SymbolCode.IsSpot() {
		fee = report.TradedValue * this.controller.Config.SpotFeeRate
	} else {
		fee = report.TradedValue * this.controller.Config.FutureFeeRate
	}
	report.PNL -= fee
	report.Fee -= fee

	this.Report = report
	this.controller.storage.Save()
}

func (this *BaseMarketMaker) IsFinished() bool {
	return this.FinishTime != nil
}

func (this *BaseMarketMaker) GetQuoteSymbol() string {
	return this.controller.Exchange.GetSymbolCodeQuote(this.SymbolCode)
}

func (this *BaseMarketMaker) UpdateQty() (holdingQty float64, quoteQty float64, err error) {
	holdings, err := this.controller.Exchange.GetAccountHoldings([]exchange.InstrumentType{this.SymbolCode.InstrumentType()})
	if err != nil {
		return 0, 0, fmt.Errorf("get account holdings failed: %s", err)
	}
	coinOrSymbol := this.SymbolCode.Coin()
	if this.SymbolCode.IsFuture() {
		coinOrSymbol = this.Symbol
	}
	for _, h := range holdings {
		if h.CoinOrSymbol == coinOrSymbol {
			holdingQty = h.Total
		}
		if h.CoinOrSymbol == this.GetQuoteSymbol() {
			quoteQty = h.Total
		}
	}
	this.HoldingQty = holdingQty
	this.QuoteQty = quoteQty
	this.Debugf("updated qty: holdingQty: %s, quoteQty: %s", this.controller.Exchange.FormatQty(this.SymbolCode.InstrumentType(), this.Symbol, this.HoldingQty), this.controller.Exchange.FormatQty(this.SymbolCode.InstrumentType(), this.GetQuoteSymbol(), this.QuoteQty))
	return this.HoldingQty, this.QuoteQty, nil
}

func (this *BaseMarketMaker) GetStatusRow(withControllerName bool, detail bool) []string {
	row := []string{}
	if withControllerName {
		row = append(row, this.controller.ID)
	}
	row = append(row, this.SymbolCode.Code)
	row = append(row, this.RefID)
	row = append(row, string(this.Status))
	row = append(row, fmt.Sprintf("%d", this.Report.BuyFilledCount))
	row = append(row, fmt.Sprintf("%d", this.Report.SaleFilledCount))
	row = append(row, utils.FormatNum(this.Report.TradedValue, 2))
	row = append(row, this.controller.Exchange.FormatQty(this.SymbolCode.InstrumentType(), this.Symbol, this.Report.PosQty))
	row = append(row, utils.FormatNum(this.Report.PNL, 2))
	row = append(row, utils.FormatNum(this.Report.Fee, 2))

	holdingQty := fmt.Sprintf("%s %s", this.controller.Exchange.FormatQty(this.SymbolCode.InstrumentType(), this.Symbol, this.HoldingQty), this.SymbolCode.Coin())
	if this.SymbolCode.IsSpot() {
		quoteQty := fmt.Sprintf("%s %s", this.controller.Exchange.FormatQty(this.SymbolCode.InstrumentType(), this.GetQuoteSymbol(), this.QuoteQty), this.GetQuoteSymbol())
		holdingQty = fmt.Sprintf("%s / %s", holdingQty, quoteQty)
	}
	row = append(row, holdingQty)
	lastPrice := "--"
	lp, err := this.controller.Exchange.GetLastPrice(this.SymbolCode.InstrumentType(), this.Symbol, true)
	if err == nil {
		lastPrice = this.controller.Exchange.FormatPrice(this.SymbolCode.InstrumentType(), this.Symbol, lp)
	}
	row = append(row, lastPrice)

	row = append(row, utils.FormatShortTimeStr(this.CreateTime, false))
	row = append(row, time.Since(*this.CreateTime).String())

	if detail {
		row = append(row, this.GetOptions().String())
		row = append(row, this.controller.Exchange.FormatPrice(this.SymbolCode.InstrumentType(), this.Symbol, this.InitPrice))
		if this.FinishTime != nil {
			row = append(row, utils.FormatShortTimeStr(this.FinishTime, false))
			row = append(row, string(this.FinishReason))
		} else {
			row = append(row, "-")
			row = append(row, "-")
		}
	}

	return row
}

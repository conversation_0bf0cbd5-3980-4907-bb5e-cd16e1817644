package marketmaker

import (
	"fmt"
	"strings"

	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type CreateMarketMakerCommand struct {
	CheckExchangeCommand
	marketmaker MarketMaker
}

func NewCreateMarketMakerCommand(controller *MarketMakerController) *CreateMarketMakerCommand {
	cmd := &CreateMarketMakerCommand{
		CheckExchangeCommand: CheckExchangeCommand{
			Command: command.Command{
				Name:            "createMarketMaker",
				Alias:           []string{"cmm"},
				Instruction:     "`.createMarketMaker SymbolCode StrategyName Options` 新增做市机 e.g. .cmm ETH-- simple OrderPairs=100,OrderPriceInterval=0.005,OrderStartSize=1,OrderStepSize=0.5,MinPosition=0,MaxPosition=10",
				RequiresConfirm: true,
				ArgMin:          3,
				ArgMax:          3,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *CreateMarketMakerCommand) Prepare() bool {
	this.marketmaker = nil

	var symbolCode *exchange.SymbolCode
	symbol := ""
	symbolCodeStr := this.Args[0]
	if code, err := this.controller.NewSymbolCode(symbolCodeStr); err != nil {
		this.ErrorMsgf("解析品种代码错误，error: %s", err)
		return false
	} else {
		symbolCode = code
	}
	if !this.controller.CheckSymbolPrefixAllowed(symbolCode.Coin()) {
		this.ErrorMsgf("不支持品种 (%s)，仅允许：(%s)", symbolCode.Coin(), utils.SliceStringJoin(this.controller.Config.AllowedSymbolPrefixs, ", ", false))
		return false
	}
	instrumentType := symbolCode.InstrumentType()
	if instrumentType == exchange.Spot {
		if spotSymbol, err := this.controller.Exchange.TranslateSymbolCodeToSpotSymbol(symbolCode); err != nil {
			this.ErrorMsgf("交易所中没有品种代码对应的现货，error: %s", err)
			return false
		} else {
			symbol = spotSymbol
		}
	} else if exchange.SliceContains([]exchange.InstrumentType{
		exchange.USDXMarginedFutures,
	}, instrumentType) {
		if futureSymbol, err := this.controller.Exchange.TranslateSymbolCodeToFutureSymbol(symbolCode); err != nil {
			this.ErrorMsgf("交易所中没有品种代码对应的合约，error: %s", err)
			return false
		} else {
			symbol = futureSymbol
		}
	} else {
		this.ErrorMsgf("不支持品种代码：%s", symbolCodeStr)
		return false
	}

	options := &CreateMarketMakerOptions{
		SymbolCode: symbolCode,
		Symbol:     symbol,
		Name:       this.Args[1],
		OptStr:     this.Args[2],
	}

	var err error
	if this.marketmaker, err = NewMarketMaker(this.controller, options, nil); err != nil {
		this.ErrorMsgf("创建做市机失败，error: %s", err)
		return false
	}

	previewStr := this.marketmaker.RenderPreview()
	if len(previewStr) > command.SLACK_MESSAGE_LIMIT {
		this.SendFileMessage(fmt.Sprintf("MarketMaker Preview %s", this.marketmaker.GetRefID()), previewStr, "")
	} else {
		this.SendMsgf("MarketMaker Preview\n%s", previewStr)
	}

	return true
}

func (this *CreateMarketMakerCommand) Do() bool {
	this.controller.MarketMakers = append(this.controller.MarketMakers, this.marketmaker)
	this.controller.storage.MarketMakers = append(this.controller.storage.MarketMakers, this.marketmaker)
	this.controller.storage.Save()
	this.marketmaker.Start()
	this.SendMsgf("做市机 %s 启动成功。", this.marketmaker.GetRefID())
	return true
}

type CloseMakerCommand struct {
	CheckExchangeCommand
	marketmakers []MarketMaker
	sell         bool
	force        bool
}

func NewCloseMakerCommand(controller *MarketMakerController) *CloseMakerCommand {
	cmd := &CloseMakerCommand{
		CheckExchangeCommand: CheckExchangeCommand{
			Command: command.Command{
				Name:            "close",
				Alias:           []string{"c"},
				Instruction:     "`.close MakerID1,MakerID2... sell/noSell[可选] force[可选]` 停止某个做市机，并卖出所有币，force 时忽略错误直接停止",
				RequiresConfirm: true,
				ArgMin:          1,
				ArgMax:          3,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *CloseMakerCommand) Prepare() bool {
	this.sell = true
	if len(this.Args) >= 2 && strings.EqualFold(this.Args[1], "nosell") {
		this.sell = false
	}

	this.force = false
	if len(this.Args) == 3 && strings.EqualFold(this.Args[2], "force") {
		this.force = true
	}

	this.marketmakers = []MarketMaker{}
	ids := strings.Split(this.Args[0], ",")
	for _, id := range ids {
		marketmaker := this.controller.GetMarketMakerByID(id, false)
		if marketmaker == nil {
			this.ErrorMsgf("没有找到做市机 %s", id)
			return false
		}
		this.marketmakers = append(this.marketmakers, marketmaker)
	}

	if len(this.marketmakers) == 0 {
		this.ErrorMsgf("没有找到做市机 %s", this.Args[0])
		return false
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Symbol", "Qty", "Sell", "Force"})
	for _, marketmaker := range this.marketmakers {
		row := []string{}
		row = append(row, marketmaker.GetRefID())
		row = append(row, marketmaker.GetSymbolCode().Code)
		row = append(row, fmt.Sprintf("%f", marketmaker.GetHoldingQty()))

		if this.sell {
			row = append(row, "Yes")
		} else {
			row = append(row, "")
		}
		if this.force {
			row = append(row, "Yes")
		} else {
			row = append(row, "")
		}
		t.AddRow(row)
	}
	this.SendMsgf("```%s```", t.Render())
	return true
}

func (this *CloseMakerCommand) Do() bool {
	for _, marketmaker := range this.marketmakers {
		marketmaker.Close(FinishReasonManual, this.sell, this.force)
	}
	return true
}

type PauseMakerCommand struct {
	CheckExchangeCommand
	marketmakers []MarketMaker
}

func NewPauseMakerCommand(controller *MarketMakerController) *PauseMakerCommand {
	cmd := &PauseMakerCommand{
		CheckExchangeCommand: CheckExchangeCommand{
			Command: command.Command{
				Name:            "pause",
				Instruction:     "`.pause MakerID1,MakerID2...` 暂停某个做市机，取消订单，期货平仓，现货不卖出",
				RequiresConfirm: true,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *PauseMakerCommand) Prepare() bool {
	this.marketmakers = []MarketMaker{}
	ids := strings.Split(this.Args[0], ",")
	for _, id := range ids {
		marketmaker := this.controller.GetMarketMakerByID(id, false)
		if marketmaker == nil {
			this.ErrorMsgf("没有找到做市机 %s", id)
			return false
		}
		this.marketmakers = append(this.marketmakers, marketmaker)
	}

	if len(this.marketmakers) == 0 {
		this.ErrorMsgf("没有找到做市机 %s", this.Args[0])
		return false
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Symbol", "Qty"})
	for _, marketmaker := range this.marketmakers {
		row := []string{}
		row = append(row, marketmaker.GetRefID())
		row = append(row, marketmaker.GetSymbolCode().Code)
		row = append(row, fmt.Sprintf("%f", marketmaker.GetHoldingQty()))
		t.AddRow(row)
	}
	this.SendMsgf("```%s```", t.Render())
	return true
}

func (this *PauseMakerCommand) Do() bool {
	for _, marketmaker := range this.marketmakers {
		marketmaker.Pause()
	}
	return true
}

type ResumeMakerCommand struct {
	command.Command
	controller   *MarketMakerController
	marketmakers []MarketMaker
}

func NewResumeMakerCommand(controller *MarketMakerController) *ResumeMakerCommand {
	cmd := &ResumeMakerCommand{
		Command: command.Command{
			Name:            "resume",
			Instruction:     "`.resume MakerID1,MakerID2...` 恢复某个做市机",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ResumeMakerCommand) Prepare() bool {
	this.marketmakers = []MarketMaker{}
	ids := strings.Split(this.Args[0], ",")
	for _, id := range ids {
		marketmaker := this.controller.GetMarketMakerByID(id, false)
		if marketmaker == nil {
			this.ErrorMsgf("没有找到做市机 %s", id)
			return false
		}
		this.marketmakers = append(this.marketmakers, marketmaker)
	}

	if len(this.marketmakers) == 0 {
		this.ErrorMsgf("没有找到做市机 %s", this.Args[0])
		return false
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Symbol", "Qty"})
	for _, marketmaker := range this.marketmakers {
		row := []string{}
		row = append(row, marketmaker.GetRefID())
		row = append(row, marketmaker.GetSymbolCode().Code)
		row = append(row, fmt.Sprintf("%f", marketmaker.GetHoldingQty()))
		t.AddRow(row)
	}
	this.SendMsgf("```%s```", t.Render())
	return true
}

func (this *ResumeMakerCommand) Do() bool {
	for _, marketmaker := range this.marketmakers {
		marketmaker.Resume()
	}
	return true
}

type UpdateOptionsCommand struct {
	CheckExchangeCommand
	marketmaker MarketMaker
	opts        *option.Options
}

func NewUpdateOptionsCommand(controller *MarketMakerController) *UpdateOptionsCommand {
	cmd := &UpdateOptionsCommand{
		CheckExchangeCommand: CheckExchangeCommand{
			Command: command.Command{
				Name:            "updateOptions",
				Alias:           []string{"uo"},
				Instruction:     "`.updateOptions MakerID Options` 更新做市机配置项",
				RequiresConfirm: true,
				ArgMin:          2,
				ArgMax:          2,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *UpdateOptionsCommand) Prepare() bool {
	this.marketmaker = nil
	this.opts = nil
	makerID := this.Args[0]
	optionsStr := this.Args[1]

	marketmaker := this.controller.GetMarketMakerByID(makerID, false)
	if marketmaker == nil {
		this.ErrorMsgf("没有找到做市机 %s", makerID)
		return false
	}
	this.marketmaker = marketmaker

	opts := marketmaker.GetOptions().Copy()
	updatedNames, err := opts.UpdateFromString(optionsStr)
	if err != nil {
		this.ErrorMsgf("解析选项错误，error: %s", err)
		return false
	}

	if err := marketmaker.ValidateOptions(opts); err != nil {
		this.ErrorMsgf("选项验证失败，error: %s", err)
		return false
	}

	this.SendMsgf("更新配置项：\n```%s```", marketmaker.GetOptions().DiffToTable(opts, updatedNames))

	this.opts = opts
	return true
}

func (this *UpdateOptionsCommand) Do() bool {
	this.marketmaker.UpdateOptions(this.opts)
	this.controller.storage.Save()
	this.SendMsgf("做市机 %s 选项已更新。", this.marketmaker.GetRefID())
	return true
}

package marketmaker

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/hyperliquid"
	"github.com/wizhodl/quanter/messenger"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"
	"golang.org/x/exp/rand"
)

type MarketMakerController struct {
	base.BaseController

	Config       *MarketMakerControllerConfig
	MarketMakers []MarketMaker

	storage     *MarketMakerStorage
	handleMutex *xsync.MapOf[string, *sync.Mutex]

	MakerOrders *xsync.MapOf[string, []*exchange.Order]
	orderMutex  *xsync.MapOf[string, *sync.Mutex]
}

func NewMarketMakerController(id string, debug bool, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath string, parentMessenger *messenger.SlackMessenger, managerID string) (*MarketMakerController, error) {
	controller := &MarketMakerController{
		BaseController: base.BaseController{
			BaseResponder: command.NewBaseResponder(debug, commitHash, buildTime, id, nil, ""),
			ID:            id,
			RefID:         exchange.NewRandomID(),
			ConfigPath:    configPath,
		},
		handleMutex: xsync.NewMapOf[*sync.Mutex](),
		MakerOrders: xsync.NewMapOf[[]*exchange.Order](),
		orderMutex:  xsync.NewMapOf[*sync.Mutex](),
	}
	controller.Controllable = controller

	controller.Setup(debug, parentMessenger, managerID)

	config, err := SetupMarketMakerControllerConfig(controller)
	if err != nil {
		return nil, err
	}
	if logDirPath != "" {
		config.LogDirPath = logDirPath
	}

	SetupMarketMakerStroage(controller)

	controller.AddCommands([]command.Commander{
		NewStatusMarketMakerCommand(controller),
		NewCreateMarketMakerCommand(controller),
		NewUpdateOptionsCommand(controller),
		NewCloseMakerCommand(controller),
		NewPauseMakerCommand(controller),
		NewResumeMakerCommand(controller),
		NewConfigMarketMakerCommand(controller),
		NewSetConfigMarketMakerCommand(controller),
		NewSetCoinsMarketMakerCommand(controller),
		cmds.NewHoldingsCommand(controller),
		cmds.NewAssetsCommand(controller, controller.storage),
		cmds.NewMuteCommand(),
		cmds.NewDebugCommand(),
		cmds.NewLogCommand(controller.Config.LogDirPath, controller.GetLogFilename()),
		cmds.NewDownloadLogCommand(controller.Config.LogDirPath, controller.GetLogFilename()),
		cmds.NewDownloadStorageCommand(controller.ConfigPath, fmt.Sprintf("%s.marketmaker_storage", controller.ID)),
		cmds.NewPriceTriggersCommand(controller),
		cmds.NewPriceWatchCommand(controller),
		cmds.NewStackTraceCommand(),
		cmds.NewDeleteErrorsCommand(),
	})

	if controller.Standalone() {
		// 注册 commander 要在
		// 根据命令行参数更新 config 中的部分字段
		if releaseBinaryDirPath != "" {
			config.ReleaseBinaryDirPath = releaseBinaryDirPath
		}
		// 初始化时检查 ReleaseBinaryDir 是否可以写，以免后续 releases 相关命令每次都要检查
		if utils.CheckReleaseBinaryDirPath(controller.Config.ReleaseBinaryDirPath) {
			controller.AddCommands([]command.Commander{
				cmds.NewReleaseCommand(controller.Config.ReleaseBinaryDirPath, controller.ID),
				cmds.NewListVersionCommand(controller.Config.ReleaseBinaryDirPath, controller.ID),
				cmds.NewUseVersionCommand(controller.Config.ReleaseBinaryDirPath, controller.ID),
			})
		}
	}

	// 尝试以 Debug 模式启动，如果不能以 Debug 模式启动，会要求以 .launch 模式启动
	debugLaunched := controller.TryLaunchWithDebug()
	if debugLaunched {
		go controller.Init()
	} else {
		controller.AddCommands([]command.Commander{NewLaunchMarketMakerCommand(controller)})
	}

	controller.Messenger.SetSensitiveCommands(controller.GetCommandProcessor().SensitiveCommands)

	return controller, nil
}

func (this *MarketMakerController) Init() {
	apiSecret := this.GetApiSecret()
	withdrawApiSecret := this.GetWithdrawApiSecret()
	var backOff backoff.BackOff
	if this.IsExchange(exchange.CTP) {
		backOff = backoff.NewConstantBackOff(time.Second * 5)
	} else {
		exp := backoff.NewExponentialBackOff()
		exp.InitialInterval = time.Second * 10
		exp.MaxInterval = time.Minute * 5
		exp.MaxElapsedTime = 0 // It never stops if MaxElapsedTime == 0.
		backOff = exp
	}
	alerted := false
	backoff.Retry(func() error {
		if this.IsClosed() {
			return nil
		}
		err := this.InitAPI(apiSecret, withdrawApiSecret)
		if err != nil {
			this.Warnf("init api failed, error: %s", err)
			if !alerted { // 警报一次
				this.ErrorMsgf("初始化 API 错误，稍后将自动重试。错误信息：%s", err)
				alerted = true
			}
		} else {
			if err := exchange.TestTranslateSymbolCode(this.Exchange); err != nil {
				this.ErrorMsgf("symbol code 测试不通过: %s", err)
			}
			this.SendMsgf("交易机 %s 初始化 API 成功", this.ID)
		}
		return err
	}, backOff)

	if this.IsClosed() {
		return
	}

	this.MarketMakers = []MarketMaker{}

	for _, mm := range this.storage.MarketMakers {
		if mm.GetStatus() != Closed {
			if newMM, err := NewMarketMaker(this, nil, mm); err == nil {
				this.MarketMakers = append(this.MarketMakers, newMM)
				go newMM.Run()
			} else {
				this.ErrorMsgf("初始化 %s(%s) 失败: %s", mm.GetSymbolCode().Code, mm.GetRefID(), err)
			}
		}
	}
}

func (this *MarketMakerController) marginUpdatedCallback(userMargin *exchange.UserMargin, currency string) {
}

var (
	orderLastUpdateTime = xsync.NewMapOf[time.Time]()
	orderUpdateMutex    = xsync.NewMapOf[*sync.Mutex]()
	orderUpdatedTimer   *time.Timer
)

func (this *MarketMakerController) orderUpdatedCallback(order *exchange.Order) {
	symbol := order.Symbol
	this.Debugf("order updated callback: %s", symbol)
	mutex, _ := orderUpdateMutex.LoadOrStore(symbol, &sync.Mutex{})
	mutex.Lock()
	defer mutex.Unlock()

	lastTime, _ := orderLastUpdateTime.LoadOrStore(symbol, time.Now().Add(-10*time.Second))
	timeSinceLastUpdate := time.Since(lastTime)

	if timeSinceLastUpdate < 2*time.Second {
		if orderUpdatedTimer != nil {
			orderUpdatedTimer.Stop()
		}
		orderUpdatedTimer = time.AfterFunc(2*time.Second-timeSinceLastUpdate, func() {
			this.doOrderUpdate(symbol)
		})
		return
	}

	// 如果距离上次更新已超过2秒，直接执行更新
	this.doOrderUpdate(symbol)
}

func (this *MarketMakerController) doOrderUpdate(symbol string) {
	orderLastUpdateTime.Store(symbol, time.Now())

	// for _, mm := range this.MarketMakers {
	// 	if mm.GetSymbol() == symbol && mm.GetStatus() == Running {
	// 		mm.UpdateQty()
	// 	}
	// }
}

func (this *MarketMakerController) orderBookCallback(orderBook *exchange.OrderBook) {
	if orderBook.Time != nil {
		delay := time.Since(*orderBook.Time)
		// 概率打印 orderBook 延迟
		if rand.Intn(100) > 95 {
			this.Infof("orderBookTime: %s %s(%s)", orderBook.Symbol, utils.FormatShortTimeStr(orderBook.Time, false), delay)
		}

		if delay > time.Second*time.Duration(this.Config.OrderBookDelayTolerance) {
			this.Errorf("orderBookTime delay too long: symbol: %s, delay: %s", orderBook.Symbol, delay)

			for _, mm := range this.MarketMakers {
				if mm.GetSymbol() == orderBook.Symbol && mm.GetStatus() == Running {
					this.ErrorMsgf("当前实时数据延迟时间 %s 超过允许的最大延迟时间 %d 秒，将暂停运行 %s", delay, this.Config.OrderBookDelayTolerance, mm.GetRefID())
					mm.Pause()
				}
			}
		}
	}
}

func (this *MarketMakerController) priceTriggeredCallback(priceTrigger *exchange.PriceTrigger) {
	this.Infof("price triggered callback: %#v", priceTrigger)
	if priceTrigger.Source == "manual" {
		for _, p := range this.storage.PriceTriggers {
			if p.ID == priceTrigger.ID {
				p.Triggered = true
				p.TriggeredPrice = priceTrigger.TriggeredPrice
			}
		}

		direction := ">"
		if priceTrigger.Direction == exchange.TriggerDirectionLower {
			direction = "<"
		}
		this.SendMsgf("%s[%s] %s %v 已触发", priceTrigger.Symbol, priceTrigger.InstrumentType, direction, priceTrigger.Price)
		return
	}
}

func (this *MarketMakerController) InitAPI(apiSecret, withdrawApiSecret secrets.SecretString) error {
	opts := &exchange.Options{
		Host:                   this.Config.Host,
		ApiKey:                 this.Config.ApiKey,
		ApiSecret:              apiSecret,
		WithdrawApiKey:         this.Config.WithdrawApiKey,
		WithdrawApiSecret:      withdrawApiSecret,
		FixerKey:               this.Config.FixerKey,
		IsTestnet:              this.Config.IsTestnet,
		ProxyUrl:               this.Config.ProxyUrl,
		ControllerID:           this.ID,
		MarginUpdatedCallback:  this.marginUpdatedCallback,
		OrderUpdatedCallback:   this.orderUpdatedCallback,
		OrderBookCallback:      this.orderBookCallback,
		PriceTriggeredCallback: this.priceTriggeredCallback,
		DataPath:               this.ConfigPath,
	}

	var instrumentTypes []exchange.InstrumentType

	if this.Config.ExchangeName == exchange.Hyperliquid {
		client, err := hyperliquid.NewHyperliquid(opts)
		if err != nil {
			return fmt.Errorf("初始化 Hyperliquid 失败，error: %s", err)
		}
		instrumentTypes = []exchange.InstrumentType{exchange.Spot, exchange.USDXMarginedFutures}
		if _, err := client.GetAccountBalances(exchange.Spot); err != nil {
			return fmt.Errorf("获取余额失败，请检查 API 是否正确. error: %s", err)
		}
		this.Exchange = client

	} else {
		this.ErrorMsgf("交易所 %s 暂未支持", this.Config.ExchangeName)
		return fmt.Errorf("unsupported exchange (%s)", this.Config.ExchangeName)
	}

	if err := this.Exchange.CacheInstruments(true); err != nil {
		return fmt.Errorf("cache instruments error: %s", err)
	}

	this.Exchange.SetHttpDebug(this.Debug)
	go this.Exchange.ConnectWebsocket(instrumentTypes, nil)
	go this.Exchange.CheckPriceTriggerTimeLoop()

	return nil
}

// debug 状态运行 和 定时任务，有多次运行防呆机制
func (this *MarketMakerController) Run() {
	tasks := []base.CronTask{
		{
			Spec: "1 12 * * *",
			Cmd: func() {
				this.storage.RecordAssets()
				this.SendStatus()
				this.Exchange.CacheInstruments(true)
				this.cleanMakerOrders()
			},
		},

		{
			Spec: "*/1 * * * *",
			Cmd: func() {
				this.queryOrders()
				this.updateReports()
			},
		},
	}
	this.BaseController.Run(tasks)

	if this.Debug {
		go this.cleanMakerOrders()
	}
}

func (this *MarketMakerController) getOpenMarginSummary(onlyInsufficient bool) (result string) {
	return
}

func (this *MarketMakerController) SendStatus() {
	this.SendMsgf("做市机状态：\n```%s```", RenderStatus(this.MarketMakers, false))
}

func (this *MarketMakerController) Close() {
	this.BaseController.Close(func() {
		this.CloseAllMarketMakers(true, true)
	})
}

func (this *MarketMakerController) Exit() {
	this.Infof("exit with pausing all market makers")
	for _, mm := range this.MarketMakers {
		if mm.GetStatus() == Running {
			mm.Pause()
		}
	}
}

func (this *MarketMakerController) CloseAllMarketMakers(sellBaseCoin bool, force bool) {
	mmsToClose := []MarketMaker{}
	for _, mm := range this.MarketMakers {
		if mm.GetStatus() != Closed {
			mmsToClose = append(mmsToClose, mm)
		}
	}

	for _, mm := range mmsToClose {
		mm.Close(FinishReasonManual, sellBaseCoin, force)
	}
}

func (this *MarketMakerController) SaveConfigsTo(id string) error {
	if err := this.Config.SaveTo(this.ConfigPath, id, false); err != nil {
		return err
	}
	return nil
}

func (this *MarketMakerController) SaveStorageTo(id string) error {
	if err := this.storage.SaveTo(this.ConfigPath, id, true); err != nil {
		return err
	}
	return nil
}

func (this *MarketMakerController) DangerousDelete() {
	this.Close()
	this.Config.Delete()
}

func ParseNumOrPercentage(numOrPercentage string, percentOnly bool, allowNegative bool) (num float64, percent float64, er error) {
	percent = 0.0
	num = 0.0
	if result, isPercent, err := utils.ParseFloatOrPercentage(numOrPercentage, percentOnly, allowNegative); err == nil {
		if isPercent {
			percent = result
		} else {
			num = result
		}
	} else {
		er = err
	}
	return num, percent, er
}

func (this *MarketMakerController) GetInstrumentTypes() []exchange.InstrumentType {
	return this.Exchange.GetSupportedInstrumentTypes()
}

func (this *MarketMakerController) RenderAssets() string {
	return this.storage.RenderAssets()
}

func (this *MarketMakerController) GetReviewRows() [][]string {
	return this.Config.GetMarketMakerConfigRows()
}

func (this *MarketMakerController) GetBaseConfig() *baseconfig.BaseConfig {
	return &this.Config.BaseConfig
}

func (this *MarketMakerController) SaveConfig(configStr string) (correctedConfigStr string, er error) {
	if configPairs, _correctedConfigStr, err := baseconfig.SetConfigWithString(this.Config, configStr); err != nil {
		return "", err
	} else {
		correctedConfigStr = _correctedConfigStr
		for _, pair := range configPairs {
			if strings.EqualFold(pair.Field, "EnableRealtimePrice") {
				enable := strings.EqualFold(pair.Value, "true")
				this.Exchange.SetEnableRealtimePrice(enable)
			}

			if strings.EqualFold(pair.Field, "MarginMode") {
				for _, instrumentType := range this.GetInstrumentTypes() {
					if !this.SetAccountMarginMode(instrumentType, this.Config.MarginMode) {
						return "", fmt.Errorf("set account margin mode error")
					}
				}
			}
		}

		this.Config.Save()
		return correctedConfigStr, nil
	}
}

func (this *MarketMakerController) GetStorage() base.Storager {
	return this.storage
}

func (this *MarketMakerController) GetMarketMakersByID(idOrAlias string, includeFromStorage bool) (result []MarketMaker) {
	result = []MarketMaker{}

	mm := this.GetMarketMakerByID(idOrAlias, includeFromStorage)
	if mm != nil {
		result = append(result, mm)
	}
	return
}

func (this *MarketMakerController) GetMarketMakerByID(idOrAlias string, includeFromStorage bool) (result MarketMaker) {
	for _, mm := range this.MarketMakers {
		if strings.EqualFold(mm.GetRefID(), idOrAlias) {
			result = mm
		}
	}
	if result == nil && includeFromStorage {
		for _, mm := range this.storage.MarketMakers {
			if strings.EqualFold(mm.GetRefID(), idOrAlias) {
				result = mm
			}
		}
	}
	return
}

func (this *MarketMakerController) queryOrders() {
	for _, mm := range this.MarketMakers {
		mm.QueryOrders()
	}
}

func (this *MarketMakerController) updateReports() {
	for _, mm := range this.MarketMakers {
		mm.UpdateReport()
	}
}

func (this *MarketMakerController) loadMakerOrders(mmID string, orders []*exchange.Order) {
	this.orderMutex.LoadOrStore(mmID, &sync.Mutex{})
	this.MakerOrders.Store(mmID, orders)
}

func (this *MarketMakerController) addMakerOrders(mmID string, newOrders []*exchange.Order) {
	mutex, ok := this.orderMutex.Load(mmID)
	if !ok {
		this.Errorf("order mutex not found for mmID: %s", mmID)
		return
	}
	mutex.Lock()
	defer mutex.Unlock()

	orders, ok := this.MakerOrders.Load(mmID)
	if !ok {
		this.Errorf("orders not found for mmID: %s", mmID)
		return
	}
	orders = append(orders, newOrders...)
	this.MakerOrders.Store(mmID, orders)
}

func (this *MarketMakerController) getMakerOrders(mmID string) []*exchange.Order {
	mutex, ok := this.orderMutex.Load(mmID)
	if !ok {
		this.Errorf("order mutex not found for mmID: %s", mmID)
		return []*exchange.Order{}
	}
	mutex.Lock()
	defer mutex.Unlock()

	orders, ok := this.MakerOrders.Load(mmID)
	if !ok {
		this.Errorf("orders not found for mmID: %s", mmID)
		return []*exchange.Order{}
	}
	return orders
}

// 删除状态为 Cancelled 且更新时间超过 24 小时的订单
func (this *MarketMakerController) cleanMakerOrders() {
	for _, mm := range this.storage.MarketMakers {
		mmID := mm.GetRefID()
		mutex, ok := this.orderMutex.Load(mmID)
		if !ok {
			this.Errorf("order mutex not found for mmID: %s", mmID)
			return
		}
		mutex.Lock()
		defer mutex.Unlock()

		orders, ok := this.MakerOrders.Load(mmID)
		if !ok {
			this.Errorf("orders not found for mmID: %s", mmID)
			return
		}

		newOrders := []*exchange.Order{}
		for _, order := range orders {
			if order.Status == exchange.OrderStatusCancelled && time.Since(*order.UpdateTime) > 24*time.Hour {
				continue
			}
			newOrders = append(newOrders, order)
		}
		this.MakerOrders.Store(mmID, newOrders)
	}
}

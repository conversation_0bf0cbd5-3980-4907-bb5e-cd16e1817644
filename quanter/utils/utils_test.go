package utils

import (
	"fmt"
	"testing"
)

func TestParseTimeBeijing(t *testing.T) {
	times := []string{"2021-02-01T15:04:05", "2021-02-01T15:04", "21-02-01T15:04:05", "21-02-01T15:04", "02-01T15:04:05", "02-01T15:04", "2021-02-01 15:04:05", "2021-02-01 15:04", "21-02-01 15:04:05", "21-02-01 15:04", "02-01 15:04:05", "02-01 15:04"}
	for _, s := range times {
		fmt.Printf("%s, %s\n", s, ParseTimeBeijing(s))
	}
}

func TestParseDirAndFilename(t *testing.T) {
	dir, filename := ParseDirAndFilename("/a/b/c/d.txt", true)
	fmt.Printf("dir: %s, filename: %s\n", dir, filename)
	dir, filename = ParseDirAndFilename("/a/b/c/d.txt", false)
	fmt.Printf("dir: %s, filename: %s\n", dir, filename)
	dir, filename = ParseDirAndFilename("/a/b/c/d", true)
	fmt.Printf("dir: %s, filename: %s\n", dir, filename)
	dir, filename = ParseDirAndFilename("/a/b/c/d", false)
	fmt.Printf("dir: %s, filename: %s\n", dir, filename)
	dir, filename = ParseDirAndFilename("/a/b/c/d/", true)
	fmt.Printf("dir: %s, filename: %s\n", dir, filename)
	dir, filename = ParseDirAndFilename("/a/b/c/d/", false)
	fmt.Printf("dir: %s, filename: %s\n", dir, filename)

	dir, filename = ParseDirAndFilename("c:\\a\\b\\c\\d.txt", true)
	fmt.Printf("dir: %s, filename: %s\n", dir, filename)
	dir, filename = ParseDirAndFilename("c:\\a\\b\\c\\d.txt", false)
	fmt.Printf("dir: %s, filename: %s\n", dir, filename)
	dir, filename = ParseDirAndFilename("c:\\a\\b\\c\\d", true)
	fmt.Printf("dir: %s, filename: %s\n", dir, filename)
	dir, filename = ParseDirAndFilename("c:\\a\\b\\c\\d", false)
	fmt.Printf("dir: %s, filename: %s\n", dir, filename)
	dir, filename = ParseDirAndFilename("c:\\a\\b\\c\\d\\", true)
	fmt.Printf("dir: %s, filename: %s\n", dir, filename)

}

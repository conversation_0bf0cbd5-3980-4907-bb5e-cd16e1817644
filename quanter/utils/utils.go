package utils

import (
	"fmt"
	"io"
	"log"
	"math/rand"
	"os"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/stevedomin/termtable"
	"github.com/wizhodl/quanter/common/backscanner"
	"github.com/wizhodl/quanter/secrets"

	"github.com/shopspring/decimal"
)

func Unique[T comparable](slice []T) []T {
	uniqueMap := make(map[T]bool)
	for _, item := range slice {
		uniqueMap[item] = true
	}
	uniqueSlice := make([]T, 0, len(uniqueMap))
	for item := range uniqueMap {
		uniqueSlice = append(uniqueSlice, item)
	}
	return uniqueSlice
}

// UniqueAny 使用反射来比较元素的值，而不是使用指针地址
// 必须实现 Key() 方法，否则使用指针地址比较
func UniqueAny[T any](slice []T) []T {
	uniqueMap := make(map[interface{}]T)
	for _, item := range slice {
		var key interface{}

		// Use reflection to check if item is a pointer
		v := reflect.ValueOf(item)
		if v.Kind() == reflect.Ptr && !v.IsNil() {
			// If it's a pointer, try to use a Key() method if available
			if keyMethod := v.MethodByName("Key"); keyMethod.IsValid() {
				results := keyMethod.Call(nil)
				key = results[0].Interface()
			} else {
				// If no Key method, use the pointer as is
				key = item
			}
		} else {
			// For non-pointers, use the value directly
			key = item
		}

		uniqueMap[key] = item
	}

	// Build result slice
	result := make([]T, 0, len(uniqueMap))
	for _, v := range uniqueMap {
		result = append(result, v)
	}
	return result
}

func Unique2[T comparable](slice []T) []T {
	uniqueSlice := make([]T, 0, len(slice))
	for _, item := range slice {
		if !SliceContains(uniqueSlice, item) {
			uniqueSlice = append(uniqueSlice, item)
		}
	}
	return uniqueSlice
}

// timestamp 转化为字符串，如 1573444862 -> 2019-11-11T12:01:02+08:00
func TimestampToString(ts int64) string {
	return time.Unix(ts, 0).Format(time.RFC3339)
}

// 判断是否是一个过去的时间
func IsTimeExpired(t *time.Time) bool {
	if t == nil {
		return true
	}
	return t.Before(time.Now())
}

// 时间转换为北京时间字符串
func FormatToBeijingTimeStr(t time.Time) string {
	// loc, _ := time.LoadLocation("Asia/Shanghai")
	return t.In(time.FixedZone("CST", 8*60*60)).Format(time.RFC3339)
}

func FormatShortTimeStr(t *time.Time, withYear bool) string {
	if t == nil {
		return "-"
	}
	if t.Before(time.Unix(86400, 0)) {
		return "-"
	} else {
		if withYear {
			return t.In(time.FixedZone("CST", 8*60*60)).Format("06-01-02 15:04:05")
		}
		return t.In(time.FixedZone("CST", 8*60*60)).Format("01-02 15:04:05")
	}
}

func FormatDuration(duration time.Duration, precision string) string {
	precision = strings.ToLower(precision)
	if !strings.Contains("d/h/m/s", precision) {
		return duration.String()
	}
	divisor := float64(60 * 60) // default: h
	if precision == "d" {
		divisor = float64(60 * 60 * 24)
	} else if precision == "m" {
		divisor = float64(60)
	} else if precision == "s" {
		divisor = float64(1)
	}
	converted := float64(duration/time.Second) / divisor
	return fmt.Sprintf("%.1f%s", converted, precision)
}

// 返回字符串在字符串数组中的位置，不存在则返回 -1
func IndexOf(element string, data []string) int {
	for k, v := range data {
		if element == v {
			return k
		}
	}
	return -1
}

func CheckReleaseBinaryDirPath(releaseBinaryDirPath string) bool {
	if releaseBinaryDirPath == "" {
		releaseBinaryDirPath = "releases"
	}

	if _, err := os.Stat(releaseBinaryDirPath); err != nil {
		if err := os.Mkdir(releaseBinaryDirPath, 0755); err != nil {
			log.Printf("创建 ReleaseBinaryDirPath(%s) 文件夹失败", releaseBinaryDirPath)
			return false
		}
	}
	return true
}

// 读取日志
// TurtleController standalone 模式下读取时以 TurtleID 为 filename
// QuanterManager 模式下以 quanter 为 filename
func ReadLog(logDirPath, filename string, numOfLines int) (string, error) {
	dirPath := "./logs"
	if logDirPath != "" {
		dirPath = logDirPath
	}
	path := filepath.Join(dirPath, filename+".log")
	if _, err := os.Stat(path); err != nil {
		return "", fmt.Errorf("log path not exist at: %s ", path)
	}

	count := 0
	logs := ""
	err := backscanner.BackScan(path, func(line []byte) bool {
		logs = string(line) + "\n" + logs
		count += 1
		return count < numOfLines
	})
	if err != nil {
		return "", fmt.Errorf("read log content error: %s", err.Error())
	}

	if err != nil {
		return "", fmt.Errorf("read log content error: %s", err.Error())
	}
	return logs, nil
}

// 优先尝试从 $QUANTER_DEBUG_SLACK_ROBOT_TOKEN 读取，否则通过 secrets.SLACK_ROBOT_TOKEN 解密得到 SlackRobotToken
// 采用从环境变量读取是为了简化 debug 的流程，不需要手工替换  secrets.SLACK_ROBOT_TOKEN  的值并且重新编译
func GetSlackRobotToken(debug bool) (robotToken, cleanerToken secrets.SecretString, er error) {
	envSlackRobotToken := os.Getenv("QUANTER_DEBUG_SLACK_ROBOT_TOKEN")
	if debug && envSlackRobotToken != "" {
		// 因为包含敏感信息，不直接打印 $QUANTER_DEBUG_SLACK_ROBOT_TOKEN 的值
		log.Printf("env $QUANTER_DEBUG_SLACK_ROBOT_TOKEN is set, use it instead of secrets.SLACK_ROBOT_TOKEN.")
		robotToken = secrets.SecretString(envSlackRobotToken)
	} else {
		robotToken = secrets.GetSlackRobotToken()
	}
	if robotToken.Contains("|||") {
		parts := robotToken.Split("|||")
		robotToken = parts[0]
		cleanerToken = parts[1]
	}
	return
}

func ParseFloatOrPercentage(numOrPercentage string, percentOnly bool, allowNegative bool) (result float64, isPercent bool, er error) {
	result = 0
	if strings.Contains(numOrPercentage, `%`) {
		if p, err := strconv.ParseFloat(numOrPercentage[:len(numOrPercentage)-1], 64); err == nil {
			result = p / 100.0
			if result > 1 {
				return 0, true, fmt.Errorf("%s percentage > 100%% error", numOrPercentage)
			}
			if !allowNegative {
				if result < 0 {
					return 0, true, fmt.Errorf("%s percentage < 0 error", numOrPercentage)
				}
			}
			if result < -1 {
				return 0, true, fmt.Errorf("%s percentage < -100%% error", numOrPercentage)
			}
		} else {
			return 0, true, fmt.Errorf("%s percentage format error", numOrPercentage)
		}
		return result, true, nil
	} else {
		if percentOnly {
			return 0, false, fmt.Errorf("%s need percent only", numOrPercentage)
		} else {
			if n, err := strconv.ParseFloat(numOrPercentage, 64); err == nil {
				result = n
			} else {
				return 0, false, fmt.Errorf("%s number format error", numOrPercentage)
			}
			if !allowNegative && result < 0 {
				return 0, false, fmt.Errorf("%s number < 0 error", numOrPercentage)
			}
		}
		return result, false, nil
	}
}

func CSVContains(csv string, target string, sep string) bool {
	found := false
	for _, p := range strings.Split(csv, sep) {
		if strings.EqualFold(p, target) {
			found = true
		}
	}
	return found
}

func ParseFloatRange(rangeStr string, sep string) (fromValue, toValue float64, er error) {
	if rangeParts := strings.Split(rangeStr, sep); len(rangeParts) != 2 {
		return 0, 0, fmt.Errorf("parse float range, format error：%s", rangeStr)
	} else {
		if f, err := cast.ToFloat64E(rangeParts[0]); err != nil {
			return 0, 0, fmt.Errorf("parse float range from value failed：%s, error: %s", rangeParts[0], err)
		} else if t, err := cast.ToFloat64E(rangeParts[1]); err != nil {
			return 0, 0, fmt.Errorf("parse float range to value failed：%s, error: %s", rangeParts[1], err)
		} else {
			fromValue = f
			toValue = t
		}
	}
	return
}

func ParseTimeBeijing(s string) (result *time.Time) {
	s = strings.TrimSpace(s)
	s = strings.ToUpper(s)
	slashCount := strings.Count(s, "-")
	if strings.EqualFold(s, "now") {
		return Ptr(time.Now().In(time.FixedZone("CST", 8*60*60)))
	}
	if slashCount == 1 {
		now := time.Now()
		s = fmt.Sprintf("%d-%s", now.Year(), s)
	} else if slashCount == 0 {
		now := time.Now()
		s = fmt.Sprintf("%d-%02d-%dT%s", now.Year(), int(now.Month()), now.Day(), s)
	}
	// 支持的格式如下
	// 如果没有 year，默认是当前 year；如果没有 date，默认是当前 date
	formats := []string{"2006-01-02T15:04:05", "2006-01-2T15:04:05", "2006-01-02T15:04", "2006-01-2T15:04", "2006-1-02T15:04:05", "2006-1-02T15:04", "06-01-02T15:04:05", "06-01-02T15:04", "01-02T15:04:05", "01-02T15:04", "2006-01-02 15:04:05", "2006-01-02 15:04", "06-01-02 15:04:05", "06-01-02 15:04", "01-02 15:04:05", "01-02 15:04", "15:04:05", "15:04:05"}
	for _, f := range formats {
		if t, err := time.Parse(f, s); err != nil {
			continue
		} else {
			result = &t
		}
	}
	if result != nil {
		if bjTime, err := time.Parse(time.RFC3339, fmt.Sprintf("%s+08:00", result.Format("2006-01-02T15:04:05"))); err == nil {
			result = &bjTime
		} else {
			result = nil
		}
	}
	return
}

func SliceContainsEqualFold[T ~string](list []T, target T) bool {
	exist := false
	for _, item := range list {
		if strings.EqualFold(fmt.Sprintf("%s", item), fmt.Sprintf("%s", target)) {
			exist = true
			break
		}
	}
	return exist
}

func SliceStringJoin[T ~string](list []T, sep string, quote bool) string {
	newList := []string{}
	if quote {
		for _, item := range list {
			newList = append(newList, fmt.Sprintf(`"%s"`, item))
		}
	} else {
		for _, item := range list {
			newList = append(newList, fmt.Sprintf("%s", item))
		}
	}
	return strings.Join(newList, sep)
}

// type comparable interface {
// 	~string | ~int64 | ~int | ~float64 | ~byte
// }

func SliceContains[T comparable](slice []T, target T) (exist bool) {
	exist = false
	for _, item := range slice {
		if item == target {
			exist = true
			break
		}
	}
	return
}

func StringEnumContains[T ~string](list []T, target T) bool {
	for _, item := range list {
		if item == target {
			return true
		}
	}
	return false
}

func Ptr[T any](v T) *T {
	return &v
}

func HideSecret(secret string) string {
	sLen := len(secret)
	if sLen > 8 {
		return fmt.Sprintf("%s******%s", secret[:4], secret[sLen-4:])
	} else if sLen == 0 {
		return "*** Empty ***"
	} else {
		return fmt.Sprintf("%s******%s", secret[:1], secret[sLen-1:])
	}
}

func DecimalAdd(x, y float64) float64 {
	dX := decimal.NewFromFloat(x)
	dY := decimal.NewFromFloat(y)
	r := dX.Add(dY)
	z, _ := r.Float64()
	return z
}

func ParseDirAndFilename(path string, withoutExtension bool) (dir, filename string) {
	// path may contains seperator / or \\
	path = strings.ReplaceAll(path, "\\", "/")
	// remove last seperator
	if strings.HasSuffix(path, "/") {
		path = path[:len(path)-1]
	}
	// get dir and filename
	dir = path
	filename = ""
	if strings.Contains(path, "/") {
		dir = path[:strings.LastIndex(path, "/")]
		filename = path[strings.LastIndex(path, "/")+1:]
	}
	// remove extension
	if withoutExtension && strings.Contains(filename, ".") {
		filename = filename[:strings.LastIndex(filename, ".")]
	}
	return
}

func SplitTrimSpace(s, sep string) []string {
	parts := strings.Split(s, sep)
	for i, p := range parts {
		parts[i] = strings.TrimSpace(p)
	}
	return parts
}

func NewRandomID() string {
	rand.Seed(time.Now().UnixNano())
	randStr := "1234567890abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ"
	var letterRunes = []rune(randStr)

	b := make([]rune, 6)
	for i := range b {
		b[i] = letterRunes[rand.Intn(len(letterRunes))]
	}
	return string(b)
}

func NewTable() *termtable.Table {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	return t
}

func TrimTrailingZeros(number string) string {
	parts := strings.Split(number, ".")
	if len(parts) == 1 {
		return number
	}

	fracPart := strings.TrimRight(parts[1], "0")

	if fracPart == "" {
		return parts[0]
	}

	return parts[0] + "." + fracPart
}

func FormatNum(num float64, maxPrecision int) string {
	return FormatNumWithSign(num, maxPrecision, false)
}

func FormatNumWithSign(num float64, maxPrecision int, withSign bool) string {
	numStr := TrimTrailingZeros(strconv.FormatFloat(num, 'f', maxPrecision, 64))
	if withSign {
		if num > 0 {
			return "+" + numStr
		}
		return numStr
	}
	return numStr
}

func FormatPercentWithSign(num float64, maxPrecision int, withSign bool) string {
	return FormatNumWithSign(num*100, maxPrecision, withSign) + "%"
}

func FormatPercent(num float64, maxPrecision int) string {
	return FormatPercentWithSign(num, maxPrecision, false)
}

// CopyFile copies a file from src to dst.
// If dst exists, it will be overwritten.
func CopyFile(src, dst string) error {
	// Open source file
	sourceFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("failed to open source file: %w", err)
	}
	defer sourceFile.Close()

	// Create destination file
	destFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("failed to create destination file: %w", err)
	}
	defer destFile.Close()

	// Copy the contents
	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return fmt.Errorf("failed to copy file contents: %w", err)
	}

	// Sync to ensure write is complete
	err = destFile.Sync()
	if err != nil {
		return fmt.Errorf("failed to sync destination file: %w", err)
	}

	return nil
}

func SliceSubtract[T comparable](slice1, slice2 []T) []T {
	diff := []T{}
	for _, item := range slice1 {
		if !SliceContains(slice2, item) {
			diff = append(diff, item)
		}
	}
	return diff
}

// Filter returns a new slice containing only the elements for which the predicate returns true
func Filter[T any](slice []T, predicate func(T) bool) []T {
	result := make([]T, 0)
	for _, item := range slice {
		if predicate(item) {
			result = append(result, item)
		}
	}
	return result
}

// Map applies a function to each element of a slice and returns a new slice with the results
func Map[T any, R any](slice []T, mapper func(T) R) []R {
	result := make([]R, len(slice))
	for i, item := range slice {
		result[i] = mapper(item)
	}
	return result
}

func Now() *time.Time {
	t := time.Now().In(time.FixedZone("CST", 8*60*60))
	return &t
}

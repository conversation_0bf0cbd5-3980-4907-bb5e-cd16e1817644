package utils

import (
	"net/http"
	"strings"

	"github.com/wizhodl/quanter/common/stack"

	"github.com/gin-gonic/gin"
)

type DebugSetter interface {
	SetDebug(debug bool)
}

type StackHttpHandler struct {
	debugSetter DebugSetter
}

func NewStackHttpHandler(loggerSetter DebugSetter) *StackHttpHandler {
	return &StackHttpHandler{debugSetter: loggerSetter}
}

func (this *StackHttpHandler) queryStackHandler(c *gin.Context) {
	key := c.Query("key")
	count, st := stack.QueryStack(key)
	if len(st) > 0 {
		c.JSON(http.StatusOK, gin.H{"count": count, "stack": string(st)})
		return
	}

	stacks := stack.ListStacksCount()
	c.JSON(http.StatusOK, stacks)
}

func (this *StackHttpHandler) setLogHandler(c *gin.Context) {
	key := c.Query("debug")
	debug := false
	if strings.ToUpper(key) == "TRUE" || strings.ToUpper(key) == "T" || strings.ToUpper(key) == "1" {
		debug = true
	} else if strings.ToUpper(key) == "False" || strings.ToUpper(key) == "F" || strings.ToUpper(key) == "0" {
		debug = false
	}
	this.debugSetter.SetDebug(debug)
	c.JSON(http.StatusOK, map[string]string{})
}

func (this *StackHttpHandler) RegisterRoutes(r *gin.Engine) {
	r.GET("/stack/query", this.queryStackHandler)
	r.GET("/log/level", this.setLogHandler)
}

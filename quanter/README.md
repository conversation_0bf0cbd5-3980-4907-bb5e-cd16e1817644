# Turtle

### Go 版本

go1.16

### 环境变量和 DEBUG

1. `$HTTP_PROXY`: 交易所连接时使用的 HTTP 代理；
2. `$QUANTER_LOG_COLORED_LEVEL`: 1/0，打印彩色的日志等级；
3. `$QUANTER_LOG_CALLER`: 1/0，打印日志的调用函数和行号
4. `$QUANTER_DEBUG_SLACK_ROBOT_TOKEN`: Debug 模式下使用的未加密的 Slack 机器人 Token
5. `$QUANTER_DEBUG_AUTH_SECRET`: Debug 模式下使用的未加密的 google 两步验证码秘钥
6. `$QUANTER_DEBUG_{turtleID}_API_SECRET`: Debug 模式下使用的 {turtleID} 对应的 ApiSecret
7. `$QUANTER_GCT_LOG_ENABLE`: 启用 gocryptotrader 日志，默认配置会产生大量的 HTTP 请求日志
8. `$QUANTER_ARBI_FAKE_EXECUTE`: 假执行套利机的套利和平仓指令，用于测试显示
9. Debug 模式会尝试到 `configPath/$firebase.json`  寻找未加密的 firebase 配置

DEBUG
1. 当 DEBUG = true 时，会尝试读取 $QUANTER_DEBUG_SLACK_ROBOT_TOKEN 初始化 Slack 连接，否则使用加密的 secrets.SLACK_ROBOT_TOKEN ；
2. 当 DEBUG = true 时，会尝试同时检查 $QUANTER_DEBUG_AUTH_SECRET 和 $QUANTER_DEBUG_{turtleID}_API_SECRET 来初始化 TurtleController

### 编译及运行

1. 准备 slack 私有频道，将机器人添加到频道。需要准备一个主频道和 N 个海龟品种的频道，N = 海龟品种数量。频道名由小写英文字母、下划线和数字组成，如主频道为 snowball，后续用到的 `TurtleID` 将和频道名相同；海龟频道名格式则为 `{TurtleID}_{Symbol}`，如 snowball_xbtusd, snowball_ethusd。

2. 进入项目根目录

3. 以 `TurtleID` 命名创建一个 toml 文件，如 snowball.turtle.toml, 内容参考 conf.turtle.toml.example。注意其中 `ExchangeName`、 `ApiKey` 和 `ApiSecret` 的配置将在第 11 步骤中获取。主配置中 `[SymbolConfig]` 以下的内容为海龟通用配置，如需单独为某个海龟创建配置，则创建 `{TurtleID}_{Symbol}.toml` 文件，如 snowball_ethusd.toml，并将通用配置项写入文件；否则所有海龟将使用相同配置初始化，之后可以再单独修改。

4. 运行 `cp secrets/apikeys.fake secrets/apikeys.go` 拷贝默认 apikey 模板文件；这样才能保证代码可以编译；

5. 运行 `cp secrets/encryptsalt.go.example secrets/encryptsalt.go` 拷贝默认 encryptosalt 模板文件；这样才能保证代码可以编译；

6. 可自行实现 encryptsalt.go 中的 GetEncryptSalt 方法，注意需要返回 8 位字符串。此步骤可选，如果不修改使用“默认字符串”。

7. 进入目录 genkeys

8. 运行 `go run genkeys.go`

9. 按提示输入需要的数据，并记录谷歌验证码和启动密码

10. 运行 `go run genkeys.go encryptAPIKey`

11. 按提示输入 API 信息，最后将加密后的输出粘贴到 toml 配置文件中

12. 返回根目录, 运行 `go build main.go`

13. 运行 `./main -turtleID TurtleID -configPath config` 启动程序，

当设置了 turtleID 参数（上述 `TurtleID` ）时，以 standalone 模式运行该 turtleID 的海龟。configPath 可选，默认为当前目录。

### 多个品种海龟共用二进制文件

如果使用的 slack 机器人、Firebase 配置、主密码和谷歌验证码都相同，则可使用同一个二进制文件。跑不同的交易所和品种，只需要使用不同配置文件即可，可能情况如下：

-   API 密钥相同，仅需运行一个程序，按上述流程使用即可
-   API 密钥不同，需运行 `go run genkeys.go encryptAPIKey` 获取不同的 API 配置信息，并使用不同的 slack 频道、`TurtleID` 及配置文件

### 更换 slack 机器人配置

如果需要更换 slack 机器人 token，运行:

`go run genkeys.go replaceSlackConfig`

带上 replaceSlackConfig 参数表示替换 slack 配置，其他密钥等信息等不变。

更换后需要重新 build 主程序。

### 启用 Firestore 存储

1. 创建项目并下载 JSON 认证文件，参考: https://firebase.google.com/docs/firestore/quickstart?authuser=0

2. 将 json 文件命名为 firebase.json 放在 genkeys 文件夹

3. 按上述流程编译主程序

启用后，本地配置文件和存储将同步到 Firestore 中，如果程序启动时没有配置或者存储，将使用之前同步的配置和存储。

### 更换 Firebase 配置

go run genkeys.go replaceFirebaseJSON

### 在线更新 config

在启用了 firebase 的情况下，你可以在 firebase 管理后台在线修改配置，然后使用 pullConfig 命令重启或加载。

1. 确保程序启用了 Firebase 功能，并已使用 launch 启动成功过至少一次；
2. 在 Firestore 控制台，修改主程序配置或对应海龟的配置项；
3. 在 slack 频道使用 pullConfig 命令覆盖本地配置文件，如果是主程序配置将会退出自动重启；如果是海龟配置则会重新加载运行，无需重启。

### 编译到 Linux 平台

GOOS=linux GOARCH=amd64 go build -v main.go


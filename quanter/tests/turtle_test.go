package test

import "github.com/wizhodl/quanter/turtle"

var tt *turtle.Turtle

// var bmApi *turtle.BitMEXAPI

func init() {
	// tt = &turtle.Turtle{
	// 	ApiKey:                                turtle.API_KEY,
	// 	ApiSecret:                             turtle.API_SECRET,
	// 	IsTestnet:                             true,
	// 	CLOSE_SLIPPAGE_RATE:                   0.001,
	// 	Symbol:                                "XBTUSD",
	// 	PeriodHour:                            6,
	// 	WAITING_SECONDS_AFTER_TRIGGERED_LONG:  10,
	// 	WAITING_SECONDS_AFTER_TRIGGERED_SHORT: 10,
	// }

	// tt.InitAPI()

	// bmApi = &turtle.BitMEXAPI{
	// 	ApiKey:    turtle.API_KEY,
	// 	ApiSecret: turtle.API_SECRET,
	// 	IsTestnet: true,
	// }
}

// func TestTakeProfitOrder(t *testing.T) {
// 	order, _ := bmApi.GetOrderByID("XBTUSD", "be34efba-ee81-2021-dcbc-93ff6f50fa95")
// 	logger.Info(order)
// 	tt.TakeTriggeredProfitOrder(order)
// }

package test

import (
	"fmt"
	"testing"

	"github.com/wizhodl/quanter/messenger"
	"github.com/wizhodl/quanter/secrets"

	"github.com/spf13/viper"
)

var slackRobot *messenger.SlackMessenger

func init() {

	viper.SetConfigName("conf")
	viper.AddConfigPath("..")
	err := viper.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("Fatal error config file: %s \n", err))
	}

	slackRobot = messenger.NewSlackMessenger(secrets.SecretString(viper.GetString("SlackRobotToken")), viper.GetString("SlackCommander"), secrets.SecretString(""))
}

// func TestGetChannels(t *testing.T) {
// 	if res, err := slackRobot.GetChannels(); err != nil {
// 		t.Errorf(`expected no err`)
// 	} else {
// 		for _, channel := range res {
// 			zlog.Infof("ID: %s, Name: %s, Creator: %s, IsPrivate: %t, Members: %v", channel.ID, channel.Name, channel.Creator, channel.IsPrivate, channel.Members)
// 		}
// 	}
// }

func TestGetUserInfo(t *testing.T) {
	slackRobot.GetUserInfo("UFRKY3Z71")
}

// func TestSendMessage(t *testing.T) {
// 	slackRobot.SendMessage("Hello world from testcase")
// }

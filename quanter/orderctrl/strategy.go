package orderctrl

import (
	"fmt"

	"github.com/wizhodl/quanter/arbitrage/cross_exchange_arbitrage"
	"github.com/wizhodl/quanter/arbitrage/funding_arbitrage"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/utils"
)

func (this *OrderController) ListStrategies() []base.Strategy {
	strategies := []base.Strategy{}
	for _, arbitrager := range this.crossExchangeArbitragers {
		strategies = append(strategies, arbitrager)
	}
	for _, arbitrager := range this.fundingArbitragers {
		strategies = append(strategies, arbitrager)
	}
	return strategies
}

func (this *OrderController) AddStrategy(strategyType base.StrategyType, strategy base.Strategy) {
	if strategyType == base.StrategyTypeCrossExchangeArbitrager {
		this.crossExchangeArbitragers = append(this.crossExchangeArbitragers, strategy.(*cross_exchange_arbitrage.CrossExchangeArbitrager))
		this.storage.CrossExchangeArbitragers = append(this.storage.CrossExchangeArbitragers, strategy.(*cross_exchange_arbitrage.CrossExchangeArbitrager))
	} else if strategyType == base.StrategyTypeFundingArbitrager {
		this.fundingArbitragers = append(this.fundingArbitragers, strategy.(*funding_arbitrage.FundingArbitrager))
		this.storage.FundingArbitragers = append(this.storage.FundingArbitragers, strategy.(*funding_arbitrage.FundingArbitrager))
	}
	this.storage.Save()
}

func (this *OrderController) RemoveStrategy(strategyType base.StrategyType, idOrAlias string) error {
	if strategyType == base.StrategyTypeCrossExchangeArbitrager {
		// remove ID/Alias
		for i, arbitrager := range this.crossExchangeArbitragers {
			if arbitrager.ID == idOrAlias || arbitrager.Alias == idOrAlias {
				this.crossExchangeArbitragers = append(this.crossExchangeArbitragers[:i], this.crossExchangeArbitragers[i+1:]...)
				this.storage.CrossExchangeArbitragers = append(this.storage.CrossExchangeArbitragers[:i], this.storage.CrossExchangeArbitragers[i+1:]...)
				this.storage.Save()
				arbitrager.DeleteTime = utils.Now()
				this.SendMsgf("removed cross exchange arbitrage: %v", idOrAlias)
				return nil
			}
		}
	} else if strategyType == base.StrategyTypeFundingArbitrager {
		for i, arbitrager := range this.fundingArbitragers {
			if arbitrager.ID == idOrAlias || arbitrager.Alias == idOrAlias {
				this.fundingArbitragers = append(this.fundingArbitragers[:i], this.fundingArbitragers[i+1:]...)
				this.storage.FundingArbitragers = append(this.storage.FundingArbitragers[:i], this.storage.FundingArbitragers[i+1:]...)
				this.storage.Save()
				arbitrager.DeleteTime = utils.Now()
				this.SendMsgf("removed funding arbitrage: %v", idOrAlias)
				return nil
			}
		}
	}
	return fmt.Errorf("strategy not found")
}

func (this *OrderController) SetStrategyAlias(strategyType base.StrategyType, id, alias string) error {
	strategy := this.GetStrategy(strategyType, id)
	if strategy == nil {
		return fmt.Errorf("strategy not found")
	}
	strategy.SetAlias(alias)
	this.SendMsgf("set strategy alias: %s -> %s", id, alias)
	return nil
}

func (this *OrderController) GetStrategy(strategyType base.StrategyType, idOrAlias string) base.Strategy {
	if strategyType == base.StrategyTypeCrossExchangeArbitrager {
		for _, arbitrager := range this.crossExchangeArbitragers {
			if arbitrager.ID == idOrAlias || arbitrager.Alias == idOrAlias {
				return arbitrager
			}
		}
	} else if strategyType == base.StrategyTypeFundingArbitrager {
		for _, arbitrager := range this.fundingArbitragers {
			if arbitrager.ID == idOrAlias || arbitrager.Alias == idOrAlias {
				return arbitrager
			}
		}
	}
	return nil
}

package orderctrl

import (
	"fmt"
	"sync"
	"time"

	"github.com/jinzhu/copier"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type ConditionalOrderManager struct {
	controller      *OrderController
	orderLocks      *exchange.SyncMapOf[string, *sync.Mutex]
	controllerLocks *exchange.SyncMapOf[string, *sync.Mutex]
}

type ConditionalOrderWrapper struct {
	conditionalOrderManager *ConditionalOrderManager
	controllerID            string
}

func NewConditionalOrderManager(controller *OrderController) *ConditionalOrderManager {
	return &ConditionalOrderManager{
		controller:      controller,
		orderLocks:      exchange.NewSyncMapOf[string, *sync.Mutex](),
		controllerLocks: exchange.NewSyncMapOf[string, *sync.Mutex](),
	}
}

func (this *ConditionalOrderManager) getOrderLock(controllerID, orderID string) *sync.Mutex {
	key := fmt.Sprintf("%s-%s", controllerID, orderID)
	lock, _ := this.orderLocks.LoadOrStore(key, &sync.Mutex{})
	return lock
}

func (this *ConditionalOrderManager) Using(controllerID string) *ConditionalOrderWrapper {
	usingController := this.controller.GetController(controllerID)
	ex := usingController.GetExchange()
	if ex == nil {
		return nil
	}
	ex.RegisterPriceTriggeredCallback(this.controller.priceTriggeredCallback)

	if !usingController.GetBaseConfig().EnableRealtimePrice {
		this.controller.AlertMsgf("[%s] 请设置 EnableRealtimePrice = true 来使用 PriceTrigger", usingController.GetID())
	}

	return &ConditionalOrderWrapper{
		conditionalOrderManager: this,
		controllerID:            controllerID,
	}
}

func (this *ConditionalOrderManager) appendConditionalOrder(order *exchange.Order, controllerID string) (*exchange.ConditionalOrder, error) {
	controllerLock, _ := this.controllerLocks.LoadOrStore(controllerID, &sync.Mutex{})
	controllerLock.Lock()
	defer controllerLock.Unlock()

	usingController := this.controller.GetController(controllerID)
	if usingController == nil {
		return nil, fmt.Errorf("controller not found")
	}
	ex := usingController.GetExchange()
	if ex == nil {
		return nil, fmt.Errorf("exchange not initialized")
	}

	var symbolCode *exchange.SymbolCode
	var er error
	uSymbol := usingController.GetBaseConfig().USDXSymbol
	if order.InstrumentType.IsFuture() {
		_, symbolCode, er = ex.TranslateFutureSymbol(order.InstrumentType, order.Symbol, uSymbol)
		if er != nil {
			return nil, fmt.Errorf("translate future symbol failed, error: %s", er)
		}
	} else {
		symbolCode, er = ex.TranslateSpotSymbol(order.Symbol, uSymbol)
		if er != nil {
			return nil, fmt.Errorf("translate spot symbol failed, error: %s", er)
		}
	}

	priceTrigger := &exchange.PriceTrigger{
		ID:             exchange.NewRandomID(),
		InstrumentType: order.InstrumentType,
		Symbol:         order.Symbol,
		SymbolCode:     symbolCode,
		Price:          order.TriggerPrice,
		Direction:      order.TriggerDirection,
		Source:         "ConditionalOrderManager",
	}

	ex.RegisterPriceTrigger(priceTrigger)

	conditionalOrder := &exchange.ConditionalOrder{
		Order:         order,
		ExchangeOrder: nil,
		PriceTrigger:  priceTrigger,
		ControllerID:  controllerID,
		ExchangeName:  ex.GetName(),
	}

	orders, _ := this.controller.storage.ConditionalOrders.LoadOrStore(controllerID, []*exchange.ConditionalOrder{})
	orders = append(orders, conditionalOrder)
	this.controller.storage.ConditionalOrders.Store(controllerID, orders)
	this.controller.storage.Save()

	return conditionalOrder, nil
}

func (this *ConditionalOrderManager) getOpenConditionalOrders(controllerID string) []*exchange.ConditionalOrder {
	orders, ok := this.controller.storage.ConditionalOrders.Load(controllerID)
	if !ok {
		return []*exchange.ConditionalOrder{}
	}

	copyOrders := make([]*exchange.ConditionalOrder, len(orders))
	copy(copyOrders, orders)
	return copyOrders
}

// 获取 Open 或 Finished 的订单
func (this *ConditionalOrderManager) getConditionalOrder(controllerID, orderID string, openOnly bool) *exchange.ConditionalOrder {
	orders, ok := this.controller.storage.ConditionalOrders.Load(controllerID)
	if !ok {
		return nil
	}

	for _, order := range orders {
		if order.Order.OrderID == orderID {
			orderCopy := &exchange.ConditionalOrder{}
			copier.Copy(orderCopy, order) // 避免返回原始指针，导致外部修改会影响到内部
			return orderCopy
		}
	}

	if !openOnly {
		// 如果订单不在 ConditionalOrders 中，可能在历史订单中，需要从历史订单中查询
		order := this.controller.storage.lookupFinishedConditionalOrder(controllerID, orderID)
		if order != nil {
			return order
		}
	}

	return nil
}

// 仅保存 Open 的订单
// TODO: 有没有可能传入了 finished 的订单，需要保存到历史订单中？
func (this *ConditionalOrderManager) saveConditionalOrder(order *exchange.ConditionalOrder) error {
	if order == nil {
		return fmt.Errorf("order is nil")
	}
	if order.ControllerID == "" {
		return fmt.Errorf("controller id is empty")
	}
	controllerLock, _ := this.controllerLocks.LoadOrStore(order.ControllerID, &sync.Mutex{})
	controllerLock.Lock()
	defer controllerLock.Unlock()

	orderCopy := &exchange.ConditionalOrder{}
	copier.Copy(orderCopy, order) // 避免引入原始指针，导致外部修改会影响到内部

	orders, ok := this.controller.storage.ConditionalOrders.Load(order.ControllerID)
	if !ok {
		return nil
	}

	for i, o := range orders {
		if o.Order.OrderID == orderCopy.Order.OrderID {
			orders[i] = orderCopy
			this.controller.storage.ConditionalOrders.Store(order.ControllerID, orders)
			this.controller.storage.Save()
			return nil
		}
	}
	return fmt.Errorf("order not found")
}

// 将 Open 的订单移到历史订单中
func (this *ConditionalOrderManager) archiveOpenOrder(controllerID, orderID string) {
	controllerLock, _ := this.controllerLocks.LoadOrStore(controllerID, &sync.Mutex{})
	controllerLock.Lock()
	defer controllerLock.Unlock()

	orders, ok := this.controller.storage.ConditionalOrders.Load(controllerID)
	if !ok {
		return
	}

	for i, order := range orders {
		if order.Order.OrderID == orderID {
			this.controller.storage.saveFinishedConditionalOrder(order)

			orders = append(orders[:i], orders[i+1:]...)
			this.controller.storage.ConditionalOrders.Store(controllerID, orders)
			this.controller.storage.Save()
			return
		}
	}
}

func (this *ConditionalOrderWrapper) CreateOrder(args exchange.CreateOrderArgs) (*exchange.Order, error) {
	nowTime := time.Now()
	order := &exchange.Order{
		InstrumentType:   args.InstrumentType,
		OrderID:          exchange.NewRandomID(),
		Symbol:           args.Symbol,
		Price:            args.Price,
		TriggerPrice:     args.TriggerPrice,
		TriggerDirection: args.TriggerDirection,
		Qty:              args.Qty,
		QuoteQty:         args.QuoteQty,
		Type:             args.Type,
		TradeMode:        args.TradeMode,
		Side:             args.Side,
		Status:           exchange.OrderStatusNew,
		TimeInForce:      args.TimeInForce,
		ClosePosition:    args.ClosePosition,
		ReduceOnly:       args.ReduceOnly,
		CreateTime:       &nowTime,
		UpdateTime:       &nowTime,
	}
	this.conditionalOrderManager.appendConditionalOrder(order, this.controllerID)
	orderCopy := &exchange.Order{}
	copier.Copy(orderCopy, order) // 避免返回原始指针，导致外部修改会影响到内部
	return orderCopy, nil
}

func (this *ConditionalOrderWrapper) CancelOrder(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol, orderID string) (er error) {
	orderLock := this.conditionalOrderManager.getOrderLock(this.controllerID, orderID)
	orderLock.Lock()
	defer orderLock.Unlock()

	order := this.conditionalOrderManager.getConditionalOrder(this.controllerID, orderID, true)
	if order == nil {
		return fmt.Errorf("order not found")
	}

	if order.Order.Status != exchange.OrderStatusTriggered && order.Order.Status != exchange.OrderStatusNew {
		return fmt.Errorf("order status is %s, cannot cancel", order.Order.Status)
	}

	usingController := this.conditionalOrderManager.controller.GetController(this.controllerID)
	ex := usingController.GetExchange()
	if ex == nil {
		return fmt.Errorf("exchange not initialized")
	}

	cancelled := false
	defer func() {
		if cancelled {
			order.Order.Status = exchange.OrderStatusCancelled
			order.Order.UpdateTime = utils.Ptr(time.Now())
			err := this.conditionalOrderManager.saveConditionalOrder(order)
			if err != nil {
				er = fmt.Errorf("save conditional order failed, error: %s", err)
			}
			// 取消订单移到历史订单中
			this.conditionalOrderManager.archiveOpenOrder(this.controllerID, orderID)

			ex.OrderUpdatedCallback(order.Order)
		}
	}()

	// 未触发则直接取消
	if order.ExchangeOrder == nil {
		cancelled = true
		ex.DeletePriceTrigger(order.PriceTrigger.ID)
		return nil
	}

	err := ex.CancelOrder(instrumentType, orderType, symbol, order.ExchangeOrder.OrderID)
	if err == nil {
		cancelled = true
	}
	return err
}

func (this *ConditionalOrderWrapper) GetOpenOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string) ([]*exchange.Order, error) {
	orders := this.conditionalOrderManager.getOpenConditionalOrders(this.controllerID)
	openOrders := make([]*exchange.Order, 0)
	for _, order := range orders {
		if instrumentType != "" && order.Order.InstrumentType != instrumentType {
			continue
		}
		if orderType != "" && order.Order.Type != orderType {
			continue
		}
		if symbol != "" && order.Order.Symbol != symbol {
			continue
		}

		if order.Order.IsOpen() {
			orderCopy := &exchange.Order{}
			copier.Copy(orderCopy, order.Order) // 避免返回原始指针，导致外部修改会影响到内部
			openOrders = append(openOrders, orderCopy)
		}
	}
	return openOrders, nil
}

func (this *ConditionalOrderWrapper) GetOrderByOrig(origOrder exchange.Order) (*exchange.Order, error) {
	order := this.conditionalOrderManager.getConditionalOrder(this.controllerID, origOrder.OrderID, false)
	if order == nil {
		return nil, fmt.Errorf("order not found")
	}
	orderCopy := &exchange.Order{}
	copier.Copy(orderCopy, order.Order) // 避免返回原始指针，导致外部修改会影响到内部
	orderCopy.SetExtStruct(&origOrder)
	return orderCopy, nil
}

func (this *ConditionalOrderWrapper) UpdateOrder(origOrder exchange.Order, args *exchange.UpdateOrderArgs) (*exchange.Order, error) {
	orderLock := this.conditionalOrderManager.getOrderLock(this.controllerID, origOrder.OrderID)
	orderLock.Lock()
	defer orderLock.Unlock()

	condOrder := this.conditionalOrderManager.getConditionalOrder(this.controllerID, origOrder.OrderID, true)
	if condOrder == nil {
		return nil, fmt.Errorf("orig order not found")
	}

	resultOrder := condOrder.Order // condOrder 返回的是拷贝，可以直接使用，不用再拷贝返回

	if resultOrder.Status != exchange.OrderStatusNew {
		return nil, fmt.Errorf("order status is %s, cannot update", resultOrder.Status)
	}

	if args.Price != 0 && args.Price != resultOrder.Price {
		resultOrder.Price = args.Price
	}
	if args.OrderQty != 0 && args.OrderQty != resultOrder.Qty {
		resultOrder.Qty = args.OrderQty
	}
	if args.TriggerPrice != 0 && args.TriggerPrice != resultOrder.TriggerPrice {
		resultOrder.TriggerPrice = args.TriggerPrice
		condOrder.PriceTrigger.Price = args.TriggerPrice

		usingController := this.conditionalOrderManager.controller.GetController(this.controllerID)
		ex := usingController.GetExchange()
		if ex == nil {
			return nil, fmt.Errorf("exchange not initialized")
		}
		ex.RegisterPriceTrigger(condOrder.PriceTrigger)
	}

	resultOrder.UpdateTime = utils.Ptr(time.Now())
	this.conditionalOrderManager.saveConditionalOrder(condOrder)

	resultOrder.SetExtStruct(&origOrder)
	return resultOrder, nil
}

func (this *ConditionalOrderManager) triggerConditionalOrder(condOrder *exchange.ConditionalOrder) error {
	orderLock := this.getOrderLock(condOrder.ControllerID, condOrder.Order.OrderID)
	orderLock.Lock()
	defer orderLock.Unlock()

	usingController := this.controller.GetController(condOrder.ControllerID)
	if usingController == nil {
		return fmt.Errorf("controller not found")
	}
	ex := usingController.GetExchange()
	if ex == nil {
		return fmt.Errorf("exchange not initialized")
	}

	order := condOrder.Order
	order.Status = exchange.OrderStatusTriggered
	order.UpdateTime = utils.Ptr(time.Now())

	args := exchange.CreateOrderArgs{
		InstrumentType: order.InstrumentType,
		Symbol:         order.Symbol,
		Price:          order.Price,
		Qty:            order.Qty,
		QuoteQty:       order.QuoteQty,
		Type:           exchange.Limit,
		TradeMode:      order.TradeMode,
		Side:           order.Side,
		TimeInForce:    order.TimeInForce,
		ReduceOnly:     order.ReduceOnly,
		ClosePosition:  order.ClosePosition,
	}
	var err error
	condOrder.ExchangeOrder, err = ex.CreateOrder(args)
	if err != nil {
		order.Status = exchange.OrderStatusCancelled // 触发失败，取消订单
		order.Comment = fmt.Sprintf("trigger failed, error: %s", err)
		order.UpdateTime = utils.Ptr(time.Now())
		err = fmt.Errorf("create order failed, order id: %s, error: %s", order.OrderID, err)
	} else {
		// 在触发单中记录交易所订单 ID，用于后续的订单查询
		// 因为 conditionalOrder 中的 ExchangeOrder 不对外暴露，如果要知道交易所订单 ID，需要通过 conditionalOrder.TriggeredOrderID 来获取
		// 比如：grid 中，就通过 conditionalOrder.TriggeredOrderID 来检查远程订单和本地订单是否一致
		order.TriggeredOrderID = condOrder.ExchangeOrder.OrderID

		ex.OrderUpdatedCallback(order)
	}

	this.saveConditionalOrder(condOrder)

	if order.Status == exchange.OrderStatusCancelled {
		this.archiveOpenOrder(condOrder.ControllerID, order.OrderID)
	}

	return err
}

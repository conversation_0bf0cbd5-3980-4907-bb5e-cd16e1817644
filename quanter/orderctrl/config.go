package orderctrl

import (
	"fmt"
	"os"
	"path"

	"github.com/spf13/viper"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/zlog"
)

type OrderControllerConfig struct {
	baseconfig.BaseConfig
	controller *OrderController
}

func NewOrderControllerConfig(controller *OrderController) (*OrderControllerConfig, error) {
	if config, err := LoadConfig(controller.ConfigPath, controller.ID); err != nil {
		return nil, err
	} else {
		config.controller = controller
		return config, nil
	}
}

func LoadConfig(configPath, controllerID string) (config *OrderControllerConfig, er error) {
	config = &OrderControllerConfig{}
	configFilePath := path.Join(configPath, controllerID+".orderctrl.toml")
	if _, err := os.Stat(configFilePath); !os.IsNotExist(err) {
		viper.SetConfigName(controllerID + ".orderctrl")
		viper.AddConfigPath(configPath)
		err := viper.ReadInConfig()
		if err == nil {
			err := viper.Unmarshal(&config)
			if err != nil {
				zlog.Errorf("[%s] unable to decode config into struct, %v", controllerID, err)
				return nil, err
			}
			if err := config.Validate(); err != nil {
				zlog.Errorf("[%s] grid config validate error: %s, %v", controllerID, err, config)
				return nil, err
			}
			zlog.Infof("[%s] load config from local file", controllerID)
			return config, nil
		} else { // 如果配置文件存在，但是解析出现错误，报错退出
			zlog.Panicf("[%s] read config file error：%s", controllerID, err)
			return nil, fmt.Errorf("read config error: %v", err)
		}
	} else {
		return nil, err
	}
}

func (this *OrderControllerConfig) ToTomlContent(hideSecret bool) string {
	result := "[BaseConfig]\n" +
		this.BaseConfig.ToTomlContent(hideSecret)
	return result
}

func (this *OrderControllerConfig) SaveTo(configPath string, id string, overwrite bool) error {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".orderctrl.toml")
	if _, err := os.Stat(path); !os.IsNotExist(err) && !overwrite {
		return fmt.Errorf("%s already exist, can not overwrite", path)
	}
	if err := os.WriteFile(path, []byte(this.ToTomlContent(false)), 0755); err != nil {
		this.controller.Errorf("write order controller config %s error: %v", id, err)
		return err
	}
	return nil
}

func (this *OrderControllerConfig) Save() {
	this.SaveTo(this.controller.ConfigPath, this.controller.ID, true)
}

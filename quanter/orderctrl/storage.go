package orderctrl

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path"
	"path/filepath"
	"sync"
	"time"

	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/arbitrage/cross_exchange_arbitrage"
	"github.com/wizhodl/quanter/arbitrage/funding_arbitrage"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/backscanner"
	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type OrderStorage struct {
	base.BaseStorage
	Orders []*exchange.OrderRecord
	Groups *option.NamedList

	CrossExchangeArbitragers []*cross_exchange_arbitrage.CrossExchangeArbitrager
	FundingArbitragers       []*funding_arbitrage.FundingArbitrager

	// 没有 finished 的订单，已经 finished 的订单都写入到文件中
	// 按 controller id 单独文件存储
	ConditionalOrders *exchange.SyncMapOf[string, []*exchange.ConditionalOrder] `json:"-"`

	mutex sync.Mutex
}

func SetupStroage(controller *OrderController) {
	storage := &OrderStorage{
		BaseStorage: base.BaseStorage{
			Controller: controller,
		},
	}
	// 紧接着赋值给 Storager，防止没有赋值前调用 Storager 相关的方法导致崩溃
	storage.Storager = storage

	if ok := storage.ReadFrom(controller.ConfigPath, controller.ID); !ok {
		storage.Orders = []*exchange.OrderRecord{}
		storage.Assets = map[int64]float64{}
		storage.Groups = &option.NamedList{}
		storage.CrossExchangeArbitragers = []*cross_exchange_arbitrage.CrossExchangeArbitrager{}
		storage.FundingArbitragers = []*funding_arbitrage.FundingArbitrager{}
	}
	for _, arbitrager := range storage.CrossExchangeArbitragers {
		// Exchange 初始化需要一些时间，稍后在 CheckExchanges 中初始化
		arbitrager.SymbolLocks = exchange.NewSyncMapOf[string, *cross_exchange_arbitrage.SymbolLock]()
		arbitrager.SetController(controller)
		arbitrager.LoadOptions()
		arbitrager.CheckExchanges() // Exchange 可能因为延迟不能初始化成功，但是 controller 可以先初始化；后续需要用到 exchange 的时候再检查
		if arbitrager.SymbolParams == nil {
			arbitrager.SymbolParams = exchange.NewSyncMapOf[string, *option.Options]()
		}
		arbitrager.LoadParams() // 加载风险参数，必须在 LoadOptions 之后，因为其中的值依赖于 options
	}
	for _, arbitrager := range storage.FundingArbitragers {
		arbitrager.SymbolLocks = exchange.NewSyncMapOf[string, *funding_arbitrage.SymbolLock]()
		arbitrager.SetController(controller)
		arbitrager.LoadOptions()
		arbitrager.CheckExchanges() // Exchange 可能因为延迟不能初始化成功，但是 controller 可以先初始化；后续需要用到 exchange 的时候再检查
		if arbitrager.SymbolParams == nil {
			arbitrager.SymbolParams = exchange.NewSyncMapOf[string, *option.Options]()
		}
		arbitrager.LoadParams() // 加载风险参数，必须在 LoadOptions 之后，因为其中的值依赖于 options
	}

	if storage.ConditionalOrders == nil {
		storage.ConditionalOrders = exchange.NewSyncMapOf[string, []*exchange.ConditionalOrder]()
	}

	controller.crossExchangeArbitragers = storage.CrossExchangeArbitragers
	controller.fundingArbitragers = storage.FundingArbitragers
	controller.storage = storage
}

func (s *OrderStorage) ReadFrom(configPath, id string) bool {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".order_storage")
	f, _ := os.OpenFile(path, os.O_RDWR|os.O_CREATE, 0755)
	defer f.Close()
	file, err := io.ReadAll(f)
	if err != nil {
		zlog.Errorf("[%s] read storage file error: %s", s.Controller.GetID(), err.Error())
		return false
	}
	zlog.Infof("[%s] local storage file (%s) loaded", s.Controller.GetID(), path)
	err = json.Unmarshal(file, s)
	if err != nil {
		zlog.Errorf("[%s] read json file error: %s", s.Controller.GetID(), err)
		return false
	}
	if s.Groups == nil {
		s.Groups = &option.NamedList{}
	}
	if s.CrossExchangeArbitragers == nil {
		s.CrossExchangeArbitragers = []*cross_exchange_arbitrage.CrossExchangeArbitrager{}
	}
	if s.FundingArbitragers == nil {
		s.FundingArbitragers = []*funding_arbitrage.FundingArbitrager{}
	}

	if err := s.readOpenConditionalOrdersFrom(id); err != nil {
		zlog.Errorf("[%s] read open conditional orders error: %s", s.Controller.GetID(), err)
		return false
	}

	return true
}

func (s *OrderStorage) getOpenConditionalOrdersPath(id string) string {
	configPath := s.Controller.GetConfigPath()
	configPath = filepath.Join(configPath, "conditional_orders/open")
	// 创建文件夹
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.MkdirAll(configPath, 0755)
	}
	return filepath.Join(configPath, fmt.Sprintf("%s.open_conditional_orders", id))
}

func (s *OrderStorage) readOpenConditionalOrdersFrom(id string) error {
	path := s.getOpenConditionalOrdersPath(id)
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return nil
	}
	f, _ := os.OpenFile(path, os.O_RDWR|os.O_CREATE, 0755)
	defer f.Close()
	file, err := io.ReadAll(f)
	if err != nil {
		return err
	}
	openConditionalOrders := exchange.NewSyncMapOf[string, []*exchange.ConditionalOrder]()
	if err := json.Unmarshal(file, &openConditionalOrders); err != nil {
		return err
	}
	s.ConditionalOrders = openConditionalOrders
	return nil
}

func (s *OrderStorage) SaveTo(configPath string, id string, overwrite bool) error {
	s.mutex.Lock()
	startTime := time.Now()
	defer func() {
		zlog.Infof("[%s] save to storage took %s", s.Controller.GetID(), time.Since(startTime))
		s.mutex.Unlock()
	}()

	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, fmt.Sprintf("%s.order_storage", id))
	if _, err := os.Stat(path); !os.IsNotExist(err) && !overwrite {
		return fmt.Errorf("[%s] %s already exist, can not overwrite", s.Controller.GetID(), path)
	}

	// backup old storage file, if the old file is valid json
	oldData, err := os.ReadFile(path)
	if err != nil {
		zlog.Errorf("[%s] read old storage file error: %s", s.Controller.GetID(), err)
		return err
	}
	if gjson.Valid(string(oldData)) {
		backupPath := filepath.Join(configPath, fmt.Sprintf("%s.order_storage.bak", id))
		if err := utils.CopyFile(path, backupPath); err != nil {
			zlog.Errorf("[%s] backup old storage file error: %s", s.Controller.GetID(), err)
			return err
		}
	}

	// Wrap marshal in a single recover
	// marshal 的过程中，如果其中的子结构中的 slices 可能发生变化，导致 crash
	// 因此，需要手工 recover 忽略错误
	data, err := func() (data []byte, err error) {
		defer func() {
			if r := recover(); r != nil {
				err = fmt.Errorf("[%s] panic during storage marshal: %v", s.Controller.GetID(), r)
			}
		}()

		data, err = json.MarshalIndent(s, "", "    ")
		if err != nil {
			return nil, fmt.Errorf("marshal error: %s", err)
		}
		return data, nil
	}()

	if err != nil {
		zlog.Errorf("[%s] marshal storage error: %s", s.Controller.GetID(), err)
		return err
	}

	if err := os.WriteFile(path, data, 0755); err != nil {
		zlog.Errorf("[%s] save storage to file, error: %s", s.Controller.GetID(), err)
		return err
	}

	if err := s.saveOpenConditionalOrdersTo(id); err != nil {
		zlog.Errorf("[%s] save open conditional orders error: %s", s.Controller.GetID(), err)
		return err
	}

	return nil
}

func (s *OrderStorage) saveOpenConditionalOrdersTo(id string) error {
	path := s.getOpenConditionalOrdersPath(id)
	f, _ := os.OpenFile(path, os.O_RDWR|os.O_CREATE, 0755)
	defer f.Close()

	// backup old storage file, if the old file is valid json
	oldData, err := os.ReadFile(path)
	if err != nil {
		zlog.Errorf("[%s] read old open conditional orders file error: %s", s.Controller.GetID(), err)
		return err
	}
	if gjson.Valid(string(oldData)) {
		backupPath := fmt.Sprintf("%s.bak", path)
		if err := utils.CopyFile(path, backupPath); err != nil {
			zlog.Errorf("[%s] backup old open conditional orders file error: %s", s.Controller.GetID(), err)
			return err
		}
	}

	// Wrap marshal in a single recover
	// marshal 的过程中，如果其中的子结构中的 slices 可能发生变化，导致 crash
	// 因此，需要手工 recover 忽略错误
	data, err := func() (data []byte, err error) {
		defer func() {
			if r := recover(); r != nil {
				err = fmt.Errorf("[%s] panic during storage marshal: %v", s.Controller.GetID(), r)
			}
		}()

		data, err = json.MarshalIndent(s.ConditionalOrders, "", "    ")
		if err != nil {
			return nil, fmt.Errorf("marshal error: %s", err)
		}
		return data, nil
	}()

	if err != nil {
		return err
	}

	if err := os.WriteFile(path, data, 0755); err != nil {
		return err
	}
	return nil
}

func (s *OrderStorage) getFinishedConditionalOrdersPath(controllerID string) string {
	configPath := s.Controller.GetConfigPath()
	configPath = filepath.Join(configPath, "conditional_orders/finished")
	// 创建文件夹
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.MkdirAll(configPath, 0755)
	}
	return filepath.Join(configPath, fmt.Sprintf("%s.finished_conditional_orders", controllerID))
}

var saveFinishedConditionalOrderMutex = exchange.NewSyncMapOf[string, sync.Mutex]()

func (s *OrderStorage) saveFinishedConditionalOrder(order *exchange.ConditionalOrder) error {
	path := s.getFinishedConditionalOrdersPath(order.ControllerID)
	mutex, _ := saveFinishedConditionalOrderMutex.LoadOrStore(path, sync.Mutex{})
	mutex.Lock()
	defer mutex.Unlock()

	order.CreateTime = utils.Ptr(time.Now()) // 写入历史记录文件前，设置 CreateTime，方便后续用 backscanner.TruncateJsonFileByCreateTime 清理历史记录
	json, err := json.Marshal(order)
	if err != nil {
		return err
	}
	if _, err := os.Stat(path); os.IsNotExist(err) {
		os.Create(path)
	}
	f, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = f.WriteString(fmt.Sprintf("%s\n", string(json)))
	if err != nil {
		return err
	}
	return nil
}

func (s *OrderStorage) lookupFinishedConditionalOrder(controllerID, orderID string) (order *exchange.ConditionalOrder) {
	path := s.getFinishedConditionalOrdersPath(controllerID)
	backscanner.BackScan(path, func(line []byte) bool {
		order = &exchange.ConditionalOrder{}
		err := json.Unmarshal(line, order)
		if err == nil {
			if orderID == order.Order.OrderID {
				return false
			}
		}
		return true
	})
	return
}

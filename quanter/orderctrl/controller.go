package orderctrl

import (
	"fmt"
	"sync"
	"time"

	"github.com/wizhodl/quanter/arbitrage/cross_exchange_arbitrage"
	"github.com/wizhodl/quanter/arbitrage/funding_arbitrage"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/messenger"
	"github.com/wizhodl/quanter/utils"
)

type OrderController struct {
	base.BaseController

	Config *OrderControllerConfig

	conditionalOrderManager *ConditionalOrderManager
	listControllerFunc      func() []base.Controllable
	usingController         base.Controllable

	// strategies
	crossExchangeArbitragers []*cross_exchange_arbitrage.CrossExchangeArbitrager
	fundingArbitragers       []*funding_arbitrage.FundingArbitrager

	orderMutex *sync.Mutex
	storage    *OrderStorage
}

func NewOrderController(id string, debug bool, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath string, parentMessenger *messenger.SlackMessenger, managerID string, listControllerFunc func() []base.Controllable) (*OrderController, error) {
	if listControllerFunc == nil {
		return nil, fmt.Errorf("listControllerFunc is nil")
	}
	controller := &OrderController{
		BaseController: base.BaseController{
			BaseResponder: command.NewBaseResponder(debug, commitHash, buildTime, id, nil, ""),
			ID:            id,
			RefID:         utils.NewRandomID(),
			ConfigPath:    configPath,
		},
		listControllerFunc:       listControllerFunc,
		orderMutex:               &sync.Mutex{},
		crossExchangeArbitragers: []*cross_exchange_arbitrage.CrossExchangeArbitrager{},
		fundingArbitragers:       []*funding_arbitrage.FundingArbitrager{},
	}
	controller.Controllable = controller
	controller.Setup(debug, parentMessenger, managerID)

	config, err := NewOrderControllerConfig(controller)
	if err != nil {
		return nil, err
	}
	controller.Config = config
	if logDirPath != "" {
		config.LogDirPath = logDirPath
	}

	controller.conditionalOrderManager = NewConditionalOrderManager(controller)

	SetupStroage(controller)

	controller.AddCommands([]command.Commander{
		NewStatusOrderCommand(controller),
		NewOrdersOrderCommand(controller),
		NewBuyOrderCommand(controller),
		NewSellOrderCommand(controller),
		NewLongOrderCommand(controller),
		NewShortOrderCommand(controller),
		NewCancelOrderCommand(controller),
		NewTransferOrderCommand(controller),
		NewWithdrawOrderCommand(controller),
		NewHoldingsOrderCommand(controller),
		NewPositionOrderCommand(controller),
		NewUseControllerOrderCommand(controller),
		NewGroupsControllersOrderCommand(controller),
		NewConfigOrderCommand(controller),
		cmds.NewPagerCommand(),
		cross_exchange_arbitrage.NewCrossExchangeArbitrageCommand(controller),
		cmds.NewPagerCommand(),
		funding_arbitrage.NewFundingArbitragerCommand(controller),
		cmds.NewPagerCommand(),
		NewFundingHistoryOrderCommand(controller),
		NewPriceTriggersCommand(controller),
		NewPriceWatchCommand(controller),
		NewAssetsCommand(controller),
		cmds.NewMuteCommand(),
		cmds.NewDebugCommand(),
		cmds.NewStackTraceCommand(),
		cmds.NewLogCommand(controller.Config.LogDirPath, controller.GetLogFilename()),
		cmds.NewDeleteErrorsCommand(),
	})

	return controller, nil
}

func (this *OrderController) Run() {
	tasks := []base.CronTask{
		{Spec: "@every 5m", Cmd: this.syncOrders, WithoutExchange: true},
		{Spec: "@every 30s", Cmd: this.syncStopLimitOrders, WithoutExchange: true},
	}
	this.BaseController.Run(tasks)
	for _, arbitrager := range this.crossExchangeArbitragers {
		go arbitrager.Run()
	}
	for _, arbitrager := range this.fundingArbitragers {
		go arbitrager.Run()
	}

	// init stop limit orders price triggers
	this.storage.ConditionalOrders.Range(func(controllerID string, stopLimitOrders []*exchange.ConditionalOrder) bool {
		usingController := this.GetController(controllerID)
		if usingController == nil {
			this.WarnMsgf("controller %s not found", controllerID)
			return true
		}
		var ex exchange.Exchange
		ex = usingController.GetExchange()
		if ex == nil {
			retries := 0
			for {
				time.Sleep(5 * time.Second)
				ex = usingController.GetExchange()
				if ex != nil {
					break
				}
				retries++
				if retries%10 == 0 {
					this.WarnMsgf("exchange not initialized for controller %s, retrying...", controllerID)
				}
			}
		}
		for _, stopLimitOrder := range stopLimitOrders {
			if stopLimitOrder.Order.Status == exchange.OrderStatusNew {
				ex.RegisterPriceTrigger(stopLimitOrder.PriceTrigger)
			}
		}
		return true
	})

	if this.Debug {
		time.Sleep(10 * time.Second)
		this.syncOrders()
	}
}

func (this *OrderController) _testFrequentMsg() {
	for i := 0; i < 3; i++ {
		this.SendMsgf("test send msg %d", i)
		time.Sleep(1 * time.Second)
	}
	for i := 0; i < 13; i++ {
		this.SendMsgf("test send msg identical")
		time.Sleep(1 * time.Second)
	}
}

func (this *OrderController) GetExchange() exchange.Exchange {
	return this.usingController.GetExchange()
}

func (this *OrderController) syncOrders() {
	orders := this.GetOrderRecords()
	for _, order := range orders {
		this.queryOrder(order)
	}
}

func (this *OrderController) GetOrderRecords() []*exchange.OrderRecord {
	this.orderMutex.Lock()
	defer this.orderMutex.Unlock()

	copyOrders := make([]*exchange.OrderRecord, len(this.storage.Orders))
	copy(copyOrders, this.storage.Orders)
	return copyOrders
}

func (this *OrderController) AppendOrderRecord(exOrder *exchange.Order, controllerID, exchangeName string, code *exchange.SymbolCode) *exchange.OrderRecord {
	this.orderMutex.Lock()
	defer this.orderMutex.Unlock()

	order := &exchange.OrderRecord{
		Order:        exOrder,
		SymbolCode:   code,
		ControllerID: controllerID,
		ExchangeName: exchangeName,
	}
	this.storage.Orders = append(this.storage.Orders, order)
	this.storage.Save()

	go func() {
		time.Sleep(5 * time.Second)
		this.queryOrder(order)
	}()

	return order
}

func (this *OrderController) GetController(controllerID string) base.Controllable {
	for _, ctrl := range this.listControllerFunc() {
		if ctrl.GetID() == controllerID {
			return ctrl
		}
	}
	return nil
}

func (this *OrderController) ListControllers() []base.Controllable {
	return this.listControllerFunc()
}

func (this *OrderController) queryOrder(order *exchange.OrderRecord) {
	if !order.Order.IsOpen() {
		return
	}

	orderController := this.GetController(order.ControllerID)
	if orderController == nil {
		this.ErrorMsgf("order controller %s not found", order.ControllerID)
		return
	}
	ex := orderController.GetExchange()
	if ex == nil {
		this.ErrorMsgf("exchange is nil for controller %s", order.ControllerID)
		return
	}

	exOrder, err := ex.GetOrderByOrig(*order.Order)
	if err != nil {
		this.Errorf("failed to get order by orig: %s", err)
		return
	}
	if exOrder.Status == order.Order.Status && exOrder.ExecQty == order.Order.ExecQty {
		return
	}
	order.Order = exOrder
	this.storage.Save()

	if !exOrder.IsOpen() {
		this.SendMsgf("订单 %s 已完成:\n```%s```", exOrder.OrderID, this.printOrders([]*exchange.OrderRecord{order}))
	}
}

func (this *OrderController) printOrders(orders []*exchange.OrderRecord) string {
	t := utils.NewTable()
	t.SetHeader([]string{"controller", "exchange", "Side", "Symbol", "OrderType", "Price", "TriggerPrice", "Qty", "Cost", "ExecPrice", "Filled", "Status", "OrderID", "Time"})
	for _, orderRecord := range orders {
		orderController := this.GetController(orderRecord.ControllerID)
		if orderController == nil {
			this.ErrorMsgf("order controller %s not found", orderRecord.ControllerID)
			continue
		}
		ex := orderController.GetExchange()
		if ex == nil {
			this.ErrorMsgf("exchange is nil for controller %s", orderRecord.ControllerID)
			continue
		}

		order := orderRecord.Order
		side := string(order.Side)
		if order.InstrumentType.IsFuture() {
			if order.Side == exchange.OrderSideBuy {
				side = "Long"
			} else {
				side = "Short"
			}
		}
		time := "-"
		if order.CreateTime != nil {
			time = utils.FormatShortTimeStr(order.CreateTime, false)
		} else if order.UpdateTime != nil {
			time = utils.FormatShortTimeStr(order.UpdateTime, false)
		}

		baseCoin := ""
		quoteCoin := ""
		if orderRecord.SymbolCode != nil {
			baseCoin = orderRecord.SymbolCode.Coin()
			quoteCoin = orderController.GetExchange().GetSymbolCodeQuote(orderRecord.SymbolCode)
		}
		qtyStr := "-"
		costStr := "-"
		if order.Qty > 0 {
			if order.InstrumentType.IsFuture() {
				qtyStr = fmt.Sprintf("%v cnt", order.Qty)
				if order.ExecPrice > 0 {
					v, _ := orderController.GetExchange().Qty2Size(order.InstrumentType, order.Symbol, order.ExecPrice, order.ExecQty)
					if v > 0 {
						ins, _ := orderController.GetExchange().GetInstrument(order.InstrumentType, order.Symbol)
						if ins != nil {
							costStr = formatAmount(v, ins.SettleCurrency)
						}
					}
				}
			} else {
				qtyStr = fmt.Sprintf("%v %s", order.Qty, baseCoin)
				costStr = formatAmount(order.ExecQty*order.ExecPrice, quoteCoin)
			}
		} else if order.QuoteQty > 0 {
			if order.InstrumentType.IsFuture() {
				costStr = fmt.Sprintf("%v%s", order.QuoteQty, order.InstrumentType.GetSuffix())
			} else {
				if order.Price > 0 {
					qtyStr = fmt.Sprintf("%s %s", orderController.GetExchange().FormatQty(order.InstrumentType, order.Symbol, order.QuoteQty/order.Price), baseCoin)
				}
				costStr = fmt.Sprintf("%v %s", order.QuoteQty, quoteCoin)
			}
		}
		execQtyStr := fmt.Sprintf("%v", order.ExecQty)
		if order.ExecQty != 0 {
			if order.InstrumentType.IsFuture() {
				execQtyStr = fmt.Sprintf("%v cnt", order.ExecQty)
			} else if orderRecord.SymbolCode != nil {
				execQtyStr = fmt.Sprintf("%v %s", order.ExecQty, orderRecord.SymbolCode.Coin())
			} else {
				execQtyStr = fmt.Sprintf("%v", order.ExecQty)
			}
		}
		t.AddRow([]string{
			orderRecord.ControllerID,
			orderRecord.ExchangeName,
			side,
			order.Symbol,
			string(order.Type),
			ex.FormatPrice(order.InstrumentType, order.Symbol, order.Price),
			ex.FormatPrice(order.InstrumentType, order.Symbol, order.TriggerPrice),
			qtyStr,
			costStr,
			ex.FormatPrice(order.InstrumentType, order.Symbol, order.ExecPrice),
			execQtyStr,
			string(order.Status),
			order.OrderID,
			time,
		})
	}
	return t.Render()
}

func (this *OrderController) GetBaseConfig() *baseconfig.BaseConfig {
	return &this.Config.BaseConfig
}

func (this *OrderController) SaveConfig(configStr string) (correctedConfigStr string, er error) {
	return "", nil
}

func (this *OrderController) GetStorage() base.Storager {
	return this.storage
}

func (this *OrderController) GetStopLimitOrderWrapper(controllerID string) exchange.Orderable {
	return this.conditionalOrderManager.Using(controllerID)
}

func (this *OrderController) priceTriggeredCallback(priceTrigger *exchange.PriceTrigger) {
	this.Infof("price triggered callback: %#v", priceTrigger)
	orders := this.conditionalOrderManager.getOpenConditionalOrders(priceTrigger.ControllerID)
	for _, order := range orders {
		if order.PriceTrigger.ID == priceTrigger.ID && priceTrigger.Triggered {
			this.Infof("stop limit order triggered: %#v", order)
			err := this.conditionalOrderManager.triggerConditionalOrder(order)
			if err != nil {
				this.ErrorMsgf("trigger order %s failed: %s", order.Order.OrderID, err)
			}
		}
	}
}

func (this *OrderController) syncStopLimitOrders() {
	finishedOrders := []*exchange.ConditionalOrder{}
	this.storage.ConditionalOrders.Range(func(controllerID string, stopLimitOrders []*exchange.ConditionalOrder) bool {
		for _, order := range stopLimitOrders {
			finished := this.syncConditionalOrder(order)
			if finished {
				finishedOrders = append(finishedOrders, order)
			}
		}
		return true
	})

	for _, order := range finishedOrders {
		this.conditionalOrderManager.archiveOpenOrder(order.ControllerID, order.Order.OrderID)
	}
}

func (this *OrderController) syncConditionalOrder(condOrder *exchange.ConditionalOrder) (finished bool) {
	if !condOrder.Order.IsOpen() || condOrder.Order.Status == exchange.OrderStatusNew {
		// 已完成订单和未触发订单不需要查询
		return
	}

	controller := this.GetController(condOrder.ControllerID)
	ex := controller.GetExchange()
	if ex == nil {
		this.Errorf("exchange is nil for controller %s", condOrder.ControllerID)
		return
	}

	if condOrder.ExchangeOrder == nil {
		// 还未下单
		return
	}

	exOrder, err := ex.GetOrderByOrig(*condOrder.ExchangeOrder)
	if err != nil {
		this.Errorf("failed to get order by orig, ex order: %v, err: %s", condOrder.ExchangeOrder, err)
		return
	}
	if exOrder.Status == condOrder.ExchangeOrder.Status && exOrder.ExecQty == condOrder.ExchangeOrder.ExecQty {
		return
	}
	condOrder.ExchangeOrder = exOrder

	condOrder.Order.ExecQty = exOrder.ExecQty
	condOrder.Order.ExecPrice = exOrder.ExecPrice
	if exOrder.Status != exchange.OrderStatusNew {
		condOrder.Order.Status = exOrder.Status
		condOrder.Order.UpdateTime = exOrder.UpdateTime
	}

	this.conditionalOrderManager.saveConditionalOrder(condOrder)
	if !exOrder.IsOpen() {
		this.SendMsgf("触发订单 %s 已完成: status: %s, execQty: %v, execPrice: %v", condOrder.Order.OrderID, exOrder.Status, exOrder.ExecQty, exOrder.ExecPrice)

		finished = true

		ex.OrderUpdatedCallback(condOrder.Order)
	}

	return
}

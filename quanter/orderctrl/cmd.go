package orderctrl

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"github.com/wizhodl/quanter/arbitrage/cross_exchange_arbitrage"
	"github.com/wizhodl/quanter/arbitrage/funding_arbitrage"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/gateway"
	"github.com/wizhodl/quanter/utils"
)

type OrderCommand struct {
	command.Command
	controller *OrderController
}

func (this *OrderCommand) GetExchange() exchange.Exchange {
	return this.controller.usingController.GetExchange()
}

func (this *OrderCommand) PrePrepare() bool {
	excludeList := []string{"use", "status"}
	if !utils.SliceContainsEqualFold(excludeList, this.Name) {
		if this.controller.usingController == nil {
			this.ErrorMsgf("请先使用 `.use` 命令选择 controller")
			return false
		} else if this.GetExchange() == nil {
			this.ErrorMsgf("controller %s exchange 未初始化", this.controller.usingController.GetID())
			return false
		}
	}
	if this.controller.usingController != nil && this.GetExchange() != nil {
		this.SendMsgf("当前正在使用控制器 *%s*, 交易所: *%s*", this.controller.usingController.GetID(), this.GetExchange().GetName())
	}
	return true
}

func (this *OrderCommand) PostDo() bool {
	this.controller.SetLastUseTime(time.Now())
	return true
}

type UseControllerOrderCommand struct {
	OrderCommand
}

type StatusOrderCommand struct {
	OrderCommand
}

func NewStatusOrderCommand(controller *OrderController) *StatusOrderCommand {
	return &StatusOrderCommand{
		OrderCommand: OrderCommand{
			Command: command.Command{
				Name:            "status",
				Alias:           []string{"s"},
				Instruction:     "`.status` 打印状态",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *StatusOrderCommand) Do() bool {
	var strategies []base.Strategy
	for _, arbitrager := range this.controller.ListStrategies() {
		if arbitrager.GetStrategyType() == base.StrategyTypeCrossExchangeArbitrager {
			strategies = append(strategies, arbitrager)
		}
	}

	t := exchange.NewTable()
	t.SetHeader(cross_exchange_arbitrage.CrossExchangeArbitragerStatusHeader)
	for _, arbitrager := range strategies {
		t.AddRow(arbitrager.(*cross_exchange_arbitrage.CrossExchangeArbitrager).RenderStatusRow())
	}
	if len(t.Rows) > 1 {
		this.SendMsgf("跨交易所套利机:\n```%s```", t.Render())
	} else {
		this.SendMsgf("[no cross exchange arbitrager]")
	}

	strategies = []base.Strategy{}
	for _, arbitrager := range this.controller.ListStrategies() {
		if arbitrager.GetStrategyType() == base.StrategyTypeFundingArbitrager {
			strategies = append(strategies, arbitrager)
		}
	}

	t2 := exchange.NewTable()
	t2.SetHeader(funding_arbitrage.StatusHeader)
	for _, arbitrager := range strategies {
		t2.AddRow(arbitrager.(*funding_arbitrage.FundingArbitrager).RenderStatusRow())
	}
	if len(t2.Rows) > 1 {
		this.SendMsgf("资金费套利机:\n```%s```", t2.Render())
	} else {
		this.SendMsgf("[no funding arbitrager]")
	}
	return true
}

func NewUseControllerOrderCommand(controller *OrderController) *UseControllerOrderCommand {
	return &UseControllerOrderCommand{
		OrderCommand: OrderCommand{
			Command: command.Command{
				Name:            "use",
				Alias:           []string{"u"},
				Instruction:     "`.use controllerID` 使用控制器 API",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *UseControllerOrderCommand) Do() bool {
	if len(this.Args) == 0 {
		t := exchange.NewTable()
		t.SetHeader([]string{"ID", "Exchange", "APIExpireTime", "Proxy", "Exchange.Proxy"})
		for _, ctrl := range this.controller.ListControllers() {
			ex := ctrl.GetExchange()
			if ex == nil {
				this.ErrorMsgf("exchange not initialized: %s", ctrl.GetID())
				continue
			}
			proxyURL := ctrl.GetBaseConfig().ProxyUrl
			apiExpireTimeStr := ctrl.FormatAPIExpireTime()
			t.AddRow([]string{ctrl.GetID(), ex.GetName(), apiExpireTimeStr, proxyURL, ex.GetProxy()})
		}
		this.SendMsgf("```%s```", t.Render())
		return true
	}
	controllerID := this.Args[0]
	this.controller.usingController = this.controller.GetController(controllerID)
	if this.controller.usingController == nil {
		this.ErrorMsgf("控制器 %s 不存在", controllerID)
		return false
	}
	this.controller.usingController.SetLastUseTime(time.Now())
	ex := this.GetExchange()
	if ex == nil {
		this.ErrorMsgf("exchange not initialized: %s", this.controller.usingController.GetID())
	}
	this.SendMsgf("已选择控制器 %s, 交易所: %s", this.controller.usingController.GetID(), ex.GetName())
	return true
}

type GroupsControllersOrderCommand struct {
	command.Command
	controller *OrderController
}

func NewGroupsControllersOrderCommand(controller *OrderController) *GroupsControllersOrderCommand {
	return &GroupsControllersOrderCommand{
		Command: command.Command{
			Name:                   "group",
			Alias:                  []string{"g"},
			Instruction:            "`.group print(默认)/add/set/remove/delete groupName controllerID1,controllerID2,...` 打印/添加/设置/移除/删除 组控制器",
			RequiresConfirm:        false,
			SkipConfirmSubcommands: []string{"print"},
			DefaultSubcommand:      "print",
			ArgMin:                 1,
			ArgMax:                 3,
		},
		controller: controller,
	}
}

func (this *GroupsControllersOrderCommand) Do() bool {
	op := strings.ToLower(this.Args[0])
	if op == "print" {
		this.SendMsgf("groups: ```%s```", this.controller.storage.Groups.ToTable("Group", "Controllers"))
		return true
	}

	if len(this.Args) < 2 {
		this.ErrorMsgf("参数 groupName 不能为空")
		return false
	}

	groupName := this.Args[1]
	controllerIDList := []string{}
	if len(this.Args) > 2 {
		controllerIDs := this.Args[2]
		for _, controllerID := range strings.Split(controllerIDs, ",") {
			if this.controller.GetController(controllerID) == nil {
				this.ErrorMsgf("控制器 %s 不存在", controllerID)
			} else if !utils.SliceContains(controllerIDList, controllerID) {
				controllerIDList = append(controllerIDList, controllerID)
			}
		}
	}

	if len(controllerIDList) == 0 && op != "delete" {
		this.ErrorMsgf("未找到有效的控制器")
		return false
	}

	if op == "add" {
		this.controller.storage.Groups.Add(groupName, controllerIDList)
		this.SendMsgf("%s 已添加到 group %s", strings.Join(controllerIDList, ","), groupName)
	} else if op == "set" {
		this.controller.storage.Groups.Set(groupName, controllerIDList)
		this.SendMsgf("group %s 已设置为: %s", groupName, strings.Join(controllerIDList, ","))
	} else if op == "delete" {
		this.controller.storage.Groups.Delete(groupName)
		this.SendMsgf("group %s 已删除", groupName)
	} else if op == "remove" {
		this.controller.storage.Groups.RemoveFrom(groupName, controllerIDList)
		this.SendMsgf("已从 group %s 中删除: %s", groupName, strings.Join(controllerIDList, ","))
	} else {
		this.ErrorMsgf("未知操作 %s", op)
		return false
	}
	this.controller.storage.Save()
	return true
}

type OrdersOrderCommand struct {
	OrderCommand
}

func NewOrdersOrderCommand(controller *OrderController) *OrdersOrderCommand {
	cmd := &OrdersOrderCommand{
		OrderCommand: OrderCommand{
			Command: command.Command{
				Name:            "orders",
				Alias:           []string{"o"},
				Instruction:     "`.orders` symbolCode[可选] 列出服务器上的 open 的订单和本地 ConditionalOrder 订单",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *OrdersOrderCommand) Do() bool {
	symbol := ""
	instrumentTypes := this.GetExchange().GetSupportedInstrumentTypes()
	if len(this.Args) > 0 {
		symbolCode, err := this.controller.usingController.NewSymbolCode(this.Args[0])
		if err != nil {
			this.ErrorMsgf("参数错误：%s", err.Error())
			return false
		}
		if symbolCode.IsSpot() {
			symbol, err = this.GetExchange().TranslateSymbolCodeToSpotSymbol(symbolCode)
			if err != nil {
				this.ErrorMsgf("获取 symbol 信息出错：%s", err)
				return false
			}
		} else {
			symbol, err = this.GetExchange().TranslateSymbolCodeToFutureSymbol(symbolCode)
			if err != nil {
				this.ErrorMsgf("获取 symbol 信息出错：%s", err)
				return false
			}
		}
		instrumentTypes = []exchange.InstrumentType{symbolCode.InstrumentType()}
	}

	if this.GetExchange().GetName() == exchange.Bybit && symbol == "" {
		this.ErrorMsgf("BYBIT 交易所 API 必须指定 symbolCode 查询订单")
		return false
	}

	orders := []*exchange.OrderRecord{}
	for _, instrumentType := range instrumentTypes {
		if symbol != "" {
			_, err := this.GetExchange().GetInstrument(instrumentType, symbol)
			if err != nil {
				this.controller.Warnf("get instrument err, instrumentType: %s, symbol: %s, error: %s", instrumentType, symbol, err)
				continue
			}
		}

		eOrders, err := this.GetExchange().GetOpenOrders(instrumentType, exchange.UnknownOrderType, symbol)
		if err != nil {
			if err == exchange.ErrNotImplemented || err == exchange.ErrNotAvailableForInstrumentType {
				continue
			}
			this.ErrorMsgf("获取订单出错，instrumentType: %s, error: %s", instrumentType, err)
			continue
		}
		for _, order := range eOrders {
			orders = append(orders, &exchange.OrderRecord{
				Order:        order,
				ControllerID: this.controller.usingController.GetID(),
				ExchangeName: this.GetExchange().GetName(),
			})
		}
	}
	if len(orders) == 0 {
		this.SendMsgf("无 open 订单")
	} else {
		this.SendMsgf("open 订单：\n```%s```", this.controller.printOrders(orders))
	}

	allConditionalOrders := this.controller.conditionalOrderManager.getOpenConditionalOrders(this.controller.usingController.GetID())
	conOrders := []*exchange.OrderRecord{}
	for _, conOrder := range allConditionalOrders {
		if symbol != "" && conOrder.Order.Symbol != symbol {
			continue
		}
		conOrders = append(conOrders, &exchange.OrderRecord{
			Order:        conOrder.Order,
			ControllerID: conOrder.ControllerID,
			ExchangeName: conOrder.ExchangeName,
		})
	}
	if len(conOrders) == 0 {
		this.SendMsgf("无 ConditionalOrder 订单")
	} else {
		this.SendMsgf("ConditionalOrder 订单：\n```%s```", this.controller.printOrders(conOrders))
	}

	return true
}

type HoldingsOrderCommand struct {
	command.Command
	controller *OrderController
}

func NewHoldingsOrderCommand(controller *OrderController) *HoldingsOrderCommand {
	cmd := &HoldingsOrderCommand{
		Command: command.Command{
			Name:            "holdings",
			Alias:           []string{"h"},
			Instruction:     "`.holdings all/controllerID/_groupName[可选]` 打印资产详情，默认打印当前 controller 资产",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
		},
		controller: controller,
	}
	return cmd
}

func (this *HoldingsOrderCommand) Do() bool {
	groupName := ""
	controllerID := ""
	if len(this.Args) == 1 {
		arg := this.Args[0]
		if strings.HasPrefix(arg, "_") {
			groupName = arg[1:]
		} else if arg != "all" {
			controllerID = arg
		}
	}

	// 查看单个 controller 资产
	if len(this.Args) == 0 || controllerID != "" {
		var ctrl base.Controllable
		if controllerID == "" {
			if this.controller.usingController == nil {
				this.ErrorMsgf("请先使用 `.use` 命令选择 controller")
				return false
			}
			ctrl = this.controller.usingController
		} else {
			ctrl = this.controller.GetController(controllerID)
			if ctrl == nil {
				this.ErrorMsgf("controller %s not found", controllerID)
				return false
			}
		}

		balances, err := ctrl.GetAccountBalances(true)
		if err != nil {
			this.ErrorMsgf("获取资产出错，error: %s", err)
			return false
		}

		this.SendMsgf("控制器 *%s* 资产汇总：\n```%s```", ctrl.GetID(), balances.RenderTotalCombined())
		this.SendMsgf("控制器 *%s* 资产明细：\n```%s```", ctrl.GetID(), balances.Render())
		return true
	}

	// 全部 或 指定组 controller 资产
	t := exchange.NewTable()
	t.SetHeader([]string{"Controller", "Exchange", "Worth", "Time"})

	ctrls := []base.Controllable{}
	for _, ctrl := range this.controller.ListControllers() {
		if ctrl.GetExchange() == nil {
			this.ErrorMsgf("exchange not initialized: %s", ctrl.GetID())
			continue
		}
		if groupName != "" {
			ids := this.controller.storage.Groups.Get(groupName)
			if len(ids) == 0 {
				this.ErrorMsgf("group %s not found", groupName)
				return false
			}
			if !utils.SliceContains(ids, ctrl.GetID()) {
				continue
			}
		}
		ctrls = append(ctrls, ctrl)
	}

	totalWorth := 0.0
	for _, ctrl := range ctrls {
		assets := ctrl.GetStorage().GetAssets()
		var lastAssetTime int64
		for k := range assets {
			if k > lastAssetTime {
				lastAssetTime = k
			}
		}
		if lastAssetTime != 0 {
			aTime := time.Unix(lastAssetTime, 0)
			t.AddRow([]string{
				ctrl.GetID(),
				ctrl.GetExchange().GetName(),
				fmt.Sprintf("%.2f USDT", assets[lastAssetTime]),
				utils.FormatShortTimeStr(&aTime, true)},
			)
			totalWorth += assets[lastAssetTime]
		} else {
			t.AddRow([]string{
				ctrl.GetID(),
				ctrl.GetExchange().GetName(),
				"-",
				"-"},
			)
		}
	}

	assetsMsg := "[No Assets]"
	if len(t.Rows) > 1 {
		if len(t.Rows) > 2 {
			t.AddRow([]string{"Total", "", fmt.Sprintf("%.2f USDT", totalWorth), ""})
		}
		assetsMsg = t.Render()
	}

	titlePrefix := ""
	if groupName != "" {
		titlePrefix = fmt.Sprintf("控制器组 *%s* ", groupName)
	} else {
		titlePrefix = "所有控制器"
	}
	this.SendMsgf("%s资产快照：\n```%s```", titlePrefix, assetsMsg)

	t = exchange.NewTable()
	t.SetHeader([]string{"Coin", "Total", "Price", "Worth", "Controller", "Exchange"})
	type balance struct {
		coin   string
		total  float64
		price  float64
		worth  float64
		ctrlID string
		exName string
	}
	type balancesDetail struct {
		total *balance
		list  []*balance
	}
	allBalances := map[string]*balancesDetail{}
	coins := []string{}
	for _, ctrl := range ctrls {
		balances, err := ctrl.GetAccountBalances(true)
		if err != nil {
			this.ErrorMsgf("获取资产出错，error: %s", err)
			return false
		}

		comBalances := balances.GetTotalCombined()
		for coin, bal := range comBalances {
			b := &balance{
				coin:   coin,
				total:  bal.Total,
				worth:  bal.WorthInUSDT,
				ctrlID: ctrl.GetID(),
				exName: ctrl.GetExchange().GetName(),
			}
			if d, ok := allBalances[coin]; ok {
				d.total.total += bal.Total
				d.total.worth += bal.WorthInUSDT
				d.list = append(d.list, b)
			} else {
				coins = append(coins, coin)
				allBalances[coin] = &balancesDetail{
					total: &balance{
						coin:  coin,
						total: bal.Total,
						price: bal.PriceInUSDT,
						worth: bal.WorthInUSDT,
					},
					list: []*balance{b},
				}
			}
		}
	}

	// sort coins by total worth
	sort.Slice(coins, func(i, j int) bool {
		return allBalances[coins[i]].total.worth > allBalances[coins[j]].total.worth
	})

	for _, coin := range coins {
		detail := allBalances[coin]
		t.AddRow([]string{
			detail.total.coin,
			utils.FormatNum(detail.total.total, 6),
			utils.FormatNum(detail.total.price, 4),
			fmt.Sprintf("%.2f USDT", detail.total.worth),
			"",
			"",
		})
		details := detail.list
		sort.Slice(details, func(i, j int) bool {
			return details[i].worth > details[j].worth
		})
		for _, b := range details {
			t.AddRow([]string{
				"",
				utils.FormatNum(b.total, 6),
				"",
				fmt.Sprintf("%.2f USDT", b.worth),
				b.ctrlID,
				b.exName,
			})
		}
	}

	assetsMsg = "[No Assets]"
	if len(t.Rows) > 1 {
		assetsMsg = t.Render()
	}

	this.SendMsgf("%s资产明细：\n```%s```", titlePrefix, assetsMsg)
	return true
}

type PositionOrderCommand struct {
	OrderCommand
}

func NewPositionOrderCommand(controller *OrderController) *PositionOrderCommand {
	cmd := &PositionOrderCommand{
		OrderCommand: OrderCommand{
			Command: command.Command{
				Name:            "position",
				Alias:           []string{"p"},
				Instruction:     "`.position` 打印合约持仓详情",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
			},
			controller: controller,
		},
	}
	return cmd
}

func formatAmount(amount float64, coin string) string {
	if amount == 0 {
		return "-"
	}
	decimals := 4
	if strings.HasPrefix(coin, "USD") {
		decimals = 2
	} else if strings.EqualFold(coin, "CNY") {
		decimals = 0
	}
	return decimal.NewFromFloat(amount).Round(int32(decimals)).String() + " " + coin
}

func (this *PositionOrderCommand) Do() bool {
	positions := []*exchange.Position{}
	for _, instrumentType := range this.GetExchange().GetSupportedInstrumentTypes() {
		if !instrumentType.IsFuture() {
			continue
		}
		iPositions, err := this.GetExchange().GetPositions(instrumentType, "", false)
		if err != nil {
			this.ErrorMsgf("获取持仓出错, instrumentType: %s, error: %s", instrumentType, err)
			return false
		}
		positions = append(positions, iPositions...)
	}

	if len(positions) == 0 {
		this.SendMsgf("无持仓")
		return true
	}

	posStr, err := exchange.RenderPositions(this.GetExchange(), positions)
	if err != nil {
		this.ErrorMsgf("打印持仓出错：%s", err)
		return false
	}

	if posStr == "" {
		this.SendMsgf("无持仓")
	} else {
		this.SendMsgf("```%s```", posStr)
	}
	return true
}

type BaseOrderCommand struct {
	OrderCommand
	side exchange.OrderSide

	symbolCode   *exchange.SymbolCode
	symbol       string
	qty          float64
	quoteQty     float64
	price        float64
	triggerPrice float64
}

func (this *BaseOrderCommand) reset() {
	this.symbolCode = nil
	this.symbol = ""
	this.qty = 0
	this.quoteQty = 0
	this.price = 0
	this.triggerPrice = 0
}

func (this *BaseOrderCommand) Prepare() bool {
	this.reset()

	code := this.Args[0]
	var err error
	this.symbolCode, err = this.controller.usingController.NewSymbolCode(code)
	if err != nil {
		this.ErrorMsgf("参数错误：%s", err.Error())
		return false
	}

	if !exchange.SliceContains(this.GetExchange().GetSupportedInstrumentTypes(), this.symbolCode.InstrumentType()) {
		this.ErrorMsgf("不支持的 instrumentType: %s", this.symbolCode.InstrumentType())
		return false
	}

	if this.Name == "buy" || this.Name == "sell" {
		if !this.symbolCode.IsSpot() {
			this.ErrorMsgf("指令 %s 只支持现货交易", this.Name)
			return false
		}
	} else if this.Name == "long" || this.Name == "short" {
		if !this.symbolCode.IsFuture() {
			this.ErrorMsgf("指令 %s 只支持合约交易", this.Name)
			return false
		}
	}

	qtyArg := this.Args[1]
	if len(qtyArg) > 2 && exchange.SliceContains([]string{".u", ".d", ".y"}, qtyArg[len(qtyArg)-2:]) {
		if this.symbolCode.IsFuture() {
			suffix := qtyArg[len(qtyArg)-2:]
			validSuffix := this.symbolCode.InstrumentType().GetSuffix()
			if !strings.EqualFold(suffix, validSuffix) {
				this.ErrorMsgf("%s 合约只支持 %s 后缀", this.symbolCode, strings.ToLower(validSuffix))
				return false
			}
		}
		qtyArg = qtyArg[:len(qtyArg)-2]
		this.quoteQty, err = strconv.ParseFloat(qtyArg, 64)
		if err != nil {
			this.ErrorMsgf("qty 参数错误：%s", err.Error())
			return false
		}
	} else {
		this.qty, err = strconv.ParseFloat(qtyArg, 64)
		if err != nil {
			this.ErrorMsgf("qty 参数错误：%s", err.Error())
			return false
		}
	}

	triggerPriceArg := ""
	if len(this.Args) > 4 {
		if strings.Contains(this.Args[4], "=") {
			this.WarnMsgf("暂不支持配置参数, 将忽略")
		}
		triggerPriceArg = this.Args[3]
	} else if len(this.Args) == 4 {
		if strings.Contains(this.Args[3], "=") {
			this.WarnMsgf("暂不支持配置参数, 将忽略")
		} else {
			triggerPriceArg = this.Args[3]
		}
	}

	if triggerPriceArg != "" {
		this.triggerPrice, err = strconv.ParseFloat(triggerPriceArg, 64)
		if err != nil {
			this.ErrorMsgf("triggerPrice 参数错误：%s", err.Error())
			return false
		}
	}

	if this.symbolCode.IsSpot() {
		this.symbol, err = this.GetExchange().TranslateSymbolCodeToSpotSymbol(this.symbolCode)
		if err != nil {
			this.ErrorMsgf("获取 symbol 信息出错：%s", err)
			return false
		}
	} else {
		this.symbol, err = this.GetExchange().TranslateSymbolCodeToFutureSymbol(this.symbolCode)
		if err != nil {
			this.ErrorMsgf("获取 symbol 信息出错：%s", err)
			return false
		}
	}

	if this.symbolCode.IsFuture() {
		positions, err := this.GetExchange().GetPositions(this.symbolCode.InstrumentType(), this.symbol, false)
		if err != nil {
			this.ErrorMsgf("获取持仓出错, instrumentType: %s, symbol: %s, error：%s", this.symbolCode.InstrumentType(), this.symbol, err)
			return false
		}
		for _, position := range positions {
			if this.side == exchange.OrderSideBuy && position.Side == exchange.PositionSideShort {
				this.SendMsgf("当前有反向持仓 %s，将仅减仓", this.GetExchange().FormatQty(position.InstrumentType, position.Symbol, position.Qty))
				break
			} else if this.side == exchange.OrderSideSell && position.Side == exchange.PositionSideLong {
				this.SendMsgf("当前有反向持仓 %s，将仅减仓", this.GetExchange().FormatQty(position.InstrumentType, position.Symbol, position.Qty))
				break
			}
		}
	}

	ins, err := this.GetExchange().GetInstrument(this.symbolCode.InstrumentType(), this.symbol)
	if err != nil {
		this.ErrorMsgf("获取 instrument 出错：%s", err)
		return false
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"controller", "exchange", "Side", "SymbolCode", "Symbol", "Price", "Qty", "Value", "TriggerPrice"})

	var priceStr string
	lastPrice, err := this.GetExchange().GetLastPrice(this.symbolCode.InstrumentType(), this.symbol, false)
	if err != nil {
		this.ErrorMsgf("获取最新价格出错：%s", err)
		return false
	}

	priceArg := this.Args[2]
	if strings.EqualFold(priceArg, "market") {
		this.price = 0
	} else if strings.EqualFold(priceArg, "last") {
		this.price = lastPrice
	} else if strings.HasSuffix(priceArg, "%") {
		delta, err := strconv.ParseFloat(priceArg[:len(priceArg)-1], 64)
		if err != nil {
			this.ErrorMsgf("price 参数错误：%s", err.Error())
			return false
		}
		this.price = lastPrice * (1 + delta/100)
		this.price = this.GetExchange().RoundPrice(this.symbolCode.InstrumentType(), this.symbol, this.price)
	} else {
		this.price, err = strconv.ParseFloat(priceArg, 64)
		if err != nil {
			this.ErrorMsgf("price 参数错误：%s", err.Error())
			return false
		}
	}

	if this.price == 0 {
		priceStr = fmt.Sprintf("market ~%s", this.GetExchange().FormatPrice(this.symbolCode.InstrumentType(), this.symbol, lastPrice))
	} else {
		delta := (this.price - lastPrice) / lastPrice * 100
		if delta > 0 {
			priceStr = fmt.Sprintf("%v (+%.2f%%)", this.price, delta)
		} else if delta < 0 {
			priceStr = fmt.Sprintf("%v (%.2f%%)", this.price, delta)
		} else {
			priceStr = fmt.Sprintf("%v", this.price)
		}
	}

	qtyStr := "-"
	valueStr := "-"
	value := 0.0
	qty := 0.0
	if this.qty > 0 {
		if this.symbolCode.IsSpot() {
			qtyStr = fmt.Sprintf("%v", this.qty)
			value = this.qty * this.price
			valueStr = formatAmount(this.qty*this.price, this.GetExchange().GetSymbolCodeQuote(this.symbolCode))
		} else {
			qtyStr = fmt.Sprintf("%v", this.qty)
			if this.price > 0 {
				v, _ := this.GetExchange().Qty2Size(this.symbolCode.InstrumentType(), this.symbol, this.price, this.qty)
				if v > 0 {
					value = v
					valueStr = formatAmount(v, ins.SettleCurrency)
				}
			}
		}
		qty = this.qty
	} else if this.quoteQty > 0 {
		value = this.quoteQty
		valueStr = fmt.Sprintf("%v %s", this.quoteQty, this.GetExchange().GetSymbolCodeQuote(this.symbolCode))
		price := this.price
		if price == 0 {
			price = lastPrice
		}
		estQty := this.quoteQty / price
		if this.symbolCode.IsFuture() {
			var err error
			estQty, err = this.GetExchange().Size2Qty(this.symbolCode.InstrumentType(), this.symbol, price, this.quoteQty)
			if err != nil {
				this.ErrorMsgf("计算合约 qty 出错：%s", err)
				return false
			}
		}
		qtyStr = fmt.Sprintf("%v", this.GetExchange().FloorQty(this.symbolCode.InstrumentType(), this.symbol, estQty))
		qty = estQty
	}
	triggerPriceStr := "-"
	if this.triggerPrice > 0 {
		triggerPriceStr = fmt.Sprintf("%v", this.triggerPrice)
	}
	side := string(this.side)
	if this.symbolCode.IsFuture() {
		if this.side == exchange.OrderSideBuy {
			side = "Long"
		} else {
			side = "Short"
		}
	}

	// 检查现货可用余额
	if this.symbolCode.IsSpot() {
		if value > 0 && this.side == exchange.OrderSideBuy {
			uSymbol := this.symbolCode.USDXSymbol
			if _, available, err := this.GetExchange().GetHoldingQty(this.symbolCode.InstrumentType(), uSymbol); err != nil {
				this.ErrorMsgf("获取 %s 可用余额出错：%s", uSymbol, err)
			} else {
				if available < value {
					this.ErrorMsgf("可用余额不足，需要 %s，实际可用 %s", formatAmount(value, uSymbol), formatAmount(available, uSymbol))
					return false
				}
			}
		}

		if this.side == exchange.OrderSideSell && qty > 0 {
			if _, available, err := this.GetExchange().GetHoldingQty(this.symbolCode.InstrumentType(), this.symbolCode.Coin()); err != nil {
				this.ErrorMsgf("获取 %s 可用余额出错：%s", this.symbolCode.Coin(), err)
			} else {
				if available < qty {
					this.ErrorMsgf("可用余额不足，需要 %s，实际可用 %s", qtyStr, formatAmount(available, this.symbolCode.Coin()))
					return false
				}
			}
		}
	}

	if this.side == exchange.OrderSideSell {
		if qtyStr != "-" {
			qtyStr = "-" + qtyStr
		}
		if valueStr != "-" {
			valueStr = "-" + valueStr
		}
	}

	t.AddRow([]string{
		this.controller.usingController.GetID(),
		this.GetExchange().GetName(),
		side,
		this.symbolCode.String(),
		this.symbol,
		priceStr,
		fmt.Sprintf("%s %s", qtyStr, this.GetExchange().GetSymbolCodeUnit(this.symbolCode)),
		valueStr,
		triggerPriceStr,
	})

	this.SendMsgf("确认下单:\n```%s```", t.Render())

	return true
}

func (this *BaseOrderCommand) Do() bool {
	orderType := exchange.Limit
	if this.price == 0 {
		orderType = exchange.Market
	} else if this.triggerPrice > 0 {
		orderType = exchange.StopLimit
	}

	tradeMode := exchange.TradeModeCash
	if this.symbolCode.IsFuture() {
		tradeMode = exchange.TradeModeIsolated
	}
	if this.controller.usingController.GetBaseConfig().MarginMode.IsCross() {
		tradeMode = exchange.TradeModeCross
	}

	args := exchange.CreateOrderArgs{
		InstrumentType: this.symbolCode.InstrumentType(),
		Symbol:         this.symbol,
		Type:           orderType,
		TradeMode:      tradeMode,
		Qty:            this.qty,
		QuoteQty:       this.quoteQty,
		TimeInForce:    exchange.GTC,
		Side:           this.side,
	}

	if orderType == exchange.Limit {
		args.Price = this.price
	} else if orderType == exchange.StopLimit {
		args.Price = this.price
		args.TriggerPrice = this.triggerPrice
	}

	if this.symbolCode.IsFuture() {
		positions, err := this.GetExchange().GetPositions(this.symbolCode.InstrumentType(), this.symbol, false)
		if err != nil {
			this.ErrorMsgf("获取持仓出错, instrumentType: %s, symbol: %s, error：%s", this.symbolCode.InstrumentType(), this.symbol, err)
			return false
		}
		for _, position := range positions {
			if this.side == exchange.OrderSideBuy && position.Side == exchange.PositionSideShort {
				args.ReduceOnly = true
				break
			} else if this.side == exchange.OrderSideSell && position.Side == exchange.PositionSideLong {
				args.ReduceOnly = true
				break
			}
		}
	}

	if this.GetExchange().GetName() == exchange.InteractiveBrokers {
		// 默认允许盘前盘后交易
		args.SetBool(gateway.ExtKeyOutsideRth, true)
	}

	var orderable exchange.Orderable
	useLocalStopLimit := false
	// TODO 不支持 StopLimit 订单的情况都可以使用本地触发订单系统
	if this.symbolCode.IsSpot() && this.GetExchange().GetName() == exchange.Hyperliquid && args.Type == exchange.StopLimit {
		this.SendMsgf("Hyperliquid 现货不支持 StopLimit 订单, 将使用本地触发订单系统")
		useLocalStopLimit = true
	}

	if useLocalStopLimit {
		orderable = this.controller.conditionalOrderManager.Using(this.controller.usingController.GetID())
	} else {
		orderable = this.GetExchange()
	}

	order, err := orderable.CreateOrder(args)
	if err != nil {
		this.ErrorMsgf("下单失败：%s", err)
		return false
	}

	this.SendMsgf("下单成功，orderID: %s", order.OrderID)
	if !useLocalStopLimit {
		this.controller.AppendOrderRecord(order, this.controller.usingController.GetID(), this.GetExchange().GetName(), this.symbolCode)
	}
	return true
}

type BuyOrderCommand struct {
	BaseOrderCommand
}

func NewBuyOrderCommand(controller *OrderController) *BuyOrderCommand {
	cmd := &BuyOrderCommand{
		BaseOrderCommand: BaseOrderCommand{
			OrderCommand: OrderCommand{
				Command: command.Command{
					Name:            "buy",
					Instruction:     "`.buy symbolCode qty[.u/.d/.y] price triggerPrice[可选] config1=value1,config2=value2[可选]` 买入现货，qty 可带后缀表示买入指定 quote 数量，price 可为 0 表示市价单",
					RequiresConfirm: true,
					ArgMin:          3,
					ArgMax:          5,
				},
				controller: controller,
			},
			side: exchange.OrderSideBuy,
		},
	}
	return cmd
}

type SellOrderCommand struct {
	BaseOrderCommand
}

func NewSellOrderCommand(controller *OrderController) *SellOrderCommand {
	cmd := &SellOrderCommand{
		BaseOrderCommand: BaseOrderCommand{
			OrderCommand: OrderCommand{
				Command: command.Command{
					Name:            "sell",
					Instruction:     "`.sell symbolCode qty[.u/.d/.y] price triggerPrice[可选] config1=value1,config2=value2[可选]` 卖出现货，qty 可带后缀表示卖出指定 quote 数量，price 可为 0 表示市价单",
					RequiresConfirm: true,
					ArgMin:          3,
					ArgMax:          5,
				},
				controller: controller,
			},
			side: exchange.OrderSideSell,
		},
	}
	return cmd
}

type LongOrderCommand struct {
	BaseOrderCommand
}

func NewLongOrderCommand(controller *OrderController) *LongOrderCommand {
	cmd := &LongOrderCommand{
		BaseOrderCommand: BaseOrderCommand{
			OrderCommand: OrderCommand{
				Command: command.Command{
					Name:            "long",
					Instruction:     "`.long symbolCode qty price triggerPrice[可选] config1=value1,config2=value2[可选]` 合约做多，price 可为 0 表示市价单",
					RequiresConfirm: true,
					ArgMin:          3,
					ArgMax:          5,
				},
				controller: controller,
			},
			side: exchange.OrderSideBuy,
		},
	}
	return cmd
}

type ShortOrderCommand struct {
	BaseOrderCommand
}

func NewShortOrderCommand(controller *OrderController) *ShortOrderCommand {
	cmd := &ShortOrderCommand{
		BaseOrderCommand: BaseOrderCommand{
			OrderCommand: OrderCommand{
				Command: command.Command{
					Name:            "short",
					Instruction:     "`.short symbolCode qty price triggerPrice[可选] config1=value1,config2=value2[可选]` 合约做空，price 可为 0 表示市价单",
					RequiresConfirm: true,
					ArgMin:          3,
					ArgMax:          5,
				},
				controller: controller,
			},
			side: exchange.OrderSideSell,
		},
	}
	return cmd
}

type CancelOrderCommand struct {
	OrderCommand
	symbolCode *exchange.SymbolCode
	symbol     string
	orderID    string
}

func NewCancelOrderCommand(controller *OrderController) *CancelOrderCommand {
	cmd := &CancelOrderCommand{
		OrderCommand: OrderCommand{
			Command: command.Command{
				Name:            "cancelOrder",
				Alias:           []string{"co", "cancel"},
				Instruction:     "`.cancelOrder symbolCode orderID[可选]` 取消某个品种的订单，orderID 为空时取消所有订单",
				RequiresConfirm: true,
				ArgMin:          1,
				ArgMax:          2,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *CancelOrderCommand) Prepare() bool {
	code := this.Args[0]
	var err error
	this.symbolCode, err = this.controller.usingController.NewSymbolCode(code)
	if err != nil {
		this.ErrorMsgf("参数错误：%s", err.Error())
		return false
	}

	if this.symbolCode.IsSpot() {
		this.symbol, err = this.GetExchange().TranslateSymbolCodeToSpotSymbol(this.symbolCode)
		if err != nil {
			this.ErrorMsgf("获取 symbol 信息出错：%s", err)
			return false
		}
	} else {
		this.symbol, err = this.GetExchange().TranslateSymbolCodeToFutureSymbol(this.symbolCode)
		if err != nil {
			this.ErrorMsgf("获取 symbol 信息出错：%s", err)
			return false
		}
	}

	if len(this.Args) == 2 {
		this.orderID = this.Args[1]
	} else {
		this.orderID = ""
	}

	openOrders, err := this.GetExchange().GetOpenOrders(this.symbolCode.InstrumentType(), exchange.UnknownOrderType, this.symbol)
	if err != nil {
		this.ErrorMsgf("获取当前订单出错：%s", err)
		return false
	}

	orders := []*exchange.OrderRecord{}
	for _, order := range openOrders {
		if this.orderID != "" && order.OrderID != this.orderID {
			continue
		}
		orders = append(orders, &exchange.OrderRecord{
			Order:        order,
			ControllerID: this.controller.usingController.GetID(),
			ExchangeName: this.GetExchange().GetName(),
		})
	}

	if len(orders) == 0 {
		this.SendMsgf("没有可取消的订单")
		return false
	} else {
		this.SendMsgf("确认取消订单：\n```%s```", this.controller.printOrders(orders))
	}

	return true
}

func (this *CancelOrderCommand) Do() bool {
	if this.orderID != "" {
		err := this.GetExchange().CancelOrder(this.symbolCode.InstrumentType(), exchange.UnknownOrderType, this.symbol, this.orderID)
		if err != nil {
			this.ErrorMsgf("取消订单失败：%s", err)
			return false
		}
		this.SendMsgf("取消订单成功，orderID: %s", this.orderID)
	} else {
		orderIDs, err := this.GetExchange().CancelAllOrders(this.symbolCode.InstrumentType(), exchange.UnknownOrderType, this.symbol)
		if err != nil {
			this.ErrorMsgf("取消订单失败：%s", err)
			return false
		}
		this.SendMsgf("成功取消 %d 个订单", len(orderIDs))
	}
	this.controller.syncOrders()
	return true
}

type TransferOrderCommand struct {
	OrderCommand
	fromType     exchange.InstrumentType
	toType       exchange.InstrumentType
	transferCoin string
	qty          float64
}

func NewTransferOrderCommand(controller *OrderController) *TransferOrderCommand {
	cmd := &TransferOrderCommand{
		OrderCommand: OrderCommand{
			Command: command.Command{
				Name:            "transfer",
				Instruction:     "`.transfer fromCode toCode qty` 划转余额",
				RequiresConfirm: true,
				ArgMin:          3,
				ArgMax:          3,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *TransferOrderCommand) Prepare() bool {
	fromCode, err := this.controller.usingController.NewSymbolCode(this.Args[0])
	if err != nil {
		this.ErrorMsgf("参数错误：%s", err.Error())
		return false
	}
	toCode, err := this.controller.usingController.NewSymbolCode(this.Args[1])
	if err != nil {
		this.ErrorMsgf("参数错误：%s", err.Error())
		return false
	}

	this.qty, err = strconv.ParseFloat(this.Args[2], 64)
	if err != nil {
		this.ErrorMsgf("qty 参数错误：%s", err.Error())
		return false
	}

	this.transferCoin = fromCode.Coin()
	if fromCode.InstrumentType() == exchange.USDXMarginedFutures {
		this.transferCoin = fromCode.USDXSymbol
	}

	if fromCode.IsSpot() && toCode.IsFuture() {
		this.fromType = exchange.Spot
		this.toType = toCode.InstrumentType()
	} else if fromCode.IsFuture() && toCode.IsSpot() {
		this.fromType = fromCode.InstrumentType()
		this.toType = exchange.Spot
	} else {
		this.ErrorMsgf("不支持的划转类型")
		return false
	}

	if fromCode.IsSpot() && toCode.IsFuture() {
		this.SendMsgf("确认从现货划转 %v %s 到合约", this.qty, this.transferCoin)
	} else if fromCode.IsFuture() && toCode.IsSpot() {
		this.SendMsgf("确认从合约划转 %v %s 到现货", this.qty, this.transferCoin)
	}

	return true
}

func (this *TransferOrderCommand) Do() bool {
	err := this.GetExchange().TransferAsset(this.fromType, this.toType, this.transferCoin, this.qty)
	if err != nil {
		this.ErrorMsgf("划转失败：%s", err)
		return false
	}
	this.SendMsgf("划转成功")
	return true
}

type PriceTriggersCommand struct {
	OrderCommand
}

func NewPriceTriggersCommand(controller *OrderController) *PriceTriggersCommand {
	cmd := &PriceTriggersCommand{
		OrderCommand: OrderCommand{
			Command: command.Command{
				Name:                   "priceTriggers",
				Alias:                  []string{"pt"},
				Instruction:            "`.priceTriggers print(默认)/add/delete` 打印/添加/删除价格触发，添加： `add {SymbolCode} >或< {Price}`，删除：`delete {ID}`",
				RequiresConfirm:        true,
				SkipConfirmSubcommands: []string{"print"},
				DefaultSubcommand:      "print",
				ArgMin:                 1,
				ArgMax:                 4,
				AuthcodePos:            -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *PriceTriggersCommand) Do() bool {
	return cmds.DoPriceTriggersCommand(this.Command, this.controller.usingController)
}

type PriceWatchCommand struct {
	OrderCommand
}

func NewPriceWatchCommand(controller *OrderController) *PriceWatchCommand {
	cmd := &PriceWatchCommand{
		OrderCommand: OrderCommand{
			Command: command.Command{
				Name:                   "priceWatch",
				Alias:                  []string{"pw"},
				Instruction:            "`.priceWatch print(默认)/add/delete SymbolCode` 打印/添加/删除价格追踪",
				RequiresConfirm:        false,
				SkipConfirmSubcommands: []string{"print"},
				DefaultSubcommand:      "print",
				ArgMin:                 1,
				ArgMax:                 2,
				AuthcodePos:            -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *PriceWatchCommand) Do() bool {
	return cmds.DoPriceWatchCommand(this.Command, this.controller.usingController)
}

type AssetsCommand struct {
	OrderCommand
}

func NewAssetsCommand(controller *OrderController) *AssetsCommand {
	cmd := &AssetsCommand{
		OrderCommand: OrderCommand{
			Command: command.Command{
				Name:            "asset",
				Alias:           []string{"as"},
				Instruction:     "`.asset(+) record/inout/detail/moredetail[可选]` 打印资产快照",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *AssetsCommand) Do() bool {
	return cmds.DoAssetsCommand(this.Command, this.controller.usingController)
}

type FundingHistoryOrderCommand struct {
	OrderCommand
}

func NewFundingHistoryOrderCommand(controller *OrderController) *FundingHistoryOrderCommand {
	cmd := &FundingHistoryOrderCommand{
		OrderCommand: OrderCommand{
			Command: command.Command{
				Name:            "funding",
				Alias:           []string{"fundings", "f"},
				Instruction:     "`.funding all/FutureCode latest/history[可选] daily[可选]` 资金费率历史",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          3,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *FundingHistoryOrderCommand) Do() bool {
	return cmds.DoFundingHistoryCommand(this.Command, this.controller.usingController, this.GetExchange())
}

type WithdrawOrderCommand struct {
	OrderCommand
	coin    string
	amount  float64
	address string
	chain   string
}

func NewWithdrawOrderCommand(controller *OrderController) *WithdrawOrderCommand {
	cmd := &WithdrawOrderCommand{
		OrderCommand: OrderCommand{
			Command: command.Command{
				Name:            "withdraw",
				Instruction:     "`.withdraw coin address amount chain[可选]` 提币",
				RequiresConfirm: true,
				ArgMin:          3,
				ArgMax:          4,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *WithdrawOrderCommand) Prepare() bool {
	this.coin = this.Args[0]
	this.address = this.Args[1]
	var err error
	this.amount, err = strconv.ParseFloat(this.Args[2], 64)
	if err != nil {
		this.ErrorMsgf("amount 参数错误：%s", err.Error())
		return false
	}
	if len(this.Args) == 4 {
		this.chain = this.Args[3]
	} else {
		this.chain = ""
	}

	chains, err := this.GetExchange().GetWithdrawChains(this.coin)
	if err != nil {
		this.ErrorMsgf("获取提币链出错：%s", err)
		return false
	} else if len(chains) == 0 {
		this.ErrorMsgf("无可用提币链")
		return false
	}

	if this.chain == "" {
		this.chain = chains[0]
	} else if !exchange.SliceContains(chains, this.chain) {
		this.ErrorMsgf("不支持的提币链：%s, 可用链: %s", this.chain, strings.Join(chains, ", "))
		return false
	}

	this.SendMsgf("确认提币 %v %s 到 %s, 提币链: %s", this.amount, this.coin, this.address, this.chain)
	return true
}

func (this *WithdrawOrderCommand) Do() bool {
	_, err := this.GetExchange().Withdraw(this.coin, this.address, this.amount, this.chain)
	if err != nil {
		this.ErrorMsgf("提币失败：%s", err)
		return false
	}
	this.SendMsgf("提币成功")
	return true
}

type ConfigOrderCommand OrderCommand

func NewConfigOrderCommand(controller *OrderController) *ConfigOrderCommand {
	cmd := &ConfigOrderCommand{
		Command: command.Command{
			Name:  "baseconfig",
			Alias: []string{"cfg"},
			Instructions: []string{
				"`.baseconfig show controllerID` 查看基础参数",
				"`.baseconfig set controllerID field1=value1,field2=value2 AuthCode` 设置基础配置 Field 字段值",
			},
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ConfigOrderCommand) Do() bool {
	args := this.GetArgs()
	subcommand := this.GetSubcommand()
	switch subcommand {
	case "show":
		if len(args) == 0 {
			this.ErrorMsgf("请指定 controllerID")
			return false
		}
		controllerID := args[0]
		if controller := this.controller.GetController(controllerID); controller != nil {
			this.SendMsgf("Build: %s, 运行参数\n```%s```", this.controller.BuildInfo(), controller.GetBaseConfig().ToTable())
		} else {
			this.ErrorMsgf("controllerID 错误：%s", controllerID)
			return false
		}
		return true
	case "set":
		if len(args) == 0 {
			this.ErrorMsgf("请指定 controllerID")
			return false
		}
		controllerID := args[0]
		configStr := args[1]
		if controller := this.controller.GetController(controllerID); controller != nil {
			if correctedConfigStr, err := controller.SaveConfig(configStr); err != nil {
				this.ErrorMsgf("设置配置错误：%s", err)
				return false
			} else {
				this.SendMsgf("设置配置成功：%s", correctedConfigStr)
				return true
			}
		} else {
			this.ErrorMsgf("controllerID 错误：%s", controllerID)
			return false
		}
	}
	return false
}

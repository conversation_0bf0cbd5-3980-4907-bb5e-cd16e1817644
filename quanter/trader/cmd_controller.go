package trader

import (
	"strings"

	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/command"
)

// 设置配置项命令

type TraderCommand struct {
	command.Command
	controller *TraderController
}

// 需组合使用才能调用到 PrePrepare, PreDo
type CheckExchangeCommand TraderCommand

func (this *CheckExchangeCommand) PrePrepare() bool {
	return this.controller.CheckExchangeReady(true)
}

func (this *CheckExchangeCommand) PreDo() bool {
	return this.controller.CheckExchangeReady(true)
}

// 启动程序
type LaunchTraderCommand TraderCommand

func NewLaunchTraderCommand(controller *TraderController) *LaunchTraderCommand {
	cmd := &LaunchTraderCommand{
		Command: command.Command{
			Name:            "launch",
			Instruction:     "`.launch Password GoogleAuthCode ` 启动程序",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          3,
			AuthcodePos:     -1, // launch 命令不能在解密主密码前校验 authcode，所以把 authCode 当成是普通参数处理，跳过统一的校验流程
			Sensitive:       true,
		},
		controller: controller,
	}
	return cmd
}

func (this *LaunchTraderCommand) Do() bool {
	password := this.Args[0]
	authCode := this.Args[1]
	success := this.controller.Launch(password, authCode)
	if success {
		this.SendMsgf("启动成功。")
	}
	return true
}

// 当前程序运行状态
type StatusTraderCommand TraderCommand

func NewStatusTraderCommand(controller *TraderController) *StatusTraderCommand {
	cmd := &StatusTraderCommand{
		Command: command.Command{
			Name:            "status",
			Alias:           []string{"s"},
			Instruction:     "`.status(+)` 查看运行状态",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *StatusTraderCommand) Do() bool {
	this.controller.SendStatus()
	return true
}

// 打印程序运行参数
type ConfigTraderCommand TraderCommand

func NewConfigTraderCommand(controller *TraderController) *ConfigTraderCommand {
	cmd := &ConfigTraderCommand{
		Command: command.Command{
			Name:            "config",
			Alias:           []string{"cfg"},
			Instruction:     "`.config` 查看当前运行参数",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ConfigTraderCommand) Do() bool {
	this.SendMsgf("Build: %s, 运行参数\n```%s```", this.controller.BuildInfo(), this.controller.Config.ToTable())
	return true
}

type SetConfigTraderCommand TraderCommand

func NewSetConfigTraderCommand(controller *TraderController) *SetConfigTraderCommand {
	cmd := &SetConfigTraderCommand{
		Command: command.Command{
			Name:            "setConfig",
			Alias:           []string{"sc"},
			Instruction:     "`.setConfig field1=value1,field2=value2` 设置配置 Field 字段值",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
		},
		controller: controller,
	}
	return cmd
}

func (this *SetConfigTraderCommand) Prepare() bool {
	configStr := this.Args[0]
	if !strings.Contains(configStr, "=") {
		this.ErrorMsgf("请按 field=value 设置配置。")
		return false
	}

	if _, correctedConfigStr, err := baseconfig.ParseConfigsFromString(this.controller.Config, configStr); err != nil {
		this.ErrorMsgf("解析配置错误：%s", err)
		return false
	} else {
		this.SendMsgf("设置配置 %s", correctedConfigStr)
		return true
	}
}

func (this *SetConfigTraderCommand) Do() bool {
	configStr := this.Args[0]
	if correctedConfigStr, err := this.controller.SaveConfig(configStr); err != nil {
		this.ErrorMsgf("设置配置错误：%s", err)
		return false
	} else {
		this.SendMsgf("设置配置成功：%s", correctedConfigStr)
		return true
	}
}

type SetCoinsTraderCommand struct {
	CheckExchangeCommand
}

func NewSetCoinsTraderCommand(controller *TraderController) *SetCoinsTraderCommand {
	cmd := &SetCoinsTraderCommand{
		CheckExchangeCommand: CheckExchangeCommand{
			Command: command.Command{
				Name:            "setCoins",
				Instruction:     "`.setCoins coin1,coin2` 设置允许的币种",
				RequiresConfirm: true,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *SetCoinsTraderCommand) Prepare() bool {
	coins := strings.Split(this.Args[0], ",")
	if _, invalidCoins, err := this.controller.Exchange.CheckValidCoins(coins); err != nil {
		this.ErrorMsgf("检查币种请求错误：%s", err)
		return false
	} else {
		if len(invalidCoins) != 0 {
			this.ErrorMsgf("交易所 %s 不支持币种 %s ", this.controller.Exchange.GetName(), strings.Join(invalidCoins, ","))
			return false
		}
		return true
	}
}

func (this *SetCoinsTraderCommand) Do() bool {
	coins := strings.Split(this.Args[0], ",")
	if validCoins, invalidCoins, err := this.controller.Exchange.CheckValidCoins(coins); err != nil {
		this.ErrorMsgf("检查币种请求错误：%s", err)
		return false
	} else {
		if len(invalidCoins) != 0 {
			this.ErrorMsgf("交易所 %s 不支持币种 %s ", this.controller.Exchange.GetName(), strings.Join(invalidCoins, ","))
			return false
		}
		this.controller.Config.AllowedSymbolPrefixs = validCoins
		this.controller.Config.Save()
		this.SendMsgf("允许的币种已设为：%s，可以通过 `.config` 查询。", strings.Join(validCoins, ","))
		return true
	}
}

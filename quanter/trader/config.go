package trader

import (
	"fmt"
	"os"
	"path"
	"strings"

	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/utils"

	"github.com/spf13/viper"
	"github.com/stevedomin/termtable"
)

var ConfigWithoutDiffHeader = []string{"Config", "Value"}

type TraderConfig struct {
}

type TraderControllerConfig struct {
	baseconfig.BaseConfig
	TraderConfig
	controller *TraderController
}

var TraderConfigOptions = &baseconfig.ConfigOptions{}

func SetupTraderControllerConfig(controller *TraderController) (*TraderControllerConfig, error) {
	if config, err := LoadConfig(controller.ConfigPath, controller.ID); err != nil {
		return nil, err
	} else {
		config.controller = controller
		controller.Config = config
		return config, nil
	}
}

func LoadConfig(configPath, controllerID string) (config *TraderControllerConfig, er error) {
	config = &TraderControllerConfig{}
	configFilePath := path.Join(configPath, controllerID+".trader.toml")
	if _, err := os.Stat(configFilePath); !os.IsNotExist(err) {
		viper.SetConfigName(controllerID + ".trader")
		viper.AddConfigPath(configPath)
		err := viper.ReadInConfig()
		if err == nil {
			err := viper.Unmarshal(&config)
			if err != nil {
				zlog.Errorf("[%s] unable to decode trader config into struct, %v", controllerID, err)
				return nil, err
			}
			if err := config.Validate(); err != nil {
				zlog.Errorf("[%s] trader config validate error: %s, %v", controllerID, err, config)
				return nil, err
			}
			zlog.Infof("[%s] load config from local file", controllerID)
			return config, nil
		} else { // 如果配置文件存在，但是解析出现错误，报错退出
			zlog.Panicf("[%s] read config file error：%s", controllerID, err)
			return nil, fmt.Errorf("read config error: %v", err)
		}
	} else {
		return nil, err
	}
}

func (g *TraderControllerConfig) snapshotTraderConfig() *TraderConfig {
	s := &TraderConfig{}
	return s
}

func (g *TraderControllerConfig) Validate() error {
	if err := g.BaseConfig.Validate(); err != nil {
		return err
	}
	return g.TraderConfig.Validate()
}

func (g *TraderControllerConfig) GetConfigOptions() *baseconfig.ConfigOptions {
	return TraderConfigOptions
}

func (g *TraderControllerConfig) GetChildValidatableConfig() baseconfig.ValidatableConfig {
	return &g.TraderConfig
}

func (g *TraderControllerConfig) GetBaseConfig() *baseconfig.BaseConfig {
	return &g.BaseConfig
}

func (g *TraderControllerConfig) ToTomlContent(hideSecret bool) string {
	result := "[BaseConfig]\n" +
		g.BaseConfig.ToTomlContent(hideSecret) +
		"\n[TraderConfig]\n"
	return result
}

func NewTable() *termtable.Table {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	return t
}

func (this *TraderControllerConfig) ToTable() string {
	apiKey := this.ApiKey
	apiSecret := this.ApiSecret
	apiKey = utils.HideSecret(apiKey)
	apiSecret = utils.HideSecret(apiSecret)

	ts := NewTable()
	ts.SetHeader([]string{"AllowedSymbolPrefixs"})
	ts.AddRow([]string{strings.Join(this.AllowedSymbolPrefixs, ",")})

	t := NewTable()
	t.SetHeader(ConfigWithoutDiffHeader)
	debugStr := "false"
	if this.controller != nil && this.controller.Debug {
		debugStr = "true"
	}
	t.AddRow([]string{"Debug*", debugStr})
	t.AddRow([]string{"", ""})
	t.AddRow([]string{"ExchangeName", fmt.Sprintf(`%v`, this.ExchangeName)})
	t.AddRow([]string{"Host", fmt.Sprintf(`%v`, this.Host)})
	t.AddRow([]string{"ApiKey", fmt.Sprintf(`%v`, apiKey)})
	t.AddRow([]string{"ApiSecret", fmt.Sprintf(`%v`, apiSecret)})
	if this.WithdrawApiKey != "" {
		t.AddRow([]string{"WithdrawApiKey", fmt.Sprintf(`%v`, utils.HideSecret(this.WithdrawApiKey))})
		t.AddRow([]string{"WithdrawApiSecret", fmt.Sprintf(`%v`, utils.HideSecret(this.WithdrawApiSecret))})
	}

	if exp, _ := this.controller.GetAPIExpireTime(); exp != nil {
		t.AddRow([]string{"ApiKeyExpire", utils.FormatShortTimeStr(exp, true)})
	}

	t.AddRow([]string{"ReleaseBinaryDirPath", fmt.Sprintf(`%v`, this.ReleaseBinaryDirPath)})
	t.AddRow([]string{"LogDirPath", fmt.Sprintf(`%v`, this.LogDirPath)})
	t.AddRow([]string{"IsTestnet", fmt.Sprintf(`%v`, this.IsTestnet)})
	t.AddRow([]string{"ProxyUrl", this.ProxyUrl})
	t.AddRow([]string{"ShowFutureQtyAsValue", fmt.Sprintf(`%v`, this.ShowFutureQtyAsValue)})
	t.AddRow([]string{"MinMarginRatio", fmt.Sprintf(`%v`, this.MinMarginRatio)})
	t.AddRow([]string{"EnableRealtimePrice", fmt.Sprintf(`%v`, this.EnableRealtimePrice)})
	t.AddRow([]string{"USDXSymbol", this.USDXSymbol})
	t.AddRow([]string{"MarginMode", string(this.MarginMode)})
	t.AddRow([]string{"-------------------------", ""})

	for _, row := range this.GetTraderConfigRows() {
		t.AddRow(row)
	}
	return fmt.Sprintf("%s\n\n%s", ts.Render(), t.Render())
}

func (this *TraderControllerConfig) GetTraderConfigRows() (rows [][]string) {
	if baseConfigRows, err := baseconfig.GetTableRows(this.snapshotTraderConfig()); err != nil {
		rows = append(rows, []string{"ERROR", fmt.Sprintf("[ERROR!>base config table error: %s]", err)})
		return
	} else {
		rows = append(rows, baseConfigRows...)
	}
	return
}

func (g *TraderControllerConfig) SaveTo(configPath string, id string, overwrite bool) error {
	if configPath == "" {
		return fmt.Errorf("configPath is empty")
	}
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".trader.toml")
	if _, err := os.Stat(path); !os.IsNotExist(err) && !overwrite {
		return fmt.Errorf("[%s] %s already exist, can not overwrite", g.controller.ID, path)
	}
	if err := os.WriteFile(path, []byte(g.ToTomlContent(false)), 0755); err != nil {
		zlog.Errorf("[%s] write trader config %s error: %v", g.controller.ID, id, err)
		return err
	}
	return nil
}

func (g *TraderControllerConfig) Save() {
	err := g.SaveTo(g.controller.ConfigPath, g.controller.ID, true)
	if err != nil {
		g.controller.AlertMsgf("保存配置文件失败: %s", err)
	}
}

func (g *TraderControllerConfig) Delete() {
	// 删除本地配置
	if err := os.Remove(path.Join(g.controller.ConfigPath, g.controller.ID+".trader.toml")); err != nil {
		g.controller.AlertMsgf("本地配置文件删除失败: %s", err)
		return
	}
}

func (g *TraderControllerConfig) GetExchangeName() string {
	return g.ExchangeName
}

func (g *TraderControllerConfig) GetHost() string {
	return g.Host
}

func (g *TraderControllerConfig) GetApiKey() string {
	return g.ApiKey
}

func (g *TraderControllerConfig) GetApiSecret() string {
	return g.ApiSecret
}

func (g *TraderConfig) Validate() error {
	return nil
}

func (g *TraderConfig) GetConfigOptions() *baseconfig.ConfigOptions {
	return TraderConfigOptions
}

func (g *TraderConfig) GetChildValidatableConfig() baseconfig.ValidatableConfig {
	return nil
}

func (g *TraderConfig) GetBaseConfig() *baseconfig.BaseConfig {
	return nil
}

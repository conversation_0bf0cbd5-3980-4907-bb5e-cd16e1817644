package trader

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path"
	"path/filepath"
	"sync"
	"time"

	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/utils"
)

type TraderStorage struct {
	base.BaseStorage
	mutex sync.Mutex
}

func SetupTraderStroage(controller *TraderController) {
	storage := &TraderStorage{
		BaseStorage: base.BaseStorage{
			Controller: controller,
		},
	}
	// 紧接着赋值给 Storager，防止没有赋值前调用 Storager 相关的方法导致崩溃
	storage.Storager = storage

	if ok := storage.ReadFrom(controller.ConfigPath, controller.ID); !ok {
		storage.Assets = map[int64]float64{}
	}

	if storage.Assets == nil {
		storage.Assets = map[int64]float64{}
	}

	controller.storage = storage
}

func (s *TraderStorage) ReadFrom(configPath, id string) bool {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".trader_storage")
	f, _ := os.OpenFile(path, os.O_RDWR|os.O_CREATE, 0755)
	defer f.Close()
	file, err := io.ReadAll(f)
	if err != nil {
		zlog.Errorf("[%s] read storage file error: %s", s.Controller.GetID(), err.Error())
		return false
	}
	zlog.Infof("[%s] local storage file (%s) loaded", s.Controller.GetID(), path)
	err = json.Unmarshal(file, s)
	if err != nil {
		zlog.Debugf("[%s] json.Unmarshal err: %#v", s.Controller.GetID(), err)
		zlog.Errorf("[%s] read json file error", s.Controller.GetID())
		return false
	}
	return true
}

// 写入本地存储到文件
func (s *TraderStorage) SaveTo(configPath string, id string, overwrite bool) error {
	s.mutex.Lock()
	startTime := time.Now()
	defer func() {
		zlog.Infof("[%s] save to storage took %s", s.Controller.GetID(), time.Since(startTime))
		s.mutex.Unlock()
	}()

	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".trader_storage")
	if _, err := os.Stat(path); !os.IsNotExist(err) && !overwrite {
		return fmt.Errorf("[%s] %s already exist, can not overwrite", s.Controller.GetID(), path)
	}

	// backup old storage file, if the old file is valid json
	oldData, err := os.ReadFile(path)
	if err != nil {
		zlog.Errorf("[%s] read old storage file error: %s", s.Controller.GetID(), err)
		return err
	}
	if gjson.Valid(string(oldData)) {
		backupPath := filepath.Join(configPath, id+".trader_storage.bak")
		if err := utils.CopyFile(path, backupPath); err != nil {
			zlog.Errorf("[%s] backup old storage file error: %s", s.Controller.GetID(), err)
			return err
		}
	}

	// Wrap marshal in a single recover
	// marshal 的过程中，如果其中的子结构中的 slices 可能发生变化，导致 crash
	// 因此，需要手工 recover 忽略错误
	data, err := func() (data []byte, err error) {
		defer func() {
			if r := recover(); r != nil {
				err = fmt.Errorf("[%s] panic during storage marshal: %v", s.Controller.GetID(), r)
			}
		}()

		data, err = json.MarshalIndent(s, "", "    ")
		if err != nil {
			return nil, fmt.Errorf("marshal error: %s", err)
		}
		return data, nil
	}()

	if err != nil {
		zlog.Errorf("[%s] marshal storage error: %s", s.Controller.GetID(), err)
		return err
	}

	if err := os.WriteFile(path, data, 0755); err != nil {
		zlog.Errorf("[%s] save storage to file, error: %s", s.Controller.GetID(), err)
		return err
	}
	return nil
}

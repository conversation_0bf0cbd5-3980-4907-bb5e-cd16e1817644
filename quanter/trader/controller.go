package trader

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/binance"
	"github.com/wizhodl/quanter/exchange/bybit"
	"github.com/wizhodl/quanter/exchange/gateway"
	"github.com/wizhodl/quanter/exchange/hyperliquid"
	"github.com/wizhodl/quanter/exchange/okex"
	"github.com/wizhodl/quanter/messenger"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"
)

type TraderController struct {
	base.BaseController

	Config *TraderControllerConfig

	storage     *TraderStorage
	handleMutex *xsync.MapOf[string, *sync.Mutex]
}

func NewTraderController(id string, debug bool, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath string, parentMessenger *messenger.SlackMessenger, managerID string) (*TraderController, error) {
	controller := &TraderController{
		BaseController: base.BaseController{
			BaseResponder: command.NewBaseResponder(debug, commitHash, buildTime, id, nil, ""),
			ID:            id,
			RefID:         exchange.NewRandomID(),
			ConfigPath:    configPath,
		},
		handleMutex: xsync.NewMapOf[*sync.Mutex](),
	}
	controller.Controllable = controller

	controller.Setup(debug, parentMessenger, managerID)

	config, err := SetupTraderControllerConfig(controller)
	if err != nil {
		return nil, err
	}
	if logDirPath != "" {
		config.LogDirPath = logDirPath
	}

	SetupTraderStroage(controller)

	controller.AddCommands([]command.Commander{
		NewStatusTraderCommand(controller),
		NewConfigTraderCommand(controller),
		NewSetConfigTraderCommand(controller),
		NewSetCoinsTraderCommand(controller),
		cmds.NewHoldingsCommand(controller),
		cmds.NewAssetsCommand(controller, controller.storage),
		cmds.NewMuteCommand(),
		cmds.NewDebugCommand(),
		cmds.NewLogCommand(controller.Config.LogDirPath, controller.GetLogFilename()),
		cmds.NewDownloadLogCommand(controller.Config.LogDirPath, controller.GetLogFilename()),
		cmds.NewDownloadStorageCommand(controller.ConfigPath, fmt.Sprintf("%s.trader_storage", controller.ID)),
		cmds.NewPriceTriggersCommand(controller),
		cmds.NewPriceWatchCommand(controller),
		cmds.NewStackTraceCommand(),
		cmds.NewDeleteErrorsCommand(),
	})

	if controller.Standalone() {
		// 注册 commander 要在
		// 根据命令行参数更新 config 中的部分字段
		if releaseBinaryDirPath != "" {
			config.ReleaseBinaryDirPath = releaseBinaryDirPath
		}
		// 初始化时检查 ReleaseBinaryDir 是否可以写，以免后续 releases 相关命令每次都要检查
		if utils.CheckReleaseBinaryDirPath(controller.Config.ReleaseBinaryDirPath) {
			controller.AddCommands([]command.Commander{
				cmds.NewReleaseCommand(controller.Config.ReleaseBinaryDirPath, controller.ID),
				cmds.NewListVersionCommand(controller.Config.ReleaseBinaryDirPath, controller.ID),
				cmds.NewUseVersionCommand(controller.Config.ReleaseBinaryDirPath, controller.ID),
			})
		}
	}

	// 尝试以 Debug 模式启动，如果不能以 Debug 模式启动，会要求以 .launch 模式启动
	debugLaunched := controller.TryLaunchWithDebug()
	if debugLaunched {
		go controller.Init()
	} else {
		controller.AddCommands([]command.Commander{NewLaunchTraderCommand(controller)})
	}

	controller.Messenger.SetSensitiveCommands(controller.GetCommandProcessor().SensitiveCommands)

	return controller, nil
}

func (this *TraderController) Init() {
	apiSecret := this.GetApiSecret()
	withdrawApiSecret := this.GetWithdrawApiSecret()
	var backOff backoff.BackOff
	if this.IsExchange(exchange.CTP) {
		backOff = backoff.NewConstantBackOff(time.Second * 5)
	} else {
		exp := backoff.NewExponentialBackOff()
		exp.InitialInterval = time.Second * 10
		exp.MaxInterval = time.Minute * 5
		exp.MaxElapsedTime = 0 // It never stops if MaxElapsedTime == 0.
		backOff = exp
	}
	alerted := false
	backoff.Retry(func() error {
		if this.IsClosed() {
			return nil
		}
		err := this.InitAPI(apiSecret, withdrawApiSecret)
		if err != nil {
			this.Warnf("init api failed, error: %s", err)
			if !alerted { // 警报一次
				this.ErrorMsgf("初始化 API 错误，稍后将自动重试。错误信息：%s", err)
				alerted = true
			}
		} else {
			if err := exchange.TestTranslateSymbolCode(this.Exchange); err != nil {
				this.ErrorMsgf("symbol code 测试不通过: %s", err)
			}
			this.SendMsgf("交易机 %s 初始化 API 成功", this.ID)
		}
		return err
	}, backOff)

	if this.IsClosed() {
		return
	}
}

func (this *TraderController) marginUpdatedCallback(userMargin *exchange.UserMargin, currency string) {
}

func (this *TraderController) orderUpdatedCallback(order *exchange.Order) {
}

func (this *TraderController) priceTriggeredCallback(priceTrigger *exchange.PriceTrigger) {
	this.Infof("price triggered callback: %#v", priceTrigger)
	if priceTrigger.Source == "manual" {
		for _, p := range this.storage.PriceTriggers {
			if p.ID == priceTrigger.ID {
				p.Triggered = true
				p.TriggeredPrice = priceTrigger.TriggeredPrice
			}
		}

		direction := ">"
		if priceTrigger.Direction == exchange.TriggerDirectionLower {
			direction = "<"
		}
		this.SendMsgf("%s[%s] %s %v 已触发", priceTrigger.Symbol, priceTrigger.InstrumentType, direction, priceTrigger.Price)
		return
	}
}

func (this *TraderController) InitAPI(apiSecret, withdrawApiSecret secrets.SecretString) error {
	opts := &exchange.Options{
		Host:                   this.Config.Host,
		ApiKey:                 this.Config.ApiKey,
		ApiSecret:              apiSecret,
		WithdrawApiKey:         this.Config.WithdrawApiKey,
		WithdrawApiSecret:      withdrawApiSecret,
		FixerKey:               this.Config.FixerKey,
		IsTestnet:              this.Config.IsTestnet,
		ProxyUrl:               this.Config.ProxyUrl,
		ControllerID:           this.ID,
		MarginUpdatedCallback:  this.marginUpdatedCallback,
		OrderUpdatedCallback:   this.orderUpdatedCallback,
		PriceTriggeredCallback: this.priceTriggeredCallback,
		DataPath:               this.ConfigPath,
	}

	var instrumentTypes []exchange.InstrumentType

	if strings.EqualFold(this.Config.ExchangeName, exchange.OKEx) {
		instrumentTypes = []exchange.InstrumentType{exchange.Spot, exchange.USDXMarginedFutures}
		client, err := okex.NewOKEx(opts)
		if err != nil {
			return fmt.Errorf("初始化 OKEX 失败，error: %s", err)
		}

		if cfg, err := client.GetAccountConfig(exchange.USDXMarginedFutures); err != nil {
			return fmt.Errorf("初始化 OKEX 失败，获取用户配置错误：%s", err)
		} else {
			if !cfg.DualPositionSide { // 挂计划委托单必须双向持仓模式
				if err := client.SetDualPositionSide(exchange.USDXMarginedFutures, true); err != nil {
					// 不要返回错误，因为如果有仓位，无法设置仓位模式
					this.Warnf("error setting position mode: %s", err)
					this.WarnMsgf("设置持仓模式错误: %s", err)
				}
			}
		}

		this.Exchange = client
	} else if strings.EqualFold(this.Config.ExchangeName, exchange.Binance) {
		instrumentTypes = []exchange.InstrumentType{exchange.Spot, exchange.USDXMarginedFutures}
		client, err := binance.NewBinance(opts)
		if err != nil {
			return fmt.Errorf("初始化 Binance 失败，error: %s", err)
		}
		if err := client.SetDualPositionSide(exchange.USDXMarginedFutures, false); err != nil {
			if strings.Contains(err.Error(), "-4059") {
				this.Debugf("no need to change position side")
			} else {
				return fmt.Errorf("账户设置单向持仓模式错误，请检测 API 是否正确或已有持仓或挂单: %s", err)
			}
		}
		this.Exchange = client

	} else if strings.EqualFold(this.Config.ExchangeName, exchange.MetaTrader) {
		instrumentTypes = []exchange.InstrumentType{exchange.USDXMarginedFutures}
		client, err := gateway.NewMT(opts, this.ID)
		if err != nil {
			return fmt.Errorf("初始化 MetaTrader 失败，error: %s", err)
		}
		if _, err := client.GetAccountBalances(exchange.USDXMarginedFutures); err != nil {
			return fmt.Errorf("获取余额失败，请检查 API 是否正确. error: %s", err)
		}
		this.Exchange = client

	} else if strings.EqualFold(this.Config.ExchangeName, exchange.InteractiveBrokers) {
		instrumentTypes = []exchange.InstrumentType{exchange.Spot}
		client, err := gateway.NewIB(opts, this.ID)
		if err != nil {
			return fmt.Errorf("初始化 InteractiveBrokers 失败，error: %s", err)
		}
		if _, err := client.GetAccountBalances(exchange.Spot); err != nil {
			return fmt.Errorf("获取余额失败，请检查 API 是否正确. error: %s", err)
		}
		this.Exchange = client

	} else if this.Config.ExchangeName == exchange.Hyperliquid {
		client, err := hyperliquid.NewHyperliquid(opts)
		if err != nil {
			return fmt.Errorf("初始化 Hyperliquid 失败，error: %s", err)
		}
		instrumentTypes = []exchange.InstrumentType{exchange.Spot, exchange.USDXMarginedFutures}
		if _, err := client.GetAccountBalances(exchange.Spot); err != nil {
			return fmt.Errorf("获取余额失败，请检查 API 是否正确. error: %s", err)
		}
		this.Exchange = client

	} else if this.Config.ExchangeName == exchange.Bybit {
		client, err := bybit.NewBybit(opts)
		if err != nil {
			return fmt.Errorf("初始化 BYBIT 失败，error: %s", err)
		}
		instrumentTypes = []exchange.InstrumentType{exchange.Spot, exchange.USDXMarginedFutures}
		// 检查并设置保证金模式
		if _, err := client.GetAccountConfig(exchange.USDXMarginedFutures); err != nil {
			return fmt.Errorf("初始化 BYBIT 失败，获取用户配置错误：%s", err)
		}
		this.Exchange = client

	} else {
		this.ErrorMsgf("交易所 %s 暂未支持", this.Config.ExchangeName)
		return fmt.Errorf("unsupported exchange (%s)", this.Config.ExchangeName)
	}

	if err := this.Exchange.CacheInstruments(true); err != nil {
		return fmt.Errorf("cache instruments error: %s", err)
	}

	this.Exchange.SetHttpDebug(this.Debug)
	go this.Exchange.ConnectWebsocket(instrumentTypes, nil)
	go this.Exchange.CheckPriceTriggerTimeLoop()

	return nil
}

// debug 状态运行 和 定时任务，有多次运行防呆机制
func (this *TraderController) Run() {
	tasks := []base.CronTask{
		{
			Spec: "1 12 * * *",
			Cmd: func() {
				this.storage.RecordAssets()
				this.SendStatus()
				this.Exchange.CacheInstruments(true)
			},
		},

		{Spec: "@every 5m", Cmd: this.handleOrders},
	}
	this.BaseController.Run(tasks)
}

func (this *TraderController) handleOrders() {
}

func (this *TraderController) getOpenMarginSummary(onlyInsufficient bool) (result string) {
	return
}

func (this *TraderController) SendStatus() {
	this.SendMsgf("Build: %s, 运行状态\n```%s```", this.BuildInfo(), this.Config.ToTable())
}

func (this *TraderController) Close() {
	this.BaseController.Close(func() {
	})
}

func (this *TraderController) SaveConfigsTo(id string) error {
	if err := this.Config.SaveTo(this.ConfigPath, id, false); err != nil {
		return err
	}
	return nil
}

func (this *TraderController) SaveStorageTo(id string) error {
	if err := this.storage.SaveTo(this.ConfigPath, id, true); err != nil {
		return err
	}
	return nil
}

// 停止运行，并删除套利机对应的配置文件
// stopAll 和 launched 设为 false
func (this *TraderController) DangerousDelete() {
	this.Close()
	this.Config.Delete()
}

func ParseNumOrPercentage(numOrPercentage string, percentOnly bool, allowNegative bool) (num float64, percent float64, er error) {
	percent = 0.0
	num = 0.0
	if result, isPercent, err := utils.ParseFloatOrPercentage(numOrPercentage, percentOnly, allowNegative); err == nil {
		if isPercent {
			percent = result
		} else {
			num = result
		}
	} else {
		er = err
	}
	return num, percent, er
}

func (this *TraderController) GetInstrumentTypes() []exchange.InstrumentType {
	return this.Exchange.GetSupportedInstrumentTypes()
}

func (this *TraderController) RenderAssets() string {
	return this.storage.RenderAssets()
}

func (this *TraderController) GetReviewRows() [][]string {
	return this.Config.GetTraderConfigRows()
}

func (this *TraderController) GetBaseConfig() *baseconfig.BaseConfig {
	return &this.Config.BaseConfig
}

func (this *TraderController) SaveConfig(configStr string) (correctedConfigStr string, er error) {
	if configPairs, _correctedConfigStr, err := baseconfig.SetConfigWithString(this.Config, configStr); err != nil {
		return "", err
	} else {
		correctedConfigStr = _correctedConfigStr
		for _, pair := range configPairs {
			if strings.EqualFold(pair.Field, "EnableRealtimePrice") {
				enable := strings.EqualFold(pair.Value, "true")
				this.Exchange.SetEnableRealtimePrice(enable)
			}

			if strings.EqualFold(pair.Field, "MarginMode") {
				for _, instrumentType := range this.GetInstrumentTypes() {
					if !this.SetAccountMarginMode(instrumentType, this.Config.MarginMode) {
						return "", fmt.Errorf("set account margin mode error")
					}
				}
			}
		}
		this.Config.Save()
		return correctedConfigStr, nil
	}
}

func (this *TraderController) GetStorage() base.Storager {
	return this.storage
}

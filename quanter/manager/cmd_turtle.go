package manager

import (
	"bytes"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/turtle"

	"github.com/spf13/viper"
	"github.com/stevedomin/termtable"
)

// 新增趋势机，并不会自动启动，需要 .runTurtle 手工启动
type AddTurtleManagerCommand ManagerCommand

func NewAddTurtleManagerCommand(manager *QuanterManager) *AddTurtleManagerCommand {
	cmd := &AddTurtleManagerCommand{
		Command: command.Command{
			Name:            "addTrender",
			Instruction:     "`.addTrender trenderID` 新增趋势机，请附带上传 trenderID.toml",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
			RequiresFile:    true,
		},
		manager: manager,
	}
	return cmd
}

func (this *AddTurtleManagerCommand) Prepare() bool {
	turtleID := this.Args[0]
	if this.manager.checkTurtleControllerExist(turtleID) {
		this.ErrorMsgf("趋势机 %s 已经运行。", turtleID)
		return false
	}
	this.SendMsgf("文件下载中，请稍等...")
	downloadURL := this.Args[len(this.Args)-1]
	fileData, err := this.GetFile(downloadURL)
	if err != nil {
		this.ErrorMsgf("文件下载失败：%s, url: %s", err, downloadURL)
		return false
	}
	// 检查配置文件是否合法
	var config *turtle.TurtleControllerConfig
	viper.SetConfigType("toml")
	viper.ReadConfig(bytes.NewBuffer(fileData))

	if err := viper.Unmarshal(&config); err != nil {
		this.ErrorMsgf("配置文件解析失败, %v", err)
		return false
	}
	if err := config.Validate(); err != nil {
		this.ErrorMsgf("配置文件验证错误: %s, %v", err, config)
		return false
	}
	this.SendMsgf("配置文件正确。")
	return true
}

func (this *AddTurtleManagerCommand) Do() bool {
	turtleID := this.Args[0]
	if this.manager.checkTurtleControllerExist(turtleID) {
		this.ErrorMsgf("趋势机 %s 已经存在。", turtleID)
		return false
	}
	downloadURL := this.Args[len(this.Args)-1]
	this.manager.addTurtle(turtleID, downloadURL)
	return true
}

// 警告：删除趋势机会删除对应的配置文件，小心使用该命令
type RemoveTurtleManagerCommand ManagerCommand

func NewRemoveTurtleManagerCommand(manager *QuanterManager) *RemoveTurtleManagerCommand {
	cmd := &RemoveTurtleManagerCommand{
		Command: command.Command{
			Name:            "removeTrender",
			Instruction:     "`.removeTrender trenderID` 删除趋势机",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
			ConfirmPrompt:   "警告：该趋势机配置文件将会被删除！",
		},
		manager: manager,
	}
	return cmd
}

func (this *RemoveTurtleManagerCommand) Prepare() bool {
	turtleID := this.Args[0]
	turtleExist := this.manager.checkTurtleControllerExist(turtleID)
	if !turtleExist {
		this.ErrorMsgf("趋势机 %s 没有运行，不能删除。", turtleID)
		return false
	}
	return true
}

func (this *RemoveTurtleManagerCommand) Do() bool {
	turtleID := this.Args[0]
	controller, _ := this.manager.getTurtleController(turtleID)
	controller.DangerousDelete()
	this.manager.removeTurtleController(turtleID, true)
	this.SendMsgf("已删除趋势机 %s", turtleID)
	return true
}

// 因为运行 TurtleController 需要 password 才能解密 APISecret，所以 .copyTurtle 之后并不能直接自动启动
// 需要通过 .runTurtle 命令单独手工启动
type CopyTurtleManagerCommand ManagerCommand

func NewCopyTurtleManagerCommand(manager *QuanterManager) *CopyTurtleManagerCommand {
	cmd := &CopyTurtleManagerCommand{
		Command: command.Command{
			Name:            "copyTrender",
			Instruction:     "`.copyTrender fromTrenderID toTrenderID` 复制运行中的趋势机的配置文件",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *CopyTurtleManagerCommand) Prepare() bool {
	fromTurtleID := this.Args[0]
	toTurtleID := this.Args[1]
	if this.manager.checkTurtleControllerExist(toTurtleID) {
		this.ErrorMsgf("目标趋势机已经存在。")
		return false
	}
	if !this.manager.checkTurtleControllerExist(fromTurtleID) {
		this.ErrorMsgf("源趋势机没有运行。")
		return false
	}
	return true
}

func (this *CopyTurtleManagerCommand) Do() bool {
	fromTurtleID := this.Args[0]
	toTurtleID := this.Args[1]
	// 复制配置文件
	controller, _ := this.manager.getTurtleController(fromTurtleID)
	if err := controller.SaveConfigsTo(toTurtleID); err == nil {
		this.manager.SendMsgf("趋势机 [%s] -> [%s] 配置文件拷贝成功，输入 .runTrender 启动。", fromTurtleID, toTurtleID)
		this.manager.Config.Turtles = append(this.manager.Config.Turtles, toTurtleID)
		this.manager.Config.save()
		return true
	} else {
		this.manager.ErrorMsgf("拷贝趋势机的配置文件出错：%v", err)
		return false
	}
}

// 因为 TurtleController 中的 APISecret 是通过主密码加密，必须提供 password 才能正确解密
type RunTurtleManagerCommand ManagerCommand

func NewRunTurtleManagerCommand(manager *QuanterManager) *RunTurtleManagerCommand {
	cmd := &RunTurtleManagerCommand{
		Command: command.Command{
			Name:            "runTrender",
			Instruction:     "`.runTrender trenderID authCode` 运行趋势机",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
			Sensitive:       true,
		},
		manager: manager,
	}
	return cmd
}

func (this *RunTurtleManagerCommand) Do() bool {
	turtleID := this.Args[0]
	authCode := this.Args[1]

	if this.manager.checkTurtleControllerExist(turtleID) {
		this.ErrorMsgf("趋势机 [%s] 已经运行了。", turtleID)
		return false
	}

	this.manager.runTurtles([]string{turtleID}, "", authCode)
	return true
}

type StopTurtleManagerCommand ManagerCommand

func NewStopTurtleManagerCommand(manager *QuanterManager) *StopTurtleManagerCommand {
	cmd := &StopTurtleManagerCommand{
		Command: command.Command{
			Name:            "stopTrender",
			Instruction:     "`.stopTrender trenderID authCode` 停止运行趋势机",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		manager: manager,
	}
	return cmd
}

func (this *StopTurtleManagerCommand) Do() bool {
	turtleID := this.Args[0]

	if !this.manager.checkTurtleControllerExist(turtleID) {
		this.ErrorMsgf("趋势机 [%s] 没有运行。", turtleID)
		return false
	}

	this.manager.stopTurtles([]string{turtleID}, true)
	this.SendMsgf("趋势机 %s 停止运行成功。", turtleID)
	return true
}

type ListTurtleManagerCommand ManagerCommand

func NewListTurtleManagerCommand(manager *QuanterManager) *ListTurtleManagerCommand {
	cmd := &ListTurtleManagerCommand{
		Command: command.Command{
			Name:            "listTrender",
			Instruction:     "`.listTrender` 列出趋势机",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *ListTurtleManagerCommand) Do() bool {
	var files []string

	err := filepath.Walk(this.manager.configPath, func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			return nil
		}
		if strings.HasSuffix(path, ".turtle.toml") {
			files = append(files, path)
		}
		return nil
	})
	if err != nil {
		zlog.Panicf("walk conifg files error: %v", err)
	}
	turtleIDs := []string{}
	for _, file := range files {
		_, filename := filepath.Split(file)
		parts := strings.Split(filename, ".turtle.toml")
		if len(parts) > 0 {
			turtleIDs = append(turtleIDs, parts[0])
		}
	}

	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	headers := []string{"TurtleID", "Running"}
	t.SetHeader(headers)
	for _, turtleID := range turtleIDs {

		row := []string{
			turtleID,
			"",
		}
		controller, _ := this.manager.getTurtleController(turtleID)
		if controller != nil && controller.IsLaunched() {
			row[1] = "Yes"
		} else {
			row[1] = "No"
		}
		t.AddRow(row)
	}
	result := t.Render()
	this.SendMsgf("```%s```", result)
	return true
}

type TurtleHistoryManagerCommand ManagerCommand

func NewTurtleHistoryManagerCommand(manager *QuanterManager) *TurtleHistoryManagerCommand {
	cmd := &TurtleHistoryManagerCommand{
		Command: command.Command{
			Name:            "trenderHistory",
			Alias:           []string{"this"},
			Instruction:     "`.trenderHistory(+) SymbolCode1,SymbolCode2[可选]` 打印趋势机历史记录",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *TurtleHistoryManagerCommand) Do() bool {
	symbolCodes := ""
	if len(this.Args) == 1 {
		symbolCodes = this.Args[0]
	}

	hisMsg := "[No Histories]"
	t := turtle.NewTable()
	t.SetHeader(turtle.DetailSummaryHeader)

	tts := []*turtle.Turtle{}
	for _, controller := range this.manager.TurtleControllers {
		tts = append(tts, controller.GetHistory(symbolCodes)...)
	}

	sort.SliceStable(tts, func(i, j int) bool {
		return tts[i].Finish.Time.After(*tts[j].Finish.Time)
	})

	for _, tt := range tts {
		t.AddRow(tt.GetSummaryRow(true))
	}

	if len(t.Rows) > 1 {
		hisMsg = t.Render()
	}
	this.SendMsgf("```%s```", hisMsg)

	return true
}

// 具体实现

func (this *QuanterManager) addTurtle(turtleID, downloadURL string) {
	fileData, err := this.GetFile(downloadURL)
	if err != nil {
		this.ErrorMsgf("文件下载失败：%s", err)
	}
	// 检查配置文件是否合法
	var config *turtle.TurtleControllerConfig
	viper.SetConfigType("toml")
	viper.ReadConfig(bytes.NewBuffer(fileData))

	if err := viper.Unmarshal(&config); err != nil {
		this.ErrorMsgf("配置文件解析失败, %v", err)
	}
	if err := config.Validate(); err != nil {
		this.ErrorMsgf("配置文件验证错误: %s, %v", err, config)
	}

	if err := config.SaveTo(this.configPath, turtleID, false); err != nil {
		this.ErrorMsgf("写 [%s] 的配置文件出现错误： %v", turtleID, err)
	} else {
		this.SendMsgf("趋势机 [%s] 添加成功，请运行 `.runTrender` 启动趋势机。", turtleID)
	}
}

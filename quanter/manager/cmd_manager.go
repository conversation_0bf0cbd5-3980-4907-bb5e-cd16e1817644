package manager

import (
	"fmt"
	"os"
	"sort"
	"strings"
	"time"

	"github.com/wizhodl/quanter/arbitrage"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/grid"
	"github.com/wizhodl/quanter/turtle"

	"github.com/stevedomin/termtable"
)

type ManagerCommand struct {
	command.Command
	manager *QuanterManager
}

// 启动程序
type StatusManagerCommand ManagerCommand

func NewStatusManagerCommand(manager *QuanterManager) *StatusManagerCommand {
	cmd := &StatusManagerCommand{
		Command: command.Command{
			Name:            "status",
			Alias:           []string{"s"},
			Instruction:     "`.status(+)` 打印状态",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *StatusManagerCommand) Do() bool {

	trenderStatusMsg := "[No trenders]"

	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	headers := []string{"Trender", "Symbol Code", "Status", "Trading", "Position Qty", "Position Value", "Units", "PNL", "Locked PNL", "Est. Loss", "Max Est. Loss"}
	t.SetHeader(headers)

	for _, controller := range this.manager.TurtleControllers {
		for _, tt := range controller.Turtles {
			row := tt.GetStatusRow(true)
			t.AddRow(row)
		}
	}
	if len(t.Rows) > 1 {
		trenderStatusMsg = t.Render()
	}

	arbiStatusMsg := "[No arbis]"

	at := arbitrage.NewTable()
	arbiHeaders := append([]string{"ArbiID", "Exchange"}, arbitrage.SimpleInstructionStatusHeader...)
	at.SetHeader(arbiHeaders)

	for _, arbitrager := range this.manager.ArbitragerControllers {
		instructions := arbitrage.FilterInstructions(arbitrager.Instructions, arbitrage.InstructionStatusRunning, nil, nil)
		// 按创建时间倒序排序
		sort.Slice(instructions, func(i, j int) bool {
			diff := instructions[i].GetReport().CreateTime.Sub(*instructions[j].GetReport().CreateTime)
			return diff > time.Duration(0)
		})
		for _, instruction := range instructions {
			row := instruction.GetStatusRow(false)
			row = append([]string{arbitrager.ID, arbitrager.Exchange.GetName()}, row...)
			at.AddRow(row)
		}
	}
	if len(at.Rows) > 1 {
		arbiStatusMsg = at.Render()
	}

	griderStatusMsg := "[No grids]"
	gt := grid.NewTable()
	gridHeaders := append([]string{"Grider ID", "Exchange"}, grid.SimpleStatusHeader...)
	gt.SetHeader(gridHeaders)
	for _, grider := range this.manager.GridControllers {
		for _, grid := range grider.Grids {
			if grid.IsFinished() {
				continue
			}
			row := grid.GetStatusRow(false, false)
			row = append([]string{grider.ID, grider.Exchange.GetName()}, row...)
			gt.AddRow(row)
		}
	}
	if len(gt.Rows) > 1 {
		griderStatusMsg = gt.Render()
	}

	this.SendMsgf("```%s\n\n%s\n\n%s```", trenderStatusMsg, griderStatusMsg, arbiStatusMsg)
	return true
}

type PositionManagerCommand ManagerCommand

func NewPositionManagerCommand(manager *QuanterManager) *PositionManagerCommand {
	cmd := &PositionManagerCommand{
		Command: command.Command{
			Name:            "position",
			Alias:           []string{"p"},
			Instruction:     "`.position(+) Asset1,Asset2[可选]` 查看现货和期货持仓",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *PositionManagerCommand) Do() bool {
	arbiPositionMsg := "[No Arbitrager Positions]"

	t := arbitrage.NewTable()
	t.SetHeader([]string{"AriID", "Exchange", "Symbol", "Type", "Qty", "Available", "Entry Price", "PNL"})

	assets := ""
	if len(this.Args) == 1 {
		assets = this.Args[0]
	}

	for _, arbitrager := range this.manager.ArbitragerControllers {
		if arbitrager.Executer == nil {
			this.ErrorMsgf("exchange not initialized")
			return false
		}
		if rows, err := arbitrager.GetPositionRows("future", assets); err != nil {
			this.ErrorMsgf("获取仓位出错：%s", err)
			return false
		} else {
			for _, row := range rows {
				row = append([]string{arbitrager.ID, arbitrager.Exchange.GetName()}, row...)
				t.AddRow(row)
			}
		}
	}
	if len(t.Rows) > 1 {
		arbiPositionMsg = t.Render()
	}

	trenderPositionMsg := "[No Trender Positions]"
	t2 := turtle.NewTable()
	header := append([]string{"Trender", "Exchange"}, turtle.PositionHeader...)
	t2.SetHeader(header)

	for _, controller := range this.manager.TurtleControllers {
		for _, row := range controller.GetPositionRows(assets) {
			row = append([]string{controller.ID, controller.Config.ExchangeName}, row...)
			t2.AddRow(row)
		}
	}
	if len(t2.Rows) > 1 {
		trenderPositionMsg = t2.Render()
	}

	this.SendMsgf("当前持仓：\n```%s\n\n%s```", arbiPositionMsg, trenderPositionMsg)
	return true
}

// 启动程序
type LaunchManagerCommand ManagerCommand

func NewLaunchManagerCommand(manager *QuanterManager) *LaunchManagerCommand {
	cmd := &LaunchManagerCommand{
		Command: command.Command{
			Name:            "launch",
			Instruction:     "`.launch Password GoogleAuthCode safe=trender/arbitrager/grider/gateway/pgate/order/trader/maker[可选](安全模式)` 启动程序",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          3,
			AuthcodePos:     -1, // launch 命令不能在解密主密码前校验 authcode，所以把 authCode 当成是普通参数处理，跳过统一的校验流程
			Sensitive:       true,
		},
		manager: manager,
	}
	return cmd
}

func (this *LaunchManagerCommand) Do() bool {
	password := this.Args[0]
	authCode := this.Args[1]
	safeModeInclude := SAFE_MODE_ALL_FEATURES
	if len(this.Args) == 3 {
		if strings.Contains(strings.ToLower(this.Args[2]), "safe") {
			parts := strings.Split(this.Args[2], "=")
			if len(parts) > 1 {
				safeModeInclude = parts[1]
			} else {
				safeModeInclude = ""
			}
		}
	}

	if this.manager.launch(password, authCode, safeModeInclude) {
		this.SendMsgf("启动成功。")
	}
	return true
}

type ExitManagerCommand ManagerCommand

func NewExitManagerCommand(manager *QuanterManager) *ExitManagerCommand {
	cmd := &ExitManagerCommand{
		Command: command.Command{
			Name:            "restart",
			Instruction:     "`.restart GoogleAuthCode` 退出程序，重新启动",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
		manager: manager,
	}
	return cmd
}

func (this *ExitManagerCommand) Do() bool {
	this.SendMsgf("准备退出程序...")
	os.Exit(0)
	return true
}

type ReviewManagerCommand ManagerCommand

func NewReviewManagerCommand(manager *QuanterManager) *ReviewManagerCommand {
	cmd := &ReviewManagerCommand{
		Command: command.Command{
			Name:            "review",
			Instruction:     "`.review` 集中检查配置",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *ReviewManagerCommand) Do() bool {
	trenderMsg := "[No Trender Configs]"
	arbiMsg := "[No Arbitrager Configs]"
	gridMsg := "[No Grider Configs]"

	trenderHeaders := []string{""} // 第一个空字符串是 configName 列的占位
	trenderTables := [][][]string{}
	for _, controller := range this.manager.TurtleControllers {
		for _, turtle := range controller.Turtles {
			table := [][]string{}
			trenderHeaders = append(trenderHeaders, turtle.TurtleSymbolID())
			table = append(table, turtle.Config.GetRows()...)
			trenderTables = append(trenderTables, table)
		}
	}
	if len(trenderTables) > 0 {
		trenderMsg = this.mergeRenderConfigRows(trenderHeaders, trenderTables)
	}

	arbiHeaders := []string{""} // 第一个空字符串是 configName 列的占位
	arbiTables := [][][]string{}
	for _, controller := range this.manager.ArbitragerControllers {
		table := [][]string{}
		arbiHeaders = append(arbiHeaders, controller.GetID())
		table = append(table, controller.Config.GetArbitragerConfigRows()...)
		arbiTables = append(arbiTables, table)
	}
	if len(arbiTables) > 0 {
		arbiMsg = this.mergeRenderConfigRows(arbiHeaders, arbiTables)
	}

	gridHeaders := []string{""} // 第一个空字符串是 configName 列的占位
	gridTables := [][][]string{}
	for _, controller := range this.manager.GridControllers {
		table := [][]string{}
		gridHeaders = append(gridHeaders, controller.GetID())
		table = append(table, controller.Config.GetGridConfigRows()...)
		gridTables = append(gridTables, table)
	}
	if len(gridTables) > 0 {
		gridMsg = this.mergeRenderConfigRows(gridHeaders, gridTables)
	}

	this.SendFileMessage("检查配置", fmt.Sprintf("%s\n\n%s\n\n%s", trenderMsg, arbiMsg, gridMsg), "")
	return true
}

// 这个函数把 m 个行数为 n 的 tables 合并为一个 table，n 行 m + 1 列，其中第 1 列为第 1 个 table 的第 1 列
// 每个不同的 controller 输出的 configRows table，每一个 table 的行数都相同，并且只有 2 列，第 1 列是 config 的名字，第 2 列是 config 的值
func (this *ReviewManagerCommand) mergeRenderConfigRows(headers []string, tables [][][]string) (result string) {
	result = "[No Configs]"
	if len(tables) == 0 {
		return
	}
	firstTable := tables[0]
	// 检查所有 table 的行数是否等于第一个 table 的行数
	for i, t := range tables {
		if len(t) != len(firstTable) {
			return fmt.Sprintf("[ERROR!> table (%d), row count isn't the same as the first table]", i)
		}
		for j, row := range t {
			if len(row) != 2 {
				return fmt.Sprintf("[ERROR!> table (%d) row (%d) doesn't have exact 2 columns", i, j)
			}
		}
	}

	mergedRows := [][]string{}
	for i, ftRow := range firstTable {
		row := []string{}
		row = append(row, ftRow...) // 写入第一个 table 的所有列
		for j := 1; j < len(tables); j++ {
			t := tables[j]
			row = append(row, t[i][1]) // 从第1个 table 往后，分别写入第二列，即值的那一列
		}
		mergedRows = append(mergedRows, row)
	}

	t := exchange.NewTable()
	t.SetHeader(headers)
	for _, row := range mergedRows {
		t.AddRow(row)
	}

	if len(t.Rows) > 1 {
		result = t.Render()
	}

	return
}

type PriceWatchCommand ManagerCommand

func NewPriceWatchCommand(manager *QuanterManager) *PriceWatchCommand {
	cmd := &PriceWatchCommand{
		Command: command.Command{
			Name:            "priceWatches",
			Alias:           []string{"pw"},
			Instruction:     "`.priceWatches ` 打印所有价格追踪",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *PriceWatchCommand) Do() bool {
	msg := ""
	controllers := []base.Controllable{}
	for _, c := range this.manager.TurtleControllers {
		controllers = append(controllers, c)
	}
	for _, c := range this.manager.GridControllers {
		controllers = append(controllers, c)
	}
	for _, c := range this.manager.ArbitragerControllers {
		controllers = append(controllers, c)
	}
	for _, controller := range controllers {
		msg += fmt.Sprintf("\n[%s]\n", controller.GetID())
		msg += cmds.RenderWatches(controller)
	}
	this.SendMsgf(msg)
	return true
}

package manager

import (
	"bytes"
	"os"
	"path/filepath"
	"strings"

	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/marketmaker"

	"github.com/spf13/viper"
	"github.com/stevedomin/termtable"
)

// 新增做市机，并不会自动启动，需要 .runMarketMaker 手工启动
type AddMarketMakerManagerCommand ManagerCommand

func NewAddMarketMakerManagerCommand(manager *QuanterManager) *AddMarketMakerManagerCommand {
	cmd := &AddMarketMakerManagerCommand{
		Command: command.Command{
			Name:            "addMarketMaker",
			Instruction:     "`.addMarketMaker marketMakerID` 新增做市机，请附带上传 marketMakerID.toml",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
			RequiresFile:    true,
		},
		manager: manager,
	}
	return cmd
}

func (this *AddMarketMakerManagerCommand) Prepare() bool {
	marketMakerID := this.Args[0]
	if this.manager.checkMarketMakerControllerExist(marketMakerID) {
		this.ErrorMsgf("做市机 %s 已经运行。", marketMakerID)
		return false
	}
	this.SendMsgf("文件下载中，请稍等...")
	downloadURL := this.Args[len(this.Args)-1]
	fileData, err := this.GetFile(downloadURL)
	if err != nil {
		this.ErrorMsgf("文件下载失败：%s, url: %s", err, downloadURL)
		return false
	}
	// 检查配置文件是否合法
	var config *marketmaker.MarketMakerControllerConfig
	viper.SetConfigType("toml")
	viper.ReadConfig(bytes.NewBuffer(fileData))

	if err := viper.Unmarshal(&config); err != nil {
		this.ErrorMsgf("配置文件解析失败, %v", err)
		return false
	}
	if err := config.Validate(); err != nil {
		this.ErrorMsgf("配置文件验证错误: %s, %v", err, config)
		return false
	}
	this.SendMsgf("配置文件正确。")
	return true
}

func (this *AddMarketMakerManagerCommand) Do() bool {
	controllerID := this.Args[0]
	if this.manager.checkMarketMakerControllerExist(controllerID) {
		this.ErrorMsgf("做市机 %s 已经存在。", controllerID)
		return false
	}
	downloadURL := this.Args[len(this.Args)-1]
	this.manager.addMarketMaker(controllerID, downloadURL)
	return true
}

// 警告：删除做市机会删除对应的配置文件，小心使用该命令
type RemoveMarketMakerManagerCommand ManagerCommand

func NewRemoveMarketMakerManagerCommand(manager *QuanterManager) *RemoveMarketMakerManagerCommand {
	cmd := &RemoveMarketMakerManagerCommand{
		Command: command.Command{
			Name:            "removeMarketMaker",
			Instruction:     "`.removeMarketMaker MarketMakerID` 删除做市机",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
			ConfirmPrompt:   "警告：该做市机的配置文件将会被删除！",
		},
		manager: manager,
	}
	return cmd
}

func (this *RemoveMarketMakerManagerCommand) Prepare() bool {
	controllerID := this.Args[0]
	controllerExist := this.manager.checkMarketMakerControllerExist(controllerID)
	if !controllerExist {
		this.ErrorMsgf("做市机 %s 没有运行，不能删除。", controllerID)
		return false
	}
	return true
}

func (this *RemoveMarketMakerManagerCommand) Do() bool {
	controllerID := this.Args[0]
	controller, _ := this.manager.getMarketMakerController(controllerID)
	controller.DangerousDelete()
	this.manager.removeMarketMakerController(controller.ID, true)
	this.SendMsgf("已删除做市机 %s", controllerID)
	return true
}

// 因为运行 MarketMakerController 需要 password 才能解密 APISecret，所以 .copyMarketMaker 之后并不能直接自动启动
// 需要通过 .runMarketMaker 命令单独手工启动
type CopyMarketMakerManagerCommand ManagerCommand

func NewCopyMarketMakerManagerCommand(manager *QuanterManager) *CopyMarketMakerManagerCommand {
	cmd := &CopyMarketMakerManagerCommand{
		Command: command.Command{
			Name:            "copyMarketMaker",
			Instruction:     "`.copyMarketMaker fromMarketMakerID toMarketMakerID` 复制运行中的做市机的配置文件",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *CopyMarketMakerManagerCommand) Prepare() bool {
	fromControllerID := this.Args[0]
	toControllerID := this.Args[1]
	if this.manager.checkMarketMakerControllerExist(toControllerID) {
		this.ErrorMsgf("目标做市机已经存在。")
		return false
	}
	if !this.manager.checkMarketMakerControllerExist(fromControllerID) {
		this.ErrorMsgf("源做市机没有运行。")
		return false
	}
	return true
}

func (this *CopyMarketMakerManagerCommand) Do() bool {
	fromControllerID := this.Args[0]
	toControllerID := this.Args[1]
	// 复制配置文件
	controller, _ := this.manager.getMarketMakerController(fromControllerID)
	if err := controller.SaveConfigsTo(toControllerID); err == nil {
		if err := controller.SaveStorageTo(toControllerID); err == nil {
			this.manager.Config.MarketMakers = append(this.manager.Config.MarketMakers, toControllerID)
			this.manager.Config.save()
			this.manager.SendMsgf("做市机 [%s] -> [%s] 配置文件和本地文件拷贝成功，输入 .runMarketMaker 启动。", fromControllerID, toControllerID)
			return true
		} else {
			this.manager.ErrorMsgf("拷贝做市机本地存储文件出错：%v", err)
			return false
		}
	} else {
		this.manager.ErrorMsgf("拷贝做市机的配置文件出错：%v", err)
		return false
	}
}

// 因为 MarketMakerController 中的 APISecret 是通过主密码加密，必须提供 password 才能正确解密
type RunMarketMakerManagerCommand ManagerCommand

func NewRunMarketMakerManagerCommand(manager *QuanterManager) *RunMarketMakerManagerCommand {
	cmd := &RunMarketMakerManagerCommand{
		Command: command.Command{
			Name:            "runMarketMaker",
			Alias:           []string{"runMarketMaker"},
			Instruction:     "`.runMarketMaker MarketMakerID authCode` 运行做市机",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
			Sensitive:       true,
		},
		manager: manager,
	}
	return cmd
}

func (this *RunMarketMakerManagerCommand) Do() bool {
	controllerID := this.Args[0]
	authCode := this.Args[1]

	if this.manager.checkMarketMakerControllerExist(controllerID) {
		this.ErrorMsgf("做市机 [%s] 已经运行了。", controllerID)
		return false
	}

	// dont need password, manager already has password set
	this.manager.runMarketMakers([]string{controllerID}, "", authCode)
	return true
}

type StopMarketMakerManagerCommand ManagerCommand

func NewStopMarketMakerManagerCommand(manager *QuanterManager) *StopMarketMakerManagerCommand {
	cmd := &StopMarketMakerManagerCommand{
		Command: command.Command{
			Name:            "stopMarketMaker",
			Alias:           []string{"stopMarketMaker"},
			Instruction:     "`.stopMarketMaker MarketMakerID authCode` 停止运行做市机",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		manager: manager,
	}
	return cmd
}

func (this *StopMarketMakerManagerCommand) Do() bool {
	controllerID := this.Args[0]

	if !this.manager.checkMarketMakerControllerExist(controllerID) {
		this.ErrorMsgf("做市机 [%s] 没有运行。", controllerID)
		return false
	}

	this.manager.stopMarketMakers([]string{controllerID}, true)
	this.SendMsgf("做市机 %s 停止运行成功。", controllerID)
	return true
}

type ListMarketMakerManagerCommand ManagerCommand

func NewListMarketMakerManagerCommand(manager *QuanterManager) *ListMarketMakerManagerCommand {
	cmd := &ListMarketMakerManagerCommand{
		Command: command.Command{
			Name:            "listMarketMaker",
			Alias:           []string{"listMarketMaker"},
			Instruction:     "`.listMarketMaker` 列出做市机",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *ListMarketMakerManagerCommand) Do() bool {
	var files []string

	err := filepath.Walk(this.manager.configPath, func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			return nil
		}
		if strings.HasSuffix(path, ".marketmaker.toml") {
			files = append(files, path)
		}
		return nil
	})
	if err != nil {
		zlog.Panicf("walk conifg files error: %v", err)
	}
	controllerIDs := []string{}
	for _, file := range files {
		_, filename := filepath.Split(file)
		parts := strings.Split(filename, ".marketmaker.toml")
		if len(parts) > 0 {
			controllerIDs = append(controllerIDs, parts[0])
		}
	}

	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	headers := []string{"MarketMakerID", "Running"}
	t.SetHeader(headers)
	for _, controllerID := range controllerIDs {

		row := []string{
			controllerID,
			"",
		}
		controller, _ := this.manager.getMarketMakerController(controllerID)
		if controller != nil && controller.IsLaunched() {
			row[1] = "Yes"
		} else {
			row[1] = "No"
		}
		t.AddRow(row)
	}
	result := t.Render()
	this.SendMsgf("```%s```", result)
	return true
}

// 具体实现

func (this *QuanterManager) addMarketMaker(controllerID, downloadURL string) {
	fileData, err := this.GetFile(downloadURL)
	if err != nil {
		this.ErrorMsgf("文件下载失败：%s", err)
	}
	// 检查配置文件是否合法
	var config *marketmaker.MarketMakerControllerConfig
	viper.SetConfigType("toml")
	viper.ReadConfig(bytes.NewBuffer(fileData))

	if err := viper.Unmarshal(&config); err != nil {
		this.ErrorMsgf("配置文件解析失败, %v", err)
	}
	if err := config.Validate(); err != nil {
		this.ErrorMsgf("配置文件验证错误: %s, %v", err, config)
	}

	if err := config.SaveTo(this.configPath, controllerID, false); err != nil {
		this.ErrorMsgf("写 [%s] 的配置文件出现错误： %v", controllerID, err)
	} else {
		this.SendMsgf("做市机 [%s] 添加成功，请运行 `.runMarketMaker` 启动做市机。", controllerID)
	}
}

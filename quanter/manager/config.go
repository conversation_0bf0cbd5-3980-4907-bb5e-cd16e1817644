package manager

import (
	"fmt"
	"os"
	"path"
	"strings"

	"github.com/wizhodl/quanter/common/zlog"

	"github.com/spf13/viper"
)

type QuanterManagerConfig struct {
	ID                string
	Turtles           []string
	Arbitragers       []string
	Griders           []string
	Traders           []string
	MarketMakers      []string
	AssetsControllers []string
	GatewayAdapters   []string

	CheckAliveUserID    string
	CheckAliveAPISecret string
	CheckAliveAPIURL    string

	ProxyManager    string
	OrderController string
	manager         *QuanterManager
}

func (this *QuanterManagerConfig) Validate() (bool, error) {
	// AssetGroups 的配置不是很关键，不验证其内容的合法性
	// 检查 Turtle 的配置是否在
	for _, turtleID := range this.Turtles {
		if _, err := os.Stat(fmt.Sprintf("%s/%s.turtle.toml", this.manager.configPath, turtleID)); os.IsNotExist(err) {
			return false, fmt.Errorf("%s.turtle.toml is not exist in %s", turtleID, this.manager.configPath)
		}
	}

	return true, nil
}

func (this *QuanterManagerConfig) save() {
	configPath := this.manager.configPath
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		if err := os.Mkdir(configPath, 0755); err != nil {
			zlog.Panicf("[%s] can not create configPath at %s", this.manager.ID, this.manager.configPath)
		}
	}
	path := path.Join(configPath, this.manager.ID+".toml")
	if err := os.WriteFile(path, []byte(this.ToTomlContent()), 0755); err != nil {
		zlog.Panicf("WriteFile %s err %v", this.manager.ID, err)
	}
}

func (this *QuanterManagerConfig) ToTomlContent() string {
	turtlesStr := []string{}
	for _, controllerID := range this.Turtles {
		turtlesStr = append(turtlesStr, fmt.Sprintf(`"%s"`, controllerID))
	}
	arbitragersStr := []string{}
	for _, controllerID := range this.Arbitragers {
		arbitragersStr = append(arbitragersStr, fmt.Sprintf(`"%s"`, controllerID))
	}
	gridersStr := []string{}
	for _, controllerID := range this.Griders {
		gridersStr = append(gridersStr, fmt.Sprintf(`"%s"`, controllerID))
	}
	tradersStr := []string{}
	for _, controllerID := range this.Traders {
		tradersStr = append(tradersStr, fmt.Sprintf(`"%s"`, controllerID))
	}
	marketMakersStr := []string{}
	for _, controllerID := range this.MarketMakers {
		marketMakersStr = append(marketMakersStr, fmt.Sprintf(`"%s"`, controllerID))
	}
	assetsControllersStr := []string{}
	for _, controllerName := range this.AssetsControllers {
		assetsControllersStr = append(assetsControllersStr, fmt.Sprintf(`"%s"`, controllerName))
	}
	gatewayAdaptersStr := []string{}
	for _, controllerAndChannel := range this.GatewayAdapters {
		gatewayAdaptersStr = append(gatewayAdaptersStr, fmt.Sprintf(`"%s"`, controllerAndChannel))
	}
	return fmt.Sprintf(`Turtles = [%v]`, strings.Join(turtlesStr, ", ")) + "\n" +
		fmt.Sprintf(`Arbitragers = [%v]`, strings.Join(arbitragersStr, ", ")) + "\n" +
		fmt.Sprintf(`Griders = [%v]`, strings.Join(gridersStr, ", ")) + "\n" +
		fmt.Sprintf(`Traders = [%v]`, strings.Join(tradersStr, ", ")) + "\n" +
		fmt.Sprintf(`MarketMakers = [%v]`, strings.Join(marketMakersStr, ", ")) + "\n" +
		fmt.Sprintf(`AssetsControllers = [%v]`, strings.Join(assetsControllersStr, ", ")) + "\n" +
		fmt.Sprintf(`GatewayAdapters = [%v]`, strings.Join(gatewayAdaptersStr, ", ")) + "\n" +
		fmt.Sprintf(`ProxyManager = "%s"`, this.ProxyManager) + "\n" +
		fmt.Sprintf(`OrderController = "%s"`, this.OrderController) + "\n" +
		fmt.Sprintf(`CheckAliveAPIURL = "%s"`, this.CheckAliveAPIURL) + "\n" +
		fmt.Sprintf(`CheckAliveAPISecret = "%s"`, this.CheckAliveAPISecret) + "\n" +
		fmt.Sprintf(`CheckAliveUserID = "%s"`, this.CheckAliveUserID) + "\n"
}

func NewQuanterManagerConfig(manager *QuanterManager) (config *QuanterManagerConfig, er error) {
	config = &QuanterManagerConfig{ID: manager.ID}
	if _, err := os.Stat(fmt.Sprintf("%s/%s.toml", manager.configPath, manager.ID)); !os.IsNotExist(err) {
		viper.SetConfigName(manager.ID)
		viper.AddConfigPath(manager.configPath)
		err := viper.ReadInConfig()
		if err == nil {
			err := viper.Unmarshal(&config)
			if err != nil {
				zlog.Panicf("unable to decode QuanterManager config into struct, %v", err)
			}
			config.manager = manager
			if ok, err := config.Validate(); !ok {
				zlog.Errorf("config validation error: %s", err.Error())
				er = fmt.Errorf("config validation error: %s", err)
			} else {
				zlog.Infof("[%s] load config from local config file", manager.ID)
			}
			return
		} else {
			er = err
		}
	} else {
		zlog.Errorf("[%s] config file %s/%s.toml not exist, start with empty quanters.", manager.ID, manager.configPath, manager.ID)
		config = &QuanterManagerConfig{Turtles: []string{}, Arbitragers: []string{}, AssetsControllers: []string{}}
		config.manager = manager
	}
	return
}

func (this *QuanterManagerConfig) removeTurtle(turtleID string) {
	index := -1
	for i, id := range this.Turtles {
		if strings.EqualFold(id, turtleID) {
			index = i
		}
	}
	if index >= 0 {
		this.Turtles = append(this.Turtles[:index], this.Turtles[index+1:]...)
	}
	this.save()
}

func (this *QuanterManagerConfig) removeArbitrager(arbiID string) {
	index := -1
	for i, id := range this.Arbitragers {
		if strings.EqualFold(id, arbiID) {
			index = i
		}
	}
	if index >= 0 {
		this.Arbitragers = append(this.Arbitragers[:index], this.Arbitragers[index+1:]...)
	}
	this.save()
}

func (this *QuanterManagerConfig) removeGrider(controllerID string) {
	index := -1
	for i, id := range this.Griders {
		if strings.EqualFold(id, controllerID) {
			index = i
		}
	}
	if index >= 0 {
		this.Griders = append(this.Griders[:index], this.Griders[index+1:]...)
	}
	this.save()
}

func (this *QuanterManagerConfig) removeTrader(controllerID string) {
	index := -1
	for i, id := range this.Traders {
		if strings.EqualFold(id, controllerID) {
			index = i
		}
	}
	if index >= 0 {
		this.Traders = append(this.Traders[:index], this.Traders[index+1:]...)
	}
	this.save()
}

func (this *QuanterManagerConfig) removeMarketMaker(controllerID string) {
	index := -1
	for i, id := range this.MarketMakers {
		if strings.EqualFold(id, controllerID) {
			index = i
		}
	}
	if index >= 0 {
		this.MarketMakers = append(this.MarketMakers[:index], this.MarketMakers[index+1:]...)
	}
	this.save()
}

func (this *QuanterManagerConfig) addTurtle(turtleID string) {
	index := -1
	for i, id := range this.Turtles {
		if strings.EqualFold(id, turtleID) {
			index = i
		}
	}
	if index == -1 {
		this.Turtles = append(this.Turtles, turtleID)
	}
	this.save()
}

func (this *QuanterManagerConfig) addArbitrager(arbiID string) {
	index := -1
	for i, id := range this.Arbitragers {
		if strings.EqualFold(id, arbiID) {
			index = i
		}
	}
	if index == -1 {
		this.Arbitragers = append(this.Arbitragers, arbiID)
	}
	this.save()
}

func (this *QuanterManagerConfig) addGrider(controllerID string) {
	index := -1
	for i, id := range this.Griders {
		if strings.EqualFold(id, controllerID) {
			index = i
		}
	}
	if index == -1 {
		this.Griders = append(this.Griders, controllerID)
	}
	this.save()
}

func (this *QuanterManagerConfig) addTrader(controllerID string) {
	index := -1
	for i, id := range this.Traders {
		if strings.EqualFold(id, controllerID) {
			index = i
		}
	}
	if index == -1 {
		this.Traders = append(this.Traders, controllerID)
	}
	this.save()
}

func (this *QuanterManagerConfig) addMarketMaker(controllerID string) {
	index := -1
	for i, id := range this.MarketMakers {
		if strings.EqualFold(id, controllerID) {
			index = i
		}
	}
	if index == -1 {
		this.MarketMakers = append(this.MarketMakers, controllerID)
	}
	this.save()
}

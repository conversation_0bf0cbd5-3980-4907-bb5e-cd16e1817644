package manager

import (
	"fmt"
	"io"
	"math"
	"os"
	"path"
	"sort"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"

	"github.com/tidwall/gjson"
)

type AssetSnapshot struct {
	Assets map[int64]float64
}

type AssetItem struct {
	Total    float64
	Asset    float64
	External float64
}
type Assets map[int64]AssetItem
type AssetColumn map[int64]float64
type AssetSnapshotMap map[string]AssetSnapshot
type AssetsMap map[string]Assets

type SetAssetsControllersManagerCommand ManagerCommand

func NewSetAssetsControllersManagerCommand(manager *QuanterManager) *SetAssetsControllersManagerCommand {
	cmd := &SetAssetsControllersManagerCommand{
		Command: command.Command{
			Name:            "setAssetsControllers",
			Instruction:     "`.setAssetsControllers Controller1,Controller2... AuthCode` 设置资产快照机器人列表",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		manager: manager,
	}
	return cmd
}

func (this *SetAssetsControllersManagerCommand) Do() bool {
	controllers := strings.Split(this.Args[0], ",")
	this.manager.Config.AssetsControllers = controllers
	this.manager.Config.save()
	this.SendMsgf("资产快照机器人列表设置 (%s) 成功。", utils.SliceStringJoin(controllers, ", ", true))
	return true
}

type AssetsManagerCommand ManagerCommand

func NewAssetsManagerCommand(manager *QuanterManager) *AssetsManagerCommand {
	cmd := &AssetsManagerCommand{
		Command: command.Command{
			Name:            "assets",
			Alias:           []string{"as"},
			Instruction:     "`.assets(+)` 打印资产快照",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *AssetsManagerCommand) Do() bool {
	// 从 storage 文件中读取原始数据，即 Assets 和 AssetInOuts
	turtleSnapshots := AssetSnapshotMap{}
	arbiSnapshots := AssetSnapshotMap{}
	gridSnapshots := AssetSnapshotMap{}
	allSnapshots := AssetSnapshotMap{}
	exchangesSnapshots := map[string]AssetSnapshotMap{} // 以交易所名称为 key

	for _, controllerName := range this.manager.Config.AssetsControllers {
		found := false
		// 首先尝试从套利机中寻找 controllerName 的数据
		arbiPath := path.Join(this.manager.configPath, fmt.Sprintf("%s.arbi_storage", controllerName))
		if _, err := os.Stat(arbiPath); os.IsNotExist(err) {
			zlog.Debugf("%s arbi storage not found", controllerName)
		} else {
			if snapshot, exchangeName, err := this.ParseDataFromStorage(arbiPath); err != nil {
				this.ErrorMsgf("从 (%s) 中读取资产快照出错。", arbiPath)
				return false
			} else {
				found = true
				arbiSnapshots[controllerName] = snapshot
				allSnapshots[controllerName] = snapshot

				if exchangeName != "" {
					if _, found := exchangesSnapshots[exchangeName]; !found {
						exchangesSnapshots[exchangeName] = AssetSnapshotMap{}
					}
					exchangesSnapshots[exchangeName][controllerName] = snapshot
				} else {
					zlog.Errorf("parese exchange name from storage failed, path: %s", arbiPath)
				}
			}
		}
		// 然后尝试从趋势机中寻找 controllerName 的数据
		turtlePath := path.Join(this.manager.configPath, fmt.Sprintf("%s.turtle_storage", controllerName))
		if _, err := os.Stat(turtlePath); os.IsNotExist(err) {
			zlog.Debugf("%s arbi storage not found", controllerName)
		} else {
			if snapshot, exchangeName, err := this.ParseDataFromStorage(turtlePath); err != nil {
				this.ErrorMsgf("从 (%s) 中读取资产快照出错。", arbiPath)
				return false
			} else {
				found = true
				turtleSnapshots[controllerName] = snapshot
				allSnapshots[controllerName] = snapshot

				if exchangeName != "" {
					if _, found := exchangesSnapshots[exchangeName]; !found {
						exchangesSnapshots[exchangeName] = AssetSnapshotMap{}
					}
					exchangesSnapshots[exchangeName][controllerName] = snapshot
				} else {
					zlog.Errorf("parese exchange name from storage failed, path: %s", turtlePath)
				}
			}
		}
		// 然后尝试从趋势机中寻找 controllerName 的数据
		gridPath := path.Join(this.manager.configPath, fmt.Sprintf("%s.grid_storage", controllerName))
		if _, err := os.Stat(gridPath); os.IsNotExist(err) {
			zlog.Debugf("%s grid storage not found", controllerName)
		} else {
			if snapshot, exchangeName, err := this.ParseDataFromStorage(gridPath); err != nil {
				this.ErrorMsgf("从 (%s) 中读取资产快照出错。", arbiPath)
				return false
			} else {
				found = true
				gridSnapshots[controllerName] = snapshot
				allSnapshots[controllerName] = snapshot

				if exchangeName != "" {
					if _, found := exchangesSnapshots[exchangeName]; !found {
						exchangesSnapshots[exchangeName] = AssetSnapshotMap{}
					}
					exchangesSnapshots[exchangeName][controllerName] = snapshot
				} else {
					zlog.Errorf("parese exchange name from storage failed, path: %s", turtlePath)
				}
			}
		}

		if !found {
			this.ErrorMsgf("资产快照机器人 (%s) 不存在。", controllerName)
			return false
		}
	}

	if len(allSnapshots) == 0 {
		this.ErrorMsgf("没有读取到任何资产快照数据。")
		return false
	}

	// 按机器人类型聚合报告
	// 从原始数据中转换为更方便后续计算的结构
	turtleAssets := turtleSnapshots.ToAssetsMap()
	arbiAssets := arbiSnapshots.ToAssetsMap()
	gridAssets := gridSnapshots.ToAssetsMap()

	// 打印资产的总体预览，需要分别合并“全部”、“趋势机”、“套利机”的数据
	turtleCombinedAssets := turtleAssets.Combined()
	arbiCombinedAssets := arbiAssets.Combined()
	gridCombinedAssets := gridAssets.Combined()

	columns := []Assets{
		turtleCombinedAssets,
		arbiCombinedAssets,
		gridCombinedAssets,
	}
	msg := this.manager.MergeRenderAssets("All", []string{"Trender", "Arbi", "Grid"}, columns)

	// 按交易所聚合报告
	exchangeNames := []string{}
	for key := range exchangesSnapshots {
		exchangeNames = append(exchangeNames, key)
	}
	sort.SliceStable(exchangeNames, func(i, j int) bool { return exchangeNames[i] < exchangeNames[j] })

	exchangeColumns := []Assets{}
	for _, exchangeName := range exchangeNames {
		exchangeColumns = append(exchangeColumns, exchangesSnapshots[exchangeName].ToAssetsMap().Combined())
	}
	exchangeMsg := this.manager.MergeRenderAssets("All", exchangeNames, exchangeColumns)
	this.SendMsgf("```%s\n\n%s```", msg, exchangeMsg)
	return true
}

func (this AssetSnapshotMap) ToAssetsMap() (result AssetsMap) {
	result = AssetsMap{}
	for controllerName, snapshot := range this {
		assets := Assets{}
		lastExternalAsset := 0.0

		keys := []int64{}
		for k := range snapshot.Assets {
			keys = append(keys, k)
		}
		sort.SliceStable(keys, func(i, j int) bool { return keys[i] < keys[j] })

		for _, timestamp := range keys {
			asset := snapshot.Assets[timestamp]
			assets[timestamp] = AssetItem{
				Total:    asset,
				Asset:    asset,
				External: lastExternalAsset,
			}
		}
		result[controllerName] = assets
	}
	return
}

func (this AssetsMap) Combined() (result Assets) {
	result = Assets{}
	// 合并要考虑缺值插值的问题
	keys := []int64{}
	for _, assets := range this {
		for timestamp := range assets {
			// 去重，否则后续同一个 key 会重复计算
			if !utils.SliceContains(keys, timestamp) {
				keys = append(keys, timestamp)
			}
		}
	}
	sort.SliceStable(keys, func(i, j int) bool { return keys[i] < keys[j] })

	for _, key := range keys {
		for _, assets := range this {
			assetColumn := assets.AssetColumn()
			externalColumn := assets.ExternalColumn()
			// 通过搜索插入空缺的值，AssetToTimestamp 函数自动搜索最接近的值
			// 比如要寻找的 T2 不存在，使用比 T2 更早的 T1 的值代替，满足 T1 < T2 < T3
			asset := assetColumn.AssetToTimestamp(key)
			external := externalColumn.AssetToTimestamp(key)

			resultItem := AssetItem{}
			if item, ok := result[key]; ok {
				resultItem.Asset = item.Asset + asset
				resultItem.External = item.External + external
				resultItem.Total = item.Total + asset + external
			} else {
				resultItem.Asset = asset
				resultItem.External = external
				resultItem.Total = asset + external
			}
			result[key] = resultItem
		}
	}

	return
}

func (this Assets) TotalColumn() (column AssetColumn) {
	column = AssetColumn{}
	for timestamp, item := range this {
		column[timestamp] = item.Total
	}
	return
}

func (this Assets) AssetColumn() (column AssetColumn) {
	column = AssetColumn{}
	for timestamp, item := range this {
		column[timestamp] = item.Asset
	}
	return
}

func (this Assets) ExternalColumn() (column AssetColumn) {
	column = AssetColumn{}
	for timestamp, item := range this {
		column[timestamp] = item.External
	}
	return
}

func (this *AssetsManagerCommand) ParseDataFromStorage(filePath string) (snapshot AssetSnapshot, exchangeName string, er error) {
	if f, err := os.OpenFile(filePath, os.O_RDONLY, 0755); err != nil {
		er = fmt.Errorf("open storage file at (%s), error:  (%s) ", filePath, err)
		return
	} else {
		if fileBytes, err := io.ReadAll(f); err != nil {
			er = fmt.Errorf("read file content failed, error: %s", err)
			return
		} else {
			content := gjson.ParseBytes(fileBytes)
			exchangeName = content.Get(`ExchangeName`).String()
			// gjson 读取的 Assets key 是字符串，需要转换一次
			assets := content.Get(`Assets`).Map()
			snapshot.Assets = map[int64]float64{}
			for ts, gValue := range assets {
				if t, err := cast.ToInt64E(ts); err != nil {
					er = fmt.Errorf("parse assets key (%s) as int64 error", ts)
					return
				} else {
					snapshot.Assets[t] = float64(gValue.Float())
				}
			}
		}
	}
	return
}

func (this *QuanterManager) MergeRenderAssets(subLabel string, assetsLabels []string, assetsList []Assets) string {
	type Row struct {
		timestamp int64
		row       []string
	}
	// 合并 label
	labels := []string{}
	labels = append(labels, subLabel)
	labels = append(labels, assetsLabels...)

	headers := []string{"Time"}
	for _, label := range labels {
		headers = append(headers, fmt.Sprintf("%s Total", label))
		headers = append(headers, fmt.Sprintf("%s Asset", label))
		headers = append(headers, fmt.Sprintf("%s External", label))
	}

	assetColumns := []AssetColumn{}
	for _, assets := range assetsList {
		assetColumns = append(assetColumns, assets.TotalColumn())
		assetColumns = append(assetColumns, assets.AssetColumn())
		assetColumns = append(assetColumns, assets.ExternalColumn())
	}

	allTimeKeys := []int64{}
	for _, column := range assetColumns {
		for key := range column {
			if !utils.SliceContains(allTimeKeys, key) {
				allTimeKeys = append(allTimeKeys, key)
			}
		}
	}
	sort.SliceStable(allTimeKeys, func(i, j int) bool { return allTimeKeys[i] < allTimeKeys[j] })

	rows := []Row{}

	// 求和
	subAssets := Assets{}
	for _, key := range allTimeKeys {
		item := AssetItem{}
		for _, assets := range assetsList {
			item.Total += assets.TotalColumn().AssetToTimestamp(key)
			item.Asset += assets.AssetColumn().AssetToTimestamp(key)
			item.External += assets.ExternalColumn().AssetToTimestamp(key)
		}
		subAssets[key] = item
	}

	// 更新求和后的 columns
	assetColumns = append([]AssetColumn{subAssets.TotalColumn(), subAssets.AssetColumn(), subAssets.ExternalColumn()}, assetColumns...)

	for _, key := range allTimeKeys {
		row := []string{}
		tKey := time.Unix(key, 0)
		if this.Debug {
			row = append(row, fmt.Sprintf("%s - %d", utils.FormatShortTimeStr(&tKey, true), key))
		} else {
			row = append(row, utils.FormatShortTimeStr(&tKey, true))
		}

		for _, column := range assetColumns {
			row = append(row, fmt.Sprintf("%.2f", column.AssetToTimestamp(key)))
		}
		rows = append(rows, Row{timestamp: key, row: row})
	}
	// rows 倒序排列
	sort.SliceStable(rows, func(i, j int) bool { return rows[i].timestamp > rows[j].timestamp })
	t := exchange.NewTable()
	t.SetHeader(headers)
	for _, row := range rows {
		t.AddRow(row.row)
	}
	return t.Render()
}

func (this AssetColumn) AssetToTimestamp(ts int64) float64 {
	if value, found := this[ts]; found {
		return value
	}
	sortedKeys := this.SortedKeys()
	var foundKey int64 = -1
	for i, key := range sortedKeys {
		var upper int64 = math.MaxInt64
		if i < len(sortedKeys)-1 {
			upper = sortedKeys[i+1]
		}
		if ts > key && ts < upper {
			foundKey = key
			break
		}
	}
	if foundKey > -1 {
		return this[foundKey]
	}
	return -1
}

func (this AssetColumn) SortedKeys() []int64 {
	keys := []int64{}
	for key := range this {
		keys = append(keys, key)
	}
	sort.SliceStable(keys, func(i, j int) bool { return keys[i] < keys[j] })
	return keys
}

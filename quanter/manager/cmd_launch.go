package manager

import (
	"os"
	"strings"

	"github.com/wizhodl/pgate"
	"github.com/wizhodl/quanter/arbitrage"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/grid"
	"github.com/wizhodl/quanter/marketmaker"
	"github.com/wizhodl/quanter/orderctrl"
	"github.com/wizhodl/quanter/pgatectrl"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/trader"
	"github.com/wizhodl/quanter/turtle"
	"github.com/wizhodl/quanter/utils"
)

func (this *QuanterManager) launch(password string, authCode string, safeModeInclude string) bool {
	if password == "" || authCode == "" {
		zlog.Warnf("launching manager with empty password or authcode, be careful")
	}
	err := secrets.CheckPassword(password, authCode)
	if err != nil {
		this.AlertMsgf("launch manager failed (%s), check password error: %s", this.ID, err)
		this.SetLaunched(false)
		return false
	}
	this.SetLaunched(true)
	go this.RefreshCheckAlive()

	excluded := []string{}
	if strings.Contains(safeModeInclude, "trender") {
		this.runTurtles(this.Config.Turtles, password, authCode)
	} else {
		excluded = append(excluded, "趋势机")
	}
	if strings.Contains(safeModeInclude, "arbitrager") {
		this.runArbitragers(this.Config.Arbitragers, password, authCode)
	} else {
		excluded = append(excluded, "套利机")
	}
	if strings.Contains(safeModeInclude, "grider") {
		this.runGriders(this.Config.Griders, password, authCode)
	} else {
		excluded = append(excluded, "网格机")
	}
	if strings.Contains(safeModeInclude, "trader") {
		this.runTraders(this.Config.Traders, password, authCode)
	} else {
		excluded = append(excluded, "交易机")
	}
	if strings.Contains(safeModeInclude, "maker") {
		this.runMarketMakers(this.Config.MarketMakers, password, authCode)
	} else {
		excluded = append(excluded, "做市机")
	}
	// 订单指令机依赖于交易机、做市机、网格机，所以需要放到最后启动
	if strings.Contains(safeModeInclude, "order") {
		this.runOrderController(password, authCode)
	} else {
		excluded = append(excluded, "订单指令机")
	}
	if strings.Contains(safeModeInclude, "gateway") {
		this.runGatewayAdapters(password, authCode)
	} else {
		excluded = append(excluded, "gateway 网关")
	}

	if strings.Contains(safeModeInclude, "pgate") {
		this.runProxyManager(password, authCode)
		go this.setupProxy() // 启动代理机后设置各策略的代理，所以需要放到各策略启动之后
	} else {
		excluded = append(excluded, "代理机")
	}

	this.safeMode = safeModeInclude
	if len(excluded) > 0 {
		this.SendMsgf("安全模式，不启动 (%s)", utils.SliceStringJoin(excluded, ", ", false))
	}
	return true
}

func (this *QuanterManager) runTurtles(turtleIDs []string, password string, authCode string) {
	controllers := []*turtle.TurtleController{}
	for _, turtleID := range turtleIDs {
		if this.checkTurtleControllerExist(turtleID) {
			break
		}
		controller, err := turtle.NewTurtleController(turtleID, this.Debug, this.configPath, this.CommitHash, this.BuildTime, this.releaseBinaryDirPath, this.logDirPath, this.Messenger, this.ID)
		if err != nil {
			this.ErrorMsgf(err.Error())
			return
		} else {
			controllers = append(controllers, controller)
			this.TurtleControllers = append(this.TurtleControllers, controller)
			this.Config.addTurtle(controller.ID)
		}
	}

	for _, controller := range controllers {
		this.SendMsgf("启动 %s ...", controller.ID)
		if success := controller.Launch(password, authCode); success {
			go controller.InitStartTurtles("")
		}
		controller.Run()
	}
}

func (this *QuanterManager) stopTurtles(turtleIDs []string, save bool) {
	for _, turtleID := range turtleIDs {
		if !this.checkTurtleControllerExist(turtleID) {
			break
		}
		controller, _ := this.getTurtleController(turtleID)
		controller.Close()
		this.removeTurtleController(controller.ID, save)
	}
}

func (this *QuanterManager) runArbitragers(arbiIDs []string, password string, authCode string) {
	controllers := []*arbitrage.ArbitragerController{}
	for _, arbiID := range arbiIDs {
		if this.checkArbitragerExist(arbiID) {
			break
		}
		controller, err := arbitrage.NewArbitragerController(arbiID, this.Debug, this.configPath, this.CommitHash, this.BuildTime, this.releaseBinaryDirPath, this.logDirPath, this.Messenger, this.ID)
		if err != nil {
			this.ErrorMsgf(err.Error())
			return
		} else {
			controllers = append(controllers, controller)
			this.ArbitragerControllers = append(this.ArbitragerControllers, controller)
			this.Config.addArbitrager(controller.ID)
		}
	}

	for _, controller := range controllers {
		this.SendMsgf("启动 %s ...", controller.ID)
		if success := controller.Launch(password, authCode); success {
			go controller.InitArbitrager()
		}
		controller.Run()
	}
}

func (this *QuanterManager) runGriders(controllerIDs []string, password string, authCode string) {
	controllers := []*grid.GridController{}
	for _, griderID := range controllerIDs {
		if this.checkGridControllerExist(griderID) {
			break
		}
		controller, err := grid.NewGridController(griderID, this.Debug, this.configPath, this.CommitHash, this.BuildTime, this.releaseBinaryDirPath, this.logDirPath, this.Messenger, this.ID, this.getOrderController)
		if err != nil {
			this.ErrorMsgf(err.Error())
			return
		} else {
			controllers = append(controllers, controller)
			this.GridControllers = append(this.GridControllers, controller)
			this.Config.addGrider(controller.ID)
		}
	}

	for _, controller := range controllers {
		this.SendMsgf("启动 %s ...", controller.ID)
		if success := controller.Launch(password, authCode); success {
			go controller.InitStartGrids()
		}
		controller.Run()
	}
}

func (this *QuanterManager) runTraders(controllerIDs []string, password string, authCode string) {
	controllers := []*trader.TraderController{}
	for _, traderID := range controllerIDs {
		if this.checkTraderControllerExist(traderID) {
			break
		}
		controller, err := trader.NewTraderController(traderID, this.Debug, this.configPath, this.CommitHash, this.BuildTime, this.releaseBinaryDirPath, this.logDirPath, this.Messenger, this.ID)
		if err != nil {
			this.ErrorMsgf(err.Error())
			return
		} else {
			controllers = append(controllers, controller)
			this.TraderControllers = append(this.TraderControllers, controller)
			this.Config.addTrader(controller.ID)
		}
	}

	for _, controller := range controllers {
		this.SendMsgf("启动 %s ...", controller.ID)
		if success := controller.Launch(password, authCode); success {
			go controller.Init()
		}
		controller.Run()
	}
}

func (this *QuanterManager) runMarketMakers(controllerIDs []string, password string, authCode string) {
	controllers := []*marketmaker.MarketMakerController{}
	for _, marketMakerID := range controllerIDs {
		if this.checkMarketMakerControllerExist(marketMakerID) {
			break
		}
		controller, err := marketmaker.NewMarketMakerController(marketMakerID, this.Debug, this.configPath, this.CommitHash, this.BuildTime, this.releaseBinaryDirPath, this.logDirPath, this.Messenger, this.ID)
		if err != nil {
			this.ErrorMsgf(err.Error())
			return
		} else {
			controllers = append(controllers, controller)
			this.MarketMakerControllers = append(this.MarketMakerControllers, controller)
			this.Config.addMarketMaker(controller.ID)
		}
	}

	for _, controller := range controllers {
		this.SendMsgf("启动 %s ...", controller.ID)
		if success := controller.Launch(password, authCode); success {
			go controller.Init()
		}
		controller.Run()
	}
}

func (this *QuanterManager) stopArbitragers(arbiIDs []string, save bool) {
	for _, arbiID := range arbiIDs {
		if !this.checkArbitragerExist(arbiID) {
			break
		}
		controller, _ := this.getArbitragerController(arbiID)
		controller.Close()
		this.removeArbitragerController(controller.ID, save)
	}
}

func (this *QuanterManager) stopGriders(controllerIDs []string, save bool) {
	for _, griderID := range controllerIDs {
		if !this.checkGridControllerExist(griderID) {
			break
		}
		controller, _ := this.getGridController(griderID)
		controller.Close()
		this.removeGridController(controller.ID, save)
	}
}

func (this *QuanterManager) stopTraders(controllerIDs []string, save bool) {
	for _, traderID := range controllerIDs {
		if !this.checkTraderControllerExist(traderID) {
			break
		}
		controller, _ := this.getTraderController(traderID)
		controller.Close()
		this.removeTraderController(controller.ID, save)
	}
}

func (this *QuanterManager) stopMarketMakers(controllerIDs []string, save bool) {
	for _, marketMakerID := range controllerIDs {
		if !this.checkMarketMakerControllerExist(marketMakerID) {
			break
		}
		controller, _ := this.getMarketMakerController(marketMakerID)
		controller.Close()
		this.removeMarketMakerController(controller.ID, save)
	}
}

func (this *QuanterManager) runProxyManager(password string, authCode string) {
	if this.Config.ProxyManager == "" {
		return
	}

	if password == "" {
		password, _ = os.LookupEnv("PGATE_PASSWORD")
	}

	controller, err := pgatectrl.NewProxyManagerController(this.Config.ProxyManager, this.Debug, this.configPath, this.CommitHash, this.BuildTime, this.releaseBinaryDirPath, this.logDirPath, this.Messenger, this.ID)
	if err != nil {
		this.ErrorMsgf(err.Error())
		return
	}

	launched := controller.Launch(password, authCode)
	if !launched {
		this.ErrorMsgf("launch proxy manager controller failed")
		return
	}
	// launch 成功后才能初始化 ProxyManager，因为需要解密密码
	err = controller.InitProxyManager()
	if err != nil {
		this.ErrorMsgf("init proxy manager failed: %v", err)
		return
	}
	this.ProxyManagerController = controller
}

func (this *QuanterManager) setupProxy() {
	var proxyGateway *pgate.ProxyGateway
	if this.ProxyManagerController != nil {
		proxyGateway = this.ProxyManagerController.ProxyManager.Gateway
	}

	for _, ctrl := range this.TurtleControllers {
		go ctrl.SetExchangeProxyLoop(proxyGateway)
	}

	for _, ctrl := range this.ArbitragerControllers {
		go ctrl.SetExchangeProxyLoop(proxyGateway)
	}

	for _, ctrl := range this.GridControllers {
		go ctrl.SetExchangeProxyLoop(proxyGateway)
	}

	for _, ctrl := range this.TraderControllers {
		go ctrl.SetExchangeProxyLoop(proxyGateway)
	}

	for _, ctrl := range this.MarketMakerControllers {
		go ctrl.SetExchangeProxyLoop(proxyGateway)
	}
}

func (this *QuanterManager) runOrderController(password string, authCode string) {
	if this.Config.OrderController == "" {
		this.SendMsgf("没有配置订单指令机")
		return
	}

	controller, err := orderctrl.NewOrderController(this.Config.OrderController, this.Debug, this.configPath, this.CommitHash, this.BuildTime, this.releaseBinaryDirPath, this.logDirPath, this.Messenger, this.ID, this.listControllers)
	if err != nil {
		this.ErrorMsgf("启动订单指令机失败: %s", err.Error())
		return
	}

	launched := controller.Launch(password, authCode)
	if !launched {
		this.ErrorMsgf("launch proxy manager controller failed")
		return
	}

	controller.Run()

	this.OrderController = controller
}

func (this *QuanterManager) listControllers() []base.Controllable {
	controllers := []base.Controllable{}
	for _, ctrl := range this.TraderControllers {
		controllers = append(controllers, ctrl)
	}
	for _, ctrl := range this.TurtleControllers {
		controllers = append(controllers, ctrl)
	}
	for _, ctrl := range this.ArbitragerControllers {
		controllers = append(controllers, ctrl)
	}
	for _, ctrl := range this.GridControllers {
		controllers = append(controllers, ctrl)
	}
	for _, ctrl := range this.MarketMakerControllers {
		controllers = append(controllers, ctrl)
	}
	return controllers
}

func (this *QuanterManager) getOrderController() *orderctrl.OrderController {
	return this.OrderController
}

func (this *QuanterManager) runGatewayAdapters(password string, authCode string) {
	if this.safeMode != "" && !strings.Contains(this.safeMode, "gateway") {
		zlog.Warnf("safe mode for gateway adapters is set, not starting gateway adapters")
		return
	}
	controllers := this.listControllers()
	if len(controllers) == 0 {
		this.WarnMsgf("找到 0 个控制器，不启动网关适配器")
		return
	}
	for _, controller := range controllers {
		// 如果没有密码，要先检查密码并设置
		if !secrets.HasPassword() {
			err := secrets.CheckPassword(password, authCode)
			if err != nil {
				zlog.Errorf("start gateway adapter failed, check password failed, controller: %s, error: %v", controller.GetID(), err)
				continue
			}
		}
		baseConfig := controller.GetBaseConfig()
		if baseConfig.GatewayAdapter != "" {
			adapter, err := NewGatewayAdapter(this, controller)
			if err != nil {
				zlog.Errorf("start gateway adapter failed, error: %v, controller: %s", err, controller.GetID())
				continue
			}
			if adapter != nil {
				this.Messenger.RegisterMessageHandler(adapter.Name, adapter.HandleSlackMessage, false)
				this.SendMsgf("启动网关适配器 %s 成功", adapter.Name)
			} else {
				zlog.Errorf("start gateway adapter failed, adapter is nil, controller: %s", controller.GetID())
			}
		}
	}
}

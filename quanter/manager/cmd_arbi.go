package manager

import (
	"strings"

	"github.com/wizhodl/quanter/arbitrage"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/utils"
)

type ArbitrageInstructionCommand struct {
	command.Command
	manager      *QuanterManager
	instructions []arbitrage.Instructable
}

func (this *ArbitrageInstructionCommand) PrePrepare() bool {
	for _, arbitrager := range this.manager.ArbitragerControllers {
		if symbolCode, err := arbitrage.NewSymbolCode(arbitrager, this.Args[1]); err != nil {
			this.ErrorMsgf("参数错误：%s", err.Error())
			return false
		} else {
			if symbolCode.IsSpot() {
				this.ErrorMsgf("参数错误：套利机 %s 不允许输入现货品种代码 %s 。", arbitrager.ID, symbolCode)
				return false
			}

			if symbolCode.IsFuture() && symbolCode.InstrumentType() != arbitrager.FutureInstrumentType() {
				this.ErrorMsgf("当前仅允许 %s 类型的合约", arbitrager.FutureInstrumentType())
				return false
			}
		}
	}
	return true
}

// InstructionCommand 的 Do 都是执行命令，共用这部分代码
func (this *ArbitrageInstructionCommand) Do() bool {
	if len(this.instructions) == 0 {
		this.ErrorMsgf("没有需要执行的命令。")
		return false
	} else {
		for _, instruction := range this.instructions {
			instruction.GetArbitrager().ProcessInstruction(instruction)
			this.SendMsgf("开始执行指令 `%s`。请输入 `.status %s %s` 查看进度。", instruction.GetID(), instruction.GetArbitrager().ID, instruction.GetID())
		}
		return true
	}
}

type ArbiArbitragerCommand struct {
	ArbitrageInstructionCommand
}

func NewArbiArbitragerCommand(manager *QuanterManager) *ArbiArbitragerCommand {
	cmd := &ArbiArbitragerCommand{
		ArbitrageInstructionCommand: ArbitrageInstructionCommand{
			Command: command.Command{
				Name:            "arbi",
				Instruction:     "`.arbi ExchangeName FutureCode 100%% targetBasisRatio(可选) config1=value1,config2=value2(可选)` 开仓套利",
				RequiresConfirm: true,
				ArgMin:          3,
				ArgMax:          5,
				AuthcodePos:     -1,
			},
			manager: manager,
		},
	}
	return cmd
}

func (this *ArbiArbitragerCommand) Prepare() bool {
	configStr := ""
	targetRatioStr := ""
	targetBasisRatio := 0.0

	if len(this.Args) == 5 {
		configStr = this.Args[4]
		targetRatioStr = this.Args[3]
	} else if len(this.Args) == 4 && strings.Contains(this.Args[3], "=") {
		configStr = this.Args[3]
	} else if len(this.Args) == 4 && !strings.Contains(this.Args[3], "=") {
		targetRatioStr = this.Args[3]
	}
	if targetRatioStr != "" {
		if r, _, err := utils.ParseFloatOrPercentage(targetRatioStr, false, true); err != nil {
			this.ErrorMsgf("参数错误：targetBasisRatio %s", err)
		} else {
			targetBasisRatio = r
		}
	}

	this.instructions = []arbitrage.Instructable{} // 几次不同的命令，可能将 instruction 添加到同一个 Command 实例中；每次 Prepare 都清理，以免串掉
	exchangeArg := this.Args[0]
	symbolCodeArg := this.Args[1]
	if exchangeName, futureCodes, err := this.manager.ParseExchangeFutureCode(exchangeArg, symbolCodeArg); err != nil {
		this.ErrorMsgf("交易所名称和期货代号解析错误：%s", err)
		return false
	} else {
		if num, percent, err := arbitrage.ParseNumOrPercentage(this.Args[2], true, false); err != nil {
			this.ErrorMsgf("参数错误：%s", err.Error())
			return false
		} else {
			arbis := this.manager.FilterArbitragersByExchangeName(exchangeName)
			for _, arbitrager := range arbis {
				for _, futureCode := range futureCodes {
					if arbitrager.CheckRunningInstruction(futureCode.Coin()) {
						this.ErrorMsgf("套利机 %s 的 %s 品种下有指令在运行。", arbitrager.ID, futureCode.Coin())
						return false
					}
					if instruction, err := arbitrage.NewInstruction(arbitrager, arbitrage.ArbiDirectionOpen, false, futureCode, num, percent, targetBasisRatio, configStr); err != nil {
						if strings.Contains(err.Error(), "qty not enough") {
							this.ErrorMsgf("套利机 %s 的品种 %s 下没有足够可用的 %s，跳过该套利机。%s", arbitrager.ID, futureCode, arbitrager.Config.USDXSymbol, err)
							continue
						}
						this.ErrorMsgf("创建指令出现错误：%s", err.Error())
						return false
					} else {
						this.instructions = append(this.instructions, instruction)
					}
				}
			}
			if len(this.instructions) > 0 {
				this.SendMsgf("\n请确认以下套利指令：\n```%s```", arbitrage.RenderRequestForInstructions(this.instructions, true))
			} else {
				this.SendMsgf("\n没有满足条件的交易所 %s 和品种 %s 。", exchangeName, symbolCodeArg)
				return false
			}
		}
	}
	return true
}

type CloseArbitragerCommand struct {
	ArbitrageInstructionCommand
	Force bool
}

func NewCloseArbitragerCommand(manager *QuanterManager) *CloseArbitragerCommand {
	cmd := &CloseArbitragerCommand{
		ArbitrageInstructionCommand: ArbitrageInstructionCommand{
			Command: command.Command{
				Name:            "closeArbi",
				Alias:           []string{"ca"},
				Instruction:     "`.closeArbi ExchangeName FutureCode 100%% targetBasisRatio(可选) config1=value1,config2=value2(可选)` 平仓套利",
				RequiresConfirm: true,
				ArgMin:          3,
				ArgMax:          5,
				AuthcodePos:     -1,
			},
			manager: manager,
		},
		Force: false,
	}
	return cmd
}

func NewCloseForceArbitragerCommand(manager *QuanterManager) *CloseArbitragerCommand {
	cmd := &CloseArbitragerCommand{
		ArbitrageInstructionCommand: ArbitrageInstructionCommand{
			Command: command.Command{
				Name:            "closeArbiForce",
				Alias:           []string{"caf"},
				Instruction:     "`.closeArbiForce ExchangeName SymbolCode 100%% targetBasisRatio(可选) config1=value1,config2=value2(可选)` 闪电平仓套利",
				RequiresConfirm: true,
				ArgMin:          3,
				ArgMax:          5,
				AuthcodePos:     -1,
			},
			manager: manager,
		},
		Force: true,
	}
	return cmd
}

func (this *CloseArbitragerCommand) Prepare() bool {
	exchangeArg := this.Args[0]
	symbolCodeArg := this.Args[1]
	percentArg := this.Args[2]

	configStr := ""
	targetRatioStr := ""
	targetBasisRatio := 0.0

	if len(this.Args) == 5 {
		configStr = this.Args[4]
		targetRatioStr = this.Args[3]
	} else if len(this.Args) == 4 && strings.Contains(this.Args[3], "=") {
		configStr = this.Args[3]
	} else if len(this.Args) == 4 && !strings.Contains(this.Args[3], "=") {
		targetRatioStr = this.Args[3]
	}
	if targetRatioStr != "" {
		if r, _, err := utils.ParseFloatOrPercentage(targetRatioStr, false, true); err != nil {
			this.ErrorMsgf("参数错误：targetBasisRatio %s", err)
		} else {
			targetBasisRatio = r
		}
	}

	this.instructions = []arbitrage.Instructable{} // 几次不同的命令，可能将 instruction 添加到同一个 Command 实例中；每次 Prepare 都清理，以免串掉

	if exchangeName, futureCodes, err := this.manager.ParseExchangeFutureCode(exchangeArg, symbolCodeArg); err != nil {
		this.ErrorMsgf("交易所名称和期货代号解析错误：%s", err)
		return false
	} else {
		// 如果 FutureCode 是 wildcard ，需要再一次翻译成实际的 FutureCode
		if num, percent, err := arbitrage.ParseNumOrPercentage(percentArg, true, false); err != nil {
			this.ErrorMsgf("参数错误：%s", err.Error())
			return false
		} else {
			arbis := this.manager.FilterArbitragersByExchangeName(exchangeName)
			for _, arbitrager := range arbis {
				for _, futureCode := range futureCodes {
					if arbitrager.CheckRunningInstruction(futureCode.Coin()) {
						this.WarnMsgf("套利机 %s 的 %s 品种下有指令在运行。", arbitrager.ID, futureCode.Coin())
						if !this.Force {
							return false
						}
					}
					if instruction, err := arbitrage.NewInstruction(arbitrager, arbitrage.ArbiDirectionClose, this.Force, futureCode, num, percent, targetBasisRatio, configStr); err != nil {
						if strings.Contains(err.Error(), "qty not enough") {
							this.ErrorMsgf("套利机 %s 的品种 %s 下没有足够的可平仓位，跳过该套利机。%s", arbitrager.ID, futureCode, err)
							continue
						}
						this.ErrorMsgf("创建指令出现错误：%s", err.Error())
						return false
					} else {
						this.instructions = append(this.instructions, instruction)
					}
				}
			}
			if len(this.instructions) > 0 {
				this.SendMsgf("\n请确认以下套利指令：\n```%s```", arbitrage.RenderRequestForInstructions(this.instructions, true))
			} else {
				this.SendMsgf("\n没有满足条件的交易所 %s 和品种 %s 。", exchangeName, symbolCodeArg)
				return false
			}
		}
	}
	return true
}

type CloseAllArbitragerCommand struct {
	ArbitrageInstructionCommand
}

func NewCloseAllArbitragerCommand(manager *QuanterManager) *CloseAllArbitragerCommand {
	cmd := &CloseAllArbitragerCommand{
		ArbitrageInstructionCommand: ArbitrageInstructionCommand{
			Command: command.Command{
				Name:            "closeArbiAll",
				Alias:           []string{"caa"},
				Instruction:     "`.closeArbiAll config1=value1,config2=value2(可选)` 闪电平仓所有套利",
				RequiresConfirm: true,
				ArgMin:          0,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			manager: manager,
		},
	}
	return cmd
}

func (this *CloseAllArbitragerCommand) Prepare() bool {
	configStr := ""
	if len(this.Args) == 1 {
		configStr = this.Args[0]
	}
	targetBasisRatio := 0.0

	this.instructions = []arbitrage.Instructable{} // 几次不同的命令，可能将 instruction 添加到同一个 Command 实例中；每次 Prepare 都清理，以免串掉

	for _, arbitrager := range this.manager.ArbitragerControllers {
		futureCodes, err := arbitrager.GetAllSymbolCodes()
		if err != nil {
			this.ErrorMsgf("获取套利机 %s 的品种代码列表出错， error: %s", err)
			return false
		}
		for _, futureCode := range futureCodes {
			if arbitrager.CheckRunningInstruction(futureCode.Coin()) {
				this.WarnMsgf("套利机 %s 的 %s 品种下有指令在运行。", arbitrager.ID, futureCode.Coin())
			}
			if instruction, err := arbitrage.NewInstruction(arbitrager, arbitrage.ArbiDirectionClose, true, futureCode, 0, 1.0, targetBasisRatio, configStr); err != nil {
				if strings.Contains(err.Error(), "qty not enough") {
					this.ErrorMsgf("套利机 %s 的品种 %s 下没有足够的可平仓位，跳过该套利机。%s", arbitrager.ID, futureCode, err)
					continue
				}
				this.ErrorMsgf("创建指令出现错误：%s", err.Error())
				return false
			} else {
				this.instructions = append(this.instructions, instruction)
			}
		}
	}
	if len(this.instructions) > 0 {
		this.SendMsgf("\n请确认以下套利指令：\n```%s```", arbitrage.RenderRequestForInstructions(this.instructions, true))
	} else {
		this.SendMsgf("\n没有满足条件的交易所和品种。")
		return false
	}
	return true
}

type MoveArbitragerCommand struct {
	ArbitrageInstructionCommand
}

func NewMoveArbitragerCommand(manager *QuanterManager) *MoveArbitragerCommand {
	cmd := &MoveArbitragerCommand{
		ArbitrageInstructionCommand: ArbitrageInstructionCommand{
			Command: command.Command{
				Name:            "moveArbi",
				Instruction:     "`.moveArbi ExchangeName FromFutureCode ToFutureCode 100%% targetBasisRatio(可选) config1=value1,config2=value2(可选)` 转移套利品种",
				RequiresConfirm: true,
				ArgMin:          4,
				ArgMax:          6,
				AuthcodePos:     -1,
			},
			manager: manager,
		},
	}
	return cmd
}

func (this *MoveArbitragerCommand) Prepare() bool {
	configStr := ""
	targetRatioStr := ""
	targetBasisRatio := 0.0

	if len(this.Args) == 6 {
		configStr = this.Args[5]
		targetRatioStr = this.Args[4]
	} else if len(this.Args) == 5 && strings.Contains(this.Args[4], "=") {
		configStr = this.Args[4]
	} else if len(this.Args) == 5 && !strings.Contains(this.Args[4], "=") {
		targetRatioStr = this.Args[4]
	}
	if targetRatioStr != "" {
		if r, _, err := utils.ParseFloatOrPercentage(targetRatioStr, false, true); err != nil {
			this.ErrorMsgf("参数错误：targetBasisRatio %s", err)
		} else {
			targetBasisRatio = r
		}
	}

	this.instructions = []arbitrage.Instructable{} // 几次不同的命令，可能将 instruction 添加到同一个 Command 实例中；每次 Prepare 都清理，以免串掉

	exchangeArg := this.Args[0]
	fromSymbolArg := this.Args[1]
	toSymbolArg := this.Args[2]
	if exchangeName, fromSymbolCodes, err := this.manager.ParseExchangeFutureCode(exchangeArg, fromSymbolArg); err != nil {
		this.ErrorMsgf("交易所名称 %s 和期货代号 %s 解析错误：%s", exchangeArg, fromSymbolArg, err)
		return false
	} else {
		if _, toSymbolCodes, err := this.manager.ParseExchangeFutureCode(exchangeArg, toSymbolArg); err != nil {
			this.ErrorMsgf("交易所名称 %s 和期货代号 %s 解析错误：%s", exchangeArg, toSymbolArg, err)
			return false
		} else {
			if len(fromSymbolCodes) != 1 || len(toSymbolCodes) != 1 {
				this.ErrorMsgf("源品种或目标品种的数量不是 1，请不要使用通配符 * 。")
				return false
			}
			fromFutureCode := fromSymbolCodes[0]
			toFutureCode := toSymbolCodes[0]
			if num, percent, err := arbitrage.ParseNumOrPercentage(this.Args[3], false, false); err != nil {
				this.ErrorMsgf("参数错误：%s", err.Error())
				return false
			} else {
				if strings.EqualFold(toFutureCode.Code, fromFutureCode.Code) {
					this.ErrorMsgf("参数错误：源品种和目标品种不能一样。")
					return false
				}

				arbis := this.manager.FilterArbitragersByExchangeName(exchangeName)
				for _, arbitrager := range arbis {
					if instruction, err := arbitrage.NewMoveInstruction(arbitrager, false, fromFutureCode, toFutureCode, num, percent, targetBasisRatio, configStr); err != nil {
						if strings.Contains(err.Error(), "qty not enough") {
							this.ErrorMsgf("套利机 %s 的品种 %s 下没有足够的可平仓位，跳过该套利机。%s", arbitrager.ID, fromFutureCode, err)
							continue
						}
						this.ErrorMsgf("创建指令出现错误：%s", err.Error())
						return false
					} else {
						this.instructions = append(this.instructions, instruction)
					}
				}
				if len(this.instructions) > 0 {
					this.SendMsgf("\n请确认以下转移指令：\n```%s```", arbitrage.RenderRequestForInstructions(this.instructions, true))
				} else {
					this.SendMsgf("\n没有满足条件的交易所 %s 和品种 %s -> %s 。", exchangeName, fromFutureCode, toFutureCode)
					return false
				}
			}
		}
	}
	return true
}

type BookManagerCommand ManagerCommand

func NewBookManagerCommand(manager *QuanterManager) *BookManagerCommand {
	cmd := &BookManagerCommand{
		Command: command.Command{
			Name:            "orders",
			Alias:           []string{"o"},
			Instruction:     "`.orders FutureCode[可选]` 查看指令挂单情况",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *BookManagerCommand) Do() bool {
	futureCodeStr := ""
	if len(this.Args) == 1 {
		futureCodeStr = this.Args[0]
	}
	t := arbitrage.NewTable()
	t.SetHeader([]string{"ArbiID", "ExchangeName", "ID", "Future Symbol", "Direction", "Target Basis", "Qty", "Filled", "Filled %"})
	rowsFound := false
	if futureCodeStr == "" {
		isSingleSide := false
		for _, arbitrager := range this.manager.ArbitragerControllers {
			rows := arbitrager.GetInstructionRows(futureCodeStr)
			for _, row := range rows {
				row = append([]string{arbitrager.ID, arbitrager.Exchange.GetName()}, row...)
				t.AddRow(row)
				rowsFound = true
			}
			if arbitrager.CheckSingleSide() {
				isSingleSide = true
			}
		}
		if rowsFound {
			this.SendMsgf("所有合约的挂单：\n```%s```", t.Render())
			if isSingleSide {
				this.WarnMsgf("指令中有单边的情形，以上报告的可用数量可能有误。")
			}
		} else {
			this.SendMsgf("没有任何挂单。")
		}
		return true
	} else {
		isSingleSide := false
		for _, arbitrager := range this.manager.ArbitragerControllers {
			if futureCode, err := arbitrage.NewSymbolCode(arbitrager, futureCodeStr); err != nil {
				this.ErrorMsgf("合约代码解析错误。%s", err)
				return false
			} else {
				if spotFuturePairs, err := arbitrager.Exchange.TranslateSymbolCode(futureCode); err != nil {
					arbitrager.Errorf("translate symbol code %s error: %s", futureCode.Code, err)
				} else {
					if len(spotFuturePairs) != 1 {
						this.ErrorMsgf("合约代码翻译错误，%s 对应了 %d 个交易对。", futureCode, len(spotFuturePairs))
						return false
					}
					pair := spotFuturePairs[0]
					rows := arbitrager.GetInstructionRows(pair.Right.Symbol)
					for _, row := range rows {
						row = append([]string{arbitrager.ID, arbitrager.Exchange.GetName()}, row...)
						t.AddRow(row)
						rowsFound = true
					}
					if arbitrager.CheckSingleSide() {
						isSingleSide = true
					}
				}
			}
		}
		if rowsFound {
			this.SendMsgf("合约 %s 的挂单：\n```%s```", futureCodeStr, t.Render())
			if isSingleSide {
				this.WarnMsgf("指令中有单边的情形，以上报告的可用数量可能有误。")
			}
		} else {
			this.SendMsgf("合约 %s 没有挂单。")
		}
		return true
	}
}

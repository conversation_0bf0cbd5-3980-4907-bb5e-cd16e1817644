package manager

import (
	"bytes"
	"os"
	"path/filepath"
	"strings"

	"github.com/wizhodl/quanter/arbitrage"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"

	"github.com/spf13/viper"
	"github.com/stevedomin/termtable"
)

// 新增套利机，并不会自动启动，需要 .runArbitrager 手工启动
type AddArbitragerManagerCommand ManagerCommand

func NewAddArbitragerManagerCommand(manager *QuanterManager) *AddArbitragerManagerCommand {
	cmd := &AddArbitragerManagerCommand{
		Command: command.Command{
			Name:            "addArbitrager",
			Alias:           []string{"addArbi"},
			Instruction:     "`.addArbitrager arbiID` 新增套利机，请附带上传 arbiID.toml",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
			RequiresFile:    true,
		},
		manager: manager,
	}
	return cmd
}

func (this *AddArbitragerManagerCommand) Prepare() bool {
	arbiID := this.Args[0]
	if this.manager.checkArbitragerExist(arbiID) {
		this.ErrorMsgf("套利机 %s 已经运行。", arbiID)
		return false
	}
	this.SendMsgf("文件下载中，请稍等...")
	downloadURL := this.Args[len(this.Args)-1]
	fileData, err := this.GetFile(downloadURL)
	if err != nil {
		this.ErrorMsgf("文件下载失败：%s, url: %s", err, downloadURL)
		return false
	}
	// 检查配置文件是否合法
	var config *arbitrage.ArbitragerControllerConfig
	viper.SetConfigType("toml")
	viper.ReadConfig(bytes.NewBuffer(fileData))

	if err := viper.Unmarshal(&config); err != nil {
		this.ErrorMsgf("配置文件解析失败, %v", err)
		return false
	}
	if err := config.Validate(); err != nil {
		this.ErrorMsgf("配置文件验证错误: %s, %v", err, config)
		return false
	}
	this.SendMsgf("配置文件正确。")
	return true
}

func (this *AddArbitragerManagerCommand) Do() bool {
	arbiID := this.Args[0]
	if this.manager.checkArbitragerExist(arbiID) {
		this.ErrorMsgf("套利机 %s 已经存在。", arbiID)
		return false
	}
	downloadURL := this.Args[len(this.Args)-1]
	this.manager.addArbitrager(arbiID, downloadURL)
	return true
}

// 警告：删除套利机会删除对应的配置文件，小心使用该命令
type RemoveArbitragerManagerCommand ManagerCommand

func NewRemoveArbitragerManagerCommand(manager *QuanterManager) *RemoveArbitragerManagerCommand {
	cmd := &RemoveArbitragerManagerCommand{
		Command: command.Command{
			Name:            "removeArbitrager",
			Alias:           []string{"removeArbi"},
			Instruction:     "`.removeArbitrager arbiID` 删除套利机",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
			ConfirmPrompt:   "警告：该套利机的配置文件将会被删除！",
		},
		manager: manager,
	}
	return cmd
}

func (this *RemoveArbitragerManagerCommand) Prepare() bool {
	arbiID := this.Args[0]
	arbiExist := this.manager.checkArbitragerExist(arbiID)
	if !arbiExist {
		this.ErrorMsgf("套利机 %s 没有运行，不能删除。", arbiID)
		return false
	}
	return true
}

func (this *RemoveArbitragerManagerCommand) Do() bool {
	arbiID := this.Args[0]
	controller, _ := this.manager.getArbitragerController(arbiID)
	controller.DangerousDelete()
	this.manager.removeArbitragerController(controller.ID, true)
	this.SendMsgf("已删除套利机 %s", arbiID)
	return true
}

// 因为运行 ArbitragerController 需要 password 才能解密 APISecret，所以 .copyArbitrager 之后并不能直接自动启动
// 需要通过 .runArbitrager 命令单独手工启动
type CopyArbitragerManagerCommand ManagerCommand

func NewCopyArbitragerManagerCommand(manager *QuanterManager) *CopyArbitragerManagerCommand {
	cmd := &CopyArbitragerManagerCommand{
		Command: command.Command{
			Name:            "copyArbitrager",
			Alias:           []string{"copyArbi"},
			Instruction:     "`.copyArbitrager fromArbiID toArbiID` 复制运行中的套利机的配置文件",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *CopyArbitragerManagerCommand) Prepare() bool {
	fromArbitragerID := this.Args[0]
	toArbitragerID := this.Args[1]
	if this.manager.checkArbitragerExist(toArbitragerID) {
		this.ErrorMsgf("目标套利机已经存在。")
		return false
	}
	if !this.manager.checkArbitragerExist(fromArbitragerID) {
		this.ErrorMsgf("源套利机没有运行。")
		return false
	}
	return true
}

func (this *CopyArbitragerManagerCommand) Do() bool {
	fromArbitragerID := this.Args[0]
	toArbitragerID := this.Args[1]
	// 复制配置文件
	controller, _ := this.manager.getArbitragerController(fromArbitragerID)
	if err := controller.SaveConfigsTo(toArbitragerID); err == nil {
		if err := controller.SaveStorageTo(toArbitragerID); err == nil {
			this.manager.Config.Arbitragers = append(this.manager.Config.Arbitragers, toArbitragerID)
			this.manager.Config.save()
			this.manager.SendMsgf("套利机 [%s] -> [%s] 配置文件和本地文件拷贝成功，输入 .runArbitrager 启动。", fromArbitragerID, toArbitragerID)
			return true
		} else {
			this.manager.ErrorMsgf("拷贝套利机本地存储文件出错：%v", err)
			return false
		}
	} else {
		this.manager.ErrorMsgf("拷贝套利机的配置文件出错：%v", err)
		return false
	}
}

// 因为 ArbitragerController 中的 APISecret 是通过主密码加密，必须提供 password 才能正确解密
type RunArbitragerManagerCommand ManagerCommand

func NewRunArbitragerManagerCommand(manager *QuanterManager) *RunArbitragerManagerCommand {
	cmd := &RunArbitragerManagerCommand{
		Command: command.Command{
			Name:            "runArbitrager",
			Alias:           []string{"runArbi"},
			Instruction:     "`.runArbitrager arbiID authCode` 运行套利机",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
			Sensitive:       true,
		},
		manager: manager,
	}
	return cmd
}

func (this *RunArbitragerManagerCommand) Do() bool {
	arbiID := this.Args[0]
	authCode := this.Args[1]

	if this.manager.checkArbitragerExist(arbiID) {
		this.ErrorMsgf("套利机 [%s] 已经运行了。", arbiID)
		return false
	}

	this.manager.runArbitragers([]string{arbiID}, "", authCode)
	return true
}

type StopArbitragerManagerCommand ManagerCommand

func NewStopArbitragerManagerCommand(manager *QuanterManager) *StopArbitragerManagerCommand {
	cmd := &StopArbitragerManagerCommand{
		Command: command.Command{
			Name:            "stopArbitrager",
			Alias:           []string{"stopArbi"},
			Instruction:     "`.stopArbitrager arbiID authCode` 停止运行套利机",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		manager: manager,
	}
	return cmd
}

func (this *StopArbitragerManagerCommand) Do() bool {
	arbiID := this.Args[0]

	if !this.manager.checkArbitragerExist(arbiID) {
		this.ErrorMsgf("套利机 [%s] 没有运行。", arbiID)
		return false
	}

	this.manager.stopArbitragers([]string{arbiID}, true)
	this.SendMsgf("套利机 %s 停止运行成功。", arbiID)
	return true
}

type ListArbitragerManagerCommand ManagerCommand

func NewListArbitragerManagerCommand(manager *QuanterManager) *ListArbitragerManagerCommand {
	cmd := &ListArbitragerManagerCommand{
		Command: command.Command{
			Name:            "listArbitrager",
			Alias:           []string{"listArbi"},
			Instruction:     "`.listArbitrager` 列出套利机",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *ListArbitragerManagerCommand) Do() bool {
	var files []string

	err := filepath.Walk(this.manager.configPath, func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			return nil
		}
		if strings.HasSuffix(path, ".arbi.toml") {
			files = append(files, path)
		}
		return nil
	})
	if err != nil {
		zlog.Panicf("walk conifg files error: %v", err)
	}
	arbiIDs := []string{}
	for _, file := range files {
		_, filename := filepath.Split(file)
		parts := strings.Split(filename, ".arbi.toml")
		if len(parts) > 0 {
			arbiIDs = append(arbiIDs, parts[0])
		}
	}

	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	headers := []string{"ArbitragerID", "Running"}
	t.SetHeader(headers)
	for _, arbiID := range arbiIDs {

		row := []string{
			arbiID,
			"",
		}
		controller, _ := this.manager.getArbitragerController(arbiID)
		if controller != nil && controller.IsLaunched() {
			row[1] = "Yes"
		} else {
			row[1] = "No"
		}
		t.AddRow(row)
	}
	result := t.Render()
	this.SendMsgf("```%s```", result)
	return true
}

// 具体实现

func (this *QuanterManager) addArbitrager(arbiID, downloadURL string) {
	fileData, err := this.GetFile(downloadURL)
	if err != nil {
		this.ErrorMsgf("文件下载失败：%s", err)
	}
	// 检查配置文件是否合法
	var config *arbitrage.ArbitragerControllerConfig
	viper.SetConfigType("toml")
	viper.ReadConfig(bytes.NewBuffer(fileData))

	if err := viper.Unmarshal(&config); err != nil {
		this.ErrorMsgf("配置文件解析失败, %v", err)
	}
	if err := config.Validate(); err != nil {
		this.ErrorMsgf("配置文件验证错误: %s, %v", err, config)
	}

	if err := config.SaveTo(this.configPath, arbiID, false); err != nil {
		this.ErrorMsgf("写 [%s] 的配置文件出现错误： %v", arbiID, err)
	} else {
		this.SendMsgf("套利机 [%s] 添加成功，请运行 `.runArbitrager` 启动套利机。", arbiID)
	}
}

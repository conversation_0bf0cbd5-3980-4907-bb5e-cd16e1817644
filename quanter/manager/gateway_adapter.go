package manager

import (
	"errors"
	"fmt"
	"math"
	"os"
	"strings"

	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/gateway"
	"github.com/wizhodl/quanter/secrets"
	"go.uber.org/atomic"
)

// gateway 适配器，收到 slack 时将消息转发给 gateway；收到 gateway 的 SlackResponse 时，发送给 slack
type GatewayAdapter struct {
	manager         *QuanterManager
	Exchange        exchange.Exchange
	Type            string
	Name            string
	apiKey          string
	apiSecret       secrets.SecretString
	slackSubscribed *atomic.Bool
}

func NewGatewayAdapter(manager *QuanterManager, controller base.Controllable) (*GatewayAdapter, error) {
	if controller == nil {
		return nil, errors.New("controller is nil")
	}

	baseConfig := controller.GetBaseConfig()
	adapterName := baseConfig.GatewayAdapter
	exchangeName := baseConfig.ExchangeName
	controllerID := controller.GetID()
	host := baseConfig.Host
	apiKey := baseConfig.ApiKey

	// 如果没有密码，要先检查密码并设置
	if baseConfig.ApiSecret == "" {
		zlog.Errorf("start gateway adapter failed, empty api secret, adapterName: %s", adapterName)
		return nil, errors.New("empty api secret")
	}

	var apiSecret secrets.SecretString
	if secrets.HasPassword() {
		var err error
		apiSecret, err = secrets.Decrypt(baseConfig.ApiSecret)
		if err != nil {
			zlog.Errorf("start gateway adapter failed, decrypt api secret failed, adapterName: %s, error: %v", adapterName, err)
			return nil, err
		}
	} else {
		apiSecret = secrets.SecretString(baseConfig.ApiSecret)
	}

	if strings.EqualFold(exchangeName, exchange.CTP) || strings.EqualFold(exchangeName, exchange.MetaTrader) || strings.EqualFold(exchangeName, exchange.InteractiveBrokers) {
		// 如果 APISecret 为空，尝试从环境变量读取
		if apiSecret == "" {
			apiSecretEnvKey := fmt.Sprintf("QUANTER_DEBUG_%s_API_SECRET", controllerID)
			envAPISecret := os.Getenv(apiSecretEnvKey)
			if envAPISecret != "" {
				zlog.Infof("[%s] env $%s is set, use it instead of config.%s.ApiSecret.", adapterName, apiSecretEnvKey, adapterName)
			}
			if manager.Debug && envAPISecret != "" {
				apiSecret = secrets.SecretString(envAPISecret)
			}
		}

		if apiSecret == "" {
			panic(fmt.Sprintf("api secret is empty for %s", adapterName))
		}

		adapter := &GatewayAdapter{
			manager:         manager,
			Type:            exchangeName,
			Name:            adapterName,
			slackSubscribed: atomic.NewBool(false),
			apiKey:          apiKey,
			apiSecret:       apiSecret,
		}

		options := &exchange.Options{
			ControllerID:          manager.ID,
			SlackResponseCallback: adapter.SlackResponseCallback,
			ApiKey:                apiKey,
			ApiSecret:             apiSecret,
			Host:                  host,
			GatewayChannelName:    adapterName,
		}

		var err error
		if strings.EqualFold(exchangeName, exchange.CTP) {
			adapter.Exchange, err = gateway.NewCTP(options, adapterName)
			if err != nil {
				return nil, err
			}
		} else if strings.EqualFold(exchangeName, exchange.MetaTrader) {
			adapter.Exchange, err = gateway.NewMT(options, adapterName)
			if err != nil {
				return nil, err
			}
		} else if strings.EqualFold(exchangeName, exchange.InteractiveBrokers) {
			adapter.Exchange, err = gateway.NewIB(options, adapterName)
			if err != nil {
				return nil, err
			}
		}
		// ConnectWebsocket 会阻塞并且会自动重新连接
		go adapter.Exchange.ConnectWebsocket([]exchange.InstrumentType{}, adapter.ConnectedCallback)
		return adapter, nil
	}
	return nil, errors.New("unsupported exchange")
}

func (this *GatewayAdapter) TryLaunchWithDebug() {
}

// 从 gateway 收到 SlackResponse 的回调后，通过该回调向 slack 发送消息
func (this *GatewayAdapter) SlackResponseCallback(msg, fileTitle string, fileContent []byte, fileType string) {
	if this.manager.Messenger != nil {
		if len(fileContent) == 0 {
			this.manager.Messenger.SendMessage(this.Name, msg)
		} else {
			this.manager.Messenger.SendFileMessage(this.Name, fileTitle, string(fileContent), msg, fileType)
		}
	}
}

// gateway 的 websocket 连接成功建立后的回调函数
func (this *GatewayAdapter) ConnectedCallback(connected bool) {
	if connected {
		zlog.Infof("[%s] websocket connected", this.Name)
		key := fmt.Sprintf("%s.slack", this.Name)
		subscribePacket := exchange.NewSubscribePacket(this.apiKey, key)
		this.Exchange.SendWebsocketMessage(subscribePacket, func(reponse *exchange.Packet, er error) {
			if er != nil {
				this.slackSubscribed.Store(false)
				this.SendMsgf("和 %s Gateway 建立实时连接失败。", this.Exchange.GetName())
				zlog.Errorf("subscribe to gateway failed (%s), error: %s", key, er)
			} else {
				this.slackSubscribed.Store(true)
				this.SendMsgf("和 %s Gateway 建立实时连接成功。", this.Exchange.GetName())
				zlog.Infof("subscribe to gateway success (%s)", key)
			}
		})
	} else {
		zlog.Infof("[%s] websocket disconnected", this.Name)
		this.slackSubscribed.Store(false)
	}
}

func (this *GatewayAdapter) SendMsgf(msg string, args ...any) {
	msg = fmt.Sprintf(msg, args...)
	zlog.Infof("[%s] %s", this.Name, msg)
	this._sendMsg(msg)
}

func (this *GatewayAdapter) ErrorMsgf(msg string, args ...any) {
	msg = fmt.Sprintf(msg, args...)
	msg = fmt.Sprintf("`错误:` %s", msg)
	zlog.Warnf("[%s] %s", this.Name, msg)
	this._sendMsg(msg)
}

func (this *GatewayAdapter) WarnMsgf(msg string, args ...any) {
	msg = fmt.Sprintf(msg, args...)
	msg = fmt.Sprintf("`警告:` %s", msg)
	zlog.Warnf("[%s] %s", this.Name, msg)
	this._sendMsg(msg)
}

func (this *GatewayAdapter) AlertMsgf(msg string, args ...any) {
	msg = fmt.Sprintf(msg, args...)
	msg = fmt.Sprintf("`运行错误:` %s", msg)
	zlog.Errorf("[%s] %s", this.Name, msg)
	if strings.Contains(msg, "err[51030]") {
		// OKEx 结算中，不报警
		return
	}
	this._sendMsg(msg)
}

func (this *GatewayAdapter) _sendMsg(msg string) {
	limit := math.Min(float64(len(msg)), command.SLACK_MESSAGE_LIMIT)
	msg = msg[:int(limit)]
	if strings.Count(msg, "```") == 1 {
		msg += "...```"
	}
	this.manager.Messenger.SendRTMMessage(this.Name, msg)
}

// 收到用户的 slack 输入消息时，调用这个函数将消息内容转发到 gateway
func (this *GatewayAdapter) HandleSlackMessage(msg string, fileURL string, fileContent []byte, timestamp string) {
	if !this.slackSubscribed.Load() {
		this.ErrorMsgf("和 %s(%s) 的实时连接已断开，无法发送消息。", this.Name, this.Exchange.GetName())
		return
	}
	packet, err := exchange.NewSlackRequestPacket(this.apiSecret, this.apiKey, this.Name, msg, fileURL, fileContent, timestamp)
	if err != nil {
		this.ErrorMsgf("relay slack message failed, msg #%s, error: %s", timestamp, err)
		return
	}
	go this.Exchange.SendWebsocketMessage(packet, func(response *exchange.Packet, er error) {
		if er != nil {
			zlog.Errorf("relay slack message failed, msg #%s, packet: %s, error: %s", timestamp, packet.PacketHeader.String(), er)
			this.ErrorMsgf("relay slack message failed, msg #%s, packet: %s, error: %s", timestamp, packet.PacketHeader.String(), er)
		} else {
			zlog.Infof("relay slack message success, msg #%s, packet: %s", timestamp, packet.PacketHeader.String())
		}
	})
}

package manager

import (
	"bytes"
	"os"
	"path/filepath"
	"strings"

	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/trader"

	"github.com/spf13/viper"
	"github.com/stevedomin/termtable"
)

// 新增交易机，并不会自动启动，需要 .runTrader 手工启动
type AddTraderManagerCommand ManagerCommand

func NewAddTraderManagerCommand(manager *QuanterManager) *AddTraderManagerCommand {
	cmd := &AddTraderManagerCommand{
		Command: command.Command{
			Name:            "addTrader",
			Instruction:     "`.addTrader traderID` 新增交易机，请附带上传 traderID.toml",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
			RequiresFile:    true,
		},
		manager: manager,
	}
	return cmd
}

func (this *AddTraderManagerCommand) Prepare() bool {
	traderID := this.Args[0]
	if this.manager.checkTraderControllerExist(traderID) {
		this.ErrorMsgf("交易机 %s 已经运行。", traderID)
		return false
	}
	this.SendMsgf("文件下载中，请稍等...")
	downloadURL := this.Args[len(this.Args)-1]
	fileData, err := this.GetFile(downloadURL)
	if err != nil {
		this.ErrorMsgf("文件下载失败：%s, url: %s", err, downloadURL)
		return false
	}
	// 检查配置文件是否合法
	var config *trader.TraderControllerConfig
	viper.SetConfigType("toml")
	viper.ReadConfig(bytes.NewBuffer(fileData))

	if err := viper.Unmarshal(&config); err != nil {
		this.ErrorMsgf("配置文件解析失败, %v", err)
		return false
	}
	if err := config.Validate(); err != nil {
		this.ErrorMsgf("配置文件验证错误: %s, %v", err, config)
		return false
	}
	this.SendMsgf("配置文件正确。")
	return true
}

func (this *AddTraderManagerCommand) Do() bool {
	controllerID := this.Args[0]
	if this.manager.checkTraderControllerExist(controllerID) {
		this.ErrorMsgf("交易机 %s 已经存在。", controllerID)
		return false
	}
	downloadURL := this.Args[len(this.Args)-1]
	this.manager.addTrader(controllerID, downloadURL)
	return true
}

// 警告：删除交易机会删除对应的配置文件，小心使用该命令
type RemoveTraderManagerCommand ManagerCommand

func NewRemoveTraderManagerCommand(manager *QuanterManager) *RemoveTraderManagerCommand {
	cmd := &RemoveTraderManagerCommand{
		Command: command.Command{
			Name:            "removeTrader",
			Instruction:     "`.removeTrader traderID` 删除交易机",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
			ConfirmPrompt:   "警告：该交易机的配置文件将会被删除！",
		},
		manager: manager,
	}
	return cmd
}

func (this *RemoveTraderManagerCommand) Prepare() bool {
	controllerID := this.Args[0]
	controllerExist := this.manager.checkTraderControllerExist(controllerID)
	if !controllerExist {
		this.ErrorMsgf("交易机 %s 没有运行，不能删除。", controllerID)
		return false
	}
	return true
}

func (this *RemoveTraderManagerCommand) Do() bool {
	controllerID := this.Args[0]
	controller, _ := this.manager.getTraderController(controllerID)
	controller.DangerousDelete()
	this.manager.removeTraderController(controller.ID, true)
	this.SendMsgf("已删除交易机 %s", controllerID)
	return true
}

// 因为运行 TraderController 需要 password 才能解密 APISecret，所以 .copyTrader 之后并不能直接自动启动
// 需要通过 .runTrader 命令单独手工启动
type CopyTraderManagerCommand ManagerCommand

func NewCopyTraderManagerCommand(manager *QuanterManager) *CopyTraderManagerCommand {
	cmd := &CopyTraderManagerCommand{
		Command: command.Command{
			Name:            "copyTrader",
			Instruction:     "`.copyTrader fromTraderID toTraderID` 复制运行中的交易机的配置文件",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *CopyTraderManagerCommand) Prepare() bool {
	fromControllerID := this.Args[0]
	toControllerID := this.Args[1]
	if this.manager.checkTraderControllerExist(toControllerID) {
		this.ErrorMsgf("目标交易机已经存在。")
		return false
	}
	if !this.manager.checkTraderControllerExist(fromControllerID) {
		this.ErrorMsgf("源交易机没有运行。")
		return false
	}
	return true
}

func (this *CopyTraderManagerCommand) Do() bool {
	fromControllerID := this.Args[0]
	toControllerID := this.Args[1]
	// 复制配置文件
	controller, _ := this.manager.getTraderController(fromControllerID)
	if err := controller.SaveConfigsTo(toControllerID); err == nil {
		if err := controller.SaveStorageTo(toControllerID); err == nil {
			this.manager.Config.Traders = append(this.manager.Config.Traders, toControllerID)
			this.manager.Config.save()
			this.manager.SendMsgf("交易机 [%s] -> [%s] 配置文件和本地文件拷贝成功，输入 .runTrader 启动。", fromControllerID, toControllerID)
			return true
		} else {
			this.manager.ErrorMsgf("拷贝交易机本地存储文件出错：%v", err)
			return false
		}
	} else {
		this.manager.ErrorMsgf("拷贝交易机的配置文件出错：%v", err)
		return false
	}
}

// 因为 TraderController 中的 APISecret 是通过主密码加密，必须提供 password 才能正确解密
type RunTraderManagerCommand ManagerCommand

func NewRunTraderManagerCommand(manager *QuanterManager) *RunTraderManagerCommand {
	cmd := &RunTraderManagerCommand{
		Command: command.Command{
			Name:            "runTrader",
			Alias:           []string{"runTrader"},
			Instruction:     "`.runTrader traderID authCode` 运行交易机",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
			Sensitive:       true,
		},
		manager: manager,
	}
	return cmd
}

func (this *RunTraderManagerCommand) Do() bool {
	controllerID := this.Args[0]
	authCode := this.Args[1]

	if this.manager.checkTraderControllerExist(controllerID) {
		this.ErrorMsgf("交易机 [%s] 已经运行了。", controllerID)
		return false
	}

	// dont need password, manager already has password set
	this.manager.runTraders([]string{controllerID}, "", authCode)
	return true
}

type StopTraderManagerCommand ManagerCommand

func NewStopTraderManagerCommand(manager *QuanterManager) *StopTraderManagerCommand {
	cmd := &StopTraderManagerCommand{
		Command: command.Command{
			Name:            "stopTrader",
			Alias:           []string{"stopTrader"},
			Instruction:     "`.stopTrader traderID authCode` 停止运行交易机",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		manager: manager,
	}
	return cmd
}

func (this *StopTraderManagerCommand) Do() bool {
	controllerID := this.Args[0]

	if !this.manager.checkTraderControllerExist(controllerID) {
		this.ErrorMsgf("交易机 [%s] 没有运行。", controllerID)
		return false
	}

	this.manager.stopTraders([]string{controllerID}, true)
	this.SendMsgf("交易机 %s 停止运行成功。", controllerID)
	return true
}

type ListTraderManagerCommand ManagerCommand

func NewListTraderManagerCommand(manager *QuanterManager) *ListTraderManagerCommand {
	cmd := &ListTraderManagerCommand{
		Command: command.Command{
			Name:            "listTrader",
			Alias:           []string{"listTrader"},
			Instruction:     "`.listTrader` 列出交易机",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *ListTraderManagerCommand) Do() bool {
	var files []string

	err := filepath.Walk(this.manager.configPath, func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			return nil
		}
		if strings.HasSuffix(path, ".trader.toml") {
			files = append(files, path)
		}
		return nil
	})
	if err != nil {
		zlog.Panicf("walk conifg files error: %v", err)
	}
	controllerIDs := []string{}
	for _, file := range files {
		_, filename := filepath.Split(file)
		parts := strings.Split(filename, ".trader.toml")
		if len(parts) > 0 {
			controllerIDs = append(controllerIDs, parts[0])
		}
	}

	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	headers := []string{"TraderID", "Running"}
	t.SetHeader(headers)
	for _, controllerID := range controllerIDs {

		row := []string{
			controllerID,
			"",
		}
		controller, _ := this.manager.getTraderController(controllerID)
		if controller != nil && controller.IsLaunched() {
			row[1] = "Yes"
		} else {
			row[1] = "No"
		}
		t.AddRow(row)
	}
	result := t.Render()
	this.SendMsgf("```%s```", result)
	return true
}

// 具体实现

func (this *QuanterManager) addTrader(controllerID, downloadURL string) {
	fileData, err := this.GetFile(downloadURL)
	if err != nil {
		this.ErrorMsgf("文件下载失败：%s", err)
	}
	// 检查配置文件是否合法
	var config *trader.TraderControllerConfig
	viper.SetConfigType("toml")
	viper.ReadConfig(bytes.NewBuffer(fileData))

	if err := viper.Unmarshal(&config); err != nil {
		this.ErrorMsgf("配置文件解析失败, %v", err)
	}
	if err := config.Validate(); err != nil {
		this.ErrorMsgf("配置文件验证错误: %s, %v", err, config)
	}

	if err := config.SaveTo(this.configPath, controllerID, false); err != nil {
		this.ErrorMsgf("写 [%s] 的配置文件出现错误： %v", controllerID, err)
	} else {
		this.SendMsgf("交易机 [%s] 添加成功，请运行 `.runTrader` 启动交易机。", controllerID)
	}
}

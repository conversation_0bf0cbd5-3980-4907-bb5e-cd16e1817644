package manager

import (
	"fmt"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/wizhodl/quanter/arbitrage"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/grid"
	"github.com/wizhodl/quanter/marketmaker"
	"github.com/wizhodl/quanter/messenger"
	"github.com/wizhodl/quanter/orderctrl"
	"github.com/wizhodl/quanter/pgatectrl"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/trader"
	"github.com/wizhodl/quanter/turtle"
	"github.com/wizhodl/quanter/utils"

	"go.uber.org/zap"
)

const SAFE_MODE_ALL_FEATURES = "trender/arbitrager/grider/gateway/pgate/order/trader/maker"

func (this *QuanterManager) SetLaunched(launched bool) {
	this.BaseResponder.Launched.Store(launched)
}

type QuanterManager struct {
	command.BaseResponder
	ID                     string
	TurtleControllers      []*turtle.TurtleController
	ArbitragerControllers  []*arbitrage.ArbitragerController
	GridControllers        []*grid.GridController
	TraderControllers      []*trader.TraderController
	MarketMakerControllers []*marketmaker.MarketMakerController
	// GatewayAdapters        []*GatewayAdapter
	safeMode               string // 记录当前的安全模式，主要用于 controller 中判断是否要启动 gateway，但是也可以用于其他用途
	ProxyManagerController *pgatectrl.ProxyManagerController
	OrderController        *orderctrl.OrderController
	Config                 *QuanterManagerConfig

	configPath           string
	releaseBinaryDirPath string
	logDirPath           string
	isDebuging           bool

	Messenger        *messenger.SlackMessenger // slack 机器人客户端
	commandProcessor *command.CommandProcessor // 命令处理器
}

// TODO: 检查 configPath releaseBinaryDirPath 等的 config 同步问题
// 有可能出现 manager, controller 上的值和 config 上的值对不上的问题
func NewQuanterManager(ID string, debug bool, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath string) *QuanterManager {
	// 自己初始化 messenger 客户端
	robotToken, cleanerToken, err := utils.GetSlackRobotToken(debug)
	if err != nil {
		zlog.Panicf("[%s] get slack robot token error: %s", ID, err.Error())
	}
	messenger := messenger.NewSlackMessenger(robotToken, secrets.GetSlackCommander(), cleanerToken)
	// messenger.TestCleaner(ID)

	manager := &QuanterManager{
		BaseResponder:         command.NewBaseResponder(debug, commitHash, buildTime, ID, messenger, ""),
		ID:                    ID,
		configPath:            configPath,
		releaseBinaryDirPath:  releaseBinaryDirPath,
		logDirPath:            logDirPath,
		TurtleControllers:     []*turtle.TurtleController{},
		ArbitragerControllers: []*arbitrage.ArbitragerController{},
		GridControllers:       []*grid.GridController{},
	}
	config, err := NewQuanterManagerConfig(manager)
	if err != nil {
		zlog.Errorf("load manager config failed, error: %s", err)
	}
	manager.Config = config
	manager.SetDebug(debug)

	manager.Messenger = messenger
	manager.Messenger.ConnectedCallback = manager.MessengerConnectedCallback
	go manager.Messenger.RTMConnect() // slack 实时连接

	manager.commandProcessor = command.NewCommandProcessor(manager,
		[]command.Commander{
			NewStatusManagerCommand(manager),
			NewLaunchManagerCommand(manager),
			NewTurtleHistoryManagerCommand(manager),
			NewGridHistoryManagerCommand(manager),
			NewAddTurtleManagerCommand(manager),
			NewCopyTurtleManagerCommand(manager),
			NewRemoveTurtleManagerCommand(manager),
			NewRunTurtleManagerCommand(manager),
			NewStopTurtleManagerCommand(manager),
			NewListTurtleManagerCommand(manager),
			NewAddArbitragerManagerCommand(manager),
			NewCopyArbitragerManagerCommand(manager),
			NewRemoveArbitragerManagerCommand(manager),
			NewRunArbitragerManagerCommand(manager),
			NewStopArbitragerManagerCommand(manager),
			NewListArbitragerManagerCommand(manager),
			NewAddGriderManagerCommand(manager),
			NewCopyGriderManagerCommand(manager),
			NewRemoveGriderManagerCommand(manager),
			NewRunGriderManagerCommand(manager),
			NewStopGriderManagerCommand(manager),
			NewListGriderManagerCommand(manager),
			cmds.NewPagerCommand(),
			NewAddTraderManagerCommand(manager),
			NewCopyTraderManagerCommand(manager),
			NewRemoveTraderManagerCommand(manager),
			NewRunTraderManagerCommand(manager),
			NewStopTraderManagerCommand(manager),
			NewListTraderManagerCommand(manager),
			NewAddMarketMakerManagerCommand(manager),
			NewCopyMarketMakerManagerCommand(manager),
			NewRemoveMarketMakerManagerCommand(manager),
			NewRunMarketMakerManagerCommand(manager),
			NewStopMarketMakerManagerCommand(manager),
			NewListMarketMakerManagerCommand(manager),
			NewPriceWatchCommand(manager),
			NewSetAssetsControllersManagerCommand(manager),
			NewReviewManagerCommand(manager),
			cmds.NewPagerCommand(),
			NewArbiArbitragerCommand(manager),
			NewCloseArbitragerCommand(manager),
			NewCloseForceArbitragerCommand(manager),
			NewCloseAllArbitragerCommand(manager),
			NewMoveArbitragerCommand(manager),
			NewPositionManagerCommand(manager),
			NewBookManagerCommand(manager),
			NewExitManagerCommand(manager),
			NewAssetsManagerCommand(manager),
			cmds.NewMuteCommand(),
			cmds.NewDebugCommand(),
			cmds.NewLogCommand(manager.logDirPath, manager.ID),
			cmds.NewDownloadLogCommand(manager.logDirPath, manager.ID),
			cmds.NewStackTraceCommand(),
			cmds.NewDeleteErrorsCommand(),
		})

	// 初始化时检查 ReleaseBinaryDir 是否可以写，以免后续 releases 相关命令每次都要检查
	if utils.CheckReleaseBinaryDirPath(releaseBinaryDirPath) {
		manager.commandProcessor.AddCommands([]command.Commander{
			cmds.NewReleaseCommand(releaseBinaryDirPath, manager.ID),
			cmds.NewListVersionCommand(releaseBinaryDirPath, manager.ID),
			cmds.NewUseVersionCommand(releaseBinaryDirPath, manager.ID),
		})
	} else {
		zlog.Panicf("[%s] release binary dir permission denied: %s", ID, releaseBinaryDirPath)
	}

	// 初始化命令处理器
	zlog.Infof("[%s] register command handler", manager.ID)
	manager.Messenger.RegisterCommandHandler(manager.ID, manager.CommandHandler, false)

	// 设置敏感命令
	manager.Messenger.SetSensitiveCommands(manager.commandProcessor.SensitiveCommands)

	manager.TryLaunchWithDebug()

	return manager
}

// 发送 check alive refresh 请求，每 60 秒发送一次
// 作为心跳，如果程序挂掉，没有成功 launch 都不会发送心跳
// 监控系统检查 https://checkalive.changebegin.com/userID/appID/ 获取程序状态，404 表示程序挂掉，200 表示服务正常
// 因为可能有多个最终用户使用同一个 apiSecret 写入状态，为了保证唯一性，建议 prd 环境使用随机字符串 userID 作为标识，防止其他人误写入
func (this *QuanterManager) RefreshCheckAlive() {
	// refresh check alive heartbeat every 60 seconds
	interval := 60 * time.Second
	if this.Debug {
		interval = 10 * time.Second
	}
	ticker := time.NewTicker(interval)
	for range ticker.C {
		this._refreshCheckAlive()
	}
}

func (this *QuanterManager) _refreshCheckAlive() {
	// curl -H "X-API-Key: " https://checkalive.changebegin.com/userID/appID/refresh
	apiSecret := this.Config.CheckAliveAPISecret
	apiURL := this.Config.CheckAliveAPIURL
	userID := this.Config.CheckAliveUserID

	if apiURL == "" {
		apiURL = "https://checkalive.changebegin.com"
	}

	if userID == "" {
		userID = "default"
	}

	if apiSecret == "" {
		zlog.Errorf("[%s] check alive api secret is empty", this.ID)
		return
	}

	apiURL = fmt.Sprintf("%s/%s/%s/refresh", apiURL, userID, this.ID)

	// use resty to request
	client := resty.New()
	resp, err := client.R().SetHeader("X-API-Key", apiSecret).Post(apiURL)
	if err != nil {
		zlog.Errorf("[%s] check alive refresh request failed, error: %s", this.ID, err)
		return
	}

	if resp.StatusCode() != 200 {
		zlog.Errorf("[%s] check alive refresh request failed, status code: %d", this.ID, resp.StatusCode())
		return
	}

	zlog.Infof("[%s] check alive refresh success", this.ID)
}

func (this *QuanterManager) CommandHandler(command string, args ...string) {
	this.commandProcessor.Process(command, args)
}

func (this *QuanterManager) MessengerConnectedCallback() {
	this.Messenger.SetDefaultChannelName(this.ID)
	this.AskForLaunch()
}

func (this *QuanterManager) AskForLaunch() {
	if !this.isDebuging {
		msg := fmt.Sprintf("Build: %s/(%s), 请输入命令 `.launch Password GoogleAuthCode safe=trender/arbitrager/grider/gateway/pgate/trader/maker[可选](安全模式)` 启动程序", this.CommitHash, this.BuildTime)
		this.SendMsgf(msg)
	}
}

func (this *QuanterManager) checkTurtleControllerExist(turtleID string) bool {
	controller, _ := this.getTurtleController(turtleID)
	return controller != nil
}

func (this *QuanterManager) checkGridControllerExist(griderID string) bool {
	controller, _ := this.getGridController(griderID)
	return controller != nil
}

func (this *QuanterManager) checkTraderControllerExist(traderID string) bool {
	controller, _ := this.getTraderController(traderID)
	return controller != nil
}

func (this *QuanterManager) checkMarketMakerControllerExist(marketMakerID string) bool {
	controller, _ := this.getMarketMakerController(marketMakerID)
	return controller != nil
}

func (this *QuanterManager) getTurtleController(turtleID string) (*turtle.TurtleController, int) {
	for i, controller := range this.TurtleControllers {
		if controller.ID == turtleID {
			return controller, i
		}
	}
	return nil, -1
}

func (this *QuanterManager) getGridController(griderID string) (*grid.GridController, int) {
	for i, controller := range this.GridControllers {
		if controller.ID == griderID {
			return controller, i
		}
	}
	return nil, -1
}

func (this *QuanterManager) getTraderController(traderID string) (*trader.TraderController, int) {
	for i, controller := range this.TraderControllers {
		if controller.ID == traderID {
			return controller, i
		}
	}
	return nil, -1
}

func (this *QuanterManager) getMarketMakerController(marketMakerID string) (*marketmaker.MarketMakerController, int) {
	for i, controller := range this.MarketMakerControllers {
		if controller.ID == marketMakerID {
			return controller, i
		}
	}
	return nil, -1
}

func (this *QuanterManager) checkArbitragerExist(arbiID string) bool {
	controller, _ := this.getArbitragerController(arbiID)
	return controller != nil
}

func (this *QuanterManager) getArbitragerController(arbiID string) (*arbitrage.ArbitragerController, int) {
	for i, controller := range this.ArbitragerControllers {
		if strings.EqualFold(controller.ID, arbiID) {
			return controller, i
		}
	}
	return nil, -1
}

func (this *QuanterManager) removeArbitragerController(arbiID string, save bool) {
	index := -1
	for i, controller := range this.ArbitragerControllers {
		if strings.EqualFold(controller.ID, arbiID) {
			index = i
			if controller.Exchange != nil {
				controller.Exchange.Remove()
			}
		}
	}
	if index >= 0 {
		this.ArbitragerControllers = append(this.ArbitragerControllers[:index], this.ArbitragerControllers[index+1:]...)
	}
	if save {
		this.Config.removeArbitrager(arbiID)
	}
}

func (this *QuanterManager) removeGridController(controllerID string, save bool) {
	index := -1
	for i, controller := range this.GridControllers {
		if strings.EqualFold(controller.ID, controllerID) {
			index = i
			if controller.Exchange != nil {
				controller.Exchange.Remove()
			}
		}
	}
	if index >= 0 {
		this.GridControllers = append(this.GridControllers[:index], this.GridControllers[index+1:]...)
	}
	if save {
		this.Config.removeGrider(controllerID)
	}
}

func (this *QuanterManager) removeTraderController(controllerID string, save bool) {
	index := -1
	for i, controller := range this.TraderControllers {
		if strings.EqualFold(controller.ID, controllerID) {
			index = i
			if controller.Exchange != nil {
				controller.Exchange.Remove()
			}
		}
	}
	if index >= 0 {
		this.TraderControllers = append(this.TraderControllers[:index], this.TraderControllers[index+1:]...)
	}
	if save {
		this.Config.removeTrader(controllerID)
	}
}

func (this *QuanterManager) removeMarketMakerController(marketMakerID string, save bool) {
	index := -1
	for i, controller := range this.MarketMakerControllers {
		if controller.ID == marketMakerID {
			index = i
			if controller.Exchange != nil {
				controller.Exchange.Remove()
			}
		}
	}
	if index >= 0 {
		this.MarketMakerControllers = append(this.MarketMakerControllers[:index], this.MarketMakerControllers[index+1:]...)
	}
	if save {
		this.Config.removeMarketMaker(marketMakerID)
	}
}

func (this *QuanterManager) removeTurtleController(turtleID string, save bool) {
	index := -1
	for i, controller := range this.TurtleControllers {
		if controller.ID == turtleID {
			index = i
			if controller.Exchange != nil {
				controller.Exchange.Remove()
			}
		}
	}
	if index >= 0 {
		this.TurtleControllers = append(this.TurtleControllers[:index], this.TurtleControllers[index+1:]...)
	}
	if save {
		this.Config.removeTurtle(turtleID)
	}
}

func (this *QuanterManager) SetDebug(debug bool) {
	this.BaseResponder.Debug = debug
	var zlogger *zap.SugaredLogger
	if debug {
		zlogger = zlog.NewRotateLogger("DEBUG", this.BaseResponder.LogPath, nil)
	} else {
		zlogger = zlog.NewRotateLogger("INFO", this.BaseResponder.LogPath, nil)
	}
	zlog.SetLogger(zlogger)
}

func (this *QuanterManager) ParseExchangeFutureCode(exchangeNameStr, futureCodeStr string) (exchangeName string, futureCodes []*exchange.SymbolCode, er error) {
	futureCodes = []*exchange.SymbolCode{}
	if !arbitrage.CheckValidExchangeName(exchangeNameStr) {
		er = fmt.Errorf("invalid exchange name: %s", exchangeNameStr)
	} else {
		exchangeName = exchangeNameStr
		arbis := this.FilterArbitragersByExchangeName(exchangeName)
		for _, arbi := range arbis {
			if futureCodeStr == "*" {
				codes, err := arbi.GetAllSymbolCodes()
				if err != nil {
					er = err
				}
				futureCodes = append(futureCodes, codes...)
			} else {
				if !arbi.CheckValidSymbolCode(futureCodeStr) {
					er = fmt.Errorf("invalid future code: %s", futureCodeStr)
				} else {
					if fc, err := arbitrage.NewSymbolCode(arbi, futureCodeStr); err != nil {
						er = err
					} else {
						futureCodes = append(futureCodes, fc)
					}
				}
			}
		}
	}
	return exchangeName, futureCodes, er
}

func (this *QuanterManager) FilterArbitragersByExchangeName(exchangeName string) []*arbitrage.ArbitragerController {
	arbis := []*arbitrage.ArbitragerController{}
	for _, arbi := range this.ArbitragerControllers {
		if exchangeName == "*" || arbi.Config.ExchangeName == exchangeName {
			arbis = append(arbis, arbi)
		}
	}
	return arbis
}

// 尝试以 Debug 模式启动，如果不能以 Debug 模式启动，会要求以 .launch 模式启动
// TODO: 尝试以 debug 模式启动 manager，通过该函数可以以 debug 模式运行 arbitrager
func (this *QuanterManager) TryLaunchWithDebug() {
	// 用于 Debug 时无需 .launch 即可启动，必须设置 ApiSecret 和 AUTH_SECRET 为未加密的值
	// 如果没有提供 ApiSecret 和 AUTH_SECRET，还是通过 .launch 命令启动

	if this.Debug {
		this.isDebuging = true
		this.SetLaunched(true)
		this.runTurtles(this.Config.Turtles, "", "")
		this.runArbitragers(this.Config.Arbitragers, "", "")
		this.runGriders(this.Config.Griders, "", "")
		this.runTraders(this.Config.Traders, "", "")
		this.runMarketMakers(this.Config.MarketMakers, "", "")
		this.runProxyManager("", "")
		this.runOrderController("", "")
		this.runGatewayAdapters("", "")
		go this.setupProxy()
		go this.RefreshCheckAlive()
	} else {
		this.commandProcessor.AddCommands([]command.Commander{NewLaunchManagerCommand(this)})
	}
}

func (this *QuanterManager) Exit() {
	for _, ctrl := range this.TurtleControllers {
		ctrl.Exit()
	}

	for _, ctrl := range this.ArbitragerControllers {
		ctrl.Exit()
	}

	for _, ctrl := range this.GridControllers {
		ctrl.Exit()
	}

	for _, ctrl := range this.TraderControllers {
		ctrl.Exit()
	}

	for _, ctrl := range this.MarketMakerControllers {
		ctrl.Exit()
	}

	if this.ProxyManagerController != nil {
		this.ProxyManagerController.Exit()
	}
}

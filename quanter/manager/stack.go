package manager

import (
	"net/http"
	"strings"

	"github.com/wizhodl/quanter/common/zlog"

	"github.com/wizhodl/quanter/common/stack"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func (this *QuanterManager) queryStackHandler(c *gin.Context) {
	key := c.Query("key")
	count, st := stack.QueryStack(key)
	if len(st) > 0 {
		c.JSON(http.StatusOK, gin.H{"count": count, "stack": string(st)})
		return
	}

	stacks := stack.ListStacksCount()
	c.JSON(http.StatusOK, stacks)
}

func (this *QuanterManager) setLogHandler(c *gin.Context) {
	key := c.Query("debug")
	var zlogger *zap.SugaredLogger
	if strings.ToUpper(key) == "TRUE" || strings.ToUpper(key) == "T" || strings.ToUpper(key) == "1" {
		zlogger = zlog.NewRotateLogger("DEBUG", this.BaseResponder.LogPath, nil)
	} else if strings.ToUpper(key) == "False" || strings.ToUpper(key) == "F" || strings.ToUpper(key) == "0" {
		zlogger = zlog.NewRotateLogger("INFO", this.BaseResponder.LogPath, nil)
	}
	zlog.SetLogger(zlogger)
	c.JSON(http.StatusOK, map[string]string{})
}

func (this *QuanterManager) RegisterRoutes(r *gin.Engine) {
	r.GET("/stack/query", this.queryStackHandler)
	r.GET("/log/level", this.setLogHandler)
}

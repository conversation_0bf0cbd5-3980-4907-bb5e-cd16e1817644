package manager

import (
	"bytes"
	"os"
	"path/filepath"
	"strings"

	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/grid"

	"github.com/spf13/viper"
	"github.com/stevedomin/termtable"
)

// 新增网格机，并不会自动启动，需要 .runGrider 手工启动
type AddGriderManagerCommand ManagerCommand

func NewAddGriderManagerCommand(manager *QuanterManager) *AddGriderManagerCommand {
	cmd := &AddGriderManagerCommand{
		Command: command.Command{
			Name:            "addGrider",
			Alias:           []string{"addGrid"},
			Instruction:     "`.addGrider griderID` 新增网格机，请附带上传 griderID.toml",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
			RequiresFile:    true,
		},
		manager: manager,
	}
	return cmd
}

func (this *AddGriderManagerCommand) Prepare() bool {
	griderID := this.Args[0]
	if this.manager.checkGridControllerExist(griderID) {
		this.ErrorMsgf("网格机 %s 已经运行。", griderID)
		return false
	}
	this.SendMsgf("文件下载中，请稍等...")
	downloadURL := this.Args[len(this.Args)-1]
	fileData, err := this.GetFile(downloadURL)
	if err != nil {
		this.ErrorMsgf("文件下载失败：%s, url: %s", err, downloadURL)
		return false
	}
	// 检查配置文件是否合法
	var config *grid.GridControllerConfig
	viper.SetConfigType("toml")
	viper.ReadConfig(bytes.NewBuffer(fileData))

	if err := viper.Unmarshal(&config); err != nil {
		this.ErrorMsgf("配置文件解析失败, %v", err)
		return false
	}
	if err := config.Validate(); err != nil {
		this.ErrorMsgf("配置文件验证错误: %s, %v", err, config)
		return false
	}
	this.SendMsgf("配置文件正确。")
	return true
}

func (this *AddGriderManagerCommand) Do() bool {
	controllerID := this.Args[0]
	if this.manager.checkGridControllerExist(controllerID) {
		this.ErrorMsgf("网格机 %s 已经存在。", controllerID)
		return false
	}
	downloadURL := this.Args[len(this.Args)-1]
	this.manager.addGrider(controllerID, downloadURL)
	return true
}

// 警告：删除网格机会删除对应的配置文件，小心使用该命令
type RemoveGriderManagerCommand ManagerCommand

func NewRemoveGriderManagerCommand(manager *QuanterManager) *RemoveGriderManagerCommand {
	cmd := &RemoveGriderManagerCommand{
		Command: command.Command{
			Name:            "removeGrider",
			Alias:           []string{"removeGrid"},
			Instruction:     "`.removeGrider griderID` 删除网格机",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
			ConfirmPrompt:   "警告：该网格机的配置文件将会被删除！",
		},
		manager: manager,
	}
	return cmd
}

func (this *RemoveGriderManagerCommand) Prepare() bool {
	controllerID := this.Args[0]
	controllerExist := this.manager.checkGridControllerExist(controllerID)
	if !controllerExist {
		this.ErrorMsgf("网格机 %s 没有运行，不能删除。", controllerID)
		return false
	}
	return true
}

func (this *RemoveGriderManagerCommand) Do() bool {
	controllerID := this.Args[0]
	controller, _ := this.manager.getGridController(controllerID)
	controller.DangerousDelete()
	this.manager.removeGridController(controller.ID, true)
	this.SendMsgf("已删除网格机 %s", controllerID)
	return true
}

// 因为运行 GridController 需要 password 才能解密 APISecret，所以 .copyGrider 之后并不能直接自动启动
// 需要通过 .runGrider 命令单独手工启动
type CopyGriderManagerCommand ManagerCommand

func NewCopyGriderManagerCommand(manager *QuanterManager) *CopyGriderManagerCommand {
	cmd := &CopyGriderManagerCommand{
		Command: command.Command{
			Name:            "copyGrider",
			Alias:           []string{"copyGrid"},
			Instruction:     "`.copyGrider fromGriderID toGriderID` 复制运行中的网格机的配置文件",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *CopyGriderManagerCommand) Prepare() bool {
	fromControllerID := this.Args[0]
	toControllerID := this.Args[1]
	if this.manager.checkGridControllerExist(toControllerID) {
		this.ErrorMsgf("目标网格机已经存在。")
		return false
	}
	if !this.manager.checkGridControllerExist(fromControllerID) {
		this.ErrorMsgf("源网格机没有运行。")
		return false
	}
	return true
}

func (this *CopyGriderManagerCommand) Do() bool {
	fromControllerID := this.Args[0]
	toControllerID := this.Args[1]
	// 复制配置文件
	controller, _ := this.manager.getGridController(fromControllerID)
	if err := controller.SaveConfigsTo(toControllerID); err == nil {
		if err := controller.SaveStorageTo(toControllerID); err == nil {
			this.manager.Config.Griders = append(this.manager.Config.Griders, toControllerID)
			this.manager.Config.save()
			this.manager.SendMsgf("网格机 [%s] -> [%s] 配置文件和本地文件拷贝成功，输入 .runGrider 启动。", fromControllerID, toControllerID)
			return true
		} else {
			this.manager.ErrorMsgf("拷贝网格机本地存储文件出错：%v", err)
			return false
		}
	} else {
		this.manager.ErrorMsgf("拷贝网格机的配置文件出错：%v", err)
		return false
	}
}

// 因为 GridController 中的 APISecret 是通过主密码加密，必须提供 password 才能正确解密
type RunGriderManagerCommand ManagerCommand

func NewRunGriderManagerCommand(manager *QuanterManager) *RunGriderManagerCommand {
	cmd := &RunGriderManagerCommand{
		Command: command.Command{
			Name:            "runGrider",
			Alias:           []string{"runGrid"},
			Instruction:     "`.runGrider griderID authCode` 运行网格机",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
			Sensitive:       true,
		},
		manager: manager,
	}
	return cmd
}

func (this *RunGriderManagerCommand) Do() bool {
	controllerID := this.Args[0]
	authCode := this.Args[1]

	if this.manager.checkGridControllerExist(controllerID) {
		this.ErrorMsgf("网格机 [%s] 已经运行了。", controllerID)
		return false
	}

	// dont need password, manager already has password set
	this.manager.runGriders([]string{controllerID}, "", authCode)
	return true
}

type StopGriderManagerCommand ManagerCommand

func NewStopGriderManagerCommand(manager *QuanterManager) *StopGriderManagerCommand {
	cmd := &StopGriderManagerCommand{
		Command: command.Command{
			Name:            "stopGrider",
			Alias:           []string{"stopGrid"},
			Instruction:     "`.stopGrider griderID authCode` 停止运行网格机",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		manager: manager,
	}
	return cmd
}

func (this *StopGriderManagerCommand) Do() bool {
	controllerID := this.Args[0]

	if !this.manager.checkGridControllerExist(controllerID) {
		this.ErrorMsgf("网格机 [%s] 没有运行。", controllerID)
		return false
	}

	this.manager.stopGriders([]string{controllerID}, true)
	this.SendMsgf("网格机 %s 停止运行成功。", controllerID)
	return true
}

type ListGriderManagerCommand ManagerCommand

func NewListGriderManagerCommand(manager *QuanterManager) *ListGriderManagerCommand {
	cmd := &ListGriderManagerCommand{
		Command: command.Command{
			Name:            "listGrider",
			Alias:           []string{"listGrid"},
			Instruction:     "`.listGrid` 列出网格机",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *ListGriderManagerCommand) Do() bool {
	var files []string

	err := filepath.Walk(this.manager.configPath, func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			return nil
		}
		if strings.HasSuffix(path, ".grid.toml") {
			files = append(files, path)
		}
		return nil
	})
	if err != nil {
		zlog.Panicf("walk conifg files error: %v", err)
	}
	controllerIDs := []string{}
	for _, file := range files {
		_, filename := filepath.Split(file)
		parts := strings.Split(filename, ".grid.toml")
		if len(parts) > 0 {
			controllerIDs = append(controllerIDs, parts[0])
		}
	}

	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	headers := []string{"GriderID", "Running"}
	t.SetHeader(headers)
	for _, controllerID := range controllerIDs {

		row := []string{
			controllerID,
			"",
		}
		controller, _ := this.manager.getGridController(controllerID)
		if controller != nil && controller.IsLaunched() {
			row[1] = "Yes"
		} else {
			row[1] = "No"
		}
		t.AddRow(row)
	}
	result := t.Render()
	this.SendMsgf("```%s```", result)
	return true
}

// 具体实现

func (this *QuanterManager) addGrider(controllerID, downloadURL string) {
	fileData, err := this.GetFile(downloadURL)
	if err != nil {
		this.ErrorMsgf("文件下载失败：%s", err)
	}
	// 检查配置文件是否合法
	var config *grid.GridControllerConfig
	viper.SetConfigType("toml")
	viper.ReadConfig(bytes.NewBuffer(fileData))

	if err := viper.Unmarshal(&config); err != nil {
		this.ErrorMsgf("配置文件解析失败, %v", err)
	}
	if err := config.Validate(); err != nil {
		this.ErrorMsgf("配置文件验证错误: %s, %v", err, config)
	}

	if err := config.SaveTo(this.configPath, controllerID, false); err != nil {
		this.ErrorMsgf("写 [%s] 的配置文件出现错误： %v", controllerID, err)
	} else {
		this.SendMsgf("网格机 [%s] 添加成功，请运行 `.runGrider` 启动网格机。", controllerID)
	}
}

type GridHistoryManagerCommand ManagerCommand

func NewGridHistoryManagerCommand(manager *QuanterManager) *GridHistoryManagerCommand {
	cmd := &GridHistoryManagerCommand{
		Command: command.Command{
			Name:            "griderHistory",
			Alias:           []string{"ghis"},
			Instruction:     "`.griderHistory(+) SymbolCode1,SymbolCode2[可选]` 打印网格机历史记录",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		manager: manager,
	}
	return cmd
}

func (this *GridHistoryManagerCommand) Do() bool {
	symbolCodes := ""
	if len(this.Args) == 1 {
		symbolCodes = this.Args[0]
	}

	hisMsg := "[No Histories]"
	t := grid.NewTable()
	headers := append([]string{"Grider ID", "Exchange Name"}, grid.SimpleStatusHeader...)
	t.SetHeader(headers)

	for _, controller := range this.manager.GridControllers {
		for _, row := range controller.GetHistoryRows(symbolCodes) {
			row = append([]string{controller.ID, controller.Exchange.GetName()}, row...)
			t.AddRow(row)
		}
	}
	if len(t.Rows) > 1 {
		hisMsg = t.Render()
	}
	this.SendMsgf("```%s```", hisMsg)

	return true
}

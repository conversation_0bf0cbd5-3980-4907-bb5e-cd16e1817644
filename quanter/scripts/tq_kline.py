#!python

import os
import sys
import time
from tqsdk import Tq<PERSON><PERSON>, TqAuth, TqAccount

import requests

def upload_klines(gateway_host, api, symbolArgs):
    errors = []
    symbolArgs = symbolArgs.split(",")
    for symbolArg in symbolArgs:
        symbol, period, limit = symbolArg.split(":")
        exchange_id, instrument_id = symbol.split(".")
        tq_period = 0
        if period == "1day":
            tq_period = 86400
        elif period == "1hour":
            tq_period = 3600

        klines = api.get_kline_serial(symbol, tq_period, data_length=limit)
        # 'datetime', 'id', 'open', 'high', 'low', 'close', 'volume', 'open_oi', 'close_oi', 'symbol', 'duration'
        out_klines = []
        for _, k0 in klines.iterrows():
            k = {"exchange": exchange_id, "symbol": instrument_id, "period": period, "time": 0, "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.0, "volume": 0.0}
            k["time"] = int(k0["datetime"] / 1e9)
            k["open"] = k0["open"]
            k["high"] = k0["high"]
            k["low"] = k0["low"]
            k["close"] = k0["close"]
            k["volume"] = k0["volume"]
            if k["time"] > 0:
                out_klines.append(k)

        resp = requests.post(f"http://{gateway_host}/v1/klines/update", json= out_klines)    
        if resp.status_code != 200:
            errors.append("%s upload failed"%symbolArg)
        else:
            print("%s upload success"%symbolArg)
        time.sleep(1)

    api.close()    
    if len(errors) > 0:
        err_str = "\n".join(errors)
        print("update klines failed", err_str)
        sys.exit(1)
    else:
        sys.exit(0)

if __name__ == "__main__":
    if len(sys.argv) != 5 and len(sys.argv) != 6:
        print("tq_klines.py gateway_host account password symbol1:period1:limit1,symbol2:period2:limit simnow_account::password")
        sys.exit(1)

    gateway_host = sys.argv[1]
    account = sys.argv[2]
    password = sys.argv[3]
    symbols = sys.argv[4]
    
    simnow_account = ""
    if len(sys.argv) == 6:
        simnow_account = sys.argv[5]
    
    if simnow_account:
        simnow_investor_id, simnow_password = simnow_account.split("::")
        api = TqApi(TqAccount("simnow", simnow_investor_id, simnow_password), auth=TqAuth(account, password))
    else:
        api = TqApi(auth=TqAuth(account, password))
    upload_klines(gateway_host, api, symbols)
    

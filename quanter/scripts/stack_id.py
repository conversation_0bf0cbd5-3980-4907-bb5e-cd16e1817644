import requests
import sys

key = sys.argv[1]

session = requests.Session()
session.trust_env = False
resp = session.get(f"http://localhost:7070/stack/query?key={key}")

if resp.ok:
    result = resp.json()
    if 'stack' in result:
        print(result['stack'])
    else:
        pairs = sorted(result.items(), key=lambda x: x[1])
        for p in pairs:
            print(f"{p[0]}:  {p[1]}")
else:
    print(resp.status_code)
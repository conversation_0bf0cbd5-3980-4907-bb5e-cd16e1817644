import json
import argparse


def load_json(file_path, escape=False):
    with open(file_path, 'r', encoding='utf-8') as file:
        if escape:
            data = file.read()
        else:
            data = json.load(file)
    return data


def main():
    parser = argparse.ArgumentParser(description='Convert JSON file to a single line JSON string.')
    parser.add_argument('json_file', type=str, help='Path to the JSON file')
    parser.add_argument('escape', type=bool, nargs='?', default=False, help='Whether to escape the JSON string')

    args = parser.parse_args()

    json_data = load_json(args.json_file, args.escape)
    single_line_json = json.dumps(json_data)

    print(single_line_json)


if __name__ == '__main__':
    main()

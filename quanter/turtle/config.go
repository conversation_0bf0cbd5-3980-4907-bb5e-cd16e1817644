package turtle

import (
	"errors"
	"fmt"
	"os"
	"path"
	"strings"

	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"

	"github.com/spf13/viper"
)

// 单个品种配置
type SymbolConfig struct {
	turtle                            *Turtle
	Symbol                            string  // 冗余字段，方便校验
	ATRPeriod                         int     // ATR 计算周期，如 20 表示使用最近已完成的 20 根 K 线计算ATR
	ATRMultiplier                     float64 // ATR 乘数，未配置则为 1
	BreakoutPeriod                    int     // 突破价周期，如 40 表示使用最近 40 根 K 线计算突破价
	SecondaryBreakoutPeriod           int     // 第二突破价周期，用于止盈退出后的突破价周期，可选
	PeriodHour                        int     // K线周期，如 6 表示使用 6 小时K线作为一个交易周期
	ExitPeriod                        int     // 退出价周期，如 20 表示使用最近 20 根 K 线计算退出价
	MaxOpenUnits                      int     // 最大开仓单位，1~4
	OpenSizePercent                   float64 // 单位头寸余额百分比
	MarginChangeMinPercent            float64 // 余额变动最小百分比（变动后影响为未仓头寸）
	OpenSlippageRate                  float64 // 开仓委托价相对于触发价的滑点，如不设置则为绝对值 0.5*atr
	TakeProfitSlippageRate            float64 // 止盈单委托价相对于触发价的滑点
	CloseSlippageRate                 float64 // 止损退出委托价相对于触发价的滑点
	BreakoutSlippageRate              float64 // 突破价滑点率
	ExitSlippageRate                  float64 // 退出价滑点率
	WaitingSecondsAfterTriggeredLong  int     // 看多订单被触发等待时间，超时未成交的直接吃单
	WaitingSecondsAfterTriggeredShort int     // 看空订单被触发等待时间，超时未成交的直接吃单
	LiquidationPriceAlertRatio        float64 // 调整爆仓价报警阈值百分比
	RotateOpenPositionSlippageRate    float64 // 展期开仓时滑点
	RotateOpenPositionRetryTimes      int     // 展期开仓时下单尝试次数
	RotateOpenPositionWaitSeconds     int     // 展期开仓时下单后等待时间
	UseMarginBalance                  bool    // 使用保证金余额开仓，否则使用钱包余额
	ExternalBalance                   float64 // 外部资金
	UseStopMarket                     bool    // 使用触发市价单挂单
	FeeRate                           float64 // 仅 simu 用到
	SleepSecondsBeforeHandle          int     // 流程处理前随机休眠时间范围，默认不休眠
	DisableShort                      bool    // 禁止做空
}

// 海龟配置格式
type TurtleControllerConfig struct {
	baseconfig.BaseConfig
	SymbolConfig
	controller *TurtleController
	Symbols    []string
}

var TurtleSymbolConfigOptions = &baseconfig.ConfigOptions{
	"OpenSizePercent":                   {IsPercent: true},
	"MarginChangeMinPercent":            {IsPercent: true},
	"OpenSlippageRate":                  {IsPercent: true},
	"TakeProfitSlippageRate":            {IsPercent: true},
	"CloseSlippageRate":                 {IsPercent: true},
	"BreakoutSlippageRate":              {IsPercent: true},
	"ExitSlippageRate":                  {IsPercent: true},
	"LiquidationPriceAlertRatio":        {IsPercent: true},
	"RotateOpenPositionSlippageRate":    {IsPercent: true},
	"ExternalBalance":                   {IsPercent: false},
	"ATRMultiplier":                     {IsPercent: false},
	"WaitingSecondsAfterTriggeredLong":  {Unit: "s"},
	"WaitingSecondsAfterTriggeredShort": {Unit: "s"},
	"RotateOpenPositionWaitSeconds":     {Unit: "s"},
	"PeriodHour":                        {Unit: "h"},
	"FeeRate":                           {IsPercent: true},
	"SleepSecondsBeforeHandle":          {Unit: "s"},
}

func NewTurtleControllerConfig(controller *TurtleController) (*TurtleControllerConfig, error) {
	if config, err := LoadConfig(controller.ConfigPath, controller.ID); err != nil {
		return nil, err
	} else {
		config.controller = controller
		return config, nil
	}
}

func LoadConfig(configPath, controllerID string) (config *TurtleControllerConfig, er error) {
	config = &TurtleControllerConfig{}
	if _, err := os.Stat(configPath); !os.IsNotExist(err) {
		viper.SetConfigName(controllerID + ".turtle")
		viper.AddConfigPath(configPath)
		err := viper.ReadInConfig()
		if err == nil {
			err := viper.Unmarshal(config)
			if err != nil {
				zlog.Errorf("unable to decode controller config into struct, %v", err)
				return nil, err
			}
			if err := config.Validate(); err != nil {
				zlog.Errorf("config validate error: %s, %v", err, config)
				return nil, err
			}
			zlog.Infof("[%s] load config from local file", controllerID)
			return config, nil
		} else { // 如果配置文件存在，但是解析出现错误，报错退出
			zlog.Errorf("配置文件解析出现错误：%s", err)
			return nil, fmt.Errorf("read config error: %v", err)
		}
	} else {
		return nil, errors.New("config path not found")
	}
}

func (this *TurtleControllerConfig) SnapshotSymbolConfig() *SymbolConfig {
	return &SymbolConfig{
		turtle:                            this.turtle,
		Symbol:                            this.Symbol,
		ATRPeriod:                         this.ATRPeriod,
		ATRMultiplier:                     this.ATRMultiplier,
		BreakoutPeriod:                    this.BreakoutPeriod,
		ExitPeriod:                        this.ExitPeriod,
		SecondaryBreakoutPeriod:           this.SecondaryBreakoutPeriod,
		PeriodHour:                        this.PeriodHour,
		MaxOpenUnits:                      this.MaxOpenUnits,
		OpenSizePercent:                   this.OpenSizePercent,
		MarginChangeMinPercent:            this.MarginChangeMinPercent,
		OpenSlippageRate:                  this.OpenSlippageRate,
		TakeProfitSlippageRate:            this.TakeProfitSlippageRate,
		CloseSlippageRate:                 this.CloseSlippageRate,
		BreakoutSlippageRate:              this.BreakoutSlippageRate,
		ExitSlippageRate:                  this.ExitSlippageRate,
		WaitingSecondsAfterTriggeredLong:  this.WaitingSecondsAfterTriggeredLong,
		WaitingSecondsAfterTriggeredShort: this.WaitingSecondsAfterTriggeredShort,
		LiquidationPriceAlertRatio:        this.LiquidationPriceAlertRatio,
		RotateOpenPositionSlippageRate:    this.RotateOpenPositionSlippageRate,
		RotateOpenPositionRetryTimes:      this.RotateOpenPositionRetryTimes,
		RotateOpenPositionWaitSeconds:     this.RotateOpenPositionWaitSeconds,
		UseMarginBalance:                  this.UseMarginBalance,
		ExternalBalance:                   this.ExternalBalance,
		UseStopMarket:                     this.UseStopMarket,
		FeeRate:                           this.FeeRate,
		SleepSecondsBeforeHandle:          this.SleepSecondsBeforeHandle,
		DisableShort:                      this.DisableShort,
	}
}

func (this *TurtleControllerConfig) ToTomlContent(hideSecret bool) string {
	return fmt.Sprintf(`Symbols = [%v]`, utils.SliceStringJoin(this.Symbols, ", ", true)) + "\n" +
		"\n[BaseConfig]\n" +
		this.BaseConfig.ToTomlContent(hideSecret) +
		"\n[SymbolConfig]\n" +
		this.SymbolConfig.ToTomlContent()
}

func (this *TurtleControllerConfig) ToTable() string {
	apiKey := this.ApiKey
	apiSecret := this.ApiSecret
	fixerKey := this.FixerKey
	apiKey = utils.HideSecret(apiKey)
	apiSecret = utils.HideSecret(apiSecret)
	fixerKey = utils.HideSecret(fixerKey)

	t := NewTable()
	t.SetHeader([]string{"Config", "Value"})

	debugStr := "false"
	if this.controller != nil && this.controller.Debug {
		debugStr = "true"
	}
	t.AddRow([]string{"Debug*", debugStr})
	t.AddRow([]string{"", ""})
	if !this.IsDemo && this.Host != "" {
		t.AddRow([]string{"Host", this.Host})
	}
	t.AddRow([]string{"ExchangeName", this.ExchangeName})
	if this.IsDemo && this.ExchangeName == "CTP" {
		t.AddRow([]string{"InvestorID", "1******2"})
		t.AddRow([]string{"Password", "N******k"})
		t.AddRow([]string{"AuthCode", "0******0"})
	} else {
		t.AddRow([]string{"ApiKey", fmt.Sprintf(`%v`, apiKey)})
		t.AddRow([]string{"ApiSecret", fmt.Sprintf(`%v`, apiSecret)})

		if exp, _ := this.controller.GetAPIExpireTime(); exp != nil {
			t.AddRow([]string{"ApiKeyExpire", utils.FormatShortTimeStr(exp, true)})
		}

		t.AddRow([]string{"FixerKey", fmt.Sprintf(`%v`, fixerKey)})
	}

	t.AddRow([]string{"ReleaseBinaryDirPath", fmt.Sprintf(`%v`, this.ReleaseBinaryDirPath)})
	t.AddRow([]string{"LogDirPath", fmt.Sprintf(`%v`, this.LogDirPath)})
	t.AddRow([]string{"IsTestnet", fmt.Sprintf(`%v`, this.IsTestnet)})
	t.AddRow([]string{"UseMarginBalance", fmt.Sprintf(`%v`, this.UseMarginBalance)})
	t.AddRow([]string{"MinMarginRatio", fmt.Sprintf(`%v`, this.MinMarginRatio)})
	t.AddRow([]string{"EnableRealtimePrice", fmt.Sprintf(`%v`, this.EnableRealtimePrice)})
	t.AddRow([]string{"ProxyUrl", this.ProxyUrl})
	t.AddRow([]string{"USDXSymbol", this.USDXSymbol})
	t.AddRow([]string{"-------------------------", ""})

	for _, row := range this.GetSymbolConfigRows() {
		t.AddRow(row)
	}
	return t.Render()
}

func (this *TurtleControllerConfig) GetSymbolConfigRows() (rows [][]string) {
	rows = this.SnapshotSymbolConfig().GetRows()
	return
}

func (this *TurtleControllerConfig) SaveTo(configPath string, turtleID string, overwrite bool) error {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, turtleID+".turtle.toml")
	if _, err := os.Stat(path); !os.IsNotExist(err) && !overwrite {
		return fmt.Errorf("%s already exist, can not overwrite", path)
	}
	if err := os.WriteFile(path, []byte(this.ToTomlContent(false)), 0755); err != nil {
		zlog.Errorf("write controller config %s error: %v", turtleID, err)
		return err
	}
	return nil
}

func (this *TurtleControllerConfig) Save() {
	this.SaveTo(this.controller.ConfigPath, this.controller.ID, true)
}

func (this *TurtleControllerConfig) Delete() {
	// 删除本地配置
	if err := os.Remove(path.Join(this.controller.ConfigPath, this.controller.ID+".turtle.toml")); err != nil {
		this.turtle.AlertMsgf("本地配置文件删除失败: %s", err)
		return
	}
}

func (this *TurtleControllerConfig) setConfigByString(configStr string) error {
	if configPairs, _, err := baseconfig.SetConfigWithString(this, configStr); err != nil {
		zlog.Errorf("turtle controller set config %s, error: %s", configStr, err)
		return err
	} else {

		for _, pair := range configPairs {
			if strings.EqualFold(pair.Field, "EnableRealtimePrice") {
				enable := strings.EqualFold(pair.Value, "true")
				this.controller.Exchange.SetEnableRealtimePrice(enable)
			}
		}

		this.Save()
	}

	// 更新 turtles config 的值
	for _, tt := range this.controller.Turtles {
		_, _, err := tt.Config.setConfigByString(configStr)
		if err != nil {
			return err
		}
		tt.Config.Save()
	}
	return nil
}

func (this *TurtleControllerConfig) Validate() error {
	if err := this.BaseConfig.Validate(); err != nil {
		return err
	}

	return this.SymbolConfig.Validate()
}

func (this *TurtleControllerConfig) GetConfigOptions() *baseconfig.ConfigOptions {
	return TurtleSymbolConfigOptions
}

func (this *TurtleControllerConfig) GetChildValidatableConfig() baseconfig.ValidatableConfig {
	return &this.SymbolConfig
}

func (this *TurtleControllerConfig) GetBaseConfig() *baseconfig.BaseConfig {
	return &this.BaseConfig
}

func (this *TurtleControllerConfig) GetExchangeName() string {
	return this.ExchangeName
}

func (this *TurtleControllerConfig) GetHost() string {
	return this.Host
}

func (this *TurtleControllerConfig) GetApiKey() string {
	return this.ApiKey
}

func (this *TurtleControllerConfig) GetApiSecret() string {
	return this.ApiSecret
}

// SymbolConfig

func (this *SymbolConfig) GetRows() (rows [][]string) {
	if configRows, err := baseconfig.GetTableRows(this); err != nil {
		rows = append(rows, []string{"ERROR", fmt.Sprintf("[ERROR!>base config table error: %s]", err)})
		return
	} else {
		rows = append(rows, configRows...)
	}
	return
}

func (this *SymbolConfig) Validate() error {
	if this.OpenSizePercent > 0.02 {
		return errors.New("config validate error, OpenSizePercent 不得大于 0.02")
	}

	if this.TakeProfitSlippageRate < this.CloseSlippageRate {
		return errors.New("config validate error, TakeProfitSlippageRate 需大于或等于 CloseSlippageRate")
	}

	if this.MaxOpenUnits < 0 || this.MaxOpenUnits > 4 {
		return errors.New("config validate error, MaxOpenUnits 有效值为 1~4 ")
	}

	if this.MaxOpenUnits == 0 {
		this.MaxOpenUnits = 4
	}

	if this.SecondaryBreakoutPeriod > 0 && this.SecondaryBreakoutPeriod < this.BreakoutPeriod {
		return errors.New("config validate error, SecondaryBreakoutPeriod 需大于 BreakoutPeriod")
	}

	return nil
}

func (this *SymbolConfig) GetConfigOptions() *baseconfig.ConfigOptions {
	return TurtleSymbolConfigOptions
}

func (this *SymbolConfig) GetChildValidatableConfig() baseconfig.ValidatableConfig {
	return nil
}

func (this *SymbolConfig) GetBaseConfig() *baseconfig.BaseConfig {
	return nil
}

func (this *SymbolConfig) ToTomlContent() string {
	return fmt.Sprintf(`Symbol = "%v"`, this.Symbol) + "\n" +
		fmt.Sprintf(`ATRPeriod = %v`, this.ATRPeriod) + "\n" +
		fmt.Sprintf(`ATRMultiplier = %v`, this.ATRMultiplier) + "\n" +
		fmt.Sprintf(`BreakoutPeriod = %v`, this.BreakoutPeriod) + "\n" +
		fmt.Sprintf(`BreakoutSlippageRate = %v`, this.BreakoutSlippageRate) + "\n" +
		fmt.Sprintf(`CloseSlippageRate = %v`, this.CloseSlippageRate) + "\n" +
		fmt.Sprintf(`ExitPeriod = %v`, this.ExitPeriod) + "\n" +
		fmt.Sprintf(`ExitSlippageRate = %v`, this.ExitSlippageRate) + "\n" +
		fmt.Sprintf(`MarginChangeMinPercent = %v`, this.MarginChangeMinPercent) + "\n" +
		fmt.Sprintf(`MaxOpenUnits = %v`, this.MaxOpenUnits) + "\n" +
		fmt.Sprintf(`OpenSizePercent = %v`, this.OpenSizePercent) + "\n" +
		fmt.Sprintf(`PeriodHour = %v`, this.PeriodHour) + "\n" +
		fmt.Sprintf(`SecondaryBreakoutPeriod = %v`, this.SecondaryBreakoutPeriod) + "\n" +
		fmt.Sprintf(`LiquidationPriceAlertRatio = %v`, this.LiquidationPriceAlertRatio) + "\n" +
		fmt.Sprintf(`OpenSlippageRate = %v`, this.OpenSlippageRate) + "\n" +
		fmt.Sprintf(`TakeProfitSlippageRate = %v`, this.TakeProfitSlippageRate) + "\n" +
		fmt.Sprintf(`WaitingSecondsAfterTriggeredLong = %v`, this.WaitingSecondsAfterTriggeredLong) + "\n" +
		fmt.Sprintf(`WaitingSecondsAfterTriggeredShort = %v`, this.WaitingSecondsAfterTriggeredShort) + "\n" +
		fmt.Sprintf(`RotateOpenPositionSlippageRate = %v`, this.RotateOpenPositionSlippageRate) + "\n" +
		fmt.Sprintf(`RotateOpenPositionRetryTimes = %v`, this.RotateOpenPositionRetryTimes) + "\n" +
		fmt.Sprintf(`RotateOpenPositionWaitSeconds = %v`, this.RotateOpenPositionWaitSeconds) + "\n" +
		fmt.Sprintf(`UseMarginBalance = %v`, this.UseMarginBalance) + "\n" +
		fmt.Sprintf(`ExternalBalance = %v`, this.ExternalBalance) + "\n" +
		fmt.Sprintf(`UseStopMarket = %v`, this.UseStopMarket) + "\n" +
		fmt.Sprintf(`FeeRate = %v`, this.FeeRate) + "\n" +
		fmt.Sprintf(`SleepSecondsBeforeHandle = %v`, this.SleepSecondsBeforeHandle) + "\n" +
		fmt.Sprintf(`DisableShort = %v`, this.DisableShort) + "\n"
}

func (this *SymbolConfig) Delete() {
	// 删除本地配置
	if err := os.Remove(path.Join(this.turtle.controller.ConfigPath, this.turtle.TurtleSymbolID()+".toml")); err != nil {
		this.turtle.AlertMsgf("本地配置文件删除失败: %s", err)
		return
	}
}

func NewSymbolConfig(turtle *Turtle) *SymbolConfig {
	var config SymbolConfig
	turtleID := turtle.TurtleSymbolID()

	// 首先从本地读取
	viper.SetConfigName(turtleID)
	viper.AddConfigPath(turtle.controller.ConfigPath)
	err := viper.ReadInConfig()
	if err == nil {
		err := viper.Unmarshal(&config)
		if err != nil {
			zlog.Panicf("unable to decode turtle config into struct, %v", err)
		}
		zlog.Infof("[%s] load %s config from local file", turtle.Symbol, turtleID)
		config.turtle = turtle
		return &config
	}

	// Firestore 也无配置，从 controller 拷贝
	zlog.Infof("[%s] load %s config from controller", turtle.Symbol, turtleID)
	config = turtle.controller.Config.SymbolConfig
	config.Symbol = turtle.Symbol
	config.turtle = turtle
	config.Save()
	return &config
}

func (this *SymbolConfig) SaveTo(configPath string, turtleSymbolID string, overwrite bool) error {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}

	path := path.Join(configPath, turtleSymbolID+".toml")
	if _, err := os.Stat(path); !os.IsNotExist(err) && !overwrite {
		errMsg := fmt.Sprintf("symbol config %s already exist, can't overwrite.", path)
		zlog.Errorf(errMsg)
		return errors.New(errMsg)
	}
	if err := os.WriteFile(path, []byte(this.ToTomlContent()), 0755); err != nil {
		zlog.Errorf("write symbol config for %s err: %v", turtleSymbolID, err)
		return err
	}
	return nil
}

func (this *SymbolConfig) Save() {
	this.SaveTo(this.turtle.controller.ConfigPath, this.turtle.TurtleSymbolID(), true)
}

func (this *SymbolConfig) setConfigByString(configStr string) (baseconfig.ConfigValueList, string, error) {
	hasPosition := this.turtle.Position.Qty != 0

	if parsedPairs, _, err := baseconfig.ParseConfigsFromString(this, configStr); err == nil {
		for _, p := range parsedPairs {
			if hasPosition && strings.Contains("ATRPeriod|ATRMultiplier|BreakoutPeriod|SecondaryBreakoutPeriod|PeriodHour|MaxOpenUnits|UseStopMarket", p.Field) {
				return baseconfig.ConfigValueList{}, "", fmt.Errorf("当前有仓位，无法设置 %s", p.Field)
			}
		}
	}

	if pairs, correctedConfigStr, err := baseconfig.SetConfigWithString(this, configStr); err != nil {
		zlog.Errorf("set config with string for %s, new config %s, error: %s", this.turtle.ID, configStr, err)
		return baseconfig.ConfigValueList{}, "", err
	} else {
		needUpdateOpenSize := false
		needUpdateKlineData := false
		for _, p := range pairs {
			if p.Field == "ATRPeriod" || p.Field == "ATRMultiplier" {
				this.turtle.Open.ATR.ExpiredAt = nil
				needUpdateKlineData = true
			}
			if strings.Contains("BreakoutPeriod|SecondaryBreakoutPeriod|PeriodHour", p.Field) {
				this.turtle.Long.BreakoutPrice.ExpiredAt = nil
				this.turtle.Short.BreakoutPrice.ExpiredAt = nil
				needUpdateKlineData = true
			}
			if strings.Contains("PeriodHour|ExitPeriod", p.Field) {
				this.turtle.Long.ExitPrice.ExpiredAt = nil
				this.turtle.Short.ExitPrice.ExpiredAt = nil
				needUpdateKlineData = true
			}
			if strings.Contains("OpenSizePercent|PeriodHour|ATRPeriod|ATRMultiplier|MaxOpenUnits|UseMarginBalance|ExternalBalance", p.Field) {
				needUpdateOpenSize = true
			}
			if strings.Contains("BreakoutPeriod", p.Field) {
				this.turtle.SendMsgf("可能需要使用 .setCurrentBreakoutPeriod 命令设置当前值。")
			}
			if p.Field == "UseStopMarket" {
				// 需重新挂不同类型单，取消另一种类型订单
				orderType := exchange.StopLimit
				if !this.turtle.Config.UseStopMarket {
					orderType = exchange.StopMarket
				}
				this.turtle.controller.Exchange.CancelAllOrders(this.turtle.InstrumentType, orderType, this.turtle.Symbol)
			}
			this.turtle.addSetHistory(fmt.Sprintf("Config.%s", p.Field), p.Value, fmt.Sprintf("IsChild: %v", p.IsChild))
		}
		this.Save()
		this.turtle.controller.storage.Save()

		if needUpdateKlineData {
			if success := this.turtle.HandleLatestKLineData(); success {
				this.turtle.updatePriceStorage(false, false)
				this.turtle.Open.ATR.set(this.turtle, this.turtle.lastATR, this.turtle.klineStatus.expireTime(), false, "Open.ATR", "")
			}
		}

		if !hasPosition && needUpdateOpenSize {
			this.turtle.Balance.External = this.ExternalBalance
			this.turtle.getBalance()
			this.turtle.updateOpenSize()
		}

		go this.turtle.handlePositionAndOrders()
		return pairs, correctedConfigStr, nil
	}
}

package turtle

import (
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

// 设置 ATR

type SetATRTurtleCommand TurtleCommand

func NewSetATRTurtleCommand(turtle *Turtle) *SetATRTurtleCommand {
	cmd := &SetATRTurtleCommand{
		Command: command.Command{
			Name:            "setATR",
			Instruction:     "`.setATR ATR Duration` 设置ATR e.g. .setATR 123.45 30m",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *SetATRTurtleCommand) Prepare() bool {
	if this.turtle.Position.Qty != 0 {
		this.turtle.ErrorMsgf("当前有持仓，无法修改")
		return false
	}

	value, _ := strconv.ParseFloat(this.Args[0], 64)
	duration := this.Args[1]

	// 价格相差过大说明可能输入错误
	if math.Abs(value-this.turtle.Open.ATR.Value) > (this.turtle.Open.ATR.Value*0.5) && !this.turtle.controller.Config.IsTestnet {
		this.turtle.ErrorMsgf("输入错误，数值偏差不能超过 50%%")
		return false
	}

	expireTime := this.turtle.getExpireTimeFromDuration(duration)
	if expireTime == "" {
		this.turtle.ErrorMsgf("请输入正确的有效时间")
		return false
	}
	return true
}

func (this *SetATRTurtleCommand) Do() bool {
	if this.turtle.Position.Qty != 0 {
		this.turtle.ErrorMsgf("当前有持仓，无法修改")
		return false
	}
	value, _ := strconv.ParseFloat(this.Args[0], 64)
	expireTime := this.turtle.getExpireTimeFromDuration(this.Args[1])
	this.turtle.manualUpdateATR(value, expireTime)
	return true
}

// 重置 ATR

type ResetATRTurtleCommand TurtleCommand

func NewResetATRTurtleCommand(turtle *Turtle) *ResetATRTurtleCommand {
	cmd := &ResetATRTurtleCommand{
		Command: command.Command{
			Name:            "resetATR",
			Instruction:     "`.resetATR GoogleAuthCode` 使用当前 K 线数据重置ATR",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *ResetATRTurtleCommand) Do() bool {
	if this.turtle.Position.Qty != 0 {
		this.turtle.ErrorMsgf("当前有持仓，无法修改")
		return false
	}

	this.turtle.resetATR(true)
	return true
}

// 设置做多突破价

type SetBreakoutPriceLongTurtleCommand TurtleCommand

func NewSetBreakoutPriceLongTurtleCommand(turtle *Turtle) *SetBreakoutPriceLongTurtleCommand {
	cmd := &SetBreakoutPriceLongTurtleCommand{
		Command: command.Command{
			Name:            "setBreakoutPriceLong",
			Instruction:     "`.setBreakoutPriceLong Price Duration` 设置做多突破价 e.g. .setBreakoutPriceLong 8765.5 1h",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *SetBreakoutPriceLongTurtleCommand) Prepare() bool {
	return this.turtle.checkCommandPriceDuration(this, this.Args[0], this.Args[1])
}

func (this *SetBreakoutPriceLongTurtleCommand) Do() bool {
	price, _ := strconv.ParseFloat(this.Args[0], 64)
	expireTime := this.turtle.getExpireTimeFromDuration(this.Args[1])
	this.turtle.manualUpdateBreakoutPrice(price, this.turtle.Short.BreakoutPrice.Value, expireTime)
	return true
}

// 设置做空突破价

type SetBreakoutPriceShortTurtleCommand TurtleCommand

func NewSetBreakoutPriceShortTurtleCommand(turtle *Turtle) *SetBreakoutPriceShortTurtleCommand {
	cmd := &SetBreakoutPriceShortTurtleCommand{
		Command: command.Command{
			Name:            "setBreakoutPriceShort",
			Instruction:     "`.setBreakoutPriceShort Price Duration` 设置做空突破价 e.g. .setBreakoutPriceShort 8765.5 1h",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *SetBreakoutPriceShortTurtleCommand) Prepare() bool {
	return this.turtle.checkCommandPriceDuration(this, this.Args[0], this.Args[1])
}

func (this *SetBreakoutPriceShortTurtleCommand) Do() bool {
	price, _ := strconv.ParseFloat(this.Args[0], 64)
	expireTime := this.turtle.getExpireTimeFromDuration(this.Args[1])
	this.turtle.manualUpdateBreakoutPrice(this.turtle.Long.BreakoutPrice.Value, price, expireTime)
	return true
}

// 设置做多退出价

type SetExitPriceLongTurtleCommand TurtleCommand

func NewSetExitPriceLongTurtleCommand(turtle *Turtle) *SetExitPriceLongTurtleCommand {
	cmd := &SetExitPriceLongTurtleCommand{
		Command: command.Command{
			Name:            "setExitPriceLong",
			Instruction:     "`.setExitPriceLong Price Duration` 设置做多退出价 e.g. .setExitPriceLong 8765.5 1h",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *SetExitPriceLongTurtleCommand) Prepare() bool {
	return this.turtle.checkCommandPriceDuration(this, this.Args[0], this.Args[1])
}

func (this *SetExitPriceLongTurtleCommand) Do() bool {
	price, _ := strconv.ParseFloat(this.Args[0], 64)
	expireTime := this.turtle.getExpireTimeFromDuration(this.Args[1])
	this.turtle.manualUpdateExitPrice(this.turtle.Short.ExitPrice.Value, price, expireTime)
	return true
}

// 设置做空退出价

type SetExitPriceShortTurtleCommand TurtleCommand

func NewSetExitPriceShortTurtleCommand(turtle *Turtle) *SetExitPriceShortTurtleCommand {
	cmd := &SetExitPriceShortTurtleCommand{
		Command: command.Command{
			Name:            "setExitPriceShort",
			Instruction:     "`.setExitPriceShort Price Duration` 设置做空退出价 e.g. .setExitPriceShort 8765.5 1h",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *SetExitPriceShortTurtleCommand) Prepare() bool {
	return this.turtle.checkCommandPriceDuration(this, this.Args[0], this.Args[1])
}

func (this *SetExitPriceShortTurtleCommand) Do() bool {
	price, _ := strconv.ParseFloat(this.Args[0], 64)
	expireTime := this.turtle.getExpireTimeFromDuration(this.Args[1])
	this.turtle.manualUpdateExitPrice(price, this.turtle.Long.ExitPrice.Value, expireTime)
	return true
}

// 设置当前退出周期，标准周期/长周期

type SetCurrentBreakoutPeriodTurtleCommand TurtleCommand

func NewSetCurrentBreakoutPeriodTurtleCommand(turtle *Turtle) *SetCurrentBreakoutPeriodTurtleCommand {
	cmd := &SetCurrentBreakoutPeriodTurtleCommand{
		Command: command.Command{
			Name:            "setCurrentBreakoutPeriod",
			Instruction:     "`.setCurrentBreakoutPeriod Period GoogleAuthCode` 设置当前突破价周期",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *SetCurrentBreakoutPeriodTurtleCommand) Instruction() string {
	return fmt.Sprintf("`.setCurrentBreakoutPeriod Period GoogleAuthCode` 设置当前突破价周期，Period 可选值为 %v, %v\n", this.turtle.Config.BreakoutPeriod, this.turtle.Config.SecondaryBreakoutPeriod)
}

func (this *SetCurrentBreakoutPeriodTurtleCommand) Do() bool {
	period, _ := strconv.ParseInt(this.Args[0], 10, 32)

	if int(period) != this.turtle.Config.BreakoutPeriod && int(period) != this.turtle.Config.SecondaryBreakoutPeriod {
		this.turtle.ErrorMsgf("参数 Period 错误，请输入 %v 或者 %v", this.turtle.Config.BreakoutPeriod, this.turtle.Config.SecondaryBreakoutPeriod)
		return false
	}
	this.turtle.Open.BreakoutPeriod = int(period)
	this.turtle.addSetHistory("Open.BreakoutPeriod", fmt.Sprintf("%.d", period), "manual")
	this.turtle.Long.BreakoutPrice.set(this.turtle, this.turtle.Long.BreakoutPrice.Value, time.Unix(0, 0), false, "Long.BreakoutPrice", ".setCurrentBreakoutPeriod")
	this.turtle.Short.BreakoutPrice.set(this.turtle, this.turtle.Short.BreakoutPrice.Value, time.Unix(0, 0), false, "Short.BreakoutPrice", ".setCurrentBreakoutPeriod")
	this.turtle.handlePositionAndOrders()
	this.turtle.SendMsgf("设置成功")
	this.turtle.sendStatus(false)
	return true
}

// 设置忽略某个时间点的 K线，用于修复 K线问题

type SetIgnoredExitKLineTurtleCommand TurtleCommand

func NewSetIgnoredExitKLineTurtleCommand(turtle *Turtle) *SetIgnoredExitKLineTurtleCommand {
	cmd := &SetIgnoredExitKLineTurtleCommand{
		Command: command.Command{
			Name:            "setIgnoredExitKLine",
			Instruction:     "`.setIgnoredExitKLine Time` 退出时忽略某个北京时间点的 K 线 e.g. .setIgnoredExitKLine 2020-09-08 20:00:0",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          math.MaxInt32,
			AuthcodePos:     -1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *SetIgnoredExitKLineTurtleCommand) Prepare() bool {
	timeStr := strings.Join(this.Args, "T")
	timeStr = fmt.Sprintf("%s+08:00", timeStr)
	kLineTime, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		this.turtle.ErrorMsgf("输入错误,请输入正确的时间格式如 2020-09-08 20:00:00")
		return false
	}

	for _, t := range this.turtle.Fix.IgnoredExitKLines {
		if t == kLineTime.Unix() {
			this.turtle.ErrorMsgf("该时间已设置忽略")
			return false
		}
	}

	nowTS := time.Now().Unix()
	if (kLineTime.Unix() + int64(this.turtle.Config.PeriodHour*3600*this.turtle.Config.ExitPeriod)) < nowTS {
		this.turtle.ErrorMsgf("该时间已过期，不在退出时间范围内")
		return false
	}

	openPrice, _, _, _ := this.turtle.GetOCHLByTimestamp(kLineTime.Unix())
	if openPrice == 0 {
		this.turtle.ErrorMsgf("找不到对应的 K 线，请确认时间是否有误")
		return false
	}
	return true
}

func (this *SetIgnoredExitKLineTurtleCommand) Do() bool {
	timeStr := strings.Join(this.Args, "T")
	timeStr = fmt.Sprintf("%s+08:00", timeStr)
	this.turtle.addIgnoredExitKLine(timeStr)
	return true
}

// 强制设置 DC 退出模式

type SetExitByDCTurtleCommand TurtleCommand

func NewSetExitByDCTurtleCommand(turtle *Turtle) *SetExitByDCTurtleCommand {
	cmd := &SetExitByDCTurtleCommand{
		Command: command.Command{
			Name:            "setExitByDC",
			Instruction:     "`.setExitByDC true/false GoogleAuthCode` 设置是否仅使用 DC 退出价来平仓，且不再检查头寸数量",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *SetExitByDCTurtleCommand) Do() bool {
	exitByDC := false

	if this.Args[0] == "true" {
		exitByDC = true
	} else if this.Args[0] == "false" {
		exitByDC = false
	} else {
		this.turtle.ErrorMsgf("参数[%s]错误，请输入 true 或者 false", this.Args[0])
		return false
	}

	this.turtle.ExitByDC = exitByDC
	this.turtle.addSetHistory("Confg.ExitByDC", fmt.Sprintf("%v", exitByDC), "manual")
	this.turtle.controller.storage.Save()
	this.turtle.SendMsgf("设置成功")
	this.turtle.sendStatus(false)
	this.turtle.handlePositionAndOrders()
	return true
}

// 本次退出后，暂停运行该品种

type SetStopOnExitTurtleCommand TurtleCommand

func NewSetStopOnExitTurtleCommand(turtle *Turtle) *SetStopOnExitTurtleCommand {
	cmd := &SetStopOnExitTurtleCommand{
		Command: command.Command{
			Name:            "setStopOnExit",
			Instruction:     "`.setStopOnExit true/false GoogleAuthCode` 本次持仓平仓后是否暂停运行",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *SetStopOnExitTurtleCommand) Do() bool {
	StopOnExit := false

	if this.Args[0] == "true" {
		StopOnExit = true
	} else if this.Args[0] == "false" {
		StopOnExit = false
	} else {
		this.turtle.ErrorMsgf("参数[%s]错误，请输入 true 或者 false", this.Args[0])
		return false
	}

	this.turtle.StopOnExit = StopOnExit
	this.turtle.addSetHistory("Confg.StopOnExit", fmt.Sprintf("%v", StopOnExit), "manual")
	this.turtle.controller.storage.Save()
	this.turtle.SendMsgf("设置成功")
	this.turtle.sendStatus(false)
	this.turtle.handlePositionAndOrders()
	return true
}

// 修复仓位头寸大小

type SetFixPositionQtyTurtleCommand TurtleCommand

func NewSetFixPositionQtyTurtleCommand(turtle *Turtle) *SetFixPositionQtyTurtleCommand {
	cmd := &SetFixPositionQtyTurtleCommand{
		Command: command.Command{
			Name:            "setFixPositionQty",
			Instruction:     "`.setFixPositionQty qty GoogleAuthCode` 当头寸检测异常时，可通过该命令修复，qty 可以为负",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *SetFixPositionQtyTurtleCommand) Do() bool {
	qty, err := strconv.ParseFloat(this.Args[0], 64)
	if err != nil {
		this.turtle.ErrorMsgf("请输入有效的数量: %s", err)
		return false
	}

	this.turtle.Fix.PositionQty = qty
	this.turtle.addSetHistory("Fix.PositionQty", fmt.Sprintf("%f", qty), "manual")
	this.turtle.SendMsgf("设置成功")
	this.turtle.sendStatus(false)
	this.turtle.handlePositionAndOrders()
	return true
}

// 完全重置突破价、退出价和人工干预 DC 退出
// FIXME: 检查功能是否完整

type ResetPriceTurtleCommand TurtleCommand

func NewResetPriceTurtleCommand(turtle *Turtle) *ResetPriceTurtleCommand {
	cmd := &ResetPriceTurtleCommand{
		Command: command.Command{
			Name:            "resetPrice",
			Instruction:     "`.resetPrice GoogleAuthCode` 使用当前 K 线数据重置突破价、退出价并删除人工干预 DC 数负",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *ResetPriceTurtleCommand) Do() bool {
	this.turtle.Fix.IgnoredExitKLines = []int64{}

	if success := this.turtle.HandleLatestKLineData(); success {
		this.turtle.updatePriceStorage(true, true)
		this.turtle.SendMsgf("重置成功")
		this.turtle.sendStatus(false)
		this.turtle.handlePositionAndOrders()
	}
	return true
}

// 使用实际挂单订单木盖本地数据

type ConfirmOrdersTurtleCommand TurtleCommand

func NewConfirmOrdersTurtleCommand(turtle *Turtle) *ConfirmOrdersTurtleCommand {
	cmd := &ConfirmOrdersTurtleCommand{
		Command: command.Command{
			Name:            "confirmOrders",
			Instruction:     "`.confirmOrders GoogleAuthCode` 使用实际挂单数据覆盖程序本地存储数",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *ConfirmOrdersTurtleCommand) Do() bool {
	this.turtle.ConfirmOpenOrders()
	this.turtle.addSetHistory("ConfirmOrders", "1", "")
	return true
}

/* 具体实现 */

func (this *Turtle) resetATR(manual bool) {
	this.Open.ATR.set(this, this.lastATR, time.Unix(this.klineStatus.kLineExpiredAt, 0), manual, "Open.ATR", "reset")
	this.controller.storage.Save()

	this.SendMsgf("重置成功")
	this.sendStatus(false)
	this.handlePositionAndOrders()
}

func (this *Turtle) checkCommandPriceDuration(command command.Commander, priceStr string, durationStr string) bool {
	price, err := strconv.ParseFloat(priceStr, 64)
	if err != nil {
		this.ErrorMsgf("请输入正确的价格")
	}
	duration := durationStr

	origPrice := 0.0
	if command.Is("setBreakoutPriceLong") {
		origPrice = this.Long.BreakoutPrice.Value
	} else if command.Is("setBreakoutPriceShort") {
		origPrice = this.Short.BreakoutPrice.Value
	} else if command.Is("setExitPriceShort") {
		origPrice = this.Long.ExitPrice.Value
	} else if command.Is("setExitPriceLong") {
		origPrice = this.Short.ExitPrice.Value
	}

	// 价格相差过大说明可能输入错误
	if math.Abs(price-origPrice) > (origPrice*0.5) && !this.controller.Config.IsTestnet {
		this.ErrorMsgf("输入错误，价格相差超过 50%%")
		return false
	}

	expireTime := this.getExpireTimeFromDuration(duration)
	if expireTime == "" {
		this.ErrorMsgf("请输入正确的有效时间")
		return false
	}
	return true
}

// 添加忽略退出 K 线
func (this *Turtle) addIgnoredExitKLine(kLineTime string) {
	if t, err := time.Parse(time.RFC3339, kLineTime); err == nil {
		this.Fix.IgnoredExitKLines = append(this.Fix.IgnoredExitKLines, t.Unix())
		this.addSetHistory("Fix.IgnoredExitKLines", kLineTime, "manual")
		this.Long.ExitPrice.set(this, this.Long.ExitPrice.Value, time.Unix(0, 0), true, "Long.ExitPrice", ".addIgnoredExitKLine")
		this.Short.ExitPrice.set(this, this.Short.ExitPrice.Value, time.Unix(0, 0), true, "Short.ExitPrice", ".addIgnoredExitKLine")
		this.controller.storage.Save()

		if success := this.HandleLatestKLineData(); success {
			this.updatePriceStorage(false, true)
			this.handlePositionAndOrders()
			this.SendMsgf("设置忽略 K 线成功")
			this.sendStatus(false)
		}
	} else {
		this.Errorf("add ignored exit kline %s, error: %s", kLineTime, err)
	}

}

func (this *Turtle) manualUpdateATR(value float64, expireTime string) {
	// expireTime 应该要大于当前时间
	newTime, err := time.Parse(time.RFC3339, expireTime)
	if err != nil {
		this.ErrorMsgf("输入错误，请输入正确的时间")
		return
	}

	if newTime.Before(time.Now()) {
		this.ErrorMsgf("输入错误，请输入正确的时间")
		return
	}

	this.Open.ATR.set(this, value, newTime, true, "Open.ATR", "")
	this.updateOpenSize()

	this.controller.storage.Save()
	this.SendMsgf("修改ATR成功")
	this.sendStatus(false)
	this.handlePositionAndOrders()
}

func (this *Turtle) manualUpdateBreakoutPrice(priceH float64, priceL float64, expireTime string) {
	if this.Position.Qty != 0 {
		this.ErrorMsgf("当前有持仓，无法修改")
		return
	}

	// expireTime 应该要大于当前时间
	newTime, err := time.Parse(time.RFC3339, expireTime)
	if err != nil {
		this.ErrorMsgf("输入错误,请输入正确的时间")
		return
	}

	if newTime.Before(time.Now()) {
		this.ErrorMsgf("输入错误,请输入正确的时间")
		return
	}

	this.Long.BreakoutPrice.set(this, priceH, newTime, true, "Long.BreakoutPrice", "")
	this.Short.BreakoutPrice.set(this, priceL, newTime, true, "Short.BreakoutPrice", "")

	this.updateOpenSize()

	this.controller.storage.Save()
	this.SendMsgf("修改突破价成功")
	this.sendStatus(false)
	this.handlePositionAndOrders()
}

func (this *Turtle) manualUpdateExitPrice(priceHigh float64, priceLow float64, expireTime string) {
	// expireTime 应该要大于当前时间
	newTime, err := time.Parse(time.RFC3339, expireTime)
	if err != nil {
		this.ErrorMsgf("输入错误,请输入正确的时间")
		return
	}

	if newTime.Before(time.Now()) {
		this.ErrorMsgf("输入错误,请输入正确的时间")
		return
	}

	this.Long.ExitPrice.set(this, priceLow, newTime, true, "Long.ExitPrice", "")
	this.Short.ExitPrice.set(this, priceHigh, newTime, true, "Short.ExitPrice", "")

	this.controller.storage.Save()
	this.SendMsgf("修改退出价成功")
	this.sendStatus(false)
	this.handlePositionAndOrders()
}

// 人工确认当前挂单数据正确，覆盖本地数据
func (this *Turtle) ConfirmOpenOrders() {
	position, err := this.getCurrentPosition(false)
	if position == nil {
		this.AlertMsgf("确认当前挂单失败: 获取当前持仓失败, error: %s", err)
		return
	}

	if position.Qty == 0 {
		this.AlertMsgf("确认当前挂单失败: 当前无持仓")
		return
	}

	openOrders, err := this.controller.Exchange.GetOpenOrders(this.InstrumentType, this.OrderType(), this.Symbol)
	if err != nil {
		this.AlertMsgf("覆盖本地挂单数据失败， 获取当前挂单失败，error: %s", err)
		return
	}

	this.CloseOrders = []*Order{}
	this.Long.OpenOrders = []*Order{}
	this.Short.OpenOrders = []*Order{}
	units := this.Position.GetInt(ExtKeyUnits) + 1

	sortedOrders := openOrders
	sort.SliceStable(sortedOrders, func(i, j int) bool {
		if position.Side == exchange.PositionSideLong {
			return sortedOrders[i].TriggerPrice < sortedOrders[j].TriggerPrice
		} else {
			return sortedOrders[i].TriggerPrice > sortedOrders[j].TriggerPrice
		}
	})

	for _, openOrder := range sortedOrders {
		if openOrder.IsCloseOrder() {
			// 平仓订单，记录订单ID
			this.CloseOrders = append(this.CloseOrders, SetOrderExt(openOrder, OrderCategoryStopLoss, 0))
		} else if openOrder.Qty != 0 {
			// 开仓单，记录订单ID、单位头寸大小
			if openOrder.Side == exchange.OrderSideBuy {
				this.Long.OpenOrders = append(this.Long.OpenOrders, SetOrderExt(openOrder, OrderCategoryLong, units))
				this.Long.OpenSize = this.Qty2Size(this.Symbol, openOrder.TriggerPrice, openOrder.Qty)
			} else {
				this.Short.OpenOrders = append(this.Short.OpenOrders, SetOrderExt(openOrder, OrderCategoryShort, units))
				this.Short.OpenSize = this.Qty2Size(this.Symbol, openOrder.TriggerPrice, openOrder.Qty)
			}
			units += 1
		} else {
			this.AlertMsgf("覆盖本地挂单数据失败，异常的挂单: %#v", openOrder)
			return
		}
	}

	this.controller.storage.Save()
	this.SendMsgf("确认当前挂单成功，已覆盖本地数据")
	this.handlePositionAndOrders()
}

type PauseToTurtleCommand TurtleCommand

func NewPauseToTurtleCommand(turtle *Turtle) *PauseToTurtleCommand {
	cmd := &PauseToTurtleCommand{
		Command: command.Command{
			Name:            "pauseTo",
			Instruction:     "`.pauseTo Time` 暂停到某个北京时间点，为 0 表示取消暂停 e.g. .pauseTo 01-02T20:00",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *PauseToTurtleCommand) Prepare() bool {
	if len(this.Args) == 1 && this.Args[0] == "0" {
		return true
	}
	timeStr := strings.Join(this.Args, "T")
	toTime := utils.ParseTimeBeijing(timeStr)
	if toTime == nil {
		this.ErrorMsgf("输入错误，请输入正确的时间格式如 01-02T20:00")
		return false
	}
	if time.Since(*toTime) > 0 {
		this.ErrorMsgf("不能输入一个过去的时间 (%s)", toTime)
		return false
	}
	return true
}

func (this *PauseToTurtleCommand) Do() bool {
	if len(this.Args) == 1 && this.Args[0] == "0" {
		this.turtle.SetPauseTo(nil, PauseReasonUnknown)
		this.SendMsgf("取消暂停成功")
		this.turtle.handlePositionAndOrders()
		return true
	}

	timeStr := strings.Join(this.Args, "T")
	toTime := utils.ParseTimeBeijing(timeStr)

	if _, err := this.turtle.controller.Exchange.CancelAllOrders(this.turtle.InstrumentType, this.turtle.OrderType(), this.turtle.Symbol); err != nil {
		this.AlertMsgf("取消所有订单失败, error: %s", err)
		return false
	} else {
		this.SendMsgf("所有订单已取消")
	}
	this.turtle.SetPauseTo(toTime, PauseReasonManual)
	this.SendMsgf("设置成功")
	return true
}

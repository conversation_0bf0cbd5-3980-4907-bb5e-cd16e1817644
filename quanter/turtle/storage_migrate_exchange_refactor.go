package turtle

import (
	"bytes"
	"io"
	"os"
	"path"
	"strings"

	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"

	"github.com/tidwall/gjson"
)

func (s *Storage) cleanupExtStruct() {
	for _, tt := range s.Turtles {
		for _, o := range tt.Long.OpenOrders {
			CleanOrderExtStruct(o)
		}
		for _, o := range tt.Short.OpenOrders {
			CleanOrderExtStruct(o)
		}
		for _, o := range tt.CloseOrders {
			CleanOrderExtStruct(o)
		}
		for _, o := range tt.ExecOrders {
			CleanOrderExtStruct(o)
		}
		for _, o := range tt.TakeOrders {
			CleanOrderExtStruct(o)
		}
		CleanPositionExtStruct(tt.Position)
		for _, p := range tt.PositionChanges {
			CleanPositionExtStruct(p)
		}
	}
}

func (s *Storage) migrateForExchangeRefactor() {
	symbolToCodeMap := map[string]string{
		"XBTUSD":        "BTC00",
		"XBTUSDT":       "BTC00.U",
		"BTCUSDT":       "BTC00.U",
		"ETHUSD":        "ETH00",
		"ETHUSDT":       "ETH00.U",
		"BTC-USDT-SWAP": "BTC00.U",
		"ETH-USDT-SWAP": "ETH00.U",
		"BNBUSDT":       "BNB00.U"}

	content := gjson.Result{}
	path := path.Join(s.Controller.GetConfigPath(), s.Controller.GetID()+".turtle_storage")
	f, _ := os.OpenFile(path, os.O_RDONLY, 0755)
	if fileBytes, err := io.ReadAll(f); err != nil {
		zlog.Errorf("error read storage as gjson, error: %s", err)
	} else {
		newVersionMatch := bytes.LastIndex(fileBytes, []byte("SymbolCode"))
		// 在整个文件中找不到 SymbolCode 证明是旧 storage
		if newVersionMatch == -1 {
			zlog.Infof("storage is before exchange refactor, migrate...")
			content = gjson.ParseBytes(fileBytes)
			turtles := content.Get(`Turtles.#(ID%"*")#`).Array()

			for _, t := range turtles {
				symbol := t.Get("Symbol").String()
				turtleID := t.Get("ID").String()
				if code, ok := symbolToCodeMap[symbol]; !ok {
					zlog.Warnf("symbol (%s) can not convert to code", symbol)
					continue
				} else {
					turtle := s.findTurtleByID(turtleID)
					if turtle == nil {
						zlog.Errorf("found turtle by id error, not found")
						continue
					}
					if sc, err := exchange.NewSymbolCode(code, ""); err != nil {
						zlog.Errorf("migrate storage error, create new symbol code error: %s", code)
					} else {
						turtle.SymbolCode = sc // migrate SymbolCode，新代码没有 SymbolCode 会 crash
						turtle.InstrumentType = turtle.SymbolCode.InstrumentType()
					}
					s.migrateTurtlePosition(turtle.Position, t.Get(`Position`))
					positionChanges := t.Get(`PositionChanges.#(Units>=0)#`).Array()
					for i, p := range positionChanges {
						s.migrateTurtlePosition(turtle.PositionChanges[i], p)
					}

					longOpenOrders := t.Get(`Long.OpenOrders.#(Unit>=0)#`).Array()
					shortOpenOrders := t.Get(`Short.OpenOrders.#(Unit>=0)#`).Array()
					closeOrders := t.Get(`CloseOrders.#(Unit>=0)#`).Array()
					takeOrders := t.Get(`TakeOrders.#(Unit>=0)#`).Array()
					execOrders := t.Get(`ExecOrders.#(Unit>=0)#`).Array()

					for i, o := range longOpenOrders {
						s.migrateTurtleOrder(turtle.Long.OpenOrders[i], o)
					}
					for i, o := range shortOpenOrders {
						s.migrateTurtleOrder(turtle.Short.OpenOrders[i], o)
					}
					for i, o := range closeOrders {
						s.migrateTurtleOrder(turtle.CloseOrders[i], o)
					}
					for i, o := range takeOrders {
						s.migrateTurtleOrder(turtle.TakeOrders[i], o)
					}
					for i, o := range execOrders {
						s.migrateTurtleOrder(turtle.ExecOrders[i], o)
					}
				}
			}
		}
	}
}

func (s *Storage) migrateTurtlePosition(np *Position, p gjson.Result) {
	np.SetInt(ExtKeyUnits, int(p.Get(`Units`).Int()))
	np.SetFloat64(ExtKeyTakeProfitPrice, p.Get(`TakeProfitPrice`).Float())
	np.SetFloat64(ExtKeyStopLossPrice, p.Get(`StopLossPrice`).Float())
	np.SetFloat64(ExtKeyLockedPNL, p.Get(`LockedPNL`).Float())
	np.SetFloat64(ExtKeyPNL, p.Get(`PNL`).Float())
	np.SetFloat64(ExtKeyAssetPrice, p.Get(`AssetPrice`).Float())
	np.SetString(ExtKeyAsset, p.Get(`Asset`).String())
}

func (s *Storage) migrateTurtleOrder(no *Order, o gjson.Result) {
	if o.Get(`IsOpen`).Bool() {
		status := exchange.OrderStatusNew
		if no.ExecQty > 0 {
			status = exchange.OrderStatusPartialFilled
		}
		no.Status = status
	} else {
		status := exchange.OrderStatusCancelled
		if no.ExecQty == no.Qty {
			status = exchange.OrderStatusFilled
		} else if no.ExecQty > 0 {
			status = exchange.OrderStatusPartialCancelled
		}
		no.Status = status
	}
	no.SetInt(ExtKeyUnit, int(o.Get(`Unit`).Int()))
	no.SetString(ExtKeyCategory, o.Get(`Type`).String())
	no.Price = o.Get(`LimitPrice`).Float()
	no.OrderID = o.Get(`ID`).String()
	no.Type = exchange.UnknownOrderType
}

func (s *Storage) migrateForOKAlgoOrderID() {
	content := gjson.Result{}
	path := path.Join(s.Controller.GetConfigPath(), s.Controller.GetID()+".turtle_storage")
	f, _ := os.OpenFile(path, os.O_RDONLY, 0755)
	if fileBytes, err := io.ReadAll(f); err != nil {
		zlog.Errorf("error read storage as gjson, error: %s", err)
	} else {
		oldVersionMatch := bytes.LastIndex(fileBytes, []byte("|||"))
		// 在整个文件中找不到 SymbolCode 证明是旧 storage
		if oldVersionMatch > 0 {
			zlog.Infof("storage is before exchange refactor, migrate...")
			content = gjson.ParseBytes(fileBytes)
			turtles := content.Get(`Turtles.#(ID%"*")#`).Array()

			for _, t := range turtles {
				turtleID := t.Get("ID").String()
				turtle := s.findTurtleByID(turtleID)
				if turtle == nil {
					zlog.Errorf("found turtle by id error, not found")
					continue
				}

				for _, order := range turtle.Long.OpenOrders {
					order.OrderID = strings.Replace(order.OrderID, "|||", "", -1)
				}
				for _, order := range turtle.Short.OpenOrders {
					order.OrderID = strings.Replace(order.OrderID, "|||", "", -1)
				}
				for _, order := range turtle.CloseOrders {
					order.OrderID = strings.Replace(order.OrderID, "|||", "", -1)
				}
				for _, order := range turtle.TakeOrders {
					order.OrderID = strings.Replace(order.OrderID, "|||", "", -1)
				}
				for _, order := range turtle.ExecOrders {
					order.OrderID = strings.Replace(order.OrderID, "|||", "", -1)
				}

			}
		}
	}
}

func (s *Storage) migrateExchangeName() bool {
	path := path.Join(s.Controller.GetConfigPath(), s.Controller.GetID()+".turtle_storage")
	f, _ := os.OpenFile(path, os.O_RDONLY, 0755)
	if fileBytes, err := io.ReadAll(f); err != nil {
		zlog.Errorf("error read storage as gjson, error: %s", err)
	} else {
		newVersionMatch := bytes.LastIndex(fileBytes, []byte("ExchangeName"))
		if newVersionMatch == -1 {
			s.ExchangeName = s.Controller.GetExchange().GetName()
			return true
		}
	}
	return false
}

func (s *Storage) FixOrderInstrumentType() {
	for _, turtle := range s.Turtles {
		for _, order := range turtle.Long.OpenOrders {
			tryFixOrderInstrumentType(turtle, order)
		}
		for _, order := range turtle.Short.OpenOrders {
			tryFixOrderInstrumentType(turtle, order)
		}
		for _, order := range turtle.CloseOrders {
			tryFixOrderInstrumentType(turtle, order)
		}
		for _, order := range turtle.TakeOrders {
			tryFixOrderInstrumentType(turtle, order)
		}
		for _, order := range turtle.ExecOrders {
			tryFixOrderInstrumentType(turtle, order)
		}
	}
}

func tryFixOrderInstrumentType(turtle *Turtle, order *exchange.Order) {
	if order.InstrumentType == exchange.UnknownInstrumentType {
		order.InstrumentType = turtle.SymbolCode.InstrumentType()
	}
	order.InstrumentType = exchange.FixInstrumentType(order.InstrumentType)
}

func (s *Storage) migrateSymbolCode() (migrated bool) {
	path := path.Join(s.Controller.GetConfigPath(), s.Controller.GetID()+".turtle_storage")
	f, _ := os.OpenFile(path, os.O_RDONLY, 0755)
	if fileBytes, err := io.ReadAll(f); err != nil {
		zlog.Errorf("error read storage as gjson, error: %s", err)
	} else {
		zlog.Infof("migrate symbol code...")
		content := gjson.ParseBytes(fileBytes)
		turtles := content.Get(`Turtles.#(ID%"*")#`).Array()

		for _, t := range turtles {
			turtleID := t.Get("ID").String()
			turtle := s.findTurtleByID(turtleID)
			if turtle == nil {
				zlog.Errorf("found turtle by id error, not found")
				continue
			}
			if exchange.FixSymbolCode(turtle.SymbolCode) {
				migrated = true
			}
			oldType := turtle.InstrumentType
			turtle.InstrumentType = exchange.FixInstrumentType(turtle.InstrumentType)
			if turtle.InstrumentType != oldType {
				migrated = true
			}
		}
	}
	return migrated
}

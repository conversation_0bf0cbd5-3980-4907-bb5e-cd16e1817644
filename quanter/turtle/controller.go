package turtle

import (
	"fmt"
	"math/rand"
	"os"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/binance"
	"github.com/wizhodl/quanter/exchange/bitmex"
	"github.com/wizhodl/quanter/exchange/bybit"
	"github.com/wizhodl/quanter/exchange/gateway"
	"github.com/wizhodl/quanter/exchange/hyperliquid"
	"github.com/wizhodl/quanter/exchange/okex"
	"github.com/wizhodl/quanter/messenger"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"
)

type RotateSchedule struct {
	id           string
	from         *exchange.SymbolCode
	to           *exchange.SymbolCode
	scheduleTime time.Time
	isCanceled   bool // 是否已经取消
}

type TurtleController struct {
	base.BaseController

	storage         *Storage                // 新的数据文件
	Config          *TurtleControllerConfig // 当前配置
	Turtles         map[string]*Turtle
	handleMutex     sync.Mutex                // 主流程处理锁
	rotateSchedules map[string]RotateSchedule // 计划的展期操作, key: scheduleID
	marginBalances  map[string]float64
	openSizeMargins map[string]float64
}

func NewTurtleController(turtleID string, debug bool, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath string, parentMessenger *messenger.SlackMessenger, managerID string) (*TurtleController, error) {
	controller := &TurtleController{
		BaseController: base.BaseController{
			BaseResponder: command.NewBaseResponder(debug, commitHash, buildTime, turtleID, nil, ""),
			ID:            turtleID,
			RefID:         exchange.NewRandomID(),
			ConfigPath:    configPath,
		},

		handleMutex:     sync.Mutex{},
		rotateSchedules: map[string]RotateSchedule{},
		marginBalances:  map[string]float64{},
		openSizeMargins: map[string]float64{},
	}
	controller.Controllable = controller

	controller.Setup(debug, parentMessenger, managerID)

	config, err := NewTurtleControllerConfig(controller)
	if err != nil {
		return nil, err
	}
	controller.Config = config

	if logDirPath != "" {
		config.LogDirPath = logDirPath
	}

	// 从缓存载入 Turtles
	// setup storage 时可能用到 config 的值，始终在 config 之后初始化 storage
	controller.storage = SetupStorage(controller)

	controller.AddCommands([]command.Commander{
		NewStatusControllerCommand(controller),
		NewParametersControllerCommand(controller),
		NewHistoryControllerCommand(controller),
		NewReportControllerCommand(controller),
		NewPositionControllerCommand(controller),
		NewMarginRatioControllerCommand(controller),
		NewPauseAllControllerCommand(controller),
		NewResumeAllControllerCommand(controller),
		NewCleanControllerCommand(controller),
		NewRemoveHistoryControllerCommand(controller),
		cmds.NewRestartCommand(),
		NewAddSymbolControllerCommand(controller),
		NewRemoveSymbolControllerCommand(controller),
		NewRotateSymbolControllerCommand(controller),
		NewCloseAllControllerCommand(controller),
		NewSetConfigControllerCommand(controller),
		NewSetExternalBalanceControllerCommand(controller),
		NewSetMinMarginRatioCommand(controller),
		NewCancelRotateControllerCommand(controller),
	})

	if !controller.Config.IsDemo {
		controller.AddCommands([]command.Commander{
			cmds.NewHoldingsCommand(controller),
			cmds.NewAssetsCommand(controller, controller.storage),
			NewSimulateControllerCommand(controller),
		})
	}
	controller.AddCommands([]command.Commander{
		cmds.NewPagerCommand(),
		NewMuteControllerCommand(controller),
		cmds.NewDebugCommand(),
		cmds.NewLogCommand(logDirPath, controller.GetLogFilename()),
		cmds.NewDownloadLogCommand(logDirPath, controller.GetLogFilename()),
		cmds.NewDownloadStorageCommand(controller.ConfigPath, fmt.Sprintf("%s.turtle_storage", controller.ID)),
		cmds.NewPriceTriggersCommand(controller),
		cmds.NewPriceWatchCommand(controller),
		cmds.NewStackTraceCommand(),
		cmds.NewDeleteErrorsCommand(),
	})

	controller.SetAdditionalHelpText("`Duration` _支持的格式: 1m, 1h, 1d, 1k, none. 分别表示几分钟内有效、几小时内有效、几天内有效、几根 k 线有效(含当前)、不过期直到平仓;")

	if controller.Standalone() {
		// 注册 commander 要在
		// 根据命令行参数更新 config 中的部分字段
		if releaseBinaryDirPath != "" {
			config.ReleaseBinaryDirPath = releaseBinaryDirPath
		}
		// 初始化时检查 ReleaseBinaryDir 是否可以写，以免后续 releases 相关命令每次都要检查
		if utils.CheckReleaseBinaryDirPath(controller.Config.ReleaseBinaryDirPath) {
			controller.AddCommands([]command.Commander{
				cmds.NewReleaseCommand(controller.Config.ReleaseBinaryDirPath, controller.ID),
				cmds.NewListVersionCommand(controller.Config.ReleaseBinaryDirPath, controller.ID),
				cmds.NewUseVersionCommand(controller.Config.ReleaseBinaryDirPath, controller.ID),
			})
		}

	}

	// 尝试以 Debug 模式启动，如果不能以 Debug 模式启动，会要求以 .launch 模式启动
	// initTurtles
	debugLaunched := controller.TryLaunchWithDebug()
	if debugLaunched {
		go controller.InitStartTurtles("")
	} else {
		controller.AddCommands([]command.Commander{NewLaunchControllerCommand(controller)})
	}

	// 设置敏感命令
	controller.Messenger.SetSensitiveCommands(controller.GetCommandProcessor().SensitiveCommands)

	return controller, nil
}

// debug 状态运行 和 定时任务，有多次运行防呆机制
func (this *TurtleController) Run() {
	tasks := []base.CronTask{
		// 定时发送状态
		{
			Spec: "1 12 * * *",
			Cmd: func() {
				this.storage.RecordAssets()
				this.sendStatus(true)
				this.Exchange.CacheInstruments(true)
			},
		},

		// 每个整点处理更新 K 线
		{Spec: "0 * * * *", Cmd: this.updateKlineHandler},

		// 从开始运行之后的每5分钟，处理持仓与订单
		{Spec: "@every 5m", Cmd: this.handlePositionAndOrders},

		{Spec: "@every 10m", Cmd: this.handleSymbolAutoRotate},

		// 每个整点 10 秒后处理持仓与订单，主要为了在收资金费率后及时调整爆仓价
		{Spec: "0 * * * *", Cmd: func() {
			time.Sleep(time.Second * 10)
			this.handlePositionAndOrders()
		}},
	}
	this.BaseController.Run(tasks)
}

func (this *TurtleController) InitAPI(apiSecret secrets.SecretString) error {
	opts := &exchange.Options{
		Host:                  this.Config.Host,
		ApiKey:                this.Config.ApiKey,
		ApiSecret:             apiSecret,
		FixerKey:              this.Config.FixerKey,
		IsTestnet:             this.Config.IsTestnet,
		ProxyUrl:              this.Config.ProxyUrl,
		MinLeverage:           secrets.GetMinLeverage(),
		MarginUpdatedCallback: this.marginUpdatedCallback,
		OrderUpdatedCallback:  this.orderUpdatedCallback,
		ControllerID:          this.ID,
		DataPath:              this.ConfigPath,
	}
	var instrumentTypes []exchange.InstrumentType
	var err error

	if this.Config.ExchangeName == exchange.BitMEX {
		this.Exchange, err = bitmex.NewBitMEX(opts)
		if err != nil {
			return fmt.Errorf("初始化 BitMEX 失败，error: %s", err)
		}
		instrumentTypes = []exchange.InstrumentType{exchange.CoinMarginedFutures, exchange.USDXMarginedFutures}
		// 检查 API 有效性
		if _, err := this.Exchange.GetUserMargin("", "XBt"); err != nil {
			return fmt.Errorf("获取余额失败，请检查 API 是否正确. error: %s", err)
		}
	} else if this.Config.ExchangeName == exchange.Binance {
		this.Exchange, err = binance.NewBinance(opts)
		if err != nil {
			return fmt.Errorf("初始化 Binance 失败，error: %s", err)
		}
		instrumentTypes = []exchange.InstrumentType{exchange.USDXMarginedFutures}
		if err := this.Exchange.SetDualPositionSide(exchange.USDXMarginedFutures, true); err != nil {
			if strings.Contains(err.Error(), "No need to change") {
				this.Debugf("position side is dual, no need to set.")
			} else {
				return fmt.Errorf("设置双向持仓模式错误，请检测 API 是否正确或已有持仓或挂单: %s", err)
			}
		}

	} else if this.Config.ExchangeName == exchange.OKEx {
		client, err := okex.NewOKEx(opts)
		if err != nil {
			return fmt.Errorf("初始化 OKEX 失败，error: %s", err)
		}
		this.Exchange = client
		instrumentTypes = []exchange.InstrumentType{exchange.USDXMarginedFutures}
		secrets := strings.Split(string(apiSecret), "|||")
		if len(secrets) < 2 {
			return fmt.Errorf("api secret 错误，需使用 \"|||\" 分隔密码")
		}

		// 设置仓位模式
		if config, err := this.Exchange.GetAccountConfig(exchange.USDXMarginedFutures); err != nil {
			return fmt.Errorf("初始化 OKEX 失败，获取用户配置错误：%s", err)
		} else {
			if !config.DualPositionSide { // 挂计划委托单必须双向持仓模式
				if err := this.Exchange.SetDualPositionSide(exchange.USDXMarginedFutures, true); err != nil {
					this.WarnMsgf("设置持仓模式错误: %s", err)
				}
			}

			if config.MarginMode != exchange.AccountMarginModeIsolated {
				return fmt.Errorf("账户模式错误，请设置为单币种保证金模式")
			}
		}

	} else if this.Config.ExchangeName == exchange.CTP {
		opts.GatewayChannelName = this.ID
		this.Exchange, err = gateway.NewCTP(opts, this.ID)
		if err != nil {
			return fmt.Errorf("初始化 CTP 失败，error: %s", err)
		}
		instrumentTypes = []exchange.InstrumentType{exchange.USDXMarginedFutures}
		// 检查 API 有效性
		if _, err := this.Exchange.GetUserMargin("", ""); err != nil {
			return fmt.Errorf("获取余额失败，请检查 API 是否正确. error: %s", err)
		}

	} else if this.Config.ExchangeName == exchange.MetaTrader {
		opts.GatewayChannelName = this.ID
		this.Exchange, err = gateway.NewMT(opts, this.ID)
		if err != nil {
			return fmt.Errorf("初始化 MetaTrader 失败，error: %s", err)
		}
		instrumentTypes = []exchange.InstrumentType{exchange.USDXMarginedFutures}
		// 检查 API 有效性
		if _, err := this.Exchange.GetUserMargin("", ""); err != nil {
			return fmt.Errorf("获取余额失败，请检查 API 是否正确. error: %s", err)
		}

	} else if this.Config.ExchangeName == exchange.Hyperliquid {
		this.Exchange, err = hyperliquid.NewHyperliquid(opts)
		if err != nil {
			return fmt.Errorf("初始化 Hyperliquid 失败，error: %s", err)
		}
		instrumentTypes = []exchange.InstrumentType{exchange.USDXMarginedFutures}
		// 检查 API 有效性
		if _, err := this.Exchange.GetUserMargin("", ""); err != nil {
			return fmt.Errorf("获取余额失败，请检查 API 是否正确. error: %s", err)
		}

		// 验证 API Key 和 Secret
		// addr := opts.ApiKey
		// privateKey, err := crypto.HexToECDSA(opts.ApiSecret)
		// if err != nil {
		// 	return fmt.Errorf("invalid private key: %s", err)
		// }

		// publicAddress := crypto.PubkeyToAddress(privateKey.PublicKey)
		// if addr != publicAddress.String() {
		// 	return fmt.Errorf("addr not match with private key")
		// }

	} else if this.Config.ExchangeName == exchange.Bybit {
		this.Exchange, err = bybit.NewBybit(opts)
		if err != nil {
			return fmt.Errorf("初始化 BYBIT 失败，error: %s", err)
		}
		instrumentTypes = []exchange.InstrumentType{exchange.USDXMarginedFutures}
		// 检查并设置保证金模式
		if config, err := this.Exchange.GetAccountConfig(exchange.USDXMarginedFutures); err != nil {
			return fmt.Errorf("初始化 BYBIT 失败，获取用户配置错误：%s", err)
		} else if config.MarginMode != exchange.AccountMarginModeIsolated {
			err := this.Exchange.SetAccountMarginMode(exchange.USDXMarginedFutures, exchange.AccountMarginModeIsolated)
			if err != nil {
				return fmt.Errorf("设置账户保证金模式失败：%s", err)
			}
		}

	} else {
		return fmt.Errorf("unsuported exchange (%s)", this.Config.ExchangeName)
	}

	if err := this.Exchange.CacheInstruments(true); err != nil {
		return fmt.Errorf("cache instruments error: %s", err)
	}
	this.Exchange.SetHttpDebug(this.Debug)
	go this.Exchange.ConnectWebsocket(instrumentTypes, nil)
	go this.Exchange.CheckPriceTriggerTimeLoop()

	return nil
}

func (this *TurtleController) InitTurtles() {
	this.Turtles = make(map[string]*Turtle)

	// 初始化 turtle
	for _, code := range this.Config.Symbols {
		this.Infof("initing turtle (%s), code (%s)", this.ID, code)
		if symbolCode, err := this.NewSymbolCode(code); err != nil {
			this.Errorf("init turtle (%s) failed, error: %s", code, err)
		} else {
			ts := this.storage.GetTurtleBySymbol(symbolCode)
			if tt, err := NewTurtle(this, symbolCode, ts, nil, false); err == nil {
				if tt != nil {
					this.Turtles[code] = tt
					this.storage.AddTurtle(tt)
				} else {
					this.Errorf("init turtle failed, turtle is nil while there is no error, symbol code: %s", symbolCode)
				}
			} else {
				this.SendMsgf("初始化失败 (%s) ：%s", code, err)
			}
		}
	}
}

func (this *TurtleController) marginUpdatedCallback(userMargin *exchange.UserMargin, currency string) {
	for _, tt := range this.Turtles {
		tt.marginUpdatedCallback(userMargin, currency)
	}
}

func (this *TurtleController) orderUpdatedCallback(order *exchange.Order) {
	for _, tt := range this.Turtles {
		if order.Symbol == tt.Symbol {
			tt.orderUpdatedCallback()
		}
	}
}

func (this *TurtleController) AskForLaunch() {
	if this.Standalone() {
		if this.IsDebuging() {
			if os.Getenv(fmt.Sprintf("QUANTER_%s_FAKE_EXECUTE", this.ID)) == "1" {
				this.SendMsgf("以 Debug - [Fake Execute] 模式启动。")
			} else {
				this.SendMsgf("以 Debug - [实盘] 模式启动。")
			}
		} else {
			msg := fmt.Sprintf("Build: %s, 请输入命令 `.launch Password GoogleAuthCode resumeAll/pauseAll` 启动程序", this.BuildInfo())
			this.SendMsgf(msg)
		}
	}
}

func (this *TurtleController) updateKlineHandler() {
	time.Sleep(time.Second * time.Duration(5+rand.Intn(10))) // 稍等 5 秒确保 K 线更新
	for _, tt := range this.Turtles {
		tt.updateKlineHandler()
		time.Sleep(time.Second * time.Duration(1+rand.Intn(4)))
	}
}

func (this *TurtleController) GetInstrumentTypes() []exchange.InstrumentType {
	instrumentTypes := []exchange.InstrumentType{}
	for _, tt := range this.Turtles {
		instrumentType := tt.SymbolCode.InstrumentType()
		found := false
		for _, insType := range instrumentTypes {
			if insType == instrumentType {
				found = true
			}
		}
		if !found {
			instrumentTypes = append(instrumentTypes, tt.SymbolCode.InstrumentType())
		}
	}
	return instrumentTypes
}

// 重载 base 的方法
// 不能通过重载 CheckSymbolPrefixAllowed 实现，因为当在 base 中调用时，无法调用到重载的方法
func (this *TurtleController) GetAccountWorth(allSymbols bool) (qtyInUSDT float64, er error) {
	filterFunc := func(coin string) bool { return true }
	if allSymbols {
		filterFunc = nil
	}
	if asset, err := this.Exchange.GetAccountWorth(this.RefID, this.GetInstrumentTypes(), base.AccountWorthCacheTime, filterFunc); err != nil {
		er = fmt.Errorf("[%s] get account worth failed, error: %s", this.ID, err)
		return
	} else {
		qtyInUSDT = asset
		return
	}
}

// 重载 base 的方法
// 不能通过重载 CheckSymbolPrefixAllowed 实现，因为当在 base 中调用时，无法调用到重载的方法
func (this *TurtleController) GetAccountBalances(allSymbols bool) (balances exchange.AccountBalanceList, er error) {
	return this.BaseController.GetAccountBalances(true)
}

func (this *TurtleController) handlePositionAndOrders() {
	for _, tt := range this.Turtles {
		tt.handlePositionAndOrders()
		tt.queryExecOrders()
	}

	alertSummary := this.getOpenMarginSummary(true)
	if alertSummary != "" {
		this.AlertMsgf("当前账号余额不足，请更改 OpenSizePercent/ExternalBalance/MinMarginRatio 等配置：\n```%s```", alertSummary)
	}

	// 检查 CTP 的保证金率
	this.checkMarginRateForCTP()
}

func (this *TurtleController) getOpenMarginSummary(onlyInsufficient bool) (result string) {
	this.marginBalances = map[string]float64{}
	this.openSizeMargins = map[string]float64{}
	for _, tt := range this.Turtles {
		if tt.Status == RUNNING && tt.Balance.Margin > 0 {
			// 一个趋势机的余额就是整个账号的余额，不需要累加
			this.marginBalances[tt.Balance.Asset] = tt.Balance.Margin
			_, found := this.openSizeMargins[tt.Balance.Asset]
			if found {
				this.openSizeMargins[tt.Balance.Asset] += tt.Balance.OpenSizeMargin
			} else {
				this.openSizeMargins[tt.Balance.Asset] = tt.Balance.OpenSizeMargin
			}
		}
	}

	minRatio := this.Config.MinMarginRatio
	if minRatio == 0 {
		minRatio = 1
	}

	// 对余额币种相同的检查余额比例
	deltaBalances := map[string]float64{}
	assets := []string{}
	// 以 openSizeMargins 为基准进行比较
	for asset, openMargin := range this.openSizeMargins {
		margin, found := this.marginBalances[asset]
		if !found {
			this.marginBalances[asset] = 0.0
		}
		deltaBalances[asset] = margin - openMargin*minRatio
		assets = append(assets, asset)
	}
	sort.SliceStable(assets, func(i, j int) bool {
		return assets[i] < assets[j]
	})
	t := exchange.NewTable()
	t.SetHeader([]string{"Asset", "Required Margin", "Min Margin Ratio", "Min Margin", "Acutal Margin", "Ratio", "Delta"})
	for _, asset := range assets {
		include := true
		delta := deltaBalances[asset]
		if onlyInsufficient && delta > 0 {
			include = false
		}
		if include {
			minMargin := this.openSizeMargins[asset] * minRatio
			ratio := this.marginBalances[asset] / minMargin
			t.AddRow([]string{
				asset,
				this.formatAmount(asset, this.openSizeMargins[asset]),
				fmt.Sprintf("%.0f%%", minRatio*100),
				this.formatAmount(asset, minMargin),
				this.formatAmount(asset, this.marginBalances[asset]),
				fmt.Sprintf("%.2f%%", ratio*100),
				this.formatAmount(asset, delta),
			})
		}
	}
	if len(t.Rows) > 1 {
		result = t.Render()
	}
	return
}

func (this *TurtleController) checkMarginRateForCTP() {
	if !this.IsExchange(exchange.CTP) {
		return
	}

	var balance float64
	var marginRequired float64
	for _, tt := range this.Turtles {
		if tt.Status != RUNNING {
			continue
		}
		if !tt.isTrading() {
			continue
		}
		if balance == 0 && tt.Balance.Wallet != 0 {
			balance = tt.Balance.Wallet
		}

		marginRequired += tt.calculateEstimatedMargin()
	}

	minRatio := this.Config.MinMarginRatio
	if minRatio == 0 {
		minRatio = 1
	}

	if balance < (marginRequired * minRatio) {
		this.WarnMsgf("账号保证金不足, 当前余额[%.0f] < 保证金[%.0f] * %.0f%%", balance, marginRequired, minRatio*100)
	}
}

func (this *TurtleController) SaveConfigsTo(turtleID string) error {
	if err := this.Config.SaveTo(this.ConfigPath, turtleID, false); err == nil {
		for _, tt := range this.Turtles {
			if err := tt.Config.SaveTo(this.ConfigPath, fmt.Sprintf("%s_%s", turtleID, tt.SymbolCode), false); err != nil {
				return err
			}
		}
	} else {
		return err
	}
	return nil
}

// 停止运行，并删除海龟对应的配置文件
// stopAll 和 launched 设为 false
func (this *TurtleController) DangerousDelete() {
	this.Close()
	this.Config.Delete()
	for _, tt := range this.Turtles {
		tt.Config.Delete()
	}
}

func (this *TurtleController) Close() {
	this.BaseController.Close(func() {
		this.stopAllTurtles()
	})
}

func (this *TurtleController) removeTurtleBySymbolCode(symbolCode *exchange.SymbolCode) {
	if existingTurtle, ok := this.Turtles[symbolCode.Code]; ok {
		existingTurtle.commandProcessor = nil
		delete(this.Turtles, symbolCode.Code)
	}
}

func (this *TurtleController) removeAndStartNewTurtle(symbolCode *exchange.SymbolCode) *Turtle {
	lastID := ""
	if existingTurtle, ok := this.Turtles[symbolCode.Code]; ok {
		this.removeTurtleBySymbolCode(symbolCode)
		lastID = existingTurtle.ID
	}
	symbolCode.USDXSymbol = this.Config.USDXSymbol
	if newTurtle, err := NewTurtle(this, symbolCode, nil, nil, true); err != nil {
		if lastID == "" {
			this.Errorf("start new %s turtle error: %s", symbolCode, err)
		} else {
			this.Errorf("start new %s turtle after (%s) error: %s", symbolCode, lastID, err)
		}
		return nil
	} else {
		this.Turtles[symbolCode.Code] = newTurtle
		this.storage.AddTurtle(newTurtle)
		this.storage.Save()
		if lastID != "" {
			this.Infof("start new %s turtle after (%s)", symbolCode, lastID)
		}
		return newTurtle
	}
}

func (this *TurtleController) GetPositionRows(assets string) (rows [][]string) {
	rows = [][]string{}
	for _, tt := range this.Turtles {
		if tt.Status == RUNNING {
			if assets == "" || utils.CSVContains(assets, tt.SymbolCode.Coin(), ",") {
				rows = append(rows, tt.GetPositionRow(false))
			}
		}
	}
	return
}

func (this *TurtleController) GetHistory(symbolCodes string) []*Turtle {
	tts := []*Turtle{}
	for _, tt := range this.storage.Turtles {
		if tt.Finish.Time == nil {
			continue
		}
		if symbolCodes == "" || utils.CSVContains(symbolCodes, tt.SymbolCode.Code, ",") {
			tts = append(tts, tt)
		}
	}
	return tts
}

func (this *TurtleController) handleSymbolAutoRotate() {
	if this.Exchange.GetName() != exchange.CTP {
		return
	}
	// 检查并且执行展期安排
	for _, schedule := range this.rotateSchedules {
		if time.Since(schedule.scheduleTime) > 0 && !schedule.isCanceled {
			instrument, err := this.Exchange.GetInstrument(schedule.from.InstrumentType(), schedule.from.Code)
			if err != nil {
				// 非交易时间获取不到 Instrument 是比较常见的，不用 Errorf
				this.Infof("check auto rotate, get instrument for schedule from symbol failed, (%s), error: %s", schedule.from.Code, err)
				continue
			}
			if instrument.Status == exchange.InstrumentStatusContinuous {
				err := this.rotateSymbol(schedule.from, schedule.to, true)
				if err != nil {
					this.Errorf("auto rotate failed, (%s) -> (%s), error: %s", schedule.from, schedule.to, err)
					return
				}
				delete(this.rotateSchedules, schedule.id)
			}
		}
		for _, tt := range this.Turtles {
			if tt.SymbolCode.Code == schedule.from.Code && schedule.isCanceled {
				this.SendMsgf("品种 [%s] 的展期安排 [%s] 已经取消，请手工展期。", schedule.from, schedule.id)
			}
		}
	}

	for _, tt := range this.Turtles {
		scheduleExist := false
		for _, schedule := range this.rotateSchedules {
			if schedule.from.Code == tt.SymbolCode.Code {
				scheduleExist = true
			}
		}
		// 如果展期安排已经存在，跳过
		if scheduleExist {
			continue
		}
		instrument, err := this.Exchange.GetInstrument(tt.InstrumentType, tt.Symbol)
		if err != nil {
			// 非交易时间获取不到 Instrument 是比较常见的，不用 Errorf
			this.Infof("check auto rotate, get instrument for turtle symbol failed, (%s), error: %s", tt.Symbol, err)
			continue
		}
		// 不在交易时间不检查是否需要展期
		if instrument.Status != exchange.InstrumentStatusContinuous && instrument.Status != exchange.InstrumentStatusExpiredSoon {
			this.Debugf("check auto rotate, symbol is not trading, continue: (%s)", instrument.Symbol)
			continue
		}
		nextSymbolCodes, err := this.Exchange.GetNextSymbolCodes(tt.InstrumentType, tt.SymbolCode, 1)
		if err != nil {
			this.Errorf("check auto rotate, get next symbol codes failed, (%s), error: %s", tt.Symbol, err)
			continue
		}
		// nextSymbolCodes 是按时间顺序排的，一个个按顺序检查
		for _, nextCode := range nextSymbolCodes {
			futureSymbol, err := this.Exchange.TranslateSymbolCodeToFutureSymbol(nextCode)
			if err != nil {
				this.Errorf("check auto rotate, translate next symbol code to future code failed, (%s) error: %s", nextCode, err)
				continue
			}
			nextInstrument, err := this.Exchange.GetInstrument(tt.InstrumentType, futureSymbol)
			if err != nil {
				this.Errorf("check auto rotate, get instrument for next symbol future code failed, (%s), error: %s", futureSymbol, err)
				continue
			}
			// 如果下一个品种合约成交量大于当前品种，加入展期安排，1个小时后执行
			// 发送消息后，可以用 cancelRotate 命令取消安排，此后只能手工展期
			if nextInstrument.Volume > instrument.Volume {
				scheduleID := exchange.NewRandomID()
				this.rotateSchedules[scheduleID] = RotateSchedule{
					id:           scheduleID,
					from:         tt.SymbolCode,
					to:           nextCode,
					scheduleTime: time.Now().Add(1 * time.Hour),
				}
				this.SendMsgf("展期安排 [%s]：1个小时后，品种 %s -> %s，取消请输入 `.cancelRotate %s`", scheduleID, tt.SymbolCode, nextCode, scheduleID)
				break
			} else {
				this.Debugf("check auto rotate, next symbol code volume is smaller, continue: (%s)", nextCode)
			}
		}
	}
}

func (this *TurtleController) formatAmount(asset string, amount float64) string {
	places := -1
	if this.IsExchange(exchange.BitMEX) {
		places = 4
	} else if this.IsExchange(exchange.Binance) {
		places = 2
	} else if this.IsExchange(exchange.OKEx) {
		places = 2
	}
	if strings.EqualFold(asset, this.Config.USDXSymbol) || strings.EqualFold(asset, "USDT") || strings.EqualFold(asset, "USD") {
		places = 2
	} else if strings.EqualFold(asset, "CNY") {
		places = 0
	}

	if places == -1 {
		// 最多 8 位小数
		strAmount := strconv.FormatFloat(amount, 'f', -1, 64)
		parts := strings.Split(strAmount, ".")
		if len(parts) == 2 && len(parts[1]) > 8 {
			return parts[0] + "." + parts[1][:8]
		}
		return strAmount
	}

	return strconv.FormatFloat(amount, 'f', places, 64)
}

func (this *TurtleController) GetBaseConfig() *baseconfig.BaseConfig {
	return &this.Config.BaseConfig
}

func (this *TurtleController) SaveConfig(configStr string) (correctedConfigStr string, er error) {
	err := this.Config.setConfigByString(configStr)
	if err != nil {
		this.ErrorMsgf("更新配置错误：%s", err)
		return "", err
	}
	return configStr, nil
}

func (this *TurtleController) GetStorage() base.Storager {
	return this.storage
}

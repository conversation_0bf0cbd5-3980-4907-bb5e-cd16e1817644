package turtle

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type TurtleCommand struct {
	command.Command
	turtle *Turtle
}

var SummaryHeader = []string{"Symbol", "ID", "Open Time", "Finish Time", "Hold Days", "Unit#", "Position", "Open Asset", "Finish Asset", "PNL", "PNL %", "Comment"}
var DetailSummaryHeader = []string{"Trender", "Symbol", "ID", "Open Time", "Finish Time", "Hold Days", "Unit#", "Position", "Open Asset", "Finish Asset", "PNL", "PNL %", "Comment"}
var PositionChangesHeader = []string{"Position", "Units", "Qty", "Entry Price", "Liq. Price", "PNL", "Locked PNL", "Update Time"}
var DetailPositionChangesHeader = []string{"Position", "Units", "Qty", "Entry Price", "TP. Price", "SL. Price", "Mark Price", "Last Price", "Liq. Price", "Locked PNL", "PNL", "Real. PNL", "Un. PNL", "Asset", "Asset Price", "Update Time"}
var OrdersHeader = []string{"Side", "Unit", "Qty", "Value", "Est. Loss", "Exec. Qty", "Open", "Slippage", "Exec Price", "Trigger Price", "Order Price", "Update Time", "Order ID"}
var PositionHeader = []string{"Position", "Units", "Qty", "Value", "Entry Price", "Liq. Price", "Mark Price", "Est. PNL", "Locked PNL", "Est. Loss", "Update Time"}
var DetailPositionHeader = []string{"Position", "Units", "Qty", "Entry Price", "TP. Price", "SL. Price", "Mark Price", "Last Price", "Liq. Price", "Locked PNL", "Est. PNL", "PNL", "Real. PNL", "Un. PNL", "Asset", "Asset Price", "Update Time"}
var DetailOrdersHeader = []string{"Side", "Unit", "Qty", "Value", "Est. Loss", "Exec. Qty", "Open", "Slippage", "Exec Price", "Trigger Price", "Order Price", "Create Time", "Update Time", "Order ID"}
var SetHistoryHeader = []string{"Time", "Field", "Value", "Comment"}

type StatusTurtleCommand TurtleCommand

func NewStatusTurtleCommand(turtle *Turtle) *StatusTurtleCommand {
	cmd := &StatusTurtleCommand{
		Command: command.Command{
			Name:            "status",
			Alias:           []string{"s"},
			Instruction:     "`.status detail(可选)` 查看当前运行状态",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *StatusTurtleCommand) Do() bool {
	detail := false
	if len(this.Args) == 1 && strings.EqualFold(this.Args[0], "detail") {
		detail = true
	}
	this.turtle.sendStatus(detail)
	this.turtle.handlePositionAndOrders()
	return true
}

type KlineStatusTurtleCommand TurtleCommand

func NewKlineStatusTurtleCommand(turtle *Turtle) *KlineStatusTurtleCommand {
	cmd := &KlineStatusTurtleCommand{
		Command: command.Command{
			Name:            "klineStatus",
			Alias:           []string{"ks"},
			Instruction:     "`.klineStatus` 查看 K 线状态",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *KlineStatusTurtleCommand) Do() bool {
	t := NewTable()
	t.AddRow([]string{"UpdateTime", utils.FormatShortTimeStr(&this.turtle.klineStatus.updateTime, true)})
	count := len(this.turtle.klineStatus.klines)
	t.AddRow([]string{"Count", fmt.Sprintf("%d", count)})
	f := this.turtle.klineStatus.firstCandleTime()
	t.AddRow([]string{"FirstCandle", utils.FormatShortTimeStr(&f, true)})
	l := this.turtle.klineStatus.lastCandleTime()
	t.AddRow([]string{"LastCandle", utils.FormatShortTimeStr(&l, true)})
	e := this.turtle.klineStatus.expireTime()
	t.AddRow([]string{"ExpiredAt", utils.FormatShortTimeStr(&e, true)})
	this.SendMsgf("K线状态：\n```%s```", t.Render())

	printNum := int(math.Max(float64(this.turtle.Open.BreakoutPeriod), float64(this.turtle.Config.ATRPeriod)) + 2)
	klineStr := this.turtle.klineStatus.klines.ToTable(printNum)
	if klineStr != "" {
		this.SendMsgf("K线列表：\n```%s```", klineStr)
	} else {
		this.SendMsgf("[ 空K线 ]")
	}

	return true
}

// 打印程序运行参数

type ParametersTurtleCommand TurtleCommand

func NewParametersTurtleCommand(turtle *Turtle) *ParametersTurtleCommand {
	cmd := &ParametersTurtleCommand{
		Command: command.Command{
			Name:            "config",
			Alias:           []string{"cfg"},
			Instruction:     "`.config` 查看当前运行参数",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *ParametersTurtleCommand) Do() bool {
	if cfg, err := baseconfig.RenderTable(this.turtle.Config); err != nil {
		this.turtle.Errorf("render config for %s error: %s", this.turtle.ID, err)
		this.ErrorMsgf("打印配置出错 %s", err)
		return false
	} else {
		this.turtle.SendMsgf("Build: %s, 运行参数:\n```%s```", this.turtle.BuildInfo(), cfg)
		return true
	}
}

// 启动当前品种命令

type ResumeTurtleCommand TurtleCommand

func NewResumeTurtleCommand(turtle *Turtle) *ResumeTurtleCommand {
	cmd := &ResumeTurtleCommand{
		Command: command.Command{
			Name:            "resume",
			Instruction:     "`.resume GoogleAuthCode` 继续运行",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *ResumeTurtleCommand) Do() bool {
	// 开启程序
	if this.turtle.Status != RUNNING {
		this.turtle.setStatus(RUNNING)
		this.turtle.handlePositionAndOrders()
		this.turtle.SendMsgf("已重新开启")
		this.turtle.sendStatus(false)
	} else {
		this.turtle.ErrorMsgf("已在运行中")
		return false
	}
	return true
}

// 暂停当前品种命令

type PauseTurtleCommand struct {
	TurtleCommand
	cancelOrders bool
}

func NewPauseTurtleCommand(turtle *Turtle) *PauseTurtleCommand {
	cmd := &PauseTurtleCommand{
		TurtleCommand: TurtleCommand{
			Command: command.Command{
				Name:            "pause",
				Instruction:     "`.pause cancel(取消当前订单，可选，无持仓时始终取消)` 暂停运行",
				RequiresConfirm: true,
				ArgMin:          0,
				ArgMax:          1,
			},
			turtle: turtle,
		},
	}
	return cmd
}

func (this *PauseTurtleCommand) Prepare() bool {
	this.cancelOrders = false
	if len(this.Args) == 1 {
		if this.Args[0] == "cancel" {
			this.cancelOrders = true
		} else {
			this.ErrorMsgf("参数错误")
			return false
		}
	}

	if this.turtle.Position.Qty != 0 {
		t := NewTable()
		t.SetHeader(PositionHeader)
		t.AddRow(this.turtle.GetPositionRow(false))
		this.SendMsgf("当前持仓:\n```%s```", t.Render())
	} else {
		this.SendMsgf("当前无持仓")
		this.cancelOrders = true
	}

	if this.cancelOrders {
		ot := NewTable()
		ot.SetHeader([]string{"Side", "Unit", "Qty", "Trigger Price", "Order Price", "Update Time", "Order ID"})
		orders := OrderList{}

		if this.turtle.Position.Qty > 0 {
			orders = append(orders, this.turtle.Long.OpenOrders...)
			orders = append(orders, this.turtle.CloseOrders...)
		} else if this.turtle.Position.Qty < 0 {
			orders = append(orders, this.turtle.Short.OpenOrders...)
			orders = append(orders, this.turtle.CloseOrders...)
		} else {
			orders = append(orders, this.turtle.Long.OpenOrders...)
			orders = append(orders, this.turtle.Short.OpenOrders...)
		}

		for _, o := range orders {
			if !o.IsOpen() {
				continue
			}
			orderUnit := o.GetInt(ExtKeyUnit)
			ot.AddRow([]string{
				o.GetString(ExtKeyCategory),
				fmt.Sprintf("%d", orderUnit),
				this.turtle.controller.Exchange.FormatDisplayQty(this.turtle.InstrumentType, this.turtle.Symbol, o.Qty),
				this.turtle.formatPrice(o.TriggerPrice),
				this.turtle.formatPrice(o.Price),
				utils.FormatShortTimeStr(o.UpdateTime, false),
				o.OrderID,
			})
		}
		if len(ot.Rows) > 1 {
			this.SendMsgf("将取消当前订单:\n```%s```", ot.Render())
		} else {
			this.SendMsgf("当前无订单。")
		}
	} else {
		this.WarnMsgf("无 cancel 参数，将不会取消当前订单")
	}

	return true
}

func (this *PauseTurtleCommand) Do() bool {
	this.turtle.setStatus(PAUSED)
	this.turtle.SendMsgf("已暂停运行")

	if this.cancelOrders {
		_, err := this.turtle.controller.Exchange.CancelAllOrders(this.turtle.InstrumentType, this.turtle.OrderType(), this.turtle.Symbol)
		if err == nil {
			this.turtle.SendMsgf("订单已全部取消")
		} else {
			this.turtle.AlertMsgf("取消订单失败")
			return false
		}
	}
	return true
}

// 快速平仓当前品种命令

type CloseTurtleCommand TurtleCommand

func NewCloseTurtleCommand(turtle *Turtle) *CloseTurtleCommand {
	cmd := &CloseTurtleCommand{
		Command: command.Command{
			Name:            "close",
			Instruction:     "`.close GoogleAuthCode` 闪电平仓，取消所有订单，并暂停运行",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *CloseTurtleCommand) Do() bool {
	this.turtle.closePositionAndStop(FinishReasonClose)
	return true
}

// 设置配置项命令

type SetConfigTurtleCommand TurtleCommand

func NewSetConfigTurtleCommand(turtle *Turtle) *SetConfigTurtleCommand {
	cmd := &SetConfigTurtleCommand{
		Command: command.Command{
			Name:            "setConfig",
			Alias:           []string{"sc"},
			Instruction:     "`.setConfig Field1=Value1,Field2=Value2` 设置配置 e.g. .setConfig PeriodHour=12",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *SetConfigTurtleCommand) Prepare() bool {
	configStr := this.Args[0]
	if !strings.Contains(configStr, "=") {
		this.ErrorMsgf("请按 field=value 设置配置。")
		return false
	}

	if _, correctedConfigStr, err := baseconfig.ParseConfigsFromString(this.turtle.Config, configStr); err != nil {
		this.ErrorMsgf("解析配置错误：%s", err)
		return false
	} else {
		this.SendMsgf("设置配置: %s", correctedConfigStr)
		return true
	}
}

func (this *SetConfigTurtleCommand) Do() bool {
	configStr := this.Args[0]
	if _, _, err := this.turtle.Config.setConfigByString(configStr); err != nil {
		this.SendMsgf("配置更新错误：%s", err)
		return false
	} else {
		this.SendMsgf("配置更新成功。")
		return true
	}
}

// 设置外部资金的命令

type SetExternalBalanceTurtleCommand TurtleCommand

func NewSetExternalBalanceTurtleCommand(turtle *Turtle) *SetExternalBalanceTurtleCommand {
	cmd := &SetExternalBalanceTurtleCommand{
		Command: command.Command{
			Name:            "setExternalBalance",
			Alias:           []string{"seb"},
			Instruction:     "`.setExternalBalance amount` 设置趋势机外部余额 e.g. .setExternalBalance 1.23",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *SetExternalBalanceTurtleCommand) Prepare() bool {
	amount, err := strconv.ParseFloat(this.Args[0], 64)
	if err != nil {
		this.turtle.ErrorMsgf("请输入有效的数量: %s", err)
		return false
	}

	if (this.turtle.Balance.Margin + this.turtle.Balance.External) > 0 {
		actualBalance := this.turtle.Balance.Margin
		ratio := this.turtle.Config.OpenSizePercent * 5
		if (actualBalance / ratio) < (actualBalance + amount) {
			this.turtle.WarnMsgf("当前账号余额已不足总资金的 %.1f%%，后续可能开仓失败。", ratio*100)
		}
	}
	return true
}

func (this *SetExternalBalanceTurtleCommand) Do() bool {
	amount, _ := strconv.ParseFloat(this.Args[0], 64)
	this.turtle.updateExternalBalance(amount, false)
	return true
}

type FixPositionCommand TurtleCommand

func NewFixPositionCommand(turtle *Turtle) *FixPositionCommand {
	cmd := &FixPositionCommand{
		Command: command.Command{
			Name:            "fixPosition",
			Instruction:     "`.fixPosition stopPrice comment` 使用指定止损价修复当前仓位，-1 时设置 ExitByDC 为 true，0 表示保持原有止损价",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          999,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *FixPositionCommand) Prepare() bool {
	stopPrice, err := strconv.ParseFloat(this.Args[0], 64)
	if err != nil {
		this.turtle.ErrorMsgf("请输入正确的价格")
		return false
	}

	// 实时获取持仓
	position, err := this.turtle.getCurrentPosition(false)
	if position == nil {
		this.turtle.ErrorMsgf("获取当前持仓失败, error: %s", err)
		return false
	}

	positionQty := position.Qty
	if positionQty == 0 {
		this.turtle.ErrorMsgf("当前无持仓")
		return false
	}

	if ok, msg := this.turtle.checkOpenPositionData(); !ok {
		this.turtle.ErrorMsgf("开仓数据检测不通过: %s", msg)
		return false
	}

	msg := fmt.Sprintf("\n当前品种: *%v*", this.turtle.Symbol)

	if this.turtle.Status != RUNNING {
		msg += "\n海龟未启动，确认后将开启"
	}

	if stopPrice == -1 {
		msg += "\nstopPrice 值为 -1，将设置 ExitByDC 为 true"
		this.turtle.SendMsgf(msg)
		return true
	} else if stopPrice == 0 {
		positionStopPrice := this.turtle.Position.GetFloat64(ExtKeyStopLossPrice)
		if positionStopPrice == 0 {
			this.turtle.ErrorMsgf("当前持仓无止损价，无法使用原有止损价修复")
			return false
		}
		this.turtle.SendMsgf(fmt.Sprintf("\nstopPrice 值为 0，将保持原有止损价 %s", this.turtle.formatPrice(positionStopPrice)))
		stopPrice = positionStopPrice
	}

	positionPrice := position.EntryPrice
	if positionQty > 0 && stopPrice >= positionPrice {
		this.turtle.ErrorMsgf("止损价不能高于持仓价")
		return false
	} else if positionQty < 0 && stopPrice <= positionPrice {
		this.turtle.ErrorMsgf("止损价不能低于持仓价")
		return false
	}

	pnl := this.turtle.calculateProfitOrderPNL(stopPrice, positionPrice, positionQty)
	msg += fmt.Sprintf("\n当前持仓价: %v", this.turtle.formatPrice(positionPrice)) +
		fmt.Sprintf("\n止损价差: %.2f%%", (stopPrice-positionPrice)/positionPrice*100) +
		fmt.Sprintf("\n止损预计亏损: %v", this.turtle.formatAmount(pnl))
	this.turtle.SendMsgf("%s", msg)
	return true
}

func (this *FixPositionCommand) Do() bool {
	price, _ := strconv.ParseFloat(this.Args[0], 64)
	if price == 0 {
		price = this.turtle.Position.GetFloat64(ExtKeyStopLossPrice)
	}
	comment := ""
	for _, s := range this.Args[1:] {
		comment += s + " "
	}
	this.turtle.Fix.StopPrice = price
	this.turtle.Fix.PositionComment = comment
	now := time.Now()
	this.turtle.Fix.FixPositionTime = &now
	if price == -1 {
		this.turtle.ExitByDC = true
	}

	if this.turtle.Status != RUNNING {
		this.turtle.setStatus(RUNNING)
	}

	_, err := this.turtle.controller.Exchange.CancelAllOrders(this.turtle.InstrumentType, this.turtle.OrderType(), this.turtle.Symbol)
	if err == nil {
		this.SendMsgf("当前订单已全部取消")
	} else {
		this.AlertMsgf("取消订单失败")
	}

	this.turtle.addSetHistory("Fix.Position", fmt.Sprintf("%v", this.turtle.Position.Qty), fmt.Sprintf("StopPrice=%v,Comment=%s", price, comment))

	this.turtle.handlePositionAndOrders()
	this.turtle.SendMsgf("设置成功")
	this.turtle.sendStatus(false)
	return true
}

type GetPositionTurtleCommand TurtleCommand

func NewGetPositionTurtleCommand(turtle *Turtle) *GetPositionTurtleCommand {
	cmd := &GetPositionTurtleCommand{
		Command: command.Command{
			Name:            "position",
			Alias:           []string{"p"},
			Instruction:     "`.position detail` 获取持仓",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *GetPositionTurtleCommand) Prepare() bool {
	return true
}

func (this *GetPositionTurtleCommand) Do() bool {
	balanceMsg := "[No Balance]"
	bt := NewTable()
	bt.SetHeader([]string{"", "Wallet", "Margin", "External", "OtherPNL", "Total"})
	balanceRows := this.turtle.GetBalanceRows()
	for _, row := range balanceRows {
		bt.AddRow(row)
	}
	if len(balanceRows) > 0 {
		balanceMsg = bt.Render()
	}

	positionMsg := "[No position]"
	pt := NewTable()
	pt.SetHeader(PositionHeader)
	pt.AddRow(this.turtle.GetPositionRow(false))
	positionMsg = pt.Render()

	orderMsg := "[No orders]"
	ot := NewTable()
	ot.SetHeader(OrdersHeader)
	orderRows := [][]string{}

	if this.turtle.Position.Qty > 0 {
		orderRows = append(orderRows, this.turtle.GetOrderRows(this.turtle.Long.OpenOrders, false, false)...)
	} else if this.turtle.Position.Qty < 0 {
		orderRows = append(orderRows, this.turtle.GetOrderRows(this.turtle.Short.OpenOrders, true, false)...)
	} else {
		orderRows = append(orderRows, this.turtle.GetOrderRows(this.turtle.Long.OpenOrders, false, false)...)
		orderRows = append(orderRows, this.turtle.GetOrderRows(this.turtle.Short.OpenOrders, true, false)...)
	}

	orderRows = append(orderRows, this.turtle.GetOrderRows(this.turtle.CloseOrders, false, false)...)
	for _, row := range orderRows {
		ot.AddRow(row)
	}
	if len(orderRows) > 0 {
		orderMsg = ot.Render()
	}

	if len(this.Args) > 0 && strings.EqualFold(this.Args[0], "detail") {
		positionChangeMsg := "[No Position Changes]"
		pt := NewTable()
		pt.SetHeader(PositionChangesHeader)
		positionRows := this.turtle.GetPositionChangesRows(false)
		for _, row := range positionRows {
			pt.AddRow(row)
		}
		if len(positionRows) > 0 {
			positionChangeMsg = pt.Render()
		}

		this.SendFileMessage(fmt.Sprintf("%s Positions", this.turtle.ID), fmt.Sprintf("Build: %s\n\n%s\n\n%s\n\n%s\n\n%s", this.turtle.BuildInfo(), balanceMsg, positionMsg, orderMsg, positionChangeMsg), "")
	} else {
		this.SendMsgf("```Build: %s\nID: %s\nStatus: %s\nIsTrading: %s\n\n%s\n\n%s\n\n%s```", this.turtle.BuildInfo(), this.turtle.ID, this.turtle.Status, fmt.Sprintf("%v", this.turtle.isTrading()), balanceMsg, positionMsg, orderMsg)
	}

	return true
}

type ReportTurtleCommand TurtleCommand

func NewReportTurtleCommand(turtle *Turtle) *ReportTurtleCommand {
	cmd := &ReportTurtleCommand{
		Command: command.Command{
			Name:            "report",
			Alias:           []string{"r"},
			Instruction:     "`.report ID[可选]` 打印完整报告",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *ReportTurtleCommand) Prepare() bool {
	return true
}

func (this *ReportTurtleCommand) Do() bool {
	var tt *Turtle
	// 仅允许打印当前品种的趋势机
	if len(this.Args) > 0 {
		turtleID := this.Args[0]
		for _, t := range this.turtle.controller.storage.Turtles {
			if t.Symbol == this.turtle.Symbol && t.ID == this.Args[0] {
				tt = t
			}
		}
		if tt == nil {
			this.ErrorMsgf("当前品种下没有找到趋势机ID：%s", turtleID)
			return false
		}
	} else {
		tt = this.turtle
	}

	reportMsg := tt.RenderReport()
	this.SendFileMessage(fmt.Sprintf("%s report", tt.ID), reportMsg, "")
	return true
}

type HistoryTurtleCommand TurtleCommand

func NewHistoryTurtleCommand(turtle *Turtle) *HistoryTurtleCommand {
	cmd := &HistoryTurtleCommand{
		Command: command.Command{
			Name:            "history",
			Alias:           []string{"his"},
			Instruction:     "`.history(+)` 打印趋势机历史",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
		},
		turtle: turtle,
	}
	return cmd
}

func (this *HistoryTurtleCommand) Prepare() bool {
	return true
}

func (this *HistoryTurtleCommand) Do() bool {
	t := NewTable()
	t.SetHeader(SummaryHeader)

	// 倒序打印，最新的 turtle 在最上面
	for i := len(this.turtle.controller.storage.Turtles) - 1; i >= 0; i-- {
		tt := this.turtle.controller.storage.Turtles[i]
		if tt.Symbol == this.turtle.Symbol {
			t.AddRow(tt.GetSummaryRow(false))
		}
	}
	hisMsg := "[No History]"
	if len(t.Rows) > 1 {
		hisMsg = t.Render()
	}
	this.SendMsgf("```%s```", hisMsg)
	return true
}

type ShadowCommand struct {
	command.Command
	turtle *Turtle
	config *SymbolConfig
}

func NewShadowCommand(turtle *Turtle) *ShadowCommand {
	cmd := &ShadowCommand{
		Command: command.Command{
			Name:            "shadow",
			Instruction:     "`.shadow` 模拟运行当前趋势机",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		turtle: turtle,
	}
	return cmd
}

// func (this *ShadowCommand) Prepare() bool {
// 	config := &SymbolConfig{}
// 	copier.Copy(config, this.turtle.Config)
// 	if len(this.Args) == 1 {
// 		configStr := this.Args[0]
// 		if !strings.Contains(configStr, "=") {
// 			this.ErrorMsgf("请按 field=value 设置配置。")
// 			return false
// 		}
// 		if _, _, err := baseconfig.ParseConfigsFromString(config, configStr); err != nil {
// 			this.ErrorMsgf("解析配置错误：%s", err)
// 			return false
// 		}
// 		if _, _, err := baseconfig.SetConfigWithString(config, configStr); err != nil {
// 			this.ErrorMsgf("设置配置错误：%s", err)
// 			return false
// 		}
// 	}
// 	if cfg, err := baseconfig.RenderTable(config); err != nil {
// 		this.ErrorMsgf("打印配置出错 %s", err)
// 		return false
// 	} else {
// 		this.SendMsgf("模拟运行参数:\n```%s```", cfg)
// 		this.config = config
// 		return true
// 	}
// }

func (this *ShadowCommand) Do() bool {
	config := &SymbolConfig{}
	copier.Copy(config, this.turtle.Config)
	if cfg, err := baseconfig.RenderTable(config); err != nil {
		this.ErrorMsgf("打印配置出错 %s", err)
		return false
	} else {
		this.SendMsgf("模拟运行参数:\n```%s```", cfg)
		this.config = config
	}

	if this.turtle.Status != RUNNING {
		this.turtle.getBalance()
	}

	toTime := time.Now()
	periods := math.Max(float64(this.config.BreakoutPeriod), float64(this.config.SecondaryBreakoutPeriod))
	fromTime := toTime.Add(-time.Duration(periods*float64(this.config.PeriodHour)) * time.Hour)

	sim := &Simulator{
		controller: this.turtle.controller,
		fromTime:   &fromTime,
		toTime:     &toTime,
		symbolCode: this.turtle.SymbolCode,
		config:     this.config,
		fixedCash:  this.turtle.Balance.Total,
	}

	_, _, err := sim.Simulate()
	if err != nil {
		this.ErrorMsgf("模拟运行出错: %s", err)
	}

	sim.SendStatus(this.turtle)
	return true
}

/* 具体实现 */

func (this *Turtle) sendStatus(detail bool) {
	msg := this.getStatusMsg(detail)
	if detail {
		this.SendFileMessage(fmt.Sprintf("Trender Status %s", this.ID), msg, "")
	} else {
		this.SendMsgf("```%s```", msg)
	}
}

func (this *Turtle) getStatusMsg(detail bool) string {
	t := NewTable()
	for _, row := range this.getStatusDetailRows(detail) {
		t.AddRow(row)
	}
	return t.Render()
}

func (this *Turtle) getStatusDetailRows(moreDetail bool) [][]string {
	rows := [][]string{}
	ignoredExitKLines := ""
	if len(this.Fix.IgnoredExitKLines) > 0 {
		ignoredExitKLines = fmt.Sprintf("\nIgnoredExitKLines: *%v*", this.Fix.IgnoredExitKLines)
	}

	rows = append(rows, []string{"Build", this.BuildInfo()})
	rows = append(rows, []string{"ID", this.ID})
	rows = append(rows, []string{"Status", this.Status})
	rows = append(rows, []string{"SymbolCode", this.SymbolCode.Code})
	rows = append(rows, []string{"Symbol", this.Symbol})
	rows = append(rows, []string{"IsTrading", fmt.Sprintf("%v", this.isTrading())})
	rows = append(rows, []string{"ATR", this.formatPrice(this.Open.ATR.Value)})
	rows = append(rows, []string{"OpenSizePercent", fmt.Sprintf("%.2f%%", this.Config.OpenSizePercent*100)})
	rows = append(rows, []string{"MaxOpenUnits", fmt.Sprintf("%d", this.Config.MaxOpenUnits)})
	rows = append(rows, []string{"BreakoutPeriod", fmt.Sprintf("%d", this.Open.BreakoutPeriod)})
	rows = append(rows, []string{"Balance.Wallet", fmt.Sprintf("%s %s", this.formatAmount(this.Balance.Wallet), this.Balance.Asset)})
	rows = append(rows, []string{"Balance.Total", fmt.Sprintf("%s %s", this.formatAmount(this.Balance.Total), this.Balance.Asset)})
	rows = append(rows, []string{"Position.Qty", this.controller.Exchange.FormatDisplayQty(this.InstrumentType, this.Symbol, this.Position.Qty)})
	value := this.Qty2Size(this.Symbol, this.Position.EntryPrice, this.Position.Qty)
	rows = append(rows, []string{"Position.Value", fmt.Sprintf("%s %s", this.formatAmount(value), this.Balance.Asset)})
	rows = append(rows, []string{"Units", fmt.Sprintf("%d", this.Position.GetInt(ExtKeyUnits))})
	rows = append(rows, []string{"Est. Loss", fmt.Sprintf("%s %s %.2f%%", this.formatAmount(this.EstimatedLoss.Position), this.Position.GetString(ExtKeyAsset), this.EstimatedLoss.Position*100/this.Open.Balance.Total)})
	rows = append(rows, []string{"Max Est. Loss", fmt.Sprintf("%s %s %.2f%%", this.formatAmount(this.GetMaxEstimatedLoss()), this.Position.GetString(ExtKeyAsset), this.GetMaxEstimatedLoss()*100/this.Open.Balance.Total)})
	rows = append(rows, []string{"PNL", fmt.Sprintf("%s %s %.2f%%", this.formatAmount(this.Position.GetFloat64(ExtKeyPNL)), this.Position.GetString(ExtKeyAsset), this.Position.GetFloat64(ExtKeyPNL)*100/this.Open.Balance.Total)})
	rows = append(rows, []string{"LockedPNL", fmt.Sprintf("%s %s", this.formatAmount(this.Position.GetFloat64(ExtKeyLockedPNL)), this.Position.GetString(ExtKeyAsset))})
	rows = append(rows, []string{"ExitByDC", fmt.Sprintf("%v", this.ExitByDC)})
	rows = append(rows, []string{"StopOnExit", fmt.Sprintf("%v", this.StopOnExit)})
	if this.PauseTo != nil {
		rows = append(rows, []string{"PauseTo", utils.FormatShortTimeStr(this.PauseTo, false)})
		rows = append(rows, []string{"PauseReason", string(this.PauseReason)})
	}
	rows = append(rows, []string{"CreateTime", utils.FormatShortTimeStr(this.CreateTime, false)})
	rows = append(rows, []string{"LastOrderUpdatedAt", utils.FormatShortTimeStr(&this.LastOrderUpdatedAt, false)})
	rows = append(rows, []string{"LastHandleSuccessAt", utils.FormatShortTimeStr(&this.LastHandleSuccessAt, false)})

	if moreDetail {
		rows = append(rows, []string{"", ""})
		rows = append(rows, []string{"Open", ""})
		rows = append(rows, []string{"    BreakoutPeriod", fmt.Sprintf("%d", this.Open.BreakoutPeriod)})
		rows = append(rows, []string{"    ATR", this.formatPrice(this.Open.ATR.Value)})
		rows = append(rows, []string{"    ATR.ExpiredAt", utils.FormatShortTimeStr(this.Open.ATR.ExpiredAt, false)})
		rows = append(rows, []string{"    ATR.ManualUpdated", fmt.Sprintf("%v", this.Open.ATR.ManualUpdated)})
		rows = append(rows, []string{"    Balance.Total", fmt.Sprintf("%s %s", this.formatAmount(this.Open.Balance.Total), this.Open.Balance.Asset)})
		rows = append(rows, []string{"    Balance.Margin", fmt.Sprintf("%s %s", this.formatAmount(this.Open.Balance.Margin), this.Open.Balance.Asset)})
		rows = append(rows, []string{"    Balance.Wallet", fmt.Sprintf("%s %s", this.formatAmount(this.Open.Balance.Wallet), this.Open.Balance.Asset)})
		rows = append(rows, []string{"    Balance.External", fmt.Sprintf("%s %s", this.formatAmount(this.Open.Balance.External), this.Open.Balance.Asset)})
		rows = append(rows, []string{"    Balance.OtherPNL", fmt.Sprintf("%s %s", this.formatAmount(this.Open.Balance.OtherPNL), this.Open.Balance.Asset)})
		rows = append(rows, []string{"    Balance.AssetPrice", fmt.Sprintf("%s USD", this.controller.formatAmount("USD", this.Open.Balance.AssetPrice))})

		rows = append(rows, []string{"Long", ""})
		rows = append(rows, []string{"    BreakoutPrice", this.formatPrice(this.Long.BreakoutPrice.Value)})
		rows = append(rows, []string{"    BreakoutPrice.ExpiredAt", utils.FormatShortTimeStr(this.Long.BreakoutPrice.ExpiredAt, false)})
		rows = append(rows, []string{"    BreakoutPrice.ManualUpdated", fmt.Sprintf("%v", this.Long.BreakoutPrice.ManualUpdated)})
		rows = append(rows, []string{"    ExitPrice", this.formatPrice(this.Long.ExitPrice.Value)})
		rows = append(rows, []string{"    ExitPrice.ExpiredAt", utils.FormatShortTimeStr(this.Long.ExitPrice.ExpiredAt, false)})
		rows = append(rows, []string{"    ExitPrice.ManualUpdated", fmt.Sprintf("%v", this.Long.ExitPrice.ManualUpdated)})
		rows = append(rows, []string{"    OpenSize", this.formatAmount(this.Long.OpenSize)})
		rows = append(rows, []string{"    OpenPrice", this.formatPrice(this.Long.OpenPrice)})

		rows = append(rows, []string{"Short", ""})
		rows = append(rows, []string{"    BreakoutPrice", this.formatPrice(this.Short.BreakoutPrice.Value)})
		rows = append(rows, []string{"    BreakoutPrice.ExpiredAt", utils.FormatShortTimeStr(this.Short.BreakoutPrice.ExpiredAt, false)})
		rows = append(rows, []string{"    BreakoutPrice.ManualUpdated", fmt.Sprintf("%v", this.Short.BreakoutPrice.ManualUpdated)})
		rows = append(rows, []string{"    ExitPrice", this.formatPrice(this.Short.ExitPrice.Value)})
		rows = append(rows, []string{"    ExitPrice.ExpiredAt", utils.FormatShortTimeStr(this.Short.ExitPrice.ExpiredAt, false)})
		rows = append(rows, []string{"    ExitPrice.ManualUpdated", fmt.Sprintf("%v", this.Short.ExitPrice.ManualUpdated)})
		rows = append(rows, []string{"    OpenSize", this.formatAmount(this.Short.OpenSize)})
		rows = append(rows, []string{"    OpenPrice", this.formatPrice(this.Short.OpenPrice)})

		rows = append(rows, []string{"Position", ""})
		rows = append(rows, []string{"    Qty", this.controller.Exchange.FormatDisplayQty(this.InstrumentType, this.Symbol, this.Position.Qty)})
		rows = append(rows, []string{"    Units", fmt.Sprintf("%d", this.Position.GetInt(ExtKeyUnits))})
		rows = append(rows, []string{"    TakeProfitPrice", this.formatPrice(this.Position.GetFloat64(ExtKeyTakeProfitPrice))})
		rows = append(rows, []string{"    StopLossPrice", this.formatPrice(this.Position.GetFloat64(ExtKeyStopLossPrice))})
		rows = append(rows, []string{"    EntryPrice", this.formatPrice(this.Position.EntryPrice)})
		rows = append(rows, []string{"    MarkPrice", this.formatPrice(this.Position.MarkPrice)})
		rows = append(rows, []string{"    LastPrice", this.formatPrice(this.Position.LastPrice)})
		rows = append(rows, []string{"    LiquidationPrice", this.formatPrice(this.Position.LiquidationPrice)})
		rows = append(rows, []string{"    LockedPNL", this.formatAmount(this.Position.GetFloat64(ExtKeyLockedPNL))})
		rows = append(rows, []string{"    PNL", this.formatAmount(this.Position.GetFloat64(ExtKeyPNL))})
		rows = append(rows, []string{"    RealisedPNL", this.formatAmount(this.Position.RealisedPNL)})
		rows = append(rows, []string{"    UnrealisedPNL", this.formatAmount(this.Position.UnrealisedPNL)})
		rows = append(rows, []string{"    Asset", this.Position.GetString(ExtKeyAsset)})
		rows = append(rows, []string{"    AssetPrice", this.formatPrice(this.Position.GetFloat64(ExtKeyAssetPrice))})
		rows = append(rows, []string{"    UpdateTime", utils.FormatShortTimeStr(this.Position.UpdateTime, false)})

		rows = append(rows, []string{"Balance", ""})
		rows = append(rows, []string{"    Total", fmt.Sprintf("%s %s", this.formatAmount(this.Balance.Total), this.Balance.Asset)})
		rows = append(rows, []string{"    Margin", fmt.Sprintf("%s %s, %.2f%%", this.formatAmount(this.Balance.Margin), this.Balance.Asset, this.Balance.Margin*100/this.Balance.Total)})
		rows = append(rows, []string{"    Wallet", fmt.Sprintf("%s %s", this.formatAmount(this.Balance.Wallet), this.Balance.Asset)})
		rows = append(rows, []string{"    External", fmt.Sprintf("%s %s", this.formatAmount(this.Balance.External), this.Balance.Asset)})
		rows = append(rows, []string{"    OpenSizeMargin", fmt.Sprintf("%s %s", this.formatAmount(this.Balance.OpenSizeMargin), this.Balance.Asset)})
		rows = append(rows, []string{"    OtherPNL", fmt.Sprintf("%s %s", this.formatAmount(this.Balance.OtherPNL), this.Balance.Asset)})
		rows = append(rows, []string{"    AssetPrice", fmt.Sprintf("%s USD", this.controller.formatAmount("USD", this.Balance.AssetPrice))})

		rows = append(rows, []string{"Fix", ""})
		rows = append(rows, []string{"    StopPrice", this.formatPrice(this.Fix.StopPrice)})
		rows = append(rows, []string{"    PositionQty", this.controller.Exchange.FormatDisplayQty(this.InstrumentType, this.Symbol, this.Fix.PositionQty)})
		rows = append(rows, []string{"    PositionComment", this.Fix.PositionComment})
		rows = append(rows, []string{"    IgnoredExitKLines", ignoredExitKLines})
		rows = append(rows, []string{"    StopLossAdjust", this.formatPrice(this.Fix.StopLossAdjust)})
		rows = append(rows, []string{"    TakeProfitAdjust", this.formatPrice(this.Fix.TakeProfitAdjust)})
	}
	return rows
}

func (this *Turtle) updateExternalBalance(amount float64, isChange bool) {
	origExternalBalance := this.Config.ExternalBalance
	if isChange {
		amount = this.Config.ExternalBalance + amount
	}
	this.Balance.External = amount
	this.Config.ExternalBalance = amount
	this.Config.Save()
	this.addSetHistory("Config.ExternalBalance", fmt.Sprintf("%.2f", amount), fmt.Sprintf("old value: %.2f", origExternalBalance))

	this.controller.storage.Save()
	this.getBalance()
	this.SendMsgf("修改 ExternalBalance 成功")
	this.sendStatus(false)
	this.handlePositionAndOrders()
}

func (this *Turtle) createMarketCloseOrder(side exchange.OrderSide, qty float64) (*Order, error) {
	qty = math.Abs(qty)
	args := exchange.CreateOrderArgs{
		InstrumentType: this.InstrumentType,
		Symbol:         this.Symbol,
		Side:           side,
		Type:           exchange.Market,
		ReduceOnly:     true,
	}
	if this.needCloseOrderQty() {
		args.Qty = qty
	}
	return this.controller.Exchange.CreateOrder(args)
}

// 平仓，取消所有订单，并暂停运行
func (this *Turtle) closePositionAndStop(finishReason FinishReason) {
	position, err := this.getCurrentPosition(false)
	if position == nil {
		this.AlertMsgf("获取 %s 当前持仓失败, error: %s", this.Symbol, err)
		return
	}

	positionSide := position.Side

	if position.Qty != 0 {
		if this.controller.Config.ExchangeName == exchange.OKEx {
			// OK 需先撤销平仓单
			this.controller.Exchange.CancelAllOrders(this.InstrumentType, this.OrderType(), this.Symbol)
		}
		orderSide := exchange.OrderSideBuy
		if positionSide == exchange.PositionSideLong {
			orderSide = exchange.OrderSideSell
		}
		order, err := this.createMarketCloseOrder(orderSide, position.Qty)
		if err != nil {
			this.AlertMsgf("创建 %s 平仓单失败: %v", this.Symbol, err)
			return
		}
		ordType := OrderCategoryStopLoss
		if position.UnrealisedPNL > 0 {
			ordType = OrderCategoryTakeProfit
		}
		order = SetOrderExt(order, ordType, 0)
		this.CloseOrders = append(this.CloseOrders, order)
		this.ExecOrders = append(this.ExecOrders, order)

		time.Sleep(time.Second * 3)
		for i := 0; i < 3; i++ {
			position, err = this.getCurrentPosition(false)
			if position == nil {
				this.AlertMsgf("获取 %s 当前持仓失败, error: %s", this.Symbol, err)
				return
			}
			if position.Qty == 0 {
				break
			}
			time.Sleep(time.Second * 3)
		}

		if position.Qty == 0 {
			if finishReason == FinishReasonClose {
				// 用于判断是否为手工平仓，修改此处也需要同时修改 .report 命令中的打印判断条件
				this.addSetHistory("Position.Qty", "0", "close")
			}
			this.SendMsgf("%s 已全部平仓", this.Symbol)
		} else {
			this.AlertMsgf("%s 平仓未完成，剩余数量: %f", this.Symbol, position.Qty)
			return
		}

		this.Finish.Reason = finishReason // 先设置退出原因，需要用 handlePositionAndOrders 运行让 storage 数据正确
		this.handlePositionAndOrders()
		this.setStatus(PAUSED)
	} else {
		this.FinishWithReason(finishReason)
	}

	this.SendMsgf("已暂停运行")

	_, err = this.controller.Exchange.CancelAllOrders(this.InstrumentType, this.OrderType(), this.Symbol)
	if err == nil {
		this.SendMsgf("%s 订单已全部取消", this.Symbol)
	} else {
		this.AlertMsgf("%s 取消订单失败", this.Symbol)
	}
}

func GetFirestoreCollectionName(turtleID string) string {
	return fmt.Sprintf("turtle||%s", turtleID)
}

func (this *Turtle) GetBalanceRows() [][]string {
	rows := [][]string{}
	balanceRow := []string{}

	balanceRow = append(balanceRow, "Balance")
	balanceRow = append(balanceRow, fmt.Sprintf("%s %s", this.formatAmount(this.Balance.Wallet), this.Balance.Asset))
	balanceRow = append(balanceRow, fmt.Sprintf("%s %s", this.formatAmount(this.Balance.Margin), this.Balance.Asset))
	balanceRow = append(balanceRow, fmt.Sprintf("%s %s", this.formatAmount(this.Balance.External), this.Balance.Asset))
	balanceRow = append(balanceRow, fmt.Sprintf("%s %s", this.formatAmount(this.Balance.OtherPNL), this.Balance.Asset))
	balanceRow = append(balanceRow, fmt.Sprintf("%s %s", this.formatAmount(this.Balance.Total), this.Balance.Asset))
	rows = append(rows, balanceRow)

	// notionalRow := []string{}
	// if !strings.EqualFold(this.Balance.Asset, "USD") {
	// 	notionalRow = append(notionalRow, "Notional")
	// 	notionalRow = append(notionalRow, fmt.Sprintf("%.f USD", this.Balance.Wallet*this.Balance.AssetPrice))
	// 	notionalRow = append(notionalRow, fmt.Sprintf("%.f USD", this.Balance.Margin*this.Balance.AssetPrice))
	// 	notionalRow = append(notionalRow, fmt.Sprintf("%.f USD", this.Balance.External*this.Balance.AssetPrice))
	// 	notionalRow = append(notionalRow, fmt.Sprintf("%.f USD", this.Balance.OtherPNL*this.Balance.AssetPrice))
	// 	notionalRow = append(notionalRow, fmt.Sprintf("%.f USD", this.Balance.Total*this.Balance.AssetPrice))
	// 	rows = append(rows, notionalRow)
	// }

	return rows
}

func (this *Turtle) GetPositionChangesRows(detail bool) [][]string {
	rows := [][]string{}
	for i := len(this.PositionChanges) - 1; i >= 0; i-- {
		p := this.PositionChanges[i]

		changeRow := []string{}
		if detail {
			changeRow = append(changeRow, this.Symbol)
			changeRow = append(changeRow, fmt.Sprintf("%d", p.GetInt(ExtKeyUnits)))
			changeRow = append(changeRow, this.controller.Exchange.FormatDisplayQty(this.InstrumentType, this.Symbol, p.Qty))
			changeRow = append(changeRow, this.formatPrice(p.EntryPrice))
			changeRow = append(changeRow, this.formatPrice(p.GetFloat64(ExtKeyTakeProfitPrice)))
			changeRow = append(changeRow, this.formatPrice(p.GetFloat64(ExtKeyStopLossPrice)))
			changeRow = append(changeRow, this.formatPrice(p.MarkPrice))
			changeRow = append(changeRow, this.formatPrice(p.LastPrice))
			changeRow = append(changeRow, this.formatPrice(p.LiquidationPrice))
			changeRow = append(changeRow, this.formatAmount(p.GetFloat64(ExtKeyLockedPNL)))
			changeRow = append(changeRow, this.formatAmount(p.GetFloat64(ExtKeyPNL)))
			changeRow = append(changeRow, this.formatAmount(p.RealisedPNL))
			changeRow = append(changeRow, this.formatAmount(p.UnrealisedPNL))
			changeRow = append(changeRow, p.GetString(ExtKeyAsset))
			changeRow = append(changeRow, this.formatPrice(p.GetFloat64(ExtKeyAssetPrice)))
		} else {
			changeRow = append(changeRow, this.Symbol)
			changeRow = append(changeRow, fmt.Sprintf("%d", p.GetInt(ExtKeyUnits)))
			changeRow = append(changeRow, this.controller.Exchange.FormatDisplayQty(this.InstrumentType, this.Symbol, p.Qty))
			changeRow = append(changeRow, this.formatPrice(p.EntryPrice))
			changeRow = append(changeRow, this.formatPrice(p.LiquidationPrice))
			changeRow = append(changeRow, fmt.Sprintf("%.2f USD", p.GetFloat64(ExtKeyPNL)*p.GetFloat64(ExtKeyAssetPrice)))
			changeRow = append(changeRow, fmt.Sprintf("%.2f USD", p.GetFloat64(ExtKeyLockedPNL)*p.GetFloat64(ExtKeyAssetPrice)))
		}
		if p.UpdateTime != nil {
			changeRow = append(changeRow, utils.FormatShortTimeStr(p.UpdateTime, false))
		} else {
			changeRow = append(changeRow, "")
		}

		rows = append(rows, changeRow)
	}
	return rows
}

// 注意 Position 主要看 Est. PNL
// 实际通过 Real. PNL + Un. PNL 的 PNL 可能不是很准确，没什么太大意义
func (this *Turtle) GetPositionRow(detail bool) []string {
	p := this.Position
	row := []string{}
	if detail {
		row = append(row, this.Symbol)
		row = append(row, fmt.Sprintf("%d", p.GetInt(ExtKeyUnits)))
		row = append(row, this.controller.Exchange.FormatDisplayQty(this.InstrumentType, this.Symbol, p.Qty))
		row = append(row, this.formatPrice(p.EntryPrice))
		row = append(row, this.formatPrice(p.GetFloat64(ExtKeyTakeProfitPrice)))
		row = append(row, this.formatPrice(p.GetFloat64(ExtKeyStopLossPrice)))
		row = append(row, this.formatPrice(p.MarkPrice))
		row = append(row, this.formatPrice(p.LastPrice))
		row = append(row, this.formatPrice(p.LiquidationPrice))
		row = append(row, this.formatAmount(p.GetFloat64(ExtKeyLockedPNL)))

		// 分别打印通过 Position 计算的 Est. PNL 和从交易所直接获取到 PNL
		row = append(row, this.formatAmount(this.calculatePositionPNL(p)))
		row = append(row, this.formatAmount(p.GetFloat64(ExtKeyPNL)))

		row = append(row, this.formatAmount(p.RealisedPNL))
		row = append(row, this.formatAmount(p.UnrealisedPNL))
		row = append(row, p.GetString(ExtKeyAsset))
		row = append(row, this.formatPrice(p.GetFloat64(ExtKeyAssetPrice)))
	} else {
		row = append(row, this.Symbol)
		row = append(row, fmt.Sprintf("%d", p.GetInt(ExtKeyUnits)))
		row = append(row, this.controller.Exchange.FormatDisplayQty(this.InstrumentType, this.Symbol, p.Qty))
		if p.Qty != 0 {
			value := this.Qty2Size(this.Symbol, p.EntryPrice, p.Qty)
			row = append(row, fmt.Sprintf("%s %s", this.formatAmount(value), p.GetString(ExtKeyAsset)))
		} else {
			row = append(row, "0")
		}
		row = append(row, this.formatPrice(p.EntryPrice))

		if p.Qty > 0 {
			row = append(row, fmt.Sprintf("%s (%.2f%%)", this.formatPrice(p.LiquidationPrice), (p.LiquidationPrice/p.MarkPrice-1)*100))
			row = append(row, fmt.Sprintf("%s (%.2f%%)", this.formatPrice(p.MarkPrice), (p.MarkPrice/p.EntryPrice-1)*100))
		} else if p.Qty < 0 {
			row = append(row, fmt.Sprintf("%s (%.2f%%)", this.formatPrice(p.LiquidationPrice), (1-p.LiquidationPrice/p.MarkPrice)*100))
			row = append(row, fmt.Sprintf("%s (%.2f%%)", this.formatPrice(p.MarkPrice), (1-p.MarkPrice/p.EntryPrice)*100))
		} else {
			row = append(row, this.formatPrice(p.LiquidationPrice))
			if p.MarkPrice > 0 {
				row = append(row, this.formatPrice(p.MarkPrice))
			} else {
				instrument, err := this.controller.Exchange.GetInstrument(this.InstrumentType, this.Symbol)
				if err != nil || time.Since(instrument.UpdateTime) > 5*time.Second {
					row = append(row, this.formatPrice(0))
				} else {
					row = append(row, this.formatPrice(instrument.LastPrice))
				}
			}
		}
		pnl := this.calculatePositionPNL(p)
		row = append(row, fmt.Sprintf("%.2f USD %.2f%%", pnl*p.GetFloat64(ExtKeyAssetPrice), pnl*100/this.Open.Balance.Total))
		lockedPNL := p.GetFloat64(ExtKeyLockedPNL)
		if lockedPNL == 0 {
			row = append(row, "-")
			row = append(row, fmt.Sprintf("%.2f USD %.2f%%", this.EstimatedLoss.Position*p.GetFloat64(ExtKeyAssetPrice), this.EstimatedLoss.Position*100/this.Open.Balance.Total))
		} else {
			row = append(row, fmt.Sprintf("%.2f USD %.2f%%", lockedPNL*p.GetFloat64(ExtKeyAssetPrice), lockedPNL*100/this.Open.Balance.Total))
			row = append(row, "-")
		}
	}

	if p.UpdateTime != nil {
		row = append(row, utils.FormatShortTimeStr(p.UpdateTime, false))
	} else {
		row = append(row, "")
	}
	return row
}

func (this *Turtle) GetOrderRows(orders []*Order, reverse bool, detail bool) [][]string {
	rows := [][]string{}

	printingOrderList := []*Order{}
	// 根据 reverse 反向打印的订单顺序
	if len(orders) > 0 {
		if reverse {
			for i := len(orders) - 1; i >= 0; i-- {
				printingOrderList = append(printingOrderList, orders[i])
			}
		} else {
			printingOrderList = orders
		}
	}

	for i := len(printingOrderList) - 1; i >= 0; i-- {
		o := printingOrderList[i]
		orderRow := []string{}
		orderRow = append(orderRow, o.GetString(ExtKeyCategory))
		orderUnit := o.GetInt(ExtKeyUnit)
		orderRow = append(orderRow, fmt.Sprintf("%d", orderUnit))

		orderRow = append(orderRow, this.controller.Exchange.FormatDisplayQty(this.InstrumentType, this.Symbol, o.Qty))
		value := this.Qty2Size(this.Symbol, o.TriggerPrice, o.Qty)
		orderRow = append(orderRow, fmt.Sprintf("%s %s", this.formatAmount(value), this.Balance.Asset))
		estLoss := 0.0
		if o.GetString(ExtKeyCategory) == OrderCategoryLong && orderUnit > 0 {
			estLoss = this.EstimatedLoss.Longs[orderUnit-1]
		} else if o.GetString(ExtKeyCategory) == OrderCategoryShort && orderUnit > 0 {
			estLoss = this.EstimatedLoss.Shorts[orderUnit-1]
		}
		if estLoss != 0 {
			orderRow = append(orderRow, fmt.Sprintf("%s %s %.2f%%", this.formatAmount(estLoss), this.Balance.Asset, estLoss*100/this.Open.Balance.Total))
		} else {
			orderRow = append(orderRow, "")
		}

		orderRow = append(orderRow, this.controller.Exchange.FormatDisplayQty(this.InstrumentType, this.Symbol, o.ExecQty))
		isOpenStr := ""
		if o.IsOpen() {
			isOpenStr = "Yes"
		}
		orderRow = append(orderRow, isOpenStr)
		if o.Price > o.TriggerPrice && o.ExecPrice > 0 && o.TriggerPrice > 0 {
			orderRow = append(orderRow, fmt.Sprintf("%.2f%%", (1-o.ExecPrice/o.TriggerPrice)*100))
		} else if o.Price < o.TriggerPrice && o.ExecPrice > 0 && o.TriggerPrice > 0 {
			orderRow = append(orderRow, fmt.Sprintf("%.2f%%", (o.ExecPrice/o.TriggerPrice-1)*100))
		} else {
			orderRow = append(orderRow, "")
		}

		orderRow = append(orderRow, this.formatPrice(o.ExecPrice))
		orderRow = append(orderRow, this.formatPrice(o.TriggerPrice))
		orderRow = append(orderRow, this.formatPrice(o.Price))
		if detail {
			if o.CreateTime != nil {
				orderRow = append(orderRow, utils.FormatShortTimeStr(o.CreateTime, false))
			} else {
				orderRow = append(orderRow, "")
			}
		}

		if o.UpdateTime != nil {
			orderRow = append(orderRow, utils.FormatShortTimeStr(o.UpdateTime, false))
		} else {
			orderRow = append(orderRow, "")
		}

		orderRow = append(orderRow, o.OrderID)
		rows = append(rows, orderRow)
	}
	return rows
}

func (this *Turtle) GetSummaryRow(withTrender bool) []string {
	firstPosition, lastPosition, closingPosition := this.getFirstAndLastPosition()

	row := []string{}
	if withTrender {
		row = append(row, this.controller.ID)
	}
	row = append(row, this.Symbol) // Trender
	row = append(row, this.ID)     // ID
	if firstPosition != nil {
		row = append(row, utils.FormatShortTimeStr(firstPosition.UpdateTime, false)) // Open Time
	} else {
		row = append(row, "-")
	}
	var duration time.Duration
	if this.Finish.Time != nil {
		row = append(row, utils.FormatShortTimeStr(this.Finish.Time, false)) // Finish Time
		if firstPosition != nil {
			duration = this.Finish.Time.Sub(*firstPosition.UpdateTime) // 不使用 lastPosition ，lastPosition 在 confirmorders 情况下有可能和 firstPosition 相同
		}
	} else {
		row = append(row, "-")
		if firstPosition != nil {
			duration = time.Since(*firstPosition.UpdateTime)
		}
	}
	if firstPosition != nil {
		row = append(row, utils.FormatDuration(duration, "d"))                                                           // Holding days
		row = append(row, fmt.Sprintf("%d", lastPosition.GetInt(ExtKeyUnits)))                                           // Units
		row = append(row, this.controller.Exchange.FormatDisplayQty(this.InstrumentType, this.Symbol, lastPosition.Qty)) // Position
		row = append(row, fmt.Sprintf("%.2f USD", this.Open.Balance.Margin*this.Open.Balance.AssetPrice))                // Open Asset
	} else {
		row = append(row, "-")
		row = append(row, "0")
		row = append(row, "0")
		row = append(row, "-")
	}
	if this.Finish != nil && this.Finish.Time != nil && closingPosition != nil {
		row = append(row, fmt.Sprintf("%.2f USD", this.Finish.Balance.getMarginNotional())) // Finish Asset
		pnl := closingPosition.GetFloat64(ExtKeyPNL)

		if p, err := this.calculateExecOrdersPNL(); err != nil {
			row = append(row, fmt.Sprintf("%.2f* USD", pnl*closingPosition.GetFloat64(ExtKeyAssetPrice)))
		} else {
			row = append(row, fmt.Sprintf("%.2f USD", p*closingPosition.GetFloat64(ExtKeyAssetPrice)))
			// 以下代码用于调试 PNL 的计算是否正确
			// 对比直接计算和从 position 中获取的值
			// 	row = append(row, fmt.Sprintf("%.2f USD / %.2f", p*closingPosition.AssetPrice, pnl*closingPosition.AssetPrice))
			pnl = p
		}
		row = append(row, fmt.Sprintf("%.2f %%", pnl*100/this.Open.Balance.Total)) // PNL %
	} else {
		row = append(row, "-")
		pnl := 0.0
		if closingPosition == nil {
			if this.Position.Qty != 0 {
				pnl = this.calculatePositionPNL(this.Position)
			} else if lastPosition != nil {
				pnl = lastPosition.GetFloat64(ExtKeyPNL)
			}
		}
		if pnl != 0 {
			row = append(row, fmt.Sprintf("(%.2f* USD)", pnl*lastPosition.GetFloat64(ExtKeyAssetPrice)))
		} else {
			row = append(row, "0")
		}
		row = append(row, "-")
	}
	comment := ""
	for _, item := range this.SetHistory {
		if strings.EqualFold(item.Field, "Position.Qty") && strings.EqualFold(item.Comment, "close") {
			comment += "ManualClose"
		}
	}
	row = append(row, comment)

	return row
}

func (this *Turtle) GetSetHistoryRows() [][]string {
	rows := [][]string{}
	for _, his := range this.SetHistory {
		row := []string{}
		row = append(row, utils.FormatShortTimeStr(his.CreateTime, false))
		row = append(row, his.Field)
		row = append(row, his.Value)
		row = append(row, his.Comment)
		rows = append(rows, row)
	}
	return rows
}

func (this *Turtle) RenderReport() string {
	st := NewTable()
	st.SetHeader(DetailSummaryHeader)
	summaryRow := this.GetSummaryRow(true)
	st.AddRow(summaryRow)
	summaryMsg := st.Render()

	positionMsg := "[No position]"
	pt := NewTable()
	pt.SetHeader(DetailPositionHeader)
	pt.AddRow(this.GetPositionRow(true))
	positionMsg = pt.Render()

	positionChangeMsg := "[No Position Changes]"
	pct := NewTable()
	pct.SetHeader(DetailPositionChangesHeader)
	positionRows := this.GetPositionChangesRows(true)
	for _, row := range positionRows {
		pct.AddRow(row)
	}
	if len(positionRows) > 0 {
		positionChangeMsg = pct.Render()
	}

	orderMsg := "[No orders]"
	ot := NewTable()
	ot.SetHeader(DetailOrdersHeader)
	orderRows := [][]string{}
	orderRows = append(orderRows, this.GetOrderRows(this.Long.OpenOrders, false, true)...)
	orderRows = append(orderRows, this.GetOrderRows(this.Short.OpenOrders, true, true)...)
	orderRows = append(orderRows, this.GetOrderRows(this.CloseOrders, false, true)...)
	for _, row := range orderRows {
		ot.AddRow(row)
	}
	if len(orderRows) > 0 {
		orderMsg = ot.Render()
	}

	execOrderMsg := "[No Exec Orders]"
	eot := NewTable()
	eot.SetHeader(DetailOrdersHeader)
	execOrderRows := this.GetOrderRows(this.ExecOrders, false, true)
	for _, row := range execOrderRows {
		eot.AddRow(row)
	}
	if len(execOrderRows) > 0 {
		execOrderMsg = eot.Render()
	}

	takeOrderMsg := "[No Take Orders]"
	tot := NewTable()
	tot.SetHeader(DetailOrdersHeader)
	takeOrderRows := this.GetOrderRows(this.TakeOrders, false, true)
	for _, row := range takeOrderRows {
		tot.AddRow(row)
	}
	if len(takeOrderRows) > 0 {
		takeOrderMsg = tot.Render()
	}

	setHistoryMsg := "[No Set History]"
	sht := NewTable()
	sht.SetHeader(SetHistoryHeader)
	setHistoryRows := this.GetSetHistoryRows()
	for _, row := range setHistoryRows {
		sht.AddRow(row)
	}
	if len(setHistoryRows) > 0 {
		setHistoryMsg = sht.Render()
	}

	rotatePlanMsg := "[No RotatePlan]"
	if this.RotatePlan != nil {
		rotatePlanMsg = this.RotatePlan.Render()
	}

	configMsg := "[No Config]"
	if this.Config != nil {
		if c, err := baseconfig.RenderTable(this.Config); err != nil {
			configMsg = fmt.Sprintf("[ERROR:render config error: %s]", err)
		} else {
			configMsg = c
		}
	}

	statusMsg := "[No Status]"
	stt := NewTable()
	statusRows := this.getStatusDetailRows(true)
	if len(statusRows) > 0 {
		for _, row := range statusRows {
			stt.AddRow(row)
		}
		statusMsg = stt.Render()
	}

	return fmt.Sprintf("摘要\n%s\n\n当前持仓\n%s\n\n持仓变动历史\n%s\n\n挂单\n%s\n\n挂单成交历史\n%s\n\n吃单历史\n%s\n\n设置历史记录\n%s\n\n展期原始数据\n%s\n\n配置快照\n%s\n\n其他内部数据\n%s", summaryMsg, positionMsg, positionChangeMsg, orderMsg, execOrderMsg, takeOrderMsg, setHistoryMsg, rotatePlanMsg, configMsg, statusMsg)
}

func (this *Turtle) GetStatusRow(withTrender bool) []string {
	row := []string{}
	if withTrender {
		row = append(row, this.controller.ID)
	}
	row = append(row, this.SymbolCode.Code)
	row = append(row, this.Status)
	if this.isTrading() {
		row = append(row, "Yes")
	} else {
		row = append(row, "No")
	}
	row = append(row, this.controller.Exchange.FormatDisplayQty(this.InstrumentType, this.Symbol, this.Position.Qty))
	value := this.Qty2Size(this.Symbol, this.Position.EntryPrice, this.Position.Qty)
	row = append(row, fmt.Sprintf("%s %s", this.formatAmount(value), this.Balance.Asset)) // Position.Value
	row = append(row, fmt.Sprintf("%d", this.Position.GetInt(ExtKeyUnits)))               // Units
	pnl := this.calculatePositionPNL(this.Position)
	row = append(row, fmt.Sprintf("%.2f USD", pnl*this.Position.GetFloat64(ExtKeyAssetPrice)))

	lockedPNL := this.Position.GetFloat64(ExtKeyLockedPNL)
	if lockedPNL == 0 {
		row = append(row, "-")
	} else {
		row = append(row, fmt.Sprintf("%.2f USD %.2f%%", lockedPNL*this.Position.GetFloat64(ExtKeyAssetPrice), lockedPNL*100/this.Open.Balance.Total))
	}

	if this.EstimatedLoss.Position != 0 && lockedPNL == 0 {
		row = append(row, fmt.Sprintf("%s %s %.2f%%", this.formatAmount(this.EstimatedLoss.Position), this.Position.GetString(ExtKeyAsset), this.EstimatedLoss.Position*100/this.Open.Balance.Total)) // Est. Loss
	} else {
		row = append(row, "-")
	}
	if this.GetMaxEstimatedLoss() != 0 {
		row = append(row, fmt.Sprintf("%s %s %.2f%%", this.formatAmount(this.GetMaxEstimatedLoss()), this.Position.GetString(ExtKeyAsset), this.GetMaxEstimatedLoss()*100/this.Open.Balance.Total)) // Max Est. Loss
	} else {
		row = append(row, "-")
	}

	return row
}

package turtle

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type ControllerCommand struct {
	command.Command
	controller *TurtleController
}

// 禁声所有海龟的 alert 消息
type MuteControllerCommand ControllerCommand

func NewMuteControllerCommand(controller *TurtleController) *MuteControllerCommand {
	cmd := &MuteControllerCommand{
		Command: command.Command{
			Name:            "mute",
			Instruction:     "`.mute minutes AuthCode` 临时禁声【警告性消息】，不超过 7 天",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		controller: controller,
	}
	return cmd
}

func (this *MuteControllerCommand) Do() bool {
	if m, err := strconv.ParseInt(this.Args[0], 10, 32); err == nil {
		minutes := cmds.MaxMute
		if m < cmds.MaxMute {
			minutes = m
		}
		expireTime := this.controller.Mute(minutes)
		for _, tt := range this.controller.Turtles {
			tt.Mute(minutes)
		}
		this.SendMsgf("[警告类消息] 禁声到：%s", expireTime)
		return true
	} else {
		return false
	}
}

// 启动程序
type LaunchControllerCommand ControllerCommand

func NewLaunchControllerCommand(controller *TurtleController) *LaunchControllerCommand {
	cmd := &LaunchControllerCommand{
		Command: command.Command{
			Name:            "launch",
			Instruction:     "`.launch Password GoogleAuthCode resumeAll/pauseAll` 启动程序, 并运行或暂停所有品种(可选)",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          3,
			AuthcodePos:     -1, // launch 命令不能在解密主密码前校验 authcode，所以把 authCode 当成是普通参数处理，跳过统一的校验流程
			Sensitive:       true,
		},
		controller: controller,
	}
	return cmd
}

func (this *LaunchControllerCommand) Do() bool {
	password := this.Args[0]
	authCode := this.Args[1]
	resumeOrPause := ""

	if len(this.Args) == 3 {
		resumeOrPause = strings.ToLower(this.Args[2])
		if resumeOrPause != "resumeall" && resumeOrPause != "pauseall" {
			this.controller.ErrorMsgf("参数 resumeAll/pauseAll 错误，请输入 resumeAll 或 pauseAll")
			return false
		}
	}
	success := this.controller.Launch(password, authCode)
	if success {
		go this.controller.InitStartTurtles(resumeOrPause)
		this.SendMsgf("启动成功。")
	}
	return true
}

// 当前程序运行状态
type StatusControllerCommand ControllerCommand

func NewStatusControllerCommand(controller *TurtleController) *StatusControllerCommand {
	cmd := &StatusControllerCommand{
		Command: command.Command{
			Name:            "status",
			Alias:           []string{"s"},
			Instruction:     "`.status` 查看运行状态",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *StatusControllerCommand) Do() bool {
	this.controller.sendStatus(true)
	return true
}

type MarginRatioControllerCommand ControllerCommand

func NewMarginRatioControllerCommand(controller *TurtleController) *MarginRatioControllerCommand {
	cmd := &MarginRatioControllerCommand{
		Command: command.Command{
			Name:            "marginRatio",
			Alias:           []string{"mr"},
			Instruction:     "`.marginRatio` 查看保证金比例",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *MarginRatioControllerCommand) Do() bool {
	s := this.controller.getOpenMarginSummary(false)
	if s == "" {
		this.SendMsgf("没有运行中的品种")
	} else {
		this.SendMsgf("```%s```", s)
	}
	return true
}

type PositionControllerCommand ControllerCommand

func NewPositionControllerCommand(controller *TurtleController) *PositionControllerCommand {
	cmd := &PositionControllerCommand{
		Command: command.Command{
			Name:            "position",
			Alias:           []string{"p"},
			Instruction:     "`.position Asset1,Asset2[可选]` 查看运行状态",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *PositionControllerCommand) Do() bool {
	positionMsg := "[No Positions]"
	assets := ""
	if len(this.Args) == 1 {
		assets = this.Args[0]
	}

	t := NewTable()
	t.SetHeader(PositionHeader)
	for _, row := range this.controller.GetPositionRows(assets) {
		t.AddRow(row)
	}
	if len(t.Rows) > 1 {
		positionMsg = t.Render()
	}
	msg := fmt.Sprintf("\nPosition:\n```%s```", positionMsg)
	mrMsg := this.controller.getOpenMarginSummary(false)
	if mrMsg != "" {
		msg = fmt.Sprintf("%s\nMarginRatio:\n```%s```", msg, mrMsg)
	}
	this.SendMsgf("%s", msg)
	return true
}

// 打印程序运行参数
type ParametersControllerCommand ControllerCommand

func NewParametersControllerCommand(controller *TurtleController) *ParametersControllerCommand {
	cmd := &ParametersControllerCommand{
		Command: command.Command{
			Name:            "config",
			Alias:           []string{"cfg"},
			Instruction:     "`.config` 查看当前运行参数",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ParametersControllerCommand) Do() bool {
	this.controller.SendMsgf("Build: %s, 运行参数:\n```%s```", this.controller.BuildInfo(), this.controller.Config.ToTable())
	return true
}

type HistoryControllerCommand ControllerCommand

func NewHistoryControllerCommand(controller *TurtleController) *HistoryControllerCommand {
	cmd := &HistoryControllerCommand{
		Command: command.Command{
			Name:            "history",
			Alias:           []string{"his"},
			Instruction:     "`.history(+) SymbolCode1,SymbolCode2[可选]` 打印趋势机历史",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          2,
		},
		controller: controller,
	}
	return cmd
}

func (this *HistoryControllerCommand) Do() bool {
	symbolCodes := ""
	if len(this.Args) == 1 {
		symbolCodes = this.Args[0]
	}
	t := NewTable()
	t.SetHeader([]string{"Symbol", "ID", "Open Time", "Finish Time", "Hold Days", "Unit#", "Position", "Open Asset", "Finish Asset", "PNL", "PNL %", "Comment"})

	// 倒序打印，最新的 turtle 在最上面
	for i := len(this.controller.storage.Turtles) - 1; i >= 0; i-- {
		tt := this.controller.storage.Turtles[i]
		if symbolCodes == "" || utils.CSVContains(symbolCodes, tt.SymbolCode.Code, ",") {
			t.AddRow(tt.GetSummaryRow(false))
		}
	}
	hisMsg := "[No History]"
	if len(t.Rows) > 1 {
		hisMsg = t.Render()
	}
	this.SendMsgf("```%s```", hisMsg)
	return true
}

type CleanControllerCommand ControllerCommand

func NewCleanControllerCommand(controller *TurtleController) *CleanControllerCommand {
	cmd := &CleanControllerCommand{
		Command: command.Command{
			Name:            "clean",
			Instruction:     "`.clean` 清理历史记录，删除没有开仓但是已经结束的记录",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
		},
		controller: controller,
	}
	return cmd
}

func (this *CleanControllerCommand) Do() bool {
	ids := []string{}
	if len(this.Args) > 0 {
		ids = strings.Split(this.Args[0], ",")
	}
	for _, symbol := range this.controller.Config.Symbols {
		if symbolCode, err := this.controller.NewSymbolCode(symbol); err == nil {
			this.controller.storage.FinishDuplicateTurtles(symbolCode)
		}

		for _, tt := range this.controller.storage.Turtles {
			firstPosition, _, _ := tt.getFirstAndLastPosition()
			if firstPosition == nil && tt.Finish != nil && tt.Finish.Time != nil {
				ids = append(ids, tt.ID)
			}
		}
	}
	removedIDs := this.controller.storage.RemoveTurtleWithIDs(ids)
	this.SendMsgf("已清理：%s", strings.Join(removedIDs, ", "))
	return true
}

type RemoveHistoryControllerCommand ControllerCommand

func NewRemoveHistoryControllerCommand(controller *TurtleController) *RemoveHistoryControllerCommand {
	cmd := &RemoveHistoryControllerCommand{
		Command: command.Command{
			Name:            "removeHistory",
			Instruction:     "`.removeHistory IDs` 删除历史记录中的某条记录",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
		},
		controller: controller,
	}
	return cmd
}

func (this *RemoveHistoryControllerCommand) Prepare() bool {
	ids := []string{}
	if len(this.Args) > 0 {
		ids = strings.Split(this.Args[0], ",")
	}
	t := NewTable()
	t.SetHeader([]string{"Symbol", "ID", "Open Time", "Finish Time", "Hold Days", "Unit#", "Position", "Open Asset", "Finish Asset", "PNL", "PNL %", "Comment"})
	for _, symbol := range this.controller.Config.Symbols {
		if symbolCode, err := this.controller.NewSymbolCode(symbol); err == nil {
			this.controller.storage.FinishDuplicateTurtles(symbolCode)
		}
	}
	for _, tt := range this.controller.storage.Turtles {
		for _, id := range ids {
			if tt.ID == id {
				t.AddRow(tt.GetSummaryRow(false))
			}
		}
	}
	removeListMsg := "[No Trenders]"

	if len(t.Rows) > 1 {
		removeListMsg = t.Render()
	}
	this.SendMsgf("请确认以下要删除的历史记录：\n```%s```", removeListMsg)
	return true
}

func (this *RemoveHistoryControllerCommand) Do() bool {
	ids := []string{}
	if len(this.Args) > 0 {
		ids = strings.Split(this.Args[0], ",")
	}
	for _, symbol := range this.controller.Config.Symbols {
		if symbolCode, err := this.controller.NewSymbolCode(symbol); err == nil {
			this.controller.storage.FinishDuplicateTurtles(symbolCode)
		}
	}
	removedIDs := this.controller.storage.RemoveTurtleWithIDs(ids)
	this.SendMsgf("已删除：%s", strings.Join(removedIDs, ","))
	return true
}

type ReportControllerCommand ControllerCommand

func NewReportControllerCommand(controller *TurtleController) *ReportControllerCommand {
	cmd := &ReportControllerCommand{
		Command: command.Command{
			Name:            "report",
			Alias:           []string{"r"},
			Instruction:     "`.report ID` 打印完整报告",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ReportControllerCommand) Do() bool {
	var tt *Turtle
	// 仅允许打印当前品种的趋势机
	turtleID := this.Args[0]
	for _, t := range this.controller.storage.Turtles {
		if t.ID == this.Args[0] {
			tt = t
		}
	}
	if tt == nil {
		this.ErrorMsgf("没有找到趋势机ID：%s", turtleID)
		return false
	}

	reportMsg := tt.RenderReport()
	this.SendFileMessage(fmt.Sprintf("%s report", tt.ID), reportMsg, "")
	return true
}

// 启动所有品种命令
type ResumeAllControllerCommand ControllerCommand

func NewResumeAllControllerCommand(controller *TurtleController) *ResumeAllControllerCommand {
	cmd := &ResumeAllControllerCommand{
		Command: command.Command{
			Name:            "resumeAll",
			Instruction:     "`.resumeAll GoogleAuthCode` 继续运行所有品种",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ResumeAllControllerCommand) Do() bool {
	for _, tt := range this.controller.Turtles {
		if tt.Status != RUNNING {
			tt.setStatus(RUNNING)
			tt.handlePositionAndOrders()
			tt.SendMsgf("%s 已重新开启", tt.Symbol)
		} else {
			tt.SendMsgf("%s 已在运行中", tt.Symbol)
		}
	}

	this.controller.SendMsgf("操作完成")
	return true
}

// 暂停所有品种命令

type PauseAllControllerCommand ControllerCommand

func NewPauseAllControllerCommand(controller *TurtleController) *PauseAllControllerCommand {
	cmd := &PauseAllControllerCommand{
		Command: command.Command{
			Name:            "pauseAll",
			Instruction:     "`.pauseAll GoogleAuthCode` 暂停运行所有品种",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
		controller: controller,
	}
	return cmd
}

func (this *PauseAllControllerCommand) Do() bool {
	this.controller.stopAllTurtles()
	return true
}

// 设置配置项命令

type SetConfigControllerCommand ControllerCommand

func NewSetConfigControllerCommand(controller *TurtleController) *SetConfigControllerCommand {
	cmd := &SetConfigControllerCommand{
		Command: command.Command{
			Name:            "setConfig",
			Alias:           []string{"sc"},
			Instruction:     "`.setConfig Field1=Value1,Field2=Value2` 设置配置并覆盖到所有品种的配置 e.g. .setConfig PeriodHour=12",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
		},
		controller: controller,
	}
	return cmd
}

func (this *SetConfigControllerCommand) Prepare() bool {
	configStr := this.Args[0]
	if !strings.Contains(configStr, "=") {
		this.ErrorMsgf("请按 field=value 设置配置。")
		return false
	}

	if _, correctedConfigStr, err := baseconfig.ParseConfigsFromString(this.controller.Config, configStr); err != nil {
		this.ErrorMsgf("解析配置错误：%s", err)
		return false
	} else {
		this.SendMsgf("设置所有品种配置: %s", correctedConfigStr)
		return true
	}
}

func (this *SetConfigControllerCommand) Do() bool {
	configStr := this.Args[0]
	_, err := this.controller.SaveConfig(configStr)
	if err != nil {
		this.ErrorMsgf("更新配置错误：%s", err)
		return false
	}
	this.SendMsgf("配置更新成功。")
	return true
}

// 设置外部资金的命令

type SetExternalBalanceControllerCommand ControllerCommand

func NewSetExternalBalanceControllerCommand(controller *TurtleController) *SetExternalBalanceControllerCommand {
	cmd := &SetExternalBalanceControllerCommand{
		Command: command.Command{
			Name:            "setExternalBalance",
			Alias:           []string{"seb"},
			Instruction:     "`.setExternalBalance amount currency[可选]` 设置保证金币种相同的趋势机的外部余额",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          2,
		},
		controller: controller,
	}
	return cmd
}

func (this *SetExternalBalanceControllerCommand) Prepare() bool {
	amount, err := strconv.ParseFloat(this.Args[0], 64)
	if err != nil {
		this.ErrorMsgf("请输入有效的数量: %s", err)
		return false
	}

	coin := ""
	if len(this.Args) == 2 {
		coin = this.Args[1]
		coin = strings.ToUpper(coin)
		if coin != "XBT" && coin != this.controller.Config.USDXSymbol {
			this.ErrorMsgf("无效的参数 coin: %s", coin)
			return false
		}
	} else {
		if this.controller.Config.ExchangeName == exchange.BitMEX {
			coin = this.controller.Config.USDXSymbol
		}
	}

	for _, tt := range this.controller.Turtles {
		if this.controller.Config.ExchangeName == exchange.BitMEX {
			instrument, _ := this.controller.Exchange.GetInstrument(tt.InstrumentType, tt.Symbol)
			if !strings.EqualFold(instrument.SettleCurrency, coin) {
				continue
			}
		}

		if (tt.Balance.Margin + tt.Balance.External) > 0 {
			actualBalance := tt.Balance.Margin
			ratio := tt.Config.OpenSizePercent * 5
			if (actualBalance / ratio) < (actualBalance + amount) {
				this.WarnMsgf("%s 账号余额已不足总资金的 %.1f%%，后续可能开仓失败。", tt.Symbol, ratio*100)
			}
		}
	}
	return true
}

func (this *SetExternalBalanceControllerCommand) Do() bool {
	amount, _ := strconv.ParseFloat(this.Args[0], 64)
	coin := ""
	if len(this.Args) == 2 {
		coin = this.Args[1]
		coin = strings.ToUpper(coin)
	} else {
		if this.controller.Config.ExchangeName == exchange.BitMEX {
			coin = this.controller.Config.USDXSymbol
		}
	}

	for _, tt := range this.controller.Turtles {
		if this.controller.Config.ExchangeName == exchange.BitMEX {
			instrument, _ := this.controller.Exchange.GetInstrument(tt.InstrumentType, tt.Symbol)
			if !strings.EqualFold(instrument.SettleCurrency, coin) {
				continue
			}
		}
		tt.updateExternalBalance(amount, false)
	}
	this.controller.SendMsgf("操作完成")
	return true
}

type SetMinMarginRatioCommand ControllerCommand

func NewSetMinMarginRatioCommand(controller *TurtleController) *SetMinMarginRatioCommand {
	return &SetMinMarginRatioCommand{
		Command: command.Command{
			Name:            "setMinMarginRatio",
			Alias:           []string{"smr"},
			Instruction:     "`.setMinMarginRatio ratio GoogleAuthCode` 设置余额与保证金最小比例",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		controller: controller,
	}
}

func (this *SetMinMarginRatioCommand) Do() bool {
	ratio, _ := strconv.ParseFloat(this.Args[0], 64)
	if ratio <= 0 || ratio > 10 {
		this.controller.ErrorMsgf("ratio 需要在 0 ~ 10 以内")
		return false
	}
	this.controller.Config.MinMarginRatio = ratio
	this.controller.Config.Save()
	this.controller.SendMsgf("设置完成")
	return true
}

type CancelRotateControllerCommand ControllerCommand

func NewCancelRotateControllerCommand(controller *TurtleController) *CancelRotateControllerCommand {
	cmd := &CancelRotateControllerCommand{
		Command: command.Command{
			Name:            "cancelRotate",
			Instruction:     "`.cancelRotate scheduleID[可选]` 取消展期安排",
			RequiresConfirm: true,
			ArgMin:          0,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *CancelRotateControllerCommand) Prepare() bool {
	if len(this.Args) == 0 {
		t := exchange.NewTable()
		t.SetHeader([]string{"ScheduleID", "From", "To", "Schedule Time", "Is Canceled"})
		for id, s := range this.controller.rotateSchedules {
			t.AddRow([]string{id, s.from.Code, s.to.Code, utils.FormatShortTimeStr(&s.scheduleTime, false), fmt.Sprintf("%v", s.isCanceled)})
		}
		this.SendMsgf("当前展期安排：\n```%s```", t.Render())
		return false
	} else {
		sID := this.Args[0]
		t := exchange.NewTable()
		t.SetHeader([]string{"ScheduleID", "From", "To", "Schedule Time"})
		for id, s := range this.controller.rotateSchedules {
			if id == sID {
				if s.isCanceled {
					this.ErrorMsgf("展期安排 [%s] 已经被取消。", sID)
					return false
				}
				t.AddRow([]string{id, s.from.Code, s.to.Code, utils.FormatShortTimeStr(&s.scheduleTime, false)})
			}
		}
		if len(t.Rows) > 1 {
			this.SendMsgf("取消展期安排：\n```%s```", t.Render())
			return true
		}
		this.ErrorMsgf("没有找到展期安排：[%s]", sID)
		return false
	}
}

func (this *CancelRotateControllerCommand) Do() bool {
	schedule, found := this.controller.rotateSchedules[this.Args[0]]
	if found {
		schedule.isCanceled = true
	}
	this.SendMsgf("品种 [%s] 的展期安排 [%s] 已经取消，请手工展期。", schedule.from, schedule.id)
	return true
}

func (this *TurtleController) InitStartTurtles(resumeOrPause string) {
	apiSecret := this.GetApiSecret()
	var backOff backoff.BackOff
	if this.IsExchange(exchange.CTP) || this.IsExchange(exchange.MetaTrader) || this.IsExchange(exchange.InteractiveBrokers) {
		backOff = backoff.NewConstantBackOff(time.Second * 5)
	} else {
		exp := backoff.NewExponentialBackOff()
		exp.InitialInterval = time.Second * 10
		exp.MaxInterval = time.Minute * 5
		exp.MaxElapsedTime = 0 // It never stops if MaxElapsedTime == 0.
		backOff = exp
	}
	alerted := false
	backoff.Retry(func() error {
		if this.IsClosed() {
			return nil
		}
		err := this.InitAPI(apiSecret)
		if err != nil {
			this.Warnf("init api failed, error: %s", err)
			if !alerted { // 警报一次
				this.ErrorMsgf("初始化 API 错误，稍后将自动重试。错误信息：%s", err)
				alerted = true
			}
		} else {
			if !this.Config.IsDemo {
				this.SendMsgf("初始化 API 成功。")
			}
			if err := exchange.TestTranslateSymbolCode(this.Exchange); err != nil {
				this.ErrorMsgf("symbol code 测试不通过: %s", err)
			}
		}
		return err
	}, backOff)

	if this.IsClosed() {
		return
	}

	this.InitTurtles()

	if resumeOrPause == "resumeall" {
		for _, tt := range this.Turtles {
			tt.setStatus(RUNNING)
			tt.handlePositionAndOrders()
		}
	} else if resumeOrPause == "pauseall" {
		for _, tt := range this.Turtles {
			tt.setStatus(PAUSED)
		}
	} else {
		// 恢复 Turtle 保存状态
		for _, tt := range this.Turtles {
			status := tt.Status
			if status == "" {
				status = PAUSED
			}
			tt.setStatus(status)
			tt.handlePositionAndOrders()
		}
	}

	this.SendMsgf("启动成功")
	this.sendStatus(false)
}

func (this *TurtleController) renderTurtleDetails() string {
	t := NewTable()
	headers := []string{"Symbol Code", "Status", "Trading", "Position Qty", "Position Value", "Units", "PNL", "Locked PNL", "Est. Loss", "Max Est. Loss"}
	t.SetHeader(headers)

	for _, tt := range this.Turtles {
		row := tt.GetStatusRow(false)
		t.AddRow(row)
	}
	return t.Render()
}

func (this *TurtleController) RenderAssets() string {
	type AssetItem struct {
		time       time.Time
		totalAsset float64
		asset      float64
		open       string
		close      string
	}

	turtles := this.storage.Turtles

	// 倒序打印，最新的时间在最上面
	// 由于计算 lastExternalAsset 需要从较早的时间到较晚的时间排序，因此，在所有计算完成后再倒序排列
	keys := make([]int64, len(this.storage.Assets))
	i := 0
	for k := range this.storage.Assets {
		keys[i] = k
		i++
	}
	// sort ascendingly by time
	sort.SliceStable(keys, func(i, j int) bool { return keys[i] < keys[j] })

	type TurtleOpenClose struct {
		time   time.Time // open or close time
		isOpen bool
		turtle *Turtle
	}

	assetItemList := []AssetItem{}
	for i, k := range keys {
		assetItem := AssetItem{}
		snapshotTime := time.Unix(k, 0)
		assetItem.time = snapshotTime
		assetItem.asset = this.storage.Assets[k]
		assetItem.totalAsset = assetItem.asset
		assetItemList = append(assetItemList, assetItem)

		ealierTime := time.Unix(0, 0) //  如果是倒数第二个，将 Unit(0, 0) 设为更早时间
		if i > 0 {
			ealierTime = time.Unix(keys[i-1], 0)
		}
		// 根据时间查找，是否在 ealierTime 和 snapshotTime 之间有开平仓
		// 如果在，打印出响应的资产快照和ID
		turtleOpenCloseList := []TurtleOpenClose{} // 用于排序，以免将 sort 函数写得过于复杂
		for _, tt := range turtles {
			firstPosition, _, _ := tt.getFirstAndLastPosition()
			if tt.Finish.Time != nil && snapshotTime.Sub(*tt.Finish.Time) > 0 && tt.Finish.Time.Sub(ealierTime) > 0 {
				turtleOpenCloseList = append(turtleOpenCloseList, TurtleOpenClose{time: *tt.Finish.Time, isOpen: false, turtle: tt})
			}
			if firstPosition != nil && firstPosition.UpdateTime != nil && snapshotTime.Sub(*firstPosition.UpdateTime) > 0 && firstPosition.UpdateTime.Sub(ealierTime) > 0 {
				turtleOpenCloseList = append(turtleOpenCloseList, TurtleOpenClose{time: *firstPosition.UpdateTime, isOpen: true, turtle: tt})
			}
		}

		sort.SliceStable(turtleOpenCloseList, func(i, j int) bool {
			i1 := turtleOpenCloseList[i]
			i2 := turtleOpenCloseList[j]
			// 比对 close 或 close 时间，倒序排列
			return i1.time.Before(i2.time)
		})

		// 打印排序后的 open 或 close 时间和资产数据
		for _, item := range turtleOpenCloseList {
			turtleAssetItem := AssetItem{}
			turtleAssetItem.time = item.time
			tt := item.turtle
			if item.isOpen {
				turtleAssetItem.asset = tt.Open.Balance.Margin * tt.Open.Balance.AssetPrice
				turtleAssetItem.totalAsset = turtleAssetItem.asset
				turtleAssetItem.open = fmt.Sprintf("%s [%s]", tt.Symbol, tt.ID)
			} else {
				turtleAssetItem.asset = tt.Balance.Margin * tt.Balance.AssetPrice
				turtleAssetItem.totalAsset = turtleAssetItem.asset
				turtleAssetItem.close = fmt.Sprintf("%s [%s]", tt.Symbol, tt.ID)
			}
			assetItemList = append(assetItemList, turtleAssetItem)
		}
	}

	t := NewTable()
	// 打印资产快照和对应的趋势机开平仓
	t.SetHeader([]string{"Time", "Total Asset", "Asset", "Open", "Close"})

	sort.SliceStable(assetItemList, func(i, j int) bool {
		return assetItemList[i].time.After(assetItemList[j].time)
	})

	for _, item := range assetItemList {
		t.AddRow([]string{utils.FormatShortTimeStr(&item.time, true), fmt.Sprintf("%.2f USDT", item.totalAsset), fmt.Sprintf("%.2f USDT", item.asset), item.open, item.close})
	}

	assetsMsg := "[No Assets]"
	if len(t.Rows) > 1 {
		assetsMsg = t.Render()
	}
	return assetsMsg
}

func (this *TurtleController) sendStatus(controllerOnly bool) {
	runningCount := 0
	pausedCount := 0
	for _, tt := range this.Turtles {
		if !controllerOnly {
			tt.sendStatus(false)
		}
		if tt.Status == RUNNING {
			runningCount += 1
		} else {
			pausedCount += 1
		}
	}

	msg := fmt.Sprintf("\nBuild: %s", this.BuildInfo())
	msg += fmt.Sprintf("\nExchange: *%v*", this.Config.ExchangeName)
	msg += fmt.Sprintf("\nLaunched: *%v*", this.IsLaunched())
	// this.SendFileMessage("Turtle Detail", this.getTurtleDetails(), msg)
	this.SendMsgf("%s```%s```", msg, this.renderTurtleDetails())
}

func (this *TurtleController) stopAllTurtles() {
	for _, tt := range this.Turtles {
		tt.setStatus(PAUSED)
		tt.SendMsgf("%s 已暂停运行", tt.Symbol)

		_, err := tt.controller.Exchange.CancelAllOrders(tt.InstrumentType, tt.OrderType(), tt.Symbol)
		if err == nil {
			tt.SendMsgf("%s 订单已全部取消", tt.Symbol)
		} else {
			tt.ErrorMsgf("%s 取消订单失败", tt.Symbol)
		}
	}

	this.SendMsgf("所有品种都已停止运行。")
}

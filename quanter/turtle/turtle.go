// Package turtle  海龟
package turtle

import (
	"errors"
	"fmt"
	"math"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"

	"github.com/jinzhu/copier"
	"github.com/stevedomin/termtable"
)

const CurrencyPriceCacheTime = 2 * time.Second

const ExtKeyUnit = "Unit"         // Order ext field
const ExtKeyCategory = "Category" // Order ext field

const ExtKeyUnits = "Units"                     // Position ext field
const ExtKeyTakeProfitPrice = "TakeProfitPrice" // Position ext field
const ExtKeyStopLossPrice = "StopLossPrice"     // Position ext field
const ExtKeyLockedPNL = "LockedPNL"             // Position ext field
const ExtKeyAsset = "Asset"
const ExtKeyAssetPrice = "AssetPrice"
const ExtKeyPNL = "PNL"

// RUNNING 程序状态常量
const RUNNING = "Running"
const PAUSED = "Paused"

const LIQUIDATION_ACCEPTABLE_DELTA_RATIO = 0.2
const MIN_LIQUIDATION_TOLERENCE_RATIO = 0.0003 // 爆仓价和止损委托价之间的容差比率，小于 0.0003 调整杠杆率很难调准了

type FinishReason string

const FinishReasonRemoveSymbol FinishReason = "RemoveSymbol"
const FinishReasonStopLoss FinishReason = "StopLoss"
const FinishReasonTakeProfit FinishReason = "TakeProfit"
const FinishReasonClose FinishReason = "Close"
const FinishReasonStopOnExit FinishReason = "StopOnExit"
const FinishReasonRotate FinishReason = "Rotate"
const FinishReasonCleanUp FinishReason = "CleanUp" // 清理无用的 Turtle

type PauseReason string

const (
	PauseReasonUnknown  PauseReason = ""
	PauseReasonManual   PauseReason = "Manual"
	PauseReasonStopLoss PauseReason = "StopLoss"
	PauseReasonExpired  PauseReason = "Expired"
)

func NewTable() *termtable.Table {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	return t
}

type KlineStatus struct {
	klines         exchange.KLines
	updateTime     time.Time
	kLineExpiredAt int64
}

func (this *KlineStatus) firstCandleTime() time.Time {
	if this.klines != nil && len(this.klines) > 0 {
		return time.Unix(this.klines[0].Time, 0)
	}
	return time.Time{}
}

func (this *KlineStatus) lastCandleTime() time.Time {
	if this.klines != nil && len(this.klines) > 0 {
		return time.Unix(this.klines[len(this.klines)-1].Time, 0)
	}
	return time.Time{}
}

func (this *KlineStatus) expireTime() time.Time {
	return time.Unix(this.kLineExpiredAt, 0)
}

// 海龟策略
type Turtle struct {
	command.BaseResponder
	controller        *TurtleController
	orderUpdatedTimer *time.Timer // ws 触发订单更新 timer

	commandProcessor *command.CommandProcessor // 命令处理器

	// 以下是临时数据，不写入缓存
	lastATR               float64
	lastBreakoutPriceHigh float64
	lastBreakoutPriceLow  float64
	lastExitPriceHigh     float64
	lastExitPriceLow      float64

	// 以下是会写入到 storage 中的字段
	InstrumentType exchange.InstrumentType
	SymbolCode     *exchange.SymbolCode
	Symbol         string

	ID          string // 参考ID，6位随机字符串
	Status      string
	PauseTo     *time.Time  // 暂停截止时间
	PauseReason PauseReason // 暂停原因

	klineStatus *KlineStatus

	Short       *SideParam
	Long        *SideParam
	Open        *OpenParam
	CloseOrders OrderList
	TakeOrders  OrderList // 用于吃单的订单
	ExecOrders  OrderList // 已成交的订单，有 ExecQty  和 ExecPrice，打印滑点报告

	Balance         *Balance
	Position        *Position
	PositionChanges []*Position // 从 Position 中 copy 数据
	EstimatedLoss   *EstimatedLoss

	LastOrderUpdatedAt  time.Time
	LastHandleSuccessAt time.Time

	ExitByDC   bool
	StopOnExit bool

	Fix *Fixation

	RotatePlan *RotatePlan

	CreateTime *time.Time
	UpdateTime *time.Time
	Finish     *FinishParam

	Config *SymbolConfig

	SetHistory []*HistoryItem

	lastSimulateTime *time.Time
}

func (this *Turtle) TurtleSymbolID() string {
	code := strings.ReplaceAll(this.SymbolCode.Code, ".", "_")
	return fmt.Sprintf("%s_%s", this.controller.ID, code)
}

func (this *Turtle) Debugf(format string, args ...any) {
	this.controller.Debugf("(%s) %s", this.SymbolCode.Code, fmt.Sprintf(format, args...))
}

func (this *Turtle) Infof(format string, args ...any) {
	this.controller.Infof("(%s) %s", this.SymbolCode.Code, fmt.Sprintf(format, args...))
}

func (this *Turtle) Warnf(format string, args ...any) {
	this.controller.Warnf("(%s) %s", this.SymbolCode.Code, fmt.Sprintf(format, args...))
}

func (this *Turtle) Errorf(format string, args ...any) {
	this.controller.Errorf("(%s) %s", this.SymbolCode.Code, fmt.Sprintf(format, args...))
}

// 可能可以传入从 controller.storage 实例化的 Turtle
func NewTurtle(controller *TurtleController, symbolCode *exchange.SymbolCode, turtle *Turtle, copyConfig *SymbolConfig, forceRegisterCommandHandler bool) (*Turtle, error) {
	nowTime := time.Now()
	var symbol string
	if s, err := controller.Exchange.TranslateSymbolCodeToFutureSymbol(symbolCode); err != nil {
		return nil, err
	} else {
		symbol = s
	}
	if symbolCode == nil || (symbolCode.Code == "" || symbol == "") {
		return nil, errors.New("symbol code and symbol can not be empty")
	}

	if turtle == nil {
		turtle = &Turtle{
			controller:     controller,
			SymbolCode:     symbolCode,
			InstrumentType: symbolCode.InstrumentType(),
			Symbol:         symbol,
			ID:             exchange.NewRandomID(),
			Status:         PAUSED,
			Short: &SideParam{
				Side:          SideShort,
				BreakoutPrice: NewExpFloat64(0.0),
				ExitPrice:     NewExpFloat64(0.0),
				OpenOrders:    []*Order{},
				Prices:        []float64{0, 0, 0, 0},
				Qtys:          []float64{0, 0, 0, 0},
			},
			Long: &SideParam{
				Side:          SideLong,
				BreakoutPrice: NewExpFloat64(0.0),
				ExitPrice:     NewExpFloat64(0.0),
				OpenOrders:    []*Order{},
				Prices:        []float64{0, 0, 0, 0},
				Qtys:          []float64{0, 0, 0, 0},
			},
			Open: &OpenParam{
				ATR: NewExpFloat64(0.0),
				Balance: &Balance{
					UpdateTime: &nowTime,
				},
			},
			Finish: &FinishParam{
				Balance: &Balance{},
			},
			CloseOrders: OrderList{},
			TakeOrders:  OrderList{},
			ExecOrders:  OrderList{},
			Balance:     &Balance{},
			Position:    &Position{},
			ExitByDC:    false,
			StopOnExit:  false,
			Fix: &Fixation{
				IgnoredExitKLines: []int64{},
			},
			CreateTime: &nowTime,
			UpdateTime: &nowTime,
			SetHistory: []*HistoryItem{
				{
					CreateTime: &nowTime,
					Field:      "this",
					Value:      fmt.Sprintf("Build: %s", controller.BuildInfo()),
					Comment:    "New",
				},
			},
		}
		config := NewSymbolConfig(turtle)
		if copyConfig != nil {
			copier.Copy(config, copyConfig)
			config.Symbol = symbol
		}
		turtle.Config = config
	}
	// 初始化反向引用
	turtle.controller = controller
	turtle.Config.turtle = turtle
	// 命令处理器初始化
	turtle.BaseResponder = command.NewBaseResponder(controller.Debug, controller.CommitHash, controller.BuildTime, turtle.GetSlackChannel(), controller.Messenger, "")
	turtle.Launched.Store(controller.Launched.Load())

	// 确保 Open.BreakoutPeriod 和 Balance.External 等冗余字段有值
	if turtle.Open.BreakoutPeriod == 0 {
		turtle.Open.BreakoutPeriod = turtle.Config.BreakoutPeriod
	}
	if turtle.Balance.External == 0 {
		turtle.Balance.External = turtle.Config.ExternalBalance
	}
	if turtle.EstimatedLoss == nil {
		turtle.EstimatedLoss = &EstimatedLoss{
			Longs:  []float64{0, 0, 0, 0},
			Shorts: []float64{0, 0, 0, 0},
		}
	}
	turtle.klineStatus = &KlineStatus{}

	turtle.commandProcessor = command.NewCommandProcessor(turtle,
		[]command.Commander{
			NewStatusTurtleCommand(turtle),
			NewKlineStatusTurtleCommand(turtle),
			NewParametersTurtleCommand(turtle),
			NewGetPositionTurtleCommand(turtle),
			NewReportTurtleCommand(turtle),
			NewHistoryTurtleCommand(turtle),
			NewResumeTurtleCommand(turtle),
			NewPauseTurtleCommand(turtle),
			NewCloseTurtleCommand(turtle),
			NewShadowCommand(turtle),
			NewSetExternalBalanceTurtleCommand(turtle),
		})

	if !turtle.controller.Config.IsDemo {
		turtle.commandProcessor.AddCommands([]command.Commander{
			NewConfirmOrdersTurtleCommand(turtle),
			NewSetATRTurtleCommand(turtle),
			NewResetATRTurtleCommand(turtle),
			NewSetBreakoutPriceLongTurtleCommand(turtle),
			NewSetBreakoutPriceShortTurtleCommand(turtle),
			NewSetExitPriceLongTurtleCommand(turtle),
			NewSetExitPriceShortTurtleCommand(turtle),
			NewSetCurrentBreakoutPeriodTurtleCommand(turtle),
			NewSetIgnoredExitKLineTurtleCommand(turtle),
			NewFixPositionCommand(turtle),
			NewSetExitByDCTurtleCommand(turtle),
			NewSetStopOnExitTurtleCommand(turtle),
			NewPauseToTurtleCommand(turtle),
			cmds.NewPagerCommand(),
			NewSetFixPositionQtyTurtleCommand(turtle),
			NewResetPriceTurtleCommand(turtle),
			NewCleanControllerCommand(controller),
			NewRemoveHistoryControllerCommand(controller),
			cmds.NewHoldingsCommand(controller),
			cmds.NewAssetsCommand(controller, controller.storage),
		})
	}

	turtle.commandProcessor.AddCommands([]command.Commander{
		NewSetConfigTurtleCommand(turtle),
		cmds.NewMuteCommand(),
		cmds.NewLogCommand(turtle.controller.Config.LogDirPath, turtle.controller.GetLogFilename()),
		cmds.NewDownloadLogCommand(turtle.controller.Config.LogDirPath, turtle.controller.GetLogFilename()),
		cmds.NewDownloadStorageCommand(turtle.controller.ConfigPath, turtle.TurtleSymbolID()+".data.json"),
		cmds.NewStackTraceCommand(),
	})

	controller.Messenger.RegisterCommandHandler(turtle.GetSlackChannel(), turtle.CommandHandler, forceRegisterCommandHandler)

	instrument, err := turtle.controller.Exchange.GetInstrument(turtle.InstrumentType, turtle.Symbol)
	if err != nil {
		if symbol == turtle.Symbol {
			return turtle, fmt.Errorf("[%s] 获取合约信息失败: %s", turtle.Symbol, err)
		} else {
			controller.AlertMsgf("当前存储品种 %s 与翻译品种 %s 不一致，可能已过期", turtle.Symbol, symbol)
			turtle.setStatus(PAUSED)
		}
	} else {
		if turtle.controller.IsExchange(exchange.BitMEX) {
			turtle.controller.Exchange.CloseWebsocket(false)

			if !strings.EqualFold(instrument.SettleCurrency, controller.Config.USDXSymbol) {
				turtle.InstrumentType = exchange.CoinMarginedFutures
			}
		}

		turtle.InitData()
		turtle.controller.Exchange.Subscribe(turtle.InstrumentType, turtle.Symbol)
	}

	zlog.Debugf("Turtle [%s #%p] add success", turtle.TurtleSymbolID(), turtle)
	return turtle, nil
}

func (this *Turtle) GetSlackChannel() string {
	if this.SymbolCode.IsPerp() {
		channel := this.TurtleSymbolID()
		return channel
	} else {
		suffix := ""
		parts := strings.Split(this.SymbolCode.Code, ".")
		if len(parts) == 2 {
			suffix = parts[1]
		}
		channel := fmt.Sprintf("%s_%s", this.controller.ID, this.SymbolCode.Coin())
		if suffix != "" {
			channel = fmt.Sprintf("%s_%s", channel, strings.ToLower(suffix))
		}
		return channel
	}
}

// 因为同一品种交割合约共用一个频道，所以删除时需确认没有其他合约在用一频道才执行
func (this *Turtle) removeChannel() {
	for _, tt := range this.controller.Turtles {
		if tt.Symbol == this.Symbol {
			continue
		}
		if tt.GetSlackChannel() == this.GetSlackChannel() {
			return
		}
	}
	this.controller.Messenger.RemoveChannel(this.GetSlackChannel())
}

func (this *Turtle) InitData() {
	// 获取最新 K 线
	success := this.HandleLatestKLineData()
	if !success {
		this.AlertMsgf("获取K线数据失败")
		return
	}

	if this.Open.ATR.ExpiredAt == nil || this.Open.ATR.Value == 0 {
		// 本地存储 atr 无过期时间时使用最新 atr
		this.Open.ATR.set(this, this.lastATR, time.Unix(this.klineStatus.kLineExpiredAt, 0), false, "Open.ATR", "")
	}

	if utils.IsTimeExpired(this.Long.BreakoutPrice.ExpiredAt) {
		breakoutPriceLong, breakoutPriceShort := this.GetBreakoutPrice()
		this.Long.BreakoutPrice.set(this, breakoutPriceLong, time.Unix(this.klineStatus.kLineExpiredAt, 0), false, "Long.BreakoutPrice", "")
		this.Short.BreakoutPrice.set(this, breakoutPriceShort, time.Unix(this.klineStatus.kLineExpiredAt, 0), false, "Short.BreakoutPrice", "")
	}

	if utils.IsTimeExpired(this.Long.ExitPrice.ExpiredAt) {
		// ExitPriceHigh = 看空退出价, ExitPriceLow => 看多退出价
		exitPriceShort, exitPriceLong := this.GetExitPrice()
		this.Long.ExitPrice.set(this, exitPriceLong, time.Unix(this.klineStatus.kLineExpiredAt, 0), false, "Long.ExitPrice", "")
		this.Short.ExitPrice.set(this, exitPriceShort, time.Unix(this.klineStatus.kLineExpiredAt, 0), false, "Short.ExitPrice", "")
	}

	if this.LastOrderUpdatedAt.IsZero() {
		this.LastOrderUpdatedAt = time.Unix(0, 0)
	}

	if this.LastHandleSuccessAt.IsZero() {
		this.LastHandleSuccessAt = time.Unix(0, 0)
	}
}

/*
Kline 和本地数据更新策略
1. 程序启动时，获取最新 K 线，本地无数据则写入；有数据时, Price 如过期则更新 BreakoutPrice, ExitPrice
2. 新的 K 线开始时(如 6小时 K 线，在 0、6、18 时)，获取最新 K 线，更新 BreakoutPrice, ExitPrice
3. 无持仓无触发挂单时ATR已过期，则更新 ATR 缓存
4. 持仓被平仓后，获取最新 K 线，更新 BreakoutPrice, ExitPrice
*/

// 获取合约持仓
func (this *Turtle) getCurrentPosition(allowCache bool) (*Position, error) {
	if position, err := this.controller.Exchange.GetPosition(this.InstrumentType, this.Symbol, this.Position.Side, allowCache); err != nil {
		this.Errorf(" get current position error: %s", err)
		return nil, err
	} else {
		this.Infof("current position: %#v", position)
		return position, nil
	}
}

func (this *Turtle) SetInitLeverage(firstLongPosition *exchange.Position, firstShortPosition *exchange.Position) (updated bool, _ error) {
	instrumentType := this.InstrumentType
	symbol := this.Symbol
	exchangeAPI := this.controller.Exchange
	if this.controller.IsExchange(exchange.Binance) || this.controller.IsExchange(exchange.Hyperliquid) {
		// 设置逐仓模式
		if err := exchangeAPI.SetMarginMode(instrumentType, symbol, exchange.Isolated); err != nil {
			if !strings.Contains(err.Error(), "No need to change") {
				return false, fmt.Errorf("设置逐仓模式失败: %s", err)
			}
		}
	}

	leverageLong, err := exchangeAPI.CalculateLeverage(firstLongPosition)
	if err != nil {
		return false, err
	}
	leverageShort, err := exchangeAPI.CalculateLeverage(firstShortPosition)
	if err != nil {
		return false, err
	}

	value := this.Qty2Size(this.Symbol, firstLongPosition.EntryPrice, math.Abs(firstLongPosition.Qty))
	maxLeverage := exchangeAPI.MaxLeverage(this.InstrumentType, this.Symbol, value)

	if leverageLong > (maxLeverage * 0.7) {
		err := fmt.Errorf("无持仓时，杠杆率不应大于 %.1f，目标设置 %.1f", maxLeverage*0.7, leverageLong)
		return false, err
	}
	if leverageShort > (maxLeverage * 0.7) {
		err := fmt.Errorf("无持仓时，杠杆率不应大于 %.1f，目标设置 %.1f", maxLeverage*0.7, leverageShort)
		return false, err
	}

	if this.controller.IsExchange(exchange.OKEx) {
		leverageLongBefore, leverageShortBefore, err := exchangeAPI.GetLeverage(instrumentType, symbol, exchange.Isolated)
		if err != nil {
			return false, err
		}

		if leverageLong == leverageLongBefore && leverageShort == leverageShortBefore {
			return false, nil
		}

		// OKEX 设置杠杠率时不能有挂单
		if _, err := exchangeAPI.CancelAllOrders(instrumentType, this.OrderType(), symbol); err != nil {
			return false, fmt.Errorf("设置初始杠杠率，取消所有订单失败: %s", err)
		}
	}

	targetLeverage := math.Max(leverageLong, leverageShort)
	if this.controller.IsExchange(exchange.OKEx) {
		// 多空都需要分别设置
		targetLeverage = leverageLong
	}
	this.Infof("set init leverage %v", targetLeverage)
	if err := exchangeAPI.SetLeverage(this.InstrumentType, symbol, exchange.Isolated, firstLongPosition.Side, targetLeverage); err != nil {
		return false, fmt.Errorf("设置初始杠杠率失败: %s", err)
	}

	if this.controller.IsExchange(exchange.OKEx) {
		// 多空都需要分别设置
		this.Infof("set init leverage[OK short] %v", targetLeverage)
		targetLeverage = leverageShort
		if err := exchangeAPI.SetLeverage(this.InstrumentType, symbol, exchange.Isolated, firstShortPosition.Side, targetLeverage); err != nil {
			return false, fmt.Errorf("设置初始杠杠率失败: %s", err)
		}
	}

	return true, nil
}

func (this *Turtle) SetLeverageToMax(position *Position) {
	value := this.Qty2Size(this.Symbol, position.EntryPrice, math.Abs(position.Qty))
	if this.controller.Exchange.MaxLeverage(this.InstrumentType, this.Symbol, value) == position.Leverage {
		return
	}

	// OKEx 设置杠杠率之前需要取消订单，设置之后再创建并更新缓存
	var openOrders []*Order
	if this.controller.IsExchange(exchange.OKEx) {
		var ok bool
		openOrders, ok = this.cancelOpenOrders()
		if !ok {
			return
		}
	}

	this.controller.Exchange.SetLeverageToMax(this.InstrumentType, this.Symbol, position.Leverage, position.Side)

	if this.controller.IsExchange(exchange.OKEx) {
		if !this.reCreateOpenOrders(openOrders, position) {
			return
		}
	}

}

// 设置当前持仓爆仓价
func (this *Turtle) AdjustLiquidationPrice(position *Position, stopLimitPrice float64) bool {
	// 检测当前持仓爆仓价是否和目标爆仓价一致
	// 目标爆仓价允许误差范围 = max(止损委托价 * 滑点率 * LIQUIDATION_ACCEPTABLE_DELTA_RATIO, TICK_SIZE * 6)
	acceptableDelta := stopLimitPrice * this.Config.CloseSlippageRate * LIQUIDATION_ACCEPTABLE_DELTA_RATIO
	acceptableDelta = math.Max(acceptableDelta, stopLimitPrice*MIN_LIQUIDATION_TOLERENCE_RATIO) // 从 6*TickSize 转为不小于 MIN_LIQUIDATION_TOLERENCE_RATIO，否则调整杠杆率很难调到范围内

	// 将止损委托价向更宽松的方向偏移 acceptableDelta
	// 使实际爆仓价始终比爆仓委托价更宽松（更不容易爆仓）
	var targetLiquidationPrice float64
	if position.Side == exchange.PositionSideLong { // 看多时目标爆仓价要比委托价低，可接受范围为 [stopPrice - 2 * acceptableDelta, stopPrice]
		targetLiquidationPrice = stopLimitPrice - acceptableDelta
	} else { // 看空时目标爆仓价要比委托价高，可接受范围为 [stopPrice, stopPrice + 2 * acceptableDelta]
		targetLiquidationPrice = stopLimitPrice + acceptableDelta
	}

	if math.Abs(targetLiquidationPrice-position.LiquidationPrice) <= acceptableDelta {
		return true
	}

	// OKEx 设置杠杠率之前需要取消订单，设置之后再创建并更新缓存
	var openOrders []*Order
	if this.controller.IsExchange(exchange.OKEx) {
		var ok bool
		openOrders, ok = this.cancelOpenOrders()
		if !ok {
			return false
		}
	}

	deltaPrice, errMsg := this.controller.Exchange.AdjustLiquidationPrice(this.InstrumentType, this.Symbol, position, targetLiquidationPrice, acceptableDelta)

	if this.controller.IsExchange(exchange.OKEx) {
		if !this.reCreateOpenOrders(openOrders, position) {
			return false
		}
	}

	if errMsg != "" && deltaPrice > (this.Config.LiquidationPriceAlertRatio*targetLiquidationPrice) {
		this.AlertMsgf(errMsg)
		return false
	}
	return true
}

func (this *Turtle) cancelOpenOrders() ([]*Order, bool) {
	openOrders, err := this.controller.Exchange.GetOpenOrders(this.InstrumentType, this.OrderType(), this.Symbol)
	if err != nil {
		this.AlertMsgf("[调整杠杠率]取消订单失败: %s", err)
		return nil, false
	}
	if len(openOrders) > 0 {
		_, err := this.controller.Exchange.CancelAllOrders(this.InstrumentType, this.OrderType(), this.Symbol)
		if err != nil {
			this.AlertMsgf("[调整杠杠率]取消所有订单失败")
			return nil, false
		}
	}
	return openOrders, true
}

func (this *Turtle) reCreateOpenOrders(openOrders []*Order, position *Position) bool {
	this.CloseOrders = []*Order{}

	for _, openOrder := range openOrders {
		if openOrder.IsCloseOrder() {
			// 平仓订单
			if openOrder.IsTakeProfitOrder(position) {
				// 创建止盈单
				if order, err := this.createProfitOrder(openOrder.Side, openOrder.TriggerPrice, openOrder.Qty); err != nil {
					this.AlertMsgf("调整杠杠率，创建止盈订单失败: %s", err)
					return false
				} else {
					this.CloseOrders = append(this.CloseOrders, SetOrderExt(order, OrderCategoryTakeProfit, 0))
				}

			} else {
				if order, err := this.createCloseOrder(openOrder.Side, openOrder.Price, openOrder.Qty); err != nil {
					this.AlertMsgf("调整杠杠率，创建止损订单失败: %s", err)
					return false
				} else {
					this.CloseOrders = append(this.CloseOrders, SetOrderExt(order, OrderCategoryStopLoss, 0))
				}

			}

		} else {
			// 加仓单
			if newOrder, err := this.createOpenOrder(openOrder.Side, openOrder.TriggerPrice, openOrder.Qty); err != nil {
				this.AlertMsgf("调整杠杠率，创建订单失败: %s", err)
				return false
			} else {
				this.updateOpenOrderStorage(openOrder.OrderID, newOrder)
			}

		}
	}

	this.LastOrderUpdatedAt = time.Now()
	this.controller.storage.Save()
	return true
}

// 获取实际止损委托价格
func (this *Turtle) getActualExitPrice(positionSide exchange.PositionSide, avgPrice float64, atrExitPrice float64, dcExitPrice float64) float64 {
	var price = atrExitPrice
	if positionSide == exchange.PositionSideLong {
		// 看多实际止损价 = 退出价 < 开仓均价 ? max(退出价, 止损价) : 止损价
		if dcExitPrice < avgPrice && dcExitPrice > atrExitPrice {
			price = dcExitPrice
		}
	} else {
		// 看空实际止损价 = 退出价 > 开仓均价 ? min(退出价, 止损价) : 止损价
		if dcExitPrice > avgPrice && dcExitPrice < atrExitPrice {
			price = dcExitPrice
		}
	}
	return price
}

// 获取当前余额 = 账号余额 + 设置的外部余额 + 同一账号其他海龟已确认盈利
func (this *Turtle) getBalance() (balance *Balance, er error) {
	instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType, this.Symbol)
	currency := instrument.SettleCurrency
	if this.Position.GetString(ExtKeyAsset) == "" {
		this.Position.SetString(ExtKeyAsset, currency)
	}
	exchangeBalance, err := this.controller.Exchange.GetUserMargin(this.InstrumentType, currency)
	if err != nil {
		this.AlertMsgf("获取账号余额失败")
		er = err
		return
	}

	balance = this.Balance
	margin := exchangeBalance.MarginBalance
	this.Balance.OpenSizeMargin = (margin + this.Balance.External) * this.Config.OpenSizePercent * getUnitsLossRatio(this.Config.MaxOpenUnits)
	this.Balance.Asset = currency
	this.Balance.Margin = margin
	this.Balance.Wallet = exchangeBalance.WalletBalance
	// 如果 USDT ，交易所 API 会直接返回 1.0 ，没有什么开销
	if ap, err := this.getCurrencyPrice(currency); err != nil {
		this.Errorf("get margin balance, fetch currency price error: %s", err)
	} else {
		this.Balance.AssetPrice = ap
	}

	var total float64
	if this.Config.UseMarginBalance {
		total = this.Balance.Margin + this.Balance.External
	} else {
		total = this.Balance.Wallet + this.Balance.External
	}

	if total < 0 {
		this.AlertMsgf("账号余额 + 外部余额之和为负值")
		er = errors.New("total balance cannot be negative")
		return
	}

	this.Balance.Total = total

	this.Balance.OtherPNL = this.getOtherPNL()
	if this.Balance.OtherPNL > 0 && this.Balance.OtherPNL > (margin-exchangeBalance.WalletBalance) {
		// this.AlertMsgf("同一账号其他品种已确认盈利 %v 已大于总浮盈 %v，请检查", this.Balance.OtherPNL, margin-exchangeBalance.WalletBalance)
		this.Balance.OtherPNL = 0
	}

	if !this.Config.UseMarginBalance {
		this.Balance.Total = this.Balance.Total + this.Balance.OtherPNL
	}
	nowTime := time.Now()
	this.Balance.UpdateTime = &nowTime
	return
}

func (this *Turtle) getOtherPNL() float64 {
	var pnl float64
	instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType, this.Symbol)
	currency := instrument.SettleCurrency
	for _, tt := range this.controller.Turtles {
		if tt.Status != RUNNING || tt.Symbol == this.Symbol {
			continue
		}
		instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType, tt.Symbol)
		if currency != instrument.SettleCurrency {
			continue
		}

		pnl += tt.Position.GetFloat64(ExtKeyLockedPNL)
	}
	return pnl
}

/*
挂单状态为已触发但未完成处理流程
1. 如果无持仓，在挂单触发一定时间后，修改首单价格至完全成交，剩下3个头寸按成交价和最新 ATR 依次重新挂单；
2. 如果有持仓，存在任意挂单已触发一定时间后，将所有触发但未完成的订单全部修改价格至完全成交，剩余头寸按已成交订单中最差成交价依次重新挂单，ATR 不变；
*/

// 处理已触发的挂单
// 参数 positionQty 持仓数量
// 参数 openOrders 未完成的订单
func (this *Turtle) handleTriggeredOrders(positionQty float64, openOrders []*Order) {
	triggeredOrders := make([]*Order, 0)   // 已触发订单
	untriggeredOrders := make([]*Order, 0) // 未触发订单
	var triggeredSide exchange.OrderSide   // 订单方向
	overtime := false                      // 触发是否已超时
	for _, order := range openOrders {
		if !order.IsTriggered() {
			// 订单未触发，添加到 untriggeredOrders
			untriggeredOrders = append(untriggeredOrders, order)
			continue
		}

		if triggeredSide == "" {
			triggeredSide = order.Side
		} else if triggeredSide != order.Side {
			this.AlertMsgf("多空均存在已触发挂单")
			return
		}

		triggeredTime := order.UpdateTime

		this.Infof("order[%s] triggeredTime %s", order.OrderID, triggeredTime)

		waitingSeconds := this.Config.WaitingSecondsAfterTriggeredLong
		if triggeredSide == exchange.OrderSideSell {
			waitingSeconds = this.Config.WaitingSecondsAfterTriggeredShort
		}

		if triggeredTime.Add(time.Second * time.Duration(waitingSeconds)).Before(time.Now()) {
			// 订单触发事件已超过 waitingSeconds
			overtime = true
		}

		// 订单添加到 triggeredOrders
		triggeredOrders = append(triggeredOrders, order)
	}

	if !overtime {
		// 没有超时的订单，暂不处理
		return
	}

	if positionQty == 0 {
		// 无持仓时，触发订单处理
		firstOrderID := ""         // 首个订单
		var otherOrderIDs []string // 其他订单
		if triggeredSide == exchange.OrderSideBuy {
			firstOrderID = this.Long.OpenOrders[0].OrderID
			for _, o := range this.Long.OpenOrders[1:] {
				otherOrderIDs = append(otherOrderIDs, o.OrderID)
			}

		} else {
			firstOrderID = this.Short.OpenOrders[0].OrderID
			for _, o := range this.Short.OpenOrders[1:] {
				otherOrderIDs = append(otherOrderIDs, o.OrderID)
			}
		}

		for _, order := range triggeredOrders {
			// 当前是首个订单
			if order.OrderID == firstOrderID {
				// 取消其他订单
				for _, otherOrderID := range otherOrderIDs {
					err := this.controller.Exchange.CancelOrder(this.InstrumentType, this.OrderType(), this.Symbol, otherOrderID)
					if err != nil {
						this.AlertMsgf("取消订单失败: %s", err)
						return
					}
				}

				// 吃掉该订单
				orderTaked := false
				avgPx := 0.0
				for i := 0; i < 3; i++ {
					// 最多尝试 3 次
					newOrder, err := this.takeOrder(order, order.Side, false, false)
					if err != nil {
						this.AlertMsgf("吃单创建失败: %s", err)
						return
					}

					orderType := OrderCategoryLong
					if triggeredSide == exchange.OrderSideSell {
						orderType = OrderCategoryShort
					}
					this.TakeOrders = append(this.TakeOrders, SetOrderExt(newOrder, orderType, 0))

					this.LastOrderUpdatedAt = time.Now()

					// 等待 5 秒
					time.Sleep(time.Second * 5)

					// 重新查询订单，确认成交数量是否等于订单数量
					takenOrder, err := this.controller.Exchange.GetOrderByOrig(*newOrder)
					if err == nil && takenOrder.Qty == takenOrder.ExecQty {
						orderTaked = true
						avgPx = takenOrder.ExecPrice
						break
					} else if err == nil {
						// 吃单未成交，在新订单上修改吃单
						order = newOrder
					}
				}

				if !orderTaked {
					this.AlertMsgf("吃单失败")
					return
				}

				// 剩下头寸重新挂单
				atr := this.Open.ATR.Value
				for i := 1; i < this.Config.MaxOpenUnits; i++ {
					var price float64
					var unit float64
					if order.Side == exchange.OrderSideBuy {
						price = avgPx + float64(i)*0.5*atr
						unit = this.Long.OpenSize
					} else {
						price = avgPx - float64(i)*0.5*atr
						unit = this.Short.OpenSize
					}

					// 反向合约每单价值相同数量不同，正向合约每单数量相同
					var qty float64
					if this.controller.Exchange.IsReverseSymbol(this.InstrumentType, this.Symbol) {
						qty = this.Size2Qty(this.Symbol, price, unit)
					} else {
						qty = order.Qty
					}

					openOrder, err := this.createOpenOrder(order.Side, price, qty)
					if err != nil {
						this.AlertMsgf("创建订单失败: %s", err)
						return
					}

					if order.Side == exchange.OrderSideBuy {
						this.Long.UpdateOrderFromExchangeOrder(i, openOrder)
					} else {
						this.Short.UpdateOrderFromExchangeOrder(i, openOrder)
					}
					this.LastOrderUpdatedAt = time.Now()
				}

				// 保存本地存储
				this.controller.storage.Save()

				break
			}
		}
	} else {
		// 有持仓时，触发订单处理

		// 最后成交均价，看多取最大值，看空取最小值
		lastAvgPx := 0.0
		for _, order := range triggeredOrders {
			// 吃掉该订单
			orderTaked := false
			for i := 0; i < 3; i++ {
				// 最多尝试 3 次
				newOrder, err := this.takeOrder(order, order.Side, false, false)
				if err != nil {
					this.AlertMsgf("吃单创建失败: %s", err)
					return
				}

				orderType := OrderCategoryLong
				if triggeredSide == exchange.OrderSideSell {
					orderType = OrderCategoryShort
				}
				this.TakeOrders = append(this.TakeOrders, SetOrderExt(newOrder, orderType, 0))

				this.LastOrderUpdatedAt = time.Now()
				time.Sleep(time.Second * 5)

				takenOrder, err := this.controller.Exchange.GetOrderByOrig(*newOrder)
				if err == nil && takenOrder.Qty == takenOrder.ExecQty {
					orderTaked = true
					if lastAvgPx == 0.0 {
						lastAvgPx = takenOrder.ExecPrice
					}

					if order.Side == exchange.OrderSideBuy {
						lastAvgPx = math.Max(takenOrder.ExecPrice, lastAvgPx)
					} else {
						lastAvgPx = math.Min(takenOrder.ExecPrice, lastAvgPx)
					}
					break
				} else if err == nil {
					// 吃单未成交，在新订单上修改吃单
					order = newOrder
				}
			}

			if !orderTaked {
				this.AlertMsgf("吃单失败")
				return
			}
		}

		// 根据 lastAvgPx 修改未触发订单
		atr := this.Open.ATR.Value
		i := 1
		for _, order := range untriggeredOrders {
			var price float64
			var unit float64
			if order.Side == exchange.OrderSideBuy {
				price = lastAvgPx + float64(i)*0.5*atr
				unit = this.Long.OpenSize
			} else {
				price = lastAvgPx - float64(i)*0.5*atr
				unit = this.Short.OpenSize
			}

			// 反向合约每单价值相同数量不同，正向合约每单数量相同
			var qty float64
			if this.controller.Exchange.IsReverseSymbol(this.InstrumentType, this.Symbol) {
				qty = this.Size2Qty(this.Symbol, price, unit)
			} else {
				qty = order.Qty
			}

			newOrder, err := this.updateOpenOrder(order, order.Side, price, atr, qty)
			if err != nil {
				this.AlertMsgf("修改订单[%s]失败: %s", order.OrderID, err)
				return
			}

			this.updateOpenOrderStorage(order.OrderID, newOrder)

			i++

			this.LastOrderUpdatedAt = time.Now()
			this.controller.storage.Save()
		}

	}
}

// 更新本地开仓订单存储
func (this *Turtle) updateOpenOrderStorage(origOrderID string, newOrder *Order) {
	if newOrder.Side == exchange.OrderSideBuy {
		if idx := utils.IndexOf(origOrderID, this.Long.OpenOrders.IDs()); idx != -1 {
			this.Long.UpdateOrderFromExchangeOrder(idx, newOrder)
		} else {
			this.AlertMsgf("更新本地看多订单[%s=>%s]失败，找不到原始订单。", origOrderID, newOrder.OrderID)
		}
	} else if newOrder.Side == exchange.OrderSideSell {
		if idx := utils.IndexOf(origOrderID, this.Short.OpenOrders.IDs()); idx != -1 {
			this.Short.UpdateOrderFromExchangeOrder(idx, newOrder)
		} else {
			this.AlertMsgf("更新本地看空订单[%s=>%s]失败，找不到原始订单。", origOrderID, newOrder.OrderID)
		}
	} else {
		this.AlertMsgf("order[%s] miss side", newOrder.OrderID)
	}
}

// 更新本地开仓订单存储
func (this *Turtle) updateCloseOrderStorage(origOrderID string, newOrder *Order, isTakeProfit bool) {
	orderType := OrderCategoryStopLoss
	if isTakeProfit {
		orderType = OrderCategoryTakeProfit
	}

	if idx := utils.IndexOf(origOrderID, this.CloseOrders.IDs()); idx != -1 {
		this.CloseOrders[idx] = SetOrderExt(newOrder, orderType, 0)
		nowTime := time.Now()
		this.CloseOrders[idx].UpdateTime = &nowTime
	} else {
		this.AlertMsgf("更新本地平仓订单[%s=>%s]失败，找不到原始订单。", origOrderID, newOrder.OrderID)
	}
}

// 检测成功运行状态，超过 15 分钟则报警
func (this *Turtle) checkLastSuccessTime() {
	if this.Status != RUNNING {
		return
	}

	if this.InstrumentType != exchange.USDXMarginedFutures {
		if this.LastHandleSuccessAt.Add(time.Minute * time.Duration(15)).Before(time.Now()) {
			this.AlertMsgf("已超过 15 分钟未成功执行完整检测流程")
		}
	}
}

func (this *Turtle) dcExpiredCheck() {
	if utils.IsTimeExpired(this.Long.BreakoutPrice.ExpiredAt) || utils.IsTimeExpired(this.Long.ExitPrice.ExpiredAt) {
		// 价格已过期
		if success := this.HandleLatestKLineData(); success {
			this.updatePriceStorage(false, false)
		}
	}
}

func (this *Turtle) openATRUpdateCheck() (updated bool) {
	if utils.IsTimeExpired(this.Open.ATR.ExpiredAt) {
		this.Open.ATR.set(this, this.lastATR, this.klineStatus.expireTime(), false, "Open.ATR", "")
		return true
	}
	return false
}

func (this *Turtle) openBalanceUpdateCheck() (updated bool, err error) {
	// 获取最新账号余额
	_, err = this.getBalance()
	if err != nil {
		return false, fmt.Errorf("获取余额失败: %s", err)
	}

	// 和本地对比是否有变动, 忽略小于 math.Max(MarginChangeMinPercent, 0.001) 的变动
	balanceChanged := math.Abs(this.Balance.Total-this.Open.Balance.Total) > (this.Open.Balance.Total * math.Max(this.Config.MarginChangeMinPercent, 0.001))

	if balanceChanged {
		this.Open.Balance.Margin = this.Balance.Margin
		this.Open.Balance.Wallet = this.Balance.Wallet
		this.Open.Balance.OtherPNL = this.Balance.OtherPNL
		this.Open.Balance.Total = this.Balance.Total
		this.Open.Balance.Asset = this.Balance.Asset
		this.Open.Balance.AssetPrice = this.Balance.AssetPrice
		this.Open.Balance.UpdateTime = this.Balance.UpdateTime
		this.Infof("marginBalance changed")
	}

	return balanceChanged, nil
}

func (this *Turtle) noPositionHandler(position *Position, openOrders []*Order) (success bool) {
	this.ExitByDC = false
	this.Position.SetFloat64(ExtKeyLockedPNL, 0)
	this.Fix.StopPrice = 0
	this.Fix.PositionComment = ""

	// 记录开仓首个单位头寸价
	this.Long.OpenPrice = this.Long.BreakoutPrice.Value
	this.Short.OpenPrice = this.Short.BreakoutPrice.Value

	orderIDsMatched := true                        // 本地记录的挂单 orderIDs 和实际挂单ID 是否一致
	hasTriggeredOrder := false                     // 是否有已触发的订单
	openOrderCount := len(openOrders)              // 未完成订单数量
	needOrderCount := this.Config.MaxOpenUnits * 2 // 需要的订单数量
	if this.Config.DisableShort {
		needOrderCount = this.Config.MaxOpenUnits
		if len(this.Long.OpenOrders.IDs()) != this.Config.MaxOpenUnits {
			orderIDsMatched = false
		}
		if len(this.Short.OpenOrders.IDs()) != 0 {
			orderIDsMatched = false
		}
	} else {
		if len(this.Long.OpenOrders.IDs()) != this.Config.MaxOpenUnits {
			orderIDsMatched = false
		}
		if len(this.Short.OpenOrders.IDs()) != this.Config.MaxOpenUnits {
			orderIDsMatched = false
		}
	}
	if openOrderCount == needOrderCount {
		for _, order := range openOrders {
			if order.IsTriggered() {
				hasTriggeredOrder = true
			}

			orderIDs := this.Long.OpenOrders.IDs()
			if order.Side == exchange.OrderSideSell {
				orderIDs = this.Short.OpenOrders.IDs()
			}

			if utils.IndexOf(order.OrderID, orderIDs) == -1 {
				orderIDsMatched = false
				break
			}

			if this.Config.DisableShort && order.Side == exchange.OrderSideSell {
				orderIDsMatched = false
				break
			}
		}
	} else {
		orderIDsMatched = false
	}

	atrUpdated := this.openATRUpdateCheck()

	marginBalanceChanged, err := this.openBalanceUpdateCheck()
	if err != nil {
		this.AlertMsgf("检查开仓余额变动错误: %s", err)
		return false
	}

	if (atrUpdated || marginBalanceChanged) && !hasTriggeredOrder {
		this.updateOpenSize()
	}

	if ok, msg := this.checkOpenPositionData(); !ok {
		this.AlertMsgf("开仓数据检测不通过: %s", msg)
		this.Errorf("open position data check failed, turtle: %#v, open: %#v, long: %#v, short: %#v", this, this.Open, this.Long, this.Short)
		return false
	}

	atr := this.Open.ATR.Value
	breakoutPriceLong := this.Long.BreakoutPrice.Value
	breakoutPriceShort := this.Short.BreakoutPrice.Value

	// 设置初始杠杠率，按首单开仓价计算，多空均计算，取其中较大者
	dcExitPrice := this.getCloseOrderPrice(exchange.OrderSideSell, this.Long.ExitPrice.Value) // 多头使用 DCLow 作为退出价
	exitPrice := this.getActualExitPrice(exchange.PositionSideLong, breakoutPriceLong, breakoutPriceLong-2*atr, dcExitPrice)
	_, firstLongQty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideBuy, 0)

	firstLongPosition := &Position{
		InstrumentType:   this.InstrumentType,
		Symbol:           this.Symbol,
		EntryPrice:       breakoutPriceLong,
		Qty:              firstLongQty,
		Side:             exchange.PositionSideLong,
		LiquidationPrice: exitPrice,
	}
	firstLongPosition.SetExtStruct(position)

	dcExitPrice = this.getCloseOrderPrice(exchange.OrderSideBuy, this.Short.ExitPrice.Value) // 空头使用 DCHigh 作为退出价
	exitPrice = this.getActualExitPrice(exchange.PositionSideShort, breakoutPriceShort, breakoutPriceShort+2*atr, dcExitPrice)
	_, firstShortQty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideSell, 0)

	firstShortPosition := &Position{
		InstrumentType:   this.InstrumentType,
		Symbol:           this.Symbol,
		EntryPrice:       breakoutPriceShort,
		Qty:              -firstShortQty,
		Side:             exchange.PositionSideShort,
		LiquidationPrice: exitPrice,
	}
	firstShortPosition.SetExtStruct(position)

	if firstLongQty == 0 || firstShortQty == 0 {
		this.AlertMsgf("开仓数量不能为 0，请检查是否余额过少或参数设置有误")
		return false
	}

	if leverageUpdated, err := this.SetInitLeverage(firstLongPosition, firstShortPosition); err != nil {
		this.AlertMsgf("设置初始杠杠率错误: %s", err)
		return false
	} else if leverageUpdated && this.controller.IsExchange(exchange.OKEx) {
		// OK 调杠杠率会取消订单，所以直接重新挂单
		orderIDsMatched = false
		openOrderCount = 0
	}

	if orderIDsMatched {
		// 挂单和本地存储一致
		this.Infof("local openOrderIDs matched")
		if hasTriggeredOrder {
			// 处理已触发订单
			this.handleTriggeredOrders(0, openOrders)
		} else {
			// 确认数量、价格是否一致，不同则修改订单
			for _, order := range openOrders {
				orderIndex := 0
				if order.Side == exchange.OrderSideBuy {
					orderIndex = utils.IndexOf(order.OrderID, this.Long.OpenOrders.IDs())
				} else {
					orderIndex = utils.IndexOf(order.OrderID, this.Short.OpenOrders.IDs())
				}

				price, qty := this.calculateOpenOrderPriceAndQtyByIndex(order.Side, orderIndex)

				if this.isOrderNeedUpdate(order, qty, price, position) {
					this.Warnf("order(%s) need update (qty: %v, price: %v) => (qty: %v, price: %v)", order.OrderID, order.Qty, order.TriggerPrice, qty, price)
					// 不一致，修改
					newOrder, err := this.updateOpenOrder(order, order.Side, price, atr, qty)
					if err != nil {
						this.AlertMsgf("修改订单[%s]失败: %s", order.OrderID, err)
						return false
					}

					this.updateOpenOrderStorage(order.OrderID, newOrder)
					this.LastOrderUpdatedAt = time.Now()
					this.controller.storage.Save()
				}
			}
		}
	} else {
		// 挂单和本地存储不一致
		this.Warnf("local openOrderIDs not match")

		if openOrderCount > 0 {
			// 取消所有挂单
			_, err := this.controller.Exchange.CancelAllOrders(this.InstrumentType, this.OrderType(), this.Symbol)
			if err != nil {
				this.AlertMsgf("取消所有订单失败，error: %s", err)
				return false
			}
		}

		this.Long.OpenOrders = []*Order{}
		this.Short.OpenOrders = []*Order{}
		this.CloseOrders = []*Order{} // 清空平仓单

		// 多空新增挂单，并记录到本地缓存
		for i := 0; i < this.Config.MaxOpenUnits; i++ {
			price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideBuy, i)
			order, err := this.createOpenOrder(exchange.OrderSideBuy, price, qty)
			if err != nil {
				this.AlertMsgf("创建订单失败: %s", err)
				return false
			}
			this.Long.OpenOrders = append(this.Long.OpenOrders, SetOrderExt(order, OrderCategoryLong, i+1))
			this.Long.Prices[i] = price
			this.Long.Qtys[i] = qty
		}
		for i := 0; i < this.Config.MaxOpenUnits; i++ {
			if this.Config.DisableShort {
				break
			}
			price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideSell, i)
			order, err := this.createOpenOrder(exchange.OrderSideSell, price, qty)
			if err != nil {
				this.AlertMsgf("创建订单失败: %s", err)
				return false
			}
			this.Short.OpenOrders = append(this.Short.OpenOrders, SetOrderExt(order, OrderCategoryShort, i+1))
			this.Short.Prices[i] = price
			this.Short.Qtys[i] = qty
		}

		this.LastOrderUpdatedAt = time.Now()
	}
	this.controller.storage.Save()
	return true
}

func (this *Turtle) positionHandler(position *Position, openOrders []*Order) (success bool) {
	positionSide := position.Side // 持仓方向

	if ok, msg := this.checkOpenPositionData(); !ok {
		this.AlertMsgf("开仓数据检测不通过: %s，将使用实时数据计算修复", msg)
		this.tryFixOpenPositionData()
		if ok, msg := this.checkOpenPositionData(); !ok {
			this.AlertMsgf("修复开仓数据失败: %s", msg)
			return
		}
	}

	for i := 0; i < this.Config.MaxOpenUnits; i++ {
		price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideBuy, i)
		this.Long.Prices[i] = price
		this.Long.Qtys[i] = qty

		price, qty = this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideSell, i)
		this.Short.Prices[i] = price
		this.Short.Qtys[i] = qty
	}

	hasTriggeredIncreaseOrder := false  // 有触发的加仓订单
	hasTakeProfitOrder := false         // 是否已存在止盈订单
	var triggeredProfitOrder *Order     // 触发的止盈订单
	increaseOrders := make([]*Order, 0) // 加仓订单
	closeOrders := make([]*Order, 0)    // 平仓订单
	for _, openOrder := range openOrders {
		if openOrder.Side.EqualsPositionSide(positionSide) {
			// 加仓订单
			increaseOrders = append(increaseOrders, openOrder)

			if openOrder.IsTriggered() {
				hasTriggeredIncreaseOrder = true
			}
		} else {
			if openOrder.IsCloseOrder() {
				// 平仓订单
				closeOrders = append(closeOrders, openOrder)

				if openOrder.IsTakeProfitOrder(position) {
					// 有止盈订单
					hasTakeProfitOrder = true

					if openOrder.IsTriggered() {
						triggeredProfitOrder = openOrder
					}
				}
			} else {
				// 反向开仓单，取消
				if err := this.controller.Exchange.CancelOrder(this.InstrumentType, this.OrderType(), this.Symbol, openOrder.OrderID); err != nil {
					this.AlertMsgf("取消订单[%s]失败: %s", openOrder.OrderID, err)
					return false
				}
			}
		}
	}

	positionUnits, success := this.positionSizeCheck(position, increaseOrders)
	if !success {
		return false
	}

	// 调整杠杠率
	// 一个头寸时止损价=持仓价-2ATR，2个时=持仓价-1.75ATR，3个时=持仓价-1.5ATR，4个时=持仓价-1.25ATR
	var actualExitPrice float64 // 止损委托价
	var atrExitPrice float64    // ATR 止损委托价
	var exitPrice float64       // DC 退出价
	atr := this.Open.ATR.Value
	positionPrice := position.EntryPrice // 持仓均价
	if positionSide == exchange.PositionSideLong {
		atrExitPrice = positionPrice - atr*1.25 - (4-positionUnits)*0.25*atr
		exitPrice = this.Long.ExitPrice.Value // 多头使用 DCLow 作为退出价
	} else {
		atrExitPrice = positionPrice + atr*1.25 + (4-positionUnits)*0.25*atr
		exitPrice = this.Short.ExitPrice.Value // 空头使用 DCHigh 作为退出价
	}

	// 如果是展期来的合约，之前有仓位平仓的，用止损比例倒推止损价
	if this.RotatePlan != nil {
		// 固定损失比例方案
		// atrExitPrice = this.calculateRotateStopPrice(int(positionUnits), this.storage.RotatePlan.ClosePositionPNL, positionPrice, position.Qty)

		// 新止损价 = 旧止损价 + 现价差 方案
		fromStopLossPrice := this.RotatePlan.From.StopLossPrice
		if positionSide == exchange.PositionSideLong {
			atrExitPrice = fromStopLossPrice + (positionUnits-float64(this.RotatePlan.From.Units))*0.5*atr + this.RotatePlan.PriceDelta
		} else {
			atrExitPrice = fromStopLossPrice - (positionUnits-float64(this.RotatePlan.From.Units))*0.5*atr + this.RotatePlan.PriceDelta
		}
	}

	if this.Fix.StopPrice > 0 {
		atrExitPrice = this.Fix.StopPrice
	}

	this.Position.SetFloat64(ExtKeyTakeProfitPrice, exitPrice)

	closeOrderSide := exchange.OrderSideBuy
	if positionSide == exchange.PositionSideLong {
		closeOrderSide = exchange.OrderSideSell
	}
	dcExitPrice := this.getCloseOrderPrice(closeOrderSide, exitPrice) // 根据 DC 退出触发价得到委托价
	actualExitPrice = this.getActualExitPrice(positionSide, positionPrice, atrExitPrice, dcExitPrice)

	if (positionSide == exchange.PositionSideLong && dcExitPrice > atrExitPrice) ||
		(positionSide == exchange.PositionSideShort && dcExitPrice < atrExitPrice) {
		if (int(positionUnits) == this.Config.MaxOpenUnits) || this.Fix.FixPositionTime != nil {
			this.ExitByDC = true
		}
	}

	// DC 触发价格可以止盈时应该有止盈单
	canTakeProfit := (positionSide == exchange.PositionSideLong && exitPrice > positionPrice) || (positionSide == exchange.PositionSideShort && exitPrice < positionPrice)

	if canTakeProfit || hasTakeProfitOrder {
		// 止损价设置为爆仓价
		// 这样做的好处：如果价格击穿止盈价未能成交，可以使用退出价退出而不是被爆仓
		// 设为爆仓价而不是一个中间价如持仓价，因为击穿后可能回调，所以不宜过早退出
		actualExitPrice = position.LiquidationPrice
	} else {
		if this.ExitByDC {
			actualExitPrice = dcExitPrice
		}
	}

	this.Position.SetFloat64(ExtKeyStopLossPrice, actualExitPrice)

	profitOrderOnly := false
	if canTakeProfit && this.controller.IsExchange(exchange.OKEx) {
		// ok 不能挂超过持仓数量的平仓单，所以只挂止盈单，如有止损单则取消
		profitOrderOnly = true
	}

	if len(closeOrders) == 0 {
		// 没有平仓单
		if canTakeProfit {
			// 优先创建止盈单
			if order, err := this.createProfitOrder(closeOrderSide, exitPrice, math.Abs(position.Qty)); err != nil {
				this.AlertMsgf("创建止盈订单失败: %s", err)
				return false
			} else {
				// 记录到本地
				this.CloseOrders = append(this.CloseOrders, SetOrderExt(order, OrderCategoryTakeProfit, 0))
				this.LastOrderUpdatedAt = time.Now()
				this.controller.storage.Save()
			}
		}

		if !profitOrderOnly {
			// 创建止损订单
			if order, err := this.createCloseOrder(closeOrderSide, actualExitPrice, math.Abs(position.Qty)); err != nil {
				this.AlertMsgf("创建止损订单失败: %s", err)
				return false
			} else {
				// 记录到本地
				this.CloseOrders = []*Order{SetOrderExt(order, OrderCategoryStopLoss, 0)}
				this.LastOrderUpdatedAt = time.Now()
				this.controller.storage.Save()
			}
		}
	} else {
		if len(closeOrders) > 2 {
			this.AlertMsgf("当前有超过两个止盈止损单")
			return false
		}

		for _, closeOrder := range closeOrders {
			if utils.IndexOf(closeOrder.OrderID, this.CloseOrders.IDs()) == -1 {
				ordType := OrderCategoryStopLoss
				if closeOrder.IsTakeProfitOrder(position) {
					ordType = OrderCategoryTakeProfit
				}
				this.CloseOrders = append(this.CloseOrders, SetOrderExt(closeOrder, ordType, 0))
			}

			if closeOrder.IsTriggered() {
				// 已触发的平仓单 不用再修改触发价
				continue
			}

			if closeOrder.IsTakeProfitOrder(position) {
				if !canTakeProfit {
					// 当前条件已不应该止盈但是有止盈单，说明止盈已被触发
					continue
				}

				// 止盈单触发价应该和退出价相同
				if this.isOrderNeedUpdate(closeOrder, closeOrder.Qty, exitPrice, position) {
					newOrder, err := this.updateProfitOrder(closeOrder, closeOrder.Side, exitPrice, math.Abs(position.Qty))
					if err != nil {
						this.AlertMsgf("修改止盈订单失败: %s", err)
						return false
					}

					this.updateCloseOrderStorage(closeOrder.OrderID, newOrder, true)
					this.LastOrderUpdatedAt = time.Now()
				}
			} else if profitOrderOnly {
				// 取消止损单
				if err := this.controller.Exchange.CancelOrder(this.InstrumentType, this.OrderType(), this.Symbol, closeOrder.OrderID); err != nil {
					this.AlertMsgf("取消止损订单[%s]失败: %s", closeOrder.OrderID, err)
					return false
				}
			} else {
				// 止损订单委托价应该和实际退出价相同
				stopPrice := this.getCloseTriggerPrice(closeOrder.Side, actualExitPrice)
				if this.isOrderNeedUpdate(closeOrder, closeOrder.Qty, stopPrice, position) {
					newOrder, err := this.updateCloseOrder(closeOrder, closeOrder.Side, actualExitPrice, math.Abs(position.Qty))
					if err != nil {
						this.AlertMsgf("修改止损订单失败: %s", err)
						return false
					}

					this.updateCloseOrderStorage(closeOrder.OrderID, newOrder, false)
					this.LastOrderUpdatedAt = time.Now()
				}
			}
		}

		// 无止盈单但应该有，需创建
		if canTakeProfit && !hasTakeProfitOrder {
			if order, err := this.createProfitOrder(closeOrderSide, exitPrice, math.Abs(position.Qty)); err != nil {
				this.AlertMsgf("创建止盈订单失败: %s", err)
				return false
			} else {
				this.CloseOrders = append(this.CloseOrders, SetOrderExt(order, OrderCategoryTakeProfit, 0))
				this.LastOrderUpdatedAt = time.Now()
			}
		}

		if canTakeProfit {
			this.Position.SetFloat64(ExtKeyLockedPNL, this.calculateProfitOrderPNL(exitPrice, positionPrice, math.Abs(position.Qty)))
		}

		this.controller.storage.Save()
	}

	if hasTriggeredIncreaseOrder {
		// 处理已触发的加仓订单
		this.handleTriggeredOrders(math.Abs(position.Qty), increaseOrders)
	}

	if triggeredProfitOrder != nil && triggeredProfitOrder.OrderID != "" {
		this.TakeTriggeredProfitOrder(triggeredProfitOrder)
	}

	// 注意：因为 OKEx 调杠杠率需要先取消订单，所以放到最后处理，先处理完平仓单、触发单等逻辑
	if canTakeProfit || hasTakeProfitOrder {
		// 有止盈条件后直接设置杠杠率到最大，因为此时已不能通过调整杠杠率让爆仓价与止盈价相同了
		this.SetLeverageToMax(position)
	} else {
		this.AdjustLiquidationPrice(position, actualExitPrice)
	}

	this.getBalance()

	return true
}

func (this *Turtle) positionSizeCheck(position *Position, increaseOrders []*Order) (units float64, success bool) {
	var openSize float64                    // 首单单位头寸大小
	localOpenOrders := this.Long.OpenOrders // 本地记录的开仓订单
	positionSide := position.Side           // 持仓方向
	openOrderSide := exchange.OrderSideBuy
	if positionSide == exchange.PositionSideLong {
		openSize = this.Long.OpenSize
	} else if positionSide == exchange.PositionSideShort {
		openSize = this.Short.OpenSize
		localOpenOrders = this.Short.OpenOrders
		openOrderSide = exchange.OrderSideSell
	}

	positionQty := math.Abs(position.Qty) // 持仓数量，取正值
	positionPrice := position.EntryPrice  // 持仓均价
	positionSize := this.Qty2Size(this.Symbol, positionPrice, positionQty)

	// 反向合约每单价值相同数量不同，正向合约每单数量相同
	var allUnitsSize float64  // 满仓时（四个单位）总头寸
	var positionUnits float64 // 当前持仓单位个数，1~4
	for i := 0; i < this.Config.MaxOpenUnits; i++ {
		price, qty := this.calculateOpenOrderPriceAndQtyByIndex(openOrderSide, i)
		allUnitsSize += this.Qty2Size(this.Symbol, price, qty)

		if this.controller.Exchange.IsReverseSymbol(this.InstrumentType, this.Symbol) {
			positionUnits = math.Round(positionSize / openSize)
		} else {
			positionUnits = math.Round(float64(positionQty) / float64(qty))
		}
	}

	this.Infof("当前持仓头寸数量: %.4f/%.4f", positionSize, allUnitsSize)

	positionUnits = math.Max(positionUnits, 1)
	this.Infof("positionUnits: %v", positionUnits)

	if int(positionUnits) > this.Config.MaxOpenUnits {
		this.Warnf("当前计算持仓头寸 %v 单位已大于最大头寸", positionUnits)
		positionUnits = float64(this.Config.MaxOpenUnits)
	}

	this.Position.SetInt(ExtKeyUnits, int(positionUnits))

	positionSize += this.Qty2Size(this.Symbol, positionPrice, this.Fix.PositionQty)

	unfilledSize := allUnitsSize - positionSize // 根据持仓计算的待加仓头寸
	unfilledSizeInOrders := 0.0                 // 根据订单计算的待加仓头寸

	localIncreaseOrderIDs := []string{} // 本地记录的未成交加仓订单
	for _, increaseOrder := range increaseOrders {
		// 订单的未成交部分头寸大小
		unfilledSizeInOrders += this.Qty2Size(this.Symbol, increaseOrder.TriggerPrice, increaseOrder.Qty-increaseOrder.ExecQty)

		// orderID 是否在本地
		existInLocal := false
		for _, localOpenOrder := range localOpenOrders {
			orderID := localOpenOrder.OrderID
			if orderID == increaseOrder.OrderID {
				existInLocal = true
				localIncreaseOrderIDs = append(localIncreaseOrderIDs, orderID)
				break
			}
		}
		if !existInLocal {
			this.Infof("increaseOrder %s not find", increaseOrder.OrderID)
			this.AlertMsgf("订单 [%s] 在本地不存在，请使用 .confirmOrders 命令确认订单", increaseOrder.OrderID)
			return positionUnits, false
		}
	}

	unfilledSizeDelta := math.Abs(unfilledSize - unfilledSizeInOrders) // 持仓中待加仓头寸数量和本地记录差值
	// this.Infof("持仓中待加仓头寸数量和本地记录差值: %.4f", unfilledSizeDelta)

	// 待加仓头寸相差值不应该大于总头寸的 5%
	if unfilledSizeDelta/allUnitsSize > 0.05 {
		// 是否有 reject 的订单
		for _, localOpenOrder := range localOpenOrders {
			orderID := localOpenOrder.OrderID
			if utils.IndexOf(orderID, localIncreaseOrderIDs) == -1 {
				if localOpenOrder.IsOpen() {
					// 挂单中才需要再查一次
					order, err := this.controller.Exchange.GetOrderByOrig(*localOpenOrder)
					if err != nil {
						this.Warnf("get order(%s) err: %s", orderID, err)
						this.AlertMsgf("获取订单[%s]失败: %s", orderID, err)
						continue
					}
					localOpenOrder = order
				}
				if !localOpenOrder.IsOpen() {
					// 订单可能会被系统 Reject 或 Cancel 而未成交或部分成交
					// 非进行中订单但未成交的数量也计算到待加仓头寸中
					unfilledSizeInOrders += this.Qty2Size(this.Symbol, localOpenOrder.TriggerPrice, localOpenOrder.Qty-localOpenOrder.ExecQty)
					unfilledSizeDelta = math.Abs(unfilledSize - unfilledSizeInOrders)
				}
			}
		}

		// 平仓订单如果有成交，已成交的数量应加从 unfilledSize 减去
		for _, closeOrder := range this.CloseOrders {
			closeOrderID := closeOrder.OrderID
			if closeOrder.IsOpen() {
				order, err := this.controller.Exchange.GetOrderByOrig(*closeOrder)
				if err != nil {
					this.Warnf("get order(%s) err: %s", closeOrderID, err)
					this.AlertMsgf("获取订单[%s]失败: %s", closeOrderID, err)
					continue
				}
				closeOrder = order
			}
			if closeOrder.ExecQty != 0 {
				unfilledSize -= this.Qty2Size(this.Symbol, positionPrice, closeOrder.ExecQty)
				unfilledSizeDelta = math.Abs(unfilledSize - unfilledSizeInOrders)
			}
		}
	}

	if this.Fix.StopPrice <= 0 && !this.ExitByDC && unfilledSizeDelta/allUnitsSize > 0.05 {
		// 这种情况也有可能正在成交导致的
		this.unfilledSizeAlert(unfilledSizeInOrders-unfilledSize, allUnitsSize)
		return positionUnits, false
	}

	return positionUnits, true
}

func (this *Turtle) isTrading() bool {
	if utils.SliceContains([]string{exchange.MetaTrader, exchange.CTP}, this.controller.Exchange.GetName()) {
		this.controller.Exchange.CacheInstruments(true) // 每次都获取最新数据，确保交易状态是最新的
		instrument, err := this.controller.Exchange.GetInstrument(this.InstrumentType, this.Symbol)
		if err != nil {
			this.Errorf("get instrument error: %v", err)
			return false
		}
		// if time.Since(instrument.UpdateTime) > 60*time.Second {
		// 	// 也可能由于流动性差 tick 不活跃
		// 	this.Errorf("instrument update time too old, tick may not working: %s", this.Symbol,instrument.UpdateTime)
		// 	return false
		// }
		return instrument.Status == exchange.InstrumentStatusContinuous
	}
	return true
}

// 主要用于控制不同 controller/symbol 之间交易所 API 的调用频率
func (this *Turtle) sleepBeforeHandle() {
	if this.Config.SleepSecondsBeforeHandle <= 0 || this.Config.SleepSecondsBeforeHandle > 100 {
		return
	}
	sec := this.Config.SleepSecondsBeforeHandle
	this.Debugf("sleep %d seconds before handle", sec)
	time.Sleep(time.Duration(sec) * time.Second)
}

func (this *Turtle) handlePositionAndOrders() {
	if this.Status != RUNNING {
		// 当前程序不在运行中
		this.Infof("not running")
		return
	}

	if !utils.IsTimeExpired(this.PauseTo) {
		this.Infof("will pause util %s", this.PauseTo)
		return
	}
	if this.PauseTo != nil {
		this.SetPauseTo(nil, PauseReasonExpired)
	}

	if !this.isTrading() {
		this.Infof("not tradable now")
		return
	}

	this.sleepBeforeHandle()

	// 防止并发处理订单，上锁
	// 此时如果有并发调用该函数的，将会等待直到解锁
	this.Infof("lock handlePositionAndOrders")
	this.controller.handleMutex.Lock()

	defer func() {
		// 函数运行结束时解锁
		this.Infof("unlock handlePositionAndOrders")
		this.controller.handleMutex.Unlock()

		this.checkLastSuccessTime()
	}()

	this.Infof("handlePositionAndOrders start...")
	this.lastHandleCheckAndWait()
	this.dcExpiredCheck()

	// 获取当前未完成的订单
	openOrders, err := this.controller.Exchange.GetOpenOrders(this.InstrumentType, this.OrderType(), this.Symbol)
	if err != nil {
		this.AlertMsgf("获取当前挂单失败，error: %s", err)
		return
	}

	// 打印当前挂单
	for i, order := range openOrders {
		this.Debugf("[open order %d] %#v", i, order)
	}

	// 获取持仓
	position, err := this.getCurrentPosition(false)
	if position == nil {
		this.AlertMsgf("获取当前持仓失败: %s", err)
		return
	}

	this.Position.MarkPrice = position.MarkPrice
	nowTime := time.Now()
	this.Position.UpdateTime = &nowTime

	if position.Qty == 0 && this.Position.Qty != 0 {
		// 平仓了，不要重置本地的开仓价、爆仓价、pnl
	} else {
		this.Position.EntryPrice = position.EntryPrice
		this.Position.LiquidationPrice = position.LiquidationPrice
		this.Position.Margin = position.Margin

		if rePnl, unPnl, pnl, asset, assetPrice, err := this.GetPNL(position); err != nil {
			this.Errorf("get pnl error: %s", err)
		} else {
			this.Position.RealisedPNL, this.Position.UnrealisedPNL = rePnl, unPnl
			this.Position.SetFloat64(ExtKeyPNL, pnl)
			this.Position.SetString(ExtKeyAsset, asset)
			this.Position.SetFloat64(ExtKeyAssetPrice, assetPrice)
		}
	}

	defer func() {
		this.Position.LastPrice, err = this.getLastPrice() // 记录一下现价
		if err != nil {
			this.AlertMsgf("获取最新价格失败: %s", err)
		}

		// 如果上次持仓和本次获取的不同，则更新本地存储
		if this.Position.Qty != position.Qty {
			this.Position.Qty = position.Qty
			this.addPositionChange(this.Position)
			this.calculateEstimatedLoss()
			this.controller.storage.Save()
			this.SendMsgf(fmt.Sprintf("仓位变动, 现价: %v", this.Position.LastPrice))
			this.sendStatus(false)

			go func() {
				time.Sleep(20 * time.Second)
				this.queryExecOrders()
			}()
		}
	}()

	if position.Qty == 0 && this.Position.Qty == 0 { // 无持仓处理流程
		// 禁用空头时，根据模拟开仓情况来判断当前是否应该使用 SecondaryBreakoutPeriod
		// 每根 K 线模拟一次
		simuTimeout := false
		if this.lastSimulateTime == nil {
			simuTimeout = true
		} else if time.Since(*this.lastSimulateTime) >= (time.Duration(this.Config.PeriodHour) * time.Hour) {
			simuTimeout = true
		}
		if this.Config.SecondaryBreakoutPeriod != 0 && this.Config.DisableShort && simuTimeout {
			toTime := time.Now()
			this.lastSimulateTime = &toTime
			periods := math.Max(float64(this.Config.BreakoutPeriod), float64(this.Config.SecondaryBreakoutPeriod))
			fromTime := toTime.Add(-time.Duration(periods*float64(this.Config.PeriodHour)) * time.Hour)

			sim := &Simulator{
				controller: this.controller,
				fromTime:   &fromTime,
				toTime:     &toTime,
				symbolCode: this.SymbolCode,
				config:     this.Config,
				fixedCash:  this.Balance.Total,
			}

			_, _, err := sim.Simulate()
			if err != nil {
				this.ErrorMsgf("模拟运行出错: %s", err)
			} else if sim.positionUnits == 0 && sim.breakoutPeriod != this.Open.BreakoutPeriod {
				// 模拟结果与当前设置不一致，更新设置
				this.Open.BreakoutPeriod = sim.breakoutPeriod
				this.Long.BreakoutPrice.set(this, this.Long.BreakoutPrice.Value, time.Unix(0, 0), false, "Long.BreakoutPrice", "through simulation")

				this.dcExpiredCheck()
			}
		}

		if success := this.noPositionHandler(position, openOrders); !success {
			return
		}
	} else if position.Qty != 0 { // 有持仓处理流程
		if success := this.positionHandler(position, openOrders); !success {
			return
		}
	} else if this.Position.Qty != 0 { // 本地记录有仓位，实际获取无仓位，本次 Turtle 结束
		this.positionClosedHandler(len(openOrders) > 0)
	}

	this.LastHandleSuccessAt = time.Now()
	this.calculateEstimatedLoss()

	if err := this.controller.storage.Save(); err != nil {
		this.controller.AlertMsgf("storage 存储失败: %s", err)
	}
}

func (this *Turtle) lastHandleCheckAndWait() {
	// bn 因为创建的订单可能无法立即获取到的问题，所以不在过短时间内重复调用 handlePositionAndOrders
	if !this.controller.IsExchange(exchange.Binance) {
		return
	}

	now := time.Now().Unix()
	secs := now - this.LastHandleSuccessAt.Unix()
	if secs < 20 {
		this.Infof("lastHandleCheckAndWait")
		time.Sleep(time.Second * time.Duration(20-secs))
	}
}

func (this *Turtle) addPositionChange(position *Position) {
	var lastPosition *Position
	if len(this.PositionChanges) > 0 {
		lastPosition = this.PositionChanges[len(this.PositionChanges)-1]
	}
	if lastPosition == nil || (lastPosition != nil && lastPosition.Qty != position.Qty) {
		positionCopy := &Position{}
		if err := copier.CopyWithOption(positionCopy, this.Position, copier.Option{DeepCopy: true}); err != nil {
			this.Errorf("append position changes error: %s", err)
		}
		positionCopy.UpdateTime = position.UpdateTime
		this.PositionChanges = append(this.PositionChanges, positionCopy)
	}
}

func getUnitsLossRatio(units int) float64 {
	lossRatio := 2.0
	if units == 2 {
		lossRatio = 3.5
	} else if units == 3 {
		lossRatio = 4.5
	} else if units == 4 {
		lossRatio = 5
	}
	return lossRatio
}

// func (this *Turtle) calculateRotateStopPrice(units int, closePositionPNL, newPositionPrice, positionQty float64) float64 {
// 	// 1单位头寸 损失比例 = OpenSizePercent * 2
// 	// 2单位头寸 损失比例 = OpenSizePercent * 3.5
// 	// 3单位头寸 损失比例 = OpenSizePercent * 4.5
// 	// 4单位头寸 损失比例 = OpenSizePercent * 5
// 	// 新止损损失 = 平仓盈亏 + 单位头寸损失 = 新止损价值 - 新开仓价值
// 	lossRatio := 2.0
// 	if units == 2 {
// 		lossRatio = 3.5
// 	} else if units == 3 {
// 		lossRatio = 4.5
// 	} else if units == 4 {
// 		lossRatio = 5
// 	}
// 	lossRatio *= this.Config.OpenSizePercent
// 	stopLoss := closePositionPNL + this.storage.OpenMarginBalance*lossRatio
// 	newPositionValue := this.Qty2Size(this.Symbol, newPositionPrice, positionQty)
// 	// 正向合约亏损绝对值 = newPositionValue - stopValue
// 	// 反向合约亏损绝对值 = stopValue - newPositionValue
// 	stopValue := newPositionValue - stopLoss
// 	if this.controller.Exchange.IsReverseSymbol(this.Symbol) {
// 		stopValue = newPositionValue + stopLoss
// 	}

// 	return this.controller.Exchange.CalcPrice(this.Symbol, positionQty, stopValue)
// }

func (this *Turtle) checkOpenPositionData() (bool, string) {
	if this.Open.ATR.Value == 0 {
		return false, "ATR 为 0"
	}

	if this.Long.OpenPrice == 0 || this.Short.OpenPrice == 0 {
		return false, "开仓价格为 0"
	}

	if this.Long.OpenSize == 0 || this.Short.OpenSize == 0 {
		return false, "开仓头寸为 0"
	}

	_, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideBuy, 0)
	if qty == 0 {
		return false, "看多订单数量计算结果为 0"
	}

	_, qty = this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideSell, 0)
	if qty == 0 {
		return false, "看空订单数量计算结果为 0"
	}

	return true, ""
}

func (this *Turtle) tryFixOpenPositionData() {
	this.openBalanceUpdateCheck()

	if this.Long.OpenPrice == 0 || this.Short.OpenPrice == 0 {
		this.Long.OpenPrice = this.Long.BreakoutPrice.Value
		this.Short.OpenPrice = this.Short.BreakoutPrice.Value
	}

	if this.Long.OpenSize == 0 || this.Short.OpenSize == 0 {
		this.updateOpenSize()
	}
}

// 平仓后需要处理的事情放到这里
func (this *Turtle) positionClosedHandler(hasOpenOrders bool) {
	if hasOpenOrders {
		// 只要存在挂单，都应该取消
		_, err := this.controller.Exchange.CancelAllOrders(this.InstrumentType, this.OrderType(), this.Symbol)
		if err != nil {
			this.AlertMsgf("取消订单失败: %s", err)
		}
	}

	if this.StopOnExit {
		this.FinishWithReason(FinishReasonStopOnExit)
	} else {
		if this.Position.GetFloat64(ExtKeyLockedPNL) > 0 {
			this.FinishWithReason(FinishReasonTakeProfit)
		} else {
			this.FinishWithReason(FinishReasonStopLoss)
		}
	}
}

// 计算止盈可以获得的盈利
func (this *Turtle) calculateProfitOrderPNL(profitPirce float64, positionPrice float64, positionQty float64) float64 {
	profitValue := this.Qty2Size(this.Symbol, profitPirce, positionQty)
	positionValue := this.Qty2Size(this.Symbol, positionPrice, positionQty)
	return math.Abs(profitValue - positionValue)
}

// 待加仓头寸数量异常警报
func (this *Turtle) unfilledSizeAlert(deltaSize float64, allUnitsSize float64) {
	this.AlertMsgf("本次开仓总头寸应为: %.4f，目前持仓+已平仓+开仓单未成交=%.4f", allUnitsSize, allUnitsSize+deltaSize)

	toDoMsg := ""
	lastPrice, err := this.getLastPrice()
	if err != nil {
		this.AlertMsgf("获取最新价格失败: %s", err)
		return
	}

	qty := this.Size2Qty(this.Symbol, lastPrice, deltaSize)
	if deltaSize > 0 {
		toDoMsg = fmt.Sprintf("订单需要减少的数量 = %v", qty)
	} else {
		toDoMsg = fmt.Sprintf("订单需要增加的数量 = %v", -qty)
	}
	toDoMsg += fmt.Sprintf("\n需要执行的命令: .setFixPositionQty %v AuthCode 或 .fixPosition stopPrice comment", -qty)

	this.SendMsgf(toDoMsg)
}

// 根据开仓单序号返回开仓订单价格、数量
func (this *Turtle) calculateOpenOrderPriceAndQtyByIndex(side exchange.OrderSide, orderIndex int) (price float64, qty float64) {
	var openSize float64
	var breakoutPrice float64
	atr := this.Open.ATR.Value
	if side == exchange.OrderSideBuy {
		openSize = this.Long.OpenSize
		breakoutPrice = this.Long.OpenPrice
		price = breakoutPrice + float64(orderIndex)*0.5*atr
	} else {
		openSize = this.Short.OpenSize
		breakoutPrice = this.Short.OpenPrice
		price = breakoutPrice - float64(orderIndex)*0.5*atr
	}

	// 反向合约每单价值相同数量不同，正向合约每单数量相同
	if this.controller.Exchange.IsReverseSymbol(this.InstrumentType, this.Symbol) {
		qty = this.Size2Qty(this.Symbol, price, openSize)
	} else {
		qty = this.Size2Qty(this.Symbol, breakoutPrice, openSize)
	}

	return
}

func (this *Turtle) OrderType() exchange.OrderType {
	if this.controller.IsExchange(exchange.Binance) && this.Config.UseStopMarket {
		// 暂只支持币安 StopMarket，主要因为币安触发限价单占用资金量很大
		return exchange.StopMarket
	}
	return exchange.StopLimit
}

// 创建开仓订单，返回订单ID
func (this *Turtle) createOpenOrder(side exchange.OrderSide, stopPx float64, orderQty float64) (*Order, error) {
	price := this.getOpenOrderPriceByStopPrice(side, stopPx, this.Open.ATR.Value)

	if price <= 0 {
		return nil, fmt.Errorf("订单价格需大于 0，请检查 DC 和 ATR 是否正常")
	}

	if orderQty == 0 {
		return nil, fmt.Errorf("订单数量为 0，请检查余额或参数是否过小")
	}

	args := exchange.CreateOrderArgs{
		InstrumentType:   this.InstrumentType,
		Symbol:           this.Symbol,
		Side:             side,
		Price:            price,
		TriggerPrice:     stopPx,
		TriggerPriceType: exchange.TriggerPriceTypeLast,
		Qty:              orderQty,
		Type:             this.OrderType(),
	}
	return this.controller.Exchange.CreateOrder(args)
}

// 吃掉已触发的止盈订单
func (this *Turtle) TakeTriggeredProfitOrder(order *Order) {
	triggeredTime := order.UpdateTime
	this.Infof("profit order[%s] triggeredTime %s", order.OrderID, triggeredTime)

	waitingSeconds := this.Config.WaitingSecondsAfterTriggeredLong
	if order.Side == exchange.OrderSideSell {
		waitingSeconds = this.Config.WaitingSecondsAfterTriggeredShort
	}

	if !triggeredTime.Add(time.Second * time.Duration(waitingSeconds)).Before(time.Now()) {
		// 订单触发事件未超过 waitingSeconds
		return
	}

	for i := 0; i < 3; i++ {
		// 最多尝试 3 次
		newOrder, err := this.takeOrder(order, order.Side, true, true)
		if err != nil {
			this.AlertMsgf("吃单创建失败: %s", err)
			return
		}

		this.TakeOrders = append(this.TakeOrders, SetOrderExt(newOrder, OrderCategoryTakeProfit, 0))

		this.LastOrderUpdatedAt = time.Now()
		time.Sleep(time.Second * 5)

		takenOrder, err := this.controller.Exchange.GetOrderByOrig(*newOrder)
		if err != nil {
			this.Warnf("get order by ID: %s error: %s", newOrder.OrderID, err)
		} else {
			if takenOrder.Qty == takenOrder.ExecQty {
				// 已完全成交
				this.Infof("profit order[%s] taken", newOrder.OrderID)
				break
			} else if err == nil {
				// 吃单未成交，在新订单上修改吃单
				order = newOrder
			}
		}
	}
}

func (this *Turtle) getLastPrice() (float64, error) {
	return this.controller.Exchange.GetLastPrice(this.InstrumentType, this.Symbol, false)
}

// 成交已触发但未成交的订单
func (this *Turtle) takeOrder(order *exchange.Order, side exchange.OrderSide, isClose, isTakeProfit bool) (*Order, error) {
	if this.controller.IsExchange(exchange.CTP) {
		return nil, errors.New("exchange not supported yet")
	}
	lastPrice, err := this.getLastPrice()
	if err != nil {
		return nil, fmt.Errorf("get last price failed: %s", err)
	}
	var price float64
	// 按最新价格滑点 0.49% 吃单
	if side == exchange.OrderSideBuy {
		price = lastPrice * (1 + 0.0049)
	} else {
		price = lastPrice * (1 - 0.0049)
	}
	return this.controller.Exchange.UpdateOrder(*order, &exchange.UpdateOrderArgs{
		InstrumentType:   this.InstrumentType,
		Price:            price,
		ReduceOnly:       isClose,
		TriggerPriceType: exchange.TriggerPriceTypeLast,
		Type:             this.OrderType(),
	})
}

// 按市价开仓 qty 数量
func (this *Turtle) rotateOpenPosition(side exchange.PositionSide, qty float64) (bool, error) {
	rotateOpenPositionSlippageRate := this.Config.RotateOpenPositionSlippageRate
	rotateOpenPositionRetryTimes := this.Config.RotateOpenPositionRetryTimes
	rotateOpenPositionWaitSeconds := this.Config.RotateOpenPositionWaitSeconds

	if rotateOpenPositionSlippageRate == 0 {
		rotateOpenPositionSlippageRate = 0.005
	}

	if rotateOpenPositionRetryTimes == 0 {
		rotateOpenPositionRetryTimes = 3
	}

	if rotateOpenPositionWaitSeconds == 0 {
		rotateOpenPositionWaitSeconds = 5
	}

	for i := 0; i < (rotateOpenPositionRetryTimes + 1); i++ {
		// 最多尝试 rotateOpenPositionRetryTimes 次
		position, _ := this.getCurrentPosition(false)
		if position == nil {
			return false, errors.New("get position failed")
		}

		orderQty := qty - math.Abs(position.Qty)
		if orderQty <= 0 {
			break
		}

		if i == rotateOpenPositionRetryTimes && orderQty > 0 {
			return false, errors.New("order cannot be full filled")
		}

		lastPrice, err := this.getLastPrice()
		if err != nil {
			return false, fmt.Errorf("get last price failed: %s", err)
		}

		price := lastPrice
		stopPrice := lastPrice

		var orderSide exchange.OrderSide
		// 每次滑点一些
		if side == exchange.PositionSideLong {
			price = price * (1 + rotateOpenPositionSlippageRate)
			stopPrice = stopPrice * (1 - rotateOpenPositionSlippageRate)
			orderSide = exchange.OrderSideBuy
		} else {
			price = price * (1 - rotateOpenPositionSlippageRate)
			stopPrice = stopPrice * (1 + rotateOpenPositionSlippageRate)
			orderSide = exchange.OrderSideSell
		}

		orderArgs := exchange.CreateOrderArgs{
			InstrumentType:   this.InstrumentType,
			Symbol:           this.Symbol,
			Side:             orderSide,
			Price:            price,
			TriggerPrice:     stopPrice,
			TriggerPriceType: exchange.TriggerPriceTypeLast,
			Qty:              orderQty,
			Type:             this.OrderType(),
		}
		if this.controller.IsExchange(exchange.Binance) {
			// binance 直接用限价单
			orderArgs.TriggerPrice = 0
		}
		if _, err := this.controller.Exchange.CreateOrder(orderArgs); err != nil {
			return false, fmt.Errorf("create order failed: %s", err)
		}

		// 等待 5 秒
		time.Sleep(time.Second * time.Duration(rotateOpenPositionWaitSeconds))

		this.controller.Exchange.CancelAllOrders(this.InstrumentType, this.OrderType(), this.Symbol)
	}

	return true, nil
}

func (this *Turtle) getOpenOrderPriceByStopPrice(side exchange.OrderSide, stopPrice, atr float64) float64 {
	if this.Config.OpenSlippageRate > 0 {
		if side == exchange.OrderSideBuy {
			return stopPrice * (1 + this.Config.OpenSlippageRate)
		} else {
			return stopPrice * (1 - this.Config.OpenSlippageRate)
		}
	}

	if side == exchange.OrderSideBuy {
		return stopPrice + 0.5*atr
	} else {
		return stopPrice - 0.5*atr
	}
}

// 更新订单
func (this *Turtle) updateOpenOrder(order *exchange.Order, side exchange.OrderSide, stopPx float64, atr float64, orderQty float64) (*Order, error) {
	price := this.getOpenOrderPriceByStopPrice(side, stopPx, atr)
	return this.controller.Exchange.UpdateOrder(*order, &exchange.UpdateOrderArgs{
		InstrumentType: this.InstrumentType,
		Price:          price,
		TriggerPrice:   stopPx,
		OrderQty:       orderQty,
		Type:           this.OrderType(),
	})
}

// 平仓是否需要传数量
func (this *Turtle) needCloseOrderQty() bool {
	return this.controller.IsExchange(exchange.OKEx) ||
		this.controller.IsExchange(exchange.CTP) ||
		this.controller.IsExchange(exchange.MetaTrader) ||
		this.controller.IsExchange(exchange.Hyperliquid) ||
		this.controller.IsExchange(exchange.Bybit) ||
		this.controller.IsExchange(exchange.Binance)
}

// 更新止损订单
func (this *Turtle) updateCloseOrder(order *exchange.Order, side exchange.OrderSide, price, qty float64) (*Order, error) {
	args := &exchange.UpdateOrderArgs{
		InstrumentType:   this.InstrumentType,
		Price:            price,
		TriggerPrice:     this.getCloseTriggerPrice(side, price),
		ReduceOnly:       true,
		TriggerPriceType: exchange.TriggerPriceTypeMark,
		Type:             this.OrderType(),
	}
	if this.needCloseOrderQty() {
		// 需要传数量
		args.OrderQty = qty
	}
	return this.controller.Exchange.UpdateOrder(*order, args)
}

// 更新止盈订单
func (this *Turtle) updateProfitOrder(order *exchange.Order, side exchange.OrderSide, triggerPrice, qty float64) (*Order, error) {
	price := this.getTakeProfitPrice(side, triggerPrice)
	args := &exchange.UpdateOrderArgs{
		InstrumentType:   this.InstrumentType,
		Price:            price,
		TriggerPrice:     triggerPrice,
		ReduceOnly:       true,
		TriggerPriceType: exchange.TriggerPriceTypeLast,
		Type:             this.OrderType(),
	}
	if this.needCloseOrderQty() {
		// 需要传数量
		args.OrderQty = qty
	}
	return this.controller.Exchange.UpdateOrder(*order, args)
}

// 根据止盈触发价返回委托价
func (this *Turtle) getTakeProfitPrice(closeSide exchange.OrderSide, stopPrice float64) float64 {
	if closeSide == exchange.OrderSideBuy {
		return stopPrice * (1 + this.Config.TakeProfitSlippageRate)
	} else {
		return stopPrice * (1 - this.Config.TakeProfitSlippageRate)
	}
}

// 根据平仓触发价返回委托价
func (this *Turtle) getCloseOrderPrice(closeSide exchange.OrderSide, stopPrice float64) float64 {
	if closeSide == exchange.OrderSideBuy {
		return stopPrice * (1 + this.Config.CloseSlippageRate)
	} else {
		return stopPrice * (1 - this.Config.CloseSlippageRate)
	}
}

// 根据平仓委托价返回触发价
func (this *Turtle) getCloseTriggerPrice(closeSide exchange.OrderSide, orderPrice float64) float64 {
	if closeSide == exchange.OrderSideBuy {
		return orderPrice / (1 + this.Config.CloseSlippageRate)
	} else {
		return orderPrice / (1 - this.Config.CloseSlippageRate)
	}
}

// 创建止损平仓订单
func (this *Turtle) createCloseOrder(side exchange.OrderSide, price, qty float64) (*Order, error) {
	// 止损时 price 为委托价
	triggerPrice := this.getCloseTriggerPrice(side, price)

	args := exchange.CreateOrderArgs{
		InstrumentType:   this.InstrumentType,
		Symbol:           this.Symbol,
		Side:             side,
		Price:            price,
		TriggerPrice:     triggerPrice,
		TriggerPriceType: exchange.TriggerPriceTypeMark,
		Type:             this.OrderType(),
		ReduceOnly:       true,
	}

	if this.needCloseOrderQty() {
		// 需要传数量
		args.Qty = qty
	}

	return this.controller.Exchange.CreateOrder(args)
}

// 创建止盈订单
func (this *Turtle) createProfitOrder(side exchange.OrderSide, triggerPrice, qty float64) (*Order, error) {
	price := this.getTakeProfitPrice(side, triggerPrice)
	args := exchange.CreateOrderArgs{
		InstrumentType:   this.InstrumentType,
		Symbol:           this.Symbol,
		Side:             side,
		Price:            price,
		TriggerPrice:     triggerPrice,
		TriggerPriceType: exchange.TriggerPriceTypeLast,
		Type:             this.OrderType(),
		ReduceOnly:       true,
	}
	if this.needCloseOrderQty() {
		// 需要传数量
		args.Qty = qty
	}
	return this.controller.Exchange.CreateOrder(args)
}

func (this *Turtle) marginUpdatedCallback(userMargin *exchange.UserMargin, currency string) {
	instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType, this.Symbol)
	if currency != instrument.SettleCurrency {
		return
	}

	if userMargin.MarginBalance != this.Balance.Margin && userMargin.MarginBalance >= userMargin.WalletBalance {
		this.Balance.Margin = userMargin.MarginBalance
		this.Balance.Asset = currency
		if !strings.EqualFold(this.Balance.Asset, "USDT") {
			if ap, err := this.getCurrencyPrice(this.Balance.Asset); err != nil {
				this.Errorf("margin update get asset price error: %s", err)
			} else {
				this.Balance.AssetPrice = ap
			}
		}
		this.controller.storage.Save()
	}

	if userMargin.WalletBalance != 0 && userMargin.WalletBalance != this.Balance.Wallet {
		this.Balance.Wallet = userMargin.WalletBalance
		this.Balance.Asset = currency
		if !strings.EqualFold(this.Balance.Asset, "USDT") {
			if ap, err := this.getCurrencyPrice(this.Balance.Asset); err != nil {
				this.Errorf("margin update get asset price error: %s", err)
			} else {
				this.Balance.AssetPrice = ap
			}
		}
		this.controller.storage.Save()
	}
}

func (this *Turtle) orderUpdatedCallback() {
	this.Infof("order updated callback")
	// 有订单更新触发一次 handlePositionAndOrders

	if this.orderUpdatedTimer != nil {
		this.orderUpdatedTimer.Stop()
	}
	// 推迟 20s 执行，规避订单成交 BM 持仓更新延迟问题
	this.orderUpdatedTimer = time.AfterFunc(20*time.Second, this.handlePositionAndOrders)
}

// 给定最高价、最低价和上个收盘价，返回 tr
func getTrueRange(high float64, low float64, prevClose float64) float64 {
	tr := math.Max(math.Abs(high-low), math.Abs(high-prevClose))
	tr = math.Max(tr, math.Abs(low-prevClose))
	return tr
}

// 根据时间戳返回 K 线的 open close high low 价格
func (this *Turtle) GetOCHLByTimestamp(ts int64) (o, c, h, l float64) {
	for _, kline := range this.klineStatus.klines {
		if kline.Time == ts {
			o = kline.Open
			c = kline.Close
			h = kline.High
			l = kline.Low
			return
		}
	}
	return
}

// 获取并计算最新 K 线数据: DC, ATR 等
func (this *Turtle) HandleLatestKLineData() bool {
	if utils.SliceContains([]string{exchange.MetaTrader, exchange.CTP}, this.controller.Exchange.GetName()) && this.Config.PeriodHour != 24 {
		this.AlertMsgf("%s 仅支持 24 小时K线，请重新设置 .sc PeriodHour=24", this.controller.Exchange.GetName())
		return false
	}

	needKineNum := int(math.Max(float64(this.Open.BreakoutPeriod), float64(this.Config.ATRPeriod)) + 2)
	klines, err := this.controller.Exchange.GetLatestKLinesWithCache(this.InstrumentType, this.Symbol, this.Config.PeriodHour, needKineNum, false)
	klineCount := len(klines)
	if err != nil || klineCount == 0 {
		this.AlertMsgf("获取K线失败，klineCount: (%d)，error: %v", klineCount, err)
		return false
	}

	this.Infof("kline num: %d", klineCount)
	logCount := 0
	for i := klineCount - 1; i >= 0; i-- {
		// 打印 10 条核对数据
		logCount += 1
		if logCount > 10 {
			break
		}
		this.Debugf("kline, time: %s open: %.1f, high: %.1f, low: %.1f, close: %.1f", time.Unix(klines[i].Time, 0).UTC(), klines[i].Open, klines[i].High, klines[i].Low, klines[i].Close)
	}

	i := klineCount - 1
	lastKlineTime := time.Unix(klines[i].Time, 0)
	this.Infof("last kline %s open: %.1f, high: %.1f, low: %.1f, close: %.1f, time: %d", lastKlineTime.UTC(), klines[i].Open, klines[i].High, klines[i].Low, klines[i].Close, klines[i].Time)

	if !utils.SliceContains([]string{exchange.MetaTrader, exchange.CTP}, this.controller.Exchange.GetName()) {
		// 除 CTP 其他 K 线都是 7*24 连续的，检查时间
		if lastKlineTime.Before(time.Now().Add(-time.Hour*time.Duration(this.Config.PeriodHour) - time.Hour)) {
			this.AlertMsgf("K 线数据错误，最后 K 线时间: %s", lastKlineTime)
			return false
		}
	}

	if klineCount < (needKineNum - 1) {
		this.AlertMsgf("K 线数量不足，当前获取数量: %v", klineCount)
		return false
	}

	// K 线过期时间
	this.klineStatus.kLineExpiredAt = klines[i].Time + int64(this.Config.PeriodHour*3600)
	this.klineStatus.updateTime = time.Now()

	// ATR 计算不包含当前 K 线
	atrPeriod := this.Config.ATRPeriod
	atr := 0.0
	sum := 0.0
	rand.Seed(time.Now().Unix())

	for i, kline := range klines[:klineCount-1] {
		// 随机打印数据对比
		// if rand.Intn(100) > 88 {
		// 	this.controller.Infof("last kline %s open: %.1f, high: %.1f, low: %.1f, close: %.1f, tr: %.2f", time.Unix(klines[i].Time, 0).UTC(), klines[i].Open, klines[i].High, klines[i].Low, klines[i].Close, klines[i].Time)
		// }

		tr := getTrueRange(kline.High, kline.Low, kline.Open)
		if i < atrPeriod {
			sum += tr
			if i == this.Config.ATRPeriod-1 {
				atr = sum / float64(this.Config.ATRPeriod)
			}
		} else {
			atr = (atr*(float64(atrPeriod)-1) + tr) / float64(atrPeriod)
		}
	}

	if this.Config.ATRMultiplier > 0 {
		atr = atr * this.Config.ATRMultiplier
	}

	instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType, this.Symbol)
	this.lastATR = exchange.RoundPrice(atr, instrument.TickSize)

	// DC 也不含当前 K 线，如果当前 K 线开仓又止损退出则此跟 K 线不再开仓
	// 分别按 BreakoutPeriod、ExitPeriod 计算突破价和退出价
	lastBreakoutPriceHigh := klines[klineCount-2].High
	lastBreakoutPriceLow := klines[klineCount-2].Low
	lastExitPriceHigh := klines[klineCount-2].High
	lastExitPriceLow := klines[klineCount-2].Low
	breakoutPeriod := this.Open.BreakoutPeriod

	periodNum := int(math.Max(float64(breakoutPeriod), float64(this.Config.ExitPeriod)))
	for i = 0; i < periodNum; i++ {
		if i < breakoutPeriod {
			lastBreakoutPriceHigh = math.Max(lastBreakoutPriceHigh, klines[klineCount-2-i].High)
			lastBreakoutPriceLow = math.Min(lastBreakoutPriceLow, klines[klineCount-2-i].Low)
		}
		if i < this.Config.ExitPeriod {

			// 如果当前 K 线被设置为退出时忽略，则跳过
			ignored := false
			for _, ts := range this.Fix.IgnoredExitKLines {
				if ts == klines[klineCount-2-i].Time {
					ignored = true
					break
				}
			}
			if ignored {
				continue
			}

			lastExitPriceHigh = math.Max(lastExitPriceHigh, klines[klineCount-2-i].High)
			lastExitPriceLow = math.Min(lastExitPriceLow, klines[klineCount-2-i].Low)
		}
	}

	this.klineStatus.klines = klines
	this.lastBreakoutPriceHigh = lastBreakoutPriceHigh
	this.lastBreakoutPriceLow = lastBreakoutPriceLow
	this.lastExitPriceHigh = lastExitPriceHigh
	this.lastExitPriceLow = lastExitPriceLow

	this.Infof("last kline calculated data: kLineExpiredAt: %v, ATR: %v, DC(%d): [%v, %v], DC(%d): [%v, %v]", this.klineStatus.expireTime(), this.lastATR, breakoutPeriod, this.lastBreakoutPriceHigh, this.lastBreakoutPriceLow, this.Config.ExitPeriod, this.lastExitPriceHigh, this.lastExitPriceLow)

	return true
}

// 判断两个价格是否相同，允许一定误差
func (this *Turtle) isTheSamePrice(p1, p2 float64) bool {
	// 相差不应超过 max(TICK_SIZE * 2, p2 * 0.0002)
	instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType, this.Symbol)
	delta := math.Max(instrument.TickSize*2, p2*0.0002)
	return math.Abs(p1-p2) < delta
}

func (this *Turtle) isOrderNeedUpdate(order *Order, qty float64, stopPrice float64, position *Position) bool {
	var price float64
	if order.IsCloseOrder() {
		if order.IsTakeProfitOrder(position) { // 止盈单触发价应该和退出价相同
			price = this.getTakeProfitPrice(order.Side, stopPrice)
		} else {
			price = this.getCloseOrderPrice(order.Side, stopPrice)
		}

		if this.needCloseOrderQty() {
			qty = math.Abs(position.Qty)
		}
	} else {
		price = this.getOpenOrderPriceByStopPrice(order.Side, stopPrice, this.Open.ATR.Value)
	}

	if order.Type == exchange.StopMarket {
		price = 0
	}

	if qty == order.Qty && this.isTheSamePrice(price, order.Price) && this.isTheSamePrice(stopPrice, order.TriggerPrice) {
		return false
	} else {
		return true
	}
}

// 定时处理更新 K 线数据
func (this *Turtle) updateKlineHandler() {
	if !this.isTrading() {
		this.Infof("not trading, no need to update kline")
		return
	}

	if time.Since(this.klineStatus.expireTime()) > 0 {
		// K 线已过期，获取新的 K 线
		if success := this.HandleLatestKLineData(); success {
			// 获取到最新 K 线后，更新价格到本地缓存，并立即处理订单
			if this.Position.Qty == 0 || utils.IsTimeExpired(this.Long.ExitPrice.ExpiredAt) {
				// 无持仓时,直接更新价格
				// 有持仓时,因为人工可以设置退出价格,所以仅在价格已过期时才更新
				this.updatePriceStorage(false, false)
			}
			this.handlePositionAndOrders()
		}
	} else {
		this.Infof("waiting new kline")
	}

	// 要稍微等会才能拿到最新费率
	time.Sleep(time.Second * 3)

	this.checkIfIgnoredExitKLinesExpire()
}

// 将最新 K 线的突破价、退出价更新到本地存储
func (this *Turtle) updatePriceStorage(forceUpdate bool, manual bool) {
	if forceUpdate || utils.IsTimeExpired(this.Long.BreakoutPrice.ExpiredAt) {
		breakoutPriceLong, breakoutPriceShort := this.GetBreakoutPrice()
		this.Long.BreakoutPrice.set(this, breakoutPriceLong, this.klineStatus.expireTime(), manual, "Long.BreakoutPrice", "")
		this.Short.BreakoutPrice.set(this, breakoutPriceShort, this.klineStatus.expireTime(), manual, "Short.BreakoutPrice", "")
		this.updateOpenSize()

	}
	if forceUpdate || utils.IsTimeExpired(this.Long.ExitPrice.ExpiredAt) {
		// ExitPriceHigh = 看空退出价, ExitPriceLow => 看多退出价
		exitPriceShort, exitPriceLong := this.GetExitPrice()
		this.Long.ExitPrice.set(this, exitPriceLong, this.klineStatus.expireTime(), manual, "Long.ExitPrice", "")
		this.Short.ExitPrice.set(this, exitPriceShort, this.klineStatus.expireTime(), manual, "Short.ExitPrice", "")
	}

	this.controller.storage.Save()
}

// 获取 DC 突破价，根据配置滑点, 让触发价稍微更不敏感一点
func (this *Turtle) GetBreakoutPrice() (high, low float64) {
	instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType, this.Symbol)
	high = exchange.RoundPrice(this.lastBreakoutPriceHigh*(1+this.Config.BreakoutSlippageRate), instrument.TickSize)
	low = exchange.RoundPrice(this.lastBreakoutPriceLow*(1-this.Config.BreakoutSlippageRate), instrument.TickSize)
	return
}

// 获取 DC 退出价，根据配置滑点, 让触发价稍微更不敏感一点
func (this *Turtle) GetExitPrice() (high, low float64) {
	instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType, this.Symbol)
	high = exchange.RoundPrice(this.lastExitPriceHigh*(1+this.Config.ExitSlippageRate), instrument.TickSize)
	low = exchange.RoundPrice(this.lastExitPriceLow*(1-this.Config.ExitSlippageRate), instrument.TickSize)
	return
}

func (this *Turtle) setStatus(status string) {
	this.Status = status

	if this.Status != status {
		this.Status = status
		this.addSetHistory("Status", status, "")
		this.controller.storage.Save()
	}
}

// Slack 命令处理
func (this *Turtle) CommandHandler(command string, args ...string) {
	this.commandProcessor.Process(command, args)
}

// 一些共用函数

func (this *Turtle) getExpireTimeFromDuration(duration string) string {
	duration = strings.ToUpper(duration)
	if duration == "NONE" {
		return "2999-01-01T00:00:00+08:00"
	}

	if strings.HasSuffix(duration, "M") {
		mStr := duration[:len(duration)-1]
		m, _ := strconv.ParseInt(mStr, 10, 32)
		if m > 0 {
			expireTime := time.Now().Add(time.Minute * time.Duration(int(m)))
			return utils.FormatToBeijingTimeStr(expireTime)
		}
	}

	if strings.HasSuffix(duration, "H") {
		hourStr := duration[:len(duration)-1]
		hour, _ := strconv.ParseInt(hourStr, 10, 32)
		if hour > 0 {
			expireTime := time.Now().Add(time.Hour * time.Duration(int(hour)))
			return utils.FormatToBeijingTimeStr(expireTime)
		}
	}

	if strings.HasSuffix(duration, "D") {
		dayStr := duration[:len(duration)-1]
		day, _ := strconv.ParseInt(dayStr, 10, 32)
		if day > 0 {
			expireTime := time.Now().Add(time.Hour * 24 * time.Duration(int(day)))
			return utils.FormatToBeijingTimeStr(expireTime)
		}
	}

	if strings.HasSuffix(duration, "K") {
		kStr := duration[:len(duration)-1]
		kNum, _ := strconv.ParseInt(kStr, 10, 32)
		if kNum > 0 {
			periodSeconds := int64(this.Config.PeriodHour * 3600)
			expireTime := time.Unix(this.klineStatus.kLineExpiredAt+(kNum-1)*periodSeconds, 0)
			return utils.FormatToBeijingTimeStr(expireTime)
		}
	}

	return ""
}

// 返回价格最小变动的小数位数
func (this *Turtle) getTickSizePlaces() int {
	instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType, this.Symbol)
	s := strconv.FormatFloat(instrument.TickSize, 'f', -1, 64)
	i := strings.IndexByte(s, '.')
	if i > -1 {
		return len(s) - i - 1
	}
	return 0
}

// 格式化价格，用于输出打印
func (this *Turtle) formatPrice(price float64) string {
	return this.controller.Exchange.FormatPrice(this.InstrumentType, this.Symbol, price)
}

// 根据交易所格式化余额
func (this *Turtle) formatAmount(amount float64) string {
	return this.controller.formatAmount(this.Balance.Asset, amount)
}

// 如果退出忽略 K 线设置的时间已不在退出 DC 时间段，则删除
func (this *Turtle) checkIfIgnoredExitKLinesExpire() {
	nowTs := time.Now().Unix()
	newIgnoredExitKLines := []int64{}
	for _, timestamp := range this.Fix.IgnoredExitKLines {
		kLineTime := time.Unix(timestamp, 0)

		if (kLineTime.Unix() + int64(this.Config.PeriodHour*3600*this.Config.ExitPeriod)) < nowTs {
			continue
		}

		newIgnoredExitKLines = append(newIgnoredExitKLines, timestamp)
	}

	if len(newIgnoredExitKLines) != len(this.Fix.IgnoredExitKLines) {
		this.Fix.IgnoredExitKLines = newIgnoredExitKLines
		this.controller.storage.Save()
	}
}

// 重新计算开仓大小
func (this *Turtle) updateOpenSize() {
	if this.Position.Qty != 0 && !(this.Long.OpenSize == 0 && this.Short.OpenSize == 0) {
		this.Warnf("have position, should not update open size")
		return
	}

	atr := this.Open.ATR.Value
	if atr == 0 {
		this.Errorf("atr is 0, should not update open size")
		return
	}
	breakoutPriceLong := this.Long.BreakoutPrice.Value
	breakoutPriceShort := this.Short.BreakoutPrice.Value
	openBalance := this.Open.Balance.Total
	this.Long.OpenSize = math.Round(breakoutPriceLong*openBalance*this.Config.OpenSizePercent/atr*1e8) / 1e8
	this.Short.OpenSize = math.Round(breakoutPriceShort*openBalance*this.Config.OpenSizePercent/atr*1e8) / 1e8
}

func (this *Turtle) SetDebug(debug bool) {
	// do nothing
}

func (this *Turtle) FinishWithReason(reason FinishReason) {
	nowTime := time.Now()
	if this.Finish.Reason == "" {
		// 可能提前设置了结束原因
		this.Finish.Reason = reason
	} else {
		reason = this.Finish.Reason
	}
	this.Finish.Time = &nowTime
	this.setStatus(PAUSED)
	copier.Copy(this.Finish.Balance, this.Balance)
	if reason == FinishReasonClose || reason == FinishReasonTakeProfit || reason == FinishReasonStopLoss || reason == FinishReasonStopOnExit {
		tt := this.controller.removeAndStartNewTurtle(this.SymbolCode)

		if !utils.IsTimeExpired(this.Open.ATR.ExpiredAt) && reason == FinishReasonStopLoss {
			// 当前 K 线开仓即止损的情况，此跟 K 线不再开仓
			tt.SetPauseTo(this.Open.ATR.ExpiredAt, PauseReasonStopLoss)
			tt.WarnMsgf("当前 K线 开仓即止损，到 %s 不再开仓。", utils.FormatShortTimeStr(this.Open.ATR.ExpiredAt, false))
		}

		if this.Position.GetFloat64(ExtKeyLockedPNL) > 0 && tt.Config.SecondaryBreakoutPeriod != 0 {
			// 如果上次是止盈，下次突破使用 SecondaryBreakoutPeriod
			tt.Open.BreakoutPeriod = tt.Config.SecondaryBreakoutPeriod
		}
		if reason == FinishReasonTakeProfit || reason == FinishReasonStopLoss {
			tt.setStatus(RUNNING)
			go tt.handlePositionAndOrders()
		}
	}
}

func (this *Turtle) GetPNL(position *Position) (realisedPNL, unrealisedPNL, pnl float64, asset string, assetPrice float64, er error) {
	realisedPNL, unrealisedPNL, pnl, assetPrice = 0.0, 0.0, 0.0, 0.0
	instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType, this.Symbol)
	asset = instrument.SettleCurrency
	// 如果传入 position 可用，实时获取最新的仓位
	if position == nil {
		// 只有 Binance 的交易所获取多空两个方向的仓位，BitMEX 用一个方向获取即可
		if pos, err := this.getCurrentPosition(true); err == nil {
			position = pos
		} else {
			this.Infof("get position error: %s", err)
			er = err
			return
		}
	}

	if ap, err := this.getCurrencyPrice(asset); err != nil {
		er = err
	} else {
		assetPrice = ap
	}

	realisedPNL, unrealisedPNL, pnl = position.GetPNL(asset)
	return
}

func (this *Turtle) getPositionPriceInfo() *PositionPriceInfo {
	result := &PositionPriceInfo{
		PositionQty:     this.Position.Qty,
		PositionPrice:   this.Position.EntryPrice,
		TakeProfitPrice: this.Position.GetFloat64(ExtKeyTakeProfitPrice),
		StopLossPrice:   this.Position.GetFloat64(ExtKeyStopLossPrice),
		LongPrices:      this.Long.Prices,
		ShortPrices:     this.Short.Prices,
		LongQty:         this.Long.Qtys,
		ShortQty:        this.Short.Qtys,
		Units:           this.Position.GetInt(ExtKeyUnits),
		LastPrice:       this.Position.LastPrice,
	}
	return result
}

func (this *Turtle) addSetHistory(field string, value string, comment string) {
	nowTime := time.Now()
	this.SetHistory = append(this.SetHistory, &HistoryItem{CreateTime: &nowTime, Field: field, Value: value, Comment: comment})
}

func (this *Turtle) getFirstAndLastPosition() (firstPosition *Position, lastPosition *Position, closingPosition *Position) {
	changesCount := len(this.PositionChanges)
	if changesCount > 0 {
		firstPosition = this.PositionChanges[0]
		lastPosition = this.PositionChanges[changesCount-1]
		if lastPosition.Qty == 0 && changesCount > 1 {
			// closingPosition 是平仓前的仓位信息，如果没有平仓，closingPosition 为 nil
			closingPosition = lastPosition
			lastPosition = this.PositionChanges[changesCount-2]
		}
	}
	return
}

func (this *Turtle) queryExecOrders() {
	newExecOrders := OrderList{}
	posQty := this.Position.Qty

	localOrders := OrderList{}
	localOrders = append(localOrders, this.CloseOrders...)
	if posQty > 0 {
		localOrders = append(localOrders, this.Long.OpenOrders...)
		for _, order := range this.Short.OpenOrders {
			order.Status = exchange.OrderStatusCancelled
		}
	} else if posQty < 0 {
		localOrders = append(localOrders, this.Short.OpenOrders...)
		for _, order := range this.Long.OpenOrders {
			order.Status = exchange.OrderStatusCancelled
		}
	} else if posQty == 0 {
		localOrders = append(localOrders, this.Long.OpenOrders...)
		localOrders = append(localOrders, this.Short.OpenOrders...)
	}

	for _, localOrder := range localOrders {
		if !localOrder.IsOpen() {
			continue
		}
		if this.controller.IsExchange(exchange.OKEx) {
			// OKEX 查询订单频率限制严格，这里延迟更新订单数据不影响策略运行
			time.Sleep(500 * time.Millisecond)
		}
		if order, err := this.controller.Exchange.GetOrderByOrig(*localOrder); err == nil {
			localOrder.Status = order.Status
			localOrder.ExecPrice = order.ExecPrice
			localOrder.ExecQty = order.ExecQty
			localOrder.UpdateTime = order.UpdateTime
			if order.ExecQty > 0 {
				newExecOrders = append(newExecOrders, localOrder)
			}
		}
	}

	for _, order := range this.ExecOrders {
		if utils.IndexOf(order.OrderID, newExecOrders.IDs()) == -1 {
			newExecOrders = append(newExecOrders, order)
		}
	}

	this.ExecOrders = newExecOrders
	this.controller.storage.Save()
}

func (this *Turtle) calculateExecOrdersPNL() (float64, error) {
	var openQty float64
	var openValue, closeValue float64
	positionSide := exchange.PositionSideLong
	for _, order := range this.ExecOrders {
		orderValue := this.Qty2Size(this.Symbol, order.ExecPrice, order.ExecQty)
		category := order.GetString(ExtKeyCategory)
		if category == OrderCategoryLong || category == OrderCategoryShort {
			openValue += orderValue
			openQty += order.ExecQty
		} else {
			closeValue += orderValue
			openQty -= order.ExecQty
		}

		if category == OrderCategoryShort {
			positionSide = exchange.PositionSideShort
		}
	}

	if !exchange.AlmostEqual(openQty, 0) {
		return 0, fmt.Errorf("sum of order filled qty should be zero")
	}

	pnl := closeValue - openValue
	if positionSide == exchange.PositionSideShort {
		pnl *= -1
	}
	if this.controller.Exchange.IsReverseSymbol(this.InstrumentType, this.Symbol) {
		pnl *= -1
	}
	return pnl, nil
}

func (this *Turtle) calculatePositionPNL(position *Position) float64 {
	if position == nil {
		position = this.Position
	}
	if position.Qty == 0 {
		if position.EntryPrice != 0 {
			if p, err := this.calculateExecOrdersPNL(); err != nil {
				this.Infof("calculate exec orders for PNL error: %s", err)
			} else {
				return p
			}
		} else {
			return 0
		}
	}

	openValue := this.Qty2Size(this.Symbol, position.EntryPrice, math.Abs(position.Qty))
	currentValue := this.Qty2Size(this.Symbol, position.MarkPrice, math.Abs(position.Qty))
	pnl := currentValue - openValue
	if position.Qty < 0 {
		pnl *= -1
	}
	if this.controller.Exchange.IsReverseSymbol(this.InstrumentType, this.Symbol) {
		pnl *= -1
	}
	return pnl
}

func (this *Turtle) Qty2Size(symbol string, price float64, qty float64) (size float64) {
	if qty == 0 {
		return 0
	}
	if size, err := this.controller.Exchange.Qty2Size(this.InstrumentType, symbol, price, qty); err != nil {
		this.ErrorMsgf("convert qty 2 size error: %s", err)
		return 0
	} else {
		return size
	}
}

func (this *Turtle) Size2Qty(symbol string, price float64, size float64) (qty float64) {
	if qty, err := this.controller.Exchange.Size2Qty(this.InstrumentType, symbol, price, size); err != nil {
		this.ErrorMsgf("convert size 2 qty error: %s", err)
		return 0
	} else {
		return qty
	}
}

func (this *Turtle) CalcPrice(symbol string, qty float64, size float64) (price float64) {
	if price, err := this.controller.Exchange.CalcPrice(this.InstrumentType, symbol, qty, size); err != nil {
		this.ErrorMsgf("convert qty and size 2 price error: %s", err)
		return 0
	} else {
		return price
	}
}

func (this *Turtle) calculateEstimatedLoss() {
	if ok, msg := this.checkOpenPositionData(); !ok {
		this.Errorf("calculate estimated loss err: %s", msg)
		return
	}

	if this.Position.Qty != 0 {
		openValue := this.Qty2Size(this.Symbol, this.Position.EntryPrice, math.Abs(this.Position.Qty))
		stopLossValue := this.Qty2Size(this.Symbol, this.Position.GetFloat64(ExtKeyStopLossPrice), math.Abs(this.Position.Qty))
		loss := stopLossValue - openValue
		if this.Position.Qty < 0 {
			loss *= -1
		}
		if this.controller.Exchange.IsReverseSymbol(this.InstrumentType, this.Symbol) {
			loss *= -1
		}
		this.EstimatedLoss.Position = loss

		// 已成交订单预估损失设为 0，估算未成交订单
		positionUnits := this.Position.GetInt(ExtKeyUnits)
		totalQty := math.Abs(this.Position.Qty)
		totalValue := openValue
		for i := 1; i <= this.Config.MaxOpenUnits; i++ {
			if this.Position.Qty < 0 {
				this.EstimatedLoss.Longs[i-1] = 0
				if positionUnits >= i {
					this.EstimatedLoss.Shorts[i-1] = 0
				} else {
					price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideSell, i-1)
					totalValue += this.Qty2Size(this.Symbol, price, math.Abs(qty))
					totalQty += qty
					avgPrice := this.CalcPrice(this.Symbol, totalQty, totalValue)
					stopLossPrice := avgPrice + (2.25-float64(i)*0.25)*this.Open.ATR.Value
					stopLossValue := this.Qty2Size(this.Symbol, stopLossPrice, math.Abs(totalQty))
					loss := totalValue - stopLossValue
					if this.controller.Exchange.IsReverseSymbol(this.InstrumentType, this.Symbol) {
						loss *= -1
					}
					this.EstimatedLoss.Shorts[i-1] = loss
				}
			} else {
				this.EstimatedLoss.Shorts[i-1] = 0
				if positionUnits >= i {
					this.EstimatedLoss.Longs[i-1] = 0
				} else {
					price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideBuy, i-1)
					totalValue += this.Qty2Size(this.Symbol, price, math.Abs(qty))
					totalQty += qty
					avgPrice := this.CalcPrice(this.Symbol, totalQty, totalValue)
					// 一个头寸时止损价=持仓价-2ATR，2个时=持仓价-1.75ATR，3个时=持仓价-1.5ATR，4个时=持仓价-1.25ATR
					stopLossPrice := avgPrice - (2.25-float64(i)*0.25)*this.Open.ATR.Value
					stopLossValue := this.Qty2Size(this.Symbol, stopLossPrice, math.Abs(totalQty))
					loss := stopLossValue - totalValue
					if this.controller.Exchange.IsReverseSymbol(this.InstrumentType, this.Symbol) {
						loss *= -1
					}
					this.EstimatedLoss.Longs[i-1] = loss
				}
			}
		}

	} else {
		// 依次估算各单位头寸累计损失
		var totalQty float64
		var totalValue float64
		for i := 1; i <= this.Config.MaxOpenUnits; i++ {
			price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideBuy, i-1)
			totalValue += this.Qty2Size(this.Symbol, price, math.Abs(qty))
			totalQty += qty
			avgPrice := this.CalcPrice(this.Symbol, totalQty, totalValue)
			// 一个头寸时止损价=持仓价-2ATR，2个时=持仓价-1.75ATR，3个时=持仓价-1.5ATR，4个时=持仓价-1.25ATR
			stopLossPrice := avgPrice - (2.25-float64(i)*0.25)*this.Open.ATR.Value
			stopLossValue := this.Qty2Size(this.Symbol, stopLossPrice, math.Abs(totalQty))
			loss := stopLossValue - totalValue
			if this.controller.Exchange.IsReverseSymbol(this.InstrumentType, this.Symbol) {
				loss *= -1
			}
			this.EstimatedLoss.Longs[i-1] = loss
		}

		totalQty = 0
		totalValue = 0
		for i := 1; i <= this.Config.MaxOpenUnits; i++ {
			price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideSell, i-1)
			totalValue += this.Qty2Size(this.Symbol, price, math.Abs(qty))
			totalQty += qty
			avgPrice := this.CalcPrice(this.Symbol, totalQty, totalValue)
			stopLossPrice := avgPrice + (2.25-float64(i)*0.25)*this.Open.ATR.Value
			stopLossValue := this.Qty2Size(this.Symbol, stopLossPrice, math.Abs(totalQty))
			loss := totalValue - stopLossValue
			if this.controller.Exchange.IsReverseSymbol(this.InstrumentType, this.Symbol) {
				loss *= -1
			}
			this.EstimatedLoss.Shorts[i-1] = loss
		}
	}
}

func (this *Turtle) calculateEstimatedMargin() (margin float64) {
	if !this.controller.IsExchange(exchange.CTP) {
		return
	}
	// 保证金 = (LongMarginRatioByVolume + LongMarginRatioByMoney * Price * VolumeMultiple) * Volume
	instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType, this.Symbol)
	var longMargin, shortMargin float64
	for _, order := range this.Long.OpenOrders {
		if !order.IsOpen() {
			continue
		}
		longMargin += (instrument.LongMarginRatioByVolume + instrument.LongMarginRatioByMoney*order.Price*instrument.ContractSize) * (order.Qty - order.ExecQty)
	}
	for _, order := range this.Short.OpenOrders {
		if !order.IsOpen() {
			continue
		}
		shortMargin += (instrument.ShortMarginRatioByVolume + instrument.ShortMarginRatioByMoney*order.Price*instrument.ContractSize) * (order.Qty - order.ExecQty)
	}

	if this.Position.Qty == 0 {
		// 无持仓，所需最大保证金 = max(多单保证金，空单保证金)
		margin = math.Max(longMargin, shortMargin)
	} else {
		// 有持仓，所需最大保证金 = 持仓保证金 + 未成交单位所需保证金
		margin += this.Position.Margin
		if this.Position.Qty > 0 {
			margin += longMargin
		} else {
			margin += shortMargin
		}
	}
	return
}

func (this *Turtle) GetMaxEstimatedLoss() float64 {
	return math.Min(this.EstimatedLoss.Longs[this.Config.MaxOpenUnits-1], this.EstimatedLoss.Shorts[this.Config.MaxOpenUnits-1])
}

func (this *Turtle) SetPauseTo(pauseTo *time.Time, reason PauseReason) {
	oldTime := fmt.Sprintf("%s", this.PauseTo)
	this.PauseTo = pauseTo
	this.PauseReason = reason
	this.addSetHistory("PauseTo", fmt.Sprintf("%s", pauseTo), fmt.Sprintf("reason: (%s), old value: (%s)", reason, oldTime))
}

func (this *Turtle) getCurrencyPrice(currency string) (float64, error) {
	return this.controller.Exchange.GetCurrencyPrice(currency, CurrencyPriceCacheTime)
}

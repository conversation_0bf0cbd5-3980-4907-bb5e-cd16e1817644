package turtle

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"

	"github.com/stevedomin/termtable"
)

type ExpFloat64 struct {
	Value         float64
	ExpiredAt     *time.Time
	ManualUpdated bool
}

func NewExpFloat64(value float64) *ExpFloat64 {
	expiredAt := time.Unix(0, 0)
	return &ExpFloat64{Value: value, ExpiredAt: &expiredAt}
}

func (f *ExpFloat64) String() string {
	return fmt.Sprintf("%.f", f.Value)
}

func (f *ExpFloat64) Sprintf(format string) string {
	return fmt.Sprintf(format, f.Value)
}

func (f *ExpFloat64) set(turtle *Turtle, value float64, expiredAt time.Time, manual bool, historyKey, comment string) {
	oldValue := f.Value
	oldExpiredAt := f.ExpiredAt
	f.Value = value
	f.ExpiredAt = &expiredAt
	f.ManualUpdated = manual
	// 如果是手工设置，记录设置历史；如果是自动设置，不记录历史
	if turtle != nil && manual {
		if comment == "" {
			comment = "manual"
		}
		if oldValue != value {
			if oldExpiredAt.Equal(expiredAt) {
				turtle.addSetHistory(historyKey, fmt.Sprintf("%.f %s", value, expiredAt), comment)
			} else {
				turtle.addSetHistory(historyKey, fmt.Sprintf("%.f", value), comment)
			}
		}
	}
}

type Side string

const SideUnknown Side = "-"
const SideLong Side = "Long"
const SideShort Side = "Short"

type OrderCategory = string

const (
	OrderCategoryLong       OrderCategory = "Long"
	OrderCategoryShort      OrderCategory = "Short"
	OrderCategoryTakeProfit OrderCategory = "TakeProfit"
	OrderCategoryStopLoss   OrderCategory = "StopLoss"
)

/*
	Order 是 exchange.Order 的别名，为了在应用中使用 exchange.Order 过程中少敲一些代码，并且方便查看定义

使用以下扩展字段：

	ExtKeyUnit int 	// 该订单是海龟的第几个头寸
	ExtKeyIsOpen bool // 是否未成交
	ExtKeyCategory string // 订单类别
*/
type Order = exchange.Order

// 清理 Order 中的扩展字段，仅保留需要的 keys，在持久化的清理函数中调用
// 如果新加了字段必须放到以下的白名单中，否则持久化中就会看不到
func CleanOrderExtStruct(order *Order) {
	order.CleanInt([]string{ExtKeyUnit}, true)
	order.CleanString([]string{ExtKeyCategory}, true)
}

type OrderList []*Order

func SetOrderExt(order *Order, orderCategory OrderCategory, unit int) *Order {
	if order == nil {
		zlog.Errorf("set order ext, order type: %s, unit: %d, error: nil order object", orderCategory, unit)
		return nil
	}
	order.SetString(ExtKeyCategory, orderCategory)
	order.SetInt(ExtKeyUnit, unit)
	zlog.Debugf("set order[%s] ext, order type: %s, unit: %d", order.OrderID, orderCategory, unit)
	return order
}

type SideParam struct {
	Side          Side
	BreakoutPrice *ExpFloat64 // K线 DC 算出来的突破价，不管是否有仓位，都会更新
	ExitPrice     *ExpFloat64 // K线 DC 算出来的突破价，不管是否有仓位，都会更新
	OpenSize      float64
	OpenPrice     float64
	OpenOrders    OrderList
	Prices        []float64
	Qtys          []float64
}

type OpenParam struct {
	ATR            *ExpFloat64 // 原 OpenATR
	BreakoutPeriod int         // 原 CurrentBreakoutPeriod
	Balance        *Balance    // 原 OpenBalance
}

type FinishParam struct {
	Time    *time.Time
	Reason  FinishReason
	Balance *Balance
}

func (sp *SideParam) UpdateOrderFromExchangeOrder(i int, order *Order) error {
	if len(sp.OpenOrders) < i+1 {
		return fmt.Errorf("update open order error: index [%d] out of range [%d]", i, len(sp.OpenOrders))
	}
	nowTime := time.Now()
	oldOrderID := sp.OpenOrders[i].OrderID
	oldOrderCreateTime := sp.OpenOrders[i].CreateTime
	sp.OpenOrders[i] = SetOrderExt(order, sp.OpenOrders[i].GetString(ExtKeyCategory), i+1)
	sp.OpenOrders[i].UpdateTime = &nowTime
	if oldOrderID == order.OrderID { // ID 不变，保持创建时间不变
		sp.OpenOrders[i].CreateTime = oldOrderCreateTime
	}
	zlog.Debugf("update order %s from exchange order: %s, index: %d", oldOrderID, order.OrderID, i)
	return nil
}

func (s OrderList) IDs() []string {
	result := []string{}
	for _, order := range s {
		result = append(result, order.OrderID)
	}
	return result
}

type Fixation struct {
	StopPrice         float64
	PositionQty       float64
	PositionComment   string
	FixPositionTime   *time.Time
	IgnoredExitKLines []int64
	StopLossAdjust    float64 // TODO: 新功能
	TakeProfitAdjust  float64 // TODO: 新功能
}

type HistoryItem struct {
	CreateTime *time.Time
	Field      string
	Value      string
	Comment    string
}

type Balance struct {
	Margin         float64
	Wallet         float64
	External       float64
	OtherPNL       float64
	OpenSizeMargin float64 // 按 OpenSizePercent 开仓所需的保证金
	Total          float64 // 可能是 Margin + External 或者 Wallet + External
	Asset          string  // 币种，BTC
	AssetPrice     float64 // 以 USDT 计价
	UpdateTime     *time.Time
}

func (b *Balance) getMarginNotional() float64 {
	return b.Margin * b.AssetPrice
}

/*
	Position 是 exchange.Position 的别名，为了在应用中使用 exchange.Position 过程中少敲一些代码，并且方便查看定义

使用以下扩展字段：

	ExtKeyUnits int // 累计头寸数量
	ExtKeyTakeProfitPrice float64 // 头寸对应的止盈价格，或头寸快照当时的止盈价格
	ExtKeyStopLossPrice float64 // 头寸对应的止损价格，或头寸快照当时的止损价格
	ExtKeyLockedPNL float64 // 头寸对应的锁定利润（由止盈价对应的利润）
	ExtKeyPNL float64 // 头寸的总盈亏
	ExtKeyCurrencyPrice float64 // 头寸盈亏的计价币种的当时价格
	ExtKeyCurrency string // 盈亏计价币种
*/
type Position = exchange.Position

// 清理 Position 中的扩展字段，仅保留需要的 keys，在持久化的清理函数中调用
// 如果新加了字段必须放到以下的白名单中，否则持久化中就会看不到
func CleanPositionExtStruct(position *Position) {
	position.CleanInt([]string{ExtKeyUnits}, true)
	position.CleanFloat64([]string{ExtKeyTakeProfitPrice, ExtKeyStopLossPrice, ExtKeyLockedPNL, ExtKeyPNL, ExtKeyAssetPrice}, true)
	position.CleanString([]string{ExtKeyAsset}, true)
}

type Storage struct {
	base.BaseStorage
	turtleController *TurtleController
	ExchangeName     string
	Turtles          []*Turtle
	mutex            sync.Mutex
	fileMutex        sync.Mutex
}

func SetupStorage(controller *TurtleController) *Storage {
	if controller.Config == nil {
		zlog.Panicf("config is nil, please init config before storage for turtle")
		return nil
	}
	storage := &Storage{
		BaseStorage: base.BaseStorage{
			Controller: controller,
		},
		turtleController: controller,
	}
	// 紧接着赋值给 Storager，防止没有赋值前调用 Storager 相关的方法导致崩溃
	storage.Storager = storage

	if ok := storage.ReadFrom(controller.ConfigPath, controller.GetID()); !ok {
		storage.Turtles = []*Turtle{}
		storage.Assets = map[int64]float64{}
		storage.ExchangeName = controller.Config.ExchangeName
	} else {
		storage.migrateForExchangeRefactor() // 尝试迁移旧 storage
		storage.migrateForOKAlgoOrderID()
		storage.FixOrderInstrumentType()
		symbolCodeMigrated := storage.migrateSymbolCode()
		exchangeNameMigrated := storage.migrateExchangeName()

		for _, tt := range storage.Turtles {
			tt.controller = controller
			tt.addSetHistory("this", fmt.Sprintf("Build: %s", controller.BuildInfo()), "init from storage") // 记录重启载入趋势机的事件

			if tt.SymbolCode.USDXSymbol == "" {
				tt.SymbolCode.USDXSymbol = "USDT"
			}
		}
		if exchangeNameMigrated || symbolCodeMigrated {
			storage.Save()
		}
	}
	controller.storage = storage
	return storage
}

func (s *Storage) ReadFrom(configPath, id string) bool {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".turtle_storage")
	f, _ := os.OpenFile(path, os.O_RDWR|os.O_CREATE, 0755)
	defer f.Close()
	file, err := io.ReadAll(f)
	if err != nil {
		zlog.Errorf("[%s] read storage file error: %s", s.Controller.GetID(), err.Error())
		return false
	}
	zlog.Infof("[%s] local storage file (%s) loaded", s.Controller.GetID(), path)
	err = json.Unmarshal(file, s)
	if err != nil {
		zlog.Debugf("[%s] json.Unmarshal err: %#v", s.Controller.GetID(), err)
		zlog.Errorf("[%s] read json file error", s.Controller.GetID())
		return false
	}
	return true
}

// 写入本地存储到文件
func (s *Storage) SaveTo(configPath string, id string, overwrite bool) error {
	s.fileMutex.Lock()
	startTime := time.Now()
	defer func() {
		zlog.Infof("[%s] save to storage took %s", s.Controller.GetID(), time.Since(startTime))
		s.fileMutex.Unlock()
	}()

	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".turtle_storage")
	if _, err := os.Stat(path); !os.IsNotExist(err) && !overwrite {
		return fmt.Errorf("[%s] %s already exist, can not overwrite", s.Controller.GetID(), path)
	}

	// backup old storage file, if the old file is valid json
	oldData, err := os.ReadFile(path)
	if err != nil {
		zlog.Errorf("[%s] read old storage file error: %s", s.Controller.GetID(), err)
		return err
	}
	if gjson.Valid(string(oldData)) {
		backupPath := filepath.Join(configPath, id+".turtle_storage.bak")
		if err := utils.CopyFile(path, backupPath); err != nil {
			zlog.Errorf("[%s] backup old storage file error: %s", s.Controller.GetID(), err)
			return err
		}
	}

	s.cleanupExtStruct()
	// Wrap marshal in a single recover
	// marshal 的过程中，如果其中的子结构中的 slices 可能发生变化，导致 crash
	// 因此，需要手工 recover 忽略错误
	data, err := func() (data []byte, err error) {
		defer func() {
			if r := recover(); r != nil {
				err = fmt.Errorf("[%s] panic during storage marshal: %v", s.Controller.GetID(), r)
			}
		}()

		data, err = json.MarshalIndent(s, "", "    ")
		if err != nil {
			return nil, fmt.Errorf("marshal error: %s", err)
		}
		return data, nil
	}()

	if err != nil {
		zlog.Errorf("[%s] marshal storage error: %s", s.Controller.GetID(), err)
		return err
	}

	if err := os.WriteFile(path, data, 0755); err != nil {
		zlog.Errorf("[%s] save storage to file, error: %s", s.Controller.GetID(), err)
		return err
	}
	return nil
}

func (s *Storage) FinishDuplicateTurtles(symbolCode *exchange.SymbolCode) (validTurtle *Turtle) {
	foundTurtles := []*Turtle{}
	for _, ts := range s.Turtles {
		if ts.Finish.Time == nil && symbolCode != nil && strings.EqualFold(ts.SymbolCode.Code, symbolCode.Code) {
			foundTurtles = append(foundTurtles, ts)
		}
	}
	// 如果找到不止一个 Turtle，清理掉较早的 Turtle
	foundCount := len(foundTurtles)
	if foundCount > 1 {
		priorTurtles := foundTurtles[0 : foundCount-1]
		for _, t := range priorTurtles {
			t.FinishWithReason(FinishReasonCleanUp)
			zlog.Errorf("get turle by symbol found more than 1, finish turtle %s with reason code CleanUp", t.ID)
		}
		return foundTurtles[len(foundTurtles)-1]
	} else if len(foundTurtles) == 1 {
		return foundTurtles[0]
	}
	return nil
}

func (s *Storage) GetTurtleBySymbol(symbolCode *exchange.SymbolCode) *Turtle {
	return s.FinishDuplicateTurtles(symbolCode)
}

func (s *Storage) AddTurtle(turtle *Turtle) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if turtle == nil {
		zlog.Errorf("trying to add nil turtle into storage")
		return
	}

	found := false
	for _, ts := range s.Turtles {
		if ts.ID == turtle.ID {
			found = true
		}
	}
	if !found {
		s.Turtles = append(s.Turtles, turtle)
		s.Save()
	}
}

func (s *Storage) RemoveTurtleWithIDs(ids []string) (removedIDs []string) {
	removedIDs = []string{}
	indexes := []int{}
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i, tt := range s.Turtles {
		found := false
		for _, id := range ids {
			if tt.ID == id {
				found = true
				removedIDs = append(removedIDs, id)
				break
			}
		}
		if found {
			indexes = append(indexes, i)
		}
	}

	sort.Ints(indexes)
	// 倒序删除对应的 index，如果是正序删除，顺序会乱掉
	for i := len(indexes) - 1; i >= 0; i-- {
		firstPart := s.Turtles[:indexes[i]]
		secondPart := s.Turtles[indexes[i]+1:]
		s.Turtles = append(firstPart, secondPart...)
	}
	s.Save()
	return
}

func removeOrderFromOrderList(orders OrderList, orderID string) (OrderList, bool) {
	if orderID == "" {
		return orders, false
	}

	index := -1
	for i, o := range orders {
		if strings.Contains(o.OrderID, orderID) {
			index = i
		}
	}

	if index >= 0 {
		former := orders[:index]
		later := orders[index+1:]
		orders = append(former, later...)
		return orders, true
	} else {
		return orders, false
	}
}

// 根据订单ID删除订单，用于清理过期的订单
func (s *Storage) removeOpenOrderByIDs(ids []string) []string {
	removedIDs := []string{}
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for _, tt := range s.Turtles {
		if tt.Finish.Time == nil {
			for _, orderID := range ids {
				if newOrders, ok := removeOrderFromOrderList(tt.Long.OpenOrders, orderID); ok {
					removedIDs = append(removedIDs, orderID)
					tt.Long.OpenOrders = newOrders
				}
				if newOrders, ok := removeOrderFromOrderList(tt.Short.OpenOrders, orderID); ok {
					removedIDs = append(removedIDs, orderID)
					tt.Short.OpenOrders = newOrders
				}
				if newOrders, ok := removeOrderFromOrderList(tt.CloseOrders, orderID); ok {
					removedIDs = append(removedIDs, orderID)
					tt.CloseOrders = newOrders
				}
			}
		}
	}
	return removedIDs
}

func (s *Storage) findTurtleByID(refID string) *Turtle {
	for _, t := range s.Turtles {
		if t.ID == refID {
			return t
		}
	}
	return nil
}

type PositionPriceInfo struct {
	PositionQty     float64
	PositionPrice   float64
	TakeProfitPrice float64
	StopLossPrice   float64
	LongPrices      []float64
	ShortPrices     []float64
	LongQty         []float64
	ShortQty        []float64
	Units           int
	LastPrice       float64
}

type RotatePlan struct {
	From             *PositionPriceInfo
	To               *PositionPriceInfo
	PriceTickSize    int     // 价格 tick size 用于 render
	PriceDelta       float64 // 展期时现价差
	ClosePositionPNL float64 // 展期时平仓盈亏
}

func (this *RotatePlan) formatPrice(price float64) string {
	return strconv.FormatFloat(price, 'f', this.PriceTickSize, 64)
}

func (this *RotatePlan) Render() string {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	headers := []string{"", "From", "To", "Change", "Change %"}
	t.SetHeader(headers)

	from := this.From
	to := this.To
	change := to.PositionQty - from.PositionQty
	changePct := 0.0
	if from.PositionQty != 0 {
		changePct = (to.PositionQty - from.PositionQty) * 100 / from.PositionQty
	}
	t.AddRow([]string{
		"Position Qty",
		strconv.FormatFloat(from.PositionQty, 'f', -1, 64),
		strconv.FormatFloat(to.PositionQty, 'f', -1, 64),
		strconv.FormatFloat(change, 'f', -1, 64),
		fmt.Sprintf("%.2f%%", changePct),
	})

	if from.PositionQty != 0 {
		change := to.PositionPrice - from.PositionPrice
		t.AddRow([]string{
			"Position Price",
			this.formatPrice(from.PositionPrice),
			this.formatPrice(to.PositionPrice),
			this.formatPrice(change),
			fmt.Sprintf("%.2f%%", change*100/from.PositionPrice),
		})

		change = to.TakeProfitPrice - from.TakeProfitPrice
		t.AddRow([]string{
			"Take Profit Price",
			this.formatPrice(from.TakeProfitPrice),
			this.formatPrice(to.TakeProfitPrice),
			this.formatPrice(change),
			fmt.Sprintf("%.2f%%", change*100/from.TakeProfitPrice),
		})

		change = to.StopLossPrice - from.StopLossPrice
		t.AddRow([]string{
			"Stop Loss Price",
			this.formatPrice(from.StopLossPrice),
			this.formatPrice(to.StopLossPrice),
			this.formatPrice(change),
			fmt.Sprintf("%.2f%%", change*100/from.StopLossPrice),
		})
	}

	for i := 4; i > 0; i-- {
		filledLabel := ""
		if from.PositionQty > 0 && int(from.Units) >= i {
			filledLabel = "*"
		}
		from := from.LongPrices[i-1]
		to := to.LongPrices[i-1]
		change := to - from
		t.AddRow([]string{
			fmt.Sprintf("Long %v %s", i, filledLabel),
			this.formatPrice(from),
			this.formatPrice(to),
			this.formatPrice(change),
			fmt.Sprintf("%.2f%%", change*100/from),
		})
	}

	for i := 1; i < 5; i++ {
		filledLabel := ""
		if from.PositionQty < 0 && int(from.Units) >= i {
			filledLabel = " *"
		}
		from := from.ShortPrices[i-1]
		to := to.ShortPrices[i-1]
		change := to - from
		t.AddRow([]string{
			fmt.Sprintf("Short %v %s", i, filledLabel),
			this.formatPrice(from),
			this.formatPrice(to),
			this.formatPrice(change),
			fmt.Sprintf("%.2f%%", change*100/from),
		})
	}
	return t.Render()
}

// 预计亏损，即以止损价退出的损失
type EstimatedLoss struct {
	Longs    []float64 // 看多 1~4 个头寸时
	Shorts   []float64 // 看空 1~4 个头寸时
	Position float64   // 当前持仓预计损失
}

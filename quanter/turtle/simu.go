package turtle

import (
	"fmt"
	"math"
	"time"

	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

type Simulator struct {
	controller *TurtleController
	fromTime   *time.Time
	toTime     *time.Time
	symbolCode *exchange.SymbolCode
	config     *SymbolConfig
	initCash   float64
	fixedCash  float64
	klines     []*exchange.KLine

	barIndex       int
	barTime        *time.Time
	lastPrice      float64
	currentATR     float64
	breakoutPeriod int
	openID         string
	openATR        float64
	openPrice      float64
	openSize       float64
	worth          float64
	position       *Position
	positionUnits  int
	orders         []*SimuOrder
	estimatedLoss  *EstimatedLoss
}

type SimuOrder struct {
	ID             string
	Side           exchange.OrderSide
	OpenATR        float64
	BreakoutPeriod int
	IsClose        bool
	StopLoss       bool
	Price          float64
	Qty            float64
	ExecTime       *time.Time
	SubmitTime     *time.Time
	Worth          float64
	TotalPnl       float64
	TotalPnlRatio  float64
	MarginLeverage float64
}

func (this *Simulator) getKlines() (klines []*exchange.KLine, er error) {
	now := time.Now()
	duration := now.Sub(*this.fromTime)
	needKineNum := int(duration.Hours()/float64(this.config.PeriodHour)) + 1
	klines, err := this.controller.Exchange.GetLatestKLinesWithCache(this.symbolCode.InstrumentType(), this.config.Symbol, this.config.PeriodHour, needKineNum, false)
	klineCount := len(klines)
	if err != nil {
		er = fmt.Errorf("获取K线失败: %v", err)
		return
	}
	if klineCount < needKineNum {
		er = fmt.Errorf("获取K线数量不足, 需要 %d 根, 获取到 %d 根", needKineNum, klineCount)
		return
	}
	this.klines = klines
	return
}

func (this *Simulator) Simulate() (csvContents, pineContents string, er error) {
	klines, err := this.getKlines()
	if err != nil {
		er = err
		return
	}

	this.worth = this.initCash
	this.breakoutPeriod = this.config.BreakoutPeriod
	this.position = &Position{}

	for i, kline := range klines {
		this.barIndex = i
		dt := time.Unix(kline.Time, 0)
		this.barTime = &dt
		this.updateATR()
		this.lastPrice = kline.Close

		if i < this.config.ATRPeriod || i < this.breakoutPeriod {
			continue
		}

		if dt.Before(*this.fromTime) {
			continue
		}
		if dt.After(*this.toTime) {
			break
		}

		if this.position.Qty == 0 {
			this.openHandle(false, "")
		} else {
			this.closeHandle()
		}
	}

	csvContents = this.genCSV()
	pineContents = this.genPineScripts()
	return
}

func (this *Simulator) genCSV() string {
	csvContents := "Time,Symbol,OpenATR,BreakoutPeriod,Side,Price,Qty,ID,MarginLeverage,TradePNL,PNLRatio,Worth,TotalPnl,TotalPnlRatio\n"
	lastTotalPnl := 0.0
	for _, order := range this.orders {
		tradePnl := 0.0
		pnlRatio := 0.0
		if order.IsClose {
			tradePnl = order.TotalPnl - lastTotalPnl
			pnlRatio = tradePnl / (this.initCash + lastTotalPnl)
			lastTotalPnl = order.TotalPnl
		}
		csvContents += fmt.Sprintf("%s,%s,%.5f,%d,%s,%.5f,%.4f,%s,%.2f,%.2f,%.4f,%.2f,%.2f,%.6f\n",
			order.ExecTime.Format("2006-01-02 15:04"),
			this.config.Symbol,
			order.OpenATR,
			order.BreakoutPeriod,
			order.Side,
			order.Price,
			order.Qty,
			order.ID,
			order.MarginLeverage,
			tradePnl,
			pnlRatio,
			order.Worth,
			order.TotalPnl,
			order.TotalPnlRatio,
		)
	}
	return csvContents
}

func (this *Simulator) genPineScripts() string {
	pineContents := fmt.Sprintf("//@version=4\nstrategy(\"simu\", default_qty_type=strategy.cash, initial_capital=%.0f, commission_type=strategy.commission.percent, commission_value=%.6f, calc_on_order_fills=true, close_entries_rule=\"ANY\")\n", this.initCash, this.config.FeeRate*100)

	var openTimeAfrerClose *time.Time
	var closeTime *time.Time
	closed := false
	var lastOpenTime *time.Time
	for _, order := range this.orders {
		submitTime := order.SubmitTime

		script := ""
		longShort := "long"
		if order.Side == exchange.OrderSideSell {
			longShort = "short"
		}
		// 注意 pine 脚本严格要求格式和对齐，不要随意加空白字符
		// 成交后下一根 K 线的挂单，要加上 position_size 判断条件，否则可能触发两次
		if order.IsClose {
			openTimeAfrerClose = nil
			closeTime = submitTime
			closed = true
			// 用 strategy.exit 时如果同一 K 线同时有开仓会无视数量也平仓，改用 strategy.order, 但开平仓数量需要精确
			if order.StopLoss {
				if lastOpenTime != submitTime {
					script = fmt.Sprintf("\nif year == %d and month == %d and dayofmonth == %d and hour == %d\n    strategy.order(\"%s\", strategy.%s, qty=%.4f, stop=%.5f, when=strategy.position_size != 0)\n", submitTime.Year(), submitTime.Month(), submitTime.Day(), submitTime.Hour(), order.ID, longShort, math.Abs(order.Qty), order.Price)
				} else {
					// 同一 K 线止损用 limit
					script = fmt.Sprintf("\nif year == %d and month == %d and dayofmonth == %d and hour == %d\n    strategy.order(\"%s\", strategy.%s, qty=%.4f, limit=%.5f)\n", submitTime.Year(), submitTime.Month(), submitTime.Day(), submitTime.Hour(), order.ID, longShort, math.Abs(order.Qty), order.Price)
				}
			} else {
				script = fmt.Sprintf("\nif year == %d and month == %d and dayofmonth == %d and hour == %d\n    strategy.order(\"%s\", strategy.%s, qty=%.4f, stop=%.5f, when=strategy.position_size != 0)\n", submitTime.Year(), submitTime.Month(), submitTime.Day(), submitTime.Hour(), order.ID, longShort, math.Abs(order.Qty), order.Price)
			}
		} else {
			// strategy.entry 如果是 stop 类型，成交之后再挂同类型订单则无法触发，用 strategy.order 没这个问题
			if openTimeAfrerClose == nil && closeTime != submitTime && closed {
				openTimeAfrerClose = submitTime
			}
			if openTimeAfrerClose == submitTime {
				script = fmt.Sprintf("\nif year == %d and month == %d and dayofmonth == %d and hour == %d\n    strategy.order(\"%s\", strategy.%s, qty=%.4f, stop=%.5f, when=strategy.position_size == 0)\n", submitTime.Year(), submitTime.Month(), submitTime.Day(), submitTime.Hour(), order.ID, longShort, math.Abs(order.Qty), order.Price)
			} else {
				script = fmt.Sprintf("\nif year == %d and month == %d and dayofmonth == %d and hour == %d\n    strategy.order(\"%s\", strategy.%s, qty=%.4f, stop=%.5f)\n", submitTime.Year(), submitTime.Month(), submitTime.Day(), submitTime.Hour(), order.ID, longShort, math.Abs(order.Qty), order.Price)
			}
			closed = false
			lastOpenTime = submitTime
		}

		pineContents += script
	}

	return pineContents
}

func (this *Simulator) getTrueRange(i int) float64 {
	kline := this.klines[i]
	return getTrueRange(kline.High, kline.Low, kline.Open)
}

func (this *Simulator) updateATR() float64 {
	i := this.barIndex - 1
	if i < this.config.ATRPeriod-1 {
		return 0
	} else if i == this.config.ATRPeriod-1 {
		sum := 0.0
		for j := 0; j < this.config.ATRPeriod; j++ {
			tr := this.getTrueRange(j)
			sum += tr
		}
		this.currentATR = sum / float64(this.config.ATRPeriod)
	} else {
		tr := this.getTrueRange(i)
		this.currentATR = (this.currentATR*(float64(this.config.ATRPeriod)-1) + tr) / float64(this.config.ATRPeriod)
	}
	return this.currentATR
}

func (this *Simulator) getDC(period int) (high, low float64) {
	high = this.klines[this.barIndex-1].High
	low = this.klines[this.barIndex-1].Low
	for i := 0; i < period; i++ {
		high = math.Max(high, this.klines[this.barIndex-i-1].High)
		low = math.Min(low, this.klines[this.barIndex-i-1].Low)
	}
	return high, low
}

func (this *Simulator) getBreakoutPrice() (high, low float64) {
	priceHigh, priceLow := this.getDC(this.breakoutPeriod)
	high = priceHigh * (1 + this.config.BreakoutSlippageRate)
	low = priceLow * (1 - this.config.BreakoutSlippageRate)
	return
}

func (this *Simulator) size2Qty(price float64, size float64) (qty float64) {
	if qty, err := this.controller.Exchange.Size2Qty(this.symbolCode.InstrumentType(), this.config.Symbol, price, size); err != nil {
		this.controller.ErrorMsgf("convert size 2 qty error: %s", err)
		return 0
	} else {
		return qty
	}
}

func (this *Simulator) qty2Size(price float64, qty float64) (size float64) {
	if size, err := this.controller.Exchange.Qty2Size(this.symbolCode.InstrumentType(), this.config.Symbol, price, qty); err != nil {
		this.controller.ErrorMsgf("convert qty 2 size error: %s", err)
		return 0
	} else {
		return size
	}
}

func (this *Simulator) calcPrice(qty float64, size float64) (price float64) {
	if price, err := this.controller.Exchange.CalcPrice(this.symbolCode.InstrumentType(), this.config.Symbol, qty, size); err != nil {
		this.controller.ErrorMsgf("convert qty and size 2 price error: %s", err)
		return 0
	} else {
		return price
	}
}

func (this *Simulator) calculateOpenOrderPriceAndQtyByIndex(side exchange.OrderSide, orderIndex int) (price float64, qty float64) {
	atr := this.openATR
	if side == exchange.OrderSideBuy {
		price = this.openPrice + float64(orderIndex)*0.5*atr
	} else {
		price = this.openPrice - float64(orderIndex)*0.5*atr
	}

	// 反向合约每单价值相同数量不同，正向合约每单数量相同
	if this.controller.Exchange.IsReverseSymbol(this.symbolCode.InstrumentType(), this.config.Symbol) {
		qty = this.size2Qty(price, this.openSize)
	} else {
		qty = this.size2Qty(this.openPrice, this.openSize)
	}
	return
}

func (this *Simulator) openHandle(afterClose bool, lastPositionSide Side) {
	priceHigh, priceLow := this.getBreakoutPrice()
	this.openATR = this.currentATR
	// dtStr := this.barTime.Format("2006-01-02 15:04")
	// zlog.Debugf("[%s] open dc[%d]: %.2f, %.2f, atr: %.2f", dtStr, this.breakoutPeriod, priceHigh, priceLow, this.openATR)

	if this.fixedCash != 0 {
		this.worth = this.fixedCash
	}

	kline := this.klines[this.barIndex]
	openLong := false
	openShort := false
	if kline.High >= priceHigh {
		openLong = true
		this.openPrice = priceHigh
		this.openSize = priceHigh * this.worth * this.config.OpenSizePercent / this.openATR
	} else if kline.Low <= priceLow {
		openShort = true
		this.openPrice = priceLow
		this.openSize = priceLow * this.worth * this.config.OpenSizePercent / this.openATR
	} else {
		return
	}

	if afterClose {
		// 平仓后立即开仓只做反方向，更接近真实情况
		if lastPositionSide == SideLong && openLong {
			return
		}
		if lastPositionSide == SideShort && openShort {
			return
		}
	}

	this.openID = exchange.NewRandomID()
	for i := 0; i < this.config.MaxOpenUnits; i++ {
		if openLong {
			price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideBuy, i)
			if kline.High >= price {
				this.trade(exchange.OrderSideBuy, price, qty, i, false)
			}
		} else if openShort {
			price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideSell, i)
			if kline.Low <= price {
				this.trade(exchange.OrderSideSell, price, qty, i, false)
			}
		}
	}

	if afterClose {
		return
	}

	// 开仓即止损的情况
	// 因为 K 线内走势是不确定的，使用收盘价作为判断是否止损
	if openLong {
		exitPrice := this.getExitPrice()
		if kline.Close <= exitPrice {
			zlog.Debugf("stop loss in the same bar @%.2f", exitPrice)
			this.trade(exchange.OrderSideSell, exitPrice, math.Abs(this.position.Qty), 0, true)
		}
	} else {
		exitPrice := this.getExitPrice()
		if kline.Close >= exitPrice {
			zlog.Debugf("stop loss in the same bar @%.2f", exitPrice)
			this.trade(exchange.OrderSideBuy, exitPrice, math.Abs(this.position.Qty), 0, true)
		}
	}
}

func (this *Simulator) trade(side exchange.OrderSide, price, qty float64, unitIndex int, isClose bool) {
	order := &SimuOrder{
		Side:           side,
		IsClose:        isClose,
		Price:          price,
		Qty:            qty,
		ExecTime:       this.barTime,
		OpenATR:        this.openATR,
		BreakoutPeriod: this.breakoutPeriod,
	}

	submitTime := this.barTime.Add(time.Hour * time.Duration(-this.config.PeriodHour))
	order.SubmitTime = &submitTime

	this.worth -= this.qty2Size(price, qty) * this.config.FeeRate

	dtStr := this.barTime.Format("2006-01-02 15:04")
	if isClose {
		stopLoss := true
		if this.position.Qty > 0 && this.position.EntryPrice < price {
			stopLoss = false
		} else if this.position.Qty < 0 && this.position.EntryPrice > price {
			stopLoss = false
		}
		order.StopLoss = stopLoss
		if stopLoss {
			order.ID = fmt.Sprintf("StopLoss_%s", this.openID)
			zlog.Debugf("[%s] stop loss @%.2f", dtStr, price)
			this.breakoutPeriod = this.config.BreakoutPeriod
		} else {
			order.ID = fmt.Sprintf("TakeProfit_%s", this.openID)
			zlog.Debugf("[%s] take profit @%.2f", dtStr, price)
			if this.config.SecondaryBreakoutPeriod != 0 {
				this.breakoutPeriod = this.config.SecondaryBreakoutPeriod
			}
		}

		pnl := this.qty2Size(price, this.position.Qty) - this.qty2Size(this.position.EntryPrice, this.position.Qty)
		if this.controller.Exchange.IsReverseSymbol(this.symbolCode.InstrumentType(), this.config.Symbol) {
			pnl *= -1
		}

		this.worth += pnl
		this.position = &Position{}
		this.positionUnits = 0

	} else {
		this.addPosition(side, price, qty)

		exitPrice := 0.0
		if side == exchange.OrderSideBuy {
			exitPrice = this.getExitPrice()
			order.ID = fmt.Sprintf("Long_%d_%s", unitIndex, this.openID)
			zlog.Debugf("[%s] break out long[%d] @%.2f, atr: %.2f", dtStr, unitIndex, price, this.openATR)
		} else {
			exitPrice = this.getExitPrice()
			order.ID = fmt.Sprintf("Short_%d_%s", unitIndex, this.openID)
			zlog.Debugf("[%s] break out short[%d] @%.2f, atr: %.2f", dtStr, unitIndex, price, this.openATR)
		}

		margin := math.Abs(this.qty2Size(exitPrice, this.position.Qty) - this.qty2Size(this.position.EntryPrice, this.position.Qty))
		order.MarginLeverage = this.qty2Size(this.position.EntryPrice, math.Abs(this.position.Qty)) / margin
	}

	order.Worth = this.worth
	order.TotalPnl = this.worth - this.initCash
	order.TotalPnlRatio = order.TotalPnl / this.initCash
	this.orders = append(this.orders, order)
}

func (this *Simulator) addPosition(side exchange.OrderSide, price, qty float64) {
	if this.position.Qty != 0 {
		positionValue := this.qty2Size(this.position.EntryPrice, math.Abs(this.position.Qty))
		positionValue += this.qty2Size(price, qty)
		newQty := math.Abs(this.position.Qty) + qty
		this.position.EntryPrice = this.calcPrice(newQty, positionValue)
	} else {
		this.position.EntryPrice = price
	}
	if side == exchange.OrderSideBuy {
		this.position.Qty += qty
	} else {
		this.position.Qty -= qty
	}
	this.positionUnits += 1
}

func (this *Simulator) getExitPrice() float64 {
	priceHigh, priceLow := this.getDC(this.config.ExitPeriod)
	priceHigh = priceHigh * (1 + this.config.ExitSlippageRate)
	priceLow = priceLow * (1 - this.config.ExitSlippageRate)

	if this.position.Qty > 0 {
		dcExitPrice := priceLow
		atrExitPrice := this.position.EntryPrice - this.openATR*1.25 - (4-float64(this.positionUnits))*0.25*this.openATR
		return math.Max(dcExitPrice, atrExitPrice)
	} else {
		dcExitPrice := priceHigh
		atrExitPrice := this.position.EntryPrice + this.openATR*1.25 + (4-float64(this.positionUnits))*0.25*this.openATR
		return math.Min(dcExitPrice, atrExitPrice)
	}
}

func (this *Simulator) closeHandle() {
	kline := this.klines[this.barIndex]
	lastPositionSide := SideLong
	if this.position.Qty > 0 {
		exitPrice := this.getExitPrice()
		if kline.Low <= exitPrice {
			if kline.High <= exitPrice {
				exitPrice = kline.Open // 说明当前 K 线已触发退出了，改用开盘价
			}
			this.trade(exchange.OrderSideSell, exitPrice, math.Abs(this.position.Qty), 0, true)
		}
	} else {
		lastPositionSide = SideShort
		exitPrice := this.getExitPrice()
		if kline.High >= exitPrice {
			if kline.Low >= exitPrice {
				exitPrice = kline.Open // 说明当前 K 线已触发退出了，改用开盘价
			}
			this.trade(exchange.OrderSideBuy, exitPrice, math.Abs(this.position.Qty), 0, true)
		}
	}

	if this.position.Qty == 0 {
		this.openHandle(true, lastPositionSide) // 如果平仓后会反向突破，也要提前挂单
		return
	}

	// 如果没有触发平仓，则判断是否加仓
	for i := 0; i < this.config.MaxOpenUnits; i++ {
		if i < this.positionUnits {
			continue
		}

		if this.position.Qty > 0 {
			price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideBuy, i)
			if kline.High >= price {
				this.trade(exchange.OrderSideBuy, price, qty, i, false)
			}
		} else {
			price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideSell, i)
			if kline.Low <= price {
				this.trade(exchange.OrderSideSell, price, qty, i, false)
			}
		}
	}
}

func (this *Simulator) calculateEstimatedLoss() {
	this.estimatedLoss = &EstimatedLoss{
		Longs:  []float64{0, 0, 0, 0},
		Shorts: []float64{0, 0, 0, 0},
	}

	if this.position.Qty != 0 {
		exitPrice := this.getExitPrice()
		openValue := this.qty2Size(this.position.EntryPrice, math.Abs(this.position.Qty))
		if (this.position.Qty > 0 && this.position.EntryPrice > exitPrice) || (this.position.Qty < 0 && this.position.EntryPrice < exitPrice) {
			stopLossValue := this.qty2Size(exitPrice, math.Abs(this.position.Qty))
			loss := stopLossValue - openValue
			if this.position.Qty < 0 {
				loss *= -1
			}
			if this.controller.Exchange.IsReverseSymbol(this.symbolCode.InstrumentType(), this.config.Symbol) {
				loss *= -1
			}
			this.estimatedLoss.Position = loss
		}

		// 已成交订单预估损失设为 0，估算未成交订单
		positionUnits := this.positionUnits
		totalQty := math.Abs(this.position.Qty)
		totalValue := openValue
		for i := 1; i <= this.config.MaxOpenUnits; i++ {
			if this.position.Qty < 0 {
				this.estimatedLoss.Longs[i-1] = 0
				if positionUnits >= i {
					this.estimatedLoss.Shorts[i-1] = 0
				} else {
					price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideSell, i-1)
					totalValue += this.qty2Size(price, math.Abs(qty))
					totalQty += qty
					avgPrice := this.calcPrice(totalQty, totalValue)
					stopLossPrice := avgPrice + (2.25-float64(i)*0.25)*this.openATR
					stopLossValue := this.qty2Size(stopLossPrice, math.Abs(totalQty))
					loss := totalValue - stopLossValue
					if this.controller.Exchange.IsReverseSymbol(this.symbolCode.InstrumentType(), this.config.Symbol) {
						loss *= -1
					}
					this.estimatedLoss.Shorts[i-1] = loss
				}
			} else {
				this.estimatedLoss.Shorts[i-1] = 0
				if positionUnits >= i {
					this.estimatedLoss.Longs[i-1] = 0
				} else {
					price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideBuy, i-1)
					totalValue += this.qty2Size(price, math.Abs(qty))
					totalQty += qty
					avgPrice := this.calcPrice(totalQty, totalValue)
					// 一个头寸时止损价=持仓价-2ATR，2个时=持仓价-1.75ATR，3个时=持仓价-1.5ATR，4个时=持仓价-1.25ATR
					stopLossPrice := avgPrice - (2.25-float64(i)*0.25)*this.openATR
					stopLossValue := this.qty2Size(stopLossPrice, math.Abs(totalQty))
					loss := stopLossValue - totalValue
					if this.controller.Exchange.IsReverseSymbol(this.symbolCode.InstrumentType(), this.config.Symbol) {
						loss *= -1
					}
					this.estimatedLoss.Longs[i-1] = loss
				}
			}
		}

	} else {
		// 依次估算各单位头寸累计损失
		var totalQty float64
		var totalValue float64
		for i := 1; i <= this.config.MaxOpenUnits; i++ {
			price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideBuy, i-1)
			totalValue += this.qty2Size(price, math.Abs(qty))
			totalQty += qty
			avgPrice := this.calcPrice(totalQty, totalValue)
			// 一个头寸时止损价=持仓价-2ATR，2个时=持仓价-1.75ATR，3个时=持仓价-1.5ATR，4个时=持仓价-1.25ATR
			stopLossPrice := avgPrice - (2.25-float64(i)*0.25)*this.openATR
			stopLossValue := this.qty2Size(stopLossPrice, math.Abs(totalQty))
			loss := stopLossValue - totalValue
			if this.controller.Exchange.IsReverseSymbol(this.symbolCode.InstrumentType(), this.config.Symbol) {
				loss *= -1
			}
			this.estimatedLoss.Longs[i-1] = loss
		}

		totalQty = 0
		totalValue = 0
		for i := 1; i <= this.config.MaxOpenUnits; i++ {
			price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideSell, i-1)
			totalValue += this.qty2Size(price, math.Abs(qty))
			totalQty += qty
			avgPrice := this.calcPrice(totalQty, totalValue)
			stopLossPrice := avgPrice + (2.25-float64(i)*0.25)*this.openATR
			stopLossValue := this.qty2Size(stopLossPrice, math.Abs(totalQty))
			loss := totalValue - stopLossValue
			if this.controller.Exchange.IsReverseSymbol(this.symbolCode.InstrumentType(), this.config.Symbol) {
				loss *= -1
			}
			this.estimatedLoss.Shorts[i-1] = loss
		}
	}
}

func (this *Simulator) getMaxEstimatedLoss() float64 {
	return math.Min(this.estimatedLoss.Longs[this.config.MaxOpenUnits-1], this.estimatedLoss.Shorts[this.config.MaxOpenUnits-1])
}

func (this *Simulator) SendStatus(responder command.CommandProcessorResponder) {
	statusTb := NewTable()
	statusTb.AddRow([]string{"ATR", this.controller.Exchange.FormatPrice(this.symbolCode.InstrumentType(), this.config.Symbol, this.openATR)})
	statusTb.AddRow([]string{"BreakoutPeriod", fmt.Sprintf("%d", this.breakoutPeriod)})
	statusTb.AddRow([]string{"Balance", this.controller.formatAmount("", this.worth)})
	statusTb.AddRow([]string{"Position.Qty", this.controller.Exchange.FormatDisplayQty(this.symbolCode.InstrumentType(), this.config.Symbol, this.position.Qty)})
	positionValue := 0.0
	if this.position.Qty != 0 {
		positionValue = this.qty2Size(this.position.EntryPrice, this.position.Qty)
	}
	statusTb.AddRow([]string{"Position.Value", this.controller.formatAmount("", positionValue)})
	statusTb.AddRow([]string{"Units", fmt.Sprintf("%d", this.positionUnits)})

	t := NewTable()
	t.SetHeader([]string{"Side", "Unit", "Qty", "Value", "Price"})
	if this.position.Qty == 0 {

		priceHigh, priceLow := this.getBreakoutPrice()
		this.openPrice = priceHigh
		this.openSize = priceHigh * this.worth * this.config.OpenSizePercent / this.openATR
		for i := 0; i < this.config.MaxOpenUnits; i++ {
			price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideBuy, i)
			row := []string{"Long"}
			row = append(row, fmt.Sprintf("%d", i+1))
			row = append(row, this.controller.Exchange.FormatDisplayQty(this.symbolCode.InstrumentType(), this.config.Symbol, qty))
			value := this.qty2Size(price, qty)
			row = append(row, this.controller.formatAmount("", value))
			row = append(row, this.controller.Exchange.FormatPrice(this.symbolCode.InstrumentType(), this.config.Symbol, price))
			t.AddRow(row)
		}

		this.openPrice = priceLow
		this.openSize = priceLow * this.worth * this.config.OpenSizePercent / this.openATR
		for i := 0; i < this.config.MaxOpenUnits; i++ {
			price, qty := this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideSell, i)
			row := []string{"Short"}
			row = append(row, fmt.Sprintf("%d", i+1))
			row = append(row, this.controller.Exchange.FormatDisplayQty(this.symbolCode.InstrumentType(), this.config.Symbol, qty))
			value := this.qty2Size(price, qty)
			row = append(row, this.controller.formatAmount("", value))
			row = append(row, this.controller.Exchange.FormatPrice(this.symbolCode.InstrumentType(), this.config.Symbol, price))
			t.AddRow(row)
		}

		this.calculateEstimatedLoss()
		statusTb.AddRow([]string{"Max Est. Loss", fmt.Sprintf("%s %.2f%%", this.controller.formatAmount("", this.getMaxEstimatedLoss()), this.getMaxEstimatedLoss()*100/this.worth)})
	} else {
		for i := 0; i < this.config.MaxOpenUnits; i++ {
			if i < this.positionUnits {
				continue
			}

			var price, qty float64
			row := []string{}
			if this.position.Qty > 0 {
				price, qty = this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideBuy, i)
				row = append(row, "Long")
			} else {
				price, qty = this.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideSell, i)
				row = append(row, "Short")
			}
			row = append(row, fmt.Sprintf("%d", i+1))
			row = append(row, this.controller.Exchange.FormatDisplayQty(this.symbolCode.InstrumentType(), this.config.Symbol, qty))
			value := this.qty2Size(price, qty)
			row = append(row, this.controller.formatAmount("", value))
			row = append(row, this.controller.Exchange.FormatPrice(this.symbolCode.InstrumentType(), this.config.Symbol, price))
			t.AddRow(row)
		}

		stopPrice := 0.0
		if this.position.Qty > 0 {
			stopPrice = this.position.EntryPrice - this.openATR*1.25 - (4-float64(this.positionUnits))*0.25*this.openATR
		} else {
			stopPrice = this.position.EntryPrice + this.openATR*1.25 + (4-float64(this.positionUnits))*0.25*this.openATR
		}

		value := this.qty2Size(stopPrice, this.position.Qty)
		t.AddRow([]string{
			"StopLoss",
			"0",
			this.controller.Exchange.FormatDisplayQty(this.symbolCode.InstrumentType(), this.config.Symbol, -this.position.Qty),
			this.controller.formatAmount("", value),
			this.controller.Exchange.FormatPrice(this.symbolCode.InstrumentType(), this.config.Symbol, stopPrice),
		})

		exitPrice := this.getExitPrice()
		canTakeProfit := false
		if this.position.Qty > 0 && this.position.EntryPrice < exitPrice {
			canTakeProfit = true
		} else if this.position.Qty < 0 && this.position.EntryPrice > exitPrice {
			canTakeProfit = true
		}
		lockedPNL := 0.0
		if canTakeProfit {
			value := this.qty2Size(exitPrice, this.position.Qty)
			t.AddRow([]string{
				"TakeProfit",
				"0",
				this.controller.Exchange.FormatDisplayQty(this.symbolCode.InstrumentType(), this.config.Symbol, -this.position.Qty),
				this.controller.formatAmount("", value),
				this.controller.Exchange.FormatPrice(this.symbolCode.InstrumentType(), this.config.Symbol, exitPrice),
			})
			lockedPNL = math.Abs(value - positionValue)
		}

		pnl := 0.0
		currentValue := this.qty2Size(this.lastPrice, this.position.Qty)
		if this.position.Qty > 0 && this.position.EntryPrice < this.lastPrice {
			pnl = math.Abs(currentValue - positionValue)
		} else if this.position.Qty < 0 && this.position.EntryPrice > this.lastPrice {
			pnl = math.Abs(currentValue - positionValue)
		}

		this.calculateEstimatedLoss()
		statusTb.AddRow([]string{"Est. Loss", fmt.Sprintf("%s %.2f%%", this.controller.formatAmount("", this.estimatedLoss.Position), this.estimatedLoss.Position*100/this.worth)})
		statusTb.AddRow([]string{"PNL", fmt.Sprintf("%s %.2f%%", this.controller.formatAmount("", pnl), pnl*100/this.worth)})
		statusTb.AddRow([]string{"LockedPNL", fmt.Sprintf("%s %.2f%%", this.controller.formatAmount("", lockedPNL), lockedPNL*100/this.worth)})
	}
	ordersMsg := t.Render()
	statusMsg := statusTb.Render()

	responder.SendMsgf("```Status\n%s\n\nOpen Orders\n%s```", statusMsg, ordersMsg)
}

package turtle

import (
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

// 新增某个品种的命令

type AddSymbolControllerCommand ControllerCommand

func NewAddSymbolControllerCommand(controller *TurtleController) *AddSymbolControllerCommand {
	cmd := &AddSymbolControllerCommand{
		Command: command.Command{
			Name:            "addSymbol",
			Instruction:     "`.addSymbol SymbolCode GoogleAuthCode` 新增一个品种",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		controller: controller,
	}
	return cmd
}

func (this *AddSymbolControllerCommand) Do() bool {
	var symbolCode *exchange.SymbolCode
	if s, err := this.controller.NewSymbolCode(this.Args[0]); err != nil {
		this.ErrorMsgf("合约代码 (%s) 格式错误：%s", this.Args[0], err)
		return false
	} else {
		symbolCode = s
	}
	symbol, err := this.controller.Exchange.TranslateSymbolCodeToFutureSymbol(symbolCode)
	if err != nil {
		this.ErrorMsgf("合约代码 (%s) 解析错误：%s", this.Args[0], err)
		return false
	}

	if _, err := this.controller.Exchange.GetInstrument(symbolCode.InstrumentType(), symbol); err != nil {
		this.controller.ErrorMsgf("获取合约信息失败，请确认 Symbol (%s) 是否正确", symbol)
		return false
	}

	if this.controller.IsExchange(exchange.BitMEX) {
		this.controller.Exchange.CloseWebsocket(false)
	}

	this.controller.addSymbol(symbolCode, nil)
	return true
}

// 删除某个品种的命令

type RemoveSymbolControllerCommand ControllerCommand

func NewRemoveSymbolControllerCommand(controller *TurtleController) *RemoveSymbolControllerCommand {
	cmd := &RemoveSymbolControllerCommand{
		Command: command.Command{
			Name:            "removeSymbol",
			Instruction:     "`.removeSymbol SymbolCode GoogleAuthCode` 删除品种 Symbol ，同时将平仓并撤销该品种订单",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		controller: controller,
	}
	return cmd
}

func (this *RemoveSymbolControllerCommand) Do() bool {
	symbolCodeStr := this.Args[0]
	foundSymbol := ""
	for symbol := range this.controller.Turtles {
		if strings.EqualFold(symbol, symbolCodeStr) {
			foundSymbol = symbol
			break
		}
	}
	if foundSymbol == "" {
		this.controller.ErrorMsgf("删除的品种不存在 %s ", symbolCodeStr)
		return false
	}

	this.controller.removeSymbol(foundSymbol)
	return true
}

// 删除某个品种的命令

type RotateSymbolControllerCommand ControllerCommand

func NewRotateSymbolControllerCommand(controller *TurtleController) *RotateSymbolControllerCommand {
	cmd := &RotateSymbolControllerCommand{
		Command: command.Command{
			Name:            "rotateSymbol",
			Instruction:     "`.rotateSymbol SymbolCodeX SymbolCodeY` 将 SymbolX 展期到 SymbolY ",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
		},
		controller: controller,
	}
	return cmd
}

func (this *RotateSymbolControllerCommand) Prepare() bool {
	var symbolCodeX *exchange.SymbolCode
	var symbolCodeY *exchange.SymbolCode

	if s, err := this.controller.NewSymbolCode(this.Args[0]); err != nil {
		this.ErrorMsgf("合约代码 (%s) 错误：%s", this.Args[0], err)
		return false
	} else {
		symbolCodeX = s
	}
	if s, err := this.controller.NewSymbolCode(this.Args[1]); err != nil {
		this.ErrorMsgf("合约代码 (%s) 错误：%s", this.Args[1], err)
		return false
	} else {
		symbolCodeY = s
	}
	if symbolCodeX.InstrumentType() != symbolCodeY.InstrumentType() {
		this.ErrorMsgf("合约代码的保证金类型不一致， (%s) 和 (%s)。", symbolCodeX.InstrumentType(), symbolCodeY.InstrumentType())
		return false
	}
	if symbolCodeX.Coin() != symbolCodeY.Coin() {
		this.ErrorMsgf("合约代码不是同一个币种，(%s) 和 (%s)", symbolCodeX.Coin(), symbolCodeY.Coin())
		return false
	}
	symbolX, _ := this.controller.Exchange.TranslateSymbolCodeToFutureSymbol(symbolCodeX)
	symbolY, _ := this.controller.Exchange.TranslateSymbolCodeToFutureSymbol(symbolCodeY)

	tt, ok := this.controller.Turtles[symbolCodeX.Code]
	if !ok {
		this.controller.ErrorMsgf("%s 不存在", symbolCodeX)
		return false
	} else if tt.Status != RUNNING {
		this.controller.ErrorMsgf("%s 不在运行中", symbolCodeX)
		return false
	}

	if ttY, ok := this.controller.Turtles[symbolCodeY.Code]; ok {
		if ttY.Position.Qty != 0 {
			this.controller.ErrorMsgf("%s 有仓位，请先平仓并暂停运行", symbolCodeY)
			return false
		} else if ttY.Status == RUNNING {
			this.controller.ErrorMsgf("%s 已在运作中，请先暂停运行", symbolCodeY)
			return false
		}
	}

	instrumentY, err := this.controller.Exchange.GetInstrument(tt.InstrumentType, symbolY)
	if err != nil {
		this.controller.ErrorMsgf("获取合约信息失败，请确认 %s 是否正确", symbolCodeY)
		return false
	}

	if this.controller.IsExchange(exchange.BitMEX) {
		this.controller.Exchange.CloseWebsocket(false)
	}

	instrumentX, _ := this.controller.Exchange.GetInstrument(tt.InstrumentType, symbolX)
	if instrumentX.SettleCurrency != instrumentY.SettleCurrency {
		this.controller.ErrorMsgf("合约保证金币种不同，无法执行")
		return false
	}

	err = this.controller.rotateSymbol(symbolCodeX, symbolCodeY, true)
	if err != nil {
		this.controller.ErrorMsgf(err.Error())
		return false
	}
	return true
}

func (this *RotateSymbolControllerCommand) Do() bool {
	// Prepare 已经检查过 symbolCode 了，这里就不再次检查了
	symbolCodeX, _ := this.controller.NewSymbolCode(this.Args[0])
	symbolCodeY, _ := this.controller.NewSymbolCode(this.Args[1])
	err := this.controller.rotateSymbol(symbolCodeX, symbolCodeY, false)
	if err != nil {
		this.controller.Errorf("rotate symbol failed, error: %s", err)
	}
	return true
}

// 跟 AddSymbol, RemoveSymbol 和 RotateSymbol 有关的具体实现

func (this *TurtleController) addSymbol(symbolCode *exchange.SymbolCode, copyConfig *SymbolConfig) {
	if symbolCode == nil {
		this.Errorf("symbol code is nil, nothing to be done")
		return
	}
	if this.Turtles[symbolCode.Code] != nil {
		this.Errorf("symbol already added, nothing to be done")
		return
	}
	this.Config.Symbols = append(this.Config.Symbols, symbolCode.Code)
	if tt, err := NewTurtle(this, symbolCode, nil, copyConfig, false); err != nil {
		this.Errorf("add symbol failed, symbol (%s) error: %s", symbolCode, err)
	} else {
		this.Turtles[symbolCode.Code] = tt
		tt.sendStatus(false)

		this.Config.Save()
		this.storage.Turtles = append(this.storage.Turtles, tt)
		this.storage.Save()

		this.SendMsgf("%s 添加完成", symbolCode)
	}
}

func (this *TurtleController) removeSymbol(symbolCodeStr string) {
	tt := this.Turtles[symbolCodeStr]
	delete(this.Turtles, symbolCodeStr)

	tt.closePositionAndStop(FinishReasonRemoveSymbol)
	tt.Config.Delete()

	tt.removeChannel()

	newSymbols := []string{}
	for _, s := range this.Config.Symbols {
		if s != symbolCodeStr {
			newSymbols = append(newSymbols, s)
		}
	}
	this.Config.Symbols = newSymbols
	this.Config.Save()

	this.SendMsgf("%s 已删除", symbolCodeStr)
}

func (this *TurtleController) rotateSymbol(symbolCodeX, symbolCodeY *exchange.SymbolCode, printOnly bool) (er error) {
	if symbolCodeX.InstrumentType() != symbolCodeY.InstrumentType() {
		er = fmt.Errorf("rotate symbol failed, different instrument type of (%s) and (%s)", symbolCodeX, symbolCodeY)
		return
	}
	if symbolCodeX.Coin() != symbolCodeY.Coin() {
		er = fmt.Errorf("rotate symbol failed, different underlying of (%s) and (%s)", symbolCodeX, symbolCodeY)
		return
	}

	symbolX, _ := this.Exchange.TranslateSymbolCodeToFutureSymbol(symbolCodeX)
	symbolY, _ := this.Exchange.TranslateSymbolCodeToFutureSymbol(symbolCodeY)

	instrumentX, err := this.Exchange.GetInstrument(symbolCodeX.InstrumentType(), symbolX)
	if err != nil {
		er = fmt.Errorf("rotate symbol failed, get instrument failed for from-symbol (%s), error: %s", symbolCodeX.Code, err)
		return
	}
	instrumentY, err := this.Exchange.GetInstrument(symbolCodeY.InstrumentType(), symbolY)
	if err != nil {
		er = fmt.Errorf("rotate symbol failed, get instrument failed for to-symbol (%s), error: %s", symbolCodeY.Code, err)
		return
	}

	if this.IsExchange(exchange.CTP) {
		if instrumentX.Status != exchange.InstrumentStatusContinuous || instrumentY.Status != exchange.InstrumentStatusContinuous {
			er = fmt.Errorf("rotate symbol failed, instrument is not continuous trading status, from-symbol status: %s, to-symbol status: %s", instrumentX.Status, instrumentY.Status)
			return
		}
	}

	if symbolX == "" || symbolY == "" {
		er = fmt.Errorf("rotate symbol failed, one of symbol is empty: (%s) -> (%s)", symbolX, symbolY)
		return
	}

	// 获取要展期的海龟数据
	tt := this.Turtles[symbolCodeX.Code]
	from := tt.getPositionPriceInfo()

	positionQty := tt.Position.Qty
	positionUnits := from.Units
	ttLastPrice, er := tt.getLastPrice()
	if er != nil {
		return fmt.Errorf("rotate symbol get %s last price error: %s", symbolCodeX, er)
	}
	from.LastPrice = ttLastPrice

	var newTT *Turtle
	symbolYExist := false
	if ttY := this.Turtles[symbolCodeY.Code]; ttY != nil {
		if nt, err := NewTurtle(this, symbolCodeY, ttY, nil, false); err != nil {
			er = fmt.Errorf("rotate symbol create new turtle (%s) error: %s", symbolCodeY, err)
			return
		} else {
			newTT = nt
		}
		symbolYExist = true
	} else {
		if nt, err := NewTurtle(this, symbolCodeY, nil, tt.Config, false); err != nil {
			er = fmt.Errorf("rotate symbol create new turtle (%s) error: %s", symbolCodeY, err)
			return
		} else {
			newTT = nt
		}
	}
	newTTLastPrice, er := newTT.getLastPrice()
	if er != nil {
		return fmt.Errorf("rotate symbol get %s last price error: %s", newTT.SymbolCode, er)
	}
	lastPriceDelta := newTTLastPrice - ttLastPrice

	to := &PositionPriceInfo{
		PositionQty:   positionQty,
		PositionPrice: 0,
		LongPrices:    []float64{0, 0, 0, 0},
		ShortPrices:   []float64{0, 0, 0, 0},
		LongQty:       []float64{0, 0, 0, 0},
		ShortQty:      []float64{0, 0, 0, 0},
		Units:         positionUnits,
		LastPrice:     newTTLastPrice,
	}

	pnl := 0.0
	if positionQty != 0 {
		positionValue := tt.Qty2Size(symbolX, tt.Position.EntryPrice, positionQty)
		closeValue := tt.Qty2Size(symbolX, ttLastPrice, positionQty)
		pnl = closeValue - positionValue
		if this.Exchange.IsReverseSymbol(tt.InstrumentType, symbolX) {
			pnl *= -1
		}

		to.PositionPrice = newTTLastPrice

		to.TakeProfitPrice = newTT.Long.ExitPrice.Value
		if positionQty < 0 {
			to.TakeProfitPrice = newTT.Short.ExitPrice.Value
		}

		// to.StopLossPrice = tt.calculateRotateStopPrice(to.Units, pnl, newTTLastPrice, positionQty)
		to.StopLossPrice = from.StopLossPrice + lastPriceDelta
	}

	for i := 4; i > 0; i-- {
		price, qty := tt.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideBuy, i-1)
		from.LongPrices[i-1] = price
		from.LongQty[i-1] = qty

		price, qty = tt.calculateOpenOrderPriceAndQtyByIndex(exchange.OrderSideSell, i-1)
		from.ShortPrices[i-1] = price
		from.ShortQty[i-1] = qty

		if positionQty != 0 {
			// 保存数量一致
			to.LongQty[i-1] = from.LongQty[i-1]
			to.ShortQty[i-1] = from.ShortQty[i-1]

			to.LongPrices[i-1] = from.LongPrices[i-1] + lastPriceDelta
			to.ShortPrices[i-1] = from.ShortPrices[i-1] + lastPriceDelta
		} else {
			to.LongPrices[i-1] = newTT.Long.BreakoutPrice.Value + float64(i-1)*0.5*newTT.Open.ATR.Value
			to.ShortPrices[i-1] = newTT.Short.BreakoutPrice.Value - float64(i-1)*0.5*newTT.Open.ATR.Value
		}
	}

	plan := &RotatePlan{
		From:             from,
		To:               to,
		PriceTickSize:    tt.getTickSizePlaces(),
		PriceDelta:       lastPriceDelta,
		ClosePositionPNL: pnl,
	}

	if !symbolYExist {
		// newTT 在此之前仅用于打印，需取消频道绑定；后面 addSymbol 才会真正加入
		newTT.removeChannel()
	}

	if printOnly {
		this.SendMsgf("```%s```", plan.Render())
		return
	}

	this.removeSymbol(symbolCodeX.Code)
	this.addSymbol(symbolCodeY, newTT.Config)

	newTT = this.Turtles[symbolCodeY.Code]
	newTTLastPrice, er = newTT.getLastPrice()
	if er != nil {
		return fmt.Errorf("rotate symbol get %s last price error: %s", symbolCodeY, er)
	}
	lastPriceDelta = newTTLastPrice - ttLastPrice

	// 如果有仓位，先开仓
	if positionQty != 0 {
		this.Infof("lastPriceDelta %v - %v = %v", newTTLastPrice, ttLastPrice, lastPriceDelta)

		openParam := &OpenParam{}
		copier.Copy(openParam, tt.Open)
		newTT.Open = openParam

		side := exchange.PositionSideLong
		if positionQty < 0 {
			side = exchange.PositionSideShort
		}
		if ok, err := newTT.rotateOpenPosition(side, math.Abs(positionQty)); !ok {
			this.ErrorMsgf("%s 开仓失败: %s", symbolCodeY, err)
			er = fmt.Errorf("rotate new symbol open position failed, error: %s", err)
			return
		}

		newTT.RotatePlan = plan
		newTT.Long.OpenPrice = tt.Long.OpenPrice
		newTT.Long.OpenSize = tt.Long.OpenSize
		newTT.Short.OpenPrice = tt.Short.OpenPrice
		newTT.Short.OpenSize = tt.Short.OpenSize

		// 加仓单
		for i := 0; i < newTT.Config.MaxOpenUnits; i++ {
			if i < int(positionUnits) {
				continue
			}

			if positionQty > 0 {
				price := plan.To.LongPrices[i]
				qty := plan.To.LongQty[i]
				order, err := newTT.createOpenOrder(exchange.OrderSideBuy, price, qty)
				if err != nil {
					this.AlertMsgf("%s创建订单(%v, %v, %v)失败: %s", symbolY, exchange.OrderSideBuy, price, qty, err)
					continue
				}
				newTT.Long.OpenOrders = append(newTT.Long.OpenOrders, SetOrderExt(order, OrderCategoryLong, i+1))
			} else {
				price := plan.To.ShortPrices[i]
				qty := plan.To.ShortQty[i]
				order, err := newTT.createOpenOrder(exchange.OrderSideSell, price, qty)
				if err != nil {
					this.AlertMsgf("%s创建订单(%v, %v, %v)失败: %s", symbolY, exchange.OrderSideSell, price, qty, err)
					continue
				}
				newTT.Short.OpenOrders = append(newTT.Short.OpenOrders, SetOrderExt(order, OrderCategoryShort, i+1))
			}
		}
	}

	// 开启运行
	newTT.setStatus(RUNNING)
	newTT.handlePositionAndOrders()
	newTT.sendStatus(false)
	this.storage.Save()

	this.SendMsgf("%s 展期至 %s 完成", symbolCodeX, symbolCodeY)
	return
}

type CloseAllControllerCommand ControllerCommand

func NewCloseAllControllerCommand(controller *TurtleController) *CloseAllControllerCommand {
	cmd := &CloseAllControllerCommand{
		Command: command.Command{
			Name:            "closeAll",
			Instruction:     "`.closeAll GoogleAuthCode` 强制平仓所有趋势机",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
		controller: controller,
	}
	return cmd
}

func (this *CloseAllControllerCommand) Do() bool {
	for _, tt := range this.controller.Turtles {
		tt.closePositionAndStop(FinishReasonClose)
	}
	return true
}

type SimulateControllerCommand struct {
	command.Command
	controller *TurtleController
	fromTime   *time.Time
	toTime     *time.Time
	symbolCode *exchange.SymbolCode
	config     *SymbolConfig
}

func NewSimulateControllerCommand(controller *TurtleController) *SimulateControllerCommand {
	cmd := &SimulateControllerCommand{
		Command: command.Command{
			Name:            "simu",
			Instruction:     "`.simu SymbolCode FromDate[~ToDate] [Cfg1=Value1,Cfg2=Value2]` 模拟运行趋势机, e.g. .simu BTC00.U 2022-01-01~2022-12-31 BreakoutPeriod=40,ExitPeriod=20",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          3,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *SimulateControllerCommand) Prepare() bool {
	var symbolCode *exchange.SymbolCode
	if s, err := this.controller.NewSymbolCode(this.Args[0]); err != nil {
		this.ErrorMsgf("合约代码 (%s) 格式错误：%s", this.Args[0], err)
		return false
	} else {
		symbolCode = s
	}
	symbol, err := this.controller.Exchange.TranslateSymbolCodeToFutureSymbol(symbolCode)
	if err != nil {
		this.ErrorMsgf("合约代码 (%s) 解析错误：%s", this.Args[0], err)
		return false
	}

	timeStr := this.Args[1]
	var fromTime *time.Time
	var toTime *time.Time
	fromStr := timeStr
	toStr := ""
	if strings.Contains(timeStr, "~") {
		timeStrs := strings.Split(timeStr, "~")
		fromStr = timeStrs[0]
		toStr = timeStrs[1]
	} else {
		n := time.Now()
		toTime = &n
	}
	fromTime = utils.ParseTimeBeijing(fromStr + " 00:00")
	if fromTime == nil {
		this.ErrorMsgf("请输入正确的开始时间, 格式如 2022-01-01")
		return false
	}
	if toStr != "" {
		toTime = utils.ParseTimeBeijing(toStr + " 00:00")
		if toTime == nil {
			this.ErrorMsgf("请输入正确的结束时间, 格式如 2022-12-31")
			return false
		}
	}

	config := &SymbolConfig{}
	copier.Copy(config, &this.controller.Config.SymbolConfig)
	if len(this.Args) == 3 {
		configStr := this.Args[2]
		if !strings.Contains(configStr, "=") {
			this.ErrorMsgf("请按 field=value 设置配置。")
			return false
		}
		if _, _, err := baseconfig.ParseConfigsFromString(&this.controller.Config.SymbolConfig, configStr); err != nil {
			this.ErrorMsgf("解析配置错误：%s", err)
			return false
		}
		if _, _, err := baseconfig.SetConfigWithString(config, configStr); err != nil {
			this.ErrorMsgf("设置配置错误：%s", err)
			return false
		}
	}
	config.Symbol = symbol
	if cfg, err := baseconfig.RenderTable(config); err != nil {
		this.ErrorMsgf("打印配置出错 %s", err)
		return false
	} else {
		this.SendMsgf("模拟运行参数:\n```%s```", cfg)

		this.fromTime = fromTime
		this.toTime = toTime
		this.symbolCode = symbolCode
		this.config = config
		return true
	}
}

func (this *SimulateControllerCommand) Do() bool {
	sim := &Simulator{
		controller: this.controller,
		fromTime:   this.fromTime,
		toTime:     this.toTime,
		symbolCode: this.symbolCode,
		config:     this.config,
		initCash:   10000,
	}
	if this.symbolCode.InstrumentType() == exchange.CoinMarginedFutures {
		sim.initCash = 10
	}
	csv, pine, err := sim.Simulate()
	if err != nil {
		this.ErrorMsgf("模拟运行出错: %s", err)
	}

	fileName := fmt.Sprintf("simu_%s_%s~%s", this.symbolCode, this.fromTime.Format("06-01-02"), this.toTime.Format("06-01-02"))
	this.SendFileMessage(fmt.Sprintf("%s.csv", fileName), csv, "")
	this.SendFileMessage(fmt.Sprintf("%s.pine", fileName), pine, "")
	sim.SendStatus(this.controller)
	return true
}

package utils

import (
	"strings"

	"github.com/AlecAivazis/survey/v2"
	"github.com/spf13/cast"
)

func SurveyYes(tips string) bool {
	result := false
	prompt := &survey.Confirm{
		Message: tips,
	}
	survey.AskOne(prompt, &result)
	return result
}

func SurveyPassword(message string) string {
	s := ""
	survey.AskOne(&survey.Password{
		Message: message,
	}, &s)
	return strings.TrimSpace(s)
}

func SurveyInput(message string) string {
	s := ""
	survey.AskOne(&survey.Input{
		Message: message,
	}, &s)
	return strings.TrimSpace(s)
}

func SurveyInt(message string) int {
	s := ""
	survey.AskOne(&survey.Input{
		Message: message,
	}, &s)
	s = strings.TrimSpace(s)
	return cast.ToInt(s)
}

func SurveyFloat(message string) float64 {
	s := ""
	survey.AskOne(&survey.Input{
		Message: message,
	}, &s)
	s = strings.TrimSpace(s)
	return cast.ToFloat64(s)
}

func SurveySelect(message string, choices []string) (int, string) {
	if len(choices) == 0 {
		return -1, ""
	}
	s := ""
	survey.AskOne(&survey.Select{
		Message:  message,
		Options:  choices,
		PageSize: 10,
	}, &s)
	for idx, choice := range choices {
		if strings.EqualFold(s, choice) {
			return idx, choice
		}
	}
	return 0, choices[0]
}

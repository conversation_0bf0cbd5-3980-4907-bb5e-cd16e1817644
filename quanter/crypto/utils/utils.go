package utils

import (
	"math/big"
	"strings"

	"github.com/stevedomin/termtable"
)

func NewTable() *termtable.Table {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	return t
}

func MulBitInt(v *big.Int, m float64) *big.Int {
	// Convert it to a big float
	bigFloat := new(big.Float).SetInt(v)

	// Multiply by m
	result := new(big.Float).Mul(bigFloat, big.NewFloat(m))

	// Convert the result back to a big int
	resultInt := new(big.Int)
	result.Int(resultInt)
	return resultInt
}

func Split(s, seperator string) []string {
	parts := strings.Split(s, seperator)
	for i, part := range parts {
		parts[i] = strings.TrimSpace(part)
	}
	return parts
}

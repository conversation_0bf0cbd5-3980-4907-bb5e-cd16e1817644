
import sys
import os
import time
import finnhub

if 'FINNHUB_API_KEY' not in os.environ:
    print('Please set $FINNHUB_API_KEY environment variable.')
    sys.exit(1)

api_key = os.environ['FINNHUB_API_KEY']
finnhub_client = finnhub.Client(api_key=api_key)

now_t = int(time.time())
start_t = now_t - 60*60*24*250
res = finnhub_client.stock_candles('TPR', 'D', start_t, now_t)

item_len = len(res['t'])

print('time,open,high,low,close,volume')
for i in range(item_len):
    print(f"{res['t'][i]},{res['o'][i]},{res['h'][i]},{res['l'][i]},{res['c'][i]},{res['v'][i]}")


package momentum

import (
	"fmt"
	"math"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestCalculateStockHoldings(t *testing.T) {
	Convey("TestCalculateStockHoldings", t, func() {
		stockList := []StockInfo{
			StockInfo{code: "AAA", atr: 12.34},
			StockInfo{code: "BBB", atr: 1.45},
			StockInfo{code: "CCC", atr: 56.12},
			StockInfo{code: "DDD", atr: 288.1},
			StockInfo{code: "EEE", atr: 5.17},
			StockInfo{code: "FFF", atr: 91.23},
		}

		assets := 100000.0
		factor := 0.001

		for _, s := range stockList {
			So(calculateStockHoldings(assets, factor, s.atr), ShouldEqual, int(math.Round(assets*factor/s.atr)))
		}
	})
}

func TestMomentumTradeSimulate(t *testing.T) {
	Convey("TestMomentumTradeSimulate", t, func() {
		momentumList := []StockInfo{
			StockInfo{code: "AAA", atr: 12.34, lastPrice: 605.1, orderPrice: 608},
			StockInfo{code: "BBB", atr: 1.45, lastPrice: 41.8, orderPrice: 43},
			StockInfo{code: "CCC", atr: 56.12, lastPrice: 2127.5, orderPrice: 2117},
			StockInfo{code: "DDD", atr: 288.1, lastPrice: 51271.2, orderPrice: 51271.2},
			StockInfo{code: "EEE", atr: 5.17, lastPrice: 88.15, orderPrice: 86.5},
			StockInfo{code: "FFF", atr: 91.23, lastPrice: 815.90, orderPrice: 810},
		}

		positions := []StockInfo{
			StockInfo{code: "BBB", holdings: 32, lastPrice: 41.8, orderPrice: 43},
			StockInfo{code: "FFF", holdings: 2, lastPrice: 815.90, orderPrice: 810},
			StockInfo{code: "CCC", holdings: 3, lastPrice: 2127.5, orderPrice: 2117},
			StockInfo{code: "GGG", holdings: 12, lastPrice: 105, orderPrice: 106},
			StockInfo{code: "ZZZ", holdings: 1, lastPrice: 345, orderPrice: 346.8},
		}

		initCash := 100000.0
		cash := initCash - 32*41.8 - 3*2127.5 - 2*815.90 - 12*105 - 1*345
		factor := 0.001

		orders := MomentumTradeSimulate(cash, factor, false, 0, positions, momentumList)
		fmt.Printf("\n orders: \n%#v", orders)
		So(len(orders), ShouldEqual, 9)
		So(orders[0].Rank, ShouldEqual, 0)
		So(orders[1].Rank, ShouldEqual, 0)
		So(orders[2].Rank, ShouldEqual, 2)
		So(orders[3].Rank, ShouldEqual, 3)
		So(orders[4].Rank, ShouldEqual, 6)
		So(orders[5].Rank, ShouldEqual, 1)
		So(orders[6].Rank, ShouldEqual, 5)
		So(orders[7].Ticker, ShouldEqual, "CASH")
		So(orders[8].Ticker, ShouldEqual, "SUMMARY")
		So(orders[8].CurrentQty, ShouldEqual, initCash)
		holdingsA := calculateStockHoldings(initCash, factor, 12.34)
		holdingsE := calculateStockHoldings(initCash, factor, 5.17)
		noBalanceCash := cash + 12*106 + 1*346.8 - float64(holdingsA)*608 - float64(holdingsE)*86.5
		So(orders[7].TargetQty, ShouldEqual, int(noBalanceCash))

		orders = MomentumTradeSimulate(cash, factor, true, 0, positions, momentumList)
		So(len(orders), ShouldEqual, 9)
		fmt.Printf("\n orders: \n%#v", orders)
		So(orders[0].Rank, ShouldEqual, 0)
		So(orders[1].Rank, ShouldEqual, 0)
		So(orders[2].Rank, ShouldEqual, 2)
		So(orders[2].TargetQty, ShouldEqual, 69)
		So(orders[3].Rank, ShouldEqual, 3)
		So(orders[3].TargetQty, ShouldEqual, 2)
		So(orders[4].Rank, ShouldEqual, 6)
		So(orders[4].TargetQty, ShouldEqual, 1)
		So(orders[5].Rank, ShouldEqual, 1)
		So(orders[6].Rank, ShouldEqual, 5)
		So(orders[7].Ticker, ShouldEqual, "CASH")
		So(orders[8].Ticker, ShouldEqual, "SUMMARY")
		leftCash := noBalanceCash - orders[2].OrderValue - orders[3].OrderValue - orders[4].OrderValue
		So(orders[7].TargetQty, ShouldEqual, int(leftCash))

		orders = MomentumTradeSimulate(cash, factor, true, 0.7, positions, momentumList)
		So(len(orders), ShouldEqual, 9)
		fmt.Printf("\n orders: \n%#v", orders)
		So(orders[0].Rank, ShouldEqual, 0)
		So(orders[1].Rank, ShouldEqual, 0)
		So(orders[2].Rank, ShouldEqual, 2)
		So(orders[2].TargetQty, ShouldEqual, 32)
		So(orders[3].Rank, ShouldEqual, 3)
		So(orders[3].TargetQty, ShouldEqual, 3)
		So(orders[4].Rank, ShouldEqual, 6)
		So(orders[4].TargetQty, ShouldEqual, 1)
		So(orders[5].Rank, ShouldEqual, 1)
		So(orders[6].Rank, ShouldEqual, 5)
		So(orders[7].Ticker, ShouldEqual, "CASH")
		So(orders[8].Ticker, ShouldEqual, "SUMMARY")
		leftCash = noBalanceCash - orders[2].OrderValue - orders[3].OrderValue - orders[4].OrderValue
		So(orders[7].TargetQty, ShouldEqual, int(leftCash))
	})
}

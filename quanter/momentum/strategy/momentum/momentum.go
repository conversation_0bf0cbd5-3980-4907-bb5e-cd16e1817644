package momentum

import (
	"fmt"
	"math"
	"sort"
)

type StockInfo struct {
	rank       int
	code       string
	atr        float64
	lastPrice  float64
	orderPrice float64 // 模拟时用第二天开盘价
	holdings   int
}

func NewStock(code string, atr float64, lastPrice float64, orderPrice float64, holdings int) StockInfo {
	if orderPrice == 0 {
		orderPrice = lastPrice
	}
	return StockInfo{
		code:       code,
		atr:        atr,
		lastPrice:  lastPrice,
		orderPrice: orderPrice,
		holdings:   holdings,
	}
}

func (this *StockInfo) SetLastPrice(price float64) {
	this.lastPrice = price
}

func (this *StockInfo) SetHoldings(holdings int) {
	this.holdings = holdings
}

type OrderInstruction struct {
	Rank         int // 0 表示平仓
	Ticker       string
	Name         string
	TargetWeight float64
	CurrentQty   int
	TargetQty    int
	LastPrice    float64
	OrderPrice   float64
	TargetValue  float64
	OrderQty     int
	OrderValue   float64
	Comment      string
}

const ORDER_COMMENT_CLOSE = "Close"
const ORDER_COMMENT_OPEN = "Open"
const ORDER_COMMENT_CHANGE = "Change"
const ORDER_COMMENT_CASH = "Cash"
const ORDER_COMMENT_SUMMARY = "Summary"

// 计算头寸数量
func calculateStockHoldings(assets float64, riskFactor float64, atr float64) int {
	holdings := assets * riskFactor / atr
	return int(math.Round(holdings))
}

func containsCode(codes []string, c string) bool {
	for _, _c := range codes {
		if _c == c {
			return true
		}
	}
	return false
}

// 根据当前持仓返回应进行的操作指令
func MomentumTradeSimulate(cash float64, riskFactor float64, needRebalance bool, riskFactorChangeThreshold float64, positions []StockInfo, momentumList []StockInfo) []OrderInstruction {
	initCash := cash
	assets := cash
	momentumMap := map[string]StockInfo{}
	for i, s := range momentumList {
		s.rank = i + 1
		momentumMap[s.code] = s
	}

	orders := []OrderInstruction{}

	positionCodes := []string{}
	needCloseCodes := []string{}
	for _, p := range positions {
		currentValue := p.lastPrice * float64(p.holdings)
		assets += currentValue

		_, ok := momentumMap[p.code]
		if ok {
			positionCodes = append(positionCodes, p.code)
		} else {
			needCloseCodes = append(needCloseCodes, p.code)

			orderValue := p.orderPrice * float64(p.holdings)
			orders = append(orders, OrderInstruction{
				Rank:         0,
				Ticker:       p.code,
				Name:         p.code,
				TargetWeight: 0,
				CurrentQty:   p.holdings,
				TargetQty:    0,
				LastPrice:    p.lastPrice,
				OrderPrice:   p.orderPrice,
				TargetValue:  0,
				OrderQty:     -p.holdings,
				OrderValue:   -orderValue,
				Comment:      ORDER_COMMENT_CLOSE,
			})

			cash += orderValue
		}
	}

	// fmt.Println("assets", assets)

	// 如果已持仓中有已不在动量列表中的股票，提示卖出
	// fmt.Println("\n\n本次需平仓的股票:")
	// fmt.Printf("%+v\n", needCloseCodes)

	// 如果需要再平衡，对已持仓股票做头寸再平衡
	// fmt.Println("\n\n头寸有变动需再平衡的股票:")
	for _, p := range positions {
		if containsCode(needCloseCodes, p.code) {
			continue
		}

		momentumStock := momentumMap[p.code]
		realRiskFactor := float64(p.holdings) * momentumStock.atr / assets
		riskDelta := (realRiskFactor - riskFactor) / riskFactor
		targetQty := calculateStockHoldings(assets, riskFactor, momentumStock.atr)

		if !needRebalance || math.Abs(riskDelta) < riskFactorChangeThreshold {
			targetQty = p.holdings
		}
		// fmt.Printf("%s 头寸变动: %v => %v, 风险因子差值: %.2f, 头寸差值: %v 股\n", p.code, p.holdings, targetQty, riskDelta, targetQty-p.holdings)

		targetValue := float64(targetQty) * momentumStock.lastPrice
		orderQty := targetQty - p.holdings
		orderValue := float64(orderQty) * momentumStock.orderPrice

		if cash < orderValue {
			// 如果是买入可能现金不够
			orderQty = int(math.Floor(cash / momentumStock.orderPrice))
			orderValue = float64(orderQty) * momentumStock.orderPrice
			targetQty = orderQty + p.holdings
			targetValue = float64(targetQty) * momentumStock.lastPrice
		}
		cash -= orderValue

		orders = append(orders, OrderInstruction{
			Rank:         momentumStock.rank,
			Ticker:       p.code,
			Name:         p.code,
			TargetWeight: targetValue / assets,
			CurrentQty:   p.holdings,
			TargetQty:    targetQty,
			LastPrice:    momentumStock.lastPrice,
			OrderPrice:   momentumStock.orderPrice,
			TargetValue:  targetValue,
			OrderQty:     orderQty,
			OrderValue:   orderValue,
			Comment:      ORDER_COMMENT_CHANGE,
		})

	}

	sort.SliceStable(orders, func(i, j int) bool { return orders[i].Rank < orders[j].Rank })

	// 其他动量列表中未持仓股票按顺序提示买入
	// fmt.Println("\n\n剩余资金按以下顺序买入:")
	for idx, stock := range momentumList {
		if containsCode(positionCodes, stock.code) {
			continue
		}
		targetQty := calculateStockHoldings(assets, riskFactor, stock.atr)
		// fmt.Printf("[%d] %s: %d 股\n", idx+1, stock.code, targetQty)
		orderValue := float64(targetQty) * stock.orderPrice

		if cash < orderValue {
			// 可能现金不够
			targetQty = int(math.Floor(cash / stock.orderPrice))
			orderValue = float64(targetQty) * stock.orderPrice
		}
		cash -= orderValue

		if orderValue == 0 {
			fmt.Printf("[%d] %s: 不足以购买 1 股，跳过\n", idx+1, stock.code)
			continue
		}

		targetValue := float64(targetQty) * stock.lastPrice
		orders = append(orders, OrderInstruction{
			Rank:         idx + 1,
			Ticker:       stock.code,
			Name:         stock.code,
			TargetWeight: targetValue / assets,
			CurrentQty:   0,
			TargetQty:    targetQty,
			LastPrice:    stock.lastPrice,
			OrderPrice:   stock.orderPrice,
			TargetValue:  targetValue,
			OrderQty:     targetQty,
			OrderValue:   orderValue,
			Comment:      ORDER_COMMENT_OPEN,
		})
	}

	orders = append(orders, OrderInstruction{
		Rank:         0,
		Ticker:       "CASH",
		Name:         "CASH",
		TargetWeight: cash / assets,
		CurrentQty:   int(math.Round(initCash)),
		TargetQty:    int(math.Round(cash)),
		TargetValue:  0,
		OrderQty:     0,
		OrderValue:   0,
		Comment:      ORDER_COMMENT_CASH,
	})

	orders = append(orders, OrderInstruction{
		Rank:         0,
		Ticker:       "SUMMARY",
		Name:         "SUMMARY",
		TargetWeight: 1,
		CurrentQty:   int(math.Round(assets)), // 执行前总资产
		TargetQty:    0,
		TargetValue:  0,
		OrderQty:     0,
		OrderValue:   0,
		Comment:      ORDER_COMMENT_SUMMARY,
	})

	return orders
}

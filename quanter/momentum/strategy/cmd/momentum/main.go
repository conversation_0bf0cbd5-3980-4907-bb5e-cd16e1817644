package main

import (
	"bytes"
	"encoding/csv"
	"errors"
	"flag"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"os"
	"strconv"
	"time"

	"momentum/strategy/exchange"
	"momentum/strategy/exchange/finnhub"
	"momentum/strategy/momentum"
	"momentum/strategy/rank"

	"github.com/spf13/cast"
	"github.com/stevedomin/termtable"
)

var token string
var rankFile string
var positionFile string
var riskFactor float64
var output string
var rebalance bool
var riskFactorChangeThreshold float64

func init() {
	flag.StringVar(&token, "token", "", "finnhub token")
	flag.StringVar(&rankFile, "ranks", "", "rank csv file")
	flag.StringVar(&output, "output", "", "order instructions output file")
	flag.StringVar(&positionFile, "positions", "positions.csv", "position csv file")
	flag.Float64Var(&riskFactor, "risk_factor", 0.001, "risk factor")
	flag.Float64Var(&riskFactorChangeThreshold, "risk_factor_change_threshold", 0.5, "rebalance only when risk factor percent changed no less then this value")
	flag.BoolVar(&rebalance, "rebalance", true, "rebalance")
	flag.Parse()

	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
}

func getLastCandle(marketClient exchange.Market, code string, endAtTime *time.Time) (*rank.Candle, error) {
	candles, err := rank.GetCandles(marketClient, code, endAtTime)
	if err != nil {
		log.Printf("%s getLastPriceByDate err: %s", code, err)
		return nil, err
	}

	if len(candles) == 0 {
		return nil, errors.New("no candles found")
	}

	return &candles[len(candles)-1], nil
}

func getLastPriceByDate(marketClient exchange.Market, code string, date string) float64 {
	var endAtTime *time.Time
	at := cast.ToTime(date)
	if !at.IsZero() {
		endAtTime = &at
	}
	candle, err := getLastCandle(marketClient, code, endAtTime)
	if err != nil {
		log.Printf("%s getLastPriceByDate err: %s", code, err)
		return 0
	}

	return candle.Close
}

func getNextOpenPriceByDate(marketClient exchange.Market, code string, date string) float64 {
	var endAtTime *time.Time
	at := cast.ToTime(date)
	endAtTime = &at
	initTime := at

	retries := 0
	for {
		_t := endAtTime.Add(time.Hour * 24)
		endAtTime = &_t
		candle, err := getLastCandle(marketClient, code, endAtTime)
		if err != nil {
			log.Printf("%s getLastCandle err: %s", code, err)
			return 0
		}

		// log.Printf("getNextOpenPriceByDate %s %s candle: %#v", code, initTime.Format("2006-01-02"), candle)
		// log.Printf("getNextOpenPriceByDate initTime %d", initTime.Unix())

		if candle.Time > initTime.Unix() {
			return candle.Open
		}

		retries += 1
		if retries > 14 {
			// 半个月没开盘？
			log.Printf("%s cannot found open price after %s", code, date)
			return 0
		}
	}
}

func main() {
	if token == "" {
		token = os.Getenv("FINNHUB_API_KEY")
		if token == "" {
			fmt.Println("need finnhub token, use -token flag or set FINNHUB_API_KEY environment variable")
			os.Exit(0)
		}
	}

	if output == "" {
		output = fmt.Sprintf("order_out_%s.csv", time.Now().Format("200601021504"))
	}

	sortCSV, err := os.Open(rankFile)
	if err != nil {
		fmt.Println("open sort file fail:", err)
		os.Exit(0)
	}
	defer sortCSV.Close()

	positionCSV, err := os.Open(positionFile)
	if err != nil {
		fmt.Println("open position file fail:", err)
		os.Exit(0)
	}
	defer positionCSV.Close()

	r := csv.NewReader(sortCSV)
	_, err = r.Read() // title line
	if err != nil {
		fmt.Println("read sort file err:", err)
		os.Exit(0)
	}

	marketClient := finnhub.NewFinnhubClient(token)

	momentumStocks := []momentum.StockInfo{}
	closePrices := map[string]float64{}
	date := ""
	for {
		line, err := r.Read() // title line
		if err != nil {
			if err == io.EOF {
				break
			}
			fmt.Println("read sort file err:", err)
			os.Exit(0)
		}

		date = line[0]
		code := line[2]
		atr, _ := strconv.ParseFloat(line[5], 64)
		lastPrice, _ := strconv.ParseFloat(line[6], 64)
		closePrices[code] = lastPrice

		orderPrice := getNextOpenPriceByDate(marketClient, code, date)
		stock := momentum.NewStock(code, atr, lastPrice, orderPrice, 0)
		momentumStocks = append(momentumStocks, stock)
	}
	// fmt.Print(momentumStocks)

	r = csv.NewReader(positionCSV)
	_, err = r.Read() // title line
	if err != nil {
		fmt.Println("read position file err:", err)
		os.Exit(0)
	}

	cashLine, err := r.Read()
	if cashLine[0] != "CASH" {
		fmt.Println("position file format error, cannot read cash")
		os.Exit(0)
	}
	cash, _ := strconv.ParseFloat(cashLine[1], 64)
	// fmt.Println("cash:", cash)

	positions := []momentum.StockInfo{}
	for {
		line, err := r.Read() // title line
		if err != nil {
			break
		}
		// fmt.Println("position:", line)
		code := line[0]
		holdings, _ := strconv.Atoi(line[1])
		lastPrice, ok := closePrices[code]
		if !ok {
			lastPrice = getLastPriceByDate(marketClient, code, date)
		}

		orderPrice := getNextOpenPriceByDate(marketClient, code, date)
		position := momentum.NewStock(code, 0, lastPrice, orderPrice, holdings)
		positions = append(positions, position)
	}

	fmt.Print("\n")

	orderInstructions := momentum.MomentumTradeSimulate(cash, riskFactor, rebalance, riskFactorChangeThreshold, positions, momentumStocks)

	buf := bytes.NewBuffer(nil)
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      1,
		UseSeparator: true,
	})
	w := csv.NewWriter(buf)
	headers := []string{"Date", "Rank", "Ticker", "Name", "Weight", "Last Price", "Cur. Qty", "Cur. Value", "Target Qty", "Target Value", "Order Price", "Order Qty", "Order Value", "Comment"}
	t.SetHeader(headers)
	_ = w.Write(headers)
	for _, order := range orderInstructions {
		currentValue := fmt.Sprintf("%.2f", float64(order.CurrentQty)*order.LastPrice)
		if order.Comment == momentum.ORDER_COMMENT_CASH || order.Comment == momentum.ORDER_COMMENT_SUMMARY {
			currentValue = fmt.Sprintf("%d", order.CurrentQty)
		}
		row := []string{
			date,
			fmt.Sprintf("%d", order.Rank),
			order.Ticker,
			order.Name,
			fmt.Sprintf("%0.4f%%", order.TargetWeight*100),
			fmt.Sprintf("%0.2f", order.LastPrice),
			fmt.Sprintf("%d", order.CurrentQty),
			currentValue,
			fmt.Sprintf("%d", order.TargetQty),
			fmt.Sprintf("%0.2f", order.TargetValue),
			fmt.Sprintf("%0.2f", order.OrderPrice),
			fmt.Sprintf("%d", order.OrderQty),
			fmt.Sprintf("%0.2f", order.OrderValue),
			order.Comment,
		}
		t.AddRow(row)
		_ = w.Write(row)
	}
	w.Flush()
	if err := ioutil.WriteFile(output, buf.Bytes(), 0644); err != nil {
		log.Fatal("write file error: " + err.Error())
	}

	fmt.Print("\nOrder Instructions\n\n")
	fmt.Println(t.Render())
}

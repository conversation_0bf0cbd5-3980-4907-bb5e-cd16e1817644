package main

import (
	"bytes"
	"encoding/csv"
	"flag"
	"fmt"
	"io/ioutil"
	"log"
	"momentum/strategy/exchange/finnhub"
	"momentum/strategy/rank"
	"os"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
	"github.com/stevedomin/termtable"
	"github.com/thoas/go-funk"
)

var token string
var output string
var filter bool
var atrPeriod int
var lookbackPeriod int
var maPeriod int
var gapThreshold float64
var codeFile string
var endAt string
var limit int
var excludes string

func init() {
	flag.StringVar(&token, "token", "", "finnhub token")
	flag.StringVar(&output, "output", "", "output file")
	// 可以直接code传入  -code A,AB,ABC
	// 也可以传入code文件，文件可以一行一个，也可以逗号分割
	flag.StringVar(&codeFile, "code", "", "code file")
	flag.StringVar(&endAt, "end_at", "", "k线截止时间，包含end_at")
	flag.BoolVar(&filter, "filter", true, "过滤均价和最大缺口")
	flag.IntVar(&atrPeriod, "atr_period", 20, "获取ATR的根数")
	flag.IntVar(&lookbackPeriod, "lookback_period", 90, "K线回溯根数")
	flag.IntVar(&maPeriod, "ma_period", 100, "均价过滤K线根数")
	flag.IntVar(&limit, "limit", 0, "限制输出数量")
	flag.Float64Var(&gapThreshold, "gap_threshold", 0.15, "最大缺口")
	flag.StringVar(&excludes, "excludes", "", "excludes code, e.g. -excludes=TSLA,BILI")
	flag.Parse()

	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
}

// 读取code，可以传入文件，或者直接命令行传入
func getCodes(filename string) (codes []string) {
	_, err := os.Stat(filename)
	r := strings.NewReplacer("\t", ",", "\n", ",")
	if err == nil {
		body, err := ioutil.ReadFile(filename)
		if err != nil {
			log.Fatal("read code file error: " + err.Error())
			return
		}
		filename = string(body)
	}
	codes = strings.Split(r.Replace(filename), ",")
	codes = funk.FilterString(codes, func(c string) bool { return c != "" })
	return
}

func containsCode(codes []string, c string) bool {
	for _, _c := range codes {
		if _c == c {
			return true
		}
	}
	return false
}

func main() {
	if token == "" {
		token = os.Getenv("FINNHUB_API_KEY")
		if token == "" {
			log.Println("need finnhub token, use -token flag or set FINNHUB_API_KEY environment variable")
			os.Exit(0)
		}
	}
	if output == "" {
		output = fmt.Sprintf("rank_out_%s.csv", time.Now().Format("200601021504"))
	}

	var endAtTime *time.Time
	if endAt != "" {
		at := cast.ToTime(endAt)
		if !at.IsZero() {
			endAtTime = &at
		}
	}
	if endAtTime != nil {
		// log.Printf("candles end at : %v", endAtTime.Format("2006-01-02"))
	}

	client := finnhub.NewFinnhubClient(token)

	var markets []string

	if codeFile != "" {
		codes := getCodes(codeFile)
		if len(codes) > 0 {
			markets = codes
			log.Printf("from code file [%s] read symbol\n", codeFile)
		}
	} else {
		var err error
		markets, err = rank.GetSP500ConstituentsCache(client)
		if err != nil {
			log.Fatal(err)
		}
		// sort.Sort(sort.StringSlice(markets))
		// markets = markets[:50]
	}

	// log.Printf("get market success: %d\n", len(markets))

	excludeCodes := strings.Split(excludes, ",")
	sortIndex := rank.NewSortIndex(atrPeriod, lookbackPeriod, maPeriod)
	for i := 0; i < len(markets); i++ {
		code := markets[i]
		if containsCode(excludeCodes, code) {
			// log.Println("ignore code", code)
			continue
		}
		candles, err := rank.GetCandles(client, code, endAtTime)
		if err != nil {
			log.Printf("get candles %s error: %v", code, err)
			continue
		}
		if len(candles) <= maPeriod+5 {
			log.Printf("get candles %s length [%v] not enough", code, len(candles))
			continue
		}
		var cs rank.Candles
		_ = copier.Copy(&cs, candles)
		sortIndex.SetCandles(code, cs)
		// log.Println("get candles", i, code, len(candles))
	}
	indexs := sortIndex.Index()
	indexs.Sort()

	if filter {
		indexs = indexs.FilterMA(gapThreshold)
	}
	if limit > 0 {
		indexs = indexs.Limit(limit)
	}

	date := time.Now()
	if endAtTime != nil {
		date = *endAtTime
	}

	buf := bytes.NewBuffer(nil)
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      3,
		UseSeparator: true,
	})
	w := csv.NewWriter(buf)
	headers := []string{"Date", "Rank", "Ticker", "Name", "Adjusted Slope", "ATR", "Last Price"}
	t.SetHeader(headers)
	_ = w.Write(headers)
	for i, idx := range indexs {
		row := []string{
			date.Format("2006-01-02"),
			cast.ToString(i + 1),
			idx.Code,
			idx.Code, // TODO
			fmt.Sprintf("%0.5f", idx.AdjustedSlope),
			fmt.Sprintf("%0.5f", idx.Atr),
			fmt.Sprintf("%0.2f", idx.LastPrice),
		}
		t.AddRow(row)
		_ = w.Write(row)
	}
	w.Flush()
	if err := ioutil.WriteFile(output, buf.Bytes(), 0644); err != nil {
		log.Fatal("write file error: " + err.Error())
	}
	// log.Printf("write file [%s] done!\n", output)

	fmt.Print("\n\nRank Results\n\n")
	fmt.Println(t.Render())
}

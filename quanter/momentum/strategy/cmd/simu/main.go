package main

import (
	"bufio"
	"bytes"
	"encoding/csv"
	"flag"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"os"
	"os/exec"
	"strconv"
	"time"

	"momentum/strategy/momentum"

	"github.com/spf13/cast"
)

var startDate string
var endDate string
var periodDays int
var limit int
var cash float64
var riskFactor float64
var riskFactorChangeThreshold float64
var excludes string
var autoRun bool
var filter bool
var maPeriod int
var gapThreshold float64

func init() {
	flag.StringVar(&startDate, "start_date", "", "simulation start date, e.g. 2020-01-01")
	flag.StringVar(&endDate, "end_date", "", "simulation end date, e.g. 2021-01-01")
	flag.IntVar(&periodDays, "period_days", 7, "how many days for a period")
	flag.IntVar(&limit, "limit", 30, "rank limit")
	flag.Float64Var(&riskFactor, "risk_factor", 0.001, "risk factor")
	flag.Float64Var(&riskFactorChangeThreshold, "risk_factor_change_threshold", 0.5, "rebalance only when risk factor percent changed no less then this value")
	flag.Float64Var(&cash, "cash", 100000, "init cash")
	flag.StringVar(&excludes, "excludes", "", "excludes code, e.g. -excludes=TSLA,BILI")
	flag.BoolVar(&autoRun, "auto_run", false, "auto run by period days")
	flag.BoolVar(&filter, "filter", true, "过滤均价和最大缺口")
	flag.IntVar(&maPeriod, "ma_period", 100, "均价过滤K线根数")
	flag.Float64Var(&gapThreshold, "gap_threshold", 0.15, "最大缺口")
	flag.Parse()

	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
}

func confirmToContinue(tips string) {
	reader := bufio.NewReader(os.Stdin)
	fmt.Printf("%s", tips)
	reader.ReadString('\n')
}

func copyAndCapture(w io.Writer, r io.Reader) ([]byte, error) {
	var out []byte
	buf := make([]byte, 1024, 1024)
	for {
		n, err := r.Read(buf[:])
		if n > 0 {
			d := buf[:n]
			out = append(out, d...)
			os.Stdout.Write(d)
		}
		if err != nil {
			// Read returns io.EOF at the end of file, which is not an error for us
			if err == io.EOF {
				err = nil
			}
			return out, err
		}
	}
	// never reached
	panic(true)
	return nil, nil
}

func writePositionSimuFile(cash float64, positions []momentum.OrderInstruction) {
	rows := [][]string{
		[]string{"code", "holdings"},
		[]string{"CASH", fmt.Sprintf("%.0f", cash)},
	}
	for _, p := range positions {
		rows = append(rows, []string{
			p.Ticker,
			fmt.Sprintf("%d", p.TargetQty),
		})
	}
	writeCSVFile("positions_simu.csv", rows)
}

func writeCSVFile(filePath string, rows [][]string) {
	buf := bytes.NewBuffer(nil)
	w := csv.NewWriter(buf)
	for _, r := range rows {
		_ = w.Write(r)
	}
	w.Flush()
	if err := ioutil.WriteFile(filePath, buf.Bytes(), 0644); err != nil {
		log.Fatal("write file error: " + err.Error())
	}
}

var rankSimuRows [][]string
var orderSimuRows [][]string
var orderDetailSimuRows [][]string

func appendToFinalRows() {
	rankSimCVS, _ := os.Open("rank_out_simu.csv")
	defer rankSimCVS.Close()
	r := csv.NewReader(rankSimCVS)
	rankRows, _ := r.ReadAll()

	rankSlopeMap := map[string]string{}
	rankATRMap := map[string]string{}
	for _, rank := range rankRows[1:] {
		rankSlopeMap[rank[2]] = rank[4]
		rankATRMap[rank[2]] = rank[5]
	}

	if len(rankSimuRows) > 0 {
		rankRows = rankRows[1:]
	}
	rankSimuRows = append(rankSimuRows, rankRows...)

	orderSimCVS, _ := os.Open("order_out_simu.csv")
	defer orderSimCVS.Close()
	r = csv.NewReader(orderSimCVS)
	orderRows, _ := r.ReadAll()
	if len(orderSimuRows) == 0 {
		titleRow := orderRows[0]
		orderSimuRows = append(orderSimuRows, titleRow)
		detailTitle := []string{}
		detailTitle = append(detailTitle, titleRow[0:len(titleRow)-1]...)
		detailTitle = append(detailTitle, "Adjusted Slope")
		detailTitle = append(detailTitle, "ATR")
		detailTitle = append(detailTitle, "Comment")
		orderDetailSimuRows = append(orderDetailSimuRows, detailTitle)
	}

	orderRows = orderRows[1:]
	for _, orderRow := range orderRows {
		orderSimuRows = append(orderSimuRows, orderRow)
		comment := orderRow[len(orderRow)-1]
		detailRow := []string{}
		detailRow = append(detailRow, orderRow[0:len(orderRow)-1]...)
		if comment != momentum.ORDER_COMMENT_CASH && comment != momentum.ORDER_COMMENT_SUMMARY {
			detailRow = append(detailRow, rankSlopeMap[orderRow[2]])
			detailRow = append(detailRow, rankATRMap[orderRow[2]])
		} else {
			detailRow = append(detailRow, "")
			detailRow = append(detailRow, "")
		}
		detailRow = append(detailRow, comment)
		orderDetailSimuRows = append(orderDetailSimuRows, detailRow)
	}
}

func main() {
	now := time.Now()

	var endTime time.Time
	at := cast.ToTime(endDate)
	if !at.IsZero() {
		endTime = at
	} else {
		endTime = now
	}

	var simuTime time.Time
	at = cast.ToTime(startDate)
	if !at.IsZero() {
		simuTime = at
	} else {
		simuTime = now
	}

	writePositionSimuFile(cash, []momentum.OrderInstruction{})

	isEvenPeriod := false
	for {
		fmt.Printf("\n模拟日期: %s, 双周期: %v\n\n", simuTime.Format("2006-01-02"), isEvenPeriod)
		commandArgs := []string{
			fmt.Sprintf("-end_at=%s", simuTime.Format("2006-01-02")),
			fmt.Sprintf("-limit=%d", limit),
			fmt.Sprintf("-filter=%v", filter),
			fmt.Sprintf("-ma_period=%d", maPeriod),
			fmt.Sprintf("-gap_threshold=%f", gapThreshold),
			"-output=rank_out_simu.csv",
		}
		if excludes != "" {
			commandArgs = append(commandArgs, fmt.Sprintf("-excludes=%s", excludes))
		}
		cmd := exec.Command("./build/apps/rank", commandArgs...)

		var errStdout, errStderr error
		stdoutIn, _ := cmd.StdoutPipe()
		stderrIn, _ := cmd.StderrPipe()
		cmd.Start()
		go func() {
			_, errStdout = copyAndCapture(os.Stdout, stdoutIn)
		}()
		go func() {
			_, errStderr = copyAndCapture(os.Stderr, stderrIn)
		}()
		err := cmd.Wait()
		if err != nil {
			log.Fatalf("command failed with %s\n", err)
		}
		if errStdout != nil || errStderr != nil {
			log.Println("failed to capture stdout or stderr\n")
		}

		cmd = exec.Command("./build/apps/momentum", fmt.Sprintf("-risk_factor=%f", riskFactor), "-ranks=rank_out_simu.csv", "-output=order_out_simu.csv", "-positions=positions_simu.csv", fmt.Sprintf("-rebalance=%v", isEvenPeriod), fmt.Sprintf("-risk_factor_change_threshold=%f", riskFactorChangeThreshold))
		stdoutIn, _ = cmd.StdoutPipe()
		stderrIn, _ = cmd.StderrPipe()
		cmd.Start()
		go func() {
			_, errStdout = copyAndCapture(os.Stdout, stdoutIn)
		}()
		go func() {
			_, errStderr = copyAndCapture(os.Stderr, stderrIn)
		}()
		err = cmd.Wait()
		if err != nil {
			log.Fatalf("command failed with %s\n", err)
		}
		if errStdout != nil || errStderr != nil {
			log.Println("failed to capture stdout or stderr\n")
		}

		orderSimCVS, _ := os.Open("order_out_simu.csv")
		r := csv.NewReader(orderSimCVS)
		r.Read() // title line

		instructions := []momentum.OrderInstruction{}
		for {
			line, err := r.Read() // title line
			if err != nil {
				break
			}
			// fmt.Println("position:", line)
			ticker := line[2]
			targetQty, _ := strconv.Atoi(line[8])
			comment := line[len(line)-1]
			if comment == momentum.ORDER_COMMENT_CASH {
				cash = float64(targetQty)
				continue
			}

			if comment == momentum.ORDER_COMMENT_SUMMARY {
				continue
			}

			if targetQty == 0 {
				continue
			}

			instructions = append(instructions, momentum.OrderInstruction{
				Ticker:    ticker,
				TargetQty: targetQty,
			})
		}

		orderSimCVS.Close()

		writePositionSimuFile(cash, instructions)
		appendToFinalRows()

		simuTime = simuTime.Add(time.Hour * 24 * time.Duration(periodDays))

		if simuTime.Before(endTime) {
			if autoRun {
				fmt.Println("\nauto run...")
				time.Sleep(time.Second)
			} else {
				confirmToContinue("\n回车继续\n")
			}
		} else {
			break
		}

		isEvenPeriod = !isEvenPeriod
	}

	if _, err := os.Stat("simu_result"); err != nil {
		_ = os.Mkdir("simu_result", 0755)
	}

	params := fmt.Sprintf("%s_%s_%v_%v_%v", startDate, endDate, riskFactor, limit, riskFactorChangeThreshold)
	if filter {
		params = fmt.Sprintf("%s_filter_%v_%v", params, maPeriod, gapThreshold)
	} else {
		params = fmt.Sprintf("%s_nofilter", params)
	}
	writeCSVFile(fmt.Sprintf("simu_result/rank_out_%s.csv", params), rankSimuRows)
	writeCSVFile(fmt.Sprintf("simu_result/order_out_%s.csv", params), orderSimuRows)
	writeCSVFile(fmt.Sprintf("simu_result/order_detail_out_%s.csv", params), orderDetailSimuRows)

	// 删除临时文件
	os.Remove("rank_out_simu.csv")
	os.Remove("positions_simu.csv")
	os.Remove("order_out_simu.csv")

	confirmToContinue("\n模拟完成\n")
}

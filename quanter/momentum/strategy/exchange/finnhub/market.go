package finnhub

import (
	"context"
	"log"
	"time"

	"momentum/strategy/exchange"

	finnhubgo "github.com/Finnhub-Stock-API/finnhub-go/v2"
)

const CANDLES_DAYS_MAX = 365 * 50

type FinnhubClient struct {
	client *finnhubgo.DefaultApiService
}

func NewFinnhubClient(token string) *FinnhubClient {
	fh := &FinnhubClient{}
	cfg := finnhubgo.NewConfiguration()
	cfg.AddDefaultHeader("X-Finnhub-Token", token)
	fh.client = finnhubgo.NewAPIClient(cfg).DefaultApi
	return fh
}

func (this *FinnhubClient) GetSP500Constituents() ([]string, error) {
	indicesConstData, _, err := this.client.IndicesConstituents(context.Background()).Symbol("^GSPC").Execute()
	if err != nil {
		return []string{}, err
	}
	return *indicesConstData.Constituents, err
}

func (this *FinnhubClient) GetCandles(code string, limit int) ([]exchange.Candle, error) {
	toTime := time.Now()
	days := int(float32(limit) * 1.5)
	if days > CANDLES_DAYS_MAX {
		days = CANDLES_DAYS_MAX
	}

	candles := []exchange.Candle{}
	for {
		fromTime := toTime.Add(-time.Hour * time.Duration(24*days))
		log.Printf("finnhub get StockCandles %s from %v to %v", code, fromTime.Format("2006-01-02"), toTime.Format("2006-01-02"))
		// stockCandles, _, err := this.client.StockCandles(this.auth, code, "D", fromTime.Unix(), toTime.Unix(), nil)
		stockCandles, _, err := this.client.StockCandles(context.Background()).Symbol(code).Resolution("D").From(fromTime.Unix()).To(toTime.Unix()).Execute()
		if err != nil {
			return []exchange.Candle{}, err
		}

		candlesTs := *stockCandles.T
		if len(candlesTs) < limit && days < CANDLES_DAYS_MAX {
			days = int(float32(days)*1.5) + 1
			// log.Printf("days %v", days)
			continue
		}

		candleNum := len(candlesTs)
		fromNum := candleNum - limit
		if candleNum < limit {
			fromNum = 0
		}
		for idx, t := range candlesTs[fromNum:] {
			candles = append(candles, exchange.Candle{
				Open:  float64((*stockCandles.O)[fromNum+idx]),
				High:  float64((*stockCandles.H)[fromNum+idx]),
				Low:   float64((*stockCandles.L)[fromNum+idx]),
				Close: float64((*stockCandles.C)[fromNum+idx]),
				Time:  t,
			})
		}
		break
	}
	// log.Printf("%s candles: %#v", code, candles)
	return candles, nil
}

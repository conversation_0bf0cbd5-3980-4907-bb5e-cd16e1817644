package finnhub

import (
	"testing"

	"momentum/strategy/exchange"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/spf13/viper"
)

var market exchange.Market

func init() {
	viper.SetConfigName("test")
	viper.AddConfigPath(".")
	viper.ReadInConfig()

	token := viper.GetString("finhubToken")
	if token == "" {
		panic("need token in test.toml")
	}
	market = NewFinnhubClient(token)
}

func TestGetSP500Constituents(t *testing.T) {
	Convey("TestGetSP500Constituents", t, func() {
		codes, _ := market.GetSP500Constituents()
		So(len(codes), ShouldBeGreaterThan, 400)
		So(codes, ShouldContain, "AAPL")
	})
}

func TestGetCandles(t *testing.T) {
	Convey("TestGetCandles", t, func() {
		candles, _ := market.GetCandles("AAPL", 123)
		So(len(candles), ShouldEqual, 123)

		candles, _ = market.GetCandles("AMCR", 99999)
		So(candles[0].Time, ShouldEqual, 1560211200)
		So(len(candles), ShouldBeGreaterThan, 100)
	})
}

package rank

import (
	"encoding/json"
	"fmt"
	"math"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"momentum/strategy/exchange"

	"github.com/jinzhu/copier"
)

// k线数据
type Candle struct {
	Open  float64
	High  float64
	Low   float64
	Close float64
	Time  int64
}

func NewCandle(time int64, open, high, low, close float64) Candle {
	return Candle{
		Open:  open,
		High:  high,
		Low:   low,
		Close: close,
		Time:  time,
	}
}

type Candles []Candle

func (cs *Candles) Sort() {
	sort.SliceStable(*cs, func(i, j int) bool { return (*cs)[i].Time < (*cs)[j].Time })
}

func (cs Candles) Unique() (candles Candles) {
	var exists = make(map[int64]bool)
	for _, c := range cs {
		if _, ok := exists[c.Time]; !ok {
			exists[c.Time] = true
			candles = append(candles, c)
		}
	}
	return
}

// 从文件解码
func (cs *Candles) Decode(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()
	return json.NewDecoder(file).Decode(cs)
}

// 编码到文件
func (cs *Candles) Encode(filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()
	return json.NewEncoder(file).Encode(cs)
}

// 大于均值
func (cs Candles) FilterMA(period int) bool {
	candles := cs[len(cs)-period-1 : len(cs)-1]
	var total float64
	for _, c := range candles {
		total += c.Close
	}
	avgPrice := total / float64(len(candles))
	return cs[len(cs)-1].Close > avgPrice
}

// 给定最高价、最低价和上个收盘价，返回 tr
func getTrueRange(high float64, low float64, prevClose float64) float64 {
	tr := math.Max(math.Abs(high-low), math.Abs(high-prevClose))
	tr = math.Max(tr, math.Abs(low-prevClose))
	return tr
}

// 计算ATR
// 不包括今天
func (cs Candles) ATR(period int) float64 {
	atr := 0.0
	sum := 0.0
	candleNum := len(cs)
	for i := 1; i < candleNum; i++ {
		candle := cs[i]
		tr := getTrueRange(candle.High, candle.Low, cs[i-1].Close)
		if i <= period {
			sum += tr
			if i == period {
				atr = sum / float64(period)
			}
		} else {
			atr = (atr*(float64(period)-1) + tr) / float64(period)
		}
	}
	return atr
}

func (cs Candles) XYData(period int) (xData, yData []float64) {
	candles := cs[len(cs)-period-1 : len(cs)-1]

	for idx, c := range candles {
		xData = append(xData, float64(idx+1))
		yData = append(yData, math.Log(c.Close))
	}
	return
}

// 斜率
func (cs Candles) Slope(period int) float64 {
	xData, yData := cs.XYData(period)
	reg := NewRegression(xData, yData)
	return reg.Slope()
}

// 年化收益率
func (cs Candles) AnnualizedSlope(period int) float64 {
	slope := cs.Slope(period)
	return math.Pow(math.Exp(slope), 250) - 1
}

// R2
func (cs Candles) RSquared(period int) float64 {
	xData, yData := cs.XYData(period)
	reg := NewRegression(xData, yData)
	return reg.RSquared()
}

// 调整后的年化收益率
func (cs Candles) AdjustedSlope(period int) float64 {
	return cs.AnnualizedSlope(period) * cs.RSquared(period)
}

// 最大缺口
func (cs Candles) MaxGap() (maxGap float64) {
	for i := 1; i < len(cs); i++ {
		open := cs[i].Open
		close := cs[i-1].Close
		gap := math.Abs(close-open) / close
		if gap > maxGap {
			maxGap = gap
		}
	}
	return
}

// 获取k线，并且本地缓存起来
func GetCandles(client exchange.Market, code string, endAt *time.Time) (candles Candles, err error) {
	cacheFile := filepath.Join(".cache", fmt.Sprintf("candles_%s.JSON", code))
	dirname := filepath.Dir(cacheFile)
	if _, err := os.Stat(dirname); err != nil {
		_ = os.Mkdir(dirname, 0755)
	}
	_ = candles.Decode(cacheFile)
	needRefresh := len(candles) <= 0 || (endAt != nil && endAt.Unix() > candles[len(candles)-1].Time)
	if needRefresh {
		var cs []exchange.Candle
		limit := 900
		for i := 0; i < 3; i++ {
			cs, err = client.GetCandles(code, limit)
			if err == nil {
				break
			}
			if strings.Contains(err.Error(), "enough") {
				limit /= 2
			}
		}
		_ = copier.Copy(&candles, &cs)
		err = candles.Encode(cacheFile)
		if err != nil {
			return nil, err
		}
	}
	candles.Sort()

	var newCandles Candles
	for _, c := range candles {
		if endAt == nil || c.Time <= endAt.Unix() {
			newCandles = append(newCandles, c)
		}
	}

	return newCandles, nil
}

type ConstituentsCache struct {
	Codes     []string
	ExpiredAt int64
}

func GetSP500ConstituentsCache(client exchange.Market) (codes []string, err error) {
	cacheFile := filepath.Join(".cache", "sp500constituents.JSON")
	dirname := filepath.Dir(cacheFile)
	if _, err := os.Stat(dirname); err != nil {
		_ = os.Mkdir(dirname, 0755)
	}

	now := time.Now()
	cache := &ConstituentsCache{}
	file, err := os.Open(cacheFile)
	if err == nil {
		defer file.Close()
		json.NewDecoder(file).Decode(cache)
		if len(cache.Codes) > 0 && (cache.ExpiredAt > now.Unix()) {
			return cache.Codes, nil
		}
	}

	markets, err := client.GetSP500Constituents()
	if err != nil {
		return codes, err
	}

	fileW, err := os.Create(cacheFile)
	if err == nil {
		defer fileW.Close()
		cache.Codes = markets
		cache.ExpiredAt = now.Add(time.Hour).Unix()
		json.NewEncoder(fileW).Encode(cache)
	}

	return markets, nil
}

package rank

type Regression struct {
	n, sx, sy, sxx, sxy, syy float64
}

func NewRegression(xData, yData []float64) *Regression {
	var r Regression
	r.UpdateArray(xData, yData)
	return &r
}

func (r *Regression) Size() int {
	return int(r.n)
}

func (r *Regression) Update(x, y float64) {
	r.n++
	r.sx += x
	r.sy += y
	r.sxx += x * x
	r.sxy += x * y
	r.syy += y * y
}

func (r *Regression) UpdateArray(xData, yData []float64) {
	if len(xData) != len(yData) {
		panic("array lengths differ in UpdateArray()")
	}
	for i := 0; i < len(xData); i++ {
		r.Update(xData[i], yData[i])
	}
}

func (r *Regression) Slope() float64 {
	ss_xy := r.n*r.sxy - r.sx*r.sy
	ss_xx := r.n*r.sxx - r.sx*r.sx
	return ss_xy / ss_xx
}

func (r *Regression) Intercept() float64 {
	return (r.sy - r.Slope()*r.sx) / r.n
}

func (r *Regression) RSquared() float64 {
	ss_xy := r.n*r.sxy - r.sx*r.sy
	ss_xx := r.n*r.sxx - r.sx*r.sx
	ss_yy := r.n*r.syy - r.sy*r.sy
	return ss_xy * ss_xy / ss_xx / ss_yy
}

package rank

import (
	"fmt"
	"sort"
	"sync"

	"github.com/thoas/go-funk"
)

// 排序结果
type IndexData struct {
	Code             string
	MAFilterPass     bool // 是否通过验证（比如价格大于100日均价)
	ExponentialSlope float64
	AnnualizedSlope  float64
	R2               float64
	AdjustedSlope    float64 // 最后使用次排名
	Gap              float64 // 缺口
	Atr              float64 // ATR
	LastPrice        float64 // 最后价格
}

type IndexDatas []IndexData

func (ss *IndexDatas) Sort() {
	sort.SliceStable(*ss, func(i, j int) bool {
		return (*ss)[i].AdjustedSlope > (*ss)[j].AdjustedSlope
	})
}

// 后面取值
func (ss IndexDatas) Limit(count int) IndexDatas {
	if len(ss) < count {
		count = len(ss)
	}
	return ss[:count]
}

// 过滤
func (ss IndexDatas) FilterMA(gapThreshold float64) IndexDatas {
	val := funk.Filter(ss, func(r IndexData) bool {
		if !r.MAFilterPass {
			fmt.Printf("%s 价格在均价以下，已过滤\n", r.Code)
			return false
		}
		if r.Gap >= gapThreshold {
			fmt.Printf("%s 最大价格缺口为 %.3f，已过滤\n", r.Code, r.Gap)
			return false
		}
		return true
	}).([]IndexData)
	return IndexDatas(val)
}

type SortIndex struct {
	rw             sync.RWMutex
	atrPeriod      int // ATR天数
	lookbackPeriod int // K线天数
	maPeriod       int // 高于均价天数
	data           map[string]Candles
}

func NewSortIndex(atrPeriod, lookbackPeriod, maPeriod int) *SortIndex {
	return &SortIndex{
		atrPeriod:      atrPeriod,
		lookbackPeriod: lookbackPeriod,
		maPeriod:       maPeriod,
		data:           make(map[string]Candles),
	}
}

func (si *SortIndex) SetCandles(code string, candles Candles) *SortIndex {
	si.rw.Lock()
	defer si.rw.Unlock()

	si.data[code] = candles
	return si
}

func (si *SortIndex) Index() (indexs IndexDatas) {
	si.rw.RLock()
	defer si.rw.RUnlock()

	for code, cs := range si.data {
		cs.Sort()
		var index IndexData
		index.Code = code
		index.MAFilterPass = cs.FilterMA(si.maPeriod)
		index.Atr = cs.ATR(si.atrPeriod)
		index.Gap = cs.MaxGap()
		index.R2 = cs.RSquared(si.lookbackPeriod)
		index.ExponentialSlope = cs.Slope(si.lookbackPeriod)
		index.AnnualizedSlope = cs.AnnualizedSlope(si.lookbackPeriod)
		index.AdjustedSlope = index.R2 * index.AnnualizedSlope
		index.LastPrice = cs[len(cs)-1].Close
		indexs = append(indexs, index)
	}
	return
}

动量策略说明

## 动量系统架构

在实现动量策略之前，我们最好对完整的动量系统有一个完整的认识。这样可以帮助我们对整个产品有清晰的认识。

完整的动量系统大概包含以下4个部分：
- 股票数据处理
- 排名策略
- 投资组合管理和再平衡
- 订单执行（自动执行再平衡）

__股票数据处理__

- 获取准确的 ETF 列表 （比如：美股标普 500 列表、港股某只 ETF 列表，暂时只考虑标普 500 即可）
- 考虑 K 线除权
- 考虑股息发放

总体目标就是：获取一个考虑过除权和股息的准确的股票列表。这部分根据需要可以用不同的编程语言开发问题都不大，其他部分最好统一用 golang 来写，可以复用一些基础代码和方便部署和保密。

这部分任务的主要挑战是：准确、稳定地提供数据。

__排名策略__

- 根据策略计算每只股票的分值
- 策略中包含一些过滤器（比如：价格高于 200 日均线、90天内无大于 15% 的跳开）

排名策略初看起来是最重要的，但它其实是几个部分中变动可能最大的部分。据我现在了解的情况是，有一些类似其他的系统，在策略部分会有比较大的不同。但是在其他三个部分则差异很小。这其实好理解：获取原始数据、再平衡资产组合和自动执行订单，这些方面其实差异较小，而策略本身其实可以差异较大。在做这部分工作的时候注意，可以单独把现有的动量策略提取出来（单独分个文件夹）。

这部分的主要挑战是：排名的准确性（即可测试性）。

__投资组合管理和再平衡__

- 资金管理（基于风险分配的资金管理）
- 再平衡计算，给出目标持仓数量
- 根据目标持仓数量和当前持仓计算出开平仓指示（打印但不具体执行） 
- 实现”再平衡计划“
- 记录资产和实际仓位
- 报告资产组合表现（总资金、收益率等）

这部分是可以非常独立和抽象的，也容易测试。已经不用考虑股票 K线的现实情况、券商 API 这些，就是一些 symbol 和数值。建议将基础的计算和“再平衡计划”分离开，即任何时候其实都可以计算得出开平仓指示，只不过在特定的时间计划任务会真正的执行。

这部分的主要挑战：测试用例。

__订单执行__

- 使用券商 API 执行订单指示
- 报告订单执行结果
- 检查仓位和再平衡系统的数据是否吻合

这部分得考虑很多现实的券商接口相关的问题，考虑订单执行的复杂性问题，考虑人为干预和其他意外情况导致的仓位变动等。

这部分的主要挑战：API 相关细节和错误处理。

__实现目标__

总的来说，完成前3个任务（不包含再平衡计划），其实就基本可以实现手工交易了。在特定的时间手工运行再平衡程序，得到股票交易的开平仓指示。实际去执行，然后手工录入到系统中即可。

暂时我们以上面这个半自动的系统为主要实现目标，后续根据情况再来处理自动订单执行的问题。

## 规则说明

### 市场趋势

当标普 500 指数低于 200 日均线，则认为市场处于熊市，空仓。


### 股票列表

- 获取标普 500 成分股列表
- 过滤掉在 100 日均线以下的股票
- 过滤掉过去 90 天存在 15% 以上价格缺口的股票
- 对 90 天的价格序列作回归，计算年化收益率并乘以判定系数，按数值从大到小排名
- 选取排名前 100 的股票

### 股票排名具体实现

1. 需要数据：天数(A)，日期(B)，价格(C)
2. 计算自然对数 LN(C) 作为指数回归的基础(D)
3. 通过 Slope(Di:Dj,Ai:Aj) 计算对数序列的回归斜率(E)
4. 通过 Power(Exp(E), 250) - 1 获得年化收益率(F)
5. 通过 Rsq(Di:Dj,Ai:Aj) 计算R2，拟合度(G)
6. 通过 F * G 获取调整后的年化收益率(H)
7. 按年化收益率(H)从大到小排序

![计算 Excel 示例](docs/images/Figure-6-1-Regression-Logic-in-Excel.png)

### 头寸规模

个股持仓数量 = 总资产 * 风险因子 / ATR

如总资产 100000，风险因子 0.001，某股票 ATR 3.26，则该股应持仓数量 = 100000 * 0.001 / 3.26 = 30.67 约 30 股。

### 头寸再平衡

对已持仓个股按最新资产和 ATR 计算实际风险因子，如果相对于初始风险因子变动超过一定比例（如 50%），则重新计算头寸，买入或卖出相差的数量。

例如总资产 100000，风险因子 0.001，某股票 ATR 3.26，初始持仓数量 = 100000 * 0.001 / 3.26 = 30.67 约 30 股。

一段时间后，假设分别出现以下情况：

- 总资产变为 110000，股票 ATR 变为 4.3，实际风险因子 = 30 * 4.3 / 110000 = 0.00117，相对于 0.001 变动为 17%，无需再平衡；

- 总资产变为 150000，股票 ATR 变为 2.3，实际风险因子 = 30 * 2.3 / 150000 = 0.00046，变动为 -54%，需再平衡，新头寸为 150000 * 0.001 / 2.3 = 65.2 约 65 股，则应该再买入 35 股；

- 总资产变为 90000，股票 ATR 变为 5，实际风险因子 = 30 * 5 / 90000 = 0.00167，变动为 67%，需再平衡，新头寸为 90000*0.001/5 = 18 股，则应该卖出 12 股；

### 策略执行时间

每周期选择一个固定时间交易日执行一次，如每周三。

## 完整策略

1. 获取最新股票列表
2. 如果当前持仓有不在列表中的股票，则卖出
3. 如果当前周期是双周期，对持仓股票做头寸再平衡
4. 依次买入列表中不在持仓中的股票，直到现金用完


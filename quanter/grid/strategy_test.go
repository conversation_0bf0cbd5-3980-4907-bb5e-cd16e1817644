package grid

import (
	"testing"

	"github.com/wizhodl/quanter/exchange"
)

func TestArithmeticStrategy(t *testing.T) {
	stg := NewArithmeticStrategy(10000, 10000, 20000, 10, 0, 0, false)
	prices, buyQtys, saleQtys := stg.GetPriceAndQty()
	if len(prices) != 11 || len(buyQtys) != 10 || len(saleQtys) != 10 {
		t.Fatalf("length of prices, buyQtys, saleQtys must be equal")
	}

	priceDelta := 1000.0
	for i := 0; i < 10; i++ {
		if !exchange.AlmostEqual(prices[i]+priceDelta, prices[i+1]) {
			t.Fatalf("prices delta error")
		}
	}

	qty := 0.0689655172
	for i := 0; i < 10; i++ {
		if !exchange.AlmostEqual(buyQtys[i], qty) {
			t.Fatalf("buyQtys error")
		}
		if !exchange.AlmostEqual(saleQtys[i], qty) {
			t.Fatalf("saleQtys error")
		}
	}

	stg = NewArithmeticStrategy(10000, 10000, 20000, 10, 0, 0, true)
	prices, buyQtys, saleQtys = stg.GetPriceAndQty()
	if len(prices) != 11 || len(buyQtys) != 10 || len(saleQtys) != 10 {
		t.Fatalf("length of prices, buyQtys, saleQtys must be equal")
	}

	qty = 0.064516129
	for i := 0; i < 10; i++ {
		if !exchange.AlmostEqual(buyQtys[i], qty) {
			t.Fatalf("buyQtys error")
		}
		if !exchange.AlmostEqual(saleQtys[i], qty) {
			t.Fatalf("saleQtys error")
		}
	}

	stg = NewArithmeticStrategy(10000, 30000, 44000, 5, 0, 0, false)
	prices, buyQtys, saleQtys = stg.GetPriceAndQty()
	if len(prices) != 6 || len(buyQtys) != 5 || len(saleQtys) != 5 {
		t.Fatalf("length of prices, buyQtys, saleQtys must be equal")
	}

	priceDelta = 2800.0
	for i := 0; i < 4; i++ {
		if !exchange.AlmostEqual(prices[i]+priceDelta, prices[i+1]) {
			t.Fatalf("prices delta error")
		}
	}

	qty = 0.056179775
	for i := 0; i < 5; i++ {
		if !exchange.AlmostEqual(buyQtys[i], qty) {
			t.Fatalf("buyQtys error")
		}
		if !exchange.AlmostEqual(saleQtys[i], qty) {
			t.Fatalf("saleQtys error")
		}
	}
}

func TestArithmeticKeepProfitStrategy(t *testing.T) {
	keepProfitRatio := 1.0
	stg := NewArithmeticKeepProfitStrategy(10000, 10000, 20000, 10, keepProfitRatio, 0, 0, false)
	prices, buyQtys, saleQtys := stg.GetPriceAndQty()
	if len(prices) != 11 || len(buyQtys) != 10 || len(saleQtys) != 10 {
		t.Fatalf("length of prices, buyQtys, saleQtys must be equal")
	}

	priceDelta := 1000.0
	for i := 0; i < 10; i++ {
		if !exchange.AlmostEqual(prices[i]+priceDelta, prices[i+1]) {
			t.Fatalf("prices delta error")
		}
	}

	qty := 0.0689655172
	for i := 0; i < 10; i++ {
		if !exchange.AlmostEqual(buyQtys[i], qty) {
			t.Fatalf("buyQtys error")
		}
		profit := qty * (prices[i+1] - prices[i])
		saleQty := qty - profit*keepProfitRatio/prices[i+1]
		if !exchange.AlmostEqual(saleQtys[i], saleQty) {
			t.Fatalf("saleQtys error")
		}
	}

	keepProfitRatio = 1.5
	stg = NewArithmeticKeepProfitStrategy(10000, 30000, 44000, 5, keepProfitRatio, 0, 0, false)
	prices, buyQtys, saleQtys = stg.GetPriceAndQty()
	if len(prices) != 6 || len(buyQtys) != 5 || len(saleQtys) != 5 {
		t.Fatalf("length of prices, buyQtys, saleQtys must be equal")
	}

	priceDelta = 2800.0
	for i := 0; i < 4; i++ {
		if !exchange.AlmostEqual(prices[i]+priceDelta, prices[i+1]) {
			t.Fatalf("prices delta error")
		}
	}

	qty = 0.056179775
	for i := 0; i < 5; i++ {
		if !exchange.AlmostEqual(buyQtys[i], qty) {
			t.Fatalf("buyQtys error")
		}
		profit := qty * (prices[i+1] - prices[i])
		saleQty := qty - profit*keepProfitRatio/prices[i+1]
		if !exchange.AlmostEqual(saleQtys[i], saleQty) {
			t.Fatalf("saleQtys error")
		}
	}

	keepProfitRatio = 1
	stg = NewArithmeticKeepProfitStrategy(10000, 30000, 44000, 5, keepProfitRatio, 0, 0, true)
	prices, buyQtys, saleQtys = stg.GetPriceAndQty()
	if len(prices) != 6 || len(buyQtys) != 5 || len(saleQtys) != 5 {
		t.Fatalf("length of prices, buyQtys, saleQtys must be equal")
	}

	qty = 0.052083333
	for i := 0; i < 5; i++ {
		if !exchange.AlmostEqual(saleQtys[i], qty) {
			t.Fatalf("saleQtys error")
		}
		profit := qty * (prices[i+1] - prices[i])
		buyQty := qty - profit*keepProfitRatio/prices[i]
		if !exchange.AlmostEqual(buyQtys[i], buyQty) {
			t.Fatalf("buyQtys error")
		}
	}
}

func TestGeometricStrategy(t *testing.T) {
	stg := NewGeometricStrategy(10000, 10000, 20000, 10, 0, 0, false)
	prices, buyQtys, saleQtys := stg.GetPriceAndQty()
	if len(prices) != 11 || len(buyQtys) != 10 || len(saleQtys) != 10 {
		t.Fatalf("length of prices, buyQtys, saleQtys must be equal")
	}

	pricesShouldBe := []float64{
		10000.0,
		10717.7,
		11486.9,
		12311.4,
		13195.0,
		14142.1,
		15157.1,
		16245.0,
		17411.0,
		18660.6,
		20000,
	}
	for i := 0; i < 11; i++ {
		if !exchange.AlmostEqual(exchange.RoundPrice(prices[i], 1), exchange.RoundPrice(pricesShouldBe[i], 1)) {
			t.Fatalf("prices error")
		}
	}

	qty := 0.0717734625
	for i := 0; i < 10; i++ {
		if !exchange.AlmostEqual(buyQtys[i], qty) {
			t.Fatalf("buyQtys error")
		}
		if !exchange.AlmostEqual(saleQtys[i], qty) {
			t.Fatalf("saleQtys error")
		}
	}

	stg = NewGeometricStrategy(10000, 10000, 20000, 10, 0, 0, true)
	prices, buyQtys, saleQtys = stg.GetPriceAndQty()
	if len(prices) != 11 || len(buyQtys) != 10 || len(saleQtys) != 10 {
		t.Fatalf("length of prices, buyQtys, saleQtys must be equal")
	}

	qty = 0.066967008
	for i := 0; i < 10; i++ {
		if !exchange.AlmostEqual(buyQtys[i], qty) {
			t.Fatalf("buyQtys error")
		}
		if !exchange.AlmostEqual(saleQtys[i], qty) {
			t.Fatalf("saleQtys error")
		}
	}
}

func TestGeometricKeepProfitStrategy(t *testing.T) {
	keepProfitRatio := 0.8
	stg := NewGeometricKeepProfitStrategy(10000, 10000, 20000, 10, keepProfitRatio, 0, 0, false)
	prices, buyQtys, saleQtys := stg.GetPriceAndQty()
	if len(prices) != 11 || len(buyQtys) != 10 || len(saleQtys) != 10 {
		t.Fatalf("length of prices, buyQtys, saleQtys must be equal")
	}

	pricesShouldBe := []float64{
		10000.0,
		10717.7,
		11486.9,
		12311.4,
		13195.0,
		14142.1,
		15157.1,
		16245.0,
		17411.0,
		18660.6,
		20000,
	}
	for i := 0; i < 11; i++ {
		if !exchange.AlmostEqual(exchange.RoundPrice(prices[i], 1), exchange.RoundPrice(pricesShouldBe[i], 1)) {
			t.Fatalf("prices error")
		}
	}

	qty := 0.0717734625
	for i := 0; i < 10; i++ {
		if !exchange.AlmostEqual(buyQtys[i], qty) {
			t.Fatalf("buyQtys error")
		}

		profit := qty * (prices[i+1] - prices[i])
		saleQty := qty - profit*keepProfitRatio/prices[i+1]
		if !exchange.AlmostEqual(saleQtys[i], saleQty) {
			t.Fatalf("saleQtys error")
		}
	}

	stg = NewGeometricKeepProfitStrategy(10000, 10000, 20000, 10, keepProfitRatio, 0, 0, true)
	prices, buyQtys, saleQtys = stg.GetPriceAndQty()

	qty = 0.066967008
	for i := 0; i < 10; i++ {
		if !exchange.AlmostEqual(saleQtys[i], qty) {
			t.Fatalf("saleQtys error")
		}
		profit := qty * (prices[i+1] - prices[i])
		buyQty := qty - profit*keepProfitRatio/prices[i]
		if !exchange.AlmostEqual(buyQtys[i], buyQty) {
			t.Fatalf("buyQtys error")
		}
	}
}

func TestPyramidStrategy(t *testing.T) {
	stg := NewPyramidStrategy(10000, 10000, 20000, 10, 0, 0, false)
	prices, buyQtys, saleQtys := stg.GetPriceAndQty()
	if len(prices) != 11 || len(buyQtys) != 10 || len(saleQtys) != 10 {
		t.Fatalf("length of prices, buyQtys, saleQtys must be equal")
	}

	priceDelta := 1000.0
	for i := 0; i < 10; i++ {
		if !exchange.AlmostEqual(prices[i]+priceDelta, prices[i+1]) {
			t.Fatalf("prices delta error")
		}
	}

	gridValue := 1000.0
	for i := 0; i < 10; i++ {
		qty := gridValue / prices[i]
		if !exchange.AlmostEqual(buyQtys[i], qty) {
			t.Fatalf("buyQtys error")
		}
		if !exchange.AlmostEqual(saleQtys[i], qty) {
			t.Fatalf("saleQtys error")
		}
	}

	stg = NewPyramidStrategy(10000, 30000, 44000, 5, 0, 0, false)
	prices, buyQtys, saleQtys = stg.GetPriceAndQty()
	if len(prices) != 6 || len(buyQtys) != 5 || len(saleQtys) != 5 {
		t.Fatalf("length of prices, buyQtys, saleQtys must be equal")
	}

	priceDelta = 2800.0
	for i := 0; i < 4; i++ {
		if !exchange.AlmostEqual(prices[i]+priceDelta, prices[i+1]) {
			t.Fatalf("prices delta error")
		}
	}

	gridValue = 2000.0
	for i := 0; i < 5; i++ {
		qty := gridValue / prices[i]
		if !exchange.AlmostEqual(buyQtys[i], qty) {
			t.Fatalf("buyQtys error")
		}
		if !exchange.AlmostEqual(saleQtys[i], qty) {
			t.Fatalf("saleQtys error")
		}
	}

	stg = NewPyramidStrategy(10000, 30000, 44000, 5, 0, 0, true)
	prices, buyQtys, saleQtys = stg.GetPriceAndQty()
	if len(prices) != 6 || len(buyQtys) != 5 || len(saleQtys) != 5 {
		t.Fatalf("length of prices, buyQtys, saleQtys must be equal")
	}

	gridValue = 2000.0
	for i := 0; i < 5; i++ {
		qty := gridValue / prices[i+1]
		if !exchange.AlmostEqual(buyQtys[i], qty) {
			t.Fatalf("buyQtys error")
		}
		if !exchange.AlmostEqual(saleQtys[i], qty) {
			t.Fatalf("saleQtys error")
		}
	}
}

func TestPyramidKeepProfitStrategy(t *testing.T) {
	keepProfitRatio := 1.0
	stg := NewPyramidKeepProfitStrategy(10000, 10000, 20000, 10, keepProfitRatio, 0, 0, false)
	prices, buyQtys, saleQtys := stg.GetPriceAndQty()
	gridValue := 1000.0
	for i := 0; i < 10; i++ {
		qty := gridValue / prices[i]
		if !exchange.AlmostEqual(buyQtys[i], qty) {
			t.Fatalf("buyQtys error")
		}
		profit := qty * (prices[i+1] - prices[i])
		saleQty := qty - profit*keepProfitRatio/prices[i+1]
		if !exchange.AlmostEqual(saleQtys[i], saleQty) {
			t.Fatalf("saleQtys error")
		}
	}

	stg = NewPyramidKeepProfitStrategy(10000, 30000, 44000, 5, keepProfitRatio, 0, 0, true)
	prices, buyQtys, saleQtys = stg.GetPriceAndQty()
	gridValue = 2000.0
	for i := 0; i < 5; i++ {
		qty := gridValue / prices[i+1]
		if !exchange.AlmostEqual(saleQtys[i], qty) {
			t.Fatalf("saleQtys error")
		}

		profit := qty * (prices[i+1] - prices[i])
		buyQty := qty - profit*keepProfitRatio/prices[i]
		if !exchange.AlmostEqual(buyQtys[i], buyQty) {
			t.Fatalf("buyQtys error")
		}
	}
}

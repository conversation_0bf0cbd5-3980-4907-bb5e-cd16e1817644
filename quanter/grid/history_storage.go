package grid

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/wizhodl/quanter/common/backscanner"
	"github.com/wizhodl/quanter/exchange"
)

var saveSnapshotMutex = exchange.NewSyncMapOf[string, sync.Mutex]()

func (this *GridController) GetSnapshotDir() string {
	path := filepath.Join(this.GetConfigPath(), "snapshots/grid")
	if _, err := os.Stat(path); os.IsNotExist(err) {
		os.MkdirAll(path, 0755)
	}
	return path
}

func (this *GridController) SaveSnapshot(snapshot *Snapshot) error {
	gridOrGroupID := snapshot.GridID
	if snapshot.GroupID != "" {
		gridOrGroupID = snapshot.GroupID
	}
	path := filepath.Join(this.GetSnapshotDir(), fmt.Sprintf("%s.snapshots", gridOrGroupID))

	mutex, _ := saveSnapshotMutex.LoadOrStore(path, sync.Mutex{})
	mutex.Lock()
	defer mutex.Unlock()

	// 如果上一个快照时间在当前快照时间前 1 秒内，则不保存
	// 更新 group 快照的时候，可能同一个快照在短时间内保存多次，而在上层比较难控制，因此在这里进行控制
	lastSnapshot := this.LastSnapshot(gridOrGroupID)
	if lastSnapshot != nil && lastSnapshot.CreateTime.After(snapshot.CreateTime.Add(-1*time.Second)) {
		return nil
	}

	// append snapshot json to file
	json, err := json.Marshal(snapshot)
	if err != nil {
		return err
	}
	// create file if not exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		os.Create(path)
	}
	f, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = f.WriteString(fmt.Sprintf("%s\n", string(json)))
	if err != nil {
		return err
	}
	return nil
}

// 查询快照
// snapshotID: 快照ID，为空则查询所有快照
// snapshotTime: 快照时间，为空则查询所有快照
// limit: 查询数量，为0则查询最近的 100 个快照
// 用 snapshotID 查询时，snapshotTime 不生效 和 limit 不生效
// 用 snapshotTime 查询时，limit 不生效，仅查询 snapshotTime 之前 1 分钟和之后 1 分钟的快照
// gridOrGroupRefID 不要用 alias，用 RefID
func (this *GridController) LookupSnapshots(gridOrGroupRefID string, snapshotID string, snapshotTime *time.Time, limit int) (snapshots []*Snapshot) {
	path := filepath.Join(this.GetSnapshotDir(), fmt.Sprintf("%s.snapshots", gridOrGroupRefID))

	count := 0
	if limit == 0 {
		limit = 1000
	}
	backscanner.BackScan(path, func(line []byte) bool {
		snapshot := &Snapshot{}
		err := json.Unmarshal(line, snapshot)
		if err == nil {
			if snapshotID == "" {
				// snapshot createTime within before or after 1 hour of time
				if snapshotTime != nil {
					timeStart := snapshotTime.Add(-1 * time.Hour)
					timeEnd := snapshotTime.Add(1 * time.Hour)
					if snapshot.CreateTime.After(timeStart) && snapshot.CreateTime.Before(timeEnd) {
						snapshots = append(snapshots, snapshot)
					}
					if snapshot.CreateTime.Before(timeStart) {
						return false
					}
				} else {
					snapshots = append(snapshots, snapshot)
					count++
					if count >= limit {
						return false
					}
				}
			} else if strings.EqualFold(snapshot.ID, snapshotID) {
				snapshots = append(snapshots, snapshot)
				return false
			}
		}
		return true
	})
	return
}

func (this *GridController) LastSnapshot(gridOrGroupRefID string) *Snapshot {
	snapshots := this.LookupSnapshots(gridOrGroupRefID, "", nil, 1)
	if len(snapshots) == 0 {
		return nil
	}
	return snapshots[0]
}

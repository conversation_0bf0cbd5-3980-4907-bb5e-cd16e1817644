package grid

import (
	"errors"
	"fmt"
	"math"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"

	"github.com/shopspring/decimal"
)

func NewGrid(controller *GridController, symbolCode *exchange.SymbolCode, symbol string, strategy Strateger, initAsset AssetPairQty, useStopLimit, reverse bool, copyGrid *Grid) (grid *Grid, er error) {
	if symbolCode == nil {
		er = errors.New("new grid failed, symbol code is nil")
		return
	}

	if reverse {
		useStopLimit = true // 做空合约默认用 StopLimit 订单
	}

	if controller.IsExchange(exchange.MetaTrader) && useStopLimit {
		controller.WarnMsgf("%s 暂不支持 StopLimit 订单，将忽略 UseStopLimit 设置", controller.Exchange.GetName())
		useStopLimit = false
	}

	if controller.IsExchange(exchange.Hyperliquid) && useStopLimit && symbolCode.InstrumentType() == exchange.Spot {
		// 现货不支持 StopLimit 单
		controller.WarnMsgf("%s 现货暂不支持 StopLimit 订单，将忽略 UseStopLimit 设置", controller.Exchange.GetName())
		useStopLimit = false
	}

	if controller.IsExchange(exchange.Bybit) && useStopLimit && symbolCode.InstrumentType() == exchange.Spot {
		// 现货不支持 StopLimit 单，都会直接成限价单
		controller.WarnMsgf("%s 现货暂不支持 StopLimit 订单，将忽略 UseStopLimit 设置", controller.Exchange.GetName())
		useStopLimit = false
	}

	if copyGrid == nil {
		if controller.IsExchange(exchange.Binance) && useStopLimit {
			return nil, errors.New("binance stop order limit num(10) too small")
		}

		instrumentType := symbolCode.InstrumentType()
		quoteAsset := controller.Exchange.GetSymbolCodeQuote(symbolCode)
		quoteQty := strategy.GetQuoteQty()

		initPrice := 0.0
		if p, err := controller.Exchange.GetLastPrice(symbolCode.InstrumentType(), symbol, false); err != nil {
			er = err
			return
		} else {
			initPrice = p
		}
		nowTime := time.Now()
		prices, buyQtys, sellQtys := strategy.GetPriceAndQty()
		grid = &Grid{
			controller:          controller,
			SymbolCode:          symbolCode,
			Symbol:              symbol,
			RefID:               exchange.NewRandomID(),
			BaseAsset:           symbolCode.Coin(),
			QuoteAsset:          quoteAsset,
			InitQty:             initAsset,
			InitPrice:           initPrice,
			QuoteAssetQty:       &AssetQty{Total: quoteQty},
			BaseAssetQty:        &AssetQty{Total: 0},
			Strategy:            strategy,
			StrategyName:        strategy.GetName(),
			Num:                 strategy.GetNum(),
			Range:               strategy.GetRange(),
			KeepProfitRatio:     strategy.GetKeepProfitRatio(),
			Prices:              prices,
			BuyQtys:             buyQtys,
			SellQtys:            sellQtys,
			UseStopLimit:        useStopLimit,
			Reverse:             reverse,
			TakeProfitPrice:     strategy.GetTakeProfitPrice(),
			StopLossPrice:       strategy.GetStopLossPrice(),
			LastPrice:           initPrice,
			LastPriceUpdateTime: &nowTime,
			Report:              &GridReport{},
			CreateTime:          &nowTime,
		}

		if !grid.isTrading() {
			return nil, fmt.Errorf("%s is not trading now", symbol)
		}

		for i := grid.Num - 1; i >= 0; i-- {
			grid.Prices[i] = grid.roundPrice(grid.Prices[i])

			if instrumentType.IsFuture() {
				grid.BuyQtys[i] = grid.assetQtyToFutureQty(grid.BuyQtys[i])
				grid.SellQtys[i] = grid.assetQtyToFutureQty(grid.SellQtys[i])
			} else {
				grid.BuyQtys[i] = grid.floorQty(grid.BuyQtys[i])
				grid.SellQtys[i] = grid.floorQty(grid.SellQtys[i])
			}
		}
		grid.Prices[grid.Num] = grid.roundPrice(grid.Prices[grid.Num])

		if err := grid.checkQtys(); err != nil {
			return nil, err
		}

		grid.tryAutoSetStopLoss()

		if err := grid.checkClosePrices(grid.StopLossPrice, grid.TakeProfitPrice); err != nil {
			return nil, err
		}

		grid.fixInitQuoteQty()

		initWorth := 0.0
		if w, err := grid.GetAssetWorthInUSDT(grid.InitQty); err != nil {
			er = err
			return
		} else {
			initWorth = w
		}
		grid.InitWorth = initWorth
	} else {
		grid = copyGrid
	}
	grid.controller = controller
	controller.handleMutex.LoadOrStore(symbol, &sync.Mutex{})

	grid.controller.Exchange.Subscribe(grid.InstrumentType(), grid.Symbol)
	return
}

func (this *Grid) Debugf(format string, args ...any) {
	symbolRefID := fmt.Sprintf("%s-%s", this.SymbolCode.Code, this.RefID)
	this.controller.Debugf("(%s) %s", symbolRefID, fmt.Sprintf(format, args...))
}

func (this *Grid) Infof(format string, args ...any) {
	symbolRefID := fmt.Sprintf("%s-%s", this.SymbolCode.Code, this.RefID)
	this.controller.Infof("(%s) %s", symbolRefID, fmt.Sprintf(format, args...))
}

func (this *Grid) Warnf(format string, args ...any) {
	symbolRefID := fmt.Sprintf("%s-%s", this.SymbolCode.Code, this.RefID)
	this.controller.Warnf("(%s) %s", symbolRefID, fmt.Sprintf(format, args...))
}

func (this *Grid) Errorf(format string, args ...any) {
	symbolRefID := fmt.Sprintf("%s-%s", this.SymbolCode.Code, this.RefID)
	this.controller.Errorf("(%s) %s", symbolRefID, fmt.Sprintf(format, args...))
}

func (this *Grid) getStopLimitOrderWrapper() exchange.Orderable {
	if this.controller.getOrderController() == nil {
		return nil
	}
	return this.controller.getOrderController().GetStopLimitOrderWrapper(this.controller.ID)
}

// 如果现价在网格中间，因为需要挂卖单的网格实际买入价会低于网格买入价，实际投入的 quoteQty 会少一些
// e.g. 价格 1000~2000 的 10 格网格，现价 1500，买入挂 1600~2000 的卖单，但按网格价本应 1500~1900 买入，所以实际需要的 quoteQty 会少
func (grid *Grid) fixInitQuoteQty() {
	if grid.InstrumentType().IsFuture() {
		return
	}

	if _, err := grid.UpdateLastPrice(); err != nil {
		grid.controller.ErrorMsgf("fixQuoteQty UpdateLastPrice failed: %s", err)
		return
	}

	deltaQuoteQty := 0.0
	for i := grid.Num - 1; i >= 0; i-- { // 价格从高到低优先成交
		price := grid.BuyPrices()[i]
		qty := grid.BuyQtys[i]
		if price >= grid.LastPrice {
			deltaQuoteQty = deltaQuoteQty + grid.Qty2Size(price, qty) - grid.Qty2Size(grid.LastPrice, qty)
		}
	}

	quoteQty := grid.InitQty.GetQuoteQty()
	if deltaQuoteQty > 0 {
		grid.Debugf("fixQuoteQty: deltaQuoteQty: %.2f", deltaQuoteQty)
		quoteQty = utils.DecimalAdd(quoteQty, -deltaQuoteQty)
		grid.InitQty.SetQuoteQty(quoteQty)
	}
}

func (grid *Grid) checkBalance() error {
	// 检查 quote 余额/保证金是否足够
	if _, availableQuoteQty, err := grid.controller.Exchange.GetBalance(grid.InstrumentType(), grid.QuoteAsset); err != nil {
		return fmt.Errorf("get %s balance failed %s", grid.QuoteAsset, err)
	} else {
		if grid.InstrumentType().IsFuture() {
			requiredMargin := grid.calculateEstimatedMargin()
			if availableQuoteQty < requiredMargin {
				return fmt.Errorf("%s balance not enough, available: %.2f, required margin: %.2f", grid.QuoteAsset, availableQuoteQty, requiredMargin)
			}
		} else {
			if availableQuoteQty < grid.InitQty.GetQuoteQty() {
				return fmt.Errorf("quote %s balance not enough, available: %.2f, required: %.2f", grid.QuoteAsset, availableQuoteQty, grid.InitQty.GetQuoteQty())
			}
		}
	}

	// 检查 base 账号余额
	coinOrSymbol := grid.Symbol
	if grid.InstrumentType() == exchange.Spot {
		coinOrSymbol = grid.SymbolCode.Coin()
	}
	if _, availableBaseQty, err := grid.controller.Exchange.GetHoldingQty(grid.InstrumentType(), coinOrSymbol); err != nil {
		return fmt.Errorf("get %s account qty failed %s", coinOrSymbol, err)
	} else {
		baseQty := grid.InitQty.GetBaseQty()
		if baseQty > 0 && availableBaseQty < baseQty {
			return fmt.Errorf("base %s balance not enough, available: %.2f", coinOrSymbol, availableBaseQty)
		}
	}

	return nil
}

// 检查买卖单数量是否符合交易所要求
func (grid *Grid) checkQtys() error {
	if instrument, err := grid.controller.Exchange.GetInstrument(grid.InstrumentType(), grid.Symbol); instrument != nil {
		for i := grid.Num - 1; i >= 0; i-- {
			if instrument.MinSize > grid.BuyQtys[i] {
				return fmt.Errorf("strategy order buy qty[%v] too small, min size: %v", grid.BuyQtys[i], instrument.MinSize)
			}
			if instrument.MinNotional > grid.Qty2Size(grid.Prices[i], grid.BuyQtys[i]) {
				return fmt.Errorf("strategy grid[%d] buy order vaule[%.2f] too small, min notional: %.2f", i, grid.Qty2Size(grid.Prices[i], grid.BuyQtys[i]), instrument.MinNotional)
			}

			if instrument.MinSize > grid.SellQtys[i] {
				return fmt.Errorf("strategy order sell qty[%v] too small, min size: %v", grid.SellQtys[i], instrument.MinSize)
			}
			if instrument.MinNotional > grid.Qty2Size(grid.Prices[i+1], grid.SellQtys[i]) {
				return fmt.Errorf("strategy grid[%d] sell order vaule[%.2f] too small, min notional: %.2f", i, grid.Qty2Size(grid.Prices[i+1], grid.SellQtys[i]), instrument.MinNotional)
			}
		}
	} else {
		return fmt.Errorf("get instrument err: %s", err)
	}
	return nil
}

// 是否支持止损止盈
func (grid *Grid) canAutoClose() bool {
	if grid.controller.IsExchange(exchange.MetaTrader) {
		// MetaTrader Limit 网格也可以支持
		return true
	}

	if grid.useOrderControllerToClose() {
		return true
	}

	if !grid.UseStopLimit {
		// 仅 StopLimit 支持止损止盈，因为不会锁定余额
		return false
	}
	if grid.controller.IsExchange(exchange.Binance) && grid.InstrumentType() == exchange.Spot {
		// bn 现货会锁定余额，也不支持
		return false
	}
	return true
}

// 使用 OrderController 的 StopLimit 订单来止损止盈
func (grid *Grid) useOrderControllerToClose() bool {
	useOrderController := false
	if grid.controller.IsExchange(exchange.Binance) || grid.controller.IsExchange(exchange.Hyperliquid) {
		useOrderController = true
	}

	if useOrderController && grid.controller.getOrderController() == nil {
		grid.controller.WarnMsgf("useOrderControllerToClose failed, orderController not found")
		return false
	}

	return useOrderController
}

func (grid *Grid) getAutoStopLoss() float64 {
	autoStopLossRatio := grid.controller.Config.AutoStopLossRatio
	if autoStopLossRatio == 0 {
		autoStopLossRatio = 0.1
	}
	if grid.Reverse {
		return grid.Prices[grid.Num] + (grid.Prices[grid.Num]-grid.Prices[grid.Num-1])*autoStopLossRatio
	} else {
		return grid.Prices[0] - (grid.Prices[1]-grid.Prices[0])*autoStopLossRatio
	}
}

func (grid *Grid) tryAutoSetStopLoss() error {
	if !grid.canAutoClose() {
		return fmt.Errorf("not supported")
	}

	if grid.StopLossPrice != 0 {
		return fmt.Errorf("StopLossPrice already set to %v", grid.StopLossPrice)
	}

	stopLossPrice := grid.getAutoStopLoss()
	if err := grid.checkClosePrices(stopLossPrice, 0); err == nil {
		grid.StopLossPrice = stopLossPrice
		grid.controller.SendMsgf("网格自动设置止损价: %s", grid.formatPrice(stopLossPrice))
		return nil
	} else {
		return err
	}
}

// 检查止损止盈价
func (grid *Grid) checkClosePrices(stopLossPrice, takeProfitPrice float64) error {
	if (stopLossPrice != 0 || takeProfitPrice != 0) && !grid.canAutoClose() {
		grid.controller.WarnMsgf("该网格不支持止损止盈功能，将忽略设置的止损止盈价")
	}

	if grid.Reverse {
		if stopLossPrice != 0 {
			if stopLossPrice <= grid.LastPrice {
				return fmt.Errorf("止损价需大于现价")
			}
			if stopLossPrice <= grid.Prices[0] {
				return fmt.Errorf("止损价需大于网格最低价")
			}
		}

		if takeProfitPrice != 0 {
			if takeProfitPrice >= grid.LastPrice {
				return fmt.Errorf("止盈价需小于现价")
			}
			if takeProfitPrice >= grid.Prices[grid.Num] {
				return fmt.Errorf("止盈价需小于网格最高价")
			}

			if stopLossPrice > 0 && stopLossPrice < takeProfitPrice {
				return fmt.Errorf("止盈价不能高于止损价")
			}
		}
	} else {
		if stopLossPrice != 0 {
			if stopLossPrice >= grid.LastPrice {
				return fmt.Errorf("止损价需小于现价")
			}
			if stopLossPrice >= grid.Prices[grid.Num] {
				return fmt.Errorf("止损价需小于网格最高价")
			}
		}

		if takeProfitPrice != 0 {
			if takeProfitPrice <= grid.LastPrice {
				return fmt.Errorf("止盈价需大于现价")
			}
			if takeProfitPrice <= grid.Prices[0] {
				return fmt.Errorf("止盈价需大于网格最低价")
			}

			if stopLossPrice > takeProfitPrice {
				return fmt.Errorf("止损价不能高于止盈价")
			}
		}
	}

	return nil
}

func (this *Grid) isTrading() bool {
	if utils.SliceContains([]string{exchange.MetaTrader, exchange.CTP, exchange.InteractiveBrokers}, this.controller.Exchange.GetName()) {
		this.controller.Exchange.CacheInstruments(true) // 每次都获取最新数据，确保交易状态是最新的
		instrument, err := this.controller.Exchange.GetInstrument(this.InstrumentType(), this.Symbol)
		if err != nil {
			this.Errorf("get instrument error: %v", err)
			return false
		}
		return instrument.Status == exchange.InstrumentStatusContinuous
	}
	return true
}

func (this *Grid) handleOrders() {
	/*
		正常流程
		- 本地挂单数据检查：应有 num 个挂单
		- API 获取挂单，与本地挂单对比
		- 实际挂单对比如果有缺少的订单，并查询到成交，按网格新增对应挂单，更新本地挂单数据

		异常情况：
		- 本地挂单数量异常
		- 本地挂单在实际挂单中没有，也查不到成交情况
		- 实际挂单出现本地没有的订单
		异常处理：发报警，可通过用指令确认重新挂单并覆盖本地挂单数据
	*/
	this.Infof("handle orders start...")

	if this.Status != Running {
		return
	}

	if !this.isTrading() {
		this.Infof("not tradable now")
		return
	}
	lock, ok := this.controller.handleMutex.Load(this.Symbol)
	if !ok {
		this.Errorf("grid controller handle mutex improperly initialized")
		return
	}
	lock.Lock()
	defer func() {
		this.Infof("handle orders end")
		lock.Unlock()
	}()

	remoteOpenOrders, err := this.controller.GetRemoteOpenOrdersByGrid(this, this.OrderType())
	if err != nil {
		this.controller.AlertMsgf("[%s] 获取当前挂单失败: %s", this.FullID(), err)
		return
	}

	sort.SliceStable(remoteOpenOrders, func(i, j int) bool { return remoteOpenOrders[i].Price < remoteOpenOrders[j].Price })
	for i, order := range remoteOpenOrders {
		this.Debugf("[open order %d] %#v", i, order)
	}

	// 本地挂单
	localOpenOrders := this.GetLocalOpenOrders()

	if len(localOpenOrders) != this.Num {
		if !this.fixMissingOrders(localOpenOrders, remoteOpenOrders) {
			this.controller.AlertMsgf("[%s] 本地挂单数量异常: 应有 %d 个，实际 %d 个。\n请使用 `.confirmOrders %s` 确认挂单", this.FullID(), this.Num, len(localOpenOrders), this.RefID)
			return
		}
	}

	sort.SliceStable(localOpenOrders, func(i, j int) bool {
		return localOpenOrders[i].GetInt(ExtKeyGridIndex) > localOpenOrders[j].GetInt(ExtKeyGridIndex)
	})
	tradedOrders := []*Order{}
	// 本地挂单是否有已成交的
	for _, localOrder := range localOpenOrders {
		found := false
		for _, openOrder := range remoteOpenOrders {
			if openOrder.OrderID == localOrder.OrderID {
				found = true

				if openOrder.ExecQty != localOrder.ExecQty {
					localOrder.ExecQty = openOrder.ExecQty // 部分成交的数量同步
				}

				if openOrder.IsTriggered() { // 已触发，更新本地状态
					localOrder.Status = openOrder.Status
					if openOrder.Type == exchange.StopLimit { // 记录委托单对应的限价单
						limitOrderID := openOrder.GetString(exchange.ExtKeyTriggeredLimitOrderID)
						if limitOrderID != "" {
							localOrder.SetString(exchange.ExtKeyTriggeredLimitOrderID, limitOrderID)
						}
					}
				} else if !this.isTheSamePrice(localOrder.Price, openOrder.Price) {
					this.controller.AlertMsgf(
						"[%s] 挂单 %s 在本地与远程的价格不一致(%s != %s)，请使用 `.confirmOrders %s` 确认挂单",
						this.FullID(),
						openOrder.OrderID,
						this.formatPrice(localOrder.Price),
						this.formatPrice(openOrder.Price),
						this.RefID,
					)
					return
				} else if !this.checkSameQty(localOrder.Qty, openOrder.Qty) {
					this.controller.AlertMsgf(
						"[%s] 挂单 %s 在本地与远程的数量不一致(%s != %s)，请使用 `.confirmOrders %s` 确认挂单",
						this.FullID(),
						openOrder.OrderID,
						this.formatQty(localOrder.Qty),
						this.formatQty(openOrder.Qty),
						this.RefID,
					)
					return
				}
				break
			}
		}
		if found {
			continue
		}

		// 本地挂单不存在，查询订单是否已成交
		queryOrder, err := this.controller.Exchange.GetOrderByOrig(*localOrder)
		if err != nil {
			this.controller.ErrorMsgf("[%s] 查询本地订单[%s]失败: %s", this.FullID(), localOrder.OrderID, err)
			return
		}

		if !queryOrder.IsFilled() {
			this.controller.ErrorMsgf("[%s] 本地订单[%s]异常，挂单无成交被取消\n请使用 `.confirmOrders %s` 确认挂单", this.FullID(), localOrder.OrderID, this.RefID)
			return
		}

		tradedOrders = append(tradedOrders, queryOrder)
		this.handleFilledOrder(queryOrder)
	}

	if len(tradedOrders) > 0 {
		this.sendTradeMsg(tradedOrders)
	}

	this.UpdateLastPrice()
	this.handleCloseOrders(remoteOpenOrders)
	this.updatePNL()

	if err := this.controller.storage.Save(); err != nil {
		this.controller.ErrorMsgf("storage 存储失败: %s", err)
	}
}

func (this *Grid) getCloseOrderExchange() exchange.Orderable {
	if this.useOrderControllerToClose() {
		ex := this.getStopLimitOrderWrapper()
		if ex == nil {
			this.controller.AlertMsgf("getStopLimitOrderWrapper failed")
		} else {
			return ex
		}
	}
	return this.controller.Exchange
}

func (this *Grid) handleCloseOrders(remoteOpenOrders []*Order) {
	if !this.canAutoClose() {
		return
	}

	stopLossPrice := this.StopLossPrice
	takeProfitPrice := this.TakeProfitPrice
	orderType := exchange.StopLimit

	ex := this.getCloseOrderExchange()

	if this.useOrderControllerToClose() {
		stopOrders, err := ex.GetOpenOrders(this.InstrumentType(), orderType, this.Symbol)
		if err != nil {
			this.controller.AlertMsgf("[%s] 获取 orderController 止损止盈挂单失败: %s", this.FullID(), err)
			return
		}
		remoteOpenOrders = stopOrders
	}

	if this.controller.IsExchange(exchange.MetaTrader) {
		// 因为 MT 网格用的限价单，止损止盈需单独查 StopLimit 订单
		stopOrders, err := this.controller.GetRemoteOpenOrdersByGrid(this, exchange.StopLimit)
		if err != nil {
			this.controller.AlertMsgf("[%s] MetaTrader 获取挂单失败: %s", this.FullID(), err)
			return
		}
		remoteOpenOrders = stopOrders
	}

	// 本地是否存在止损止盈挂单
	var stopLossOrder *Order
	var takeProfitOrder *Order
	for _, order := range this.CloseOrders {
		if !order.IsOpen() {
			continue
		}
		if order.GetString(ExtKeyCategory) == string(OrderCategoryStopLoss) {
			if stopLossOrder != nil {
				this.controller.ErrorMsgf("[%s] 出现多个止损挂单: %s, %s", this.FullID(), stopLossOrder.OrderID, order.OrderID)
				return
			}
			stopLossOrder = order
		} else if order.GetString(ExtKeyCategory) == string(OrderCategoryTakeProfit) {
			if takeProfitOrder != nil {
				this.controller.ErrorMsgf("[%s] 出现多个止盈挂单: %s, %s", this.FullID(), takeProfitOrder.OrderID, order.OrderID)
				return
			}
			takeProfitOrder = order
		}
	}

	// 本地止损止盈挂单是否和远程挂单一致
	// 远程无该挂单：已成交，则关闭网格；已取消，则重新挂单
	if stopLossOrder != nil {
		found := false
		for _, remoteOrder := range remoteOpenOrders {
			if remoteOrder.OrderID == stopLossOrder.OrderID {
				found = true
				// remoteOrder 可能是 exchangeOrder 也可能是 orderctrl.ConditionalOrder
				// 当 ConditionalOrder 在 orderctrl 系统中被触发后，grid 系统是不知道的，在这里更新本地订单的 RefID，用于后续核对本地订单和远程订单是否一致
				if remoteOrder.TriggeredOrderID != "" && stopLossOrder.TriggeredOrderID == "" {
					stopLossOrder.TriggeredOrderID = remoteOrder.TriggeredOrderID
				}
				break
			}
		}
		if !found {
			queryOrder, err := ex.GetOrderByOrig(*stopLossOrder)
			if err != nil {
				this.controller.ErrorMsgf("[%s] 查询本地止损订单[%s]失败: %s", this.FullID(), stopLossOrder.OrderID, err)
				return
			}
			if queryOrder.IsFilled() {
				this.onCloseOrderFilled(queryOrder)
				return
			} else if queryOrder.IsCanceled() {
				stopLossOrder.Status = exchange.OrderStatusCancelled
				stopLossOrder = nil
			}
		}
	}

	if takeProfitOrder != nil {
		found := false
		for _, remoteOrder := range remoteOpenOrders {
			if remoteOrder.OrderID == takeProfitOrder.OrderID {
				found = true
				// remoteOrder 可能是 exchangeOrder 也可能是 orderctrl.ConditionalOrder
				// 当 ConditionalOrder 在 orderctrl 系统中被触发后，grid 系统是不知道的，在这里更新本地订单的 TriggeredOrderID，用于后续核对本地订单和远程订单是否一致
				if remoteOrder.TriggeredOrderID != "" && takeProfitOrder.TriggeredOrderID == "" {
					takeProfitOrder.TriggeredOrderID = remoteOrder.TriggeredOrderID
				}
				break
			}
		}
		if !found {
			queryOrder, err := ex.GetOrderByOrig(*takeProfitOrder)
			if err != nil {
				this.controller.ErrorMsgf("[%s] 查询本地止盈订单[%s]失败: %s", this.FullID(), takeProfitOrder.OrderID, err)
				return
			}
			if queryOrder.IsFilled() {
				this.onCloseOrderFilled(queryOrder)
				return
			} else if queryOrder.IsCanceled() {
				takeProfitOrder.Status = exchange.OrderStatusCancelled
				takeProfitOrder = nil
			}
		}
	}

	closeQty := this.floorQty(math.Abs(this.BaseAssetQty.Total))

	if stopLossPrice == 0 && stopLossOrder != nil {
		// 取消止损单
		if err := ex.CancelOrder(this.InstrumentType(), orderType, this.Symbol, stopLossOrder.OrderID); err != nil {
			this.controller.ErrorMsgf("取消止损订单[%s] 失败: %s", stopLossOrder.OrderID, err)
		} else {
			stopLossOrder.Status = exchange.OrderStatusCancelled
		}
	} else if stopLossPrice > 0 && stopLossOrder == nil {
		// 新增止损单
		if _, err := this.createCloseOrder(stopLossPrice, orderType, OrderCategoryStopLoss); err != nil {
			this.controller.ErrorMsgf("[%s] 创建止损单失败: %s", this.FullID(), err)
		}
	} else if stopLossPrice > 0 {
		// 检查当前止损挂单价格、数量是否需要修改
		orderPrice := stopLossOrder.TriggerPrice
		if orderPrice == 0 {
			orderPrice = stopLossOrder.Price
		}
		if !this.checkSameQty(closeQty, stopLossOrder.Qty) ||
			!this.isTheSamePrice(stopLossPrice, orderPrice) {
			if _, err := this.updateCloseOrder(stopLossOrder, stopLossPrice, closeQty); err != nil {
				this.controller.ErrorMsgf("[%s] 修改止损单失败: %s", this.FullID(), err)
			}
		}
	}

	if takeProfitPrice == 0 && takeProfitOrder != nil {
		// 取消止盈单
		if err := ex.CancelOrder(this.InstrumentType(), orderType, this.Symbol, takeProfitOrder.OrderID); err != nil {
			this.controller.ErrorMsgf("取消止盈订单[%s] 失败: %s", takeProfitOrder.OrderID, err)
		} else {
			takeProfitOrder.Status = exchange.OrderStatusCancelled
		}
	} else if takeProfitPrice > 0 && takeProfitOrder == nil {
		// 新增止盈单
		if _, err := this.createCloseOrder(takeProfitPrice, orderType, OrderCategoryTakeProfit); err != nil {
			this.controller.ErrorMsgf("[%s] 创建止盈单失败: %s", this.FullID(), err)
		}
	} else if takeProfitPrice > 0 {
		// 检查当前止盈挂单价格、数量是否需要修改
		orderPrice := takeProfitOrder.TriggerPrice
		if orderPrice == 0 {
			orderPrice = takeProfitOrder.Price
		}
		if !this.checkSameQty(closeQty, takeProfitOrder.Qty) ||
			!this.isTheSamePrice(takeProfitPrice, orderPrice) {
			if _, err := this.updateCloseOrder(takeProfitOrder, takeProfitPrice, closeQty); err != nil {
				this.controller.ErrorMsgf("[%s] 修改止盈单失败: %s", this.FullID(), err)
			}
		}
	}
}

func (this *Grid) onCloseOrderFilled(filledOrder *Order) {
	this.sendTradeMsg([]*Order{filledOrder})

	ex := this.getCloseOrderExchange()

	for i, order := range this.CloseOrders {
		if order.OrderID == filledOrder.OrderID {
			this.CloseOrders[i] = filledOrder
		} else if order.IsOpen() {
			if err := ex.CancelOrder(this.InstrumentType(), this.OrderType(), this.Symbol, order.OrderID); err != nil {
				this.controller.ErrorMsgf("取消订单[%s] 失败: %s", order.OrderID, err)
			} else {
				order.Status = exchange.OrderStatusCancelled
			}
		}
	}

	if this.Reverse {
		this.QuoteAssetQty.Total -= this.Qty2Size(filledOrder.ExecPrice, filledOrder.ExecQty)
		this.BaseAssetQty.Total = utils.DecimalAdd(this.BaseAssetQty.Total, filledOrder.ExecQty)
	} else {
		fee := 0.0
		if filledOrder.FeeAsset == this.QuoteAsset {
			fee = math.Abs(filledOrder.Fee)
		}
		this.QuoteAssetQty.Total += (this.Qty2Size(filledOrder.ExecPrice, filledOrder.ExecQty) - fee)
		this.BaseAssetQty.Total = utils.DecimalAdd(this.BaseAssetQty.Total, -filledOrder.ExecQty)
	}

	localOpenOrders := this.GetLocalOpenOrders()
	for _, order := range localOpenOrders {
		if err := this.controller.Exchange.CancelOrder(this.InstrumentType(), this.OrderType(), this.Symbol, order.OrderID); err != nil {
			this.controller.ErrorMsgf("取消订单[%s] 失败: %s", order.OrderID, err)
		} else {
			order.Status = exchange.OrderStatusCancelled
		}
	}

	if filledOrder.GetString(ExtKeyCategory) == string(OrderCategoryStopLoss) {
		if this.CoolMinutes > 0 {
			this.StartCooling()
			this.controller.SendMsgf("网格 %s 已止损，将暂停冷却 %.3f 天。", this.GetDisplayID(true), float64(this.CoolMinutes)/60/24)
		} else {
			this.Finish(FinishReasonStopLoss)
			this.controller.SendMsgf("网格 %s 已止损停止。", this.GetDisplayID(true))
		}
	} else {
		this.Finish(FinishReasonTakeProfit)
		this.controller.SendMsgf("网格 %s 已止盈停止。", this.GetDisplayID(true))
	}

	if this.Status == Closed {
		// 如果是 ihg 网格，同时关闭另一个
		_, oppositeGrid := this.isInverseHedgedGroupGrid()
		if oppositeGrid != nil && oppositeGrid.Status != Closed {
			this.controller.SendMsgf("IHG 同组网格 %s 将同时自动关闭", oppositeGrid.GetDisplayID(true))
			oppositeGrid.Close(FinishReasonAuto, true, true)
		}
	}

	this.updateSnapshot()
}

func (this *Grid) isInverseHedgedGroupGrid() (_ bool, oppositeGrid *Grid) {
	for _, group := range this.controller.GridGroups {
		if group.Type == GroupTypeInverseHedged && utils.SliceContains(group.GridIDs, this.RefID) {
			for _, gridID := range group.GridIDs {
				oppositeGrid = this.controller.GetGridByID(gridID, true)
				return true, oppositeGrid
			}
		}
	}
	return false, nil
}

func (this *Grid) fixMissingOrders(localOpenOrders, remoteOpenOrders []*Order) (success bool) {
	// 如果本地和远程挂单数量都为 N([1,num-1]) 且一致，则自动补挂缺少的订单
	if len(localOpenOrders) == 0 || len(localOpenOrders) >= this.Num || len(localOpenOrders) != len(remoteOpenOrders) {
		return false
	}

	for _, localOrder := range localOpenOrders {
		found := false
		for _, remoteOrder := range remoteOpenOrders {
			if remoteOrder.OrderID == localOrder.OrderID {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	holdingCount := this.HoldingCount()
	sellOpenOrderCount := holdingCount
	if this.Reverse {
		sellOpenOrderCount = this.Num - holdingCount
	}
	for i := this.Num - 1; i >= 0; i-- {
		found := false
		for _, order := range localOpenOrders {
			if order.GetInt(ExtKeyGridIndex) == i {
				found = true
				break
			}
		}

		if !found {
			category := OrderCategoryBuy
			price := this.Prices[i]
			qty := this.BuyQtys[i]
			refOrderID := ""
			if i >= (this.Num - sellOpenOrderCount) {
				category = OrderCategorySell
				price = this.Prices[i+1]
			}

			if this.Reverse && category == OrderCategoryBuy {
				refOrderID = this.FindMissingRefOrderID(i)
			} else if !this.Reverse && category == OrderCategorySell {
				refOrderID = this.FindMissingRefOrderID(i)
				qty = this.SellQtys[i] * (1 - this.getBaseFeeRate(exchange.OrderSideBuy, this.OrderType())) // 考虑手续费
			}

			orderPrint := fmt.Sprintf("Index: %d, %s %s@%s", i, string(category), this.formatQty(qty), this.formatPrice(price))
			if _, err := this.createOrder(price, qty, this.OrderType(), category, i, refOrderID); err != nil {
				this.controller.ErrorMsgf("[%s] 创建缺失的网格订单[%s]失败: %s", this.FullID(), orderPrint, err)
			} else {
				this.controller.SendMsgf("[%s] 创建缺失的网格订单[%s]成功", this.FullID(), orderPrint)
			}
		}
	}

	return true
}

// 已成交的 index 买单中没有卖单的订单 ID
func (this *Grid) FindMissingRefOrderID(index int) string {
	existRefIDs := []string{}
	existOrders := this.SellOrders
	queryOrders := this.BuyOrders
	if this.Reverse {
		existOrders = this.BuyOrders
		queryOrders = this.SellOrders
	}
	for _, order := range existOrders {
		if order.GetInt(ExtKeyGridIndex) == index && order.GetString(ExtKeyRefOrderID) != "" && order.Status != exchange.OrderStatusCancelled {
			existRefIDs = append(existRefIDs, order.GetString(ExtKeyRefOrderID))
		}
	}

	foundIDs := []string{}
	for _, order := range queryOrders {
		if order.GetInt(ExtKeyGridIndex) != index {
			continue
		}
		if order.IsOpen() {
			continue
		}
		exist := utils.SliceContainsEqualFold(existRefIDs, order.OrderID)
		if !exist {
			foundIDs = append(foundIDs, order.OrderID)
		}
	}

	if len(foundIDs) > 1 {
		this.controller.ErrorMsgf("[%s] 网格 [%d] 存在多个已成交无卖单的买单: %v", this.FullID(), index, foundIDs)
		// 当止损冷却后重启，也可能会出现这种情况
	}
	if len(foundIDs) == 0 {
		this.controller.ErrorMsgf("[%s] 网格 [%d] 不存在已成交无卖单的买单", this.FullID(), index)
		return "MISSING"
	}
	return foundIDs[len(foundIDs)-1]
}

func (this *Grid) FullID() string {
	return fmt.Sprintf("%s_%s", this.controller.ID, this.RefID)
}

func (this *Grid) sendTradeMsg(orders []*Order) {
	t := NewTable()
	t.SetHeader([]string{"Grid Index", "Symbol", "Grid ID", "Open/Close", "Order ID", "Direction", "Exec. Price", "Exec. Qty", "Exec. Time", "Profit"})
	for _, order := range orders {
		profitStr := "-"
		refOrderID := order.GetString(ExtKeyRefOrderID)
		openStr := "Open"
		if order.Side == exchange.OrderSideSell && !this.Reverse {
			openStr = "Close"
			for _, buyOrder := range this.BuyOrders {
				if buyOrder.IsFilled() && refOrderID == buyOrder.OrderID {
					profit := this.Qty2Size(order.ExecPrice, order.ExecQty) - this.Qty2Size(buyOrder.ExecPrice, buyOrder.ExecQty)
					if this.KeepProfitRatio > 0 {
						profit = this.Qty2Size(order.ExecPrice, order.ExecQty) - this.Qty2Size(buyOrder.ExecPrice, order.ExecQty)
					}
					profitStr = fmt.Sprintf("%.2f %s", profit, this.QuoteAsset)
					break
				}
			}
		} else if order.Side == exchange.OrderSideBuy && this.Reverse {
			openStr = "Close"
			for _, sellOrder := range this.SellOrders {
				if sellOrder.IsFilled() && refOrderID == sellOrder.OrderID {
					profit := this.Qty2Size(sellOrder.ExecPrice, sellOrder.ExecQty) - this.Qty2Size(order.ExecPrice, order.ExecQty)
					if this.KeepProfitRatio > 0 {
						profit = this.Qty2Size(sellOrder.ExecPrice, order.ExecQty) - this.Qty2Size(order.ExecPrice, order.ExecQty)
					}
					profitStr = fmt.Sprintf("%.2f %s", profit, this.QuoteAsset)
					break
				}
			}
		}

		dir := order.GetString(ExtKeyCategory)
		if dir == "" {
			dir = string(order.Side)
		}
		t.AddRow([]string{
			fmt.Sprintf("%d", order.GetInt(ExtKeyGridIndex)),
			this.Symbol,
			this.GetDisplayID(true),
			openStr,
			order.OrderID,
			dir,
			this.formatPrice(order.ExecPrice),
			this.formatQty(order.ExecQty),
			utils.FormatShortTimeStr(order.UpdateTime, false),
			profitStr,
		})
	}
	this.controller.SendMsgf(fmt.Sprintf("网格 [%s / %s] 订单成交提醒\n```%s```", this.Symbol, this.GetDisplayID(false), t.Render()))
}

func (this *Grid) fakeOpenOrder(price, qty float64, category OrderCategory, gridIndex int) (*Order, error) {
	nowTime := time.Now()
	order := &exchange.Order{
		InstrumentType: this.InstrumentType(),
		OrderID:        "FAKE_" + exchange.NewRandomID(),
		Symbol:         this.Symbol,
		Price:          price,
		Qty:            qty,
		Type:           exchange.Limit,
		Status:         exchange.OrderStatusFilled,
		FeeAsset:       this.BaseAsset,
		Fee:            0,
		ExecQty:        qty,
		ExecPrice:      price,
		CreateTime:     &nowTime,
		UpdateTime:     &nowTime,
	}

	order.SetString(ExtKeyCategory, string(category))
	order.SetInt(ExtKeyGridIndex, gridIndex)

	if category == OrderCategoryBuy {
		order.Side = exchange.OrderSideBuy
		this.BuyOrders = append(this.BuyOrders, order)
	} else {
		order.Side = exchange.OrderSideSell
		this.SellOrders = append(this.SellOrders, order)
	}

	this.handleFilledOrder(order)
	this.sendTradeMsg([]*Order{order})

	return order, nil
}

func (this *Grid) createOrder(price, qty float64, orderType exchange.OrderType, category OrderCategory, gridIndex int, refOrderId string) (*Order, error) {
	lastPrice, err := this.UpdateLastPrice()
	if err != nil {
		return nil, fmt.Errorf("获取现价失败: %s", err)
	}

	orderMayFilled := false
	if category == OrderCategorySell {
		if lastPrice > price {
			orderType = exchange.Limit // 可以直接成交，用限价单
			orderMayFilled = true
		}

		if (lastPrice * 0.99) > price {
			price = lastPrice * 0.99 // 如果卖价低于现价太多可能因为价格限制无法提交
		}

		if this.controller.IsExchange(exchange.MetaTrader) && lastPrice > price {
			// MetaTrader 价格不得低于买 1 价，所以直接用市价单
			orderType = exchange.Market
		}
	} else {
		if lastPrice < price {
			orderType = exchange.Limit // 可以直接成交，用限价单
			orderMayFilled = true
		}

		if (lastPrice * 1.01) < price {
			price = lastPrice * 1.01 // 如果买入价高于现价太多可能因为价格限制无法提交
		}

		if this.controller.IsExchange(exchange.MetaTrader) && lastPrice < price {
			// MetaTrader 价格不得高于卖 1 价，所以直接用市价单
			orderType = exchange.Market
		}
	}

	qty = this.floorQty(qty)
	if qty == 0 {
		return nil, fmt.Errorf("qty can not be 0")
	}
	args := exchange.CreateOrderArgs{
		InstrumentType: this.InstrumentType(),
		Symbol:         this.Symbol,
		Type:           orderType,
		TradeMode:      exchange.TradeModeCash,
		Qty:            qty,
		TimeInForce:    exchange.GTC,
	}

	if orderType == exchange.Limit {
		args.Price = price
	} else if orderType == exchange.StopLimit {
		args.TriggerPrice = price
		if category == OrderCategoryBuy {
			args.Price = price * (1 - this.controller.Config.SlippageRate)
			args.TriggerDirection = exchange.TriggerDirectionLower
		} else {
			args.Price = price * (1 + this.controller.Config.SlippageRate)
			args.TriggerDirection = exchange.TriggerDirectionHigher
		}
	} else if orderType == exchange.Market {
		// no price
	} else {
		return nil, fmt.Errorf("order type %s not supported", orderType)
	}

	if this.InstrumentType().IsFuture() {
		args.TradeMode = exchange.TradeModeCross
		if (!this.Reverse && category == OrderCategorySell) || (this.Reverse && category == OrderCategoryBuy) {
			args.ClosePosition = true
			if !this.controller.IsExchange(exchange.OKEx) {
				args.ReduceOnly = true
			}
		}
	}
	if category == OrderCategoryBuy {
		args.Side = exchange.OrderSideBuy
	} else {
		args.Side = exchange.OrderSideSell
	}
	order, err := this.controller.Exchange.CreateOrder(args)
	if err != nil {
		return nil, err
	}
	order.SetString(ExtKeyCategory, string(category))
	order.SetInt(ExtKeyGridIndex, gridIndex)
	order.SetString(ExtKeyRefOrderID, refOrderId)

	if category == OrderCategoryBuy {
		this.BuyOrders = append(this.BuyOrders, order)
	} else {
		this.SellOrders = append(this.SellOrders, order)
	}

	if orderMayFilled && !order.IsFilled() {
		// 主动查一次，避免因为价格和网格实际不同导致 confirmOrders 时对不上
		filledOrder, err := this.controller.Exchange.GetOrderByOrig(*order)
		if err == nil {
			order = filledOrder
		}
	}

	if order.IsFilled() { // 直接成交的订单
		this.handleFilledOrder(order)
		this.sendTradeMsg([]*Order{order})
	}

	return order, nil
}

func (this *Grid) updateOpenOrder(origOrder *exchange.Order, price, qty float64) (*Order, error) {
	qty = this.floorQty(qty)

	args := &exchange.UpdateOrderArgs{
		InstrumentType: this.InstrumentType(),
		OrderQty:       qty,
		Type:           this.OrderType(),
	}

	if args.Type == exchange.Limit {
		args.Price = price
	} else if args.Type == exchange.StopLimit {
		args.TriggerPrice = price
		if origOrder.Side == exchange.OrderSideBuy {
			args.Price = price * (1 - this.controller.Config.SlippageRate)
		} else {
			args.Price = price * (1 + this.controller.Config.SlippageRate)
		}
	}

	order, err := this.controller.Exchange.UpdateOrder(*origOrder, args)
	if err != nil {
		return nil, err
	}

	if origOrder.Side == exchange.OrderSideBuy {
		for idx, o := range this.BuyOrders {
			if o.OrderID == origOrder.OrderID {
				this.BuyOrders[idx] = order
				break
			}
		}
	} else {
		for idx, o := range this.SellOrders {
			if o.OrderID == origOrder.OrderID {
				this.SellOrders[idx] = order
				break
			}
		}
	}

	return order, nil
}

func (this *Grid) createCloseOrder(price float64, orderType exchange.OrderType, category OrderCategory) (*Order, error) {
	qty := math.Abs(this.BaseAssetQty.Total)
	qty = this.floorQty(qty)
	if qty == 0 {
		return nil, nil
	}
	args := exchange.CreateOrderArgs{
		InstrumentType: this.InstrumentType(),
		Symbol:         this.Symbol,
		Type:           orderType,
		TradeMode:      exchange.TradeModeCash,
		Qty:            qty,
		Side:           exchange.OrderSideSell,
		TimeInForce:    exchange.GTC,
	}

	if orderType == exchange.StopLimit {
		args.TriggerPrice = price
		if category == OrderCategoryStopLoss {
			args.Price = price * (1 - this.controller.Config.SlippageRate)
			if this.Reverse {
				args.Price = price * (1 + this.controller.Config.SlippageRate)
			}
		} else {
			args.Price = price * (1 + this.controller.Config.SlippageRate)
			args.TriggerDirection = exchange.TriggerDirectionHigher
			if this.Reverse {
				args.Price = price * (1 - this.controller.Config.SlippageRate)
				args.TriggerDirection = exchange.TriggerDirectionLower
			}
		}
	} else if orderType == exchange.Limit {
		args.Price = price
	} else {
		return nil, fmt.Errorf("order type %s not supported", orderType)
	}

	if this.InstrumentType().IsFuture() {
		args.TradeMode = exchange.TradeModeCross
		args.ClosePosition = true
		if !this.controller.IsExchange(exchange.OKEx) {
			args.ReduceOnly = true
		}
	}

	if this.Reverse {
		args.Side = exchange.OrderSideBuy
	}

	ex := this.getCloseOrderExchange()
	order, err := ex.CreateOrder(args)
	if err != nil {
		return nil, err
	}
	order.SetString(ExtKeyCategory, string(category))
	this.CloseOrders = append(this.CloseOrders, order)

	return order, nil
}

func (this *Grid) updateCloseOrder(origOrder *exchange.Order, price, qty float64) (*Order, error) {
	qty = this.floorQty(qty)

	ex := this.getCloseOrderExchange()

	if qty == 0 {
		if err := ex.CancelOrder(this.InstrumentType(), this.OrderType(), this.Symbol, origOrder.OrderID); err != nil {
			this.controller.ErrorMsgf("取消订单[%s] 失败: %s", origOrder.OrderID, err)
			return nil, err
		} else {
			origOrder.Status = exchange.OrderStatusCancelled
			return origOrder, nil
		}
	}

	args := &exchange.UpdateOrderArgs{
		InstrumentType: this.InstrumentType(),
		OrderQty:       qty,
		Type:           exchange.StopLimit,
	}
	args.TriggerPrice = price
	if origOrder.GetString(ExtKeyCategory) == string(OrderCategoryStopLoss) {
		args.Price = price * (1 - this.controller.Config.SlippageRate)
		if this.Reverse {
			args.Price = price * (1 + this.controller.Config.SlippageRate)
		}
	} else {
		args.Price = price * (1 + this.controller.Config.SlippageRate)
		if this.Reverse {
			args.Price = price * (1 - this.controller.Config.SlippageRate)
		}
	}
	if this.InstrumentType().IsFuture() {
		args.ClosePosition = true
		if !this.controller.IsExchange(exchange.OKEx) {
			args.ReduceOnly = true
		}
	}
	order, err := ex.UpdateOrder(*origOrder, args)
	if err != nil {
		return nil, err
	}

	for idx, o := range this.CloseOrders {
		if o.OrderID == origOrder.OrderID {
			this.CloseOrders[idx] = order
			break
		}
	}

	return order, nil
}

func (this *Grid) CloseAllBaseAsset() (*Order, error) {
	qty := math.Abs(this.BaseAssetQty.Total)
	qty = this.floorQty(qty)
	if qty == 0 {
		return nil, errors.New("qty can not be zero")
	}
	args := exchange.CreateOrderArgs{
		InstrumentType: this.InstrumentType(),
		Symbol:         this.Symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCash,
		Qty:            qty,
		TimeInForce:    exchange.GTC,
		Side:           exchange.OrderSideSell,
	}
	if this.InstrumentType().IsFuture() {
		args.TradeMode = exchange.TradeModeCross
		args.ReduceOnly = true
	}
	if this.Reverse {
		args.Side = exchange.OrderSideBuy
	}
	order, err := this.controller.Exchange.CreateOrder(args)
	if err != nil {
		return nil, err
	}

	orderID := order.OrderID

	retries := 0
	createdOrder := *order
	for {
		order, err = this.controller.Exchange.GetOrderByOrig(createdOrder)
		if err != nil {
			if retries <= 3 {
				// ftx 可能会查不到订单，稍等一会重试
				this.controller.ErrorMsgf("[%s] 查询平仓订单[%s]失败: %s (重试中: %d)", this.FullID(), orderID, err, retries)
				retries++
				time.Sleep(time.Millisecond * 200)
				continue
			}
			this.controller.ErrorMsgf("[%s] 查询平仓订单[%s]失败: %s (已重试 %d 次)", this.FullID(), orderID, err, retries)
			return nil, err
		}
		break
	}

	order.SetInt(ExtKeyGridIndex, -1)
	order.SetString(ExtKeyCategory, string(OrderCategoryManualClose))
	this.CloseOrders = append(this.CloseOrders, order)

	if this.Reverse {
		this.QuoteAssetQty.Total -= this.Qty2Size(order.ExecPrice, order.ExecQty)
		this.BaseAssetQty.Total = utils.DecimalAdd(this.BaseAssetQty.Total, order.ExecQty)
	} else {
		fee := 0.0
		if order.FeeAsset == this.QuoteAsset {
			fee = math.Abs(order.Fee)
		}
		this.QuoteAssetQty.Total += (this.Qty2Size(order.ExecPrice, order.ExecQty) - fee)
		this.BaseAssetQty.Total = utils.DecimalAdd(this.BaseAssetQty.Total, -order.ExecQty)
	}

	this.updateSnapshot()

	return order, nil
}

func (this *Grid) UpdateLastPrice() (float64, error) {
	lastPrice, err := this.controller.Exchange.GetLastPrice(this.SymbolCode.InstrumentType(), this.Symbol, false)
	if err == nil {
		this.LastPrice = lastPrice
		nowTime := time.Now()
		this.LastPriceUpdateTime = &nowTime
	}
	return lastPrice, err
}

func (this *Grid) Start() {
	lock, ok := this.controller.handleMutex.Load(this.Symbol)
	if !ok {
		this.Errorf("grid controller handle mutex improperly initialized")
		return
	}
	lock.Lock()
	defer lock.Unlock()

	this.Status = Running

	if this.InstrumentType().IsFuture() {
		// 杠杠率设到最大
		this.setLeverageToMax()

		if err := this.controller.Exchange.SetMarginMode(this.InstrumentType(), this.Symbol, exchange.Cross); err != nil {
			if !strings.Contains(err.Error(), "No need to change") {
				this.controller.AlertMsgf("设置全仓模式失败: %s", err)
			}
		}
	}

	baseQty := this.InitQty.GetBaseQty()
	extraQty := baseQty
	if baseQty != 0 {
		// baseQty 满足网格应有持仓的部分创建 Fake 成交订单完善数据，如有剩余则卖掉
		// e.g. 当前应持仓 0.3 = 0.1 * 3 格，baseQty 为 0.4，Fake 3 订单，卖掉 0.1；
		// 不足一格的部分也卖掉，避免非网格数量订单
		// e.g. 当前应持仓 0.3 = 0.1 * 3 格，baseQty 为 0.25，Fake 2 订单，卖掉 0.05；
		if _, err := this.UpdateLastPrice(); err != nil {
			this.controller.ErrorMsgf("获取现价失败: %s", err)
			return
		}
	}

	closeExtraBase := func() {
		if extraQty != 0 {
			side := exchange.OrderSideSell
			if extraQty < 0 {
				side = exchange.OrderSideBuy
			}
			if order, err := this.controller.createMarketOrder(this.SymbolCode, this.Symbol, math.Abs(extraQty), side, true); err != nil {
				this.controller.ErrorMsgf("平仓剩余 BaseQty %v 失败: %s", extraQty, err.Error())
			} else {
				this.controller.SendMsgf("平仓剩余 BaseQty 成交: %s %v@%v", side, order.ExecQty, order.ExecPrice)
			}
			extraQty = 0
		}
	}

	// 挂所有格子买单，看空网格挂卖单
	if this.Reverse {
		sellPrices := this.SellPrices()
		for i, price := range sellPrices { // 价格从低到高优先成交
			qty := this.SellQtys[i]
			if price <= this.LastPrice && extraQty <= -qty {
				extraQty = utils.DecimalAdd(extraQty, qty)
				this.fakeOpenOrder(this.LastPrice, qty, OrderCategorySell, i)
				continue
			}

			closeExtraBase()

			_, err := this.createOrder(price, qty, this.OrderType(), OrderCategorySell, i, "")
			if err != nil {
				this.controller.ErrorMsgf("创建初始卖单[%d]失败: %s", i, err)
				return
			}
			time.Sleep(time.Millisecond * 200)
		}
	} else {
		for i := this.Num - 1; i >= 0; i-- { // 价格从高到低优先成交
			price := this.BuyPrices()[i]
			qty := this.BuyQtys[i]
			if price >= this.LastPrice && extraQty >= qty {
				extraQty = utils.DecimalAdd(extraQty, -qty)
				this.fakeOpenOrder(this.LastPrice, qty, OrderCategoryBuy, i)
				continue
			}

			closeExtraBase()

			_, err := this.createOrder(price, qty, this.OrderType(), OrderCategoryBuy, i, "")
			if err != nil {
				this.controller.ErrorMsgf("创建初始买单[%d]失败: %s", i, err)
				return
			}
			time.Sleep(time.Millisecond * 200)
		}
	}

	this.updateSnapshot()
	this.controller.storage.Save()
}

func (this *Grid) getBuyOrderFee(order *Order) (fee float64) {
	if order.Fee > 0 {
		// 目前 OK 查询订单有返回 Fee 和 FeeAsset, Binance 没有
		fee = math.Abs(order.Fee)
	} else {
		// 这里默认都是收取基础币种, 用 BNB 抵扣等情况配置 SpotFeeRate 为 0 即可
		fee = order.ExecQty * this.getBaseFeeRate(exchange.OrderSideBuy, order.Type)
	}
	return
}

// 收取的 BaseAsset 费率，如果是收费是 QuoteAsset，不影响买卖数量，所以无需处理
func (this *Grid) getBaseFeeRate(orderSide exchange.OrderSide, orderType exchange.OrderType) float64 {
	if this.controller.IsExchange(exchange.InteractiveBrokers) {
		// IB 手续费都是收 Quote
		return 0
	}

	if this.InstrumentType() == exchange.Spot {
		feeRate := this.controller.Config.SpotFeeRate
		if orderType == exchange.Limit {
			if orderSide == exchange.OrderSideBuy && this.controller.Config.SpotFeeRateLimitBuy > 0 {
				feeRate = this.controller.Config.SpotFeeRateLimitBuy
			} else if orderSide == exchange.OrderSideSell && this.controller.Config.SpotFeeRateLimitSell > 0 {
				feeRate = this.controller.Config.SpotFeeRateLimitSell
			}
		} else if orderType == exchange.Market {
			if orderSide == exchange.OrderSideBuy && this.controller.Config.SpotFeeRateMarketBuy > 0 {
				feeRate = this.controller.Config.SpotFeeRateMarketBuy
			} else if orderSide == exchange.OrderSideSell && this.controller.Config.SpotFeeRateMarketSell > 0 {
				feeRate = this.controller.Config.SpotFeeRateMarketSell
			}
		}
		if feeRate <= 0.0000010001 {
			feeRate = 0 // 极小值也表示不收手续费
		}
		return feeRate
	}
	// 合约费用都是 QuoteAsset 无需处理
	return 0
}

// 更新成交数据，挂对应网格反方向单
func (this *Grid) handleFilledOrder(filledOrder *Order) {
	if filledOrder.Side == exchange.OrderSideBuy {
		this.Report.BuyFilledCount += 1
		fee := this.getBuyOrderFee(filledOrder)
		this.BaseAssetQty.Total = utils.DecimalAdd(this.BaseAssetQty.Total, filledOrder.ExecQty-fee)
		this.QuoteAssetQty.Total -= this.Qty2Size(filledOrder.ExecPrice, filledOrder.ExecQty)

		updated := false
		for i, order := range this.BuyOrders {
			if order.OrderID == filledOrder.OrderID {
				filledOrder.SetExtStruct(order)
				this.BuyOrders[i] = filledOrder
				updated = true
				break
			}
		}
		if !updated {
			this.controller.AlertMsgf("更新本地买单[%s]失败，找不到原始订单。", filledOrder.OrderID)
			return
		}
	} else if filledOrder.Side == exchange.OrderSideSell {
		this.Report.SaleFilledCount += 1
		fee := 0.0
		if filledOrder.FeeAsset == this.QuoteAsset {
			fee = math.Abs(filledOrder.Fee)
		}
		this.QuoteAssetQty.Total += (this.Qty2Size(filledOrder.ExecPrice, filledOrder.ExecQty) - fee)
		this.BaseAssetQty.Total = utils.DecimalAdd(this.BaseAssetQty.Total, -filledOrder.ExecQty)

		updated := false
		for i, order := range this.SellOrders {
			if order.OrderID == filledOrder.OrderID {
				filledOrder.SetExtStruct(order)
				this.SellOrders[i] = filledOrder
				updated = true
				break
			}
		}
		if !updated {
			this.controller.AlertMsgf("更新本地卖单[%s]失败，找不到原始订单。", filledOrder.OrderID)
			return
		}
	} else {
		this.controller.AlertMsgf("order[%s] miss side", filledOrder.OrderID)
		return
	}

	gridIndex := filledOrder.GetInt(ExtKeyGridIndex)
	refOrderID := ""
	if filledOrder.Side == exchange.OrderSideBuy {
		// 买单成交，挂对应网格卖单
		fee := this.getBuyOrderFee(filledOrder)
		if !this.Reverse {
			refOrderID = filledOrder.OrderID
		}
		_, err := this.createOrder(this.SellPrices()[gridIndex], this.SellQtys[gridIndex]-fee, this.OrderType(), OrderCategorySell, gridIndex, refOrderID)
		if err != nil {
			this.controller.ErrorMsgf("[%s] 创建卖单[%d]失败: %s", this.FullID(), gridIndex, err)
			return
		}
	} else {
		// 卖单成交，挂对应网格买单
		if this.Reverse {
			refOrderID = filledOrder.OrderID
		}
		_, err := this.createOrder(this.BuyPrices()[gridIndex], this.BuyQtys[gridIndex], this.OrderType(), OrderCategoryBuy, gridIndex, refOrderID)
		if err != nil {
			this.controller.ErrorMsgf("[%s] 创建买单[%d]失败: %s", this.FullID(), gridIndex, err)
			return
		}
	}

	this.updateSnapshot()
}

func (this *Grid) updatePNL() {
	// 网格收益 = 每组已完全成交的网格收益总和
	gridPNL := 0.0
	holdingValue := 0.0
	unrealisedQty := 0.0
	unrealisedPNL := 0.0
	orderPairs := this.GetFilledOrderPairs()
	for _, pair := range orderPairs {
		buyOrder, sellOrder := pair[0], pair[1]
		if this.Reverse {
			if sellOrder == nil {
				continue
			}
			if buyOrder == nil {
				holdingValue += this.Qty2Size(sellOrder.ExecPrice, sellOrder.ExecQty)
				continue
			}
		} else {
			if buyOrder == nil {
				continue
			}
			if sellOrder == nil {
				holdingValue += this.Qty2Size(buyOrder.ExecPrice, buyOrder.ExecQty)
				continue
			}
		}

		if this.KeepProfitRatio == 0 {
			gridPNL += this.Qty2Size(sellOrder.ExecPrice, sellOrder.ExecQty) - this.Qty2Size(buyOrder.ExecPrice, buyOrder.ExecQty)
		} else {
			keepQty := buyOrder.ExecQty - sellOrder.ExecQty
			unrealisedQty += keepQty
			if this.Reverse {
				gridPNL += this.Qty2Size(sellOrder.ExecPrice, buyOrder.ExecQty) - this.Qty2Size(buyOrder.ExecPrice, buyOrder.ExecQty)
				unrealisedPNL += this.Qty2Size(sellOrder.ExecPrice, keepQty) - this.Qty2Size(this.LastPrice, keepQty)
			} else {
				gridPNL += this.Qty2Size(sellOrder.ExecPrice, sellOrder.ExecQty) - this.Qty2Size(buyOrder.ExecPrice, sellOrder.ExecQty)
				unrealisedPNL += this.Qty2Size(this.LastPrice, keepQty) - this.Qty2Size(buyOrder.ExecPrice, keepQty)
			}
		}
	}

	for _, order := range this.CloseOrders {
		if order.IsFilled() {
			holdingValue -= this.Qty2Size(order.ExecPrice, order.ExecQty)
		}
	}

	// 持仓收益 = 当前持有币浮动盈亏
	currentValue := this.Qty2Size(this.LastPrice, math.Abs(this.BaseAssetQty.Total-unrealisedQty))
	holdingPNL := currentValue - holdingValue
	if this.Reverse {
		holdingPNL = -holdingPNL
	}

	this.Report.GridPNL = gridPNL
	this.Report.HoldingPNL = holdingPNL
	this.Report.UnrealisedPNL = unrealisedPNL
	this.Report.PNL = gridPNL + holdingPNL + unrealisedPNL
}

func (this *Grid) updateSnapshot() {
	this.updatePNL()
	now := time.Now()
	positionValue, err := this.controller.Exchange.Qty2Size(this.InstrumentType(), this.Symbol, this.LastPrice, this.BaseAssetQty.Total)
	if err != nil {
		this.controller.ErrorMsgf("获取持仓价值失败: %s", err)
		return
	}
	balanceValue := positionValue + this.QuoteAssetQty.Total
	snapshot := &Snapshot{
		ID:            exchange.NewRandomID(),
		CreateTime:    now,
		LastPrice:     this.LastPrice,
		GridID:        this.RefID,
		PNL:           this.Report.PNL,
		GridPNL:       this.Report.GridPNL,
		HoldingPNL:    this.Report.HoldingPNL,
		UnrealisedPNL: this.Report.UnrealisedPNL,
		AssetPairQty:  NewAssetPairQty(this.BaseAssetQty.Total, this.QuoteAssetQty.Total),
		PositionValue: positionValue,
		TotalValue:    balanceValue,
		BuyCount:      this.Report.BuyFilledCount,
		SellCount:     this.Report.SaleFilledCount,
		InitWorth:     this.InitWorth,
	}
	this.Snapshot = snapshot
	this.controller.SaveSnapshot(snapshot)
	this.updateGroupSnapshot(snapshot)
}

// 更新某个 gridSnapshot 对应的 groupSnapshot
func (this *Grid) updateGroupSnapshot(gridSnapshot *Snapshot) {
	updatedGroups := []*GridGroup{}
	for _, group := range this.controller.GridGroups {
		if utils.SliceContainsEqualFold(group.GridIDs, gridSnapshot.GridID) {
			updatedGroups = append(updatedGroups, group)
		}
	}
	for _, group := range updatedGroups {
		groupSnapshot := this.mergeGroupSnapshot(group)
		this.controller.SaveSnapshot(groupSnapshot)
	}
}

func (this *Grid) mergeGroupSnapshot(group *GridGroup) *Snapshot {
	groupSnapshot := &Snapshot{
		CreateTime: time.Now(),
		ID:         exchange.NewRandomID(),
		GroupID:    group.RefID,
	}
	for _, gridID := range group.GridIDs {
		grid := this.controller.GetGridByID(gridID, false)
		if grid == nil || grid.Snapshot == nil {
			continue
		}
		groupSnapshot.PNL = utils.DecimalAdd(groupSnapshot.PNL, grid.Snapshot.PNL)
		groupSnapshot.GridPNL = utils.DecimalAdd(groupSnapshot.GridPNL, grid.Snapshot.GridPNL)
		groupSnapshot.HoldingPNL = utils.DecimalAdd(groupSnapshot.HoldingPNL, grid.Snapshot.HoldingPNL)
		groupSnapshot.UnrealisedPNL = utils.DecimalAdd(groupSnapshot.UnrealisedPNL, grid.Snapshot.UnrealisedPNL)
		groupSnapshot.AssetPairQty.SetBaseQty(utils.DecimalAdd(groupSnapshot.AssetPairQty.GetBaseQty(), grid.Snapshot.AssetPairQty.GetBaseQty()))
		groupSnapshot.AssetPairQty.SetQuoteQty(utils.DecimalAdd(groupSnapshot.AssetPairQty.GetQuoteQty(), grid.Snapshot.AssetPairQty.GetQuoteQty()))
		groupSnapshot.PositionValue = utils.DecimalAdd(groupSnapshot.PositionValue, grid.Snapshot.PositionValue)
		groupSnapshot.TotalValue = utils.DecimalAdd(groupSnapshot.TotalValue, grid.Snapshot.TotalValue)
		groupSnapshot.BuyCount = groupSnapshot.BuyCount + grid.Snapshot.BuyCount
		groupSnapshot.SellCount = groupSnapshot.SellCount + grid.Snapshot.SellCount
		groupSnapshot.InitWorth = groupSnapshot.InitWorth + grid.Snapshot.InitWorth
	}
	return groupSnapshot
}

func (this *Grid) ManualClose(sellBaseCoin bool, force bool) {
	this.handleOrders() // close 之前检查同步一下最新数据
	this.Close(FinishReasonManual, sellBaseCoin, force)
}

func (this *Grid) Close(reason FinishReason, sellBaseCoin bool, force bool) {
	if this.Status == Closed {
		return
	}

	lock, ok := this.controller.handleMutex.Load(this.Symbol)
	if !ok {
		this.controller.ErrorMsgf("执行停止网格 %s 失败，锁未初始化", this.FullID())
		return
	}
	lock.Lock()
	defer lock.Unlock()

	if sellBaseCoin {
		this.controller.SendMsgf("执行停止网格 %s，将依次执行取消订单、平仓操作", this.FullID())
	}

	localOpenOrders := this.GetLocalOpenOrders()
	for _, order := range localOpenOrders {
		if err := this.controller.Exchange.CancelOrder(this.InstrumentType(), this.OrderType(), this.Symbol, order.OrderID); err != nil {
			this.controller.ErrorMsgf("取消订单[%s] 失败: %s", order.OrderID, err)
			if !force {
				this.controller.SendMsgf("停止操作已中断")
				return
			}
		} else {
			order.Status = exchange.OrderStatusCancelled
		}
	}

	for _, order := range this.CloseOrders {
		if !order.IsOpen() {
			continue
		}

		ex := this.getCloseOrderExchange()

		if err := ex.CancelOrder(this.InstrumentType(), this.OrderType(), this.Symbol, order.OrderID); err != nil {
			this.controller.ErrorMsgf("取消平仓订单[%s] 失败: %s", order.OrderID, err)
			if !force {
				this.controller.SendMsgf("停止操作已中断")
				return
			}
		} else {
			order.Status = exchange.OrderStatusCancelled
		}
	}

	if sellBaseCoin && this.floorQty(math.Abs(this.BaseAssetQty.Total)) != 0 {
		holdingQtyBefore := 0.0
		coinOrSymbol := this.Symbol
		if this.InstrumentType() == exchange.Spot {
			coinOrSymbol = this.SymbolCode.Coin()
		}
		if total, _, err := this.controller.Exchange.GetHoldingQty(this.InstrumentType(), coinOrSymbol); err != nil {
			this.controller.ErrorMsgf("获取当前持有 %s 数量失败: %s", this.Symbol, err)
		} else {
			holdingQtyBefore = total
		}

		wayStr := "卖出"
		if this.Reverse {
			wayStr = "买入"
		}
		order, err := this.CloseAllBaseAsset()
		if err != nil {
			this.controller.ErrorMsgf("%s %s失败: %s", this.Symbol, wayStr, err)
			if !force {
				this.controller.SendMsgf("停止操作已中断，请人工确认 %s 是否成功", wayStr)
				return
			}
		} else {
			holdingQtyAfter := 0.0
			if total, _, err := this.controller.Exchange.GetHoldingQty(this.InstrumentType(), coinOrSymbol); err != nil {
				this.controller.ErrorMsgf("获取当前持有 %s 数量失败: %s", this.Symbol, err)
			} else {
				holdingQtyAfter = total
			}

			t := NewTable()
			t.SetHeader([]string{"Direction", "Exe. Price", "Exe. Qty", "Holding Before", "Holding After"})
			t.AddRow([]string{
				string(order.Side),
				this.formatPrice(order.ExecPrice),
				this.formatQty(order.ExecQty),
				this.formatQty(holdingQtyBefore),
				this.formatQty(holdingQtyAfter),
			})
			this.controller.SendMsgf("网格平仓成功:\n```%s```", t.Render())
		}
	}

	this.Finish(reason)
	this.controller.SendMsgf("网格 %s 已停止。", this.RefID)
}

func (this *Grid) Pause() {
	if this.Status != Running {
		this.controller.WarnMsgf("网格 %s 不是运行状态，无法暂停。", this.RefID)
		return
	}

	lock, ok := this.controller.handleMutex.Load(this.Symbol)
	if !ok {
		this.controller.ErrorMsgf("暂停网格 %s 失败，锁未初始化", this.FullID())
		return
	}
	lock.Lock()
	defer lock.Unlock()

	localOpenOrders := this.GetLocalOpenOrders()
	for _, order := range localOpenOrders {
		if err := this.controller.Exchange.CancelOrder(this.InstrumentType(), this.OrderType(), this.Symbol, order.OrderID); err != nil {
			this.controller.ErrorMsgf("取消订单[%s] 失败: %s", order.OrderID, err)
		} else {
			order.Status = exchange.OrderStatusCancelled
		}
	}

	this.Status = Paused
	this.controller.storage.Save()
	this.controller.SendMsgf("网格 %s 已暂停。", this.RefID)
}

func (this *Grid) Resume() {
	if this.Status != Paused {
		this.controller.WarnMsgf("网格 %s 不是暂停状态，无法恢复。", this.RefID)
		return
	}

	this.Status = Running
	this.handleOrders()
	this.controller.storage.Save()
	this.controller.SendMsgf("网格 %s 已恢复。", this.RefID)
}

func (this *Grid) CheckIfCoolEnd() bool {
	if this.CoolEndTime == nil || this.Status != Cooling {
		return false
	}
	if time.Now().Before(*this.CoolEndTime) {
		// 在 CoolEndTime 前 24 小时提醒一次
		if !this.CoolEndReminded && this.CoolEndTime.Sub(time.Now()) <= (time.Hour*24) {
			this.CoolEndReminded = true
			this.controller.SendMsgf("网格 %s 将在 24 小时后结束冷却（同时价格满足 CoolReEntryIndex 条件才会恢复运行）。", this.GetDisplayID(true))
		}
		return false
	}

	this.UpdateLastPrice()

	if this.CoolReEntryIndex < 0 || this.CoolReEntryIndex > this.Num {
		this.controller.ErrorMsgf("CoolReEntryIndex 值 %d 错误", this.CoolReEntryIndex)
		return false
	}

	if this.Reverse {
		reEntryPrice := this.Prices[this.Num-this.CoolReEntryIndex]
		return this.LastPrice <= reEntryPrice
	} else {
		reEntryPrice := this.Prices[this.CoolReEntryIndex]
		return this.LastPrice >= reEntryPrice
	}
}

func (this *Grid) StartCooling() {
	if this.Status != Running {
		this.controller.WarnMsgf("网格 %s 不是运行状态，无法进入冷却。", this.RefID)
		return
	}

	this.Report.StopLossGridCount += this.HoldingCount() // 当前持仓网格都止损了
	this.Status = Cooling
	t := time.Now().Add(time.Duration(this.CoolMinutes) * time.Minute)
	this.CoolEndTime = &t

	// 如果是 HedgedGroupGrid，自动 pause 另外一个方向的网格，直到 end cool 重启
	_, oppositeGrid := this.isInverseHedgedGroupGrid()
	if oppositeGrid != nil && oppositeGrid.Status == Running {
		this.controller.SendMsgf("暂停反方向网格 %s", oppositeGrid.GetDisplayID(true))
		oppositeGrid.Pause()
	}

	this.controller.storage.Save()
}

func (this *Grid) EndCooling() {
	this.Status = Running
	this.CoolEndTime = nil
	this.CoolEndReminded = false

	this.controller.SendMsgf("网格 %s 冷却已结束，满足开启条件，将重新开启。", this.GetDisplayID(true))

	_, oppositeGrid := this.isInverseHedgedGroupGrid()
	if oppositeGrid != nil && oppositeGrid.Status == Paused {
		this.controller.SendMsgf("恢复运行反方向网格 %s", oppositeGrid.GetDisplayID(true))
		oppositeGrid.Resume()
	}
}

func (this *Grid) Finish(reason FinishReason) {
	nowTime := time.Now()
	this.FinishTime = &nowTime
	this.FinishReason = reason
	this.Status = Closed
	this.Alias = ""
	for i, grid := range this.controller.Grids {
		if grid.RefID == this.RefID {
			this.controller.Grids = append(this.controller.Grids[:i], this.controller.Grids[i+1:]...) // 删除结束 grid
			break
		}
	}

	this.controller.storage.Save()
}

func (this *Grid) IsFinished() bool {
	return this.FinishTime != nil
}

func (this *Grid) BuyPrices() []float64 {
	return this.Prices[:this.Num]
}

func (this *Grid) SellPrices() []float64 {
	return this.Prices[1:]
}

func (this *Grid) HoldingCount() int {
	count := this.Report.BuyFilledCount - this.Report.SaleFilledCount
	if this.Reverse {
		count = this.Report.SaleFilledCount - this.Report.BuyFilledCount
	}

	count -= this.Report.StopLossGridCount

	if count < 0 || count > this.Num {
		this.controller.AlertMsgf("当前持仓网格数量错误：%d", count)
	}
	return count
}

func (this *Grid) GetLocalOpenOrders() (localOpenOrders []*Order) {
	for _, order := range this.BuyOrders {
		if order.IsOpen() {
			localOpenOrders = append(localOpenOrders, order)
		}
	}
	for _, order := range this.SellOrders {
		if order.IsOpen() {
			localOpenOrders = append(localOpenOrders, order)
		}
	}
	return
}

func (this *Grid) GetPreview() (preview *GridPreview) {
	leftColumn := []*OrderTableRow{}
	rightColumn := []*OrderTableRow{}
	profitRateLow := 1.0
	profitRateHigh := 0.0
	estQuoteQty := this.InitWorth
	estBaseQty := 0.0
	extraBaseQty := this.InitQty.GetBaseQty()
	for i := this.Num - 1; i >= 0; i-- {
		// 0 ~ Num-1 是买价，1 ~ Num 是卖价
		buyPrice := this.Prices[i]
		sellPrice := this.Prices[i+1]
		profitRate := (sellPrice - buyPrice) / buyPrice
		profitRateLow = math.Min(profitRateLow, profitRate)
		profitRateHigh = math.Max(profitRateHigh, profitRate)

		if this.Reverse {
			if sellPrice > this.LastPrice {
				rightColumn = append(rightColumn, &OrderTableRow{
					IndexDisplay: i,
					Qty:          this.SellQtys[i],
					Price:        sellPrice,
					Delta:        (sellPrice - this.LastPrice) / this.LastPrice,
				})
			} else if buyPrice < this.LastPrice {
				estQuoteQty += this.Qty2Size(this.LastPrice, this.SellQtys[i])
				estBaseQty = utils.DecimalAdd(estBaseQty, -this.SellQtys[i])
				leftColumn = append(leftColumn, &OrderTableRow{
					IndexDisplay: i,
					Qty:          this.BuyQtys[i],
					Price:        buyPrice,
					Delta:        (buyPrice - this.LastPrice) / this.LastPrice,
				})
			}
		} else {
			if buyPrice < this.LastPrice {
				leftColumn = append(leftColumn, &OrderTableRow{
					IndexDisplay: i,
					Qty:          this.BuyQtys[i],
					Price:        buyPrice,
					Delta:        (buyPrice - this.LastPrice) / this.LastPrice,
				})
			} else if sellPrice > this.LastPrice {
				estQuoteQty -= this.Qty2Size(this.LastPrice, this.BuyQtys[i])
				estBaseQty = utils.DecimalAdd(estBaseQty, this.BuyQtys[i])
				rightColumn = append(rightColumn, &OrderTableRow{
					IndexDisplay: i,
					Qty:          this.SellQtys[i],
					Price:        sellPrice,
					Delta:        (sellPrice - this.LastPrice) / this.LastPrice,
				})
			}
		}
	}

	if this.InitQty.GetBaseQty() != 0 {
		extraBaseQty = this.InitQty.GetBaseQty() - estBaseQty
	}

	preview = &GridPreview{
		OrderTable: &OrderTable{
			LeftColumn:  leftColumn,
			RightColumn: rightColumn,
		},
		ProfitRateLow:  profitRateLow,
		ProfitRateHigh: profitRateHigh,
		AssetPairQty:   NewAssetPairQty(estBaseQty, estQuoteQty),
		ExtraBaseQty:   extraBaseQty,
	}
	return
}

func (this *Grid) GetOpenOrderTable() (openOrderTable *OrderTable) {
	leftColumn := []*OrderTableRow{}
	rightColumn := []*OrderTableRow{}
	for _, order := range this.BuyOrders {
		if order.IsOpen() {
			price := order.TriggerPrice
			if price == 0 {
				price = order.Price
			}
			leftColumn = append(leftColumn, &OrderTableRow{
				IndexDisplay: order.GetInt(ExtKeyGridIndex),
				Qty:          order.Qty,
				Price:        price,
				Delta:        (price - this.LastPrice) / this.LastPrice,
				OrderID:      order.OrderID,
			})
		}
	}
	for _, order := range this.SellOrders {
		if order.IsOpen() {
			price := order.TriggerPrice
			if price == 0 {
				price = order.Price
			}
			rightColumn = append(rightColumn, &OrderTableRow{
				IndexDisplay: order.GetInt(ExtKeyGridIndex),
				Qty:          order.Qty,
				Price:        price,
				Delta:        (price - this.LastPrice) / this.LastPrice,
				OrderID:      order.OrderID,
			})
		}
	}

	openOrderTable = &OrderTable{
		LeftColumn:  leftColumn,
		RightColumn: rightColumn,
	}
	return
}

func (this *Grid) orderUpdatedCallback() {
	this.Infof("order updated callback")

	if this.orderUpdatedTimer != nil {
		this.orderUpdatedTimer.Stop()
	}
	// 推迟 10s 执行
	this.orderUpdatedTimer = time.AfterFunc(10*time.Second, this.handleOrders)
}

func (this *Grid) GetDisplayID(detail bool) string {
	if this.Alias == "" {
		return this.RefID
	}
	if detail {
		return fmt.Sprintf("%s / %s", this.RefID, this.Alias)
	}
	return this.Alias
}

func (this *Grid) floorQty(qty float64) float64 {
	// lot size 为最小单位向下取其整数倍
	if instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType(), this.Symbol); instrument != nil {
		qtyDec := decimal.NewFromFloat(qty)
		lotSizeDec := decimal.NewFromFloat(instrument.LotSize)
		qtyDec = qtyDec.Div(lotSizeDec).Floor().Mul(lotSizeDec)
		qty, _ = qtyDec.Float64()

		if qty < instrument.MinSize {
			return 0
		}
	}
	return qty
}

func (this *Grid) roundPrice(price float64) float64 {
	// TickSize 为最小单位向下取其整数倍
	if instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType(), this.Symbol); instrument != nil {
		priceDec := decimal.NewFromFloat(price)
		tickSizeDec := decimal.NewFromFloat(instrument.TickSize)
		priceDec = priceDec.Div(tickSizeDec).Round(0).Mul(tickSizeDec)
		price, _ = priceDec.Float64()
	}
	return price
}

func (this *Grid) Qty2Size(price float64, qty float64) (size float64) {
	if this.InstrumentType() == exchange.Spot {
		return price * qty
	}

	if size, err := this.controller.Exchange.Qty2Size(this.InstrumentType(), this.Symbol, price, qty); err != nil {
		this.controller.ErrorMsgf("convert qty 2 size error: %s", err)
		return 0
	} else {
		return size
	}
}

func (this *Grid) Size2Qty(price float64, size float64) (qty float64) {
	if qty, err := this.controller.Exchange.Size2Qty(this.InstrumentType(), this.Symbol, price, size); err != nil {
		this.controller.ErrorMsgf("convert size 2 qty error: %s", err)
		return 0
	} else {
		return qty
	}
}

func (this *Grid) assetQtyToFutureQty(qty float64) (futureQty float64) {
	futureQty, _ = this.controller.Exchange.Size2Qty(this.InstrumentType(), this.Symbol, 1, qty*1)
	return futureQty
}

func (this *Grid) setLeverageToMax() {
	position, err := this.controller.Exchange.GetPosition(this.InstrumentType(), this.Symbol, exchange.UnknownPositionSide, false)
	if position == nil {
		this.controller.AlertMsgf("获取当前持仓失败: %s", err)
		return
	}
	value := this.Qty2Size(position.EntryPrice, math.Abs(position.Qty))
	maxLeverage := this.controller.Exchange.MaxLeverage(this.InstrumentType(), this.Symbol, value)
	if maxLeverage == position.Leverage {
		return
	}

	if err := this.controller.Exchange.SetLeverage(this.InstrumentType(), this.Symbol, exchange.Cross, "", maxLeverage); err != nil {
		this.controller.AlertMsgf("设置杠杠率失败: %s", err)
	}
}

// 所需保证金 = 每格开仓到爆仓价所需保证金之和
// 看多爆仓价 = 最低买入价 - (次低买入价 - 最低买入价)
// 看空爆仓价 = 最高卖出价 + (最高卖出价 - 次高卖出价)
func (this *Grid) calculateEstimatedMargin() (margin float64) {
	marginCallPrice := this.MarginCallPrice
	if marginCallPrice == 0 {
		marginCallPrice = this.Prices[0] - (this.Prices[1] - this.Prices[0])
		marginCallPrice = math.Max(marginCallPrice, 0)
		if this.Reverse {
			marginCallPrice = this.Prices[this.Num] + (this.Prices[this.Num] - this.Prices[this.Num-1])
		}
	}

	if this.Reverse {
		for i, qty := range this.BuyQtys {
			if this.SellPrices()[i] > marginCallPrice {
				continue
			}
			margin += this.Qty2Size(marginCallPrice, qty) - this.Qty2Size(this.SellPrices()[i], qty)
		}
	} else {
		for i, qty := range this.BuyQtys {
			if this.BuyPrices()[i] < marginCallPrice {
				continue
			}
			margin += this.Qty2Size(this.BuyPrices()[i], qty) - this.Qty2Size(marginCallPrice, qty)
		}
	}
	return
}

func (this *Grid) getRealWorth() (worth float64) {
	if this.Reverse {
		for i, qty := range this.SellQtys {
			worth += this.Qty2Size(this.SellPrices()[i], qty)
		}
	} else {
		for i, qty := range this.BuyQtys {
			// this.Debugf("Buy Grid [%02d]:   %.5f: %.2f", i, this.BuyPrices()[i], qty)
			worth += this.Qty2Size(this.BuyPrices()[i], qty)
		}
	}
	return
}

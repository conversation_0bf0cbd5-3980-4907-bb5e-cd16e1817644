package grid

import (
	"fmt"
	"sync"
	"time"

	"github.com/wizhodl/quanter/exchange"
)

type FinishReason string

const FinishReasonTakeProfit FinishReason = "TakeProfit"
const FinishReasonStopLoss FinishReason = "StopLoss"
const FinishReasonManual FinishReason = "Manual"
const FinishReasonAuto FinishReason = "Auto"

const ExtKeyRefOrderID string = "RefOrderID"
const ExtKeyGridIndex string = "GridIndex"
const ExtKeyCategory string = "Category"

type OrderCategory string

const OrderCategoryBuy OrderCategory = "Buy"
const OrderCategorySell OrderCategory = "Sell"
const OrderCategoryStopLoss OrderCategory = "StopLoss"
const OrderCategoryTakeProfit OrderCategory = "TakeProfit"
const OrderCategoryManualClose OrderCategory = "ManualClose"

type Strateger interface {
	GetName() (name StrategyName)
	GetRange() Range
	GetNum() int
	GetQuoteQty() float64
	GetStopLossPrice() float64
	GetTakeProfitPrice() float64
	GetKeepProfitRatio() float64
	GetIsManual() bool
	GetPriceAndQty() (prices []float64, buyQtys []float64, sellQtys []float64)
}

type AssetQty struct {
	mutex      *sync.Mutex // 更新数量时的锁
	Total      float64
	Locked     float64
	Available  float64
	UpdateTime *time.Time
}

type Range [2]float64
type OrderPair [2]*Order

type GridStatus string

const Running GridStatus = "Running"
const Cooling GridStatus = "Cooling"
const Paused GridStatus = "Paused"
const Closed GridStatus = "Closed"

type Grid struct {
	controller        *GridController
	orderUpdatedTimer *time.Timer // ws 触发订单更新 timer
	SymbolCode        *exchange.SymbolCode
	Symbol            string
	Alias             string
	RefID             string
	QuoteAsset        string       // 报价币币种
	BaseAsset         string       // 基础币币种
	InitQty           AssetPairQty // 初始资产数量
	InitWorth         float64      // 初始价值
	InitPrice         float64      // 初始市场价格
	QuoteAssetQty     *AssetQty    // 报价币数量，挂撤卖单后都要更新这个数量；用于和其他 Grid 共存时计算可用数量
	BaseAssetQty      *AssetQty    // 基础币或合约持仓数量，挂撤买单后都要更新这个数量；用于和其他 Grid 共存时计算可用数量
	Reverse           bool         // 做空网格

	Status              GridStatus
	Strategy            Strateger `json:"-"`
	StrategyName        StrategyName
	Num                 int // 网格个数
	Range               Range
	Prices              []float64  // 网格价格，Num + 1 个
	BuyQtys             []float64  // 网格买入数量，Num 个
	SellQtys            []float64  // 网格卖出数量，Num 个
	TakeProfitPrice     float64    // 止盈价
	StopLossPrice       float64    // 止损价
	KeepProfitRatio     float64    // 预留利润比例
	MarginCallPrice     float64    // 保证金报警价格
	LastPrice           float64    // 最新价格
	LastPriceUpdateTime *time.Time // 最新价格更新时间
	BuyOrders           []*Order
	SellOrders          []*Order
	CloseOrders         []*Order    // 止损止盈单
	UseStopLimit        bool        // 是否使用触发单
	Report              *GridReport // report 在 grid 的计算中用的比较多
	Snapshot            *Snapshot   // snapshot 仅仅是用于记录历史快照，不用 grid 的计算；特别是用于 groupSnapshot 的计算
	CreateTime          *time.Time
	FinishTime          *time.Time
	FinishReason        FinishReason
	CoolMinutes         int        // 止损后冷却时长
	CoolReEntryIndex    int        // 多是从最低价开始算 index，空是从最高价开始算 index
	CoolEndTime         *time.Time // 冷却结束时间
	CoolEndReminded     bool       // 冷却结束前提醒
}

type GroupType string

const GroupTypeDefault GroupType = ""
const GroupTypeInverseHedged GroupType = "InverseHedged"

type GridGroup struct {
	Alias   string
	RefID   string
	GridIDs []string
	Type    GroupType
}

func (this *GridGroup) GetDisplayID(detail bool) string {
	if this.Alias == "" {
		return this.RefID
	}
	if detail {
		return fmt.Sprintf("%s / %s", this.RefID, this.Alias)
	}
	return this.Alias
}

type Snapshot struct {
	ID            string
	GridID        string
	GroupID       string
	CreateTime    time.Time
	LastPrice     float64
	PNL           float64
	GridPNL       float64
	HoldingPNL    float64
	UnrealisedPNL float64
	AssetPairQty  AssetPairQty
	PositionValue float64
	TotalValue    float64
	InitWorth     float64
	BuyCount      int // 买入次数
	SellCount     int // 卖出次数
}

type GridReport struct {
	BuyFilledCount    int
	SaleFilledCount   int
	StopLossGridCount int     // 止损过的网格数
	PNL               float64 // 总收益
	GridPNL           float64 // 网格利润
	HoldingPNL        float64 // 现有持仓收益，即浮动盈亏
	UnrealisedPNL     float64 // 未结算收益
}

type OrderTableRow struct {
	IndexDisplay int
	Qty          float64
	Price        float64
	Delta        float64
	OrderID      string
}

type OrderTable struct {
	LeftColumn  []*OrderTableRow
	RightColumn []*OrderTableRow
}

type GridPreview struct {
	OrderTable     *OrderTable
	ProfitRateLow  float64
	ProfitRateHigh float64
	AssetPairQty   AssetPairQty
	ExtraBaseQty   float64 // 额外的基础币数量, 如果是正数，将卖出；如果是负数，将买入
}

func (this *Grid) InstrumentType() exchange.InstrumentType {
	return this.SymbolCode.InstrumentType()
}

func (this *Grid) OrderType() exchange.OrderType {
	if this.UseStopLimit {
		return exchange.StopLimit
	} else {
		return exchange.Limit
	}
}

func (this *Grid) SetRange(low float64, high float64) {
	this.Range[0] = low
	this.Range[1] = high
}

func (this *Grid) GetAssetWorthInUSDT(assetQty AssetPairQty) (worthInUSDT float64, er error) {
	quoteWorth := 0.0
	baseWorth := 0.0
	if p, err := this.controller.Exchange.GetCurrencyPrice(this.QuoteAsset, 2*time.Second); err != nil {
		er = fmt.Errorf("get asset worth, error: %s", err)
	} else {
		quoteWorth = assetQty.GetQuoteQty() * p
	}
	if p, err := this.controller.Exchange.GetCurrencyPrice(this.BaseAsset, 2*time.Second); err != nil {
		er = fmt.Errorf("get asset worth, error: %s", err)
	} else {
		baseWorth = this.Qty2Size(p, assetQty.GetBaseQty())
	}
	worthInUSDT = quoteWorth + baseWorth
	return
}

func (this Range) Low() float64 {
	return this[0]
}

func (this Range) High() float64 {
	return this[1]
}

// 网格挂单实现：
// 买单：从 BuyOrders 中筛选 IsOpen 的订单展示出来
// 卖单：从 SellOrders 中筛选 IsOpen 的订单展示出来
//
// 成交记录实现：
// BuyOrders 按状态和更新时间排序，然后在 SellOrders 中根据 ExtKeyRefOrderID 确定利润率
// 止盈价格：
// 		SellOrders 中 Category == TakeProfit 的价格
// 止损价格：
// 		SellOrders 中 Category == StopLoss 的价格
// 挂单成交次数：
// 		BuyOrders 中 Category == Buy，Status == Filled 的订单个数
// 套利次数：
// 		SellOrders 中 Category == Sell，Status == Filled 的订单个数

// Order 是 exchange.Order 的一个别名
//
//	ExtKeyRefOrderID string // 参考买单订单号，只有卖单才需要填写
//	ExtKeyGridIndex int // 网格索引，绝对位置，买单 [0, Num), 卖单[0, Num) （可以讨论）
//	ExtKeyCategory string // 订单类别，Buy/Sell/TakeProfit/StopLoss
type Order = exchange.Order

type AssetPairQty [2]float64

func NewAssetPairQty(baseQty, quoteQty float64) AssetPairQty {
	return AssetPairQty{baseQty, quoteQty}
}

func (q AssetPairQty) GetQuoteQty() float64 {
	return q[1]
}

func (q AssetPairQty) GetBaseQty() float64 {
	return q[0]
}

func (q *AssetPairQty) SetQuoteQty(qty float64) {
	q[1] = qty
}

func (q *AssetPairQty) SetBaseQty(qty float64) {
	q[0] = qty
}

func NewAssetQty(qty float64) *AssetQty {
	nowTime := time.Now()
	q := &AssetQty{
		Total:      qty,
		Available:  qty,
		Locked:     0,
		mutex:      &sync.Mutex{},
		UpdateTime: &nowTime,
	}
	return q
}

func (q *AssetQty) Use(qty float64) (available, total float64) {
	q.mutex.Lock()
	defer q.mutex.Unlock()
	q.Available -= qty
	q.Locked += qty
	nowTime := time.Now()
	q.UpdateTime = &nowTime
	return q.Available, q.Total
}

func (q *AssetQty) Return(qty float64) (available, total float64) {
	q.mutex.Lock()
	defer q.mutex.Unlock()
	q.Available += qty
	q.Locked -= qty
	nowTime := time.Now()
	q.UpdateTime = &nowTime
	return q.Available, q.Total
}

type Performance struct {
	Time          *time.Time
	SymbolCode    *exchange.SymbolCode
	Symbol        string
	InitWorth     float64
	Worth         float64
	Position      float64
	PositionInUSD float64
	Opens         int
	Closes        int
	PNL           float64
	GridPNL       float64
	HoldingPNL    float64
	UnrealisedPNL float64
}

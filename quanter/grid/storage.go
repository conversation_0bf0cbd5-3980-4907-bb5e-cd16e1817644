package grid

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type GridStorage struct {
	base.BaseStorage
	Grids      []*Grid
	GridGroups []*GridGroup
	mutex      sync.Mutex
}

func SetupGridStroage(controller *GridController) {
	storage := &GridStorage{
		BaseStorage: base.BaseStorage{
			Controller: controller,
		},
	}
	// 紧接着赋值给 Storager，防止没有赋值前调用 Storager 相关的方法导致崩溃
	storage.Storager = storage

	if ok := storage.ReadFrom(controller.ConfigPath, controller.ID); !ok {
		storage.Grids = []*Grid{}
		storage.GridGroups = []*GridGroup{}
		storage.Assets = map[int64]float64{}
	} else {
		for _, g := range storage.Grids {
			g.controller = controller
			if g.Status == "" {
				if g.FinishTime == nil {
					g.Status = Running
				} else {
					g.Status = Closed
				}
			}

			exchange.FixSymbolCode(g.SymbolCode)
		}

		instrumentTypeMigrated := storage.FixOrderInstrumentType()
		storage.MigrateSpotSymbolCode()
		if instrumentTypeMigrated {
			storage.Save()
		}
	}

	if storage.Assets == nil {
		storage.Assets = map[int64]float64{}
	}

	controller.storage = storage
}

func (s *GridStorage) ReadFrom(configPath, id string) bool {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".grid_storage")
	f, _ := os.OpenFile(path, os.O_RDWR|os.O_CREATE, 0755)
	defer f.Close()
	file, err := io.ReadAll(f)
	if err != nil {
		zlog.Errorf("[%s] read storage file error: %s", s.Controller.GetID(), err.Error())
		return false
	}
	zlog.Infof("[%s] local storage file (%s) loaded", s.Controller.GetID(), path)
	err = json.Unmarshal(file, s)
	if err != nil {
		zlog.Debugf("[%s] json.Unmarshal err: %#v", s.Controller.GetID(), err)
		zlog.Errorf("[%s] read json file error", s.Controller.GetID())
		return false
	}
	return true
}

// 写入本地存储到文件
func (s *GridStorage) SaveTo(configPath string, id string, overwrite bool) error {
	s.mutex.Lock()
	startTime := time.Now()
	defer func() {
		zlog.Infof("[%s] save to storage took %s", s.Controller.GetID(), time.Since(startTime))
		s.mutex.Unlock()
	}()

	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".grid_storage")
	if _, err := os.Stat(path); !os.IsNotExist(err) && !overwrite {
		return fmt.Errorf("[%s] %s already exist, can not overwrite", s.Controller.GetID(), path)
	}

	// backup old storage file, if the old file is valid json
	oldData, err := os.ReadFile(path)
	if err != nil {
		zlog.Errorf("[%s] read old storage file error: %s", s.Controller.GetID(), err)
		return err
	}
	if gjson.Valid(string(oldData)) {
		backupPath := filepath.Join(configPath, id+".grid_storage.bak")
		if err := utils.CopyFile(path, backupPath); err != nil {
			zlog.Errorf("[%s] backup old storage file error: %s", s.Controller.GetID(), err)
			return err
		}
	}

	// Wrap marshal in a single recover
	// marshal 的过程中，如果其中的子结构中的 slices 可能发生变化，导致 crash
	// 因此，需要手工 recover 忽略错误
	data, err := func() (data []byte, err error) {
		defer func() {
			if r := recover(); r != nil {
				err = fmt.Errorf("[%s] panic during storage marshal: %v", s.Controller.GetID(), r)
			}
		}()

		data, err = json.MarshalIndent(s, "", "    ")
		if err != nil {
			return nil, fmt.Errorf("marshal error: %s", err)
		}
		return data, nil
	}()

	if err != nil {
		zlog.Errorf("[%s] marshal storage error: %s", s.Controller.GetID(), err)
		return err
	}

	if err := os.WriteFile(path, data, 0755); err != nil {
		zlog.Errorf("[%s] save storage to file, error: %s", s.Controller.GetID(), err)
		return err
	}
	return nil
}

func (s *GridStorage) getPerformanceFileName(symbolCode *exchange.SymbolCode) string {
	if symbolCode == nil {
		return fmt.Sprintf("%s_perf_ALL.json", s.Controller.GetID())
	} else {
		code := strings.ReplaceAll(symbolCode.Code, ".", "_")
		return fmt.Sprintf("%s_perf_%s.json", s.Controller.GetID(), code)
	}
}

func (s *GridStorage) logPerformance(perf Performance) {
	archivePath := s.getPerformanceFileName(perf.SymbolCode)
	archivePath = path.Join(s.Controller.GetConfigPath(), archivePath)
	f, err := os.OpenFile(archivePath, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0755)
	if err != nil {
		zlog.Errorf("open performance file err: %s", err)
		return
	}
	defer f.Close()

	if data, err := json.Marshal(perf); err != nil {
		zlog.Errorf("save performance failed, marshal error: %s", err)
		return
	} else {
		f.Write(data)
		_, err := f.WriteString("\n")
		if err != nil {
			zlog.Errorf("log performance, write file error: %s", err)
		}
	}
}

func (s *GridStorage) FixOrderInstrumentType() (migrated bool) {
	for _, grid := range s.Grids {
		for _, order := range grid.BuyOrders {
			if tryFixOrderInstrumentType(grid, order) {
				migrated = true
			}
		}
		for _, order := range grid.SellOrders {
			if tryFixOrderInstrumentType(grid, order) {
				migrated = true
			}
		}
	}
	return migrated
}

func tryFixOrderInstrumentType(grid *Grid, order *exchange.Order) (migrated bool) {
	if order.InstrumentType == exchange.UnknownInstrumentType {
		order.InstrumentType = grid.SymbolCode.InstrumentType()
		migrated = true
	}
	if string(order.InstrumentType) == "CNYMarginedFutures" {
		order.InstrumentType = exchange.USDXMarginedFutures
		migrated = true
	}
	if string(order.InstrumentType) == "USDMarginedFutures" {
		order.InstrumentType = exchange.USDXMarginedFutures
		migrated = true
	}
	if string(order.InstrumentType) == "USDTMarginedFutures" {
		order.InstrumentType = exchange.USDXMarginedFutures
		migrated = true
	}
	return migrated
}

func (s *GridStorage) MigrateSpotSymbolCode() {
	for _, grid := range s.Grids {
		if strings.HasSuffix(grid.SymbolCode.Code, "++") {
			grid.SymbolCode.Code = grid.SymbolCode.Code[:len(grid.SymbolCode.Code)-2] + "--"
		}
	}
}

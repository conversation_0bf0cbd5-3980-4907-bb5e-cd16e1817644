package grid

import (
	"fmt"
	"math"
)

type StrategyName string

const StrategyNameArithmetic StrategyName = "Arithmetic"
const StrategyNameArithmeticKeepProfit StrategyName = "ArithmeticKeepProfit"
const StrategyNameGeometric StrategyName = "Geometric"
const StrategyNameGeometricKeepProfit StrategyName = "GeometricKeepProfit"
const StrategyNamePyramid StrategyName = "Pyramid"
const StrategyNamePyramidKeepProfit StrategyName = "PyramidKeepProfit"

type BaseStrategy struct {
	Name            StrategyName
	QuoteQty        float64
	Range           Range
	Num             int
	TakeProfitPrice float64
	StopLossPrice   float64
	KeepProfitRatio float64
	IsManual        bool
	Reverse         bool
}

func (this *BaseStrategy) GetName() StrategyName {
	return this.Name
}

func (this *BaseStrategy) String() string {
	return fmt.Sprintf("[%sGrid, Range(%.f, %f), Num(%d), TP-SL(%.f, %.f), Manual(%v)]", this.GetName(), this.Range[0], this.Range[1], this.Num, this.TakeProfitPrice, this.StopLossPrice, this.IsManual)
}

func (this *BaseStrategy) GetRange() Range {
	return this.Range
}

func (this *BaseStrategy) GetNum() int {
	return this.Num
}

func (this *BaseStrategy) GetQuoteQty() float64 {
	return this.QuoteQty
}

func (this *BaseStrategy) GetTakeProfitPrice() float64 {
	return this.TakeProfitPrice
}

func (this *BaseStrategy) GetStopLossPrice() float64 {
	return this.StopLossPrice
}

func (this *BaseStrategy) GetKeepProfitRatio() float64 {
	return this.KeepProfitRatio
}

func (this *BaseStrategy) GetIsManual() bool {
	return this.IsManual
}

// 等差网格
type ArithmeticStrategy struct {
	BaseStrategy
}

func NewArithmeticStrategy(quoteQty float64, priceLow, priceHigh float64, num int, takeProfitPrice, stopLossPrice float64, reverse bool) *ArithmeticStrategy {
	result := &ArithmeticStrategy{
		BaseStrategy: BaseStrategy{
			Name:            StrategyNameArithmetic,
			Range:           Range{priceLow, priceHigh},
			Num:             num,
			QuoteQty:        quoteQty,
			TakeProfitPrice: takeProfitPrice,
			StopLossPrice:   stopLossPrice,
			IsManual:        true,
			Reverse:         reverse,
		},
	}
	return result
}

func (this *ArithmeticStrategy) GetPriceAndQty() (prices []float64, buyQtys []float64, saleQtys []float64) {
	deltaPrice := (this.Range[1] - this.Range[0]) / float64(this.Num)
	prices = append(prices, this.Range[0])
	for i := 1; i <= this.Num; i++ {
		prices = append(prices, prices[i-1]+deltaPrice)
	}
	qty := this.QuoteQty / ((prices[0] + prices[this.Num-1]) / 2) / float64(this.Num)
	if this.Reverse {
		qty = this.QuoteQty / ((prices[1] + prices[this.Num]) / 2) / float64(this.Num)
	}
	for i := 0; i < this.Num; i++ {
		buyQtys = append(buyQtys, qty)
		saleQtys = append(saleQtys, qty)
	}
	return
}

// 等差留利润网格
type ArithmeticKeepProfitStrategy struct {
	ArithmeticStrategy
}

func NewArithmeticKeepProfitStrategy(quoteQty float64, priceLow, priceHigh float64, num int, keepProfitRatio, takeProfitPrice, stopLossPrice float64, reverse bool) *ArithmeticKeepProfitStrategy {
	s := &ArithmeticKeepProfitStrategy{
		ArithmeticStrategy: *NewArithmeticStrategy(quoteQty, priceLow, priceHigh, num, takeProfitPrice, stopLossPrice, reverse),
	}
	s.KeepProfitRatio = keepProfitRatio
	s.Name = StrategyNameArithmeticKeepProfit
	return s
}

func (this *ArithmeticKeepProfitStrategy) GetPriceAndQty() (prices []float64, buyQtys []float64, saleQtys []float64) {
	prices, buyQtys, saleQtys = this.ArithmeticStrategy.GetPriceAndQty()
	for i := 0; i < this.Num; i++ {
		if this.Reverse {
			buyQty := buyQtys[i]
			profit := (prices[i+1] - prices[i]) * buyQty
			keepQty := (profit * this.KeepProfitRatio) / prices[i]
			buyQty = math.Max(buyQty-keepQty, 0)
			buyQtys[i] = buyQty
		} else {
			saleQty := saleQtys[i]
			profit := (prices[i+1] - prices[i]) * saleQty
			keepQty := (profit * this.KeepProfitRatio) / prices[i+1]
			saleQty = math.Max(saleQty-keepQty, 0)
			saleQtys[i] = saleQty
		}
	}
	return
}

// 等比网格
type GeometricStrategy struct {
	BaseStrategy
}

func NewGeometricStrategy(quoteQty float64, priceLow, priceHigh float64, num int, takeProfitPrice, stopLossPrice float64, reverse bool) *GeometricStrategy {
	result := &GeometricStrategy{
		BaseStrategy: BaseStrategy{
			Name:            StrategyNameGeometric,
			Range:           Range{priceLow, priceHigh},
			Num:             num,
			QuoteQty:        quoteQty,
			TakeProfitPrice: takeProfitPrice,
			StopLossPrice:   stopLossPrice,
			IsManual:        true,
			Reverse:         reverse,
		},
	}
	return result
}

func (this *GeometricStrategy) GetPriceAndQty() (prices []float64, buyQtys []float64, saleQtys []float64) {
	ratio := math.Pow(this.Range.High()/this.Range.Low(), 1.0/float64(this.Num))
	prices = append(prices, this.Range.Low())
	priceSum := prices[0]
	for i := 1; i < this.Num; i++ {
		price := prices[i-1] * ratio
		priceSum += price
		prices = append(prices, price)
	}
	prices = append(prices, prices[this.Num-1]*ratio)
	if this.Reverse {
		priceSum = priceSum - prices[0] + prices[this.Num]
	}
	qty := this.QuoteQty / priceSum
	for i := 0; i < this.Num; i++ {
		buyQtys = append(buyQtys, qty)
		saleQtys = append(saleQtys, qty)
	}
	return
}

// 等比留利润网格
type GeometricKeepProfitStrategy struct {
	GeometricStrategy
}

func NewGeometricKeepProfitStrategy(quoteQty float64, priceLow, priceHigh float64, num int, keepProfitRatio, takeProfitPrice, stopLossPrice float64, reverse bool) *GeometricKeepProfitStrategy {
	s := &GeometricKeepProfitStrategy{
		GeometricStrategy: *NewGeometricStrategy(quoteQty, priceLow, priceHigh, num, takeProfitPrice, stopLossPrice, reverse),
	}
	s.KeepProfitRatio = keepProfitRatio
	s.Name = StrategyNameGeometricKeepProfit
	return s
}

func (this *GeometricKeepProfitStrategy) GetPriceAndQty() (prices []float64, buyQtys []float64, saleQtys []float64) {
	prices, buyQtys, saleQtys = this.GeometricStrategy.GetPriceAndQty()
	for i := 0; i < this.Num; i++ {
		if this.Reverse {
			buyQty := buyQtys[i]
			profit := (prices[i+1] - prices[i]) * buyQty
			keepQty := (profit * this.KeepProfitRatio) / prices[i]
			buyQty = math.Max(buyQty-keepQty, 0)
			buyQtys[i] = buyQty
		} else {
			saleQty := saleQtys[i]
			profit := (prices[i+1] - prices[i]) * saleQty
			keepQty := (profit * this.KeepProfitRatio) / prices[i+1]
			saleQty = math.Max(saleQty-keepQty, 0)
			saleQtys[i] = saleQty
		}
	}
	return
}

// 金字塔网格（等差等金额）
type PyramidStrategy struct {
	BaseStrategy
}

func NewPyramidStrategy(quoteQty float64, priceLow, priceHigh float64, num int, takeProfitPrice, stopLossPrice float64, reverse bool) *PyramidStrategy {
	result := &PyramidStrategy{
		BaseStrategy: BaseStrategy{
			Name:            StrategyNamePyramid,
			Range:           Range{priceLow, priceHigh},
			Num:             num,
			QuoteQty:        quoteQty,
			TakeProfitPrice: takeProfitPrice,
			StopLossPrice:   stopLossPrice,
			IsManual:        true,
			Reverse:         reverse,
		},
	}
	return result
}

func (this *PyramidStrategy) GetPriceAndQty() (prices []float64, buyQtys []float64, saleQtys []float64) {
	deltaPrice := (this.Range[1] - this.Range[0]) / float64(this.Num)
	prices = append(prices, this.Range[0])
	for i := 1; i <= this.Num; i++ {
		prices = append(prices, prices[i-1]+deltaPrice)
	}
	value := this.QuoteQty / float64(this.Num)
	for i := 0; i < this.Num; i++ {
		qty := value / prices[i]
		if this.Reverse {
			qty = value / prices[i+1]
		}
		buyQtys = append(buyQtys, qty)
		saleQtys = append(saleQtys, qty)
	}
	return
}

type PyramidKeepProfitStrategy struct {
	PyramidStrategy
}

func NewPyramidKeepProfitStrategy(quoteQty float64, priceLow, priceHigh float64, num int, keepProfitRatio, takeProfitPrice, stopLossPrice float64, reverse bool) *PyramidKeepProfitStrategy {
	s := &PyramidKeepProfitStrategy{
		PyramidStrategy: *NewPyramidStrategy(quoteQty, priceLow, priceHigh, num, takeProfitPrice, stopLossPrice, reverse),
	}
	s.KeepProfitRatio = keepProfitRatio
	s.Name = StrategyNamePyramidKeepProfit
	return s
}

func (this *PyramidKeepProfitStrategy) GetPriceAndQty() (prices []float64, buyQtys []float64, saleQtys []float64) {
	prices, buyQtys, saleQtys = this.PyramidStrategy.GetPriceAndQty()
	for i := 0; i < this.Num; i++ {
		if this.Reverse {
			buyQty := buyQtys[i]
			profit := (prices[i+1] - prices[i]) * buyQty
			keepQty := (profit * this.KeepProfitRatio) / prices[i]
			buyQty = math.Max(buyQty-keepQty, 0)
			buyQtys[i] = buyQty
		} else {
			saleQty := saleQtys[i]
			profit := (prices[i+1] - prices[i]) * saleQty
			keepQty := (profit * this.KeepProfitRatio) / prices[i+1]
			saleQty = math.Max(saleQty-keepQty, 0)
			saleQtys[i] = saleQty
		}
	}
	return
}

package grid

import (
	"errors"
	"fmt"
	"os"
	"path"
	"strings"

	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/utils"

	"github.com/spf13/viper"
	"github.com/stevedomin/termtable"
)

var ConfigWithoutDiffHeader = []string{"Config", "Value"}

type GridConfig struct {
	SpotFeeRate           float64
	SpotFeeRateLimitBuy   float64 // 如果没有设置，则使用 SpotFeeRate
	SpotFeeRateLimitSell  float64 // 如果没有设置，则使用 SpotFeeRate
	SpotFeeRateMarketBuy  float64 // 如果没有设置，则使用 SpotFeeRate
	SpotFeeRateMarketSell float64 // 如果没有设置，则使用 SpotFeeRate
	SlippageRate          float64
	UseStopLimit          bool
	AutoStopLossRatio     float64 // 自动设置的止损价比例，止损价 = 最后一格 +(-) 每格价差 * AutoStopLossRatio
	PositionTolerance     float64 // 持仓检查容差，默认 2%
	SkipSpotPositionCheck bool
}

type GridControllerConfig struct {
	baseconfig.BaseConfig
	GridConfig
	controller *GridController
}

var GridConfigOptions = &baseconfig.ConfigOptions{
	"SpotFeeRate":           {IsPercent: true},
	"SpotFeeRateLimitBuy":   {IsPercent: true},
	"SpotFeeRateLimitSell":  {IsPercent: true},
	"SpotFeeRateMarketBuy":  {IsPercent: true},
	"SpotFeeRateMarketSell": {IsPercent: true},
	"SlippageRate":          {IsPercent: true},
	"UseStopLimit":          {IsPercent: false},
	"AutoStopLossRatio":     {IsPercent: true},
	"PositionTolerance":     {IsPercent: true},
}

func NewGridControllerConfig(controller *GridController) (*GridControllerConfig, error) {
	if config, err := LoadConfig(controller.ConfigPath, controller.ID); err != nil {
		return nil, err
	} else {
		config.controller = controller
		return config, nil
	}
}

func LoadConfig(configPath, controllerID string) (config *GridControllerConfig, er error) {
	config = &GridControllerConfig{}
	configFilePath := path.Join(configPath, controllerID+".grid.toml")
	if _, err := os.Stat(configFilePath); !os.IsNotExist(err) {
		viper.SetConfigName(controllerID + ".grid")
		viper.AddConfigPath(configPath)
		err := viper.ReadInConfig()
		if err == nil {
			err := viper.Unmarshal(&config)
			if err != nil {
				zlog.Errorf("[%s] unable to decode grid config into struct, %v", controllerID, err)
				return nil, err
			}
			if err := config.Validate(); err != nil {
				zlog.Errorf("[%s] grid config validate error: %s, %v", controllerID, err, config)
				return nil, err
			}
			zlog.Infof("[%s] load config from local file", controllerID)
			return config, nil
		} else { // 如果配置文件存在，但是解析出现错误，报错退出
			zlog.Panicf("[%s] read config file error：%s", controllerID, err)
			return nil, fmt.Errorf("read config error: %v", err)
		}
	} else {
		return nil, err
	}
}

func (g *GridControllerConfig) snapshotGridConfig() *GridConfig {
	s := &GridConfig{
		SpotFeeRate:           g.SpotFeeRate,
		SpotFeeRateLimitBuy:   g.SpotFeeRateLimitBuy,
		SpotFeeRateLimitSell:  g.SpotFeeRateLimitSell,
		SpotFeeRateMarketBuy:  g.SpotFeeRateMarketBuy,
		SpotFeeRateMarketSell: g.SpotFeeRateMarketSell,
		SlippageRate:          g.SlippageRate,
		UseStopLimit:          g.UseStopLimit,
		AutoStopLossRatio:     g.AutoStopLossRatio,
		PositionTolerance:     g.PositionTolerance,
		SkipSpotPositionCheck: g.SkipSpotPositionCheck,
	}
	return s
}

func (g *GridControllerConfig) Validate() error {
	if err := g.BaseConfig.Validate(); err != nil {
		return fmt.Errorf("base config validate error: %v", err)
	}
	return g.GridConfig.Validate()
}

func (g *GridControllerConfig) GetConfigOptions() *baseconfig.ConfigOptions {
	return GridConfigOptions
}

func (g *GridControllerConfig) GetChildValidatableConfig() baseconfig.ValidatableConfig {
	return &g.GridConfig
}

func (g *GridControllerConfig) GetBaseConfig() *baseconfig.BaseConfig {
	return &g.BaseConfig
}

func (g *GridControllerConfig) ToTomlContent(hideSecret bool) string {
	result := "[BaseConfig]\n" +
		g.BaseConfig.ToTomlContent(hideSecret) +
		"\n[GridConfig]\n" +
		fmt.Sprintf(`SpotFeeRate = %v`, g.SpotFeeRate) + "\n" +
		fmt.Sprintf(`SpotFeeRateLimitBuy = %v`, g.SpotFeeRateLimitBuy) + "\n" +
		fmt.Sprintf(`SpotFeeRateLimitSell = %v`, g.SpotFeeRateLimitSell) + "\n" +
		fmt.Sprintf(`SpotFeeRateMarketBuy = %v`, g.SpotFeeRateMarketBuy) + "\n" +
		fmt.Sprintf(`SpotFeeRateMarketSell = %v`, g.SpotFeeRateMarketSell) + "\n" +
		fmt.Sprintf(`SlippageRate = %v`, g.SlippageRate) + "\n" +
		fmt.Sprintf(`UseStopLimit = %v`, g.UseStopLimit) + "\n" +
		fmt.Sprintf(`AutoStopLossRatio = %v`, g.AutoStopLossRatio) + "\n" +
		fmt.Sprintf(`PositionTolerance = %v`, g.PositionTolerance) + "\n" +
		fmt.Sprintf(`SkipSpotPositionCheck = %v`, g.SkipSpotPositionCheck) + "\n"
	return result
}

func NewTable() *termtable.Table {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	return t
}

func (this *GridControllerConfig) ToTable() string {
	apiKey := this.ApiKey
	apiSecret := this.ApiSecret
	apiKey = utils.HideSecret(apiKey)
	apiSecret = utils.HideSecret(apiSecret)

	ts := NewTable()
	ts.SetHeader([]string{"AllowedSymbolPrefixs"})
	ts.AddRow([]string{strings.Join(this.AllowedSymbolPrefixs, ",")})

	t := NewTable()
	t.SetHeader(ConfigWithoutDiffHeader)
	debugStr := "false"
	if this.controller != nil && this.controller.Debug {
		debugStr = "true"
	}
	t.AddRow([]string{"Debug*", debugStr})
	t.AddRow([]string{"", ""})
	t.AddRow([]string{"ExchangeName", fmt.Sprintf(`%v`, this.ExchangeName)})
	t.AddRow([]string{"Host", fmt.Sprintf(`%v`, this.Host)})
	t.AddRow([]string{"ApiKey", fmt.Sprintf(`%v`, apiKey)})
	t.AddRow([]string{"ApiSecret", fmt.Sprintf(`%v`, apiSecret)})

	if exp, _ := this.controller.GetAPIExpireTime(); exp != nil {
		t.AddRow([]string{"ApiKeyExpire", utils.FormatShortTimeStr(exp, true)})
	}

	t.AddRow([]string{"ReleaseBinaryDirPath", fmt.Sprintf(`%v`, this.ReleaseBinaryDirPath)})
	t.AddRow([]string{"LogDirPath", fmt.Sprintf(`%v`, this.LogDirPath)})
	t.AddRow([]string{"IsTestnet", fmt.Sprintf(`%v`, this.IsTestnet)})
	t.AddRow([]string{"ProxyUrl", this.ProxyUrl})
	t.AddRow([]string{"ShowFutureQtyAsValue", fmt.Sprintf(`%v`, this.ShowFutureQtyAsValue)})
	t.AddRow([]string{"MinMarginRatio", fmt.Sprintf(`%v`, this.MinMarginRatio)})
	t.AddRow([]string{"EnableRealtimePrice", fmt.Sprintf(`%v`, this.EnableRealtimePrice)})
	t.AddRow([]string{"USDXSymbol", this.USDXSymbol})
	t.AddRow([]string{"-------------------------", ""})

	for _, row := range this.GetGridConfigRows() {
		t.AddRow(row)
	}
	return fmt.Sprintf("%s\n\n%s", ts.Render(), t.Render())
}

func (this *GridControllerConfig) GetGridConfigRows() (rows [][]string) {
	if baseConfigRows, err := baseconfig.GetTableRows(this.snapshotGridConfig()); err != nil {
		rows = append(rows, []string{"ERROR", fmt.Sprintf("[ERROR!>base config table error: %s]", err)})
		return
	} else {
		rows = append(rows, baseConfigRows...)
	}
	return
}

func GetFirestoreCollectionName(id string) string {
	return fmt.Sprintf("grid||%s", id)
}

func (g *GridControllerConfig) SaveTo(configPath string, id string, overwrite bool) error {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".grid.toml")
	if _, err := os.Stat(path); !os.IsNotExist(err) && !overwrite {
		return fmt.Errorf("[%s] %s already exist, can not overwrite", g.controller.ID, path)
	}
	if err := os.WriteFile(path, []byte(g.ToTomlContent(false)), 0755); err != nil {
		zlog.Errorf("[%s] write grid config %s error: %v", g.controller.ID, id, err)
		return err
	}
	return nil
}

func (g *GridControllerConfig) Save() {
	g.SaveTo(g.controller.ConfigPath, g.controller.ID, true)
}

func (g *GridControllerConfig) Delete() {
	// 删除本地配置
	if err := os.Remove(path.Join(g.controller.ConfigPath, g.controller.ID+".grid.toml")); err != nil {
		g.controller.AlertMsgf("本地配置文件删除失败: %s", err)
		return
	}
}

func (g *GridControllerConfig) GetExchangeName() string {
	return g.ExchangeName
}

func (g *GridControllerConfig) GetHost() string {
	return g.Host
}

func (g *GridControllerConfig) GetApiKey() string {
	return g.ApiKey
}

func (g *GridControllerConfig) GetApiSecret() string {
	return g.ApiSecret
}

func (g *GridConfig) Validate() error {
	feeRates := []float64{g.SpotFeeRate, g.SpotFeeRateLimitBuy, g.SpotFeeRateLimitSell, g.SpotFeeRateMarketBuy, g.SpotFeeRateMarketSell}
	feeRatesName := []string{"SpotFeeRate", "SpotFeeRateLimitBuy", "SpotFeeRateLimitSell", "SpotFeeRateMarketBuy", "SpotFeeRateMarketSell"}

	for i, feeRate := range feeRates {
		if feeRate < 0 || feeRate > 0.003 {
			return fmt.Errorf("%s must in [0, 0.003]", feeRatesName[i])
		}
	}

	if g.SlippageRate < 0 || g.SlippageRate > 0.01 {
		return errors.New("SlippageRate must in [0, 0.01]")
	}

	if g.PositionTolerance == 0 {
		g.PositionTolerance = 0.02
	}

	return nil
}

func (g *GridConfig) GetConfigOptions() *baseconfig.ConfigOptions {
	return GridConfigOptions
}

func (g *GridConfig) GetChildValidatableConfig() baseconfig.ValidatableConfig {
	return nil
}

func (g *GridConfig) GetBaseConfig() *baseconfig.BaseConfig {
	return nil
}

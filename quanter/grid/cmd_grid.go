package grid

import (
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

var SimpleStatusHeader = []string{"Symbol", "ID", "Status", "Strategy", "Range", "Input Worth", "Init Worth", "Num", "Buys", "Sells", "Worth", "Position", "PNL", "Grid PNL", "Holding PNL", "Unr. PNL", "SL Price"}
var DetailStatusHeader = []string{"Symbol", "ID", "Status", "Strategy", "Range", "Input Worth", "Init Worth", "Num", "Buys", "Sells", "Worth", "Position", "PNL", "Grid PNL", "Holding PNL", "Unr. PNL", "SL Price", "TP Price", "Margin Call Price", "Finish Time", "Finish Reason", "Asset", "Create Time", "Init Price", "Stop Limit Order", "Cool Days", "ReEntry Index"}

type StrategyCommand struct {
	CheckExchangeGridCommand
	grid         *Grid
	strategyName StrategyName
}

func (this *StrategyCommand) Prepare() bool {
	symbolCodeStr := this.Args[0]
	reverse := false
	codeStrs := strings.Split(symbolCodeStr, ",")
	if len(codeStrs) == 2 && strings.EqualFold(codeStrs[1], "Reverse") {
		reverse = true
		symbolCodeStr = codeStrs[0]
	}

	var symbolCode *exchange.SymbolCode
	symbol := ""
	if code, err := this.controller.NewSymbolCode(symbolCodeStr); err != nil {
		this.ErrorMsgf("解析品种代码错误，error: %s", err)
		return false
	} else {
		symbolCode = code
	}
	if !this.controller.CheckSymbolPrefixAllowed(symbolCode.Coin()) {
		this.ErrorMsgf("不支持品种 (%s)，仅允许：(%s)", symbolCode.Coin(), utils.SliceStringJoin(this.controller.Config.AllowedSymbolPrefixs, ", ", false))
		return false
	}
	instrumentType := symbolCode.InstrumentType()
	if instrumentType == exchange.Spot {
		if spotSymbol, err := this.controller.Exchange.TranslateSymbolCodeToSpotSymbol(symbolCode); err != nil {
			this.ErrorMsgf("交易所中没有品种代码对应的现货，error: %s", err)
			return false
		} else {
			symbol = spotSymbol
		}
	} else if exchange.SliceContains([]exchange.InstrumentType{
		exchange.USDXMarginedFutures,
	}, instrumentType) {
		if futureSymbol, err := this.controller.Exchange.TranslateSymbolCodeToFutureSymbol(symbolCode); err != nil {
			this.ErrorMsgf("交易所中没有品种代码对应的合约，error: %s", err)
			return false
		} else {
			symbol = futureSymbol
		}
	} else {
		this.ErrorMsgf("不支持品种代码：%s", symbolCodeStr)
		return false
	}

	if reverse && !instrumentType.IsFuture() {
		this.WarnMsgf("Reverse 参数仅支持合约做空网格，当前指令将忽略。")
		reverse = false
	}

	baseQty := 0.0
	quoteQty := 0.0
	priceLow := 0.0
	priceHigh := 0.0
	num := 0
	stopLossPrice := 0.0
	takeProfitPrice := 0.0

	qtyStrs := strings.Split(this.Args[1], ",")
	if q, _, err := utils.ParseFloatOrPercentage(qtyStrs[0], false, false); err != nil {
		this.ErrorMsgf("解析数量出错：%s, error: %s", this.Args[1], err)
		return false
	} else {
		quoteQty = q
	}
	if len(qtyStrs) == 2 {
		// BaseQty 定义为在 QuoteQty 中包含 BaseQty
		// e.g. ETH-USDT 10000,2 表示运行价值 10000 U 的网格，其中已有 2 ETH，则实际投入的 U = 10000 - 2 * 当前价
		if q, _, err := utils.ParseFloatOrPercentage(qtyStrs[1], false, false); err != nil {
			this.ErrorMsgf("解析数量出错：%s, error: %s", this.Args[1], err)
			return false
		} else {
			baseQty = q
		}
	}

	if pl, ph, err := utils.ParseFloatRange(this.Args[2], "~"); err != nil {
		this.ErrorMsgf("解析网格价格范围出错，error: %s", err)
		return false
	} else {
		priceLow = pl
		priceHigh = ph
	}
	if priceLow >= priceHigh {
		this.ErrorMsgf("网格价格范围出错: 最高价需大于最低价")
	}
	if n, err := cast.ToInt64E(this.Args[3]); err != nil {
		this.ErrorMsgf("解析网格数量出错：%s, error: %s", this.Args[3], err)
		return false
	} else {
		num = int(n)
	}

	lastArgsIndex := 4
	keepProfitRatio := 0.0
	if this.strategyName == StrategyNameGeometricKeepProfit ||
		this.strategyName == StrategyNameArithmeticKeepProfit ||
		this.strategyName == StrategyNamePyramidKeepProfit {
		lastArgsIndex = 5
		if q, _, err := utils.ParseFloatOrPercentage(this.Args[4], false, false); err != nil {
			this.ErrorMsgf("解析预留利润比例出错：%s, error: %s", this.Args[4], err)
			return false
		} else {
			keepProfitRatio = q
		}
	}

	useStopLimit := this.controller.Config.UseStopLimit
	if len(this.Args) > lastArgsIndex {
		parts := strings.Split(this.Args[lastArgsIndex], ",")
		if len(parts) != 3 {
			this.ErrorMsgf("解析可选参数格式出错: `%s`", this.Args[lastArgsIndex])
			return false
		}
		if strings.EqualFold(parts[2], "StopLimit") {
			useStopLimit = true
		} else if strings.EqualFold(parts[2], "Limit") {
			useStopLimit = false
		}
		if sp, tp, err := utils.ParseFloatRange(strings.Join(parts[:2], ","), ","); err != nil {
			this.ErrorMsgf("解析止损价和止盈价出错，error:%s", err)
			return false
		} else {
			stopLossPrice = sp
			takeProfitPrice = tp
		}
	}

	if reverse && !useStopLimit {
		this.SendMsgf("做空网格将默认使用 StopLimit 订单")
		useStopLimit = true
	}

	totalQty := quoteQty
	if baseQty != 0 {
		// 全换算成 quote 计算策略总价值
		if v, err := this.controller.BaseQtyInQuoteValue(symbolCode.InstrumentType(), symbol, baseQty); err != nil {
			this.ErrorMsgf("计算 base 价值出错: %s", err)
			return false
		} else {
			if totalQty < v {
				this.ErrorMsgf("base 价值 %.2f 超过总价值 %.2f", v, totalQty)
				return false
			}
			quoteQty = totalQty - v
		}
	}
	var strategy Strateger
	if this.strategyName == StrategyNameGeometric {
		strategy = NewGeometricStrategy(totalQty, priceLow, priceHigh, num, takeProfitPrice, stopLossPrice, reverse)
	} else if this.strategyName == StrategyNameArithmetic {
		strategy = NewArithmeticStrategy(totalQty, priceLow, priceHigh, num, takeProfitPrice, stopLossPrice, reverse)
	} else if this.strategyName == StrategyNameGeometricKeepProfit {
		strategy = NewGeometricKeepProfitStrategy(totalQty, priceLow, priceHigh, num, keepProfitRatio, takeProfitPrice, stopLossPrice, reverse)
	} else if this.strategyName == StrategyNameArithmeticKeepProfit {
		strategy = NewArithmeticKeepProfitStrategy(totalQty, priceLow, priceHigh, num, keepProfitRatio, takeProfitPrice, stopLossPrice, reverse)
	} else if this.strategyName == StrategyNamePyramid {
		strategy = NewPyramidStrategy(totalQty, priceLow, priceHigh, num, takeProfitPrice, stopLossPrice, reverse)
	} else if this.strategyName == StrategyNamePyramidKeepProfit {
		strategy = NewPyramidKeepProfitStrategy(totalQty, priceLow, priceHigh, num, keepProfitRatio, takeProfitPrice, stopLossPrice, reverse)
	}

	asset := NewAssetPairQty(baseQty, quoteQty)
	if g, err := NewGrid(this.controller, symbolCode, symbol, strategy, asset, useStopLimit, reverse, nil); err != nil {
		this.ErrorMsgf("创建网格失败，error: %s", err)
		return false
	} else {
		this.grid = g
	}
	previewStr := this.grid.RenderPreview()
	if len(previewStr) > command.SLACK_MESSAGE_LIMIT {
		this.SendFileMessage(fmt.Sprintf("Grid Preview %s", this.grid.RefID), previewStr, "")
	} else {
		this.SendMsgf("\n%s", previewStr)
	}

	if err := this.grid.checkBalance(); err != nil {
		this.ErrorMsgf("检查余额失败: %s", err)
		return false
	}

	return true
}

func (this *GridController) BaseQtyInQuoteValue(instrumentType exchange.InstrumentType, symbol string, qty float64) (float64, error) {
	lastPrice, err := this.Exchange.GetLastPrice(instrumentType, symbol, false)
	if err != nil {
		return 0, err
	}

	if instrumentType == exchange.Spot {
		return lastPrice * qty, nil
	}

	if size, err := this.Exchange.Qty2Size(instrumentType, symbol, lastPrice, qty); err != nil {
		return 0, err
	} else {
		return size, nil
	}
}

func (this *StrategyCommand) Do() bool {
	this.controller.Grids = append(this.controller.Grids, this.grid)
	this.controller.storage.Grids = append(this.controller.storage.Grids, this.grid)
	this.controller.storage.Save()
	this.grid.Start()
	return true
}

type ArithmeticGridCommand struct {
	StrategyCommand
}

func NewArithmeticGridCommand(controller *GridController) *ArithmeticGridCommand {
	cmd := &ArithmeticGridCommand{
		StrategyCommand: StrategyCommand{
			CheckExchangeGridCommand: CheckExchangeGridCommand{
				Command: command.Command{
					Name:            "arithmetic",
					Alias:           []string{"a"},
					Instruction:     "`.arithmetic SymbolCode[,Reverse] QuoteQty[,BaseQty] PriceLow~PriceHigh NumOfGrid StopLossPrice,TakeProfitPrice,Limit/StopLimit[可选]` 新增等差网格",
					RequiresConfirm: true,
					ArgMin:          4,
					ArgMax:          5,
					AuthcodePos:     -1,
				},
				controller: controller,
			},
			grid:         nil,
			strategyName: StrategyNameArithmetic,
		},
	}
	return cmd
}

func (this *ArithmeticGridCommand) Do() bool {
	if this.StrategyCommand.Do() {
		this.SendMsgf("等差网格 %s 启动成功。", this.grid.RefID)
		return true
	} else {
		return false
	}
}

type ArithmeticKeepProfitGridCommand struct {
	StrategyCommand
}

func NewArithmeticKeepProfitGridCommand(controller *GridController) *ArithmeticKeepProfitGridCommand {
	cmd := &ArithmeticKeepProfitGridCommand{
		StrategyCommand: StrategyCommand{
			CheckExchangeGridCommand: CheckExchangeGridCommand{
				Command: command.Command{
					Name:            "arithmeticKeepProfit",
					Alias:           []string{"ak"},
					Instruction:     "`.arithmeticKeepProfit SymbolCode[,Reverse] QuoteQty[,BaseQty] PriceLow~PriceHigh NumOfGrid KeepProfitRatio StopLossPrice,TakeProfitPrice,Limit/StopLimit[可选]` 新增等差预留利润网格",
					RequiresConfirm: true,
					ArgMin:          5,
					ArgMax:          6,
					AuthcodePos:     -1,
				},
				controller: controller,
			},
			grid:         nil,
			strategyName: StrategyNameArithmeticKeepProfit,
		},
	}
	return cmd
}

func (this *ArithmeticKeepProfitGridCommand) Do() bool {
	if this.StrategyCommand.Do() {
		this.SendMsgf("等差预留利润网格 %s 启动成功。", this.grid.RefID)
		return true
	} else {
		return false
	}
}

type GeometricGridCommand struct {
	StrategyCommand
}

func NewGeometricGridCommand(controller *GridController) *GeometricGridCommand {
	cmd := &GeometricGridCommand{
		StrategyCommand: StrategyCommand{
			CheckExchangeGridCommand: CheckExchangeGridCommand{
				Command: command.Command{
					Name:            "geometric",
					Alias:           []string{"g"},
					Instruction:     "`.geometric SymbolCode[,Reverse] QuoteQty[,BaseQty] PriceLow~PriceHigh NumOfGrid StopLossPrice,TakeProfitPrice,Limit/StopLimit[可选]` 新增等比网格",
					RequiresConfirm: true,
					ArgMin:          4,
					ArgMax:          5,
					AuthcodePos:     -1,
				},
				controller: controller,
			},
			grid:         nil,
			strategyName: StrategyNameGeometric,
		},
	}
	return cmd
}

func (this *GeometricGridCommand) Do() bool {
	if this.StrategyCommand.Do() {
		this.SendMsgf("等比网格 %s 启动成功。", this.grid.RefID)
		return true
	} else {
		return false
	}
}

type GeometricKeepProfitGridCommand struct {
	StrategyCommand
}

func NewGeometricKeepProfitGridCommand(controller *GridController) *GeometricKeepProfitGridCommand {
	cmd := &GeometricKeepProfitGridCommand{
		StrategyCommand: StrategyCommand{
			CheckExchangeGridCommand: CheckExchangeGridCommand{
				Command: command.Command{
					Name:            "geometricKeepProfit",
					Alias:           []string{"gk"},
					Instruction:     "`.geometricKeepProfit SymbolCode[,Reverse] QuoteQty[,BaseQty] PriceLow~PriceHigh NumOfGrid KeepProfitRatio StopLossPrice,TakeProfitPrice,Limit/StopLimit[可选]` 新增等比预留利润网格",
					RequiresConfirm: true,
					ArgMin:          5,
					ArgMax:          6,
					AuthcodePos:     -1,
				},
				controller: controller,
			},
			grid:         nil,
			strategyName: StrategyNameGeometricKeepProfit,
		},
	}
	return cmd
}

func (this *GeometricKeepProfitGridCommand) Do() bool {
	if this.StrategyCommand.Do() {
		this.SendMsgf("等比预留利润网格 %s 启动成功。", this.grid.RefID)
		return true
	} else {
		return false
	}
}

type PyramidGridCommand struct {
	StrategyCommand
}

func NewPyramidGridCommand(controller *GridController) *PyramidGridCommand {
	cmd := &PyramidGridCommand{
		StrategyCommand: StrategyCommand{
			CheckExchangeGridCommand: CheckExchangeGridCommand{
				Command: command.Command{
					Name:            "pyramid",
					Alias:           []string{"pr"},
					Instruction:     "`.pyramid SymbolCode[,Reverse] QuoteQty[,BaseQty] PriceLow~PriceHigh NumOfGrid StopLossPrice,TakeProfitPrice,Limit/StopLimit[可选]` 新增金字塔网格",
					RequiresConfirm: true,
					ArgMin:          4,
					ArgMax:          5,
					AuthcodePos:     -1,
				},
				controller: controller,
			},
			grid:         nil,
			strategyName: StrategyNamePyramid,
		},
	}
	return cmd
}

func (this *PyramidGridCommand) Do() bool {
	if this.StrategyCommand.Do() {
		this.SendMsgf("金字塔网格 %s 启动成功。", this.grid.RefID)
		return true
	} else {
		return false
	}
}

type PyramidKeepProfitGridCommand struct {
	StrategyCommand
}

func NewPyramidKeepProfitGridCommand(controller *GridController) *PyramidKeepProfitGridCommand {
	cmd := &PyramidKeepProfitGridCommand{
		StrategyCommand: StrategyCommand{
			CheckExchangeGridCommand: CheckExchangeGridCommand{
				Command: command.Command{
					Name:            "pyramidKeepProfit",
					Alias:           []string{"prk"},
					Instruction:     "`.pyramidKeepProfit SymbolCode[,Reverse] QuoteQty[,BaseQty] PriceLow~PriceHigh NumOfGrid KeepProfitRatio StopLossPrice,TakeProfitPrice,Limit/StopLimit[可选]` 新增金字塔预留利润网格",
					RequiresConfirm: true,
					ArgMin:          5,
					ArgMax:          6,
					AuthcodePos:     -1,
				},
				controller: controller,
			},
			grid:         nil,
			strategyName: StrategyNamePyramidKeepProfit,
		},
	}
	return cmd
}

func (this *PyramidKeepProfitGridCommand) Do() bool {
	if this.StrategyCommand.Do() {
		this.SendMsgf("金字塔预留利润网格 %s 启动成功。", this.grid.RefID)
		return true
	} else {
		return false
	}
}

type InverseHedgedGridCommand struct {
	CheckExchangeGridCommand
	gridShort *Grid
	gridLong  *Grid
}

func NewInverseHedgedGridCommand(controller *GridController) *InverseHedgedGridCommand {
	cmd := &InverseHedgedGridCommand{
		CheckExchangeGridCommand: CheckExchangeGridCommand{
			Command: command.Command{
				Name:            "inverseHedgedGrid",
				Alias:           []string{"ihg"},
				Instruction:     "`.inverseHedgedGrid StrategyName SymbolCode QuoteQty[,BaseQty] PriceLow~PriceHigh NumOfGrid KeepProfitRatio[可选]` 新增多空网格组",
				RequiresConfirm: true,
				ArgMin:          5,
				ArgMax:          6,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *InverseHedgedGridCommand) Prepare() bool {
	// 清理上一个命令的数据
	this.gridShort = nil
	this.gridLong = nil

	strategyNameStr := this.Args[0]
	var strategyName StrategyName
	allStrategies := "arithmetic/arithmeticKeepProfit/geometric/geometricKeepProfit/pyramid/pyramidKeepProfit"
	if !strings.Contains(allStrategies, strategyNameStr) {
		this.ErrorMsgf("不支持 Strategy: (%s)。仅支持：%s", strategyNameStr, allStrategies)
		return false
	}
	if strings.EqualFold(strategyNameStr, "arithmetic") {
		strategyName = StrategyNameArithmetic
	} else if strings.EqualFold(strategyNameStr, "arithmeticKeepProfit") {
		strategyName = StrategyNameArithmeticKeepProfit
	} else if strings.EqualFold(strategyNameStr, "geometric") {
		strategyName = StrategyNameGeometric
	} else if strings.EqualFold(strategyNameStr, "geometricKeepProfit") {
		strategyName = StrategyNameGeometricKeepProfit
	} else if strings.EqualFold(strategyNameStr, "pyramid") {
		strategyName = StrategyNamePyramid
	} else if strings.EqualFold(strategyNameStr, "pyramidKeepProfit") {
		strategyName = StrategyNamePyramidKeepProfit
	}

	var symbolCode *exchange.SymbolCode
	if code, err := this.controller.NewSymbolCode(this.Args[1]); err != nil {
		this.ErrorMsgf("解析品种代码错误，error: %s", err)
		return false
	} else {
		symbolCode = code
	}

	if !symbolCode.IsFuture() {
		this.ErrorMsgf("做空网格仅支持现货")
	}

	symbol := ""
	instrumentType := symbolCode.InstrumentType()
	if exchange.SliceContains([]exchange.InstrumentType{
		exchange.USDXMarginedFutures,
	}, instrumentType) {
		if futureSymbol, err := this.controller.Exchange.TranslateSymbolCodeToFutureSymbol(symbolCode); err != nil {
			this.ErrorMsgf("交易所中没有品种代码对应的合约，error: %s", err)
			return false
		} else {
			symbol = futureSymbol
		}
	} else {
		this.ErrorMsgf("不支持品种代码：%s", symbolCode)
		return false
	}

	baseQty := 0.0
	quoteQty := 0.0
	priceLow := 0.0
	priceHigh := 0.0
	num := 0

	qtyStrs := strings.Split(this.Args[2], ",")
	if q, _, err := utils.ParseFloatOrPercentage(qtyStrs[0], false, false); err != nil {
		this.ErrorMsgf("解析数量出错：%s, error: %s", this.Args[1], err)
		return false
	} else {
		quoteQty = q
	}
	if len(qtyStrs) == 2 {
		if q, _, err := utils.ParseFloatOrPercentage(qtyStrs[1], false, false); err != nil {
			this.ErrorMsgf("解析数量出错：%s, error: %s", this.Args[1], err)
			return false
		} else {
			baseQty = q
		}
	}

	if pl, ph, err := utils.ParseFloatRange(this.Args[3], "~"); err != nil {
		this.ErrorMsgf("解析网格价格范围出错，error: %s", err)
		return false
	} else {
		priceLow = pl
		priceHigh = ph
	}
	if priceLow >= priceHigh {
		this.ErrorMsgf("网格价格范围出错: 最高价需大于最低价")
	}
	if n, err := cast.ToInt64E(this.Args[4]); err != nil {
		this.ErrorMsgf("解析网格数量出错：%s, error: %s", this.Args[4], err)
		return false
	} else {
		num = int(n)
	}

	keepProfitRatio := 0.0
	if len(this.Args) == 6 {
		if q, _, err := utils.ParseFloatOrPercentage(this.Args[5], false, false); err != nil {
			this.ErrorMsgf("解析预留利润比例出错：%s, error: %s", this.Args[4], err)
			return false
		} else {
			keepProfitRatio = q
		}
	}

	priceMid := (priceLow + priceHigh) / 2

	totalQty := quoteQty
	if baseQty != 0 {
		// 全换算成 quote 计算策略总价值
		if v, err := this.controller.BaseQtyInQuoteValue(symbolCode.InstrumentType(), symbol, baseQty); err != nil {
			this.ErrorMsgf("计算 base 价值出错: %s", err)
			return false
		} else {
			if totalQty < v {
				this.ErrorMsgf("base 价值 %.2f 超过总价值 %.2f", v, totalQty)
				return false
			}
			quoteQty = totalQty - v
		}
	}
	var strategyShort Strateger
	var strategyLong Strateger
	if strategyName == StrategyNameGeometric {
		strategyShort = NewGeometricStrategy(totalQty, priceMid, priceHigh, num, 0, 0, true)
		strategyLong = NewGeometricStrategy(totalQty, priceLow, priceMid, num, 0, 0, false)
	} else if strategyName == StrategyNameArithmetic {
		strategyShort = NewArithmeticStrategy(totalQty, priceMid, priceHigh, num, 0, 0, true)
		strategyLong = NewArithmeticStrategy(totalQty, priceLow, priceMid, num, 0, 0, false)
	} else if strategyName == StrategyNameGeometricKeepProfit {
		strategyShort = NewGeometricKeepProfitStrategy(totalQty, priceMid, priceHigh, num, keepProfitRatio, 0, 0, true)
		strategyLong = NewGeometricKeepProfitStrategy(totalQty, priceLow, priceMid, num, keepProfitRatio, 0, 0, false)
	} else if strategyName == StrategyNameArithmeticKeepProfit {
		strategyShort = NewArithmeticKeepProfitStrategy(totalQty, priceMid, priceHigh, num, keepProfitRatio, 0, 0, true)
		strategyLong = NewArithmeticKeepProfitStrategy(totalQty, priceLow, priceMid, num, keepProfitRatio, 0, 0, false)
	} else if strategyName == StrategyNamePyramid {
		strategyShort = NewPyramidStrategy(totalQty, priceMid, priceHigh, num, 0, 0, true)
		strategyLong = NewPyramidStrategy(totalQty, priceLow, priceMid, num, 0, 0, false)
	} else if strategyName == StrategyNamePyramidKeepProfit {
		strategyShort = NewPyramidKeepProfitStrategy(totalQty, priceMid, priceHigh, num, keepProfitRatio, 0, 0, true)
		strategyLong = NewPyramidKeepProfitStrategy(totalQty, priceLow, priceMid, num, keepProfitRatio, 0, 0, false)
	}

	var assetLong, assetShort AssetPairQty
	if baseQty > 0 {
		assetLong = NewAssetPairQty(baseQty, quoteQty)
		assetShort = NewAssetPairQty(0, totalQty)
	} else {
		assetLong = NewAssetPairQty(0, totalQty)
		assetShort = NewAssetPairQty(baseQty, quoteQty)
	}

	if g, err := NewGrid(this.controller, symbolCode, symbol, strategyShort, assetShort, true, true, nil); err != nil {
		this.ErrorMsgf("创建网格失败，error: %s", err)
		return false
	} else {
		this.gridShort = g
	}

	previewStr := this.gridShort.RenderPreview()
	if len(previewStr) > command.SLACK_MESSAGE_LIMIT {
		this.SendFileMessage(fmt.Sprintf("Grid Short Preview %s", this.gridShort.RefID), previewStr, "")
	} else {
		this.SendMsgf("\n%s", previewStr)
	}

	if g, err := NewGrid(this.controller, symbolCode, symbol, strategyLong, assetLong, true, false, nil); err != nil {
		this.ErrorMsgf("创建网格失败，error: %s", err)
		return false
	} else {
		this.gridLong = g
	}

	previewStr = this.gridLong.RenderPreview()
	if len(previewStr) > command.SLACK_MESSAGE_LIMIT {
		this.SendFileMessage(fmt.Sprintf("Grid Short Preview %s", this.gridLong.RefID), previewStr, "")
	} else {
		this.SendMsgf("\n%s", previewStr)
	}

	if err := this.gridShort.checkBalance(); err != nil {
		this.ErrorMsgf("检查余额失败: %s", err)
		return false
	}
	if err := this.gridLong.checkBalance(); err != nil {
		this.ErrorMsgf("检查余额失败: %s", err)
		return false
	}

	return true
}

func (this *InverseHedgedGridCommand) Do() bool {
	this.controller.Grids = append(this.controller.Grids, this.gridLong)
	this.controller.storage.Grids = append(this.controller.storage.Grids, this.gridLong)
	this.controller.Grids = append(this.controller.Grids, this.gridShort)
	this.controller.storage.Grids = append(this.controller.storage.Grids, this.gridShort)
	this.controller.storage.Save()

	group := &GridGroup{
		RefID:   exchange.NewRandomID(),
		GridIDs: []string{this.gridShort.RefID, this.gridLong.RefID},
		Type:    GroupTypeInverseHedged,
	}
	this.controller.GridGroups = append(this.controller.GridGroups, group)
	this.controller.storage.GridGroups = append(this.controller.storage.GridGroups, group)

	this.SendMsgf("InverseHedged 网格组 %s 创建成功。", group.RefID)

	this.gridLong.Start()
	this.gridShort.Start()
	return true
}

type SetMarginCallPriceGridCommand struct {
	command.Command
	controller *GridController
	grid       *Grid
	price      float64
}

type GroupGridCommand GridCommand

func NewGroupGridCommand(controller *GridController) *GroupGridCommand {
	cmd := &GroupGridCommand{
		Command: command.Command{
			Name: "group",
			Instructions: []string{
				"`.group status [all/SymbolCode/groupID,gridID…]` 查看网格组状态",
				"`.group history` 查看网格组历史",
				"`.group create GridID1,GridID2... AuthCode` 创建网格组",
				"`.group delete GroupID/GroupAlias AuthCode` 删除网格组",
			},
		},
		controller: controller,
	}
	return cmd
}

func (this *GroupGridCommand) Do() bool {
	args := this.GetArgs()
	subcommand := this.GetSubcommand()
	if subcommand == "" {
		subcommand = "status"
	}
	switch subcommand {
	case "status":
		var groups []*GridGroup
		if len(args) == 0 || strings.EqualFold(args[0], "all") {
			groups = this.controller.GridGroups
		} else if len(args) > 0 {
			isSymbolCode := false
			if symbolCode, err := exchange.NewSymbolCode(args[0], ""); err == nil {
				for _, gGroup := range this.controller.GridGroups {
					for _, id := range gGroup.GridIDs {
						grid := this.controller.GetGridByID(id, false)
						if grid != nil && grid.SymbolCode.Code == symbolCode.Code {
							groups = append(groups, gGroup)
							isSymbolCode = true
							break
						}
					}
				}
			}

			if !isSymbolCode {
				ids := strings.Split(args[0], ",")
				for _, id := range ids {
					group := this.controller.GetGroupByID(id, false)
					if group == nil {
						for _, gGroup := range this.controller.GridGroups {
							if utils.SliceContains(gGroup.GridIDs, id) {
								group = gGroup
								break
							}
						}
					}
					if group == nil {
						this.ErrorMsgf("没有找到网格组 %s", id)
						continue
					}
					groups = append(groups, group)
				}
			}
		}
		this.SendMsgf("```%s```", this.controller.RenderGroupStatus(groups, this.IsMore(), false))
		return true
	case "history":
		this.SendMsgf("```%s```", this.controller.RenderGroupStatus(this.controller.GridGroups, this.IsMore(), true))
	case "create":
		gridIDs := strings.Split(args[0], ",")
		symbol := ""
		for _, gridID := range gridIDs {
			grid := this.controller.GetGridByID(gridID, false)
			if grid == nil {
				this.ErrorMsgf("没有找到网格 %s", this.Args[0])
				return false
			}
			if symbol == "" {
				symbol = grid.Symbol
			} else if symbol != grid.Symbol {
				this.ErrorMsgf("网格组品种需相同")
				return false
			}
		}
		group := &GridGroup{
			RefID:   exchange.NewRandomID(),
			GridIDs: gridIDs,
		}
		this.controller.GridGroups = append(this.controller.GridGroups, group)
		this.controller.storage.GridGroups = append(this.controller.storage.GridGroups, group)
		this.controller.storage.Save()
		this.SendMsgf("网格组 %s 创建成功。", group.RefID)
		return true
	case "delete":
		idOrAlias := args[0]
		for i, group := range this.controller.GridGroups {
			if group.RefID == idOrAlias || group.Alias == idOrAlias {
				this.controller.GridGroups = append(this.controller.GridGroups[:i], this.controller.GridGroups[i+1:]...)
				this.controller.storage.GridGroups = this.controller.GridGroups
				this.controller.storage.Save()
				this.SendMsgf("网格组 %s 删除成功。", idOrAlias)
				return true
			}
		}
		this.ErrorMsgf("没有找到网格组 %s", idOrAlias)
		return false
	}
	return true
}

func NewSetMarginCallPriceGridCommand(controller *GridController) *SetMarginCallPriceGridCommand {
	cmd := &SetMarginCallPriceGridCommand{
		Command: command.Command{
			Name:            "setMarginCallPrice",
			Alias:           []string{"smcp"},
			Instruction:     "`.setMarginCallPrice GridID Price` 设置合约保证金报警价格",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *SetMarginCallPriceGridCommand) Prepare() bool {
	gridID := this.Args[0]
	grid := this.controller.GetGridByID(gridID, true)
	if grid == nil {
		this.ErrorMsgf("没有找到网格 %s", gridID)
		return false
	}
	this.grid = grid

	if p, _, err := utils.ParseFloatOrPercentage(this.Args[1], false, false); err != nil {
		this.ErrorMsgf("解析价格出错：%s, error: %s", this.Args[1], err)
		return false
	} else {
		this.price = p
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Symbol", "Input Worth", "PNL", "SL Price", "Margin Call Price"})
	row := []string{}
	row = append(row, grid.GetDisplayID(false))
	if grid.Reverse {
		row = append(row, fmt.Sprintf("%s [Short]", grid.SymbolCode.Code))
	} else {
		row = append(row, grid.SymbolCode.Code)
	}
	row = append(row, grid.GetAssetPairQtyString(grid.InitQty))
	row = append(row, fmt.Sprintf("%.2f (%.2f%%)", grid.Report.PNL, grid.Report.PNL*100/math.Abs(grid.InitWorth))) // PNL
	row = append(row, grid.formatPrice(grid.StopLossPrice))
	row = append(row, fmt.Sprintf("%s -> %s", grid.formatPrice(grid.MarginCallPrice), grid.formatPrice(this.price)))
	t.AddRow(row)
	this.SendMsgf("```%s```", t.Render())

	return true
}

func (this *SetMarginCallPriceGridCommand) Do() bool {
	this.grid.MarginCallPrice = this.price
	this.grid.controller.storage.Save()
	this.SendMsgf("设置成功")
	return true
}

type SetCoolGridCommand struct {
	command.Command
	controller   *GridController
	grids        []*Grid
	days         float64
	reEntryIndex int
	reset        bool
}

func NewSetCoolGridCommand(controller *GridController) *SetCoolGridCommand {
	cmd := &SetCoolGridCommand{
		Command: command.Command{
			Name:            "setCool",
			Instruction:     "`.setCool GridID1,GridID2(GroupIDs) days reEntryIndex reset[可选]` 设置止损后冷却天数及重新进入的网格(价格 index), reset 为重置冷却时间（仅冷却中有效）",
			RequiresConfirm: true,
			ArgMin:          3,
			ArgMax:          4,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *SetCoolGridCommand) Prepare() bool {
	ids := strings.Split(this.Args[0], ",")
	this.grids = []*Grid{}
	for _, id := range ids {
		grids := this.controller.GetGridsByID(id, false)
		if len(grids) == 0 {
			this.ErrorMsgf("没有找到网格 %s", id)
			continue
		}
		this.grids = append(this.grids, grids...)
	}

	daysStr := this.Args[1]

	if n, err := strconv.ParseFloat(daysStr, 64); err != nil {
		this.ErrorMsgf("解析 hours 出错: %s", err)
		return false
	} else {
		this.days = n
	}

	if n, err := cast.ToInt64E(this.Args[2]); err != nil {
		this.ErrorMsgf("解析 reEntryIndex 出错: %s", err)
		return false
	} else {
		this.reEntryIndex = int(n)
	}

	if len(this.Args) == 4 && strings.EqualFold(this.Args[3], "reset") {
		this.reset = true
	}

	gridDetails := "[no grids]"
	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Symbol", "Input Worth", "PNL", "SL Price", "Cool Days", "Re-entry Index", "Reset"})
	for _, grid := range this.grids {
		row := []string{}
		row = append(row, grid.GetDisplayID(false))
		if grid.Reverse {
			row = append(row, fmt.Sprintf("%s [Short]", grid.SymbolCode.Code))
		} else {
			row = append(row, grid.SymbolCode.Code)
		}
		row = append(row, grid.GetAssetPairQtyString(grid.InitQty))
		row = append(row, fmt.Sprintf("%.2f (%.2f%%)", grid.Report.PNL, grid.Report.PNL*100/math.Abs(grid.InitWorth))) // PNL
		row = append(row, grid.formatPrice(grid.StopLossPrice))
		if grid.CoolMinutes > 0 {
			row = append(row, fmt.Sprintf("%.2f -> %.2f", float64(grid.CoolMinutes)/60/24, this.days))
		} else {
			row = append(row, fmt.Sprintf("%.2f", this.days))
		}
		if grid.CoolReEntryIndex > 0 {
			row = append(row, fmt.Sprintf("%d -> %d", grid.CoolReEntryIndex, this.reEntryIndex))
		} else {
			row = append(row, fmt.Sprintf("%d", this.reEntryIndex))
		}

		if this.reset {
			row = append(row, "Yes")
		} else {
			row = append(row, "")
		}
		t.AddRow(row)
	}
	if len(t.Rows) > 1 {
		gridDetails = t.Render()
	}
	this.SendMsgf("```%s```", gridDetails)
	return true
}

func (this *SetCoolGridCommand) Do() bool {
	for _, grid := range this.grids {
		grid.CoolMinutes = int(this.days * 24 * 60)
		grid.CoolReEntryIndex = this.reEntryIndex

		if this.reset && grid.Status == Cooling {
			t := time.Now().Add(time.Duration(grid.CoolMinutes) * time.Minute)
			grid.CoolEndTime = &t
			this.SendMsgf("网格 %s 冷却时间已重置到 %s", grid.FullID(), utils.FormatShortTimeStr(grid.CoolEndTime, false))
		}

		this.controller.storage.Save()
		this.SendMsgf("网格 [%s] 设置完成", grid.RefID)
	}
	return true
}

type ReportGridCommand GridCommand

func NewReportGridCommand(controller *GridController) *ReportGridCommand {
	cmd := &ReportGridCommand{
		Command: command.Command{
			Name:            "report",
			Alias:           []string{"r"},
			Instruction:     "`.report GridID` 打印某个网格的报告",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ReportGridCommand) Do() bool {
	gridID := this.Args[0]
	grid := this.controller.GetGridByID(gridID, true)
	if grid == nil {
		this.ErrorMsgf("没有找到网格 %s", gridID)
		return false
	}

	report := grid.RenderReport(this.IsMore())
	this.SendMsgf("```%s```", report)
	return true
}

type SnapshotsGridCommand GridCommand

func NewSnapshotsGridCommand(controller *GridController) *SnapshotsGridCommand {
	cmd := &SnapshotsGridCommand{
		Command: command.Command{
			Name:            "snapshots",
			Alias:           []string{"ss"},
			Instruction:     "`.snapshots GridID/GroupID [daily/exactTime]` 打印某个网格或网格组的快照",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *SnapshotsGridCommand) Do() bool {
	args := this.Args
	gridOrGroupID := args[0]

	refID := gridOrGroupID
	grid := this.controller.GetGridByID(gridOrGroupID, false)
	if grid == nil {
		group := this.controller.GetGroupByID(gridOrGroupID, false)
		if group == nil {
			this.ErrorMsgf("没有找到网格组 %s", gridOrGroupID)
			return false
		}
		refID = group.RefID
	} else {
		refID = grid.RefID
	}

	var exactTime *time.Time
	isDaily := false
	if len(this.Args) == 2 {
		timeStr := args[1]
		if strings.EqualFold(timeStr, "daily") {
			isDaily = true
		} else {
			exactTime = utils.ParseTimeBeijing(timeStr)
			if exactTime == nil {
				this.ErrorMsgf("解析时间失败: %s", timeStr)
				return false
			}
		}
	}

	snapshots := this.controller.LookupSnapshots(refID, "", exactTime, 0)
	if isDaily {
		snapshots = this.controller.FilterDailySnapshots(snapshots)
	}
	result := this.controller.RenderSnapshots(snapshots)
	this.SendMsgf("```%s```", result)
	return true
}

func (this *GridController) FilterDailySnapshots(snapshots []*Snapshot) []*Snapshot {
	// sort by create time, from new to old
	sort.Slice(snapshots, func(i, j int) bool {
		return snapshots[i].CreateTime.After(snapshots[j].CreateTime)
	})
	lastDay := ""
	keptSnapshots := []*Snapshot{}
	for _, snapshot := range snapshots {
		// 如果创建时间是新的一天，则记录
		// timeStr: 2025-01-15T15:04:05Z08:00
		timeStr := utils.FormatToBeijingTimeStr(snapshot.CreateTime)
		currentDay := timeStr[:10] // 2025-01-15，只取日期
		if lastDay == "" || currentDay != lastDay {
			keptSnapshots = append(keptSnapshots, snapshot)
			lastDay = currentDay
		}
	}
	return keptSnapshots
}

func (this *GridController) RenderSnapshots(snapshots []*Snapshot) string {
	// sort by create time
	sort.Slice(snapshots, func(i, j int) bool {
		return snapshots[i].CreateTime.After(snapshots[j].CreateTime)
	})
	t := exchange.NewTable()
	t.SetHeader([]string{"Time", "ID", "GridID", "GroupID", "LastPrice", "PNL", "GridPNL", "HoldingPNL", "UnrealisedPNL", "AssetQty", "Position", "Worth", "Buys", "Sells"})
	for _, snapshot := range snapshots {
		row := []string{}
		row = append(row, utils.FormatShortTimeStr(&snapshot.CreateTime, false))
		row = append(row, snapshot.ID)
		row = append(row, snapshot.GridID)
		row = append(row, snapshot.GroupID)
		row = append(row, utils.FormatNum(snapshot.LastPrice, 8))
		pnlRatioStr := "-"
		gridPNLStr := "-"
		holdingPNLStr := "-"
		if snapshot.InitWorth > 0 {
			pnlRatio := snapshot.PNL / snapshot.InitWorth
			pnlRatioStr = fmt.Sprintf("%.2f%%", pnlRatio*100)
			gridPNL := snapshot.GridPNL / snapshot.InitWorth
			gridPNLStr = fmt.Sprintf("%.2f%%", gridPNL*100)
			holdingPNL := snapshot.HoldingPNL / snapshot.InitWorth
			holdingPNLStr = fmt.Sprintf("%.2f%%", holdingPNL*100)
		}
		row = append(row, fmt.Sprintf("%.2f (%s)", snapshot.PNL, pnlRatioStr))
		row = append(row, fmt.Sprintf("%.2f (%s)", snapshot.GridPNL, gridPNLStr))
		row = append(row, fmt.Sprintf("%.2f (%s)", snapshot.HoldingPNL, holdingPNLStr))
		row = append(row, fmt.Sprintf("%.2f", snapshot.UnrealisedPNL))
		row = append(row, fmt.Sprintf("%.4f / %.2f USDX", snapshot.AssetPairQty.GetBaseQty(), snapshot.AssetPairQty.GetQuoteQty()))
		row = append(row, fmt.Sprintf("%.2f", snapshot.PositionValue))
		row = append(row, fmt.Sprintf("%.2f", snapshot.TotalValue))
		row = append(row, fmt.Sprintf("%d", snapshot.BuyCount))
		row = append(row, fmt.Sprintf("%d", snapshot.SellCount))
		t.AddRow(row)
	}
	return t.Render()
}

type gridStopLoss struct {
	grid     *Grid
	stopLoss float64
}

type SetStopLossPriceCommand struct {
	command.Command
	controller *GridController
	stopLosses []*gridStopLoss
}

func NewSetStopLossPriceCommand(controller *GridController) *SetStopLossPriceCommand {
	cmd := &SetStopLossPriceCommand{
		Command: command.Command{
			Name:            "setStopLoss",
			Instruction:     "`.setStopLoss GridID1,GridID2/GroupID1,GroupID2 StopLossPrice/auto` 设置止损价，0 表示取消，auto 为恢复默认自动止损价",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *SetStopLossPriceCommand) Prepare() bool {
	this.stopLosses = []*gridStopLoss{}
	priceStr := this.Args[1]
	auto := strings.EqualFold(priceStr, "auto")
	zero := priceStr == "0"

	ids := strings.Split(this.Args[0], ",")
	for _, id := range ids {
		grids := this.controller.GetGridsByID(id, false)
		if len(grids) == 0 {
			this.ErrorMsgf("没有找到网格 %s", id)
			return false
		}
		for _, grid := range grids {
			if !auto && !zero && len(this.stopLosses) > 0 {
				this.ErrorMsgf("仅允许设置多个网格的止损价为 auto 或 0")
				return false
			}

			stopLoss := 0.0
			if auto {
				stopLoss = grid.getAutoStopLoss()
			} else if !zero {
				if p, _, err := utils.ParseFloatOrPercentage(priceStr, false, false); err != nil {
					this.ErrorMsgf("解析止损价出错，error: %s", err)
					return false
				} else {
					stopLoss = p
				}

				if err := grid.checkClosePrices(stopLoss, 0); err != nil {
					this.ErrorMsgf("%s 止损价设置错误: %s", id, err)
					return false
				}
			}
			this.stopLosses = append(this.stopLosses, &gridStopLoss{grid: grid, stopLoss: stopLoss})
		}
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Symbol", "Input Worth", "PNL", "SL Price"})
	for _, stopLoss := range this.stopLosses {

		row := []string{}
		row = append(row, stopLoss.grid.GetDisplayID(false))
		if stopLoss.grid.Reverse {
			row = append(row, fmt.Sprintf("%s [Short]", stopLoss.grid.SymbolCode.Code))
		} else {
			row = append(row, stopLoss.grid.SymbolCode.Code)
		}
		row = append(row, stopLoss.grid.GetAssetPairQtyString(stopLoss.grid.InitQty))
		row = append(row, fmt.Sprintf("%.2f (%.2f%%)", stopLoss.grid.Report.PNL, stopLoss.grid.Report.PNL*100/math.Abs(stopLoss.grid.InitWorth))) // PNL

		stopLossPrice := stopLoss.grid.formatPrice(stopLoss.stopLoss)
		if err := stopLoss.grid.checkClosePrices(stopLoss.stopLoss, 0); err != nil {
			stopLossPrice = fmt.Sprintf("-- check stoploss price failed, error: %s", err)
			row = append(row, stopLossPrice)
		} else {
			row = append(row, fmt.Sprintf("%s -> %s", stopLoss.grid.formatPrice(stopLoss.grid.StopLossPrice), stopLossPrice))
		}

		t.AddRow(row)
	}
	if len(t.Rows) > 1 {
		this.SendMsgf("设置以下止损：\n```%s```", t.Render())
	} else {
		this.ErrorMsgf("没有找到网格")
		return false
	}
	return true
}

func (this *SetStopLossPriceCommand) Do() bool {
	for _, sl := range this.stopLosses {
		sl.grid.StopLossPrice = sl.stopLoss
		sl.grid.handleOrders()
		this.SendMsgf("网格 %s 设置成功", sl.grid.RefID)
	}
	return true
}

type SetTakeProfitPriceCommand struct {
	command.Command
	controller      *GridController
	grid            *Grid
	takeProfitPrice float64
}

func NewSetTakeProfitPriceCommand(controller *GridController) *SetTakeProfitPriceCommand {
	cmd := &SetTakeProfitPriceCommand{
		Command: command.Command{
			Name:            "setTakeProfit",
			Instruction:     "`.setTakeProfit GridID TakeProfitPrice` 设置止盈价，0 表示取消",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *SetTakeProfitPriceCommand) Prepare() bool {
	grid := this.controller.GetGridByID(this.Args[0], false)
	if grid == nil {
		this.ErrorMsgf("没有找到网格 %s", this.Args[0])
		return false
	}
	this.grid = grid

	if p, _, err := utils.ParseFloatOrPercentage(this.Args[1], false, false); err != nil {
		this.ErrorMsgf("解析止盈价出错，error:%s", err)
		return false
	} else {
		this.takeProfitPrice = p
	}
	if err := this.grid.checkClosePrices(0, this.takeProfitPrice); err != nil {
		this.ErrorMsgf("%s", err)
		return false
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Symbol", "Input Worth", "PNL", "SL Price", "TP Price"})
	row := []string{}
	row = append(row, grid.GetDisplayID(false))
	if grid.Reverse {
		row = append(row, fmt.Sprintf("%s [Short]", grid.SymbolCode.Code))
	} else {
		row = append(row, grid.SymbolCode.Code)
	}
	row = append(row, grid.GetAssetPairQtyString(grid.InitQty))
	row = append(row, fmt.Sprintf("%.2f (%.2f%%)", grid.Report.PNL, grid.Report.PNL*100/math.Abs(grid.InitWorth))) // PNL
	row = append(row, grid.formatPrice(grid.StopLossPrice))
	row = append(row, fmt.Sprintf("%s -> %s", grid.formatPrice(grid.TakeProfitPrice), grid.formatPrice(this.takeProfitPrice)))
	t.AddRow(row)
	this.SendMsgf("```%s```", t.Render())

	return true
}

func (this *SetTakeProfitPriceCommand) Do() bool {
	this.grid.TakeProfitPrice = this.takeProfitPrice
	this.grid.handleOrders()
	this.SendMsgf("设置成功")
	return true
}

// 暂停当前品种命令

type PauseGridCommand struct {
	command.Command
	controller *GridController
	grids      []*Grid
}

func NewPauseGridCommand(controller *GridController) *PauseGridCommand {
	cmd := &PauseGridCommand{
		Command: command.Command{
			Name:            "pause",
			Instruction:     "`.pause GridID1/GroupID1,GridID2/GroupID2` 暂停运行网格",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *PauseGridCommand) Prepare() bool {
	this.grids = []*Grid{}
	ids := strings.Split(this.Args[0], ",")
	for _, id := range ids {
		grids := this.controller.GetGridsByID(id, false)
		if len(grids) == 0 {
			this.ErrorMsgf("没有找到网格 %s", id)
			return false
		}
		for _, grid := range grids {
			if grid.Status != Running {
				this.ErrorMsgf("网格 %s 不是运行状态", grid.GetDisplayID(true))
				return false
			}
		}
		this.grids = append(this.grids, grids...)
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Status", "Symbol", "Input Worth", "PNL", "SL Price", "TP Price"})
	for _, grid := range this.grids {
		row := []string{}
		row = append(row, grid.GetDisplayID(false))
		row = append(row, "Running -> Paused")
		if grid.Reverse {
			row = append(row, fmt.Sprintf("%s [Short]", grid.SymbolCode.Code))
		} else {
			row = append(row, grid.SymbolCode.Code)
		}
		row = append(row, grid.GetAssetPairQtyString(grid.InitQty))
		row = append(row, fmt.Sprintf("%.2f (%.2f%%)", grid.Report.PNL, grid.Report.PNL*100/math.Abs(grid.InitWorth))) // PNL
		row = append(row, grid.formatPrice(grid.StopLossPrice))
		row = append(row, grid.formatPrice(grid.TakeProfitPrice))
		t.AddRow(row)
	}
	this.SendMsgf("```%s```", t.Render())
	return true
}

func (this *PauseGridCommand) Do() bool {
	for _, grid := range this.grids {
		grid.Pause()
	}
	return true
}

type ResumeGridCommand struct {
	command.Command
	controller *GridController
	grids      []*Grid
}

func NewResumeGridCommand(controller *GridController) *ResumeGridCommand {
	cmd := &ResumeGridCommand{
		Command: command.Command{
			Name:            "resume",
			Instruction:     "`.resume GridID1/GroupID1,GridID2/GroupID2` 恢复运行网格",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ResumeGridCommand) Prepare() bool {
	this.grids = []*Grid{}
	ids := strings.Split(this.Args[0], ",")
	for _, id := range ids {
		grids := this.controller.GetGridsByID(id, false)
		if len(grids) == 0 {
			this.ErrorMsgf("没有找到网格 %s", id)
			return false
		}
		for _, grid := range grids {
			if grid.Status != Paused {
				this.ErrorMsgf("网格 %s 不是暂停状态", grid.GetDisplayID(true))
				return false
			}
		}
		this.grids = append(this.grids, grids...)
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Status", "Symbol", "Input Worth", "PNL", "SL Price", "TP Price"})
	for _, grid := range this.grids {
		row := []string{}
		row = append(row, grid.GetDisplayID(false))
		row = append(row, "Paused -> Running")
		if grid.Reverse {
			row = append(row, fmt.Sprintf("%s [Short]", grid.SymbolCode.Code))
		} else {
			row = append(row, grid.SymbolCode.Code)
		}
		row = append(row, grid.GetAssetPairQtyString(grid.InitQty))
		row = append(row, fmt.Sprintf("%.2f (%.2f%%)", grid.Report.PNL, grid.Report.PNL*100/math.Abs(grid.InitWorth))) // PNL
		row = append(row, grid.formatPrice(grid.StopLossPrice))
		row = append(row, grid.formatPrice(grid.TakeProfitPrice))
		t.AddRow(row)
	}
	this.SendMsgf("```%s```", t.Render())
	return true
}

func (this *ResumeGridCommand) Do() bool {
	for _, grid := range this.grids {
		grid.Resume()
	}
	return true
}

type CloseGridCommand struct {
	command.Command
	controller *GridController
	grids      []*Grid
	sell       bool
	force      bool
}

func NewCloseGridCommand(controller *GridController) *CloseGridCommand {
	cmd := &CloseGridCommand{
		Command: command.Command{
			Name:            "close",
			Alias:           []string{"c"},
			Instruction:     "`.close GridID1/GroupID1,GridID2/GroupID2 sell/noSell[可选] force[可选]` 停止某个网格，并卖出所有币，force 时忽略错误直接停止",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          3,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *CloseGridCommand) Prepare() bool {
	this.sell = true
	if len(this.Args) >= 2 && strings.EqualFold(this.Args[1], "nosell") {
		this.sell = false
	}

	this.force = false
	if len(this.Args) == 3 && strings.EqualFold(this.Args[2], "force") {
		this.force = true
	}

	this.grids = []*Grid{}
	ids := strings.Split(this.Args[0], ",")
	for _, id := range ids {
		grids := this.controller.GetGridsByID(id, false)
		if len(grids) == 0 {
			this.ErrorMsgf("没有找到网格 %s", id)
			return false
		}
		this.grids = append(this.grids, grids...)
	}

	refIds := []string{}
	for _, grid := range this.grids {
		refIds = append(refIds, grid.RefID)
	}

	// 如果有单个 ihg 网格，需同时关闭另一个
	for _, grid := range this.grids {
		_, oppositeGrid := grid.isInverseHedgedGroupGrid()
		if oppositeGrid != nil && oppositeGrid.Status != Closed && !utils.SliceContains(refIds, oppositeGrid.RefID) {
			this.SendMsgf("IHG 网格组内网格 %s 将一同被关闭", oppositeGrid.GetDisplayID(false))
			this.grids = append(this.grids, oppositeGrid)
		}
	}

	if len(this.grids) == 0 {
		return false
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Symbol", "Input Worth", "PNL", "Qty", "Sell", "Force"})
	for _, grid := range this.grids {
		row := []string{}
		row = append(row, grid.GetDisplayID(false))
		if grid.Reverse {
			row = append(row, fmt.Sprintf("%s [Short]", grid.SymbolCode.Code))
		} else {
			row = append(row, grid.SymbolCode.Code)
		}
		row = append(row, grid.GetAssetPairQtyString(grid.InitQty))
		row = append(row, fmt.Sprintf("%.2f (%.2f%%)", grid.Report.PNL, grid.Report.PNL*100/math.Abs(grid.InitWorth))) // PNL
		row = append(row, grid.formatQty(grid.BaseAssetQty.Total))

		if this.sell {
			row = append(row, "Yes")
		} else {
			row = append(row, "")
		}
		if this.force {
			row = append(row, "Yes")
		} else {
			row = append(row, "")
		}
		t.AddRow(row)
	}
	this.SendMsgf("```%s```", t.Render())
	return true
}

func (this *CloseGridCommand) Do() bool {
	for _, grid := range this.grids {
		grid.ManualClose(this.sell, this.force)
	}
	return true
}

type CloseAllGridCommand struct {
	command.Command
	controller *GridController
	sell       bool
	force      bool
}

func NewCloseAllGridCommand(controller *GridController) *CloseAllGridCommand {
	cmd := &CloseAllGridCommand{
		Command: command.Command{
			Name:            "closeAll",
			Alias:           []string{"ca"},
			Instruction:     "`.closeAll sell/noSell[可选] force[可选]` 停止所有网格，并卖出所有币，force 时忽略错误直接停止",
			RequiresConfirm: true,
			ArgMin:          0,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *CloseAllGridCommand) Prepare() bool {
	this.sell = true
	if len(this.Args) >= 1 && strings.EqualFold(this.Args[0], "nosell") {
		this.sell = false
	}

	this.force = false
	if len(this.Args) == 2 && strings.EqualFold(this.Args[1], "force") {
		this.force = true
	}

	grids := []*Grid{}
	for _, grid := range this.controller.Grids {
		if grid.Status != Closed {
			grids = append(grids, grid)
		}
	}

	if len(grids) == 0 {
		this.ErrorMsgf("没有需要 close 的网格")
		return false
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Symbol", "Input Worth", "PNL", "Qty", "Sell", "Force"})
	for _, grid := range grids {
		row := []string{}
		row = append(row, grid.GetDisplayID(false))
		if grid.Reverse {
			row = append(row, fmt.Sprintf("%s [Short]", grid.SymbolCode.Code))
		} else {
			row = append(row, grid.SymbolCode.Code)
		}
		row = append(row, grid.GetAssetPairQtyString(grid.InitQty))
		row = append(row, fmt.Sprintf("%.2f (%.2f%%)", grid.Report.PNL, grid.Report.PNL*100/math.Abs(grid.InitWorth))) // PNL
		row = append(row, grid.formatQty(grid.BaseAssetQty.Total))

		if this.sell {
			row = append(row, "Yes")
		} else {
			row = append(row, "")
		}
		if this.force {
			row = append(row, "Yes")
		} else {
			row = append(row, "")
		}
		t.AddRow(row)
	}
	this.SendMsgf("```%s```", t.Render())
	return true
}

func (this *CloseAllGridCommand) Do() bool {
	this.controller.CloseAllGrids(this.sell, this.force)
	return true
}

type CleanGridCommand struct {
	command.Command
	controller *GridController
	grids      []*Grid
}

func NewCleanGridCommand(controller *GridController) *CleanGridCommand {
	cmd := &CleanGridCommand{
		Command: command.Command{
			Name:            "clean",
			Instruction:     "`.clean GridID1/GroupID1,GridID2/GroupID2/all` 清理已关闭网格的存储数据",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *CleanGridCommand) Prepare() bool {
	this.grids = []*Grid{}

	if strings.EqualFold(this.Args[0], "all") {
		for _, grid := range this.controller.storage.Grids {
			if grid.Status == Closed {
				this.grids = append(this.grids, grid)
			}
		}
	} else {
		ids := strings.Split(this.Args[0], ",")
		for _, id := range ids {
			grids := this.controller.GetGridsByID(id, true)
			if len(grids) == 0 {
				this.ErrorMsgf("没有找到网格 %s", id)
				return false
			}
			for _, grid := range grids {
				if grid.Status != Closed {
					this.ErrorMsgf("网格 %s 没有关闭", grid.GetDisplayID(true))
					return false
				}
			}
			this.grids = append(this.grids, grids...)
		}
	}

	if len(this.grids) == 0 {
		this.ErrorMsgf("没有需要清理的网格")
		return false
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Symbol", "Input Worth", "PNL", "Finish Time"})
	for _, grid := range this.grids {
		row := []string{}
		row = append(row, grid.GetDisplayID(false))
		if grid.Reverse {
			row = append(row, fmt.Sprintf("%s [Short]", grid.SymbolCode.Code))
		} else {
			row = append(row, grid.SymbolCode.Code)
		}
		row = append(row, grid.GetAssetPairQtyString(grid.InitQty))
		row = append(row, fmt.Sprintf("%.2f (%.2f%%)", grid.Report.PNL, grid.Report.PNL*100/math.Abs(grid.InitWorth))) // PNL
		row = append(row, utils.FormatShortTimeStr(grid.FinishTime, true))
		t.AddRow(row)
	}
	this.SendMsgf("```%s```", t.Render())
	return true
}

func (this *CleanGridCommand) Do() bool {
	for _, gridToClean := range this.grids {
		for i, grid := range this.controller.storage.Grids {
			if grid.RefID == gridToClean.RefID {
				this.controller.storage.Grids = append(this.controller.storage.Grids[:i], this.controller.storage.Grids[i+1:]...)
				break
			}
		}
	}
	this.controller.storage.Save()
	this.SendMsgf("清理完成")
	return true
}

type MarketOrderCommand struct {
	CheckExchangeGridCommand
	symbolCode *exchange.SymbolCode
	symbol     string
	side       exchange.OrderSide
	volume     float64
	close      bool
}

func NewMarketOrderCommand(controller *GridController) *MarketOrderCommand {
	cmd := &MarketOrderCommand{
		CheckExchangeGridCommand: CheckExchangeGridCommand{
			Command: command.Command{
				Name:            "marketOrder",
				Alias:           []string{"mo"},
				Instruction:     "`.marketOrder SymbolCode buy/sell volume close[仅平仓，可选]` 市价买入/卖出指定数量品种",
				RequiresConfirm: true,
				ArgMin:          3,
				ArgMax:          4,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *MarketOrderCommand) Prepare() bool {
	if strings.EqualFold(this.Args[1], "buy") {
		this.side = exchange.OrderSideBuy
	} else if strings.EqualFold(this.Args[1], "sell") {
		this.side = exchange.OrderSideSell
	} else {
		this.ErrorMsgf("参数 %s 错误，应为 buy 或 sell", this.Args[1])
		return false
	}

	if q, _, err := utils.ParseFloatOrPercentage(this.Args[2], false, false); err != nil {
		this.ErrorMsgf("解析数量出错：%s, error: %s", this.Args[2], err)
		return false
	} else {
		this.volume = q
	}

	if code, err := this.controller.NewSymbolCode(this.Args[0]); err != nil {
		this.ErrorMsgf("解析品种代码错误，error: %s", err)
		return false
	} else {
		this.symbolCode = code
	}
	instrumentType := this.symbolCode.InstrumentType()
	if instrumentType == exchange.Spot {
		if spotSymbol, err := this.controller.Exchange.TranslateSymbolCodeToSpotSymbol(this.symbolCode); err != nil {
			this.ErrorMsgf("交易所中没有品种代码对应的现货，error: %s", err)
			return false
		} else {
			this.symbol = spotSymbol
		}
	} else if exchange.SliceContains([]exchange.InstrumentType{
		exchange.USDXMarginedFutures,
	}, instrumentType) {
		if futureSymbol, err := this.controller.Exchange.TranslateSymbolCodeToFutureSymbol(this.symbolCode); err != nil {
			this.ErrorMsgf("交易所中没有品种代码对应的合约，error: %s", err)
			return false
		} else {
			this.symbol = futureSymbol
		}
	} else {
		this.ErrorMsgf("不支持品种代码：%s", this.Args[0])
		return false
	}

	coinOrSymbol := this.symbol
	if instrumentType == exchange.Spot {
		coinOrSymbol = this.symbolCode.Coin()
	}
	qtyBefore, _, err := this.controller.Exchange.GetHoldingQty(this.symbolCode.InstrumentType(), coinOrSymbol)
	if err != nil {
		this.controller.ErrorMsgf("获取当前持有 %s 数量失败: %s", this.symbol, err)
		return false
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"Symbol", "Side", "Close", "Qty"})
	close := ""
	if len(this.Args) > 3 && strings.EqualFold(this.Args[3], "close") {
		this.close = true
		close = "yes"

		canCloseQty := 0.0
		if this.side == exchange.OrderSideBuy && qtyBefore < 0 {
			canCloseQty = qtyBefore
		} else if this.side == exchange.OrderSideSell && qtyBefore > 0 {
			canCloseQty = qtyBefore
		}

		if math.Abs(canCloseQty) < this.volume {
			this.ErrorMsgf("平仓数量大于当前 %s 持仓数量 %f", this.side.OppositeSide(), canCloseQty)
			return false
		}
	}
	qtyAfter := 0.0
	if this.side == exchange.OrderSideBuy {
		qtyAfter = qtyBefore + this.volume
	} else {
		qtyAfter = qtyBefore - this.volume
	}
	t.AddRow([]string{
		this.symbol,
		string(this.side),
		close,
		fmt.Sprintf("%s -> %s",
			this.controller.Exchange.FormatQty(this.symbolCode.InstrumentType(), this.symbol, qtyBefore),
			this.controller.Exchange.FormatQty(this.symbolCode.InstrumentType(), this.symbol, qtyAfter),
		),
	})
	this.SendMsgf("```%s```", t.Render())
	return true
}

func (this *MarketOrderCommand) Do() bool {
	if order, err := this.controller.createMarketOrder(this.symbolCode, this.symbol, this.volume, this.side, this.close); err != nil {
		this.controller.ErrorMsgf(err.Error())
		return false
	} else {
		this.controller.SendMsgf("市价订单成交: %s %v@%v", this.side, order.ExecQty, order.ExecPrice)
	}
	return true
}

func (this *GridController) createMarketOrder(symbolCode *exchange.SymbolCode, symbol string, qty float64, side exchange.OrderSide, close bool) (*exchange.Order, error) {
	args := exchange.CreateOrderArgs{
		InstrumentType: symbolCode.InstrumentType(),
		Symbol:         symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCash,
		Qty:            qty,
		TimeInForce:    exchange.GTC,
		Side:           side,
	}
	if close {
		args.ReduceOnly = true
	}
	if symbolCode.IsFuture() {
		args.TradeMode = exchange.TradeModeCross
	}
	order, err := this.Exchange.CreateOrder(args)
	if err != nil {
		return nil, fmt.Errorf("创建市价单失败: %s", err)
	}

	queryTime := 0
	for {
		if order.ExecQty > 0 {
			break
		}
		if queryTime >= 5 {

			return nil, fmt.Errorf("市价单多次查询无成交，订单号：%s", order.OrderID)
		}
		queryTime += 1

		order, err = this.Exchange.GetOrderByOrig(*order)
		if err != nil {
			return nil, fmt.Errorf("市价单查询失败：%s", err)
		}
	}
	return order, nil
}

type ConfirmOrdersCommand struct {
	CheckExchangeGridCommand
	grid      *Grid
	dirtySets []DirtyGridSet
}

type DirtyGridSet struct {
	index        int
	side         exchange.OrderSide
	price        float64
	qty          float64
	needUpdate   bool // 订单号相同，价格/数量需要更新
	localOrderID string
	localOrder   Order
	remoteOrder  Order
}

func NewConfirmOrdersCommand(controller *GridController) *ConfirmOrdersCommand {
	cmd := &ConfirmOrdersCommand{
		CheckExchangeGridCommand: CheckExchangeGridCommand{
			Command: command.Command{
				Name:            "confirmOrders",
				Instruction:     "`.confirmOrders GridID` 确认本地和交易所订单并同步",
				RequiresConfirm: true,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

/*
confirmOrders 处理逻辑：
持有网格数量 holdingCount = 已买入网格数量 - 已卖出网格数量 = BuyFilledCount - SaleFilledCount
根据 holdingCount 打印网格所有挂单，对比本地挂单和服务端实际挂单是否正确
*/
func (this *ConfirmOrdersCommand) Prepare() bool {
	grid := this.controller.GetGridByID(this.Args[0], false)
	if grid == nil {
		this.ErrorMsgf("没有找到网格 %s", this.Args[0])
		return false
	}
	this.grid = grid

	lock, ok := this.controller.handleMutex.Load(grid.Symbol)
	if !ok {
		this.ErrorMsgf("[%s] grid controller handle mutex improperly initialized", grid.FullID())
		return false
	}
	lock.Lock()
	defer lock.Unlock()

	remoteOpenOrders, err := this.controller.GetRemoteOpenOrdersByGrid(grid, grid.OrderType())
	if err != nil {
		this.AlertMsgf("获取当前挂单失败: %s", err)
		return false
	}

	t := NewTable()
	t.SetHeader([]string{"Grid Index", "Direction", "Price", "Qty", "LocalOrder", "RemoteOrder", "Dirty"})

	localOpenOrders := grid.GetLocalOpenOrders()

	this.dirtySets = []DirtyGridSet{}
	holdingCount := grid.HoldingCount()
	sellOpenOrderCount := holdingCount
	if grid.Reverse {
		sellOpenOrderCount = grid.Num - holdingCount
	}
	for i := grid.Num - 1; i >= 0; i-- {
		side := exchange.OrderSideBuy
		price := grid.Prices[i]
		qty := grid.BuyQtys[i]
		if i >= (grid.Num - sellOpenOrderCount) {
			side = exchange.OrderSideSell
			price = grid.Prices[i+1]
			qty = grid.SellQtys[i]
		}

		// 找到本地对应挂单
		localOrderID := ""
		var localOrder Order
		for j, order := range localOpenOrders {
			orderPrice := order.Price
			if order.TriggerPrice != 0 {
				orderPrice = order.TriggerPrice
			}
			if order.GetInt(ExtKeyGridIndex) == i && order.Side == side &&
				grid.isTheSamePrice(orderPrice, price) && grid.checkSameQty(order.Qty, qty) {
				localOrderID = order.OrderID
				localOrder = *order
				localOpenOrders = append(localOpenOrders[:j], localOpenOrders[j+1:]...) // 找到则剔除
				break
			}
		}

		remoteOrderID := ""
		var remoteOrder Order
		needUpdate := false
		for _, order := range remoteOpenOrders {
			orderPrice := order.Price
			if order.TriggerPrice != 0 {
				orderPrice = order.TriggerPrice
			}

			// 和本地订单 ID 对比是否一致
			if order.OrderID == localOrderID {
				remoteOrderID = localOrderID
				remoteOrder = *order

				if !grid.isTheSamePrice(orderPrice, price) || !grid.checkSameQty(order.Qty, qty) {
					needUpdate = true
				}

				break
			}
			// 对比价格和数量是否一致
			if order.Side == side && grid.isTheSamePrice(orderPrice, price) && grid.checkSameQty(order.Qty, qty) {
				remoteOrderID = order.OrderID
				remoteOrder = *order
				break
			}
		}

		dirty := "-"
		if localOrderID == "" || remoteOrderID == "" || localOrderID != remoteOrderID || needUpdate {
			dirty = "yes"
			this.dirtySets = append(this.dirtySets, DirtyGridSet{
				index:        i,
				side:         side,
				price:        price,
				qty:          qty,
				needUpdate:   needUpdate,
				localOrderID: localOrderID,
				localOrder:   localOrder,
				remoteOrder:  remoteOrder,
			})
		}
		if needUpdate {
			localOrderID = fmt.Sprintf("%s[%s@%s]", localOrderID, grid.formatPrice(qty), grid.formatPrice(price))
			orderPrice := remoteOrder.Price
			if remoteOrder.TriggerPrice != 0 {
				orderPrice = remoteOrder.TriggerPrice
			}
			remoteOrderID = fmt.Sprintf("%s[%s@%s]", remoteOrderID, grid.formatPrice(remoteOrder.Qty), grid.formatPrice(orderPrice))
		}
		t.AddRow([]string{
			fmt.Sprintf("%d", i),
			string(side),
			grid.formatPrice(price),
			grid.formatQty(qty),
			localOrderID,
			remoteOrderID,
			dirty,
		})
	}

	this.SendMsgf("请确认网格[%s]的挂单\n```%s```", grid.RefID, t.Render())

	// 经过筛选后本地多余的错误数据
	if len(localOpenOrders) > 0 {
		sort.SliceStable(localOpenOrders, func(i, j int) bool {
			return localOpenOrders[i].GetInt(ExtKeyGridIndex) > localOpenOrders[j].GetInt(ExtKeyGridIndex)
		})

		t := NewTable()
		t.SetHeader([]string{"Grid Index", "Direction", "Price", "Qty", "OrderID", "CreateTime", "UpdateTime"})
		for _, order := range localOpenOrders {
			t.AddRow([]string{
				fmt.Sprintf("%d", order.GetInt(ExtKeyGridIndex)),
				string(order.Side),
				grid.formatPrice(order.Price),
				grid.formatQty(order.Qty),
				order.OrderID,
				utils.FormatShortTimeStr(order.CreateTime, false),
				utils.FormatShortTimeStr(order.UpdateTime, false),
			})

			this.dirtySets = append(this.dirtySets, DirtyGridSet{
				index:        -1, // 表示多余的脏数据
				side:         order.Side,
				price:        order.Price,
				qty:          order.Qty,
				localOrderID: order.OrderID,
				localOrder:   *order,
			})
		}
		this.ErrorMsgf("本地多余的挂单[程序可能存在 bug]\n```%s```", t.Render())
		return false
	}

	if len(this.dirtySets) == 0 {
		this.SendMsgf("所有挂单都正常，无需确认")
		return false
	}

	this.SendMsgf("错误数据将在确认后修正")
	return true
}

func (this *ConfirmOrdersCommand) Do() bool {
	grid := this.grid
	lock, _ := this.controller.handleMutex.Load(grid.Symbol)
	lock.Lock()
	defer lock.Unlock()
	// ISSUE: 这里可能 handleOrders 先执行了对订单的修改，导致和 dirtySets 里的数据已经不一致

	for _, dirtySet := range this.dirtySets {
		if dirtySet.needUpdate { // 订单号相同，价格/数量需要更新
			origOrder := dirtySet.localOrder
			if _, err := grid.updateOpenOrder(&origOrder, dirtySet.price, dirtySet.qty); err != nil {
				this.ErrorMsgf("修改订单 %s 失败: %s", origOrder.OrderID, err)
			}
			continue
		}

		if dirtySet.localOrderID != "" {
			queryOrder, err := this.controller.Exchange.GetOrderByOrig(dirtySet.localOrder)
			if err != nil {
				this.controller.ErrorMsgf("查询订单[%s]失败: %s", dirtySet.localOrderID, err)
				return false
			}
			if queryOrder.IsFilled() {
				grid.handleFilledOrder(queryOrder)
				this.SendMsgf("本地订单[%s]已成交，本地更新成功", dirtySet.localOrderID)
				continue // 如果已成交，忽略远程订单
			}

			// 更新本地挂单状态为 cancel
			if dirtySet.side == exchange.OrderSideBuy {
				for i, order := range grid.BuyOrders {
					if order.OrderID == dirtySet.localOrderID && order.IsOpen() {
						grid.BuyOrders[i].Status = exchange.OrderStatusCancelled
						break
					}
				}
			} else if dirtySet.side == exchange.OrderSideSell {
				for i, order := range grid.SellOrders {
					if order.OrderID == dirtySet.localOrderID && order.IsOpen() {
						grid.SellOrders[i].Status = exchange.OrderStatusCancelled
						break
					}
				}
			}
		}

		if dirtySet.index == -1 {
			continue
		}

		if dirtySet.remoteOrder.OrderID != "" {
			// 记录到本地
			order := dirtySet.remoteOrder
			order.SetInt(ExtKeyGridIndex, dirtySet.index)
			if dirtySet.side == exchange.OrderSideBuy {
				order.SetString(ExtKeyCategory, string(OrderCategoryBuy))
				if grid.Reverse {
					order.SetString(ExtKeyRefOrderID, grid.FindMissingRefOrderID(dirtySet.index))
				}
				grid.BuyOrders = append(grid.BuyOrders, &order)
			} else if dirtySet.side == exchange.OrderSideSell {
				order.SetString(ExtKeyCategory, string(OrderCategorySell))
				if !grid.Reverse {
					order.SetString(ExtKeyRefOrderID, grid.FindMissingRefOrderID(dirtySet.index))
				}
				grid.SellOrders = append(grid.SellOrders, &order)
			}
		} else {
			// 新建订单
			category := OrderCategoryBuy
			refOrderID := ""
			if dirtySet.side == exchange.OrderSideSell {
				category = OrderCategorySell
			}
			if grid.Reverse && dirtySet.side == exchange.OrderSideBuy {
				refOrderID = grid.FindMissingRefOrderID(dirtySet.index)
			} else if !grid.Reverse && dirtySet.side == exchange.OrderSideSell {
				refOrderID = grid.FindMissingRefOrderID(dirtySet.index)
				dirtySet.qty *= (1 - grid.getBaseFeeRate(exchange.OrderSideBuy, grid.OrderType())) // 考虑手续费
			}
			if order, err := grid.createOrder(dirtySet.price, dirtySet.qty, grid.OrderType(), category, dirtySet.index, refOrderID); err != nil {
				this.ErrorMsgf("创建订单[Index: %d]失败: %s", dirtySet.index, err)
			} else {
				this.SendMsgf("创建订单[Index: %d - %s]成功", dirtySet.index, order.OrderID)
			}
		}
	}
	this.controller.storage.Save()
	this.SendMsgf("数据修正完成")
	go grid.handleOrders()
	return true
}

type PositionGridCommand struct {
	CheckExchangeGridCommand
}

func NewPositionGridCommand(controller *GridController) *PositionGridCommand {
	cmd := &PositionGridCommand{
		CheckExchangeGridCommand: CheckExchangeGridCommand{
			Command: command.Command{
				Name:            "position",
				Alias:           []string{"p"},
				Instruction:     "`.position` 打印当前持仓",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *PositionGridCommand) Do() bool {
	positionStr := "[No Positions]"
	t := NewTable()
	t.SetHeader([]string{"Symbol", "Type", "Qty", "Available"})

	assets := ""
	if len(this.Args) == 1 {
		assets = this.Args[0]
	}

	if rows, err := this.controller.GetPositionRows("spot/future", assets); err != nil {
		this.ErrorMsgf("获取仓位出错：%s", err)
		return false
	} else {
		for _, row := range rows {
			t.AddRow(row)
		}

	}
	if len(t.Rows) > 1 {
		positionStr = t.Render()
	}

	this.SendMsgf("当前持仓：\n```%s```", positionStr)

	bsStr := "[No Balances]"
	if bs, err := this.controller.Exchange.GetAccountBalancesValidCoins(this.controller.ID, this.controller.GetInstrumentTypes(), func(coin string) bool { return true }); err != nil {
		this.ErrorMsgf("获取余额出错：%s", err)
		return false
	} else {
		bsStr = bs.Render()
	}

	this.SendMsgf("当前余额：\n```%s```", bsStr)

	return true
}

type TradesGridCommand GridCommand

func NewTradesGridCommand(controller *GridController) *TradesGridCommand {
	cmd := &TradesGridCommand{
		Command: command.Command{
			Name:            "trades",
			Alias:           []string{"ts"},
			Instruction:     "`.trades SymbolCode/All all/open/close[可选，默认 all] Limit[可选，默认 10]` 打印最近成交的订单",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          3,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

type GridTrade struct {
	Trade *Order
	Grid  *Grid
}

func (this *TradesGridCommand) Do() bool {
	var symbolCode string
	if code, err := this.controller.NewSymbolCode(this.Args[0]); err == nil {
		symbolCode = code.Code
	} else if strings.EqualFold(this.Args[0], "all") {
		symbolCode = "all"
	} else {
		this.ErrorMsgf("解析品种代码错误，error: %s", err)
		return false
	}

	way := "all"
	if len(this.Args) > 1 {
		if strings.EqualFold(this.Args[1], "open") {
			way = "open"
		} else if strings.EqualFold(this.Args[1], "close") {
			way = "close"
		}
	}

	limit := 10
	if len(this.Args) > 2 {
		if n, err := cast.ToInt64E(this.Args[2]); err == nil {
			limit = int(n)
		}
	}

	trades := []*GridTrade{}
	for _, grid := range this.controller.storage.Grids {
		if grid.SymbolCode.Code != symbolCode && symbolCode != "all" {
			continue
		}
		orders := grid.BuyOrders
		orders = append(orders, grid.SellOrders...)
		orders = append(orders, grid.CloseOrders...)
		for _, order := range orders {
			if order.IsFilled() {
				trades = append(trades, &GridTrade{
					Trade: order,
					Grid:  grid,
				})
			}
		}
	}
	sort.SliceStable(trades, func(i, j int) bool {
		o1 := trades[i]
		o2 := trades[j]
		return o2.Trade.UpdateTime.Before(*o1.Trade.UpdateTime)
	})

	t := NewTable()
	t.SetHeader([]string{"Grid Index", "Symbol", "Grid ID", "Open/Close", "Order ID", "Direction", "Exec. Price", "Exec. Qty", "Exec. Time", "Profit"})
	for _, trade := range trades {
		if len(t.Rows) > limit {
			break
		}
		order := trade.Trade
		grid := trade.Grid
		profitStr := "-"
		refOrderID := order.GetString(ExtKeyRefOrderID)
		openStr := "Open"
		if order.Side == exchange.OrderSideSell && !grid.Reverse {
			openStr = "Close"
			for _, buyOrder := range grid.BuyOrders {
				if buyOrder.IsFilled() && refOrderID == buyOrder.OrderID {
					profit := grid.Qty2Size(order.ExecPrice, order.ExecQty) - grid.Qty2Size(buyOrder.ExecPrice, buyOrder.ExecQty)
					if grid.KeepProfitRatio > 0 {
						profit = grid.Qty2Size(order.ExecPrice, order.ExecQty) - grid.Qty2Size(buyOrder.ExecPrice, order.ExecQty)
					}
					profitStr = fmt.Sprintf("%.2f %s", profit, grid.QuoteAsset)
					break
				}
			}
		} else if order.Side == exchange.OrderSideBuy && grid.Reverse {
			openStr = "Close"
			for _, sellOrder := range grid.SellOrders {
				if sellOrder.IsFilled() && refOrderID == sellOrder.OrderID {
					profit := grid.Qty2Size(sellOrder.ExecPrice, sellOrder.ExecQty) - grid.Qty2Size(order.ExecPrice, order.ExecQty)
					if grid.KeepProfitRatio > 0 {
						profit = grid.Qty2Size(sellOrder.ExecPrice, order.ExecQty) - grid.Qty2Size(order.ExecPrice, order.ExecQty)
					}
					profitStr = fmt.Sprintf("%.2f %s", profit, grid.QuoteAsset)
					break
				}
			}
		}

		if way != "all" && !strings.EqualFold(openStr, way) {
			continue
		}

		dir := order.GetString(ExtKeyCategory)
		if dir == "" {
			dir = string(order.Side)
		}
		t.AddRow([]string{
			fmt.Sprintf("%d", order.GetInt(ExtKeyGridIndex)),
			grid.Symbol,
			grid.GetDisplayID(true),
			openStr,
			order.OrderID,
			dir,
			grid.formatPrice(order.ExecPrice),
			grid.formatQty(order.Qty),
			utils.FormatShortTimeStr(order.UpdateTime, false),
			profitStr,
		})
	}

	if len(t.Rows) == 1 {
		this.controller.SendMsgf("当前无成交订单")
	} else {
		this.controller.SendMsgf(fmt.Sprintf("最近成交订单\n```%s```", t.Render()))
	}
	return true
}

type AliasGridCommand GridCommand

func NewAliasGridCommand(controller *GridController) *AliasGridCommand {
	cmd := &AliasGridCommand{
		Command: command.Command{
			Name: "alias",
			Instructions: []string{
				"`.alias list` 显示所有网格机/组别名",
				"`.alias set GridID alias ?` 设置某个网格机的别名",
				"`.alias delete alias AuthCode` 删除某个网格机的别名",
			},
		},
		controller: controller,
	}
	return cmd
}

func (this *AliasGridCommand) Prepare() bool {
	subCommand := this.GetSubcommand()
	args := this.GetArgs()
	switch subCommand {
	case "set":
		t := NewTable()
		t.SetHeader([]string{"RefID", "Old Alias", "New Alias"})
		refID := args[0]
		alias := args[1]
		oldAlias := ""
		for _, grid := range this.controller.Grids {
			if strings.EqualFold(grid.Alias, alias) {
				this.ErrorMsgf("%s 已经设置给了网格机 %s", alias, grid.RefID)
				return false
			}
		}
		for _, group := range this.controller.GridGroups {
			if strings.EqualFold(group.Alias, alias) {
				this.ErrorMsgf("%s 已经设置给了网格组 %s", alias, group.RefID)
				return false
			}
		}

		for _, grid := range this.controller.Grids {
			if strings.EqualFold(grid.RefID, refID) {
				oldAlias = grid.Alias
				break
			}
		}
		for _, group := range this.controller.GridGroups {
			if strings.EqualFold(group.RefID, refID) {
				oldAlias = group.Alias
				break
			}
		}

		t.AddRow([]string{refID, oldAlias, alias})
		this.SendMsgf("确认设置别名：\n```%s```", t.Render())
	}
	return true
}

func (this *AliasGridCommand) Do() bool {
	args := this.GetArgs()
	subcommand := this.GetSubcommand()
	if subcommand == "" {
		subcommand = "list"
	}
	switch subcommand {
	case "set":
		refID := args[0]
		alias := args[1]
		for _, grid := range this.controller.Grids {
			if strings.EqualFold(grid.RefID, refID) {
				grid.Alias = alias
				break
			}
		}
		for _, group := range this.controller.GridGroups {
			if strings.EqualFold(group.RefID, refID) {
				group.Alias = alias
				break
			}
		}
		this.controller.storage.Save()
		this.SendMsgf("设置别名成功：%s -> %s", refID, alias)
	case "delete":
		alias := args[0]
		refID := ""
		for _, grid := range this.controller.Grids {
			if strings.EqualFold(grid.Alias, alias) {
				grid.Alias = ""
				refID = grid.RefID
				break
			}
		}
		for _, group := range this.controller.GridGroups {
			if strings.EqualFold(group.Alias, alias) {
				group.Alias = ""
				refID = group.RefID
				break
			}
		}
		this.controller.storage.Save()
		if refID != "" {
			this.SendMsgf("删除网格机/组 %s 的别名 %s 成功。", refID, alias)
		} else {
			this.SendMsgf("没有找到别名为 %s 的网格机/组。", alias)
		}
	case "list":
		this.SendMsgf("```%s```", this.RenderAliases())
	}

	return true
}

func (this *AliasGridCommand) RenderAliases() string {
	aliasMsg := "[No Alias]"
	t := NewTable()
	t.SetHeader([]string{"Alias", "RefID", "Type"})
	for _, grid := range this.controller.Grids {
		if grid.Alias != "" {
			t.AddRow([]string{grid.Alias, grid.RefID, "Grid"})
		}
	}
	for _, group := range this.controller.GridGroups {
		if group.Alias != "" {
			t.AddRow([]string{group.Alias, group.RefID, "GridGroup"})
		}
	}
	if len(t.Rows) > 1 {
		aliasMsg = t.Render()
	}
	return aliasMsg
}

func (this *GridController) GetPositionRows(spotOrFuture, assets string) (rows [][]string, er error) {
	instrumentTypes := []exchange.InstrumentType{}
	if strings.Contains(spotOrFuture, "spot") {
		instrumentTypes = append(instrumentTypes, exchange.Spot)
	}
	if strings.Contains(spotOrFuture, "future") {
		instrumentTypes = append(instrumentTypes, exchange.USDXMarginedFutures)
	}

	if holdings, err := this.Exchange.GetAccountHoldings(instrumentTypes); err != nil {
		er = fmt.Errorf("get position rows failed, error: %s", err)
		return
	} else {
		for _, holding := range holdings {
			if holding.Total == 0 {
				continue
			}
			total := holding.Total
			available := holding.Available
			if holding.InstrumentType.IsFuture() && this.Config.ShowFutureQtyAsValue {
				total = this.futureCntToValue(holding.InstrumentType, holding.CoinOrSymbol, holding.Total)
				available = this.futureCntToValue(holding.InstrumentType, holding.CoinOrSymbol, holding.Available)
			}
			rows = append(rows, []string{
				holding.CoinOrSymbol,
				string(holding.InstrumentType),
				fmt.Sprintf("%.5f", total),
				fmt.Sprintf("%.5f", available),
			})
		}
	}
	return
}

func (this *Grid) RenderOrderTable(orderTable *OrderTable) string {
	t := NewTable()
	t.SetHeader([]string{"Index", "OrderID", "Qty", "Price", "Delta %", "   ", "Index", "OrderID", "Qty", "Price", "Delta %"})
	rows := [][]string{}

	leftColumn := orderTable.LeftColumn
	rightColumn := orderTable.RightColumn
	// 左边倒序 右边正序
	sort.SliceStable(leftColumn, func(i, j int) bool { return leftColumn[i].IndexDisplay > leftColumn[j].IndexDisplay })
	sort.SliceStable(rightColumn, func(i, j int) bool { return rightColumn[i].IndexDisplay < rightColumn[j].IndexDisplay })

	leftCount := len(leftColumn)
	rightCount := len(rightColumn)
	maxCount := leftCount
	if rightCount > maxCount {
		maxCount = rightCount
	}
	i := 0
	for {
		if i >= maxCount {
			break
		}
		row := []string{}
		if i < leftCount {
			item := leftColumn[i]
			row = append(row, fmt.Sprintf("%d", item.IndexDisplay))
			row = append(row, item.OrderID)
			if this.Reverse {
				row = append(row, "-"+this.formatQty(item.Qty)) // 副号仅用于 Reverse 网格订单显示
			} else {
				row = append(row, this.formatQty(item.Qty))
			}
			if this.UseStopLimit {
				row = append(row, "~"+this.formatPrice(item.Price))
			} else {
				row = append(row, this.formatPrice(item.Price))
			}
			row = append(row, fmt.Sprintf("%.2f%%", item.Delta*100))
		} else {
			row = []string{"", "", "", "", ""}
		}
		row = append(row, "") // 中间的间隔列
		if i < rightCount {
			item := rightColumn[i]
			row = append(row, fmt.Sprintf("%d", item.IndexDisplay))
			row = append(row, item.OrderID)
			if this.Reverse {
				row = append(row, "-"+this.formatQty(item.Qty)) // 副号仅用于 Reverse 网格订单显示
			} else {
				row = append(row, this.formatQty(item.Qty))
			}
			if this.UseStopLimit {
				row = append(row, "~"+this.formatPrice(item.Price))
			} else {
				row = append(row, this.formatPrice(item.Price))
			}
			row = append(row, fmt.Sprintf("%.2f%%", item.Delta*100))
		} else {
			row = append(row, "", "", "", "", "")
		}
		rows = append(rows, row)
		i += 1
	}
	for _, row := range rows {
		t.AddRow(row)
	}
	return t.Render()
}

func (this *Grid) RenderPreview() string {
	preview := this.GetPreview()
	orderTableStr := this.RenderOrderTable(preview.OrderTable)

	t2 := NewTable()
	rows2 := [][]string{}
	rows2 = append(rows2, []string{"Input Worth", this.GetAssetPairQtyString(this.InitQty)})
	rows2 = append(rows2, []string{"Init Worth", fmt.Sprintf("%.0f %s", this.InitWorth, this.QuoteAsset)})
	rows2 = append(rows2, []string{"Range", fmt.Sprintf("[%s ~ %s]", this.formatPrice(this.Range.Low()), this.formatPrice(this.Range.High()))})
	rows2 = append(rows2, []string{"Profit Rate", fmt.Sprintf("%.2f%% - %.2f%%", preview.ProfitRateLow*100, preview.ProfitRateHigh*100)})
	rows2 = append(rows2, []string{"Current Price", this.formatPrice(this.LastPrice)})
	rows2 = append(rows2, []string{"StopLoss Price", this.formatPrice(this.StopLossPrice)})
	rows2 = append(rows2, []string{"TakeProfit Price", this.formatPrice(this.TakeProfitPrice)})
	rows2 = append(rows2, []string{"Est. Worth After Started", this.GetAssetPairQtyString(preview.AssetPairQty)})
	if preview.ExtraBaseQty != 0 {
		qtyStr := this.controller.Exchange.FormatQty(this.InstrumentType(), this.Symbol, preview.ExtraBaseQty)
		rows2 = append(rows2, []string{"Extra Base Qty", fmt.Sprintf("%s %s", qtyStr, this.BaseAsset)})
	}
	for _, row := range rows2 {
		t2.AddRow(row)
	}
	return fmt.Sprintf("```%s```\n```%s```", orderTableStr, t2.Render())
}

func RenderStatus(grids []*Grid, detail bool) string {
	t := NewTable()
	if detail {
		t.SetHeader(DetailStatusHeader)
	} else {
		t.SetHeader(SimpleStatusHeader)
	}

	for _, grid := range grids {
		if !grid.IsFinished() {
			row := grid.GetStatusRow(false, detail)
			t.AddRow(row)
		}
	}
	if len(t.Rows) == 1 {
		return "无运行中的网格"
	}
	return t.Render()
}

func (this *GridController) RenderGroupStatus(groups []*GridGroup, detail bool, history bool) string {
	if len(groups) == 0 {
		return "当前无网格组"
	}
	msg := ""
	renderCount := 0
	for _, group := range groups {
		t := NewTable()
		header := []string{"Group"}
		if detail {
			header = append(header, DetailStatusHeader...)
		} else {
			header = append(header, SimpleStatusHeader...)
		}
		t.SetHeader(header)

		groupRow := []string{group.GetDisplayID(detail), "", "", "", "", ""}

		totalInitWorth := 0.0
		totalRealWorth := 0.0
		totalAssetQty := AssetPairQty{}
		totalReport := GridReport{}
		groupOpen := false
		var grid *Grid
		for _, id := range group.GridIDs {
			grid = this.GetGridByID(id, true)
			if grid != nil {
				if grid.Status != Closed {
					groupOpen = true
				}
				totalInitWorth += math.Abs(grid.InitWorth)
				totalRealWorth += math.Abs(grid.getRealWorth())
				totalReport.BuyFilledCount += grid.Report.BuyFilledCount
				totalReport.SaleFilledCount += grid.Report.SaleFilledCount
				totalReport.PNL += grid.Report.PNL
				totalReport.GridPNL += grid.Report.GridPNL
				totalReport.HoldingPNL += grid.Report.HoldingPNL
				totalReport.UnrealisedPNL += grid.Report.UnrealisedPNL
				totalAssetQty[0] += grid.BaseAssetQty.Total
				totalAssetQty[1] += grid.QuoteAssetQty.Total
			}
		}

		if !history && !groupOpen {
			continue
		}

		if grid == nil {
			msg += "找不到对应网格"
			continue
		}

		worth, _ := grid.GetAssetWorthInUSDT(totalAssetQty)

		if group.Type == GroupTypeInverseHedged {
			totalInitWorth = totalInitWorth / 2.0
			totalRealWorth = totalRealWorth / 2.0
			worth = worth / 2.0
		}
		groupRow = append(groupRow, fmt.Sprintf("%.2f %s", totalInitWorth, grid.QuoteAsset))
		groupRow = append(groupRow, fmt.Sprintf("%.2f %s", totalRealWorth, grid.QuoteAsset))
		groupRow = append(groupRow, "")
		groupRow = append(groupRow, fmt.Sprintf("%d", totalReport.BuyFilledCount))
		groupRow = append(groupRow, fmt.Sprintf("%d", totalReport.SaleFilledCount))
		groupRow = append(groupRow, fmt.Sprintf("%.2f USDT", worth))
		groupRow = append(groupRow, this.Exchange.FormatQty(grid.InstrumentType(), grid.Symbol, totalAssetQty.GetBaseQty()))
		groupRow = append(groupRow, fmt.Sprintf("%.2f (%.2f%%)", totalReport.PNL, totalReport.PNL*100/math.Abs(totalInitWorth)))
		groupRow = append(groupRow, fmt.Sprintf("%.2f (%.2f%%)", totalReport.GridPNL, totalReport.GridPNL*100/math.Abs(totalInitWorth)))
		groupRow = append(groupRow, fmt.Sprintf("%.2f (%.2f%%)", totalReport.HoldingPNL, totalReport.HoldingPNL*100/math.Abs(totalInitWorth)))
		groupRow = append(groupRow, fmt.Sprintf("%.2f (%.2f%%)", totalReport.UnrealisedPNL, totalReport.UnrealisedPNL*100/math.Abs(totalInitWorth)))
		groupRow = append(groupRow, "")
		if detail {
			groupRow = append(groupRow, "")
			groupRow = append(groupRow, grid.GetAssetPairQtyString(totalAssetQty))
			groupRow = append(groupRow, "")
			groupRow = append(groupRow, "")
			groupRow = append(groupRow, "")
			groupRow = append(groupRow, "")
			groupRow = append(groupRow, "")
			groupRow = append(groupRow, "")
		}
		t.AddRow(groupRow)

		for _, id := range group.GridIDs {
			grid := this.GetGridByID(id, true)
			if grid != nil {
				t.AddRow(append([]string{""}, grid.GetStatusRow(false, detail)...))
			}
		}

		if renderCount > 0 {
			msg += "\n"
		}
		renderCount += 1
		msg += fmt.Sprintf("\n%s\n", t.Render())
	}
	if renderCount == 0 {
		return "当前无网格组"
	}
	return msg
}

func (this *Grid) GetAssetPairQtyString(assetPairQty AssetPairQty) string {
	baseQty := assetPairQty.GetBaseQty()
	if baseQty != 0 {
		qtyStr := this.controller.Exchange.FormatQty(this.InstrumentType(), this.Symbol, baseQty)
		return fmt.Sprintf("%s %s / %.2f %s", qtyStr, this.BaseAsset, assetPairQty.GetQuoteQty(), this.QuoteAsset)
	} else {
		return fmt.Sprintf("%.2f %s", assetPairQty.GetQuoteQty(), this.QuoteAsset)
	}
}

func (this *Grid) GetStatusRow(withControllerName bool, detail bool) []string {
	assetQty := NewAssetPairQty(this.BaseAssetQty.Total, this.QuoteAssetQty.Total)

	row := []string{}
	if withControllerName {
		row = append(row, this.controller.ID)
	}
	if this.Reverse {
		row = append(row, fmt.Sprintf("%s [Short]", this.SymbolCode.Code))
	} else {
		row = append(row, this.SymbolCode.Code)
	}
	row = append(row, this.GetDisplayID(true))

	if this.Status == Cooling {
		row = append(row, fmt.Sprintf("%s [To %s]", this.Status, utils.FormatShortTimeStr(this.CoolEndTime, false)))
	} else {
		row = append(row, string(this.Status))
	}

	row = append(row, string(this.StrategyName))
	row = append(row, fmt.Sprintf("[%s ~ %s]", this.formatPrice(this.Range.Low()), this.formatPrice(this.Range.High())))
	row = append(row, this.GetAssetPairQtyString(this.InitQty))
	row = append(row, fmt.Sprintf("%.2f %s", this.getRealWorth(), this.QuoteAsset))
	row = append(row, fmt.Sprintf("%d", this.Num))
	row = append(row, fmt.Sprintf("%d", this.Report.BuyFilledCount))  // Buy Fill Count
	row = append(row, fmt.Sprintf("%d", this.Report.SaleFilledCount)) // Sale Fill Count
	worth, _ := this.GetAssetWorthInUSDT(assetQty)
	row = append(row, fmt.Sprintf("%.2f USDT", worth))                                                                                 // Worth
	row = append(row, this.controller.Exchange.FormatQty(this.InstrumentType(), this.Symbol, this.BaseAssetQty.Total))                 // position
	row = append(row, fmt.Sprintf("%.2f (%.2f%%)", this.Report.PNL, this.Report.PNL*100/math.Abs(this.InitWorth)))                     // PNL
	row = append(row, fmt.Sprintf("%.2f (%.2f%%)", this.Report.GridPNL, this.Report.GridPNL*100/math.Abs(this.InitWorth)))             // Grid PNL
	row = append(row, fmt.Sprintf("%.2f (%.2f%%)", this.Report.HoldingPNL, this.Report.HoldingPNL*100/math.Abs(this.InitWorth)))       // Holding PNL
	row = append(row, fmt.Sprintf("%.2f (%.2f%%)", this.Report.UnrealisedPNL, this.Report.UnrealisedPNL*100/math.Abs(this.InitWorth))) // Unrealised PNL
	row = append(row, this.formatPrice(this.StopLossPrice))                                                                            // StopLoss price

	if detail {
		row = append(row, this.formatPrice(this.TakeProfitPrice)) // TakeProfit price
		row = append(row, this.formatPrice(this.MarginCallPrice)) // MarginCallPrice
		row = append(row, utils.FormatShortTimeStr(this.FinishTime, false))
		row = append(row, string(this.FinishReason))
		row = append(row, this.GetAssetPairQtyString(assetQty))             // Asset Qty
		row = append(row, utils.FormatShortTimeStr(this.CreateTime, false)) // Create Time
		row = append(row, fmt.Sprintf("%.2f", this.InitPrice))              // Init Price
		row = append(row, fmt.Sprintf("%v", this.UseStopLimit))             // Stop Limit Order
		row = append(row, fmt.Sprintf("%d", this.CoolMinutes/60/24))
		row = append(row, fmt.Sprintf("%d", this.CoolReEntryIndex))
	}
	return row
}

func (this *Grid) RenderReport(detail bool) string {
	t := NewTable()
	if detail {
		t.SetHeader(DetailStatusHeader)
		t.AddRow(this.GetStatusRow(false, true))
	} else {
		t.SetHeader(SimpleStatusHeader)
		t.AddRow(this.GetStatusRow(false, false))
	}

	summaryStr := t.Render()

	openOrderTableStr := this.RenderOrderTable(this.GetOpenOrderTable())

	if detail {
		filledOrderStr := "[no filled orders]"
		fot := NewTable()
		fot.SetHeader([]string{"Grid Index", "Buy OrderID", "Buy Time", "Buy Price", "Buy Qty", "Sell OrderID", "Sell Time", "Sell Price", "Sell Qty", "Delta %", "Profit"})
		for _, row := range this.GetFilledOrderRows() {
			fot.AddRow(row)
		}
		if len(fot.Rows) > 1 {
			filledOrderStr = fot.Render()
		}

		closeOrderStr := "[no filled close orders]"
		fot = NewTable()
		fot.SetHeader([]string{"Category", "OrderID", "Side", "Exe. Time", "Exe. Price", "Exe. Qty"})
		for _, order := range this.CloseOrders {
			if order.IsFilled() {
				fot.AddRow([]string{
					order.GetString(ExtKeyCategory),
					order.OrderID,
					string(order.Side),
					utils.FormatShortTimeStr(order.UpdateTime, false),
					this.formatPrice(order.ExecPrice),
					this.formatQty(order.ExecQty),
				})
			}
		}
		if len(fot.Rows) > 1 {
			closeOrderStr = fot.Render()
		}

		return fmt.Sprintf("%s\n\n%s\n\n%s\n\n%s", summaryStr, openOrderTableStr, filledOrderStr, closeOrderStr)
	} else {
		return fmt.Sprintf("%s\n\n%s", summaryStr, openOrderTableStr)
	}
}

func (this *Grid) isTheSamePrice(p1, p2 float64) bool {
	// 相差不应超过 TICK_SIZE * 2 或 p2 * 0.0002
	delta := p2 * 0.0002
	if instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType(), this.Symbol); instrument != nil {
		delta = instrument.TickSize * 2
	}
	return math.Abs(p1-p2) < delta
}

func (this *Grid) checkSameQty(q1, q2 float64) bool {
	// 因为手续费影响，容差在千2 内
	ratio := 0.002
	if this.getBaseFeeRate(exchange.OrderSideBuy, this.OrderType()) > 0 {
		feeRate := this.getBaseFeeRate(exchange.OrderSideBuy, this.OrderType()) * 1.01
		ratio = math.Max(ratio, feeRate)
	}
	delta := q2 * ratio
	return math.Abs(q1-q2) < delta
}

func (this *Grid) formatPrice(price float64) string {
	return this.controller.Exchange.FormatPrice(this.InstrumentType(), this.Symbol, price)
}

func (this *Grid) formatQty(qty float64) string {
	if this.controller.Config.ShowFutureQtyAsValue {
		return this.formatQtyAsValue(qty)
	} else {
		return this.controller.Exchange.FormatQty(this.InstrumentType(), this.Symbol, qty)
	}
}

func (this *Grid) formatQtyAsValue(qty float64) string {
	instrument, _ := this.controller.Exchange.GetInstrument(this.InstrumentType(), this.Symbol)
	if instrument == nil {
		this.Errorf("%s %s instrument not found", this.InstrumentType(), this.Symbol)
		return this.controller.Exchange.FormatQty(this.InstrumentType(), this.Symbol, qty)
	}
	if instrument.ContractSize == 0 {
		return this.controller.Exchange.FormatQty(this.InstrumentType(), this.Symbol, qty)
	}
	qty = instrument.ContractSize * qty

	places := 2
	_places := exchange.DecimalBit(instrument.ContractSize)
	if _places > -1 {
		places = _places
	}
	if places > 0 {
		qty = math.Round(qty/instrument.ContractSize) * instrument.ContractSize
	}
	return fmt.Sprintf("%0."+cast.ToString(places)+"f", qty)
}

func (this *Grid) GetFilledOrderRows() (rows [][]string) {
	orderPairs := this.GetFilledOrderPairs()
	for _, pair := range orderPairs {
		buyOrder := pair[0]
		sellOrder := pair[1]
		row := []string{}
		if this.Reverse {
			row = append(row, fmt.Sprintf("%d", sellOrder.GetInt(ExtKeyGridIndex)))
		} else {
			row = append(row, fmt.Sprintf("%d", buyOrder.GetInt(ExtKeyGridIndex)))
		}
		if buyOrder != nil {
			row = append(row, buyOrder.OrderID)
			row = append(row, utils.FormatShortTimeStr(buyOrder.UpdateTime, false))
			row = append(row, this.formatPrice(buyOrder.ExecPrice))
			row = append(row, this.formatQty(buyOrder.ExecQty))
		} else {
			row = append(row, "", "", "", "")
		}
		if sellOrder != nil {
			row = append(row, sellOrder.OrderID)
			row = append(row, utils.FormatShortTimeStr(sellOrder.UpdateTime, false))
			row = append(row, this.formatPrice(sellOrder.ExecPrice))
			row = append(row, this.formatQty(sellOrder.ExecQty))
		} else {
			row = append(row, "", "", "", "")
		}

		if sellOrder != nil && buyOrder != nil {
			row = append(row, fmt.Sprintf("%.2f%%", 100*(sellOrder.ExecPrice/buyOrder.ExecPrice-1)))
			profit := this.Qty2Size(sellOrder.ExecPrice, sellOrder.ExecQty) - this.Qty2Size(buyOrder.ExecPrice, buyOrder.ExecQty)
			if this.KeepProfitRatio > 0 {
				if this.Reverse {
					profit = this.Qty2Size(sellOrder.ExecPrice, buyOrder.ExecQty) - this.Qty2Size(buyOrder.ExecPrice, buyOrder.ExecQty)
				} else {
					profit = this.Qty2Size(sellOrder.ExecPrice, sellOrder.ExecQty) - this.Qty2Size(buyOrder.ExecPrice, sellOrder.ExecQty)
				}
			}
			row = append(row, fmt.Sprintf("%.2f %s", profit, this.QuoteAsset))
		} else {
			row = append(row, "", "")
		}

		rows = append(rows, row)
	}
	return
}

// 按 UpdateTime 降序排列
func (this *Grid) GetFilledOrderPairs() []OrderPair {
	orderPairs := []OrderPair{}

	if this.Reverse {
		filledSellOrders := []*Order{}
		for _, o := range this.SellOrders {
			if o.IsFilled() {
				filledSellOrders = append(filledSellOrders, o)
			}
		}
		sort.SliceStable(filledSellOrders, func(i, j int) bool {
			o1 := filledSellOrders[i]
			o2 := filledSellOrders[j]
			return o2.UpdateTime.Before(*o1.UpdateTime)
		})
		// 单独处理 TakeProfit 和 StopLoss 的订单，这种订单一定没有 RefOrderID，显示在最上面
		for _, o := range this.BuyOrders {
			if utils.CSVContains(fmt.Sprintf("%s/%s", OrderCategoryTakeProfit, OrderCategoryStopLoss), o.GetString(ExtKeyCategory), "/") {
				orderPairs = append(orderPairs, OrderPair{nil, o})
			}
		}
		for _, sellOrder := range filledSellOrders {
			var refBuyOrder *Order
			for _, o := range this.BuyOrders {
				if o.IsFilled() && strings.Contains(o.GetString(ExtKeyRefOrderID), sellOrder.OrderID) {
					refBuyOrder = o
					break
				}
			}
			orderPairs = append(orderPairs, OrderPair{refBuyOrder, sellOrder})
		}

	} else {
		filledBuyOrders := []*Order{}
		for _, bo := range this.BuyOrders {
			if bo.IsFilled() {
				filledBuyOrders = append(filledBuyOrders, bo)
			}
		}
		sort.SliceStable(filledBuyOrders, func(i, j int) bool {
			o1 := filledBuyOrders[i]
			o2 := filledBuyOrders[j]
			return o2.UpdateTime.Before(*o1.UpdateTime)
		})
		// 单独处理 TakeProfit 和 StopLoss 的订单，这种订单一定没有 RefOrderID，显示在最上面
		for _, o := range this.SellOrders {
			if utils.CSVContains(fmt.Sprintf("%s/%s", OrderCategoryTakeProfit, OrderCategoryStopLoss), o.GetString(ExtKeyCategory), "/") {
				orderPairs = append(orderPairs, OrderPair{nil, o})
			}
		}
		for _, bo := range filledBuyOrders {
			var so *Order
			for _, o := range this.SellOrders {
				if o.IsFilled() && strings.Contains(o.GetString(ExtKeyRefOrderID), bo.OrderID) {
					so = o
					break
				}
			}
			orderPairs = append(orderPairs, OrderPair{bo, so})
		}
	}
	return orderPairs
}

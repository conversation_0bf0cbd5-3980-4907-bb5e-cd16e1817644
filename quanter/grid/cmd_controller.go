package grid

import (
	"encoding/json"
	"fmt"
	"path"
	"strconv"
	"strings"

	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/backscanner"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

// 设置配置项命令

type GridCommand struct {
	command.Command
	controller *GridController
}

// 需组合使用才能调用到 PrePrepare, PreDo
type CheckExchangeGridCommand GridCommand

func (this *CheckExchangeGridCommand) PrePrepare() bool {
	return this.controller.CheckExchangeReady(true)
}

func (this *CheckExchangeGridCommand) PreDo() bool {
	return this.controller.CheckExchangeReady(true)
}

// 启动程序
type LaunchGridCommand GridCommand

func NewLaunchGridCommand(controller *GridController) *LaunchGridCommand {
	cmd := &LaunchGridCommand{
		Command: command.Command{
			Name:            "launch",
			Instruction:     "`.launch Password GoogleAuthCode ` 启动程序",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          3,
			AuthcodePos:     -1, // launch 命令不能在解密主密码前校验 authcode，所以把 authCode 当成是普通参数处理，跳过统一的校验流程
			Sensitive:       true,
		},
		controller: controller,
	}
	return cmd
}

func (this *LaunchGridCommand) Do() bool {
	password := this.Args[0]
	authCode := this.Args[1]
	success := this.controller.Launch(password, authCode)
	if success {
		go this.controller.InitStartGrids()
		this.SendMsgf("启动成功。")
	}
	return true
}

// 当前程序运行状态
type StatusGridCommand GridCommand

func NewStatusGridCommand(controller *GridController) *StatusGridCommand {
	cmd := &StatusGridCommand{
		Command: command.Command{
			Name:            "status",
			Alias:           []string{"s"},
			Instruction:     "`.status(+) all/SymbolCode/gridID,groupID…[可选]` 查看运行状态",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *StatusGridCommand) Do() bool {
	var grids []*Grid
	if len(this.Args) == 0 || strings.EqualFold(this.Args[0], "all") {
		grids = this.controller.Grids
	} else if len(this.Args) > 0 {
		isSymbolCode := false
		if symbolCode, err := exchange.NewSymbolCode(this.Args[0], ""); err == nil {
			for _, grid := range this.controller.Grids {
				if grid.SymbolCode.Code == symbolCode.Code {
					grids = append(grids, grid)
					isSymbolCode = true
				}
			}
		}

		if !isSymbolCode {
			ids := strings.Split(this.Args[0], ",")
			for _, id := range ids {
				gs := this.controller.GetGridsByID(id, false)
				if len(gs) == 0 {
					this.ErrorMsgf("没有找到网格 %s", id)
					continue
				}
				grids = append(grids, gs...)
			}
		}
	}

	this.SendMsgf("```%s```", RenderStatus(grids, this.IsMore()))

	this.controller.handleOrders()
	for _, grid := range grids {
		grid.updateSnapshot()
	}
	return true
}

type HistoryGridCommand GridCommand

func NewHistoryGridCommand(controller *GridController) *HistoryGridCommand {
	cmd := &HistoryGridCommand{
		Command: command.Command{
			Name:            "history",
			Alias:           []string{"his"},
			Instruction:     "`.history(+)` 查看网格机历史",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *HistoryGridCommand) Do() bool {
	historyMsg := "[No Grid History]"
	t := NewTable()
	rows := this.controller.GetHistoryRows("")
	t.SetHeader(SimpleStatusHeader)
	for _, row := range rows {
		t.AddRow(row)
	}
	if len(t.Rows) > 1 {
		historyMsg = t.Render()
	}
	this.SendMsgf("```%s```", historyMsg)
	return true
}

type ConfigGridCommand GridCommand

func NewConfigGridCommand(controller *GridController) *ConfigGridCommand {
	cmd := &ConfigGridCommand{
		Command: command.Command{
			Name:  "config",
			Alias: []string{"cfg"},
			Instructions: []string{
				"`.config show` 查看当前运行参数",
				"`.config set field1=value1,field2=value2 ?` 设置配置 Field 字段值",
			},
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ConfigGridCommand) Prepare() bool {
	args := this.GetArgs()
	subcommand := this.GetSubcommand()
	switch subcommand {
	case "set":
		configStr := args[0]
		if !strings.Contains(configStr, "=") {
			this.ErrorMsgf("请按 field=value 设置配置。")
			return false
		}

		if _, correctedConfigStr, err := baseconfig.ParseConfigsFromString(this.controller.Config, configStr); err != nil {
			this.ErrorMsgf("解析配置错误：%s", err)
			return false
		} else {
			this.SendMsgf("设置配置 %s", correctedConfigStr)
			return true
		}
	case "show":
		return true
	default:
		return false
	}
}

func (this *ConfigGridCommand) Do() bool {
	args := this.GetArgs()
	subcommand := this.GetSubcommand()
	switch subcommand {
	case "show":
		this.SendMsgf("Build: %s, 运行参数\n```%s```", this.controller.BuildInfo(), this.controller.Config.ToTable())
		return true
	case "set":
		configStr := args[0]
		if _, err := this.controller.SaveConfig(configStr); err != nil {
			this.ErrorMsgf("设置配置错误：%s", err)
			return false
		}
		return true
	}
	return false
}

type SetCoinsGridCommand struct {
	CheckExchangeGridCommand
}

func NewSetCoinsGridCommand(controller *GridController) *SetCoinsGridCommand {
	cmd := &SetCoinsGridCommand{
		CheckExchangeGridCommand: CheckExchangeGridCommand{
			Command: command.Command{
				Name:            "setCoins",
				Instruction:     "`.setCoins coin1,coin2` 设置允许的币种",
				RequiresConfirm: true,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *SetCoinsGridCommand) Prepare() bool {
	coins := strings.Split(this.Args[0], ",")
	if _, invalidCoins, err := this.controller.Exchange.CheckValidCoins(coins); err != nil {
		this.ErrorMsgf("检查币种请求错误：%s", err)
		return false
	} else {
		if len(invalidCoins) != 0 {
			this.ErrorMsgf("交易所 %s 不支持币种 %s ", this.controller.Exchange.GetName(), strings.Join(invalidCoins, ","))
			return false
		}
		return true
	}
}

func (this *SetCoinsGridCommand) Do() bool {
	coins := strings.Split(this.Args[0], ",")
	if validCoins, invalidCoins, err := this.controller.Exchange.CheckValidCoins(coins); err != nil {
		this.ErrorMsgf("检查币种请求错误：%s", err)
		return false
	} else {
		if len(invalidCoins) != 0 {
			this.ErrorMsgf("交易所 %s 不支持币种 %s ", this.controller.Exchange.GetName(), strings.Join(invalidCoins, ","))
			return false
		}
		this.controller.Config.AllowedSymbolPrefixs = validCoins
		this.controller.Config.Save()
		this.SendMsgf("允许的币种已设为：%s，可以通过 `.config` 查询。", strings.Join(validCoins, ","))
		return true
	}
}

type SetMinMarginRatioCommand GridCommand

func NewSetMinMarginRatioCommand(controller *GridController) *SetMinMarginRatioCommand {
	return &SetMinMarginRatioCommand{
		Command: command.Command{
			Name:            "setMinMarginRatio",
			Alias:           []string{"smr"},
			Instruction:     "`.setMinMarginRatio ratio GoogleAuthCode` 设置余额与保证金最小比例",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		controller: controller,
	}
}

func (this *SetMinMarginRatioCommand) Do() bool {
	ratio, _ := strconv.ParseFloat(this.Args[0], 64)
	if ratio <= 0 || ratio > 10 {
		this.controller.ErrorMsgf("ratio 需要在 0 ~ 10 以内")
		return false
	}
	this.controller.Config.MinMarginRatio = ratio
	this.controller.Config.Save()
	this.controller.SendMsgf("设置完成")
	return true
}

type MarginRatioControllerCommand struct {
	CheckExchangeGridCommand
}

func NewMarginRatioControllerCommand(controller *GridController) *MarginRatioControllerCommand {
	cmd := &MarginRatioControllerCommand{
		CheckExchangeGridCommand: CheckExchangeGridCommand{
			Command: command.Command{
				Name:            "marginRatio",
				Alias:           []string{"mr"},
				Instruction:     "`.marginRatio` 查看保证金比例",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *MarginRatioControllerCommand) Do() bool {
	s := this.controller.getOpenMarginSummary(false)
	if s == "" {
		this.SendMsgf("没有运行中的合约网格")
	} else {
		this.SendMsgf("```%s```", s)
	}
	return true
}

type PerformanceCommand struct {
	CheckExchangeGridCommand
}

func NewPerformanceCommand(controller *GridController) *PerformanceCommand {
	cmd := &PerformanceCommand{
		CheckExchangeGridCommand: CheckExchangeGridCommand{
			Command: command.Command{
				Name:            "performance",
				Alias:           []string{"perf"},
				Instruction:     "`.performance now/all/symbolCode csv[可选] ` 查看表现快照记录",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          2,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *PerformanceCommand) Do() bool {
	symbol := this.Args[0]
	var symbolCode *exchange.SymbolCode
	if code, err := this.controller.NewSymbolCode(symbol); err == nil {
		symbolCode = code
		instrumentType := symbolCode.InstrumentType()
		if instrumentType == exchange.Spot {
			if spotSymbol, err := this.controller.Exchange.TranslateSymbolCodeToSpotSymbol(symbolCode); err == nil {
				symbol = spotSymbol
			} else {
				this.ErrorMsgf("解析 SymbolCode 出错，error: %s", err)
				return false
			}
		} else {
			if futureSymbol, err := this.controller.Exchange.TranslateSymbolCodeToFutureSymbol(symbolCode); err == nil {
				symbol = futureSymbol
			} else {
				this.ErrorMsgf("解析 SymbolCode 出错，error: %s", err)
				return false
			}
		}
	} else if !strings.EqualFold(symbol, "ALL") && !strings.EqualFold(symbol, "now") {
		this.ErrorMsgf("解析 SymbolCode 出错，error: %s", err)
		return false
	}

	records := []Performance{}
	currentPfms := this.controller.snapshootPerformance()

	if strings.EqualFold(symbol, "now") {
		for _, pfm := range currentPfms {
			records = append(records, pfm)
		}
	} else {
		archivePath := path.Join(this.controller.ConfigPath, this.controller.storage.getPerformanceFileName(symbolCode))
		count := 0
		limit := 100
		err := backscanner.BackScan(archivePath, func(line []byte) bool {
			if !strings.Contains(string(line), "{") {
				return true
			}
			pfm := &Performance{}
			err := json.Unmarshal(line, pfm)
			if err != nil {
				this.controller.Errorf("unmarshal failed, error: %s", err)
				// 读取出错跳过，而不是终止
				return true
			}
			records = append(records, *pfm)
			count += 1
			return count < limit
		})
		if err != nil {
			this.ErrorMsgf("读取 Performance 记录出错，error: %s", err)
			return false
		}

		// 首行插入当前表现
		for _, pfm := range currentPfms {
			if (symbolCode == nil && pfm.SymbolCode == nil) ||
				(symbolCode != nil && pfm.SymbolCode != nil && symbolCode.Code == pfm.SymbolCode.Code) {
				records = append(records[:1], records[0:]...)
				records[0] = pfm
				break
			}
		}
	}

	if len(records) > 0 {
		t := exchange.NewTable()
		t.SetHeader([]string{"SymbolCode", "InitWorth", "Worth", "Position", "Pos. USD", "Opens", "Closes", "PNL", "Grid PNL", "Holding PNL", "Unr. PNL", "Record Time"})
		for _, r := range records {
			code := "All"
			positionStr := fmt.Sprintf("%.2f", r.Position)
			if r.SymbolCode != nil {
				code = r.SymbolCode.Code
				if r.Symbol != "" {
					positionStr = this.controller.Exchange.FormatQty(r.SymbolCode.InstrumentType(), r.Symbol, r.Position)
				}
			}
			t.AddRow([]string{
				code,
				fmt.Sprintf("%.2f", r.InitWorth),
				fmt.Sprintf("%.2f", r.Worth),
				positionStr,
				fmt.Sprintf("%.2f", r.PositionInUSD),
				fmt.Sprintf("%d", r.Opens),
				fmt.Sprintf("%d", r.Closes),
				fmt.Sprintf("%.2f", r.PNL),
				fmt.Sprintf("%.2f", r.GridPNL),
				fmt.Sprintf("%.2f", r.HoldingPNL),
				fmt.Sprintf("%.2f", r.UnrealisedPNL),
				utils.FormatShortTimeStr(r.Time, false),
			})
		}

		if len(this.Args) == 2 && strings.EqualFold(this.Args[1], "csv") {
			content := ""
			for _, r := range t.Rows {
				content += strings.Join(r, ",") + "\n"
			}
			this.SendFileMessage(fmt.Sprintf("Performance %s 记录", symbol), content, "")
		} else {
			this.SendMsgf("Performance %s 记录：\n```%s```", symbol, t.Render())
		}
		return true
	} else {
		this.SendMsgf("没有 Performance 的记录。")
		return false
	}
}

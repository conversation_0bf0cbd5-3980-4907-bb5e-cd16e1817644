package grid

import (
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/binance"
	"github.com/wizhodl/quanter/exchange/bybit"
	"github.com/wizhodl/quanter/exchange/gateway"
	"github.com/wizhodl/quanter/exchange/hyperliquid"
	"github.com/wizhodl/quanter/exchange/okex"
	"github.com/wizhodl/quanter/messenger"
	"github.com/wizhodl/quanter/orderctrl"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"
)

type GridController struct {
	base.BaseController

	Config     *GridControllerConfig
	Grids      []*Grid
	GridGroups []*GridGroup

	storage                   *GridStorage
	handleMutex               *xsync.MapOf[string, *sync.Mutex]
	checkPositionErrStartTime *time.Time
	getOrderController        func() *orderctrl.OrderController
}

func NewGridController(id string, debug bool, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath string, parentMessenger *messenger.SlackMessenger, managerID string, getOrderController func() *orderctrl.OrderController) (*GridController, error) {
	controller := &GridController{
		BaseController: base.BaseController{
			BaseResponder: command.NewBaseResponder(debug, commitHash, buildTime, id, nil, ""),
			ID:            id,
			RefID:         exchange.NewRandomID(),
			ConfigPath:    configPath,
		},
		handleMutex:        xsync.NewMapOf[*sync.Mutex](),
		getOrderController: getOrderController,
	}
	controller.Controllable = controller

	controller.Setup(debug, parentMessenger, managerID)

	config, err := NewGridControllerConfig(controller)
	if err != nil {
		return nil, err
	}
	controller.Config = config
	if logDirPath != "" {
		config.LogDirPath = logDirPath
	}

	SetupGridStroage(controller)

	controller.AddCommands([]command.Commander{
		NewStatusGridCommand(controller),
		NewHistoryGridCommand(controller),
		NewReportGridCommand(controller),
		NewPerformanceCommand(controller),
		NewPauseGridCommand(controller),
		NewResumeGridCommand(controller),
		NewCloseGridCommand(controller),
		NewCloseAllGridCommand(controller),
		NewCleanGridCommand(controller),
		NewMarketOrderCommand(controller),
		NewArithmeticGridCommand(controller),
		NewArithmeticKeepProfitGridCommand(controller),
		NewGeometricGridCommand(controller),
		NewGeometricKeepProfitGridCommand(controller),
		NewPyramidGridCommand(controller),
		NewPyramidKeepProfitGridCommand(controller),
		NewInverseHedgedGridCommand(controller),
		NewSetStopLossPriceCommand(controller),
		NewSetTakeProfitPriceCommand(controller),
		cmds.NewPagerCommand(),
		NewAliasGridCommand(controller),
		NewGroupGridCommand(controller),
		NewSnapshotsGridCommand(controller),
		NewPositionGridCommand(controller),
		NewTradesGridCommand(controller),
		NewConfirmOrdersCommand(controller),
		NewSetMinMarginRatioCommand(controller),
		NewSetMarginCallPriceGridCommand(controller),
		NewMarginRatioControllerCommand(controller),
		NewSetCoolGridCommand(controller),
		cmds.NewPagerCommand(),
		NewConfigGridCommand(controller),
		NewSetCoinsGridCommand(controller),
		cmds.NewHoldingsCommand(controller),
		cmds.NewAssetsCommand(controller, controller.storage),
		cmds.NewMuteCommand(),
		cmds.NewDebugCommand(),
		cmds.NewLogCommand(controller.Config.LogDirPath, controller.GetLogFilename()),
		cmds.NewDownloadLogCommand(controller.Config.LogDirPath, controller.GetLogFilename()),
		cmds.NewDownloadStorageCommand(controller.ConfigPath, fmt.Sprintf("%s.grid_storage", controller.ID)),
		cmds.NewPriceTriggersCommand(controller),
		cmds.NewPriceWatchCommand(controller),
		cmds.NewStackTraceCommand(),
		cmds.NewDeleteErrorsCommand(),
	})

	if controller.Standalone() {
		// 注册 commander 要在
		// 根据命令行参数更新 config 中的部分字段
		if releaseBinaryDirPath != "" {
			config.ReleaseBinaryDirPath = releaseBinaryDirPath
		}
		// 初始化时检查 ReleaseBinaryDir 是否可以写，以免后续 releases 相关命令每次都要检查
		if utils.CheckReleaseBinaryDirPath(controller.Config.ReleaseBinaryDirPath) {
			controller.AddCommands([]command.Commander{
				cmds.NewReleaseCommand(controller.Config.ReleaseBinaryDirPath, controller.ID),
				cmds.NewListVersionCommand(controller.Config.ReleaseBinaryDirPath, controller.ID),
				cmds.NewUseVersionCommand(controller.Config.ReleaseBinaryDirPath, controller.ID),
			})
		}
	}

	// 尝试以 Debug 模式启动，如果不能以 Debug 模式启动，会要求以 .launch 模式启动
	debugLaunched := controller.TryLaunchWithDebug()
	if debugLaunched {
		go controller.InitStartGrids()
	} else {
		controller.AddCommands([]command.Commander{NewLaunchGridCommand(controller)})
	}

	controller.Messenger.SetSensitiveCommands(controller.GetCommandProcessor().SensitiveCommands)

	return controller, nil
}

func (this *GridController) InitStartGrids() {
	apiSecret := this.GetApiSecret()
	withdrawApiSecret := this.GetWithdrawApiSecret()
	var backOff backoff.BackOff
	if this.IsExchange(exchange.CTP) {
		backOff = backoff.NewConstantBackOff(time.Second * 5)
	} else {
		exp := backoff.NewExponentialBackOff()
		exp.InitialInterval = time.Second * 10
		exp.MaxInterval = time.Minute * 5
		exp.MaxElapsedTime = 0 // It never stops if MaxElapsedTime == 0.
		backOff = exp
	}
	alerted := false
	backoff.Retry(func() error {
		if this.IsClosed() {
			return nil
		}
		err := this.InitAPI(apiSecret, withdrawApiSecret)
		if err != nil {
			this.Warnf("init api failed, error: %s", err)
			if !alerted { // 警报一次
				this.ErrorMsgf("初始化 API 错误，稍后将自动重试。错误信息：%s", err)
				alerted = true
			}
		} else {
			if err := exchange.TestTranslateSymbolCode(this.Exchange); err != nil {
				this.ErrorMsgf("symbol code 测试不通过: %s", err)
			}
		}
		return err
	}, backOff)

	if this.IsClosed() {
		return
	}

	this.Grids = []*Grid{}
	this.GridGroups = this.storage.GridGroups

	for _, grid := range this.storage.Grids {
		if grid.Status != Closed {
			if newGrid, err := NewGrid(this, grid.SymbolCode, grid.Symbol, grid.Strategy, grid.InitQty, grid.UseStopLimit, grid.Reverse, grid); err == nil {
				this.Grids = append(this.Grids, newGrid)
			} else {
				this.ErrorMsgf("初始化 %s(%s) 失败: %s", grid.Symbol, grid.RefID, err)
			}
		}
	}

	this.handleOrders()
}

func (this *GridController) marginUpdatedCallback(userMargin *exchange.UserMargin, currency string) {
}

func (this *GridController) orderUpdatedCallback(order *exchange.Order) {
	for _, grid := range this.Grids {
		if order.Symbol == grid.Symbol {
			grid.orderUpdatedCallback()
		}
	}
}

func (this *GridController) priceTriggeredCallback(priceTrigger *exchange.PriceTrigger) {
	this.Infof("price triggered callback: %#v", priceTrigger)
	if priceTrigger.Source == "manual" {
		for _, p := range this.storage.PriceTriggers {
			if p.ID == priceTrigger.ID {
				p.Triggered = true
				p.TriggeredPrice = priceTrigger.TriggeredPrice
			}
		}

		direction := ">"
		if priceTrigger.Direction == exchange.TriggerDirectionLower {
			direction = "<"
		}
		this.SendMsgf("%s[%s] %s %v 已触发", priceTrigger.Symbol, priceTrigger.InstrumentType, direction, priceTrigger.Price)
		return
	}
}

func (this *GridController) InitAPI(apiSecret, withdrawApiSecret secrets.SecretString) error {
	opts := &exchange.Options{
		Host:                   this.Config.Host,
		ApiKey:                 this.Config.ApiKey,
		ApiSecret:              apiSecret,
		WithdrawApiKey:         this.Config.WithdrawApiKey,
		WithdrawApiSecret:      withdrawApiSecret,
		FixerKey:               this.Config.FixerKey,
		IsTestnet:              this.Config.IsTestnet,
		ProxyUrl:               this.Config.ProxyUrl,
		ControllerID:           this.ID,
		MarginUpdatedCallback:  this.marginUpdatedCallback,
		OrderUpdatedCallback:   this.orderUpdatedCallback,
		PriceTriggeredCallback: this.priceTriggeredCallback,
		DataPath:               this.ConfigPath,
	}

	var instrumentTypes []exchange.InstrumentType

	if strings.EqualFold(this.Config.ExchangeName, exchange.OKEx) {
		instrumentTypes = []exchange.InstrumentType{exchange.Spot, exchange.USDXMarginedFutures}
		client, err := okex.NewOKEx(opts)
		if err != nil {
			return fmt.Errorf("初始化 OKEX 失败，error: %s", err)
		}

		if cfg, err := client.GetAccountConfig(exchange.USDXMarginedFutures); err != nil {
			return fmt.Errorf("初始化 OKEX 失败，获取用户配置错误：%s", err)
		} else {
			if cfg.MarginMode != exchange.AccountMarginModeIsolated {
				return fmt.Errorf("账户模式错误，请设置为单币种保证金模式")
			}

			if !cfg.DualPositionSide { // 挂计划委托单必须双向持仓模式
				if err := client.SetDualPositionSide(exchange.USDXMarginedFutures, true); err != nil {
					// 不要返回错误，因为如果有仓位，无法设置仓位模式
					this.Warnf("error setting position mode: %s", err)
					this.WarnMsgf("设置持仓模式错误: %s", err)
				}
			}
		}

		this.Exchange = client
	} else if strings.EqualFold(this.Config.ExchangeName, exchange.Binance) {
		instrumentTypes = []exchange.InstrumentType{exchange.Spot, exchange.USDXMarginedFutures}
		client, err := binance.NewBinance(opts)
		if err != nil {
			return fmt.Errorf("初始化 Binance 失败，error: %s", err)
		}
		if err := client.SetDualPositionSide(exchange.USDXMarginedFutures, false); err != nil {
			if strings.Contains(err.Error(), "-4059") {
				this.Debugf("no need to change position side")
			} else {
				return fmt.Errorf("账户设置单向持仓模式错误，请检测 API 是否正确或已有持仓或挂单: %s", err)
			}
		}
		this.Exchange = client

	} else if strings.EqualFold(this.Config.ExchangeName, exchange.MetaTrader) {
		instrumentTypes = []exchange.InstrumentType{exchange.USDXMarginedFutures}
		client, err := gateway.NewMT(opts, this.ID)
		if err != nil {
			return fmt.Errorf("初始化 MetaTrader 失败，error: %s", err)
		}
		if _, err := client.GetAccountBalances(exchange.USDXMarginedFutures); err != nil {
			return fmt.Errorf("获取余额失败，请检查 API 是否正确. error: %s", err)
		}
		this.Exchange = client

	} else if strings.EqualFold(this.Config.ExchangeName, exchange.InteractiveBrokers) {
		instrumentTypes = []exchange.InstrumentType{exchange.Spot}
		client, err := gateway.NewIB(opts, this.ID)
		if err != nil {
			return fmt.Errorf("初始化 InteractiveBrokers 失败，获取用户配置错误：%s", err)
		}
		if _, err := client.GetAccountBalances(exchange.Spot); err != nil {
			return fmt.Errorf("获取余额失败，请检查 API 是否正确. error: %s", err)
		}
		this.Exchange = client

	} else if this.Config.ExchangeName == exchange.Hyperliquid {
		client, err := hyperliquid.NewHyperliquid(opts)
		if err != nil {
			return fmt.Errorf("初始化 Hyperliquid 失败，error: %s", err)
		}
		instrumentTypes = []exchange.InstrumentType{exchange.Spot, exchange.USDXMarginedFutures}
		if _, err := client.GetAccountBalances(exchange.Spot); err != nil {
			return fmt.Errorf("获取余额失败，请检查 API 是否正确. error: %s", err)
		}
		this.Exchange = client

	} else if this.Config.ExchangeName == exchange.Bybit {
		client, err := bybit.NewBybit(opts)
		if err != nil {
			return fmt.Errorf("初始化 BYBIT 失败，error: %s", err)
		}
		instrumentTypes = []exchange.InstrumentType{exchange.Spot, exchange.USDXMarginedFutures}
		// 检查并设置保证金模式
		if config, err := client.GetAccountConfig(exchange.USDXMarginedFutures); err != nil {
			return fmt.Errorf("初始化 BYBIT 失败，获取用户配置错误：%s", err)
		} else if config.MarginMode != exchange.AccountMarginModeIsolated {
			err := client.SetAccountMarginMode(exchange.USDXMarginedFutures, exchange.AccountMarginModeIsolated)
			if err != nil {
				return fmt.Errorf("设置账户保证金模式失败：%s", err)
			}
		}
		this.Exchange = client

	} else {
		this.ErrorMsgf("交易所 %s 暂未支持", this.Config.ExchangeName)
		return fmt.Errorf("unsupported exchange (%s)", this.Config.ExchangeName)
	}

	if err := this.Exchange.CacheInstruments(true); err != nil {
		return fmt.Errorf("cache instruments error: %s", err)
	}

	this.Exchange.SetHttpDebug(this.Debug)
	go this.Exchange.ConnectWebsocket(instrumentTypes, nil)
	go this.Exchange.CheckPriceTriggerTimeLoop()

	return nil
}

// debug 状态运行 和 定时任务，有多次运行防呆机制
func (this *GridController) Run() {
	tasks := []base.CronTask{
		{
			Spec: "1 12 * * *",
			Cmd: func() {
				this.storage.RecordAssets()
				this.SendStatus()
				this.Exchange.CacheInstruments(true)
			},
		},

		{Spec: "@every 5m", Cmd: this.handleOrders},

		{Spec: "5 16 * * *", Cmd: this.recordPerformance},

		// {Spec: "* * * * *", Cmd: g.recordPerformance},
	}
	this.BaseController.Run(tasks)
}

func (this *GridController) handleOrders() {
	if this.Exchange == nil {
		return
	}
	if err := this.Exchange.CacheInstruments(true); err != nil {
		this.Errorf("cache instruments err: %s", err)
		return
	}

	gridSymbols := []string{}
	symbolTypes := map[string]exchange.InstrumentType{}
	for _, grid := range this.Grids {
		if grid.Status == Running {
			grid.handleOrders()

			if !utils.SliceContainsEqualFold(gridSymbols, grid.Symbol) {
				gridSymbols = append(gridSymbols, grid.Symbol)
				symbolTypes[grid.Symbol] = grid.InstrumentType()
			}
		} else if grid.Status == Cooling {
			if grid.CheckIfCoolEnd() {
				grid.EndCooling()
				grid.Start()
			}
		}
	}

	alertSummary := this.getOpenMarginSummary(true)
	if alertSummary != "" {
		this.AlertMsgf("当前账号余额不足：\n```%s```", alertSummary)
	}

	// 所有 grid 处理完订单之后，检查是否有多余的挂单不在任何 grid 中
	allRemoteOpenOrders := []*Order{}
	for _, symbol := range gridSymbols {
		stopOrders, err := this.Exchange.GetOpenOrders(symbolTypes[symbol], exchange.StopLimit, symbol)
		if err != nil {
			this.ErrorMsgf("获取挂单列表错误: %s", err)
			return
		}
		allRemoteOpenOrders = append(allRemoteOpenOrders, stopOrders...)

		limitOrders, err := this.Exchange.GetOpenOrders(symbolTypes[symbol], exchange.Limit, symbol)
		if err != nil {
			this.ErrorMsgf("获取挂单列表错误: %s", err)
			return
		}
		for _, limitOrder := range limitOrders {
			isTriggeredOrder := false
			for _, stopOrder := range stopOrders {
				if stopOrder.GetString(exchange.ExtKeyTriggeredLimitOrderID) == limitOrder.OrderID {
					isTriggeredOrder = true
					break
				}
			}
			if !isTriggeredOrder { // 忽略触发单生成的限价单
				allRemoteOpenOrders = append(allRemoteOpenOrders, limitOrder)
			}
		}
	}

	for _, remoteOrder := range allRemoteOpenOrders {
		found := false
		for _, grid := range this.Grids {
			if grid.Status != Running {
				continue
			}
			gridOpenOrders := grid.GetLocalOpenOrders()
			for _, localOrder := range gridOpenOrders {
				if localOrder.OrderID == remoteOrder.OrderID {
					found = true
					break
				}
			}
			for _, order := range grid.CloseOrders {
				// closeOrder 可能是 exchangeOrder 也可能是 orderctrl.ConditionalOrder
				// 如果是 closeOrder 是 orderctrl.ConditionalOrder，则通过 order.TriggeredOrderID 来检查远程订单（RemoteOrder 都是 ExchangeOrder）
				if order.IsOpen() && (order.OrderID == remoteOrder.OrderID || order.TriggeredOrderID == remoteOrder.OrderID) {
					found = true
					break
				}
			}
			if found {
				break
			}
		}
		if !found {
			this.WarnMsgf("%s 挂单[%s, %s %s@%s] 不在任何网格中", remoteOrder.Symbol, remoteOrder.OrderID, remoteOrder.Side, strconv.FormatFloat(remoteOrder.Qty, 'f', -1, 64), strconv.FormatFloat(remoteOrder.Price, 'f', -1, 64))
		}
	}

	if this.Config.SpotFeeRate == 0 && this.IsExchange(exchange.Binance) {
		// 如果用 BNB 抵扣费用但 BNB 余额不足 0.01 则提醒
		if _, availableQuoteQty, err := this.Exchange.GetBalance(exchange.Spot, "BNB"); err == nil {
			if availableQuoteQty < 0.01 {
				this.WarnMsgf("BNB 余额不足 0.01，请及时补充或修改 SpotFeeRate 为非 0 值")
			}
		}
	}

	this.checkPosition()
}

// 核对本地存储持仓和交易所持仓是否一致
func (this *GridController) checkPosition() {
	groups := map[string][]*Grid{}
	localPositions := map[string]float64{}
	instrumentTypes := []exchange.InstrumentType{}
	for _, grid := range this.storage.Grids {
		if grid.BaseAssetQty.Total == 0 {
			continue
		}

		if grid.IsFinished() {
			continue
		}

		key := fmt.Sprintf("%s.%s", grid.InstrumentType(), grid.Symbol)
		if grid.InstrumentType() == exchange.Spot {
			key = fmt.Sprintf("%s.%s", grid.InstrumentType(), grid.SymbolCode.Coin())
		}
		groups[key] = append(groups[key], grid)
		localPositions[key] += grid.BaseAssetQty.Total

		if !utils.SliceContains(instrumentTypes, grid.InstrumentType()) {
			instrumentTypes = append(instrumentTypes, grid.InstrumentType())
		}
	}

	exchangeHoldings, err := this.Exchange.GetAccountHoldings(instrumentTypes)
	if err != nil {
		this.AlertMsgf("检查仓位时获取持仓错误: %s", err)
		return
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"Symbol", "Local Position", "Acutal Position", "Delta", "Delta Ratio", "Grids"})
	for _, holding := range exchangeHoldings {
		if holding.InstrumentType == exchange.Spot && this.Config.SkipSpotPositionCheck {
			continue
		}

		key := fmt.Sprintf("%s.%s", holding.InstrumentType, holding.CoinOrSymbol)
		for symbolKey, position := range localPositions {
			if symbolKey == key {
				delta := position - holding.Total
				deltaRatio := delta / holding.Total
				if delta != 0 && math.Abs(deltaRatio) > this.Config.PositionTolerance {
					symbol := ""
					gridIDs := []string{}
					for _, grid := range groups[key] {
						gridIDs = append(gridIDs, grid.RefID)
						symbol = grid.Symbol
					}

					t.AddRow([]string{
						key,
						this.Exchange.FormatQty(holding.InstrumentType, symbol, position),
						this.Exchange.FormatQty(holding.InstrumentType, symbol, holding.Total),
						this.Exchange.FormatQty(holding.InstrumentType, symbol, delta),
						fmt.Sprintf("%.2f%%", deltaRatio*100),
						strings.Join(gridIDs, ","),
					})
				}
			}
		}
	}

	if len(t.Rows) > 1 {
		now := time.Now()
		if this.checkPositionErrStartTime != nil && this.checkPositionErrStartTime.Before(now.Add(time.Minute*-5)) {
			// 5 分钟前就已经出错时再提醒
			this.AlertMsgf("仓位核对超出当前配置 %v:\n```%s```", this.Config.PositionTolerance, t.Render())
		}
		if this.checkPositionErrStartTime == nil {
			this.checkPositionErrStartTime = &now
		}
	} else {
		this.checkPositionErrStartTime = nil
	}
}

func (this *GridController) getOpenMarginSummary(onlyInsufficient bool) (result string) {
	spotGridLockedBalances := map[string]float64{} // 现货网格占用资金
	spotBalances := map[string]float64{}           // 现货账号余额
	requiredMargins := map[string]float64{}        // 合约所需保证金
	futureBalances := map[string]float64{}         // 合约账号余额
	for _, grid := range this.Grids {
		if grid.Status != Running {
			continue
		}
		if grid.InstrumentType() == exchange.Spot {
			balance := 0.0
			for _, order := range grid.BuyOrders {
				if order.IsOpen() {
					balance += order.Price * (order.Qty - order.ExecQty)
				}
			}

			_, found := spotGridLockedBalances[grid.QuoteAsset]
			if found {
				spotGridLockedBalances[grid.QuoteAsset] += balance
			} else {
				spotGridLockedBalances[grid.QuoteAsset] = balance
			}

			_, found = spotBalances[grid.QuoteAsset]
			if !found {
				if total, _, err := this.Exchange.GetBalance(grid.InstrumentType(), grid.QuoteAsset); err != nil {
					spotBalances[grid.QuoteAsset] = 0.0
					this.AlertMsgf("get %s balance failed %s", grid.QuoteAsset, err)
				} else {
					spotBalances[grid.QuoteAsset] = total
				}
			}
		} else if grid.InstrumentType().IsFuture() {
			_, found := requiredMargins[grid.QuoteAsset]
			if found {
				requiredMargins[grid.QuoteAsset] += grid.calculateEstimatedMargin()
			} else {
				requiredMargins[grid.QuoteAsset] = grid.calculateEstimatedMargin()
			}

			_, found = futureBalances[grid.QuoteAsset]
			if !found {
				if total, _, err := this.Exchange.GetBalance(grid.InstrumentType(), grid.QuoteAsset); err != nil {
					futureBalances[grid.QuoteAsset] = 0.0
					this.AlertMsgf("get %s balance failed %s", grid.QuoteAsset, err)
				} else {
					futureBalances[grid.QuoteAsset] = total
				}
			}
		}
	}

	// 如果 InverseHedged 的网格都在运行中，仅需要一半的保证金
	for _, group := range this.GridGroups {
		if group.Type != GroupTypeInverseHedged {
			continue
		}
		groupMargin := 0.0
		running := true
		asset := ""
		for _, gridID := range group.GridIDs {
			grid := this.GetGridByID(gridID, true)
			if grid == nil || grid.Status != Running {
				running = false
				continue
			}
			asset = grid.QuoteAsset
			groupMargin += grid.calculateEstimatedMargin()
		}

		if !running {
			continue
		}

		_, found := requiredMargins[asset]
		if found {
			requiredMargins[asset] -= groupMargin / 2
		}
	}

	minRatio := this.Config.MinMarginRatio
	if minRatio == 0 {
		minRatio = 1
	}

	marginBalances := map[string]float64{} // 合约可用余额，即总余额减去现货挂单占用
	deltaBalances := map[string]float64{}
	assets := []string{}
	for asset, requiredMargin := range requiredMargins {
		marginBalances[asset] = futureBalances[asset]

		// 如果 现货 & 合约 共用同一账号余额，需减去现货网格占用资金部分
		if this.IsExchange(exchange.OKEx) {
			if _, found := spotGridLockedBalances[asset]; found {
				marginBalances[asset] -= spotGridLockedBalances[asset]
			}
		}

		deltaBalances[asset] = marginBalances[asset] - requiredMargin*minRatio
		assets = append(assets, asset)
	}
	sort.SliceStable(assets, func(i, j int) bool {
		return assets[i] < assets[j]
	})
	t := exchange.NewTable()
	t.SetHeader([]string{"Type", "Asset", "Required Margin", "Min Margin Ratio", "Min Margin", "Acutal Margin", "Ratio", "Delta"})
	for _, asset := range assets {
		include := true
		delta := deltaBalances[asset]
		if onlyInsufficient && delta > 0 {
			include = false
		}
		if include {
			minMargin := requiredMargins[asset] * minRatio
			ratio := marginBalances[asset] / minMargin
			t.AddRow([]string{
				"Future",
				asset,
				this.formatAmount(asset, requiredMargins[asset]),
				fmt.Sprintf("%.0f%%", minRatio*100),
				this.formatAmount(asset, minMargin),
				this.formatAmount(asset, marginBalances[asset]),
				fmt.Sprintf("%.2f%%", ratio*100),
				this.formatAmount(asset, delta),
			})
		}
	}

	for asset, locked := range spotGridLockedBalances {
		balance := spotBalances[asset]
		if this.IsExchange(exchange.OKEx) {
			futureMargin := requiredMargins[asset]
			balance -= futureMargin
		}
		minMargin := locked // 现货所需即挂单所需资金
		minMargin *= minRatio
		delta := balance - minMargin
		ratio := balance / minMargin
		if onlyInsufficient && ratio > 0.99999 {
			continue
		}
		t.AddRow([]string{
			"Spot",
			asset,
			this.formatAmount(asset, locked),
			fmt.Sprintf("%.0f%%", minRatio*100),
			this.formatAmount(asset, minMargin),
			this.formatAmount(asset, balance),
			fmt.Sprintf("%.2f%%", ratio*100),
			this.formatAmount(asset, delta),
		})
	}

	if len(t.Rows) > 1 {
		result = t.Render()
	}
	return
}

func (this *GridController) SendStatus() {
	this.SendMsgf("网格机状态：\n```%s```", RenderStatus(this.Grids, false))
}

func (this *GridController) Close() {
	this.BaseController.Close(func() {
		this.CloseAllGrids(true, true)
	})
}

func (this *GridController) CloseAllGrids(sellBaseCoin bool, force bool) {
	gridsToClose := []*Grid{}
	for _, grid := range this.Grids {
		if grid.Status != Closed {
			// 在此 close 会修改 g.Grids 导致可能无法关闭所有网格
			gridsToClose = append(gridsToClose, grid)
		}
	}

	for _, grid := range gridsToClose {
		grid.ManualClose(sellBaseCoin, force)
	}
}

func (this *GridController) SaveConfigsTo(id string) error {
	if err := this.Config.SaveTo(this.ConfigPath, id, false); err != nil {
		return err
	}
	return nil
}

func (this *GridController) SaveStorageTo(id string) error {
	if err := this.storage.SaveTo(this.ConfigPath, id, true); err != nil {
		return err
	}
	return nil
}

// 停止运行，并删除套利机对应的配置文件
// stopAll 和 launched 设为 false
func (this *GridController) DangerousDelete() {
	this.Close()
	this.Config.Delete()
}

func ParseNumOrPercentage(numOrPercentage string, percentOnly bool, allowNegative bool) (num float64, percent float64, er error) {
	percent = 0.0
	num = 0.0
	if result, isPercent, err := utils.ParseFloatOrPercentage(numOrPercentage, percentOnly, allowNegative); err == nil {
		if isPercent {
			percent = result
		} else {
			num = result
		}
	} else {
		er = err
	}
	return num, percent, er
}

func (this *GridController) GetGridByID(idOrAlias string, includeFromStorage bool) (result *Grid) {
	for _, grid := range this.Grids {
		if strings.EqualFold(grid.RefID, idOrAlias) || strings.EqualFold(grid.Alias, idOrAlias) {
			result = grid
		}
	}
	if result == nil && includeFromStorage {
		for _, grid := range this.storage.Grids {
			if strings.EqualFold(grid.RefID, idOrAlias) || strings.EqualFold(grid.Alias, idOrAlias) {
				result = grid
			}
		}
	}
	return
}

func (this *GridController) GetGroupByID(idOrAlias string, includeFromStorage bool) (result *GridGroup) {
	for _, group := range this.GridGroups {
		if strings.EqualFold(group.RefID, idOrAlias) || strings.EqualFold(group.Alias, idOrAlias) {
			result = group
		}
	}
	if result == nil && includeFromStorage {
		for _, group := range this.storage.GridGroups {
			if strings.EqualFold(group.RefID, idOrAlias) || strings.EqualFold(group.Alias, idOrAlias) {
				result = group
			}
		}
	}
	return
}

func (this *GridController) GetGridsByID(idOrAlias string, includeFromStorage bool) (result []*Grid) {
	result = []*Grid{}

	grid := this.GetGridByID(idOrAlias, includeFromStorage)
	if grid != nil {
		result = append(result, grid)
		return
	}

	group := this.GetGroupByID(idOrAlias, includeFromStorage)
	if group != nil {
		for _, gridID := range group.GridIDs {
			grid := this.GetGridByID(gridID, includeFromStorage)
			if grid != nil {
				result = append(result, grid)
			}
		}
	}
	return
}

func (this *GridController) GetRemoteOpenOrdersByGrid(grid *Grid, orderType exchange.OrderType) ([]*Order, error) {
	allRemoteOpenOrders, err := this.Exchange.GetOpenOrders(grid.InstrumentType(), orderType, grid.Symbol)
	if err != nil {
		return nil, err
	}
	remoteOpenOrders := []*Order{}
	for _, remoteOrder := range allRemoteOpenOrders {
		found := false
		// 筛选掉其他 grid 的挂单
		for _, g := range this.Grids {
			if g.RefID == grid.RefID || g.Symbol != grid.Symbol {
				continue
			}
			gridOpenOrders := g.GetLocalOpenOrders()
			for _, localOrder := range gridOpenOrders {
				if localOrder.OrderID == remoteOrder.OrderID {
					found = true
					break
				}
			}
		}
		if !found {
			remoteOpenOrders = append(remoteOpenOrders, remoteOrder)
		}
	}
	return remoteOpenOrders, nil
}

func (this *GridController) GetInstrumentTypes() []exchange.InstrumentType {
	return this.Exchange.GetSupportedInstrumentTypes()
}

func (this *GridController) RenderAssets() string {
	return this.storage.RenderAssets()
}

func (this *GridController) GetHistoryRows(symbolCodes string) (rows [][]string) {
	rows = [][]string{}
	sort.SliceStable(this.storage.Grids, func(i, j int) bool { return this.storage.Grids[i].CreateTime.After(*this.storage.Grids[j].CreateTime) })
	for _, grid := range this.storage.Grids {
		if symbolCodes == "" || strings.Contains(symbolCodes, grid.SymbolCode.Code) {
			rows = append(rows, grid.GetStatusRow(false, false))
		}
	}
	return rows
}

func (this *GridController) GetReviewRows() [][]string {
	return this.Config.GetGridConfigRows()
}

func (this *GridController) formatAmount(asset string, amount float64) string {
	places := -1
	if strings.EqualFold(asset, this.Config.USDXSymbol) || strings.EqualFold(asset, "USDT") || strings.EqualFold(asset, "USD") {
		places = 2
	} else if strings.EqualFold(asset, "CNY") {
		places = 0
	}
	return strconv.FormatFloat(amount, 'f', places, 64)
}

func (this *GridController) futureCntToValue(instrumentType exchange.InstrumentType, symbol string, futureQty float64) (qty float64) {
	instrument, _ := this.Exchange.GetInstrument(instrumentType, symbol)
	if instrument == nil {
		this.Errorf("%s %s instrument not found", instrumentType, symbol)
		return futureQty
	}
	if instrument.ContractSize == 0 {
		return futureQty
	}
	qty = instrument.ContractSize * futureQty
	return qty
}

func (this *GridController) snapshootPerformance() []Performance {
	if this.Exchange == nil {
		return []Performance{}
	}

	now := time.Now()
	all := Performance{Time: &now}
	symbolPfms := map[string]Performance{}

	ihgGridIDs := []string{}
	// ihg group 优先统计，InitWorth 用平均值
	// InitWorth & Worth 只算非 close 的
	for _, group := range this.storage.GridGroups {
		if group.Type != GroupTypeInverseHedged {
			continue
		}
		var symbolPfm Performance
		initWorth := 0.0
		var symbolCode *exchange.SymbolCode
		for _, gridID := range group.GridIDs {
			grid := this.GetGridByID(gridID, true)
			if grid == nil {
				continue
			}
			ihgGridIDs = append(ihgGridIDs, grid.RefID)
			symbolCode = grid.SymbolCode
			if pfm, found := symbolPfms[grid.SymbolCode.Code]; found {
				symbolPfm = pfm
			} else {
				symbolPfm = Performance{Time: &now, SymbolCode: symbolCode, Symbol: grid.Symbol}
			}

			if grid.Status != Closed {
				initWorth += grid.getRealWorth()
				assetQty := NewAssetPairQty(grid.BaseAssetQty.Total, grid.QuoteAssetQty.Total)
				worth, _ := grid.GetAssetWorthInUSDT(assetQty)
				all.Worth += worth
				symbolPfm.Worth += worth
			}

			if grid.Reverse {
				all.Opens += grid.Report.SaleFilledCount
				all.Closes += grid.Report.BuyFilledCount
				symbolPfm.Opens += grid.Report.SaleFilledCount
				symbolPfm.Closes += grid.Report.BuyFilledCount
			} else {
				all.Opens += grid.Report.BuyFilledCount
				all.Closes += grid.Report.SaleFilledCount
				symbolPfm.Opens += grid.Report.BuyFilledCount
				symbolPfm.Closes += grid.Report.SaleFilledCount
			}
			all.PNL += grid.Report.PNL
			all.GridPNL += grid.Report.GridPNL
			all.HoldingPNL += grid.Report.HoldingPNL
			all.UnrealisedPNL += grid.Report.UnrealisedPNL

			positionWorth, _ := grid.GetAssetWorthInUSDT(NewAssetPairQty(grid.BaseAssetQty.Total, 0))
			all.PositionInUSD += math.Abs(positionWorth)

			symbolPfm.PNL += grid.Report.PNL
			symbolPfm.GridPNL += grid.Report.GridPNL
			symbolPfm.HoldingPNL += grid.Report.HoldingPNL
			symbolPfm.UnrealisedPNL += grid.Report.UnrealisedPNL
			symbolPfm.Position += grid.BaseAssetQty.Total
			symbolPfm.PositionInUSD += positionWorth

			symbolPfms[grid.SymbolCode.Code] = symbolPfm
		}

		if symbolCode == nil {
			continue
		}

		initWorth = initWorth / float64(len(group.GridIDs))
		all.InitWorth += initWorth
		symbolPfm.InitWorth += initWorth
		symbolPfms[symbolCode.Code] = symbolPfm
	}

	for _, grid := range this.storage.Grids {
		if utils.SliceContains(ihgGridIDs, grid.RefID) {
			continue
		}

		var symbolPfm Performance
		if pfm, found := symbolPfms[grid.SymbolCode.Code]; found {
			symbolPfm = pfm
		} else {
			symbolPfm = Performance{Time: &now, SymbolCode: grid.SymbolCode, Symbol: grid.Symbol}
		}

		if grid.Status != Closed {
			all.InitWorth += grid.getRealWorth()
			symbolPfm.InitWorth += grid.getRealWorth()
			assetQty := NewAssetPairQty(grid.BaseAssetQty.Total, grid.QuoteAssetQty.Total)
			worth, _ := grid.GetAssetWorthInUSDT(assetQty)
			all.Worth += worth
			symbolPfm.Worth += worth
		}

		if grid.Reverse {
			all.Opens += grid.Report.SaleFilledCount
			all.Closes += grid.Report.BuyFilledCount
			symbolPfm.Opens += grid.Report.SaleFilledCount
			symbolPfm.Closes += grid.Report.BuyFilledCount
		} else {
			all.Opens += grid.Report.BuyFilledCount
			all.Closes += grid.Report.SaleFilledCount
			symbolPfm.Opens += grid.Report.BuyFilledCount
			symbolPfm.Closes += grid.Report.SaleFilledCount
		}
		all.PNL += grid.Report.PNL
		all.GridPNL += grid.Report.GridPNL
		all.HoldingPNL += grid.Report.HoldingPNL
		all.UnrealisedPNL += grid.Report.UnrealisedPNL

		positionWorth, _ := grid.GetAssetWorthInUSDT(NewAssetPairQty(grid.BaseAssetQty.Total, 0))
		all.PositionInUSD += math.Abs(positionWorth)

		symbolPfm.PNL += grid.Report.PNL
		symbolPfm.GridPNL += grid.Report.GridPNL
		symbolPfm.HoldingPNL += grid.Report.HoldingPNL
		symbolPfm.UnrealisedPNL += grid.Report.UnrealisedPNL
		symbolPfm.Position += grid.BaseAssetQty.Total
		symbolPfm.PositionInUSD += positionWorth

		symbolPfms[grid.SymbolCode.Code] = symbolPfm
	}

	pfms := []Performance{}
	pfms = append(pfms, all)
	for _, pfm := range symbolPfms {
		pfms = append(pfms, pfm)
	}
	return pfms
}

func (this *GridController) recordPerformance() {
	pfms := this.snapshootPerformance()
	for _, pfm := range pfms {
		this.storage.logPerformance(pfm)
	}
}

func (this *GridController) GetBaseConfig() *baseconfig.BaseConfig {
	return &this.Config.BaseConfig
}

func (this *GridController) SaveConfig(configStr string) (correctedConfigStr string, er error) {
	config := this.Config
	if configPairs, _correctedConfigStr, err := baseconfig.SetConfigWithString(config, configStr); err != nil {
		this.ErrorMsgf("设置配置错误：%s", err)
		return "", err
	} else {
		correctedConfigStr = _correctedConfigStr
		for _, pair := range configPairs {
			if strings.EqualFold(pair.Field, "EnableRealtimePrice") {
				enable := strings.EqualFold(pair.Value, "true")
				this.Exchange.SetEnableRealtimePrice(enable)
			}
		}
		this.Config.Save()
	}
	return correctedConfigStr, nil
}

func (this *GridController) GetStorage() base.Storager {
	return this.storage
}

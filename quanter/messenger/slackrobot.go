package messenger

import (
	"bytes"
	"errors"
	"fmt"
	"os"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/spf13/cast"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"

	"github.com/slack-go/slack"
)

const SENSITIVE_CMD_DELETE_SECONDS time.Duration = 5 * time.Second

// Slack 机器人客户端
type SlackMessenger struct {
	id                   string            // slack 机器人 ID
	commanderID          string            // 指令用户ID
	channelIDs           map[string]string // 频道名与频道 ID，key: channelName, value: channelID
	commandsRequiresFile [][]string        // 需要上传文件的命令列表，[[channelID, commandName],...]
	defaultChannelID     string            // 默认频道，如果其他频道发送消息失败，转发到默认频道

	client        *slack.Client // api 客户端
	cleanerClient *slack.Client // 清理消息用的 slack 客户端
	rtm           *slack.RTM    // 实时连接
	debug         bool          // 是否调试

	sensitiveCommands []string // 敏感信息命令列表

	ConnectedCallback   func()                                                                            // 连接回调
	commandHandlers     map[string]func(command string, args ...string)                                   // 指令处理函数，以 channelID 为 key
	commandNameHandlers map[string]func(command string, args ...string)                                   // 指令处理函数，以 channelName 为 key
	messageHandlers     map[string]func(msg string, fileURL string, fileContent []byte, timestamp string) // 消息处理函数，以 channelID 为 key
	messageNameHandlers map[string]func(msg string, fileURL string, fileContent []byte, timestamp string) // 消息处理函数，以 channelName 为 key
}

// 创建一个新的 slack 客户端并返回
func NewSlackMessenger(token secrets.SecretString, commanderID string, cleanerToken secrets.SecretString) *SlackMessenger {

	debug := os.Getenv("QUANTER_SLACK_DEBUG") == "1"
	zlog.Infof("env $QUANTER_SLACK_DEBUG=%s", os.Getenv("QUANTER_SLACK_DEBUG"))

	rbt := &SlackMessenger{
		id:                   utils.NewRandomID(),
		debug:                debug,
		commanderID:          commanderID,
		channelIDs:           map[string]string{},
		commandHandlers:      map[string]func(command string, args ...string){},
		commandNameHandlers:  map[string]func(command string, args ...string){},
		messageNameHandlers:  map[string]func(msg string, fileURL string, fileContent []byte, timestamp string){},
		messageHandlers:      map[string]func(msg string, fileURL string, fileContent []byte, timestamp string){},
		commandsRequiresFile: [][]string{},
		sensitiveCommands:    []string{"launch"},
	}
	if rbt.debug {
		rbt.client = slack.New(string(token), slack.OptionDebug(true))
		if cleanerToken != "" {
			rbt.cleanerClient = slack.New(string(cleanerToken), slack.OptionDebug(true))
			rbt.Infof("slack cleaner client created")
		}
	} else {
		rbt.client = slack.New(string(token))
		if cleanerToken != "" {
			rbt.cleanerClient = slack.New(string(cleanerToken))
			rbt.Infof("slack cleaner client created")
		}
	}

	go rbt.jobs()
	return rbt
}

func (this *SlackMessenger) jobs() {
	// call every 1 minutes to cleanup password in 5 minutes ago
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		secrets.DangerouslyCallbackWithPassword(func(password secrets.SecretString) error {
			for _, channelID := range this.channelIDs {
				this.DeleteMsgs(channelID, []string{password.Reveal()}, time.Now().Add(-time.Minute), utils.Ptr(time.Now().Add(-5*time.Minute)))
			}
			return nil
		})
	}
}

func (this *SlackMessenger) CleanupPasswordsInAllChannels() {
	secrets.DangerouslyCallbackWithPassword(func(password secrets.SecretString) error {
		for _, channelID := range this.channelIDs {
			this.DeleteMsgs(channelID, []string{password.Reveal()}, time.Now().Add(-time.Minute), utils.Ptr(time.Time{}))
		}
		return nil
	})
}

func (this *SlackMessenger) Debugf(format string, args ...interface{}) {
	raw := fmt.Sprintf(format, args...)
	zlog.Debugf("[slackrobot] (%s) %s", this.id, raw)
}

func (this *SlackMessenger) Infof(format string, args ...interface{}) {
	raw := fmt.Sprintf(format, args...)
	zlog.Infof("[slackrobot] (%s) %s", this.id, raw)
}

func (this *SlackMessenger) Errorf(format string, args ...interface{}) {
	raw := fmt.Sprintf(format, args...)
	zlog.Errorf("[slackrobot] (%s) %s", this.id, raw)
}

func (this *SlackMessenger) Warnf(format string, args ...interface{}) {
	raw := fmt.Sprintf(format, args...)
	zlog.Warnf("[slackrobot] (%s) %s", this.id, raw)
}

func (this *SlackMessenger) SetSensitiveCommands(cmds []string) {
	for _, c := range cmds {
		if !utils.SliceContainsEqualFold(this.sensitiveCommands, c) {
			this.sensitiveCommands = append(this.sensitiveCommands, strings.ToLower(c))
		}
	}
	this.Infof("slack set sensitive commands: [%s]", strings.Join(this.sensitiveCommands, ","))
}

func (this *SlackMessenger) SetDefaultChannelName(channelName string) bool {
	// 检查是否已经注册过了，注册过直接返回成功
	if channelID, found := this.channelIDs[channelName]; found {
		this.defaultChannelID = channelID
		return true
	}
	return false
}

// 可以重复调用
func (this *SlackMessenger) RegisterCommandHandler(channelName string, commandFunc func(command string, args ...string), replace bool) {
	channelName = strings.ToLower(channelName) // 频道名只能小写

	// 检查是否已经注册过了，注册过直接返回成功
	if _, found := this.commandNameHandlers[channelName]; found && !replace {
		this.Infof("register command handler for %s, already exist, replace=%v", channelName, replace)
		return
	}

	// 以 channelName 注册命令处理器，保证成功
	this.commandNameHandlers[channelName] = commandFunc
	// 然后再查找是否有对应的 channelName，如果没有，再下次发送消息的时候会重试
	if _, err := this.RegisterChannelName(channelName, true, false); err != nil {
		this.Errorf("channel not exist %s, error: %s", channelName, err.Error())
	}
}

func (this *SlackMessenger) RegisterMessageHandler(channelName string, messageFunc func(msg string, fileURL string, fileContent []byte, timestamp string), replace bool) {
	channelName = strings.ToLower(channelName) // 频道名只能小写

	// 检查是否已经注册过了，注册过直接返回成功
	if _, found := this.messageNameHandlers[channelName]; found && !replace {
		this.Infof("register message handler for %s, already exist, replace=%v", channelName, replace)
		return
	}

	// 以 channelName 注册命令处理器，保证成功
	this.messageNameHandlers[channelName] = messageFunc
	// 然后再查找是否有对应的 channelName，如果没有，再下次发送消息的时候会重试
	if _, err := this.RegisterChannelName(channelName, false, true); err != nil {
		this.Errorf("channel not exist %s, error: %s", channelName, err.Error())
	}
}

func (this *SlackMessenger) RegisterChannelName(channelName string, forCommand bool, forMessage bool) (string, error) {
	channels, err := this.GetChannels()
	if err != nil {
		return "", fmt.Errorf("fetch slack channels failed: %s", err)
	}

	channelID := ""
	reason := "channel name not found"
	for _, channel := range channels {
		groupConversation := channel.GroupConversation
		conversation := channel.Conversation
		if groupConversation.Name == channelName {
			if groupConversation.Creator == this.commanderID {
				channelID = conversation.ID
				this.channelIDs[channelName] = conversation.ID
				if forCommand {
					this.commandHandlers[conversation.ID] = this.commandNameHandlers[channelName]
				} else if forMessage {
					this.messageHandlers[conversation.ID] = this.messageNameHandlers[channelName]
				}

			} else {
				reason = "channel creator isn't match secrets.S.SlackCommander, please check settings"
			}
		}
	}

	if channelID == "" {
		return "", fmt.Errorf("register channel name failed, reason: %s", reason)
	}
	return channelID, nil
}

func (this *SlackMessenger) GetChannelName(id string) string {
	for channelName, channelID := range this.channelIDs {
		if channelID == id {
			return channelName
		}
	}
	return ""
}

func (this *SlackMessenger) GetChannelID(name string) string {
	if id, found := this.channelIDs[name]; found {
		return id
	}
	return ""
}

func (this *SlackMessenger) RemoveChannel(channelName string) {
	channelName = strings.ToLower(channelName) // 频道名只能小写
	if channelID, ok := this.channelIDs[channelName]; ok {
		delete(this.channelIDs, channelName)
		delete(this.commandHandlers, channelID)
		delete(this.messageHandlers, channelID)
	}
	delete(this.commandNameHandlers, channelName) // 如果不删除，重新 register 的时候应该就无法成功了
	delete(this.messageNameHandlers, channelName)
}

// 根据用户ID返回用户信息
func (this *SlackMessenger) GetUserInfo(userID string) {
	user, err := this.client.GetUserInfo(userID)
	if err != nil {
		this.Infof("%s", err)
		return
	}
	this.Infof("ID: %s, Fullname: %s, Email: %s\n", user.ID, user.Profile.RealName, user.Profile.Email)
}

type SlackChannelCache struct {
	Channels  []slack.Channel
	CacheTime time.Time
}

var slackChannelCache = &SlackChannelCache{}
var slackChannelMutex = &sync.Mutex{}

// TODO: 将返回值重构为不依赖 slack，方便引入 teams 或其他消息客户端
// 获取频道列表
func (this *SlackMessenger) GetChannels() ([]slack.Channel, error) {
	slackChannelMutex.Lock()
	defer slackChannelMutex.Unlock()

	if slackChannelCache.CacheTime.Add(time.Second * 10).After(time.Now()) {
		return slackChannelCache.Channels, nil
	}

	channels := []slack.Channel{}
	var next string
	var res []slack.Channel
	var err error
	for {
		res, next, err = this.client.GetConversations(&slack.GetConversationsParameters{
			Limit:           50,
			ExcludeArchived: true,
			Types:           []string{"private_channel"},
			Cursor:          next,
		})
		if err != nil {
			return nil, err
		}
		channels = append(channels, res...)
		if next == "" {
			break
		}
	}
	slackChannelCache.Channels = channels
	slackChannelCache.CacheTime = time.Now()
	return channels, nil
}

func (this *SlackMessenger) TestCleaner(channelID string) error {
	if this.client == nil || this.cleanerClient == nil {
		return fmt.Errorf("slack client or cleaner client is nil")
	}
	channel, msgTimestamp, _, err := this.client.SendMessage(channelID, slack.MsgOptionText(">>>TestCleaner<<<", false))
	zlog.Debugf("after send message: channelID: %s, channel: %s, msgTimestamp: %s, err: %s", channelID, channel, msgTimestamp, err)
	if err != nil {
		return fmt.Errorf("send message failed, error: %s", err)
	}

	_, _, err2 := this.cleanerClient.DeleteMessage(channel, msgTimestamp)
	zlog.Debugf("after delete message: channelID: %s, channel: %s, msgTimestamp: %s, err: %s", channelID, channel, msgTimestamp, err2)
	if err2 != nil {
		return fmt.Errorf("delete message failed, error: %s", err2)
	}
	return nil
}

// 使用 API 发送消息
func (this *SlackMessenger) SendMessage(channelName, msg string) {
	channelName = strings.ToLower(channelName) // 频道名只能小写
	if channelID, found := this.channelIDs[channelName]; found {
		this.client.SendMessage(channelID, slack.MsgOptionText(msg, false))
	}
}

// 通过实时连接发送消息
func (this *SlackMessenger) SendRTMMessage(channelName, msg string) (bool, error) {
	channelName = strings.ToLower(channelName) // 频道名只能小写
	if this == nil {
		return false, errors.New("no slackRobot")
	}

	if channelID, found := this.channelIDs[channelName]; found {
		this.rtm.SendMessage(this.rtm.NewOutgoingMessage(fmt.Sprintf("*[%s]* %s", channelName, msg), channelID))
		return true, nil
	} else {
		if channelID, err := this.RegisterChannelName(channelName, true, false); err == nil {
			this.rtm.SendMessage(this.rtm.NewOutgoingMessage(fmt.Sprintf("*[%s]* %s", channelName, msg), channelID))
			return true, nil
		} else {
			this.Errorf("channel not exist %s, error: %s", channelName, err.Error())
			if this.defaultChannelID != "" {
				this.rtm.SendMessage(this.rtm.NewOutgoingMessage(fmt.Sprintf("频道 %s 不存在，消息：```*[%s]* %s```", channelName, channelName, msg), this.defaultChannelID))
			}
			return false, errors.New("channel not found")
		}
	}
}

func (this *SlackMessenger) SendFileMessage(channelName, title, msg, comment, ftype string) {
	if this == nil {
		return
	}
	channelName = strings.ToLower(channelName) // 频道名只能小写
	if channelID, found := this.channelIDs[channelName]; found {
		this.client.UploadFile(slack.FileUploadParameters{
			Title:          title,
			Content:        msg,
			Filetype:       ftype,
			InitialComment: fmt.Sprintf("*[%s]* %s", channelName, comment),
			Channels:       []string{channelID},
		})
	}
}

func (this *SlackMessenger) GetFile(url string) ([]byte, error) {
	var file bytes.Buffer
	if err := this.client.GetFile(url, &file); err != nil {
		return nil, err
	} else {
		return file.Bytes(), nil
	}
}

// 实时连接
func (this *SlackMessenger) RTMConnect() {
	zlog.Debugf("Connecting Slack RTM...")
	rtm := this.client.NewRTM()
	this.rtm = rtm
	go rtm.ManageConnection() // 自动管理连接状态，如断线重连

	firstConnected := true

	// 处理 slack 事件
	for msg := range rtm.IncomingEvents {
		switch ev := msg.Data.(type) {
		case *slack.HelloEvent:
			// Ignore hello

		case *slack.ConnectedEvent:
			// 连接成功事件
			zlog.Debugf("slack connected infos: %#v", ev.Info)

			if firstConnected {
				// 首次连接
				time.Sleep(time.Second * 1) // 延迟发送，否则可能发不出去
				if this.ConnectedCallback != nil {
					this.ConnectedCallback()
				}
				firstConnected = false
			}

		case *slack.MessageEvent:
			// 危险：打开日志可能会记录敏感信息
			// zlog.Debugf("slack.MessageEvent: %#v", ev)
			// 消息事件
			for _, channelID := range this.channelIDs {
				if ev.Channel == channelID && ev.User == this.commanderID {
					// 危险：打开日志可能会记录敏感信息
					// this.Infof("Message: %#v", ev)
					go this.handleMessage(ev, channelID)
				}
			}

		case *slack.PresenceChangeEvent:
			zlog.Debugf("slack presence change: %v", ev)

		case *slack.LatencyReport:
			if this.debug {
				zlog.Debugf("slack current latency: %v", ev.Value)
			}
			if ev.Value > time.Duration(time.Second*3) {
				this.Infof("lack latency is high: %v", ev.Value)
			}

		case *slack.RTMError:
			this.Errorf("slack realtime error: %s", ev.Error())

		case *slack.InvalidAuthEvent:
			this.Errorf("slack invalid credentials, check your slack robot token")
			return

		default:
			if this.debug {
				zlog.Debugf("unexpected slack event: %v\n", ev)
			}
		}
	}
}

func (this *SlackMessenger) RegisterCommandRequiresFile(channelName, commandName string) {
	this.commandsRequiresFile = append(this.commandsRequiresFile, []string{channelName, commandName})
}

// 消息处理
func (this *SlackMessenger) handleMessage(msg *slack.MessageEvent, channelID string) {
	this.handleMessageForCommand(msg, channelID)
	this.handleMessageForAdapter(msg, channelID)
}

func (this *SlackMessenger) handleMessageForCommand(msg *slack.MessageEvent, channelID string) {
	// this.Infof("slack message: %s", msg.Text)
	txt := msg.Text
	if txt == "This message contains interactive elements." {
		this.SendRTMMessage(this.GetChannelName(channelID), fmt.Sprintf("slack 系统 bug：%s", txt))
		return
	}
	// 有时候客户端输入的字符容易被 markdown 转义，比如英文双引号，比如 -- ，__， ** 这些都可能被命令行自动转义
	// 因此，输入的时候可能用 code 将命令包裹起来，所以我们这里首先将 code 的包裹标签删除掉
	txt = strings.Replace(txt, "`", "", -1)
	if strings.HasPrefix(txt, ".") {
		txtStr := txt[1:]

		if strings.HasPrefix(txtStr, "ok") {
			this.Debugf("ok command detected: %s", txtStr)
		}

		// "xxx <tel:1001123456|1001 123456>" 是自动被格式化为电话号码了，需要特殊处理
		if strings.Contains(txtStr, " <tel:") && strings.Contains(txtStr, ">") {
			r := regexp.MustCompile(`<tel:(.+)\|(.+)>`)
			tels := r.FindStringSubmatch(txtStr)
			if len(tels) == 3 && tels[1] == strings.ReplaceAll(tels[2], " ", "") {
				txtStr = strings.ReplaceAll(txtStr, tels[0], tels[2])
			} else {
				// 有时候会变成 <tel:1014607768|1014 ><tel:1014607768|607768> 这种格式，兼容处理
				r := regexp.MustCompile(`<tel:(.+)\|(.+) ><tel:(.+)\|(.+)>`)
				tels := r.FindStringSubmatch(txtStr)
				if len(tels) == 5 && tels[1] == tels[3] && fmt.Sprintf("%s%s", tels[2], tels[4]) == tels[1] {
					// e.g. txtStr 包含字符串 "<tel:1014607768|1014 ><tel:1014607768|607768>"
					// tels[0]: "<tel:1014607768|1014 ><tel:1014607768|607768>"
					// tels[1]: "1014607768"
					// tels[2]: "1014"
					// tels[3]: "1014607768"
					// tels[4]: "607768"
					// 处理后替换为: "1014 607768"
					txtStr = strings.ReplaceAll(txtStr, tels[0], fmt.Sprintf("%s %s", tels[2], tels[4]))
				}
			}
		}

		txtArrOrig := strings.Split(txtStr, " ")
		txtArr := []string{}
		for _, txt := range txtArrOrig {
			// 忽略参数之间多余的空格
			if txt != "" {
				txtArr = append(txtArr, txt)
			}
		}
		if len(txtArr) == 0 {
			return
		}

		command := txtArr[0]
		args := txtArr[1:]
		this.Infof("recv slack command: %s", command)

		for _, channelCommand := range this.commandsRequiresFile {
			channelName, commandName := channelCommand[0], channelCommand[1]
			if channelName == this.GetChannelName(channelID) && strings.EqualFold(commandName, command) {
				if len(msg.Files) == 0 {
					this.SendRTMMessage(this.GetChannelName(channelID), fmt.Sprintf("`.%s 同时需上传文件`", commandName))
					return
				}
				downloadURL := msg.Files[0].URLPrivateDownload
				args = append(args, downloadURL)
			}
		}

		handleFunc := this.commandHandlers[channelID]
		if handleFunc != nil {
			handleFunc(command, args...)
			// 配置了 cleanerToken，删除 launch 命令
			this.TryDeleteSensitiveCommand(channelID, command, msg.Timestamp)
		} else {
			this.Infof("command handler not found for command: %s, channelID: %s", command, channelID)
		}
	}
}

func (this *SlackMessenger) handleMessageForAdapter(msgEvent *slack.MessageEvent, channelID string) {
	// this.Infof("slack message: %s", msg.Text)
	msg := msgEvent.Text
	var fileContent = []byte{}
	var fileURL = ""

	if handleFunc, found := this.messageHandlers[channelID]; !found {
		channelName := this.GetChannelName(channelID)
		this.Errorf("message handler not found for channelID: %s, channelName: %s, message handlers: %#v", channelID, channelName, this.messageHandlers)
		return
	} else {
		if len(msgEvent.Files) == 1 {
			fileURL = msgEvent.Files[0].URLPrivateDownload
			if content, err := this.GetFile(fileURL); err != nil {
				this.SendRTMMessage(this.GetChannelName(channelID), "下载文件失败")
				return
			} else {
				fileContent = content
			}
		} else {
			if len(msgEvent.Files) > 1 {
				this.SendRTMMessage(this.GetChannelName(channelID), "命令中不允许上传多余一个文件")
			}
		}
		handleFunc(msg, fileURL, fileContent, msgEvent.Timestamp)
		// 配置了 cleanerToken，删除 launch 命令
		if this.cleanerClient != nil {
			cmd := strings.Split(msg, " ")[0]
			this.TryDeleteSensitiveCommand(channelID, cmd, msgEvent.Timestamp)
		}
	}
}

func (this *SlackMessenger) TryDeleteSensitiveCommand(channelID, command, timestamp string) {
	command = strings.TrimPrefix(command, ".")
	if utils.SliceContainsEqualFold(this.sensitiveCommands, command) {
		this.Infof("removing sensitive message for command: %s", command)
		time.AfterFunc(SENSITIVE_CMD_DELETE_SECONDS, func() {
			// 必须用 cleanerClient，用 client 删除会出现错误码: cant_delete_message
			if this.cleanerClient != nil {
				_, _, err := this.cleanerClient.DeleteMessage(channelID, timestamp)
				if err != nil {
					this.Errorf("automatically delete sensitive message failed, error: %s", err)
				}
			} else {
				this.Warnf("automatically delete sensitive message failed, cleanerClient is nil")
			}
		})
	} else {
		this.Infof("command is not sensitive: %s, all sensitive commands: [%s]", command, strings.Join(this.sensitiveCommands, ","))
	}
}

func ParseCommandFromMessage(msg string) string {
	parts := strings.Split(msg, " ")
	return strings.TrimPrefix(parts[0], ".")
}

func (this *SlackMessenger) DeleteMsgs(channelID string, queries []string, before time.Time, after *time.Time) (count int, err error) {
	validQueries := []string{}
	for _, query := range queries {
		if len(query) > 4 {
			validQueries = append(validQueries, query)
		}
	}
	if len(validQueries) == 0 {
		validQueries = []string{"`错误:`", "`运行错误:`", "`警告:`", "`运行错误`:"}
	}

	if after == nil {
		after = utils.Ptr(before.Add(-time.Hour * 24 * 7)) // 默认删除一周前的消息
	}
	if this.cleanerClient == nil {
		return 0, fmt.Errorf("cleaner client is nil")
	}
	var messages []slack.Message
	params := slack.GetConversationHistoryParameters{
		ChannelID: channelID,
		Limit:     1000,
		Latest:    fmt.Sprintf("%d.000000", before.Unix()),
	}
	if after != nil {
		params.Oldest = fmt.Sprintf("%d.000000", after.Unix())
	}
	for {
		history, err := this.cleanerClient.GetConversationHistory(&params)
		if err != nil {
			return 0, fmt.Errorf("get conversation history failed, error: %s", err)
		}
		for _, msg := range history.Messages {
			containsError := false
			for _, query := range validQueries {
				if strings.Contains(msg.Text, query) {
					containsError = true
					break
				}
			}
			if containsError {
				messages = append(messages, msg)
			}
		}
		if history.ResponseMetaData.NextCursor == "" {
			break
		}
		params.Cursor = history.ResponseMetaData.NextCursor
	}

	for _, msg := range messages {
		this.Infof("deleting message at timestamp: #%s", msg.Timestamp)
		this.cleanerClient.DeleteMessage(channelID, msg.Timestamp)
		time.Sleep(time.Second * 1)
	}
	return len(messages), nil
}

func parseSlackTimestamp(ts string) (time.Time, error) {
	// Split the timestamp at the dot
	parts := strings.Split(ts, ".")

	// Convert the seconds part to int64
	seconds, err := cast.ToInt64E(parts[0])
	if err != nil {
		return time.Time{}, err
	}

	// Create time from Unix timestamp
	return time.Unix(seconds, 0), nil
}

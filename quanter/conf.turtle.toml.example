# 新建文件 {TurtleID}.turtle.toml 复制以下内容，修改对应配置项

Debug = false
ExchangeName = "BitMEX"
Symbols = ["XBTUSD", "ETHUSD"]
ApiKey = "YOUR_API_KEY"
ApiSecret = "YOUR_API_SECRET"
IsTestnet = false
ProxyUrl = "http://127.0.0.1:1087"
ReleaseBinaryDirPath = "/home/<USER>/turtle_releases"

[SymbolConfig]
Symbol = ""
ATRPeriod = 20
BreakoutPeriod = 20
SecondaryBreakoutPeriod = 0
PeriodHour = 4
ExitPeriod = 10
MaxOpenUnits = 4
OpenSizePercent = 0.003
MarginChangeMinPercent = 0.01
TakeProfitSlippageRate = 0.004
CloseSlippageRate = 0.003
BreakoutSlippageRate = 0.002
ExitSlippageRate = 0.002
WaitingSecondsAfterTriggeredLong = 600
WaitingSecondsAfterTriggeredShort = 600
LiquidationPriceAlertRatio = 0.005
RotateOpenPositionSlippageRate = 0.005
RotateOpenPositionRetryTimes = 3
RotateOpenPositionWaitSeconds = 5

package exchange

import (
	"encoding/base64"
	"fmt"
	"math/rand"
	"strings"

	"github.com/jinzhu/copier"
)

// 不同的 gateway 实例，返回的 packetID 的前缀不一样，有小概率重复
func randomServerPrefix() int64 {
	// Generate a random int64 between 20000 and 29999
	min := int64(20000)
	max := int64(30000)
	randomNumber := rand.Int63n(max-min) + min
	return randomNumber
}

type SlackResponse struct {
	Msg         string `json:"msg,omitempty"`
	FileTitle   string `json:"file_title,omitempty"`
	FileContent string `json:"file_content,omitempty"`
	FileType    string `json:"file_type,omitempty"`
}

func (this *SlackResponse) Decode() (msg string, title string, fileContent []byte, fileType string, er error) {
	// Decode Msg
	d, err := base64.StdEncoding.DecodeString(this.Msg)
	if err != nil {
		er = fmt.Errorf("failed to decode Msg: %w", err)
		return
	}
	msg = string(d)

	// Decode FileTitle
	d, err = base64.StdEncoding.DecodeString(this.FileTitle)
	if err != nil {
		er = fmt.Errorf("failed to decode FileTitle: %w", err)
		return
	}
	title = string(d)

	// Decode FileContent
	fileContent, err = base64.StdEncoding.DecodeString(this.FileContent)
	if err != nil {
		er = fmt.Errorf("failed to decode FileContent: %w", err)
		return
	}

	// Decode FileType
	d, err = base64.StdEncoding.DecodeString(this.FileType)
	if err != nil {
		er = fmt.Errorf("failed to decode FileType: %w", err)
		return
	}
	fileType = string(d)

	return
}

type Packet struct {
	PacketHeader
	Instruments   []*Instrument  `json:"instruments,omitempty"`
	Orders        []*Order       `json:"orders,omitempty"`
	Margin        *UserMargin    `json:"margin,omitempty"`
	SlackResponse *SlackResponse `json:"slack_response,omitempty"`
}

func (this *Packet) IsOK() bool {
	return !(strings.HasSuffix(string(this.Event), ".fail") || this.Event == EventError)
}

/* 协议中不要直接使用 Key，Event 这些字段；而是使用以下接口 */

func (this *Packet) CheckSubscribeSuccess() (success bool, key string) {
	if this.Event == EventSubscribeSuccess {
		return true, this.Key
	}
	return false, ""
}

func (this *Packet) CheckSubscribeFail() (fail bool, key string, errMsg string) {
	if this.Event == EventSubscribeFail {
		return true, this.Key, this.Error
	}
	return false, "", ""
}

func (this *Packet) CheckUnsubscribeSuccess() (success bool, key string) {
	if this.Event == EventUnsubscribeSuccess {
		return true, this.Key
	}
	return false, ""
}

func (this *Packet) CheckUnsubscribeFail() (fail bool, key string, errMsg string) {
	if this.Event == EventUnsubscribeFail {
		return true, this.Key, this.Error
	}
	return false, "", ""
}

func (this *Packet) CheckPong() (result bool) {
	return this.Event == EventPong
}

func (this *Packet) CheckOrder() (result bool, order []*Order) {
	if this.Event == EventOrdersUpdate && len(this.Orders) > 0 {
		return true, this.Orders
	}
	return false, nil
}

func (this *Packet) CheckInstrument() (result bool, instruments []*Instrument) {
	if this.Event == EventInstrumentsUpdate && len(this.Instruments) > 0 {
		return true, this.Instruments
	}
	return false, nil
}

func (this *Packet) CheckSlackMessage() (result bool, slackResponse *SlackResponse) {
	if this.Event == EventSlackResponse && this.SlackResponse != nil {
		return true, this.SlackResponse
	}
	return false, nil
}

func (this *Packet) CheckMargin() (result bool, margin *UserMargin) {
	if this.Event == EventMarginUpdate && this.Margin != nil {
		return true, this.Margin
	}
	return false, nil
}

func (this *Packet) CheckSlackResponse() (result bool, channel string, slackResponse *SlackResponse) {
	if this.Event == EventSlackResponse {
		return true, this.Key, this.SlackResponse
	}
	return false, "", nil
}

/* 协议中不要直接使用 Key，Event 这些字段；而是使用以下接口 */

func (this *Packet) AddOrder(o *Order) {
	orderCopy := &Order{}
	copier.Copy(orderCopy, o)
	this.Orders = append(this.Orders, orderCopy)
}

func (this *Packet) AddInstrument(i *Instrument) {
	instrumentCopy := &Instrument{}
	copier.Copy(instrumentCopy, i)
	this.Instruments = append(this.Instruments, instrumentCopy)
}

func (this *Packet) SetMargin(m *UserMargin) {
	marginCopy := &UserMargin{}
	copier.Copy(marginCopy, m)
	this.Margin = marginCopy
}

func NewSubscribeSuccessPacket(origPacket *ClientPacket, key string) *Packet {
	p := &Packet{
		PacketHeader: NewPacketHeader(origPacket.APIKey, EventSubscribeSuccess, key, ""),
	}
	p.SetID()
	p.ReplyTo = origPacket.ID
	return p
}

func NewUnsubscribeSuccessPacket(origPacket *ClientPacket, key string) *Packet {
	p := &Packet{
		PacketHeader: NewPacketHeader(origPacket.APIKey, EventUnsubscribeSuccess, key, ""),
	}
	p.SetID()
	p.ReplyTo = origPacket.ID
	return p
}

func NewSubscribeFailPacket(origPacket *ClientPacket, key string, errMsg string) *Packet {
	p := &Packet{
		PacketHeader: NewPacketHeader(origPacket.APIKey, EventSubscribeFail, key, errMsg),
	}
	p.SetID()
	p.ReplyTo = origPacket.ID
	return p
}

func NewUnsubscribeFailPacket(origPacket *ClientPacket, key string, errMsg string) *Packet {
	p := &Packet{
		PacketHeader: NewPacketHeader(origPacket.APIKey, EventUnsubscribeFail, key, errMsg),
	}
	p.SetID()
	p.ReplyTo = origPacket.ID
	return p
}

func NewPongPacket(origPacket *ClientPacket) *Packet {
	p := &Packet{
		PacketHeader: NewPacketHeader(origPacket.APIKey, "pong", "", ""),
	}
	p.SetID()
	p.ReplyTo = origPacket.ID
	return p
}

func NewOrderPacket(apiKey string, order *Order) *Packet {
	orderCopy := &Order{}
	copier.Copy(orderCopy, order)
	p := &Packet{
		PacketHeader: NewPacketHeader(apiKey, EventOrdersUpdate, fmt.Sprintf("%s.orders", orderCopy.Symbol), ""),
		Orders:       []*Order{orderCopy},
	}
	p.SetID()
	return p
}

func NewInstrumentPacket(apiKey string, instrument *Instrument) *Packet {
	instrumentCopy := &Instrument{}
	copier.Copy(instrumentCopy, instrument)
	p := &Packet{
		PacketHeader: NewPacketHeader(apiKey, EventInstrumentsUpdate, fmt.Sprintf("%s.instruments", instrumentCopy.Symbol), ""),
		Instruments:  []*Instrument{instrumentCopy},
	}
	return p
}

func NewUserMarginPacket(apiKey string, margin *UserMargin) *Packet {
	marginCopy := &UserMargin{}
	copier.Copy(marginCopy, margin)
	p := &Packet{
		PacketHeader: NewPacketHeader(apiKey, EventMarginUpdate, "margin", ""),
		Margin:       marginCopy,
	}
	p.SetID()
	return p
}

func NewSlackFileResponsePacket(apiKey string, channelName, title, msg, comment, ftype string) *Packet {
	header := NewPacketHeader(apiKey, EventSlackResponse, fmt.Sprintf("%s.slack", channelName), "")
	header.SetID()
	return &Packet{
		PacketHeader: header,
		SlackResponse: &SlackResponse{
			Msg:         base64.StdEncoding.EncodeToString([]byte(comment)),
			FileTitle:   base64.StdEncoding.EncodeToString([]byte(title)),
			FileContent: base64.StdEncoding.EncodeToString([]byte(msg)),
			FileType:    base64.StdEncoding.EncodeToString([]byte(ftype)),
		},
	}

}

func NewSlackRequestAckPacket(origPacket *ClientPacket, key string) *Packet {
	header := NewPacketHeader(origPacket.APIKey, EventSlackAck, fmt.Sprintf("%s.slack", key), "")
	header.SetID()
	header.ReplyTo = origPacket.ID
	return &Packet{
		PacketHeader: header,
	}
}

func NewSlackResponsePacket(apiKey string, channelName, msg string) *Packet {
	header := NewPacketHeader(apiKey, EventSlackResponse, fmt.Sprintf("%s.slack", channelName), "")
	header.SetID()
	packet := &Packet{
		PacketHeader: header,
		SlackResponse: &SlackResponse{
			Msg: base64.StdEncoding.EncodeToString([]byte(msg)),
		},
	}
	return packet
}

func (this *Packet) CheckIsProtocol() bool {
	protocolEvents := []PacketEvent{EventPong,
		EventSubscribeSuccess,
		EventSubscribeFail,
		EventUnsubscribeSuccess,
		EventUnsubscribeFail,
		EventSlackAck,
		EventSlackResponse,
	}
	for _, e := range protocolEvents {
		if this.Event == e {
			return true
		}
	}
	return false
}

func NewCheckSignErrorPacket(origPacket *ClientPacket) *Packet {
	p := &Packet{
		PacketHeader: NewPacketHeader(origPacket.APIKey, EventError, "", "check sign error"),
	}
	p.SetID()
	p.ReplyTo = origPacket.ID
	return p
}

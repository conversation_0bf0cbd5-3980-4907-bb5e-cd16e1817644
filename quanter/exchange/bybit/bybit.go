package bybit

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	resty "github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"
)

const API_URL = "api.bybit.com"
const TESTNET_API_URL = "api-testnet.bybit.com"
const RECV_WINDOW = 10_000

func (this *Bybit) getBaseRequest() *resty.Request {
	client := this.Client
	hostURL := API_URL
	if this.IsTestnet {
		hostURL = TESTNET_API_URL
	}
	client.
		SetBaseURL(fmt.Sprintf("https://%s", hostURL)).
		SetTimeout(30 * time.Second).     // 设置超时时间，超时无反应则返回超时错误
		SetRetryCount(0).                 // 请求失败时的重试次数
		SetRetryWaitTime(5 * time.Second) // 重试前等待时间

	req := client.R()
	req.SetHeader("Content-Type", "application/json")

	return req
}

// timestamp+api_key+recv_window+queryString/raw_request_body
func (this *Bybit) sign(ts int64, signStr, apiKey string, secret secrets.SecretString) string {
	str := fmt.Sprintf("%d%s%d%s", ts, apiKey, RECV_WINDOW, signStr)
	// this.Debugf("sign str: %s", str)
	h := hmac.New(sha256.New, []byte(secret.Reveal()))
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

func (this *Bybit) request(method, path string, data map[string]any) (ret *gjson.Result, err error) {
	return this._request(method, path, data, false)
}

func (this *Bybit) requestWithdrawKey(method, path string, data map[string]any) (ret *gjson.Result, err error) {
	return this._request(method, path, data, true)
}

func (this *Bybit) _request(method, path string, data map[string]any, useWithdrawApiKey bool) (ret *gjson.Result, err error) {
	req := this.getBaseRequest()

	signStr := ""
	if data != nil {
		if method == "GET" {
			var ret []string
			for k, v := range data {
				ret = append(ret, fmt.Sprintf("%v=%v", k, v))
			}
			queryData := strings.Join(ret, "&")
			req.SetQueryString(queryData)
			signStr = req.QueryParam.Encode()
		} else if method == "POST" {
			payloadData, err := json.Marshal(data)
			if err != nil {
				return nil, err
			}
			payload := payloadData
			signStr = string(payload)
			req.SetBody(payload)
		}
	}

	// this.Debugf("request %s with data: %#v", path, data)

	if this.IsSubaccount() && utils.SliceContains([]string{
		"/v5/asset/withdraw/create",
		"/v5/asset/transfer/universal-transfer",
	}, path) {
		useWithdrawApiKey = true
	}

	apiKey := this.apiKey.ApiKey
	secret := this.apiKey.ApiSecret
	if useWithdrawApiKey {
		apiKey = this.withdrawApiKey.ApiKey
		secret = this.withdrawApiKey.ApiSecret
	}

	ts := time.Now().UnixMilli()
	sign := this.sign(ts, signStr, apiKey, secret)
	req.SetHeader("X-BAPI-API-KEY", apiKey)
	req.SetHeader("X-BAPI-TIMESTAMP", fmt.Sprintf("%d", ts))
	req.SetHeader("X-BAPI-SIGN", sign)
	req.SetHeader("X-BAPI-RECV-WINDOW", fmt.Sprintf("%d", RECV_WINDOW))

	var resp *resty.Response
	if method == "GET" {
		resp, err = req.Get(path)
	} else if method == "POST" {
		resp, err = req.Post(path)
	} else {
		return nil, fmt.Errorf("unsupported method: %s", method)
	}

	if err != nil {
		return nil, err
	}

	// this.Debugf("request %s response: %s", path, resp.String())

	if resp.StatusCode() != 200 {
		this.Errorf("request %s failed: %d - %s", path, resp.StatusCode(), resp.String())
		return nil, fmt.Errorf("request %s failed: %d - %s", path, resp.StatusCode(), resp.String())
	}

	newRet := gjson.Parse(resp.String())
	if newRet.Get("retCode").Int() != 0 {
		this.Errorf("request %s failed, resp: %s", path, newRet.String())
		return nil, fmt.Errorf("request %s failed, msg: %s", path, newRet.Get("retMsg").String())
	}
	res := newRet.Get("result")
	return &res, nil
}

func (this *Bybit) spotFetchInstruments() (instruments []Instrument) {
	ret, err := this.request("GET", "/v5/market/instruments-info?category=spot", nil)
	if err != nil {
		this.Errorf("get spot instruments error, %v", err)
		return
	}

	list := ret.Get("list").Array()
	for _, item := range list {
		i := &Instrument{
			Symbol:           item.Get("symbol").String(),
			Status:           item.Get("status").String(),
			BaseCoin:         item.Get("baseCoin").String(),
			QuoteCoin:        item.Get("quoteCoin").String(),
			DeliveryTime:     0,
			MinLeverage:      0,
			MaxLeverage:      0,
			LeverageStep:     0,
			TickSize:         item.Get("priceFilter.tickSize").Float(),
			MaxOrderQty:      item.Get("lotSizeFilter.maxOrderQty").Float(),
			MinOrderQty:      item.Get("lotSizeFilter.minOrderQty").Float(),
			QtyStep:          item.Get("lotSizeFilter.basePrecision").Float(),
			MinNotionalValue: item.Get("lotSizeFilter.minOrderAmt").Float(),
			SettleCoin:       "",
			instrumentType:   exchange.Spot,
			updatedAt:        time.Now().Unix(),
		}
		instruments = append(instruments, *i)
	}

	return
}

func getInstrumentsCacheKey(instrumentType exchange.InstrumentType, symbol string) string {
	return fmt.Sprintf("%s_%s", string(instrumentType), symbol)
}

func (this *Bybit) spotCacheLatestInstruments() (instruments []Instrument) {
	instruments = this.spotFetchInstruments()
	for idx := range instruments {
		instrument := instruments[idx]
		this.instruments.Store(getInstrumentsCacheKey(instrument.instrumentType, instrument.Symbol), &instrument)
	}
	return
}

// instruments-info 现货不支持翻页，其他需要翻页
func (this *Bybit) getInstrumentsInfo(category string) ([]gjson.Result, error) {
	cursor := ""

	res := []gjson.Result{}

	for {
		ret, err := this.request("GET", "/v5/market/instruments-info", map[string]any{
			"category": category,
			"cursor":   cursor,
			"limit":    1000,
		})
		if err != nil {
			return nil, err
		}

		res = append(res, ret.Get("list").Array()...)

		if ret.Get("nextPageCursor").String() == "" {
			break
		}

		cursor = ret.Get("nextPageCursor").String()
	}

	return res, nil
}

func (this *Bybit) cFuturesFetchInstruments() (instruments []Instrument) {
	list, err := this.getInstrumentsInfo("inverse")
	if err != nil {
		this.Errorf("get inverse instruments error, %v", err)
		return
	}

	for _, item := range list {
		i := &Instrument{
			Symbol:               item.Get("symbol").String(),
			Status:               item.Get("status").String(),
			BaseCoin:             item.Get("baseCoin").String(),
			QuoteCoin:            item.Get("quoteCoin").String(),
			DeliveryTime:         item.Get("deliveryTime").Int(),
			MinLeverage:          item.Get("leverageFilter.minLeverage").Float(),
			MaxLeverage:          item.Get("leverageFilter.maxLeverage").Float(),
			LeverageStep:         item.Get("leverageFilter.leverageStep").Float(),
			TickSize:             item.Get("priceFilter.tickSize").Float(),
			MaxOrderQty:          item.Get("lotSizeFilter.maxOrderQty").Float(),
			MinOrderQty:          item.Get("lotSizeFilter.minOrderQty").Float(),
			QtyStep:              item.Get("lotSizeFilter.qtyStep").Float(),
			MinNotionalValue:     item.Get("lotSizeFilter.minNotionalValue").Float(),
			SettleCoin:           item.Get("settleCoin").String(),
			FundingIntervalHours: int(item.Get("fundingInterval").Int() / 60),
			instrumentType:       exchange.CoinMarginedFutures,
			updatedAt:            time.Now().Unix(),
		}
		instruments = append(instruments, *i)
	}

	return
}

func (this *Bybit) cFuturesCacheLatestInstruments() (instruments []Instrument) {
	instruments = this.cFuturesFetchInstruments()
	for idx := range instruments {
		instrument := instruments[idx]
		this.instruments.Store(getInstrumentsCacheKey(instrument.instrumentType, instrument.Symbol), &instrument)
	}
	return
}

func (this *Bybit) uFuturesFetchInstruments() (instruments []Instrument) {
	list, err := this.getInstrumentsInfo("linear")
	if err != nil {
		this.Errorf("get linear instruments error, %v", err)
		return
	}

	for _, item := range list {
		i := &Instrument{
			Symbol:               item.Get("symbol").String(),
			Status:               item.Get("status").String(),
			BaseCoin:             item.Get("baseCoin").String(),
			QuoteCoin:            item.Get("quoteCoin").String(),
			DeliveryTime:         item.Get("deliveryTime").Int(),
			MinLeverage:          item.Get("leverageFilter.minLeverage").Float(),
			MaxLeverage:          item.Get("leverageFilter.maxLeverage").Float(),
			LeverageStep:         item.Get("leverageFilter.leverageStep").Float(),
			TickSize:             item.Get("priceFilter.tickSize").Float(),
			MaxOrderQty:          item.Get("lotSizeFilter.maxOrderQty").Float(),
			MinOrderQty:          item.Get("lotSizeFilter.minOrderQty").Float(),
			QtyStep:              item.Get("lotSizeFilter.qtyStep").Float(),
			MinNotionalValue:     item.Get("lotSizeFilter.minNotionalValue").Float(),
			SettleCoin:           item.Get("settleCoin").String(),
			FundingIntervalHours: int(item.Get("fundingInterval").Int() / 60),
			instrumentType:       exchange.USDXMarginedFutures,
			updatedAt:            time.Now().Unix(),
		}
		instruments = append(instruments, *i)
	}

	return
}

func (this *Bybit) uFuturesCacheLatestInstruments() (instruments []Instrument) {
	instruments = this.uFuturesFetchInstruments()
	for idx := range instruments {
		instrument := instruments[idx]
		this.instruments.Store(getInstrumentsCacheKey(instrument.instrumentType, instrument.Symbol), &instrument)
	}
	return
}

func (this *Bybit) getInstrument(instrumentType exchange.InstrumentType, symbol string) (instrument *Instrument) {
	instrument, _ = this.instruments.Load(getInstrumentsCacheKey(instrumentType, symbol))
	if instrument == nil || instrument.IsExpired() {
		if instrumentType == exchange.CoinMarginedFutures {
			this.cFuturesCacheLatestInstruments()
		} else if instrumentType == exchange.USDXMarginedFutures {
			this.uFuturesCacheLatestInstruments()
		} else if instrumentType == exchange.Spot {
			this.spotCacheLatestInstruments()
		}

		instrument, _ = this.instruments.Load(getInstrumentsCacheKey(instrumentType, symbol))
	}

	if instrument != nil && instrument.riskLimits == nil && instrumentType != exchange.Spot {
		if err := this.cacheRiskLimits(instrument); err != nil {
			this.Errorf("cache risk limits error, %v", err)
			return nil
		}
	}

	return instrument
}

// open status
// New order has been placed successfully
// PartiallyFilled
// Untriggered Conditional orders are created
// closed status
// Rejected
// PartiallyFilledCanceled Only spot has this order status
// Filled
// Cancelled In derivatives, orders with this status may have an executed qty
// Triggered instantaneous state for conditional orders from Untriggered to New
// Deactivated UTA: Spot tp/sl order, conditional order, OCO order are cancelled before they are triggered
func convertOrderStatus(status string, isTriggerOrder bool) exchange.OrderStatus {
	switch status {
	case "New":
		if isTriggerOrder {
			return exchange.OrderStatusTriggered
		}
		return exchange.OrderStatusNew
	case "PartiallyFilled":
		return exchange.OrderStatusPartialFilled
	case "Untriggered":
		return exchange.OrderStatusNew
	case "Rejected":
		return exchange.OrderStatusRejected
	case "PartiallyFilledCanceled":
		return exchange.OrderStatusPartialCancelled
	case "Filled":
		return exchange.OrderStatusFilled
	case "Cancelled":
		return exchange.OrderStatusCancelled
	case "Triggered":
		return exchange.OrderStatusTriggered
	case "Deactivated":
		return exchange.OrderStatusCancelled
	default:
		return exchange.UnknownOrderStatus
	}
}

func (this *Bybit) cacheRiskLimits(instrument *Instrument) error {
	ret, err := this.request("GET", "/v5/market/risk-limit", map[string]any{
		"category": instrumentTypeToCategory(instrument.instrumentType),
		"symbol":   instrument.Symbol,
	})
	if err != nil {
		return err
	}

	list := ret.Get("list").Array()
	riskLimits := []RiskLimit{}
	for _, item := range list {
		riskLimits = append(riskLimits, RiskLimit{
			RiskLimitValue:    item.Get("riskLimitValue").Float(),
			MaintenanceMargin: item.Get("maintenanceMargin").Float(),
			InitialMargin:     item.Get("initialMargin").Float(),
			MaxLeverage:       item.Get("maxLeverage").Float(),
		})
	}

	sort.SliceStable(riskLimits, func(i, j int) bool {
		return riskLimits[i].RiskLimitValue < riskLimits[j].RiskLimitValue
	})

	instrument.riskLimits = &riskLimits
	return nil
}

func (this *Bybit) addMargin(instrumentType exchange.InstrumentType, symbol string, margin float64) error {
	_, err := this.request("POST", "/v5/position/add-margin", map[string]any{
		"category": instrumentTypeToCategory(instrumentType),
		"symbol":   symbol,
		"margin":   fmt.Sprintf("%.4f", margin),
	})
	return err
}

func (this *Bybit) getPositions(instrumentType exchange.InstrumentType, symbol string) ([]gjson.Result, error) {
	req := map[string]any{
		"category": instrumentTypeToCategory(instrumentType),
		"symbol":   symbol,
		"cursor":   "",
	}

	resList := []gjson.Result{}
	reqFun := func() error {
		for {
			res, err := this.request("GET", "/v5/position/list", req)
			if err != nil {
				return err
			}
			resList = append(resList, res.Get("list").Array()...)
			nextPageCursor := res.Get("nextPageCursor").String()
			if nextPageCursor == "" {
				break
			}
			req["cursor"] = nextPageCursor
		}
		return nil
	}

	// For linear, either symbol or settleCoin is required
	if symbol == "" && instrumentType == exchange.USDXMarginedFutures {
		req["settleCoin"] = "USDT"
	}

	if err := reqFun(); err != nil {
		return nil, err
	}

	if symbol == "" && instrumentType == exchange.USDXMarginedFutures {
		req["settleCoin"] = "USDC"
		req["cursor"] = ""

		if err := reqFun(); err != nil {
			return nil, err
		}
	}

	return resList, nil
}

func (this *Bybit) getMarketTickers(category InstrumentCategory) ([]gjson.Result, error) {
	ret, err := this.request("GET", "/v5/market/tickers", map[string]any{
		"category": category,
	})
	if err != nil {
		return nil, err
	}

	return ret.Get("list").Array(), nil
}

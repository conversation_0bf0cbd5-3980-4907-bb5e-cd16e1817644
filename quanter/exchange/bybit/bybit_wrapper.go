package bybit

import (
	"errors"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/puzpuzpuz/xsync/v2"
	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

func NewBybit(options *exchange.Options) (*Bybit, error) {
	if options.ControllerID == "" {
		return nil, errors.New("controller id is required")
	}
	apiKey, err := NewAPISecret(options.ApiKey, options.ApiSecret, false)
	if err != nil {
		return nil, err
	}
	var withdrawApiKey *APISecret
	if options.WithdrawApiKey != "" && options.WithdrawApiSecret != "" {
		withdrawApiKey, err = NewAPISecret(options.WithdrawApiKey, options.WithdrawApiSecret, false)
		if err != nil {
			return nil, err
		}
	}
	h := &Bybit{
		BaseExchange:      *exchange.NewBaseExchange(options),
		instruments:       xsync.NewMapOf[*Instrument](),
		wsConnsPublic:     xsync.NewMapOf[*websocket.Conn](),
		subscribedSymbols: xsync.NewMapOf[[]string](),
		apiKey:            apiKey,
		withdrawApiKey:    withdrawApiKey,
	}

	h.Exchange = h

	return h, nil
}

func (this *Bybit) GetSupportedInstrumentTypes() []exchange.InstrumentType {
	return []exchange.InstrumentType{exchange.Spot, exchange.CoinMarginedFutures, exchange.USDXMarginedFutures}
}

func (this *Bybit) GetUserMargin(instrumentType exchange.InstrumentType, currency string) (*exchange.UserMargin, error) {
	res, err := this.request("GET", "/v5/account/wallet-balance", map[string]any{"accountType": "UNIFIED"})
	if err != nil {
		return nil, err
	}

	for _, account := range res.Get("list").Array() {
		for _, accountCoin := range account.Get("coin").Array() {
			coin := accountCoin.Get("coin").String()
			if coin == currency {
				return &exchange.UserMargin{
					WalletBalance:   accountCoin.Get("walletBalance").Float(),
					MarginBalance:   accountCoin.Get("equity").Float(),
					AvailableMargin: accountCoin.Get("availableToWithdraw").Float(),
					Currency:        currency,
				}, nil
			}
		}
	}

	return nil, fmt.Errorf("cannot find currency %s", currency)
}

func (this *Bybit) MaxLeverage(instrumentType exchange.InstrumentType, symbol string, value float64) float64 {
	ins := this.getInstrument(instrumentType, symbol)
	if ins == nil {
		return 0
	}

	return ins.GetMaxLeverage(value)
}

func (this *Bybit) GetSymbolCodeUnit(symbolCode *exchange.SymbolCode) string {
	if symbolCode.InstrumentType() == exchange.Spot {
		return symbolCode.Coin()
	} else if symbolCode.InstrumentType() == exchange.USDXMarginedFutures {
		return symbolCode.Coin()
	} else if symbolCode.InstrumentType() == exchange.CoinMarginedFutures {
		return "USD"
	} else {
		return ""
	}
}

func (this *Bybit) Qty2Size(instrumentType exchange.InstrumentType, symbol string, price float64, qty float64) (size float64, er error) {
	ins := this.getInstrument(instrumentType, symbol)
	if ins == nil {
		return 0, fmt.Errorf("cannot find instrument %s", symbol)
	}
	if ins.instrumentType == exchange.USDXMarginedFutures || ins.instrumentType == exchange.Spot {
		value := price * qty
		roundSize := math.Min(ins.TickSize, 0.01)
		return math.Round(value/roundSize) * roundSize, nil
	} else if ins.instrumentType == exchange.CoinMarginedFutures {
		if price == 0 {
			return 0, nil
		}
		return qty / price, nil
	} else {
		return 0, exchange.ErrNotAvailableForInstrumentType
	}
}

func (this *Bybit) Size2Qty(instrumentType exchange.InstrumentType, symbol string, price float64, size float64) (qty float64, er error) {
	ins := this.getInstrument(instrumentType, symbol)
	if ins == nil {
		return 0, fmt.Errorf("cannot find instrument %s", symbol)
	}

	if ins.instrumentType == exchange.USDXMarginedFutures {
		qty := size / price
		stepSize := ins.QtyStep
		qty = math.Round(qty/stepSize) * stepSize

		// 舍去 stepSize 后面的小数位
		stepPow := math.Pow(10, float64(exchange.DecimalBit(stepSize)))
		return float64(int(qty*stepPow)) / stepPow, nil
	} else if ins.instrumentType == exchange.CoinMarginedFutures {
		qty := size * price
		return math.Round(qty/ins.QtyStep) * ins.QtyStep, nil
	} else {
		return 0, exchange.ErrNotAvailableForInstrumentType
	}
}

func (this *Bybit) CalcPrice(instrumentType exchange.InstrumentType, symbol string, qty float64, size float64) (price float64, er error) {
	ins := this.getInstrument(instrumentType, symbol)
	if ins == nil {
		return 0, fmt.Errorf("cannot find instrument %s", symbol)
	}

	if ins.instrumentType == exchange.USDXMarginedFutures {
		price := size / qty
		tickSize := ins.TickSize
		return math.Round(price/tickSize) * tickSize, nil
	} else if ins.instrumentType == exchange.CoinMarginedFutures {
		return qty / size, nil
	} else {
		return 0, exchange.ErrNotAvailableForInstrumentType
	}
}

// USDT 合约
// 对于买入/做多：
// 强平价格 (多) = 入场价格 - [（初始保证金 - 维持保证金) / 合约数量] -（追加保证金/合约数量）
// 对于卖出/做空：
// 强平价格 (空） = 入场价格 + [（初始保证金 - 维持保证金） / 合约数量] +（追加保证金/合约数量）
// 注：
// — 仓位价值 = 合约数量 × 入场价格
// — 初始保证金 (IM) = 仓位价值 / 杠杆倍数
// — 维持保证金（MM）=（仓位价值 x MMR）- 维持保证金扣除额
// — 维持保证金率 (MMR) 视风险限额等级而定
// 反向合约
// 做多：强平价格 = 入场价格 * 杠杆 / (杠杆 + 1 - 维持保证金率 * 杠杆) => 杠杆 = 强平价格 / (入场价格 - 强平价格 * (1 - 维持保证金率))
// 做空：强平价格 = 入场价格 * 杠杆 / (杠杆 - 1 + 维持保证金率 * 杠杆) => 杠杆 = 强平价格 / (强平价格 * (1 + 维持保证金率) - 入场价格)
func (this *Bybit) CalculateLeverage(position *exchange.Position) (float64, error) {
	instrument := this.getInstrument(position.InstrumentType, position.Symbol)
	if instrument == nil {
		return 0, fmt.Errorf("instrument not found")
	}

	qty := math.Abs(position.Qty)
	posValue, _ := this.Qty2Size(position.InstrumentType, position.Symbol, position.EntryPrice, qty)
	MMR := instrument.GetMMR(posValue)
	MM := posValue * MMR

	if position.InstrumentType == exchange.USDXMarginedFutures {
		if position.Side == exchange.PositionSideLong {
			return posValue / ((position.EntryPrice-position.LiquidationPrice)*qty + MM), nil
		} else {
			return posValue / ((position.LiquidationPrice-position.EntryPrice)*qty + MM), nil
		}
	} else if position.InstrumentType == exchange.CoinMarginedFutures {
		if position.Side == exchange.PositionSideLong {
			return position.LiquidationPrice / (position.EntryPrice - position.LiquidationPrice*(1-MMR)), nil
		} else {
			return position.LiquidationPrice / (position.LiquidationPrice*(1+MMR) - position.EntryPrice), nil
		}
	} else {
		return 0, exchange.ErrNotAvailableForInstrumentType
	}
}

func (this *Bybit) SetLeverageToMax(instrumentType exchange.InstrumentType, symbol string, currentLeverage float64, positionSide exchange.PositionSide) bool {
	ins := this.getInstrument(instrumentType, symbol)
	if ins == nil {
		return false
	}

	maxLeverage := ins.GetMaxLeverage(0)
	if currentLeverage >= maxLeverage {
		return true
	}

	err := this.SetLeverage(instrumentType, symbol, exchange.Isolated, positionSide, maxLeverage)
	if err != nil {
		this.Errorf("set leverage to max failed: %s", err)
		return false
	}

	return true
}

func (this *Bybit) debugAdjustLiquidationPrice(position *exchange.Position, targetLiquidationPrice float64) {
	deltaPrice := targetLiquidationPrice - position.LiquidationPrice
	this.Infof("%s adjust liquidation price: %s -> %s, delta: %s",
		position.Symbol,
		this.FormatPrice(position.InstrumentType, position.Symbol, position.LiquidationPrice),
		this.FormatPrice(position.InstrumentType, position.Symbol, targetLiquidationPrice),
		this.FormatPrice(position.InstrumentType, position.Symbol, deltaPrice),
	)
}

func (this *Bybit) AdjustLiquidationPrice(instrumentType exchange.InstrumentType, symbol string, position *exchange.Position, targetLiquidationPrice float64, acceptableDelta float64) (deltaPrice float64, errMsg string) {
	targetPosition := &exchange.Position{
		InstrumentType:   position.InstrumentType,
		Symbol:           symbol,
		Qty:              position.Qty,
		Side:             position.Side,
		LiquidationPrice: targetLiquidationPrice,
		MarkPrice:        position.MarkPrice,
		EntryPrice:       position.EntryPrice,
	}
	deltaPrice = math.Abs(targetLiquidationPrice - position.LiquidationPrice)

	targetLeverage, err := this.CalculateLeverage(targetPosition)
	if err != nil {
		return deltaPrice, fmt.Sprintf("calculate target leverage failed: %s", err)
	}

	err = this.SetLeverage(instrumentType, symbol, exchange.Isolated, position.Side, targetLeverage)
	if err != nil {
		return deltaPrice, fmt.Sprintf("set leverage failed: %s", err)
	}

	position, err = this.GetPosition(instrumentType, symbol, position.Side, false)
	if err != nil {
		return deltaPrice, fmt.Sprintf("get position failed: %s", err)
	}

	if position.Qty == 0 {
		return deltaPrice, "position qty is 0"
	}

	deltaPrice = math.Abs(targetLiquidationPrice - position.LiquidationPrice)

	if deltaPrice <= acceptableDelta {
		this.Infof("%s adjust liquidation price success", symbol)
		return deltaPrice, ""
	}

	// 如果用公式不能精确调整，后面用当前保证金的百分比来循环调整: 增加或减少当前保证金的一定比例
	// 当爆仓价朝着目标价调整时，百分比保持不变
	// 当爆仓价朝着目标价反方向调整时，百分比降为原来的一半
	MIN_STEP := 0.001
	marginStepPercent := 0.02
	lastDeltaPriceFlag := targetLiquidationPrice > position.LiquidationPrice
	canNotReduceMargin := false
	for i := 0; i < 20; i++ {
		this.debugAdjustLiquidationPrice(position, targetLiquidationPrice)

		needAdd := true
		if position.Side == exchange.PositionSideLong {
			if targetLiquidationPrice > position.LiquidationPrice {
				// 需要减保证金
				needAdd = false
			}
		} else {
			if targetLiquidationPrice < position.LiquidationPrice {
				// 需要减保证金
				needAdd = false
			}
		}

		deltaPriceFlag := targetLiquidationPrice > position.LiquidationPrice
		if lastDeltaPriceFlag != deltaPriceFlag {
			marginStepPercent /= 2
			marginStepPercent = math.Max(marginStepPercent, MIN_STEP)
		}

		var deltaMargin float64
		if needAdd {
			deltaMargin = position.Margin * marginStepPercent
		} else {
			deltaMargin = -position.Margin * marginStepPercent
		}

		// addMargin 接口最多支持 4 位小数，所以至少要用 0.0001
		if math.Abs(deltaMargin) < 0.0001 {
			this.Infof("adjust margin %f too small, use 0.0001", deltaMargin)
			if deltaMargin < 0 {
				deltaMargin = -0.0001
			} else {
				deltaMargin = 0.0001
			}
		}

		this.Debugf("update %s margin %0.4f", symbol, deltaMargin)

		if err := this.addMargin(instrumentType, symbol, deltaMargin); err != nil {
			this.Errorf("update %s margin %0.4f err: %s", symbol, deltaMargin, err)
			if strings.Contains(err.Error(), "added margin more than max can reduce margin") {
				canNotReduceMargin = true
				marginStepPercent /= 2
				continue
			}
		}

		canNotReduceMargin = false

		position, err = this.GetPosition(instrumentType, symbol, position.Side, false)
		if err != nil {
			return deltaPrice, fmt.Sprintf("get position failed: %v", err)
		}

		deltaPrice = math.Abs(targetLiquidationPrice - position.LiquidationPrice)

		if deltaPrice <= acceptableDelta {
			this.Infof("%s adjust liquidation price success", symbol)
			return deltaPrice, ""
		}

		lastDeltaPriceFlag = deltaPriceFlag
		time.Sleep(time.Second)
	}

	if canNotReduceMargin {
		this.Infof("can not reduce margin any more")
		return deltaPrice, ""
	}

	return deltaPrice, fmt.Sprintf("调整爆仓价失败, 目标价: %.2f, 当前价: %.2f, 当前杠杠率: %.2f", targetLiquidationPrice, position.LiquidationPrice, position.Leverage)
}

func (this *Bybit) UpdateOrder(origOrder exchange.Order, args *exchange.UpdateOrderArgs) (*exchange.Order, error) {
	ins := this.getInstrument(args.InstrumentType, origOrder.Symbol)
	if ins == nil {
		return nil, fmt.Errorf("instrument not found")
	}

	if ins.MinNotionalValue > 0 {
		value, _ := this.Qty2Size(args.InstrumentType, origOrder.Symbol, args.Price, args.OrderQty)
		if value < ins.MinNotionalValue {
			return nil, fmt.Errorf("min notional value is %f", ins.MinNotionalValue)
		}
	}

	if ins.MinOrderQty > 0 && args.OrderQty < ins.MinOrderQty {
		return nil, fmt.Errorf("min order qty is %f", ins.MinOrderQty)
	}

	req := map[string]any{
		"category": instrumentTypeToCategory(args.InstrumentType),
		"symbol":   origOrder.Symbol,
		"qty":      this.FormatQty(args.InstrumentType, origOrder.Symbol, args.OrderQty),
		"orderId":  origOrder.OrderID,
	}

	if args.Type != exchange.Market {
		req["price"] = this.FormatPrice(args.InstrumentType, origOrder.Symbol, args.Price)
		if args.TriggerPrice != 0 {
			req["triggerPrice"] = this.FormatPrice(args.InstrumentType, origOrder.Symbol, args.TriggerPrice)
		}
	}

	if args.TriggerPriceType == exchange.TriggerPriceTypeMark {
		req["triggerBy"] = "MarkPrice"
	} else if args.TriggerPriceType == exchange.TriggerPriceTypeLast {
		req["triggerBy"] = "LastPrice"
	} else if args.TriggerPriceType == exchange.TriggerPriceTypeIndex {
		req["triggerBy"] = "IndexPrice"
	}

	res, err := this.request("POST", "/v5/order/amend", req)
	if err != nil {
		return nil, err
	}

	orderID := res.Get("orderId").String()
	if orderID == "" {
		return nil, fmt.Errorf("order id not found")
	}

	if orderID != origOrder.OrderID {
		return nil, fmt.Errorf("order id not match, %s != %s", orderID, origOrder.OrderID)
	}

	now := time.Now()
	origOrder.Qty = args.OrderQty
	origOrder.Price = args.Price
	origOrder.TriggerPrice = args.TriggerPrice
	origOrder.UpdateTime = &now
	return &origOrder, nil
}

func (this *Bybit) CancelAllOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string) ([]string, error) {
	openOrders, err := this.GetOpenOrders(instrumentType, orderType, symbol)
	if err != nil {
		return []string{}, fmt.Errorf("get open orders failed: %s", err)
	}

	canceledIDs := []string{}
	for _, o := range openOrders {
		err := this.CancelOrder(instrumentType, exchange.UnknownOrderType, symbol, o.OrderID)
		if err != nil {
			return canceledIDs, fmt.Errorf("cancel order %s failed: %s", o.OrderID, err)
		}
		canceledIDs = append(canceledIDs, o.OrderID)
	}

	return canceledIDs, nil
}

func (this *Bybit) GetOpenOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string) ([]*exchange.Order, error) {
	req := map[string]any{
		"category": instrumentTypeToCategory(instrumentType),
		"symbol":   symbol,
		"cursor":   "",
	}

	resList := []gjson.Result{}
	for {
		res, err := this.request("GET", "/v5/order/realtime", req)
		if err != nil {
			return nil, err
		}
		l := res.Get("list").Array() // 按时间倒序的 K 线数据

		resList = append(resList, l...)

		nextPageCursor := res.Get("nextPageCursor").String()
		if nextPageCursor == "" {
			break
		}
		req["cursor"] = nextPageCursor
	}

	orders := []*exchange.Order{}
	for _, bOrder := range resList {
		this.Debugf("order: %s", bOrder.String())
		side := exchange.OrderSideBuy
		if bOrder.Get("side").String() == "Sell" {
			side = exchange.OrderSideSell
		}

		bOrderType := exchange.Limit
		triggerPrice := bOrder.Get("triggerPrice").Float()
		if triggerPrice != 0 {
			bOrderType = exchange.StopLimit
		}
		orderLinkId := bOrder.Get("orderLinkId").String()
		if strings.HasPrefix(orderLinkId, string(orderType)+"_") {
			bOrderType = orderType
		}

		if bOrderType != orderType && orderType != exchange.UnknownOrderType {
			continue
		}

		createTime := time.UnixMilli(bOrder.Get("createdTime").Int())
		updateTime := time.UnixMilli(bOrder.Get("updatedTime").Int())
		order := &exchange.Order{
			InstrumentType: instrumentType,
			Symbol:         symbol,
			OrderID:        bOrder.Get("orderId").String(),
			Side:           side,
			Type:           bOrderType,
			Qty:            bOrder.Get("qty").Float(),
			Price:          bOrder.Get("price").Float(),
			TriggerPrice:   triggerPrice,
			Status:         convertOrderStatus(bOrder.Get("orderStatus").String(), bOrderType != exchange.Limit),
			ReduceOnly:     bOrder.Get("reduceOnly").Bool(),
			ExecQty:        bOrder.Get("cumExecQty").Float(),
			ExecPrice:      bOrder.Get("avgPrice").Float(),
			CreateTime:     &createTime,
			UpdateTime:     &updateTime,
		}
		orders = append(orders, order)
	}
	return orders, nil
}

func (this *Bybit) GetInstrument(instrumentType exchange.InstrumentType, symbol string) (i *exchange.Instrument, _ error) {
	ins := this.getInstrument(instrumentType, symbol)
	if ins == nil {
		return i, fmt.Errorf("%s instrument not found", symbol)
	}
	i = &exchange.Instrument{}
	i.Symbol = ins.Symbol
	i.MinNotional = ins.MinNotionalValue
	i.TickSize = ins.TickSize
	i.LotSize = ins.QtyStep
	i.MinSize = ins.MinOrderQty
	i.MaxLeverage = ins.MaxLeverage
	i.SettleCurrency = ins.SettleCoin
	i.UnderlyCurrency = ins.BaseCoin
	i.QuoteCurrency = ins.QuoteCoin
	i.MarkPrice = ins.MarkPrice
	i.IndexPrice = ins.IndexPrice
	i.FundingRate = ins.FundingRate
	i.FundingRatePeriod = fmt.Sprintf("%dh", ins.FundingIntervalHours)
	i.FundingTime = ins.NextFundingTime
	i.OpenInterest = ins.OpenInterest

	if ins.tickerUpdateTime > 0 {
		i.UpdateTime = time.Unix(ins.tickerUpdateTime, 0)
	} else {
		i.UpdateTime = time.Unix(ins.updatedAt, 0)
	}
	i.InstrumentType = ins.instrumentType

	return i, nil
}

func (this *Bybit) GetLastPrice(instrumentType exchange.InstrumentType, symbol string, allowDelay bool) (float64, error) {
	res, err := this.request("GET", "/v5/market/tickers", map[string]any{
		"category": instrumentTypeToCategory(instrumentType),
		"symbol":   symbol,
	})
	if err != nil {
		return 0, err
	}

	ticker := res.Get("list").Array()
	if len(ticker) == 0 {
		return 0, fmt.Errorf("ticker not found")
	}

	return ticker[0].Get("lastPrice").Float(), nil
}

func (this *Bybit) CreateOrder(args exchange.CreateOrderArgs) (order *exchange.Order, _ error) {
	err := this.CheckQuoteQty(&args)
	if err != nil {
		return nil, err
	}

	ins := this.getInstrument(args.InstrumentType, args.Symbol)
	if ins == nil {
		return nil, fmt.Errorf("instrument not found")
	}

	if ins.MinNotionalValue > 0 {
		price := args.Price
		if args.Type == exchange.Market {
			lastPrice, err := this.GetLastPrice(args.InstrumentType, args.Symbol, false)
			if err != nil {
				return nil, fmt.Errorf("get last price failed: %s", err)
			}
			price = lastPrice
		}
		value, _ := this.Qty2Size(args.InstrumentType, args.Symbol, price, args.Qty)
		if value < ins.MinNotionalValue {
			return nil, fmt.Errorf("order value %f less than min notional value %f", value, ins.MinNotionalValue)
		}
	}

	if ins.MinOrderQty > 0 && args.Qty < ins.MinOrderQty {
		return nil, fmt.Errorf("min order qty is %f", ins.MinOrderQty)
	}

	orderType := "Limit"
	if args.Type == exchange.Market {
		orderType = "Market"
	}

	// 触发单触发后无法识别是 StopLimit 还是 Limit，所以通过自定义订单号来区分
	orderLinkId := fmt.Sprintf("%s_%s_%d", args.Type, exchange.NewRandomID(), time.Now().UnixMilli())

	req := map[string]any{
		"category":       instrumentTypeToCategory(args.InstrumentType),
		"symbol":         args.Symbol,
		"side":           string(args.Side),
		"orderType":      orderType,
		"reduceOnly":     args.ReduceOnly,
		"closeOnTrigger": args.ReduceOnly,
		"orderLinkId":    orderLinkId,
	}

	if args.Type == exchange.Market {
		req["marketUnit"] = "baseCoin" // Spot: quoteCoin for market buy by default, baseCoin for market sell by default
		if args.QuoteQty != 0 && args.InstrumentType == exchange.Spot {
			req["marketUnit"] = "quoteCoin"
			req["qty"] = fmt.Sprintf("%v", args.QuoteQty)
		} else {
			req["qty"] = this.FormatQty(args.InstrumentType, args.Symbol, args.Qty)
		}
	} else {
		req["qty"] = this.FormatQty(args.InstrumentType, args.Symbol, args.Qty)

		req["price"] = this.FormatPrice(args.InstrumentType, args.Symbol, args.Price)
		if args.TriggerPrice != 0 {
			req["triggerPrice"] = this.FormatPrice(args.InstrumentType, args.Symbol, args.TriggerPrice)

			if args.InstrumentType != exchange.Spot {
				// triggerDirection 仅合约有效
				if args.TriggerDirection == exchange.TriggerDirectionHigher {
					req["triggerDirection"] = 1
				} else if args.TriggerDirection == exchange.TriggerDirectionLower {
					req["triggerDirection"] = 2
				} else {
					lastPrice, err := this.GetLastPrice(args.InstrumentType, args.Symbol, false)
					if err != nil {
						return nil, fmt.Errorf("get last price failed: %s", err)
					}

					if args.TriggerPrice > lastPrice {
						req["triggerDirection"] = 1
					} else {
						req["triggerDirection"] = 2
					}
				}
			} else {
				return nil, fmt.Errorf("trigger price not supported in spot market")
			}
		}
	}

	if args.TriggerPriceType == exchange.TriggerPriceTypeMark {
		req["triggerBy"] = "MarkPrice"
	} else if args.TriggerPriceType == exchange.TriggerPriceTypeLast {
		req["triggerBy"] = "LastPrice"
	} else if args.TriggerPriceType == exchange.TriggerPriceTypeIndex {
		req["triggerBy"] = "IndexPrice"
	}

	if args.TimeInForce == exchange.GTC {
		req["timeInForce"] = "GTC"
	} else if args.TimeInForce == exchange.IOC {
		req["timeInForce"] = "IOC"
	}

	this.Debugf("create order: %#v", req)

	res, err := this.request("POST", "/v5/order/create", req)
	if err != nil {
		this.Errorf("create order failed with req: %#v, err: %s", req, err)
		return nil, err
	}

	orderID := res.Get("orderId").String()
	if orderID == "" {
		return nil, fmt.Errorf("order id not found")
	}

	nowTime := time.Now()
	return &exchange.Order{
		InstrumentType: args.InstrumentType,
		OrderID:        orderID,
		Symbol:         args.Symbol,
		Price:          args.Price,
		TriggerPrice:   args.TriggerPrice,
		Qty:            args.Qty,
		QuoteQty:       args.QuoteQty,
		Type:           args.Type,
		TradeMode:      args.TradeMode,
		Side:           args.Side,
		Status:         exchange.OrderStatusNew,
		TimeInForce:    args.TimeInForce,
		ReduceOnly:     args.ReduceOnly,
		CreateTime:     &nowTime,
		UpdateTime:     &nowTime,
	}, nil
}

func (this *Bybit) GetOrder(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol, orderID string, timeRange *exchange.TimeRange) (order *exchange.Order, _ error) {
	req := map[string]any{
		"category": instrumentTypeToCategory(instrumentType),
		"symbol":   symbol,
		"orderId":  orderID,
	}

	res, err := this.request("GET", "/v5/order/realtime", req)
	if err != nil {
		return nil, err
	}
	list := res.Get("list").Array()
	if len(list) == 0 {
		return nil, fmt.Errorf("order not found")
	}

	bOrder := list[0]
	side := exchange.OrderSideBuy
	if bOrder.Get("side").String() == "Sell" {
		side = exchange.OrderSideSell
	}
	createTime := time.UnixMilli(bOrder.Get("createdTime").Int())
	updateTime := time.UnixMilli(bOrder.Get("updatedTime").Int())
	order = &exchange.Order{
		InstrumentType: instrumentType,
		Symbol:         symbol,
		OrderID:        bOrder.Get("orderId").String(),
		Side:           side,
		Type:           orderType,
		Qty:            bOrder.Get("qty").Float(),
		Price:          bOrder.Get("price").Float(),
		TriggerPrice:   bOrder.Get("triggerPrice").Float(),
		Status:         convertOrderStatus(bOrder.Get("orderStatus").String(), orderType != exchange.Limit),
		ReduceOnly:     bOrder.Get("reduceOnly").Bool(),
		ExecQty:        bOrder.Get("cumExecQty").Float(),
		ExecPrice:      bOrder.Get("avgPrice").Float(),
		CreateTime:     &createTime,
		UpdateTime:     &updateTime,
	}

	if bOrder.Get("marketUnit").String() == "quoteCoin" {
		order.QuoteQty = bOrder.Get("qty").Float()
		order.Qty = 0
	}

	return order, nil
}

func (this *Bybit) CancelOrder(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol, orderID string) error {
	res, err := this.request("POST", "/v5/order/cancel", map[string]any{
		"category": instrumentTypeToCategory(instrumentType),
		"symbol":   symbol,
		"orderId":  orderID,
	})
	if err != nil {
		return err
	}
	if res.Get("orderId").String() != orderID {
		return fmt.Errorf("cancel order failed, res: %v", res.String())
	}
	return err
}

func (this *Bybit) GetAccountConfig(instrumentType exchange.InstrumentType) (config *exchange.AccountConfig, _ error) {
	res, err := this.request("GET", "/v5/account/info", nil)
	if err != nil {
		return nil, err
	}

	bMarginMode := res.Get("marginMode").String()
	var marginMode exchange.AccountMarginMode
	if bMarginMode == string(MarginModeIsolated) {
		marginMode = exchange.AccountMarginModeIsolated
	} else if bMarginMode == string(MarginModeCross) {
		marginMode = exchange.AccountMarginModeCross
	} else if bMarginMode == string(MarginModePortfolio) {
		marginMode = exchange.AccountMarginModePortfolio
	} else {
		return nil, fmt.Errorf("unknown margin mode: %s", bMarginMode)
	}

	return &exchange.AccountConfig{
		AccountBalanceCross: true,
		MarginMode:          marginMode,
		DualPositionSide:    true,
	}, nil
}

// 该接口获取的是统一(交易)账号余额
func (this *Bybit) GetAccountBalances(instrumentType exchange.InstrumentType) (accountBalances []*exchange.AccountBalance, err error) {
	res, err := this.request("GET", "/v5/account/wallet-balance", map[string]any{"accountType": "UNIFIED"})
	if err != nil {
		return nil, err
	}

	for _, account := range res.Get("list").Array() {
		for _, accountCoin := range account.Get("coin").Array() {
			coin := accountCoin.Get("coin").String()
			available := accountCoin.Get("availableToWithdraw").Float()
			if available == 0 && this.IsSubaccount() {
				available, err = this.GetAvailableWithdrawal(coin)
				if err != nil {
					return nil, fmt.Errorf("get available withdrawal failed: %s", err)
				}
			}

			accountBalances = append(accountBalances, &exchange.AccountBalance{
				InstrumentType: instrumentType,
				Currency:       coin,
				Total:          accountCoin.Get("equity").Float(),
				Available:      available,
			})
		}
	}

	return
}

// 查詢統一帳戶錢包裡指定幣種的可劃轉餘額
// 子账号使用 /wallet-balance 接口获取到的 availableToWithdraw 是 0，只能用这个接口获取到可划转余额
func (this *Bybit) GetAvailableWithdrawal(coin string) (float64, error) {
	res, err := this.request("GET", "/v5/account/withdrawal", map[string]any{"coinName": coin})
	if err != nil {
		return 0, err
	}

	return res.Get("availableWithdrawal").Float(), nil
}

func (this *Bybit) GetAccountCurrencies(instrumentType exchange.InstrumentType) (currencies []string, _ error) {
	this.instruments.Range(func(key string, i *Instrument) bool {
		if i.instrumentType == instrumentType && !utils.StringEnumContains(currencies, i.BaseCoin) {
			currencies = append(currencies, i.BaseCoin)
		}
		return true
	})
	return
}

func (this *Bybit) GetPositions(instrumentType exchange.InstrumentType, symbol string, allowCache bool) (positions []*exchange.Position, er error) {
	resList, err := this.getPositions(instrumentType, symbol)
	if err != nil {
		return nil, err
	}

	for _, rawPos := range resList {
		sz := rawPos.Get("size").Float()
		if sz == 0 {
			continue
		}

		p := exchange.Position{}
		p.InstrumentType = instrumentType
		p.ExchangeName = this.GetName()
		p.Symbol = rawPos.Get("symbol").String()
		side := rawPos.Get("side").String()
		if side == "Sell" {
			p.Qty = -math.Abs(sz)
			p.Side = exchange.PositionSideShort
		} else {
			p.Qty = math.Abs(sz)
			p.Side = exchange.PositionSideLong
		}
		p.EntryPrice = rawPos.Get("avgPrice").Float()
		p.MarkPrice = rawPos.Get("markPrice").Float()
		p.Leverage = rawPos.Get("leverage").Float()
		p.LiquidationPrice = rawPos.Get("liqPrice").Float()
		p.UnrealisedPNL = rawPos.Get("unrealisedPnl").Float()
		p.Margin = rawPos.Get("positionBalance").Float()
		p.InitialMargin = rawPos.Get("positionIM").Float()
		p.MaintenanceMargin = rawPos.Get("positionMM").Float()
		updateTime := time.UnixMilli(rawPos.Get("updatedTime").Int())
		p.UpdateTime = &updateTime
		positions = append(positions, &p)
	}

	return
}

func (this *Bybit) SetDualPositionSide(instrumentType exchange.InstrumentType, dualPositionSide bool) error {
	return exchange.ErrNotImplemented
}

func (this *Bybit) SetMarginMode(instrumentType exchange.InstrumentType, symbol string, mode exchange.MarginMode) error {
	// 只能设置账号保证金模式
	var accountMode exchange.AccountMarginMode
	if mode == exchange.Isolated {
		accountMode = exchange.AccountMarginModeIsolated
	} else if mode == exchange.Cross {
		accountMode = exchange.AccountMarginModeCross
	} else {
		return exchange.ErrInvalidArgs
	}
	return this.SetAccountMarginMode(instrumentType, accountMode)
}

func (this *Bybit) SetAccountMarginMode(instrumentType exchange.InstrumentType, mode exchange.AccountMarginMode) error {
	var setMarginMode MarginMode
	if mode == exchange.AccountMarginModeIsolated {
		setMarginMode = MarginModeIsolated
	} else if mode == exchange.AccountMarginModeCross {
		setMarginMode = MarginModeCross
	} else if mode == exchange.AccountMarginModePortfolio {
		setMarginMode = MarginModePortfolio
	} else {
		return exchange.ErrInvalidArgs
	}

	_, err := this.request("POST", "/v5/account/set-margin-mode", map[string]any{
		"setMarginMode": string(setMarginMode),
	})
	return err
}

func (this *Bybit) QueryFundingHistory(instrumentType exchange.InstrumentType, symbol string, limit int, from time.Time, to time.Time) (his []*exchange.FundingHistory, _ error) {
	req := map[string]any{
		"category": instrumentTypeToCategory(instrumentType),
		"symbol":   symbol,
		"limit":    200,
	}
	if limit < req["limit"].(int) {
		req["limit"] = limit
	}
	if to.IsZero() {
		to = time.Now()
	}
	req["endTime"] = to.UnixMilli()

	resList := []gjson.Result{}
	for {
		res, err := this.request("GET", "/v5/market/funding/history", req)
		if err != nil {
			return nil, err
		}
		l := res.Get("list").Array()
		if (len(resList)+len(l) >= limit) || len(l) < req["limit"].(int) {
			resList = append(resList, l...)
			break
		}

		last := l[len(l)-1]
		l = l[:len(l)-1]
		resList = append(resList, l...)
		req["endTime"] = last.Get("fundingRateTimestamp").Int()
	}

	period := "8h"
	ins, _ := this.instruments.Load(getInstrumentsCacheKey(instrumentType, symbol))
	if ins != nil {
		period = fmt.Sprintf("%dh", ins.FundingIntervalHours)
	}

	_, code, err := this.TranslateFutureSymbol(instrumentType, symbol, "")
	if err != nil {
		return his, err
	}

	for _, raw := range resList {
		h := exchange.FundingHistory{}
		h.Code = code
		h.Exchange = this.GetName()
		h.Symbol = raw.Get("symbol").String()
		h.Rate = raw.Get("fundingRate").Float()
		h.Time = time.UnixMilli(raw.Get("fundingRateTimestamp").Int())
		h.Period = period
		his = append(his, &h)
	}
	return
}

func (this *Bybit) GetTradablePairs(instrumentType exchange.InstrumentType, uSymbol string) (pairs []string, _ error) {
	var instruments []Instrument
	if instrumentType == exchange.CoinMarginedFutures {
		instruments = this.cFuturesCacheLatestInstruments()
	} else if instrumentType == exchange.USDXMarginedFutures {
		instruments = this.uFuturesCacheLatestInstruments()
	} else if instrumentType == exchange.Spot {
		instruments = this.spotCacheLatestInstruments()
	} else {
		return pairs, exchange.ErrNotAvailableForInstrumentType
	}

	for _, instrument := range instruments {
		if instrument.IsExpiredFutures() {
			continue
		}
		if instrumentType == exchange.USDXMarginedFutures && uSymbol != instrument.QuoteCoin {
			continue
		}
		pairs = append(pairs, instrument.Symbol)
	}
	return
}

func (this *Bybit) SetLeverage(instrumentType exchange.InstrumentType, symbol string, marginMode exchange.MarginMode, side exchange.PositionSide, leverage float64) error {
	// leverage round to leverageStep
	ins := this.getInstrument(instrumentType, symbol)
	if ins == nil {
		return fmt.Errorf("set leverage failed, instrument not found")
	}

	if leverage < ins.MinLeverage {
		this.Warnf("leverage %f is less than min leverage %f, use min leverage", leverage, ins.MinLeverage)
		leverage = ins.MinLeverage
	} else if leverage > ins.MaxLeverage {
		this.Warnf("leverage %f is greater than max leverage %f, use max leverage", leverage, ins.MaxLeverage)
		leverage = ins.MaxLeverage
	}

	leverage = math.Round(leverage/ins.LeverageStep) * ins.LeverageStep

	_, err := this.request("POST", "/v5/position/set-leverage", map[string]any{
		"category":     instrumentTypeToCategory(instrumentType),
		"symbol":       symbol,
		"buyLeverage":  fmt.Sprintf("%f", leverage),
		"sellLeverage": fmt.Sprintf("%f", leverage),
	})
	if err != nil {
		if strings.Contains(err.Error(), "leverage not modified") {
			return nil
		}
		return err
	}
	return nil
}

func (this *Bybit) GetLeverage(instrumentType exchange.InstrumentType, symbol string, marginMode exchange.MarginMode) (leverageLong, leverageShort float64, err error) {
	return 0, 0, exchange.ErrNotImplemented
}

// code -> spotSymbol, spotCode, futureSymbol
var CodeToSymbolFixes = map[string][3]string{
	"KPEPE00.U": {"", "", "1000PEPEUSDT"},
}

func (this *Bybit) TranslateSymbolCode(symbolCode *exchange.SymbolCode) (spotAndFutureSymbols []*exchange.SymbolPair, er error) {
	spotSymbol, _, futureSymbol, fixed := this.FixSymbolCode(CodeToSymbolFixes, symbolCode)
	if fixed {
		spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(symbolCode, spotSymbol, futureSymbol))
		return
	}

	spotAndFutureSymbols = []*exchange.SymbolPair{}
	this.CacheInstruments(false)

	spotPairs := []string{}
	futurePairs := []string{}
	this.instruments.Range(func(_ string, ins *Instrument) bool {
		if ins.instrumentType == exchange.Spot {
			spotPairs = append(spotPairs, ins.Symbol)
		} else if ins.instrumentType == exchange.CoinMarginedFutures || ins.instrumentType == exchange.USDXMarginedFutures {
			futurePairs = append(futurePairs, ins.Symbol)
		}
		return true
	})

	codeBase := symbolCode.Coin()
	codeQuote := this.GetSymbolCodeQuote(symbolCode)
	spotQuote := symbolCode.USDXSymbol

	for _, s := range spotPairs {
		if strings.EqualFold(s, fmt.Sprintf("%s%s", symbolCode.Coin(), spotQuote)) {
			spotSymbol = s
		}
	}

	if symbolCode.IsFuture() {
		// USDT 合约格式为: BTCUSDT, 无交割
		// USDC 合约格式为: 永续 BTCPERP, 交割 BTC-09AUG24
		// 币本位格式为: 永续 BTCUSD, 交割 BTCUSDU24

		for _, s := range futurePairs {
			if codeQuote == "USDT" { // USDT 合约
				if s == fmt.Sprintf("%s%s", codeBase, codeQuote) {
					futureSymbol = s
				}
			} else if codeQuote == "USDC" { // USDC 合约
				if symbolCode.IsPerp() {
					if s == fmt.Sprintf("%s%s", codeBase, "PERP") {
						futureSymbol = s
					}
				} else if symbolCode.IsWildcard() {
					if strings.HasPrefix(s, fmt.Sprintf("%s-", codeBase)) {
						futureSymbol = s
					}
				} else {
					if strings.HasPrefix(s, fmt.Sprintf("%s-", codeBase)) {
						monthStr := s[len(s)-5 : len(s)-2]
						if convertUSDCMonth(monthStr) == symbolCode.Month() {
							futureSymbol = s
						}
					}
				}
			} else { // 币本位合约
				if symbolCode.IsPerp() {
					if s == fmt.Sprintf("%s%s", codeBase, "USD") {
						futureSymbol = s
					}
				} else if symbolCode.IsWildcard() {
					if strings.HasPrefix(s, fmt.Sprintf("%s%s", codeBase, "USD")) && len(s) == (len(codeBase)+6) {
						futureSymbol = s
					}
				} else {
					if strings.HasPrefix(s, fmt.Sprintf("%s%s", codeBase, "USD")) && len(s) == (len(codeBase)+6) {
						monthStr := string(s[len(s)-3])
						if convertUSDMonth(monthStr) == symbolCode.Month() {
							futureSymbol = s
						}
					}
				}
			}

			if futureSymbol != "" {
				spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(symbolCode, spotSymbol, futureSymbol))
				if !symbolCode.IsWildcard() {
					break
				}
			}
		}
	} else {
		if spotSymbol != "" {
			spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(symbolCode, spotSymbol, ""))
		} else {
			er = fmt.Errorf("spot symbol not found (%s)", symbolCode)
			return
		}
	}
	if len(spotAndFutureSymbols) == 0 {
		er = fmt.Errorf("bybit spot and future symbol not found, symbol code: %s, USDX symbol: %s", symbolCode, symbolCode.USDXSymbol)
	}
	return
}

func convertUSDCMonth(code string) (digits string) {
	monthCodeMap := map[string]string{
		"AUG": "08",
		"SEP": "09",
		"OCT": "10",
		"NOV": "11",
		"DEC": "12",
		"JAN": "01",
		"FEB": "02",
		"MAR": "03",
		"APR": "04",
		"MAY": "05",
		"JUN": "06",
		"JUL": "07",
	}
	if value, ok := monthCodeMap[code]; ok {
		return value
	}
	return ""
}

func convertUSDMonth(code string) (digits string) {
	monthCodeMap := map[string]string{
		"H": "03",
		"M": "06",
		"U": "09",
		"Z": "12",
	}
	if value, ok := monthCodeMap[code]; ok {
		return value
	}
	return ""
}

// futureSymbol -> spotSymbol, spotCode, futureCode
var SymbolToCodeFixes = map[string][3]string{
	"1000PEPEUSDT": {"", "", "KPEPE00.U"},
}

func (this *Bybit) TranslateFutureSymbol(instrumentType exchange.InstrumentType, futureSymbol, uSymbol string) (spotSymbol string, futureCode *exchange.SymbolCode, er error) {
	var fixed bool
	spotSymbol, _, futureCode, fixed = this.FixFutureSymbol(SymbolToCodeFixes, instrumentType, futureSymbol, uSymbol)
	if fixed {
		return
	}

	spotSymbol = ""
	spotPairs := []string{}
	futurePairs := []string{}
	this.instruments.Range(func(_ string, ins *Instrument) bool {
		if ins.instrumentType == exchange.Spot {
			spotPairs = append(spotPairs, ins.Symbol)
		} else if ins.instrumentType == exchange.CoinMarginedFutures || ins.instrumentType == exchange.USDXMarginedFutures {
			futurePairs = append(futurePairs, ins.Symbol)
		}
		return true
	})

	if uSymbol == "" {
		uSymbol = "USDT"
	}

	futureSymbolExist := false
	for _, s := range futurePairs {
		if strings.EqualFold(futureSymbol, s) {
			futureSymbolExist = true
			break
		}
	}

	if !futureSymbolExist {
		return "", nil, errors.New("future symbol not found")
	}

	// USDT 合约格式为: BTCUSDT, 无交割
	// USDC 合约格式为: 永续 BTCPERP, 交割 BTC-09AUG24
	// 币本位格式为: 永续 BTCUSD, 交割 BTCUSDU24
	futureCodeStr := ""
	if strings.HasSuffix(futureSymbol, "USDT") {
		// USDT 永续
		if uSymbol == "USDT" {
			futureCodeStr = futureSymbol[:len(futureSymbol)-4] + "00.U"
		}
	} else if strings.HasSuffix(futureSymbol, "PERP") {
		// USDC 永续
		if uSymbol == "USDC" {
			futureCodeStr = futureSymbol[:len(futureSymbol)-4] + "00.U"
		}
	} else if strings.Contains(futureSymbol, "-") {
		// USDC 交割
		if uSymbol == "USDC" {
			monthStr := futureSymbol[len(futureSymbol)-5 : len(futureSymbol)-2]
			month := convertUSDCMonth(monthStr)
			futureCodeStr = fmt.Sprintf("%s%s.U", strings.Split(futureSymbol, "-")[0], month)
		}
	} else if strings.HasSuffix(futureSymbol, "USD") {
		// 币本位永续
		futureCodeStr = futureSymbol[:len(futureSymbol)-3] + "00"
	} else {
		// 币本位交割
		monthStr := string(futureSymbol[len(futureSymbol)-3])
		month := convertUSDMonth(monthStr)
		if month != "" && fmt.Sprintf("USD%s", monthStr) == futureSymbol[len(futureSymbol)-6:len(futureSymbol)-2] {
			futureCodeStr = fmt.Sprintf("%s%s", futureSymbol[:len(futureSymbol)-6], month)
		}
	}

	if futureCodeStr == "" {
		return "", nil, errors.New("future code not found")
	}

	if instrumentType == exchange.USDXMarginedFutures && !strings.HasSuffix(futureCodeStr, ".U") {
		return "", nil, errors.New("future code is not USDT margined")
	} else if instrumentType == exchange.CoinMarginedFutures && strings.HasSuffix(futureCodeStr, ".U") {
		return "", nil, errors.New("future code is not coin margined")
	}

	futureCode = &exchange.SymbolCode{Code: futureCodeStr, USDXSymbol: uSymbol}

	for _, s := range spotPairs {
		if strings.EqualFold(s, fmt.Sprintf("%s%s", futureCode.Coin(), uSymbol)) {
			spotSymbol = s
		}
	}
	return
}

// 更新交易对信息，force 时忽略缓存
func (this *Bybit) CacheInstruments(force bool) error {
	spotCached := false
	cFutureCached := false
	uFutureCached := false
	this.instruments.Range(func(_ string, instrument *Instrument) bool {
		if instrument.instrumentType == exchange.Spot {
			spotCached = true
		} else if instrument.instrumentType == exchange.CoinMarginedFutures {
			cFutureCached = true
		} else if instrument.instrumentType == exchange.USDXMarginedFutures {
			uFutureCached = true
		}
		return true
	})

	if force || !spotCached {
		if len(this.spotCacheLatestInstruments()) == 0 {
			return errors.New("cache spot instruments failed")
		}
	}

	if force || !cFutureCached {
		if len(this.cFuturesCacheLatestInstruments()) == 0 {
			return errors.New("cache cfutures instruments failed")
		}
	}

	if force || !uFutureCached {
		if len(this.uFuturesCacheLatestInstruments()) == 0 {
			return errors.New("cache ufutures instruments failed")
		}
	}

	go this.updateInstrumentPricesLoop()

	return nil
}

// 更新合约 markprice 等价格信息
func (this *Bybit) updateInstrumentPrices() {
	categories := []InstrumentCategory{InstrumentCategoryLinear, InstrumentCategoryInverse}
	for _, category := range categories {
		tickers, err := this.getMarketTickers(category)
		if err != nil {
			this.Errorf("get spot tickers failed: %s", err)
			return
		}

		for _, ticker := range tickers {
			ins, _ := this.instruments.Load(getInstrumentsCacheKey(categoryToInstrumentType(category), ticker.Get("symbol").String()))
			if ins != nil {
				ins.MarkPrice = ticker.Get("markPrice").Float()
				ins.IndexPrice = ticker.Get("indexPrice").Float()
				ins.FundingRate = ticker.Get("fundingRate").Float()
				ins.NextFundingTime = time.UnixMilli(ticker.Get("nextFundingTime").Int())
				ins.OpenInterest = ticker.Get("openInterest").Float()
				ins.tickerUpdateTime = time.Now().Unix()
			}
		}
	}
}

func (this *Bybit) updateInstrumentPricesLoop() {
	if !this.updatePriceMutex.TryLock() {
		return
	}
	defer this.updatePriceMutex.Unlock()

	this.updateInstrumentPrices()

	t := time.NewTicker(time.Second * 5)
	for {
		if this.Removed.Load() {
			break
		}

		select {
		case <-t.C:
			this.updateInstrumentPrices()
		}
	}
}

func (this *Bybit) GetName() string {
	return exchange.Bybit
}

// 获取并计算最新 K 线数据
func (this *Bybit) GetLatestKLines(instrumentType exchange.InstrumentType, symbol string, periodHour, num int) (klines []*exchange.KLine, er error) {
	toTime := time.Now()                                               // 当前时间
	fromTime := toTime.Add(-time.Hour * time.Duration(periodHour*num)) // 当前时间 - num * PeriodHour 个小时
	limit := 1000

	req := map[string]any{
		"category": instrumentTypeToCategory(instrumentType),
		"symbol":   symbol,
		"interval": "60",
		"start":    fromTime.UnixMilli(),
		"limit":    limit,
	}

	resList := []gjson.Result{}
	for {
		res, err := this.request("GET", "/v5/market/kline", req)
		if err != nil {
			return nil, err
		}
		l := res.Get("list").Array() // 按时间倒序的 K 线数据
		if len(l) >= limit {
			// 需要翻页 & 删掉第一条数据
			req["start"] = l[0].Array()[0].Int()
			l = l[1:]
		}

		sort.Slice(l, func(i, j int) bool {
			return l[i].Array()[0].Int() < l[j].Array()[0].Int()
		})

		resList = append(resList, l...)

		if len(l) < limit {
			break
		}
	}

	// 并小时线为 PeriodHour 小时线
	// 取 PeriodHour 点，如 6小时线，取 0、6、12、18 点
	periodSeconds := int64(periodHour * 3600)
	klineCount := 0
	for _, candle := range resList {
		ts := candle.Array()[0].Int() / 1000
		if ts%periodSeconds == 0 {
			// 这根小时线是 PeriodHour 的开盘线
			klineCount++

			klines = append(klines, &exchange.KLine{
				Open:  candle.Array()[1].Float(),
				High:  candle.Array()[2].Float(),
				Low:   candle.Array()[3].Float(),
				Close: candle.Array()[4].Float(),
				Time:  ts,
			})
		} else if klineCount > 0 {
			// 非 PeriodHour 的开盘线
			klineIdx := klineCount - 1

			klines[klineIdx].Close = candle.Array()[4].Float()
			klines[klineIdx].High = math.Max(klines[klineIdx].High, candle.Array()[2].Float())
			klines[klineIdx].Low = math.Min(klines[klineIdx].Low, candle.Array()[3].Float())
		}
	}

	return
}

func (this *Bybit) TransferAsset(from, to exchange.InstrumentType, coin string, amount float64) error {
	return nil
}

func (this *Bybit) queryCoinInfo(coin string) ([]gjson.Result, error) {
	req, err := this.request("GET", "/v5/asset/coin/query-info", map[string]any{"coin": coin})
	if err != nil {
		return nil, err
	}

	return req.Get("rows").Array(), nil
}

func (this *Bybit) GetWithdrawChains(coin string) ([]exchange.WithdrawChain, error) {
	coinInfos, err := this.queryCoinInfo(coin)
	if err != nil {
		return nil, err
	}

	chains := []exchange.WithdrawChain{}
	for _, coinInfo := range coinInfos {
		if coinInfo.Get("coin").String() == coin {
			for _, chain := range coinInfo.Get("chains").Array() {
				chains = append(chains, chain.Get("chain").String())
			}
		}
	}
	return chains, nil
}

func (this *Bybit) Withdraw(coin string, address string, amount float64, chain exchange.WithdrawChain) (id string, err error) {
	if coin == "" {
		return "", errors.New("coin is empty")
	}
	if address == "" {
		return "", errors.New("address is empty")
	}

	req, err := this.request("POST", "/v5/asset/withdraw/create", map[string]any{
		"coin":      coin,
		"chain":     chain,
		"address":   address,
		"amount":    fmt.Sprintf("%v", amount),
		"timestamp": time.Now().UnixMilli(),
		"feeType":   1, // 輸入金額不是實際收到的金額, 系統將會自動計算所需的手續費
	})
	if err != nil {
		return "", err
	}

	id = req.Get("id").String()
	if id == "" {
		return "", fmt.Errorf("withdraw failed, res: %v", req.String())
	}

	return id, nil
}

// 该接口获取的是资金账号余额
func (this *Bybit) GetFundAccountBalances(coin string) (accountBalances []*exchange.AccountBalance, err error) {
	req := map[string]any{"accountType": "FUND"}
	if coin != "" {
		req["coin"] = coin
	}
	res, err := this.request("GET", "/v5/asset/transfer/query-account-coins-balance", req)
	if err != nil {
		return nil, err
	}

	for _, balance := range res.Get("balance").Array() {
		if balance.Get("coin").String() == coin || coin == "" {
			accountBalances = append(accountBalances, &exchange.AccountBalance{
				Currency:  balance.Get("coin").String(),
				Total:     balance.Get("walletBalance").Float(),
				Available: balance.Get("transferBalance").Float(),
			})
		}
	}

	return
}

// 资金账号与交易账号之间的划转
func (this *Bybit) InterTransfer(coin string, amount float64, from, to AccountType) error {
	req := map[string]any{
		"transferId":      uuid.New().String(),
		"coin":            coin,
		"amount":          fmt.Sprintf("%v", amount),
		"fromAccountType": string(from),
		"toAccountType":   string(to),
	}
	res, err := this.request("POST", "/v5/asset/transfer/inter-transfer", req)
	if err != nil {
		return err
	}
	if res.Get("status").String() == "SUCCESS" {
		return nil
	}
	return fmt.Errorf("inter transfer failed, res: %v", res.String())
}

func (this *Bybit) GetWithdrawFee(coin, chain exchange.WithdrawChain) (fee float64, err error) {
	coinInfos, err := this.queryCoinInfo(coin)
	if err != nil {
		return 0, err
	}

	for _, coinInfo := range coinInfos {
		if coinInfo.Get("coin").String() == coin {
			for _, chainInfo := range coinInfo.Get("chains").Array() {
				if chainInfo.Get("chain").String() == chain {
					return chainInfo.Get("withdrawFee").Float(), nil
				}
			}
		}
	}
	return 0, fmt.Errorf("withdraw fee not found, coin: %s, chain: %s", coin, chain)
}

func (this *Bybit) UniversalTransfer(coin string, amount float64, fromMemberId, toMemberId int, from, to AccountType) (string, error) {
	req := map[string]any{
		"transferId":      uuid.New().String(),
		"coin":            coin,
		"amount":          fmt.Sprintf("%v", amount),
		"fromAccountType": string(from),
		"toAccountType":   string(to),
		"fromMemberId":    fromMemberId,
		"toMemberId":      toMemberId,
	}
	res, err := this.request("POST", "/v5/asset/transfer/universal-transfer", req)
	if err != nil {
		return "", err
	}
	if res.Get("status").String() == "SUCCESS" {
		return res.Get("transferId").String(), nil
	}
	return "", fmt.Errorf("universal transfer failed, res: %v", res.String())
}

func (this *Bybit) IsSubaccount() bool {
	return this.withdrawApiKey != nil && this.withdrawApiKey.UID != 0 && this.apiKey.UID != this.withdrawApiKey.UID
}

func (this *Bybit) SubaccountTransfer(coin string, amount float64, isDeposit bool) (err error) {
	if !this.IsSubaccount() {
		return nil
	}

	fromMemberId := this.apiKey.UID
	toMemberId := this.withdrawApiKey.UID
	if isDeposit {
		fromMemberId, toMemberId = toMemberId, fromMemberId
	}
	_, err = this.UniversalTransfer(coin, amount, fromMemberId, toMemberId, AccountTypeFund, AccountTypeFund)
	return
}

func (this *Bybit) GetWithdrawAccountBalance(coin string) (total, available float64, er error) {
	req := map[string]any{"accountType": "FUND"}
	if coin != "" {
		req["coin"] = coin
	}
	res, err := this._request("GET", "/v5/asset/transfer/query-account-coins-balance", req, this.IsSubaccount())
	if err != nil {
		return 0, 0, err
	}

	for _, balance := range res.Get("balance").Array() {
		if balance.Get("coin").String() == coin {
			return balance.Get("walletBalance").Float(), balance.Get("transferBalance").Float(), nil
		}
	}

	return
}

var _ exchange.Exchange = (*Bybit)(nil)

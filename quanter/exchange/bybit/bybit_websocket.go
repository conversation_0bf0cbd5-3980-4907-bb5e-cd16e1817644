package bybit

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/gorilla/websocket"
	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/exchange"
)

const WS_URL = "stream.bybit.com"
const TESTNET_WS_URL = "stream-testnet.bybit.com"

func (this *Bybit) ConnectWebsocket(instrumentTypes []exchange.InstrumentType, connectedCallback func(connected bool)) {
	if this.apiKey.ApiSecret == "" {
		// 密码还未设置,稍后再试
		go this.reconnectLater()
		return
	}

	hostURL := WS_URL
	if this.IsTestnet {
		hostURL = TESTNET_WS_URL
	}

	u := url.URL{Scheme: "wss", Host: hostURL, Path: "/v5/private"}
	this.Infof("connecting to %s", u.String())

	// 进行连接
	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		this.Errorf("ws dial err: %s", err)
		go this.reconnectLater()
		return
	}
	this.wsConn = c

	defer func() {
		// 函数运行结束时关闭连接
		c.Close()
		this.Infof("websocket closed")
	}()

	done := make(chan struct{}) // 连接是否结束

	this.authenticate()

	go this.publicWebsocket(InstrumentCategorySpot)
	go this.publicWebsocket(InstrumentCategoryLinear)
	go this.publicWebsocket(InstrumentCategoryInverse)

	go func() {
		defer close(done)
		for {
			// 读取实时消息
			_, message, err := c.ReadMessage()
			if err != nil {
				this.Errorf("read message err: %s", err)

				go this.reconnectLater()
				return
			}
			if strings.Contains(string(message), `"op":"pong"`) {
				// 心跳消息
				continue
			}

			// this.Debugf("websocket recv: %s", message)

			data := gjson.Parse(string(message))
			this.handleWebsocketMessage("", data)
		}
	}()

	subscribeTicker := time.NewTicker(time.Second * 10)
	defer subscribeTicker.Stop()

	for {
		// 保存运行直到连接结束
		select {
		case <-subscribeTicker.C:
			this.sendWsMsg(c, `{"op":"ping"}`)
		case <-done:
			return
		}
	}
}

func (this *Bybit) sendWsMsg(ws *websocket.Conn, msg string) error {
	this.wsMutex.Lock()
	defer this.wsMutex.Unlock()

	if ws == nil {
		return fmt.Errorf("can not subscribe when wsConn is nil")
	}
	// this.Debugf("websocket send: %s", msg)
	return ws.WriteMessage(websocket.TextMessage, []byte(msg))
}

func (this *Bybit) subscribe(ws *websocket.Conn, sub string) error {
	msg := fmt.Sprintf(`{"op": "subscribe", "args": %s }`, sub)
	return this.sendWsMsg(ws, msg)
}

func (this *Bybit) authenticate() {
	expire := time.Now().Add(time.Second * 10).UnixMilli()
	h := hmac.New(sha256.New, []byte(this.apiKey.ApiSecret))
	h.Write([]byte(fmt.Sprintf("GET/realtime%d", expire)))
	sign := hex.EncodeToString(h.Sum(nil))
	this.sendWsMsg(this.wsConn, fmt.Sprintf(`{"op":"auth","args":["%s",%d,"%s"]}`, this.apiKey.ApiKey, expire, sign))
}

func (this *Bybit) handleWebsocketMessage(category InstrumentCategory, msg gjson.Result) {
	op := msg.Get("op").String()
	if op == "auth" {
		success := msg.Get("success").Bool()
		if success {
			this.subscribe(this.wsConn, `["wallet", "order"]`)
		} else {
			this.Errorf("auth failed, msg: %s", msg.String())
		}
	} else if op == "subscribe" {
		success := msg.Get("success").Bool()
		if success {
			this.Infof("subscribe success, msg: %s", msg.String())
		} else {
			this.Errorf("subscribe failed, msg: %s", msg.String())
		}
	} else if op == "ping" {
		return
	} else if op != "" {
		this.Infof("unhandled op msg: %s", msg.String())
	}

	topic := msg.Get("topic").String()
	if topic == "" {
		return
	}

	if topic == "wallet" {
		for _, data := range msg.Get("data").Array() {
			for _, accountCoin := range data.Get("coin").Array() {
				coin := accountCoin.Get("coin").String()
				margin := &exchange.UserMargin{
					WalletBalance:   accountCoin.Get("walletBalance").Float(),
					MarginBalance:   accountCoin.Get("equity").Float(),
					AvailableMargin: accountCoin.Get("availableToWithdraw").Float(),
					Currency:        coin,
				}

				this.MarginUpdatedCallback(margin, margin.Currency)
			}
		}
	} else if topic == "order" {
		for _, order := range msg.Get("data").Array() {
			if category == "" {
				category = InstrumentCategory(order.Get("category").String())
			}
			exOrder := &exchange.Order{
				InstrumentType: categoryToInstrumentType(category),
				Symbol:         order.Get("symbol").String(),
				OrderID:        order.Get("orderId").String(),
				Price:          order.Get("price").Float(),
				Qty:            order.Get("qty").Float(),
				ExecQty:        order.Get("cumExecQty").Float(),
			}
			if order.Get("side").String() == "Buy" {
				exOrder.Side = exchange.OrderSideBuy
			} else {
				exOrder.Side = exchange.OrderSideSell
			}
			orderType := exchange.Limit
			triggerPrice := order.Get("triggerPrice").Float()
			if triggerPrice != 0 {
				orderType = exchange.StopLimit
			}

			exOrder.Status = convertOrderStatus(order.Get("orderStatus").String(), orderType != exchange.Limit)
			this.OrderUpdatedCallback(exOrder)
		}
	} else if strings.HasPrefix(topic, "tickers.") {
		exTicker := &exchange.Ticker{
			InstrumentType: categoryToInstrumentType(category),
			Time:           msg.Get("ts").Int(),
			Symbol:         msg.Get("data.symbol").String(),
			Close:          msg.Get("data.lastPrice").Float(),
		}
		if exTicker.Symbol == "" || exTicker.Close == 0 {
			return
		}
		this.StoreTickerCache(exTicker)
		this.CheckPriceTrigger(exTicker.InstrumentType, exTicker.Symbol, exTicker.Close, time.UnixMilli(exTicker.Time))
	} else {
		this.Infof("unhandled topic msg: %s", msg.String())
	}
}

func (this *Bybit) reconnectLater() {
	if this.wsClosed {
		return
	}
	this.Infof("ws reconnect in 30 seconds...")
	time.Sleep(time.Second * 30)
	this.ConnectWebsocket([]exchange.InstrumentType{}, nil)
}

func (this *Bybit) CloseWebsocket(stop bool) {
	if stop {
		this.wsClosed = true
	}
	if this.wsConn != nil {
		this.wsConn.Close()
	}
	this.wsConnsPublic.Range(func(_ string, c *websocket.Conn) bool {
		if c != nil {
			c.Close()
		}
		return true
	})
}

func (this *Bybit) publicWebsocket(category InstrumentCategory) {
	hostURL := WS_URL
	if this.IsTestnet {
		hostURL = TESTNET_WS_URL
	}

	u := url.URL{Scheme: "wss", Host: hostURL, Path: fmt.Sprintf("/v5/public/%s", category)}
	this.Infof("connecting to %s", u.String())

	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		this.Errorf("public ws dial err: %s", err)
		go this.reconnectPublicWebsocket(category)
		return
	}
	this.wsConnsPublic.Store(string(category), c)

	defer func() {
		c.Close()
		this.Infof("public %s websocket closed", category)
	}()

	done := make(chan struct{})

	go func() {
		defer close(done)
		for {
			// 读取实时消息
			_, message, err := c.ReadMessage()
			if err != nil {
				this.Errorf("read public ws message err: %s", err)
				go this.reconnectPublicWebsocket(category)
				return
			}

			if strings.Contains(string(message), `"op":"pong"`) {
				// 心跳消息
				continue
			}

			// this.Debugf("websocket public[%s] recv: %s", category, message)

			data := gjson.Parse(string(message))
			this.handleWebsocketMessage(category, data)
		}
	}()

	this.subscribedSymbols.Store(string(category), []string{})
	subscribeTicker := time.NewTicker(time.Second * 10)
	defer subscribeTicker.Stop()

	for {
		select {
		case <-subscribeTicker.C:
			this.sendWsMsg(c, `{"op":"ping"}`)
			this.subscribeSymbols(category)
		case <-done:
			return
		}
	}
}

func (this *Bybit) reconnectPublicWebsocket(category InstrumentCategory) {
	if this.wsClosed {
		return
	}
	this.Infof("public ws %s reconnect in 30 seconds...", category)
	time.Sleep(time.Second * 30)
	this.publicWebsocket(category)
}

func (this *Bybit) subscribeSymbols(category InstrumentCategory) {
	ws, ok := this.wsConnsPublic.Load(string(category))
	if !ok || ws == nil {
		return
	}

	if !this.EnableRealtimePrice {
		return
	}

	symbolsNeed := []string{}
	for _, priceTrigger := range this.PriceTriggers {
		if instrumentTypeToCategory(priceTrigger.InstrumentType) == category {
			symbolsNeed = append(symbolsNeed, priceTrigger.Symbol)
		}
	}

	for _, watch := range this.PriceWatches {
		if instrumentTypeToCategory(watch.InstrumentType) == category {
			symbolsNeed = append(symbolsNeed, watch.Symbol)
		}
	}

	for _, symbol := range symbolsNeed {
		symbols, _ := this.subscribedSymbols.Load(string(category))
		if !exchange.SliceContains(symbols, symbol) {
			sub := fmt.Sprintf(
				`["tickers.%s"]`,
				symbol,
			)
			if err := this.subscribe(ws, sub); err != nil {
				this.Errorf("subscribe msg error: %s", err)
			} else {
				symbols = append(symbols, symbol)
				this.subscribedSymbols.Store(string(category), symbols)
			}
		}
	}
}

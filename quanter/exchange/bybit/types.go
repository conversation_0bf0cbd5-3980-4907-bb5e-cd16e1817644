package bybit

import (
	"math"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/quanter/exchange"
)

type AccountType string

const (
	AccountTypeUnified AccountType = "UNIFIED"
	AccountTypeFund    AccountType = "FUND"
)

type Bybit struct {
	exchange.BaseExchange
	instruments       *xsync.MapOf[string, *Instrument]
	wsConn            *websocket.Conn
	wsConnsPublic     *xsync.MapOf[string, *websocket.Conn]
	wsClosed          bool
	wsMutex           sync.Mutex
	subscribedSymbols *xsync.MapOf[string, []string]
	updatePriceMutex  sync.Mutex

	apiKey         *APISecret
	withdrawApiKey *APISecret
}

type Instrument struct {
	Symbol               string
	Status               string
	BaseCoin             string
	QuoteCoin            string
	DeliveryTime         int64
	MinLeverage          float64
	MaxLeverage          float64
	LeverageStep         float64
	TickSize             float64
	MaxOrderQty          float64
	MinOrderQty          float64
	QtyStep              float64
	MinNotionalValue     float64
	SettleCoin           string
	FundingRate          float64
	NextFundingTime      time.Time // 下次結算資金費用的時間 (毫秒)
	FundingIntervalHours int
	OpenInterest         float64
	MarkPrice            float64
	IndexPrice           float64
	tickerUpdateTime     int64
	instrumentType       exchange.InstrumentType
	updatedAt            int64
	riskLimits           *[]RiskLimit
}

type RiskLimit struct {
	RiskLimitValue    float64 `json:"riskLimitValue,string"`
	MaintenanceMargin float64 `json:"maintenanceMargin,string"`
	InitialMargin     float64 `json:"initialMargin,string"`
	MaxLeverage       float64 `json:"maxLeverage,string"`
}

func (i *Instrument) GetMMR(v float64) float64 {
	v = math.Abs(v)
	limits := *i.riskLimits

	for _, tier := range limits {
		if tier.RiskLimitValue > v {
			return tier.MaintenanceMargin
		}
	}
	return 0.004
}

func (i *Instrument) GetMaxLeverage(v float64) float64 {
	v = math.Abs(v)
	limits := *i.riskLimits

	for _, tier := range limits {
		if tier.RiskLimitValue > v {
			return tier.MaxLeverage
		}
	}
	return i.MaxLeverage
}

func (i Instrument) IsExpired() bool {
	return (i.updatedAt + 3600) < time.Now().Unix()
}

func (i Instrument) IsExpiredFutures() bool {
	if i.DeliveryTime == 0 {
		return false
	}

	t := time.UnixMilli(i.DeliveryTime / 1000)
	return t.Before(time.Now())
}

type MarginMode string

const MarginModeIsolated MarginMode = "ISOLATED_MARGIN"   //逐仓保证金
const MarginModeCross MarginMode = "REGULAR_MARGIN"       // 全仓保证金
const MarginModePortfolio MarginMode = "PORTFOLIO_MARGIN" // 组合保证金

type InstrumentCategory string

const InstrumentCategorySpot InstrumentCategory = "spot"
const InstrumentCategoryLinear InstrumentCategory = "linear"
const InstrumentCategoryInverse InstrumentCategory = "inverse"

func instrumentTypeToCategory(instrumentType exchange.InstrumentType) InstrumentCategory {
	switch instrumentType {
	case exchange.Spot:
		return InstrumentCategorySpot
	case exchange.USDXMarginedFutures:
		return InstrumentCategoryLinear
	case exchange.CoinMarginedFutures:
		return InstrumentCategoryInverse
	}
	return ""
}

func categoryToInstrumentType(category InstrumentCategory) exchange.InstrumentType {
	switch category {
	case InstrumentCategorySpot:
		return exchange.Spot
	case InstrumentCategoryLinear:
		return exchange.USDXMarginedFutures
	case InstrumentCategoryInverse:
		return exchange.CoinMarginedFutures
	}
	return exchange.UnknownInstrumentType
}

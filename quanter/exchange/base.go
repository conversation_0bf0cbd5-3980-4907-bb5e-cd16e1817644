package exchange

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	resty "github.com/go-resty/resty/v2"
	"github.com/puzpuzpuz/xsync/v2"
	"github.com/spf13/cast"
	"github.com/wizhodl/quanter/common/backscanner"
	"github.com/wizhodl/quanter/common/zlog"
)

type BaseExchange struct {
	Exchange

	Host string
	ID   string

	FixerKey              string
	IsTestnet             bool
	ProxyUrl              string
	MinLeverage           float64
	ControllerID          string
	DataPath              string
	OrderUpdatedCallbacks []func(*Order)            // 订单变动回调，注意数据可能不完整
	MarginUpdatedCallback func(*UserMargin, string) // 余额变动回调
	OrderBookCallback     func(orderBook *OrderBook)
	Client                *resty.Client // 请求 client

	SlackChannelName string

	Instruments *xsync.MapOf[string, *Instrument] // map[string]*exchange.Instrument

	SlackMessageCallback func(msg string, fileTitle string, fileContent []byte, fileType string)
	ConnectedCallback    func(connected bool)

	tickerCache             *xsync.MapOf[string, *Ticker]
	orderBook               *xsync.MapOf[string, *OrderBook]
	PriceTriggers           []*PriceTrigger
	PriceTriggeredCallbacks []func(*PriceTrigger)
	priceTriggersMutex      *sync.Mutex
	PriceWatches            []*PriceWatch
	priceWatchesMutex       *sync.Mutex
	EnableRealtimePrice     bool
	Removed                 *atomic.Bool
}

func (this *BaseExchange) OrderUpdatedCallback(order *Order) {
	for _, callback := range this.OrderUpdatedCallbacks {
		go callback(order)
	}
}

func (this *BaseExchange) RegisterOrderUpdatedCallback(callback func(order *Order)) {
	found := false
	for _, cb := range this.OrderUpdatedCallbacks {
		if fmt.Sprintf("%p", cb) == fmt.Sprintf("%p", callback) {
			found = true
			break
		}
	}
	if !found {
		this.OrderUpdatedCallbacks = append(this.OrderUpdatedCallbacks, callback)
	}
}

func (this *BaseExchange) PriceTriggeredCallback(trigger *PriceTrigger) {
	for _, callback := range this.PriceTriggeredCallbacks {
		trigger.ControllerID = this.ControllerID
		go callback(trigger)
	}
}

func (this *BaseExchange) RegisterPriceTriggeredCallback(callback func(trigger *PriceTrigger)) {
	found := false
	for _, cb := range this.PriceTriggeredCallbacks {
		if fmt.Sprintf("%p", cb) == fmt.Sprintf("%p", callback) {
			this.Debugf("price triggered callback already registered, %p", callback)
			found = true
			break
		}
	}
	if !found {
		this.PriceTriggeredCallbacks = append(this.PriceTriggeredCallbacks, callback)
	}
}

func NewBaseExchange(options *Options) *BaseExchange {
	ex := &BaseExchange{
		Host:                    options.Host,
		ID:                      options.ApiKey,
		FixerKey:                options.FixerKey,
		IsTestnet:               options.IsTestnet,
		MinLeverage:             options.MinLeverage,
		ControllerID:            options.ControllerID,
		MarginUpdatedCallback:   options.MarginUpdatedCallback,
		OrderUpdatedCallbacks:   []func(order *Order){options.OrderUpdatedCallback},
		OrderBookCallback:       options.OrderBookCallback,
		PriceTriggeredCallbacks: []func(*PriceTrigger){options.PriceTriggeredCallback},
		SlackChannelName:        options.GatewayChannelName,
		SlackMessageCallback:    options.SlackResponseCallback,
		Client:                  resty.New(),
		tickerCache:             xsync.NewMapOf[*Ticker](),
		orderBook:               xsync.NewMapOf[*OrderBook](),
		priceTriggersMutex:      &sync.Mutex{},
		priceWatchesMutex:       &sync.Mutex{},
		Instruments:             xsync.NewMapOf[*Instrument](),
		DataPath:                options.DataPath,

		Removed: &atomic.Bool{},
	}
	return ex
}

func (this *BaseExchange) GetID() string {
	return this.ID
}

func (this *BaseExchange) GetLoggerID() string {
	return this.ControllerID
}

func (this *BaseExchange) SetHttpDebug(debug bool) {
	if this.Client != nil {
		disableHttpDebug := os.Getenv("QUANTER_HTTP_DEBUG_DISABLE")
		if disableHttpDebug == "" {
			this.Client.SetDebug(debug)
		}
	}
}

// code -> spotSymbol, futureSymbol
func (this *BaseExchange) FixSymbolCode(fixes map[string][3]string, symbolCode *SymbolCode) (spotSymbol string, spotCode *SymbolCode, futureSymbol string, fixed bool) {
	if v, ok := fixes[symbolCode.Code]; ok {
		if v[1] != "" {
			var err error
			spotCode, err = NewSymbolCode(v[1], symbolCode.USDXSymbol)
			if err != nil {
				zlog.Errorf("fix symbol code failed, code: %s, error: %s", symbolCode.Code, err)
				return "", nil, "", false
			}
		} else {
			// zlog.Errorf("fix symbol code failed, code: %s, error: no spot code in fixes", symbolCode.Code)
			// return "", nil, "", false
			return v[0], nil, v[2], true
		}
		return v[0], spotCode, v[2], true
	}
	return "", nil, "", false
}

// futureSymbol -> spotSymbol, futureCode
func (this *BaseExchange) FixFutureSymbol(fixes map[string][3]string, instrumentType InstrumentType, futureSymbol string, uSymbol string) (spotSymbol string, spotCode *SymbolCode, futureCode *SymbolCode, fixed bool) {
	if v, ok := fixes[futureSymbol]; ok {
		futureCode, _ := NewSymbolCode(v[2], uSymbol)
		if instrumentType == UnknownInstrumentType || futureCode.InstrumentType() == instrumentType {
			var err error
			if v[1] != "" {
				spotCode, err = NewSymbolCode(v[1], uSymbol)
				if err != nil {
					zlog.Errorf("fix future symbol failed, symbol: %s, error: %s", futureSymbol, err)
					return "", nil, nil, false
				}
			}
			return v[0], spotCode, futureCode, true
		}
	}
	return "", nil, nil, false
}

func (this *BaseExchange) SendWebsocketMessage(packet *ClientPacket, callback func(response *Packet, er error)) {
	if callback != nil {
		callback(nil, ErrNotImplemented)
	}
}

func (this *BaseExchange) Subscribe(instrumentType InstrumentType, symbol string) {
}

func (this *BaseExchange) UnSubscribe(instrumentType InstrumentType, symbol string) {
}

// 是否是反向合约
// 主要区别在于：正向合约每单位的合约数量相同，反向合约每单位的头寸（即价值）相同
func (this *BaseExchange) IsReverseSymbol(instrumentType InstrumentType, symbol string) bool {
	// 默认都是正向合约
	return false
}

func (this *BaseExchange) FormatDisplayQty(instrumentType InstrumentType, symbol string, qty float64) string {
	return strconv.FormatFloat(qty, 'f', -1, 64)
}

func (this *BaseExchange) TransferAsset(from, to InstrumentType, coin string, amount float64) error {
	return nil
}

func (this *BaseExchange) GetNextSymbolCodes(instrumentType InstrumentType, futureCode *SymbolCode, count int) (symbolCodes []*SymbolCode, er error) {
	return
}

func (this *BaseExchange) TranslateSpotSymbol(symbol, uSymbol string) (code *SymbolCode, er error) {
	ins, err := this.GetInstrument(Spot, symbol)
	if err != nil {
		er = fmt.Errorf("get instrument failed, error: %s", err)
	}
	code, err = NewSymbolCode(fmt.Sprintf("%s--", ins.UnderlyCurrency), uSymbol)
	if err != nil {
		er = fmt.Errorf("new symbol code failed, error: %s", err)
	}
	return
}

// 此处对于需要各 exchange 实现的方法 TranslateSymbolCode，在 BaseExchange 不要实现，通过初始化交易所时 ex.Exchange = ex 来使用各交易所方法
// 如果 TranslateSymbolCode 在 BaseExchange 也实现，则在此处仍会调用 BaseExchange 的实现
func (this *BaseExchange) TranslateSymbolCodeToSpotSymbol(symbolCode *SymbolCode) (spotSymbol string, er error) {
	if pairs, err := this.TranslateSymbolCode(symbolCode); len(pairs) < 1 {
		er = fmt.Errorf("failed to translate symbol code %s, error: %s", symbolCode, err)
		return
	} else {
		spotSymbol = pairs[0].Left.Symbol
		if spotSymbol == "" {
			er = fmt.Errorf("failed to translate symbol code %s, error: spot symbol is empty", symbolCode)
			return
		}
		return
	}
}

func (this *BaseExchange) TranslateSymbolCodeToFutureSymbol(symbolCode *SymbolCode) (futureSymbol string, er error) {
	if pairs, err := this.TranslateSymbolCode(symbolCode); len(pairs) < 1 {
		er = fmt.Errorf("failed to translate symbol code %s, error: %s", symbolCode, err)
		return
	} else {
		futureSymbol = pairs[0].Right.Symbol
		if futureSymbol == "" {
			er = fmt.Errorf("failed to translate symbol code %s, error: future symbol is empty", symbolCode)
			return
		}
		return
	}
}

func (this *BaseExchange) GetSymbolCodeQuote(symbolCode *SymbolCode) string {
	instrumentType := symbolCode.InstrumentType()
	if instrumentType == CoinMarginedFutures {
		return "USD"
	} else if instrumentType == USDXMarginedFutures {
		if symbolCode.USDXSymbol != "" {
			return symbolCode.USDXSymbol
		}
		return "USDT"
	} else if instrumentType == Spot {
		_, _, month, _, _ := parseSymbolCode(symbolCode.Code)
		if month != "--" && strings.HasPrefix(month, "-") {
			return month[1:]
		} else if symbolCode.USDXSymbol != "" {
			return symbolCode.USDXSymbol
		}
		return "USDT"
	}
	return "USDT"
}

func (this *BaseExchange) GetSymbolCodeUnit(symbolCode *SymbolCode) string {
	if symbolCode.InstrumentType() == Spot {
		return symbolCode.Coin()
	} else {
		// 默认期货合约单位为 cnt 代表合约数量，交易所可根据实际情况重写
		return "cnt"
	}
}

func (this *BaseExchange) GetPosition(instrumentType InstrumentType, symbol string, side PositionSide, allowCache bool) (*Position, error) {
	position := &Position{
		InstrumentType: instrumentType,
		ExchangeName:   this.GetName(),
		Symbol:         symbol,
	}

	// 获取当前合约的持仓
	positions, err := this.GetPositions(instrumentType, symbol, allowCache)
	if err != nil {
		return nil, fmt.Errorf("get position from exchange error: %s", err)
	}

	for _, pos := range positions {
		if pos.Qty == 0 {
			continue
		}
		if side == UnknownPositionSide || side == pos.Side {
			position = pos
			break
		}
	}
	return position, nil
}

// 获取所有币种余额、合约持仓
func (this *BaseExchange) GetAccountHoldings(instrumentTypes []InstrumentType) (holdings HoldingList, er error) {
	holdings = HoldingList{}

	isSeparateAccount := false
	// 账号余额独立的交易所所有类型需要单独获取，其他交易所统一账号仅需获取现货余额
	if this.GetName() == Binance || this.GetName() == Hyperliquid {
		isSeparateAccount = true
	}

	for _, instrumentType := range instrumentTypes {
		if instrumentType == Spot || isSeparateAccount {
			if balances, err := this.GetAccountBalances(instrumentType); err != nil {
				er = fmt.Errorf("list spot positions error: %s", err)
				return
			} else {
				//  完全重置 balances 数据，否则如果仓位里没有，而 balances 已经存在的情况下，数量无法重置
				for _, b := range balances {
					holdings = append(holdings, &Holding{instrumentType, b.Currency, b.Total, b.Available})
				}
			}
		}

		if !instrumentType.IsFuture() {
			continue
		}

		if futurePositions, err := this.GetPositions(instrumentType, "", true); err != nil {
			er = fmt.Errorf("list future positions for (%s) failed, error: %s", instrumentType, err)
		} else {
			for _, fp := range futurePositions {
				// TODO: 看 future 的 available qty 怎么处理
				holdings = append(holdings, &Holding{instrumentType, fp.Symbol, fp.Qty, fp.Qty})
			}
		}
	}
	return
}

// 获取某个币种的现货数量，某个期货的持仓数量
func (this *BaseExchange) GetHoldingQty(instrumentType InstrumentType, coinOrSymbol string) (total, available float64, er error) {
	if holdings, err := this.GetAccountHoldings([]InstrumentType{instrumentType}); err != nil {
		er = err
		return
	} else {
		for _, h := range holdings {
			if h.InstrumentType == instrumentType && h.CoinOrSymbol == coinOrSymbol {
				total += h.Total
				available += h.Available
			}
		}
	}
	return
}

// 获取某个币种的现货数量，某个期货的保证金数量
func (this *BaseExchange) GetBalance(instrumentType InstrumentType, coin string) (total, available float64, er error) {
	if instrumentType == Spot {
		return this.GetHoldingQty(instrumentType, coin)
	} else if instrumentType.IsFuture() {
		if balances, err := this.GetAccountBalances(instrumentType); err != nil {
			er = err
		} else {
			found := false
			for _, c := range balances {
				if strings.EqualFold(c.Currency, coin) {
					total = c.Total
					available = c.Available
					found = true
				}
			}
			if !found {
				er = fmt.Errorf("balance of code %s not found in future account, the balance may be 0", coin)
			}
		}
	}
	return
}

func (this *BaseExchange) getFundingHistoryCachePath(instrumentType InstrumentType, symbol string) string {
	dir := filepath.Join(this.DataPath, "funding_history")
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		os.MkdirAll(dir, 0755)
	}
	return filepath.Join(dir, fmt.Sprintf("%s_%s_%s.funding_history", this.GetName(), instrumentType, symbol))
}

func (this *BaseExchange) saveFundingHistory(path string, his *FundingHistory) error {
	json, err := json.Marshal(his)
	if err != nil {
		return err
	}
	// create file if not exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		os.Create(path)
	}
	f, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = f.WriteString(fmt.Sprintf("%s\n", string(json)))
	if err != nil {
		return err
	}
	return nil
}

var queryFundingHistoryMutexs = NewSyncMapOf[string, sync.Mutex]()

func (this *BaseExchange) QueryFundingHistoryWithCache(instrumentType InstrumentType, symbol string, limit int, startTime, endTime time.Time) (fundings []*FundingHistory, er error) {
	cachePath := this.getFundingHistoryCachePath(instrumentType, symbol)
	mutex, _ := queryFundingHistoryMutexs.LoadOrStore(cachePath, sync.Mutex{})
	mutex.Lock()
	defer mutex.Unlock()

	if endTime.IsZero() {
		endTime = time.Now()
	}

	fundings = []*FundingHistory{}

	count := 0

	backscanner.BackScan(cachePath, func(line []byte) bool {
		his := &FundingHistory{}
		err := json.Unmarshal(line, his)
		if err == nil {
			fundings = append(fundings, his)
			count++
			if count >= limit {
				return false
			}
		}
		return true
	})

	if len(fundings) > 0 {
		if fundings[0].Time.After(endTime.Add(-1 * time.Hour)) {
			// 如果第一条数据的时间在 endTime 1 小时之内，使用缓存
			if count >= limit {
				return
			}
		} else {
			startTime = fundings[0].Time.Add(-1 * time.Hour)
		}
	}

	if his, err := this.QueryFundingHistory(instrumentType, symbol, limit, startTime, endTime); err != nil {
		er = err
		return
	} else {
		if len(fundings) > 0 {
			lastFundingTime := fundings[0].Time
			for _, h := range his {
				if h.Time.After(lastFundingTime) {
					fundings = append([]*FundingHistory{h}, fundings...)
					if err := this.saveFundingHistory(cachePath, h); err != nil {
						this.Errorf("save funding history failed, error: %s", err)
					}
				}
			}
		} else {
			fundings = his
			for _, h := range his {
				if err := this.saveFundingHistory(cachePath, h); err != nil {
					this.Errorf("save funding history failed, error: %s", err)
				}
			}
		}
	}
	return
}

func (this *BaseExchange) GetFundingHistory(futureCode *SymbolCode, latestOnly bool, isDaily bool) (fundingHis []FundingHistory, er error) {
	fundingHis = []FundingHistory{}
	symbol := ""
	if symbolPairs, err := this.TranslateSymbolCode(futureCode); len(symbolPairs) == 1 {
		symbol = symbolPairs[0].Right.Symbol
	} else if err != nil {
		er = fmt.Errorf("translate %s err: %s", futureCode, err)
		return
	} else if len(symbolPairs) > 1 {
		er = fmt.Errorf("translate %s result more than 1", futureCode)
		return
	}

	if !futureCode.IsPerp() {
		er = errors.New("only support funding history for perp futures")
		return
	}

	dates := []time.Time{}
	dailyHisMap := map[time.Time][]FundingHistory{}
	dailyHis := []FundingHistory{}
	limit := 1000
	if latestOnly {
		if isDaily {
			limit = 24
		} else {
			limit = 1
		}
	}
	if his, err := this.QueryFundingHistoryWithCache(futureCode.InstrumentType(), symbol, limit, time.Time{}, time.Time{}); err != nil {
		er = err
		return
	} else {
		for _, item := range his {
			fundingTime := item.Time
			year, month, day := fundingTime.Date()
			fundingDate := time.Date(year, month, day, 0, 0, 0, 0, time.UTC)
			h := FundingHistory{
				Code:   futureCode,
				Symbol: symbol,
				Rate:   item.Rate,
				Time:   fundingTime,
				Period: item.Period,
			}
			fundingHis = append(fundingHis, h) // 如果不是 daily，这里就加入进去了
			// populate dates
			dateFound := false
			for _, d := range dates {
				if d == fundingDate {
					dateFound = true
				}
			}
			if !dateFound {
				dates = append(dates, fundingDate)
			}
			dailyHisMap[fundingDate] = append(dailyHisMap[fundingDate], FundingHistory{
				Code:   futureCode,
				Symbol: symbol,
				Rate:   item.Rate,
				Time:   fundingDate,
				Period: item.Period,
			})
		}
	}

	if isDaily {
		if len(dates) == 0 {
			fundingHis = []FundingHistory{}
			return
		}
		// 按日期倒序排列
		sort.Slice(dates, func(i, j int) bool {
			return dates[i].Sub(dates[j]) > 0
		})
		todayFound := false
		lastDay := dates[len(dates)-1] // 前一日，因为是倒序的，先找到今天的日期，然后将下一个日期设为“前一日”
		for _, d := range dates {
			if todayFound {
				lastDay = d
				todayFound = false
			}
			dailyRate := 0.0
			if hs, found := dailyHisMap[d]; found {
				for _, h := range hs {
					dailyRate += h.Rate
				}
			}
			dailyHis = append(dailyHis, FundingHistory{
				Code:   futureCode,
				Symbol: symbol,
				Rate:   dailyRate,
				Time:   d,
				Period: "1d",
			})
			nowYear, nowMonth, nowDay := time.Now().Date()
			if d == time.Date(nowYear, nowMonth, nowDay, 0, 0, 0, 0, time.UTC) {
				todayFound = true
			}
		}
		// 仅显示最新
		if latestOnly {
			for _, dh := range dailyHis {
				if dh.Time == lastDay {
					fundingHis = []FundingHistory{dh}
					break
				}
			}
		} else {
			fundingHis = dailyHis
		}
		return
	} else {
		// 倒序排列
		sort.Slice(fundingHis, func(i, j int) bool {
			return fundingHis[i].Time.Sub(fundingHis[j].Time) > 0
		})
		return
	}

}

var klineCaches = xsync.NewMapOf[KLines]()

// Klines 按 KLine.Time 从小到大排序的
func (this *BaseExchange) GetLatestKLinesWithCache(instrumentType InstrumentType, symbol string, periodHour, num int, reload bool) ([]*KLine, error) {
	if strings.EqualFold(this.GetName(), CTP) {
		return this.GetLatestKLines(instrumentType, symbol, periodHour, num)
	}
	cacheKey := fmt.Sprintf("%s|%s|%d|%d", this.GetName(), symbol, periodHour, num)
	exitingLines := KLines{}
	if !reload {
		if klines, found := klineCaches.Load(cacheKey); found {
			exitingLines = klines
		}
	}
	if len(exitingLines) > 0 {
		num = 3
	}
	if newKlines, err := this.GetLatestKLines(instrumentType, symbol, periodHour, num); err != nil {
		return []*KLine{}, fmt.Errorf("(%s) get latest klines peroid hour: %d, num more: %d, error: %s", symbol, periodHour, num, err)
	} else {
		klines := exitingLines.Update(newKlines)
		// 时间从早到晚排序
		sort.SliceStable(klines, func(i, j int) bool {
			return klines[i].Time < klines[j].Time
		})
		klineCaches.Store(cacheKey, klines)
		return klines, nil
	}
}

var cachedAccountWorth = xsync.NewMapOf[*Float64CacheItem]()
var accountWorthLocks = xsync.NewMapOf[*sync.Mutex]() // key: cacheKey/string, value: *sync.Mutex

// 获取账号所有配置中支持的币种余额 USDT 价值
func (this *BaseExchange) GetAccountWorth(controllerRefID string, instrumentTypes []InstrumentType, cacheTime time.Duration, symbolFilterFunc func(symbol string) bool) (worthInUSDT float64, er error) {
	types := ""
	for _, t := range instrumentTypes {
		types += string(t) + ","
	}
	cacheKey := fmt.Sprintf("%s||%s||%s||%s", this.GetName(), this.GetID(), controllerRefID, types)
	// 开销很高，防止并发计算
	lock, _ := accountWorthLocks.LoadOrStore(cacheKey, &sync.Mutex{})
	lock.Lock()
	defer lock.Unlock()

	useCache := false
	if cacheTime > 0*time.Second {
		useCache = true
	}

	if useCache {
		if item, found := cachedAccountWorth.Load(cacheKey); found {
			since := time.Since(item.CacheTime)
			timeDiff := since - cacheTime
			if timeDiff < 0 {
				this.Debugf("get account worth from cache (%s), value: %f, cache time: %s", cacheKey, worthInUSDT, item.CacheTime)
				worthInUSDT = item.Value
				return
			}
		}
	}

	worthInUSDT = 0.0

	if balances, err := this.GetAccountBalancesValidCoins(controllerRefID, instrumentTypes, symbolFilterFunc); err != nil {
		er = fmt.Errorf("get account balances error: %s", err)
	} else {
		assetCount := 0
		assetMap := balances.GetTotalCombined()
		for coin, b := range assetMap {
			qty := b.Total
			if len(assetMap) > 5 && assetCount > 0 && assetCount%5 == 0 {
				this.Debugf("get account worth, iterating asset map, sleep 1 seconds for every 5 assets")
				time.Sleep(1 * time.Second)
			}
			if strings.EqualFold(coin, "USDT") {
				worthInUSDT += qty
			} else {
				// 仅获取允许币种的余额
				if symbolFilterFunc == nil || symbolFilterFunc(coin) {
					price, err := this.GetCurrencyPrice(coin, 2*time.Second)
					if err != nil {
						// 如果是频率限制，稍等 2s 再试一次
						if strings.Contains(err.Error(), "response status: 429") {
							time.Sleep(time.Second * 2)
							price, err = this.GetCurrencyPrice(coin, 2*time.Second)
						}
						if err != nil {
							er = fmt.Errorf("get account worth, error: %s", err)
							return
						}
					}
					worthInUSDT += qty * price
				} else {
					this.Debugf("get account worth, coin not valid (%s), ignored", coin)
				}
			}
			assetCount += 1
		}
	}

	if useCache {
		nowTime := time.Now()
		if item, found := cachedAccountWorth.Load(cacheKey); found {
			item.Value = worthInUSDT
			item.CacheTime = nowTime
			this.Debugf("get account worth, update cache (%s), value: %f, cache time: %s", cacheKey, item.Value, item.CacheTime)
		} else {
			item := &Float64CacheItem{Value: worthInUSDT, CacheTime: nowTime}
			cachedAccountWorth.Store(cacheKey, item)
			this.Debugf("get account worth, write new cache (%s), value: %f, cache time: %s", cacheKey, item.Value, item.CacheTime)
		}
	}
	return
}

type AccountConfigCacheItem struct {
	Value     *AccountConfig
	CacheTime time.Time
}

var cachedAccountConfig = xsync.NewMapOf[*AccountConfigCacheItem]()

func (this *BaseExchange) GetAccountConfigWithCache(instrumentType InstrumentType, cacheTime time.Duration) (cfg *AccountConfig, er error) {
	cacheKey := fmt.Sprintf("%s||%s||%s", this.GetName(), this.GetID(), instrumentType)
	useCache := false
	if cacheTime > time.Second*0 {
		useCache = true
	}
	if useCache {
		if item, found := cachedAccountConfig.Load(cacheKey); found {
			since := time.Since(item.CacheTime)
			timeDiff := since - cacheTime
			if timeDiff < 0 {
				cfg = item.Value
				this.Debugf("get account config from cache (%s), value: %v, cache time: %s", cacheKey, cfg, item.CacheTime)
				return
			}
		}
	}

	if c, err := this.GetAccountConfig(instrumentType); err != nil {
		this.Errorf("get account config error: %v", err)
		er = err
		return
	} else {
		cfg = c
	}

	if useCache {
		nowTime := time.Now()
		if item, found := cachedAccountConfig.Load(cacheKey); found {
			item.Value = cfg
			item.CacheTime = nowTime
			this.Debugf("get account config, update cache (%s), value: %v, cache time: %s", cacheKey, item.Value, item.CacheTime)
		} else {
			item := &AccountConfigCacheItem{Value: cfg, CacheTime: nowTime}
			cachedAccountConfig.Store(cacheKey, item)
			this.Debugf("get account config, write new cache (%s), value: %v, cache time: %s", cacheKey, item.Value, item.CacheTime)
		}
	}
	return
}

var accountAssetsLocks = xsync.NewMapOf[*sync.Mutex]() // key: lockKey/string, value: *sync.Mutex

// 获取账号所有配置中支持的币种余额 USDT 价值
func (this *BaseExchange) GetAccountBalancesValidCoins(controllerRefID string, instrumentTypes []InstrumentType, symbolFilterFunc func(string) bool) (balances AccountBalanceList, er error) {
	balances = AccountBalanceList{}
	lockKey := fmt.Sprintf("%s||%s||%s", this.GetName(), this.GetID(), controllerRefID)
	// 开销很高，防止并发计算
	lock, _ := accountAssetsLocks.LoadOrStore(lockKey, &sync.Mutex{})
	lock.Lock()
	defer lock.Unlock()

	// OKEx 仅支持统一保证金模式，使用不同 InstrumentType 获取的余额都相同，因此只能获取一次 Spot 余额，否则会重复加
	// 只是为了获取 AccountBalanceCross 的值，统一传 USDXMarginedFutures 就可以，但是如果 USDXMarginedFutures 没实现，就可能有 bug
	if cfg, err := this.GetAccountConfigWithCache(USDXMarginedFutures, 10*time.Second); err != nil {
		this.Errorf("get account balance, get account config error: %s", err)
	} else {
		if cfg.AccountBalanceCross {
			instrumentTypes = []InstrumentType{Spot}
		}
	}
	for _, instrumentType := range instrumentTypes {
		if bs, err := this.GetAccountBalances(instrumentType); err != nil {
			if !errors.Is(err, ErrNotAvailableForInstrumentType) {
				er = fmt.Errorf("get account balance failed, error: %s", err)
				return
			} else {
				this.Debugf("get account balance is not available for instrument type (%s), ignored", instrumentType)
			}
		} else {
			for _, this := range bs {
				if this.Total == 0 {
					continue
				}
				// 仅获取允许币种的余额
				if symbolFilterFunc == nil || symbolFilterFunc(this.Currency) {
					balances = append(balances, this)
				}
			}
		}
	}
	return
}

var cachedSpotPrices = xsync.NewMapOf[*Float64CacheItem]()

func (this *BaseExchange) GetSpotPrice(spotSymbol string, cacheTime time.Duration) (price float64, er error) {
	cacheKey := fmt.Sprintf("%s||%s||%s", this.GetName(), this.GetID(), spotSymbol)
	useCache := false
	if cacheTime > time.Second*0 {
		useCache = true
	}
	if useCache {
		if item, found := cachedSpotPrices.Load(cacheKey); found {
			since := time.Since(item.CacheTime)
			timeDiff := since - cacheTime
			if timeDiff < 0 {
				price = item.Value
				this.Debugf("get spot price from cache (%s), value: %f, cache time: %s", cacheKey, price, item.CacheTime)
				return
			}
		}
	} else {
		price = 0
	}

	instrumentType := Spot
	// BitMEX 没有 Spot，使用 USDXMarginedFutures 合约价格代替
	if this.GetName() == BitMEX {
		instrumentType = USDXMarginedFutures
	}
	if p, err := this.GetLastPrice(instrumentType, spotSymbol, false); err != nil {
		this.Errorf("get spot price for (%s) err: %v", spotSymbol, err)
		er = err
	} else {
		price = p
	}

	if useCache {
		nowTime := time.Now()
		if item, found := cachedSpotPrices.Load(cacheKey); found {
			item.Value = price
			item.CacheTime = nowTime
			this.Debugf("get spot price, update cache (%s), value: %f, cache time: %s", cacheKey, item.Value, item.CacheTime)
		} else {
			item := &Float64CacheItem{Value: price, CacheTime: nowTime}
			cachedSpotPrices.Store(cacheKey, item)
			this.Debugf("get spot price, write new cache (%s), value: %f, cache time: %s", cacheKey, item.Value, item.CacheTime)
		}
	}
	return price, er
}

// 获取币种的 USD* 价格，BitMEx 没有现货，用 USDT 合约价格代替
func (this *BaseExchange) GetCurrencyPrice(currency string, cacheTime time.Duration) (priceInUSDT float64, er error) {
	if SliceContains([]string{"HKD", "CNY"}, currency) {
		return GetFixerExchangeRate(this.FixerKey, currency, "USD")
	}

	if SliceContains([]string{"USD", "USDT", "USDC"}, currency) {
		return 1, nil
	}
	if spotSymbolCode, err := NewSymbolCode(fmt.Sprintf("%s--", currency), "USDT"); err != nil {
		return 0, fmt.Errorf("get currency price, new symbol code for (%s), error: %s", currency, err)
	} else {
		if spotSymbol, err := this.TranslateSymbolCodeToSpotSymbol(spotSymbolCode); err != nil {
			this.Errorf("get currency price, tranlsate symbol code to spot symbol error: %s", err)
			return 0, nil
		} else {
			if price, err := this.GetSpotPrice(spotSymbol, cacheTime); err != nil {
				return 0, fmt.Errorf("get currency %s price, get spot price error: %s", currency, err)
			} else {
				return price, nil
			}
		}
	}
}

func (this *BaseExchange) RoundPrice(instrumentType InstrumentType, symbol string, price float64) float64 {
	if instrument, _ := this.GetInstrument(instrumentType, symbol); instrument != nil {
		return instrument.RoundPrice(price)
	}
	return price
}

func (this *BaseExchange) FloorPrice(instrumentType InstrumentType, symbol string, price float64) float64 {
	if instrument, _ := this.GetInstrument(instrumentType, symbol); instrument != nil {
		return instrument.FloorPrice(price)
	}
	return price
}

func (this *BaseExchange) RoundQty(instrumentType InstrumentType, symbol string, qty float64) float64 {
	if instrument, _ := this.GetInstrument(instrumentType, symbol); instrument != nil {
		return instrument.RoundQty(qty)
	}
	return qty
}

func (this *BaseExchange) FloorQty(instrumentType InstrumentType, symbol string, qty float64) float64 {
	if instrument, _ := this.GetInstrument(instrumentType, symbol); instrument != nil {
		return instrument.FloorQty(qty)
	}
	return qty
}

func (this *BaseExchange) FormatPrice(instrumentType InstrumentType, symbol string, price float64) string {
	places := 2
	if instrument, _ := this.GetInstrument(instrumentType, symbol); instrument != nil {
		tickSize := instrument.TickSize
		_places := DecimalBit(tickSize)
		if _places > -1 {
			places = _places
		}
		price = math.Round(price/tickSize) * tickSize
	}
	return fmt.Sprintf("%0."+cast.ToString(places)+"f", price)
}

func (this *BaseExchange) FormatQty(instrumentType InstrumentType, symbol string, qty float64) string {
	places := 2
	if instrument, _ := this.GetInstrument(instrumentType, symbol); instrument != nil {
		lotSize := instrument.LotSize
		_places := DecimalBit(lotSize)
		if _places > -1 {
			places = _places
		}
		qty = math.Round(qty/lotSize) * lotSize
	}
	return fmt.Sprintf("%0."+cast.ToString(places)+"f", qty)
}

func (this *BaseExchange) GetOrderByOrig(origOrder Order) (order *Order, err error) {
	if origOrder.InstrumentType == UnknownInstrumentType {
		return nil, errors.New("missing order InstrumentType")
	}
	if origOrder.Type == UnknownOrderType {
		return nil, errors.New("missing order Type")
	}
	if origOrder.Symbol == "" {
		return nil, errors.New("missing order Symbol")
	}
	if origOrder.OrderID == "" {
		return nil, errors.New("missing order OrderID")
	}

	order, err = this.GetOrder(origOrder.InstrumentType, origOrder.Type, origOrder.Symbol, origOrder.OrderID, nil)
	if order != nil {
		order.SetExtStruct(&origOrder)
	}
	return order, err
}

// 在使用钱包币种信息检查 coins 是否合法，有网络请求，开销很高
func (this *BaseExchange) CheckValidCoins(coins []string) (validCoins []string, invalidCoins []string, er error) {
	validCoins = []string{}
	invalidCoins = []string{}
	if currencies, err := this.GetAccountCurrencies(Spot); err != nil {
		er = err
	} else {
		for _, coin := range coins {
			found := false
			coin = strings.ToUpper(coin)
			if strings.EqualFold(coin, "*") {
				found = true
			} else {
				for _, c := range currencies {
					if strings.EqualFold(c, coin) {
						found = true
					}
				}
			}
			if found {
				validCoins = append(validCoins, coin)
			} else {
				invalidCoins = append(invalidCoins, coin)
			}
		}
	}
	return
}

func (this *BaseExchange) RegisterPriceTrigger(priceTrigger *PriceTrigger) (err error) {
	if lastPrice, err := this.GetLastPrice(priceTrigger.InstrumentType, priceTrigger.Symbol, false); err != nil {
		this.Errorf("get last price for (%s) err: %v", priceTrigger.Symbol, err)
		return err
	} else {
		if priceTrigger.Direction == TriggerDirectionHigher && lastPrice >= priceTrigger.Price {
			return fmt.Errorf("last price %v is higher than price %v", lastPrice, priceTrigger.Price)
		}
		if priceTrigger.Direction == TriggerDirectionLower && lastPrice <= priceTrigger.Price {
			return fmt.Errorf("last price %v is lower than price %v", lastPrice, priceTrigger.Price)
		}
		if priceTrigger.Direction == "" {
			if lastPrice >= priceTrigger.Price {
				priceTrigger.Direction = TriggerDirectionLower
			} else {
				priceTrigger.Direction = TriggerDirectionHigher
			}
		}
	}

	this.priceTriggersMutex.Lock()
	defer this.priceTriggersMutex.Unlock()

	for i, p := range this.PriceTriggers {
		if p.ID == priceTrigger.ID {
			if p.Price != priceTrigger.Price {
				this.Infof("update priceTrigger: %#v", priceTrigger)
				this.PriceTriggers[i] = priceTrigger // 修改价格
			}
			return nil
		}
	}

	this.Infof("add priceTrigger: %#v", priceTrigger)
	this.PriceTriggers = append(this.PriceTriggers, priceTrigger)
	return nil
}

func (this *BaseExchange) DeletePriceTrigger(id string) error {
	this.priceTriggersMutex.Lock()
	defer this.priceTriggersMutex.Unlock()
	for i, priceTrigger := range this.PriceTriggers {
		if priceTrigger.ID == id {
			this.PriceTriggers = append(this.PriceTriggers[:i], this.PriceTriggers[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("price trigger %s not found", id)
}

func (this *BaseExchange) GetPriceTriggers() []*PriceTrigger {
	this.priceTriggersMutex.Lock()
	defer this.priceTriggersMutex.Unlock()
	return this.PriceTriggers
}

func (this *BaseExchange) AddPriceWatch(watch *PriceWatch) (err error) {
	this.priceWatchesMutex.Lock()
	defer this.priceWatchesMutex.Unlock()

	for _, p := range this.PriceWatches {
		if p.Key() == watch.Key() {
			return nil
		}
	}

	this.Infof("add price watch: %#v", watch)
	this.PriceWatches = append(this.PriceWatches, watch)
	return nil
}

func (this *BaseExchange) DeletePriceWatch(symbolCode *SymbolCode) error {
	this.priceWatchesMutex.Lock()
	defer this.priceWatchesMutex.Unlock()

	newPriceWatches := make([]*PriceWatch, 0, len(this.PriceWatches))
	for _, w := range this.PriceWatches {
		if w.SymbolCode.Code != symbolCode.Code {
			newPriceWatches = append(newPriceWatches, w)
		}
	}
	this.PriceWatches = newPriceWatches
	return nil
}

func (this *BaseExchange) GetPriceWatches() []*PriceWatch {
	this.priceWatchesMutex.Lock()
	defer this.priceWatchesMutex.Unlock()
	return this.PriceWatches
}

func (this *BaseExchange) CheckPriceTriggerTimeLoop() {
	t := time.NewTicker(time.Second * 30)
	for {
		if this.Removed.Load() {
			break
		}

		select {
		case <-t.C:
			for _, p := range this.PriceTriggers {
				if p.Triggered {
					continue
				}
				// 如果超过 10s 没有检查，说明 ws 可能出问题，用 http 请求价格检查一次
				if p.LastCheckTime == nil || p.LastCheckTime.Add(time.Second*10).Before(time.Now()) {
					this.Warnf("price trigger %s last check time too old: %v", p.ID, p.LastCheckTime)
					if lastPrice, err := this.GetLastPrice(p.InstrumentType, p.Symbol, false); err != nil {
						this.Errorf("get price for (%s) err: %v", p.Symbol, err)
					} else {
						this.CheckPriceTrigger(p.InstrumentType, p.Symbol, lastPrice, time.Now())
					}
				}
			}
		}
	}
}

func (this *BaseExchange) CheckPriceTrigger(instrumentType InstrumentType, symbol string, lastPrice float64, priceTime time.Time) {
	now := time.Now()
	if priceTime.Add(time.Second * 30).Before(now) {
		// 超过 30s 价格无效
		return
	}

	if lastPrice == 0 {
		this.Errorf("check %s price trigger with price 0", symbol)
		return
	}

	triggeredIDs := []string{}
	for _, p := range this.PriceTriggers {
		if p.Triggered {
			continue
		}
		if p.InstrumentType != instrumentType || p.Symbol != symbol {
			continue
		}
		if lastPrice >= p.Price && p.Direction == TriggerDirectionHigher {
			p.Triggered = true
			triggeredIDs = append(triggeredIDs, p.ID)
		}
		if lastPrice <= p.Price && p.Direction == TriggerDirectionLower {
			p.Triggered = true
			triggeredIDs = append(triggeredIDs, p.ID)
		}
		p.LastCheckTime = &now
		if p.Triggered {
			p.TriggeredPrice = lastPrice
			this.Infof("price triggered: %v", p)
			this.PriceTriggeredCallback(p)
		}
	}

	for _, id := range triggeredIDs {
		this.DeletePriceTrigger(id)
	}
}

func getSymbolCacheKey(instrumentType InstrumentType, symbol string) string {
	return fmt.Sprintf("%s_%s", instrumentType, symbol)
}

func (this *BaseExchange) GetAllTickers() []*Ticker {
	ts := []*Ticker{}
	this.tickerCache.Range(func(_ string, t *Ticker) bool {
		ts = append(ts, t)
		return true
	})
	return ts
}

func (this *BaseExchange) GetTicker(instrumentType InstrumentType, symbol string) (*Ticker, bool) {
	return this.tickerCache.Load(getSymbolCacheKey(instrumentType, symbol))
}

func (this *BaseExchange) StoreTickerCache(ticker *Ticker) {
	this.tickerCache.Store(getSymbolCacheKey(ticker.InstrumentType, ticker.Symbol), ticker)
}

func (this *BaseExchange) GetTickerCachePrice(instrumentType InstrumentType, symbol string) float64 {
	ticker, _ := this.GetTicker(instrumentType, symbol)
	if ticker != nil && ticker.Close > 0 && (int64(ticker.Time/1000)+2) > time.Now().Unix() {
		// 2s 内的 ticker 数据可直接返回
		return ticker.Close
	}
	return 0
}

func (this *BaseExchange) SetEnableRealtimePrice(enable bool) {
	this.EnableRealtimePrice = enable
}

func (this *BaseExchange) Remove() {
	this.Removed.Store(true)
	this.CloseWebsocket(true)
}

func (this *BaseExchange) Infof(format string, args ...interface{}) {
	zlog.Infof(fmt.Sprintf("[%s] %s", this.GetLoggerID(), format), args...)
}

func (this *BaseExchange) Errorf(format string, args ...interface{}) {
	zlog.Errorf(fmt.Sprintf("[%s] %s", this.GetLoggerID(), format), args...)
}

func (this *BaseExchange) Debugf(format string, args ...interface{}) {
	zlog.Debugf(fmt.Sprintf("[%s] %s", this.GetLoggerID(), format), args...)
}

func (this *BaseExchange) Warnf(format string, args ...interface{}) {
	zlog.Warnf(fmt.Sprintf("[%s] %s", this.GetLoggerID(), format), args...)
}

func (this *BaseExchange) SetProxy(proxy string) {
	// slack cmds 设置的 url 可能是 "<pgate://group/tk>"，需要去掉 "<" 和 ">"
	// config 模块并不容易解决这个问题，因此在这里做兼容
	if strings.HasPrefix(proxy, "<") && strings.HasSuffix(proxy, ">") {
		proxy = strings.TrimPrefix(proxy, "<")
		proxy = strings.TrimSuffix(proxy, ">")
	}
	if proxy != "" && !strings.HasPrefix(proxy, "pgate://") && this.ProxyUrl != proxy {
		this.Infof("set proxy: %s", proxy)
		this.ProxyUrl = proxy
		this.Client = resty.New() // 使用新的 client 以便立即生效
		this.Client.SetProxy(proxy)
	}
}

func (this *BaseExchange) RemoveProxy() {
	if this.ProxyUrl != "" {
		this.Infof("remove proxy")
		this.ProxyUrl = ""
		this.Client = resty.New() // 使用新的 client 以便立即生效
		this.Client.RemoveProxy()
	}
}

func (this *BaseExchange) GetProxy() string {
	return this.ProxyUrl
}

func (this *BaseExchange) CheckQuoteQty(orderArgs *CreateOrderArgs) error {
	if orderArgs.QuoteQty > 0 && orderArgs.Qty == 0 {
		price := orderArgs.Price
		if price == 0 {
			lastPrice, err := this.GetLastPrice(orderArgs.InstrumentType, orderArgs.Symbol, false)
			if err != nil {
				return fmt.Errorf("get last price for %s err: %v", orderArgs.Symbol, err)
			}
			price = lastPrice
		}
		qty := orderArgs.QuoteQty / price
		if orderArgs.InstrumentType.IsFuture() {
			var err error
			qty, err = this.Size2Qty(orderArgs.InstrumentType, orderArgs.Symbol, price, orderArgs.QuoteQty)
			if err != nil {
				return fmt.Errorf("size2Qty for %s err: %v", orderArgs.Symbol, err)
			}
		}
		orderArgs.Qty = this.FloorQty(orderArgs.InstrumentType, orderArgs.Symbol, qty)
		this.Infof("order with quote qty, quoteQty: %v, price: %v => qty: %v", orderArgs.QuoteQty, price, orderArgs.Qty)
	} else if orderArgs.QuoteQty == 0 && orderArgs.Qty == 0 && !orderArgs.ReduceOnly {
		return fmt.Errorf("quoteQty and qty both 0")
	}
	return nil
}

func (this *BaseExchange) GetAPIExpireTime() (*time.Time, error) {
	return nil, nil
}

func (this *BaseExchange) SubscribeOrderBook(instrumentType InstrumentType, symbol string) {
	this.Errorf("not implemented")
}

func (this *BaseExchange) UnSubscribeOrderBook(instrumentType InstrumentType, symbol string) {
	this.Errorf("not implemented")
}

func (this *BaseExchange) GetOrderBook(instrumentType InstrumentType, symbol string) (*OrderBook, error) {
	book, _ := this.orderBook.Load(getSymbolCacheKey(instrumentType, symbol))
	if book == nil {
		return nil, fmt.Errorf("order book not found")
	}
	return book, nil
}

func (this *BaseExchange) StoreOrderBook(orderBook *OrderBook) {
	this.orderBook.Store(getSymbolCacheKey(orderBook.InstrumentType, orderBook.Symbol), orderBook)
}

func (this *BaseExchange) GetWithdrawChains(coin string) ([]WithdrawChain, error) {
	return []WithdrawChain{}, ErrNotImplemented
}

func (this *BaseExchange) GetWithdrawFee(coin, chain WithdrawChain) (fee float64, err error) {
	return 0, ErrNotImplemented
}

func (this *BaseExchange) Withdraw(coin string, address string, amount float64, chain WithdrawChain) (id string, err error) {
	return "", ErrNotImplemented
}

func (this *BaseExchange) SetAccountMarginMode(instrumentType InstrumentType, mode AccountMarginMode) error {
	return nil
}

func (this *BaseExchange) IsSubaccount() bool {
	return false
}

func (this *BaseExchange) SubaccountTransfer(coin string, amount float64, isDeposit bool) (err error) {
	return nil
}

func (this *BaseExchange) GetWithdrawAccountBalance(coin string) (total, available float64, er error) {
	return 0, 0, ErrNotImplemented
}

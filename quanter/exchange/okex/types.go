package okex

import (
	"math"
	"sort"
	"strconv"
	"time"

	"github.com/wizhodl/quanter/exchange"
)

type OKExResponse struct {
	Code string `json:"code,omitempty"`
	Msg  string `json:"msg,omitempty"`
	Data []any  `json:"data,omitempty"`
}

// 合约数据
type Instrument struct {
	InstType       string  `json:"instType"`
	Symbol         string  `json:"instId"`
	<PERSON>as          string  `json:"alias"`
	TickSize       float64 `json:"tickSz,string"` // 价格最小变动单位
	LotSize        float64 `json:"lotSz,string"`  // 最小下单数量
	MinSize        float64 `json:"minSz,string"`
	MaxLeverage    float64 `json:"lever,string"` // 最大杠杆率
	SettlCurrency  string  `json:"settleCcy"`    // 盈亏结算和保证金币种
	CtVal          float64 `json:"ctVal,string"` // 合约面值
	CtValCcy       string  `json:"ctValCcy"`     // 合约面值计价币种
	Uly            string  `json:"uly"`          // 合约面值计价币种
	QuoteCcy       string  `json:"quoteCcy"`
	ExpTime        string  `json:"expTime"`
	State          string  `json:"state"`
	positionTiers  *[]PositionTier
	updatedAt      int64 // 更新时间
	instrumentType exchange.InstrumentType
}

func (this Instrument) IsExpiredFutures() bool {
	if this.InstType != FUTURES {
		return false
	}
	ts, _ := strconv.ParseInt(this.ExpTime, 10, 64)
	if ts == 0 {
		return false
	}
	t := time.Unix(ts/1000, 0)
	if t.Before(time.Now()) {
		return true
	}
	return false
}

type PositionTier struct {
	Uly      string  `json:"uly"`             // 合约标的指数
	Tier     int     `json:"tier,string"`     // 仓位档位
	MinSz    float64 `json:"minSz,string"`    // 该档位最少持仓数量 期权/永续/交割 最小持仓量 默认0
	MaxSz    float64 `json:"maxSz,string"`    // 该档位最多持仓数量 期权/永续/交割
	Mmr      float64 `json:"mmr,string"`      // 维持保证金率
	Imr      float64 `json:"imr,string"`      // 最低初始保证金率
	MaxLever float64 `json:"maxLever,string"` // 最高可用杠杆倍数
}

func (i *Instrument) GetMMR(sz float64) float64 {
	sz = math.Abs(sz)
	tiers := *i.positionTiers
	sort.SliceStable(tiers, func(i, j int) bool {
		return tiers[i].Tier < tiers[j].Tier
	})

	for _, tier := range tiers {
		if tier.MaxSz > sz {
			return tier.Mmr
		}
	}
	return 0.004
}

type PosMode string

const (
	LongShortMode PosMode = "long_short_mode"
	NetMode       PosMode = "net_mode"
)

type AccountConfig struct {
	UserID  string  `json:"uid"`
	AcctLv  string  `json:"acctLv"`  // 账户层级 1：简单交易模式，2：单币种保证金模式，3：跨币种保证金模式
	PosMode PosMode `json:"posMode"` // 持仓方式 long_short_mode：双向持仓 net_mode：单向持仓 仅适用交割/永续
}

type MarketCandle struct {
	Ts int64
	O  float64
	H  float64
	L  float64
	C  float64
}

type AlgoState string

const (
	Effective   AlgoState = "effective"
	Canceled    AlgoState = "canceled"
	OrderFailed AlgoState = "order_failed"
)

type AlgoType string

const (
	Conditional AlgoType = "conditional" // 单向止盈止损
	Oco         AlgoType = "oco"         // 双向止盈止损
	Trigger     AlgoType = "trigger"     // 计划委托
	Iceberg     AlgoType = "iceberg"     // 冰山委托
	Twap        AlgoType = "twap"        // 时间加权委托
)

type PosSide string

const (
	Long  PosSide = "long"
	Short PosSide = "short"
	Net   PosSide = "net"
)

type AlgoOrder struct {
	InstrumentID string   `json:"instId"`
	Ccy          string   `json:"ccy"`
	AlgoID       string   `json:"algoId"`
	OrderID      string   `json:"ordId"`
	Size         float64  `json:"sz,string"`
	OrdType      AlgoType `json:"ordType"`
	Side         string   `json:"side"`
	PosSide      PosSide  `json:"posSide"`
	TdMode       string   `json:"tdMode"`
	TgtCcy       string   `json:"tgtCcy"`
	State        string   `json:"state"`
	Lever        string   `json:"lever"`
	TpTriggerPx  string   `json:"tpTriggerPx"`
	TpOrdPx      string   `json:"tpOrdPx"`
	SlTriggerPx  string   `json:"slTriggerPx"`
	SlOrdPx      string   `json:"slOrdPx"`
	TriggerPx    string   `json:"triggerPx"`
	OrdPx        string   `json:"ordPx"`
	ActualSz     string   `json:"actualSz"`
	ActualPx     string   `json:"actualPx"`
	ActualSide   string   `json:"actualSide"`
	TriggerTime  string   `json:"triggerTime"`
	CTime        string   `json:"cTime"`
}

func (a *AlgoOrder) IsOpen() bool {
	return a.State == "live" || a.State == "pause" || a.State == "partially_effective"
}

func (a *AlgoOrder) IsTriggered() bool {
	return a.State == "effective"
}

func (a *AlgoOrder) IsOpenOrder() bool { // 开仓单
	return a.TriggerPx != "" && a.TriggerPx != "0"
}

func (a *AlgoOrder) IsCloseOrder() bool { // 平仓单
	return (a.TpTriggerPx != "" && a.TpTriggerPx != "0") || (a.SlTriggerPx != "" && a.SlTriggerPx != "0")
}

// 订单信息
type Order struct {
	InstrumentID string  `json:"instId"`
	OrderID      string  `json:"ordId"`
	TgtCcy       string  `json:"tgtCcy"`
	Ccy          string  `json:"ccy"`
	ClOrdId      string  `json:"clOrdId"`
	Px           string  `json:"px"`
	Sz           string  `json:"sz"`
	OrdType      string  `json:"ordType"`
	Side         string  `json:"side"`
	PosSide      PosSide `json:"posSide"`
	TdMode       string  `json:"tdMode"`
	AccFillSz    string  `json:"accFillSz"` // 累计成交数量
	FillPx       string  `json:"fillPx"`
	TradeId      string  `json:"tradeId"`
	FillSz       string  `json:"fillSz"`
	FillTime     string  `json:"fillTime"`
	AvgPx        string  `json:"avgPx"` // 成交均价
	State        string  `json:"state"`
	Lever        string  `json:"lever"`
	TpTriggerPx  string  `json:"tpTriggerPx"`
	TpOrdPx      string  `json:"tpOrdPx"`
	SlTriggerPx  string  `json:"slTriggerPx"`
	SlOrdPx      string  `json:"slOrdPx"`
	FeeCcy       string  `json:"feeCcy"`
	Fee          string  `json:"fee"`
	UTime        string  `json:"uTime"`
	CTime        string  `json:"cTime"`
}

type AccountBalance struct {
	Currency  string `json:"ccy"`
	Balance   string `json:"cashBal"` // 现金余额，不含期货持仓保证金
	Available string `json:"availEq"` // 可用保证金
	Eq        string `json:"eq"`      // 总权益，含盈亏
	UPL       string `json:"upl"`     // 未实现盈亏
	Frozen    string `json:"frozenBal"`
}

// 创建订单参数
type CreateOrderArgs struct {
	InstrumentID string   `json:"instId"`
	TdMode       string   `json:"tdMode"`
	Side         string   `json:"side"`
	PosSide      PosSide  `json:"posSide,omitempty"`
	OrdType      AlgoType `json:"ordType"`
	Sz           float64  `json:"sz,string"`
	ReduceOnly   bool     `json:"reduceOnly"`
	TriggerPx    float64  `json:"triggerPx,string,omitempty"`
	OrderPx      float64  `json:"orderPx,string,omitempty"`
	TpTriggerPx  float64  `json:"tpTriggerPx,string,omitempty"`
	TpOrdPx      float64  `json:"tpOrdPx,string,omitempty"`
	SlTriggerPx  float64  `json:"slTriggerPx,string,omitempty"`
	SlOrdPx      float64  `json:"slOrdPx,string,omitempty"`
	// SlTriggerPxType 参数有问题，平仓单带上该参数报错
}

type CreateOrderResp struct {
	AlgoId string `json:"algoId"`
	SCode  string `json:"sCode"`
	SMsg   string `json:"sMsg"`
}

type Position struct {
	InstrumentType string  `json:"instType"`
	InstrumentID   string  `json:"instId"`
	MgnMode        string  `json:"mgnMode"`
	PosId          string  `json:"posId"`
	PosSide        PosSide `json:"posSide"`
	Pos            string  `json:"pos"`
	PosCcy         string  `json:"posCcy"`
	AvailPos       string  `json:"availPos"`
	AvgPx          string  `json:"avgPx"`
	Lever          string  `json:"lever"`
	LiqPx          string  `json:"liqPx"`
	MarkPx         string  `json:"markPx"`
	Last           string  `json:"last"`
	Margin         string  `json:"margin"`
	Upl            string  `json:"upl"`
}

type LeverageInfo struct {
	InstrumentID string  `json:"instId"`
	MgnMode      string  `json:"mgnMode"`
	PosSide      PosSide `json:"posSide"`
	Lever        float64 `json:"lever,string"`
}

type MarketTicker struct {
	InstrumentID string  `json:"instId"`
	LastPrice    float64 `json:"last,string"`
}

type FundingRateHistory struct {
	InstrumentID string  `json:"instId"`
	FundingRate  float64 `json:"fundingRate,string"`
	FundingTime  int64   `json:"fundingTime,string"`
}

type AssetCurrency struct {
	Currency    string `json:"ccy"`
	Name        string `json:"name"`
	Chain       string `json:"chain"`
	CanDep      bool   `json:"canDep"`
	CanWd       bool   `json:"canWd"`
	CanInternal bool   `json:"canInternal"`
	MinWd       string `json:"minWd"`
	MinFee      string `json:"minFee"`
	MaxFee      string `json:"maxFee"`
	MainNet     bool   `json:"mainNet"`
}

type Ticker struct {
	InstType  string  `json:"instType"`
	Symbol    string  `json:"instId"`
	Last      float64 `json:"last,string"`
	LastSz    float64 `json:"lastSz,string"`
	AskPx     float64 `json:"askPx,string"`
	AskSz     float64 `json:"askSz,string"`
	BidPx     float64 `json:"bidPx,string"`
	BidSz     float64 `json:"bidSz,string"`
	Open24h   float64 `json:"open24h,string"`
	High24h   float64 `json:"high24h,string"`
	Low24h    float64 `json:"low24h,string"`
	VolCcy24h float64 `json:"volCcy24h,string"`
	Vol24h    float64 `json:"vol24h,string"`
	Time      int64   `json:"ts,string"`
}

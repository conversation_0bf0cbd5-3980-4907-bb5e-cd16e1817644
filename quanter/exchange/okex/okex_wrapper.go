package okex

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/quanter/exchange"

	resty "github.com/go-resty/resty/v2"
	"github.com/gorilla/websocket"
)

func NewOKEx(options *exchange.Options) (*OKEx, error) {
	if options.ControllerID == "" {
		return nil, errors.New("controller id is required")
	}
	apiKey, err := NewAPISecret(options.ApiKey, options.ApiSecret, false)
	if err != nil {
		return nil, err
	}
	o := &OKEx{
		BaseExchange: *exchange.NewBaseExchange(options),
		instruments:  xsync.NewMapOf[Instrument](),
		apiKey:       apiKey,
	}

	o.Exchange = o
	return o, nil
}

func (this *OKEx) GetSupportedInstrumentTypes() []exchange.InstrumentType {
	return []exchange.InstrumentType{exchange.Spot, exchange.CoinMarginedFutures, exchange.USDXMarginedFutures}
}

func (this *OKEx) MaxLeverage(instrumentType exchange.InstrumentType, symbol string, value float64) float64 {
	return this.getInstrument(symbol).MaxLeverage
}

// 获取账号余额，如获取成功则返回 UserMargin 结构
func (this *OKEx) GetUserMargin(instrumentType exchange.InstrumentType, currency string) (*exchange.UserMargin, error) {
	if instrumentType == exchange.Spot {
		return nil, exchange.ErrNotAvailableForInstrumentType
	}
	path := fmt.Sprintf("/api/v5/account/balance?ccy=%s", currency)

	var data accountData
	if err := this.sendHTTPRequest(resty.MethodGet, path, nil, &data, true, false); err != nil {
		return nil, err
	}
	margin := &exchange.UserMargin{Currency: currency}
	for _, balance := range data.Details {
		if strings.EqualFold(balance.Currency, currency) {
			available, _ := strconv.ParseFloat(balance.Available, 64)
			upl, _ := strconv.ParseFloat(balance.UPL, 64)
			eq, _ := strconv.ParseFloat(balance.Eq, 64)
			margin.AvailableMargin = available
			margin.MarginBalance = eq
			margin.WalletBalance = eq - upl
			break
		}
	}
	return margin, nil
}

// U 本位合约面值为币的数量，订单数量单位为张，Size 表示 U 价值
// 币本位合约面值为 USD，订单数量单位为张，Size 表示币价值
func (this *OKEx) Qty2Size(instrumentType exchange.InstrumentType, symbol string, price float64, qty float64) (size float64, er error) {
	ins := this.getInstrument(symbol)
	if ins == nil {
		return 0, fmt.Errorf("cannot find instrument %s", symbol)
	}

	if ins.instrumentType == exchange.USDXMarginedFutures || ins.instrumentType == exchange.Spot {
		size = price * (qty * ins.CtVal)
		roundSize := math.Min(ins.TickSize, 0.01)
		size = math.Round(size/roundSize) * roundSize
	} else if ins.instrumentType == exchange.CoinMarginedFutures {
		size = qty * ins.CtVal / price
	} else {
		return 0, exchange.ErrNotAvailableForInstrumentType
	}
	return
}

func (this *OKEx) Size2Qty(instrumentType exchange.InstrumentType, symbol string, price float64, size float64) (qty float64, er error) {
	ins := this.getInstrument(symbol)
	if ins == nil {
		return 0, fmt.Errorf("cannot find instrument %s", symbol)
	}

	if ins.instrumentType == exchange.USDXMarginedFutures {
		qty = size / price
		qty = math.Round(qty/ins.CtVal/ins.LotSize) * ins.LotSize
	} else if ins.instrumentType == exchange.CoinMarginedFutures {
		qty = size * price / ins.CtVal
	} else {
		return 0, exchange.ErrNotAvailableForInstrumentType
	}
	return
}

func (this *OKEx) CalcPrice(instrumentType exchange.InstrumentType, symbol string, qty float64, size float64) (price float64, er error) {
	ins := this.getInstrument(symbol)
	if ins == nil {
		return 0, fmt.Errorf("cannot find instrument %s", symbol)
	}

	if ins.instrumentType == exchange.USDXMarginedFutures {
		price = size / (qty * ins.CtVal)
		price = math.Round(price/ins.TickSize) * ins.TickSize
	} else if ins.instrumentType == exchange.CoinMarginedFutures {
		price = qty * ins.CtVal / size
	} else {
		return 0, exchange.ErrNotAvailableForInstrumentType
	}
	return
}

// 根据持仓计算杠杠率
func (this *OKEx) CalculateLeverage(position *exchange.Position) (float64, error) {
	// 看多杠杠率 = 开仓价 / (爆仓价 * (维持保证金率 + 平仓手续费率 - 1) + 开仓价)
	// 看空杠杠率 = 开仓价 / (爆仓价 * (维持保证金率 + 平仓手续费率 + 1) - 开仓价)
	posSide := position.Side
	entryPrice := position.EntryPrice
	liquidationPrice := position.LiquidationPrice
	ins := this.getInstrument(position.Symbol)
	mmr, err := this.getMMR(ins, position.Qty)
	if err != nil {
		return 0, err
	}
	takerFeeRate := 0.0005
	var leverage float64
	if posSide == exchange.PositionSideLong {
		leverage = entryPrice / (liquidationPrice*(mmr+takerFeeRate-1) + entryPrice)
	} else {
		leverage = entryPrice / (liquidationPrice*(mmr+takerFeeRate+1) - entryPrice)
	}
	return math.Round(leverage*100) / 100, nil
}

// TODO: 删除，暴露 SetLeverage 接口，可以保留为 convenient 接口
// 设置杠杠率到最大
func (this *OKEx) SetLeverageToMax(instrumentType exchange.InstrumentType, symbol string, currentLeverage float64, positionSide exchange.PositionSide) bool {
	maxLeverage := this.getInstrument(symbol).MaxLeverage
	if currentLeverage == maxLeverage {
		return true
	}
	return this.setLeverage(symbol, maxLeverage, convertPositionSideToOKSide(positionSide), exchange.Isolated) == nil
}

// 根据持仓调整爆仓价到指定价格，返回调整后的价差
// https://www.okex.com/support/hc/zh-cn/articles/************-7-%E4%BF%9D%E8%AF%81%E9%87%91
// 开仓保证金=面值*张数*开仓价／杠杆
// 爆仓亏损 = 面值*张数*爆仓价-面值*张数*开仓价  (看空，多则取反)
// 维持保证金率 + 平仓手续费率 = (固定保证金+未实现盈亏) / 仓位价值 = (固定保证金-爆仓亏损) / (面值*张数*爆仓价)
// 维持保证金率 + 平仓手续费率 = 保证金/面值/张数/爆仓价 - 1 + 开仓价/爆仓价
// =>
// 看空爆仓价 = (保证金/面值/张数 + 开仓价) / (维持保证金率 + 平仓手续费率 + 1)
// 看多爆仓价 = (保证金/面值/张数 - 开仓价) / (维持保证金率 + 平仓手续费率 - 1)
// 看空杠杠率 = 开仓价 / (爆仓价 * (维持保证金率 + 平仓手续费率 + 1) - 开仓价)
// 看多杠杠率 = 开仓价 / (爆仓价 * (维持保证金率 + 平仓手续费率 - 1) + 开仓价)
func (this *OKEx) AdjustLiquidationPrice(instrumentType exchange.InstrumentType, symbol string, position *exchange.Position, targetLiquidationPrice float64, acceptableDelta float64) (deltaPrice float64, errMsg string) {
	deltaPrice = math.Abs(targetLiquidationPrice - position.LiquidationPrice)
	posSide := convertPositionSideToOKSide(position.Side)

	targetPosition := &exchange.Position{
		Symbol:           symbol,
		Qty:              position.Qty,
		Side:             position.Side,
		LiquidationPrice: targetLiquidationPrice,
		EntryPrice:       position.EntryPrice,
	}
	origTargetLeverage, err := this.CalculateLeverage(targetPosition)
	targetLeverage := math.Round(origTargetLeverage*2*100) / 100 // 公式已经调不准了，额外加 100%，通过调保证金来调整
	this.Warnf("(%s) target leverage: %0.2f, orig: %0.2f", symbol, targetLeverage, origTargetLeverage)
	if err != nil {
		return deltaPrice, err.Error()
	}
	if err := this.setLeverage(symbol, targetLeverage, posSide, exchange.Isolated); err != nil {
		return deltaPrice, fmt.Sprintf("设置杠杆[%0.1f]失败: %s", targetLeverage, err)
	}

	deltaPrice, err = this.checkLiquidationPrice(instrumentType, symbol, position.Side, deltaPrice, targetLiquidationPrice, acceptableDelta)
	if err == nil {
		return deltaPrice, ""
	}

	// 调整杠杠率后可能需要减少保证金
	targetMargin, err := this.calculateMargin(symbol, targetPosition)
	if err != nil {
		return deltaPrice, err.Error()
	}
	if position.Margin > targetMargin {
		deltaMargin := position.Margin - targetMargin

		// 减少保证金时，计算值可能会稍微比可减少的值大一点，所以尝试几次，每次减少 1%
		for i := 0; i < 5; i++ {
			if err := this.updateMargin(symbol, posSide, "reduce", deltaMargin); err != nil {
				this.Errorf("(%s) reduce margin %0.4f err: %s", symbol, deltaMargin, err)
				deltaMargin *= 0.99
				time.Sleep(time.Second)
			} else {
				break
			}
		}
	} else if position.Margin < targetMargin {
		// 增加保证金
		deltaMargin := targetMargin - position.Margin
		if err := this.updateMargin(symbol, posSide, "add", deltaMargin); err != nil {
			return deltaPrice, fmt.Sprintf("增加保证金 %0.4f 失败: %s", deltaMargin, err)
		}
	}

	deltaPrice, err = this.checkLiquidationPrice(instrumentType, symbol, position.Side, deltaPrice, targetLiquidationPrice, acceptableDelta)
	if err == nil {
		return deltaPrice, ""
	}

	if targetLeverage >= this.getInstrument(symbol).MaxLeverage {
		this.Infof("(%s) leverage hit maximum", symbol)
		return deltaPrice, ""
	}

	if err != nil {
		errMsg = err.Error()
	}
	return deltaPrice, errMsg
}

// 更新订单
func (this *OKEx) UpdateOrder(origOrder exchange.Order, args *exchange.UpdateOrderArgs) (*exchange.Order, error) {
	if args.Type != exchange.StopLimit {
		return nil, exchange.ErrNotImplemented
	}
	oldOrder, err := this.getAlgoOrder(args.InstrumentType, origOrder.Symbol, origOrder.OrderID)
	if err != nil {
		return nil, err
	}

	err = this.CancelOrder(args.InstrumentType, args.Type, origOrder.Symbol, origOrder.OrderID)
	if err != nil {
		return nil, err
	}

	if args.OrderQty == 0 {
		args.OrderQty = oldOrder.Qty - oldOrder.ExecQty
	}

	createArgs := exchange.CreateOrderArgs{
		InstrumentType: args.InstrumentType,
		Symbol:         origOrder.Symbol,
		Side:           oldOrder.Side,
		TradeMode:      oldOrder.TradeMode,
		Qty:            args.OrderQty,
		ClosePosition:  args.ClosePosition,
	}

	if args.ReduceOnly {
		createArgs.ReduceOnly = true
		createArgs.Type = exchange.StopLimit
		createArgs.TriggerPrice = args.TriggerPrice
		createArgs.Price = args.Price
	} else {
		createArgs.Type = exchange.StopLimit
		createArgs.TriggerPrice = args.TriggerPrice
		createArgs.Price = args.Price
	}
	if args.TriggerPrice == 0 {
		createArgs.TriggerPrice = oldOrder.TriggerPrice
	}

	order, err := this.CreateOrder(createArgs)
	if order != nil {
		order.SetExtStruct(&origOrder)
	}
	return order, err
}

// 取消所有订单，返回是否调用成功
func (this *OKEx) CancelAllOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string) ([]string, error) {
	algoIDs := []string{}
	orderIDs := []string{}

	limitOrders, err := this.fetchPendingOrders(symbol, "limit")
	if err != nil {
		return nil, fmt.Errorf("fetch pending orders failed, error: %s", err)
	}

	if orderType == exchange.StopLimit || orderType == exchange.UnknownOrderType {
		if algoOrders, err := this.getAlgoPendingOrders(symbol, ""); err != nil {
			return nil, fmt.Errorf("get pending algo orders failed, error: %s", err)
		} else {
			for _, order := range algoOrders {
				algoIDs = append(algoIDs, order.AlgoID)
			}
		}

		if len(limitOrders) > 0 {
			// 查询已生效的委托单，找到对应限价挂单
			algoOrders, err := this.getAlgoHistoryOrders(symbol, Effective, "")
			if err != nil {
				return nil, fmt.Errorf("get history algo orders failed, error: %s", err)
			}
			for _, limitOrder := range limitOrders {
				for _, algoOrder := range algoOrders {
					if limitOrder.OrderID == algoOrder.OrderID {
						orderIDs = append(orderIDs, limitOrder.OrderID)
					}
				}
			}
		}
	}

	if orderType == exchange.Limit || orderType == exchange.UnknownOrderType {
		for _, limitOrder := range limitOrders {
			if exchange.SliceContains(orderIDs, limitOrder.OrderID) {
				continue
			}
			orderIDs = append(orderIDs, limitOrder.OrderID)
		}
	}

	canceledIDs := []string{}
	BATCH_NUM := 10 // 每次最多只能取消 10 个订单
	var batchAlgoIDs []string
	var batchOrderIDs []string
	for {
		if len(algoIDs) > BATCH_NUM {
			batchAlgoIDs = algoIDs[:BATCH_NUM]
			algoIDs = algoIDs[BATCH_NUM:]
		} else {
			batchAlgoIDs = algoIDs
			algoIDs = []string{}
		}

		if len(orderIDs) > BATCH_NUM {
			batchOrderIDs = orderIDs[:BATCH_NUM]
			orderIDs = orderIDs[BATCH_NUM:]
		} else {
			batchOrderIDs = orderIDs
			orderIDs = []string{}
		}

		if len(batchAlgoIDs) > 0 {
			if err := this.cancelBatchAlgoOrders(symbol, batchAlgoIDs); err != nil {
				this.Errorf("(%s) cancel batch algo orders err: %s", symbol, err)
				return []string{}, fmt.Errorf("cancel batch algo orders failed, error: %s", err)
			} else {
				canceledIDs = append(canceledIDs, batchAlgoIDs...)
			}
		}

		if len(batchOrderIDs) > 0 {
			if err := this.cancelBatchOrders(symbol, batchOrderIDs); err != nil {
				this.Errorf("(%s) cancel batch orders err: %s", symbol, err)
				return []string{}, fmt.Errorf("cancel batch orders failed, error: %s", err)
			} else {
				canceledIDs = append(canceledIDs, batchOrderIDs...)
			}
		}

		if len(algoIDs) == 0 && len(orderIDs) == 0 {
			break
		}
	}

	return canceledIDs, nil
}

// 获取进行中的订单
func (this *OKEx) GetOpenOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string) ([]*exchange.Order, error) {
	if symbol != "" {
		ins := this.getInstrument(symbol)
		if ins == nil {
			return nil, fmt.Errorf("cannot find instrument %s", symbol)
		} else if ins.instrumentType != instrumentType {
			return nil, fmt.Errorf("instrument type not match, expect %s, got %s", instrumentType, ins.instrumentType)
		}
	}

	var allOrders []*exchange.Order
	if orderType == exchange.StopLimit || orderType == exchange.UnknownOrderType {
		algoOrder, err := this.getAlgoOpenOrders(instrumentType, symbol)
		if err != nil {
			return nil, err
		}
		allOrders = append(allOrders, algoOrder...)
	}
	if orderType == exchange.Limit || orderType == exchange.UnknownOrderType {
		limitOrders, err := this.getOpenOrders(instrumentType, symbol)
		if err != nil {
			return nil, err
		}
		allOrders = append(allOrders, limitOrders...)
	}

	orders := []*exchange.Order{}
	for _, order := range allOrders {
		ins := this.getInstrument(order.Symbol)
		if ins == nil {
			return nil, fmt.Errorf("cannot find instrument for order symbol %s", order.Symbol)
		}
		if ins.instrumentType != instrumentType {
			this.Warnf("order instrument type not match, args: %s, order: %s", instrumentType, ins.instrumentType)
			continue
		}
		orders = append(orders, order)
	}
	return orders, nil
}

func (this *OKEx) getAlgoOpenOrders(instrumentType exchange.InstrumentType, symbol string) ([]*exchange.Order, error) {
	var orders []*exchange.Order

	// 查询未触发的委托单
	if algoOrders, err := this.getAlgoPendingOrders(symbol, ""); err != nil {
		return nil, err
	} else {
		for _, order := range algoOrders {
			orders = append(orders, convertAlgoOrderToExchangeOrder(order, instrumentType))
		}
	}

	// 查询已触发生成的限价单，并查找到对应的委托单
	if limitOrders, err := this.fetchPendingOrders(symbol, "limit"); err != nil {
		return nil, err
	} else if len(limitOrders) > 0 {
		// 查询已生效的委托单
		algoOrders, err := this.getAlgoHistoryOrders(symbol, Effective, "")
		if err != nil {
			return nil, err
		}
		for _, limitOrder := range limitOrders {
			for _, algoOrder := range algoOrders {
				if limitOrder.OrderID == algoOrder.OrderID {
					orders = append(orders, convertAlgoOrderToExchangeOrder(algoOrder, instrumentType))
				}
			}
		}
	}
	return orders, nil
}

func (this *OKEx) getOpenOrders(instrumentType exchange.InstrumentType, symbol string) ([]*exchange.Order, error) {
	limitOrders, err := this.fetchPendingOrders(symbol, "limit")
	if err != nil {
		return nil, err
	}
	var orders []*exchange.Order
	for _, limitOrder := range limitOrders {
		orders = append(orders, convertOrderToExchangeOrder(limitOrder, instrumentType))
	}
	return orders, nil
}

func (this *OKEx) CloseWebsocket(stop bool) {
	if this.wsConn == nil {
		return
	}
	if stop {
		this.wsClosed = true
	}
	this.wsConn.Close()
}

func (this *OKEx) GetInstrument(instrumentType exchange.InstrumentType, symbol string) (i *exchange.Instrument, _ error) {
	ins := this.getInstrument(symbol)
	if ins == nil {
		return i, fmt.Errorf("%s instrument not found", symbol)
	}
	if ins.instrumentType != instrumentType {
		return i, fmt.Errorf("instrument type not match, expect %s, got %s", instrumentType, ins.instrumentType)
	}
	i = &exchange.Instrument{}
	i.Symbol = ins.Symbol
	i.MinNotional = 0
	i.TickSize = ins.TickSize
	i.LotSize = ins.LotSize
	i.MinSize = ins.MinSize
	i.ContractSize = ins.CtVal
	i.MaxLeverage = ins.MaxLeverage
	i.SettleCurrency = ins.SettlCurrency
	i.UnderlyCurrency = ins.Uly
	i.QuoteCurrency = ins.QuoteCcy
	i.UpdateTime = time.Unix(ins.updatedAt, 0)
	i.InstrumentType = ins.instrumentType

	switch ins.Alias {
	case "this_week":
		i.ContractType = exchange.ContractTypeWeek
	case "next_week":
		i.ContractType = exchange.ContractTypeNextWeek
	case "quarter":
		i.ContractType = exchange.ContractTypeQuarter
	case "next_quarter":
		i.ContractType = exchange.ContractTypeNextQuarter
	}

	if ins.InstType == SWAP {
		i.ContractType = exchange.ContractTypeSwap
	}

	return i, nil
}

func (this *OKEx) GetLastPrice(instrumentType exchange.InstrumentType, symbol string, allowDelay bool) (float64, error) {
	path := fmt.Sprintf("/api/v5/market/ticker?instId=%s", symbol)
	ticker := MarketTicker{}
	if err := this.sendHTTPRequest(resty.MethodGet, path, nil, &ticker, false, false); err != nil {
		return 0, err
	}
	return ticker.LastPrice, nil
}

func (this *OKEx) CreateOrder(args exchange.CreateOrderArgs) (order *exchange.Order, _ error) {
	err := this.CheckQuoteQty(&args)
	if err != nil {
		return nil, fmt.Errorf("check order quoteQty failed, %v", err)
	}

	orderReq := map[string]string{}
	orderReq["instId"] = args.Symbol
	if args.TradeMode == exchange.TradeModeCross {
		orderReq["tdMode"] = "cross"
	} else if args.TradeMode == exchange.TradeModeCash {
		orderReq["tdMode"] = "cash"
	} else {
		orderReq["tdMode"] = "isolated"
		args.TradeMode = exchange.TradeModeIsolated
	}
	orderReq["side"] = strings.ToLower(string(args.Side))

	// ReduceOnly 和 ClosePosition 区别：ReduceOnly 会锁定持仓，仅用 ClosePosition 不会
	closePosition := args.ReduceOnly
	if args.ClosePosition {
		closePosition = true
	}
	if this.dualPositionSide && args.InstrumentType != exchange.Spot {
		// 买卖模式不能传 posSide, 双向持仓模式需要
		orderReq["posSide"] = string(Long)
		if orderReq["side"] == "buy" && closePosition {
			orderReq["posSide"] = string(Short)
		} else if orderReq["side"] == "sell" && !closePosition {
			orderReq["posSide"] = string(Short)
		}
	}

	useAlgoOrder := false
	switch args.Type {
	case exchange.Limit:
		orderReq["ordType"] = "limit"
		if args.TimeInForce == exchange.IOC {
			orderReq["ordType"] = "ioc"
		}
	case exchange.Market:
		orderReq["ordType"] = "market"
	case exchange.StopLimit:
		orderReq["ordType"] = string(Trigger)
		if args.ReduceOnly {
			orderReq["ordType"] = string(Conditional)
		}
		useAlgoOrder = true
	}

	orderReq["sz"] = this.FormatQty(args.InstrumentType, args.Symbol, args.Qty)

	if args.QuoteQty > 0 && args.Type == exchange.Market && args.InstrumentType == exchange.Spot {
		// 仅适用于币币市价订单
		orderReq["sz"] = strconv.FormatFloat(args.QuoteQty, 'f', -1, 64)
		orderReq["tgtCcy"] = "quote_ccy"
	} else {
		orderReq["tgtCcy"] = "base_ccy"
	}

	// tgtCcy 适用于币币市价订单
	if args.Type != exchange.Market || args.InstrumentType != exchange.Spot {
		delete(orderReq, "tgtCcy")
	}

	path := "/api/v5/trade/order"
	if useAlgoOrder {
		path = "/api/v5/trade/order-algo"

		// 止损止盈单会锁定余额/持仓，计划委托单不锁定
		// HACK: 用计划委托创建开仓单，单向止盈止损创建平仓单，为了获取订单时据此区分是否平仓单
		if args.ReduceOnly {
			// 由现价决定是止损还是止盈
			lastPrice, err := this.GetLastPrice(args.InstrumentType, args.Symbol, false)
			if err != nil {
				return nil, err
			}
			if (args.Side == exchange.OrderSideSell && args.TriggerPrice < lastPrice) ||
				(args.Side == exchange.OrderSideBuy && args.TriggerPrice > lastPrice) {
				orderReq["slTriggerPx"] = this.FormatPrice(args.InstrumentType, args.Symbol, args.TriggerPrice)
				orderReq["slOrdPx"] = this.FormatPrice(args.InstrumentType, args.Symbol, args.Price)
			} else {
				orderReq["tpTriggerPx"] = this.FormatPrice(args.InstrumentType, args.Symbol, args.TriggerPrice)
				orderReq["tpOrdPx"] = this.FormatPrice(args.InstrumentType, args.Symbol, args.Price)
			}
		} else {
			orderReq["triggerPx"] = this.FormatPrice(args.InstrumentType, args.Symbol, args.TriggerPrice)
			orderReq["orderPx"] = this.FormatPrice(args.InstrumentType, args.Symbol, args.Price)
		}
		if args.ReduceOnly {
			orderReq["reduceOnly"] = "true"
		}
	} else {
		if args.Price > 0 {
			orderReq["px"] = this.FormatPrice(args.InstrumentType, args.Symbol, args.Price)
		}

		if args.ReduceOnly && !this.dualPositionSide && args.InstrumentType != exchange.Spot {
			// 仅适用于币币杠杆，以及买卖模式下的交割/永续
			orderReq["reduceOnly"] = "true"
		}
	}

	this.Infof("(%s) create oder args: %#v", args.Symbol, orderReq)

	resp := map[string]string{}
	if err := this.sendHTTPRequest(resty.MethodPost, path, orderReq, &resp, true, false); err != nil {
		return order, err
	}
	nowTime := time.Now()
	orderID := resp["ordId"]
	if useAlgoOrder {
		orderID = resp["algoId"]
	}
	return &exchange.Order{
		InstrumentType: args.InstrumentType,
		OrderID:        orderID,
		Symbol:         args.Symbol,
		Price:          args.Price,
		TriggerPrice:   args.TriggerPrice,
		Qty:            args.Qty,
		QuoteQty:       args.QuoteQty,
		Type:           args.Type,
		TradeMode:      args.TradeMode,
		Side:           args.Side,
		Status:         exchange.OrderStatusNew,
		TimeInForce:    args.TimeInForce,
		ReduceOnly:     args.ReduceOnly,
		CreateTime:     &nowTime,
		UpdateTime:     &nowTime,
	}, nil
}

func (this *OKEx) GetOrder(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol, orderID string, timeRange *exchange.TimeRange) (order *exchange.Order, _ error) {
	if orderType == exchange.StopLimit {
		return this.getAlgoOrder(instrumentType, symbol, orderID)
	}

	okOrder, err := this.fetchOrderByID(symbol, orderID)
	if err != nil {
		return order, err
	}
	return convertOrderToExchangeOrder(okOrder, instrumentType), nil
}

func (this *OKEx) CancelOrder(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol, orderID string) error {
	cancalAlgoOrder := func(symbol, orderID string) error {
		err := this.cancelBatchAlgoOrders(symbol, []string{orderID})
		if err != nil {
			// 如果已生效，尝试取消对应限价单
			if algoOrders, er := this.getAlgoHistoryOrders(symbol, "", orderID); er == nil && len(algoOrders) == 1 {
				algoOrder := &algoOrders[0]
				if algoOrder.IsTriggered() && algoOrder.OrderID != "" {
					return this.cancelBatchOrders(symbol, []string{algoOrder.OrderID})
				}
			}
		}
		return err
	}

	if orderType == exchange.StopLimit {
		return cancalAlgoOrder(symbol, orderID)
	} else if orderType == exchange.Limit {
		return this.cancelBatchOrders(symbol, []string{orderID})
	}

	if orderType == exchange.UnknownOrderType {
		order, err := this.GetOrder(instrumentType, exchange.StopLimit, symbol, orderID, nil)
		if err == nil && order != nil {
			return cancalAlgoOrder(symbol, orderID)
		} else {
			return this.cancelBatchOrders(symbol, []string{orderID})
		}
	}

	return exchange.ErrNotImplemented
}

func (this *OKEx) GetTradablePairs(instrumentType exchange.InstrumentType, uSymbol string) ([]string, error) {
	this.cacheLatestInstruments()

	var pairs []string

	if uSymbol == "" {
		uSymbol = "USDT"
	}

	this.instruments.Range(func(symbol string, instrument Instrument) bool {
		if instrumentType == exchange.Spot && instrument.InstType != SPOT {
			return true
		}
		if instrumentType == exchange.CoinMarginedFutures {
			if instrument.InstType != FUTURES && instrument.InstType != SWAP {
				return true
			}
			if fmt.Sprintf("%s-USD", instrument.SettlCurrency) != instrument.Uly {
				return true
			}
		}
		if instrumentType == exchange.USDXMarginedFutures {
			if instrument.InstType != FUTURES && instrument.InstType != SWAP {
				return true
			}
			if instrument.SettlCurrency != uSymbol {
				return true
			}
		}
		if instrument.IsExpiredFutures() {
			return true
		}
		pairs = append(pairs, symbol)
		return true
	})

	return pairs, nil
}
func (this *OKEx) GetAccountConfig(instrumentType exchange.InstrumentType) (config *exchange.AccountConfig, _ error) {
	if instrumentType == exchange.Spot {
		return nil, exchange.ErrNotAvailableForInstrumentType
	}
	if okConfig, err := this.fetchAccountConfig(); err != nil {
		return nil, err
	} else {
		config = &exchange.AccountConfig{}
		config.UserID = okConfig.UserID
		if okConfig.AcctLv == "1" {
			config.MarginMode = exchange.AccountMarginModeSimple
		} else if okConfig.AcctLv == "2" {
			config.MarginMode = exchange.AccountMarginModeIsolated
		} else if okConfig.AcctLv == "3" {
			config.MarginMode = exchange.AccountMarginModeCross
		}
		if okConfig.PosMode == LongShortMode {
			config.DualPositionSide = true
			this.dualPositionSide = true
		}
		config.AccountBalanceCross = true
	}
	return config, nil
}

func (this *OKEx) GetAccountBalances(instrumentType exchange.InstrumentType) (accountBalances []*exchange.AccountBalance, err error) {
	if accounts, er := this.fetchAccountBalance(); er != nil {
		err = er
	} else {
		for _, balance := range accounts {
			available, _ := strconv.ParseFloat(balance.Available, 64)
			total, _ := strconv.ParseFloat(balance.Eq, 64)

			accountBalances = append(accountBalances, &exchange.AccountBalance{
				InstrumentType: instrumentType,
				Currency:       balance.Currency,
				Total:          total,
				Available:      available,
			})
		}
	}
	return accountBalances, err
}

func (this *OKEx) GetAccountCurrencies(instrumentType exchange.InstrumentType) (currencies []string, _ error) {
	assetCurrencies := []AssetCurrency{}
	if err := this.sendHTTPRequest(resty.MethodGet, "/api/v5/asset/currencies", nil, &assetCurrencies, true, true); err != nil {
		return []string{}, err
	}
	for _, c := range assetCurrencies {
		currencies = append(currencies, c.Currency)
	}
	return currencies, nil
}

func (this *OKEx) GetPositions(instrumentType exchange.InstrumentType, symbol string, allowCache bool) (positions []*exchange.Position, er error) {
	positions = []*exchange.Position{}
	if instrumentType == exchange.CoinMarginedFutures || instrumentType == exchange.USDXMarginedFutures {
		positions, er = this.fetchPositions(instrumentType, symbol)
	} else {
		er = exchange.ErrNotImplemented
	}
	return positions, er
}

func (this *OKEx) SetDualPositionSide(instrumentType exchange.InstrumentType, dualPositionSide bool) error {
	this.dualPositionSide = dualPositionSide
	if dualPositionSide {
		return this.setPositionMode(LongShortMode)
	} else {
		return this.setPositionMode(NetMode)
	}
}

func (this *OKEx) SetMarginMode(instrumentType exchange.InstrumentType, symbol string, mode exchange.MarginMode) error {
	// 下单时指定保证金模式
	return nil
}

func (this *OKEx) QueryFundingHistory(instrumentType exchange.InstrumentType, symbol string, limit int, from time.Time, to time.Time) (his []*exchange.FundingHistory, _ error) {
	resp := []FundingRateHistory{}
	path := fmt.Sprintf("/api/v5/public/funding-rate-history?instId=%s&limit=%v", symbol, limit)
	if err := this.sendHTTPRequest(resty.MethodGet, path, nil, &resp, false, true); err != nil {
		return his, err
	}
	_, code, err := this.TranslateFutureSymbol(instrumentType, symbol, "")
	if err != nil {
		return his, err
	}
	for _, okHis := range resp {
		his = append(his, &exchange.FundingHistory{
			Code:     code,
			Exchange: exchange.OKEx,
			Symbol:   symbol,
			Rate:     okHis.FundingRate,
			Time:     time.Unix(okHis.FundingTime/1000, 0),
			Period:   "8h",
		})
	}
	return his, nil
}

func (this *OKEx) SetLeverage(instrumentType exchange.InstrumentType, symbol string, marginMode exchange.MarginMode, side exchange.PositionSide, leverage float64) error {
	okSide := Net
	if side == exchange.PositionSideShort {
		okSide = Short
	} else if side == exchange.PositionSideLong {
		okSide = Long
	}
	return this.setLeverage(symbol, leverage, okSide, marginMode)
}

func (this *OKEx) GetLeverage(instrumentType exchange.InstrumentType, symbol string, marginMode exchange.MarginMode) (leverageLong, leverageShort float64, err error) {
	leverageInfos, err := this.fetchLeverageInfo(symbol)
	if err != nil {
		return 0, 0, err
	}
	for _, info := range *leverageInfos {
		if info.PosSide == Long {
			leverageLong = info.Lever
		} else if info.PosSide == Short {
			leverageShort = info.Lever
		}
	}
	if leverageLong == 0 || leverageShort == 0 {
		err = fmt.Errorf("got 0 leverage response")
	}
	return
}

func (this *OKEx) TranslateSymbolCode(symbolCode *exchange.SymbolCode) (spotAndFutureSymbols []*exchange.SymbolPair, er error) {
	spotAndFutureSymbols = []*exchange.SymbolPair{}
	this.CacheInstruments(false)
	spotSymbol := ""
	futureSymbol := ""
	spotPairs := []string{}
	futurePairs := []string{}
	this.instruments.Range(func(symbol string, ins Instrument) bool {
		if ins.instrumentType == exchange.Spot {
			spotPairs = append(spotPairs, ins.Symbol)
		} else if ins.instrumentType == exchange.CoinMarginedFutures || ins.instrumentType == exchange.USDXMarginedFutures {
			futurePairs = append(futurePairs, ins.Symbol)
		}
		return true
	})

	uSymbol := symbolCode.USDXSymbol
	spotQuote := uSymbol
	if symbolCode.IsSpot() || symbolCode.InstrumentType() == exchange.USDXMarginedFutures {
		spotQuote = this.GetSymbolCodeQuote(symbolCode)
	}

	for _, s := range spotPairs {
		if strings.HasPrefix(s, symbolCode.Coin()+"-") && strings.HasSuffix(s, "-"+spotQuote) {
			spotSymbol = s
			break
		}
	}

	// 按从大到小排序，则同一月份优先取日期靠后的
	sort.SliceStable(futurePairs, func(i, j int) bool {
		return futurePairs[i] > futurePairs[j]
	})

	if symbolCode.IsFuture() {
		marginSep := "-USD-"
		if symbolCode.InstrumentType() == exchange.USDXMarginedFutures {
			marginSep = fmt.Sprintf("-%s-", uSymbol)
		}
		for _, s := range futurePairs {
			isPrefixOK := false
			if (symbolCode.InstrumentType() == exchange.CoinMarginedFutures && strings.HasPrefix(s, symbolCode.Coin()+marginSep)) ||
				(symbolCode.InstrumentType() == exchange.USDXMarginedFutures && strings.HasPrefix(s, symbolCode.Coin()+marginSep)) {
				isPrefixOK = true
			}
			if !isPrefixOK {
				continue
			}

			if symbolCode.IsWildcard() {
				futureSymbol = s
			} else {
				equalNotPerp := !strings.HasSuffix(s, "-SWAP") && s[len(s)-4:len(s)-2] == symbolCode.Month()
				equalPerp := strings.HasSuffix(s, "-SWAP") && symbolCode.Month() == "00"
				if equalNotPerp || equalPerp {
					futureSymbol = s
				}
			}
			if futureSymbol != "" {
				spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(symbolCode, spotSymbol, futureSymbol))
				if !symbolCode.IsWildcard() {
					break
				}
			}
		}
	} else {
		if spotSymbol != "" {
			spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(symbolCode, spotSymbol, ""))
		} else {
			er = fmt.Errorf("spot symbol not found (%s)", symbolCode)
			return
		}
	}
	if len(spotAndFutureSymbols) == 0 {
		er = fmt.Errorf("spot and future symbol not found (%s)", symbolCode)
	}
	return
}

func (this *OKEx) TranslateFutureSymbol(instrumentType exchange.InstrumentType, futureSymbol, uSymbol string) (spotSymbol string, futureCode *exchange.SymbolCode, er error) {
	spotSymbol = ""
	spotPairs := []string{}
	futurePairs := []string{}
	this.instruments.Range(func(symbol string, ins Instrument) bool {
		if ins.instrumentType == exchange.Spot {
			spotPairs = append(spotPairs, ins.Symbol)
		} else if ins.instrumentType == exchange.CoinMarginedFutures || ins.instrumentType == exchange.USDXMarginedFutures {
			futurePairs = append(futurePairs, ins.Symbol)
		}
		return true
	})

	if uSymbol == "" {
		uSymbol = "USDT"
	}

	for _, s := range futurePairs {
		futureCodeStr := ""
		if strings.EqualFold(futureSymbol, s) {
			marginSep := "-USD-"
			if instrumentType == exchange.USDXMarginedFutures {
				marginSep = fmt.Sprintf("-%s-", uSymbol)
			}
			parts := strings.Split(s, marginSep)
			if len(parts) == 2 {
				futureCodeStr += parts[0]
				tail := parts[1]
				if tail != "SWAP" {
					futureCodeStr += tail[2:4]
				} else {
					futureCodeStr += "00" // 永续代号
				}
				if instrumentType == exchange.USDXMarginedFutures {
					futureCodeStr += ".U"
				}
				futureCode = &exchange.SymbolCode{Code: futureCodeStr, USDXSymbol: uSymbol}
			}
		}
	}
	if futureCode == nil {
		er = errors.New("future symbol not found")
		return
	}
	for _, s := range spotPairs {
		if strings.EqualFold(s, fmt.Sprintf("%s-%s", futureCode.Coin(), uSymbol)) {
			spotSymbol = s
			break
		}
	}
	return
}

// 更新交易对信息，force 时忽略缓存
func (this *OKEx) CacheInstruments(force bool) error {
	spotCached := false
	cFutureCached := false
	uFutureCached := false
	this.instruments.Range(func(symbol string, instrument Instrument) bool {
		if instrument.instrumentType == exchange.Spot {
			spotCached = true
		} else if instrument.instrumentType == exchange.CoinMarginedFutures {
			cFutureCached = true
		} else if instrument.instrumentType == exchange.USDXMarginedFutures {
			uFutureCached = true
		}
		return true
	})

	if force || !spotCached || !cFutureCached || !uFutureCached {
		return this.cacheLatestInstruments()
	}

	return nil
}

func (this *OKEx) GetName() string {
	return exchange.OKEx
}

// 获取并计算最新 K 线数据
func (this *OKEx) GetLatestKLines(instrumentType exchange.InstrumentType, symbol string, periodHour, num int) (klines []*exchange.KLine, er error) {
	// 因为 okex 对k线请求频率限制很严格，防止并发读取导致错误，因此加锁
	klineRequestMutex.Lock()
	defer klineRequestMutex.Unlock()

	origKlines, err := this.getCandles(symbol, periodHour, num)
	if err != nil {
		er = err
		return
	}

	// 时间从早到晚排序
	sort.SliceStable(origKlines, func(i, j int) bool {
		return origKlines[i].Ts < origKlines[j].Ts
	})

	// origKlines 里是小时线，需合并小时线为 PeriodHour 小时线
	// 取 PeriodHour 点，如 6小时线，取 0、6、12、18 点
	periodSeconds := int64(periodHour * 3600)
	klineCount := 0
	for _, candle := range origKlines {
		ts := candle.Ts / 1000
		if ts%periodSeconds == 0 {
			// 这根小时线是 PeriodHour 的开盘线
			klineCount++

			klines = append(klines, &exchange.KLine{
				Open:  candle.O,
				Close: candle.C,
				High:  candle.H,
				Low:   candle.L,
				Time:  ts,
			})
		} else if klineCount > 0 {
			// 非 PeriodHour 的开盘线
			klineIdx := klineCount - 1

			klines[klineIdx].Close = candle.C
			klines[klineIdx].High = math.Max(klines[klineIdx].High, candle.H)
			klines[klineIdx].Low = math.Min(klines[klineIdx].Low, candle.L)
		}
	}

	return
}

// 实时连接
func (this *OKEx) ConnectWebsocket(instrumentTypes []exchange.InstrumentType, connectedCallback func(connected bool)) {
	if this.apiKey.ApiSecret == "" {
		// 密码还未设置,稍后再试
		go this.reconnectLater()
		return
	}

	hostURL := WS_URL
	path := "/ws/v5/private"
	if this.IsTestnet {
		hostURL = TESTNET_WS_URL
	}

	u := url.URL{Scheme: "wss", Host: hostURL, Path: path}
	if this.IsTestnet {
		u.RawQuery = "brokerId=9999"
	}
	this.Infof("connecting to %s", u.String())

	// 进行连接
	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		this.Errorf("okex ws dial err: %s", err)
		go this.reconnectLater()
		return
	}
	this.wsConn = c

	defer func() {
		c.Close()
		this.Infof("okex websocket closed", this.ControllerID)
	}()

	done := make(chan struct{}) // 连接是否结束

	logined := false
	go func() {
		defer close(done)
		for {
			// 读取实时消息
			_, message, err := c.ReadMessage()
			if err != nil {
				this.Errorf("read message err: %s", err)

				go this.reconnectLater()
				return
			}

			// 未登录时登录
			if !logined {
				if this.wsLogin() {
					this.Infof("okex ws logined")
					logined = true
				}
			}

			if string(message) == "pong" {
				continue
			}

			// this.Debugf("okex websocket recv: %s", this.loggerID, message)

			var jsonData map[string]any
			err = json.Unmarshal(message, &jsonData)
			if err == nil {
				event, _ := jsonData["event"].(string)
				code, _ := jsonData["code"].(string)
				if event == "login" && code == "0" {
					// 登录成功
					this.subscribe()
					continue
				}

				arg, _ := jsonData["arg"].(map[string]any)
				if arg != nil {
					channel, _ := arg["channel"].(string)
					data, _ := jsonData["data"].([]any)
					if data == nil {
						continue
					}
					if channel == "account" {
						this.onBalanceChange(data)
					} else if channel == "orders" || channel == "orders-algo" {
						this.onOrderChange(data)
					}
				}
			}
		}
	}()

	go this.publicWebsocket()

	pingTicker := time.NewTicker(time.Second * 10)
	defer pingTicker.Stop()

	for {
		// 保存运行直到连接结束
		select {
		case <-pingTicker.C:
			this.sendWsMsg("ping")
		case <-done:
			return
		}
	}
}

func (this *OKEx) publicWebsocket() {
	hostURL := WS_URL
	path := "/ws/v5/public"
	if this.IsTestnet {
		hostURL = TESTNET_WS_URL
	}

	u := url.URL{Scheme: "wss", Host: hostURL, Path: path}
	if this.IsTestnet {
		u.RawQuery = "brokerId=9999"
	}
	this.Infof("connecting to %s", u.String())

	// 进行连接
	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		this.Errorf("okex public ws dial err: %s", err)
		go this.reconnectPublicWebsocket()
		return
	}
	this.wsConnPublic = c

	defer func() {
		// 函数运行结束时关闭连接
		c.Close()
		this.Infof("okex public websocket closed", this.ControllerID)
	}()

	done := make(chan struct{}) // 连接是否结束

	go func() {
		defer close(done)
		for {
			// 读取实时消息
			_, message, err := c.ReadMessage()
			if err != nil {
				this.Errorf("read message err: %s", err)
				go this.reconnectPublicWebsocket()
				return
			}

			if string(message) == "pong" {
				continue
			}

			// this.Debugf("okex public websocket recv: %s", this.GetLoggerID(), message)

			var jsonData map[string]any
			err = json.Unmarshal(message, &jsonData)
			if err == nil {
				arg, _ := jsonData["arg"].(map[string]any)
				if arg != nil {
					channel, _ := arg["channel"].(string)
					data, _ := jsonData["data"].([]any)
					if data == nil {
						continue
					}
					if channel == "tickers" {
						this.onTickers(data)
					}
				}
			}
		}
	}()

	this.subscribedSymbols = []string{}
	subscribeTicker := time.NewTicker(time.Second * 10)
	defer subscribeTicker.Stop()

	for {
		select {
		case <-subscribeTicker.C:
			this.sendWsPublicMsg("ping")
			this.subscribeSymbols()
		case <-done:
			return
		}
	}
}

func (this *OKEx) reconnectPublicWebsocket() {
	if this.wsClosed {
		return
	}
	this.Infof("okex public ws reconnect in 30 seconds...")
	time.Sleep(time.Second * 30)
	this.publicWebsocket()
}

func (this *OKEx) subscribeSymbols() {
	if this.wsConnPublic == nil {
		return
	}

	if !this.EnableRealtimePrice {
		return
	}

	symbolsNeed := []string{}
	for _, priceTrigger := range this.PriceTriggers {
		symbolsNeed = append(symbolsNeed, priceTrigger.Symbol)
	}

	for _, watch := range this.PriceWatches {
		symbolsNeed = append(symbolsNeed, watch.Symbol)
	}

	for _, symbol := range symbolsNeed {
		if !exchange.SliceContains(this.subscribedSymbols, symbol) {
			msg := fmt.Sprintf(
				`{"op":"subscribe","args":[{"channel": "tickers","instId": "%s"}]}`,
				symbol,
			)
			if err := this.sendWsPublicMsg(msg); err != nil {
				this.Errorf("subscribe msg error: %s", err)
			} else {
				this.subscribedSymbols = append(this.subscribedSymbols, symbol)
			}
		}
	}
}

func (this *OKEx) sendWsPublicMsg(msg string) error {
	this.wsPublicMutex.Lock()
	defer this.wsPublicMutex.Unlock()

	if this.wsConnPublic == nil {
		return errors.New("wsConnPublic is nil")
	}

	return this.wsConnPublic.WriteMessage(websocket.TextMessage, []byte(msg))
}

var _ exchange.Exchange = (*OKEx)(nil)

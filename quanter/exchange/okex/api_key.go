package okex

import (
	"errors"
	"fmt"
	"strings"

	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/secrets"
)

type APISecret struct {
	exchange.BaseAPISecret
	ApiPassword string
}

func NewEmptyAPISecret() *APISecret {
	s := &APISecret{
		BaseAPISecret: exchange.BaseAPISecret{
			ExchangeName: exchange.OKEx,
		},
	}
	s.Secretable = s
	return s
}

func NewAPISecret(apiKey string, apiSecret secrets.SecretString, isEncrypted bool) (*APISecret, error) {
	s := NewEmptyAPISecret()
	s.ApiKey = apiKey
	if isEncrypted {
		s.EncyptedApiSecret = apiSecret
		if err := s.Decrypt(); err != nil {
			return nil, err
		}
	} else {
		s.DecryptedApiSecret = apiSecret
	}
	if err := s.Decode(); err != nil {
		return nil, err
	}
	return s, nil
}

func (this *APISecret) Decode() error {
	parts := strings.Split(string(this.DecryptedApiSecret), "|||")
	if len(parts) != 2 {
		return errors.New("wrong number of arguments")
	}
	this.BaseAPISecret.ApiSecret = secrets.SecretString(parts[0])
	this.ApiPassword = parts[1]
	return nil
}

func (this *APISecret) Encode() secrets.SecretString {
	this.BaseAPISecret.DecryptedApiSecret = secrets.SecretString(fmt.Sprintf("%s|||%s", this.BaseAPISecret.ApiSecret.Reveal(), this.ApiPassword))
	return this.BaseAPISecret.DecryptedApiSecret
}

func (this *APISecret) Prompt() bool {
	this.ApiKey = exchange.SurveyInput("请输入 ApiKey: ")
	apiSecret := exchange.SurveyPassword("请输入 ApiSecret: ")
	this.ApiSecret = secrets.SecretString(apiSecret)
	// 组合 secret 和 password
	this.ApiPassword = exchange.SurveyInput("请输入 API Password: ")
	fmt.Printf("\n\n*** 请确认输入的API *** \n\nAPI Key: %s\nAPI Secret: %s\nAPI Password: %s\n\n",
		this.ApiKey,
		exchange.FormatPassword(apiSecret),
		exchange.FormatPassword(this.ApiPassword),
	)
	return true
}

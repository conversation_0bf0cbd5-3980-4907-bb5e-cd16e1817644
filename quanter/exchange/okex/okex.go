package okex

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/quanter/exchange"

	resty "github.com/go-resty/resty/v2"
	"github.com/gorilla/websocket"
)

// API doc: https://www.okx.com/docs-v5/zh/

const HOST_URL = "www.okx.com"
const WS_URL = "ws.okx.com:8443"
const TESTNET_WS_URL = "wspap.okx.com:8443"

const (
	SPOT    = "SPOT"
	FUTURES = "FUTURES"
	SWAP    = "SWAP"
)

const USE_CALCULATOR_THRESHOLD = 0.02 // 爆仓价差大于该比例时用公式计算杠杠率

var klineRequestMutex sync.Mutex = sync.Mutex{}

type OKEx struct {
	exchange.BaseExchange

	dualPositionSide  bool
	instruments       *xsync.MapOf[string, Instrument]
	wsConn            *websocket.Conn // 私有频道连接
	wsConnPublic      *websocket.Conn // 公共频道连接
	wsClosed          bool
	wsMutex           sync.Mutex
	wsPublicMutex     sync.Mutex
	subscribedSymbols []string

	apiKey *APISecret
}

func (i Instrument) IsExpired() bool {
	return (i.updatedAt + 3600) < time.Now().Unix()
}

// 获取基本请求 request
func (this *OKEx) getBaseRequest() *resty.Request {
	client := this.Client
	hostURL := HOST_URL
	client.
		SetBaseURL(fmt.Sprintf("https://%s", hostURL)).
		SetTimeout(30 * time.Second).     // 设置超时时间，超时无反应则返回超时错误
		SetRetryCount(0).                 // 请求失败时的重试次数
		SetRetryWaitTime(5 * time.Second) // 重试前等待时间

	req := client.R()
	req.SetHeader("Content-Type", "application/json")

	if this.IsTestnet {
		req.SetHeader("x-simulated-trading", "1")
	}

	return req
}

func (this *OKEx) signData(data, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func (this *OKEx) sendHTTPRequest(httpMethod, requestPath string, data, result any, authenticated bool, arrayData bool) (_ error) {
	req := this.getBaseRequest()

	payload := []byte("")
	if data != nil {
		payloadData, err := json.Marshal(data)
		if err != nil {
			return errors.New("sendHTTPRequest: Unable to JSON request")
		}
		payload = payloadData
		req.SetBody(payload)
	}

	if authenticated {
		apiSecret := this.apiKey.ApiSecret
		apiPwd := this.apiKey.ApiPassword

		utcTime := time.Now().UTC().Format(time.RFC3339)
		sign := this.signData(utcTime+httpMethod+requestPath+string(payload), string(apiSecret))

		req.SetHeader("OK-ACCESS-KEY", this.apiKey.ApiKey)
		req.SetHeader("OK-ACCESS-SIGN", sign)
		req.SetHeader("OK-ACCESS-TIMESTAMP", utcTime)
		req.SetHeader("OK-ACCESS-PASSPHRASE", apiPwd)
	}

	okResult := &OKExResponse{}
	resp, err := req.SetResult(okResult).Execute(httpMethod, requestPath)
	if err != nil {
		this.Errorf("send http request(%s) err: %s", requestPath, err)
		return err
	}

	if resp.StatusCode() != 200 {
		this.Errorf("request(%s) resp err: %s %s", requestPath, resp.Status(), resp)
		// 错误 code 类型可能不是 string，不能转换为 OKExResponse
		return fmt.Errorf("[%d]%s", resp.StatusCode(), resp)
	}

	if okResult.Code != "0" {
		msg := okResult.Msg
		if len(okResult.Data) > 0 {
			type sMsgData struct {
				SCode string `json:"sCode"`
				SMsg  string `json:"sMsg"`
			}
			var msgData sMsgData
			dataJson, _ := json.Marshal(okResult.Data[0])
			if err := json.Unmarshal(dataJson, &msgData); err == nil && msgData.SMsg != "" {
				msg = msgData.SMsg
			}
		}
		this.Errorf("send http request(%s) err, resp: %s", requestPath, resp)
		return fmt.Errorf("err[%v]: %v", okResult.Code, msg)
	}

	if len(okResult.Data) > 0 && result != nil {
		if arrayData {
			dataJson, _ := json.Marshal(okResult.Data)
			err := json.Unmarshal(dataJson, result)
			if err != nil {
				this.Errorf("request(%s) unmarshal data to result err, resp: %s", requestPath, resp)
			}
			return err
		} else {
			data := okResult.Data[0]
			dataJson, _ := json.Marshal(data)
			err := json.Unmarshal(dataJson, result)
			if err != nil {
				this.Errorf("request(%s) unmarshal data to result err, resp: %s", requestPath, resp)
			}
			return err
		}
	}

	return nil
}

// 获取合约持仓信息
func (this *OKEx) getPosition(instrumentType exchange.InstrumentType, symbol string, side exchange.PositionSide) (*exchange.Position, error) {
	positions, err := this.fetchPositions(instrumentType, symbol)
	if err != nil {
		return nil, err
	}
	for _, position := range positions {
		if side != "" && side != position.Side {
			continue
		}
		return position, nil
	}
	p := exchange.Position{}
	p.ExchangeName = exchange.OKEx
	p.Symbol = symbol
	return &p, nil
}

func (this *OKEx) fetchPositionTiers(instType, uly string) (*[]PositionTier, error) {
	positionTiers := &[]PositionTier{}
	path := fmt.Sprintf("/api/v5/public/position-tiers?instType=%s&tdMode=isolated&Uly=%s", instType, uly)
	if err := this.sendHTTPRequest(resty.MethodGet, path, nil, positionTiers, false, true); err != nil {
		this.Errorf("fetch position-tiers err: %s", err)
		return nil, err
	}
	return positionTiers, nil
}

func (this *OKEx) cachePositionTiers(instrument *Instrument) error {
	var err error
	instrument.positionTiers, err = this.fetchPositionTiers(instrument.InstType, instrument.Uly)
	if err != nil {
		return err
	}
	instrument.updatedAt = time.Now().Unix()
	this.instruments.Store(instrument.Symbol, *instrument)
	return nil
}

// 获取 instType 所有产品信息
func (this *OKEx) fetchInstruments(instType string) ([]Instrument, error) {
	path := fmt.Sprintf("/api/v5/public/instruments?instType=%s", instType)

	instruments := []Instrument{}
	rawInstruments := &[]map[string]any{}
	if err := this.sendHTTPRequest(resty.MethodGet, path, nil, rawInstruments, false, true); err != nil {
		return instruments, err
	}

	// 需 float64 字段无值时返回的空字符串无法直接 unmarshal
	for _, raw := range *rawInstruments {
		if raw["ctVal"] == "" {
			raw["ctVal"] = "0"
		}
		if raw["lever"] == "" {
			raw["lever"] = "0"
		}
	}

	data, _ := json.Marshal(rawInstruments)
	err := json.Unmarshal(data, &instruments)
	for i, ins := range instruments {
		instruments[i].updatedAt = time.Now().Unix()
		switch instType {
		case SPOT:
			instruments[i].instrumentType = exchange.Spot
		case FUTURES:
			if fmt.Sprintf("%s-USD", ins.SettlCurrency) == ins.Uly {
				instruments[i].instrumentType = exchange.CoinMarginedFutures
			} else {
				instruments[i].instrumentType = exchange.USDXMarginedFutures
			}
		case SWAP:
			if fmt.Sprintf("%s-USD", ins.SettlCurrency) == ins.Uly {
				instruments[i].instrumentType = exchange.CoinMarginedFutures
			} else {
				instruments[i].instrumentType = exchange.USDXMarginedFutures
			}
		}
	}
	return instruments, err
}

func (this *OKEx) cacheLatestInstruments() error {
	if spotInstruments, err := this.fetchInstruments(SPOT); err != nil {
		return fmt.Errorf("cache latest instruments get spotInstruments err: %s", err)
	} else {
		for _, ins := range spotInstruments {
			if ins.State != "live" {
				continue
			}
			this.instruments.Store(ins.Symbol, ins)
		}
	}

	if futureInstruments, err := this.fetchInstruments(FUTURES); err != nil {
		return fmt.Errorf("cache latest instruments get futureInstruments err: %s", err)
	} else {
		for _, ins := range futureInstruments {
			if ins.State != "live" {
				continue
			}
			this.instruments.Store(ins.Symbol, ins)
		}
	}

	if swapInstruments, err := this.fetchInstruments(SWAP); err != nil {
		return fmt.Errorf("cache latest instruments get swapInstruments err: %s", err)
	} else {
		for _, ins := range swapInstruments {
			if ins.State != "live" {
				continue
			}
			this.instruments.Store(ins.Symbol, ins)
		}
	}
	return nil
}

// 合约 instrument
func (this *OKEx) getInstrument(symbol string) *Instrument {
	instrument, found := this.instruments.Load(symbol)
	if !found || instrument.IsExpired() {
		this.cacheLatestInstruments()
		instrument, found = this.instruments.Load(symbol)
		if !found {
			return nil
		}
	}

	return &instrument
}

func (this *OKEx) fetchMarketCandles(symbol string, bar string, after int64) (resp [][]string, _ error) {
	path := fmt.Sprintf("/api/v5/market/candles?instId=%s&bar=%s&limit=300", symbol, bar)
	if after != 0 {
		path += fmt.Sprintf("&after=%v", after)
	}
	return resp, this.sendHTTPRequest(resty.MethodGet, path, nil, &resp, true, true)
}

// 获取最新K线数据, K线数据每个粒度最多可获取最近1,440条
// 交易所支持的粒度直接返回，否则返回 1H 线
func (this *OKEx) getCandles(symbol string, periodHour, num int) (candles []MarketCandle, er error) {
	bar := "1H" // 支持的时间粒度 1m/3m/5m/15m/30m/1H/2H/4H/6H/12H/1D/2D/3D/1W/1M/3M
	if periodHour == 2 {
		bar = "2H"
	} else if periodHour == 4 {
		bar = "4H"
	} else if periodHour == 6 {
		bar = "6Hutc"
	} else if periodHour == 12 {
		bar = "12Hutc"
	} else if periodHour == 24 {
		bar = "1Dutc"
	}
	fromTime := time.Now().Add(-time.Hour * time.Duration(periodHour*num)) // 当前时间 - num * PeriodHour 个小时
	fromTs := fromTime.Unix() * 1000
	var after int64

	for {
		if res, err := this.fetchMarketCandles(symbol, bar, after); err != nil {
			er = fmt.Errorf("fetch market candles err: %s", err)
			return
		} else {
			end := true
			for _, candle := range res {
				ts, _ := strconv.ParseInt(candle[0], 10, 64)
				this, _ := strconv.ParseFloat(candle[1], 64)
				h, _ := strconv.ParseFloat(candle[2], 64)
				l, _ := strconv.ParseFloat(candle[3], 64)
				c, _ := strconv.ParseFloat(candle[4], 64)
				candles = append(candles, MarketCandle{ts, this, h, l, c})

				end = ts <= fromTs
				after = ts
			}
			if end {
				break
			}
		}
	}
	if len(candles) == 0 {
		er = errors.New("get kline history error, empty result")
		return
	}
	return
}

func (this *OKEx) fetchAccountConfig() (*AccountConfig, error) {
	config := &AccountConfig{}
	if err := this.sendHTTPRequest(resty.MethodGet, "/api/v5/account/config", nil, config, true, false); err != nil {
		this.Errorf("fetch account config err: %s", err)
		return nil, err
	}
	return config, nil
}

func (this *OKEx) setPositionMode(mode PosMode) error {
	return this.sendHTTPRequest(resty.MethodPost, "/api/v5/account/set-position-mode", map[string]PosMode{"posMode": mode}, nil, true, false)
}

type accountData struct {
	Details []AccountBalance `json:"details"`
	TotalEq string           `json:"totalEq"`
}

func (this *OKEx) fetchAccountBalance() (bs []AccountBalance, _ error) {
	path := "/api/v5/account/balance"
	var data accountData
	if err := this.sendHTTPRequest(resty.MethodGet, path, nil, &data, true, false); err != nil {
		return bs, err
	}
	bs = data.Details
	return bs, nil
}

func (this *OKEx) fetchLeverageInfo(symbol string) (*[]LeverageInfo, error) {
	path := fmt.Sprintf("/api/v5/account/leverage-info?instId=%s&mgnMode=isolated", symbol)
	lever := []LeverageInfo{}
	return &lever, this.sendHTTPRequest(resty.MethodGet, path, nil, &lever, true, true)
}

// okex 调整杠杠率、保证金需要将当前挂单全部取消
func (this *OKEx) setLeverage(symbol string, leverage float64, posSide PosSide, marginMode exchange.MarginMode) error {
	if leverage < this.MinLeverage {
		leverage = this.MinLeverage
	} else if leverage > this.getInstrument(symbol).MaxLeverage {
		leverage = this.getInstrument(symbol).MaxLeverage
	}

	req := map[string]string{
		"instId":  symbol,
		"lever":   strconv.FormatFloat(leverage, 'f', -1, 64),
		"mgnMode": "isolated",
		"posSide": string(posSide),
	}
	if marginMode == exchange.Cross {
		req["mgnMode"] = "cross"
	}
	this.Debugf("(%s) set leverage: %v", symbol, req)
	err := this.sendHTTPRequest(resty.MethodPost, "/api/v5/account/set-leverage", req, nil, true, false)
	if err != nil {
		this.Errorf("(%s) set leverage err: %s", symbol, err)
		return err
	}
	return nil
}

func convertPositionSideToOKSide(side exchange.PositionSide) PosSide {
	if side == exchange.PositionSideLong {
		return Long
	} else {
		return Short
	}
}

// symbol 可选，传空查所有
func (this *OKEx) fetchPositions(instrumentType exchange.InstrumentType, symbol string) (positions []*exchange.Position, _ error) {
	path := fmt.Sprintf("/api/v5/account/positions?instId=%s", symbol)
	okPositions := []Position{}
	if err := this.sendHTTPRequest(resty.MethodGet, path, nil, &okPositions, true, true); err != nil {
		this.Errorf("(%s) get position err: %s", symbol, err)
		return positions, err
	}

	for _, okPosition := range okPositions {
		qty, _ := strconv.ParseFloat(okPosition.Pos, 64)
		if qty == 0 {
			continue
		}

		instrumentID := okPosition.InstrumentID
		if instrumentType == exchange.USDXMarginedFutures {
			if !strings.Contains(instrumentID, "-USDT-") && !strings.Contains(instrumentID, "-USDC-") {
				continue
			}
		} else {
			if !strings.Contains(instrumentID, "-USD-") {
				continue
			}
		}

		p := exchange.Position{}
		p.InstrumentType = instrumentType
		p.ExchangeName = exchange.OKEx
		p.Symbol = okPosition.InstrumentID
		if okPosition.PosSide == Short {
			p.Qty = -qty
			p.Side = exchange.PositionSideShort
		} else {
			p.Qty = qty
			p.Side = exchange.PositionSideLong
		}
		p.EntryPrice, _ = strconv.ParseFloat(okPosition.AvgPx, 64)
		p.MarkPrice, _ = strconv.ParseFloat(okPosition.MarkPx, 64)
		p.Leverage, _ = strconv.ParseFloat(okPosition.Lever, 64)
		p.LiquidationPrice, _ = strconv.ParseFloat(okPosition.LiqPx, 64)
		p.UnrealisedPNL, _ = strconv.ParseFloat(okPosition.Upl, 64)
		p.Margin, _ = strconv.ParseFloat(okPosition.Margin, 64)
		positions = append(positions, &p)
	}
	return positions, nil
}

func (this *OKEx) cancelBatchOrders(symbol string, orderIDs []string) error {
	if len(orderIDs) == 0 {
		return nil
	}
	req := []map[string]string{}
	for _, orderID := range orderIDs {
		req = append(req, map[string]string{
			"instId": symbol,
			"ordId":  orderID,
		})
	}
	return this.sendHTTPRequest(resty.MethodPost, "/api/v5/trade/cancel-batch-orders", req, nil, true, false)
}

func (this *OKEx) cancelBatchAlgoOrders(symbol string, algoIDs []string) error {
	if len(algoIDs) == 0 {
		return nil
	}
	req := []map[string]string{}
	for _, algoId := range algoIDs {
		req = append(req, map[string]string{
			"instId": symbol,
			"algoId": algoId,
		})
	}
	return this.sendHTTPRequest(resty.MethodPost, "/api/v5/trade/cancel-algos", req, nil, true, false)
}

// 按将价格四舍五入到最小价格单位
func (this *OKEx) roundPrice(symbol string, price float64) float64 {
	return exchange.RoundPrice(price, this.getInstrument(symbol).TickSize)
}

// 未成交限价/市价订单列表
func (this *OKEx) fetchPendingOrders(symbol, ordType string) (orders []Order, _ error) {
	path := fmt.Sprintf("/api/v5/trade/orders-pending?ordType=%s&instId=%s", ordType, symbol)
	return orders, this.sendHTTPRequest(resty.MethodGet, path, nil, &orders, true, true)
}

func (this *OKEx) fetchOrderByID(symbol, orderID string) (orders Order, _ error) {
	path := fmt.Sprintf("/api/v5/trade/order?ordId=%s&instId=%s", orderID, symbol)
	return orders, this.sendHTTPRequest(resty.MethodGet, path, nil, &orders, true, false)
}

// 未完成策略委托单列表
func (this *OKEx) fetchAlgoPendingOrders(symbol string, ordType AlgoType, algoId string) (orders []AlgoOrder, _ error) {
	path := fmt.Sprintf("/api/v5/trade/orders-algo-pending?ordType=%s&instId=%s", ordType, symbol)
	if algoId != "" {
		path += fmt.Sprintf("&algoId=%s", algoId)
	}
	return orders, this.sendHTTPRequest(resty.MethodGet, path, nil, &orders, true, true)
}

// 历史策略委托单列表
// 【state和algoId必填且只能填其一】
func (this *OKEx) fetchAlgoHistoryOrders(symbol string, ordType AlgoType, state AlgoState, algoId string) (orders []AlgoOrder, _ error) {
	path := fmt.Sprintf("/api/v5/trade/orders-algo-history?ordType=%s&instId=%s", ordType, symbol)
	if state != "" {
		path += fmt.Sprintf("&state=%s", state)
	}
	if algoId != "" {
		path += fmt.Sprintf("&algoId=%s", algoId)
	}
	return orders, this.sendHTTPRequest(resty.MethodGet, path, nil, &orders, true, true)
}

// 转换 order 结构到通用结构
func convertAlgoOrderToExchangeOrder(algoOrder AlgoOrder, instrumentType exchange.InstrumentType) *exchange.Order {
	orderSide := exchange.OrderSideSell
	if algoOrder.Side == "buy" {
		orderSide = exchange.OrderSideBuy
	}
	var price, stopPx float64
	if algoOrder.IsOpenOrder() {
		price, _ = strconv.ParseFloat(algoOrder.OrdPx, 64)
		stopPx, _ = strconv.ParseFloat(algoOrder.TriggerPx, 64)
	} else {
		price, _ = strconv.ParseFloat(algoOrder.SlOrdPx, 64)
		stopPx, _ = strconv.ParseFloat(algoOrder.SlTriggerPx, 64)
		if stopPx == 0 {
			price, _ = strconv.ParseFloat(algoOrder.TpOrdPx, 64)
			stopPx, _ = strconv.ParseFloat(algoOrder.TpTriggerPx, 64)
		}
	}
	ts, _ := strconv.ParseFloat(algoOrder.CTime, 64)
	createdAt := time.Unix(int64(math.Round(ts/1000.0)), 0)
	ts, _ = strconv.ParseFloat(algoOrder.TriggerTime, 64)
	updatedAt := time.Unix(int64(math.Round(ts/1000.0)), 0)

	var orderStatus exchange.OrderStatus
	switch algoOrder.State {
	case "live":
		orderStatus = exchange.OrderStatusNew
	case "partially_filled":
		orderStatus = exchange.OrderStatusPartialFilled
	case "effective":
		orderStatus = exchange.OrderStatusTriggered
	case "filled":
		orderStatus = exchange.OrderStatusFilled
	case "canceled":
		orderStatus = exchange.OrderStatusCancelled
	case "order_failed":
		orderStatus = exchange.OrderStatusCancelled
	default:
		orderStatus = exchange.UnknownOrderStatus
	}

	var tradeMode exchange.TradeMode
	switch algoOrder.TdMode {
	case "isolated":
		tradeMode = exchange.TradeModeIsolated
	case "cross":
		tradeMode = exchange.TradeModeCross
	case "cash":
		tradeMode = exchange.TradeModeCash
	}

	order := &exchange.Order{
		InstrumentType: instrumentType,
		OrderID:        algoOrder.AlgoID,
		Symbol:         algoOrder.InstrumentID,
		Side:           orderSide,
		Type:           exchange.StopLimit,
		TradeMode:      tradeMode,
		Qty:            algoOrder.Size,
		Price:          price,
		TriggerPrice:   stopPx,
		Status:         orderStatus,
		ReduceOnly:     algoOrder.IsCloseOrder(),
		ExecQty:        0,
		ExecPrice:      0,
		CreateTime:     &createdAt,
		UpdateTime:     &updatedAt,
	}

	if order.IsTriggered() {
		order.SetString(exchange.ExtKeyTriggeredLimitOrderID, algoOrder.OrderID)
	}

	return order
}

func convertOrderToExchangeOrder(okOrder Order, instrumentType exchange.InstrumentType) *exchange.Order {
	order := &exchange.Order{}
	order.InstrumentType = instrumentType
	order.OrderID = okOrder.OrderID
	order.Symbol = okOrder.InstrumentID
	price, _ := strconv.ParseFloat(okOrder.Px, 64)
	order.Price = price
	amount, _ := strconv.ParseFloat(okOrder.Sz, 64)
	order.Qty = amount
	if okOrder.TgtCcy == "quote_ccy" {
		order.Qty = 0
		order.QuoteQty = amount
	}
	order.ExecQty, _ = strconv.ParseFloat(okOrder.AccFillSz, 64)
	order.ExecPrice, _ = strconv.ParseFloat(okOrder.AvgPx, 64)
	fee, _ := strconv.ParseFloat(okOrder.Fee, 64)
	order.Fee = fee
	order.FeeAsset = okOrder.FeeCcy

	ts, err := strconv.ParseInt(okOrder.CTime, 10, 64)
	if err == nil {
		t := time.Unix(ts/1000, 0)
		order.CreateTime = &t
	}
	ts, err = strconv.ParseInt(okOrder.UTime, 10, 64)
	if err == nil {
		ut := time.Unix(ts/1000, 0)
		order.UpdateTime = &ut
	}

	switch okOrder.OrdType {
	case "limit":
		order.Type = exchange.Limit
	case "market":
		order.Type = exchange.Market
	case "ioc":
		order.Type = exchange.Limit
		order.TimeInForce = exchange.IOC
	}

	if okOrder.Side == "buy" {
		order.Side = exchange.OrderSideBuy
	} else {
		order.Side = exchange.OrderSideSell
	}

	switch okOrder.State {
	case "live":
		order.Status = exchange.OrderStatusNew
	case "partially_filled":
		order.Status = exchange.OrderStatusPartialFilled
	case "effective":
		order.Status = exchange.OrderStatusTriggered
	case "filled":
		order.Status = exchange.OrderStatusFilled
	case "canceled":
		order.Status = exchange.OrderStatusCancelled
	default:
		order.Status = exchange.UnknownOrderStatus
	}

	switch okOrder.TdMode {
	case "isolated":
		order.TradeMode = exchange.TradeModeIsolated
	case "cross":
		order.TradeMode = exchange.TradeModeCross
	case "cash":
		order.TradeMode = exchange.TradeModeCash
	}

	return order
}

func (this *OKEx) getAlgoPendingOrders(symbol, algoID string) ([]AlgoOrder, error) {
	// 需要查找 Trigger && Conditional 两种类型的订单
	var orders []AlgoOrder
	if algoOrders, err := this.fetchAlgoPendingOrders(symbol, Trigger, algoID); err != nil {
		if algoID == "" || !strings.Contains(err.Error(), "51603") {
			// 按 ID 查找可能在其他类型订单里，不报错，继续查找
			return nil, err
		}
	} else {
		orders = append(orders, algoOrders...)
	}
	if algoOrders, err := this.fetchAlgoPendingOrders(symbol, Conditional, algoID); err != nil {
		if algoID == "" || !strings.Contains(err.Error(), "51603") {
			return nil, err
		}
	} else {
		orders = append(orders, algoOrders...)
	}
	return orders, nil
}

func (this *OKEx) getAlgoHistoryOrders(symbol string, state AlgoState, algoID string) ([]AlgoOrder, error) {
	// 需要查找 Trigger && Conditional 两种类型的订单
	var orders []AlgoOrder
	if algoOrders, err := this.fetchAlgoHistoryOrders(symbol, Trigger, state, algoID); err != nil {
		if algoID == "" || !strings.Contains(err.Error(), "51603") {
			return nil, fmt.Errorf("fetch trigger algo orders failed, error: %s", err)
		}
	} else {
		orders = append(orders, algoOrders...)
	}
	if algoOrders, err := this.fetchAlgoHistoryOrders(symbol, Conditional, state, algoID); err != nil {
		if algoID == "" || !strings.Contains(err.Error(), "51603") {
			return nil, fmt.Errorf("fetch conditional algo orders failed, error: %s", err)
		}
	} else {
		orders = append(orders, algoOrders...)
	}
	return orders, nil
}

func (this *OKEx) getMMR(instrument *Instrument, qty float64) (float64, error) {
	if instrument.positionTiers == nil {
		if err := this.cachePositionTiers(instrument); err != nil {
			return 0, err
		}
	}
	return instrument.GetMMR(qty), nil
}

func (this *OKEx) calculateMargin(symbol string, position *exchange.Position) (float64, error) {
	// 看多保证金 = (爆仓价 * (维持保证金率 + 平仓手续费率 - 1) + 开仓价) * 面值 * 张数
	// 看空保证金 = (爆仓价 * (维持保证金率 + 平仓手续费率 + 1) - 开仓价) * 面值 * 张数

	ins := this.getInstrument(symbol)
	mmr, err := this.getMMR(ins, position.Qty)
	if err != nil {
		return 0, err
	}
	ctVal := ins.CtVal
	posSide := position.Side
	entryPrice := position.EntryPrice
	liquidationPrice := position.LiquidationPrice
	takerFeeRate := 0.0005
	qty := math.Abs(position.Qty)
	if posSide == exchange.PositionSideLong {
		return (liquidationPrice*(mmr+takerFeeRate-1) + entryPrice) * ctVal * qty, nil
	} else {
		return (liquidationPrice*(mmr+takerFeeRate+1) - entryPrice) * ctVal * qty, nil
	}
}

func (this *OKEx) checkLiquidationPrice(instrumentType exchange.InstrumentType, symbol string, side exchange.PositionSide, deltaPrice, targetLiquidationPrice, acceptableDelta float64) (newDeltaPrice float64, _ error) {
	position, err := this.getPosition(instrumentType, symbol, side)
	if position == nil {
		return deltaPrice, fmt.Errorf("获取当前持仓失败: %s", err)
	}

	newDeltaPrice = math.Abs(targetLiquidationPrice - position.LiquidationPrice)
	if newDeltaPrice <= acceptableDelta {
		this.Infof("(%s) adjust liquidation success", symbol)
		return newDeltaPrice, nil
	}

	return newDeltaPrice, fmt.Errorf("调整爆仓价失败, 目标价: %.2f, 当前价: %.2f, 当前杠杠率: %.2f", targetLiquidationPrice, position.LiquidationPrice, position.Leverage)
}

func (this *OKEx) updateMargin(symbol string, posSide PosSide, way string, amt float64) error {
	req := map[string]string{
		"instId":  symbol,
		"posSide": string(posSide),
		"type":    way,
		"amt":     strconv.FormatFloat(amt, 'f', -1, 64),
	}
	this.Debugf("(%s) update margin args: %#v", symbol, req)
	resp := map[string]string{}
	err := this.sendHTTPRequest(resty.MethodPost, "/api/v5/account/position/margin-balance", req, &resp, true, false)
	this.Debugf("(%s) update margin resp: %#v", symbol, resp)
	return err
}

// 重新实时连接，用于断线重连
func (this *OKEx) reconnectLater() {
	if this.wsClosed {
		return
	}
	this.Infof("okex ws reconnect in 30 seconds...", this.ControllerID)
	time.Sleep(time.Second * 30)
	this.ConnectWebsocket([]exchange.InstrumentType{}, nil)
}

func (this *OKEx) subscribe() {
	subscribeMsg := `{"op": "subscribe", "args": [{"channel": "account"}, {"channel": "orders", "instType": "SPOT"}, {"channel": "orders", "instType": "FUTURES"}, {"channel": "orders", "instType": "SWAP"}, {"channel": "orders-algo", "instType": "FUTURES"}, {"channel": "orders-algo", "instType": "SWAP"}]}`
	err := this.sendWsMsg(subscribeMsg)
	if err != nil {
		this.Errorf("send subscribe msg err: %s", err)
	}
}

func (this *OKEx) sendWsMsg(msg string) error {
	this.wsMutex.Lock()
	defer this.wsMutex.Unlock()

	if this.wsConn == nil {
		return errors.New("wsConn is nil")
	}

	return this.wsConn.WriteMessage(websocket.TextMessage, []byte(msg))
}

// 实时连接用户登录
func (this *OKEx) wsLogin() (success bool) {
	apiSecret, apiPwd := this.apiKey.ApiSecret, this.apiKey.ApiPassword
	ts := time.Now().Unix()
	sign := this.signData(fmt.Sprintf("%vGET/users/self/verify", ts), string(apiSecret))
	msg := fmt.Sprintf(`{"op": "login", "args": [{"apiKey": "%s", "passphrase": "%s", "timestamp": "%v", "sign": "%s"}]}`, this.apiKey.ApiKey, apiPwd, ts, sign)
	err := this.sendWsMsg(msg)
	return err == nil
}

func (this *OKEx) onBalanceChange(data []any) {
	for _, d := range data {
		d, _ := d.(map[string]any)
		if d == nil {
			continue
		}
		detailData, _ := json.Marshal(d["details"])
		details := []AccountBalance{}
		json.Unmarshal(detailData, &details)
		for _, balance := range details {
			available, _ := strconv.ParseFloat(balance.Available, 64)
			bl, _ := strconv.ParseFloat(balance.Balance, 64)
			eq, _ := strconv.ParseFloat(balance.Eq, 64)
			if bl == 0 || eq == 0 {
				continue
			}
			margin := &exchange.UserMargin{}
			margin.Currency = balance.Currency
			margin.AvailableMargin = available
			margin.MarginBalance = eq
			margin.WalletBalance = bl
			this.MarginUpdatedCallback(margin, margin.Currency)
		}
	}
}

func (this *OKEx) onOrderChange(data []any) {
	for _, d := range data {
		d, _ := d.(map[string]any)
		if d == nil {
			continue
		}
		symbol := d["instId"].(string)
		if symbol != "" {
			exOrder := &exchange.Order{
				Symbol: symbol,
			}
			ins := this.getInstrument(symbol)
			if ins == nil {
				exOrder.InstrumentType = exchange.UnknownInstrumentType
			} else {
				exOrder.InstrumentType = ins.instrumentType
			}
			// TODO 完善订单字段
			this.OrderUpdatedCallback(exOrder)
		}
	}
}

func (this *OKEx) onTickers(data []any) {
	for _, d := range data {
		d, _ := d.(map[string]any)
		if d == nil {
			continue
		}
		data, _ := json.Marshal(d)
		ticker := Ticker{}
		json.Unmarshal(data, &ticker)

		ins := this.getInstrument(ticker.Symbol)
		if ins == nil {
			continue
		}

		exTicker := &exchange.Ticker{
			InstrumentType: ins.instrumentType,
			Time:           ticker.Time,
			Symbol:         ticker.Symbol,
			Close:          ticker.Last,
			Open:           ticker.Open24h,
			High:           ticker.High24h,
			Low:            ticker.Low24h,
			Volume:         ticker.Vol24h,
			Amount:         ticker.VolCcy24h,
		}
		this.StoreTickerCache(exTicker)
		this.CheckPriceTrigger(exTicker.InstrumentType, exTicker.Symbol, exTicker.Close, time.Unix(exTicker.Time/1000, 0))
	}
}

// 获取指定 ID 订单
// 可能在挂单接口，也可能在历史订单
// 另外 OK 对已经撤销的未成交单只保留2小时，所以可能是查不到的
func (this *OKEx) getAlgoOrder(instrumentType exchange.InstrumentType, symbol string, orderID string) (*exchange.Order, error) {
	var algoOrder *AlgoOrder

	if algoOrders, err := this.getAlgoHistoryOrders(symbol, "", orderID); err != nil {
		return nil, err
	} else {
		if len(algoOrders) == 1 {
			algoOrder = &algoOrders[0]
		}
	}

	if algoOrder == nil {
		if algoOrders, err := this.getAlgoPendingOrders(symbol, orderID); err != nil {
			return nil, err
		} else {
			if len(algoOrders) == 1 {
				algoOrder = &algoOrders[0]
			}
		}
	}

	if algoOrder != nil {
		order := convertAlgoOrderToExchangeOrder(*algoOrder, instrumentType)
		if len(algoOrder.OrderID) > 1 {
			// 查询限价单成交情况
			if limitOrder, err := this.fetchOrderByID(symbol, algoOrder.OrderID); err != nil {
				return nil, err
			} else {
				order.ExecQty, _ = strconv.ParseFloat(limitOrder.AccFillSz, 64)
				order.ExecPrice, _ = strconv.ParseFloat(limitOrder.AvgPx, 64)
				ts, _ := strconv.ParseFloat(limitOrder.UTime, 64)
				updatedAt := time.Unix(int64(math.Round(ts/1000.0)), 0)
				order.UpdateTime = &updatedAt

				// 直接使用限价单状态
				order.Status = convertOrderToExchangeOrder(limitOrder, instrumentType).Status
			}
		}
		return order, nil
	}

	return nil, fmt.Errorf("cannot find order[%s]", orderID)
}

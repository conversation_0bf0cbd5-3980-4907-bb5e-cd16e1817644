package hyperliquid

import (
	"fmt"
	"strings"

	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/secrets"
)

// Hyperliquid
type APISecret struct {
	exchange.BaseAPISecret
	agentAddress   string
	accountAddress string
	vaultAddress   string
}

func NewEmptyAPISecret() *APISecret {
	s := &APISecret{
		BaseAPISecret: exchange.BaseAPISecret{
			ExchangeName: exchange.Hyperliquid,
		},
	}
	s.Secretable = s
	return s
}

func NewAPISecret(apiKey string, apiSecret secrets.SecretString, isEncrypted bool) (*APISecret, error) {
	s := NewEmptyAPISecret()
	s.ApiKey = apiKey
	if isEncrypted {
		s.EncyptedApiSecret = apiSecret
		if err := s.Decrypt(); err != nil {
			return nil, err
		}
	} else {
		s.DecryptedApiSecret = apiSecret
	}
	if err := s.Decode(); err != nil {
		return nil, err
	}
	return s, nil
}

func (this *APISecret) Decode() error {
	if strings.HasPrefix(this.ApiKey, "HL:") {
		this.vaultAddress = strings.TrimLeft(this.ApiKey, "HL:")
	}
	// 格式: ApiSecret|||AgentAddress|||AccountAddress
	secretParts := strings.Split(string(this.DecryptedApiSecret), "|||")
	if len(secretParts) >= 3 {
		this.ApiSecret = secrets.SecretString(secretParts[0])
		this.agentAddress = secretParts[1]
		this.accountAddress = secretParts[2]
	} else {
		return fmt.Errorf("invalid api secret format, need 3 parts, got %d", len(secretParts))
	}
	return nil
}

func (this *APISecret) Encode() secrets.SecretString {
	this.BaseAPISecret.DecryptedApiSecret = secrets.SecretString(fmt.Sprintf("%s|||%s|||%s", this.BaseAPISecret.ApiSecret.Reveal(), this.agentAddress, this.accountAddress))
	return this.BaseAPISecret.DecryptedApiSecret
}

func (this *APISecret) Prompt() bool {
	accountAddress := exchange.SurveyInput("请输入主账号地址: ")
	this.accountAddress = accountAddress

	index, _ := exchange.SurveySelect("是否输入主账号私钥，以下情况：", []string{"1. 直接用于提币", "2. 不希望 API 过期（安全性更差）", "3. 以上都不是"})
	needWithdraw := index == 0
	needMasterSecret := index == 0 || index == 1

	subAccountAddress := ""
	if !needWithdraw {
		subAccountAddress = exchange.SurveyInput("请输入子账号地址(HL: 开头，可以留空): ")
	}

	if accountAddress == "" {
		fmt.Println("请输入主账号地址")
		return false
	}

	if subAccountAddress != "" {
		if !strings.HasPrefix(subAccountAddress, "HL:") {
			fmt.Println("请输入子账号地址，格式为 HL: 开头")
			return false
		}
		this.ApiKey = subAccountAddress
		this.vaultAddress = strings.TrimLeft(subAccountAddress, "HL:")
	} else {
		this.ApiKey = accountAddress
	}

	if needMasterSecret {
		apiSecret := exchange.SurveyPassword("请输入主账号的私钥: ")
		this.ApiSecret = secrets.SecretString(apiSecret)
		this.agentAddress = accountAddress
		if this.ApiSecret == "" {
			fmt.Println("请输入主账号的私钥")
			return false
		}
	} else {
		apiWalletAddress := exchange.SurveyInput("请输入 API wallet 地址: ")
		this.agentAddress = apiWalletAddress
		apiSecret := exchange.SurveyPassword("请输入 API wallet 私钥: ")
		this.ApiSecret = secrets.SecretString(apiSecret)
		if this.ApiSecret == "" || this.agentAddress == "" {
			fmt.Println("请输入 API wallet 地址和私钥")
			return false
		}
	}

	fmt.Printf("\n\n*** 请确认输入的API *** \n\nAPI Key: %s\nAPI Secret: %s\n\n",
		this.ApiKey,
		exchange.FormatPassword(string(this.ApiSecret)),
	)

	return true
}

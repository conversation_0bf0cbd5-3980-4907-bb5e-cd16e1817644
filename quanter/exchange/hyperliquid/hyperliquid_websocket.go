package hyperliquid

import (
	"errors"
	"fmt"
	"net/url"
	"time"

	"github.com/gorilla/websocket"
	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/exchange"
)

// 实时连接
func (h *Hyperliquid) ConnectWebsocket(instrumentTypes []exchange.InstrumentType, connectedCallback func(connected bool)) {
	if h.apiKey.ApiSecret == "" {
		// 密码还未设置,稍后再试
		go h.reconnectLater()
		return
	}

	hostURL := API_URL
	if h.IsTestnet {
		hostURL = TESTNET_API_URL
	}

	u := url.URL{Scheme: "wss", Host: hostURL, Path: "/ws"}
	h.Infof("connecting to %s", u.String())

	// 进行连接
	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		h.<PERSON>rrorf("ws dial err: %s", err)
		go h.reconnectLater()
		return
	}
	h.wsConn = c

	defer func() {
		// 函数运行结束时关闭连接
		c.Close()
		h.Infof("websocket closed")
	}()

	done := make(chan struct{}) // 连接是否结束

	h.initSubscribe()

	go func() {
		defer close(done)
		for {
			// 读取实时消息
			_, message, err := c.ReadMessage()
			if err != nil {
				h.Errorf("read message err: %s", err)

				go h.reconnectLater()
				return
			}
			if string(message) == `{"channel":"pong"}` {
				// 心跳消息
				continue
			}

			// h.Debugf("websocket recv: %s", message)

			data := gjson.Parse(string(message))
			h.handleWebsocketMessage(data)
		}
	}()

	h.subscribedSymbols = []string{}
	subscribeTicker := time.NewTicker(time.Second * 10)
	defer subscribeTicker.Stop()

	for {
		// 保存运行直到连接结束
		select {
		case <-subscribeTicker.C:
			h.sendWsMsg(`{ "method": "ping" }`)
			h.subscribeSymbols()
		case <-done:
			return
		}
	}
}

func (h *Hyperliquid) handleWebsocketMessage(msg gjson.Result) {
	channel := msg.Get("channel").String()
	data := msg.Get("data")
	switch channel {
	case "subscriptionResponse":
		h.Infof("subscription response: %s", data.String())
	case "user":
		h.Debugf("user events: %s", data.String())
	case "orderUpdates":
		orders := data.Array()
		for _, order := range orders {
			symbol := h.name2Symbol(order.Get("order.coin").String())
			if symbol != "" {
				ins := h.getInstrument(symbol)
				if ins == nil {
					continue
				}
				exOrder := &exchange.Order{
					Symbol:         symbol,
					InstrumentType: ins.instrumentType,
				}

				// TODO 完善订单字段
				h.OrderUpdatedCallback(exOrder)
			}
		}
	case "webData2":
		clearinghouseState := data.Get("clearinghouseState")
		accountValue := clearinghouseState.Get("marginSummary.accountValue").Float()
		totalMarginUsed := clearinghouseState.Get("marginSummary.totalMarginUsed").Float()
		unrealizedPnl := 0.0
		assetPositions := clearinghouseState.Get("assetPositions").Array()
		for _, assetPosition := range assetPositions {
			unrealizedPnl += assetPosition.Get("position.unrealizedPnl").Float()
		}

		margin := &exchange.UserMargin{
			WalletBalance:   accountValue - unrealizedPnl,
			MarginBalance:   accountValue,
			AvailableMargin: accountValue - totalMarginUsed,
			Currency:        DEFAULT_CURRENCY,
		}

		h.MarginUpdatedCallback(margin, margin.Currency)

	case "candle":
		ticker := data
		symbol := h.name2Symbol(ticker.Get("s").String())
		ins := h.getInstrument(symbol)
		if ins == nil {
			return
		}

		now := time.Now()
		exTicker := &exchange.Ticker{
			InstrumentType: ins.instrumentType,
			Time:           now.UnixMilli(),
			Symbol:         symbol,
			Close:          ticker.Get("c").Float(),
			Open:           ticker.Get("o").Float(),
			High:           ticker.Get("h").Float(),
			Low:            ticker.Get("l").Float(),
			Volume:         ticker.Get("v").Float(),
			Amount:         ticker.Get("n").Float(),
		}
		h.StoreTickerCache(exTicker)
		h.CheckPriceTrigger(exTicker.InstrumentType, exTicker.Symbol, exTicker.Close, now)
		// h.Debugf("ticker: %#v", exTicker)
	case "l2Book":
		// h.Debugf("order book: %s", data.String())
		symbol := h.name2Symbol(data.Get("coin").String())
		ins := h.getInstrument(symbol)
		if ins == nil {
			return
		}
		t := time.UnixMilli(data.Get("time").Int())
		orderBook := &exchange.OrderBook{
			InstrumentType: ins.instrumentType,
			Symbol:         symbol,
			Time:           &t,
			Asks:           []exchange.OrderBookLevel{},
			Bids:           []exchange.OrderBookLevel{},
		}
		bids := data.Get("levels.0").Array()
		for _, bid := range bids {
			orderBook.Bids = append(orderBook.Bids, exchange.OrderBookLevel{Price: bid.Get("px").Float(), Amount: bid.Get("sz").Float()})
		}
		asks := data.Get("levels.1").Array()
		for _, ask := range asks {
			orderBook.Asks = append(orderBook.Asks, exchange.OrderBookLevel{Price: ask.Get("px").Float(), Amount: ask.Get("sz").Float()})
		}
		h.StoreOrderBook(orderBook)
		if h.OrderBookCallback != nil {
			h.OrderBookCallback(orderBook)
		}
	default:
		h.Infof("unhandled channel: %s, data: %s", channel, data.String())
	}
}

func (h *Hyperliquid) initSubscribe() {
	subscriptions := []string{
		// `{"type": "allMids"}`,
		// fmt.Sprintf(`{"type": "userEvents", "user": "%s"}`, h.GetUserAddress()), // 只有订单成交通知，没有余额变动、订单取消等
		// fmt.Sprintf(`{"type": "notification", "user": "%s"}`, h.GetUserAddress()), // 没消息
		fmt.Sprintf(`{"type": "webData2", "user": "%s"}`, h.GetUserAddress()), // 有余额和其他很多数据，1次/s
		fmt.Sprintf(`{"type": "orderUpdates", "user": "%s"}`, h.GetUserAddress()),
	}

	for _, symbol := range h.subscribedOrderBookSymbols {
		ins := h.getInstrument(symbol)
		if ins == nil {
			continue
		}
		subscriptions = append(subscriptions, fmt.Sprintf(`{ "type": "l2Book", "coin": "%s" }`, ins.Name))
	}

	for _, subscription := range subscriptions {
		h.subscribe(subscription)
	}
}

func (h *Hyperliquid) subscribe(sub string) error {
	if h.wsConn == nil {
		return fmt.Errorf("can not subscribe when wsConn is nil")
	}
	msg := fmt.Sprintf(`{"method": "subscribe", "subscription": %s }`, sub)
	return h.sendWsMsg(msg)
}

func (this *Hyperliquid) sendWsMsg(msg string) error {
	this.wsMutex.Lock()
	defer this.wsMutex.Unlock()

	if this.wsConn == nil {
		return errors.New("wsConn is nil")
	}

	return this.wsConn.WriteMessage(websocket.TextMessage, []byte(msg))
}

func (h *Hyperliquid) reconnectLater() {
	if h.wsClosed {
		return
	}
	h.Infof("ws reconnect in 30 seconds...")
	time.Sleep(time.Second * 30)
	h.ConnectWebsocket([]exchange.InstrumentType{}, nil)
}

func (h *Hyperliquid) CloseWebsocket(stop bool) {
	if h.wsConn == nil {
		return
	}
	if stop {
		h.wsClosed = true
	}
	h.wsConn.Close()
}

func (h *Hyperliquid) subscribeSymbols() {
	if h.wsConn == nil {
		return
	}

	if !h.EnableRealtimePrice {
		return
	}

	symbolsNeed := []string{}
	for _, priceTrigger := range h.PriceTriggers {
		symbolsNeed = append(symbolsNeed, priceTrigger.Symbol)
	}

	for _, watch := range h.PriceWatches {
		symbolsNeed = append(symbolsNeed, watch.Symbol)
	}

	for _, symbol := range symbolsNeed {
		if !exchange.SliceContains(h.subscribedSymbols, symbol) {
			ins := h.getInstrument(symbol)
			if ins == nil {
				h.Errorf("subscribe symbol failed, instrument not found: %s", symbol)
				continue
			}
			msg := fmt.Sprintf(
				`{ "type": "candle", "coin": "%s", "interval": "1h" }`,
				ins.Name,
			)
			if err := h.subscribe(msg); err != nil {
				h.Errorf("subscribe msg error: %s", err)
			} else {
				h.subscribedSymbols = append(h.subscribedSymbols, symbol)
			}
		}
	}
}

func (h *Hyperliquid) SubscribeOrderBook(instrumentType exchange.InstrumentType, symbol string) {
	ins := h.getInstrument(symbol)
	if ins == nil {
		h.Errorf("subscribe order book failed, instrument not found: %s", symbol)
		return
	}
	h.subscribe(fmt.Sprintf(`{ "type": "l2Book", "coin": "%s" }`, ins.Name))
	if !exchange.SliceContains(h.subscribedOrderBookSymbols, symbol) {
		h.subscribedOrderBookSymbols = append(h.subscribedOrderBookSymbols, symbol)
	}
}

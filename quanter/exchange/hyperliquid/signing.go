package hyperliquid

import (
	"crypto/ecdsa"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/math"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/signer/core/apitypes"
	"github.com/vmihailenco/msgpack/v5"
	"github.com/wizhodl/quanter/secrets"
)

type Signature struct {
	R string `json:"r"`
	S string `json:"s"`
	V int    `json:"v"`
}

func signL1Action(secretPrivateKey secrets.SecretString, action any, activePool string, nonce int64, isTestnet bool) (*Signature, error) {
	// 解析私钥
	privateKeyStr := string(secretPrivateKey)
	if privateKeyStr[:2] == "0x" {
		privateKeyStr = privateKeyStr[2:]
	}
	privateKey, err := crypto.HexToECDSA(privateKeyStr)
	if err != nil {
		return nil, err
	}

	// zlog.Debugf("privateKey address: %s", crypto.PubkeyToAddress(privateKey.PublicKey).Hex())

	// 计算 action hash
	hash, err := actionHash(action, activePool, nonce)
	if err != nil {
		return nil, err
	}

	var connectionId [32]byte
	copy(connectionId[:], hash)
	source := "a"
	if isTestnet {
		source = "b"
	}

	typedData := apitypes.TypedData{
		Types: apitypes.Types{
			"Agent": []apitypes.Type{
				{Name: "source", Type: "string"},
				{Name: "connectionId", Type: "bytes32"},
			},
			"EIP712Domain": []apitypes.Type{
				{Name: "name", Type: "string"},
				{Name: "version", Type: "string"},
				{Name: "chainId", Type: "uint256"},
				{Name: "verifyingContract", Type: "address"},
			},
		},
		PrimaryType: "Agent",
		Domain: apitypes.TypedDataDomain{
			Name:              "Exchange",
			Version:           "1",
			ChainId:           (*math.HexOrDecimal256)(big.NewInt(1337)),
			VerifyingContract: "******************************************",
		},
		Message: map[string]interface{}{
			"source":       source,
			"connectionId": fmt.Sprintf("0x%x", connectionId[:]),
		},
	}

	return signInner(privateKey, typedData)
}

// action 要用结构定义不能用 map，否则可能因为字段顺序不同导致签名不一致
func actionHash(action any, vaultAddress string, nonce int64) ([]byte, error) {
	data, err := msgpack.Marshal(action)
	if err != nil {
		return nil, err
	}
	// zlog.Debugf("msgpack data: %s", hex.EncodeToString(data))

	nonceBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(nonceBytes, uint64(nonce))
	data = append(data, nonceBytes...)

	if vaultAddress == "" {
		data = append(data, 0x00)
	} else {
		data = append(data, 0x01)
		addressBytes := common.HexToAddress(vaultAddress).Bytes()
		data = append(data, addressBytes...)
	}

	// zlog.Debugf("data with vaultAddress: %s", hex.EncodeToString(data))

	hash := crypto.Keccak256Hash(data)

	// zlog.Debugf("action hash: %s", hash.Hex())

	return hash.Bytes(), nil
}

func signInner(privateKey *ecdsa.PrivateKey, data apitypes.TypedData) (*Signature, error) {
	// 打印原始数据
	// jsonBytes, err := json.Marshal(data)
	// if err != nil {
	// 	return nil, err
	// }
	// zlog.Debugf("Original JSON bytes: %s", string(jsonBytes))

	encodedData, err := typedDataHash(data)
	if err != nil {
		return nil, err
	}
	// zlog.Debugf("encoded data: %s", hex.EncodeToString(encodedData))

	// 签名
	sig, err := crypto.Sign(encodedData, privateKey)
	if err != nil {
		return nil, err
	}

	r := new(big.Int).SetBytes(sig[:32])
	s := new(big.Int).SetBytes(sig[32:64])
	v := int(sig[64]) + 27

	return &Signature{
		R: "0x" + hex.EncodeToString(r.Bytes()),
		S: "0x" + hex.EncodeToString(s.Bytes()),
		V: v,
	}, nil
}

func typedDataHash(data apitypes.TypedData) ([]byte, error) {
	// 根据 EIP-712 编码 TypedData
	domainSeparator, err := data.HashStruct("EIP712Domain", data.Domain.Map())
	if err != nil {
		return nil, err
	}
	messageHash, err := data.HashStruct(data.PrimaryType, data.Message)
	if err != nil {
		return nil, err
	}
	rawData := append([]byte("\x19\x01"), domainSeparator...)
	rawData = append(rawData, messageHash...)
	return crypto.Keccak256(rawData), nil
}

// 数字字符串去掉尾部的无效 0，否则会导致签名不一致
func trimTrailingZeros(number string) string {
	parts := strings.Split(number, ".")
	if len(parts) == 1 {
		return number
	}

	fracPart := strings.TrimRight(parts[1], "0")

	if fracPart == "" {
		return parts[0]
	}

	return parts[0] + "." + fracPart
}

func signWithdrawFromBridgeAction(secretPrivateKey secrets.SecretString, action any, isTestnet bool) (*Signature, error) {
	actionMap, ok := action.(map[string]any)
	if !ok {
		return nil, fmt.Errorf("action is not a map")
	}

	payloadTypes := []apitypes.Type{
		{Name: "hyperliquidChain", Type: "string"},
		{Name: "destination", Type: "string"},
		{Name: "amount", Type: "string"},
		{Name: "time", Type: "uint64"},
	}

	message := map[string]interface{}{
		"hyperliquidChain": actionMap["hyperliquidChain"],
		"destination":      actionMap["destination"],
		"amount":           actionMap["amount"],
		"time":             fmt.Sprintf("%v", actionMap["time"]),
	}
	return signUserSignedAction(secretPrivateKey, payloadTypes, "HyperliquidTransaction:Withdraw", message, isTestnet)
}

func signUsdClassTransferAction(secretPrivateKey secrets.SecretString, action TransferAction, isTestnet bool) (*Signature, error) {
	payloadTypes := []apitypes.Type{
		{Name: "hyperliquidChain", Type: "string"},
		{Name: "amount", Type: "string"},
		{Name: "toPerp", Type: "bool"},
		{Name: "nonce", Type: "uint64"},
	}

	message := map[string]interface{}{
		"hyperliquidChain": action.HyperliquidChain,
		"amount":           action.Amount,
		"toPerp":           action.ToPerp,
		"nonce":            fmt.Sprintf("%v", action.Nonce),
	}
	return signUserSignedAction(secretPrivateKey, payloadTypes, "HyperliquidTransaction:UsdClassTransfer", message, isTestnet)
}

func signUserSignedAction(secretPrivateKey secrets.SecretString, payloadTypes []apitypes.Type, primaryType string, message map[string]interface{}, isTestnet bool) (*Signature, error) {
	chainId := 42161
	if isTestnet {
		chainId = 421614
	}

	data := apitypes.TypedData{
		Types: apitypes.Types{
			primaryType: payloadTypes,
			"EIP712Domain": []apitypes.Type{
				{Name: "name", Type: "string"},
				{Name: "version", Type: "string"},
				{Name: "chainId", Type: "uint256"},
				{Name: "verifyingContract", Type: "address"},
			},
		},
		PrimaryType: primaryType,
		Domain: apitypes.TypedDataDomain{
			Name:              "HyperliquidSignTransaction",
			Version:           "1",
			ChainId:           (*math.HexOrDecimal256)(big.NewInt(int64(chainId))),
			VerifyingContract: "******************************************",
		},
		Message: message,
	}

	// 解析私钥
	privateKeyStr := string(secretPrivateKey)
	if privateKeyStr[:2] == "0x" {
		privateKeyStr = privateKeyStr[2:]
	}
	privateKey, err := crypto.HexToECDSA(privateKeyStr)
	if err != nil {
		return nil, err
	}

	return signInner(privateKey, data)
}

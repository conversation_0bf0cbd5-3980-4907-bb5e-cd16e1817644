package hyperliquid

import (
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/quanter/exchange"
)

type Hyperliquid struct {
	exchange.BaseExchange
	// accountAddress             string // main wallet Address 主钱包地址
	// vaultAddress               string // subaccount Address(vault 实现就是用的子账号) 子账号地址
	// agentAddress               string // api/agent address API钱包地址，用于查找对应 api key 的有效期
	apiExpireAt                *time.Time
	instruments                *xsync.MapOf[string, Instrument]
	name2Symbols               *xsync.MapOf[string, string]
	isCross                    bool
	wsConn                     *websocket.Conn
	wsMutex                    sync.Mutex
	wsClosed                   bool
	subscribedSymbols          []string
	subscribedOrderBookSymbols []string
	lastNonce                  atomic.Int64
	updatePriceMutex           sync.Mutex

	apiKey         *APISecret
	withdrawApiKey *APISecret
}

// API 里现货的符号（Name 字段）大都是 @1 这种格式，可读性很差，所以接口实现的 symbol 输入输出参数都需要格式化为: BASE/QUOTE
type Instrument struct {
	Name             string // 内部 API 符号
	Symbol           string // 接口实现对外用的符号，现货格式：BASE/QUOTE，期货与 Name 一致
	BaseCurrency     string
	QuoteCurrency    string
	SzDecimals       int
	MaxLeverage      int
	OnlyIsolated     bool
	MinSize          float64
	TickSize         float64
	FundingRate      float64
	OpenInterest     float64
	MarkPrice        float64
	IndexPrice       float64
	tickerUpdateTime int64
	assetIndex       int
	updatedAt        int64 // 更新时间
	instrumentType   exchange.InstrumentType
}

type ActionType string

const ActionTypeOrder ActionType = "order"
const ActionTypeCancelOrder ActionType = "cancel"
const ActionTypeUpdateLeverage ActionType = "updateLeverage"
const ActionTypeModifyOrder ActionType = "batchModify"
const ActionTypeUpdateIsolatedMargin ActionType = "updateIsolatedMargin"
const ActionTypeTransfer ActionType = "usdClassTransfer"

type Trigger struct {
	IsMarket  bool   `msgpack:"isMarket" json:"isMarket"`
	TriggerPx string `msgpack:"triggerPx" json:"triggerPx"`
	Tpsl      string `msgpack:"tpsl,omitempty" json:"tpsl,omitempty"`
}

type Limit struct {
	Tif string `msgpack:"tif" json:"tif"`
}

type OrderType struct {
	Limit   *Limit   `msgpack:"limit,omitempty" json:"limit,omitempty"`
	Trigger *Trigger `msgpack:"trigger,omitempty" json:"trigger,omitempty"`
}

type Order struct {
	AssetIndex int       `msgpack:"a" json:"a"`
	IsBuy      bool      `msgpack:"b" json:"b"`
	Price      string    `msgpack:"p" json:"p"`
	Size       string    `msgpack:"s" json:"s"`
	ReduceOnly bool      `msgpack:"r" json:"r"`
	OrderType  OrderType `msgpack:"t" json:"t"`
}

type OrderAction struct {
	Type     string  `msgpack:"type" json:"type"`
	Orders   []Order `msgpack:"orders" json:"orders"`
	Grouping string  `msgpack:"grouping" json:"grouping"`
}

type CancelArgs struct {
	AssetIndex int `msgpack:"a" json:"a"`
	OrderID    int `msgpack:"o" json:"o"`
}

type CancelOrderAction struct {
	Type    string       `msgpack:"type" json:"type"`
	Cancels []CancelArgs `msgpack:"cancels" json:"cancels"`
}

type UpdateLeverageAction struct {
	Type     string `msgpack:"type" json:"type"`
	Asset    int    `msgpack:"asset" json:"asset"`
	IsCross  bool   `msgpack:"isCross" json:"isCross"`
	Leverage int    `msgpack:"leverage" json:"leverage"`
}

type ModifyOrder struct {
	Oid   int   `msgpack:"oid" json:"oid"`
	Order Order `msgpack:"order" json:"order"`
}

type ModifyOrderAction struct {
	Type     string        `msgpack:"type" json:"type"`
	Modifies []ModifyOrder `msgpack:"modifies" json:"modifies"`
}

type UpdateIsolatedMarginAction struct {
	Type        string `msgpack:"type" json:"type"`
	Asset       int    `msgpack:"asset" json:"asset"`
	IsBuy       bool   `msgpack:"isBuy" json:"isBuy"` // this parameter won't have any effect until hedge mode is introduced
	AmountInUSD int    `msgpack:"ntli" json:"ntli"`
}

type TransferAction struct {
	Type             string `msgpack:"type" json:"type"`
	HyperliquidChain string `msgpack:"hyperliquidChain" json:"hyperliquidChain"`
	SignatureChainId string `msgpack:"signatureChainId" json:"signatureChainId"`
	Amount           string `msgpack:"amount" json:"amount"`
	ToPerp           bool   `msgpack:"toPerp" json:"toPerp"`
	Nonce            int64  `msgpack:"nonce" json:"nonce"`
}

type SubAccountTransferAction struct {
	Type           string `msgpack:"type" json:"type"`
	SubAccountUser string `msgpack:"subAccountUser" json:"subAccountUser"`
	IsDeposit      bool   `msgpack:"isDeposit" json:"isDeposit"`
	Usd            int    `msgpack:"usd" json:"usd"`
}

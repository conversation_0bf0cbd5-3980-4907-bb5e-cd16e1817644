package hyperliquid

import (
	"context"
	"errors"
	"fmt"
	"math"
	"math/big"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/puzpuzpuz/xsync/v2"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/crypto/core"
	"github.com/wizhodl/quanter/exchange"
)

const MIN_NOTIONAL = 10 // 小于该值的触发单可以下单成功，但触发后会被 reject
const DEFAULT_CURRENCY = "USDC"

var CurrencyFixes = map[string]string{
	"UBTC":  "BTC",
	"UETH":  "ETH",
	"USOL":  "SOL",
	"UPUMP": "PUMP",
}

/*
RateLimits:

The following rate limits apply per IP address:
--------------------------------

REST requests share an aggregated weight limit of 1200 per minute.
All documented exchange API requests have a weight of 1.
The following info requests have weight 2: l2Book, allMids, clearinghouseState, orderStatus, spotClearinghouseState, exchangeStatus.
All other documented info requests have weight 20.
All explorer API requests have a weight of 40.
Maximum of 100 websocket connections
Maximum of 1000 websocket subscriptions
Maximum of 10 unique users across user-specific websocket subscriptions
Maximum of 2000 inbound messages per minute across all websocket connections

Use websockets for lowest latency realtime data. See the python SDK for a full-featured example.

Address-based L1 Rate limits
---------
The L1 rate limiting logic will allow 1 requests per 1 USDC traded cumulatively since address inception.

Using an order value of 100 USDC, this only requires a fill rate of 1%.

Each address starts with an initial buffer of 10000 requests. When rate limited, an address will still be allowed one request every 10 seconds.

Cancels have cumulative limit min(limit + 100000, limit * 2) where limit is the default limit for other actions. This way, hitting the address-based rate limit will still allow open orders to be canceled.

Note that this rate limit only applies to L1 actions, not info requests.
*/

func NewHyperliquid(options *exchange.Options) (*Hyperliquid, error) {
	if options.ControllerID == "" {
		return nil, errors.New("controller id is required")
	}
	apiKey, err := NewAPISecret(options.ApiKey, options.ApiSecret, false)
	if err != nil {
		return nil, err
	}
	var withdrawApiKey *APISecret
	if options.WithdrawApiKey != "" && options.WithdrawApiSecret != "" {
		withdrawApiKey, err = NewAPISecret(options.WithdrawApiKey, options.WithdrawApiSecret, false)
		if err != nil {
			return nil, err
		}
	}
	h := &Hyperliquid{
		BaseExchange:   *exchange.NewBaseExchange(options),
		instruments:    xsync.NewMapOf[Instrument](),
		name2Symbols:   xsync.NewMapOf[string](),
		apiKey:         apiKey,
		withdrawApiKey: withdrawApiKey,
	}

	h.Exchange = h

	return h, nil
}

func (this *Hyperliquid) HasWithdrawAPI() bool {
	return this.withdrawApiKey != nil
}

// 除了 extraAgents 接口 user 参数必须用钱包地址 accountAddress，其他接口的都需要调用该方法获取实独立余额的地址
func (this *Hyperliquid) GetUserAddress() string {
	if this.apiKey.vaultAddress != "" {
		return this.apiKey.vaultAddress
	}
	return this.apiKey.accountAddress
}

func (this *Hyperliquid) IsMainAccountAgent() bool {
	return this.apiKey.agentAddress == this.apiKey.accountAddress
}

func (this *Hyperliquid) GetAPIExpireTime() (*time.Time, error) {
	if this.IsMainAccountAgent() {
		thisYearFirstDay := time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, time.Local)
		tenYearLater := thisYearFirstDay.Add(10 * 365 * 24 * time.Hour)
		return &tenYearLater, nil
	}

	if this.apiExpireAt != nil {
		return this.apiExpireAt, nil
	}
	res, err := this.info(map[string]string{"type": "extraAgents", "user": this.apiKey.accountAddress})
	if err != nil {
		return nil, err
	}
	this.Debugf("extraAgents res: %v", res)
	var validUntil int64
	resultArray := res.Array()
	for _, agent := range resultArray {
		agentAddress := agent.Get("address").String()
		if strings.EqualFold(agentAddress, this.apiKey.agentAddress) {
			validUntil = agent.Get("validUntil").Int()
			break
		}
	}
	if validUntil == 0 {
		this.Errorf("api expire time not found, res: %v", res)
		return nil, errors.New("api expire time not found")
	}
	t := time.UnixMilli(validUntil)
	this.apiExpireAt = &t
	return &t, nil
}

func (this *Hyperliquid) GetSupportedInstrumentTypes() []exchange.InstrumentType {
	return []exchange.InstrumentType{exchange.Spot, exchange.USDXMarginedFutures}
}

func (this *Hyperliquid) GetUserMargin(instrumentType exchange.InstrumentType, currency string) (*exchange.UserMargin, error) {
	res, err := this.info(map[string]string{"type": "clearinghouseState", "user": this.GetUserAddress()})
	if err != nil {
		return nil, err
	}

	accountValue := res.Get("marginSummary.accountValue").Float()
	totalMarginUsed := res.Get("marginSummary.totalMarginUsed").Float()
	unrealizedPnl := 0.0
	assetPositions := res.Get("assetPositions").Array()
	for _, assetPosition := range assetPositions {
		unrealizedPnl += assetPosition.Get("position.unrealizedPnl").Float()
	}

	margin := &exchange.UserMargin{
		WalletBalance:   accountValue - unrealizedPnl,
		MarginBalance:   accountValue,
		AvailableMargin: accountValue - totalMarginUsed,
		Currency:        currency,
	}
	return margin, nil
}

func (this *Hyperliquid) MaxLeverage(instrumentType exchange.InstrumentType, symbol string, value float64) float64 {
	ins := this.getInstrument(symbol)
	if ins == nil {
		this.Errorf("cannot find instrument %s", symbol)
		return 0
	}

	return float64(ins.MaxLeverage)
}

func (this *Hyperliquid) GetSymbolCodeUnit(symbolCode *exchange.SymbolCode) string {
	if symbolCode.InstrumentType() == exchange.Spot {
		return symbolCode.Coin()
	} else if symbolCode.InstrumentType() == exchange.USDXMarginedFutures {
		return symbolCode.Coin()
	} else {
		return ""
	}
}

func (this *Hyperliquid) Qty2Size(instrumentType exchange.InstrumentType, symbol string, price float64, qty float64) (size float64, er error) {
	ins := this.getInstrument(symbol)
	if ins == nil {
		return 0, fmt.Errorf("cannot find instrument %s", symbol)
	}

	value := price * qty
	roundSize := math.Min(ins.TickSize, 0.01)
	return math.Round(value/roundSize) * roundSize, nil
}

func (this *Hyperliquid) Size2Qty(instrumentType exchange.InstrumentType, symbol string, price float64, size float64) (qty float64, er error) {
	ins := this.getInstrument(symbol)
	if ins == nil {
		return 0, fmt.Errorf("cannot find instrument %s", symbol)
	}

	if price == 0 {
		return 0, nil
	}

	qty = size / price
	stepSize := ins.MinSize
	qty = math.Round(qty/stepSize) * stepSize

	// 舍去 stepSize 后面的小数位
	stepPow := math.Pow(10, float64(exchange.DecimalBit(stepSize)))
	return float64(int(qty*stepPow)) / stepPow, nil
}

func (this *Hyperliquid) CalcPrice(instrumentType exchange.InstrumentType, symbol string, qty float64, size float64) (price float64, er error) {
	ins := this.getInstrument(symbol)
	if ins == nil {
		return 0, fmt.Errorf("cannot find instrument %s", symbol)
	}

	if qty == 0 {
		return 0, nil
	}

	price = size / qty
	tickSize := ins.TickSize
	return math.Round(price/tickSize) * tickSize, nil
}

// liq_price = price - side * margin_available / position_size / (1 - l * side)
// l = 1 / MAINTENANCE_LEVERAGE
// side = 1 for long and -1 for short
// margin_available (cross) = account_value - maintenance_margin_required
// margin_available (isolated) = isolated_margin - maintenance_margin_required
func (this *Hyperliquid) CalculateLeverage(position *exchange.Position) (float64, error) {
	side := 1.0
	if position.Side == exchange.PositionSideShort {
		side = -1
	}

	// initMargin := position.EntryPrice * math.Abs(position.Qty) / leverage
	// marginAvailable := initMargin - initMargin / 2
	// marginAvailable * side / math.Abs(position.Qty) / (1 - 1/leverage * side) = position.EntryPrice - position.LiquidationPrice
	// position.EntryPrice * math.Abs(position.Qty) / leverage / 2 * side / math.Abs(position.Qty) / (1 - 1/leverage * side) = position.EntryPrice - position.LiquidationPrice
	// position.EntryPrice / 2 * side / (leverage - 1 * side) = position.EntryPrice - position.LiquidationPrice
	leverage := position.EntryPrice/2*side/(position.EntryPrice-position.LiquidationPrice) + side
	return leverage, nil
}

func (this *Hyperliquid) SetLeverageToMax(instrumentType exchange.InstrumentType, symbol string, currentLeverage float64, positionSide exchange.PositionSide) bool {
	ins := this.getInstrument(symbol)
	if ins == nil {
		return false
	}

	if currentLeverage >= float64(ins.MaxLeverage) {
		return true
	}

	action := UpdateLeverageAction{
		Type:     string(ActionTypeUpdateLeverage),
		Asset:    ins.assetIndex,
		IsCross:  this.isCross,
		Leverage: ins.MaxLeverage,
	}
	res, err := this.exchange(action)
	if err != nil {
		this.Errorf("set leverage to max failed: %v", err)
		return false
	}
	if res.Get("status").String() != "ok" {
		this.Errorf("set leverage to max failed: %v", res)
		return false
	}
	return true
}

func (this *Hyperliquid) updateIsolatedMargin(symbol string, amount float64) error {
	if amount == 0 {
		return nil
	}
	ins := this.getInstrument(symbol)
	if ins == nil {
		return fmt.Errorf("cannot find instrument %s", symbol)
	}

	updateIsolatedMarginAction := UpdateIsolatedMarginAction{
		Type:        string(ActionTypeUpdateIsolatedMargin),
		Asset:       ins.assetIndex,
		AmountInUSD: floatToUSDInt(amount),
		IsBuy:       true,
	}

	res, err := this.exchange(updateIsolatedMarginAction)
	if err != nil {
		this.Errorf("update isolated margin failed: %v", err)
		return err
	}

	if res.Get("status").String() != "ok" {
		this.Errorf("update isolated margin failed: %v", res)
		return fmt.Errorf("update isolated margin failed: %v", res)
	}

	return nil
}

func (this *Hyperliquid) getPosition(instrumentType exchange.InstrumentType, symbol string) (*exchange.Position, error) {
	positions, err := this.GetPositions(instrumentType, symbol, false)
	if err != nil {
		return nil, err
	}
	if len(positions) == 0 {
		return nil, errors.New("position not found")
	}
	return positions[0], nil
}

func (this *Hyperliquid) debugAdjustLiquidationPrice(position *exchange.Position, targetLiquidationPrice float64) {
	deltaPrice := targetLiquidationPrice - position.LiquidationPrice
	this.Infof("%s adjust liquidation price: %s -> %s, delta: %s",
		position.Symbol,
		formatPrice(position.LiquidationPrice, position.InstrumentType, 0),
		formatPrice(targetLiquidationPrice, position.InstrumentType, 0),
		formatPrice(deltaPrice, position.InstrumentType, 0),
	)
}

// liq_price = price - side * margin_available / position_size / (1 - l * side)
// l = 1 / MAINTENANCE_LEVERAGE
// side = 1 for long and -1 for short
// margin_available (cross) = account_value - maintenance_margin_required
// margin_available (isolated) = isolated_margin - maintenance_margin_required
func (this *Hyperliquid) AdjustLiquidationPrice(instrumentType exchange.InstrumentType, symbol string, position *exchange.Position, targetLiquidationPrice float64, acceptableDelta float64) (deltaPrice float64, errMsg string) {
	// 直接设置最大杠杠率，然后调节保证金
	this.SetLeverageToMax(instrumentType, symbol, 0, position.Side)
	deltaPrice = math.Abs(targetLiquidationPrice - position.LiquidationPrice)

	side := 1.0
	if position.Side == exchange.PositionSideShort {
		side = -1
	}
	maxLeverage := this.MaxLeverage(instrumentType, symbol, 0)
	targetMargin := (position.EntryPrice - targetLiquidationPrice) * (1 - 1/maxLeverage*side) * math.Abs(position.Qty) / side
	minMargin := position.EntryPrice * math.Abs(position.Qty) / maxLeverage
	this.Infof("begin adjust liquidation price, position: %#v", position)
	this.Infof("target margin: %0.1f, current margin: %0.1f, min margin: %0.1f", targetMargin, position.Margin, minMargin)
	if targetMargin < minMargin {
		targetMargin = minMargin
	}

	position, err := this.getPosition(instrumentType, symbol)
	if err != nil {
		return deltaPrice, fmt.Sprintf("get position failed: %v", err)
	}

	deltaMargin := targetMargin - (position.Margin - position.UnrealisedPNL) // 因为 position.Margin 包含了 unrealisedPNL，所以这里需要减去

	// 实际调整发现用公式不能精确调整，后面用当前保证金的百分比来循环调整: 增加或减少当前保证金的一定比例
	// 当爆仓价朝着目标价调整时，百分比保持不变
	// 当爆仓价朝着目标价反方向调整时，百分比降为原来的一半
	MIN_STEP := 0.001
	marginStepPercent := 0.02
	lastDeltaPriceFlag := targetLiquidationPrice > position.LiquidationPrice
	for i := 0; i < 20; i++ {
		this.debugAdjustLiquidationPrice(position, targetLiquidationPrice)
		this.Debugf("update %s margin %0.4f", symbol, deltaMargin)
		if err := this.updateIsolatedMargin(symbol, deltaMargin); err != nil {
			this.Errorf("update %s margin %0.4f err: %s", symbol, deltaMargin, err)
		}

		position, err = this.getPosition(instrumentType, symbol)
		if err != nil {
			return deltaPrice, fmt.Sprintf("get position failed: %v", err)
		}

		deltaPrice = math.Abs(targetLiquidationPrice - position.LiquidationPrice)

		if deltaPrice <= acceptableDelta {
			this.Infof("%s adjust liquidation success", symbol)
			return deltaPrice, ""
		}

		needAdd := true
		if position.Side == exchange.PositionSideLong {
			if targetLiquidationPrice > position.LiquidationPrice {
				// 需要减保证金
				needAdd = false
			}
		} else {
			if targetLiquidationPrice < position.LiquidationPrice {
				// 需要减保证金
				needAdd = false
			}
		}

		deltaPriceFlag := targetLiquidationPrice > position.LiquidationPrice
		if lastDeltaPriceFlag != deltaPriceFlag {
			marginStepPercent /= 2
			marginStepPercent = math.Max(marginStepPercent, MIN_STEP)
		}

		if needAdd {
			deltaMargin = position.Margin * marginStepPercent
		} else {
			deltaMargin = -position.Margin * marginStepPercent
		}

		lastDeltaPriceFlag = deltaPriceFlag
		time.Sleep(time.Second)
	}

	return deltaPrice, fmt.Sprintf("调整爆仓价失败, 目标价: %.2f, 当前价: %.2f, 当前杠杠率: %.2f", targetLiquidationPrice, position.LiquidationPrice, position.Leverage)
}

// 更新订单
func (this *Hyperliquid) UpdateOrder(origOrder exchange.Order, args *exchange.UpdateOrderArgs) (*exchange.Order, error) {
	ins := this.getInstrument(origOrder.Symbol)
	if ins == nil {
		return nil, fmt.Errorf("cannot find instrument %s", origOrder.Symbol)
	}

	var orderType OrderType
	if args.Type == exchange.Limit {
		tif := "Gtc"
		// Tif = Union[Literal["Alo"], Literal["Ioc"], Literal["Gtc"]]
		if origOrder.TimeInForce == exchange.IOC {
			tif = "Ioc"
		}
		orderType = OrderType{
			Limit: &Limit{
				Tif: tif,
			},
		}
	} else {
		isMarket := false
		if args.Type == exchange.Market {
			isMarket = true
		}

		triggerPx := formatPrice(args.TriggerPrice, args.InstrumentType, ins.SzDecimals)
		args.TriggerPrice, _ = strconv.ParseFloat(triggerPx, 64) // 返回订单数据保持一致

		orderType = OrderType{
			Trigger: &Trigger{
				IsMarket:  isMarket,
				TriggerPx: triggerPx,
				Tpsl:      "sl",
			},
		}
	}

	isBuy := true
	if origOrder.Side == exchange.OrderSideSell {
		isBuy = false
	}

	if args.OrderQty == 0 {
		args.OrderQty = origOrder.Qty - origOrder.ExecQty
	}

	value := args.Price * args.OrderQty
	if value < MIN_NOTIONAL {
		return nil, fmt.Errorf("order value less than min notional: %v < %v", value, MIN_NOTIONAL)
	}

	price := formatPrice(args.Price, args.InstrumentType, ins.SzDecimals)
	args.Price, _ = strconv.ParseFloat(price, 64) // 返回订单数据保持一致

	hOrder := Order{
		AssetIndex: ins.assetIndex,
		IsBuy:      isBuy,
		Price:      price,
		Size:       trimTrailingZeros(this.FormatQty(args.InstrumentType, origOrder.Symbol, args.OrderQty)),
		ReduceOnly: args.ReduceOnly,
		OrderType:  orderType,
	}

	action := ModifyOrderAction{
		Type: string(ActionTypeModifyOrder),
		Modifies: []ModifyOrder{
			{
				Oid:   cast.ToInt(origOrder.OrderID),
				Order: hOrder,
			},
		},
	}

	res, err := this.exchange(action)
	if err != nil {
		this.Errorf("modify order failed: %v", err)
		return nil, err
	}
	if res.Get("status").String() != "ok" {
		this.Errorf("modify order failed: %v", res)
		return nil, fmt.Errorf("modify order failed: %v", res)
	}

	orderID := res.Get("response.data.statuses.0.resting.oid").String()
	if orderID == "" {
		this.Errorf("no order id found: %v", res)
		err = fmt.Errorf("modify order failed: %v", res)
		return nil, err
	}

	nowTime := time.Now()
	return &exchange.Order{
		InstrumentType: args.InstrumentType,
		OrderID:        orderID,
		Symbol:         origOrder.Symbol,
		Price:          args.Price,
		TriggerPrice:   args.TriggerPrice,
		Qty:            args.OrderQty,
		Type:           args.Type,
		Side:           origOrder.Side,
		Status:         origOrder.Status,
		TimeInForce:    origOrder.TimeInForce,
		ReduceOnly:     args.ReduceOnly,
		CreateTime:     origOrder.CreateTime,
		UpdateTime:     &nowTime,
	}, nil
}

// 取消所有订单，返回是否调用成功
func (this *Hyperliquid) CancelAllOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string) ([]string, error) {
	openOrders, err := this.GetOpenOrders(instrumentType, orderType, symbol)
	if err != nil {
		return nil, err
	}

	var orderIDs []string
	for _, order := range openOrders {
		err := this.CancelOrder(instrumentType, orderType, symbol, order.OrderID)
		if err != nil {
			return orderIDs, err
		}
		orderIDs = append(orderIDs, order.OrderID)
	}

	return orderIDs, nil
}

// 获取进行中的订单
func (this *Hyperliquid) GetOpenOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string) ([]*exchange.Order, error) {
	res, err := this.info(map[string]string{"type": "frontendOpenOrders", "user": this.GetUserAddress()})
	if err != nil {
		return nil, err
	}

	var orders []*exchange.Order
	for _, hOrder := range res.Array() {
		orderSymbol := this.name2Symbol(hOrder.Get("coin").String())
		ins := this.getInstrument(orderSymbol)
		if ins == nil {
			this.Errorf("cannot find instrument %s", orderSymbol)
			continue
		}
		if instrumentType != exchange.UnknownInstrumentType && ins.instrumentType != instrumentType {
			continue
		}

		if symbol != "" && orderSymbol != symbol {
			continue
		}
		hOrderType := convertOrderType(hOrder.Get("orderType").String())
		if orderType != exchange.UnknownOrderType && orderType != hOrderType {
			continue
		}

		side := exchange.OrderSideBuy
		if hOrder.Get("side").String() == "A" {
			side = exchange.OrderSideSell
		}
		ts := hOrder.Get("timestamp").Int()
		createdAt := time.UnixMilli(ts)
		updatedAt := createdAt

		order := &exchange.Order{
			InstrumentType: instrumentType,
			Symbol:         orderSymbol,
			OrderID:        hOrder.Get("oid").String(),
			Side:           side,
			Type:           hOrderType,
			Qty:            hOrder.Get("origSz").Float(),
			Price:          hOrder.Get("limitPx").Float(),
			TriggerPrice:   hOrder.Get("triggerPx").Float(),
			Status:         exchange.OrderStatusNew, // 这里没有状态字段，都用默认值
			ReduceOnly:     hOrder.Get("reduceOnly").Bool(),
			ExecQty:        0,
			ExecPrice:      0,
			CreateTime:     &createdAt,
			UpdateTime:     &updatedAt,
		}
		orders = append(orders, order)
	}

	return orders, nil
}

func (this *Hyperliquid) GetInstrument(instrumentType exchange.InstrumentType, symbol string) (i *exchange.Instrument, _ error) {
	ins := this.getInstrument(symbol)
	if ins == nil {
		return i, fmt.Errorf("%s instrument not found", symbol)
	}
	i = &exchange.Instrument{}
	i.Symbol = ins.Symbol
	i.MinNotional = MIN_NOTIONAL
	i.TickSize = ins.TickSize
	i.LotSize = ins.MinSize
	i.MinSize = ins.MinSize
	i.MaxLeverage = float64(ins.MaxLeverage)
	i.SettleCurrency = DEFAULT_CURRENCY
	i.UnderlyCurrency = ins.BaseCurrency
	i.MarkPrice = ins.MarkPrice
	i.IndexPrice = ins.IndexPrice
	i.FundingRate = ins.FundingRate
	i.FundingRatePeriod = "1h"
	i.OpenInterest = ins.OpenInterest

	if ins.tickerUpdateTime > 0 {
		i.UpdateTime = time.Unix(ins.tickerUpdateTime, 0)
	} else {
		i.UpdateTime = time.Unix(ins.updatedAt, 0)
	}

	i.InstrumentType = ins.instrumentType

	return i, nil
}

func (this *Hyperliquid) GetLastPrice(instrumentType exchange.InstrumentType, symbol string, allowDelay bool) (float64, error) {
	res, err := this.info(map[string]string{"type": "allMids"})
	if err != nil {
		return 0, err
	}

	ins := this.getInstrument(symbol)
	if ins == nil {
		return 0, fmt.Errorf("cannot find instrument %s", symbol)
	}

	price := res.Get(ins.Name).Float()
	if price == 0 {
		return 0, fmt.Errorf("price not found")
	}
	return price, nil
}

func (this *Hyperliquid) CreateOrder(args exchange.CreateOrderArgs) (order *exchange.Order, _ error) {
	err := this.CheckQuoteQty(&args)
	if err != nil {
		return nil, fmt.Errorf("check order quoteQty failed, %v", err)
	}

	value := args.Price * args.Qty
	if args.Price != 0 && value < MIN_NOTIONAL {
		return nil, fmt.Errorf("order value less than min notional: %v < %v", value, MIN_NOTIONAL)
	}

	ins := this.getInstrument(args.Symbol)
	if ins == nil {
		return nil, fmt.Errorf("cannot find instrument %s", args.Symbol)
	}

	isBuy := true
	if args.Side == exchange.OrderSideSell {
		isBuy = false
	}

	var orderType OrderType
	if args.Type == exchange.Limit {
		tif := "Gtc"
		// Tif = Union[Literal["Alo"], Literal["Ioc"], Literal["Gtc"]]
		if args.TimeInForce == exchange.IOC {
			tif = "Ioc"
		}
		orderType = OrderType{
			Limit: &Limit{
				Tif: tif,
			},
		}
	} else if args.Type == exchange.Market {
		// 没有 Market 类型，需用 limit 单，其网站也是这样实现的
		orderType = OrderType{
			Limit: &Limit{
				Tif: "Ioc",
			},
		}
		if args.Price == 0 {
			price, err := this.GetLastPrice(args.InstrumentType, args.Symbol, false)
			if err != nil {
				return nil, err
			}
			if args.Side == exchange.OrderSideBuy {
				args.Price = price * 1.1
			} else {
				args.Price = price * 0.9
			}
		}
	} else {
		isMarket := false
		if args.Type == exchange.Market {
			isMarket = true
		}
		tpsl := "sl"
		if args.TriggerDirection != "" {
			if args.Side == exchange.OrderSideBuy && args.TriggerDirection == exchange.TriggerDirectionLower {
				tpsl = "tp"
			} else if args.Side == exchange.OrderSideSell && args.TriggerDirection == exchange.TriggerDirectionHigher {
				tpsl = "tp"
			}
		}

		triggerPx := formatPrice(args.TriggerPrice, args.InstrumentType, ins.SzDecimals)
		args.TriggerPrice, _ = strconv.ParseFloat(triggerPx, 64) // 返回订单数据保持一致

		orderType = OrderType{
			Trigger: &Trigger{
				IsMarket:  isMarket,
				TriggerPx: triggerPx,
				Tpsl:      tpsl,
			},
		}
	}

	args.Qty = this.FloorQty(args.InstrumentType, args.Symbol, args.Qty)
	price := formatPrice(args.Price, args.InstrumentType, ins.SzDecimals)
	args.Price, _ = strconv.ParseFloat(price, 64) // 返回订单数据保持一致

	hOrder := Order{
		AssetIndex: ins.assetIndex,
		IsBuy:      isBuy,
		Price:      price,
		Size:       trimTrailingZeros(this.FormatQty(args.InstrumentType, args.Symbol, args.Qty)),
		ReduceOnly: args.ReduceOnly,
		OrderType:  orderType,
	}

	if args.InstrumentType == exchange.Spot && hOrder.ReduceOnly {
		// Reduce-only is invalid for spot trading
		hOrder.ReduceOnly = false
	}

	action := OrderAction{
		Type:     "order",
		Orders:   []Order{hOrder},
		Grouping: "na",
	}

	res, err := this.exchange(action)
	if err != nil {
		this.Errorf("create order failed: %v", err)
		return nil, err
	}
	if res.Get("status").String() != "ok" {
		this.Errorf("create order failed: %v", res)
		return nil, fmt.Errorf("create order failed: %v", res)
	}
	orderID := res.Get("response.data.statuses.0.resting.oid").String()
	if orderID == "" {
		// 可能直接成交了
		orderID = res.Get("response.data.statuses.0.filled.oid").String()
	}

	if orderID == "" {
		this.Errorf("no order id found: %v", res)
		err = fmt.Errorf("create order failed: %v", res)
		return nil, err
	}
	this.Debugf("create order success, order id: %s", orderID)

	nowTime := time.Now()
	return &exchange.Order{
		InstrumentType: args.InstrumentType,
		OrderID:        orderID,
		Symbol:         args.Symbol,
		Price:          args.Price,
		TriggerPrice:   args.TriggerPrice,
		Qty:            args.Qty,
		QuoteQty:       args.QuoteQty,
		Type:           args.Type,
		TradeMode:      args.TradeMode,
		Side:           args.Side,
		Status:         exchange.OrderStatusNew,
		TimeInForce:    args.TimeInForce,
		ReduceOnly:     args.ReduceOnly,
		CreateTime:     &nowTime,
		UpdateTime:     &nowTime,
	}, nil
}

func (this *Hyperliquid) GetOrder(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol, orderID string, timeRange *exchange.TimeRange) (order *exchange.Order, _ error) {
	res, err := this.info(map[string]any{"type": "orderStatus", "user": this.GetUserAddress(), "oid": cast.ToInt(orderID)})
	if err != nil {
		return nil, err
	}

	hOrder := res.Get("order.order")
	if hOrder.Get("oid").String() != orderID {
		this.Errorf("order not found: %v", res)
		return nil, fmt.Errorf("order not found")
	}

	side := exchange.OrderSideBuy
	if hOrder.Get("side").String() == "A" {
		side = exchange.OrderSideSell
	}

	createdAt := time.UnixMilli(hOrder.Get("timestamp").Int())
	updatedAt := createdAt
	if res.Get("order.statusTimestamp").Int() > 0 {
		updatedAt = time.UnixMilli(res.Get("order.statusTimestamp").Int())
	}
	order = &exchange.Order{
		InstrumentType: instrumentType,
		Symbol:         symbol,
		OrderID:        hOrder.Get("oid").String(),
		Side:           side,
		Type:           orderType,
		Qty:            hOrder.Get("origSz").Float(),
		Price:          hOrder.Get("limitPx").Float(),
		TriggerPrice:   hOrder.Get("triggerPx").Float(),
		ReduceOnly:     hOrder.Get("reduceOnly").Bool(),
		ExecQty:        0,
		ExecPrice:      0,
		CreateTime:     &createdAt,
		UpdateTime:     &updatedAt,
	}

	status := res.Get("order.status").String()
	order.Status = convertOrderStatus(status)

	orderSz := hOrder.Get("sz").Float()
	if (order.Qty - orderSz) > 0 {
		order.ExecQty = order.Qty - orderSz
		if order.Status == exchange.UnknownOrderStatus {
			order.Status = exchange.OrderStatusPartialFilled
		} else if order.Status == exchange.OrderStatusCancelled {
			order.Status = exchange.OrderStatusPartialCancelled
		}
	}

	if order.ExecQty > 0 {
		// 查 Filled 订单的成交价
		order.ExecPrice, err = this.queryOrderExecPrice(order)
		if err != nil {
			return nil, err
		}
		if order.ExecPrice == 0 {
			this.Errorf("order exec price not found, use order price instead: %s", orderID)
			order.ExecPrice = order.Price
		}
	}

	return order, nil
}

var queryFillMutex sync.Mutex
var userFillsCache []gjson.Result

func (this *Hyperliquid) queryOrderExecPrice(order *exchange.Order) (execPrice float64, _ error) {
	queryFillMutex.Lock()
	defer queryFillMutex.Unlock()

	endTime := time.Now().UnixMilli()
	startTime := endTime - 7*24*3600*1000 // 7 天前

	if len(userFillsCache) > 0 {
		cacheStartTime := userFillsCache[0].Get("time").Int()
		cacheEndTime := userFillsCache[len(userFillsCache)-1].Get("time").Int()

		// 如果 order.UpdateTime 在 cache 的 time 之间，则直接从缓存中查询
		if order.UpdateTime.UnixMilli() >= cacheStartTime && order.UpdateTime.UnixMilli() <= cacheEndTime {
			return getFillAvgPrice(userFillsCache, order.OrderID), nil
		}

		// 否则从最后一条数据的时间戳开始查询, 减 1 小时容错
		startTime = cacheEndTime - 3600*1000
	}

	// 该接口每次最多返回 2000 条数据
	// 当大于等于 2000 条数据时，需要分页查询
	LIMIT := 2000
	for {
		fills, err := this.getUserFills(startTime, endTime)
		if err != nil {
			return 0, err
		}

		sort.Slice(fills, func(i, j int) bool {
			return fills[i].Get("time").Int() < fills[j].Get("time").Int()
		})

		// fills 与 userFillsCache 中可能有重复的订单，需要去重
		fills = removeDuplicateFills(fills, userFillsCache)

		userFillsCache = append(userFillsCache, fills...)

		// 如果数据量小于 LIMIT，说明无更多数据
		if len(fills) < LIMIT {
			break
		}

		startTime = fills[len(fills)-1].Get("time").Int() - 1000
	}

	// 最多保存 30 天的数据
	THIRTY_DAYS := int64(30 * 24 * 3600 * 1000)
	cutoffTime := endTime - THIRTY_DAYS
	var validFills []gjson.Result
	for _, fill := range userFillsCache {
		if fill.Get("time").Int() >= cutoffTime {
			validFills = append(validFills, fill)
		}
	}
	userFillsCache = validFills

	return getFillAvgPrice(userFillsCache, order.OrderID), nil
}

func (this *Hyperliquid) getUserFills(startTime int64, endTime int64) ([]gjson.Result, error) {
	res, err := this.info(map[string]any{
		"type":            "userFillsByTime",
		"user":            this.GetUserAddress(),
		"aggregateByTime": true,
		"endTime":         endTime,
		"startTime":       startTime,
	})
	if err != nil {
		return nil, err
	}
	return res.Array(), nil
}

func removeDuplicateFills(fills []gjson.Result, userFillsCache []gjson.Result) []gjson.Result {
	fillMap := make(map[string]struct{})
	for _, fill := range userFillsCache {
		fillMap[fill.Get("hash").String()] = struct{}{}
	}

	var uniqueFills []gjson.Result
	for _, fill := range fills {
		if _, exists := fillMap[fill.Get("hash").String()]; !exists {
			uniqueFills = append(uniqueFills, fill)
		}
	}

	return uniqueFills
}

func getFillAvgPrice(fills []gjson.Result, orderID string) float64 {
	orderValue := 0.0
	orderSz := 0.0
	for _, fill := range fills {
		if fill.Get("oid").String() != orderID {
			continue
		}
		orderSz += fill.Get("sz").Float()
		orderValue += (fill.Get("sz").Float() * fill.Get("px").Float())
	}

	if orderSz == 0 {
		return 0
	}

	return orderValue / orderSz
}

func (this *Hyperliquid) CancelOrder(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol, orderID string) error {
	ins := this.getInstrument(symbol)
	if ins == nil {
		return fmt.Errorf("cannot find instrument %s", symbol)
	}

	cancelAction := CancelOrderAction{
		Type: "cancel",
		Cancels: []CancelArgs{
			{
				AssetIndex: ins.assetIndex,
				OrderID:    cast.ToInt(orderID),
			},
		},
	}

	res, err := this.exchange(cancelAction)
	if err != nil {
		this.Errorf("cancel order failed: %v", err)
		return err
	}
	if res.Get("status").String() != "ok" {
		this.Errorf("cancel order failed: %v", res)
		return fmt.Errorf("cancel order failed: %v", res)
	}
	return nil
}

func (this *Hyperliquid) GetAccountConfig(instrumentType exchange.InstrumentType) (config *exchange.AccountConfig, _ error) {
	return &exchange.AccountConfig{
		MarginMode:       exchange.AccountMarginModeIsolated,
		DualPositionSide: false,
	}, nil
}

func (this *Hyperliquid) GetAccountBalances(instrumentType exchange.InstrumentType) (accountBalances []*exchange.AccountBalance, err error) {
	if instrumentType == exchange.Spot {
		// ratelimit weight: 2
		res, err := this.info(map[string]string{"type": "spotClearinghouseState", "user": this.GetUserAddress()})
		if err != nil {
			return nil, err
		}

		balances := res.Get("balances").Array()
		for _, balance := range balances {
			total := balance.Get("total").Float()
			hold := balance.Get("hold").Float()
			coin := balance.Get("coin").String()
			fixedCoin, ok := CurrencyFixes[coin]
			if ok {
				coin = fixedCoin
			}

			accountBalances = append(accountBalances, &exchange.AccountBalance{
				InstrumentType: instrumentType,
				Currency:       coin,
				Total:          total,
				Available:      total - hold,
			})
		}

	} else {
		// ratelimit weight: 2
		res, err := this.info(map[string]string{"type": "clearinghouseState", "user": this.GetUserAddress()})
		if err != nil {
			return nil, err
		}

		accountValue := res.Get("marginSummary.accountValue").Float()
		totalMarginUsed := res.Get("marginSummary.totalMarginUsed").Float()

		accountBalances = append(accountBalances, &exchange.AccountBalance{
			InstrumentType: instrumentType,
			Currency:       DEFAULT_CURRENCY,
			Total:          accountValue,
			Available:      accountValue - totalMarginUsed,
		})
	}

	return
}

func (this *Hyperliquid) GetAccountCurrencies(instrumentType exchange.InstrumentType) (currencies []string, _ error) {
	this.instruments.Range(func(symbol string, i Instrument) bool {
		currencies = append(currencies, i.BaseCurrency)
		return true
	})
	return
}

func (this *Hyperliquid) GetPositions(instrumentType exchange.InstrumentType, symbol string, allowCache bool) (positions []*exchange.Position, er error) {
	// ratelimit weight: 2
	res, err := this.info(map[string]string{"type": "clearinghouseState", "user": this.GetUserAddress()})
	if err != nil {
		return nil, err
	}

	for _, assetPosition := range res.Get("assetPositions").Array() {
		rawPos := assetPosition.Get("position")
		sz := rawPos.Get("szi").Float()
		if sz == 0 {
			continue
		}

		p := exchange.Position{}
		p.InstrumentType = instrumentType
		p.ExchangeName = this.GetName()
		p.Symbol = rawPos.Get("coin").String()
		if symbol != "" && p.Symbol != symbol {
			continue
		}
		p.Qty = sz
		if sz < 0 {
			p.Side = exchange.PositionSideShort
		} else {
			p.Side = exchange.PositionSideLong
		}
		p.EntryPrice = rawPos.Get("entryPx").Float()
		posValue := rawPos.Get("positionValue").Float()
		p.MarkPrice = posValue / math.Abs(sz)
		p.Leverage = rawPos.Get("leverage.value").Float()
		p.LiquidationPrice = rawPos.Get("liquidationPx").Float()
		p.UnrealisedPNL = rawPos.Get("unrealizedPnl").Float()
		p.Margin = rawPos.Get("marginUsed").Float()
		positions = append(positions, &p)
	}

	return
}

func (this *Hyperliquid) SetDualPositionSide(instrumentType exchange.InstrumentType, dualPositionSide bool) error {
	return exchange.ErrNotImplemented
}

func (this *Hyperliquid) SetMarginMode(instrumentType exchange.InstrumentType, symbol string, mode exchange.MarginMode) error {
	ins := this.getInstrument(symbol)
	if ins == nil {
		return fmt.Errorf("cannot find instrument %s", symbol)
	}

	this.isCross = mode == exchange.Cross
	action := UpdateLeverageAction{
		Type:     string(ActionTypeUpdateLeverage),
		Asset:    ins.assetIndex,
		IsCross:  mode == exchange.Cross,
		Leverage: ins.MaxLeverage,
	}
	res, err := this.exchange(action)
	if err == nil && res.Get("status").String() != "ok" {
		err = fmt.Errorf("set margin mode failed: %v", res)
	}
	return err
}

func (this *Hyperliquid) QueryFundingHistory(instrumentType exchange.InstrumentType, symbol string, limit int, from time.Time, to time.Time) (his []*exchange.FundingHistory, er error) {
	var startTime, endTime int64
	if from.IsZero() {
		// 看起来最多只能查询 20 天内的数据
		startTime = time.Now().Add(-time.Hour * 24 * 20).UnixMilli()
	} else {
		startTime = from.UnixMilli()
	}

	data := map[string]any{"type": "fundingHistory", "coin": symbol, "startTime": startTime}
	if !to.IsZero() {
		endTime = to.UnixMilli()
		if startTime > endTime {
			return nil, errors.New("startTime cannot be after endTime")
		}
		data["endTime"] = endTime
	}
	res, err := this.info(data)
	if err != nil {
		return nil, fmt.Errorf("send funding history http request failed, error: %v", err)
	}

	_, code, err := this.TranslateFutureSymbol(instrumentType, symbol, "")
	if err != nil {
		return his, err
	}

	for _, raw := range res.Array() {
		his = append(his, &exchange.FundingHistory{
			Code:     code,
			Exchange: this.GetName(),
			Symbol:   raw.Get("coin").String(),
			Rate:     raw.Get("fundingRate").Float(),
			Time:     time.UnixMilli(raw.Get("time").Int()),
			Period:   "1h",
		})
	}
	return
}

func (this *Hyperliquid) SetLeverage(instrumentType exchange.InstrumentType, symbol string, marginMode exchange.MarginMode, side exchange.PositionSide, leverage float64) error {
	ins := this.getInstrument(symbol)
	if ins == nil {
		return fmt.Errorf("cannot find instrument %s", symbol)
	}

	this.isCross = marginMode == exchange.Cross
	action := UpdateLeverageAction{
		Type:     string(ActionTypeUpdateLeverage),
		Asset:    ins.assetIndex,
		IsCross:  marginMode == exchange.Cross,
		Leverage: int(math.Ceil(leverage)),
	}
	res, err := this.exchange(action)
	if err == nil && res.Get("status").String() != "ok" {
		err = fmt.Errorf("set leverage failed: %v", res)
	}
	return err
}

func (this *Hyperliquid) GetLeverage(instrumentType exchange.InstrumentType, symbol string, marginMode exchange.MarginMode) (leverageLong, leverageShort float64, err error) {
	return 0, 0, exchange.ErrNotImplemented
}

// code -> spotSymbol, spotCode, futureSymbol
var CodeToSymbolFixes = map[string][3]string{
	// "BTC00.U":   {"UBTC/USDC", "BTC--", "BTC"},
	// "BTC--":     {"UBTC/USDC", "BTC--", "BTC"},
	"KPEPE00.U": {"", "", "kPEPE"},
}

func (this *Hyperliquid) TranslateSymbolCode(symbolCode *exchange.SymbolCode) (spotAndFutureSymbols []*exchange.SymbolPair, er error) {
	spotSymbol, _, futureSymbol, fixed := this.FixSymbolCode(CodeToSymbolFixes, symbolCode)
	if fixed {
		spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(symbolCode, spotSymbol, futureSymbol))
		return
	}

	this.instruments.Range(func(symbol string, i Instrument) bool {
		if i.instrumentType != exchange.Spot {
			return true
		}

		if symbolCode.Coin() == i.BaseCurrency && DEFAULT_CURRENCY == i.QuoteCurrency {
			spotSymbol = symbol
		}
		return true
	})

	if symbolCode.IsFuture() {
		this.instruments.Range(func(symbol string, i Instrument) bool {
			if !i.instrumentType.IsFuture() {
				return true
			}

			if symbolCode.Coin() == i.Name && symbolCode.InstrumentType() == i.instrumentType {
				spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(symbolCode, spotSymbol, symbol))
			}
			return true
		})
	} else {
		spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(symbolCode, spotSymbol, ""))
	}
	if len(spotAndFutureSymbols) == 0 {
		er = errors.New("symbol code not found")
		return
	}
	return
}

// futureSymbol -> spotSymbol, spotCode, futureCode
var SymbolToCodeFixes = map[string][3]string{
	// "BTC":   {"UBTC/USDC", "BTC--", "BTC00.U"},
	"kPEPE": {"", "", "KPEPE00.U"},
}

func (this *Hyperliquid) TranslateFutureSymbol(instrumentType exchange.InstrumentType, futureSymbol, uSymbol string) (spotSymbol string, futureCode *exchange.SymbolCode, er error) {
	var fixed bool
	spotSymbol, _, futureCode, fixed = this.FixFutureSymbol(SymbolToCodeFixes, instrumentType, futureSymbol, uSymbol)
	if fixed {
		return
	}

	this.instruments.Range(func(symbol string, i Instrument) bool {
		if i.instrumentType != instrumentType {
			return true
		}
		if symbol == futureSymbol && i.BaseCurrency == i.Name && instrumentType == exchange.USDXMarginedFutures {
			// 目前只有 U 永续
			code := fmt.Sprintf("%s00.U", i.BaseCurrency)
			futureCode = &exchange.SymbolCode{Code: code, USDXSymbol: uSymbol}
		}
		return true
	})

	if futureCode == nil {
		er = errors.New("future symbol not found")
		return
	}

	this.instruments.Range(func(symbol string, i Instrument) bool {
		if i.instrumentType != exchange.Spot {
			return true
		}
		if i.BaseCurrency == futureCode.Coin() && DEFAULT_CURRENCY == i.QuoteCurrency {
			spotSymbol = symbol
		}
		return true
	})

	return
}

// 更新交易对信息，force 时忽略缓存
func (this *Hyperliquid) CacheInstruments(force bool) error {
	res, err := this.info(map[string]string{"type": "meta"})
	if err != nil {
		return err
	}

	instrumentMap := map[string]Instrument{}
	for i, ins := range res.Get("universe").Array() {
		szDecimals := int(ins.Get("szDecimals").Int())
		minSize := math.Pow(10, -float64(szDecimals))
		name := ins.Get("name").String()

		instrumentMap[name] = Instrument{
			Name:           name,
			BaseCurrency:   name,
			SzDecimals:     szDecimals,
			MaxLeverage:    int(ins.Get("maxLeverage").Int()),
			OnlyIsolated:   ins.Get("onlyIsolated").Bool(),
			MinSize:        minSize,
			assetIndex:     i,
			updatedAt:      time.Now().Unix(),
			instrumentType: exchange.USDXMarginedFutures,
		}
	}

	res, err = this.info(map[string]string{"type": "spotMeta"})
	if err != nil {
		return err
	}

	tokens := res.Get("tokens").Array()
	for _, ins := range res.Get("universe").Array() {
		name := ins.Get("name").String()
		tokenIndexes := ins.Get("tokens").Array()
		token := tokens[tokenIndexes[0].Int()]
		quote := tokens[tokenIndexes[1].Int()]
		szDecimals := int(token.Get("szDecimals").Int())
		minSize := math.Pow(10, -float64(szDecimals))

		rawBaseCurrency := token.Get("name").String()

		// 检查 rawBaseCurrency 是否在 CurrencyFixes 的 values 中
		// 逻辑：如果原始 PUMP 品种可以正常使用，就不会有 UPUMP->PUMP 的修复了对吧？
		// 在这种情况下，原始 PUMP 品种与 UPUMP 修复存在冲突，所以我们暂时排除这个品种
		// 如果原始 PUMP 有用的话，我们可以为这种情况开发更高级的修复方案
		skipInstrument := false
		for _, value := range CurrencyFixes {
			if rawBaseCurrency == value {
				zlog.Warnf("Excluding spot instrument %s due to CurrencyFixes conflict with %s", name, value)
				skipInstrument = true
				break
			}
		}

		if skipInstrument {
			continue
		}

		baseCurrency := rawBaseCurrency
		fixedBaseCurrency, ok := CurrencyFixes[rawBaseCurrency]
		if ok {
			baseCurrency = fixedBaseCurrency
		}

		instrumentMap[name] = Instrument{
			Name:           name,
			BaseCurrency:   baseCurrency,
			QuoteCurrency:  quote.Get("name").String(),
			SzDecimals:     szDecimals,
			MinSize:        minSize,
			assetIndex:     int(ins.Get("index").Int()) + 10000,
			updatedAt:      time.Now().Unix(),
			instrumentType: exchange.Spot,
		}
	}

	// 没有直接的 tick size，通过读取价格小数位数来计算
	mids, err := this.info(map[string]string{"type": "allMids"})
	if err != nil {
		return err
	}

	for name := range mids.Map() {
		instrument, found := instrumentMap[name]
		if found {
			price := mids.Get(name).Float()
			instrument.TickSize = getTickSize(price, instrument.instrumentType)
			symbol := name
			if instrument.instrumentType == exchange.Spot {
				symbol = fmt.Sprintf("%s/%s", instrument.BaseCurrency, instrument.QuoteCurrency)
			}
			instrument.Symbol = symbol
			this.instruments.Store(symbol, instrument)
			this.name2Symbols.Store(name, symbol)
		}
	}

	go this.updateInstrumentPricesLoop()

	return nil
}

func (this *Hyperliquid) updateInstrumentPricesLoop() {
	if !this.updatePriceMutex.TryLock() {
		return
	}
	defer this.updatePriceMutex.Unlock()

	this.updateInstrumentPrices()

	t := time.NewTicker(time.Second * 5)
	for {
		if this.Removed.Load() {
			break
		}

		select {
		case <-t.C:
			this.updateInstrumentPrices()
		}
	}
}

// 更新合约 markprice 等价格信息
func (this *Hyperliquid) updateInstrumentPrices() {
	res, err := this.info(map[string]string{"type": "metaAndAssetCtxs"})
	if err != nil {
		this.Errorf("get meta and asset ctxs failed: %v", err)
		return
	}

	universe := res.Array()[0].Get("universe").Array()
	ctxs := res.Array()[1].Array()
	for i, ins := range universe {
		name := ins.Get("name").String()
		if len(ctxs) <= i {
			this.Errorf("asset ctxs not found for %s", name)
			break
		}
		assetCtx := ctxs[i]
		ins, found := this.instruments.Load(name)
		if !found {
			continue
		}
		ins.MarkPrice = assetCtx.Get("markPx").Float()
		ins.IndexPrice = assetCtx.Get("oraclePx").Float()
		ins.FundingRate = assetCtx.Get("funding").Float()
		ins.OpenInterest = assetCtx.Get("openInterest").Float()
		ins.tickerUpdateTime = time.Now().Unix()
		this.instruments.Store(name, ins)
	}
}

func (this *Hyperliquid) GetName() string {
	return exchange.Hyperliquid
}

// 获取并计算最新 K 线数据
func (this *Hyperliquid) GetLatestKLines(instrumentType exchange.InstrumentType, symbol string, periodHour, num int) (klines []*exchange.KLine, er error) {
	toTime := time.Now()                                               // 当前时间
	fromTime := toTime.Add(-time.Hour * time.Duration(periodHour*num)) // 当前时间 - num * PeriodHour 个小时

	ins := this.getInstrument(symbol)
	if ins == nil {
		return nil, fmt.Errorf("cannot find instrument %s", symbol)
	}

	req := map[string]any{
		"coin":      ins.Name,
		"interval":  "1h",
		"startTime": fromTime.UnixMilli(),
		"endTime":   toTime.UnixMilli(),
	}
	res, err := this.info(map[string]any{"type": "candleSnapshot", "req": req})
	if err != nil {
		return nil, err
	}

	// 并小时线为 PeriodHour 小时线
	// 取 PeriodHour 点，如 6小时线，取 0、6、12、18 点
	periodSeconds := int64(periodHour * 3600)
	klineCount := 0
	for _, candle := range res.Array() {
		ts := candle.Get("t").Int() / 1000
		if ts%periodSeconds == 0 {
			// 这根小时线是 PeriodHour 的开盘线
			klineCount++

			klines = append(klines, &exchange.KLine{
				Open:  candle.Get("o").Float(),
				Close: candle.Get("c").Float(),
				High:  candle.Get("h").Float(),
				Low:   candle.Get("l").Float(),
				Time:  ts,
			})
		} else if klineCount > 0 {
			// 非 PeriodHour 的开盘线
			klineIdx := klineCount - 1

			klines[klineIdx].Close = candle.Get("c").Float()
			klines[klineIdx].High = math.Max(klines[klineIdx].High, candle.Get("h").Float())
			klines[klineIdx].Low = math.Min(klines[klineIdx].Low, candle.Get("l").Float())
		}
	}

	return
}

func (this *Hyperliquid) TransferAsset(from, to exchange.InstrumentType, coin string, amount float64) error {
	if coin != DEFAULT_CURRENCY {
		return fmt.Errorf("only support %s transfer", DEFAULT_CURRENCY)
	}
	toPerp := false
	if from == exchange.Spot && to == exchange.USDXMarginedFutures {
		toPerp = true
	} else if from == exchange.USDXMarginedFutures && to == exchange.Spot {
		toPerp = false
	} else {
		return nil
	}

	action := TransferAction{
		Type:   string(ActionTypeTransfer),
		Amount: fmt.Sprintf("%v", amount),
		ToPerp: toPerp,
	}

	if this.apiKey.vaultAddress != "" {
		action.Amount = fmt.Sprintf("%v subaccount:%s", amount, this.apiKey.vaultAddress)
	}

	if this.IsTestnet {
		action.HyperliquidChain = "Testnet"
		action.SignatureChainId = "0x66eee"
	} else {
		action.HyperliquidChain = "Mainnet"
		action.SignatureChainId = "0xa4b1"
	}

	res, err := this.exchange(action)
	if err != nil {
		return err
	}
	if res.Get("status").String() != "ok" {
		return fmt.Errorf("transfer asset failed: %v", res)
	}
	return nil
}

func (this *Hyperliquid) GetWithdrawChains(coin string) ([]exchange.WithdrawChain, error) {
	return []exchange.WithdrawChain{exchange.WithdrawChainARBI}, nil
}

func (this *Hyperliquid) GetWithdrawFee(coin, chain exchange.WithdrawChain) (fee float64, err error) {
	if chain != exchange.WithdrawChainARBI {
		return 0, fmt.Errorf("unsupported chain: %v", chain)
	}
	return 1, nil
}

// 提币费用在 amount 中扣除
func (this *Hyperliquid) Withdraw(coin string, address string, amount float64, chain exchange.WithdrawChain) (id string, err error) {
	if strings.ToUpper(coin) != DEFAULT_CURRENCY {
		return "", fmt.Errorf("only support %s withdraw", DEFAULT_CURRENCY)
	}

	if chain != exchange.WithdrawChainARBI {
		return "", fmt.Errorf("unsupported chain: %v", chain)
	}

	action := map[string]any{
		"type":        "withdraw3",
		"amount":      fmt.Sprintf("%v", amount),
		"destination": address,
	}

	if this.IsTestnet {
		action["hyperliquidChain"] = "Testnet"
		action["signatureChainId"] = "0x66eee"
	} else {
		action["hyperliquidChain"] = "Mainnet"
		action["signatureChainId"] = "0xa4b1"
	}

	res, err := this.exchange(action)
	if err != nil {
		return "", err
	}
	if res.Get("status").String() != "ok" {
		return "", fmt.Errorf("withdraw failed: %v", res)
	}
	return "", nil
}

func (this *Hyperliquid) getEthClient() (*core.EthClient, error) {
	secret := this.apiKey.ApiSecret
	if this.withdrawApiKey != nil {
		secret = this.withdrawApiKey.ApiSecret
	}

	endpoint := "https://arb-mainnet.g.alchemy.com/v2/non1-7_GRkkf2xkPVP7muaE7YWqETISE"
	if this.IsTestnet {
		endpoint = "https://sepolia-rollup.arbitrum.io/rpc"
	}

	return core.NewEthClient(endpoint, string(secret))
}

func (this *Hyperliquid) getUSDCTokenAddr() string {
	if this.IsTestnet {
		return "******************************************"
	}
	return "******************************************"
}

func (this *Hyperliquid) IsSubaccount() bool {
	return this.apiKey.vaultAddress != ""
}

// 当 API 密钥同时也是钱包私钥时，可以调用该方法将 USDC 转入交易所
func (this *Hyperliquid) Deposit(amount float64) (string, error) {
	client, err := this.getEthClient()
	if err != nil {
		return "", err
	}

	balance, err := this.GetWalletBalance(DEFAULT_CURRENCY)
	if err != nil {
		return "", fmt.Errorf("get wallet balance failed: %v", err)
	}

	if balance < amount {
		return "", fmt.Errorf("insufficient balance: %v < %v", balance, amount)
	}

	tokenAddr := this.getUSDCTokenAddr()
	decimals, err := client.GetTokenDecimals(tokenAddr)
	if err != nil {
		return "", fmt.Errorf("get token decimals failed: %v", err)
	}

	amountBig := big.NewInt(int64(amount * float64(math.Pow10(int(decimals)))))

	bridgeAddr := "******************************************"
	if this.IsTestnet {
		bridgeAddr = "******************************************"
	}

	tx, err := client.TransferToken(tokenAddr, amountBig, bridgeAddr, core.TransferOptions{})
	if err != nil {
		return "", fmt.Errorf("transfer token failed: %v", err)
	}
	this.Infof("transfer token success, tx: %s", tx)
	return tx, nil
}

func (this *Hyperliquid) IsTransactionSuccess(txID string) (success bool, pending bool, err error) {
	client, err := this.getEthClient()
	if err != nil {
		return false, false, err
	}

	_, isPenging, err := client.TransactionByHash(context.Background(), common.HexToHash(txID))
	if err != nil {
		return false, false, err
	}

	if isPenging {
		return false, true, nil
	}

	rec, err := client.TransactionReceipt(context.Background(), common.HexToHash(txID))
	if err != nil {
		return false, false, err
	}

	return rec.Status == 1, false, nil
}

// 当 API 密钥同时也是钱包私钥时，可以调用该方法获取 USDC 余额
func (this *Hyperliquid) GetWalletBalance(coin string) (float64, error) {
	if coin != DEFAULT_CURRENCY {
		return 0, fmt.Errorf("only support %s balance", DEFAULT_CURRENCY)
	}

	client, err := this.getEthClient()
	if err != nil {
		return 0, err
	}

	tokenAddr := this.getUSDCTokenAddr()
	decimals, err := client.GetTokenDecimals(tokenAddr)
	if err != nil {
		return 0, err
	}

	tokenBalance, err := client.GetTokenBalance(tokenAddr, client.GetPublicAddress())
	if err != nil {
		return 0, err
	}

	balance := float64(tokenBalance.Int64()) / math.Pow10(int(decimals))
	return balance, nil
}

// 覆盖 Base 的特殊实现
func (this *Hyperliquid) FormatPrice(instrumentType exchange.InstrumentType, symbol string, price float64) string {
	ins := this.getInstrument(symbol)
	szDecimals := 0
	if ins != nil {
		szDecimals = ins.SzDecimals
	}
	return formatPrice(price, instrumentType, szDecimals)
}

func (this *Hyperliquid) GetRateLimit() (used, cap int64, err error) {
	res, err := this.info(map[string]string{"type": "userRateLimit", "user": this.GetUserAddress()})
	if err != nil {
		return
	}

	used = res.Get("nRequestsUsed").Int()
	cap = res.Get("nRequestsCap").Int()
	return
}

// 往子账号划转或提取资金
// 用钱包密钥或 API 密钥都可以划转
func (this *Hyperliquid) SubaccountTransfer(coin string, amount float64, isDeposit bool) (err error) {
	if !this.IsSubaccount() {
		return nil
	}

	action := SubAccountTransferAction{
		Type:           "subAccountTransfer",
		SubAccountUser: this.apiKey.vaultAddress,
		IsDeposit:      isDeposit,
		Usd:            floatToUSDInt(amount),
	}
	req, err := this.exchange(action)
	if err != nil {
		return
	}
	if req.Get("status").String() != "ok" {
		err = fmt.Errorf("vault transfer failed: %v", req)
	}
	return
}

func (this *Hyperliquid) GetWithdrawAccountBalance(coin string) (total, available float64, er error) {
	if coin != DEFAULT_CURRENCY {
		return 0, 0, fmt.Errorf("only support %s balance", DEFAULT_CURRENCY)
	}

	res, err := this.info(map[string]string{"type": "clearinghouseState", "user": this.apiKey.accountAddress})
	if err != nil {
		return 0, 0, err
	}

	accountValue := res.Get("marginSummary.accountValue").Float()
	totalMarginUsed := res.Get("marginSummary.totalMarginUsed").Float()

	return accountValue, accountValue - totalMarginUsed, nil
}

func (this *Hyperliquid) IsAtOpenInterestCap(symbol string) (bool, error) {
	res, err := this.info(map[string]string{"type": "perpsAtOpenInterestCap"})
	if err != nil {
		return false, err
	}
	for _, name := range res.Array() {
		if name.String() == symbol {
			return true, nil
		}
	}
	return false, nil
}

var _ exchange.Exchange = (*Hyperliquid)(nil)

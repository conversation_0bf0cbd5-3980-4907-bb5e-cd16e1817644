package hyperliquid

import (
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	resty "github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/exchange"
)

const API_URL = "api.hyperliquid.xyz"
const TESTNET_API_URL = "api.hyperliquid-testnet.xyz"

func (this *Hyperliquid) getBaseRequest() *resty.Request {
	client := this.Client
	hostURL := API_URL
	if this.IsTestnet {
		hostURL = TESTNET_API_URL
	}
	client.
		SetBaseURL(fmt.Sprintf("https://%s", hostURL)).
		SetTimeout(30 * time.Second).     // 设置超时时间，超时无反应则返回超时错误
		SetRetryCount(0).                 // 请求失败时的重试次数
		SetRetryWaitTime(5 * time.Second) // 重试前等待时间

	req := client.R()
	req.SetHeader("Content-Type", "application/json")

	return req
}

func (this *Hyperliquid) info(data any) (ret *gjson.Result, err error) {
	return this.post("/info", data)
}

func (this *Hyperliquid) exchange(action any) (ret *gjson.Result, err error) {
	if this.apiExpireAt != nil && time.Now().After(*this.apiExpireAt) {
		return nil, fmt.Errorf("api expired")
	}

	// nonce 必须是递增的，否则交易所会返回错误
	nonce := time.Now().UnixMilli()
	lastNonce := this.lastNonce.Load()
	for {
		// 确保新的 nonce 值大于上一次的值
		// 如果当前时间戳小于等于上次的 nonce，则将当前值加1
		if nonce <= lastNonce {
			nonce = lastNonce + 1
		}
		// 原子操作 CAS (Compare-And-Swap) 尝试更新 nonce
		// 仅当 lastNonce 仍然等于之前读取的值时才更新成功
		// 这样可以避免多个 goroutine 并发访问时使用重复的 nonce
		if this.lastNonce.CompareAndSwap(lastNonce, nonce) {
			break
		}
		// CAS 失败说明其他 goroutine 已经更新了 nonce
		// 重新获取最新的 nonce 值继续尝试
		lastNonce = this.lastNonce.Load()
	}

	isWithdraw := false
	if actionMap, ok := action.(map[string]any); ok {
		typ, _ := actionMap["type"].(string)
		if typ == "withdraw3" {
			// withdraw3 的参数 time 需要和 nonce 一致
			actionMap["time"] = nonce
			action = actionMap
			isWithdraw = true
		}
	}

	var sign *Signature
	vaultAddress := this.apiKey.vaultAddress

	if actionMap, ok := action.(TransferAction); ok {
		// transfer 的参数 nonce 需要和 nonce 一致
		actionMap.Nonce = nonce
		action = actionMap

		secret := this.apiKey.ApiSecret
		if this.withdrawApiKey != nil {
			secret = this.withdrawApiKey.ApiSecret
		}

		sign, err = signUsdClassTransferAction(secret, actionMap, this.IsTestnet)
		if err != nil {
			this.Errorf("sign transfer failed: %v", err)
			return nil, err
		}

		vaultAddress = "" // transfer 不需要 vaultAddress
	}

	if _, ok := action.(SubAccountTransferAction); ok {
		vaultAddress = "" // 子账号转账时不需要 vaultAddress
	}

	if isWithdraw {
		secret := this.apiKey.ApiSecret
		if this.withdrawApiKey != nil {
			secret = this.withdrawApiKey.ApiSecret
		}
		sign, err = signWithdrawFromBridgeAction(secret, action, this.IsTestnet)
		if err != nil {
			this.Errorf("sign withdraw failed: %v", err)
			return nil, err
		}
		vaultAddress = "" // withdraw3 不需要 vaultAddress
	}

	if sign == nil {
		sign, err = signL1Action(this.apiKey.ApiSecret, action, vaultAddress, nonce, this.IsTestnet)
		if err != nil {
			this.Errorf("sign cancel order failed: %v", err)
			return nil, err
		}
	}

	payload := map[string]any{
		"action":       action,
		"nonce":        nonce,
		"signature":    sign,
		"vaultAddress": nil,
	}
	if vaultAddress != "" {
		payload["vaultAddress"] = vaultAddress
	}

	return this.post("/exchange", payload)
}

func (this *Hyperliquid) post(path string, data any) (ret *gjson.Result, err error) {
	req := this.getBaseRequest()

	if data != nil {
		payloadData, err := json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("marshal payload failed, error: %v", err)
		}
		payload := payloadData

		if path == "/exchange" {
			this.Debugf("POST %s: %s", path, payload)
		}

		req.SetBody(payload)
	}

	// this.Debugf("POST with data path: %s, : %#v", path, data)

	resp, err := req.Post(path)
	if err != nil {
		return nil, fmt.Errorf("send post request failed, error: %v", err)
	}

	// this.Debugf("POST %s response: %s", path, resp.String())

	if resp.StatusCode() != 200 {
		this.Errorf("POST failed, path: %s, status: %d, response: %s", path, resp.StatusCode(), resp.String())
		return nil, fmt.Errorf("POST failed, path: %s, status: %d, response: %s", path, resp.StatusCode(), resp.String())
	}

	newRet := gjson.Parse(resp.String())
	return &newRet, nil
}

func (this *Hyperliquid) getInstrument(symbol string) *Instrument {
	instrument, found := this.instruments.Load(symbol)
	if !found {
		this.CacheInstruments(true)
		instrument, found = this.instruments.Load(symbol)
		if !found {
			return nil
		}
	}

	return &instrument
}

func (this *Hyperliquid) name2Symbol(name string) string {
	symbol, found := this.name2Symbols.Load(name)
	if !found {
		this.Errorf("name2Symbol not found: %s", name)
		return ""
	}
	return symbol
}

func floatToUSDInt(x float64) int {
	withDecimals := x * math.Pow10(6)
	return int(math.Round(withDecimals))
}

// Prices can have up to 5 significant figures, but no more than MAX_DECIMALS - szDecimals decimal places where MAX_DECIMALS is 6 for perps and 8 for spot.
func formatPrice(price float64, instrumentType exchange.InstrumentType, szDecimals int) string {
	var MAX_DECIMALS int
	if instrumentType == exchange.Spot {
		MAX_DECIMALS = 8
	} else {
		MAX_DECIMALS = 6
	}

	maxDecimals := MAX_DECIMALS - szDecimals

	// 确定有效数字的数量
	absPrice := math.Abs(price)
	sigFigs := getSignificantFigures(absPrice)

	// 检查是否超过5个有效数字
	if sigFigs > 5 {
		// 将价格四舍五入到5个有效数字
		price = roundToSigFigs(price, 5)
	}

	// 格式化价格
	formattedPrice := strconv.FormatFloat(price, 'f', -1, 64)

	// 将格式化的价格修剪到允许的最大小数位数
	parts := strings.Split(formattedPrice, ".")
	if len(parts) == 2 && len(parts[1]) > maxDecimals {
		formattedPrice = parts[0] + "." + parts[1][:maxDecimals]
	}

	return trimTrailingZeros(formattedPrice)
}

// getSignificantFigures calculates the number of significant figures in a number
func getSignificantFigures(num float64) int {
	str := strconv.FormatFloat(num, 'f', -1, 64)
	str = strings.TrimLeft(str, "0")        // Trim leading zeros
	str = strings.Replace(str, ".", "", -1) // Remove the decimal point
	str = strings.TrimLeft(str, "0")        // Trim any new leading zeros after removing the decimal point
	return len(str)
}

// roundToSigFigs rounds the given float to the specified number of significant figures
func roundToSigFigs(num float64, sigFigs int) float64 {
	if num == 0 {
		return 0
	}
	d := math.Ceil(math.Log10(math.Abs(num)))
	power := sigFigs - int(d)
	magnitude := math.Pow(10, float64(power))
	shifted := math.Round(num * magnitude)
	return shifted / magnitude
}

func getTickSize(price float64, instrumentType exchange.InstrumentType) float64 {
	absPrice := math.Abs(price)
	if absPrice >= 1 {
		exponent := math.Floor(math.Log10(absPrice))
		tickSize := math.Pow(10, exponent-4)
		return tickSize
	} else {
		maxDecimals := 6
		if instrumentType == exchange.Spot {
			maxDecimals = 8
		}

		if absPrice >= 0.1 {
			maxDecimals = 5
		} else if absPrice >= 0.01 && instrumentType == exchange.Spot {
			maxDecimals = 6
		} else if absPrice >= 0.001 && instrumentType == exchange.Spot {
			maxDecimals = 7
		}

		tickSize := math.Pow(10, -float64(maxDecimals))
		return tickSize
	}
}

func convertOrderType(hOrderType string) exchange.OrderType {
	switch hOrderType {
	case "Limit":
		return exchange.Limit
	case "Market":
		return exchange.Market
	case "Stop Limit":
		return exchange.StopLimit
	case "Take Profit Limit":
		return exchange.StopLimit
	case "Stop Market":
		return exchange.StopMarket
	default:
		return exchange.UnknownOrderType
	}
}

func convertOrderStatus(status string) exchange.OrderStatus {
	switch status {
	case "open":
		return exchange.OrderStatusNew
	case "filled":
		return exchange.OrderStatusFilled
	case "canceled":
		return exchange.OrderStatusCancelled
	case "vaultWithdrawalCanceled":
		return exchange.OrderStatusCancelled
	case "openInterestCapCanceled":
		return exchange.OrderStatusCancelled
	case "selfTradeCanceled":
		return exchange.OrderStatusCancelled
	case "reduceOnlyCanceled":
		return exchange.OrderStatusCancelled
	case "siblingFilledCanceled":
		return exchange.OrderStatusCancelled
	case "delistedCanceled":
		return exchange.OrderStatusCancelled
	case "liquidatedCanceled":
		return exchange.OrderStatusCancelled
	case "scheduledCancel":
		return exchange.OrderStatusCancelled
	case "marginCanceled":
		return exchange.OrderStatusCancelled
	case "rejected":
		return exchange.OrderStatusRejected
	case "triggered":
		return exchange.OrderStatusTriggered
	default:
		return exchange.UnknownOrderStatus
	}
}

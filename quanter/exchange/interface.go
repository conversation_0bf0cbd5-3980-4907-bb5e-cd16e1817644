package exchange

import (
	"math/rand"
	"time"

	"github.com/stevedomin/termtable"
)

type Exchange interface {
	GetLoggerID() string
	GetName() string
	GetID() string
	SetProxy(string)
	GetProxy() string
	RemoveProxy()
	ConnectWebsocket(instrumentTypes []InstrumentType, connectedCallback func(connected bool))
	CloseWebsocket(stop bool)
	CheckPriceTriggerTimeLoop()
	Remove()

	// 因为不能阻塞其他操作，不能直接返回错误；该函数如果需要知道确切的回复，用 callback 处理
	// 主要是给 ctp 的 slack 适配器调用转发消息
	SendWebsocketMessage(packet *ClientPacket, callback func(response *Packet, er error))

	Subscribe(instrumentType InstrumentType, symbol string)
	UnSubscribe(instrumentType InstrumentType, symbol string)
	SubscribeOrderBook(instrumentType InstrumentType, symbol string)
	UnSubscribeOrderBook(instrumentType InstrumentType, symbol string)
	GetOrderBook(instrumentType InstrumentType, symbol string) (*OrderBook, error)

	IsReverseSymbol(instrumentType InstrumentType, symbol string) bool                                             // 是否是反向合约
	GetLatestKLines(instrumentType InstrumentType, symbol string, periodHour, num int) (klines []*KLine, er error) // 获取最新指定根数的 K 线, 时间从早到晚排序
	CacheInstruments(force bool) error
	GetInstrument(instrumentType InstrumentType, symbol string) (*Instrument, error)
	PreInstrumentCallback(instrument *Instrument) error
	MaxLeverage(instrumentType InstrumentType, symbol string, value float64) float64 // 最大杠杠，有的交易所根据合约价值决定
	GetLastPrice(instrumentType InstrumentType, symbol string, allowDelay bool) (float64, error)
	CreateOrder(args CreateOrderArgs) (*Order, error)
	GetOrder(instrumentType InstrumentType, orderType OrderType, symbol, orderID string, timeRange *TimeRange) (order *Order, err error)
	UpdateOrder(order Order, args *UpdateOrderArgs) (*Order, error)                                                      // 如交易所无法修改订单，撤销订单后重新挂单，返回新的 orderID
	CancelAllOrders(instrumentType InstrumentType, orderType OrderType, symbol string) (canceledIDs []string, err error) // 取消所有订单
	CancelOrder(instrumentType InstrumentType, orderType OrderType, symbol, orderID string) error
	GetOpenOrders(instrumentType InstrumentType, orderType OrderType, symbol string) ([]*Order, error) // 获取挂单
	TransferAsset(from, to InstrumentType, coin string, amount float64) error
	GetTradablePairs(instrumentType InstrumentType, uSymbol string) ([]string, error)
	GetAccountConfig(instrumentType InstrumentType) (*AccountConfig, error)
	GetAccountBalances(instrumentType InstrumentType) ([]*AccountBalance, error)
	GetAccountCurrencies(instrumentType InstrumentType) ([]string, error)
	GetPositions(instrumentType InstrumentType, symbol string, allowCache bool) ([]*Position, error)
	SetDualPositionSide(instrumentType InstrumentType, dualPositionSide bool) error
	SetMarginMode(instrumentType InstrumentType, symbol string, mode MarginMode) error
	SetAccountMarginMode(instrumentType InstrumentType, mode AccountMarginMode) error
	QueryFundingHistory(instrumentType InstrumentType, symbol string, limit int, from time.Time, to time.Time) ([]*FundingHistory, error)
	SetLeverage(instrumentType InstrumentType, symbol string, marginMode MarginMode, side PositionSide, leverage float64) error
	GetLeverage(instrumentType InstrumentType, symbol string, marginMode MarginMode) (leverageLong, leverageShort float64, err error)
	TranslateSymbolCode(symbolCode *SymbolCode) (spotAndFutureSymbols []*SymbolPair, er error)                                                      // 将 SymbolCode 转换为该交易所对应的现货和期货交易对，因为 SymbolCode 可能包含通配符，所以可能对应多个 SpotFuture 对
	TranslateFutureSymbol(instrumentType InstrumentType, futureSymbol string, uSymbol string) (spotSymbol string, futureCode *SymbolCode, er error) // 将 FutureSymbol 转换为该交易所对应的现货和个统一期货代码
	TranslateSpotSymbol(symbol, uSymbol string) (code *SymbolCode, er error)                                                                        // 将 SpotSymbol 转换为该交易所对应的现货代码
	GetSymbolCodeQuote(symbolCode *SymbolCode) string                                                                                               // 某些交易所有特殊实现，所以放在交易所接口而不是 SymbolCode 接口
	GetSymbolCodeUnit(symbolCode *SymbolCode) string                                                                                                // 获取交易对数量单位，如 cnt, BTC, ETH 等
	GetSupportedInstrumentTypes() []InstrumentType
	GetWithdrawChains(coin string) ([]WithdrawChain, error)
	GetWithdrawFee(coin, chain WithdrawChain) (fee float64, err error)
	Withdraw(coin string, address string, amount float64, chain WithdrawChain) (id string, err error) // 提币费用统一从 amount 扣除
	IsSubaccount() bool
	SubaccountTransfer(coin string, amount float64, isDeposit bool) (err error) // 子账户转账，isDeposit 为 true 时是转入，false 时是转出; 如果交易所有多账号，都是在充提账号之间转账

	GetAccountWorth(controllerRefID string, instrumentTypes []InstrumentType, cacheTime time.Duration, symbolFilterFunc func(string) bool) (worthInUSDT float64, er error)
	GetUserMargin(instrumentType InstrumentType, currency string) (*UserMargin, error)                                      // 获取账号余额
	FormatDisplayQty(instrumentType InstrumentType, symbol string, qty float64) string                                      // 格式化显示合约数量，将 API 数量转换为人工可识别数量，比如 BM ETHUSDT 1000 张转换为 0.01 ETH
	Qty2Size(instrumentType InstrumentType, symbol string, price float64, qty float64) (float64, error)                     // 根据价格、数量计算合约价值
	Size2Qty(instrumentType InstrumentType, symbol string, price float64, size float64) (float64, error)                    // 根据价格、价值计算数量
	CalcPrice(instrumentType InstrumentType, symbol string, qty float64, size float64) (float64, error)                     // 根据数量、价值计算价格
	SetLeverageToMax(instrumentType InstrumentType, symbol string, currentLeverage float64, positionSide PositionSide) bool // 设置杠杠率到最大
	CalculateLeverage(position *Position) (float64, error)
	AdjustLiquidationPrice(instrumentType InstrumentType, symbol string, position *Position, targetLiquidationPrice, acceptableDelta float64) (deltaPrice float64, errMsg string) // 根据持仓调整爆仓价到指定价格，返回调整后的价差
	SetHttpDebug(debug bool)
	FormatPrice(instrumentType InstrumentType, symbol string, price float64) string
	FormatQty(instrumentType InstrumentType, symbol string, qty float64) string
	RoundPrice(instrumentType InstrumentType, symbol string, price float64) float64
	FloorPrice(instrumentType InstrumentType, symbol string, price float64) float64
	RoundQty(instrumentType InstrumentType, symbol string, qty float64) float64
	FloorQty(instrumentType InstrumentType, symbol string, qty float64) float64
	GetAPIExpireTime() (*time.Time, error)

	// 通用接口函数，由 BaseExchange 统一实现
	GetTicker(instrumentType InstrumentType, symbol string) (*Ticker, bool)
	GetAllTickers() []*Ticker
	SetEnableRealtimePrice(enable bool)
	RegisterPriceTrigger(priceTrigger *PriceTrigger) (err error)
	DeletePriceTrigger(id string) error
	GetPriceTriggers() []*PriceTrigger
	AddPriceWatch(watch *PriceWatch) (err error)
	DeletePriceWatch(symbolCode *SymbolCode) error
	GetPriceWatches() []*PriceWatch
	RegisterOrderUpdatedCallback(callback func(order *Order))
	RegisterPriceTriggeredCallback(callback func(trigger *PriceTrigger))
	OrderUpdatedCallback(order *Order)

	// convenient 接口函数，由 BaseExchange 统一实现
	TranslateSymbolCodeToSpotSymbol(symbolCode *SymbolCode) (spotSymbol string, er error)
	TranslateSymbolCodeToFutureSymbol(symbolCode *SymbolCode) (futureSymbol string, er error)
	GetNextSymbolCodes(instrumentType InstrumentType, futureCode *SymbolCode, count int) (symbolCodes []*SymbolCode, er error)
	GetPosition(instrumentType InstrumentType, symbol string, side PositionSide, allowCache bool) (*Position, error)
	GetAccountHoldings(instrumentTypes []InstrumentType) (holdings HoldingList, er error)
	GetHoldingQty(instrumentType InstrumentType, coinOrSymbol string) (total, available float64, er error)
	GetBalance(instrumentType InstrumentType, coin string) (total, available float64, er error)
	GetWithdrawAccountBalance(coin string) (total, available float64, er error) // 充提账号余额，如果使用了 subaccount，则会返回主账号的余额
	GetLatestKLinesWithCache(instrumentType InstrumentType, symbol string, periodHour, num int, reload bool) ([]*KLine, error)
	GetFundingHistory(symbolCode *SymbolCode, latestOnly bool, isDaily bool) (fundingHis []FundingHistory, er error)
	GetAccountBalancesValidCoins(controllerRefID string, instrumentTypes []InstrumentType, checkValidCoinFunc func(string) bool) (balances AccountBalanceList, er error)
	GetSpotPrice(spotSymbol string, cacheTime time.Duration) (price float64, er error)
	GetCurrencyPrice(currency string, cacheTime time.Duration) (priceInUSDT float64, er error)
	GetOrderByOrig(origOrder Order) (order *Order, err error)
	CheckValidCoins(coins []string) (validCoins []string, invalidCoins []string, er error)
}

type Orderable interface {
	CreateOrder(args CreateOrderArgs) (*Order, error)
	CancelOrder(instrumentType InstrumentType, orderType OrderType, symbol, orderID string) error
	GetOpenOrders(instrumentType InstrumentType, orderType OrderType, symbol string) ([]*Order, error)
	GetOrderByOrig(origOrder Order) (order *Order, err error)
	UpdateOrder(order Order, args *UpdateOrderArgs) (*Order, error)
}

func NewRandomID() string {
	rand.Seed(time.Now().UnixNano())
	randStr := "1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ"
	var letterRunes = []rune(randStr)

	b := make([]rune, 6)
	for i := range b {
		b[i] = letterRunes[rand.Intn(len(letterRunes))]
	}
	return string(b)
}

func NewTable() *termtable.Table {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	return t
}

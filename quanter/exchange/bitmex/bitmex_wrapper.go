package bitmex

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/quanter/exchange"

	"github.com/asaskevich/govalidator"
	"github.com/gorilla/websocket"
	"github.com/shopspring/decimal"
)

// 创建 BitMEX 对象
func NewBitMEX(options *exchange.Options) (*BitMEX, error) {
	if options.ControllerID == "" {
		return nil, errors.New("controller id is empty")
	}

	apiKey, err := exchange.NewAPISecret(exchange.BitMEX, options.ApiKey, options.ApiSecret, false)
	if err != nil {
		return nil, err
	}

	b := &BitMEX{
		BaseExchange: *exchange.NewBaseExchange(options),
		instruments:  xsync.NewMapOf[*Instrument](),
		apiKey:       apiKey,
	}
	b.Exchange = b

	return b, nil
}

func (this *BitMEX) GetName() string {
	return exchange.BitMEX
}

func (this *BitMEX) GetSupportedInstrumentTypes() []exchange.InstrumentType {
	return []exchange.InstrumentType{exchange.CoinMarginedFutures, exchange.USDXMarginedFutures}
}

// 是否是反向合约
// 主要区别在于：正向合约每单位的合约数量相同，反向合约每单位的头寸（即价值）相同
func (this *BitMEX) IsReverseSymbol(instrumentType exchange.InstrumentType, symbol string) bool {
	if this.getInstrument(symbol).Multiplier < 0 {
		return true
	} else {
		return false
	}
}

func (this *BitMEX) GetInstrument(instrumentType exchange.InstrumentType, symbol string) (i *exchange.Instrument, _ error) {
	ins := this.getInstrument(symbol)
	if ins == nil {
		return i, fmt.Errorf("%s instrument not found", symbol)
	}

	i = &exchange.Instrument{}
	i.Symbol = ins.Symbol
	i.TickSize = ins.TickSize
	i.LotSize = ins.LotSize
	i.MinSize = ins.LotSize
	i.MarkPrice = ins.MarkPrice
	i.LastPrice = ins.LastPrice
	i.FundingRate = ins.FundingRate
	i.FundingRatePeriod = "8h"
	t, _ := time.Parse(time.RFC3339, ins.FundingTimestamp)
	i.FundingTime = t
	i.MaxLeverage = ins.MaxLeverage
	i.SettleCurrency = ins.SettlCurrency
	i.UnderlyCurrency = ins.Underlying
	i.QuoteCurrency = ins.QuoteCurrency
	i.UpdateTime = time.Unix(ins.updatedAt, 0)
	i.InstrumentType = ins.InstrumentType()
	return
}

// 获取账号余额，如获取成功则返回 UserMargin 结构
func (this *BitMEX) GetUserMargin(instrumentType exchange.InstrumentType, currency string) (*exchange.UserMargin, error) {
	if instrumentType == exchange.Spot {
		return nil, exchange.ErrNotAvailableForInstrumentType
	}
	path := fmt.Sprintf("/api/v1/user/margin?currency=%s", currency)
	userMargin := &exchange.UserMargin{}
	err := this.sendHTTPRequest("GET", path, nil, userMargin, true)
	if err != nil {
		this.Errorf(" get user margin for %s err: %s", currency, err)
		return nil, err
	}

	if userMargin.Currency != currency {
		this.Warnf(" get user margin %s failed", currency)
		return nil, errors.New("get user margin failed")
	}

	decimals := this.getCurrencyDecimals(currency)
	userMargin.MarginBalance = userMargin.MarginBalance / math.Pow10(decimals)
	userMargin.WalletBalance = userMargin.WalletBalance / math.Pow10(decimals)

	return userMargin, nil
}

func (this *BitMEX) GetPositions(instrumentType exchange.InstrumentType, symbol string, allowCache bool) (positions []*exchange.Position, _ error) {
	filter := ""
	if symbol != "" {
		filter = url.QueryEscape(fmt.Sprintf(`{"symbol":"%s"}`, symbol))
	}
	path := fmt.Sprintf("/api/v1/position?filter=%s", filter)
	bmPositions := &[]Position{}
	err := this.sendHTTPRequest("GET", path, nil, bmPositions, true)
	if err != nil {
		this.Errorf("(%s) get position err: %s", symbol, err)
		return nil, err
	}

	if len(*bmPositions) == 0 {
		return positions, nil
	}

	for _, bmPosition := range *bmPositions {
		this.Debugf("(%s) current position: %#v", symbol, bmPosition)
		ins := this.getInstrument(bmPosition.Symbol)
		posInstrumentType := ins.InstrumentType()
		if instrumentType != exchange.UnknownInstrumentType && posInstrumentType != instrumentType {
			continue
		}

		ePosition := &exchange.Position{}
		ePosition.ExchangeName = exchange.BitMEX
		ePosition.InstrumentType = instrumentType
		ePosition.Symbol = bmPosition.Symbol
		ePosition.Qty = bmPosition.CurrentQty
		ePosition.EntryPrice = bmPosition.AvgEntryPrice
		ePosition.MarkPrice = bmPosition.MarkPrice
		ePosition.LastPrice = bmPosition.LastPrice
		ePosition.Leverage = bmPosition.Leverage
		ePosition.LiquidationPrice = bmPosition.LiquidationPrice
		ePosition.UnrealisedPNL = bmPosition.UnrealisedPnl
		ePosition.RealisedPNL = bmPosition.RealisedPnl
		ePosition.Margin = bmPosition.Margin
		if ePosition.Qty == 0 {
			ePosition.RealisedPNL = 0 // 无持仓时无需之前的已结算数据
		} else if ePosition.Qty > 0 {
			ePosition.Side = exchange.PositionSideLong
		} else {
			ePosition.Side = exchange.PositionSideShort
		}
		// exchange.Position 部分字段没有，但是对于计算杠杆率比较重要，需要附带到 Position 结构上
		ePosition.SetFloat64(ExtKeyPosCost, bmPosition.PosCost)
		ePosition.SetFloat64(ExtKeyPosCross, bmPosition.PosCross)
		ePosition.SetFloat64(ExtKeyMaintMarginReq, bmPosition.MaintMarginReq)
		positions = append(positions, ePosition)
	}

	return positions, nil
}

func (this *BitMEX) CreateOrder(args exchange.CreateOrderArgs) (order *exchange.Order, _ error) {
	err := this.CheckQuoteQty(&args)
	if err != nil {
		return nil, fmt.Errorf("check order quoteQty failed, %v", err)
	}

	symbol := args.Symbol
	bmArgs := &CreateOrderArgs{
		Symbol:   symbol,
		Side:     string(args.Side),
		Price:    args.Price,
		StopPx:   args.TriggerPrice,
		OrderQty: int(args.Qty),
	}
	bmArgs.Price = this.roundPrice(args.Symbol, bmArgs.Price)
	bmArgs.StopPx = this.roundPrice(args.Symbol, bmArgs.StopPx)

	switch args.Type {
	case exchange.Limit:
		bmArgs.OrdType = "Limit"
	case exchange.Market:
		bmArgs.OrdType = "Market"
	case exchange.StopLimit:
		bmArgs.OrdType = "StopLimit"
	}

	execInst := ""
	if args.Type == exchange.StopLimit {
		if args.TriggerPriceType == exchange.TriggerPriceTypeLast {
			execInst = "LastPrice"
		} else if args.TriggerPriceType == exchange.TriggerPriceTypeMark {
			execInst = "MarkPrice"
		}
	}
	if args.ReduceOnly {
		if execInst == "" {
			execInst = "Close"
		} else {
			execInst += ",Close"
		}
	}
	bmArgs.ExecInst = execInst

	path := "/api/v1/order"
	this.Infof("(%s) create order args: %#v", symbol, bmArgs)
	this.Client.SetRetryCount(0) // 创建订单不重试，风险较大
	bmOrder := &Order{}
	err = this.sendHTTPRequest("POST", path, bmArgs, bmOrder, true)
	if err != nil {
		this.Errorf("(%s) create order err: %s", symbol, err)
		return nil, err
	}

	if bmOrder == nil || bmOrder.OrderID == "" {
		this.Errorf("(%s) resp empty order", symbol)
		return nil, fmt.Errorf("(%s) create oder error: resp empty order", symbol)
	}

	exchangeOrder := convertOrderToExchangeOrder(*bmOrder)
	exchangeOrder.CreateTime = exchangeOrder.UpdateTime
	exchangeOrder.InstrumentType = args.InstrumentType
	return exchangeOrder, nil
}

// 获取进行中的订单
func (this *BitMEX) GetOpenOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string) ([]*exchange.Order, error) {
	if instrumentType == exchange.Spot {
		return nil, exchange.ErrNotImplementedForInstrumentType
	}
	return this.getOrders(instrumentType, orderType, symbol, true, "")
}

func (this *BitMEX) GetOrder(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol, orderID string, timeRange *exchange.TimeRange) (*exchange.Order, error) {
	orders, err := this.getOrders(instrumentType, orderType, symbol, false, orderID)
	if err != nil {
		return nil, err
	}
	if len(orders) == 1 {
		order := orders[0]
		return order, nil
	}
	return nil, fmt.Errorf("can not find order: %s", orderID)
}

// 更新订单
func (this *BitMEX) UpdateOrder(origOrder exchange.Order, args *exchange.UpdateOrderArgs) (*exchange.Order, error) {
	if args.InstrumentType == exchange.Spot {
		return nil, exchange.ErrNotImplementedForInstrumentType
	}
	orderArgs := &UpdateOrderArgs{}
	orderArgs.OrderID = origOrder.OrderID
	orderArgs.OrderQty = args.OrderQty
	orderArgs.Price = this.roundPrice(origOrder.Symbol, args.Price)
	orderArgs.StopPx = this.roundPrice(origOrder.Symbol, args.TriggerPrice)

	this.Infof("(%s) update order args: %#v", origOrder.Symbol, orderArgs)

	path := "/api/v1/order"
	bmOrder := &Order{}
	err := this.sendHTTPRequest("PUT", path, orderArgs, bmOrder, true)

	if err != nil {
		this.Errorf("(%s) update order err: %s", origOrder.Symbol, err)
		return nil, err
	}

	if bmOrder == nil || bmOrder.OrderID == "" {
		this.Errorf("(%s) resp empty order", origOrder.Symbol)
		return nil, fmt.Errorf("resp empty order")
	}

	exchangeOrder := convertOrderToExchangeOrder(*bmOrder)
	exchangeOrder.SetExtStruct(&origOrder)
	exchangeOrder.InstrumentType = args.InstrumentType
	return exchangeOrder, nil
}

// 取消所有订单，返回是否调用成功
// issue: 参数 instrumentType, orderType 未使用
func (this *BitMEX) CancelAllOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string) ([]string, error) {
	if instrumentType == exchange.Spot {
		return []string{}, exchange.ErrNotImplementedForInstrumentType
	}

	path := fmt.Sprintf("/api/v1/order/all?symbol=%s", symbol)
	orders := &[]Order{}
	err := this.sendHTTPRequest("DELETE", path, nil, orders, true)
	if err == nil {
		orderIDs := []string{}
		for _, order := range *orders {
			orderIDs = append(orderIDs, order.OrderID)
		}
		return orderIDs, nil
	} else {
		return []string{}, fmt.Errorf("cancel all orders err: %s", err)
	}
}

// 使用订单 ID 取消订单
func (this *BitMEX) CancelOrder(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string, orderID string) error {
	path := fmt.Sprintf("/api/v1/order?orderID=%s", orderID)
	err := this.sendHTTPRequest("DELETE", path, nil, nil, true)
	this.Infof("(%s) cancel order %s", symbol, orderID)
	return err
}

// 获取并计算最新 K 线数据
func (this *BitMEX) GetLatestKLines(instrumentType exchange.InstrumentType, symbol string, periodHour, num int) (klines []*exchange.KLine, er error) {
	var origKlines *KlineHistory
	if history, err := this.getHistory(symbol, periodHour, num); err != nil {
		er = err
		return
	} else {
		origKlines = history
	}

	// origKlines 里是小时线，需合并小时线为 PeriodHour 小时线
	// 取 PeriodHour 点，如 6小时线，取 0、6、12、18 点
	periodSeconds := int64(periodHour * 3600)
	klineCount := 0
	for i, ts := range origKlines.T {
		if ts%periodSeconds == 0 {
			// 这根小时线是 PeriodHour 的开盘线
			klineCount++

			klines = append(klines, &exchange.KLine{
				Open:  origKlines.O[i],
				Close: origKlines.C[i],
				High:  origKlines.H[i],
				Low:   origKlines.L[i],
				Time:  ts,
			})
		} else if klineCount > 0 {
			// 非 PeriodHour 的开盘线
			klineIdx := klineCount - 1

			klines[klineIdx].Close = origKlines.C[i]
			klines[klineIdx].High = math.Max(klines[klineIdx].High, origKlines.H[i])
			klines[klineIdx].Low = math.Min(klines[klineIdx].Low, origKlines.L[i])
		}
	}

	return
}

func (this *BitMEX) GetLastPrice(instrumentType exchange.InstrumentType, symbol string, allowDelay bool) (float64, error) {
	ins := this.cacheLatestInstrument(symbol)
	if ins != nil {
		return ins.LastPrice, nil
	}
	return 0, errors.New("get instrument price failed")
}

// 根据持仓计算杠杠率
func (this *BitMEX) CalculateLeverage(position *exchange.Position) (float64, error) {
	// 仅在下一资金费用结算点需要付费时, 计算杠杠率需考虑资金费率, 其他情况不需要
	// 付资金费用:  杠杆率 = 爆仓价 / (abs(计算价格 - 爆仓价) + 维持保证金率 * 爆仓价 + abs(当前资金费率) * 爆仓价)
	// 收资金费用:  杠杆率 = 爆仓价 / (abs(计算价格 - 爆仓价) + 维持保证金率 * 爆仓价)

	// 2020.10.14 最新公式
	// multiplier > 0 时
	// 多仓 leverage = 1 / (1 + maintMarginReq + fundingRate - (liquidationPrice * qty * multiplier + posCross) / posCost)
	// 空仓 leverage = 1 / ((liquidationPrice * qty * multiplier + posCross) / posCost - 1 + maintMarginReq + fundingRate)
	// multiplier < 0 时
	// 多仓 leverage = 1 / ((multiplier / liquidationPrice * qty + posCross)/posCost - 1 + maintMarginReq + fundingRate)
	// 空仓 leverage = 1 / (1 + maintMarginReq + fundingRate - (multiplier / liquidationPrice * qty + posCross)/posCost)
	// fundingRate = abs(needPayFunding ? fundingRate : 0)
	// qty 带符号
	// 有持仓时: posCross, posCost 从持仓获取；无持仓时: posCross 为 0, posCost 使用下列方法计算
	// posCost 算法
	// function V(multiplier, qty, posPrice) {
	//     var v = Math.round(multiplier >= 0 ? multiplier * posPrice : multiplier / posPrice);
	//     return Math.round(qty * v)
	// }

	symbol := position.Symbol
	positionSide := position.Side
	maintMarginReq := position.GetFloat64(ExtKeyMaintMarginReq)
	liquidationPrice := this.roundPrice(symbol, position.LiquidationPrice)
	qty := float64(position.Qty)
	posCost := position.GetFloat64(ExtKeyPosCost)
	posCross := position.GetFloat64(ExtKeyPosCross)

	if posCost == 0 {
		posCost = this.calculatePosCost(symbol, position.Qty, position.EntryPrice)
	}

	fundingRate := this.getInstrument(symbol).FundingRate
	needPayFunding := false
	if positionSide == exchange.PositionSideLong && fundingRate > 0 { // fundingRate 为正，多头付费
		needPayFunding = true
	} else if positionSide == exchange.PositionSideShort && fundingRate < 0 { // fundingRate 为负，空头付费
		needPayFunding = true
	}

	// 盈利情况 posCross 设为 0 计算杠杠率
	if positionSide == exchange.PositionSideLong && position.MarkPrice > position.EntryPrice {
		posCross = 0
	} else if positionSide == exchange.PositionSideShort && position.MarkPrice < position.EntryPrice {
		posCross = 0
	}

	if needPayFunding {
		fundingRate = math.Abs(fundingRate)
	} else {
		fundingRate = 0
	}

	if maintMarginReq == 0 {
		maintMarginReq = 0.0035
	}

	multiplier := float64(this.getInstrument(symbol).Multiplier)
	var leverage float64
	if multiplier >= 0 {
		if positionSide == exchange.PositionSideLong {
			leverage = 1 / (1 + maintMarginReq + fundingRate - (liquidationPrice*qty*multiplier+posCross)/posCost)
		} else {
			leverage = 1 / ((liquidationPrice*qty*multiplier+posCross)/posCost - 1 + maintMarginReq + fundingRate)
		}
	} else {
		if positionSide == exchange.PositionSideLong {
			leverage = 1 / ((multiplier/liquidationPrice*qty+posCross)/posCost - 1 + maintMarginReq + fundingRate)
		} else {
			leverage = 1 / (1 + maintMarginReq + fundingRate - (multiplier/liquidationPrice*qty+posCross)/posCost)
		}
	}
	leverage = math.Round(leverage*100) / 100
	return leverage, nil
}

func (this *BitMEX) MaxLeverage(instrumentType exchange.InstrumentType, symbol string, value float64) float64 {
	return this.getInstrument(symbol).MaxLeverage
}

// 设置杠杠率到最大
func (this *BitMEX) SetLeverageToMax(instrumentType exchange.InstrumentType, symbol string, currentLeverage float64, positionSide exchange.PositionSide) bool {
	return this.setLeverage(symbol, this.getInstrument(symbol).MaxLeverage, currentLeverage)
}

// 根据持仓调整爆仓价到指定价格，返回调整后的价差
func (this *BitMEX) AdjustLiquidationPrice(instrumentType exchange.InstrumentType, symbol string, position *exchange.Position, targetLiquidationPrice float64, acceptableDelta float64) (deltaPrice float64, errMsg string) {
	deltaPrice = math.Abs(targetLiquidationPrice - position.LiquidationPrice)

	if this.isFundingRateExpired(symbol) {
		if instrument := this.cacheLatestInstrument(symbol); instrument == nil {
			return deltaPrice, "获取最新资金费用失败"
		}
	}

	deltaPriceRatio := deltaPrice / targetLiquidationPrice
	targetPosition := &exchange.Position{
		Symbol:           symbol,
		Qty:              position.Qty,
		Side:             position.Side,
		LiquidationPrice: targetLiquidationPrice,
		MarkPrice:        position.MarkPrice,
		EntryPrice:       position.EntryPrice,
	}
	targetPosition.SetExtStruct(position)

	currentLeverage, _ := this.CalculateLeverage(position)      // 当前实际杠杠率
	targetLeverage, _ := this.CalculateLeverage(targetPosition) // 目标杠杠率

	this.Infof("(%s) 爆仓价待调整: %.2f => %.2f, 价差: %.2f(%.4f), 目标价差: %.2f", symbol, position.LiquidationPrice, targetLiquidationPrice, deltaPrice, deltaPriceRatio, acceptableDelta)
	this.Infof("(%s) 当前杠杆率：%.2f", symbol, position.Leverage)

	if currentLeverage >= this.getInstrument(symbol).MaxLeverage && targetLeverage >= this.getInstrument(symbol).MaxLeverage {
		this.Infof("(%s) 当前杠杠率已达上限", symbol)
		return deltaPrice, ""
	}

	// 爆仓价不一致，则需要通过调整杠杠率重新设置，尝试调整 10 次
	// 价差在 2% 以上，用公式计算杠杠率，否则微调
	// 微调范围 [MIN_LEVERAGE_STEP, MAX_LEVERAGE_STEP]
	MIN_LEVERAGE_STEP := 0.01
	MAX_LEVERAGE_STEP := 2.0
	leverageStep := MAX_LEVERAGE_STEP
	lastDeltaPriceFlag := targetLiquidationPrice > position.LiquidationPrice
	calculatorUsedLastTime := false

	// 采用步长减半法调整时，大约需要 7 个循环，可以将杠杆率调到 0.01 以下（0.5^7 = 0.007）
	// 20次循环是留有一定余量的，通常情况下可以在 3-4 个循环内调整到目标值，费时不超过 5 秒
	for i := 0; i < 20; i++ {
		position, _ = this.getPosition(instrumentType, symbol)
		if position == nil {
			return deltaPrice, "获取当前持仓失败"
		}

		deltaPrice = math.Abs(targetLiquidationPrice - position.LiquidationPrice)
		deltaPriceRatio = deltaPrice / targetLiquidationPrice
		this.Infof("(%s) liquidation price delta: %.2f(%.4f)", symbol, deltaPrice, deltaPriceRatio)

		// 当前价差如果超过 USE_CALCULATOR_THRESHOLD ，先用公式调整一次，这样调整的速度可能更快
		// 如果持仓是盈利的，用公式调整一次可以大概率一次调整到位，不需要用步长减半法微调
		useCalculator := deltaPriceRatio > USE_CALCULATOR_THRESHOLD
		prifitable := ((position.Side == exchange.PositionSideLong && position.EntryPrice < position.MarkPrice) || (position.Side == exchange.PositionSideShort && position.EntryPrice > position.MarkPrice))
		if i == 0 && prifitable {
			// 如果持仓是盈利的，先用公式调整一次
			useCalculator = true
		}

		// 使用公式法调整法调整杠杆率
		if useCalculator {
			targetPosition = &exchange.Position{
				Symbol:           symbol,
				Qty:              position.Qty,
				Side:             position.Side,
				LiquidationPrice: targetLiquidationPrice,
				MarkPrice:        position.MarkPrice,
				EntryPrice:       position.EntryPrice,
			}
			targetPosition.SetExtStruct(position) // 复制 position 中的 ExtXXX 数据

			currentLeverage, _ = this.CalculateLeverage(position) // 当前实际杠杠率
			targetLeverage, _ = this.CalculateLeverage(targetPosition)

			this.Infof("(%s) use CalculateLeverage() instead of step adjust", symbol)
			calculatorUsedLastTime = true
		} else { // 使用步长减半法微调杠杆率
			currentLeverage = position.Leverage

			// 如果价差符号变动，step 减半；但是如果上次是用计算器调整，不减半
			deltaPriceFlag := targetLiquidationPrice > position.LiquidationPrice
			if deltaPriceFlag != lastDeltaPriceFlag && !calculatorUsedLastTime {
				leverageStep = leverageStep / 2 // 步长减半
				leverageStep = math.Max(MIN_LEVERAGE_STEP, leverageStep)
				leverageStep = math.Min(MAX_LEVERAGE_STEP, leverageStep)
			}

			if position.Side == exchange.PositionSideLong {
				if targetLiquidationPrice > position.LiquidationPrice {
					targetLeverage = currentLeverage + leverageStep
				} else {
					targetLeverage = currentLeverage - leverageStep
				}
			} else {
				if targetLiquidationPrice > position.LiquidationPrice {
					targetLeverage = currentLeverage - leverageStep
				} else {
					targetLeverage = currentLeverage + leverageStep
				}
			}
			targetLeverage = math.Round(targetLeverage*100) / 100
			// 在使用公式法调整后，可能再用步长减半调整，重置 calculatorUsedLastTime
			calculatorUsedLastTime = false
		}

		this.Infof("(%s) (%0d) - targetLeverage: %.2f, leverageStep: %v", symbol, i, targetLeverage, leverageStep)

		if currentLeverage >= this.getInstrument(symbol).MaxLeverage && targetLeverage >= this.getInstrument(symbol).MaxLeverage {
			this.Infof("(%s) current leverage hit maximum", symbol)
			deltaPrice = math.Abs(targetLiquidationPrice - position.LiquidationPrice)
			return deltaPrice, ""
		}

		// 重置价差符号
		lastDeltaPriceFlag = targetLiquidationPrice > position.LiquidationPrice

		success := this.setLeverage(symbol, targetLeverage, currentLeverage)
		if !success {
			return math.Abs(targetLiquidationPrice - position.LiquidationPrice), "设置杠杠率失败"
		}

		// 稍等检测是否一致，因为获取 Position 是同步 HTTP 请求，不需要等太久
		time.Sleep(time.Millisecond * 200)

		position, _ = this.getPosition(instrumentType, symbol)
		if position == nil {
			return deltaPrice, "获取当前持仓失败"
		}

		deltaPrice = math.Abs(targetLiquidationPrice - position.LiquidationPrice)

		if deltaPrice <= acceptableDelta {
			this.Infof("(%s) adjust liquidation success", symbol)
			return deltaPrice, ""
		}

		if targetLeverage >= this.getInstrument(symbol).MaxLeverage {
			this.Infof("(%s) leverage hit maximum", symbol)
			return deltaPrice, ""
		}
	}

	return deltaPrice, fmt.Sprintf("调整爆仓价失败, 目标价: %.2f, 当前价: %.2f, 当前杠杠率: %.2f", targetLiquidationPrice, position.LiquidationPrice, position.Leverage)
}

func (this *BitMEX) Qty2Size(instrumentType exchange.InstrumentType, symbol string, price float64, qty float64) (size float64, er error) {
	ins := this.getInstrument(symbol)
	reverse := false

	decimals := this.getCurrencyDecimals(ins.SettlCurrency)
	pow := math.Pow10(decimals)
	// 1 张合约的 XBT/USDT 价值
	// 反向合约: 1 / 价格 * $1
	// 线性 或 双币种: 乘数 * 价格
	// XBT 乘数 = Multiplier / 1e8
	// USDT 乘数 = Multiplier / 1e6
	reverse = this.IsReverseSymbol(instrumentType, symbol)
	if reverse {
		size = qty / price
	} else {
		multiplier := float64(ins.Multiplier) / pow
		size = qty * price * multiplier
	}
	size = math.Round(size*pow) / pow
	return
}

func (this *BitMEX) Size2Qty(instrumentType exchange.InstrumentType, symbol string, price float64, size float64) (qty float64, er error) {
	ins := this.getInstrument(symbol)
	if ins.InstrumentType() == exchange.Spot {
		return 0, exchange.ErrNotAvailableForInstrumentType
	}
	if this.IsReverseSymbol(instrumentType, symbol) {
		qty = size * price
	} else {
		decimals := this.getCurrencyDecimals(ins.SettlCurrency)
		multiplier := float64(ins.Multiplier) / math.Pow10(decimals)
		qty = size / (price * multiplier)
	}
	return math.Round(qty/ins.LotSize) * ins.LotSize, nil
}

func (this *BitMEX) CalcPrice(instrumentType exchange.InstrumentType, symbol string, qty float64, size float64) (price float64, er error) {
	ins := this.getInstrument(symbol)
	if ins.InstrumentType() == exchange.Spot {
		return 0, exchange.ErrNotAvailableForInstrumentType
	}
	if this.IsReverseSymbol(instrumentType, symbol) {
		price = qty / size
	} else {
		decimals := this.getCurrencyDecimals(ins.SettlCurrency)
		multiplier := float64(ins.Multiplier) / math.Pow10(decimals)
		price = size / (qty * multiplier)
	}
	return this.roundPrice(symbol, price), nil
}

func (this *BitMEX) FormatDisplayQty(instrumentType exchange.InstrumentType, symbol string, qty float64) string {
	ins := this.getInstrument(symbol)
	if ins.InstrumentType() == exchange.USDXMarginedFutures {
		decimals := this.getCurrencyDecimals(ins.SettlCurrency)
		multiplier := decimal.NewFromInt(ins.Multiplier).Div(decimal.NewFromFloat(math.Pow10(decimals)))
		qtyDec := decimal.NewFromFloat(qty).Mul(multiplier)
		qty, _ = qtyDec.Float64()
	}
	return strconv.FormatFloat(qty, 'f', -1, 64)
}

func (this *BitMEX) ReConnectWebsocket() {
	if this.wsConn == nil {
		return
	}
	this.Infof("[%s] close ws to resubscribe", this.ControllerID)
	this.wsConn.Close() // 断开重连，重新订阅；仅新增订阅无效，所以需要重连重新订阅
}

func (this *BitMEX) CloseWebsocket(stop bool) {
	if this.wsConn == nil {
		return
	}
	if stop {
		this.wsClosed = true
	}
	this.wsConn.Close()
}

// 实时连接
func (this *BitMEX) ConnectWebsocket(instrumentTypes []exchange.InstrumentType, connectedCallback func(connected bool)) {
	if this.apiKey.ApiSecret == "" {
		// 密码还未设置,稍后再试
		go this.reconnectLater()
		return
	}

	hostURL := WS_URL
	if this.IsTestnet {
		hostURL = TESTNET_WS_URL
	}

	u := url.URL{Scheme: "wss", Host: hostURL, Path: "/realtime"}
	this.Infof("[%s] connecting to %s", u.String())

	// 进行连接
	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		this.Errorf(" bitmex ws dial err: %s", err)
		go this.reconnectLater()
		return
	}
	this.wsConn = c

	defer func() {
		// 函数运行结束时关闭连接
		c.Close()
		this.Infof("[%s] websocket closed", this.ControllerID)
	}()

	done := make(chan struct{}) // 连接是否结束

	logined := false
	go func() {
		defer close(done)
		for {
			// 读取实时消息
			_, message, err := c.ReadMessage()
			if err != nil {
				this.Errorf("read message err: %s", err)

				go this.reconnectLater()
				return
			}
			if string(message) == "pong" {
				// 心跳消息
				continue
			}

			// this.Debugf(" bitmex websocket recv: %s", this.loggerID, message)

			// 未登录时登录
			if !logined {
				if this.wsLogin() {
					this.Infof("bitmex ws logined")
					logined = true
				}
			}

			var jsonData map[string]any
			err = json.Unmarshal(message, &jsonData)
			if err == nil {
				// 消息格式化后进行处理，如有订单更新，则调用回调
				// 根据消息
				request, _ := jsonData["request"].(map[string]any)

				if request != nil {
					success, _ := jsonData["success"].(bool)
					op, _ := request["op"].(string)
					if success && op == "authKeyExpires" {
						// 成功登录后，订阅订单消息
						this.subscribe()
						continue
					}
				}
				this.handleWebsocketMessage(jsonData, c)

			}
		}
	}()

	subscribeTicker := time.NewTicker(time.Second * 10)
	defer subscribeTicker.Stop()

	for {
		// 保存运行直到连接结束
		select {
		case <-subscribeTicker.C:
			this.sendWsMsg("ping")
			this.subscribeSymbols()
		case <-done:
			return
		}
	}
}

func (this *BitMEX) TranslateSymbolCode(futureCode *exchange.SymbolCode) (spotAndFutureSymbols []*exchange.SymbolPair, er error) {
	if futureCode.IsFuture() {
		this.instruments.Range(func(symbol string, v *Instrument) bool {
			if !v.IsFuture() {
				return true
			}
			currency := convertUnderlying(v.Underlying)
			monthDigits := "00"
			months := futureCode.Month()
			if govalidator.IsInt(string(symbol[len(symbol)-1])) {
				monthChar := string(symbol[len(symbol)-3])
				if d, found := convertMonthCode(monthChar); found {
					monthDigits = d
				} else {
					return true
				}
			}
			if futureCode.IsWildcard() {
				months = "00|03|06|09|12"
			}
			if strings.EqualFold(currency, futureCode.Coin()) {
				if strings.Contains(months, monthDigits) {
					// XBTH22 这种格式非常特殊，省略了 USD，需要单独处理
					if monthDigits != "00" && futureCode.InstrumentType() != exchange.USDXMarginedFutures {
						if convertUnderlying(symbol[:len(symbol)-3]) == futureCode.Coin() {
							spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(futureCode, "", symbol))
						}
					} else {
						suffix := "USD"
						if futureCode.InstrumentType() == exchange.USDXMarginedFutures {
							suffix = "USDT"
						}
						if (monthDigits == "00" && strings.HasSuffix(symbol, suffix)) ||
							(monthDigits != "00" && strings.HasSuffix(symbol[:len(symbol)-3], suffix)) {
							spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(futureCode, "", symbol))
							if !futureCode.IsWildcard() {
								return false
							}
						}
					}
				}
			}
			return true
		})
	} else {
		symbol := fmt.Sprintf("%s_USDT", futureCode.Coin())
		_, found := this.instruments.Load(symbol)
		if found {
			spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(futureCode, symbol, symbol))
		}
	}
	if len(spotAndFutureSymbols) == 0 {
		er = errors.New("future code not found")
		return
	}
	return
}

func (this *BitMEX) TranslateFutureSymbol(instrumentType exchange.InstrumentType, futureSymbol string, uSymbol string) (spotSymbol string, futureCode *exchange.SymbolCode, er error) {
	monthCodeMap := map[string]string{
		"H": "03",
		"M": "06",
		"U": "09",
		"Z": "12",
	}

	if uSymbol == "" {
		uSymbol = "USDT"
	}

	this.instruments.Range(func(symbol string, v *Instrument) bool {
		if !v.IsFuture() {
			return true
		}
		if symbol != futureSymbol {
			return true
		}
		// 如果是交割合约
		if govalidator.IsInt(string(symbol[len(symbol)-1])) {
			monthChar := string(symbol[len(symbol)-3])
			if monthDigits, ok := monthCodeMap[monthChar]; ok {
				currency := convertUnderlying(v.Underlying)

				code := fmt.Sprintf("%s%s", currency, monthDigits)
				if strings.EqualFold(v.SettlCurrency, uSymbol) {
					code += ".U"
				}
				futureCode = &exchange.SymbolCode{Code: code, USDXSymbol: uSymbol}
			}
		} else {
			currency := convertUnderlying(v.Underlying)
			code := fmt.Sprintf("%s00", currency)
			if strings.EqualFold(v.SettlCurrency, uSymbol) {
				code += ".U"
			}
			futureCode = &exchange.SymbolCode{Code: code, USDXSymbol: uSymbol}
		}
		return true
	})
	if futureCode == nil {
		er = errors.New("future symbol not found")
	}
	return
}

// 更新交易对信息，force 时忽略缓存
func (this *BitMEX) CacheInstruments(force bool) error {
	return this.cacheAllInstruments()
}

func (this *BitMEX) GetAccountBalances(instrumentType exchange.InstrumentType) (accountBalances []*exchange.AccountBalance, _ error) {
	path := "/api/v1/user/margin?currency=all"
	userMargins := &[]exchange.UserMargin{}
	err := this.sendHTTPRequest("GET", path, nil, userMargins, true)

	if err != nil {
		return nil, err
	}

	for _, userMargin := range *userMargins {
		if instrumentType == exchange.USDXMarginedFutures && !strings.EqualFold(userMargin.Currency, "USDt") {
			continue
		} else if instrumentType == exchange.CoinMarginedFutures && strings.EqualFold(userMargin.Currency, "USDt") {
			continue
		}

		decimals := this.getCurrencyDecimals(userMargin.Currency)
		total := userMargin.MarginBalance / math.Pow10(decimals)
		available := userMargin.AvailableMargin / math.Pow10(decimals)

		accountBalances = append(accountBalances, &exchange.AccountBalance{
			InstrumentType: instrumentType,
			Currency:       userMargin.Currency,
			Total:          total,
			Available:      available,
		})
	}

	return
}

func (this *BitMEX) GetAccountConfig(instrumentType exchange.InstrumentType) (*exchange.AccountConfig, error) {
	if instrumentType == exchange.Spot {
		return nil, exchange.ErrNotAvailableForInstrumentType
	}
	return &exchange.AccountConfig{
		MarginMode:       exchange.AccountMarginModeIsolated,
		DualPositionSide: false,
	}, nil
}

func (this *BitMEX) GetAccountCurrencies(instrumentType exchange.InstrumentType) (currencies []string, _ error) {
	return currencies, exchange.ErrNotImplemented
}

func (this *BitMEX) GetTradablePairs(instrumentType exchange.InstrumentType, uSymbol string) (pairs []string, _ error) {
	return pairs, exchange.ErrNotImplemented
}

func (this *BitMEX) QueryFundingHistory(instrumentType exchange.InstrumentType, symbol string, limit int, from time.Time, to time.Time) ([]*exchange.FundingHistory, error) {
	return []*exchange.FundingHistory{}, exchange.ErrNotImplemented
}

func (this *BitMEX) SetDualPositionSide(instrumentType exchange.InstrumentType, dualPositionSide bool) error {
	return exchange.ErrNotImplemented
}

func (this *BitMEX) SetLeverage(instrumentType exchange.InstrumentType, symbol string, marginMode exchange.MarginMode, side exchange.PositionSide, leverage float64) error {
	if ok := this.setLeverage(symbol, leverage, 0); !ok {
		return fmt.Errorf("set leverage err")
	}
	return nil
}

func (b *BitMEX) GetLeverage(instrumentType exchange.InstrumentType, symbol string, marginMode exchange.MarginMode) (leverageLong, leverageShort float64, err error) {
	return 0, 0, exchange.ErrNotImplemented
}

func (this *BitMEX) SetMarginMode(instrumentType exchange.InstrumentType, symbol string, mode exchange.MarginMode) error {
	return exchange.ErrNotImplemented
}

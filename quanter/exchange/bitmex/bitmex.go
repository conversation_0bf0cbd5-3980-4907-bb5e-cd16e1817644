package bitmex

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/url"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/quanter/exchange"

	resty "github.com/go-resty/resty/v2"
	"github.com/gorilla/websocket"
)

const HOST_URL = "www.bitmex.com"             // bitmex 域名
const TESTNET_HOST_URL = "testnet.bitmex.com" // bitmex 测试环境域名

const WS_URL = "ws.bitmex.com"
const TESTNET_WS_URL = "ws.testnet.bitmex.com"

const USE_CALCULATOR_THRESHOLD = 0.02 // 爆仓价差大于该比例时用公式计算杠杠率

var _ exchange.Exchange = (*BitMEX)(nil)

var ExtKeyPosCost = "PosCost"
var ExtKeyPosCross = "PosCross"
var ExtKeyMaintMarginReq = "MaintMarginReq"

type BitMEX struct {
	exchange.BaseExchange

	instruments       *xsync.MapOf[string, *Instrument]
	wsConn            *websocket.Conn
	wsMutex           sync.Mutex
	wsClosed          bool
	subscribedSymbols []string

	apiKey *exchange.APISecret
}

// 合约数据
type Instrument struct {
	Symbol           string  // 合约名，如 XBTUSD
	Typ              string  // 合约类型，现货 IF 开头，期货 FF 开头
	MarkPrice        float64 // 标记价格
	LastPrice        float64 // 最新价格
	FundingRate      float64 // 当前资金费用
	FundingTimestamp string  // 资金费用有效期
	InitMargin       float64 // 初始保证金率，最大杠杠率 = 1/InitMargin
	TickSize         float64 // 价格最小变动单位
	LotSize          float64 // 合约数量最小单位
	Multiplier       int64   // multiplier 计算价值系数
	MaxLeverage      float64 // 最大杠杆率
	SettlCurrency    string
	Underlying       string
	QuoteCurrency    string
	Timestamp        string
	updatedAt        int64 // 更新时间
}

func (i Instrument) IsExpired() bool {
	return (i.updatedAt + 3600) < time.Now().Unix()
}

func (i Instrument) IsFuture() bool {
	return strings.HasPrefix(i.Typ, "FF")
}

func (i Instrument) InstrumentType() exchange.InstrumentType {
	if i.IsFuture() {
		if strings.EqualFold(i.SettlCurrency, "USDT") {
			return exchange.USDXMarginedFutures
		} else {
			return exchange.CoinMarginedFutures
		}
	} else {
		return exchange.Spot
	}
}

// BitMEX 返回的 K 线数据结构
type KlineHistory struct {
	S string    // 成功或失败信息，如 ok
	T []int64   // 时间戳
	O []float64 // 开盘价
	C []float64 // 收盘价
	H []float64 // 最高价
	L []float64 // 最低价
}

// 订单信息
type Order struct {
	OrderID      string  `json:"orderID"`      // 唯一ID
	ClOrdID      string  `json:"clOrdID"`      // 自定义ID
	Symbol       string  `json:"symbol"`       // 合约名
	Side         string  `json:"side"`         // 方向
	OrderQty     int     `json:"orderQty"`     // 数量
	Price        float64 `json:"price"`        // 委托价
	StopPx       float64 `json:"stopPx"`       // 触发价
	Currency     string  `json:"currency"`     // 计价币种
	OrdType      string  `json:"ordType"`      // 类型
	TimeInForce  string  `json:"timeInForce"`  // 有效时间，默认都是 GoodTillCancel
	ExecInst     string  `json:"execInst"`     // 附加指令，如 Close: 平仓，LastPrice: 使用最新成交作为触发价
	OrdStatus    string  `json:"ordStatus"`    // 状态，目前已知 New, Filled, PartiallyFilled, Canceled, Rejected
	Triggered    string  `json:"triggered"`    // 触发信息，未触发时为空字符串
	LeavesQty    int     `json:"leavesQty"`    // 剩余数量
	CumQty       int     `json:"cumQty"`       // 成交数量
	AvgPx        float64 `json:"avgPx"`        // 成交均价
	Text         string  `json:"text"`         // BitMEX 自带备注信息
	TransactTime string  `json:"transactTime"` // 交易时间
	Timestamp    string  `json:"timestamp"`    // 交易时间
}

type Position struct {
	ExchangeName     string  // 交易所名字
	Symbol           string  // 合约名，如 XBTUSD
	CurrentQty       float64 // 持仓数量
	Leverage         float64 // 杠杠率
	AvgEntryPrice    float64 // 开仓均价
	LiquidationPrice float64 // 强平价
	MarkPrice        float64 // 标记价
	LastPrice        float64 // 最新价，实际上接口返回的是标记价一样的值
	MaintMarginReq   float64 // 维持保证金率
	PosCost          float64 // 持仓价值，单位聪
	PosCross         float64 // 具体含义不清楚，BitMEX 计算杠杠率用
	UnrealisedPnl    float64 // 浮动盈亏
	RealisedPnl      float64 // 已实现盈亏
	Margin           float64 // 逐仓保证金
}

// 订单是否开放中
func (this *Order) IsOpen() bool {
	return this.OrdStatus != "Filled" && this.OrdStatus != "PartiallyFilled" && this.OrdStatus != "Canceled" && this.OrdStatus != "Rejected"
}

// 订单是否已触发
func (this *Order) IsTriggered() bool {
	return this.Triggered != ""
}

// 订单是否是平仓单
func (this *Order) IsCloseOrder() bool {
	return strings.Contains(this.ExecInst, "Close")
}

// 创建订单参数
type CreateOrderArgs struct {
	ClOrdID  string  `json:"clOrdID,omitempty"`  // 自定义ID，可不传
	Symbol   string  `json:"symbol"`             // 合约名
	Side     string  `json:"side"`               // 方向
	Price    float64 `json:"price,omitempty"`    // 委托价
	StopPx   float64 `json:"stopPx,omitempty"`   // 触发价
	OrderQty int     `json:"orderQty,omitempty"` // 数量，平仓时不传
	OrdType  string  `json:"ordType"`            // 订单类型
	ExecInst string  `json:"execInst"`           // 附加指令
}

// 修改订单参数
type UpdateOrderArgs struct {
	OrderID  string  `json:"orderID"`            // 唯一ID
	Price    float64 `json:"price"`              // 委托价
	StopPx   float64 `json:"stopPx,omitempty"`   // 触发价，已触发的订单不传
	OrderQty float64 `json:"orderQty,omitempty"` // 数量，平仓时不传
}

// 获取基本请求 request
func (this *BitMEX) getBaseRequest() *resty.Request {
	client := this.Client
	hostURL := HOST_URL
	if this.IsTestnet {
		// 测试网络时使用测试域名
		hostURL = TESTNET_HOST_URL
	}
	client.
		SetBaseURL(fmt.Sprintf("https://%s", hostURL)).
		SetTimeout(30 * time.Second).     // 设置超时时间，超时无反应则返回超时错误
		SetRetryCount(3).                 // 请求失败时的重试次数
		SetRetryWaitTime(5 * time.Second) // 重试前等待时间

	req := client.R()
	req.SetHeader("Content-Type", "application/json")
	return req
}

// 获取合约的最新信息
func (this *BitMEX) fetchInstrument(symbol string) *Instrument {
	path := fmt.Sprintf("/api/v1/instrument?symbol=%s&count=1", symbol)
	instruments := &[]Instrument{}
	err := this.sendHTTPRequest("GET", path, nil, instruments, false)
	if err != nil {
		this.Errorf("(%s) get instrument err: %s", symbol, err)
		return nil
	}

	if len(*instruments) == 0 {
		this.Errorf("(%s) got 0 instruments", symbol)
		return nil
	}

	instrument := (*instruments)[0]
	instrument.updatedAt = time.Now().Unix()
	return &instrument
}

func (this *BitMEX) getInstrument(symbol string) (instrument *Instrument) {
	instrument, _ = this.instruments.Load(symbol)
	if instrument == nil || instrument.IsExpired() {
		ins := this.cacheLatestInstrument(symbol)
		if ins != nil {
			instrument = ins
		}
	}
	return instrument
}

// 获取最新K线数据
func (this *BitMEX) getHistory(symbol string, periodHour, num int) (klines *KlineHistory, er error) {
	toTime := time.Now()                                               // 当前时间
	fromTime := toTime.Add(-time.Hour * time.Duration(periodHour*num)) // 当前时间 - num * PeriodHour 个小时

	// 获取小时 K 线，时间从 fromTime 到 toTime
	path := fmt.Sprintf(`/api/udf/history?symbol=%s&resolution=60&from=%d&to=%d`, symbol, fromTime.Unix(), toTime.Unix())
	this.Infof("(%s) get bm klines url: %s", symbol, path)

	klines = &KlineHistory{}
	err := this.sendHTTPRequest("GET", path, nil, klines, false)

	if err != nil {
		er = fmt.Errorf("get history KLines err: %s", err)
		return
	}

	if klines.S != "ok" {
		er = fmt.Errorf("get history response err, resp not ok")
		return
	}
	if len(klines.O) == 0 {
		er = errors.New("get kline history error, empty result")
		return
	}
	return
}

type WizKline struct {
	Open      float64
	Close     float64
	High      float64
	Low       float64
	Timestamp int64
}

func (this *BitMEX) getKlineByWiz(symbol string, periodHour, num int) (klines *KlineHistory, er error) {
	ins := this.getInstrument(symbol)
	baseCoin := ins.Underlying
	quoteCoin := ins.QuoteCurrency
	if baseCoin == "XBT" {
		baseCoin = "BTC"
	}
	fromTs := time.Now().Add(-time.Hour * time.Duration(periodHour*num)).Unix() // 当前时间 - num * PeriodHour 个小时
	var toTs int64
	limit := 500

	wizKlines := []WizKline{}
	for {
		path := fmt.Sprintf("https://api.wizhodl.com/v3/market/kline/graph?exchange=BitMEX&pair=%s/%s:%s&period=1h&time=%v&limit=%v", baseCoin, quoteCoin, symbol, toTs, limit)
		this.Infof("(%s) get wiz klines url: %s", symbol, path)
		resp, err := this.getBaseRequest().Get(path)
		if err != nil {
			er = fmt.Errorf("get wiz KLines err: %s", err)
			return
		}

		var jsonData map[string]any
		if err := json.Unmarshal(resp.Body(), &jsonData); err == nil {
			data, _ := jsonData["data"].(map[string]any)
			if data != nil {
				klines := []WizKline{}
				graph, _ := json.Marshal(data["graph"])
				json.Unmarshal(graph, &klines)
				wizKlines = append(wizKlines, klines...)
				if len(klines) > 0 && klines[len(klines)-1].Timestamp > fromTs {
					toTs = klines[len(klines)-1].Timestamp - 3600
					continue
				}
			}
		}
		break
	}

	// 时间从早到晚排序
	sort.SliceStable(wizKlines, func(i, j int) bool {
		return wizKlines[i].Timestamp < wizKlines[j].Timestamp
	})

	klines = &KlineHistory{}
	for _, kline := range wizKlines {
		klines.T = append(klines.T, kline.Timestamp)
		klines.O = append(klines.O, kline.Open)
		klines.H = append(klines.H, kline.High)
		klines.L = append(klines.L, kline.Low)
		klines.C = append(klines.C, kline.Close)
	}
	if len(klines.O) == 0 {
		er = errors.New("get wiz klines error, empty result")
		return
	}
	return
}

// BitMEX 数据签名，调用 API 或连接 WebSocket 时需要
// 传入待签名的数据，返回签名字符串
func (this *BitMEX) signData(data string) string {
	h := hmac.New(sha256.New, this.apiKey.ApiSecret.Bytes())
	h.Write([]byte(data))
	sha := hex.EncodeToString(h.Sum(nil))
	// this.Infof("result: %s", sha)
	return sha
}

// 获取加密的请求 request
func (this *BitMEX) getSignedRequest(method string, path string, data string) *resty.Request {
	expires := time.Now().Unix() + 60
	signature := this.signData(fmt.Sprintf("%s%s%d%s", method, path, expires, data))

	req := this.getBaseRequest()
	req.SetHeader("api-expires", fmt.Sprintf("%d", expires)). // 签名超时时间
									SetHeader("api-key", this.apiKey.ApiKey). // apikey
									SetHeader("api-signature", signature)     // 签名

	return req
}

func (this *BitMEX) sendHTTPRequest(httpMethod, requestPath string, data, result any, authenticated bool) (_ error) {
	payload := []byte("")
	if data != nil {
		payloadData, err := json.Marshal(data)
		if err != nil {
			return errors.New("sendHTTPRequest: Unable to JSON request")
		}
		payload = payloadData
	}

	var req *resty.Request
	if authenticated {
		req = this.getSignedRequest(httpMethod, requestPath, string(payload))
	} else {
		req = this.getBaseRequest()
	}

	if data != nil {
		req.SetBody(payload)
	}

	if result != nil {
		req.SetResult(result).ForceContentType("application/json")
	}

	resp, err := req.Execute(httpMethod, requestPath)
	if err != nil {
		this.Errorf("send http request(%s) err: %s", requestPath, err)
		return err
	}

	if resp.StatusCode() != 200 {
		this.Errorf("request resp err: %s %s", resp.Status(), resp)
		err := fmt.Errorf("request resp err: %d %s", resp.StatusCode(), resp.String())
		if strings.Contains(resp.String(), "RateLimitError") {
			err = fmt.Errorf("rate limit exceeded. should wait for %s seconds.", resp.Header().Get("retry-after"))
		}
		return err
	}

	return nil
}

type Asset struct {
	Currency string `json:"currency"`
	Scale    int    `json:"scale"`
	Enabled  bool   `json:"enabled"`
}

var currencyDecimals map[string]int

func (this *BitMEX) getCurrencyDecimals(currency string) int {
	if currencyDecimals == nil {
		currencyDecimals = make(map[string]int)
		assets := &[]Asset{}
		err := this.sendHTTPRequest("GET", "/api/v1/wallet/assets", nil, assets, false)
		if err != nil {
			this.Errorf("fetch assets err: %s", err)
			return 0
		}
		for _, asset := range *assets {
			currencyDecimals[asset.Currency] = asset.Scale
		}
	}

	if decimals, ok := currencyDecimals[currency]; ok {
		return decimals
	}
	return 0
}

// 获取最新合约信息
func (this *BitMEX) cacheLatestInstrument(symbol string) (instrument *Instrument) {
	instrument = this.fetchInstrument(symbol)
	if instrument == nil || instrument.InitMargin == 0 {
		return nil
	}
	this.instruments.Store(symbol, instrument)

	leverage := 1 / instrument.InitMargin
	instrument.MaxLeverage = leverage

	return instrument
}

func (this *BitMEX) cacheAllInstruments() error {
	path := "/api/v1/instrument/active"
	instruments := &[]Instrument{}
	err := this.sendHTTPRequest("GET", path, nil, instruments, false)
	if err != nil {
		return fmt.Errorf("fetch all instrument err: %s", err)
	}

	if len(*instruments) == 0 {
		return fmt.Errorf("fetch all instrument err, got 0 instruments")
	}

	for i := range *instruments {
		instrument := (*instruments)[i]
		instrument.updatedAt = time.Now().Unix()
		instrument.MaxLeverage = 1 / instrument.InitMargin
		this.instruments.Store(instrument.Symbol, &instrument)
	}
	return nil
}

// 设置合约 symbol 的杠杠率，返回是否调用成功
func (this *BitMEX) setLeverage(symbol string, leverage, origLeverage float64) (success bool) {
	if leverage < this.MinLeverage {
		leverage = this.MinLeverage
	} else if leverage > this.getInstrument(symbol).MaxLeverage {
		leverage = this.getInstrument(symbol).MaxLeverage
	}

	if math.Abs(origLeverage-leverage) < 0.01 && !exchange.AlmostEqual(math.Abs(origLeverage-leverage), 0.01) {
		return true
	}

	data := map[string]any{
		"symbol":   symbol,
		"leverage": math.Round(leverage*100) / 100,
	}
	this.Debugf("(%s) set leverage %#v", symbol, data)
	err := this.sendHTTPRequest("POST", "/api/v1/position/leverage", data, nil, true)

	if err == nil {
		return true
	} else {
		this.Errorf("(%s) set leverage error: %s", symbol, err)
		return false
	}
}

func (this *BitMEX) getPosition(instrumentType exchange.InstrumentType, symbol string) (*exchange.Position, error) {
	positions, err := this.GetPositions(instrumentType, symbol, false)
	if err != nil {
		return nil, err
	}
	if len(positions) == 0 {
		return nil, nil
	}
	return positions[0], nil
}

// 按将价格四舍五入到最小价格单位
func (this *BitMEX) roundPrice(symbol string, price float64) float64 {
	return exchange.RoundPrice(price, this.getInstrument(symbol).TickSize)
}

// 获取订单列表，根据参数不同可实现获取进行中订单或指定ID订单
func (this *BitMEX) getOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string, openOnly bool, orderID string) ([]*exchange.Order, error) {
	path := "/api/v1/order?reverse=true&symbol=" + symbol
	if openOnly {
		// 进获取进行中的订单
		filter := url.QueryEscape(`{"open":true}`)
		path = fmt.Sprintf(`%s&filter=%s`, path, filter)
	} else if orderID != "" {
		// 获取指定 ID 订单
		filter := url.QueryEscape(fmt.Sprintf(`{"orderID":"%s"}`, orderID))
		path = fmt.Sprintf(`%s&filter=%s`, path, filter)
	}
	this.Infof("(%s) get orders req: %s", symbol, path)
	bmOrders := &[]Order{}
	err := this.sendHTTPRequest("GET", path, nil, bmOrders, true)

	if err != nil {
		this.Errorf("(%s) get orders err: %s", symbol, err)
		return nil, err
	}

	// 将 BM order 转换为 exchange.Order
	var orders []*exchange.Order
	for _, order := range *bmOrders {
		order := convertOrderToExchangeOrder(order)
		ins := this.getInstrument(order.Symbol)
		order.InstrumentType = ins.InstrumentType()
		if instrumentType != exchange.UnknownInstrumentType && order.InstrumentType != instrumentType {
			continue
		}
		if orderType != exchange.UnknownOrderType && order.Type != orderType {
			continue
		}
		orders = append(orders, order)
	}
	return orders, nil
}

// 转换 BitMEX order 结构到通用结构
func convertOrderToExchangeOrder(order Order) *exchange.Order {
	orderSide := exchange.OrderSideBuy
	if strings.EqualFold(order.Side, "Sell") {
		orderSide = exchange.OrderSideSell
	}
	var status exchange.OrderStatus
	switch order.OrdStatus {
	case "New":
		status = exchange.OrderStatusNew
	case "PartiallyFilled":
		status = exchange.OrderStatusPartialFilled
	case "Filled":
		status = exchange.OrderStatusFilled
	case "Canceled":
		status = exchange.OrderStatusCancelled
	case "Rejected":
		status = exchange.OrderStatusCancelled
	default:
		status = exchange.UnknownOrderStatus
	}

	if status == exchange.OrderStatusNew && order.IsTriggered() {
		status = exchange.OrderStatusTriggered
	}

	var orderType exchange.OrderType
	switch order.OrdType {
	case "Limit":
		orderType = exchange.Limit
	case "Market":
		orderType = exchange.Market
	case "StopLimit":
		orderType = exchange.StopLimit
	}

	t, _ := time.Parse(time.RFC3339, order.Timestamp)
	return &exchange.Order{
		Symbol:       order.Symbol,
		OrderID:      order.OrderID,
		Side:         orderSide,
		Type:         orderType,
		Qty:          float64(order.OrderQty),
		Price:        order.Price,
		TriggerPrice: order.StopPx,
		Status:       status,
		ReduceOnly:   order.IsCloseOrder(),
		ExecQty:      float64(order.CumQty),
		ExecPrice:    order.AvgPx,
		CreateTime:   &t,
		UpdateTime:   &t,
	}
}

// 资金费率是否过期
func (this *BitMEX) isFundingRateExpired(symbol string) bool {
	if this.getInstrument(symbol) == nil {
		return true
	}

	FundingTimestamp := this.getInstrument(symbol).FundingTimestamp
	if FundingTimestamp == "" {
		return true
	}
	now := time.Now()
	t, _ := time.Parse(time.RFC3339, FundingTimestamp)
	return t.Before(now)
}

// 计算持仓价值
func (this *BitMEX) calculatePosCost(symbol string, qty float64, price float64) float64 {
	var v float64
	if this.getInstrument(symbol).Multiplier >= 0 {
		v = math.Round(float64(this.getInstrument(symbol).Multiplier) * price)
	} else {
		v = math.Round(float64(this.getInstrument(symbol).Multiplier) / price)
	}
	return math.Round(qty * v)
}

// 重新实时连接，用于断线重连
func (this *BitMEX) reconnectLater() {
	if this.wsClosed {
		return
	}
	this.Infof("ws reconnect in 30 seconds...")
	time.Sleep(time.Second * 30)
	this.ConnectWebsocket([]exchange.InstrumentType{}, nil)
}

func (this *BitMEX) subscribe() {
	var symbols []string
	this.instruments.Range(func(symbol string, _ *Instrument) bool {
		symbols = append(symbols, symbol)
		return true
	})

	subscribeArgs := []string{`"margin"`}
	for _, symbol := range symbols {
		subscribeArgs = append(subscribeArgs, fmt.Sprintf(`"order:%s"`, symbol))
	}
	subscribeMsg := fmt.Sprintf(`{"op": "subscribe", "args": [%s]}`, strings.Join(subscribeArgs, ", "))
	this.Infof("subscribeMsg: %s", subscribeMsg)
	this.sendWsMsg(subscribeMsg)
}

// 实时连接用户登录
func (this *BitMEX) wsLogin() (success bool) {
	expires := time.Now().Unix() + 60
	signature := this.signData(fmt.Sprintf("%s%s%d%s", "GET", "/realtime", expires, ""))
	msg := fmt.Sprintf(`{"op": "authKeyExpires", "args": ["%s", %d, "%s"]}`, this.apiKey.ApiKey, expires, signature) // 登录指令
	// this.Infof("ws: %s", msg)
	err := this.sendWsMsg(msg)
	return err == nil
}

func (this *BitMEX) sendWsMsg(msg string) error {
	this.wsMutex.Lock()
	defer this.wsMutex.Unlock()

	if this.wsConn == nil {
		return errors.New("wsConn is nil")
	}

	return this.wsConn.WriteMessage(websocket.TextMessage, []byte(msg))
}

func (this *BitMEX) subscribeSymbols() {
	if !this.EnableRealtimePrice {
		return
	}

	symbolsNeed := []string{}
	for _, priceTrigger := range this.PriceTriggers {
		symbolsNeed = append(symbolsNeed, priceTrigger.Symbol)
	}

	for _, watch := range this.PriceWatches {
		symbolsNeed = append(symbolsNeed, watch.Symbol)
	}

	for _, symbol := range symbolsNeed {
		if !exchange.SliceContains(this.subscribedSymbols, symbol) {
			msg := fmt.Sprintf(
				`{"op":"subscribe","args":["instrument:%s"]}`,
				symbol,
			)
			if err := this.sendWsMsg(msg); err != nil {
				this.Errorf("subscribe msg error: %s", err)
			} else {
				this.subscribedSymbols = append(this.subscribedSymbols, symbol)
			}
		}
	}
}

// 处理接受的实时消息，返回订单是否有更新
func (this *BitMEX) handleWebsocketMessage(msg map[string]any, conn *websocket.Conn) {
	// this.Infof("handle websocket message", this.loggerID)

	table, _ := msg["table"].(string)
	action, _ := msg["action"].(string)

	if table == "order" {
		rawMsg, _ := json.Marshal(msg)
		this.Infof("order ws message: %s", rawMsg)
	}

	// if table == "margin" {
	// 	rawMsg, _ := json.Marshal(msg)
	// 	this.Infof("margin ws message: %s", this.loggerID, rawMsg)
	// }

	// this.Debugf("bitmex websocket message: %v", this.GetLoggerID(), msg)

	if table == "order" && action == "update" {
		// 订单有更新
		orders, ok := msg["data"].([]any)
		if ok {
			for _, order := range orders {
				orderMap, ok := order.(map[string]any)
				if !ok {
					break
				}

				symbol := orderMap["symbol"].(string)
				if symbol != "" {
					ins := this.getInstrument(symbol)
					insType := exchange.UnknownInstrumentType
					if ins != nil {
						insType = ins.InstrumentType()
					}
					// TODO 完善订单字段
					this.OrderUpdatedCallback(&exchange.Order{
						InstrumentType: insType,
						Symbol:         symbol,
					})
				}
			}
		}
	} else if table == "margin" && (action == "partial" || action == "update") {
		userMargins, ok := msg["data"].([]any)
		if ok {
			for _, userMargin := range userMargins {
				marginData, _ := json.Marshal(userMargin)
				// this.Debugf("margin ws message: %s", this.loggerID, marginData)
				margin := &exchange.UserMargin{}
				json.Unmarshal(marginData, margin)

				if margin.MarginBalance == 0 && margin.AvailableMargin > 0 {
					// 没有 MarginBalance 数据时不更新
					break
				}

				if margin.Currency != "" {
					decimals := this.getCurrencyDecimals(margin.Currency)
					margin.MarginBalance = margin.MarginBalance / math.Pow10(decimals)
					margin.WalletBalance = margin.WalletBalance / math.Pow10(decimals)
					this.MarginUpdatedCallback(margin, margin.Currency)
				}
			}
		}
	} else if table == "instrument" {
		instruments, ok := msg["data"].([]any)
		if ok {
			for _, insData := range instruments {
				bytes, _ := json.Marshal(insData)
				ins := &Instrument{}
				json.Unmarshal(bytes, ins)
				if ins.LastPrice == 0 {
					continue
				}
				t, _ := time.Parse(time.RFC3339, ins.Timestamp)
				exTicker := &exchange.Ticker{
					InstrumentType: ins.InstrumentType(),
					Time:           t.Unix() * 1000,
					Symbol:         ins.Symbol,
					Close:          ins.LastPrice,
				}
				this.StoreTickerCache(exTicker)
				this.CheckPriceTrigger(exTicker.InstrumentType, exTicker.Symbol, exTicker.Close, t)
			}
		}
	}
}

func convertUnderlying(underlying string) string {
	switch underlying {
	case "XBT":
		underlying = "BTC"
	}
	return underlying
}

func convertMonthCode(code string) (digits string, found bool) {
	monthCodeMap := map[string]string{
		"H": "03",
		"M": "06",
		"U": "09",
		"Z": "12",
	}
	if value, ok := monthCodeMap[code]; ok {
		digits = value
		found = true
	}
	return
}

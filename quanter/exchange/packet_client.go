package exchange

import (
	"encoding/base64"
	"fmt"
	"strings"

	"github.com/wizhodl/quanter/secrets"
)

// all fields in SlackRequest are base64 encoded
// to protect sensitive data been logged
// and to avoid special characters in websocket
type SlackRequest struct {
	Timestamp   string `json:"timestamp,omitempty"`
	Msg         string `json:"msg,omitempty"`
	FileURL     string `json:"file_url,omitempty"`
	FileContent string `json:"file_content,omitempty"`
}

// 解密 slack request 的所有字段，其中 msg 是加密的，其他仅仅用 base64 解码即可
// encryptKey isn't secrets.Password, use APISecret instead
// because when initial slack request happends, the secrets.Password may not be set yet
func (this *SlackRequest) Decode(key secrets.SecretString) (msg string, fileURL string, fileContent []byte, er error) {
	ajustedKey := secrets.AdjustKey(key.Bytes(), 0)
	s, err := secrets.AesDecrypt(this.Msg, []byte(ajustedKey))
	if err != nil {
		er = fmt.Errorf("decode msg failed: %w", err)
		return
	}
	msg = string(s)

	d, err := base64.StdEncoding.DecodeString(this.FileURL)
	if err != nil {
		er = fmt.Errorf("decode fileURL failed: %w", err)
		return
	}
	fileURL = string(d)

	fileContent, err = base64.StdEncoding.DecodeString(this.FileContent)
	if err != nil {
		er = fmt.Errorf("decode fileContent failed: %w", err)
		return
	}

	return
}

type ClientPacket struct {
	PacketHeader
	SlackRequest *SlackRequest `json:"slack_request,omitempty"`
}

/* 客户端 packet 相关函数 */

func NewSubscribePacket(apiKey string, key string) *ClientPacket {
	p := &ClientPacket{
		PacketHeader: NewPacketHeader(apiKey, EventSubscribe, key, ""),
	}
	p.SetID()
	return p
}

func NewUnSubscribePacket(apiKey string, key string) *ClientPacket {
	p := &ClientPacket{
		PacketHeader: NewPacketHeader(apiKey, EventUnsubscribe, key, ""),
	}
	p.SetID()
	return p
}

func NewPingPacket(apiKey string) *ClientPacket {
	p := &ClientPacket{
		PacketHeader: NewPacketHeader(apiKey, EventPing, "", ""),
	}
	p.SetID()
	return p
}

// encode all the data in base64 to protect sensitive data been logged
// encryptKey isn't secrets.Password, use APISecret instead
// because when initial slack request happends, the secrets.Password may not be set yet
func NewSlackRequestPacket(encryptKey secrets.SecretString, apiKey string, channel, msg, fileURL string, fileContent []byte, timestamp string) (*ClientPacket, error) {
	p := &ClientPacket{
		PacketHeader: NewPacketHeader(apiKey, EventSlackRequest, channel, ""),
	}
	var m string
	var err error
	// 如果设置了密码，则加密消息
	ajustedKey := secrets.AdjustKey(encryptKey.Bytes(), 0)
	m, err = secrets.AesEncrypt([]byte(msg), []byte(ajustedKey))
	if err != nil {
		return nil, fmt.Errorf("encrypt slack request packet.msg failed: %w", err)
	}

	p.SlackRequest = &SlackRequest{
		Timestamp:   timestamp,
		Msg:         m,
		FileURL:     base64.StdEncoding.EncodeToString([]byte(fileURL)),
		FileContent: base64.StdEncoding.EncodeToString(fileContent),
	}
	p.SetID()
	return p, nil
}

func (this *ClientPacket) CheckSubscribe() (result bool, key string) {
	if this.Event == EventSubscribe && !strings.HasSuffix(this.Key, "slack") {
		return true, this.Key
	}
	return false, ""
}

func (this *ClientPacket) CheckSlackSubscribe() (result bool, key string) {
	if this.Event == EventSubscribe && strings.HasSuffix(this.Key, "slack") {
		return true, this.Key
	}
	return false, ""
}

func (this *ClientPacket) CheckUnsubscribe() (result bool, key string) {
	if this.Event == EventUnsubscribe {
		return true, this.Key
	}
	return false, ""
}

func (this *ClientPacket) CheckPing() (result bool) {
	return this.Event == EventPing
}

func (this *ClientPacket) CheckSlackRequest() (result bool, channel string) {
	if this.Event == EventSlackRequest {
		return true, this.Key
	}
	return false, ""
}

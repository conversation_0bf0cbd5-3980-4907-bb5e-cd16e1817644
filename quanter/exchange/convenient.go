package exchange

import (
	"errors"
	"fmt"
	"time"

	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/common/zlog"

	resty "github.com/go-resty/resty/v2"
)

func GetFixerExchangeRate(accessKey, base, quote string) (float64, error) {
	if accessKey == "" {
		return 0, errors.New("need fixer accessKey")
	}
	cacheKey := fmt.Sprintf("%s||%s||%s", "Fixer", accessKey[:4], fmt.Sprintf("%s_%s", base, quote))
	cacheTime := time.Hour * 24
	if item, found := cachedSpotPrices.Load(cacheKey); found {
		since := time.Since(item.CacheTime)
		timeDiff := since - cacheTime
		if timeDiff < 0 {
			zlog.Debugf("get fixer exchange rate price from cache (%s), value: %f, cache time: %s", cacheKey, item.Value, item.CacheTime)
			return item.Value, nil
		}
	}

	url := fmt.Sprintf("https://api.apilayer.com/fixer/latest?base=%s&symbols=%s", base, quote)
	resp, err := resty.New().R().
		SetHeader("apikey", accessKey).Get(url)
	if err != nil {
		return 0, err
	}
	ret := gjson.Parse(resp.String())
	if !ret.Get("success").Bool() {
		return 0, fmt.Errorf("fixer api failed, error: %s", ret.Get("message").String())
	}

	rate := ret.Get("rates").Get(quote).Float()
	if rate == 0 {
		return 0, fmt.Errorf("fixer api did not return rate for %s/%s", base, quote)
	}

	item := &Float64CacheItem{Value: rate, CacheTime: time.Now()}
	cachedSpotPrices.Store(cacheKey, item)
	zlog.Debugf("write new fixer rate cache (%s), value: %f, cache time: %s", cacheKey, item.Value, item.CacheTime)

	return rate, nil
}

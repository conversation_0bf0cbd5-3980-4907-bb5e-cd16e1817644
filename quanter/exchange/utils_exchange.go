package exchange

import (
	"fmt"
	"strings"

	"github.com/shopspring/decimal"
)

func RenderPositions(ex Exchange, positions []*Position) (result string, er error) {
	t := NewTable()
	t.<PERSON>Header([]string{"Symbol", "Qty", "Value", "Entry Price", "Liq. Price", "Mark Price", "PNL"})
	for _, position := range positions {
		value, _ := ex.Qty2Size(position.InstrumentType, position.Symbol, position.EntryPrice, position.Qty)
		ins, err := ex.GetInstrument(position.InstrumentType, position.Symbol)
		if err != nil {
			return "", err
		}
		if position.Qty == 0 {
			continue
		}
		_, _, pnl := position.GetPNL(ins.SettleCurrency)
		t.AddRow([]string{
			position.Symbol,
			ex.FormatQty(position.InstrumentType, position.Symbol, position.Qty),
			formatAmount(value, ins.SettleCurrency),
			ex.FormatPrice(position.InstrumentType, position.Symbol, position.EntryPrice),
			ex.FormatPrice(position.InstrumentType, position.Symbol, position.LiquidationPrice),
			ex.FormatPrice(position.InstrumentType, position.Symbol, position.MarkPrice),
			formatAmount(pnl, ins.SettleCurrency),
		})
	}
	if len(t.Rows) == 0 {
		return "", nil
	}
	return t.Render(), nil
}

func formatAmount(amount float64, coin string) string {
	if amount == 0 {
		return "-"
	}
	decimals := 4
	if strings.HasPrefix(coin, "USD") {
		decimals = 2
	} else if strings.EqualFold(coin, "CNY") {
		decimals = 0
	}
	return decimal.NewFromFloat(amount).Round(int32(decimals)).String() + " " + coin
}

func ParseSymbolsFromSymbolCodes(ex Exchange, codeStr string, USDXSymbol string) (allSymbolCodeStrs []string, spotSymbols []string, futuresSymbols []string, er error) {
	parts := strings.Split(codeStr, ",")

	for _, code := range parts {
		if len(code) < 2 {
			er = fmt.Errorf("code %s length must greater than 2", code)
			return
		}
		symbolCode, err := NewSymbolCode(code, USDXSymbol)
		if err != nil {
			er = fmt.Errorf("parse symbol code failed: %s", err)
			return
		}
		allSymbolCodeStrs = append(allSymbolCodeStrs, symbolCode.String())
		symbolPairs, err := ex.TranslateSymbolCode(symbolCode)
		if err != nil {
			er = fmt.Errorf("parse symbol code failed: %s", err)
			return
		}
		for _, symbolPair := range symbolPairs {
			spotSymbols = append(spotSymbols, symbolPair.Left.Symbol)
			futuresSymbols = append(futuresSymbols, symbolPair.Right.Symbol)
		}
	}
	return
}

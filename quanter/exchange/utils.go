package exchange

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
)

func SliceStringJoin[T ~string](list []T, sep string, quote bool) string {
	newList := []string{}
	if quote {
		for _, item := range list {
			newList = append(newList, fmt.Sprintf(`"%s"`, item))
		}
	} else {
		for _, item := range list {
			newList = append(newList, fmt.Sprintf("%s", item))
		}
	}
	return strings.Join(newList, sep)
}

type comparable interface {
	~string | ~int64 | ~int | ~float64 | ~byte
}

func SliceContains[T comparable](slice []T, target T) (exist bool) {
	exist = false
	for _, item := range slice {
		if item == target {
			exist = true
			break
		}
	}
	return
}

func StringEnumContains[T ~string](list []T, target T) bool {
	for _, item := range list {
		if item == target {
			return true
		}
	}
	return false
}

func FormatShortTimeStr(t *time.Time, withYear bool) string {
	if t == nil {
		return "-"
	}
	if t.Before(time.Unix(86400, 0)) {
		return "-"
	} else {
		if withYear {
			return t.In(time.FixedZone("CST", 8*60*60)).Format("06-01-02 15:04:05")
		}
		return t.In(time.FixedZone("CST", 8*60*60)).Format("01-02 15:04:05")
	}
}

func DecimalBit(v any) int {
	s := cast.ToString(v)
	if s == "" {
		return -1
	}
	if strings.Contains(s, "e") {
		val, _ := strconv.ParseFloat(s, 64)
		return DecimalBit(fmt.Sprintf("%.12f", val))
	}
	if !strings.Contains(s, ".") {
		return 0
	}
	var pos int
	for i := 0; i < len(s); i++ {
		if s[i] == '.' {
			pos = i
		}
	}
	for i := len(s) - 1; i >= 0; i-- {
		if s[i] == '.' {
			return 0
		}
		if (s[i] - '0') > 0 {
			if i-1 > 0 {
				return i - pos
			}
			return 0
		}
	}
	return -1
}

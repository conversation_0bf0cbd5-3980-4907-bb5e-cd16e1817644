package gateway

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	resty "github.com/go-resty/resty/v2"
	"github.com/puzpuzpuz/xsync/v2"
	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/exchange"
)

type GatewayExchange struct {
	exchange.BaseExchange

	ws                     *exchange.WebsocketWithLock
	packetResponses        *xsync.MapOf[string, chan exchange.Packet]
	subscribedSymbols      []string
	subscribedSymbolsMutex sync.Mutex
	apiKey                 *exchange.APISecret
}

func NewGatewayExchange(options *exchange.Options) (*GatewayExchange, error) {
	apiKey, err := exchange.NewAPISecret("", options.ApiKey, options.ApiSecret, false)
	if err != nil {
		return nil, err
	}
	e := &GatewayExchange{
		BaseExchange: *exchange.NewBaseExchange(options),
		ws: &exchange.WebsocketWithLock{
			Mutex:        sync.Mutex{},
			ConnectMutex: sync.Mutex{},
		},
		packetResponses:        xsync.NewMapOf[chan exchange.Packet](),
		subscribedSymbols:      []string{},
		subscribedSymbolsMutex: sync.Mutex{},
		apiKey:                 apiKey,
	}
	return e, nil
}

// 获取基本请求 request
func (this *GatewayExchange) getBaseRequest() *resty.Request {
	client := this.Client
	client.
		SetBaseURL(fmt.Sprintf("http://%s", this.Host)).
		SetTimeout(30 * time.Second).     // 设置超时时间，超时无反应则返回超时错误
		SetRetryCount(0).                 // 请求失败时的重试次数
		SetRetryWaitTime(5 * time.Second) // 重试前等待时间

	req := client.R()
	req.SetHeader("Content-Type", "application/json")
	return req
}

func (this *GatewayExchange) SendHTTPRequest(httpMethod, requestPath string, data, result any) (_ error) {
	req := this.getBaseRequest()
	payload := []byte("")
	if data != nil {
		payloadData, err := json.Marshal(data)
		if err != nil {
			return errors.New("sendHTTPRequest: Unable to JSON request")
		}
		payload = payloadData
		req.SetBody(payload)
	}

	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	req.Header.Set("X-QUANTER-API-ID", this.apiKey.ApiKey)
	req.Header.Set("X-QUANTER-TIMESTAMP", timestamp)

	h := hmac.New(sha256.New, this.apiKey.ApiSecret.Bytes())
	dataStr := strings.Join([]string{timestamp, requestPath, string(payload)}, "|||||")
	h.Write([]byte(dataStr))
	sign := hex.EncodeToString(h.Sum(nil))
	req.Header.Set("X-QUANTER-SIGN", sign)

	resp, err := req.Execute(httpMethod, requestPath)
	if err != nil {
		this.Errorf("send http request(%s) err: %s", requestPath, err)
		return err
	}

	if resp.StatusCode() != 200 {
		respStr := resp.String()
		msg := gjson.Parse(respStr).Get("message").String()
		this.Errorf("request(%s) error: %d - %s", requestPath, resp.StatusCode(), msg)
		return fmt.Errorf("%s", msg)
	}

	respStr := resp.Body()
	if string(respStr) == "" {
		return fmt.Errorf("response is 200 with empty body, check backend code")
	}
	return json.Unmarshal(respStr, result)
}

func (this *GatewayExchange) GetInstrument(instrumentType exchange.InstrumentType, symbol string) (*exchange.Instrument, error) {
	instrument, _ := this.Instruments.Load(symbol)
	if instrument == nil {
		if err := this.CacheInstruments(true); err != nil {
			return nil, err
		}
		instrument, _ = this.Instruments.Load(symbol)
	}
	if instrument == nil {
		return nil, fmt.Errorf("instrument not found")
	}
	return instrument, nil
}

func (this *GatewayExchange) GetLastPrice(instrumentType exchange.InstrumentType, symbol string, allowDelay bool) (float64, error) {
	path := fmt.Sprintf("/v1/instruments/price?instrument_id=%s", symbol)
	type priceData struct {
		Price      float64 `json:"price"`
		UpdateTime string  `json:"update_time"`
	}
	resp := priceData{}
	if err := this.SendHTTPRequest(resty.MethodGet, path, nil, &resp); err != nil {
		return 0, err
	}
	if !allowDelay {
		updateTime, err := time.Parse(time.RFC3339, resp.UpdateTime)
		if err != nil {
			return 0, fmt.Errorf("parse price update time failed, %v", err)
		}
		// 默认允许 10 以内的价格延迟
		delayThreshold := 10 * time.Second
		if this.Exchange.GetName() == exchange.InteractiveBrokers {
			delayThreshold = 3 * 24 * time.Hour // 美股允许 3 天以上的过去价格
		} else if this.Exchange.GetName() == exchange.CTP {
			delayThreshold = 7 * 24 * time.Hour // 期货允许 7 天以上的过去价格
		}
		if time.Since(updateTime).Seconds() > delayThreshold.Seconds() {
			return 0, fmt.Errorf("price update time is too old, %s", resp.UpdateTime)
		}
	}
	return resp.Price, nil
}

func (this *GatewayExchange) CreateOrder(args exchange.CreateOrderArgs) (order *exchange.Order, _ error) {
	err := this.CheckQuoteQty(&args)
	if err != nil {
		return nil, fmt.Errorf("check order quoteQty failed, %v", err)
	}

	if args.Side == exchange.OrderSideBuy {
		args.TriggerDirection = exchange.TriggerDirectionHigher
	} else {
		args.TriggerDirection = exchange.TriggerDirectionLower
	}
	if args.Type == exchange.Market {
		args.Price = 0
	}
	args.Price, _ = strconv.ParseFloat(this.FormatPrice(args.InstrumentType, args.Symbol, args.Price), 64)
	args.TriggerPrice, _ = strconv.ParseFloat(this.FormatPrice(args.InstrumentType, args.Symbol, args.TriggerPrice), 64)
	order = &exchange.Order{}
	this.Infof("create order args: %#v, name: %s", args, this.GetName())
	if err := this.SendHTTPRequest(resty.MethodPost, "/v1/orders/create", args, order); err != nil {
		return nil, err
	}
	order.InstrumentType = args.InstrumentType
	return
}

func (this *GatewayExchange) GetOrder(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol, orderID string, timeRange *exchange.TimeRange) (order *exchange.Order, err error) {
	path := fmt.Sprintf("/v1/orders?order_ids=%s&order_type=%s", orderID, orderType)
	orders := []exchange.Order{}
	if err := this.SendHTTPRequest(resty.MethodGet, path, nil, &orders); err != nil {
		return nil, err
	}
	if len(orders) == 0 {
		return nil, errors.New("order not found")
	}
	order = &orders[0]
	order.InstrumentType = instrumentType
	return
}

func (this *GatewayExchange) UpdateOrder(origOrder exchange.Order, args *exchange.UpdateOrderArgs) (*exchange.Order, error) {
	oldOrder, err := this.GetOrder(args.InstrumentType, origOrder.Type, origOrder.Symbol, origOrder.OrderID, nil)
	if err != nil {
		return nil, err
	}

	err = this.CancelOrder(args.InstrumentType, args.Type, origOrder.Symbol, origOrder.OrderID)
	if err != nil {
		return nil, err
	}

	if args.OrderQty == 0 {
		args.OrderQty = oldOrder.Qty - oldOrder.ExecQty
	}

	createArgs := exchange.CreateOrderArgs{
		InstrumentType:   args.InstrumentType,
		Symbol:           origOrder.Symbol,
		Side:             oldOrder.Side,
		Qty:              args.OrderQty,
		Type:             oldOrder.Type,
		TriggerPrice:     args.TriggerPrice,
		TriggerDirection: oldOrder.TriggerDirection,
		Price:            args.Price,
	}
	if args.ReduceOnly {
		createArgs.ReduceOnly = true
	}

	if args.TriggerPrice == 0 {
		createArgs.TriggerPrice = oldOrder.TriggerPrice
	}

	order, err := this.CreateOrder(createArgs)
	if order != nil {
		order.SetExtStruct(&origOrder)
	}
	return order, err
}

func (this *GatewayExchange) CancelAllOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string) (canceledIDs []string, _ error) {
	orders, err := this.GetOpenOrders(instrumentType, orderType, symbol)
	if err != nil {
		return nil, err
	}
	for _, order := range orders {
		if err := this.CancelOrder(instrumentType, orderType, symbol, order.OrderID); err != nil {
			return canceledIDs, err
		}
		canceledIDs = append(canceledIDs, order.OrderID)
	}
	return
}

func (this *GatewayExchange) CancelOrder(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol, orderID string) error {
	path := fmt.Sprintf("/v1/orders/cancel?order_id=%s&order_type=%s", orderID, orderType)
	order := exchange.Order{}
	if err := this.SendHTTPRequest(resty.MethodGet, path, nil, &order); err != nil {
		return err
	}
	return nil
}

func (this *GatewayExchange) GetOpenOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string) ([]*exchange.Order, error) {
	path := fmt.Sprintf("/v1/orders/open?instrument_id=%s&order_type=%s", symbol, orderType)
	orders := []*exchange.Order{}
	if err := this.SendHTTPRequest(resty.MethodGet, path, nil, &orders); err != nil {
		return nil, err
	}
	return orders, nil
}

func (this *GatewayExchange) GetTradablePairs(instrumentType exchange.InstrumentType, uSymbol string) ([]string, error) {
	return nil, exchange.ErrNotImplemented
}

func (this *GatewayExchange) GetAccountConfig(instrumentType exchange.InstrumentType) (*exchange.AccountConfig, error) {
	return &exchange.AccountConfig{
		MarginMode:       exchange.AccountMarginModeIsolated,
		DualPositionSide: false,
	}, nil
}

func (this *GatewayExchange) GetAccountBalances(instrumentType exchange.InstrumentType) (bls []*exchange.AccountBalance, _ error) {
	if err := this.SendHTTPRequest(resty.MethodGet, "/v1/balances", nil, &bls); err != nil {
		return nil, err
	}
	return
}

func (this *GatewayExchange) GetAccountCurrencies(instrumentType exchange.InstrumentType) ([]string, error) {
	this.CacheInstruments(true)
	symbols := []string{}
	this.Instruments.Range(func(symbol string, instrument *exchange.Instrument) bool {
		symbols = append(symbols, symbol)
		return true
	})
	return symbols, nil
}

func (this *GatewayExchange) GetPositions(instrumentType exchange.InstrumentType, symbol string, allowCache bool) (pos []*exchange.Position, _ error) {
	path := fmt.Sprintf("/v1/positions?instrument_id=%s", symbol)
	if !allowCache {
		path = fmt.Sprintf("/v1/positions?instrument_id=%s&no_cache=1", symbol)
	}
	if err := this.SendHTTPRequest(resty.MethodGet, path, nil, &pos); err != nil {
		return nil, err
	}
	for _, p := range pos {
		// short qty 统一为负数
		if p.Side == exchange.PositionSideShort && p.Qty > 0 {
			p.Qty = -p.Qty
		}
	}
	return
}

func (this *GatewayExchange) SetDualPositionSide(instrumentType exchange.InstrumentType, dualPositionSide bool) error {
	return exchange.ErrNotImplemented
}

func (this *GatewayExchange) SetMarginMode(instrumentType exchange.InstrumentType, symbol string, mode exchange.MarginMode) error {
	return nil
}

func (this *GatewayExchange) SetAccountMarginMode(instrumentType exchange.InstrumentType, mode exchange.AccountMarginMode) error {
	return exchange.ErrNotImplemented
}

func (this *GatewayExchange) QueryFundingHistory(instrumentType exchange.InstrumentType, symbol string, limit int, from time.Time, to time.Time) ([]*exchange.FundingHistory, error) {
	return nil, exchange.ErrNotImplemented
}

func (this *GatewayExchange) SetLeverage(instrumentType exchange.InstrumentType, symbol string, marginMode exchange.MarginMode, side exchange.PositionSide, leverage float64) error {
	return nil
}

func (this *GatewayExchange) GetLeverage(instrumentType exchange.InstrumentType, symbol string, marginMode exchange.MarginMode) (leverageLong, leverageShort float64, err error) {
	return 0, 0, exchange.ErrNotImplemented
}

func (this *GatewayExchange) TranslateSymbolCode(symbolCode *exchange.SymbolCode) (spotAndFutureSymbols []*exchange.SymbolPair, er error) {
	return nil, exchange.ErrNotImplemented
}

func (this *GatewayExchange) TranslateFutureSymbol(instrumentType exchange.InstrumentType, futureSymbol string, uSymbol string) (spotSymbol string, futureCode *exchange.SymbolCode, er error) {
	return "", nil, exchange.ErrNotImplemented
}

func (this *GatewayExchange) CacheInstruments(force bool) error {
	if !force && this.Instruments.Size() > 0 {
		return nil
	}

	instruments := &[]exchange.Instrument{}
	if err := this.SendHTTPRequest(resty.MethodGet, "/v1/instruments", nil, instruments); err != nil {
		return err
	}

	for _, instrument := range *instruments {
		in := instrument
		this.PreInstrumentCallback(&in)
		this.Instruments.Store(instrument.Symbol, &in)
	}

	return nil
}

func (this *GatewayExchange) GetUserMargin(instrumentType exchange.InstrumentType, currency string) (*exchange.UserMargin, error) {
	margin := &exchange.UserMargin{}
	if err := this.SendHTTPRequest(resty.MethodGet, "/v1/margin", nil, margin); err != nil {
		return nil, err
	}
	return margin, nil
}

func (this *GatewayExchange) Qty2Size(instrumentType exchange.InstrumentType, symbol string, price float64, qty float64) (float64, error) {
	ins, err := this.GetInstrument(instrumentType, symbol)
	if err != nil {
		return 0, err
	}
	size := price * (qty * ins.ContractSize)
	return math.Round(size/ins.TickSize) * ins.TickSize, nil
}

func (this *GatewayExchange) Size2Qty(instrumentType exchange.InstrumentType, symbol string, price float64, size float64) (float64, error) {
	ins, err := this.GetInstrument(instrumentType, symbol)
	if err != nil {
		return 0, err
	}
	qty := size / price / ins.ContractSize
	return math.Round(qty/ins.LotSize) * ins.LotSize, nil
}

func (this *GatewayExchange) CalcPrice(instrumentType exchange.InstrumentType, symbol string, qty float64, size float64) (float64, error) {
	ins, err := this.GetInstrument(instrumentType, symbol)
	if err != nil {
		return 0, err
	}
	price := size / (qty * ins.ContractSize)
	return math.Round(price/ins.TickSize) * ins.TickSize, nil
}

func (this *GatewayExchange) SetLeverageToMax(instrumentType exchange.InstrumentType, symbol string, currentLeverage float64, positionSide exchange.PositionSide) bool {
	return true
}

func (this *GatewayExchange) CalculateLeverage(position *exchange.Position) (float64, error) {
	return 0, nil
}

func (this *GatewayExchange) AdjustLiquidationPrice(instrumentType exchange.InstrumentType, symbol string, position *exchange.Position, targetLiquidationPrice, acceptableDelta float64) (deltaPrice float64, errMsg string) {
	return 0, ""
}

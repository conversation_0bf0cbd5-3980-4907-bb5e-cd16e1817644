package gateway

import (
	"errors"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	resty "github.com/go-resty/resty/v2"
	"github.com/wizhodl/quanter/exchange"
)

type CTP struct {
	GatewayExchange
}

func NewCTP(options *exchange.Options, gatewayChannel string) (*CTP, error) {
	if options.ControllerID == "" {
		return nil, errors.New("controller id is required")
	}

	options.GatewayChannelName = gatewayChannel
	ge, err := NewGatewayExchange(options)
	if err != nil {
		return nil, err
	}
	c := &CTP{
		GatewayExchange: *ge,
	}
	c.Exchange = c
	return c, nil
}

func (this *CTP) GetSupportedInstrumentTypes() []exchange.InstrumentType {
	return []exchange.InstrumentType{exchange.USDXMarginedFutures}
}

func (this *CTP) GetName() string {
	return exchange.CTP
}

func (this *CTP) GetID() string {
	return this.Host
}

func (this *CTP) GetLatestKLines(instrumentType InstrumentType, symbol string, periodHour, num int) (klines []*KLine, er error) {
	ctpExchange := ""
	if ins, err := this.GetInstrument(instrumentType, symbol); err == nil {
		ctpExchange = ins.ExchangeID
	} else {
		return nil, err
	}

	// 因为周末、节假日 gateway 也会提供连续的 K 线，需筛选出，所以获取的 num 要比实际用到数量多
	num = num * 2

	fromTs := time.Now().Add(-time.Hour * time.Duration(periodHour*num)).Unix() // 当前时间 - num * PeriodHour 个小时
	var toTs int64
	limit := 500

	hourKlines := []KLine{}
	for {
		respKlines := []KLine{}
		period := "1hour"
		if periodHour == 24 {
			period = "1day"
		}
		path := fmt.Sprintf("/v1/klines/lookup?exchange=%s&instrument_id=%s&period=%s&limit=%v&time=%v", ctpExchange, symbol, period, limit, toTs)
		this.Debugf("request ctp gateway kline: %s", path)
		if err := this.SendHTTPRequest(resty.MethodGet, path, nil, &respKlines); err != nil {
			return nil, err
		}
		if len(respKlines) == 0 {
			return nil, fmt.Errorf("zero kline found")
		}
		this.Debugf("resp ctp gateway kline: num: %v, time range: %s ~ %s", len(respKlines), time.Unix(respKlines[0].Time, 0), time.Unix(respKlines[len(respKlines)-1].Time, 0))
		logCount := 0
		for _, kline := range respKlines {
			// 打印 10 条核对数据
			logCount += 1
			if logCount > 10 {
				break
			}
			this.Debugf("[kline] %#v", kline)
		}

		for _, k := range respKlines {
			// 都相同说明是休盘数据，过滤
			if exchange.AlmostEqual(k.Open, k.Close) &&
				exchange.AlmostEqual(k.Open, k.High) &&
				exchange.AlmostEqual(k.Open, k.Low) {
				continue
			}
			hourKlines = append(hourKlines, k)
		}

		if len(respKlines) > 0 && respKlines[len(respKlines)-1].Time > fromTs {
			toTs = respKlines[len(respKlines)-1].Time - 3600
			continue
		}

		break
	}

	// 时间从早到晚排序
	sort.SliceStable(hourKlines, func(i, j int) bool {
		return hourKlines[i].Time < hourKlines[j].Time
	})

	// 合并小时线为 PeriodHour 小时线
	periodSeconds := int64(periodHour * 3600)
	klineCount := 0
	for _, kline := range hourKlines {
		ts := kline.Time
		if ts%periodSeconds == 0 || periodHour == 24 {
			// 这根小时线是 PeriodHour 的开盘线
			klineCount++

			klines = append(klines, &exchange.KLine{
				Open:  kline.Open,
				Close: kline.Close,
				High:  kline.High,
				Low:   kline.Low,
				Time:  ts,
			})
		} else if klineCount > 0 {
			// 非 PeriodHour 的开盘线
			klineIdx := klineCount - 1

			klines[klineIdx].Close = kline.Close
			klines[klineIdx].High = math.Max(klines[klineIdx].High, kline.High)
			klines[klineIdx].Low = math.Min(klines[klineIdx].Low, kline.Low)
		}
	}

	if klineCount > 0 {
		// 最后一根时间不应早于昨日，否则说明数据更新有问题
		yesterday := time.Now().Add(-time.Hour * 24)
		yesterday = time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location())
		lastKlineTime := time.Unix(klines[klineCount-1].Time, 0)
		if lastKlineTime.Before(yesterday) {
			return nil, fmt.Errorf("last kline time too old: %s", lastKlineTime)
		}
	}

	if len(klines) > 0 {
		this.Debugf("ctp klines after filter and sort: num: %v, time range: %s ~ %s", len(klines), time.Unix(klines[0].Time, 0), time.Unix(klines[len(klines)-1].Time, 0))
		logCount := 0
		for i := klineCount - 1; i >= 0; i-- {
			// 打印 10 条核对数据
			logCount += 1
			if logCount > 10 {
				break
			}
			this.Debugf("[kline] %#v", klines[i])
		}
	}

	return
}

func (this *CTP) TranslateSymbolCode(symbolCode *SymbolCode) (spotAndFutureSymbols []*SymbolPair, er error) {
	futureSymbols := []string{}
	this.Instruments.Range(func(symbol string, instrument *Instrument) bool {
		coin := symbolCode.Coin()
		if !strings.EqualFold(coin, instrument.UnderlyCurrency) {
			return true
		}
		if !strings.HasSuffix(symbol, symbolCode.Month()) {
			return true
		}
		futureSymbols = append(futureSymbols, symbol)
		return true
	})
	if len(futureSymbols) == 0 {
		er = fmt.Errorf("symbol not found (%s)", symbolCode)
		return
	}
	// 按从小到大排序，离现在最近的年份就会排到最前
	sort.SliceStable(futureSymbols, func(i, j int) bool {
		return futureSymbols[i] < futureSymbols[j]
	})
	spotAndFutureSymbols = []*SymbolPair{exchange.NewSymbolPair(symbolCode, "", futureSymbols[0])}
	return
}

func (this *CTP) TranslateFutureSymbol(instrumentType InstrumentType, futureSymbol string, uSymbol string) (spotSymbol string, futureCode *SymbolCode, er error) {
	instrument, err := this.GetInstrument(instrumentType, futureSymbol)
	if err != nil {
		er = err
		return
	}
	if instrument != nil && (instrumentType == exchange.USDXMarginedFutures) {
		lastTwoDigits := futureSymbol[len(futureSymbol)-2:]
		code := fmt.Sprintf("%s%s.U", instrument.UnderlyCurrency, lastTwoDigits)
		futureCode, er = exchange.NewSymbolCode(code, "CNY")
	}
	return
}

func (this *CTP) GetNextSymbolCodes(instrumentType exchange.InstrumentType, futureCode *exchange.SymbolCode, count int) (symbolCodes []*exchange.SymbolCode, er error) {
	pairs, err := this.TranslateSymbolCode(futureCode)
	if err != nil {
		er = err
		return
	}
	if len(pairs) != 1 {
		er = errors.New("future code translate to more than one pairs")
		return
	}
	instrumentReady := false
	this.Instruments.Range(func(k string, v *Instrument) bool {
		instrumentReady = true
		return false
	})
	if !instrumentReady {
		er = errors.New("instruments not ready")
		return
	}
	futureSymbol := pairs[0].Right.Symbol
	instrument, err := this.GetInstrument(instrumentType, futureSymbol)
	if err != nil {
		er = fmt.Errorf("instrument for future code (%s) not found", futureCode)
		return
	}

	productID := instrument.UnderlyCurrency
	symbols := []string{}
	now := time.Now()
	month, err := futureCode.MonthNumber()
	if err != nil {
		er = fmt.Errorf("future code parse month number failed, error: %s", err)
		return
	}
	year := now.Year()
	year0, month0 := foldMonth(year, int(month+1))
	year1, month1 := foldMonth(year, int(month+2))
	year2, month2 := foldMonth(year, int(month+3))
	if instrument.ExchangeID == "ZCE" {
		symbols = append(symbols, fmt.Sprintf("%s%02d", productID, month0))
		symbols = append(symbols, fmt.Sprintf("%s%02d", productID, month1))
		symbols = append(symbols, fmt.Sprintf("%s%02d", productID, month2))
	} else {
		symbols = append(symbols, fmt.Sprintf("%s%s%02d", productID, fmt.Sprintf("%v", year0)[2:], month0))
		symbols = append(symbols, fmt.Sprintf("%s%s%02d", productID, fmt.Sprintf("%v", year1)[2:], month1))
		symbols = append(symbols, fmt.Sprintf("%s%s%02d", productID, fmt.Sprintf("%v", year2)[2:], month2))
	}

	for _, symbol := range symbols {
		instrument, err := this.GetInstrument(instrumentType, symbol)
		if err == nil && instrument != nil {
			_, futureCode, err := this.TranslateFutureSymbol(instrumentType, symbol, "")
			if err == nil {
				symbolCodes = append(symbolCodes, futureCode)
			}
		}
	}
	return
}

func (this *CTP) PreInstrumentCallback(instrument *Instrument) error {
	return nil
}

func (this *CTP) MaxLeverage(instrumentType InstrumentType, symbol string, value float64) float64 {
	return 0.0
}

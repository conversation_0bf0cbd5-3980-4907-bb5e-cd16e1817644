package gateway

import (
	"errors"
	"fmt"
	"math"
	"sort"
	"time"

	resty "github.com/go-resty/resty/v2"
	"github.com/wizhodl/quanter/exchange"
)

type MT struct {
	GatewayExchange
}

func NewMT(options *exchange.Options, gatewayChannel string) (*MT, error) {
	if options.ControllerID == "" {
		return nil, errors.New("controller id is required")
	}

	options.GatewayChannelName = gatewayChannel
	ge, err := NewGatewayExchange(options)
	if err != nil {
		return nil, err
	}
	m := &MT{
		GatewayExchange: *ge,
	}
	m.Exchange = m
	return m, nil
}

func (this *MT) GetSupportedInstrumentTypes() []exchange.InstrumentType {
	return []exchange.InstrumentType{exchange.USDXMarginedFutures}
}

func (this *MT) GetName() string {
	return exchange.MetaTrader
}

func (this *MT) GetID() string {
	return this.Host
}

func (this *MT) GetLatestKLines(instrumentType InstrumentType, symbol string, periodHour, num int) (klines []*KLine, er error) {
	if periodHour != 24 {
		return nil, fmt.Errorf("period hour %d not supported", periodHour)
	}

	// 因为周末、节假日 gateway 也会提供连续的 K 线，需筛选出，所以获取的 num 要比实际用到数量多
	num = num * 2

	fromTs := time.Now().Add(-time.Hour * time.Duration(periodHour*num)).Unix() // 当前时间 - num * PeriodHour 个小时
	toTs := time.Now().Unix()
	limit := 500

	klines = []*KLine{}
	for {
		respKlines := []*KLine{}
		path := fmt.Sprintf("/v1/klines/lookup?exchange=mt5&instrument_id=%s&period=1day&limit=%v&time=%v", symbol, limit, toTs)
		this.Debugf("request mt5 gateway kline: %s", path)
		if err := this.SendHTTPRequest(resty.MethodGet, path, nil, &respKlines); err != nil {
			return nil, err
		}
		if len(respKlines) == 0 {
			return nil, fmt.Errorf("zero kline found")
		}
		this.Debugf("resp mt5 gateway kline: num: %v, time range: %s ~ %s", len(respKlines), time.Unix(respKlines[0].Time, 0), time.Unix(respKlines[len(respKlines)-1].Time, 0))

		klines = append(klines, respKlines...)
		if len(respKlines) > 0 && respKlines[0].Time > fromTs {
			toTs = respKlines[0].Time - 3600
			continue
		}

		break
	}

	// 时间从早到晚排序
	sort.SliceStable(klines, func(i, j int) bool {
		return klines[i].Time < klines[j].Time
	})

	return
}

func (this *MT) MaxLeverage(instrumentType InstrumentType, symbol string, value float64) float64 {
	return 0.0
}

func (this *MT) GetNextSymbolCodes(instrumentType InstrumentType, futureCode *SymbolCode, count int) (symbolCodes []*SymbolCode, er error) {
	pairs, err := this.TranslateSymbolCode(futureCode)
	if err != nil {
		er = err
		return
	}
	if len(pairs) != 1 {
		er = errors.New("future code translate to more than one pairs")
		return
	}
	instrumentReady := false
	this.Instruments.Range(func(k string, v *Instrument) bool {
		instrumentReady = true
		return false
	})
	if !instrumentReady {
		er = errors.New("instruments not ready")
		return
	}
	futureSymbol := pairs[0].Right.Symbol
	instrument, err := this.GetInstrument(instrumentType, futureSymbol)
	if err != nil {
		er = fmt.Errorf("instrument for future code (%s) not found", futureCode)
		return
	}

	productID := instrument.UnderlyCurrency
	symbols := []string{}
	now := time.Now()
	month, err := futureCode.MonthNumber()
	if err != nil {
		er = fmt.Errorf("future code parse month number failed, error: %s", err)
		return
	}
	year := now.Year()
	year0, month0 := foldMonth(year, int(month+1))
	year1, month1 := foldMonth(year, int(month+2))
	year2, month2 := foldMonth(year, int(month+3))
	if instrument.ExchangeID == "ZCE" {
		symbols = append(symbols, fmt.Sprintf("%s%02d", productID, month0))
		symbols = append(symbols, fmt.Sprintf("%s%02d", productID, month1))
		symbols = append(symbols, fmt.Sprintf("%s%02d", productID, month2))
	} else {
		symbols = append(symbols, fmt.Sprintf("%s%s%02d", productID, fmt.Sprintf("%v", year0)[2:], month0))
		symbols = append(symbols, fmt.Sprintf("%s%s%02d", productID, fmt.Sprintf("%v", year1)[2:], month1))
		symbols = append(symbols, fmt.Sprintf("%s%s%02d", productID, fmt.Sprintf("%v", year2)[2:], month2))
	}

	for _, symbol := range symbols {
		instrument, err := this.GetInstrument(instrumentType, symbol)
		if err == nil && instrument != nil {
			_, futureCode, err := this.TranslateFutureSymbol(instrumentType, symbol, "")
			if err == nil {
				symbolCodes = append(symbolCodes, futureCode)
			}
		}
	}
	return
}

func (this *MT) TranslateSymbolCode(symbolCode *SymbolCode) (spotAndFutureSymbols []*SymbolPair, er error) {
	this.CacheInstruments(false)
	futureSymbols := []string{}
	this.Instruments.Range(func(symbol string, instrument *Instrument) bool {
		coin := symbolCode.Coin()
		// this.Debugf("checking %s <-> %s", instrument.Symbol, coin)
		if instrument.Symbol == coin {
			futureSymbols = append(futureSymbols, symbol)
		}
		return true
	})
	if len(futureSymbols) == 0 {
		er = fmt.Errorf("symbol not found (%s)", symbolCode)
		return
	}
	// 按从小到大排序，离现在最近的年份就会排到最前
	sort.SliceStable(futureSymbols, func(i, j int) bool {
		return futureSymbols[i] < futureSymbols[j]
	})
	spotAndFutureSymbols = []*SymbolPair{exchange.NewSymbolPair(symbolCode, futureSymbols[0], futureSymbols[0])}
	return
}

func (this *MT) TranslateFutureSymbol(instrumentType InstrumentType, futureSymbol string, uSymbol string) (spotSymbol string, futureCode *SymbolCode, er error) {
	instrument, err := this.GetInstrument(instrumentType, futureSymbol)
	if err != nil {
		er = err
		return
	}
	if instrument != nil && (instrumentType == exchange.USDXMarginedFutures) {
		lastTwoDigits := "00"
		code := fmt.Sprintf("%s%s.U", instrument.Symbol, lastTwoDigits)
		futureCode, er = exchange.NewSymbolCode(code, "USD")
	}
	return
}

func (this *MT) PreInstrumentCallback(instrument *Instrument) error {
	if instrument.QuoteToSettleRate == 0 {
		return nil
	}
	contractSize := instrument.ContractSize
	places := exchange.DecimalBit(contractSize)
	contractSize *= instrument.QuoteToSettleRate
	if places == 0 {
		contractSize = float64(int64(contractSize))
	} else if places > 0 {
		pow := math.Pow(10, float64(places))
		contractSize = float64(int64(contractSize*pow)) / pow
	}
	instrument.ContractSize = contractSize
	return nil
}

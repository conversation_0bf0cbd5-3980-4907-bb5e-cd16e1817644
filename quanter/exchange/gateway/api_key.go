package gateway

import (
	"errors"
	"fmt"
	"strings"

	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/secrets"
)

type CTPAPISecret struct {
	exchange.BaseAPISecret
	InvestorID string
	Password   string
	AuthCode   string
}

func NewEmptyCTPAPISecret() *CTPAPISecret {
	s := &CTPAPISecret{
		BaseAPISecret: exchange.BaseAPISecret{
			ExchangeName: exchange.CTP,
		},
	}
	s.Secretable = s
	return s
}

func NewCTPAPISecret(apiKey string, apiSecret secrets.SecretString, isEncrypted bool) (*CTPAPISecret, error) {
	s := NewEmptyCTPAPISecret()
	s.ApiKey = apiKey
	if isEncrypted {
		s.EncyptedApiSecret = apiSecret
		if err := s.Decrypt(); err != nil {
			return nil, err
		}
	} else {
		s.DecryptedApiSecret = apiSecret
	}
	if err := s.Decode(); err != nil {
		return nil, err
	}
	return s, nil
}

func (this *CTPAPISecret) Encode() secrets.SecretString {
	apiSecret := fmt.Sprintf("%s|||%s|||%s", this.InvestorID, this.Password, this.AuthCode)
	this.DecryptedApiSecret = secrets.SecretString(apiSecret)
	return this.DecryptedApiSecret
}

func (this *CTPAPISecret) Decode() error {
	parts := strings.Split(string(this.DecryptedApiSecret), "|||")
	if len(parts) != 3 {
		return errors.New("wrong number of arguments")
	}
	this.InvestorID = parts[0]
	this.Password = parts[1]
	this.AuthCode = parts[2]
	return nil
}

func (this *CTPAPISecret) Prompt() bool {
	this.InvestorID = exchange.SurveyInput("请输入 InvestorID : ")
	this.Password = exchange.SurveyPassword("请输入 Password: ")
	this.AuthCode = exchange.SurveyPassword("请输入 AuthCode: ")

	fmt.Printf("\n\n*** 请确认一下内容 *** \n\nInvestorID: %s\nPassword: %s\nAuthCode: %s\n\n",
		this.InvestorID,
		exchange.FormatPassword(this.Password),
		exchange.FormatPassword(this.AuthCode),
	)
	return true
}

type MetaTraderAPISecret struct {
	exchange.BaseAPISecret
	Platform string
	UserID   string
	Password string
}

func NewEmptyMetaTraderAPISecret() *MetaTraderAPISecret {
	s := &MetaTraderAPISecret{
		BaseAPISecret: exchange.BaseAPISecret{
			ExchangeName: exchange.MetaTrader,
		},
	}
	s.Secretable = s
	return s
}

func NewMetaTraderAPISecret(apiKey string, apiSecret secrets.SecretString, isEncrypted bool) (*MetaTraderAPISecret, error) {
	s := NewEmptyMetaTraderAPISecret()
	s.ApiKey = apiKey
	if isEncrypted {
		s.EncyptedApiSecret = apiSecret
		if err := s.Decrypt(); err != nil {
			return nil, err
		}
	} else {
		s.DecryptedApiSecret = apiSecret
	}
	if err := s.Decode(); err != nil {
		return nil, err
	}
	return s, nil
}

func (this *MetaTraderAPISecret) Encode() secrets.SecretString {
	apiSecret := fmt.Sprintf("%s|||%s|||%s", this.Platform, this.UserID, this.Password)
	this.DecryptedApiSecret = secrets.SecretString(apiSecret)
	return this.DecryptedApiSecret
}

func (this *MetaTraderAPISecret) Decode() error {
	if len(string(this.DecryptedApiSecret)) == 0 {
		return errors.New("empty api secret")
	}
	parts := strings.Split(string(this.DecryptedApiSecret), "|||")
	if len(parts) != 3 {
		return errors.New("wrong number of arguments")
	}
	this.Platform = parts[0]
	this.UserID = parts[1]
	this.Password = parts[2]
	return nil
}

func (this *MetaTraderAPISecret) Prompt() bool {
	_, this.Platform = exchange.SurveySelect("请选择平台: ", []string{"MT5", "MT4"})
	this.UserID = exchange.SurveyInput("请输入 UserID: ")
	this.Password = exchange.SurveyPassword("请输入 Password: ")

	fmt.Printf("\n\n*** 请确认一下内容 *** \n\nPlatform: %s\nUserID: \"%s\"\nPassword: %s\n\n",
		this.Platform,
		this.UserID,
		exchange.FormatPassword(this.Password),
	)
	return true
}

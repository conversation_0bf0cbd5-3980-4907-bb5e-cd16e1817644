package gateway

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/gorilla/websocket"
	"github.com/wizhodl/quanter/exchange"
)

// 定时发送 ping 包，订阅实时价格
// 这些操作只能在 GatewayExchange 中才可以做，所以不放在更底层的 ws 中
func (this *GatewayExchange) pingAndSubscribeLoop(done <-chan struct{}) {
	pingTicker := time.NewTicker(time.Second * 10)
	subscribeCheckTicker := time.NewTicker(time.Second * 10)

	defer func() {
		pingTicker.Stop()
		subscribeCheckTicker.Stop()
	}()

	for {
		select {
		case <-done:
			return
		case <-pingTicker.C:
			if this.IsConnected() {
				pingPacket := exchange.NewPingPacket(this.apiKey.ApiKey)
				this.SendWebsocketMessage(pingPacket, nil)
			}
		case <-subscribeCheckTicker.C:
			if this.IsConnected() {
				this.subscribeRealtimePriceSymbols()
			}
		}
	}
}

func (this *GatewayExchange) IsConnected() bool {
	return this.Exchange != nil && this.ws.IsConnected()
}

func (this *GatewayExchange) websocketLoop() (er error) {
	this.Infof("websocket loop init")

	// 防止重复连接
	// 这个函数在调用方会重复调用，如果重复连接 + goroutine 不清理，会导致 goroutine 泄露
	if !this.ws.ConnectMutex.TryLock() {
		if this.ws.Conn != nil {
			this.Errorf("websocket is already connected")
		} else {
			this.Errorf("websocket is connecting")
		}
		return
	}
	defer this.ws.ConnectMutex.Unlock()

	defer func() {
		this.ws.Close()
		if er != nil {
			this.Errorf("websocket error: %v", er)
		}
		if this.ConnectedCallback != nil {
			this.ConnectedCallback(false)
		}
	}()

	// 连接 gateway 的 websocket 端口
	conn, _, err := websocket.DefaultDialer.Dial(fmt.Sprintf("ws://%s/v1/ws", this.Host), nil)
	if err != nil {
		er = fmt.Errorf("dial websocket failed, error: %s", err)
		return
	}
	this.ws.SetConn(conn)

	done := make(chan struct{})
	defer close(done)
	go this.pingAndSubscribeLoop(done)

	// 连接成功后，重新回调；slack adapter 可能需要重新 subscribe
	if this.ConnectedCallback != nil {
		this.ConnectedCallback(true)
	}
	// 重连成功后自动发送 subscribe 消息
	for _, symbol := range this.subscribedSymbols {
		this.Subscribe(exchange.UnknownInstrumentType, symbol)
	}

	for {
		this.ws.SetReadDeadline(time.Now().Add(time.Minute * 1)) // 半小时没有消息重启
		packet := &exchange.Packet{}
		// 读到错误一定要跳出循环，否则重复 ReadJSON 会导致 crash
		// 错误为：panic: repeated read on failed websocket connection
		if err := this.ws.ReadJSON(packet); err != nil {
			er = fmt.Errorf("read incoming response packet failed, error: %s", err)
			return
		}

		if !packet.CheckSign(this.apiKey.ApiSecret) {
			this.Errorf("check sign of incoming response packet failed, packet: %s", packet.PacketHeader.FullString())
			continue
		}

		// 不会处理 gateway 主动推送的消息；仅处理 quanter 发送的 packet 后收到的 gateway 的回复
		if packet.ReplyTo != "" {
			if packetChan, found := this.packetResponses.Load(packet.ReplyTo); found {
				this.Debugf("received response, putting response signal for packet: %s", packet)
				packetChan <- *packet
				close(packetChan)
				this.packetResponses.Delete(packet.ReplyTo)
				this.Debugf("response signal put for packet: %s", packet)
			}
		}

		if yes, orders := packet.CheckOrder(); yes {
			for _, order := range orders {
				this.Debugf("<ws:order update> %#v", order)
				// TODO 完善订单字段
				this.OrderUpdatedCallback(&exchange.Order{
					InstrumentType: exchange.UnknownInstrumentType,
					Symbol:         order.Symbol,
				})
			}
		} else if yes, margin := packet.CheckMargin(); yes {
			// this.Debugf("<ws:margin> %#v", margin)
			this.MarginUpdatedCallback(margin, margin.Currency)
		} else if yes, instruments := packet.CheckInstrument(); yes {
			for _, instrument := range instruments {
				this.PreInstrumentCallback(instrument)
				this.Instruments.Store(instrument.Symbol, instrument)

				this.handleTicker(instrument)
			}
		} else if yes, channel, slackResponse := packet.CheckSlackResponse(); yes {
			if !strings.HasPrefix(channel, this.SlackChannelName) {
				this.Errorf("received response from wrong channel (%s), my channel is (%s)", channel, this.SlackChannelName)
				continue
			}
			if this.SlackMessageCallback != nil {
				var msg string
				var fileTitle string
				var fileContent []byte
				var fileType string
				if m, t, c, tt, err := slackResponse.Decode(); err != nil {
					this.Errorf("decode slack response msg failed, error: %s", err)
					continue
				} else {
					msg = m
					fileTitle = t
					fileContent = c
					fileType = tt
				}
				this.SlackMessageCallback(msg, fileTitle, fileContent, fileType)
			} else {
				this.Errorf("message callback is nil, skip message: %s", packet.PacketHeader)
			}
		}
	}
}

func (this *GatewayExchange) subscribeRealtimePriceSymbols() {
	if !this.EnableRealtimePrice {
		return
	}

	symbolsNeed := []string{}
	for _, priceTrigger := range this.PriceTriggers {
		symbolsNeed = append(symbolsNeed, priceTrigger.Symbol)
	}

	for _, watch := range this.PriceWatches {
		symbolsNeed = append(symbolsNeed, watch.Symbol)
	}

	for _, symbol := range symbolsNeed {
		if !exchange.SliceContains(this.subscribedSymbols, symbol) {
			subSymbol := symbol
			this.sendSubscribe(subSymbol, func() {
				this.AppendSubscribedSymbol(subSymbol)
			})
		}
	}
}

func (this *GatewayExchange) AppendSubscribedSymbol(symbol string) {
	this.subscribedSymbolsMutex.Lock()
	defer this.subscribedSymbolsMutex.Unlock()

	if !exchange.SliceContains(this.subscribedSymbols, symbol) {
		this.subscribedSymbols = append(this.subscribedSymbols, symbol)
	}
}

func (this *GatewayExchange) RemoveSubscribedSymbol(symbol string) {
	this.subscribedSymbolsMutex.Lock()
	defer this.subscribedSymbolsMutex.Unlock()

	index := -1
	for i, sym := range this.subscribedSymbols {
		if symbol == sym {
			index = i
			break
		}
	}
	if index > -1 {
		this.subscribedSymbols = append(this.subscribedSymbols[:index], this.subscribedSymbols[index+1:]...)
	}
}

func (this *GatewayExchange) IsSubscribed(symbol string) bool {
	return exchange.SliceContains(this.subscribedSymbols, symbol)
}

func (this *GatewayExchange) Subscribe(instrumentType exchange.InstrumentType, symbol string) {
	if !this.IsConnected() {
		this.Errorf("websocket not connected, skip subscribe %s", symbol)
		return
	}
	this.sendSubscribe(symbol, func() {
		if !exchange.SliceContains(this.subscribedSymbols, symbol) {
			this.subscribedSymbols = append(this.subscribedSymbols, symbol)
		}
	})
	this.sendSubscribe(fmt.Sprintf("%s.orders", symbol), nil)
	this.sendSubscribe("margin", nil)
}

func (this *GatewayExchange) sendSubscribe(key string, successCallback func()) {
	packet := exchange.NewSubscribePacket(this.apiKey.ApiKey, key)
	this.SendWebsocketMessage(packet, func(response *exchange.Packet, err error) {
		if err != nil {
			time.Sleep(2 * time.Second)
			this.Debugf("send subscribe packet failed, error: %s, retry...", err)
			// 如果订阅失败，直接返回，不重试了，websocketLoop 中会重新订阅所有的 symbols
			// this.sendSubscribe(key, successCallback)
			return
		} else {
			if successCallback != nil {
				successCallback()
			}
		}
	})
}

func (this *GatewayExchange) UnSubscribe(instrumentType exchange.InstrumentType, symbol string) {
	if !this.IsConnected() {
		this.Errorf("websocket not connected, skip unsubscribe")
		return
	}

	this.sendUnsubscribe(symbol, func() {
		this.RemoveSubscribedSymbol(symbol)
	})
}

func (this *GatewayExchange) sendUnsubscribe(symbol string, successCallback func()) {
	packet := exchange.NewUnSubscribePacket(this.apiKey.ApiKey, symbol)
	this.SendWebsocketMessage(packet, func(response *exchange.Packet, err error) {
		if err != nil {
			time.Sleep(2 * time.Second)
			this.Debugf("send unsubscribe packet failed, error: %s, retry...", err)
			// 如果订阅失败，直接返回，不重试了，websocketLoop 中会重新订阅所有的 symbols
			// this.sendUnsubscribe(key, successCallback)
			return
		} else {
			if successCallback != nil {
				successCallback()
			}
		}
	})
}

func (this *GatewayExchange) SendWebsocketMessage(clientPacket *exchange.ClientPacket, callback func(response *exchange.Packet, er error)) {
	if clientPacket == nil {
		if callback != nil {
			callback(nil, errors.New("packet is nil"))
		}
		return
	}
	if !this.IsConnected() {
		if callback != nil {
			callback(nil, exchange.ErrNotConnected)
		}
		return
	}
	packetChan := make(chan exchange.Packet, 1)
	this.packetResponses.Store(clientPacket.ID, packetChan)
	if err := this.ws.WriteJSON(clientPacket, this.apiKey.ApiSecret); err != nil {
		if callback != nil {
			callback(nil, fmt.Errorf("write json to websocket failed, error: %s", err))
		}
		this.packetResponses.Delete(clientPacket.ID)
		return
	}
	// 等待 timeout 秒如果没有结果，返回错误
	go func(callback func(response *exchange.Packet, er error)) {
		defer func() {
			this.packetResponses.Delete(clientPacket.ID)
		}()

		timeout := time.Second * 5
		timer := time.After(timeout)

		var response *exchange.Packet
		var err error
		select {
		case packet := <-packetChan:
			if !packet.IsSuccess() {
				err = fmt.Errorf("response is error, error: %s", packet.Error)
			} else {
				response = &packet
			}
		case <-timer:
			err = fmt.Errorf("response did not return after timeout: %s, packet: %s", timeout, clientPacket.PacketHeader.String())
		}
		if callback != nil {
			callback(response, err)
		} else {
			this.Debugf("no callback for packet: %s", clientPacket.PacketHeader.String())
		}
	}(callback)
}

func (this *GatewayExchange) ConnectWebsocket(instrumentTypes []exchange.InstrumentType, connectedCallback func(connected bool)) {
	this.ConnectedCallback = connectedCallback

	exp := backoff.NewConstantBackOff(time.Second * 3)
	backoff.Retry(func() error {
		err := this.websocketLoop()
		if err != nil {
			this.Errorf("websocket loop exit: %v, retry...", err)
		}
		return err
	}, exp)
}

func (this *GatewayExchange) CloseWebsocket(stop bool) {
	if this.ws.Conn == nil {
		return
	}
	this.ws.Close()
}

func (this *GatewayExchange) handleTicker(ins *exchange.Instrument) {
	if ins.LastPrice == 0 {
		return
	}
	exTicker := &exchange.Ticker{
		InstrumentType: ins.InstrumentType,
		Time:           ins.UpdateTime.Unix() * 1000,
		Symbol:         ins.Symbol,
		Close:          ins.LastPrice,
	}
	if exchange.SliceContains(this.subscribedSymbols, ins.Symbol) {
		this.StoreTickerCache(exTicker)
		this.CheckPriceTrigger(exTicker.InstrumentType, exTicker.Symbol, exTicker.Close, ins.UpdateTime)
	}
}

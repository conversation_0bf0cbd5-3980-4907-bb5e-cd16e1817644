package gateway

import "github.com/wizhodl/quanter/exchange"

type Instrument = exchange.Instrument
type InstrumentType = exchange.InstrumentType
type KLine = exchange.KLine
type SymbolCode = exchange.SymbolCode
type SymbolPair = exchange.SymbolPair

// 将在 1～12 月之外的数字折叠为合法的 1～12
// month 因为是通过简单的加/减算术运算，可能有 <1 和 >12  的情况出现
func foldMonth(year, month int) (yearOut, monthOut int) {
	if month > 12 {
		return year + 1, month % 12
	} else if month <= 0 {
		return year - 1, month + 12
	}
	return year, month
}

package gateway

import (
	"errors"
	"fmt"

	"github.com/wizhodl/quanter/exchange"
)

type IB struct {
	GatewayExchange
}

const ExtKeyOutsideRth = "outsideRth"

func NewIB(options *exchange.Options, gatewayChannel string) (*IB, error) {
	if options.ControllerID == "" {
		return nil, errors.New("controller id is required")
	}

	options.GatewayChannelName = gatewayChannel
	ge, err := NewGatewayExchange(options)
	if err != nil {
		return nil, err
	}
	i := &IB{
		GatewayExchange: *ge,
	}
	i.Exchange = i
	return i, nil
}

func (this *IB) GetSupportedInstrumentTypes() []exchange.InstrumentType {
	return []exchange.InstrumentType{exchange.Spot}
}

func (this *IB) GetName() string {
	return exchange.InteractiveBrokers
}

func (this *IB) GetID() string {
	return this.Host
}

func (this *IB) GetLatestKLines(instrumentType InstrumentType, symbol string, periodHour, num int) (klines []*KLine, er error) {
	// TODO
	return
}

func (this *IB) MaxLeverage(instrumentType InstrumentType, symbol string, value float64) float64 {
	return 0.0
}

func (this *IB) TranslateSymbolCode(symbolCode *SymbolCode) (spotAndFutureSymbols []*SymbolPair, er error) {
	if symbolCode.InstrumentType() != exchange.Spot {
		return nil, exchange.ErrNotAvailableForInstrumentType
	}

	this.CacheInstruments(false)
	spotSymbol := ""
	this.Instruments.Range(func(symbol string, instrument *Instrument) bool {
		coin := symbolCode.Coin()
		if instrument.Symbol == coin && instrument.InstrumentType == symbolCode.InstrumentType() {
			spotSymbol = instrument.Symbol
		}
		return true
	})
	if spotSymbol == "" {
		return nil, fmt.Errorf("symbol not found for %s", symbolCode.String())
	}
	spotAndFutureSymbols = []*SymbolPair{exchange.NewSymbolPair(symbolCode, spotSymbol, "")}
	return
}

func (this *IB) TranslateFutureSymbol(instrumentType InstrumentType, futureSymbol string, uSymbol string) (spotSymbol string, futureCode *SymbolCode, er error) {
	return "", nil, exchange.ErrNotImplemented
}

func (this *IB) PreInstrumentCallback(instrument *Instrument) error {
	return nil
}

func (this *IB) GetSymbolCodeQuote(symbolCode *SymbolCode) string {
	quote := "USD"
	this.Instruments.Range(func(symbol string, instrument *Instrument) bool {
		coin := symbolCode.Coin()
		if instrument.Symbol == coin && instrument.InstrumentType == symbolCode.InstrumentType() {
			quote = instrument.QuoteCurrency
			return false
		}
		return true
	})
	return quote
}

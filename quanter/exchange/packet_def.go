package exchange

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/secrets"
	"go.uber.org/atomic"
)

/* websocket 数据结构 */

var _packetIDPrefix string = randomCounterPrefix()
var _packetIDCounter *atomic.Int64 = atomic.NewInt64(1)

type PacketEvent string

var EventSubscribeSuccess PacketEvent = "subscribe.success"
var EventSubscribeFail PacketEvent = "subscribe.fail"
var EventUnsubscribeSuccess PacketEvent = "unsubscribe.success"
var EventUnsubscribeFail PacketEvent = "unsubscribe.fail"
var EventPong PacketEvent = "pong"
var EventOrdersUpdate PacketEvent = "orders.update"
var EventInstrumentsUpdate PacketEvent = "instruments.update"
var EventMarginUpdate PacketEvent = "margin.update"
var EventSlackResponse PacketEvent = "slack.response"

var EventSubscribe PacketEvent = "subscribe"
var EventUnsubscribe PacketEvent = "unsubscribe"
var EventPing PacketEvent = "ping"
var EventSlackRequest PacketEvent = "slack.request"
var EventSlackAck PacketEvent = "slack.ack"
var EventError PacketEvent = "error"

type PacketHeader struct {
	APIKey  string      `json:"api_key,omitempty"`
	Key     string      `json:"key,omitempty"`
	Event   PacketEvent `json:"event,omitempty"`
	ID      string      `json:"id,omitempty"`
	Error   string      `json:"error,omitempty"`
	Time    int64       `json:"time,omitempty"`
	Sign    string      `json:"sign,omitempty"`
	ReplyTo string      `json:"reply_to,omitempty"`
}

func NewPacketHeader(apiKey string, event PacketEvent, key string, error string) PacketHeader {
	return PacketHeader{
		APIKey: apiKey,
		Event:  event,
		Key:    key,
		Error:  error,
		Time:   time.Now().UnixMilli(),
	}
}

func randomCounterPrefix() string {
	randStr := "1234567890abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ"
	var letterRunes = []rune(randStr)

	b := make([]rune, 4)
	for i := range b {
		b[i] = letterRunes[rand.Intn(len(letterRunes))]
	}
	return string(b)
}

func (this *PacketHeader) SetID() {
	this.ID = fmt.Sprintf("%s.%d", _packetIDPrefix, _packetIDCounter.Inc())
}

func (this *PacketHeader) _signData(key secrets.SecretString) string {
	data := fmt.Sprintf("%s|%s|%s|%s|%s|%d|%s", this.APIKey, this.ID, this.Event, this.Key, this.Error, this.Time, this.ReplyTo)
	h := hmac.New(sha256.New, key.Bytes())
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

func (this *PacketHeader) SignWithKey(key secrets.SecretString) string {
	this.Sign = this._signData(key)
	return this.Sign
}

func (this *PacketHeader) CheckSign(key secrets.SecretString) (ok bool) {
	// zlog.Debugf("checking sign with key: %s", key.String())
	dataSign := this._signData(key)
	ok = dataSign == this.Sign
	if !ok {
		zlog.Debugf("check sign failed, sign: %s, dataSign: %s", this.Sign, dataSign)
	}
	return
}

func (this *PacketHeader) String() string {
	if this.ReplyTo != "" {
		return fmt.Sprintf("[Packet:%s:%s->%s]", this.Event, this.ReplyTo, this.ID)
	}
	return fmt.Sprintf("[Packet:%s:%s]", this.Event, this.ID)
}

func (this *PacketHeader) FullString() string {
	extra := fmt.Sprintf("api_key: %s, key: %s, error: %s, time: %d, sign: %s", this.APIKey, this.Key, this.Error, this.Time, this.Sign)
	if this.ReplyTo != "" {
		return fmt.Sprintf("[Packet:%s:%s->%s] %s", this.Event, this.ReplyTo, this.ID, extra)
	}
	return fmt.Sprintf("[Packet:%s:%s] %s", this.Event, this.ID, extra)
}

func (this *PacketHeader) IsSuccess() bool {
	return !(strings.HasSuffix(string(this.Event), ".fail") || this.Event == EventError)
}

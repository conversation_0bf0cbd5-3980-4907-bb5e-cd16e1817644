package binance

import (
	"math"
	"sort"
	"time"

	"github.com/wizhodl/quanter/exchange"
)

type Bracket struct {
	Level            int64
	InitialLeverage  float64 // 该层允许的最高初始杠杆倍数
	NotionalCap      float64 // 最大金额上线
	MaintMarginRatio float64 // 维持保证金率
	Cum              float64 // 速算数
}

type Brackets []Bracket

func (bs Brackets) MaxLeverage() (leverage float64) {
	for _, b := range bs {
		if b.InitialLeverage > leverage {
			leverage = b.InitialLeverage
		}
	}
	return leverage
}

func (bs Brackets) Get(value float64) *Bracket {
	value = math.Abs(value)
	sort.SliceStable(bs, func(i, j int) bool {
		return bs[i].Level < bs[j].Level
	})

	for idx := range bs {
		b := bs[idx]
		if b.NotionalCap > value {
			return &b
		}
	}
	return nil
}

type Instrument struct {
	Symbol               string
	ContractType         string
	MarkPrice            float64
	LastPrice            float64 // 最新价格
	FundingRate          float64 // 当前资金费用
	FundingIntervalHours int
	NextFundingTime      time.Time
	OpenInterest         float64
	IndexPrice           float64
	tickerUpdateTime     int64
	TickSize             float64 // 价格最小变动单位
	StepSize             float64 // 数量最小变动单位
	MinQty               float64
	MaxQty               float64
	MarketMaxQty         float64
	ContractSize         float64
	MaxLeverage          float64
	Brackets             *Brackets
	MinNotional          float64
	BaseAsset            string
	QuoteAsset           string
	MarginAsset          string
	DeliveryDate         int64
	updatedAt            int64 // 更新时间
	instrumentType       exchange.InstrumentType
}

func (this Instrument) IsExpiredFutures() bool {
	if this.DeliveryDate == 0 {
		return false
	}

	t := time.Unix(this.DeliveryDate/1000, 0)
	if t.Before(time.Now()) {
		return true
	}
	return false
}

func (i Instrument) IsExpired() bool {
	return (i.updatedAt + 3600) < time.Now().Unix()
}

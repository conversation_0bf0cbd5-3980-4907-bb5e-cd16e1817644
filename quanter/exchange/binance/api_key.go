package binance

import (
	"errors"
	"fmt"
	"strings"

	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/secrets"
)

// Binance
type APISecret struct {
	exchange.BaseAPISecret
	Email string
}

func NewEmptyAPISecret() *APISecret {
	s := &APISecret{
		BaseAPISecret: exchange.BaseAPISecret{
			ExchangeName: exchange.Binance,
		},
	}
	s.Secretable = s
	return s
}

func NewAPISecret(apiKey string, apiSecret secrets.SecretString, isEncrypted bool) (*APISecret, error) {
	s := NewEmptyAPISecret()
	s.ApiKey = apiKey
	if isEncrypted {
		s.EncyptedApiSecret = apiSecret
		err := s.Decrypt()
		if err != nil {
			return nil, err
		}
	} else {
		s.DecryptedApiSecret = apiSecret
	}
	err := s.Decode()
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (this *APISecret) Decode() error {
	if strings.Contains(string(this.DecryptedApiSecret), "|||") {
		parts := strings.Split(string(this.DecryptedApiSecret), "|||")
		if len(parts) != 2 {
			return errors.New("wrong number of arguments")
		}
		this.BaseAPISecret.ApiSecret = secrets.SecretString(parts[0])
		this.Email = parts[1]
	} else {
		this.BaseAPISecret.ApiSecret = this.DecryptedApiSecret
	}
	return nil
}

func (this *APISecret) Encode() secrets.SecretString {
	this.DecryptedApiSecret = secrets.SecretString(fmt.Sprintf("%s|||%s", this.ApiSecret.Reveal(), this.Email))
	return this.DecryptedApiSecret
}

func (this *APISecret) Prompt() bool {
	this.ApiKey = exchange.SurveyInput("请输入 ApiKey: ")
	apiSecret := exchange.SurveyPassword("请输入 ApiSecret: ")
	this.ApiSecret = secrets.SecretString(apiSecret)
	// 组合 secret 和 password
	this.Email = exchange.SurveyInput("请输入账号邮箱: ")
	fmt.Printf("\n\n*** 请确认输入的API *** \n\nAPI Key: %s\nAPI Secret: %s\nEmail: %s\n\n",
		this.ApiKey,
		exchange.FormatPassword(apiSecret),
		this.Email,
	)
	return true
}

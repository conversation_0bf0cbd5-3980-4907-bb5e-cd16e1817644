package binance

import (
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/wizhodl/quanter/exchange"
)

func (this *Binance) GetSupportedInstrumentTypes() []exchange.InstrumentType {
	return []exchange.InstrumentType{exchange.Spot, exchange.CoinMarginedFutures, exchange.USDXMarginedFutures}
}

func (this *Binance) GetInstrument(instrumentType exchange.InstrumentType, symbol string) (i *exchange.Instrument, _ error) {
	ins := this.getInstrument(instrumentType, symbol)
	if ins == nil {
		return i, fmt.Errorf("%s instrument not found", symbol)
	}

	if ins.instrumentType == "" {
		return nil, fmt.Errorf("%s instrument miss InstrumentType", symbol)
	}

	i = &exchange.Instrument{}
	i.InstrumentType = ins.instrumentType
	i.Symbol = ins.Symbol
	i.MinNotional = ins.MinNotional
	i.TickSize = ins.TickSize
	i.LotSize = ins.StepSize
	i.MinSize = ins.MinQty
	i.ContractSize = ins.ContractSize
	i.MarkPrice = ins.MarkPrice
	i.IndexPrice = ins.IndexPrice
	i.OpenInterest = ins.OpenInterest
	i.LastPrice = ins.LastPrice
	i.FundingRate = ins.FundingRate
	i.FundingRatePeriod = fmt.Sprintf("%dh", ins.FundingIntervalHours)
	i.FundingTime = ins.NextFundingTime
	i.MaxLeverage = ins.MaxLeverage
	i.SettleCurrency = ins.MarginAsset
	i.UnderlyCurrency = ins.BaseAsset
	i.QuoteCurrency = ins.QuoteAsset

	if ins.tickerUpdateTime > 0 {
		i.UpdateTime = time.Unix(ins.tickerUpdateTime, 0)
	} else {
		i.UpdateTime = time.Unix(ins.updatedAt, 0)
	}

	switch i.ContractType {
	case "PERPETUAL":
		i.ContractType = exchange.ContractTypeSwap
	case "CURRENT_QUARTER":
		i.ContractType = exchange.ContractTypeQuarter
	case "NEXT_QUARTER":
		i.ContractType = exchange.ContractTypeNextQuarter
	}

	return
}

func (this *Binance) GetLastPrice(instrumentType exchange.InstrumentType, symbol string, allowDelay bool) (float64, error) {
	// if price := this.GetTickerCachePrice(getInstrumentsCacheKey(instrumentType, symbol)); price > 0 {
	// 	return price, nil
	// }

	if instrumentType == exchange.Spot {
		return this.spotGetLastPrice(symbol)
	} else if instrumentType == exchange.CoinMarginedFutures {
		return this.cFuturesGetLastPrice(symbol)
	} else if instrumentType == exchange.USDXMarginedFutures {
		return this.uFuturesGetLastPrice(symbol)
	}
	return 0, exchange.ErrNotImplemented
}

func (this *Binance) CreateOrder(args exchange.CreateOrderArgs) (order *exchange.Order, err error) {
	if args.InstrumentType == exchange.Spot {
		order, err = this.spotCreateOrder(args)
	} else if args.InstrumentType == exchange.CoinMarginedFutures {
		order, err = this.cFuturesCreateOrder(args)
	} else if args.InstrumentType == exchange.USDXMarginedFutures {
		order, err = this.uFuturesCreateOrder(args)
	} else {
		return nil, exchange.ErrNotImplemented
	}
	if err != nil {
		return nil, err
	}
	nowTime := time.Now()
	order.CreateTime = &nowTime
	order.UpdateTime = &nowTime
	order.InstrumentType = args.InstrumentType
	return order, err
}

func (this *Binance) GetOrder(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol, orderID string, timeRange *exchange.TimeRange) (order *exchange.Order, err error) {
	if instrumentType == exchange.Spot {
		order, err = this.spotGetOrder(symbol, orderID)
	} else if instrumentType == exchange.CoinMarginedFutures {
		order, err = this.cFuturesGetOrder(symbol, orderID)
	} else if instrumentType == exchange.USDXMarginedFutures {
		order, err = this.uFuturesGetOrder(symbol, orderID)
	} else {
		return nil, exchange.ErrNotImplemented
	}
	if err != nil || order == nil {
		return
	}
	order.InstrumentType = instrumentType
	return
}

func (this *Binance) CancelOrder(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol, orderID string) error {
	if instrumentType == exchange.Spot {
		return this.spotCancelOrder(symbol, orderID)
	} else if instrumentType == exchange.CoinMarginedFutures {
		return this.cFuturesCancelOrder(symbol, orderID)
	} else if instrumentType == exchange.USDXMarginedFutures {
		return this.uFuturesCancelOrder(symbol, orderID)
	}
	return exchange.ErrNotImplemented
}

func (this *Binance) TransferAsset(from, to exchange.InstrumentType, coin string, amount float64) error {
	var tranferType string
	if from == exchange.Spot {
		if to == exchange.USDXMarginedFutures {
			tranferType = "1"
		} else if to == exchange.CoinMarginedFutures {
			tranferType = "3"
		}
	} else if to == exchange.Spot {
		if from == exchange.USDXMarginedFutures {
			tranferType = "2"
		} else if from == exchange.CoinMarginedFutures {
			tranferType = "4"
		}
	}

	if tranferType == "" {
		return exchange.ErrNotImplemented
	}

	params := map[string]string{
		"asset":  coin,
		"amount": strconv.FormatFloat(amount, 'f', -1, 64),
		"type":   tranferType,
	}
	_, err := this.do("/sapi/v1/futures/transfer", params, "POST")
	return err
}

func (this *Binance) GetTradablePairs(instrumentType exchange.InstrumentType, uSymbol string) (pairs []string, _ error) {
	var instruments []Instrument
	if instrumentType == exchange.CoinMarginedFutures {
		instruments = this.cFuturesCacheLatestInstruments()
	} else if instrumentType == exchange.USDXMarginedFutures {
		instruments = this.uFuturesCacheLatestInstruments()
	} else if instrumentType == exchange.Spot {
		instruments = this.spotCacheLatestInstruments()
	} else {
		return pairs, exchange.ErrNotImplemented
	}

	for _, instrument := range instruments {
		if instrument.IsExpiredFutures() {
			continue
		}
		if instrumentType == exchange.USDXMarginedFutures && uSymbol != instrument.QuoteAsset {
			continue
		}
		pairs = append(pairs, instrument.Symbol)
	}
	return
}

func (this *Binance) GetAccountConfig(instrumentType exchange.InstrumentType) (cfg *exchange.AccountConfig, err error) {
	if instrumentType == exchange.Spot {
		return nil, exchange.ErrNotAvailableForInstrumentType
	}
	var dualPositionSide bool
	marginMode := exchange.AccountMarginModeIsolated
	if instrumentType == exchange.CoinMarginedFutures {
		dualPositionSide, err = this.cFuturesGetDualPositionSide()
	} else if instrumentType == exchange.USDXMarginedFutures {
		dualPositionSide, err = this.uFuturesGetDualPositionSide()

		// 目前仅U合约支持联合保证金模式
		if multiAssetsMargin, er := this.uFuturesGetMultiAssetsMargin(); er != nil {
			return nil, err
		} else if multiAssetsMargin {
			marginMode = exchange.AccountMarginModeCross
		}
	}
	if err != nil {
		return nil, err
	}
	this.dualPositionSides.Store(string(instrumentType), dualPositionSide)
	return &exchange.AccountConfig{
		MarginMode:       marginMode,
		DualPositionSide: dualPositionSide,
	}, nil
}

func (this *Binance) GetAccountBalances(instrumentType exchange.InstrumentType) ([]*exchange.AccountBalance, error) {
	if instrumentType == exchange.Spot {
		return this.spotGetAccount()
	} else if instrumentType == exchange.CoinMarginedFutures {
		return this.cFuturesGetAccount()
	} else if instrumentType == exchange.USDXMarginedFutures {
		return this.uFuturesGetAccount()
	}
	return nil, exchange.ErrNotImplemented
}

func (this *Binance) GetAccountCurrencies(instrumentType exchange.InstrumentType) (currencies []string, _ error) {
	bl, err := this.GetAccountBalances(instrumentType)
	if err != nil {
		return []string{}, err
	}
	for _, this := range bl {
		currencies = append(currencies, this.Currency)
	}
	return currencies, nil
}

func (this *Binance) GetPositions(instrumentType exchange.InstrumentType, symbol string, allowCache bool) ([]*exchange.Position, error) {
	if instrumentType == exchange.CoinMarginedFutures {
		return this.cFuturesGetPositions(symbol)
	} else if instrumentType == exchange.USDXMarginedFutures {
		return this.uFuturesGetPositions(symbol)
	}
	return []*exchange.Position{}, exchange.ErrNotImplemented
}

func (this *Binance) SetDualPositionSide(instrumentType exchange.InstrumentType, dualPositionSide bool) error {
	this.dualPositionSides.Store(string(instrumentType), dualPositionSide)
	if instrumentType == exchange.CoinMarginedFutures {
		return this.cFuturesSetDualPositionSide(dualPositionSide)
	} else if instrumentType == exchange.USDXMarginedFutures {
		return this.uFuturesSetDualSidePosition(dualPositionSide)
	}
	return exchange.ErrNotImplemented
}

func (this *Binance) QueryFundingHistory(instrumentType exchange.InstrumentType, symbol string, limit int, from time.Time, to time.Time) ([]*exchange.FundingHistory, error) {
	if instrumentType == exchange.CoinMarginedFutures {
		return this.cFuturesQueryFundingHistory(symbol, limit, from, to)
	} else if instrumentType == exchange.USDXMarginedFutures {
		return this.uFuturesQueryFundingHistory(symbol, limit, from, to)
	}
	return []*exchange.FundingHistory{}, exchange.ErrNotImplemented
}

func (this *Binance) SetLeverage(instrumentType exchange.InstrumentType, symbol string, marginMode exchange.MarginMode, side exchange.PositionSide, leverage float64) error {
	if instrumentType == exchange.CoinMarginedFutures {
		return this.cFuturesSetLeverage(symbol, leverage)
	} else if instrumentType == exchange.USDXMarginedFutures {
		return this.uFuturesSetLeverage(symbol, leverage)
	}
	return exchange.ErrNotImplemented
}

func (this *Binance) GetLeverage(instrumentType exchange.InstrumentType, symbol string, marginMode exchange.MarginMode) (leverageLong, leverageShort float64, err error) {
	return 0, 0, exchange.ErrNotImplemented
}

func (this *Binance) SetMarginMode(instrumentType exchange.InstrumentType, symbol string, mode exchange.MarginMode) error {
	if instrumentType == exchange.CoinMarginedFutures {
		return this.cFuturesSetMarginType(symbol, mode)
	} else if instrumentType == exchange.USDXMarginedFutures {
		return this.uFuturesSetMarginType(symbol, mode)
	}
	return exchange.ErrNotImplemented
}

func (this *Binance) SetAccountMarginMode(instrumentType exchange.InstrumentType, mode exchange.AccountMarginMode) error {
	if instrumentType == exchange.USDXMarginedFutures {
		v := false
		if mode == exchange.AccountMarginModeCross {
			v = true
		}
		return this.uFuturesSetMultiAssetsMargin(v)
	}
	return nil
}

// 更新交易对信息，force 时忽略缓存
func (this *Binance) CacheInstruments(force bool) error {
	spotCached := false
	cFutureCached := false
	uFutureCached := false
	this.instruments.Range(func(_ string, instrument *Instrument) bool {
		if instrument.instrumentType == exchange.Spot {
			spotCached = true
		} else if instrument.instrumentType == exchange.CoinMarginedFutures {
			cFutureCached = true
		} else if instrument.instrumentType == exchange.USDXMarginedFutures {
			uFutureCached = true
		}
		return true
	})

	if force || !spotCached {
		if len(this.spotCacheLatestInstruments()) == 0 {
			return errors.New("cache spot instruments failed")
		}
	}

	if force || !cFutureCached {
		if len(this.cFuturesCacheLatestInstruments()) == 0 {
			return errors.New("cache cfutures instruments failed")
		}
	}

	if force || !uFutureCached {
		if len(this.uFuturesCacheLatestInstruments()) == 0 {
			return errors.New("cache ufutures instruments failed")
		}
	}

	go this.updateInstrumentPricesLoop()

	return nil
}

// 更新合约 markprice 等价格信息
func (this *Binance) updateInstrumentPrices() {
	this.uFuturesUpdateInstrumentPrices()
}

func (this *Binance) updateInstrumentPricesLoop() {
	if !this.updatePriceMutex.TryLock() {
		return
	}
	defer this.updatePriceMutex.Unlock()

	this.updateInstrumentPrices()

	t := time.NewTicker(time.Second * 5)
	for {
		if this.Removed.Load() {
			break
		}

		select {
		case <-t.C:
			this.updateInstrumentPrices()
		}
	}
}

// // CodeToSymbolFixes 定义特殊的 code 到 symbol 的映射关系
// code -> spotSymbol, spotCode, futureSymbol
var CodeToSymbolFixes = map[string][3]string{
	"KPEPE00.U": {"", "", "1000PEPEUSDT"},
}

// // SymbolToCodeFixes 定义特殊的 symbol 到 code 的映射关系
// // futureSymbol -> spotSymbol, spotCode, futureCode
// var SymbolToCodeFixes = map[string][3]string{
// 	"ETHBTC": {"ETH/BTC", "ETH-BTC", "ETHBTC00.U"},
// }

// ETHBTC 合约是一种特殊合约，BTC 做保证金和计价货币，ETH 做单位，目前不符合 USDTMarginedFutures 的定义，暂不兼容

func (this *Binance) TranslateSymbolCode(symbolCode *exchange.SymbolCode) (spotAndFutureSymbols []*exchange.SymbolPair, er error) {
	spotAndFutureSymbols = []*exchange.SymbolPair{}
	spotSymbol := ""
	futureSymbol := ""

	// 有些 symbolCode 比较特殊，需要特殊处理
	spotSymbol, _, futureSymbol, fixed := this.FixSymbolCode(CodeToSymbolFixes, symbolCode)
	if fixed {
		spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(symbolCode, spotSymbol, futureSymbol))
		return
	}

	this.CacheInstruments(false)

	spotPairs := []string{}
	futurePairs := []string{}
	this.instruments.Range(func(_ string, ins *Instrument) bool {
		if ins.instrumentType == exchange.Spot {
			spotPairs = append(spotPairs, ins.Symbol)
		} else if ins.instrumentType == exchange.CoinMarginedFutures || ins.instrumentType == exchange.USDXMarginedFutures {
			futurePairs = append(futurePairs, ins.Symbol)
		}
		return true
	})

	uSymbol := symbolCode.USDXSymbol
	spotQuote := uSymbol
	if symbolCode.IsSpot() {
		// 其他类型如 CoinMarginedFutures 会返回 USD
		spotQuote = this.GetSymbolCodeQuote(symbolCode)
	}

	for _, s := range spotPairs {
		if strings.EqualFold(s, fmt.Sprintf("%s%s", symbolCode.Coin(), spotQuote)) {
			spotSymbol = s
		}
	}

	if symbolCode.IsFuture() {
		marginSep := "USD_"
		if symbolCode.InstrumentType() == exchange.USDXMarginedFutures {
			marginSep = uSymbol
		}
		for _, s := range futurePairs {
			if symbolCode.IsWildcard() {
				if strings.HasPrefix(s, symbolCode.Coin()+marginSep) {
					futureSymbol = s
				}
			} else {
				month := symbolCode.Month()
				suffix := uSymbol
				if symbolCode.IsPerp() && symbolCode.InstrumentType() == exchange.CoinMarginedFutures {
					suffix = "_PERP"
				}
				equalPrefix := strings.HasPrefix(s, symbolCode.Coin()+marginSep)
				equalNotPerp := !strings.HasSuffix(s, suffix) && s[len(s)-4:len(s)-2] == month
				equalPerp := strings.HasSuffix(s, suffix) && strings.EqualFold(month, "00")
				if equalPrefix && (equalNotPerp || equalPerp) {
					futureSymbol = s
				}
			}
			if futureSymbol != "" {
				spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(symbolCode, spotSymbol, futureSymbol))
				if !symbolCode.IsWildcard() {
					break
				}
			}
		}
	} else {
		if spotSymbol != "" {
			spotAndFutureSymbols = append(spotAndFutureSymbols, exchange.NewSymbolPair(symbolCode, spotSymbol, ""))
		} else {
			er = fmt.Errorf("spot symbol not found (%s)", symbolCode)
			return
		}
	}
	if len(spotAndFutureSymbols) == 0 {
		er = fmt.Errorf("spot and future symbol not found (%s)", symbolCode)
	}
	return
}

// futureSymbol -> spotSymbol, spotCode, futureCode
var SymbolToCodeFixes = map[string][3]string{
	"1000PEPEUSDT": {"", "", "KPEPE00.U"},
}

func (this *Binance) TranslateFutureSymbol(instrumentType exchange.InstrumentType, futureSymbol string, uSymbol string) (spotSymbol string, futureCode *exchange.SymbolCode, er error) {
	// 有些 futureSymbol 比较特殊，需要特殊处理
	var fixed bool
	spotSymbol, _, futureCode, fixed = this.FixFutureSymbol(SymbolToCodeFixes, instrumentType, futureSymbol, uSymbol)
	if fixed {
		return
	}

	spotSymbol = ""
	spotPairs := []string{}
	futurePairs := []string{}
	this.instruments.Range(func(_ string, ins *Instrument) bool {
		if ins.instrumentType == exchange.Spot {
			spotPairs = append(spotPairs, ins.Symbol)
		} else if ins.instrumentType == exchange.CoinMarginedFutures || ins.instrumentType == exchange.USDXMarginedFutures {
			futurePairs = append(futurePairs, ins.Symbol)
		}
		return true
	})

	if uSymbol == "" {
		uSymbol = "USDT"
	}

	for _, s := range futurePairs {
		futureCodeStr := ""
		if strings.EqualFold(futureSymbol, s) {
			marginSep := "USD_"
			// 注意：U 本位永续直接是 USDT 结尾，币本位永续是 USD_PERP 结尾
			if instrumentType == exchange.USDXMarginedFutures {
				marginSep = fmt.Sprintf("%s_", uSymbol)
				if strings.HasSuffix(s, uSymbol) {
					s += "_PERP" // 补上通用格式
				}
			}
			parts := strings.Split(s, marginSep)
			if len(parts) == 2 {
				futureCodeStr += parts[0]

				if instrumentType == exchange.USDXMarginedFutures && !strings.HasPrefix(uSymbol, "USD") {
					futureCodeStr += fmt.Sprintf("-%s", uSymbol) // 非 USDx 的 U 合约，如 ETHBTC
				}

				tail := parts[1]
				if tail != "PERP" {
					futureCodeStr += tail[2:4]
				} else {
					futureCodeStr += "00" // 永续代号
				}
				// TODO: not tested
				if instrumentType == exchange.USDXMarginedFutures {
					futureCodeStr += ".U"
				}
				futureCode = &exchange.SymbolCode{Code: futureCodeStr, USDXSymbol: uSymbol}
			}
		}
	}
	if futureCode == nil {
		er = errors.New("future symbol not found")
		return
	}
	for _, s := range spotPairs {
		if strings.EqualFold(s, fmt.Sprintf("%s%s", futureCode.Coin(), uSymbol)) {
			spotSymbol = s
		}
	}
	return
}

func (this *Binance) GetLatestKLines(instrumentType exchange.InstrumentType, symbol string, periodHour, num int) (klines []*exchange.KLine, er error) {
	origKlines, err := this.getHistory(symbol, periodHour, num)
	if err != nil {
		er = err
		return
	}

	// origKlines 里是小时线，需合并小时线为 PeriodHour 小时线
	// 取 PeriodHour 点，如 6小时线，取 0、6、12、18 点
	periodSeconds := int64(periodHour * 3600)
	klineCount := 0
	for _, k := range origKlines {
		if k.Time%periodSeconds == 0 {
			// 这根小时线是 PeriodHour 的开盘线
			klineCount++

			klines = append(klines, &exchange.KLine{
				Open:  k.Open,
				Close: k.Close,
				High:  k.High,
				Low:   k.Low,
				Time:  k.Time,
			})
		} else if klineCount > 0 {
			// 非 PeriodHour 的开盘线
			klineIdx := klineCount - 1

			klines[klineIdx].Close = k.Close
			klines[klineIdx].High = math.Max(klines[klineIdx].High, k.High)
			klines[klineIdx].Low = math.Min(klines[klineIdx].Low, k.Low)
		}
	}
	return
}

func (this *Binance) CancelAllOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string) ([]string, error) {
	// 先获取将被取消的订单, 成功取消后记录 ID
	openOrders, err := this.GetOpenOrders(instrumentType, orderType, symbol)
	if err != nil {
		return []string{}, fmt.Errorf("get open orders failed: %v", err)
	}

	if symbol == "" {
		return []string{}, errors.New("symbol cannot be empty")
	}

	this.Infof("(%s) cancel all orders", symbol)
	params := map[string]string{
		"symbol": symbol,
	}

	path := ""
	if instrumentType == exchange.USDXMarginedFutures {
		path = "/fapi/v1/allOpenOrders"
	} else if instrumentType == exchange.CoinMarginedFutures {
		path = "/dapi/v1/allOpenOrders"
	} else if instrumentType == exchange.Spot {
		path = "/api/v3/openOrders"
	} else {
		return []string{}, exchange.ErrNotAvailableForInstrumentType
	}

	_, err = this.do(path, params, "DELETE")
	if err != nil {
		return []string{}, err
	}

	canceledIDs := []string{}
	for _, o := range openOrders {
		this.recordCanceledOrderID(symbol, o.OrderID)
		canceledIDs = append(canceledIDs, o.OrderID)
	}

	return canceledIDs, nil
}

func (this *Binance) GetUserMargin(instrumentType exchange.InstrumentType, currency string) (margin *exchange.UserMargin, err error) {
	if instrumentType == exchange.Spot {
		return nil, exchange.ErrNotAvailableForInstrumentType
	}
	ret, err := this.do("/fapi/v2/account", nil)
	if err != nil {
		return nil, err
	}

	for _, item := range ret.Get("assets").Array() {
		if currency == item.Get("asset").String() {
			margin = new(exchange.UserMargin)
			margin.Currency = currency
			margin.WalletBalance = item.Get("walletBalance").Float()
			margin.MarginBalance = item.Get("marginBalance").Float()
			margin.AvailableMargin = item.Get("availableBalance").Float()
			return
		}
	}
	return nil, errors.New("not found user margin")
}

func (this *Binance) GetOpenOrders(instrumentType exchange.InstrumentType, orderType exchange.OrderType, symbol string) (orders []*exchange.Order, err error) {
	if instrumentType == exchange.USDXMarginedFutures {
		return this.uGetOpenOrders(orderType, symbol)
	} else if instrumentType == exchange.CoinMarginedFutures {
		return this.cFuturesGetOpenOrders(orderType, symbol)
	} else if instrumentType == exchange.Spot {
		return this.spotGetOpenOrders(orderType, symbol)
	}
	return nil, exchange.ErrNotAvailableForInstrumentType
}

// 更新订单
func (this *Binance) UpdateOrder(origOrder exchange.Order, args *exchange.UpdateOrderArgs) (*exchange.Order, error) {
	if args.InstrumentType != exchange.USDXMarginedFutures {
		return nil, exchange.ErrNotImplementedForInstrumentType
	}

	oldOrder, err := this.uFuturesGetOrder(origOrder.Symbol, origOrder.OrderID)
	if err != nil {
		return nil, err
	}

	err = this.uFuturesCancelOrder(origOrder.Symbol, origOrder.OrderID)
	if err != nil {
		return nil, err
	}
	if args.OrderQty == 0 {
		args.OrderQty = oldOrder.Qty - oldOrder.ExecQty
	}

	// 如果是止损单并且不是止盈单，使用mark price
	newArgs := exchange.CreateOrderArgs{
		Symbol:           oldOrder.Symbol,
		Side:             oldOrder.Side,
		Type:             oldOrder.Type,
		Price:            args.Price,
		TriggerPrice:     args.TriggerPrice,
		Qty:              args.OrderQty,
		ReduceOnly:       args.ReduceOnly,
		TriggerPriceType: args.TriggerPriceType,
	}
	if args.TriggerPrice == 0 {
		newArgs.TriggerPrice = oldOrder.TriggerPrice
	}
	order, err := this.uFuturesCreateOrder(newArgs)
	if order != nil {
		order.SetExtStruct(&origOrder)
		order.InstrumentType = args.InstrumentType
	}
	return order, err
}

func (this *Binance) GetSymbolCodeUnit(symbolCode *exchange.SymbolCode) string {
	if symbolCode.InstrumentType() == exchange.Spot {
		return symbolCode.Coin()
	} else if symbolCode.InstrumentType() == exchange.USDXMarginedFutures {
		return symbolCode.Coin()
	} else {
		return "cnt"
	}
}

// U 本位合约面值为币的数量，订单数量单位为币数量，Size 表示 U 价值
// 币本位合约面值为 USD，订单数量单位为张，Size 表示币价值
func (this *Binance) Qty2Size(instrumentType exchange.InstrumentType, symbol string, price, qty float64) (float64, error) {
	ins := this.getInstrument(instrumentType, symbol)
	if ins == nil {
		return 0, fmt.Errorf("cannot find instrument %s", symbol)
	}
	if ins.instrumentType == exchange.USDXMarginedFutures || ins.instrumentType == exchange.Spot {
		value := price * qty
		roundSize := math.Min(ins.TickSize, 0.01)
		return math.Round(value/roundSize) * roundSize, nil
	} else if ins.instrumentType == exchange.CoinMarginedFutures {
		return qty * ins.ContractSize / price, nil
	} else {
		return 0, exchange.ErrNotAvailableForInstrumentType
	}
}

// 计算合约数量
func (this *Binance) Size2Qty(instrumentType exchange.InstrumentType, symbol string, price, value float64) (float64, error) {
	ins := this.getInstrument(instrumentType, symbol)
	if ins == nil {
		return 0, fmt.Errorf("cannot find instrument %s", symbol)
	}

	if ins.instrumentType == exchange.USDXMarginedFutures {
		qty := value / price
		stepSize := ins.StepSize
		qty = math.Round(qty/stepSize) * stepSize

		// 舍去 stepSize 后面的小数位
		stepPow := math.Pow(10, float64(exchange.DecimalBit(stepSize)))
		return float64(int(qty*stepPow)) / stepPow, nil
	} else if ins.instrumentType == exchange.CoinMarginedFutures {
		return value * price / ins.ContractSize, nil
	} else {
		return 0, exchange.ErrNotAvailableForInstrumentType
	}
}

// 计算合约价格
func (this *Binance) CalcPrice(instrumentType exchange.InstrumentType, symbol string, qty, value float64) (float64, error) {
	ins := this.getInstrument(instrumentType, symbol)
	if ins == nil {
		return 0, fmt.Errorf("cannot find instrument %s", symbol)
	}

	if ins.instrumentType == exchange.USDXMarginedFutures {
		price := value / qty
		tickSize := ins.TickSize
		return math.Round(price/tickSize) * tickSize, nil
	} else if ins.instrumentType == exchange.CoinMarginedFutures {
		return qty * ins.ContractSize / value, nil
	} else {
		return 0, exchange.ErrNotAvailableForInstrumentType
	}
}

func (this *Binance) MaxLeverage(instrumentType exchange.InstrumentType, symbol string, value float64) float64 {
	instrument := this.getInstrument(instrumentType, symbol)
	if instrument != nil {
		return instrument.Brackets.Get(value).InitialLeverage
	} else {
		return 0
	}
}

func (this *Binance) Withdraw(coin string, address string, amount float64, chain exchange.WithdrawChain) (id string, err error) {
	params := map[string]string{
		"coin":    coin,
		"address": address,
		"amount":  strconv.FormatFloat(amount, 'f', -1, 64),
		"network": chain2Network(chain),
	}
	if params["network"] == "" {
		return "", errors.New("invalid chain")
	}
	ret, err := this.do("/sapi/v1/capital/withdraw/apply", params, "POST")
	if err != nil {
		return "", err
	}
	return ret.Get("id").String(), nil
}

func (this *Binance) GetWithdrawChains(coin string) ([]exchange.WithdrawChain, error) {
	ret, err := this.do("/sapi/v1/capital/config/getall", nil)
	if err != nil {
		return nil, err
	}
	var chains []exchange.WithdrawChain
	for _, item := range ret.Array() {
		if item.Get("coin").String() == coin {
			networkList := item.Get("networkList").Array()
			for _, network := range networkList {
				networkName := network2Chain(network.Get("network").String())
				if networkName != "" {
					chains = append(chains, networkName)
				}
			}
		}
	}
	return chains, nil
}

func (this *Binance) GetWithdrawFee(coin, chain exchange.WithdrawChain) (fee float64, err error) {
	ret, err := this.do("/sapi/v1/capital/config/getall", nil)
	if err != nil {
		return 0, err
	}
	for _, item := range ret.Array() {
		if item.Get("coin").String() == coin {
			networkList := item.Get("networkList").Array()
			for _, network := range networkList {
				if network.Get("network").String() == chain2Network(chain) {
					return network.Get("withdrawFee").Float(), nil
				}
			}
		}
	}
	return 0, errors.New("not found")
}

func network2Chain(network string) exchange.WithdrawChain {
	switch network {
	case "BTC":
		return exchange.WithdrawChainBTC
	case "ETH":
		return exchange.WithdrawChainETH
	case "ARBITRUM":
		return exchange.WithdrawChainARBI
	default:
		return ""
	}
}

func chain2Network(chain exchange.WithdrawChain) string {
	switch chain {
	case exchange.WithdrawChainBTC:
		return "BTC"
	case exchange.WithdrawChainETH:
		return "ETH"
	case exchange.WithdrawChainARBI:
		return "ARBITRUM"
	default:
		return ""
	}
}

// 子母账户万能划转
// 需要开启母账户apikey“允许子母账户划转”权限。
// 若 fromEmail 未传，默认从母账户转出。
// 若 toEmail 未传，默认转入母账户。
// 最少指定fromEmail和toEmail 其中之一。
// 该接口支持的划转操作有：
// 现货账户划转到现货账户、U本位合约账户、币本位合约账户（无论母账户或子账户）
// 现货账户、U本位合约账户、币本位合约账户划转到现货账户（无论母账户或子账户）
// 母账户现货账户划转到子账户杠杆全仓账户、杠杆逐仓账户
// 子账户杠杆全仓账户、杠杆逐仓账户划转到母账户现货账户
// 子账户杠杆全仓账户划转到子账户杠杆全仓账户
func (this *Binance) UniversalTransfer(fromEmail, toEmail string, fromAccountType, toAccountType exchange.InstrumentType, coin string, amount float64) (string, error) {
	params := map[string]string{
		"fromEmail":       fromEmail,
		"toEmail":         toEmail,
		"fromAccountType": instrumentType2AccountType(fromAccountType),
		"toAccountType":   instrumentType2AccountType(toAccountType),
		"asset":           coin,
		"amount":          strconv.FormatFloat(amount, 'f', -1, 64),
	}
	res, err := this.do("/sapi/v1/sub-account/universalTransfer", params, "POST")
	if err != nil {
		return "", err
	}
	return res.Get("tranId").String(), nil
}

func instrumentType2AccountType(instrumentType exchange.InstrumentType) string {
	switch instrumentType {
	case exchange.Spot:
		return "SPOT"
	case exchange.CoinMarginedFutures:
		return "COIN_FUTURE"
	case exchange.USDXMarginedFutures:
		return "USDT_FUTURE"
	default:
		return ""
	}
}

func (this *Binance) IsSubaccount() bool {
	return this.withdrawApiKey != nil && this.withdrawApiKey.Email != "" && this.apiKey.Email != this.withdrawApiKey.Email
}

func (this *Binance) SubaccountTransfer(coin string, amount float64, isDeposit bool) (err error) {
	if !this.IsSubaccount() {
		return nil
	}

	from := this.apiKey.Email
	to := this.withdrawApiKey.Email
	if isDeposit {
		from, to = to, from
	}
	_, err = this.UniversalTransfer(from, to, exchange.Spot, exchange.Spot, coin, amount)
	return
}

func (this *Binance) GetWithdrawAccountBalance(coin string) (total, available float64, er error) {
	ret, err := this._do("/api/v3/account", nil, this.IsSubaccount())
	if err != nil {
		return 0, 0, err
	}
	for _, balance := range ret.Get("balances").Array() {
		free := balance.Get("free").Float()
		locked := balance.Get("locked").Float()
		if balance.Get("asset").String() == coin {
			return free + locked, free, nil
		}
	}
	return
}

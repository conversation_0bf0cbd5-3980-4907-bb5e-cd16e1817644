package binance

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/wizhodl/quanter/exchange"

	"github.com/tidwall/gjson"
)

func (this *Binance) spotFetchInstruments() (instruments []Instrument) {
	ret, err := this.do("/api/v1/exchangeInfo", nil)
	if err != nil {
		this.Errorf("get spot exchangeInfo error, %v", err)
		return
	}
	symbols := ret.Get("symbols").Array()

	for _, data := range symbols {
		symbol := data.Get("symbol").String()
		i := &Instrument{}
		i.Symbol = symbol
		i.TickSize = data.Get(`filters.#(filterType=="PRICE_FILTER").tickSize`).Float()
		i.StepSize = data.Get(`filters.#(filterType=="LOT_SIZE").stepSize`).Float()
		i.MinNotional = data.Get(`filters.#(filterType=="MIN_NOTIONAL").minNotional`).Float()
		i.MinQty = data.Get(`filters.#(filterType=="LOT_SIZE").minQty`).Float()
		i.MaxQty = data.Get(`filters.#(filterType=="LOT_SIZE").maxQty`).Float()
		i.MarketMaxQty = data.Get(`filters.#(filterType=="MARKET_LOT_SIZE").maxQty`).Float()
		i.BaseAsset = data.Get("baseAsset").String()
		i.QuoteAsset = data.Get("quoteAsset").String()
		i.updatedAt = time.Now().Unix()
		i.instrumentType = exchange.Spot
		instruments = append(instruments, *i)
	}

	return
}

func (this *Binance) spotCacheLatestInstruments() (instruments []Instrument) {
	instruments = this.spotFetchInstruments()
	for idx := range instruments {
		instrument := instruments[idx]
		this.instruments.Store(getInstrumentsCacheKey(exchange.Spot, instrument.Symbol), &instrument)
	}
	return
}

func (this *Binance) spotGetAccount() (accountBalances []*exchange.AccountBalance, _ error) {
	ret, err := this.do("/api/v3/account", nil)
	if err != nil {
		return accountBalances, err
	}
	for _, balance := range ret.Get("balances").Array() {
		free := balance.Get("free").Float()
		locked := balance.Get("locked").Float()
		accountBalances = append(accountBalances, &exchange.AccountBalance{
			InstrumentType: exchange.Spot,
			Currency:       balance.Get("asset").String(),
			Total:          free + locked,
			Available:      free,
		})
	}
	return
}

func (this *Binance) spotGetLastPrice(symbol string) (float64, error) {
	symbol = strings.ReplaceAll(symbol, "-", "")
	ret, err := this.do("/api/v3/ticker/price", map[string]string{"symbol": symbol})
	if err != nil {
		return 0, err
	}
	return ret.Get("price").Float(), nil
}

func (this *Binance) spotCreateOrder(args exchange.CreateOrderArgs) (order *exchange.Order, _ error) {
	side := "BUY"
	if args.Side == exchange.OrderSideSell {
		side = "SELL"
	}
	params := map[string]string{
		"symbol": args.Symbol,
		"side":   side,
	}

	if args.QuoteQty != 0 {
		params["quoteOrderQty"] = strconv.FormatFloat(args.QuoteQty, 'f', -1, 64)
	} else if args.Qty != 0 {
		params["quantity"] = this.FormatQty(exchange.Spot, args.Symbol, args.Qty)
	} else {
		return nil, fmt.Errorf("quoteQty or qty must be set")
	}

	if args.Price != 0 {
		params["price"] = this.FormatPrice(exchange.Spot, args.Symbol, args.Price)
	}

	if args.TriggerPrice != 0 {
		params["stopPrice"] = this.FormatPrice(exchange.Spot, args.Symbol, args.TriggerPrice)
	}

	if args.TimeInForce == exchange.IOC {
		params["timeInForce"] = "IOC"
	}

	switch args.Type {
	case exchange.Limit:
		params["type"] = "LIMIT"
		if params["timeInForce"] == "" { // LIMIT 时必需
			params["timeInForce"] = "GTC"
		}
	case exchange.Market:
		params["type"] = "MARKET"
	case exchange.StopLimit:
		lastPrice, err := this.spotGetLastPrice(args.Symbol)
		if err != nil {
			return nil, err
		}
		// 根据限价决定是止损还是止盈单
		if (args.Side == exchange.OrderSideSell && args.TriggerPrice < lastPrice) ||
			(args.Side == exchange.OrderSideBuy && args.TriggerPrice > lastPrice) {
			params["type"] = "STOP_LOSS_LIMIT"
		} else {
			params["type"] = "TAKE_PROFIT_LIMIT"
		}

		if params["timeInForce"] == "" { // 必需
			params["timeInForce"] = "GTC"
		}
	}

	ret, err := this.do("/api/v3/order", params, "POST")
	if err != nil {
		return order, err
	}

	order = &exchange.Order{
		InstrumentType: args.InstrumentType,
		OrderID:        ret.Get("orderId").String(),
		Symbol:         args.Symbol,
		Price:          args.Price,
		TriggerPrice:   args.TriggerPrice,
		Qty:            args.Qty,
		QuoteQty:       args.QuoteQty,
		Type:           args.Type,
		Side:           args.Side,
		Status:         exchange.OrderStatusNew,
		TimeInForce:    args.TimeInForce,
		ReduceOnly:     args.ReduceOnly,
	}

	var trades []exchange.TradeHistory
	for _, fill := range ret.Get("fills").Array() {
		trades = append(trades, exchange.TradeHistory{
			Price:    fill.Get("price").Float(),
			Qty:      fill.Get("qty").Float(),
			Fee:      fill.Get("commission").Float(),
			FeeAsset: fill.Get("commissionAsset").String(),
		})
	}
	order.Trades = trades
	return
}

func (this *Binance) spotGetOrder(symbol, orderID string) (order *exchange.Order, _ error) {
	ret, err := this.do("/api/v3/order", map[string]string{"symbol": symbol, "orderId": orderID})
	if err != nil {
		return order, err
	}
	order = convertSpotOrder(ret)
	return
}

func convertSpotOrder(ret *gjson.Result) *exchange.Order {
	order := &exchange.Order{}
	order.InstrumentType = exchange.Spot
	order.OrderID = ret.Get("orderId").String()
	order.Symbol = ret.Get("symbol").String()
	order.Price = ret.Get("price").Float()
	order.TriggerPrice = ret.Get("stopPrice").Float()
	order.Qty = ret.Get("origQty").Float()
	order.QuoteQty = ret.Get("origQuoteOrderQty").Float()
	order.ExecQty = ret.Get("executedQty").Float()

	tradeValue := ret.Get("cummulativeQuoteQty").Float()
	if tradeValue != 0 && order.ExecQty != 0 {
		order.ExecPrice = tradeValue / order.ExecQty
	}

	switch ret.Get("type").String() {
	case "LIMIT":
		order.Type = exchange.Limit
	case "MARKET":
		order.Type = exchange.Market
	case "STOP_LOSS_LIMIT":
		order.Type = exchange.StopLimit
	case "TAKE_PROFIT_LIMIT":
		order.Type = exchange.StopLimit
	}

	order.Side = exchange.OrderSideBuy
	if ret.Get("side").String() == "SELL" {
		order.Side = exchange.OrderSideSell
	}

	switch ret.Get("status").String() {
	case "NEW":
		order.Status = exchange.OrderStatusNew
	case "PARTIALLY_FILLED":
		order.Status = exchange.OrderStatusPartialFilled
	case "FILLED":
		order.Status = exchange.OrderStatusFilled
	case "CANCELED":
		order.Status = exchange.OrderStatusCancelled
	default:
		order.Status = exchange.UnknownOrderStatus
	}

	if ret.Get("timeInForce").String() == "IOC" {
		order.TimeInForce = exchange.IOC
	}

	t := time.Unix(ret.Get("time").Int()/1000, 0)
	ut := time.Unix(ret.Get("updateTime").Int()/1000, 0)
	order.CreateTime = &t
	order.UpdateTime = &ut
	return order
}

func (this *Binance) spotCancelOrder(symbol string, orderID string) error {
	_, err := this.do("/api/v3/order", map[string]string{"symbol": symbol, "orderId": orderID}, "DELETE")
	return err
}

func (c *Binance) spotGetOpenOrders(orderType exchange.OrderType, symbol string) (orders []*exchange.Order, err error) {
	params := map[string]string{}
	if symbol != "" {
		params["symbol"] = symbol
	}
	ret, err := c.do("/api/v3/openOrders", params)
	if err != nil {
		return nil, err
	}
	os := make([]*exchange.Order, 0)
	for _, item := range ret.Array() {
		order := convertSpotOrder(&item)
		if orderType != exchange.UnknownOrderType && order.Type != orderType {
			continue
		}
		os = append(os, order)
	}
	return os, nil
}

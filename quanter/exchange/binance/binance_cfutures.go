package binance

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/wizhodl/quanter/exchange"
)

func (this *Binance) cFuturesSetDualPositionSide(dualSidePosition bool) error {
	params := map[string]string{
		"dualSidePosition": "false",
	}
	if dualSidePosition {
		params["dualSidePosition"] = "true"
	}
	_, err := this.do("/dapi/v1/positionSide/dual", params, "POST")
	return err
}

func (this *Binance) cFuturesGetDualPositionSide() (bool, error) {
	ret, err := this.do("/dapi/v1/positionSide/dual", nil)
	if err != nil {
		return false, err
	}
	return ret.Get("dualSidePosition").Bool(), nil
}

func (this *Binance) cFuturesFetchInstruments() (instruments []Instrument) {
	ret, err := this.do("/dapi/v1/exchangeInfo", nil)
	if err != nil {
		this.Errorf("get exchangeInfo error, %v", err)
		return
	}
	symbols := ret.Get("symbols").Array()

	for _, data := range symbols {
		symbol := data.Get("symbol").String()
		i := &Instrument{}
		i.Symbol = symbol
		i.ContractType = data.Get("contractType").String()
		i.ContractSize = data.Get("contractSize").Float()
		i.TickSize = data.Get(`filters.#(filterType=="PRICE_FILTER").tickSize`).Float()
		i.StepSize = data.Get(`filters.#(filterType=="LOT_SIZE").stepSize`).Float()
		i.MinNotional = data.Get(`filters.#(filterType=="MIN_NOTIONAL").minNotional`).Float()
		i.MinQty = data.Get(`filters.#(filterType=="LOT_SIZE").minQty`).Float()
		i.MaxQty = data.Get(`filters.#(filterType=="LOT_SIZE").maxQty`).Float()
		i.MarketMaxQty = data.Get(`filters.#(filterType=="MARKET_LOT_SIZE").maxQty`).Float()
		i.MarginAsset = data.Get("marginAsset").String()
		i.BaseAsset = data.Get("baseAsset").String()
		i.QuoteAsset = data.Get("quoteAsset").String()
		i.DeliveryDate = data.Get("deliveryDate").Int()
		i.FundingIntervalHours = 8 // 默认8小时,调整过的通过下面的接口获取
		i.updatedAt = time.Now().Unix()
		i.instrumentType = exchange.CoinMarginedFutures
		instruments = append(instruments, *i)
	}

	ret, err = this.do("/dapi/v1/fundingInfo", nil)
	if err != nil {
		this.Errorf("get fundingInfo error: %v", err)
		return
	}
	for _, info := range ret.Array() {
		symbol := info.Get("symbol").String()
		for idx := range instruments {
			instrument := &instruments[idx]
			if instrument.Symbol == symbol {
				instrument.FundingIntervalHours = int(info.Get("fundingIntervalHours").Int())
				break
			}
		}
	}

	return
}

func (this *Binance) cFuturesCacheLatestInstruments() (instruments []Instrument) {
	instruments = this.cFuturesFetchInstruments()
	for idx := range instruments {
		instrument := instruments[idx]
		this.instruments.Store(getInstrumentsCacheKey(exchange.CoinMarginedFutures, instrument.Symbol), &instrument)
	}
	return
}

func (this *Binance) cFuturesGetAccount() (accountBalances []*exchange.AccountBalance, _ error) {
	ret, err := this.do("/dapi/v1/account", nil)
	if err != nil {
		return accountBalances, err
	}
	for _, balance := range ret.Get("assets").Array() {
		accountBalances = append(accountBalances, &exchange.AccountBalance{
			InstrumentType: exchange.CoinMarginedFutures,
			Currency:       balance.Get("asset").String(),
			Total:          balance.Get("marginBalance").Float(),
			Available:      balance.Get("availableBalance").Float(),
		})
	}
	return
}

func (this *Binance) cFuturesGetPositions(symbol string) (positions []*exchange.Position, _ error) {
	ret, err := this.do("/dapi/v1/positionRisk", map[string]string{"symbol": symbol})
	if err != nil {
		return positions, err
	}
	for _, data := range ret.Array() {
		qty := data.Get("positionAmt").Float()
		if qty == 0 {
			continue
		}

		pos := exchange.Position{}
		pos.InstrumentType = exchange.CoinMarginedFutures
		pos.ExchangeName = exchange.OKEx
		pos.Symbol = data.Get("symbol").String()
		pos.Qty = qty
		if qty > 0 {
			pos.Side = exchange.PositionSideLong
		} else {
			pos.Side = exchange.PositionSideShort
		}
		pos.EntryPrice = data.Get("entryPrice").Float()
		pos.MarkPrice = data.Get("markPrice").Float()
		pos.LastPrice = pos.MarkPrice
		pos.Leverage = data.Get("leverage").Float()
		pos.LiquidationPrice = data.Get("liquidationPrice").Float()
		unpnl := data.Get("unRealizedProfit").Float()
		pos.UnrealisedPNL = unpnl
		pos.RealisedPNL = 0
		pos.Margin = data.Get("isolatedMargin").Float() - unpnl
		positions = append(positions, &pos)
	}
	return
}

func (this *Binance) cFuturesGetLastPrice(symbol string) (float64, error) {
	ret, err := this.do("/dapi/v1/ticker/price", map[string]string{"symbol": symbol})
	if err != nil {
		return 0, err
	}
	for _, p := range ret.Array() {
		return p.Get("price").Float(), nil
	}
	return 0, fmt.Errorf("cannot find price of %s", symbol)
}

func (this *Binance) cFuturesSetLeverage(symbol string, leverage float64) error {
	params := map[string]string{
		"symbol":   symbol,
		"leverage": fmt.Sprintf("%v", int(leverage)),
	}
	_, err := this.do("/dapi/v1/leverage", params, "POST")
	return err
}

func (this *Binance) cFuturesSetMarginType(symbol string, marginType exchange.MarginMode) error {
	params := map[string]string{
		"symbol": symbol,
	}
	if marginType == exchange.Isolated {
		params["marginType"] = "ISOLATED"
	} else {
		params["marginType"] = "CROSSED"
	}
	_, err := this.do("/dapi/v1/marginType", params, "POST")
	return err
}

func (this *Binance) cFuturesQueryFundingHistory(symbol string, limit int, startTime time.Time, endTime time.Time) (his []*exchange.FundingHistory, _ error) {
	params := map[string]string{
		"symbol": symbol,
		"limit":  strconv.FormatInt(int64(limit), 10),
	}
	if !startTime.IsZero() && !endTime.IsZero() {
		if startTime.After(endTime) {
			return his, errors.New("startTime cannot be after endTime")
		}
		params["startTime"] = strconv.FormatInt(startTime.Unix(), 10)
		params["endTime"] = strconv.FormatInt(endTime.Unix(), 10)
	}
	ret, err := this.do("/dapi/v1/fundingRate", params)
	if err != nil {
		return his, err
	}

	period := "8h"
	ins, _ := this.instruments.Load(getInstrumentsCacheKey(exchange.CoinMarginedFutures, symbol))
	if ins != nil {
		period = fmt.Sprintf("%dh", ins.FundingIntervalHours)
	}

	_, code, err := this.TranslateFutureSymbol(exchange.CoinMarginedFutures, symbol, "")
	if err != nil {
		return his, err
	}

	for _, h := range ret.Array() {
		his = append(his, &exchange.FundingHistory{
			Code:     code,
			Exchange: exchange.Binance,
			Symbol:   symbol,
			Rate:     h.Get("fundingRate").Float(),
			Time:     time.Unix(h.Get("fundingTime").Int()/1000, 0),
			Period:   period,
		})
	}
	return
}

func (this *Binance) cFuturesCreateOrder(args exchange.CreateOrderArgs) (order *exchange.Order, _ error) {
	side := "BUY"
	if args.Side == exchange.OrderSideSell {
		side = "SELL"
	}
	params := map[string]string{
		"symbol":   args.Symbol,
		"side":     side,
		"quantity": this.FormatQty(exchange.CoinMarginedFutures, args.Symbol, args.Qty),
		"price":    this.FormatPrice(exchange.CoinMarginedFutures, args.Symbol, args.Price),
	}

	if args.TriggerPrice != 0 {
		params["stopPrice"] = this.FormatPrice(exchange.CoinMarginedFutures, args.Symbol, args.TriggerPrice)
	}

	if args.TimeInForce == exchange.IOC {
		params["timeInForce"] = "IOC"
	}

	switch args.Type {
	case exchange.Limit:
		params["type"] = "LIMIT"
		if params["timeInForce"] == "" { // LIMIT 时必需
			params["timeInForce"] = "GTC"
		}
	case exchange.Market:
		params["type"] = "MARKET"
	case exchange.StopLimit:
		params["type"] = "STOP"
	}

	if args.ReduceOnly {
		params["reduceOnly"] = "true"
	}

	ret, err := this.do("/dapi/v1/order", params, "POST")
	if err != nil {
		return order, err
	}

	order = &exchange.Order{
		InstrumentType: args.InstrumentType,
		OrderID:        ret.Get("orderId").String(),
		Symbol:         args.Symbol,
		Price:          args.Price,
		TriggerPrice:   args.TriggerPrice,
		Qty:            args.Qty,
		QuoteQty:       args.QuoteQty,
		Type:           args.Type,
		Side:           args.Side,
		Status:         exchange.OrderStatusNew,
		TimeInForce:    args.TimeInForce,
		ReduceOnly:     args.ReduceOnly,
	}
	return
}

func (this *Binance) cFuturesGetOrder(symbol, orderID string) (order *exchange.Order, _ error) {
	ret, err := this.do("/dapi/v1/order", map[string]string{"symbol": symbol, "orderId": orderID})
	if err != nil {
		return order, err
	}
	order = &exchange.Order{}
	order.InstrumentType = exchange.CoinMarginedFutures
	order.OrderID = orderID
	order.Symbol = symbol
	toOrder(ret, order, exchange.CoinMarginedFutures)
	return
}

func (this *Binance) cFuturesCancelOrder(symbol string, orderID string) error {
	_, err := this.do("/dapi/v1/order", map[string]string{"symbol": symbol, "orderId": orderID}, "DELETE")
	return err
}

func (this *Binance) cFuturesGetOpenOrders(orderType exchange.OrderType, symbol string) (orders []*exchange.Order, err error) {
	params := map[string]string{}
	if symbol != "" {
		params["symbol"] = symbol
	}
	ret, err := this.do("/dapi/v1/openOrders", params)
	if err != nil {
		return nil, err
	}
	os := make([]*exchange.Order, 0)
	for _, item := range ret.Array() {
		var order exchange.Order
		toOrder(&item, &order, exchange.CoinMarginedFutures)

		if this.isOrderCanceled(symbol, order.OrderID) {
			this.Warnf("(%s) order has been canceled, order: %s", symbol, order.OrderID)
			continue
		}

		if orderType != exchange.UnknownOrderType && order.Type != orderType {
			continue
		}

		os = append(os, &order)
	}
	return os, nil
}

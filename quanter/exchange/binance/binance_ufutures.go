package binance

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/exchange"

	"github.com/spf13/cast"
)

func (this *Binance) uFuturesGetAccount() (accountBalances []*exchange.AccountBalance, _ error) {
	ret, err := this.do("/fapi/v2/account", nil)
	if err != nil {
		return accountBalances, err
	}
	for _, balance := range ret.Get("assets").Array() {
		total := balance.Get("marginBalance").Float()
		available := balance.Get("availableBalance").Float()
		if available > total {
			// 若用户开启多资产模式, availableBalance 计入各种资产并转化为其USD价值显示
			available = total
		}
		accountBalances = append(accountBalances, &exchange.AccountBalance{
			InstrumentType: exchange.USDXMarginedFutures,
			Currency:       balance.Get("asset").String(),
			Total:          total,
			Available:      available,
		})
	}
	return
}

func (this *Binance) uFuturesFetchInstruments() (instruments []Instrument) {
	ret, err := this.do("/fapi/v1/exchangeInfo", nil)
	if err != nil {
		this.Errorf("get exchangeInfo error: %v", err)
		return
	}
	symbols := ret.Get("symbols").Array()

	for _, data := range symbols {
		i := &Instrument{}
		i.Symbol = data.Get("symbol").String()
		i.ContractType = data.Get("contractType").String()
		i.TickSize = data.Get(`filters.#(filterType=="PRICE_FILTER").tickSize`).Float()
		i.StepSize = data.Get(`filters.#(filterType=="LOT_SIZE").stepSize`).Float()
		i.MinNotional = data.Get(`filters.#(filterType=="MIN_NOTIONAL").minNotional`).Float()
		i.MinQty = data.Get(`filters.#(filterType=="LOT_SIZE").minQty`).Float()
		i.MaxQty = data.Get(`filters.#(filterType=="LOT_SIZE").maxQty`).Float()
		i.MarketMaxQty = data.Get(`filters.#(filterType=="MARKET_LOT_SIZE").maxQty`).Float()
		i.MarginAsset = data.Get("marginAsset").String()
		i.BaseAsset = data.Get("baseAsset").String()
		i.QuoteAsset = data.Get("quoteAsset").String()
		i.DeliveryDate = data.Get("deliveryDate").Int()
		i.updatedAt = time.Now().Unix()
		i.instrumentType = exchange.USDXMarginedFutures
		i.FundingIntervalHours = 8 // 默认8小时,调整过的通过下面的接口获取
		instruments = append(instruments, *i)
	}

	// 查询资金费率信息，接口仅返回FundingRateCap/FundingRateFloor/fundingIntervalHours等被特殊调整过的交易对，没调整过的不返回。
	ret, err = this.do("/fapi/v1/fundingInfo", nil)
	if err != nil {
		this.Errorf("get fundingInfo error: %v", err)
		return
	}
	for _, info := range ret.Array() {
		symbol := info.Get("symbol").String()
		for idx := range instruments {
			instrument := &instruments[idx]
			if instrument.Symbol == symbol {
				instrument.FundingIntervalHours = int(info.Get("fundingIntervalHours").Int())
				break
			}
		}
	}

	return
}

func (this *Binance) uFuturesCacheLatestInstruments() (instruments []Instrument) {
	instruments = this.uFuturesFetchInstruments()
	for idx := range instruments {
		instrument := instruments[idx]
		this.instruments.Store(getInstrumentsCacheKey(exchange.USDXMarginedFutures, instrument.Symbol), &instrument)
	}
	return
}

func (this *Binance) uFuturesGetPositions(symbol string) (positions []*exchange.Position, _ error) {
	ret, err := this.do("/fapi/v2/positionRisk", map[string]string{"symbol": symbol})
	if err != nil {
		return nil, err
	}
	var poss []*exchange.Position
	for _, item := range ret.Array() {
		qty := item.Get("positionAmt").Float()
		if qty == 0 {
			continue
		}

		var p exchange.Position
		p.InstrumentType = exchange.USDXMarginedFutures
		p.ExchangeName = exchange.Binance
		p.Symbol = item.Get("symbol").String()
		p.Qty = qty
		if qty > 0 {
			p.Side = exchange.PositionSideLong
		} else {
			p.Side = exchange.PositionSideShort
		}
		p.Leverage = item.Get("leverage").Float()
		p.MarkPrice = item.Get("markPrice").Float()
		p.LastPrice = p.MarkPrice
		p.LiquidationPrice = item.Get("liquidationPrice").Float()
		p.EntryPrice = item.Get("entryPrice").Float()
		unpnl := item.Get("unRealizedProfit").Float()
		p.UnrealisedPNL = unpnl
		p.RealisedPNL = 0.0
		p.Margin = item.Get("isolatedMargin").Float() - unpnl
		poss = append(poss, &p)
	}
	return poss, nil
}

func toOrder(item *gjson.Result, order *exchange.Order, instrumentType exchange.InstrumentType) {
	order.InstrumentType = instrumentType
	order.Symbol = item.Get("symbol").String()
	order.OrderID = item.Get("orderId").String()
	order.Side = exchange.OrderSideSell
	if item.Get("side").String() == "BUY" {
		order.Side = exchange.OrderSideBuy
	}
	order.Qty = item.Get("origQty").Float()
	order.Price = item.Get("price").Float()
	order.ExecPrice = item.Get("avgPrice").Float()
	order.TriggerPrice = item.Get("stopPrice").Float()

	switch item.Get("status").String() {
	case "NEW":
		order.Status = exchange.OrderStatusNew
	case "PARTIALLY_FILLED":
		order.Status = exchange.OrderStatusPartialFilled
	case "FILLED":
		order.Status = exchange.OrderStatusFilled
	case "CANCELED":
		order.Status = exchange.OrderStatusCancelled
	default:
		order.Status = exchange.UnknownOrderStatus
	}

	switch item.Get("type").String() {
	case "LIMIT":
		order.Type = exchange.Limit
	case "MARKET":
		order.Type = exchange.Market
	case "STOP":
		order.Type = exchange.StopLimit
	case "TAKE_PROFIT":
		order.Type = exchange.StopLimit
	case "STOP_MARKET":
		order.Type = exchange.StopMarket
	case "TAKE_PROFIT_MARKET":
		order.Type = exchange.StopMarket
	}

	switch item.Get("timeInForce").String() {
	case "GTC":
		order.TimeInForce = exchange.GTC
	case "IOC":
		order.TimeInForce = exchange.IOC
	}

	order.ReduceOnly = item.Get("reduceOnly").Bool()
	order.ExecQty = item.Get("executedQty").Float()
	createTime := time.Unix(item.Get("time").Int()/1e3, 0)
	order.CreateTime = &createTime
	updateTime := time.Unix(item.Get("updateTime").Int()/1e3, 0)
	order.UpdateTime = &updateTime
}

func (this *Binance) uFuturesCreateOrder(args exchange.CreateOrderArgs) (order *exchange.Order, err error) {
	err = this.CheckQuoteQty(&args)
	if err != nil {
		return nil, fmt.Errorf("check order quoteQty failed, %v", err)
	}

	params := map[string]string{
		"symbol":       args.Symbol,
		"positionSide": "SHORT",
		"side":         strings.ToUpper(string(args.Side)),
		"type":         "LIMIT",
		"price":        this.FormatPrice(exchange.USDXMarginedFutures, args.Symbol, args.Price),
		"quantity":     this.FormatQty(exchange.USDXMarginedFutures, args.Symbol, args.Qty),
		"timeInForce":  "GTC",
	}
	if args.TriggerPrice != 0 {
		delete(params, "timeInForce")
		params["stopPrice"] = this.FormatPrice(exchange.USDXMarginedFutures, args.Symbol, args.TriggerPrice)
		params["type"] = "STOP"
	}
	if args.Type == exchange.StopMarket {
		delete(params, "price")
		params["type"] = "STOP_MARKET"
	}
	if args.TriggerPriceType == exchange.TriggerPriceTypeMark {
		params["workingType"] = "MARK_PRICE"
	}
	if args.Price <= 0 {
		delete(params, "price")
		delete(params, "timeInForce")
		params["type"] = "MARKET"
	}
	if args.ReduceOnly {
		// 用平仓不能用STOP_LIMIT，所以此处不用平仓单
		// params["closePosition"] = "true"
		// delete(params, "quantity")
		if args.Side == exchange.OrderSideSell {
			params["positionSide"] = "LONG"
		}
		// 平仓单，可以传最大的数量
		if args.Qty <= 0 {
			params["quantity"] = this.FormatQty(exchange.USDXMarginedFutures, args.Symbol, this.getInstrument(exchange.USDXMarginedFutures, args.Symbol).MaxQty)
			if params["type"] == "MARKET" {
				params["quantity"] = this.FormatQty(exchange.USDXMarginedFutures, args.Symbol, this.getInstrument(exchange.USDXMarginedFutures, args.Symbol).MarketMaxQty)
			}
		}
	} else {
		if args.Side == exchange.OrderSideBuy {
			params["positionSide"] = "LONG"
		}
	}

	if dualPositionSide, found := this.dualPositionSides.Load(string(exchange.USDXMarginedFutures)); found && !dualPositionSide {
		params["positionSide"] = "BOTH" // 单向持仓模式下仅可填BOTH
		if args.ReduceOnly {
			params["reduceOnly"] = "true" // 非双开模式下可用
		}
	}

	body, _ := json.Marshal(params)
	this.Infof("(%s) create order, body: %v", args.Symbol, string(body))

	ret, err := this.do("/fapi/v1/order", params, "POST")
	if err != nil {
		if strings.Contains(err.Error(), "immediately trigger.") && strings.Contains(params["type"], "STOP") {
			r := strings.NewReplacer("STOP", "TAKE_PROFIT")
			params["type"] = r.Replace(params["type"])
			this.Warnf("(%s) replace order with TAKE_PROFIT type", args.Symbol)
			ret, err = this.do("/fapi/v1/order", params, "POST")
		}
	}
	if err != nil {
		return nil, err
	}
	order = new(exchange.Order)
	toOrder(ret, order, exchange.USDXMarginedFutures)
	return
}

func (this *Binance) uFuturesGetOrder(symbol string, orderID string) (order *exchange.Order, err error) {
	params := map[string]string{
		"symbol":  symbol,
		"orderId": orderID,
	}
	ret, err := this.do("/fapi/v1/order", params)
	if err != nil {
		return nil, err
	}
	order = new(exchange.Order)
	toOrder(ret, order, exchange.USDXMarginedFutures)
	return
}

func (this *Binance) uFuturesCancelOrder(symbol string, orderID string) error {
	this.Infof("(%s) cancel order %s", symbol, orderID)
	params := map[string]string{
		"symbol":  symbol,
		"orderId": orderID,
	}
	_, err := this.do("/fapi/v1/order", params, "DELETE")
	if err != nil {
		return err
	}

	this.recordCanceledOrderID(symbol, orderID)
	return nil
}

func (this *Binance) uFuturesSetLeverage(symbol string, leverage float64) error {
	params := map[string]string{
		"symbol":   symbol,
		"leverage": cast.ToString(int(leverage)),
	}
	this.Debugf("(%s) set leverage to %d", symbol, int(leverage))
	_, err := this.do("/fapi/v1/leverage", params, "POST")
	return err
}

func (this *Binance) uFuturesSetMarginType(symbol string, marginType exchange.MarginMode) error {
	params := map[string]string{
		"symbol": symbol,
	}
	if marginType == exchange.Isolated {
		params["marginType"] = "ISOLATED"
	} else {
		params["marginType"] = "CROSSED"
	}
	_, err := this.do("/fapi/v1/marginType", params, "POST")
	return err
}

func (this *Binance) uFuturesGetLastPrice(symbol string) (float64, error) {
	ret, err := this.do("/fapi/v1/ticker/price", map[string]string{"symbol": symbol})
	if err != nil {
		return 0, err
	}
	return ret.Get("price").Float(), nil
}

func (this *Binance) uFuturesSetDualSidePosition(dualSidePosition bool) error {
	dual := "false"
	if dualSidePosition {
		dual = "true"
	}
	params := map[string]string{
		"dualSidePosition": dual,
	}
	_, err := this.do("/fapi/v1/positionSide/dual", params, "POST")
	return err
}

func (this *Binance) uFuturesGetDualPositionSide() (bool, error) {
	ret, err := this.do("/fapi/v1/positionSide/dual", nil)
	if err != nil {
		return false, err
	}
	return ret.Get("dualSidePosition").Bool(), nil
}

// 是否开启联合保证金模式
func (this *Binance) uFuturesGetMultiAssetsMargin() (bool, error) {
	ret, err := this.do("/fapi/v1/multiAssetsMargin", nil)
	if err != nil {
		return false, err
	}
	return ret.Get("multiAssetsMargin").Bool(), nil
}

func (this *Binance) uFuturesSetMultiAssetsMargin(multiAssetsMargin bool) error {
	v := "false"
	if multiAssetsMargin {
		v = "true"
	}
	params := map[string]string{
		"multiAssetsMargin": v,
	}
	_, err := this.do("/fapi/v1/multiAssetsMargin", params, "POST")
	return err
}

func (this *Binance) uGetOpenOrders(orderType exchange.OrderType, symbol string) (orders []*exchange.Order, err error) {
	params := map[string]string{}
	if symbol != "" {
		params["symbol"] = symbol
	}
	ret, err := this.do("/fapi/v1/openOrders", params)
	if err != nil {
		return nil, err
	}
	os := make([]*exchange.Order, 0)
	for _, item := range ret.Array() {
		var order exchange.Order
		toOrder(&item, &order, exchange.USDXMarginedFutures)

		if this.isOrderCanceled(symbol, order.OrderID) {
			this.Warnf("(%s) order %s has been canceled", symbol, order.OrderID)
			continue
		}

		if orderType != exchange.UnknownOrderType && order.Type != orderType {
			continue
		}

		os = append(os, &order)
	}
	return os, nil
}

func (this *Binance) uFuturesQueryFundingHistory(symbol string, limit int, startTime time.Time, endTime time.Time) (his []*exchange.FundingHistory, _ error) {
	params := map[string]string{
		"symbol": symbol,
		"limit":  strconv.FormatInt(int64(limit), 10),
	}
	if !startTime.IsZero() && !endTime.IsZero() {
		if startTime.After(endTime) {
			return his, errors.New("startTime cannot be after endTime")
		}
		params["startTime"] = strconv.FormatInt(startTime.Unix(), 10)
		params["endTime"] = strconv.FormatInt(endTime.Unix(), 10)
	}
	ret, err := this.do("/fapi/v1/fundingRate", params)
	if err != nil {
		return his, err
	}

	period := "8h"
	ins, _ := this.instruments.Load(getInstrumentsCacheKey(exchange.USDXMarginedFutures, symbol))
	if ins != nil {
		period = fmt.Sprintf("%dh", ins.FundingIntervalHours)
	}

	_, code, err := this.TranslateFutureSymbol(exchange.USDXMarginedFutures, symbol, "")
	if err != nil {
		return his, err
	}

	for _, h := range ret.Array() {
		his = append(his, &exchange.FundingHistory{
			Code:     code,
			Exchange: exchange.Binance,
			Symbol:   symbol,
			Rate:     h.Get("fundingRate").Float(),
			Time:     time.Unix(h.Get("fundingTime").Int()/1000, 0),
			Period:   period,
		})
	}
	return
}

func (this *Binance) uFuturesUpdateInstrumentPrices() (err error) {
	ret, err := this.do("/fapi/v1/premiumIndex", nil)
	if err != nil {
		return err
	}
	for _, item := range ret.Array() {
		symbol := item.Get("symbol").String()
		ins, _ := this.instruments.Load(getInstrumentsCacheKey(exchange.USDXMarginedFutures, symbol))
		if ins == nil {
			continue
		}
		ins.MarkPrice = item.Get("markPrice").Float()
		ins.IndexPrice = item.Get("indexPrice").Float()
		ins.FundingRate = item.Get("lastFundingRate").Float()
		ins.NextFundingTime = time.UnixMilli(item.Get("nextFundingTime").Int())
		ins.tickerUpdateTime = time.Now().Unix()
	}
	return
}

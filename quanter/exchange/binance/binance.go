package binance

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"

	"github.com/gorilla/websocket"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"

	backoff "github.com/cenkalti/backoff/v4"
	resty "github.com/go-resty/resty/v2"
	funk "github.com/thoas/go-funk"
)

var _ exchange.Exchange = (*Binance)(nil)

const SPOT_HOST_URL = "https://api.binance.com"
const USDT_FUTURES_HOST_URL = "https://fapi.binance.com"
const COIN_FUTURES_HOST_URL = "https://dapi.binance.com"
const TESTNET_HOST_URL = "https://testnet.binancefuture.com"

const MAX_CANCELED_ORDERID_NUM = 1000

type Binance struct {
	exchange.BaseExchange

	instruments       *xsync.MapOf[string, *Instrument]
	canceledOrderIDs  *xsync.MapOf[string, []string] // 因为币安有个获取挂单偶尔会返回已取消订单的 bug，所以记录下已取消订单来排除
	dualPositionSides *xsync.MapOf[string, bool]

	wsConns           *xsync.MapOf[string, *websocket.Conn]
	wsBackOffs        *xsync.MapOf[string, *backoff.ExponentialBackOff]
	subscribedSymbols *xsync.MapOf[string, []string]
	updatePriceMutex  sync.Mutex

	apiKey         *APISecret
	withdrawApiKey *APISecret
}

func NewBinance(options *exchange.Options) (*Binance, error) {
	if options.ControllerID == "" {
		return nil, errors.New("controller id is empty")
	}

	apiKey, err := NewAPISecret(options.ApiKey, options.ApiSecret, false)
	if err != nil {
		return nil, err
	}

	var withdrawApiKey *APISecret
	if options.WithdrawApiKey != "" && options.WithdrawApiSecret != "" {
		withdrawApiKey, err = NewAPISecret(options.WithdrawApiKey, options.WithdrawApiSecret, false)
		if err != nil {
			return nil, err
		}
	}

	b := &Binance{
		BaseExchange:      *exchange.NewBaseExchange(options),
		instruments:       xsync.NewMapOf[*Instrument](),
		canceledOrderIDs:  xsync.NewMapOf[[]string](),
		dualPositionSides: xsync.NewMapOf[bool](),
		apiKey:            apiKey,
		withdrawApiKey:    withdrawApiKey,
	}
	b.Exchange = b
	return b, nil
}

func (this *Binance) GetName() string {
	return exchange.Binance
}

func (this *Binance) sign(data map[string]string, secret secrets.SecretString) string {
	data["timestamp"] = fmt.Sprint(time.Now().UnixNano() / 1e6)
	keys := funk.Keys(data).([]string)
	sort.Strings(keys)
	var ret []string
	for _, key := range keys {
		ret = append(ret, fmt.Sprintf("%v=%v", key, data[key]))
	}
	h := hmac.New(sha256.New, []byte(secret.Reveal()))
	h.Write([]byte(strings.Join(ret, "&")))
	val := fmt.Sprintf("%02X", h.Sum(nil))
	ret = append(ret, "signature="+val)
	return strings.Join(ret, "&")
}

var PUBLIC_PATHS = []string{
	"/api/v1/exchangeInfo",
	"/api/v3/ticker/price",
	"/api/v3/userDataStream", // 这个居然只要 header 里的 APIKey 即可，无需签名
}

func include(data []string, element string) bool {
	for _, v := range data {
		if element == v {
			return true
		}
	}
	return false
}

func (this *Binance) do(path string, params map[string]string, methods ...string) (ret *gjson.Result, err error) {
	return this._do(path, params, false, methods...)
}

func (this *Binance) doWithdrawKey(path string, params map[string]string, methods ...string) (ret *gjson.Result, err error) {
	return this._do(path, params, true, methods...)
}

func (this *Binance) _do(path string, params map[string]string, useWithdrawApiKey bool, methods ...string) (ret *gjson.Result, err error) {
	method := "GET"
	if len(methods) > 0 {
		method = methods[0]
	}
	if params == nil {
		params = make(map[string]string)
	}

	if this.IsSubaccount() && utils.SliceContains([]string{
		"/sapi/v1/capital/withdraw/apply",
		"/sapi/v1/capital/config/getall",
		"/sapi/v1/sub-account/universalTransfer",
	}, path) {
		useWithdrawApiKey = true
	}

	apiKey := this.apiKey.ApiKey
	secret := this.apiKey.ApiSecret
	if useWithdrawApiKey {
		apiKey = this.withdrawApiKey.ApiKey
		secret = this.withdrawApiKey.ApiSecret
	}

	req := this.Client.R()
	if include(PUBLIC_PATHS, path) {
		var ret []string
		for k, v := range params {
			ret = append(ret, fmt.Sprintf("%v=%v", k, v))
		}
		path += "?" + strings.Join(ret, "&")

		if strings.HasPrefix(path, "/api/v3/userDataStream") {
			req.SetHeader("X-MBX-APIKEY", apiKey)
		}
	} else {
		params["recvWindow"] = "30000"
		path += "?" + this.sign(params, secret)
		req.SetHeader("X-MBX-APIKEY", apiKey)
	}

	if this.IsTestnet {
		this.Client.SetBaseURL(TESTNET_HOST_URL)
	} else if strings.HasPrefix(path, "/fapi") {
		this.Client.SetBaseURL(USDT_FUTURES_HOST_URL)
	} else if strings.HasPrefix(path, "/dapi") {
		this.Client.SetBaseURL(COIN_FUTURES_HOST_URL)
	} else {
		this.Client.SetBaseURL(SPOT_HOST_URL)
	}

	var resp *resty.Response
	if method == "POST" {
		resp, err = req.Post(path)
	} else if method == "PUT" {
		resp, err = req.Put(path)
	} else if method == "DELETE" {
		resp, err = req.Delete(path)
	} else {
		resp, err = req.Get(path)
	}
	if err != nil {
		return nil, err
	}

	newRet := gjson.Parse(resp.String())
	if resp.StatusCode() != 200 {
		msg := newRet.Get("msg").String()
		code := newRet.Get("code").Int()
		this.Errorf("binance req[%s] resp error: params: %v, err: [%v]%v", path, params, code, msg)
		return &newRet, fmt.Errorf("[%v] %v", code, msg)
	}
	return &newRet, nil
}

func (this *Binance) getBracket(instrument *Instrument) {
	if this.apiKey.ApiSecret == "" {
		this.Errorf("(%s) need ApiSecret to get bracket", instrument.Symbol)
		return
	}
	params := map[string]string{"symbol": instrument.Symbol}
	ret, err := this.do("/fapi/v1/leverageBracket", params)
	if err != nil {
		this.Errorf("(%s) get leverage bracket error, %v", instrument.Symbol, err)
		return
	}

	for _, item := range ret.Array() {
		var bs Brackets
		for _, bt := range item.Get("brackets").Array() {
			var b Bracket
			b.Level = bt.Get("bracket").Int()
			b.InitialLeverage = bt.Get("initialLeverage").Float()
			b.NotionalCap = bt.Get("notionalCap").Float()
			b.MaintMarginRatio = bt.Get("maintMarginRatio").Float()
			b.Cum = bt.Get("cum").Float()
			bs = append(bs, b)
		}
		instrument.Brackets = &bs
	}
}

// spot 和 u本位永续 symbol 重名，需要加 instrumentType
func getInstrumentsCacheKey(instrumentType exchange.InstrumentType, symbol string) string {
	return fmt.Sprintf("%s_%s", string(instrumentType), symbol)
}

func (this *Binance) getInstrument(instrumentType exchange.InstrumentType, symbol string) (instrument *Instrument) {
	instrument, _ = this.instruments.Load(getInstrumentsCacheKey(instrumentType, symbol))
	if instrument == nil || instrument.IsExpired() {
		if instrumentType == exchange.CoinMarginedFutures {
			this.cFuturesCacheLatestInstruments()
		} else if instrumentType == exchange.USDXMarginedFutures {
			this.uFuturesCacheLatestInstruments()
		} else if instrumentType == exchange.Spot {
			this.spotCacheLatestInstruments()
		}

		instrument, _ = this.instruments.Load(getInstrumentsCacheKey(instrumentType, symbol))
	}

	if instrument != nil && instrument.Brackets == nil && instrumentType == exchange.USDXMarginedFutures {
		this.getBracket(instrument)
		this.instruments.Store(getInstrumentsCacheKey(instrumentType, symbol), instrument)
		instrument.MaxLeverage = 100
		if strings.Contains(instrument.Symbol, "BTCUSDT") {
			instrument.MaxLeverage = 125
		}
	}

	return instrument
}

type klineData struct {
	Open  float64
	High  float64
	Low   float64
	Close float64
	Time  int64
}

func (this *Binance) getKlines(symbol string, endAt int64) (klines []klineData, er error) {
	if endAt <= 0 {
		endAt = time.Now().Unix()
		endAt = endAt / 3600 * 3600
	}
	startAt := endAt - 3600*499
	params := map[string]string{
		"symbol":    symbol,
		"interval":  "1h",
		"startTime": cast.ToString(startAt * 1e3),
		"endTime":   cast.ToString(endAt * 1e3),
	}
	ret, err := this.do("/fapi/v1/klines", params)
	if err != nil {
		er = fmt.Errorf("get klines error, %v", err)
		return
	}

	for _, out := range ret.Array() {
		this := out.Array()
		var k klineData
		k.Time = this[0].Int() / 1e3
		k.Open = this[1].Float()
		k.High = this[2].Float()
		k.Low = this[3].Float()
		k.Close = this[4].Float()
		klines = append(klines, k)
	}
	return
}

func (this *Binance) getHistory(symbol string, periodHour, num int) (klines []klineData, er error) {
	maxCount := (periodHour*num)/500. + 1

	var endAt int64
	for i := 0; i <= maxCount; i++ {
		ks, err := this.getKlines(symbol, endAt)
		if err != nil {
			er = err
			return
		}
		klines = append(klines, ks...)

		// 选取最小的为EndAt
		for _, k := range ks {
			if k.Time < endAt || endAt <= 0 {
				endAt = k.Time
			}
		}
		endAt -= 3600
	}
	sort.SliceStable(klines, func(i, j int) bool {
		return klines[i].Time < klines[j].Time
	})
	if len(klines) == 0 {
		er = errors.New("get kline history error, empty result")
	}
	return
}

func (this *Binance) getPosition(symbol string, side exchange.PositionSide) (pos *exchange.Position, err error) {
	positions, err := this.uFuturesGetPositions(symbol)
	if err != nil {
		return nil, err
	}
	for _, position := range positions {
		if side != "" && side != position.Side {
			continue
		}
		return position, nil
	}
	p := exchange.Position{}
	p.ExchangeName = exchange.Binance
	p.Symbol = symbol
	return &p, nil
}

func (this *Binance) setPositionMargin(symbol string, side string, amount float64) error {
	// type	调整方向 1: 增加逐仓保证金，2: 减少逐仓保证金
	typ := "1"
	if amount < 0 {
		typ = "2"
	}

	params := map[string]string{
		"symbol":       symbol,
		"positionSide": side,
		"type":         typ,
		"amount":       fmt.Sprintf("%0.8f", math.Abs(amount)),
	}
	_, err := this.do("/fapi/v1/positionMargin", params, "POST")
	return err
}

func (this *Binance) isOrderCanceled(symbol string, orderID string) bool {
	orderIDs, ok := this.canceledOrderIDs.Load(symbol)
	if !ok {
		return false
	}

	for _, canceledOrderID := range orderIDs {
		if canceledOrderID == orderID {
			return true
		}
	}

	return false
}

func (this *Binance) recordCanceledOrderID(symbol string, orderID string) {
	ids, _ := this.canceledOrderIDs.LoadOrStore(symbol, []string{})
	ids = append(ids, orderID)
	if len(ids) > MAX_CANCELED_ORDERID_NUM {
		ids = ids[1:]
	}
	this.canceledOrderIDs.Store(symbol, ids)
}

// 首先调整杠杆率，然后转移保证金
func (this *Binance) SetLeverageToMax(instrumentType exchange.InstrumentType, symbol string, currentLeverage float64, positionSide exchange.PositionSide) bool {
	pos, err := this.getPosition(symbol, positionSide)
	if err != nil {
		this.Errorf("(%s) get position error: %v", symbol, err)
		return false
	}

	value := pos.EntryPrice * math.Abs(pos.Qty)
	bracket := this.getInstrument(instrumentType, symbol).Brackets.Get(value)
	this.Infof("(%s) value: %0.2f, set leverage to: %v", symbol, value, bracket.InitialLeverage)
	if currentLeverage < bracket.InitialLeverage {
		if err := this.uFuturesSetLeverage(symbol, bracket.InitialLeverage); err != nil {
			this.Errorf("(%s) set leverage to max error: %v", symbol, err)
			return false
		}
	}

	maintMargin := bracket.MaintMarginRatio*pos.MarkPrice*math.Abs(pos.Qty) + bracket.Cum
	deltaMargin := math.Min(pos.Margin-maintMargin, pos.Margin)

	side := "LONG"
	if pos.Qty < 0 {
		side = "SHORT"
	}
	var err2 error
	for i := 0; i < 3; i++ {
		amount := deltaMargin * math.Pow(0.98, float64(i))
		if math.Round(amount*100)/100 != 0 {
			err2 = this.setPositionMargin(symbol, side, -amount)
			if err2 == nil {
				break
			}
			time.Sleep(time.Second)
		}
	}

	if err2 != nil {
		this.Errorf("(%s) adjust marigin error: %0.2f, %v", symbol, pos.Margin, err2)
	}
	return true
}

func (this *Binance) CalculateLeverage(pos *exchange.Position) (float64, error) {
	per := math.Abs(pos.EntryPrice-pos.LiquidationPrice) / pos.EntryPrice
	return math.Ceil(1 / per), nil
}

// 先调整杠杆率然后划转保证金
// 保证金跟杠杆率的关系参考： https://www.binance.com/cn/support/faq/b3c689c1f50a44cabb3a84e663b81d93
// 逐仓在有持仓情况下，不可以调低杠杆，会返回错误: Leverage reduction is not supported in Isolated Margin Mode with open positions.
// 杠杠只决定了当前持仓需要的最少保证金，爆仓价由保证金的多少决定，所以理论上可以直接用最大杠杠
func (this *Binance) AdjustLiquidationPrice(instrumentType exchange.InstrumentType, symbol string, pos *exchange.Position, targetLiquidationPrice float64, acceptableDelta float64) (deltaPrice float64, errMsg string) {
	value := pos.EntryPrice * pos.Qty
	bracket := this.getInstrument(instrumentType, symbol).Brackets.Get(value)

	newMargin := targetLiquidationPrice*((bracket.MaintMarginRatio*math.Abs(pos.Qty))-pos.Qty) - bracket.Cum + (pos.Qty * pos.EntryPrice)
	deltaMargin := newMargin - pos.Margin

	deltaPrice = math.Abs(targetLiquidationPrice - pos.LiquidationPrice)
	this.Infof("(%s)  爆仓价待调整: %.2f => %.2f, 价差: %.2f, 目标价差: %.2f", symbol, pos.LiquidationPrice, targetLiquidationPrice, deltaPrice, acceptableDelta)
	this.Infof("(%s)  保证金需调整: %.2f", symbol, deltaMargin)

	side := "LONG"
	if pos.Qty < 0 {
		side = "SHORT"
	}

	// 先尝试在当前杠杠调整保证金，如果失败，则 杠杠 + 5 之后再次尝试直到最大杠杠率
	targetLeverage := pos.Leverage
	var err2 error
	for {
		err2 = this.setPositionMargin(symbol, side, deltaMargin)
		if err2 == nil {
			break
		}

		if targetLeverage >= bracket.InitialLeverage {
			break
		}

		targetLeverage += 5
		if targetLeverage > bracket.InitialLeverage {
			targetLeverage = bracket.InitialLeverage
		}

		if err := this.uFuturesSetLeverage(symbol, targetLeverage); err != nil {
			return deltaPrice, fmt.Sprintf("设置杠杆[%0.1f]失败", targetLeverage)
		}

		time.Sleep(time.Second)
	}
	if err2 != nil {
		this.Errorf("(%s) adjust marigin error: %0.2f, %v", symbol, deltaMargin, err2)
		return deltaPrice, fmt.Sprintf("调整保证金失败: %s", err2)
	}

	si := exchange.PositionSideLong
	if pos.Qty < 0 {
		si = exchange.PositionSideShort
	}

	pos, _ = this.getPosition(symbol, si)
	if pos == nil {
		return deltaPrice, "获取当前持仓失败"
	}

	deltaPrice = math.Abs(targetLiquidationPrice - pos.LiquidationPrice)

	if deltaPrice <= acceptableDelta {
		this.Infof("(%s) 调整爆仓价成功", symbol)
		return deltaPrice, ""
	}
	if targetLeverage >= bracket.InitialLeverage {
		this.Infof("(%s) 杠杠率已达上限", symbol)
		return deltaPrice, ""
	}

	return deltaPrice, fmt.Sprintf("调整爆仓价失败, 目标价: %.2f, 当前价: %.2f, 当前杠杠率: %.2f", targetLiquidationPrice, pos.LiquidationPrice, targetLeverage)
}

func (this *Binance) listenKey(instrumentType exchange.InstrumentType) (key string, err error) {
	url := "/api/v3/userDataStream"
	if instrumentType == exchange.USDXMarginedFutures {
		url = "/fapi/v1/listenKey"
	}
	ret, err := this.do(url, nil, "POST")
	if err != nil {
		return "", err
	}
	return ret.Get("listenKey").String(), nil
}

func (this *Binance) prolongListenKey(instrumentType exchange.InstrumentType, listenKey string) (err error) {
	url := "/api/v3/userDataStream"
	params := map[string]string{"listenKey": listenKey}
	if instrumentType == exchange.USDXMarginedFutures {
		url = "/fapi/v1/listenKey"
		params = nil
	}
	_, err = this.do(url, params, "PUT")
	return
}

func (this *Binance) subscribeSymbols(instrumentType exchange.InstrumentType) {
	ws, _ := this.wsConns.Load(string(instrumentType))
	if ws == nil {
		return
	}

	if !this.EnableRealtimePrice {
		return
	}

	subscribedSymbols, _ := this.subscribedSymbols.LoadOrStore(string(instrumentType), []string{})

	symbolsNeed := []string{}
	for _, priceTrigger := range this.PriceTriggers {
		if priceTrigger.InstrumentType == instrumentType {
			symbolsNeed = append(symbolsNeed, priceTrigger.Symbol)
		}
	}

	for _, watch := range this.PriceWatches {
		if watch.InstrumentType == instrumentType {
			symbolsNeed = append(symbolsNeed, watch.Symbol)
		}
	}

	for _, symbol := range symbolsNeed {
		if !exchange.SliceContains(subscribedSymbols, symbol) {
			msg := fmt.Sprintf(
				`{"method": "SUBSCRIBE","params":["%s@miniTicker"],"id": %d}`,
				strings.ToLower(symbol), // 必须小写才能订阅成功
				time.Now().Unix(),
			)
			if err := ws.WriteMessage(websocket.TextMessage, []byte(msg)); err != nil {
				this.Errorf("[%s] subscribe msg error: %s", err)
			} else {
				subscribedSymbols = append(subscribedSymbols, symbol)
				this.subscribedSymbols.Store(string(instrumentType), subscribedSymbols)
			}
		}
	}
}

func (this *Binance) websocket(instrumentType exchange.InstrumentType) error {
	key, err := this.listenKey(instrumentType)
	if err != nil {
		return err
	}
	err = this.prolongListenKey(instrumentType, key)
	if err != nil {
		return err
	}

	this.subscribedSymbols.Store(string(instrumentType), []string{})

	listenKeyticker := time.NewTicker(time.Minute * 10)
	defer listenKeyticker.Stop()

	subscribeTicker := time.NewTicker(time.Second * 10)
	defer subscribeTicker.Stop()

	go func() {
		for {
			select {
			case <-listenKeyticker.C:
				this.prolongListenKey(instrumentType, key)
			case <-subscribeTicker.C:
				this.subscribeSymbols(instrumentType)
			}
		}
	}()

	wsurl := "wss://stream.binance.com:9443/ws/" + key
	if instrumentType == exchange.USDXMarginedFutures {
		wsurl = "wss://fstream.binance.com/ws/" + key
		if this.IsTestnet {
			wsurl = "wss://testnet.binancefuture.com/ws/" + key
		}
	}
	conn, _, err := websocket.DefaultDialer.Dial(wsurl, nil)
	if err != nil {
		return err
	}
	defer conn.Close()
	this.wsConns.Store(string(instrumentType), conn)

	for {
		conn.SetReadDeadline(time.Now().Add(time.Minute * 30)) // 半小时没有消息重启
		_, msg, err := conn.ReadMessage()
		if err != nil {
			return err
		}

		// this.Debugf("binance websocket recv: %s", msg)

		ret := gjson.ParseBytes(msg)
		event := ret.Get("e").String()

		if strings.EqualFold(event, "24hrMiniTicker") {
			ticker := &exchange.Ticker{InstrumentType: instrumentType}
			json.Unmarshal(msg, ticker)
			this.handleTicker(ticker)
		}

		if instrumentType == exchange.Spot && strings.EqualFold(event, "balanceUpdate") {
			asset := ret.Get("a").String()
			if asset != "" {
				margin, err := this.GetUserMargin(exchange.Spot, asset)
				if err == nil {
					this.MarginUpdatedCallback(margin, asset)
				}
			}
		} else if instrumentType == exchange.Spot && strings.EqualFold(event, "executionReport") {
			typ := ret.Get("x").String()
			if typ == "TRADE" {
				symbol := ret.Get("s").String()
				// TODO 完善订单字段
				this.OrderUpdatedCallback(&exchange.Order{
					InstrumentType: instrumentType,
					Symbol:         symbol,
				})
			}
		}

		if instrumentType == exchange.USDXMarginedFutures && strings.EqualFold(event, "ORDER_TRADE_UPDATE") {
			typ := ret.Get("o.x").String()
			if typ == "TRADE" {
				exOrder := &exchange.Order{
					InstrumentType: instrumentType,
					Symbol:         ret.Get("o.s").String(),
					OrderID:        ret.Get("o.i").String(),
					Price:          ret.Get("o.p").Float(),
					Qty:            ret.Get("o.q").Float(),
					ExecQty:        ret.Get("o.z").Float(),
				}
				if ret.Get("o.S").String() == "SELL" {
					exOrder.Side = exchange.OrderSideSell
				} else {
					exOrder.Side = exchange.OrderSideBuy
				}
				switch ret.Get("o.X").String() {
				case "NEW":
					exOrder.Status = exchange.OrderStatusNew
				case "PARTIALLY_FILLED":
					exOrder.Status = exchange.OrderStatusPartialFilled
				case "FILLED":
					exOrder.Status = exchange.OrderStatusFilled
				case "CANCELED":
					exOrder.Status = exchange.OrderStatusCancelled
				default:
					exOrder.Status = exchange.UnknownOrderStatus
				}

				this.OrderUpdatedCallback(exOrder)
			}
		} else if instrumentType == exchange.USDXMarginedFutures && strings.EqualFold(event, "ACCOUNT_UPDATE") {
			bls := ret.Get("a.B").Array()
			symbol := ""
			if len(bls) > 0 {
				symbol = bls[0].Get("a").String()
			}
			// balance := ret.Get("a.B.wb").Float()
			if symbol == "USDT" {
				// margin := &exchange.UserMargin{}
				// margin.Currency = symbol
				// margin.WalletBalance = balance
				// margin.AvailableMargin = ret.Get("a.B.cw").Float()
				// 此处没有保证金余额
				margin, err := this.GetUserMargin(exchange.USDXMarginedFutures, symbol)
				if err == nil {
					this.MarginUpdatedCallback(margin, symbol)
				}
			}
		}
	}
}

func (this *Binance) handleTicker(ticker *exchange.Ticker) {
	if ticker.Close == 0 {
		return
	}
	this.StoreTickerCache(ticker)
	this.CheckPriceTrigger(ticker.InstrumentType, ticker.Symbol, ticker.Close, time.Unix(ticker.Time/1000, 0))
}

func (this *Binance) ConnectWebsocket(instrumentTypes []exchange.InstrumentType, connectedCallback func(connected bool)) {
	this.wsConns = xsync.NewMapOf[*websocket.Conn]()
	this.wsBackOffs = xsync.NewMapOf[*backoff.ExponentialBackOff]()
	this.subscribedSymbols = xsync.NewMapOf[[]string]()
	for _, instrumentType := range instrumentTypes {
		go func(instrumentType exchange.InstrumentType) {
			exp := backoff.NewExponentialBackOff()
			exp.InitialInterval = time.Second
			// 此处一定要设置为0，才能保持一直重试
			exp.MaxElapsedTime = 0
			this.wsBackOffs.Store(string(instrumentType), exp)
			backoff.Retry(func() error {
				this.Infof("binance %s websocket init", instrumentType)
				err := this.websocket(instrumentType)
				if err != nil {
					this.Errorf("[%s] binance websocket error: %v", this.ControllerID, err)
				}
				return err
			}, exp)
		}(instrumentType)
	}
}

func (this *Binance) CloseWebsocket(stop bool) {
	this.wsConns.Range(func(instrumentType string, wsConn *websocket.Conn) bool {
		if wsConn != nil {
			if bf, ok := this.wsBackOffs.Load(instrumentType); ok {
				bf.MaxElapsedTime = 1
			}

			wsConn.Close()
		}
		return true
	})
}

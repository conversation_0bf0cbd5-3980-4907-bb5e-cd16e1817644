package exchange

import (
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/shopspring/decimal"
	"github.com/wizhodl/quanter/common/extstruct"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"

	"go.uber.org/zap"
)

const BitMEX string = "BitMEX"
const Binance string = "Binance"
const OKEx string = "OKEx"
const CTP string = "CTP"
const MetaTrader string = "MetaTrader"
const InteractiveBrokers string = "InteractiveBrokers"
const Hyperliquid string = "Hyperliquid"
const Bybit string = "Bybit"

const ISOTimeFormat = "2006-01-02 15:04:05"

type Options struct {
	Host                   string               // 服务器地址
	ApiKey                 string               // api key
	ApiSecret              secrets.SecretString // api secret
	WithdrawApiKey         string               // 主账号 API，用于提现、充值、子账号划转等处理
	WithdrawApiSecret      secrets.SecretString
	FixerKey               string  // fixer key
	IsTestnet              bool    // 是否使用测试网络
	ProxyUrl               string  // 代理 URL
	MinLeverage            float64 // 最小杠杠率
	OrderUpdatedCallback   func(*Order)
	MarginUpdatedCallback  func(*UserMargin, string)
	OrderBookCallback      func(orderBook *OrderBook)
	PriceTriggeredCallback func(*PriceTrigger)
	// 从 gateway 收到 SlackResponse 后的回调函数
	SlackResponseCallback func(msg string, fileTitle string, fileContent []byte, fileType string)
	Logger                *zap.SugaredLogger
	ControllerID          string
	GatewayChannelName    string
	DataPath              string
}

// 保证金余额
type UserMargin struct {
	WalletBalance   float64 // 钱包余额，不包含当前持仓中的浮盈
	MarginBalance   float64 // 保证金余额，包含当前持仓中的浮盈
	AvailableMargin float64 // 可用余额
	Currency        string
}

// K 线数据结构
type KLine struct {
	Open  float64
	Close float64
	High  float64
	Low   float64
	Time  int64 // 时间戳，单位秒
}

type KLines []*KLine

// K线是否过期：K线中最新一根K线的时间 + peroidHour > nowTime
func (this KLines) Expired(periodHour int) bool {
	latestKlineTime := time.Time{}
	for _, line := range this {
		lineTime := time.Unix(line.Time, 0)
		if lineTime.After(latestKlineTime) {
			latestKlineTime = lineTime
		}
	}
	return time.Since(latestKlineTime) > time.Hour*time.Duration(periodHour)
}

func (this KLines) Update(newLines KLines) (updatedLines KLines) {
	if len(this) == 0 {
		this = newLines
		updatedLines = newLines
		return
	} else {
		appendCount := 0
		for _, newline := range newLines {
			foundIndex := -1
			for i, oldline := range this {
				if oldline.Time == newline.Time {
					foundIndex = i
					break
				}
			}
			if foundIndex > -1 {
				this[foundIndex] = newline
			} else {
				this = append(this, newline)
				appendCount += 1
			}
		}
		updatedLines = this[appendCount:]
		return
	}
}

func (this KLines) ToTable(limit int) string {
	sort.SliceStable(this, func(i, j int) bool {
		return this[i].Time < this[j].Time
	})
	count := len(this)
	t := NewTable()
	t.SetHeader([]string{"Open", "Close", "High", "Low", "Time"})
	if limit == 0 {
		limit = count
	}
	for i := 0; i < limit; i++ {
		if count-1-i < 0 {
			break
		}
		k := this[count-1-i]
		kt := time.Unix(k.Time, 0)
		t.AddRow([]string{
			fmt.Sprintf("%v", k.Open),
			fmt.Sprintf("%v", k.Close),
			fmt.Sprintf("%v", k.High),
			fmt.Sprintf("%v", k.Low),
			FormatShortTimeStr(&kt, true),
		})
	}
	if len(t.Rows) > 1 {
		return t.Render()
	}
	return ""
}

type InstrumentType string

const (
	UnknownInstrumentType InstrumentType = "" // 空值默认对应的 instrumentType
	Spot                  InstrumentType = "Spot"
	CoinMarginedFutures   InstrumentType = "CoinMarginedFutures"
	USDXMarginedFutures   InstrumentType = "USDXMarginedFutures"
)

func (this InstrumentType) IsFuture() bool {
	return strings.Contains(string(this), "Futures")
}

func (this InstrumentType) GetSuffix() string {
	if this == USDXMarginedFutures {
		return ".U"
	}
	return ""
}

type ContractType string

const (
	ContractTypeSwap        ContractType = "Swap"
	ContractTypeMonth       ContractType = "Month"
	ContractTypeQuarter     ContractType = "Quarter"
	ContractTypeNextQuarter ContractType = "NextQuarter"
	ContractTypeWeek        ContractType = "Week"
	ContractTypeNextWeek    ContractType = "NextWeek"
)

type InstrumentStatus string

const (
	UnknownInstrumentStatus         InstrumentStatus = "Unknown"
	InstrumentStatusBeforeTrading   InstrumentStatus = "Before"
	InstrumentStatusNoTrading       InstrumentStatus = "NoTrading"
	InstrumentStatusContinuous      InstrumentStatus = "Continuous"
	InstrumentStatusAuctionOrdering InstrumentStatus = "AuctionOrdering"
	InstrumentStatusAuctionBalance  InstrumentStatus = "AuctionBalance"
	InstrumentStatusAuctionMatch    InstrumentStatus = "AuctionMatch"
	InstrumentStatusClosed          InstrumentStatus = "Closed"
	InstrumentStatusExpiredSoon     InstrumentStatus = "ExpiredSoon"
)

type Instrument struct {
	extstruct.ExtStruct
	ExchangeID               string // 交易所名称，CTP 需要
	InstrumentType           InstrumentType
	Symbol                   string
	MinNotional              float64          // 现货最小成交金额
	TickSize                 float64          // 最小价格精度
	LotSize                  float64          // 下单数量精度
	MinSize                  float64          // 最小下单数量，是 LotSize 的整数倍
	ContractSize             float64          // 合约面值
	MarkPrice                float64          // 标记价格
	IndexPrice               float64          // 指数价格
	LastPrice                float64          // 最新价格
	LastPriceUpdateTime      time.Time        // 最新价格更新时间
	UpperLimitPrice          float64          // 涨停价
	LowerLimitPrice          float64          // 跌停价
	LongMarginRatioByMoney   float64          // 多头保证金率
	LongMarginRatioByVolume  float64          // 多头保证金费
	ShortMarginRatioByMoney  float64          // 空头保证金率
	ShortMarginRatioByVolume float64          // 空头保证金费
	FundingRate              float64          // 当前资金费用
	FundingRatePeriod        string           // 资金费用周期
	FundingTime              time.Time        // 资金费用有效期
	MaxLeverage              float64          // 最大杠杠率
	SettleCurrency           string           // 结算币种
	UnderlyCurrency          string           // 基础币种
	QuoteCurrency            string           // 计价币种
	QuoteToSettleRate        float64          // 计价币对结算币汇率
	ContractType             ContractType     // 合约类型
	Status                   InstrumentStatus // 产品状态
	Volume                   float64          // 成交量
	OpenInterest             float64          // 持仓量
	UpdateTime               time.Time        // 更新时间戳
}

// 获取年化资金费用率
func (this *Instrument) GetFundingRateAPR(isLong bool) float64 {
	periodHours := PeriodHours(this.FundingRatePeriod)
	if periodHours == 0 {
		return 0
	}
	rate := this.FundingRate / float64(periodHours) * 24 * 365
	if isLong {
		return -rate
	} else {
		return rate
	}
}

func (this *Instrument) RoundPrice(price float64) float64 {
	priceDec := decimal.NewFromFloat(price)
	tickSize := decimal.NewFromFloat(this.TickSize)
	priceDec = priceDec.Div(tickSize).Round(0).Mul(tickSize)
	price, _ = priceDec.Float64()
	return price
}

func (this *Instrument) FloorPrice(price float64) float64 {
	priceDec := decimal.NewFromFloat(price)
	tickSize := decimal.NewFromFloat(this.TickSize)
	priceDec = priceDec.Div(tickSize).Floor().Mul(tickSize)
	price, _ = priceDec.Float64()
	return price
}

func (this *Instrument) RoundQty(qty float64) float64 {
	qtyDec := decimal.NewFromFloat(qty)
	lotSize := decimal.NewFromFloat(this.LotSize)
	qtyDec = qtyDec.Div(lotSize).Round(0).Mul(lotSize)
	qty, _ = qtyDec.Float64()
	return qty
}

func (this *Instrument) FloorQty(qty float64) float64 {
	qtyDec := decimal.NewFromFloat(qty)
	lotSize := decimal.NewFromFloat(this.LotSize)
	qtyDec = qtyDec.Div(lotSize).Floor().Mul(lotSize)
	qty, _ = qtyDec.Float64()
	return qty
}

type OrderSide string

const (
	UnknownOrderSide OrderSide = "" // 空值对应的 OrderSide
	OrderSideBuy     OrderSide = "Buy"
	OrderSideSell    OrderSide = "Sell"
)

func (o OrderSide) OppositeSide() OrderSide {
	if o == OrderSideBuy {
		return OrderSideSell
	} else if o == OrderSideSell {
		return OrderSideBuy
	} else {
		return UnknownOrderSide
	}
}

func (o OrderSide) EqualsPositionSide(positionSide PositionSide) bool {
	if o == OrderSideBuy && positionSide == PositionSideLong {
		return true
	} else if o == OrderSideSell && positionSide == PositionSideShort {
		return true
	} else {
		return false
	}
}

type OrderType string

const (
	UnknownOrderType OrderType = ""
	Limit            OrderType = "Limit"
	Market           OrderType = "Market"
	StopLimit        OrderType = "StopLimit"
	StopMarket       OrderType = "StopMarket"
)

type TimeInForce string

const (
	// GTC TODO: 检查设为空字符是否能帮助减少存储
	UnknownTimeInForce TimeInForce = ""
	GTC                TimeInForce = "GTC" // GoodTillCancel, default, 为了减少数据存储，设为空字符
	IOC                TimeInForce = "IOC" // ImmediateOrCancel
	GTD                TimeInForce = "GTD" // GoodTillDay
	GTE                TimeInForce = "GTE" // GoodTillExpire
)

type ExecInstruction string

type TradeMode string

const (
	TradeModeIsolated TradeMode = "Isolated"
	TradeModeCross    TradeMode = "Cross"
	TradeModeCash     TradeMode = "Cash"
)

type TriggerPriceType string

const (
	TriggerPriceTypeLast  TriggerPriceType = "last"
	TriggerPriceTypeMark  TriggerPriceType = "mark"
	TriggerPriceTypeIndex TriggerPriceType = "index"
)

type TriggerDirection string

const (
	TriggerDirectionHigher TriggerDirection = "Higher"
	TriggerDirectionLower  TriggerDirection = "Lower"
)

type CreateOrderArgs struct {
	extstruct.ExtStruct
	InstrumentType   InstrumentType
	Symbol           string
	Side             OrderSide
	Type             OrderType
	TradeMode        TradeMode
	Price            float64
	TriggerPrice     float64
	TriggerDirection TriggerDirection
	TriggerPriceType TriggerPriceType
	Qty              float64
	QuoteQty         float64
	TimeInForce      TimeInForce
	ReduceOnly       bool
	ClosePosition    bool // 双向持仓时合约平仓用，目前仅 OKEx 使用
}

// 修改订单参数
type UpdateOrderArgs struct {
	InstrumentType   InstrumentType
	Price            float64 // 委托价
	TriggerPrice     float64 // 触发价，已触发的订单不传
	OrderQty         float64 // 数量，平仓时不传
	Type             OrderType
	ReduceOnly       bool
	ClosePosition    bool
	TriggerPriceType TriggerPriceType
}

type TimeRange struct {
	StartTime int64
	EndTime   int64
}

type OrderStatus string

const (
	UnknownOrderStatus          OrderStatus = ""
	OrderStatusNew              OrderStatus = "New"
	OrderStatusRejected         OrderStatus = "Rejected"
	OrderStatusPartialCancelled OrderStatus = "PartialCancelled"
	OrderStatusPartialFilled    OrderStatus = "PartialFilled"
	OrderStatusTriggered        OrderStatus = "Triggered" // 已触发但无成交
	OrderStatusFilled           OrderStatus = "Filled"
	OrderStatusCancelled        OrderStatus = "Cancelled"
)

const ExtKeyTriggeredLimitOrderID string = "TriggeredLimitOrderID"

type Order struct {
	extstruct.ExtStruct
	InstrumentType   InstrumentType   `json:",omitempty"`
	OrderID          string           `json:",omitempty"`
	RefID            string           `json:",omitempty"` // 应用端的唯一参考号，即 ClientOrderID
	Symbol           string           `json:",omitempty"`
	Price            float64          `json:",omitempty"`
	TriggerPrice     float64          `json:",omitempty"`
	TriggerDirection TriggerDirection `json:",omitempty"`
	TriggeredOrderID string           `json:",omitempty"` // 触发后交易所返回的订单 ID
	LastPrice        float64          `json:",omitempty"` // TODO: 检查有什么用
	Qty              float64          `json:",omitempty"`
	QuoteQty         float64          `json:",omitempty"`
	ExecPrice        float64          `json:",omitempty"`
	ExecQty          float64          `json:",omitempty"`
	Fee              float64          `json:",omitempty"`
	FeeAsset         string           `json:",omitempty"`
	Type             OrderType        `json:",omitempty"`
	TradeMode        TradeMode        `json:",omitempty"`
	Side             OrderSide        `json:",omitempty"`
	Status           OrderStatus      `json:",omitempty"`
	TimeInForce      TimeInForce      `json:",omitempty"`
	ReduceOnly       bool             `json:",omitempty"` // 平仓单为 true
	ClosePosition    bool             `json:",omitempty"` // 双向持仓时合约平仓用，目前仅 OKEx 使用
	CreateTime       *time.Time       `json:",omitempty"`
	UpdateTime       *time.Time       `json:",omitempty"`
	Trades           []TradeHistory   `json:",omitempty"`
	Comment          string           `json:",omitempty"` // 备注
}

var StatusValidChanges [][]OrderStatus = [][]OrderStatus{
	{UnknownOrderStatus, OrderStatusNew},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusRejected},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusTriggered},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusTriggered, OrderStatusFilled},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusTriggered, OrderStatusPartialFilled},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusTriggered, OrderStatusPartialFilled, OrderStatusFilled},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusTriggered, OrderStatusPartialFilled, OrderStatusPartialCancelled},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusTriggered, OrderStatusCancelled},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusTriggered, OrderStatusPartialCancelled},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusFilled},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusPartialFilled},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusPartialFilled, OrderStatusFilled},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusPartialFilled, OrderStatusPartialCancelled},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusCancelled},
	{UnknownOrderStatus, OrderStatusNew, OrderStatusPartialCancelled},
}

func (this *Order) ChangeStatus(newStatus OrderStatus) (er error) {
	valid := false
	validRows := [][]OrderStatus{}
	for _, row := range StatusValidChanges {
		// 如果 row 的最终状态是 newStatus
		if row[len(row)-1] == newStatus {
			validRows = append(validRows, row)
		}
	}
	for _, vRow := range validRows {
		if SliceContains(vRow, this.Status) {
			valid = true
			break
		}
	}
	if !valid {
		er = fmt.Errorf("can not change from status (%s) to status (%s)", this.Status, newStatus)
		return
	}
	zlog.Infof("order status change of (%s): (%s) -> (%s)", this.OrderID, this.Status, newStatus)
	this.Status = newStatus
	return
}

func (o *Order) IsFuture() bool {
	return o.InstrumentType.IsFuture()
}

func (o *Order) IsOpen() bool {
	return o.Status == OrderStatusNew || o.Status == OrderStatusPartialFilled || o.Status == OrderStatusTriggered
}

func (o *Order) IsTriggered() bool {
	return o.Status == OrderStatusFilled || o.Status == OrderStatusPartialCancelled || o.Status == OrderStatusPartialFilled || o.Status == OrderStatusTriggered
}

func (o *Order) IsFilled() bool {
	return o.Status == OrderStatusFilled || o.Status == OrderStatusPartialCancelled
}

func (o *Order) IsCanceled() bool {
	return o.Status == OrderStatusCancelled || o.Status == OrderStatusPartialCancelled || o.Status == OrderStatusRejected
}

func (o *Order) IsCloseOrder() bool {
	return o.ReduceOnly
}

// 根据仓位判断是否是止盈单
func (o *Order) IsTakeProfitOrder(position *Position) bool {
	if !o.IsCloseOrder() {
		return false
	}
	positionSide := position.Side
	positionPrice := position.EntryPrice
	orderPrice := o.TriggerPrice
	return (positionSide == PositionSideLong && orderPrice > positionPrice) || (positionSide == PositionSideShort && orderPrice < positionPrice)
}

type OrderRecord struct {
	Order        *Order
	SymbolCode   *SymbolCode
	ControllerID string
	ExchangeName string
}

// 由 order controller 实现的 ConditionalOrder 接口
type ConditionalOrder struct {
	Order         *Order // 本地维护的订单
	ExchangeOrder *Order // 触发后发送到交易所的订单
	PriceTrigger  *PriceTrigger
	ControllerID  string
	ExchangeName  string
	CreateTime    *time.Time // 写入历史记录文件前，设置 CreateTime，方便清理历史记录
}

type TradeHistory struct {
	Price    float64
	Qty      float64
	Fee      float64 `json:",omitempty"`
	FeeAsset string  `json:",omitempty"`
	TradeAt  *time.Time
}

type AccountMarginMode string

const (
	UnknownAccountMarginMode   AccountMarginMode = ""
	AccountMarginModeSimple    AccountMarginMode = "Simple"
	AccountMarginModeIsolated  AccountMarginMode = "Isolated" // 逐仓/单币种模式
	AccountMarginModeCross     AccountMarginMode = "Cross"
	AccountMarginModePortfolio AccountMarginMode = "Portfolio"
)

func (a AccountMarginMode) IsCross() bool {
	return a == AccountMarginModeCross || a == AccountMarginModePortfolio
}

type AccountConfig struct {
	UserID              string
	AccountBalanceCross bool              // 现货、期货的账户余额是不是同一个，无法区分
	MarginMode          AccountMarginMode // 账户层级 1：简单交易模式，2：单币种保证金模式，3：跨币种保证金模式
	DualPositionSide    bool              // 是否双向持仓模式
}

type AccountBalance struct {
	InstrumentType InstrumentType `json:",omitempty"`
	Currency       string
	Total          float64 // 账户余额
	Available      float64 // 可用余额
	PriceInUSDT    float64
	WorthInUSDT    float64
}

type AccountBalanceList []*AccountBalance

func (this AccountBalanceList) Render() string {
	result := "[No Balances]"
	t := NewTable()
	t.SetHeader([]string{"InstrumentType", "Currency", "Total", "Available"})
	for _, b := range this {
		t.AddRow([]string{string(b.InstrumentType), b.Currency, utils.FormatNum(b.Total, 6), utils.FormatNum(b.Available, 6)})
	}
	if len(t.Rows) > 1 {
		result = t.Render()
	}
	return result
}

func (this AccountBalanceList) RenderTotalCombined() string {
	result := "[No Balances]"
	t := NewTable()
	t.SetHeader([]string{"Currency", "Total", "Price", "Worth"})
	totalWorth := 0.0
	for currency, total := range this.GetTotalCombined() {
		t.AddRow([]string{
			currency,
			utils.FormatNum(total.Total, 6),
			utils.FormatNum(total.PriceInUSDT, 4),
			utils.FormatNum(total.WorthInUSDT, 2)},
		)
		totalWorth += total.WorthInUSDT
	}
	if totalWorth > 0 {
		t.AddRow([]string{"Total", "", "", utils.FormatNum(totalWorth, 2)})
	}
	if len(t.Rows) > 1 {
		result = t.Render()
	}
	return result
}

func (this AccountBalanceList) GetTotalCombined() (assetMap map[string]*AccountBalance) {
	assetMap = map[string]*AccountBalance{}
	for _, b := range this {
		if _, found := assetMap[b.Currency]; !found {
			assetMap[b.Currency] = &AccountBalance{
				Currency:    b.Currency,
				Total:       b.Total,
				Available:   b.Available,
				PriceInUSDT: b.PriceInUSDT,
				WorthInUSDT: b.WorthInUSDT,
			}
		} else {
			assetMap[b.Currency].Total += b.Total
			assetMap[b.Currency].Available += b.Available
			assetMap[b.Currency].WorthInUSDT += b.WorthInUSDT
		}
	}
	return
}

func (a *AccountBalance) GetLocked() float64 {
	return a.Total - a.Available
}

type HoldingKey struct {
	InstrumentType InstrumentType
	CoinOrSymbol   string
}

func (this HoldingKey) String() string {
	return fmt.Sprintf("%s.%s", this.InstrumentType, this.CoinOrSymbol)
}

func NewSpotHoldingKey(coin string) HoldingKey {
	return HoldingKey{InstrumentType: Spot, CoinOrSymbol: coin}
}

type Holding struct {
	InstrumentType InstrumentType `json:",omitempty"`
	CoinOrSymbol   string
	Total          float64 // 账户余额
	Available      float64 // 可用余额
}

type HoldingList []*Holding
type HoldingMap map[HoldingKey]float64

func (this HoldingList) Render() string {
	result := "[No Balances]"
	t := NewTable()
	t.SetHeader([]string{"InstrumentType", "Coin/Symbol", "Total", "Available"})
	for _, b := range this {
		t.AddRow([]string{string(b.InstrumentType), b.CoinOrSymbol, fmt.Sprintf("%.6f", b.Total), fmt.Sprintf("%.6f", b.Available)})
	}
	if len(t.Rows) > 1 {
		result = t.Render()
	}
	return result
}

func (this HoldingList) RenderTotalCombined() string {
	result := "[No Balances]"
	t := NewTable()
	t.SetHeader([]string{"Coin/Symbol", "Total"})
	for key, total := range this.GetTotalCombined() {
		t.AddRow([]string{key.String(), fmt.Sprintf("%.6f", total)})
	}
	if len(t.Rows) > 1 {
		result = t.Render()
	}
	return result
}

func (this HoldingList) GetTotalCombined() (assetMap HoldingMap) {
	assetMap = HoldingMap{}
	for _, b := range this {
		key := HoldingKey{b.InstrumentType, b.CoinOrSymbol}
		if _, found := assetMap[key]; !found {
			assetMap[key] = b.Total
		} else {
			assetMap[key] += b.Total
		}
	}
	return
}

func (a *Holding) GetLocked() float64 {
	return a.Total - a.Available
}

type PositionSide string

const (
	UnknownPositionSide PositionSide = "" // 空值对应的默认 PositionSide
	PositionSideLong    PositionSide = "Long"
	PositionSideShort   PositionSide = "Short"
)

func (p PositionSide) OppositeSide() PositionSide {
	if p == PositionSideLong {
		return PositionSideShort
	} else if p == PositionSideShort {
		return PositionSideLong
	} else {
		return UnknownPositionSide
	}
}

type Position struct {
	extstruct.ExtStruct
	InstrumentType    InstrumentType `json:",omitempty"`
	ExchangeName      string         `json:",omitempty"`
	Symbol            string         `json:",omitempty"`
	Qty               float64        `json:",omitempty"` // 区分正负
	Available         float64        `json:",omitempty"` // 可用数量
	Side              PositionSide   `json:",omitempty"`
	EntryPrice        float64        `json:",omitempty"`
	MarkPrice         float64        `json:",omitempty"`
	LastPrice         float64        `json:",omitempty"`
	Leverage          float64        `json:",omitempty"`
	LiquidationPrice  float64        `json:",omitempty"`
	UnrealisedPNL     float64        `json:",omitempty"`
	RealisedPNL       float64        `json:",omitempty"`
	Margin            float64        `json:",omitempty"`
	InitialMargin     float64        `json:",omitempty"`
	MaintenanceMargin float64        `json:",omitempty"`
	UpdateTime        *time.Time     `json:",omitempty"`
}

func (p *Position) GetLastPrice() float64 {
	if p.LastPrice == 0 {
		return p.MarkPrice
	}
	return p.LastPrice
}

// 获得当前持仓的盈亏，单位 BTC，返回：已实现盈亏，未实现盈亏，总盈亏
func (p *Position) GetPNL(currency string) (realisedPNL, unrealisedPNL, pnl float64) {
	realisedPNL, unrealisedPNL, pnl = 0, 0, 0
	if strings.EqualFold(p.ExchangeName, BitMEX) {
		digs := 1e8
		if strings.EqualFold(currency, "USDT") {
			digs = 1e6
		}
		realisedPNL = p.RealisedPNL / digs
		unrealisedPNL = p.UnrealisedPNL / digs
		pnl = (p.RealisedPNL + p.UnrealisedPNL) / digs

	} else {
		realisedPNL = p.RealisedPNL
		unrealisedPNL = p.UnrealisedPNL
		pnl = p.RealisedPNL + p.UnrealisedPNL
	}
	return realisedPNL, unrealisedPNL, pnl
}

type FundingHistory struct {
	Code     *SymbolCode
	Exchange string
	Symbol   string
	Rate     float64
	Time     time.Time
	Period   string
}

func (this FundingHistory) PeriodHours() int {
	return PeriodHours(this.Period)
}

// 将 period 转换为小时数，支持 d 和 h
func PeriodHours(period string) int {
	if len(period) == 0 {
		return 0
	}
	period = strings.ToLower(period)
	unit := period[len(period)-1]
	if !utils.SliceContains([]string{"d", "h"}, string(unit)) {
		return 0
	}
	if unit == 'd' {
		periodValue, _ := strconv.Atoi(period[:len(period)-1])
		return periodValue * 24
	} else if unit == 'h' {
		periodValue, _ := strconv.Atoi(period[:len(period)-1])
		return periodValue
	}
	return 0
}

type MarginMode string

const (
	Isolated  MarginMode = "Isolated"
	Cross     MarginMode = "Cross"
	Portfolio MarginMode = "Portfolio"
)

type Float64CacheItem struct {
	Value     float64
	CacheTime time.Time
}

type SpotPriceCacheMap map[string]*Float64CacheItem

type SymbolCode struct {
	Code       string
	USDXSymbol string
}

func (this *SymbolCode) Equals(other *SymbolCode) bool {
	return this.Code == other.Code
}

func (this *SymbolCode) Equals2(other *SymbolCode) bool {
	return this.Code == other.Code && this.USDXSymbol == other.USDXSymbol
}

// 供 UniqueAny 使用
func (this *SymbolCode) Key() string {
	return fmt.Sprintf("%s.%s", this.Code, this.USDXSymbol)
}

type SymbolPair struct {
	extstruct.ExtStruct
	// SpotFuture, Left=Spot, Right=Future
	// FutureFuture, Left=Future, Right=Future
	Left  *SymbolItem
	Right *SymbolItem
}

type SymbolItem struct {
	Code   *SymbolCode
	Symbol string
}

func (this SymbolPair) GetSpotHoldingKey() HoldingKey {
	return HoldingKey{this.Right.Code.InstrumentType(), this.Right.Code.Coin()}
}

func (this SymbolPair) GetFutureHoldingKey() HoldingKey {
	return HoldingKey{this.Right.Code.InstrumentType(), this.Right.Symbol}
}

func (this SymbolPair) IsSpotFuture() bool {
	return this.Left.Code.IsSpot() && this.Right.Code.IsFuture()
}

func (this SymbolPair) IsFutureFuture() bool {
	return this.Left.Code.IsFuture() && this.Right.Code.IsFuture()
}

// 任意类型组合的交易对
func NewCommonSymbolPair(leftCode, rightCode *SymbolCode, leftSymbol, rightSymbol string) *SymbolPair {
	pair := &SymbolPair{
		Left:  &SymbolItem{},
		Right: &SymbolItem{},
	}
	pair.Left.Code = leftCode
	pair.Right.Code = rightCode
	pair.Left.Symbol = leftSymbol
	pair.Right.Symbol = rightSymbol
	return pair
}

// 根据 Code 返回对应的现货/期货交易对
func NewSymbolPair(code *SymbolCode, spotSymbol, futureSymbol string) *SymbolPair {
	pair := &SymbolPair{
		Left:  &SymbolItem{},
		Right: &SymbolItem{},
	}
	pair.Left.Code = &SymbolCode{
		Code:       code.Coin() + "--",
		USDXSymbol: code.USDXSymbol,
	}
	pair.Right.Code = code
	pair.Left.Symbol = spotSymbol
	pair.Right.Symbol = futureSymbol
	return pair
}

func NewSymbolPairFutureFuture(leftCode, rightCode *SymbolCode, leftFutureSymbol, rightFutureSymbol string) *SymbolPair {
	pair := &SymbolPair{
		Left:  &SymbolItem{},
		Right: &SymbolItem{},
	}
	pair.Left.Code = leftCode
	pair.Right.Code = rightCode
	pair.Left.Symbol = leftFutureSymbol
	pair.Right.Symbol = rightFutureSymbol
	if !pair.Left.Code.IsFuture() || !pair.Right.Code.IsFuture() {
		return nil
	}
	return pair
}

type Configable interface {
	GetExchangeName() string
	GetHost() string
	GetApiKey() string
	GetApiSecret() string
}

var ErrNotImplemented = errors.New("not implemented")
var ErrNotConnected = errors.New("not connected")
var ErrNotImplementedForInstrumentType = errors.New("not implemented for instrument type")
var ErrNotAvailableForInstrumentType = errors.New("not available for instrument type")
var ErrInvalidArgs = errors.New("invalid args")

type PriceTrigger struct {
	ID             string
	ControllerID   string
	InstrumentType InstrumentType
	Symbol         string
	SymbolCode     *SymbolCode
	Price          float64
	Direction      TriggerDirection
	Type           TriggerPriceType
	Triggered      bool
	TriggeredPrice float64
	LastCheckTime  *time.Time
	Source         string
}

type PriceWatch struct {
	InstrumentType InstrumentType
	Symbol         string
	SymbolCode     SymbolCode
}

func (this *PriceWatch) Key() string {
	return fmt.Sprintf("%s.%s.%s.%s", this.InstrumentType, this.SymbolCode.Code, this.SymbolCode.USDXSymbol, this.Symbol)
}

type Ticker struct {
	InstrumentType InstrumentType
	Time           int64   `json:"E"` // 毫秒
	Symbol         string  `json:"s"`
	Close          float64 `json:"c,string"`
	Open           float64 `json:"o,string"`
	High           float64 `json:"h,string"`
	Low            float64 `json:"l,string"`
	Volume         float64 `json:"v,string"`
	Amount         float64 `json:"q,string"`
}

type OrderBookLevel struct {
	Price  float64
	Amount float64
}

type OrderBook struct {
	InstrumentType InstrumentType
	Symbol         string
	Time           *time.Time
	Asks           []OrderBookLevel
	Bids           []OrderBookLevel
}

// 客户端的 websocket 包装器, 用于写入 ClientPacket（客户端发送的包） 和读取 Packet（gateway 发送过来的包）
// 必须用这个结构包装一下，保证 conn 不会并发写，否则会导致奇怪的 crash
type WebsocketWithLock struct {
	Conn         *websocket.Conn
	Mutex        sync.Mutex
	ConnectMutex sync.Mutex
}

func (this *WebsocketWithLock) WriteJSON(v *ClientPacket, apiSecret secrets.SecretString) error {
	this.Mutex.Lock()
	defer this.Mutex.Unlock()

	if v.Sign == "" && apiSecret != "" {
		v.SignWithKey(apiSecret)
	}
	if this.Conn != nil {
		return this.Conn.WriteJSON(v)
	} else {
		return errors.New("write json to websocket connection failed: connection is not ready")
	}
}

func (this *WebsocketWithLock) ReadJSON(packet *Packet) error {
	if this.Conn != nil {
		return this.Conn.ReadJSON(packet)
	}
	return errors.New("read json from websocket connection failed: connection is not ready")
}

func (this *WebsocketWithLock) RemoteAddr() string {
	return this.Conn.RemoteAddr().String()
}

func (this *WebsocketWithLock) Close() error {
	if this.Conn != nil {
		return this.Conn.Close()
	}
	return errors.New("close websocket connection failed: connection is not ready")
}

func (this *WebsocketWithLock) IsConnected() bool {
	return this.Conn != nil
}

func (this *WebsocketWithLock) SetReadDeadline(t time.Time) error {
	if this.Conn != nil {
		return this.Conn.SetReadDeadline(t)
	}
	return errors.New("set read deadline failed: websocket connection is not ready")
}

func (this *WebsocketWithLock) SetWriteDeadline(t time.Time) error {
	if this.Conn != nil {
		return this.Conn.SetWriteDeadline(t)
	}
	return errors.New("set write deadline failed: websocket connection is not ready")
}

func (this *WebsocketWithLock) SetConn(conn *websocket.Conn) {
	this.Conn = conn
}

type WithdrawChain = string

const (
	WithdrawChainUnknown WithdrawChain = ""
	WithdrawChainBTC     WithdrawChain = "BTC"
	WithdrawChainETH     WithdrawChain = "ETH"
	WithdrawChainARBI    WithdrawChain = "ARBI"
)

// TODO: 等待 1.24 版本，将 SyncMapOf[string, T] 简写为 SyncMap[T]
// 1.23 本身支持 generic type alias，但是不能跨 package 引用，用处不大，等 1.24 版本发布后才可以
type SyncMapOf[K comparable, T any] struct {
	mp sync.Map
}

func NewSyncMapOf[K comparable, T any]() *SyncMapOf[K, T] {
	return &SyncMapOf[K, T]{}
}

func (s *SyncMapOf[K, T]) Load(key K) (value T, ok bool) {
	v, ok := s.mp.Load(key)
	if ok {
		v2 := v.(T)
		return v2, ok
	}
	return
}

func (s *SyncMapOf[K, T]) LoadOrStore(key K, value T) (actual T, loaded bool) {
	v, loaded := s.mp.LoadOrStore(key, value)
	return v.(T), loaded
}

func (s *SyncMapOf[K, T]) Exist(key K) bool {
	_, ok := s.mp.Load(key)
	return ok
}

func (s *SyncMapOf[K, T]) Store(key K, value T) {
	s.mp.Store(key, value)
}

func (s *SyncMapOf[K, T]) Delete(key K) {
	s.mp.Delete(key)
}

func (s *SyncMapOf[K, T]) Range(f func(key K, value T) bool) {
	s.mp.Range(func(k, v interface{}) bool {
		return f(k.(K), v.(T))
	})
}

func (s *SyncMapOf[K, T]) Size() int {
	var size int
	s.Range(func(key K, value T) bool {
		size++
		return true
	})
	return size
}

func (s *SyncMapOf[K, T]) MarshalJSON() ([]byte, error) {
	var m = make(map[K]T)
	s.Range(func(key K, value T) bool {
		m[key] = value
		return true
	})
	return json.Marshal(m)
}

func (s *SyncMapOf[K, T]) UnmarshalJSON(data []byte) error {
	var m = make(map[K]T)
	err := json.Unmarshal(data, &m)
	if err != nil {
		return err
	}
	for k, v := range m {
		s.Store(k, v)
	}
	return nil
}

package exchange

import (
	"errors"
	"fmt"
	"strings"

	"github.com/AlecAivazis/survey/v2"
	"github.com/wizhodl/quanter/secrets"
)

type Secretable interface {
	Encode() secrets.SecretString
	Decode() error
	Prompt() bool
	Generate() (string, error)
}

type BaseAPISecret struct {
	Secretable
	ExchangeName       string
	ApiKey             string
	EncyptedApiSecret  secrets.SecretString // encrypted api secret and extra data
	DecryptedApiSecret secrets.SecretString // decrypted api secret
	ApiSecret          secrets.SecretString // decrypted api secret
}

func (this *BaseAPISecret) String() string {
	return fmt.Sprintf("ExchangeName=\"%s\"\nApiKey=\"%s\"\nApiSecret=\"%s\"\n", this.ExchangeName, this.ApiKey, string(this.EncyptedApiSecret))
}

func (this *BaseAPISecret) Set(apiSecret string) {
	this.EncyptedApiSecret = secrets.SecretString(apiSecret)
}

func (this *BaseAPISecret) Encrypt() error {
	this.Encode()
	password := SurveyPassword("请输入主密码: ")
	if password == "" {
		return errors.New("main password is empty")
	}
	authCode := SurveyInput("请输入 2FA: ")
	if authCode == "" {
		return errors.New("2fa is empty")
	}
	encrypted, err := EncryptWithPassword(string(this.DecryptedApiSecret), password, authCode)
	if err != nil {
		return fmt.Errorf("encrypt failed, error: %s", err)
	}
	this.EncyptedApiSecret = secrets.SecretString(encrypted)
	return nil
}

/* 公开使用的 api */

// 生成API密钥，并加密
func (this *BaseAPISecret) Generate() (string, error) {
	ok := this.Prompt()
	if !ok {
		return "", errors.New("prompt failed")
	}
	err := this.Encrypt()
	if err != nil {
		if err.Error() == "no password set" {
			fmt.Println("请先设置主密码")
		} else if err.Error() == "2fa is empty" {
			fmt.Println("请先设置2FA")
		} else if strings.Contains(err.Error(), "encrypt failed") {
			fmt.Printf("加密失败，请检查主密码或者算法，error: %s\n", err)
		}
		return "", err
	}
	return this.String(), nil
}

// 解密
func (this *BaseAPISecret) Decrypt() (er error) {
	hasPass := secrets.HasPassword()
	if !hasPass {
		return errors.New("no password set")
	}
	this.DecryptedApiSecret, er = secrets.Decrypt(string(this.EncyptedApiSecret))
	this.Decode()
	return
}

type APISecret struct {
	BaseAPISecret
}

func NewEmptyAPISecret(exchangeName string) *APISecret {
	s := &APISecret{
		BaseAPISecret: BaseAPISecret{
			ExchangeName: exchangeName,
		},
	}
	s.Secretable = s
	return s
}

func NewAPISecret(exchangeName string, apiKey string, apiSecret secrets.SecretString, isEncrypted bool) (*APISecret, error) {
	s := NewEmptyAPISecret(exchangeName)
	s.ApiKey = apiKey
	if isEncrypted {
		s.EncyptedApiSecret = apiSecret
		err := s.Decrypt()
		if err != nil {
			return nil, err
		}
	} else {
		s.DecryptedApiSecret = apiSecret
	}
	err := s.Decode()
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (this *APISecret) Encode() secrets.SecretString {
	this.DecryptedApiSecret = secrets.SecretString(this.ApiSecret)
	return this.DecryptedApiSecret
}

func (this *APISecret) Decode() error {
	this.ApiSecret = this.DecryptedApiSecret
	return nil
}

func (this *APISecret) Prompt() bool {
	this.ApiKey = SurveyInput("请输入 ApiKey: ")
	apiSecret := SurveyPassword("请输入 ApiSecret: ")
	this.ApiSecret = secrets.SecretString(apiSecret)
	fmt.Printf("\n\n*** 请确认输入的API *** \n\nAPI Key: %s\nAPI Secret: %s\n\n",
		this.ApiKey,
		FormatPassword(apiSecret),
	)
	return true
}

// utils functions

func FormatPassword(password string) string {
	mask := strings.Repeat("*", len(password)-4)
	return fmt.Sprintf("%s%s%s (%d位)", password[:2], mask, password[len(password)-2:], len(password))
}

func SurveyYes(tips string) bool {
	result := false
	prompt := &survey.Confirm{
		Message: tips,
	}
	survey.AskOne(prompt, &result)
	return result
}

func SurveyPassword(message string) string {
	s := ""
	survey.AskOne(&survey.Password{
		Message: message,
	}, &s)
	return strings.TrimSpace(s)
}

func SurveyInput(message string) string {
	s := ""
	survey.AskOne(&survey.Input{
		Message: message,
	}, &s)
	return strings.TrimSpace(s)
}

func SurveySelect(message string, choices []string) (int, string) {
	if len(choices) == 0 {
		return -1, ""
	}
	s := ""
	survey.AskOne(&survey.Select{
		Message:  message,
		Options:  choices,
		PageSize: 10,
	}, &s)
	for idx, choice := range choices {
		if strings.EqualFold(s, choice) {
			return idx, choice
		}
	}
	return 0, choices[0]
}

var errInvalidPassword = errors.New("invalid password")
var errTestDecryptFailed = errors.New("test decrypt failed")

func EncryptWithPassword(secretPlain string, password string, authCode string) (encryptedApiSecret string, er error) {
	err := secrets.CheckPassword(password, authCode)
	if err != nil {
		fmt.Println("\n主密码验证失败，请检查主密码")
		er = errInvalidPassword
		return
	}

	encryptedApiSecret, err = secrets.Encrypt([]byte(secretPlain))
	if err != nil {
		fmt.Println("\n加密失败，请检查主密码或者算法")
		er = err
		return
	}

	decryptApiSecret, err := secrets.Decrypt(encryptedApiSecret)
	if err != nil {
		fmt.Println("\n测试解密失败，请检查主密码或者算法")
		er = err
		return
	}

	if string(decryptApiSecret) != secretPlain {
		fmt.Println("\n验证加解密失败，解密后的值与原始值不一致")
		er = errTestDecryptFailed
		return
	}
	return encryptedApiSecret, nil
}

package exchange

import (
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

var SymbolCodeSuffixInstrumentMap = map[string]InstrumentType{
	".U": USDXMarginedFutures,
}

// 正则表达式解析并不是很完善
func parseSymbolCode(code string) (ok bool, product, month, suffix, quote string) {
	if r, err := regexp.Compile(`(\S+)(\d{2}|\-{2}|\-\S+|\*)(\.[U|u])?`); err == nil {
		if r.MatchString(code) {
			groups := r.FindStringSubmatch(code)
			product = groups[1]
			month = groups[2]
			suffix = groups[3]
			if strings.Contains(product, "-") {
				// 合约指定了报价币种，如 ETH-BTC00.U
				quote = strings.Split(product, "-")[1]
				product = strings.Split(product, "-")[0]
			}
			return true, product, month, suffix, quote
		}
	}
	return false, "", "", "", ""
}

func NewSymbolCode(code string, uSymbol string) (symbolCode *SymbolCode, er error) {
	uSymbol = strings.ToUpper(uSymbol)
	if _, quote, valid := CheckValidSymbolCode(code); valid {
		symbolCode = &SymbolCode{Code: code, USDXSymbol: uSymbol}
		if quote != "" {
			symbolCode.USDXSymbol = strings.ToUpper(quote)
		}
		// CNYMarginedFutures 因为 ctp 品种区分大小写，需要保持原大小写，如果强制转换为大写会导致问题
		if uSymbol != "CNY" {
			symbolCode.Code = strings.ToUpper(string(symbolCode.Code))
		}
	} else {
		er = errors.New("invalid symbol code format")
	}
	return
}

func NewSymbolCodeForce(code string, uSymbol string) (symbolCode *SymbolCode) {
	var err error
	symbolCode, err = NewSymbolCode(code, uSymbol)
	if err != nil {
		panic(err)
	}
	return
}

func (this *SymbolCode) String() string {
	if this.InstrumentType().IsFuture() {
		return strings.ToUpper(this.Code)
	}
	return this.Code
}

func (this *SymbolCode) Coin() string {
	if product, _, valid := CheckValidSymbolCode(this.Code); valid {
		return product
	}
	return ""
}

func (this *SymbolCode) InstrumentType() (instrumentType InstrumentType) {
	instrumentType = UnknownInstrumentType
	if _, _, valid := CheckValidSymbolCode(this.Code); !valid {
		return
	}
	if this.Month() != "" {
		_, _, _, suffix, _ := parseSymbolCode(this.Code)
		if suffix != "" {
			suffix = strings.ToUpper(suffix)
			instrumentType = SymbolCodeSuffixInstrumentMap[suffix]
		} else {
			instrumentType = CoinMarginedFutures
		}
	} else {
		instrumentType = Spot
	}
	return
}

func (this *SymbolCode) IsSpot() bool {
	return this.InstrumentType() == Spot
}

func (this *SymbolCode) IsFuture() bool {
	return this.InstrumentType().IsFuture()
}

func (this *SymbolCode) IsPerp() bool {
	return this.Month() == "00"
}

func (this *SymbolCode) Month() string {
	valid, _, month, _, _ := parseSymbolCode(this.Code)
	if valid && !strings.HasPrefix(month, "-") {
		return month
	}
	return ""
}

func (this *SymbolCode) MonthNumber() (month int, er error) {
	monthStr := strings.TrimLeft(this.Month(), "0")
	m, err := strconv.ParseInt(monthStr, 10, 32)
	if err != nil {
		er = err
	}
	month = int(m)
	return
}

func (this *SymbolCode) IsWildcard() bool {
	return strings.Contains(this.Code, "*")
}

func (this *SymbolCode) Matches(symbolCode *SymbolCode) bool {
	if symbolCode.IsWildcard() {
		return strings.EqualFold(symbolCode.Coin(), this.Coin())
	} else {
		return strings.EqualFold(this.Code, symbolCode.Code)
	}
}

// 直接转换为现货 symbolCode
func (this *SymbolCode) ToSpot() *SymbolCode {
	if this.IsSpot() {
		return this
	}
	return &SymbolCode{Code: this.Coin() + "--", USDXSymbol: this.USDXSymbol}
}

// 直接转换为 UDSX 永续合约 symbolCode
func (this *SymbolCode) ToUDSXPerp() *SymbolCode {
	if this.IsPerp() {
		return this
	}
	return &SymbolCode{Code: this.Coin() + "00.U", USDXSymbol: this.USDXSymbol}
}

// BTC--/ETH-BTC: 现货 BTC/USDT, ETH/BTC
// BTC00: 币本位 BTC 永续合约
// BTC03/BTC06/BTC09/BTC12: 币本位 BTC 季度合约
// TODO: 暂未实现 U 本位合约检查
// BTC00.U: U本位 BTC 永续合约；
// BTC03.U/BTC06.U/BTC09.U/BTC12.U: U本位 BTC 季度合约
// ETH-BTC00.U: 类U本位 ETH/BTC 永续合约, 计价货币为 BTC
func CheckValidSymbolCode(code string) (product, quote string, valid bool) {
	valid, product, month, suffix, quote := parseSymbolCode(code)

	// 检查 month 的范围
	if month != "00" && !strings.HasPrefix(month, "-") {
		instrumentType := SymbolCodeSuffixInstrumentMap[suffix]
		validMonthes := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12}
		if SliceContains([]InstrumentType{USDXMarginedFutures, CoinMarginedFutures}, instrumentType) {
			validMonthes = []int64{3, 6, 9, 12}
		}
		monthWithoutLeadingZero := strings.TrimLeft(month, "0")
		if month, err := strconv.ParseInt(monthWithoutLeadingZero, 10, 64); err != nil {
			return "", "", false

		} else {
			mFound := false
			for _, m := range validMonthes {
				if month == m {
					mFound = true
				}
			}
			if !mFound {
				return "", "", false

			}
		}
	}

	// 币种必须在允许的列表内
	return product, quote, valid
}

func TestTranslateSymbolCode(exchange Exchange) error {
	testCodes := map[string][][]string{
		OKEx: {
			{"ETH--", "USDT", "ETH-USDT", ""},
			{"ETH--", "USDC", "ETH-USDC", ""},
			{"ETH-USDT", "USDC", "ETH-USDT", ""},
			{"ETH-BTC", "USDT", "ETH-BTC", ""},
			{"ETH00.U", "USDT", "ETH-USDT", "ETH-USDT-SWAP"},
			{"ETH00.U", "USDC", "ETH-USDC", "ETH-USDC-SWAP"},
			{"ETH00", "USDT", "ETH-USDT", "ETH-USD-SWAP"},
			{"ETH00", "USDC", "ETH-USDC", "ETH-USD-SWAP"},
		},
		Binance: {
			{"ETH--", "USDT", "ETHUSDT", ""},
			{"ETH-BTC", "USDT", "ETHBTC", ""},
			{"ETH00.U", "USDT", "ETHUSDT", "ETHUSDT"},
			{"ETH00", "USDT", "ETHUSDT", "ETHUSD_PERP"},
			{"ETH-BTC00.U", "BTC", "ETHBTC", "ETHBTC"},
			{"KPEPE00.U", "USDT", "", "1000PEPEUSDT"},
		},
		BitMEX: {
			{"ETH00.U", "USDT", "", "ETHUSDT"},
			{"ETH00", "USDT", "", "ETHUSD"},
			{"BTC00.U", "USDT", "", "XBTUSDT"},
			{"BTC00", "USDT", "", "XBTUSD"},
		},
		Hyperliquid: {
			{"ETH00.U", "USDC", "ETH/USDC", "ETH"},
			{"BTC00.U", "USDC", "BTC/USDC", "BTC"},
			// {"PURR--", "USDC", "PURR/USDC", "PURR"},
			{"KPEPE00.U", "USDC", "", "kPEPE"},
			{"HYPE00.U", "USDC", "HYPE/USDC", "HYPE"},
		},
		Bybit: {
			{"ETH00.U", "USDC", "ETHUSDC", "ETHPERP"},
			// {"BTC03.U", "USDC", "BTCUSDC", "BTC-28MAR25"},
			{"BTC00.U", "USDT", "BTCUSDT", "BTCUSDT"},
			{"BTC00", "USDT", "BTCUSDT", "BTCUSD"},
			// {"ETH09", "USDT", "ETHUSDT", "ETHUSDU24"},
			{"BTC--", "USDT", "BTCUSDT", ""},
			{"KPEPE00.U", "USDT", "", "1000PEPEUSDT"},
		},
	}

	codes, found := testCodes[exchange.GetName()]
	if !found {
		return nil
	}

	for _, code := range codes {
		symbolCodeStr := code[0]
		uSymbol := code[1]
		spotSymbol := code[2]
		futureSymbol := code[3]
		code, err := NewSymbolCode(symbolCodeStr, uSymbol)
		if err != nil {
			return fmt.Errorf("new symbol code %s err: %s", symbolCodeStr, err)
		}
		pairs, err := exchange.TranslateSymbolCode(code)
		if err != nil {
			return fmt.Errorf("translate symbol code %s err: %s", symbolCodeStr, err)
		}
		if len(pairs) == 0 {
			return fmt.Errorf("translate symbol code %s return 0 pair", symbolCodeStr)
		}
		if pairs[0].Left.Symbol != spotSymbol {
			return fmt.Errorf("translate symbol code %s spot symbol not match: %s != %s", symbolCodeStr, pairs[0].Left.Symbol, spotSymbol)
		}
		if pairs[0].Right.Symbol != futureSymbol {
			return fmt.Errorf("translate symbol code %s future symbol not match: %s != %s", symbolCodeStr, pairs[0].Right.Symbol, futureSymbol)
		}

		if futureSymbol == "" {
			continue
		}

		_spotSymbol, _code, err := exchange.TranslateFutureSymbol(code.InstrumentType(), futureSymbol, uSymbol)
		if err != nil {
			if err == ErrNotImplemented {
				continue
			}
			return fmt.Errorf("translate future symbol %s err: %s", futureSymbol, err)
		}
		if _spotSymbol != spotSymbol {
			return fmt.Errorf("translate future symbol %s spot symbol not match: %s != %s", futureSymbol, _spotSymbol, spotSymbol)
		}
		if _code != nil && _code.Code != symbolCodeStr {
			return fmt.Errorf("translate future symbol %s code not match: %s != %s", futureSymbol, _code.Code, symbolCodeStr)
		}
	}

	return nil
}

func FixSymbolCode(symbolCode *SymbolCode) (migrated bool) {
	code := symbolCode.Code
	if strings.HasSuffix(code, ".Y") || strings.HasSuffix(code, ".y") {
		symbolCode.Code = code[:len(code)-1] + "U"
		symbolCode.USDXSymbol = "CNY"
		migrated = true
	} else if strings.HasSuffix(code, ".D") || strings.HasSuffix(code, ".d") {
		symbolCode.Code = code[:len(code)-1] + "U"
		symbolCode.USDXSymbol = "USD"
		migrated = true
	}
	if symbolCode.USDXSymbol == "" {
		symbolCode.USDXSymbol = "USDT"
		migrated = true
	}
	return migrated
}

func FixInstrumentType(instrumentType InstrumentType) InstrumentType {
	if string(instrumentType) == "CNYMarginedFutures" {
		return USDXMarginedFutures
	}
	if string(instrumentType) == "USDMarginedFutures" {
		return USDXMarginedFutures
	}
	if string(instrumentType) == "USDTMarginedFutures" {
		return USDXMarginedFutures
	}
	return instrumentType
}

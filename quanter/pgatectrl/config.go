package pgatectrl

import (
	"fmt"
	"os"
	"path"

	"github.com/spf13/viper"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/zlog"
)

type ProxyManagerControllerConfig struct {
	baseconfig.BaseConfig
	controller *ProxyManagerController
}

func NewProxyManagerControllerConfig(controller *ProxyManagerController) (*ProxyManagerControllerConfig, error) {
	if config, err := LoadConfig(controller.ConfigPath, controller.ID); err != nil {
		return nil, err
	} else {
		config.controller = controller
		return config, nil
	}
}

func LoadConfig(configPath, controllerID string) (config *ProxyManagerControllerConfig, er error) {
	config = &ProxyManagerControllerConfig{}
	configFilePath := path.Join(configPath, controllerID+".proxy.toml")
	if _, err := os.Stat(configFilePath); !os.IsNotExist(err) {
		viper.SetConfigName(controllerID + ".proxy")
		viper.AddConfigPath(configPath)
		err := viper.ReadInConfig()
		if err == nil {
			err := viper.Unmarshal(&config)
			if err != nil {
				zlog.Errorf("[%s] unable to decode config into struct, %v", controllerID, err)
				return nil, err
			}
			if err := config.Validate(); err != nil {
				zlog.Errorf("[%s] grid config validate error: %s, %v", controllerID, err, config)
				return nil, err
			}
			zlog.Infof("[%s] load config from local file", controllerID)
			return config, nil
		} else { // 如果配置文件存在，但是解析出现错误，报错退出
			zlog.Panicf("[%s] read config file error：%s", controllerID, err)
			return nil, fmt.Errorf("read config error: %v", err)
		}
	} else {
		return nil, err
	}
}

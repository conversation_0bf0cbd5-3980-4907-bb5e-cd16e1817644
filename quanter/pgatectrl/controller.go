package pgatectrl

import (
	"github.com/wizhodl/pgate"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/messenger"
	"github.com/wizhodl/quanter/utils"
)

type ProxyManagerController struct {
	base.BaseController

	Config       *ProxyManagerControllerConfig
	ProxyManager *pgate.ProxyManager
}

func NewProxyManagerController(id string, debug bool, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath string, parentMessenger *messenger.SlackMessenger, managerID string) (*ProxyManagerController, error) {
	controller := &ProxyManagerController{
		BaseController: base.BaseController{
			BaseResponder: command.NewBaseResponder(debug, commitHash, buildTime, id, nil, ""),
			ID:            id,
			RefID:         utils.NewRandomID(),
			ConfigPath:    configPath,
		},
	}
	controller.Controllable = controller
	controller.Setup(debug, parentMessenger, managerID)

	config, err := NewProxyManagerControllerConfig(controller)
	if err != nil {
		return nil, err
	}

	controller.Config = config
	if logDirPath != "" {
		config.LogDirPath = logDirPath
	}

	controller.AddCommands([]command.Commander{
		NewNewProxyCommand(controller),
		NewListProxyCommand(controller),
		NewListForwardersCommand(controller),
		NewListLoginForwardersCommand(controller),
		NewListGroupForwardersCommand(controller),
		NewDeleteProxyCommand(controller),
		NewTestProxyCommand(controller),
		NewRepairProxyCommand(controller),
		NewStopProxyCommand(controller),
		NewStartProxyCommand(controller),
		NewRestartProgramCommand(controller),
		NewListDeletedProxyCommand(controller),
		NewEnableWARPCmd(controller),
		NewDisableWARPCmd(controller),
		NewIncreaseLifeHourCmd(controller),
		NewListAWSReservedInstancesCommand(controller),
		NewListAWSReservationCoveragesCommand(controller),
		NewListGoogelCommitmentsCommand(controller),
		NewListGoogelCommitmentsCoveragesCommand(controller),
		NewSyncCloudStorageCommand(controller),
		cmds.NewMuteCommand(),
		cmds.NewDebugCommand(),
		cmds.NewStackTraceCommand(),
		cmds.NewDeleteErrorsCommand(),
	})

	return controller, nil
}

func (this *ProxyManagerController) GetBaseConfig() *baseconfig.BaseConfig {
	return &this.Config.BaseConfig
}

func (this *ProxyManagerController) SaveConfig(configStr string) (correctedConfigStr string, er error) {
	return "", nil
}

func (this *ProxyManagerController) InitProxyManager() error {
	if !this.IsLaunched() {
		zlog.Panicf("proxy manager controller not launched")
	}

	options := &pgate.ProxyOptions{}
	err := this.LoadOptions(options)
	if err != nil {
		this.ErrorMsgf("load proxy manager options failed: %v", err)
		return err
	}

	var err2 error
	this.ProxyManager, err2 = pgate.NewProxyManager(options, this)
	if err2 != nil {
		this.ErrorMsgf("load proxy manager failed: %v", err2)
		return err2
	}

	this.Debugf("starting proxy gateway...")
	gateway := pgate.NewProxyGateway(this.ProxyManager)
	this.ProxyManager.Gateway = gateway

	this.SendMsgf("proxy manager started")
	return nil
}

func (this *ProxyManagerController) Exit() {
	this.Infof("exit with clearing proxy gateway")
	this.ProxyManager.Gateway.ClearSurgeConfig()
}

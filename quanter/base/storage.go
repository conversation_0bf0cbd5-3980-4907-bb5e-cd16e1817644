package base

import (
	"errors"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type Storager interface {
	Save() error
	SaveTo(configPath string, id string, overwrite bool) error
	RecordAssets()
	GetAssets() map[int64]float64
	GetPriceTriggers() []*exchange.PriceTrigger
	AddPriceTrigger(pt *exchange.PriceTrigger)
	DeletePriceTrigger(id string) error
	GetPriceWatches() []*exchange.PriceWatch
	AddPriceWatch(pt *exchange.PriceWatch)
	DeletePriceWatch(symbolCode *exchange.SymbolCode) error
}

type BaseStorage struct {
	Storager `json:"-"`

	Assets        map[int64]float64
	PriceTriggers []*exchange.PriceTrigger
	PriceWatches  []*exchange.PriceWatch
	Controller    Controllable `json:"-"`

	priceTriggersMutex *sync.Mutex
	priceWatchesMutex  *sync.Mutex
}

func (this *BaseStorage) RecordAssets() {
	if this == nil {
		return
	}
	if asset, err := this.Controller.GetAccountWorth(true); err != nil {
		zlog.Errorf("[%s] get account worth failed, error: %s", this.Controller.GetID(), err)
	} else {
		nowT := time.Now().Unix()
		nowT = nowT - nowT%60 // round 到分钟
		this.Assets[nowT] = asset
	}
	this.Save()
}

func (this *BaseStorage) GetAssets() map[int64]float64 {
	return this.Assets
}

func (this *BaseStorage) Save() error {
	configPath := this.Controller.GetConfigPath()
	id := this.Controller.GetID()
	return this.SaveTo(configPath, id, true)
}

func (this *BaseStorage) RenderAssets() string {

	type AssetItem struct {
		time       time.Time
		totalAsset float64
		asset      float64
	}

	// 倒序打印，最新的时间在最上面
	keys := make([]int64, len(this.Assets))
	i := 0
	for k := range this.Assets {
		keys[i] = k
		i++
	}
	sort.SliceStable(keys, func(i, j int) bool {
		return keys[i] < keys[j]
	})

	assetItemList := []AssetItem{}
	for _, k := range keys {
		assetItem := AssetItem{}
		assetItem.time = time.Unix(k, 0)
		assetItem.asset = this.Assets[k]
		assetItem.totalAsset = assetItem.asset
		assetItemList = append(assetItemList, assetItem)
	}

	t := exchange.NewTable()
	// 打印资产快照和对应的趋势机开平仓
	t.SetHeader([]string{"Time", "Total Asset", "Asset"})

	sort.SliceStable(assetItemList, func(i, j int) bool {
		return assetItemList[i].time.After(assetItemList[j].time)
	})

	for _, item := range assetItemList {
		t.AddRow([]string{utils.FormatShortTimeStr(&item.time, true), fmt.Sprintf("%.2f USDT", item.totalAsset), fmt.Sprintf("%.2f USDT", item.asset)})
	}

	assetsMsg := "[No Assets]"
	if len(t.Rows) > 1 {
		assetsMsg = t.Render()
	}
	return assetsMsg
}

func (this *BaseStorage) lockPriceTriggers() {
	if this.priceTriggersMutex == nil {
		this.priceTriggersMutex = &sync.Mutex{}
	}
	this.priceTriggersMutex.Lock()
}

func (this *BaseStorage) unlockPriceTriggers() {
	this.priceTriggersMutex.Unlock()
}

func (this *BaseStorage) GetPriceTriggers() []*exchange.PriceTrigger {
	this.lockPriceTriggers()
	defer this.unlockPriceTriggers()
	return this.PriceTriggers
}

func (this *BaseStorage) AddPriceTrigger(pt *exchange.PriceTrigger) {
	this.lockPriceTriggers()
	defer this.unlockPriceTriggers()
	this.PriceTriggers = append(this.PriceTriggers, pt)
	this.Save()
}

func (this *BaseStorage) DeletePriceTrigger(id string) error {
	this.lockPriceTriggers()
	defer this.unlockPriceTriggers()
	for i, p := range this.PriceTriggers {
		if p.ID == id {
			this.PriceTriggers = append(this.PriceTriggers[:i], this.PriceTriggers[i+1:]...)
			this.Save()
			return nil
		}
	}
	return errors.New("priceTrigger not found")
}

func (this *BaseStorage) lockPriceWatches() {
	if this.priceWatchesMutex == nil {
		this.priceWatchesMutex = &sync.Mutex{}
	}
	this.priceWatchesMutex.Lock()
}

func (this *BaseStorage) unlockPriceWatches() {
	this.priceWatchesMutex.Unlock()
}

func (this *BaseStorage) GetPriceWatches() []*exchange.PriceWatch {
	this.lockPriceWatches()
	defer this.unlockPriceWatches()
	return this.PriceWatches
}

func (this *BaseStorage) AddPriceWatch(pt *exchange.PriceWatch) {
	this.lockPriceWatches()
	defer this.unlockPriceWatches()

	for _, p := range this.PriceWatches {
		if p.SymbolCode.Code == pt.SymbolCode.Code {
			return
		}
	}
	this.PriceWatches = append(this.PriceWatches, pt)
	this.Save()
}

func (this *BaseStorage) DeletePriceWatch(symbolCode *exchange.SymbolCode) error {
	this.lockPriceWatches()
	defer this.unlockPriceWatches()

	newPriceWatches := make([]*exchange.PriceWatch, 0, len(this.PriceWatches))

	for _, p := range this.PriceWatches {
		if p.SymbolCode.Code != symbolCode.Code {
			newPriceWatches = append(newPriceWatches, p)
		}
	}

	this.PriceWatches = newPriceWatches
	this.Save()
	return nil
}

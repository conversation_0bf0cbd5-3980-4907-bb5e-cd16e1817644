package base

import (
	"encoding/json"
	"fmt"
	"os"
	"path"
	"strings"
	"time"

	cron "github.com/robfig/cron/v3"
	"github.com/wizhodl/pgate"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/messenger"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"
	"go.uber.org/zap"
)

const AccountWorthCacheTime = 10 * time.Second

// 负责获取资产余额的接口，通常是 controller
type Controllable interface {
	GetID() string
	GetConfigPath() string
	GetExchange() exchange.Exchange
	GetInstrumentTypes() []exchange.InstrumentType
	GetStorage() Storager
	GetAccountBalances(allSymbols bool) (balances exchange.AccountBalanceList, er error)
	GetAccountWorth(allSymbols bool) (qtyInUSDT float64, er error)
	RenderAssets() string
	GetBaseConfig() *baseconfig.BaseConfig
	SaveConfig(configStr string) (correctedConfigStr string, er error)
	NewSymbolCode(code string) (symbolCode *exchange.SymbolCode, er error)
	GetCommandProcessor() *command.CommandProcessor
	SetLastUseTime(time time.Time)
	GetLastUseTime() time.Time
	GetAPIExpireTime() (*time.Time, error)
	FormatAPIExpireTime() string
	Debugf(format string, args ...interface{})
	Infof(format string, args ...interface{})
	Warnf(format string, args ...interface{})
	Errorf(format string, args ...interface{})
}

type OrderControllable interface {
	Controllable
	command.CommandProcessorResponder
	AppendOrderRecord(exOrder *exchange.Order, controllerID, exchangeName string, code *exchange.SymbolCode) *exchange.OrderRecord
	GetController(controllerID string) Controllable
	ListControllers() []Controllable
	ListStrategies() []Strategy
	AddStrategy(strategyType StrategyType, strategy Strategy)
	RemoveStrategy(strategyType StrategyType, idOrAlias string) error
	SetStrategyAlias(strategyType StrategyType, id, alias string) error
	GetStrategy(strategyType StrategyType, idOrAlias string) Strategy
	GetLaunchTime() *time.Time
}

type StrategyType string

const (
	StrategyTypeCrossExchangeArbitrager StrategyType = "CrossExchangeArbitrager"
	StrategyTypeFundingArbitrager       StrategyType = "FundingArbitrager"
)

type Strategy interface {
	GetStrategyType() StrategyType
	SetAlias(alias string)
	GetAliasOrID() string
	GetLastUseTime() time.Time
}

type FileOptionable interface {
	Load(path string) error
}

type BaseController struct {
	Controllable

	ID         string
	RefID      string
	ConfigPath string
	Exchange   exchange.Exchange
	Messenger  *messenger.SlackMessenger
	Cron       *cron.Cron

	options FileOptionable

	command.BaseResponder
	commandProcessor *command.CommandProcessor

	managerID         string // QuanterManager 的名字，读取 log 文件用
	standalone        bool   // 是否以独立模式运行
	isDebuging        bool   // 是否正以 Debug 模式运行
	apiSecret         secrets.SecretString
	withdrawApiSecret secrets.SecretString
	closed            bool // 是否已经退出
	running           bool // 是否已经 Run() 过
	lastUseTime       time.Time
}

func (this *BaseController) SetLastUseTime(time time.Time) {
	this.lastUseTime = time
}

func (this *BaseController) GetLastUseTime() time.Time {
	return this.lastUseTime
}

func (this *BaseController) Infof(format string, args ...interface{}) {
	zlog.Infof(fmt.Sprintf("[%s] %s", this.ID, format), args...)
}

func (this *BaseController) Errorf(format string, args ...interface{}) {
	zlog.Errorf(fmt.Sprintf("[%s] %s", this.ID, format), args...)
}

func (this *BaseController) Debugf(format string, args ...interface{}) {
	zlog.Debugf(fmt.Sprintf("[%s] %s", this.ID, format), args...)
}

func (this *BaseController) Warnf(format string, args ...interface{}) {
	zlog.Warnf(fmt.Sprintf("[%s] %s", this.ID, format), args...)
}

func (this *BaseController) IsExchange(name string) bool {
	exchangeName := this.GetBaseConfig().ExchangeName
	if this.Exchange != nil {
		exchangeName = this.Exchange.GetName()
	}
	return strings.EqualFold(exchangeName, name)
}

func (this *BaseController) Setup(debug bool, parentMessenger *messenger.SlackMessenger, managerID string) error {
	standalone := false
	if managerID == "" {
		standalone = true
	}

	var msger *messenger.SlackMessenger
	// 仅在传递 -id 时 Controller 以 standalone 模式运行，具有 launch 和 release 的功能
	// 如果以 QuanterManager 的方式工作，去除 TurtleController 的 launch 和 release 的功能
	// 初始化 messager 要在初始化 commander 前完成，因为 commander 需要用到 messenger 的 RegisterRequiresFile 函数
	if standalone {
		// 以独立模式运行时，自己初始化 messenger 客户端
		robotToken, cleanerToken, err := utils.GetSlackRobotToken(debug)
		if err != nil {
			zlog.Panicf("[%s] get slack robot token error: %s", err.Error())
		}
		msger = messenger.NewSlackMessenger(robotToken, secrets.GetSlackCommander(), cleanerToken)
	} else {
		msger = parentMessenger
	}

	this.BaseResponder.Messenger = msger
	this.Messenger = msger
	this.managerID = managerID
	this.standalone = standalone

	this.SetDebug(debug)

	this.commandProcessor = command.NewCommandProcessor(this, []command.Commander{})

	if standalone {
		this.Messenger.ConnectedCallback = this.MessengerConnectedCallback
		go this.Messenger.RTMConnect() // slack 实时连接
		go this.SetExchangeProxyLoop(nil)
	}

	go this.initRealTimePrice()

	// 初始化命令处理器
	this.Messenger.RegisterCommandHandler(this.ID, this.CommandHandler, false)
	return nil
}

func (this *BaseController) LoadOptions(options FileOptionable) error {
	if options == nil {
		return fmt.Errorf("options is nil")
	}

	err := options.Load(this.GetBaseConfig().OptionsPath)
	if err != nil {
		this.Errorf("load options failed: %s", err)
		return err
	}
	this.options = options
	return nil
}

func (this *BaseController) NewSymbolCode(code string) (symbolCode *exchange.SymbolCode, er error) {
	if len(code) < 2 {
		return nil, fmt.Errorf("code %s length must greater than 2", code)
	}
	return exchange.NewSymbolCode(code, this.GetBaseConfig().USDXSymbol)
}

func (this *BaseController) initRealTimePrice() {
	for {
		if this.IsClosed() {
			return
		}
		if this.GetExchange() == nil {
			time.Sleep(time.Second * 10)
			continue
		}

		this.GetExchange().SetEnableRealtimePrice(this.GetBaseConfig().EnableRealtimePrice)

		priceTriggers := this.GetStorage().GetPriceTriggers()
		for _, priceTrigger := range priceTriggers {
			if !priceTrigger.Triggered {
				this.GetExchange().RegisterPriceTrigger(priceTrigger)
			}
		}

		priceWatches := this.GetStorage().GetPriceWatches()
		for _, watch := range priceWatches {
			this.GetExchange().AddPriceWatch(watch)
		}
		return
	}
}

func (this *BaseController) AddCommands(commands []command.Commander) {
	this.commandProcessor.AddCommands(commands)
}

func (this *BaseController) SetAdditionalHelpText(text string) {
	this.commandProcessor.SetAdditionalHelpText(text)
}

func (this *BaseController) SetLaunched(launched bool) {
	this.Launched.Store(launched)
	if launched && this.LaunchTime == nil {
		now := time.Now()
		this.LaunchTime = &now
	} else if !launched && this.LaunchTime != nil {
		this.LaunchTime = nil
	}
}

func (this *BaseController) IsLaunched() bool {
	return this.Launched.Load()
}

func (this *BaseController) IsDebuging() bool {
	return this.isDebuging
}

func (this *BaseController) SetDebug(debug bool) {
	this.BaseResponder.Debug = debug
	var zlogger *zap.SugaredLogger
	if debug {
		zlogger = zlog.NewRotateLogger("DEBUG", this.BaseResponder.LogPath, nil)
	} else {
		zlogger = zlog.NewRotateLogger("INFO", this.BaseResponder.LogPath, nil)
	}
	zlog.SetLogger(zlogger)
	if this.Exchange != nil {
		this.Exchange.SetHttpDebug(debug)
	}
}

func (this *BaseController) GetID() string {
	return this.ID
}

func (this *BaseController) GetExchange() exchange.Exchange {
	return this.Exchange
}

func (this *BaseController) GetApiSecret() secrets.SecretString {
	return this.apiSecret
}

func (this *BaseController) GetWithdrawApiSecret() secrets.SecretString {
	return this.withdrawApiSecret
}

func (this *BaseController) Standalone() bool {
	return this.standalone
}

func (this *BaseController) MessengerConnectedCallback() {
	if this.standalone {
		this.Messenger.SetDefaultChannelName(this.ID) // 如果是 standalone 模式，将海龟的频道设为默认频道，如果 symbol 对应的频道不存在，消息会转发到这个频道
		this.AskForLaunch()
	}
}

func (this *BaseController) AskForLaunch() {
	if this.standalone {
		if this.isDebuging {
			this.SendMsgf("以 Debug - [实盘] 模式启动。")
		} else {
			msg := fmt.Sprintf("Build: %s, 请输入命令 `.launch Password GoogleAuthCode ` 启动程序", this.BuildInfo())
			this.SendMsgf(msg)
		}
	}
}

func (this *BaseController) CommandHandler(command string, args ...string) {
	this.commandProcessor.Process(command, args)
}

func (this *BaseController) TryLaunchWithDebug() (debugLaunched bool) {
	// 用于 Debug 时无需 .launch 即可启动，必须设置 ApiSecret 和 AUTH_SECRET 为未加密的值
	// 如果没有提供 ApiSecret 和 AUTH_SECRET，还是通过 .launch 命令启动
	apiSecretEnvKey := fmt.Sprintf("QUANTER_DEBUG_%s_API_SECRET", this.ID)
	envAPISecret := os.Getenv(apiSecretEnvKey)

	if this.Debug && envAPISecret != "" {
		this.Infof("env $%s is set, use it instead of config.%s.ApiSecret.", apiSecretEnvKey, this.ID)
		this.isDebuging = true
		this.SetLaunched(true)
		this.apiSecret = secrets.SecretString(envAPISecret)

		withdrawApiSecret := os.Getenv(fmt.Sprintf("QUANTER_DEBUG_%s_WITHDRAW_API_SECRET", this.ID))
		if withdrawApiSecret != "" {
			this.withdrawApiSecret = secrets.SecretString(withdrawApiSecret)
		}

		return true
	} else if !this.standalone {
		this.AskForLaunch()
		return false
	}
	return false
}

// 因为 apiSecret 以加密的方式配置，无法以无 password 的方式启动
func (this *BaseController) Launch(password string, authCode string) bool {
	// 在 Launch 之前可能已经以 debug 模式运行了，这里就不重复运行了
	if this.IsLaunched() {
		this.Infof("controller already launched.")
		return false
	}
	// 如果是 debug 模式，无需密码和 authCode
	if this.Debug && (password == "" && authCode == "") {
		this.SetLaunched(true)
		return true
	}

	// if secrets.Password already set, dont need password and authcode
	if secrets.HasPassword() {
		ok := secrets.ValidateAuthCode(authCode)
		if !ok {
			this.AlertMsgf("launch failed, check authcode failed")
			this.SetLaunched(false)
			return false
		}
	} else {
		if password == "" || authCode == "" {
			this.Warnf("launching with empty password or authcode, be careful")
		}
		err := secrets.CheckPassword(password, authCode)
		if err != nil {
			this.AlertMsgf("launch failed, check password error: %s", err)
			this.SetLaunched(false)
			return false
		}
	}

	// 如果设置了 apiSecret，就解密
	// 但是并不是每一个 controller 都有 baseConfig ，比如 orderctrl
	baseConfig := this.GetBaseConfig()
	if baseConfig != nil {
		rawApiSecret := baseConfig.ApiSecret
		if rawApiSecret != "" {
			apiSecret, err := secrets.Decrypt(rawApiSecret)
			if err != nil {
				this.AlertMsgf("launch failed (%s), check password error: %s", err)
				this.SetLaunched(false)
				return false
			}
			this.apiSecret = apiSecret
		}
		rawWithdrawApiSecret := baseConfig.WithdrawApiSecret
		if rawWithdrawApiSecret != "" {
			withdrawApiSecret, err := secrets.Decrypt(rawWithdrawApiSecret)
			if err != nil {
				this.AlertMsgf("launch failed, decrypt withdraw api secret error: %s", err)
				this.SetLaunched(false)
				return false
			}
			this.withdrawApiSecret = withdrawApiSecret
		}
	} else {
		this.Warnf("base config is nil")
	}

	this.SetLaunched(true)
	this.Infof("controller launched")
	return true
}

func (this *BaseController) GetLogFilename() string {
	logFilename := this.ID
	if !this.standalone {
		logFilename = this.managerID
	}
	return logFilename
}

func (this *BaseController) GetConfigPath() string {
	return this.ConfigPath
}

func (this *BaseController) IsClosed() bool {
	return this.closed
}

func (this *BaseController) Close(closeFunc func()) {
	this.closed = true
	this.SetLaunched(false)
	this.Cron.Stop()
	if closeFunc != nil {
		closeFunc() // 在 RemoveChannel 前
	}
	this.Messenger.RemoveChannel(this.ID)
}

func (this *BaseController) Exit() {
	this.Infof("exit with doing nothing")
}

type CronTask struct {
	Spec            string
	WithoutExchange bool
	Cmd             func()
}

// debug 状态运行 和 定时任务，有多次运行防呆机制
func (this *BaseController) Run(tasks []CronTask) {
	if this.running {
		this.Warnf("controller already running.")
		return
	}

	// 开始运行策略
	this.Infof("controller run...")

	c := cron.New()

	for _, task := range tasks {
		taskFunc := task.Cmd
		c.AddFunc(task.Spec, func() {
			if this.Exchange != nil || task.WithoutExchange {
				taskFunc()
			}
		})
	}

	c.AddFunc("0 * * * *", this.snapshootTickerCache)
	c.AddFunc("0 */4 * * *", this.checkAPIKeyExpire)

	go func() {
		time.Sleep(20 * time.Second)
		this.checkAPIKeyExpire()
	}()

	this.Cron = c
	this.Infof("start cron...")

	go c.Run()
	this.running = true
}

func (this *BaseController) snapshootTickerCache() {
	if this.Exchange == nil {
		return
	}

	now := time.Now()
	tickers := this.Exchange.GetAllTickers()
	snapshot := map[string]any{
		"time":    &now,
		"tickers": tickers,
	}

	archivePath := path.Join(this.GetConfigPath(), fmt.Sprintf("%s_ticker_cache.json", this.ID))
	f, err := os.OpenFile(archivePath, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0755)
	if err != nil {
		this.Errorf("open tickers file err: %s", err)
		return
	}
	defer f.Close()

	if data, err := json.Marshal(snapshot); err != nil {
		this.Errorf("save tickers failed, marshal error: %s", err)
		return
	} else {
		f.Write(data)
		_, err := f.WriteString("\n")
		if err != nil {
			this.Errorf("log tickers, write file error: %s", err)
		}
	}
}

func (this *BaseController) CheckSymbolPrefixAllowed(prefix string) bool {
	if utils.SliceContains([]string{"USDT", "USD", "CNY", "HKD"}, prefix) {
		return true
	}

	if strings.EqualFold(prefix, this.GetBaseConfig().USDXSymbol) {
		return true
	}

	prefixValid := false
	allowedPrefixes := this.GetBaseConfig().AllowedSymbolPrefixs
	for _, p := range allowedPrefixes {
		if strings.EqualFold(p, "*") {
			prefixValid = true
		} else {
			if strings.EqualFold(p, prefix) {
				prefixValid = true
			}
		}
	}
	return prefixValid
}

func (this *BaseController) CheckExchangeReady(sendMsg bool) bool {
	if this.Exchange == nil {
		if sendMsg {
			this.ErrorMsgf("交易所还没有初始化。")
		}
		return false
	}
	return true
}

func (this *BaseController) GetCommandProcessor() *command.CommandProcessor {
	return this.commandProcessor
}

func (this *BaseController) SetExchangeProxyLoop(proxyGateway *pgate.ProxyGateway) {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		if this.Exchange == nil {
			continue
		}

		proxy := this.GetBaseConfig().ProxyUrl
		// slack cmds 设置的 url 可能是 "<pgate://group/tk>"，需要去掉 "<" 和 ">"
		// config 模块并不容易解决这个问题，因此在这里做兼容
		if strings.HasPrefix(proxy, "<") && strings.HasSuffix(proxy, ">") {
			proxy = strings.TrimPrefix(proxy, "<")
			proxy = strings.TrimSuffix(proxy, ">")
		}

		if strings.HasPrefix(proxy, "pgate://") {
			if proxyGateway == nil {
				this.Errorf("set pgate proxy, proxyGateway is nil")
				continue
			}

			var err error
			proxy, err = proxyGateway.ParseLocalProxy(proxy)
			if err != nil {
				if strings.Contains(err.Error(), "invalid url") {
					this.Warnf("set pgate proxy, parse proxy failed, error: %s", err)
				} else if strings.Contains(err.Error(), "group forwarder not ready") {
					this.Infof("set pgate proxy, group forwarder not ready, wait for retry, error: %s", err)
				} else {
					this.Errorf("set pgate proxy, unexpected error parsing pgate proxy: %s", err)
				}
				continue
			}
		}

		if proxy == "" {
			this.Exchange.RemoveProxy()
		} else {
			this.Exchange.SetProxy(proxy)
		}
	}
}

func (this *BaseController) SetAccountMarginMode(instrumentType exchange.InstrumentType, mode exchange.AccountMarginMode) bool {
	if this.Exchange == nil {
		return false
	}
	if cfg, err := this.Exchange.GetAccountConfig(instrumentType); err != nil {
		this.ErrorMsgf("get account config failed, error: %s", err)
		return false
	} else {
		if cfg.MarginMode == mode {
			return true
		}
		// 不一致则尝试修改，有的交易所可 API 设置，有的需人工设置
		if err := this.Exchange.SetAccountMarginMode(instrumentType, mode); err != nil {
			this.ErrorMsgf("set account margin mode failed, error: %s", err)
			return false
		} else {
			return true
		}
	}
}

func (this *BaseController) GetAccountBalances(allSymbols bool) (balances exchange.AccountBalanceList, er error) {
	if this.Exchange == nil {
		return nil, fmt.Errorf("exchange is nil")
	}
	filterFunc := this.CheckSymbolPrefixAllowed
	if allSymbols {
		filterFunc = nil
	}
	if bs, err := this.Exchange.GetAccountBalancesValidCoins(this.RefID, this.GetInstrumentTypes(), filterFunc); err != nil {
		er = fmt.Errorf("get account balance failed，error: %s", err)
		return
	} else {
		balances = bs
	}
	for _, balance := range balances {
		if balance.Total == 0 {
			continue
		}
		if p, err := this.Exchange.GetCurrencyPrice(balance.Currency, 10*time.Second); err != nil {
			this.Errorf("get currency price failed, error: %s", err)
		} else {
			balance.PriceInUSDT = p
			balance.WorthInUSDT = balance.Total * p
		}
	}
	return
}

func (this *BaseController) GetAccountWorth(allSymbols bool) (qtyInUSDT float64, er error) {
	if this.Exchange == nil {
		return 0, fmt.Errorf("exchange is nil")
	}
	filterFunc := this.CheckSymbolPrefixAllowed
	if allSymbols {
		filterFunc = nil
	}
	qtyInUSDT, er = this.Exchange.GetAccountWorth(this.RefID, this.GetInstrumentTypes(), AccountWorthCacheTime, filterFunc)
	return
}

func (this *BaseController) GetAPIExpireTime() (t *time.Time, er error) {
	if this.Exchange == nil {
		return nil, nil
	}
	return this.Exchange.GetAPIExpireTime()
}

func (this *BaseController) checkAPIKeyExpire() {
	if t, err := this.GetAPIExpireTime(); err != nil {
		this.WarnMsgf("获取 API key 过期时间失败，error: %s", err)
	} else if t != nil {
		if t.Before(time.Now()) {
			this.ErrorMsgf("API key 已过期，请即时更新。")
		} else if t.Before(time.Now().Add(15 * 24 * time.Hour)) {
			this.WarnMsgf("API key 将于 %s 过期，请即时更新。", utils.FormatShortTimeStr(t, true))
		}
	}
}

func (this *BaseController) FormatAPIExpireTime() string {
	apiExpireTimeStr := ""
	apiExpireTime, err := this.GetAPIExpireTime()
	if err != nil {
		apiExpireTimeStr = fmt.Sprintf("<error: %s>", err)
	} else if apiExpireTime != nil {
		apiExpireTimeStr = utils.FormatShortTimeStr(apiExpireTime, true)
		// display minus how many days
		days := time.Since(*apiExpireTime).Hours() / 24
		apiExpireTimeStr = fmt.Sprintf("%s (%d days)", apiExpireTimeStr, int(days))

		// 如果交易所是 hyperliquid，并且是主账户代理，则显示 "<MainWallet::LongTerm>"
		if this.Exchange.GetName() == exchange.Hyperliquid {
			aYearLater := time.Now().Add(365 * 24 * time.Hour)
			if apiExpireTime.After(aYearLater) {
				apiExpireTimeStr = "<MainWallet::NoExpire>"
			}
		}
	}
	return apiExpireTimeStr
}

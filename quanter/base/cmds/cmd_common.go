package cmds

import (
	"fmt"
	"strings"
	"time"

	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type PriceTriggersCommand struct {
	command.Command
	controller base.Controllable
	storage    base.Storager
}

func NewPriceTriggersCommand(controller base.Controllable) *PriceTriggersCommand {
	cmd := &PriceTriggersCommand{
		Command: command.Command{
			Name:                   "priceTriggers",
			Alias:                  []string{"pt"},
			Instruction:            "`.priceTriggers print(默认)/add/delete` 打印/添加/删除价格触发，添加： `add {SymbolCode} >或< {Price}`，删除：`delete {ID}`",
			RequiresConfirm:        true,
			SkipConfirmSubcommands: []string{"print"},
			DefaultSubcommand:      "print",
			ArgMin:                 1,
			ArgMax:                 4,
			AuthcodePos:            -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *PriceTriggersCommand) Do() bool {
	return DoPriceTriggersCommand(this.Command, this.controller)
}

func DoPriceTriggersCommand(cmd command.Command, controller base.Controllable) bool {
	ex := controller.GetExchange()
	if ex == nil {
		cmd.ErrorMsgf("controller %s 未设置 exchange", controller.GetID())
		return false
	}
	storage := controller.GetStorage()
	if storage == nil {
		cmd.ErrorMsgf("controller %s 未设置 storage", controller.GetID())
		return false
	}

	op := strings.ToLower(cmd.Args[0])
	if op == "add" {
		if len(cmd.Args) < 4 {
			cmd.ErrorMsgf("缺少指定的参数")
			return false
		}

		var symbolCode *exchange.SymbolCode
		var symbol string
		if code, err := controller.NewSymbolCode(cmd.Args[1]); err != nil {
			cmd.ErrorMsgf("解析品种代码错误，error: %s", err)
			return false
		} else {
			symbolCode = code
		}
		instrumentType := symbolCode.InstrumentType()
		if instrumentType == exchange.Spot {
			if spotSymbol, err := ex.TranslateSymbolCodeToSpotSymbol(symbolCode); err != nil {
				cmd.ErrorMsgf("交易所中没有品种代码对应的现货，error: %s", err)
				return false
			} else {
				symbol = spotSymbol
			}
		} else if instrumentType.IsFuture() {
			if futureSymbol, err := ex.TranslateSymbolCodeToFutureSymbol(symbolCode); err != nil {
				cmd.ErrorMsgf("交易所中没有品种代码对应的合约，error: %s", err)
				return false
			} else {
				symbol = futureSymbol
			}
		} else {
			cmd.ErrorMsgf("不支持品种代码：%s", cmd.Args[1])
			return false
		}

		var direction exchange.TriggerDirection
		if cmd.Args[2] == "&gt;" {
			direction = exchange.TriggerDirectionHigher
		} else if cmd.Args[2] == "&lt;" {
			direction = exchange.TriggerDirectionLower
		} else {
			cmd.ErrorMsgf("参数错误：方向只能为 > 或 <")
			return false
		}

		price, _, err := utils.ParseFloatOrPercentage(cmd.Args[3], false, false)
		if err != nil {
			cmd.ErrorMsgf("解析价格出错，error:%s", err)
			return false
		}

		priceTrigger := &exchange.PriceTrigger{
			ID:             exchange.NewRandomID(),
			InstrumentType: symbolCode.InstrumentType(),
			Symbol:         symbol,
			SymbolCode:     symbolCode,
			Price:          price,
			Direction:      direction,
			Source:         "manual",
		}

		if er := ex.RegisterPriceTrigger(priceTrigger); er != nil {
			cmd.ErrorMsgf("添加 PriceTrigger 错误：%s", er)
			return false
		}

		storage.AddPriceTrigger(priceTrigger)
		cmd.SendMsgf("添加成功")
	}

	priceTriggers := ex.GetPriceTriggers()
	if len(priceTriggers) == 0 && op == "print" {
		cmd.SendMsgf("no PriceTriggers now")
		return true
	}

	if op == "delete" {
		if len(cmd.Args) < 2 {
			cmd.ErrorMsgf("缺少指定的参数")
		}
		id := cmd.Args[1]
		for _, p := range priceTriggers {
			if p.ID == id && p.Source == "manual" {
				if err := ex.DeletePriceTrigger(p.ID); err == nil {
					storage.DeletePriceTrigger(p.ID)
					cmd.SendMsgf("删除成功")
				} else {
					cmd.ErrorMsgf("删除失败：%s", err)
				}
				return true
			}
		}
		cmd.ErrorMsgf("没有找到 %s 对应的 manual PriceTrigger", id)
		return false
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "InstrumentType", "Symbol", "Price", "TickerPrice", "Delta %", "Direction", "Type", "Triggered", "LastCheckTime", "Source"})
	for _, p := range priceTriggers {
		tickerPrice := 0.0
		ticker, _ := ex.GetTicker(p.InstrumentType, p.Symbol)
		delta := "-"
		if ticker != nil && ticker.Close > 0 {
			tickerPrice = ticker.Close
			delta = fmt.Sprintf("%.2f%%", 100*(p.Price/tickerPrice-1))
		}
		triggered := "-"
		if p.Triggered {
			triggered = "Yes"
		}
		t.AddRow([]string{
			p.ID,
			string(p.InstrumentType),
			p.Symbol,
			ex.FormatPrice(p.InstrumentType, p.Symbol, p.Price),
			ex.FormatPrice(p.InstrumentType, p.Symbol, tickerPrice),
			delta,
			string(p.Direction),
			string(p.Type),
			triggered,
			utils.FormatShortTimeStr(p.LastCheckTime, false),
			p.Source,
		})
	}
	cmd.SendMsgf("```%s```", t.Render())

	if !controller.GetBaseConfig().EnableRealtimePrice {
		cmd.WarnMsgf("当前无实时价格，需设置 `.sc EnableRealtimePrice=true` 启用")
	}
	return true
}

type PriceWatchCommand struct {
	command.Command
	controller base.Controllable
	storage    base.Storager
}

func NewPriceWatchCommand(controller base.Controllable) *PriceWatchCommand {
	cmd := &PriceWatchCommand{
		Command: command.Command{
			Name:                   "priceWatch",
			Alias:                  []string{"pw"},
			Instruction:            "`.priceWatch print(默认)/add/delete SymbolCode` 打印/添加/删除价格追踪",
			RequiresConfirm:        false,
			SkipConfirmSubcommands: []string{"print"},
			DefaultSubcommand:      "print",
			ArgMin:                 1,
			ArgMax:                 2,
			AuthcodePos:            -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *PriceWatchCommand) Do() bool {
	return DoPriceWatchCommand(this.Command, this.controller)
}

func DoPriceWatchCommand(cmd command.Command, controller base.Controllable) bool {
	ex := controller.GetExchange()
	if ex == nil {
		cmd.ErrorMsgf("controller %s 未设置 exchange", controller.GetID())
		return false
	}
	storage := controller.GetStorage()
	if storage == nil {
		cmd.ErrorMsgf("controller %s 未设置 storage", controller.GetID())
		return false
	}

	op := "print"
	var symbolCode *exchange.SymbolCode
	var symbol string
	if len(cmd.Args) > 0 {
		op = strings.ToLower(cmd.Args[0])
		if op == "add" || op == "delete" {
			if len(cmd.Args) < 2 {
				cmd.ErrorMsgf("缺少指定的参数")
				return false
			}
			if code, err := controller.NewSymbolCode(cmd.Args[1]); err != nil {
				cmd.ErrorMsgf("解析品种代码错误，error: %s", err)
				return false
			} else {
				symbolCode = code
			}
			instrumentType := symbolCode.InstrumentType()
			if instrumentType == exchange.Spot {
				if spotSymbol, err := ex.TranslateSymbolCodeToSpotSymbol(symbolCode); err != nil {
					cmd.ErrorMsgf("交易所中没有品种代码对应的现货，error: %s", err)
					return false
				} else {
					symbol = spotSymbol
				}
			} else if instrumentType.IsFuture() {
				if futureSymbol, err := ex.TranslateSymbolCodeToFutureSymbol(symbolCode); err != nil {
					cmd.ErrorMsgf("交易所中没有品种代码对应的合约，error: %s", err)
					return false
				} else {
					symbol = futureSymbol
				}
			} else {
				cmd.ErrorMsgf("不支持品种代码：%s", cmd.Args[1])
				return false
			}
		}
	}

	if op == "add" {
		watch := &exchange.PriceWatch{
			InstrumentType: symbolCode.InstrumentType(),
			Symbol:         symbol,
			SymbolCode:     *symbolCode,
		}

		if er := ex.AddPriceWatch(watch); er != nil {
			cmd.ErrorMsgf("添加错误：%s", er)
			return false
		}

		storage.AddPriceWatch(watch)
		cmd.SendMsgf("添加成功")
	}

	priceWatches := storage.GetPriceWatches()
	priceTriggers := ex.GetPriceTriggers()
	// PriceTriggers 的也同时打印
	if len(priceWatches) == 0 && len(priceTriggers) == 0 && op == "print" {
		cmd.SendMsgf("no PriceWatches now")
		return true
	}

	if op == "delete" {
		id := cmd.Args[1]
		for _, p := range priceWatches {
			if p.SymbolCode.Code == symbolCode.Code {
				if err := ex.DeletePriceWatch(symbolCode); err == nil {
					storage.DeletePriceWatch(symbolCode)
					cmd.SendMsgf("删除成功")
				} else {
					cmd.ErrorMsgf("删除失败：%s", err)
				}
				return true
			}
		}
		cmd.ErrorMsgf("没有找到 %s 对应的 PriceWatch", id)
		return false
	}

	cmd.SendMsgf(RenderWatches(controller))

	if !controller.GetBaseConfig().EnableRealtimePrice {
		cmd.WarnMsgf("当前无实时价格，需设置 `.sc EnableRealtimePrice=true` 启用")
	}
	return true
}

func RenderWatches(controller base.Controllable) string {
	ex := controller.GetExchange()
	t := exchange.NewTable()
	priceWatches := controller.GetStorage().GetPriceWatches()
	priceTriggers := ex.GetPriceTriggers()
	t.SetHeader([]string{"InstrumentType", "SymbolCode", "Symbol", "TickerPrice", "TickerTime", "HttpPrice"})
	printSymbols := map[string]bool{}
	for _, p := range priceWatches {
		t.AddRow(renderWatchRow(ex, p.InstrumentType, &p.SymbolCode, p.Symbol))
		printSymbols[fmt.Sprintf("%s_%s", p.InstrumentType, p.Symbol)] = true
	}
	for _, p := range priceTriggers {
		if printSymbols[fmt.Sprintf("%s_%s", p.InstrumentType, p.Symbol)] {
			continue
		}
		printSymbols[fmt.Sprintf("%s_%s", p.InstrumentType, p.Symbol)] = true
		t.AddRow(renderWatchRow(ex, p.InstrumentType, p.SymbolCode, p.Symbol))
	}
	if len(t.Rows) == 1 {
		return "no PriceWatches"
	}
	return fmt.Sprintf("```%s```", t.Render())
}

func renderWatchRow(ex exchange.Exchange, instrumentType exchange.InstrumentType, symbolCode *exchange.SymbolCode, symbol string) []string {
	tickerPrice := 0.0
	ticker, _ := ex.GetTicker(instrumentType, symbol)
	tickerTime := ""
	if ticker != nil && ticker.Close > 0 {
		tickerPrice = ticker.Close
		t := time.Unix(ticker.Time/1000, 0)
		tickerTime = utils.FormatShortTimeStr(&t, false)
	}
	price, _ := ex.GetLastPrice(instrumentType, symbol, false)
	code := ""
	if symbolCode != nil {
		code = symbolCode.String()
	}
	return []string{
		string(instrumentType),
		code,
		symbol,
		ex.FormatPrice(instrumentType, symbol, tickerPrice),
		tickerTime,
		ex.FormatPrice(instrumentType, symbol, price),
	}
}

func DoFundingHistoryCommand(cmd command.Command, controller base.Controllable, ex exchange.Exchange) bool {
	futureCodeStr := "all"
	isDaily := false
	latestOnly := true
	if len(cmd.Args) > 0 {
		futureCodeStr = cmd.Args[0]
	}
	if len(cmd.Args) >= 2 && strings.EqualFold(strings.ToLower(cmd.Args[1]), "history") {
		latestOnly = false
	}
	if len(cmd.Args) == 3 && strings.EqualFold(strings.ToLower(cmd.Args[2]), "daily") {
		isDaily = true
	}
	combinedHis := []exchange.FundingHistory{}
	uSymbol := controller.GetBaseConfig().USDXSymbol

	if strings.Contains("*/all", futureCodeStr) {
		instrumentType := controller.GetBaseConfig().FutureInstrumentType
		if futures, err := ex.GetTradablePairs(instrumentType, uSymbol); err != nil {
			cmd.ErrorMsgf("获取合约列表出错：%s", err)
			return false
		} else {
			for _, futureSymbol := range futures {
				// 如果没有指定特定品种
				for _, coin := range controller.GetBaseConfig().AllowedSymbolPrefixs {
					if _, futureCode, err := ex.TranslateFutureSymbol(instrumentType, futureSymbol, uSymbol); err == nil {
						// 如果 futureSymbol 是允许的品种，则获取其资金费率
						if strings.EqualFold(futureCode.Coin(), coin) && futureCode.IsPerp() {
							if his, err := ex.GetFundingHistory(futureCode, latestOnly, isDaily); err != nil {
								cmd.ErrorMsgf("获取 %s 的资金费率出错：%s", futureSymbol, err)
								continue
							} else {
								combinedHis = append(combinedHis, his...)
							}
						}
					} else {
						if !strings.Contains(err.Error(), "invalid future code") {
							cmd.WarnMsgf("获取资金费率历史，内部错误：%s", err)
						}
					}
				}

			}
		}
	} else {
		futureCode, err := exchange.NewSymbolCode(futureCodeStr, uSymbol)
		if err != nil {
			cmd.ErrorMsgf("不合法的合约代码，code: %s, error: %s", futureCodeStr, err)
			return false
		}
		if his, err := ex.GetFundingHistory(futureCode, latestOnly, isDaily); err != nil {
			cmd.ErrorMsgf("获取 %s 的资金费率出错：%s", futureCodeStr, err)
			return false
		} else {
			combinedHis = append(combinedHis, his...)
		}
	}

	if len(combinedHis) == 0 {
		cmd.SendMsgf("没有查询到 %s 品种的资金费率。", futureCodeStr)
		return true
	} else {
		t := exchange.NewTable()
		t.SetHeader([]string{"Future Code", "Symbol", "Funding Rate", "Funding Time", "Period"})
		for _, his := range combinedHis {
			t.AddRow([]string{
				his.Code.String(),
				his.Symbol,
				fmt.Sprintf("%.4f%%", his.Rate*100),
				utils.FormatShortTimeStr(&his.Time, false),
				his.Period,
			})
		}
		if latestOnly {
			cmd.SendMsgf("资金费率：\n```%s```", t.Render())
		} else {
			cmd.SendFileMessage("资金费率", t.Render(), "")
		}

		return true
	}
}

package cmds

import (
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"os"
	"os/exec"
	"path"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"

	"github.com/stevedomin/termtable"
)

const BINARY_FILE_PREFIX = "qt_"
const BINARY_FILE_MAX_NUM = 20

// 发布二进制新版本

// 和 release 相关的函数实现到该结构体上
// 原因：
// 1、不希望相关的函数污染 Turtle、TurtleController 的 API
// 2、也不希望相关的函数污染 turtle 包的 API，即成为 turtle 包的独立函数
// 3、ReleaseCommand 等已经和 TurtleController 解耦，因此对应的函数无法实现在其他对象上
type ReleaserCommand struct {
	command.Command
	releaseBinaryDirPath string // 在上层保证该目录可写，该模块均不做额外检查
	binaryFileName       string // 如果是由 TurtleController 初始化，传入 turtleID；如果是 QuanterManager 初始化，传入 "quanter"
}

// 通过 type alias 无法在 ReleaseCommand 上访问 ReleaserCommand 上实现的函数
// 只能通过嵌套的方法才能达到目的
type ReleaseCommand struct {
	ReleaserCommand
}

func NewReleaseCommand(releaseBinaryDirPath string, binaryFileName string) *ReleaseCommand {
	if releaseBinaryDirPath == "" {
		releaseBinaryDirPath = "./releases"
	}
	cmd := &ReleaseCommand{
		ReleaserCommand: ReleaserCommand{
			Command: command.Command{
				Name:            "release",
				Instruction:     "`.release MD5 logs` 发布新版本，需在同一消息中上传二进制文件",
				RequiresConfirm: true,
				ArgMin:          3,
				ArgMax:          math.MaxInt32,
				RequiresFile:    true,
			},
			releaseBinaryDirPath: releaseBinaryDirPath,
			binaryFileName:       binaryFileName,
		},
	}
	return cmd
}

func (this *ReleaseCommand) Prepare() bool {
	md5Str := this.Args[0]
	downloadURL := this.Args[len(this.Args)-1]
	if ok := this.saveReleaseBinary(md5Str, downloadURL); !ok {
		return false
	}
	return true
}

func (this *ReleaseCommand) Do() bool {
	logs := ""
	// logs 中间空格可能会占多个位置
	for _, log := range this.Args[1 : len(this.Args)-1] {
		logs += log + " "
	}
	this.releaseBinary(this.Args[0], logs)
	return true
}

// 查看历史版本

type ListVersionCommand struct {
	ReleaserCommand
}

func NewListVersionCommand(releaseBinaryDirPath string, binaryFileName string) *ListVersionCommand {
	cmd := &ListVersionCommand{
		ReleaserCommand: ReleaserCommand{
			Command: command.Command{
				Name:            "listVersion",
				Instruction:     "`.listVersion` 查看历史版本",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			releaseBinaryDirPath: releaseBinaryDirPath,
			binaryFileName:       binaryFileName,
		},
	}
	return cmd
}

func (this *ListVersionCommand) Do() bool {
	files, err := os.ReadDir(this.releaseBinaryDirPath)
	if err != nil {
		this.SendMsgf("无历史版本")
		return true
	}

	sort.Slice(files, func(i, j int) bool {
		infoX, _ := files[i].Info()
		infoY, _ := files[j].Info()
		return infoY.ModTime().Before(infoX.ModTime())
	})

	fileNames := []string{}
	for _, file := range files {
		if !strings.HasPrefix(file.Name(), BINARY_FILE_PREFIX) {
			continue
		}
		fileNames = append(fileNames, file.Name())
	}
	if len(fileNames) == 0 {
		this.SendMsgf("无历史版本")
	} else {
		logs, _ := this.readReleaseLogs()
		this.SendFileMessage("历史版本", getReleaseLogsTable(fileNames, logs), "")
	}

	return true
}

func getReleaseLogsTable(fileNames []string, logs map[string]string) string {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      3,
		UseSeparator: false,
	})
	headers := []string{"FileName", "Logs"}
	t.SetHeader(headers)
	for _, name := range fileNames {
		row := []string{
			name,
			logs[name],
		}
		t.AddRow(row)
	}
	return t.Render()
}

// 切换到指定的二进制版本

type UseVersionCommand struct {
	ReleaserCommand
}

func NewUseVersionCommand(releaseBinaryDirPath string, binaryFileName string) *UseVersionCommand {
	cmd := &UseVersionCommand{
		ReleaserCommand: ReleaserCommand{
			Command: command.Command{
				Name:            "useVersion",
				Instruction:     "`.useVersion filename` 切换到指定版本",
				RequiresConfirm: true,
				ArgMin:          1,
				ArgMax:          1,
			},
			releaseBinaryDirPath: releaseBinaryDirPath,
			binaryFileName:       binaryFileName,
		},
	}
	return cmd
}

func (this *UseVersionCommand) Prepare() bool {
	filename := this.Args[0]
	return this.checkVersion(filename)
}

func (this *UseVersionCommand) Do() bool {
	this.useVersion(this.Args[0])
	return true
}

// 和 ReleaseCommand 和 RevertVersionCommand 相关的具体实现

func (this *ReleaserCommand) checkVersion(filename string) bool {
	filePath := fmt.Sprintf("%s/%s", this.releaseBinaryDirPath, filename)
	if _, err := os.Stat(filePath); err != nil {
		this.ErrorMsgf("版本 %s 不存在", filename)
		return false
	}

	cmd := exec.Command(fmt.Sprintf("%s/%s", this.releaseBinaryDirPath, filename), "-version")
	buildInfo, err := cmd.CombinedOutput()
	if err != nil {
		this.ErrorMsgf("获取版本信息错误: %s", err)
		return false
	}

	this.SendMsgf("切换版本信息: %s", buildInfo)
	return true
}

func (this *ReleaserCommand) saveReleaseBinary(md5Str, url string) bool {
	this.SendMsgf("文件下载中，请稍等...")
	fileData, err := this.GetFile(url)
	if err != nil {
		this.ErrorMsgf("文件下载失败: %s", err)
		return false
	}

	_md5 := fmt.Sprintf("%x", md5.Sum(fileData))
	if !strings.EqualFold(strings.ToUpper(_md5), strings.ToUpper(md5Str)) {
		this.ErrorMsgf("MD5 对比错误 <%s> != <%s>", _md5, md5Str)
		return false
	}

	if err := os.WriteFile(fmt.Sprintf("%s/%s", this.releaseBinaryDirPath, md5Str), fileData, 0755); err != nil {
		this.ErrorMsgf("文件保存失败: %s", err)
		return false
	}

	this.SendMsgf("MD5 对比正确，文件已保存")
	return true
}

func (this *ReleaserCommand) releaseBinary(md5Str, logs string) {
	md5FilePath := fmt.Sprintf("%s/%s", this.releaseBinaryDirPath, md5Str)

	// 运行 version 获取 githash
	cmd := exec.Command(md5FilePath, "-version")
	buildInfo, err := cmd.CombinedOutput()
	if err != nil {
		this.AlertMsgf("运行程序获取版本号出错: %s", err)
		return
	}

	githash := ""
	buildTime := ""
	re := regexp.MustCompile(`^Build: (.+)/\((.+)\)`)
	reStrs := re.FindStringSubmatch(string(buildInfo))
	if len(reStrs) == 3 {
		githash = reStrs[1]
		buildTime = reStrs[2]
	} else {
		this.AlertMsgf("解析程序版本号出错: %s", buildInfo)
		return
	}

	binaryFileName := fmt.Sprintf("%s%s_%s", BINARY_FILE_PREFIX, githash, buildTime)
	if err := copyFile(
		md5FilePath,
		fmt.Sprintf("%s/%s", this.releaseBinaryDirPath, binaryFileName),
	); err != nil {
		this.AlertMsgf("保存新版本程序出错: %s", err)
		return
	}

	if err := os.Rename(
		md5FilePath,
		this.binaryFileName,
	); err != nil {
		this.AlertMsgf("覆盖主程序失败: %s", err)
		return
	}

	this.addReleaseLogs(binaryFileName, logs)
	this.removeOldReleaseBinary()
	this.SendMsgf("重启新版本程序 %s", buildInfo)

	time.Sleep(time.Second * 2)
	os.Exit(0)
}

func (this *ReleaserCommand) readReleaseLogs() (map[string]string, error) {
	f, _ := os.OpenFile(fmt.Sprintf("%s/logs.json", this.releaseBinaryDirPath), os.O_RDWR|os.O_CREATE, 0755)
	defer f.Close()
	file, err := io.ReadAll(f)
	if err != nil {
		zlog.Errorf("read log file error: %s", err.Error())
	}
	logs := map[string]string{}
	err = json.Unmarshal(file, &logs)
	if string(file) != "" && err != nil {
		zlog.Errorf("read release logs error: %s", err)
		return logs, errors.New("read release logs error")
	}
	return logs, nil
}

func (this *ReleaserCommand) addReleaseLogs(binaryFileName, logs string) {
	releaseLogs, err := this.readReleaseLogs()
	if err != nil {
		return
	}
	releaseLogs[binaryFileName] = logs
	this.saveReleaseLogs(releaseLogs)
}

func (this *ReleaserCommand) saveReleaseLogs(releaseLogs map[string]string) {
	data, _ := json.MarshalIndent(releaseLogs, "", "    ")
	if err := os.WriteFile(fmt.Sprintf("%s/logs.json", this.releaseBinaryDirPath), data, 0755); err != nil {
		this.ErrorMsgf("logs 保存失败: %s", err)
	}
}

func (this *ReleaserCommand) removeOldReleaseBinary() {
	files, err := os.ReadDir(this.releaseBinaryDirPath)
	if err != nil {
		return
	}

	sort.Slice(files, func(i, j int) bool {
		infoX, _ := files[i].Info()
		infoY, _ := files[j].Info()
		return infoY.ModTime().Before(infoX.ModTime())
	})

	releaseLogs, err := this.readReleaseLogs()
	if err != nil {
		return
	}

	for i, file := range files {
		if !strings.HasPrefix(file.Name(), BINARY_FILE_PREFIX) {
			continue
		}
		if i >= BINARY_FILE_MAX_NUM {
			if err := os.Remove(path.Join(this.releaseBinaryDirPath, file.Name())); err != nil {
				this.AlertMsgf("删除旧版本程序失败: %s", err)
				return
			}
			delete(releaseLogs, file.Name())
		}
	}

	this.saveReleaseLogs(releaseLogs)
}

func (this *ReleaserCommand) useVersion(filename string) {
	tmpFile := fmt.Sprintf("revert_%s", time.Now())
	if err := copyFile(
		path.Join(this.releaseBinaryDirPath, filename),
		tmpFile,
	); err != nil {
		this.AlertMsgf("拷贝程序失败: %s", err)
		return
	}

	if err := os.Rename(
		tmpFile,
		this.binaryFileName,
	); err != nil {
		this.AlertMsgf("覆盖主程序失败: %s", err)
		return
	}

	this.SendMsgf("切换版本 %s\n程序重启中...", filename)
	time.Sleep(time.Second * 2)
	os.Exit(0)
}

func copyFile(source, dest string) error {
	input, err := os.ReadFile(source)
	if err != nil {
		return err
	}

	err = os.WriteFile(dest, input, 0755)
	if err != nil {
		return err
	}

	return nil
}

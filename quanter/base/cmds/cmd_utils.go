package cmds

import (
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/stack"
	"github.com/wizhodl/quanter/utils"
)

type DeleteErrorsCommand struct {
	command.Command
}

func NewDeleteErrorsCommand() *DeleteErrorsCommand {
	cmd := &DeleteErrorsCommand{
		Command: command.Command{
			Name:            "deleteErrors",
			Alias:           []string{"delErr"},
			Instruction:     "`.deleteErrors beforeTime/now/all` 删除报警消息",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
	}
	return cmd
}

func (this *DeleteErrorsCommand) Do() bool {
	args := this.GetArgs()
	beforeTimeStr := args[0]
	beforeTimeStr = strings.ToLower(beforeTimeStr)

	var afterTime *time.Time
	if strings.EqualFold(beforeTimeStr, "all") {
		beforeTimeStr = "now"
		afterTime = utils.Ptr(time.Now().Add(-time.Hour * 24 * 365))
	}
	beforeTime := utils.ParseTimeBeijing(beforeTimeStr)
	if beforeTime == nil {
		this.ErrorMsgf("解析时间失败，error: parse beijing time failed, time: %s", beforeTimeStr)
		return false
	}
	if afterTime != nil {
		this.SendMsgf("删除 [%s ~ %s] 之间的报警消息...", utils.FormatShortTimeStr(afterTime, true), utils.FormatShortTimeStr(beforeTime, true))
	} else {
		this.SendMsgf("删除 %s 之前的报警消息...", utils.FormatShortTimeStr(beforeTime, true))
	}
	count, err := this.Processor.Responder.DeleteMsgs([]string{}, *beforeTime, afterTime)
	if err != nil && (strings.Contains(err.Error(), "get channel id failed") || strings.Contains(err.Error(), "cleaner client is nil")) {
		this.ErrorMsgf("删除报警消息失败，error: %s", err)
		return false
	}
	this.SendMsgf("删除报警消息成功，共删除 %d 条消息", count)
	return true
}

// 查询日志中 StackID 对应的 stacktrace

type StackTraceControllerCommand struct {
	command.Command
}

func NewStackTraceCommand() *StackTraceControllerCommand {
	cmd := &StackTraceControllerCommand{
		Command: command.Command{
			Name:            "stack",
			Alias:           []string{"st"},
			Instruction:     "`.stack StackID` 查询日志中 StackID 对应的 StackTrace",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
	}
	return cmd
}

func (this *StackTraceControllerCommand) Do() bool {
	_, stackTrace := stack.QueryStack(this.Args[0])
	if stackTrace == nil {
		this.ErrorMsgf("没有查询到 %s 对应的 Stack Trace", this.Args[0])
		return false
	} else {
		this.SendMsgf("```%s```", stackTrace)
		return true
	}
}

// 打印日志命令

type BaseLogCommand struct {
	command.Command
	LogDirPath  string
	LogFilename string
}

type LogCommand struct {
	BaseLogCommand
}

func NewLogCommand(logDirPath string, logFilename string) *LogCommand {
	cmd := &LogCommand{
		BaseLogCommand: BaseLogCommand{
			Command: command.Command{
				Name:            "log",
				Alias:           []string{},
				Instruction:     "`.log numOfLines（可选，默认 200 行）` 打印日志的最后 numOfLines 行",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			LogDirPath:  logDirPath,
			LogFilename: logFilename,
		},
	}
	return cmd
}

func (this *LogCommand) Do() bool {
	numOfLines := 200
	if len(this.Args) == 1 {
		_numOfLines, err := strconv.ParseInt(this.Args[0], 10, 32)
		if err == nil {
			numOfLines = int(_numOfLines)
		} else {
			this.ErrorMsgf("numOfLines 必须是一个数字")
		}
	}

	if numOfLines > 10000 {
		numOfLines = 10000
		this.WarnMsgf("最大可获取 10000 行日志")
	}

	//  如果以非 standalone 模式运行，以 manager 的名字读取 log
	filename := this.LogFilename
	logContent, err := utils.ReadLog(this.LogDirPath, filename, numOfLines)
	if err == nil {
		if logContent != "" {
			this.SendFileMessage("调试日志", logContent, "")
		} else {
			this.SendMsgf("日志文件为空")
		}
		return true
	} else {
		this.ErrorMsgf("读取日志文件失败，Error: %s", err.Error())
		return false
	}
}

type DownloadLogCommand struct {
	BaseLogCommand
}

func NewDownloadLogCommand(logDirPath string, logFilename string) *DownloadLogCommand {
	cmd := &DownloadLogCommand{
		BaseLogCommand: BaseLogCommand{
			Command: command.Command{
				Name:            "downloadLog",
				Alias:           []string{},
				Instruction:     "`.downloadLog index（可选 0~20，默认 0）` 下载日志文件，index 为分片号",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			LogDirPath:  logDirPath,
			LogFilename: logFilename,
		},
	}
	return cmd
}

func (this *DownloadLogCommand) Do() bool {
	indexNum := 0
	if len(this.Args) == 1 {
		index, err := strconv.ParseInt(this.Args[0], 10, 32)
		if err == nil {
			indexNum = int(index)
			if indexNum < 0 || indexNum > 20 {
				this.ErrorMsgf("index 取值为 0 ~ 20 ")
			}
		} else {
			this.ErrorMsgf("index 必须是一个数字")
		}
	}

	dirPath := "./logs"
	if this.LogDirPath != "" {
		dirPath = this.LogDirPath
	}

	logName := this.LogFilename + ".log"

	if indexNum > 0 {
		// rotate 格式不固定，按时间顺序读取
		files, err := os.ReadDir(dirPath)
		if err != nil {
			this.SendMsgf("无日志文件")
			return true
		}

		sort.Slice(files, func(i, j int) bool {
			infoX, _ := files[i].Info()
			infoY, _ := files[j].Info()
			return infoY.ModTime().Before(infoX.ModTime())
		})

		logCount := 0
		found := false
		for _, file := range files {
			if !strings.HasPrefix(file.Name(), this.LogFilename) {
				continue
			}
			if !strings.Contains(file.Name(), ".log") {
				continue
			}
			if logCount == indexNum {
				logName = file.Name()
				found = true
				break
			}
			logCount += 1
		}
		if !found {
			this.ErrorMsgf("没有 index 为 %d 的日志文件", indexNum)
			return false
		}
	}

	logData, err := os.ReadFile(filepath.Join(dirPath, logName))
	if err == nil {
		this.SendFileMessage(logName, string(logData), "")
		return true
	} else {
		this.ErrorMsgf("读取日志文件失败，Error: %s", err.Error())
		return false
	}
}

type DownloadStorageCommand struct {
	command.Command
	StorageDirPath  string
	StorageFilename string
}

func NewDownloadStorageCommand(logDirPath string, logFilename string) *DownloadStorageCommand {
	cmd := &DownloadStorageCommand{
		Command: command.Command{
			Name:            "downloadStorage",
			Alias:           []string{},
			Instruction:     "`.downloadStorage` 下载缓存数据文件",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		StorageDirPath:  logDirPath,
		StorageFilename: logFilename,
	}
	return cmd
}

func (this *DownloadStorageCommand) Do() bool {
	dirPath := "./"
	if this.StorageDirPath != "" {
		dirPath = this.StorageDirPath
	}
	data, err := os.ReadFile(filepath.Join(dirPath, this.StorageFilename))
	if err == nil {
		this.SendFileMessage(this.StorageFilename, string(data), "")
		return true
	} else {
		this.ErrorMsgf("读取缓存数据失败，Error: %s", err.Error())
		return false
	}
}

type PagerCommand struct {
	command.Command
}

func NewPagerCommand() *PagerCommand {
	cmd := &PagerCommand{
		Command: command.Command{
			Name:            "pager",
			Instruction:     "Built in command for pagination",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
	}
	return cmd
}

// 设置为调试模式，主要是日志改为调试等级

type DebugCommand struct {
	command.Command
}

func NewDebugCommand() *DebugCommand {
	cmd := &DebugCommand{
		Command: command.Command{
			Name:            "debug",
			Instruction:     "`.debug true/false GoogleAuthCode` 开启/关闭调试模式",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          3,
			AuthcodePos:     2,
		},
	}
	return cmd
}

func (this *DebugCommand) Do() bool {
	if strings.ToLower(this.Args[0]) == "true" || strings.ToLower(this.Args[0]) == "t" {
		this.SetDebug(true)
		this.SendMsgf("已开启调试模式")
	} else {
		this.SetDebug(false)
		this.SendMsgf("已关闭调试模式")
	}

	return true
}

// 重启程序命令

type RestartCommand struct {
	command.Command
}

func NewRestartCommand() *RestartCommand {
	cmd := &RestartCommand{
		Command: command.Command{
			Name:            "restart",
			Instruction:     "`.restart AuthCode` 程序重启",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
	}
	return cmd
}

func (this *RestartCommand) Do() bool {
	this.SendMsgf("程序重启中...")
	os.Exit(0)
	return true
}

// 通用的禁声 alert 消息
type MuteCommand struct {
	command.Command
}

func NewMuteCommand() *MuteCommand {
	cmd := &MuteCommand{
		Command: command.Command{
			Name:            "mute",
			Instruction:     "`.mute minutes AuthCode` 临时禁声【警告性消息】，不超过 7 天",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
	}
	return cmd
}

var MaxMute int64 = 60 * 24 * 7

func (this *MuteCommand) Do() bool {
	if m, err := strconv.ParseInt(this.Args[0], 10, 32); err == nil {
		minutes := MaxMute
		if m < MaxMute {
			minutes = m
		}
		expireTime := this.Processor.Responder.Mute(minutes)
		this.SendMsgf("[警告类消息] 禁声到：%s", expireTime)
		return true
	} else {
		this.ErrorMsgf("解析 minutes 错误。")
		return false
	}
}

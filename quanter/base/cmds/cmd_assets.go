package cmds

import (
	"strings"

	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/command"
)

type AssetsCommand struct {
	command.Command
	storage    base.Storager
	controller base.Controllable
}

func NewAssetsCommand(controller base.Controllable, storage base.Storager) *AssetsCommand {
	cmd := &AssetsCommand{
		Command: command.Command{
			Name:            "asset",
			Alias:           []string{"as"},
			Instruction:     "`.asset(+) record/detail[可选]` 打印资产快照",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
		},
		controller: controller,
		storage:    storage,
	}
	return cmd
}

func (this *AssetsCommand) Do() bool {
	return DoAssetsCommand(this.Command, this.controller)
}

func DoAssetsCommand(cmd command.Command, controller base.Controllable) bool {
	if len(cmd.Args) == 1 {
		if strings.EqualFold(cmd.Args[0], "record") {
			controller.GetStorage().RecordAssets()
		} else if strings.Contains(cmd.Args[0], "detail") {
			if balances, err := controller.GetAccountBalances(true); err != nil {
				cmd.ErrorMsgf("获取资产出错，error: %s", err)
				return false
			} else {
				if strings.Contains(cmd.Args[0], "moredetail") {
					cmd.SendMsgf("```%s```", balances.Render())
				} else {
					cmd.SendMsgf("```%s```", balances.RenderTotalCombined())
				}
				return true
			}
		}
	}
	assetsMsg := controller.RenderAssets()
	cmd.SendMsgf("```%s```", assetsMsg)
	return true
}

type HoldingsCommand struct {
	command.Command
	controller base.Controllable
}

func NewHoldingsCommand(controller base.Controllable) *HoldingsCommand {
	cmd := &HoldingsCommand{
		Command: command.Command{
			Name:            "holdings",
			Alias:           []string{"h", "holding"},
			Instruction:     "`.holdings` 打印资产详情",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
		},
		controller: controller,
	}
	return cmd
}

func (this *HoldingsCommand) Do() bool {
	balances, err := this.controller.GetAccountBalances(true)
	if err != nil {
		this.ErrorMsgf("获取资产出错，error: %s", err)
		return false
	}

	this.SendMsgf("资产汇总：\n```%s```", balances.RenderTotalCombined())
	this.SendMsgf("资产明细：\n```%s```", balances.Render())
	return true
}

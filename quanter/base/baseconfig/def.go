package baseconfig

import (
	"fmt"
	"strings"

	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"
)

type ValidatableConfig interface {
	Validate() error
	GetConfigOptions() *ConfigOptions
	GetChildValidatableConfig() ValidatableConfig
	GetBaseConfig() *BaseConfig
}

type BaseConfig struct {
	Host                 string
	ProxyUrl             string
	ReleaseBinaryDirPath string
	LogDirPath           string
	IsTestnet            bool
	ExchangeName         string
	ApiKey               string
	ApiSecret            string
	WithdrawApiKey       string // 主账号 API，用于提现、充值、子账号划转等处理
	WithdrawApiSecret    string
	AllowedSymbolPrefixs []string
	ShowFutureQtyAsValue bool                    // 期货张数转换显示为币种数量
	MinMarginRatio       float64                 // 账号资金占保证金最小比例
	FixerKey             string                  // fixer api key
	IsDemo               bool                    // 是否是展示模式
	EnableRealtimePrice  bool                    // 启用实时连接价格
	FutureInstrumentType exchange.InstrumentType // 限制交易的期货类型，不指定则不限
	USDXSymbol           string                  // 现货 XXX-- 和 合约 XXX.U 中指定的类 USDT 币种
	OptionsPath          string                  // 额外的配置文件路径
	GatewayAdapter       string                  // 网关适配器，用于连接 gateway
	MarginMode           exchange.AccountMarginMode
}

func (b *BaseConfig) ToTomlContent(hideSecret bool) string {
	apiKey := b.ApiKey
	apiSecret := b.ApiSecret
	withdrawApiKey := b.WithdrawApiKey
	withdrawApiSecret := b.WithdrawApiSecret
	fixerKey := b.FixerKey
	if hideSecret {
		apiKey = utils.HideSecret(apiKey)
		apiSecret = utils.HideSecret(apiSecret)
		withdrawApiKey = utils.HideSecret(withdrawApiKey)
		withdrawApiSecret = utils.HideSecret(withdrawApiSecret)
		fixerKey = utils.HideSecret(fixerKey)
	}
	return fmt.Sprintf(`ExchangeName = "%v"`, b.ExchangeName) + "\n" +
		fmt.Sprintf(`ApiKey = "%v"`, apiKey) + "\n" +
		fmt.Sprintf(`ApiSecret = "%v"`, apiSecret) + "\n" +
		fmt.Sprintf(`WithdrawApiKey = "%v"`, withdrawApiKey) + "\n" +
		fmt.Sprintf(`WithdrawApiSecret = "%v"`, withdrawApiSecret) + "\n" +
		fmt.Sprintf(`Host = "%v"`, b.Host) + "\n" +
		fmt.Sprintf(`IsTestnet = %v`, b.IsTestnet) + "\n" +
		fmt.Sprintf(`ReleaseBinaryDirPath = "%v"`, b.ReleaseBinaryDirPath) + "\n" +
		fmt.Sprintf(`LogDirPath = "%v"`, b.LogDirPath) + "\n" +
		fmt.Sprintf(`ProxyUrl = "%s"`, b.ProxyUrl) + "\n" +
		fmt.Sprintf(`FutureInstrumentType = "%s"`, b.FutureInstrumentType) + "\n" +
		fmt.Sprintf(`AllowedSymbolPrefixs = [%v]`, utils.SliceStringJoin(b.AllowedSymbolPrefixs, ", ", true)) + "\n" +
		fmt.Sprintf(`EnableRealtimePrice = %v`, b.EnableRealtimePrice) + "\n" +
		fmt.Sprintf(`ShowFutureQtyAsValue = %v`, b.ShowFutureQtyAsValue) + "\n" +
		fmt.Sprintf(`FixerKey = "%v"`, fixerKey) + "\n" +
		fmt.Sprintf(`MinMarginRatio = %v`, b.MinMarginRatio) + "\n" +
		fmt.Sprintf(`USDXSymbol = "%s"`, b.USDXSymbol) + "\n" +
		fmt.Sprintf(`IsDemo = %v`, b.IsDemo) + "\n" +
		fmt.Sprintf(`OptionsPath = "%s"`, b.OptionsPath) + "\n" +
		fmt.Sprintf(`GatewayAdapter = "%s"`, b.GatewayAdapter) + "\n" +
		fmt.Sprintf(`MarginMode = "%s"`, b.MarginMode)
}

var ValidExchangeUSDXSymbols = map[string][]string{
	exchange.BitMEX:      {"USDT"},
	exchange.Binance:     {"USDT"},
	exchange.OKEx:        {"USDT", "USDC"},
	exchange.Bybit:       {"USDT", "USDC"},
	exchange.Hyperliquid: {"USDC"},
}

func (b *BaseConfig) Validate() error {
	if b.USDXSymbol == "" {
		b.USDXSymbol = "USDT"
	}

	for name, validUSDXSymbols := range ValidExchangeUSDXSymbols {
		if strings.EqualFold(b.ExchangeName, name) && !utils.SliceContains(validUSDXSymbols, b.USDXSymbol) {
			return fmt.Errorf("invalid USDXSymbol %s, must be %v", b.USDXSymbol, validUSDXSymbols)
		}
	}

	if !utils.SliceContains([]exchange.AccountMarginMode{
		exchange.UnknownAccountMarginMode,
		exchange.AccountMarginModeSimple,
		exchange.AccountMarginModeIsolated,
		exchange.AccountMarginModeCross,
		exchange.AccountMarginModePortfolio,
	}, b.MarginMode) {
		return fmt.Errorf("invalid MarginMode %s", b.MarginMode)
	}

	return nil
}

func redactString(str string) string {
	ss := secrets.SecretString(str)
	return fmt.Sprintf("%s", ss)
}

func (b *BaseConfig) ToTable() string {
	t := exchange.NewTable()
	t.SetHeader([]string{"Field", "Value"})
	t.AddRow([]string{"Host", b.Host})
	t.AddRow([]string{"ProxyUrl", b.ProxyUrl})
	t.AddRow([]string{"ReleaseBinaryDirPath", b.ReleaseBinaryDirPath})
	t.AddRow([]string{"LogDirPath", b.LogDirPath})
	t.AddRow([]string{"IsTestnet", fmt.Sprintf("%v", b.IsTestnet)})
	t.AddRow([]string{"ExchangeName", b.ExchangeName})
	t.AddRow([]string{"ApiKey", b.ApiKey})
	t.AddRow([]string{"ApiSecret", redactString(b.ApiSecret)})
	t.AddRow([]string{"WithdrawApiKey", b.WithdrawApiKey})
	t.AddRow([]string{"WithdrawApiSecret", redactString(b.WithdrawApiSecret)})
	t.AddRow([]string{"AllowedSymbolPrefixs", utils.SliceStringJoin(b.AllowedSymbolPrefixs, ", ", true)})
	t.AddRow([]string{"ShowFutureQtyAsValue", fmt.Sprintf("%v", b.ShowFutureQtyAsValue)})
	t.AddRow([]string{"MinMarginRatio", fmt.Sprintf("%v", b.MinMarginRatio)})
	t.AddRow([]string{"USDXSymbol", b.USDXSymbol})
	t.AddRow([]string{"IsDemo", fmt.Sprintf("%v", b.IsDemo)})
	t.AddRow([]string{"EnableRealtimePrice", fmt.Sprintf("%v", b.EnableRealtimePrice)})
	t.AddRow([]string{"FutureInstrumentType", string(b.FutureInstrumentType)})
	t.AddRow([]string{"GatewayAdapter", b.GatewayAdapter})
	t.AddRow([]string{"MarginMode", string(b.MarginMode)})
	return t.Render()
}

func (b *BaseConfig) GetConfigOptions() *ConfigOptions {
	return nil
}

func (b *BaseConfig) GetChildValidatableConfig() ValidatableConfig {
	return nil
}

func (b *BaseConfig) GetBaseConfig() *BaseConfig {
	return nil
}

type ConfigCompareResult struct {
	match   bool
	thisStr string
	thatStr string
}

type ConfigOptionTags struct {
	IsPercent bool
	Unit      string
}

type ConfigOptions map[string]ConfigOptionTags

type ConfigValueItem struct {
	Field   string
	Value   string
	IsChild bool
	IsBase  bool
}

type ConfigValueList []ConfigValueItem

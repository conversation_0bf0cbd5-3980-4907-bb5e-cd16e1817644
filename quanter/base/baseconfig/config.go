package baseconfig

import (
	"encoding/csv"
	"fmt"
	"io"
	"reflect"
	"strconv"
	"strings"

	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/utils"

	"github.com/stevedomin/termtable"
)

func NewTable() *termtable.Table {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	return t
}

// 因为 any 中可能有未导出的字段，这种 field 无法获取其中的值，会导致崩溃
// 并且该包仅处理导出的字段
func isFieldNotExported(fieldName string) bool {
	if len(fieldName) == 0 {
		return true
	}
	return fieldName[0:1] != strings.ToUpper(fieldName[0:1])
}

func SetValue(this ValidatableConfig, fieldNameStr string, newValueStr string) error {
	fieldName, fieldType, oldValue := GetTypeValue(this, fieldNameStr)
	s := reflect.ValueOf(this).Elem()
	f := s.FieldByName(fieldName)
	oldValueStr := ""
	if _, isPercent, _, err := GetValueString(this, fieldName); err != nil {
		return fmt.Errorf("check field %s=%s value error: %s", fieldName, newValueStr, err)
	} else {
		if fieldType == "int" {
			value, err := strconv.ParseInt(newValueStr, 10, 64)
			if err != nil {
				return fmt.Errorf("%s=%s is not a int", fieldName, newValueStr)
			}
			f.SetInt(value)
			oldValueStr = fmt.Sprintf("%d", oldValue.(int))
		} else if fieldType == "float64" {
			value := 0.0
			if v, err := strconv.ParseFloat(newValueStr, 64); err != nil {
				if isPercent {
					if f, _, err := utils.ParseFloatOrPercentage(newValueStr, false, false); err != nil {
						return fmt.Errorf("%s=%s is not a valid float or percentage, error: %s", fieldName, newValueStr, err)
					} else {
						value = f
					}
				} else {
					return fmt.Errorf("%s=%s is not a float", fieldName, newValueStr)
				}
			} else {
				value = v
			}
			f.SetFloat(value)
			oldValueStr = fmt.Sprintf("%.f", oldValue.(float64))
		} else if fieldType == "bool" {
			if !strings.EqualFold(newValueStr, "true") && !strings.EqualFold(newValueStr, "false") {
				return fmt.Errorf("%s=%s is not a valid bool", fieldName, newValueStr)
			}
			value := false
			if strings.EqualFold(newValueStr, "true") {
				value = true
			}
			f.SetBool(value)
			oldValueStr = fmt.Sprintf("%v", oldValue.(bool))
		} else if fieldType == "string" {
			f.SetString(newValueStr)
			oldValueStr = oldValue.(string)
		} else if fieldType == "exchange.AccountMarginMode" {
			f.SetString(newValueStr)
			oldValueStr = fmt.Sprintf("%s", oldValue)
		} else {
			return fmt.Errorf("unsupported value type, only int, float supported")
		}
	}
	zlog.Debugf("set field %s, old value: %s, new value: %s", fieldNameStr, oldValueStr, newValueStr)
	if err := this.Validate(); err != nil {
		// 如果出错，将旧的值再设回去
		SetValue(this, fieldNameStr, oldValueStr)
		return err
	}
	return nil
}

func GetTypeValue(this ValidatableConfig, name string) (fieldName, typeStr string, value any) {
	s := reflect.ValueOf(this).Elem()
	typeOfT := s.Type()

	for i := 0; i < s.NumField(); i++ {
		f := s.Field(i)
		if strings.EqualFold(strings.ToLower(typeOfT.Field(i).Name), strings.ToLower(name)) {
			return typeOfT.Field(i).Name, f.Type().String(), f.Interface()
		}
	}
	return "", "", nil
}

func GetValueString(this ValidatableConfig, fieldName string) (valueStr string, isPercent bool, unit string, er error) {
	s := reflect.ValueOf(this).Elem()
	typeOfT := s.Type()
	valueStr = ""
	configOptions := this.GetConfigOptions()

	for i := 0; i < s.NumField(); i++ {
		f := s.Field(i)
		thisFieldName := typeOfT.Field(i).Name
		if isFieldNotExported(thisFieldName) {
			continue
		}
		thisFieldValue := f.Interface()
		thisFieldType := f.Type().String()
		fieldType := thisFieldType

		if strings.EqualFold(thisFieldName, fieldName) {
			if configOptions != nil {
				isPercent := false
				unit := ""
				if option, found := (*configOptions)[thisFieldName]; found {
					isPercent = option.IsPercent
					unit = option.Unit

					if fieldType == "int" {
						valueStr = fmt.Sprint(thisFieldValue.(int))
					} else if fieldType == "float64" {
						valueStr = fmt.Sprint(thisFieldValue.(float64))
						if isPercent {
							valueStr = fmt.Sprint(thisFieldValue.(float64) * 100)
							if strings.Contains(valueStr, ".") {
								valueStr = fmt.Sprintf(`%s%%`, strings.TrimRight(valueStr, "0"))
							} else {
								valueStr = fmt.Sprintf(`%s%%`, valueStr)
							}
						}
					} else if fieldType == "bool" {
						valueStr = fmt.Sprint(thisFieldValue.(bool))
					} else if fieldType == "string" {
						valueStr = thisFieldValue.(string)
					} else {
						return "", false, "", fmt.Errorf("unsupported value type, only int, float, bool and string are supported")
					}
					if strings.Contains("int,float64", fieldType) && unit != "" {
						valueStr = fmt.Sprintf("%s %s", valueStr, unit)
					}
					return valueStr, isPercent, unit, nil
				}
			}
			if fieldType == "int" {
				valueStr = fmt.Sprint(thisFieldValue.(int))
			} else if fieldType == "float64" {
				valueStr = fmt.Sprint(thisFieldValue.(float64))
				valueStr = fmt.Sprintf(`%s%%`, strings.TrimRight(valueStr, "0"))
			} else if fieldType == "bool" {
				valueStr = fmt.Sprint(thisFieldValue.(bool))
			} else if fieldType == "string" {
				valueStr = thisFieldValue.(string)
			} else if fieldType == "exchange.AccountMarginMode" {
				valueStr = fmt.Sprintf("%s", thisFieldValue)
			} else {
				return "", false, "", fmt.Errorf("unsupported value type, only int, float, bool and string are supported")
			}
			return valueStr, false, "", nil
		}
	}
	return "", false, "", fmt.Errorf("field %s not found", fieldName)
}

// 比较两个配置，计算每个字段的值是否相同，并且将相应的值转换为字符串
func Compare(this ValidatableConfig, that ValidatableConfig) (map[string]*ConfigCompareResult, error) {
	s := reflect.ValueOf(this).Elem()
	typeOfT := s.Type()

	compareResult := map[string]*ConfigCompareResult{}

	for i := 0; i < s.NumField(); i++ {
		f := s.Field(i)
		thisFieldName := typeOfT.Field(i).Name
		if isFieldNotExported(thisFieldName) {
			continue
		}
		thisFieldValue := f.Interface()
		thisFieldType := f.Type().String()
		fieldType := thisFieldType
		_, thatFieldType, thatFieldValue := GetTypeValue(that, thisFieldName)
		if thatFieldValue == nil {
			return nil, fmt.Errorf("value of that.%s is nil", thisFieldName)
		}
		if thatFieldType != thisFieldType {
			return nil, fmt.Errorf("this.%s of type %s match that.%s of type %s", thisFieldName, thisFieldType, thisFieldName, thatFieldType)
		}

		if thisValueStr, _, _, err := GetValueString(this, thisFieldName); err != nil {
			return nil, err
		} else {
			if thatValueStr, _, _, err := GetValueString(that, thisFieldName); err != nil {
				return nil, err
			} else {
				matchResult := false
				if fieldType == "int" {
					matchResult = thisFieldValue.(int) == thatFieldValue.(int)
				} else if fieldType == "float64" {
					matchResult = thisFieldValue.(float64) == thatFieldValue.(float64)
				} else if fieldType == "bool" {
					matchResult = thisFieldValue.(bool) == thatFieldValue.(bool)
				} else if fieldType == "string" {
					matchResult = thisFieldValue.(string) == thatFieldValue.(string)
				}
				compareResult[thisFieldName] = &ConfigCompareResult{
					match:   matchResult,
					thisStr: thisValueStr,
					thatStr: thatValueStr,
				}
			}
		}
	}
	return compareResult, nil
}

func RenderConfigDiff(this ValidatableConfig, that ValidatableConfig, isTable bool) (string, error) {
	s := reflect.ValueOf(this).Elem()
	typeOfT := s.Type()
	t := NewTable()

	if isTable {
		headers := []string{"Config", "Value", "Default"}
		t.SetHeader(headers)
	}

	diffFound := false
	configValues := []string{}
	if compareResult, err := Compare(this, that); err != nil {
		return "", err
	} else {
		for i := 0; i < s.NumField(); i++ {
			fieldName := typeOfT.Field(i).Name
			if isFieldNotExported(fieldName) {
				continue
			}
			if result, found := compareResult[fieldName]; found && !result.match {
				if isTable {
					t.AddRow([]string{fieldName, result.thisStr, result.thatStr})
				} else {
					configValues = append(configValues, fmt.Sprintf("%s=%s", fieldName, result.thisStr))
				}
				diffFound = true
			}
		}
	}
	if diffFound {
		if isTable {
			return t.Render(), nil
		} else {
			return strings.Join(configValues, ","), nil
		}
	} else {
		return "", nil
	}
}

func GetTableRows(this ValidatableConfig) ([][]string, error) {
	s := reflect.ValueOf(this).Elem()
	typeOfT := s.Type()
	rows := [][]string{}

	for i := 0; i < s.NumField(); i++ {
		fieldName := typeOfT.Field(i).Name
		if isFieldNotExported(fieldName) {
			continue
		}
		if valueStr, _, _, err := GetValueString(this, fieldName); err != nil {
			return nil, err
		} else {
			rows = append(rows, []string{fieldName, valueStr})
		}
	}
	return rows, nil
}

func RenderTable(this ValidatableConfig) (string, error) {
	t := NewTable()

	headers := []string{"Config", "Value"}
	t.SetHeader(headers)

	if rows, err := GetTableRows(this); err != nil {
		return "", err
	} else {
		for _, row := range rows {
			t.AddRow(row)
		}
		return t.Render(), nil
	}
}

func SetConfigWithString(this ValidatableConfig, configStr string) (configPairs ConfigValueList, correctedConfigStr string, er error) {
	configPairs = ConfigValueList{}
	correctedConfigStr = configStr
	childConfig := this.GetChildValidatableConfig()
	hasChild := false
	if childConfig != nil {
		hasChild = true
	}
	baseConfig := this.GetBaseConfig()
	if baseConfig != nil {
		hasChild = true
	}

	if values, cfgStr, err := ParseConfigsFromString(this, configStr); err != nil {
		er = err
		return
	} else {
		configPairs = values
		correctedConfigStr = cfgStr
		// 如果有子配置，优先设置子配置；直接设置到父配置上是不能成功的
		for _, pair := range values {
			// 字段是子配置的字段
			if pair.IsChild {
				// 操作对象是父配置，则获取其子配置，设置参数
				if hasChild {
					if pair.IsBase && baseConfig != nil {
						if err := SetValue(baseConfig, pair.Field, pair.Value); err != nil {
							er = err
						}
					} else {
						if err := SetValue(childConfig, pair.Field, pair.Value); err != nil {
							er = err
						}
					}
				} else {
					// 操作对象是子配置，直接设置参数
					if err := SetValue(this, pair.Field, pair.Value); err != nil {
						er = err
					}
				}
			} else {
				// 如果是父配置的字段，并且是父配置，直接设置参数
				if hasChild {
					if err := SetValue(this, pair.Field, pair.Value); err != nil {
						er = err
					}
				}
			}
		}
		return
	}
}

// 支持同时设置子配置和父配置的值
func ParseConfigsFromString(this ValidatableConfig, configValueString string) (configValues ConfigValueList, correctedConfigStr string, er error) {
	if configValueString == "" {
		return
	}

	origConfigValues := [][2]string{}
	correctedConfigStr = configValueString
	parts := strings.Split(configValueString, ",")
	for _, p := range parts {
		// slack 输入引号有可能会被自动转成 “”，需要替换为英文引号 "
		// It's a smart Unicode quote (U+201C LEFT DOUBLE QUOTATION MARK) rather than
		// a standard ASCII double quote (U+0022 QUOTATION MARK).
		// This can cause problems in parsing, as most parsers expect standard ASCII quotes.
		p = strings.Replace(p, "”", "\"", -1)
		p = strings.Replace(p, "“", "\"", -1)

		parts := []string{}
		// 使用 csv 模块解析值，因为值中间有可能有 =，可能需要将 value 用引号括起来
		// 即可能出现：Field="Value=" 这种情况，这种情况 Field 的值可以正确的解析为 Value=
		r := csv.NewReader(strings.NewReader(p))
		r.Comma = '='
		for {
			record, err := r.Read()
			if err == io.EOF {
				break
			}
			parts = record
		}

		if len(parts) != 2 {
			er = fmt.Errorf("invalid config=value pair: %s", p)
			return
		}
		origConfigValues = append(origConfigValues, [2]string{parts[0], parts[1]})
	}

	childConfigValues := ConfigValueList{}
	configValues = ConfigValueList{}

	childConfig := this.GetChildValidatableConfig()
	// 如果有子配置，先解析子配置
	if childConfig != nil {
		if childConfig.GetChildValidatableConfig() != nil {
			panic("nesting configs with more than one level is not allowed")
		}
		if cv, _, err := ParseConfigsFromString(childConfig, configValueString); err == nil {
			childConfigValues = cv
			configValues = append(configValues, cv...)
		}
	}

	baseConfig := this.GetBaseConfig()
	if baseConfig != nil {
		if cv, _, err := ParseConfigsFromString(baseConfig, configValueString); err == nil {
			childConfigValues = append(childConfigValues, cv...)
			configValues = append(configValues, cv...)
		}
	}

	// convert miscapitalized config name to correct ones
	s := reflect.ValueOf(this).Elem()
	typeOfT := s.Type()

	for _, pair := range origConfigValues {
		fieldName := pair[0]
		fieldFound := false
		numOfFields := s.NumField()
		for i := 0; i < numOfFields; i++ {
			correctedFieldName := typeOfT.Field(i).Name
			if isFieldNotExported(correctedFieldName) {
				continue
			}
			// 如果原始的 configName 和字段名转换为小写后匹配，则将原始 configName 替换为字段名
			if strings.EqualFold(strings.ToLower(correctedFieldName), strings.ToLower(fieldName)) {
				item := ConfigValueItem{Field: correctedFieldName, Value: pair[1], IsChild: childConfig == nil}
				if _, ok := this.(*BaseConfig); ok {
					item.IsBase = true
				}
				configValues = append(configValues, item)
				fieldFound = true
			}
		}
		// 在子配置中查找是否能找到
		for _, childPair := range childConfigValues {
			if strings.EqualFold(fieldName, childPair.Field) {
				fieldFound = true
			}
		}
		// 仅在父配置中检查是否最终没有找到字段
		if childConfig != nil && !fieldFound {
			er = fmt.Errorf("config field not found: %s", fieldName)
			return
		}
	}
	pairs := []string{}
	for _, pair := range configValues {
		pairs = append(pairs, fmt.Sprintf("%s=%s", pair.Field, pair.Value))
	}
	correctedConfigStr = strings.Join(pairs, ",")
	return
}

# -*- coding: utf-8 -*-

from fabric.api import run, hosts, local, execute, env, put
from fabric.context_managers import cd, prefix
from fabric.contrib.project import rsync_project

SERVER = 'USERNAME@HOST'


# useage: fab deploy:turtle_name='turtle_seven'
@hosts(SERVER)
def deploy(turtle_name="turtle_seven"):
    projects_dir = '/home/<USER>/projects/{}'.format(turtle_name)
    put("bin/{}".format(turtle_name), "{}/{}_new".format(projects_dir, turtle_name), mode=0o755)

    with cd(projects_dir):
        run('cp {} {}_last_backup'.format(turtle_name, turtle_name), warn_only=True)
        run('mv {}_new {}'.format(turtle_name, turtle_name))
        run('supervisorctl restart {}'.format(turtle_name))

package main

import (
	"archive/zip"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gorilla/websocket"
	"github.com/tidwall/gjson"
)

const (
	API_URL = "api.hyperliquid.xyz"
)

type MarketData struct {
	Time   string      `json:"time"`
	VerNum int64       `json:"ver_num"`
	Raw    interface{} `json:"raw"`
}

// 压缩文件
func compressFile(srcFile, destZip string) error {
	zipfile, err := os.Create(destZip)
	if err != nil {
		return fmt.Errorf("create zip file error: %v", err)
	}
	defer zipfile.Close()

	archive := zip.NewWriter(zipfile)
	defer archive.Close()

	file, err := os.Open(srcFile)
	if err != nil {
		return fmt.Errorf("open source file error: %v", err)
	}
	defer file.Close()

	writer, err := archive.Create(filepath.Base(srcFile))
	if err != nil {
		return fmt.Errorf("create zip entry error: %v", err)
	}

	_, err = io.Co<PERSON>(writer, file)
	if err != nil {
		return fmt.Errorf("write to zip error: %v", err)
	}

	return nil
}

// 检查并压缩旧文件
func compressOldFiles(baseDir string) {
	for {
		now := time.Now().UTC()
		twoHoursAgo := now.Add(-2 * time.Hour)

		// 构建两小时前的目录路径
		oldPath := filepath.Join(
			baseDir,
			"market_data",
			twoHoursAgo.Format("20060102"),
			fmt.Sprintf("%d", twoHoursAgo.Hour()),
			"l2Book",
		)

		// 检查目录是否存在
		if _, err := os.Stat(oldPath); os.IsNotExist(err) {
			time.Sleep(10 * time.Minute)
			continue
		}

		// 遍历目录下的所有文件
		files, err := os.ReadDir(oldPath)
		if err != nil {
			fmt.Printf("read directory error: %v\n", err)
			time.Sleep(10 * time.Minute)
			continue
		}

		for _, file := range files {
			if file.IsDir() {
				continue
			}

			filename := file.Name()
			// 跳过已经压缩的文件
			if strings.HasSuffix(filename, ".zip") {
				continue
			}

			srcFile := filepath.Join(oldPath, filename)
			destZip := srcFile + ".zip"

			// 检查目标zip文件是否已存在
			if _, err := os.Stat(destZip); err == nil {
				continue
			}

			// 压缩文件
			err := compressFile(srcFile, destZip)
			if err != nil {
				fmt.Printf("compress file error: %v\n", err)
				continue
			}

			// 压缩成功后删除原文件
			err = os.Remove(srcFile)
			if err != nil {
				fmt.Printf("remove original file error: %v\n", err)
				continue
			}

			fmt.Printf("Successfully compressed %s to %s\n", srcFile, destZip)
		}

		time.Sleep(10 * time.Minute)
	}
}

func connectWebSocket(coins []string) (*websocket.Conn, error) {
	u := url.URL{Scheme: "wss", Host: API_URL, Path: "/ws"}
	fmt.Printf("connecting to %s\n", u.String())

	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("dial error: %v", err)
	}

	// 订阅所有币种的 l2Book
	for _, coin := range coins {
		subscription := fmt.Sprintf(`{"method": "subscribe", "subscription": {"type": "l2Book", "coin": "%s"}}`, coin)
		err = c.WriteMessage(websocket.TextMessage, []byte(subscription))
		if err != nil {
			c.Close()
			return nil, fmt.Errorf("subscribe error for %s: %v", coin, err)
		}
		fmt.Printf("Subscribed to %s\n", coin)
	}

	return c, nil
}

func main() {
	var (
		coinsStr = flag.String("coins", "", "Comma separated list of coins to subscribe (e.g. ETH,SOL,PURR/USDC)")
		baseDir  = flag.String("dir", ".", "Base directory for saving data")
	)

	// 解析命令行参数
	flag.Parse()

	if *coinsStr == "" {
		fmt.Println("Please specify coins using -coins flag (e.g. -coins ETH,SOL)")
		flag.Usage()
		return
	}

	// 解析币种列表
	coins := strings.Split(*coinsStr, ",")
	// 创建币种到安全文件名的映射
	safeSymbols := make(map[string]string)
	for _, coin := range coins {
		coin = strings.TrimSpace(coin) // 移除可能的空格
		safeSymbols[coin] = strings.ReplaceAll(coin, "/", "_")
	}

	// 启动压缩任务
	go compressOldFiles(*baseDir)

	for {
		// 尝试连接
		c, err := connectWebSocket(coins)
		if err != nil {
			fmt.Printf("Connection failed: %v, retrying in 5 seconds...\n", err)
			time.Sleep(5 * time.Second)
			continue
		}

		// 确保连接会被关闭
		func() {
			defer c.Close()

			// 设置读取超时
			c.SetReadDeadline(time.Now().Add(60 * time.Second))
			c.SetPongHandler(func(string) error {
				c.SetReadDeadline(time.Now().Add(60 * time.Second))
				return nil
			})

			// 启动心跳检测
			go func() {
				ticker := time.NewTicker(10 * time.Second)
				defer ticker.Stop()

				for {
					select {
					case <-ticker.C:
						if err := c.WriteMessage(websocket.TextMessage, []byte(`{ "method": "ping" }`)); err != nil {
							return
						}
					}
				}
			}()

			// 主消息处理循环
			for {
				_, message, err := c.ReadMessage()
				if err != nil {
					fmt.Printf("read error: %v\n", err)
					return
				}

				// 重置读取超时
				c.SetReadDeadline(time.Now().Add(60 * time.Second))

				// fmt.Printf("recv: %s\n", string(message))

				// 解析消息
				data := gjson.Parse(string(message))
				if data.Get("channel").String() == "l2Book" {
					// 获取消息对应的币种
					coin := data.Get("data.coin").String()
					if safeSymbol, ok := safeSymbols[coin]; ok {
						now := time.Now().UTC()
						filename := filepath.Join(
							*baseDir,
							"market_data",
							now.Format("20060102"),
							fmt.Sprintf("%d", now.Hour()),
							"l2Book",
							safeSymbol,
						)

						// 确保父目录存在
						err = os.MkdirAll(filepath.Dir(filename), 0755)
						if err != nil {
							fmt.Printf("create directory error: %v\n", err)
							continue
						}

						// 构建数据，使用 UTC 时间
						marketData := MarketData{
							Time:   now.Format(time.RFC3339Nano),
							VerNum: 1,
							Raw:    json.RawMessage(message),
						}

						// 序列化数据
						jsonData, err := json.Marshal(marketData)
						if err != nil {
							fmt.Printf("marshal error: %v\n", err)
							continue
						}

						// 写入文件
						f, err := os.OpenFile(filename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
						if err != nil {
							fmt.Printf("open file error: %v\n", err)
							continue
						}

						// 写入数据并换行
						if _, err := f.WriteString(string(jsonData) + "\n"); err != nil {
							fmt.Printf("write file error: %v\n", err)
						}
						f.Close()

						fmt.Printf("Saved l2Book data for %s at %s UTC\n", coin, now.Format(time.RFC3339))
					} else {
						fmt.Printf("Unknown coin: %s\n", coin)
					}
				}
			}
		}()

		fmt.Println("Connection lost, reconnecting in 5 seconds...")
		time.Sleep(5 * time.Second)
	}
}

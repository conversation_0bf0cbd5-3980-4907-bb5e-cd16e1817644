<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <title>订单和L2 Order Book展示</title>
        <!-- 引入ECharts库 -->
        <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
            }
            #controls {
                margin-bottom: 20px;
            }
            #controls div {
                margin-bottom: 10px;
            }
            #chart {
                width: 100%;
                height: 500px;
            }
            #details {
                margin-top: 20px;
                display: flex;
                flex-wrap: wrap;
            }
            #details > div {
                flex: 1;
                min-width: 300px;
                margin-right: 20px;
            }
            h3 {
                margin-top: 0;
            }
            pre {
                background-color: #f5f5f5;
                padding: 10px;
                overflow: auto;
            }
        </style>
    </head>
    <body>
        <h1>订单和L2 Order Book展示</h1>
        <div id="controls">
            <div>
                <label for="strategy-select">选择策略ID：</label>
                <select id="strategy-select"></select>
            </div>
            <div>
                <label for="start-time">开始时间：</label>
                <input type="datetime-local" id="start-time" />
                <label for="end-time">结束时间：</label>
                <input type="datetime-local" id="end-time" />
                <button id="update-chart">更新图表</button>
            </div>
        </div>

        <div id="chart"></div>

        <div id="details">
            <div>
                <h3>订单详情</h3>
                <pre id="order-details">将鼠标悬停在图表上查看订单详情。</pre>
            </div>
            <div>
                <h3>L2 Order Book详情</h3>
                <pre id="l2orderbook-details">将鼠标悬停在图表上查看L2 Order Book详情。</pre>
            </div>
        </div>

        <script>
            // 1. 变量声明部分 - 确保所有全局变量都在这里声明
            let myChart;
            let orders = [];
            let l2DataList = [];
            let strategyOrders = {};
            let bidPrices = [];
            let askPrices = [];
            let originalBidPrices = [];
            let originalAskPrices = [];

            // 2. loadOrders 函数 - 添加错误处理
            async function loadOrders() {
                try {
                    const response = await fetch("/orders");
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const rawOrders = await response.json();

                    // 过滤每个策略的订单，只保留 ExecQty > 0 的订单
                    strategyOrders = {};
                    Object.entries(rawOrders).forEach(([strategyId, orders]) => {
                        strategyOrders[strategyId] = orders.filter((order) => order.ExecQty > 0);
                    });

                    // 初始化策略选择
                    const strategySelect = document.getElementById("strategy-select");
                    strategySelect.innerHTML = ""; // 清空现有选项

                    const strategyIds = Object.keys(strategyOrders);
                    strategyIds.forEach((strategyId) => {
                        const option = document.createElement("option");
                        option.value = strategyId;
                        option.text = strategyId;
                        strategySelect.appendChild(option);
                    });

                    // 设置默认时间范围
                    if (strategyIds.length > 0) {
                        const firstStrategy = strategyIds[0];
                        const firstOrder = strategyOrders[firstStrategy][0];
                        if (firstOrder) {
                            const now = new Date();
                            const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

                            document.getElementById("start-time").value = oneDayAgo.toISOString().slice(0, 16);
                            document.getElementById("end-time").value = now.toISOString().slice(0, 16);
                        }
                    }

                    // 初始更新图表
                    await updateChart();
                } catch (error) {
                    console.error("加载订单数据失败:", error);
                    alert("加载订单数据失败，请刷新页面重试");
                }
            }

            function parseTimeToUTC(timeStr) {
                const [datePart, timePart] = timeStr.split("T");
                const [year, month, day] = datePart.split("-").map(Number);
                const [hour = 0, minute = 0, second = 0] = (timePart || "").split(":").map(Number);

                // 使用 Date.UTC 创建 UTC 时间的时间戳
                return Date.UTC(year, month - 1, day, hour, minute, second);
            }

            // 3. updateChart 函数 - 添加数据验证
            async function updateChart() {
                const strategySelect = document.getElementById("strategy-select");
                if (!strategySelect) {
                    console.error("找不到策略选择器元素");
                    return;
                }

                const selectedStrategy = strategySelect.value;
                const startTimeInput = document.getElementById("start-time").value;
                const endTimeInput = document.getElementById("end-time").value;

                if (!startTimeInput || !endTimeInput) {
                    console.error("时间范围未设置");
                    return;
                }

                let startTime = parseTimeToUTC(startTimeInput);
                let endTime = parseTimeToUTC(endTimeInput);

                if (isNaN(startTime) || isNaN(endTime)) {
                    console.error("无效的时间格式");
                    return;
                }

                // 获取当前策略的订单
                orders = strategyOrders[selectedStrategy] || [];
                const currentSymbol = orders[0]?.Symbol;

                if (!currentSymbol) {
                    console.error("未找到有效的交易对信息");
                    return;
                }

                // 过滤订单数据
                const filteredOrders = orders.filter((order) => {
                    const orderTime = Date.parse(order.UpdateTime);
                    return orderTime >= startTime && orderTime <= endTime;
                });

                // 处理买单
                const buyOrders = filteredOrders
                    .filter((order) => order.Side === "Buy" && order.ExecQty > 0)
                    .map((order) => ({
                        value: [Date.parse(order.UpdateTime), order.ExecPrice],
                        order: order,
                    }));

                // 处理卖单
                const sellOrders = filteredOrders
                    .filter((order) => order.Side === "Sell" && order.ExecQty > 0)
                    .map((order) => ({
                        value: [Date.parse(order.UpdateTime), order.ExecPrice],
                        order: order,
                    }));

                // 4. L2数据处理部分 - 优化错误处理
                try {
                    const response = await fetch(
                        `/l2book?symbol=${currentSymbol}&startTime=${Math.floor(startTime / 1000)}&endTime=${Math.floor(endTime / 1000)}`
                    );
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const l2Data = await response.json();

                    bidPrices = [];
                    askPrices = [];
                    l2DataList = [];
                    originalBidPrices = [];
                    originalAskPrices = [];

                    if (!Array.isArray(l2Data)) {
                        throw new Error("L2数据格式错误");
                    }

                    l2Data.forEach((data) => {
                        if (!data || !data.time || !data.raw?.data?.levels) {
                            console.warn("跳过无效的L2数据记录");
                            return;
                        }

                        const dataTime = Date.parse(data.time);
                        const bidLevels = data.raw.data.levels[0];
                        const askLevels = data.raw.data.levels[1];

                        if (bidLevels && bidLevels.length > 0) {
                            bidPrices.push([dataTime, parseFloat(bidLevels[0].px)]);
                            originalBidPrices.push([dataTime, parseFloat(bidLevels[0].px)]);
                        }
                        if (askLevels && askLevels.length > 0) {
                            askPrices.push([dataTime, parseFloat(askLevels[0].px)]);
                            originalAskPrices.push([dataTime, parseFloat(askLevels[0].px)]);
                        }

                        l2DataList.push({
                            time: dataTime,
                            data: data,
                        });
                    });

                    // 5. 数据抽样处理
                    const maxInitialPoints = 1000;
                    bidPrices = adaptiveSampling(originalBidPrices, maxInitialPoints);
                    askPrices = adaptiveSampling(originalAskPrices, maxInitialPoints);

                    // 6. 图表配置 - 确保所有必要的配置都存在
                    const option = {
                        title: {
                            text: `策略 ${selectedStrategy} 的成交订单和Order Book展示（${currentSymbol}）`,
                        },
                        tooltip: {
                            trigger: "axis",
                            axisPointer: {
                                type: "cross",
                            },
                            formatter: function (params) {
                                const time = params[0].value[0];

                                // 查找最近的订单
                                const nearestOrder = getNearestOrder(orders, time);
                                // 查找最近的L2数据
                                const nearestL2Data = getNearestL2Data(l2DataList, time);

                                let tooltipText = `时间：${formatDateTime(time)}<br>`;
                                params.forEach((p) => {
                                    if (p.value && p.value[1] !== undefined) {
                                        tooltipText += `${p.marker}${p.seriesName}：${p.value[1]}<br>`;
                                    }
                                });

                                if (nearestOrder) {
                                    tooltipText += `最近订单（${nearestOrder.Side}）：${nearestOrder.ExecPrice}<br>`;
                                    tooltipText += `订单时间：${formatDateTime(Date.parse(nearestOrder.UpdateTime))}<br>`;
                                    if (nearestOrder.ExtFloat64) {
                                        tooltipText += `浮动盈亏：${nearestOrder.ExtFloat64.PNL.toFixed(2)}<br>`;
                                        tooltipText += `持仓：${nearestOrder.ExtFloat64.PosQty}<br>`;
                                    }
                                }

                                return tooltipText;
                            },
                        },
                        legend: {
                            data: ["买单", "卖单", "买一价", "卖一价"],
                        },
                        xAxis: {
                            type: "time",
                            boundaryGap: false,
                            axisLabel: {
                                formatter: function (value) {
                                    // 修改为不显示毫秒
                                    return formatDateTimeWithoutMilliseconds(value);
                                },
                            },
                        },
                        yAxis: {
                            type: "value",
                            scale: true,
                            splitNumber: 8,
                        },
                        dataZoom: [
                            {
                                type: "inside",
                                start: 0,
                                end: 100,
                            },
                            {
                                type: "slider",
                                start: 0,
                                end: 100,
                            },
                        ],
                        series: [
                            {
                                name: "买单",
                                type: "scatter",
                                data: buyOrders.map((item) => item.value),
                                symbolSize: 8,
                                itemStyle: {
                                    color: "green",
                                },
                            },
                            {
                                name: "卖单",
                                type: "scatter",
                                data: sellOrders.map((item) => item.value),
                                symbolSize: 8,
                                itemStyle: {
                                    color: "red",
                                },
                            },
                            {
                                name: "买一价",
                                type: "line",
                                data: bidPrices,
                                lineStyle: {
                                    type: "solid",
                                    color: "blue",
                                },
                                symbol: "none",
                            },
                            {
                                name: "卖一价",
                                type: "line",
                                data: askPrices,
                                lineStyle: {
                                    type: "solid",
                                    color: "orange",
                                },
                                itemStyle: {
                                    color: "orange",
                                },
                                symbol: "none",
                            },
                        ],
                    };

                    // 7. 事件处理优化
                    if (!myChart) {
                        myChart = echarts.init(document.getElementById("chart"));
                    }
                    myChart.setOption(option);

                    // 使用 updateAxisPointer 事件来更新详情
                    myChart.on("updateAxisPointer", function (event) {
                        const xAxisInfo = event.axesInfo[0];
                        if (xAxisInfo) {
                            const time = xAxisInfo.value;

                            // 查找最近的订单
                            const nearestOrder = getNearestOrder(orders, time);
                            if (nearestOrder) {
                                document.getElementById("order-details").innerText = `订单详情：
OrderID: ${nearestOrder.OrderID}
Symbol: ${nearestOrder.Symbol}
Price: ${nearestOrder.Price}
ExecPrice: ${nearestOrder.ExecPrice}
ExecQty: ${nearestOrder.ExecQty}
Side: ${nearestOrder.Side}
Status: ${nearestOrder.Status}
UpdateTime: ${formatDateTime(Date.parse(nearestOrder.UpdateTime))}`;
                            } else {
                                document.getElementById("order-details").innerText = "无订单详情。";
                            }

                            // 查找最近的L2数据
                            const nearestL2Data = getNearestL2Data(l2DataList, time);
                            if (nearestL2Data && nearestL2Data.data) {
                                const raw = nearestL2Data.data.raw;
                                if (raw && raw.data && raw.data.levels) {
                                    const bidLevels = raw.data.levels[0] || [];
                                    const askLevels = raw.data.levels[1] || [];

                                    document.getElementById("l2orderbook-details").innerText = `L2 Order Book详情：
时间：${formatDateTime(nearestL2Data.time)}
买盘：
${bidLevels.map((level) => `价格：${level.px}, 数量：${level.sz}`).join("\n")}
卖盘：
${askLevels.map((level) => `价格：${level.px}, 数量：${level.sz}`).join("\n")}`;
                                } else {
                                    document.getElementById("l2orderbook-details").innerText = "L2 Order Book数据结构不完整。";
                                }
                            } else {
                                document.getElementById("l2orderbook-details").innerText = "无L2 Order Book详情。";
                            }
                        } else {
                            document.getElementById("order-details").innerText = "将鼠标悬停在图表上查看订单详情。";
                            document.getElementById("l2orderbook-details").innerText = "将鼠标悬停在图表上查看L2 Order Book详情。";
                        }
                    });

                    // 修改 dataZoom 事件处理
                    myChart.on("dataZoom", function (params) {
                        let startTime, endTime;

                        const xAxis = myChart.getModel().getComponent("xAxis");
                        if (xAxis && xAxis.axis) {
                            const extent = xAxis.axis.scale.getExtent();
                            startTime = extent[0];
                            endTime = extent[1];
                        } else {
                            return; // 如果无法获取有效的时间范围，直接返回
                        }

                        // 过滤当前视窗内的 L2 数据
                        const visibleBidPrices = originalBidPrices.filter((item) => item && item[0] >= startTime && item[0] <= endTime);
                        const visibleAskPrices = originalAskPrices.filter((item) => item && item[0] >= startTime && item[0] <= endTime);

                        // 对可见数据进行抽样
                        const maxDataPoints = 1000;
                        const sampledBidPrices = adaptiveSampling(visibleBidPrices, maxDataPoints);
                        const sampledAskPrices = adaptiveSampling(visibleAskPrices, maxDataPoints);

                        // 更新系列数据
                        myChart.setOption({
                            series: [
                                { name: "买单", data: buyOrders.map((item) => item.value) }, // 保持买卖单数据不变
                                { name: "卖单", data: sellOrders.map((item) => item.value) },
                                { name: "买一价", data: sampledBidPrices },
                                { name: "卖一价", data: sampledAskPrices },
                            ],
                        });
                    });
                } catch (error) {
                    console.error("更新图表失败:", error);
                    alert("更新图表失败，请重试");
                }
            }

            // 格式化日期时间，显示到毫秒，使用京时间
            function formatDateTime(timestamp) {
                const date = new Date(timestamp + 8 * 3600 * 1000); // 北京时间偏移量
                const yyyy = date.getUTCFullYear();
                const MM = String(date.getUTCMonth() + 1).padStart(2, "0");
                const dd = String(date.getUTCDate()).padStart(2, "0");
                const hh = String(date.getUTCHours()).padStart(2, "0");
                const mm = String(date.getUTCMinutes()).padStart(2, "0");
                const ss = String(date.getUTCSeconds()).padStart(2, "0");
                const SSS = String(date.getUTCMilliseconds()).padStart(3, "0");
                return `${yyyy}-${MM}-${dd} ${hh}:${mm}:${ss}.${SSS}`;
            }

            // 格式化日期时间，不显示毫秒，使用北京时间
            function formatDateTimeWithoutMilliseconds(timestamp) {
                const date = new Date(timestamp + 8 * 3600 * 1000); // 北京时间偏移量
                const yyyy = date.getUTCFullYear();
                const MM = String(date.getUTCMonth() + 1).padStart(2, "0");
                const dd = String(date.getUTCDate()).padStart(2, "0");
                const hh = String(date.getUTCHours()).padStart(2, "0");
                const mm = String(date.getUTCMinutes()).padStart(2, "0");
                const ss = String(date.getUTCSeconds()).padStart(2, "0");
                return `${yyyy}-${MM}-${dd} ${hh}:${mm}:${ss}`;
            }

            // 页面加载完成后执行
            document.addEventListener("DOMContentLoaded", loadOrders);

            // 绑定事件
            document.getElementById("update-chart").addEventListener("click", updateChart);

            // 获取最近的订单
            function getNearestOrder(orders, targetTime) {
                let nearest = null;
                let minDiff = Infinity;
                orders.forEach((order) => {
                    const orderTime = Date.parse(order.UpdateTime);
                    const diff = Math.abs(orderTime - targetTime);
                    if (diff < minDiff) {
                        minDiff = diff;
                        nearest = order;
                    }
                });
                return nearest;
            }

            // 获取最近的L2数据
            function getNearestL2Data(l2DataList, targetTime) {
                if (!Array.isArray(l2DataList) || l2DataList.length === 0) {
                    return null;
                }

                let nearest = null;
                let minDiff = Infinity;

                l2DataList.forEach((entry) => {
                    if (entry && entry.time) {
                        const diff = Math.abs(entry.time - targetTime);
                        if (diff < minDiff) {
                            minDiff = diff;
                            nearest = entry;
                        }
                    }
                });

                // 如果时间差太大（比如超过1分钟），返回null
                if (minDiff > 60000) {
                    return null;
                }

                return nearest;
            }

            // 修改 adaptiveSampling 函数，添加数据验证
            function adaptiveSampling(dataArray, maxPoints) {
                if (!Array.isArray(dataArray) || dataArray.length === 0) {
                    return [];
                }

                if (dataArray.length <= maxPoints) {
                    return dataArray;
                }

                const sampledData = [];
                const samplingInterval = dataArray.length / maxPoints;

                for (let i = 0; i < dataArray.length; i += samplingInterval) {
                    const index = Math.floor(i);
                    if (dataArray[index] && Array.isArray(dataArray[index])) {
                        sampledData.push(dataArray[index]);
                    }
                }

                return sampledData;
            }
        </script>
    </body>
</html>

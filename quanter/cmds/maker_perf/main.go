package main

import (
	"archive/zip"
	"bufio"
	_ "embed"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/wizhodl/quanter/exchange"
)

//go:embed index.html
var indexHTML []byte

type Config struct {
	Port      int
	OrdersDir string
	L2BookDir string
}

func main() {
	// 解析命令行参数
	config := parseFlags()

	r := gin.Default()

	r.GET("/", func(c *gin.Context) {
		c.Data(http.StatusOK, "text/html; charset=utf-8", indexHTML)
	})

	// orders 接口
	r.GET("/orders", func(c *gin.Context) {
		files, err := os.ReadDir(config.OrdersDir)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		allOrders := make(map[string][]*exchange.Order)
		for _, file := range files {
			if file.IsDir() || !strings.HasSuffix(file.Name(), ".maker_orders") {
				continue
			}

			filePath := filepath.Join(config.OrdersDir, file.Name())
			data, err := os.ReadFile(filePath)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}

			var ordersMap map[string][]*exchange.Order
			if err := json.Unmarshal(data, &ordersMap); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}

			for mm, mmOrders := range ordersMap {
				// 过滤掉 ExecQty 为 0 的订单
				var filteredOrders []*exchange.Order
				for _, order := range mmOrders {
					if order.ExecQty > 0 {
						filteredOrders = append(filteredOrders, order)
					}
				}

				if _, ok := allOrders[mm]; !ok {
					allOrders[mm] = filteredOrders
				} else {
					c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("duplicated market maker: %s", mm)})
					return
				}
			}
		}

		c.JSON(http.StatusOK, allOrders)
	})

	// l2book 接口
	r.GET("/l2book", func(c *gin.Context) {
		symbol := c.Query("symbol")
		startTime := c.Query("startTime")
		endTime := c.Query("endTime")

		// 替换 symbol 中的 /
		symbol = strings.ReplaceAll(symbol, "/", "_")
		if symbol == "" {
			c.JSON(400, gin.H{"error": "symbol is required"})
			return
		}

		start, err := strconv.ParseInt(startTime, 10, 64)
		if err != nil {
			c.JSON(400, gin.H{"error": "invalid startTime"})
			return
		}

		end, err := strconv.ParseInt(endTime, 10, 64)
		if err != nil {
			c.JSON(400, gin.H{"error": "invalid endTime"})
			return
		}

		result, err := readL2BookFiles(config.L2BookDir, symbol, time.Unix(start, 0), time.Unix(end, 0))
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
			return
		}

		if len(result) == 0 {
			c.JSON(200, gin.H{}) // 当没有数据时返回空对象
			return
		}

		c.JSON(200, result)
	})

	r.Run(fmt.Sprintf(":%d", config.Port))
}

func parseFlags() *Config {
	config := &Config{}
	flag.IntVar(&config.Port, "port", 8080, "server port")
	flag.StringVar(&config.OrdersDir, "orders_dir", "", "orders directory path")
	flag.StringVar(&config.L2BookDir, "l2book_dir", "", "l2book directory path")
	flag.Parse()
	return config
}

func readL2BookFiles(baseDir, symbol string, startTime, endTime time.Time) ([]map[string]interface{}, error) {
	var result []map[string]interface{}

	// 遍历时间范围内的每一天
	for d := startTime; !d.After(endTime); d = d.Add(24 * time.Hour) {
		date := d.Format("20060102")

		// 遍历每小时
		for h := 0; h < 24; h++ {
			hour := fmt.Sprintf("%d", h)
			path := filepath.Join(baseDir, "market_data", date, hour, "l2Book", symbol)

			// 尝试读取普通文件
			if data, err := readL2BookFile(path); err == nil {
				result = append(result, data...)
				continue
			}

			// 尝试读取 zip 文件
			if data, err := readL2BookZipFile(path + ".zip"); err == nil {
				result = append(result, data...)
			}
		}
	}

	return result, nil
}

func readL2BookFile(path string) ([]map[string]interface{}, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	return parseL2BookContent(file)
}

func readL2BookZipFile(path string) ([]map[string]interface{}, error) {
	reader, err := zip.OpenReader(path)
	if err != nil {
		return nil, err
	}
	defer reader.Close()

	if len(reader.File) == 0 {
		return nil, fmt.Errorf("empty zip file")
	}

	rc, err := reader.File[0].Open()
	if err != nil {
		return nil, err
	}
	defer rc.Close()

	fmt.Println("read zip file", path)
	return parseL2BookContent(rc)
}

func parseL2BookContent(reader io.Reader) ([]map[string]interface{}, error) {
	var result []map[string]interface{}
	scanner := bufio.NewScanner(reader)

	for scanner.Scan() {
		var data map[string]interface{}
		if err := json.Unmarshal(scanner.Bytes(), &data); err != nil {
			return nil, err
		}

		// 获取原始数据中的 levels
		if raw, ok := data["raw"].(map[string]interface{}); ok {
			if rawData, ok := raw["data"].(map[string]interface{}); ok {
				if levels, ok := rawData["levels"].([]interface{}); ok && len(levels) >= 2 {
					// 只保留买一价和卖一价
					bidLevels := levels[0].([]interface{})
					askLevels := levels[1].([]interface{})

					// 创建新的 levels 数组
					newLevels := make([][]interface{}, 2)
					if len(bidLevels) > 0 {
						newLevels[0] = []interface{}{bidLevels[0]} // 只保留买一价
					} else {
						newLevels[0] = []interface{}{}
					}
					if len(askLevels) > 0 {
						newLevels[1] = []interface{}{askLevels[0]} // 只保留卖一价
					} else {
						newLevels[1] = []interface{}{}
					}

					// 更新 levels 数据
					rawData["levels"] = newLevels
				}
			}
		}

		result = append(result, data)
	}

	return result, scanner.Err()
}

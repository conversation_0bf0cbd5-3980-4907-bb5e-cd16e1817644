package arbitrage

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

// 设置配置项命令

type ArbitragerCommand struct {
	command.Command
	arbitrager *ArbitragerController
}

func CheckCommandExecuterAndUpdateBalance(arbitrager *ArbitragerController) bool {
	if arbitrager.Executer == nil {
		arbitrager.ErrorMsgf("交易所还没有初始化，请稍等。")
		return false
	}
	// 尝试重新获取数量快照
	// 如果发现 Book 中的 balance 和 available 数量出现问题，只需要取消全部挂单重新挂单，即可触发重新获取数量快照
	// 函数内会检查是否有正在运行的指令，如果有正在运行的指令，会直接返回什么也不做
	go arbitrager.UpdateBalance(false)
	return true
}

type ArbitragerExecuterCommand struct {
	command.Command
	arbitrager *ArbitragerController
}

func (this *ArbitragerExecuterCommand) PrePrepare() bool {
	return CheckCommandExecuterAndUpdateBalance(this.arbitrager)
}

func (this *ArbitragerExecuterCommand) PreDo() bool {
	return CheckCommandExecuterAndUpdateBalance(this.arbitrager)
}

// 启动程序
type LaunchArbitragerCommand ArbitragerCommand

func NewLaunchArbitragerCommand(arbitrager *ArbitragerController) *LaunchArbitragerCommand {
	cmd := &LaunchArbitragerCommand{
		Command: command.Command{
			Name:            "launch",
			Instruction:     "`.launch Password GoogleAuthCode ` 启动程序",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          3,
			AuthcodePos:     -1, // launch 命令不能在解密主密码前校验 authcode，所以把 authCode 当成是普通参数处理，跳过统一的校验流程
			Sensitive:       true,
		},
		arbitrager: arbitrager,
	}
	return cmd
}

func (this *LaunchArbitragerCommand) Do() bool {
	password := this.Args[0]
	authCode := this.Args[1]
	success := this.arbitrager.Launch(password, authCode)
	if success {
		go this.arbitrager.InitArbitrager()
		this.SendMsgf("启动成功。")
	}
	return true
}

// 当前程序运行状态
type StatusArbitragerCommand struct {
	ArbitragerExecuterCommand
}

func NewStatusArbitragerCommand(arbitrager *ArbitragerController) *StatusArbitragerCommand {
	cmd := &StatusArbitragerCommand{
		ArbitragerExecuterCommand: ArbitragerExecuterCommand{
			Command: command.Command{
				Name:            "status",
				Alias:           []string{"s"},
				Instruction:     "`.status(+) running/ended/all/{InstructionID}(可选) detail(可选)` 查看运行状态",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          2,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *StatusArbitragerCommand) Do() bool {
	isDetail := false
	instructionID := ""
	status := InstructionStatusRunning
	if len(this.Args) >= 1 {
		if s, err := ConvertInstructionStatus(this.Args[0]); err != nil {
			instructionID = strings.ToUpper(this.Args[0])
			if _, _, _, err := ParseInstructionID(instructionID); err != nil {
				this.ErrorMsgf("指令格式不正确：%s", err)
				return false
			} else {
				status = InstructionStatusAll // 如果要查询 InstructionID，默认用 all 状态查询，用 running 可能查不到
			}
		} else {
			status = s
		}
	}
	if len(this.Args) == 2 && strings.EqualFold(strings.ToLower(this.Args[1]), "detail") {
		isDetail = true
	}
	this.arbitrager.SendStatus(status, isDetail, this.IsMore(), instructionID)
	return true
}

type MarginRatioControllerCommand ArbitragerExecuterCommand

func NewMarginRatioControllerCommand(controller *ArbitragerController) *MarginRatioControllerCommand {
	cmd := &MarginRatioControllerCommand{
		Command: command.Command{
			Name:            "marginRatio",
			Alias:           []string{"mr"},
			Instruction:     "`.marginRatio` 查看保证金比例",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		arbitrager: controller,
	}
	return cmd
}

func (this *MarginRatioControllerCommand) Do() bool {
	this.arbitrager.CheckMarginRatio(0, true)
	return true
}

// 打印程序运行参数
type ParametersArbitragerCommand ArbitragerExecuterCommand

func NewParametersArbitragerCommand(arbitrager *ArbitragerController) *ParametersArbitragerCommand {
	cmd := &ParametersArbitragerCommand{
		Command: command.Command{
			Name:            "config",
			Alias:           []string{"cfg"},
			Instruction:     "`.config` 查看当前运行参数",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		arbitrager: arbitrager,
	}
	return cmd
}

func (this *ParametersArbitragerCommand) Do() bool {
	this.SendMsgf("Build: %s, 运行参数\n```%s```", this.arbitrager.BuildInfo(), this.arbitrager.Config.ToTable(false))
	this.SendMsgf("\n```%s```", this.arbitrager.Config.ConfigSaveToTable())
	return true
}

type SetConfigArbitragerCommand ArbitragerCommand

func NewSetConfigArbitragerCommand(arbitrager *ArbitragerController) *SetConfigArbitragerCommand {
	cmd := &SetConfigArbitragerCommand{
		Command: command.Command{
			Name:            "setConfig",
			Alias:           []string{"sc"},
			Instruction:     "`.setConfig field1=value1,field2=value2` 设置配置 Field 字段值",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
		},
		arbitrager: arbitrager,
	}
	return cmd
}

func (this *SetConfigArbitragerCommand) Prepare() bool {
	configStr := this.Args[0]
	if !strings.Contains(configStr, "=") {
		this.ErrorMsgf("请按 field=value 设置配置。")
		return false
	}

	config := &ArbitragerControllerConfig{}
	if configPairs, correctedConfigStr, err := baseconfig.ParseConfigsFromString(config, configStr); err != nil {
		this.ErrorMsgf("解析配置错误：%s", err)
		return false
	} else {
		for _, pair := range configPairs {
			fieldName := pair.Field

			if strings.EqualFold(fieldName, "MarginMode") {
				instructions := FilterInstructions(this.arbitrager.Instructions, InstructionStatusRunning, nil, nil)
				if len(instructions) > 0 {
					this.ErrorMsgf("有指令正在运行时，不能设置 CrossMode")
					return false
				}
			}
		}

		this.SendMsgf("设置配置 %s", correctedConfigStr)
		return true
	}
}

func (this *SetConfigArbitragerCommand) Do() bool {
	configStr := this.Args[0]
	if correctedConfigStr, err := this.arbitrager.SaveConfig(configStr); err != nil {
		this.ErrorMsgf("设置配置错误：%s", err)
		return false
	} else {
		this.SendMsgf("设置配置成功：%s", correctedConfigStr)
		return true
	}
}

type SetInstructionConfigArbitragerCommand struct {
	ArbitragerExecuterCommand
}

func NewSetInstructionConfigArbitragerCommand(arbitrager *ArbitragerController) *SetInstructionConfigArbitragerCommand {
	cmd := &SetInstructionConfigArbitragerCommand{
		ArbitragerExecuterCommand: ArbitragerExecuterCommand{
			Command: command.Command{
				Name:            "setInstructionConfig",
				Alias:           []string{"sic"},
				Instruction:     "`.setInstructionConfig InstructionID field1=value1,field2,value2` 设置指令的配置 Field 字段值",
				RequiresConfirm: true,
				ArgMin:          2,
				ArgMax:          2,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *SetInstructionConfigArbitragerCommand) Prepare() bool {
	configStr := this.Args[1]
	if !strings.Contains(configStr, "=") && !strings.Contains(configStr, "_") {
		this.ErrorMsgf("请按 field=value 设置配置，或者使用配置模板。")
		return false
	}
	if strings.HasPrefix(configStr, "_") {
		if saveValue, err := this.arbitrager.Config.GetConfigSave(configStr); err != nil {
			this.ErrorMsgf("配置模板错误：%s", err)
			return false
		} else {
			configStr = saveValue
		}
	}
	if configPairs, correctedConfigStr, err := baseconfig.ParseConfigsFromString(this.arbitrager.Config, configStr); err != nil {
		this.ErrorMsgf("解析配置错误：%s", err)
		return false
	} else {
		for _, pair := range configPairs {
			fieldName := pair.Field
			if strings.Contains("Splits/BNBFee", fieldName) {
				this.ErrorMsgf("指令开始运行后不能设置 %s", fieldName)
				return false
			}
		}
		this.SendMsgf("设置配置 %s", correctedConfigStr)
		return true
	}
}

func (this *SetInstructionConfigArbitragerCommand) Do() bool {
	instructionID := strings.ToUpper(this.Args[0])
	if instructions := FilterInstructions(this.arbitrager.Instructions, InstructionStatusRunning, nil, &instructionID); len(instructions) != 1 {
		this.ErrorMsgf("没有正在执行的指令： %s。通过 `.status %s` 可以查询命令状态。", instructionID, instructionID)
		return false
	} else {
		instruction := instructions[0]
		config := instruction.GetConfig()
		configStr := this.Args[1]
		if strings.HasPrefix(configStr, "_") {
			if saveValue, err := this.arbitrager.Config.GetConfigSave(configStr); err != nil {
				this.ErrorMsgf("配置模板错误：%s", err)
				return false
			} else {
				configStr = saveValue
			}
		}
		if configPairs, correctedConfigStr, err := baseconfig.SetConfigWithString(config, configStr); err != nil {
			this.ErrorMsgf("设置配置错误：%s", err)
			return false
		} else {
			this.SendMsgf("设置配置成功：%s", correctedConfigStr)
			for _, pair := range configPairs {
				fieldName := pair.Field
				if strings.EqualFold(fieldName, "Timeout") {
					instruction.SetTimeout(config.Timeout)
				}
			}
		}
		return true
	}
}

func (this *ArbitragerController) InitArbitrager() {
	apiSecret := this.GetApiSecret()
	withdrawApiSecret := this.GetWithdrawApiSecret()
	var backOff backoff.BackOff
	exp := backoff.NewExponentialBackOff()
	exp.InitialInterval = time.Second * 10
	exp.MaxInterval = time.Minute * 5
	exp.MaxElapsedTime = 0 // It never stops if MaxElapsedTime == 0.
	backOff = exp
	alerted := false
	backoff.Retry(func() error {
		if this.IsClosed() {
			return nil
		}
		err := this.InitAPI(apiSecret, withdrawApiSecret)
		if err != nil {
			this.Warnf("init api failed, error: %s", err)
			if !alerted { // 警报一次
				this.ErrorMsgf("初始化 API 错误，稍后将自动重试。错误信息：%s", err)
				alerted = true
			}
		} else {
			if err := exchange.TestTranslateSymbolCode(this.Exchange); err != nil {
				this.ErrorMsgf("symbol code 测试不通过: %s", err)
			}
		}
		return err
	}, backOff)
}

type SaveConfigArbitragerCommand ArbitragerCommand

func NewSaveConfigArbitragerCommand(arbitrager *ArbitragerController) *SaveConfigArbitragerCommand {
	cmd := &SaveConfigArbitragerCommand{
		Command: command.Command{
			Name:            "saveConfig",
			Instruction:     "`.saveConfig saveName field1=value1,field2=value2` 保存配置模板",
			RequiresConfirm: true,
			ArgMin:          2,
			ArgMax:          2,
		},
		arbitrager: arbitrager,
	}
	return cmd
}

func (this *SaveConfigArbitragerCommand) Prepare() bool {
	saveName := this.Args[0]
	if !strings.HasPrefix(saveName, "_") {
		saveName = fmt.Sprintf("_%s", saveName)
	}
	configStr := this.Args[1]
	if !strings.Contains(configStr, "=") {
		this.ErrorMsgf("请按 field=value 设置配置。")
		return false
	}

	if _, correctedConfigStr, err := baseconfig.ParseConfigsFromString(this.arbitrager.Config, configStr); err != nil {
		this.ErrorMsgf("解析配置错误：%s", err)
		return false
	} else {
		this.SendMsgf("设置配置模板: %s => %s", saveName, correctedConfigStr)
		return true
	}
}

func (this *SaveConfigArbitragerCommand) Do() bool {
	config := this.arbitrager.Config
	saveName := this.Args[0]
	if !strings.HasPrefix(saveName, "_") {
		saveName = fmt.Sprintf("_%s", saveName)
	}
	configStr := this.Args[1]
	if correctedConfigStr, err := config.SaveConfig(saveName, configStr); err != nil {
		this.ErrorMsgf("保存配置模板错误：%s", err)
		return false
	} else {
		this.SendMsgf("保存设置模板成功：%s => %s", saveName, correctedConfigStr)
		config.Save()
		go config.Upload()
		return true
	}
}

type DeleteConfigSaveArbitragerCommand ArbitragerCommand

func NewDeleteConfigSaveArbitragerCommand(arbitrager *ArbitragerController) *DeleteConfigSaveArbitragerCommand {
	cmd := &DeleteConfigSaveArbitragerCommand{
		Command: command.Command{
			Name:            "deleteConfigSave",
			Instruction:     "`.deleteConfigSave saveName authCode` 删除配置模板",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          2,
			AuthcodePos:     2,
		},
		arbitrager: arbitrager,
	}
	return cmd
}

func (this *DeleteConfigSaveArbitragerCommand) Do() bool {
	config := this.arbitrager.Config
	saveName := this.Args[0]
	if !strings.HasPrefix(saveName, "_") {
		saveName = fmt.Sprintf("_%s", saveName)
	}
	if err := config.DeleteConfigSave(saveName); err != nil {
		this.ErrorMsgf("删除配置模板错误：%s", err)
		return false
	} else {
		this.SendMsgf("删除配置模板成功：%s", saveName)
		config.Save()
		go config.Upload()
		return true
	}
}

func (this *ArbitragerController) RenderAssets() string {

	type AssetItem struct {
		time       time.Time
		totalAsset float64
		asset      float64
		comment    string
	}
	instructions := this.Instructions

	// 倒序打印，最新的时间在最上面
	keys := make([]int64, len(this.storage.Assets))
	i := 0
	for k := range this.storage.Assets {
		keys[i] = k
		i++
	}
	sort.SliceStable(keys, func(i, j int) bool {
		return keys[i] < keys[j]
	})

	assetItemList := []AssetItem{}
	for i, k := range keys {
		assetItem := AssetItem{}
		assetItem.time = time.Unix(k, 0)
		assetItem.asset = this.storage.Assets[k]
		assetItem.totalAsset = assetItem.asset
		assetItemList = append(assetItemList, assetItem)

		ealierTime := time.Unix(0, 0) //  如果是倒数第二个，将 Unit(0, 0) 设为更早时间
		if i > 0 {
			ealierTime = time.Unix(keys[i-1], 0)
		}

		type InstructionAsset struct {
			time          time.Time
			asset         float64
			instructionID string
		}
		instructionAssetList := []InstructionAsset{}
		for _, instruction := range instructions {
			// 跳过开仓
			if instruction.GetDirection() == ArbiDirectionOpen {
				continue
			}
			report := instruction.GetReport()
			finishTime := report.EndTime
			if finishTime != nil && assetItem.time.Sub(*finishTime) > 0 && finishTime.Sub(ealierTime) > 0 {
				if instruction.GetFinishProgress() > 0 {
					instructionAssetList = append(instructionAssetList, InstructionAsset{time: *finishTime, asset: report.AccountWorthAfter, instructionID: instruction.GetID()})
				}
			}
		}
		sort.SliceStable(instructionAssetList, func(i, j int) bool {
			i1 := instructionAssetList[i]
			i2 := instructionAssetList[j]
			return i1.time.Before(i2.time)
		})
		for _, item := range instructionAssetList {
			instructionAssetItem := AssetItem{}
			instructionAssetItem.time = item.time
			instructionAssetItem.totalAsset = item.asset
			instructionAssetItem.asset = item.asset
			instructionAssetItem.comment = item.instructionID
			assetItemList = append(assetItemList, instructionAssetItem)
		}
	}

	t := NewTable()
	// 打印资产快照和对应的趋势机开平仓
	t.SetHeader([]string{"Time", "Total Asset", "Asset", "Close Instruction"})

	sort.SliceStable(assetItemList, func(i, j int) bool {
		return assetItemList[i].time.After(assetItemList[j].time)
	})

	for _, item := range assetItemList {
		t.AddRow([]string{utils.FormatShortTimeStr(&item.time, true), fmt.Sprintf("%.2f USDT", item.totalAsset), fmt.Sprintf("%.2f USDT", item.asset), item.comment})
	}

	assetsMsg := "[No Assets]"
	if len(t.Rows) > 1 {
		assetsMsg = t.Render()
	}
	return assetsMsg
}

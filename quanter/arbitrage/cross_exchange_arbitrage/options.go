package cross_exchange_arbitrage

import (
	"fmt"
	"time"

	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/exchange"
)

const EnableMarginRatioFastChange = false
const EnableCheckMarginRatioOption = false

const LoopInterval = 5 * time.Second
const MonitorLimit = 120                   // 5 秒钟一个数据点的话，总共约 10 分钟的高频数据
const PositionHistoryLimit = 7 * 24 * 3600 // 每小时记录一次，最多记录 7 天的仓位数据
const RebalanceTimeout = 10 * time.Minute
const EmptyPositionAutoRebalanceThreshold = 0.2 // 空仓时，如果保证金差异超过 20% ，自动平衡保证金
const MinMarginValue = 10
const SyncPositionErrorLimit = 5          // 同步仓位出错指定次数时，减少仓位
const SyncPositionErrorReduceRatio = 0.02 // 同步仓位出错减仓比例
const StoplossMAWindow = 5 * time.Minute  // 止损止盈订单的价格均线窗口
const OrderSplitInterval = 10             // 拆单间隔，单位秒
const OrderSplitValue = 1000.0            // 拆单金额，单位 usdt
const WashRequestLimit = 1000.0           // 自动刷量请求金额限制，如果剩余的金额小于这个值，则开始自动刷量 WashRequestLimit usdt
const AtCapDuration = 10 * time.Minute    // 交易所下单报持仓超过上限，在此时间内不再加仓
const InterTransferWait = 2 * time.Second
const SlowInterval = 5 * time.Minute               // 低频模式下的时间频率，用于执行一些不需频繁执行的任务
const SlowMod = int64(SlowInterval / LoopInterval) // 低频模式下的 loop 次数
const HourlySlowInterval = 1 * time.Hour           // Slow 低频模式下的时间频率，用于执行一些不需频繁执行的任务
const HourlySlowMod = int64(HourlySlowInterval / LoopInterval)
const DailySlowInterval = 24 * time.Hour
const DailySlowMod = int64(DailySlowInterval / LoopInterval)

const SnapshotRetentionPeriod = 24 * time.Hour * 14

const SnapshotHistoryTimeWindow = 2 * time.Minute

type ArbitragerOption string

const (
	OptionTargetMarginRatio     ArbitragerOption = "TargetMarginRatio"
	OptionRebalanceMarginRatio  ArbitragerOption = "RebalanceMarginRatio"
	OptionSplitInterval         ArbitragerOption = "SplitInterval"
	OptionStoplossInterval      ArbitragerOption = "StoplossInterval"
	OptionSplitValue            ArbitragerOption = "SplitValue"
	OptionWashRequestLimit      ArbitragerOption = "WashRequestLimit"
	OptionStoplossDiffTolerance ArbitragerOption = "StoplossDiffTolerance"
)

const (
	ParamTotalMarginRatioLowTimeWindow    = 10 * time.Minute
	ParamTotalMarginRatioLowCountLimit    = 10
	ParamMarginRatioLowTimeWindow         = 5 * time.Minute
	ParamMarginRatioLowCountLimit         = 10
	ParamMarginRatioFastChangeTimeWindow  = 5 * time.Minute
	ParamMarginRatioFastChangeDangerDelta = 0.3 // 30%
	ParamPriceVolatilityTimeWindow        = 5 * time.Minute
	ParamPriceVolatilityRatio             = 0.2             // 20%
	ParamPriceDiffTimeWindow              = 2 * time.Minute // 两分钟内价格差超过 0.5% 则认为价格波动较大
	ParamPriceDiffRatio                   = 0.005           // 0.5%
	ParamPriceDiffCountLimit              = 5
	ParamMarkPriceDiffTimeWindow          = 1 * time.Minute // 一分钟内价格差超过 0.5% 则认为价格波动较大
	ParamMarkPriceDiffRatio               = 0.005           // 0.5%
	ParamMarkPriceDiffCountLimit          = 5
	// 默认杠杆，这个杠杆率对部分交易所有效，比如 binance/bybit 部分品种需要使用更小的杠杆率来保证可以开更大的头寸
	// hyperliquid 统一限制了高风险币种的杠杆率，一般较低的是 3x，因此 hyperliquid 不太需需要单独设置品种的杠杆率，只需要默认调到最大即可
	// bybit 和 binance，对于高风险币种，默认给的杠杆率可能很高，比如 50x，但是使用 10x 以上杠杆的时候，可能只能开很小的头寸，这和我们的需求就会有冲突；也就是说为了在 bybit/binance 上开更大的头寸，需要使用更小的杠杆率
	// 因此，对于 bybit/binance，我们设置默认杠杆率为 5x，并且允许用户使用 SetParam 来设置单个品种的杠杆率
	ParamLeverage            = 5.0
	ParamStoplossRatio       = 0.05  // 止损止盈订单的价格波动比例
	StoplossDiffTolerance    = 0.1   // 止损止盈订单的价格差容忍度，10% 的止损止盈价格差
	ParamStoplossSlippage    = 0.001 // 止损止盈订单的价格滑点
	ParamStoplossInterval    = 30    // 止损止盈订单的更新间隔
	ParamTakeProfitPriceDiff = 0.005 // 止盈价差，0.5%
	ParamAlertPriceDiffRatio = 0.005 // 价格波动预警，0.5%
)

type ParamType string

const (
	ParamTypePriceDiff           ParamType = "PriceDiff"
	ParamTypeStoploss            ParamType = "Stoploss"
	ParamTypeStoplossSlippage    ParamType = "StoplossSlippage"
	ParamTypeMarkPriceDiff       ParamType = "MarkPriceDiff"
	ParamTypeLeverageLeft        ParamType = "LeverageLeft"
	ParamTypeLeverageRight       ParamType = "LeverageRight"
	ParamTypeSplitValue          ParamType = "SplitValue"
	ParamTypeAlertPriceDiff      ParamType = "AlertPriceDiff"
	ParamTypeTakeProfitPriceDiff ParamType = "TakeProfitPriceDiff"
)

type Options struct {
	option.Options
	TargetMarginRatio      float64
	RebalanceMarginRatio   float64
	SplitInterval          int
	SplitValue             float64
	StoplossUpdateInterval int
	StoplossRatio          float64
	LeverageLeft           float64
	LeverageRight          float64
	PriceDiff              float64
	MarkPriceDiff          float64
	EnablePriceVolatility  bool
	WashRequestLimit       float64
	StoplossDiffTolerance  float64
	AlertPriceDiff         float64
	TakeProfitPriceDiff    float64
}

func getCrossExchangeArbitragerOptionDefinitions() []*option.TypedOption {
	return []*option.TypedOption{
		option.NewTypedOption(string(OptionTargetMarginRatio), option.Float64, 0.2, "", true),
		option.NewTypedOption(string(OptionRebalanceMarginRatio), option.Float64, 0.1, "", true),
		option.NewTypedOption(string(OptionStoplossInterval), option.Int, ParamStoplossInterval, "s", false),
		option.NewTypedOption(string(OptionSplitInterval), option.Int, OrderSplitInterval, "s", false),
		option.NewTypedOption(string(OptionSplitValue), option.Float64, OrderSplitValue, "", false),
		option.NewTypedOption(string(ParamTypePriceDiff), option.Float64, ParamPriceDiffRatio, "", true),
		option.NewTypedOption(string(ParamTypeMarkPriceDiff), option.Float64, ParamMarkPriceDiffRatio, "", true),
		option.NewTypedOption(string(ParamTypeStoploss), option.Float64, ParamStoplossRatio, "", true),
		option.NewTypedOption(string(OptionStoplossDiffTolerance), option.Float64, StoplossDiffTolerance, "", true),
		option.NewTypedOption(string(ParamTypeLeverageLeft), option.Float64, ParamLeverage, "", false),
		option.NewTypedOption(string(ParamTypeLeverageRight), option.Float64, ParamLeverage, "", false),
		option.NewTypedOption(string(OptionWashRequestLimit), option.Float64, WashRequestLimit, "", false),
		option.NewTypedOption(string(ParamTypeAlertPriceDiff), option.Float64, ParamAlertPriceDiffRatio, "", true),
		option.NewTypedOption(string(ParamTypeTakeProfitPriceDiff), option.Float64, ParamTakeProfitPriceDiff, "", true),
	}
}

func NewCrossExchangeArbitragerOptions(optStr string) (*Options, error) {
	opts, err := option.NewOptions(optStr, getCrossExchangeArbitragerOptionDefinitions()...)
	if err != nil {
		return nil, err
	}
	return &Options{Options: *opts}, nil
}

func (this *CrossExchangeArbitrager) GetOptions() *Options {
	return this.Options
}

func (this *CrossExchangeArbitrager) GetOptionDefinitions() []*option.TypedOption {
	return this.Options.GetDefinitions()
}

func (this *CrossExchangeArbitrager) UpdateOptions(opts *option.Options) {
	this.Options.Options = *opts
	this.Options.TargetMarginRatio = opts.Get(string(OptionTargetMarginRatio)).(float64)
	this.Options.RebalanceMarginRatio = opts.Get(string(OptionRebalanceMarginRatio)).(float64)
	this.Options.SplitInterval = opts.Get(string(OptionSplitInterval)).(int)
	this.Options.SplitValue = opts.Get(string(OptionSplitValue)).(float64)
	this.Options.StoplossUpdateInterval = opts.Get(string(OptionStoplossInterval)).(int)
	this.Options.StoplossRatio = opts.Get(string(ParamTypeStoploss)).(float64)
	this.Options.StoplossDiffTolerance = opts.Get(string(OptionStoplossDiffTolerance)).(float64)
	this.Options.PriceDiff = opts.Get(string(ParamTypePriceDiff)).(float64)
	this.Options.MarkPriceDiff = opts.Get(string(ParamTypeMarkPriceDiff)).(float64)
	this.Options.LeverageLeft = opts.Get(string(ParamTypeLeverageLeft)).(float64)
	this.Options.LeverageRight = opts.Get(string(ParamTypeLeverageRight)).(float64)
	this.Options.WashRequestLimit = opts.Get(string(OptionWashRequestLimit)).(float64)
	this.Options.AlertPriceDiff = opts.Get(string(ParamTypeAlertPriceDiff)).(float64)
	this.Options.TakeProfitPriceDiff = opts.Get(string(ParamTypeTakeProfitPriceDiff)).(float64)
}

func (this *CrossExchangeArbitrager) LoadOptions() error {
	defs := getCrossExchangeArbitragerOptionDefinitions()
	err := this.Options.Load(defs...)
	if err != nil {
		return fmt.Errorf("load options failed: %s", err)
	}
	this.UpdateOptions(&this.Options.Options)
	return nil
}

func (this *CrossExchangeArbitrager) ValidateOptions(opts *option.Options) error {
	if opts.Get(string(OptionTargetMarginRatio)).(float64) <= 0 || opts.Get(string(OptionTargetMarginRatio)).(float64) >= 1 {
		return fmt.Errorf("option TargetMarginRatio must be between 0 and 1")
	}
	if opts.Get(string(OptionRebalanceMarginRatio)).(float64) <= 0 || opts.Get(string(OptionRebalanceMarginRatio)).(float64) >= 1 {
		return fmt.Errorf("option RebalanceMarginRatio must be between 0 and 1")
	}
	if opts.Get(string(OptionRebalanceMarginRatio)).(float64) >= opts.Get(string(OptionTargetMarginRatio)).(float64) {
		return fmt.Errorf("option RebalanceMarginRatio must be less than TargetMarginRatio")
	}
	if opts.Get(string(OptionSplitInterval)).(int) <= 0 {
		return fmt.Errorf("option SplitInterval must be greater than 0")
	}
	if opts.Get(string(OptionSplitValue)).(float64) <= 0 || opts.Get(string(OptionSplitValue)).(float64) > 10000 {
		return fmt.Errorf("option SplitValue must be greater than 0 and less than 10000")
	}
	if opts.Get(string(OptionStoplossInterval)).(int) <= 0 || opts.Get(string(OptionStoplossInterval)).(int) > int(StoplossMAWindow/time.Second) {
		return fmt.Errorf("option StoplossUpdateInterval must be greater than 0 and less than %d", int(StoplossMAWindow/time.Second))
	}
	if opts.Get(string(ParamTypeAlertPriceDiff)).(float64) < 0 || opts.Get(string(ParamTypeAlertPriceDiff)).(float64) > 1 {
		return fmt.Errorf("option AlertPriceDiff must be between 0 and 1")
	}
	if opts.Get(string(ParamTypeTakeProfitPriceDiff)).(float64) < 0 || opts.Get(string(ParamTypeTakeProfitPriceDiff)).(float64) > 1 {
		return fmt.Errorf("option TakeProfitPriceDiff must be between 0 and 1")
	}
	if opts.Get(string(ParamTypeStoploss)).(float64) <= 0 || opts.Get(string(ParamTypeStoploss)).(float64) >= 1 {
		return fmt.Errorf("option Stoploss must be between 0 and 1")
	}
	if opts.Get(string(OptionStoplossDiffTolerance)).(float64) <= 0 || opts.Get(string(OptionStoplossDiffTolerance)).(float64) >= 1 {
		return fmt.Errorf("option StoplossDiffTolerance must be between 0 and 1")
	}
	if opts.Get(string(ParamTypePriceDiff)).(float64) <= 0 || opts.Get(string(ParamTypePriceDiff)).(float64) >= 1 {
		return fmt.Errorf("option PriceDiff must be between 0 and 1")
	}
	if opts.Get(string(ParamTypeMarkPriceDiff)).(float64) <= 0 || opts.Get(string(ParamTypeMarkPriceDiff)).(float64) >= 1 {
		return fmt.Errorf("option MarkPriceDiff must be between 0 and 1")
	}
	if opts.Get(string(ParamTypeLeverageLeft)).(float64) <= 0 || opts.Get(string(ParamTypeLeverageLeft)).(float64) >= 100 {
		return fmt.Errorf("option LeverageLeft must be between 0 and 100")
	}
	if opts.Get(string(ParamTypeLeverageRight)).(float64) <= 0 || opts.Get(string(ParamTypeLeverageRight)).(float64) >= 100 {
		return fmt.Errorf("option LeverageRight must be between 0 and 100")
	}
	return nil
}

/* Params
* 用户可以首先设置全局的 option，作为 Params 的默认值，Params 主要是给品种自定义配置使用
 */

func (this *CrossExchangeArbitrager) getParamDefinitions() []*option.TypedOption {
	return []*option.TypedOption{
		{Name: string(ParamTypePriceDiff), Type: option.Float64, Default: this.Options.PriceDiff},
		{Name: string(ParamTypeStoploss), Type: option.Float64, Default: this.Options.StoplossRatio},
		{Name: string(ParamTypeMarkPriceDiff), Type: option.Float64, Default: this.Options.MarkPriceDiff},
		{Name: string(ParamTypeLeverageLeft), Type: option.Float64, Default: this.Options.LeverageLeft},
		{Name: string(ParamTypeLeverageRight), Type: option.Float64, Default: this.Options.LeverageRight},
		{Name: string(ParamTypeSplitValue), Type: option.Float64, Default: this.Options.SplitValue},
		{Name: string(ParamTypeAlertPriceDiff), Type: option.Float64, Default: this.Options.AlertPriceDiff},
		{Name: string(ParamTypeTakeProfitPriceDiff), Type: option.Float64, Default: this.Options.TakeProfitPriceDiff},
	}
}

// 加载风险参数，必须在 LoadOptions 之后，因为其中的值依赖于 options
func (this *CrossExchangeArbitrager) LoadParams() {
	this.SymbolParams.Range(func(key string, value *option.Options) bool {
		value.Load(this.getParamDefinitions()...)
		return true
	})
}

func (this *CrossExchangeArbitrager) UpdateParams(symbolCode string, params string) *option.Options {
	return this._updateParams(this.SymbolParams, symbolCode, params)
}

func (this *CrossExchangeArbitrager) _updateParams(symbolParams *exchange.SyncMapOf[string, *option.Options], symbolCode string, params string) *option.Options {
	if symbolParams == nil {
		symbolParams = &exchange.SyncMapOf[string, *option.Options]{}
	}

	options, ok := symbolParams.Load(symbolCode)
	if ok && options != nil && !options.IsZero() {
		options.UpdateFromString(params)
	} else {
		var err error
		options, err = this.NewParams(params)
		if err != nil {
			this.Errorf("set risk params failed, error: %s", err)
			return nil
		}
	}
	symbolParams.Store(symbolCode, options)
	return options
}

func (this *CrossExchangeArbitrager) GetParam(paramType ParamType, symbolCode string) (value any) {
	return this._getParam(this.SymbolParams, paramType, symbolCode)
}

func (this *CrossExchangeArbitrager) _getParam(params *exchange.SyncMapOf[string, *option.Options], paramType ParamType, symbolCode string) (value any) {
	options, ok := params.Load(symbolCode)
	if ok && options != nil && !options.IsZero() {
		value = options.Get(string(paramType))
	} else {
		defaultOptions, _ := this.NewParams("")
		value = defaultOptions.Default(string(paramType))
	}
	return
}

func (this *CrossExchangeArbitrager) NewParams(params string) (*option.Options, error) {
	return option.NewOptions(
		params,
		this.getParamDefinitions()...,
	)
}

package cross_exchange_arbitrage

import (
	"fmt"
	"math"
	"time"
)

const EnableStopLossStdev = false
const CloseOrderPriceStdevWindow = 5 * time.Minute // 止损止盈订单的价格标准差窗口
const CloseOrderPriceStdevMultiplier = 4.0         // 止损止盈订单的价格标准差倍数

func (this *CrossExchangeArbitrager) CalculateSymbolPriceStdev(symbol string, timeWindow time.Duration, isRight bool) (mean float64, stdDev float64, er error) {
	prices := []float64{}
	for _, snapshot := range this.Monitors {
		if snapshot.CreateTime.Add(timeWindow).Before(time.Now()) {
			continue
		}
		// 早于 MonitorRelaxAtTime 的数据不检查
		if snapshot.CreateTime.Before(this.MonitorRelaxAtTime) {
			continue
		}
		if isRight {
			symbolCtx, ok := snapshot.SymbolContextRight.Load(symbol)
			if ok {
				realPrice := symbolCtx.LastPrice
				if realPrice > 0 {
					prices = append(prices, realPrice)
				}
			}
		} else {
			symbolCtx, ok := snapshot.SymbolContextLeft.Load(symbol)
			if ok {
				realPrice := symbolCtx.LastPrice
				if realPrice > 0 {
					prices = append(prices, realPrice)
				}
			}
		}
	}
	limit := int(timeWindow / LoopInterval / 2)
	count := len(prices)
	if count < limit {
		er = fmt.Errorf("not enough data to calculate stdev, count: %d, limit: %d", count, limit)
		return
	}
	mean, stdDev = CalculateStdev(prices)
	if stdDev == 0 {
		er = fmt.Errorf("not enough data to calculate stdev, count: %d, limit: %d", count, limit)
		return
	}
	return
}

func CalculateStdev(prices []float64) (mean float64, stdDev float64) {
	// Need at least 2 prices for meaningful calculation
	if len(prices) < 2 {
		return
	}

	// Calculate mean
	var sum float64
	for _, price := range prices {
		sum += price
	}
	mean = sum / float64(len(prices))

	// Calculate standard deviation
	var squaredDiffs float64
	for _, price := range prices {
		diff := price - mean
		squaredDiffs += diff * diff
	}
	stdDev = math.Sqrt(squaredDiffs / float64(len(prices)-1))
	return
}

func CalculateATR(prices []float64, period int) float64 {
	if len(prices) < period+1 {
		return 0
	}

	// Calculate True Range (TR)
	tr := make([]float64, len(prices)-1)
	for i := 1; i < len(prices); i++ {
		tr[i-1] = math.Abs(prices[i] - prices[i-1])
	}

	// Calculate initial ATR
	var trSum float64
	for i := 0; i < period; i++ {
		trSum += tr[i]
	}
	atr := trSum / float64(period)

	// Calculate smoothed ATR
	for i := period; i < len(tr); i++ {
		atr = (atr*float64(period-1) + tr[i]) / float64(period)
	}

	return atr
}

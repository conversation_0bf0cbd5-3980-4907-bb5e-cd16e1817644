package cross_exchange_arbitrage

import (
	"fmt"
)

func (this *CrossExchangeArbitrager) SimulateRiskEvent(riskCategory RiskCategory) (er error) {
	if !this.controller.IsDebug() {
		this.WarnMsgf("当前不是 debug 模式，不执行模拟风险事件")
		return
	}
	this.Debugf("模拟风险事件: %s, equal: %v", riskCategory, riskCategory == RiskCategoryMarginRatioLow)

	switch riskCategory {
	case RiskCategoryMarginRatioFastChange:
		er = this.SimulateMarginRatioFastChangeRiskEvent()
	case RiskCategoryMarginValueIsZero:
		er = this.SimulateMarginValueIsZeroRiskEvent()
	case RiskCategoryTotalMarginRatioLow:
		er = this.SimulateTotalMarginRatioKeepLowRiskEvent()
	case RiskCategoryMarginRatioLow:
		er = this.SimulateMarginRatioKeepLowRiskEvent()
	case RiskCategoryPriceVolatility:
		er = this.SimulateSymbolPriceVolatilityRiskEvent()
	case RiskCategoryPriceDiff:
		er = this.SimulateSymbolPriceDiffTooLargeRiskEvent()
	case RiskCategoryMarkPriceDiff:
		er = this.SimulateSymbolMarkPriceDiffTooLargeRiskEvent()
	default:
		return fmt.Errorf("unknown risk category: %s", riskCategory)
	}
	return
}

// 单个交易所保证金率快速变化，可能因为整体行情出现较大波动
func (this *CrossExchangeArbitrager) SimulateMarginRatioFastChangeRiskEvent() (er error) {
	dangerDelta := ParamMarginRatioFastChangeDangerDelta
	if len(this.Monitors) == 0 {
		return fmt.Errorf("monitor data point not enough, at least 1 point")
	}
	lastSnapshot := this.Monitors[len(this.Monitors)-1]
	timeWindow := ParamMarginRatioFastChangeTimeWindow
	validMonitors := []*AccountSnapshot{}
	for _, monitor := range this.Monitors {
		if monitor.CreateTime.Add(timeWindow).Before(lastSnapshot.CreateTime) {
			continue
		}
		if monitor.CreateTime.Before(this.MonitorRelaxAtTime) {
			continue
		}
		validMonitors = append(validMonitors, monitor)
	}
	if len(validMonitors) < 2 {
		return fmt.Errorf("monitor data point not enough, at least 2 valid points in %s", timeWindow)
	}
	lastMonitor := validMonitors[len(validMonitors)-1]
	previousMonitor := validMonitors[len(validMonitors)-2]
	// 最后一个点的值要小于之前值才可以触发
	lastMonitor.MarginRatioLeft = this.Options.TargetMarginRatio * 0.9
	previousMonitor.MarginRatioLeft = lastMonitor.MarginRatioLeft * (1 + dangerDelta) * 1.1
	previousMonitor.MarginValueLeft = lastMonitor.MarginValueLeft * (1 + dangerDelta) * 1.1
	return nil
}

func (this *CrossExchangeArbitrager) SimulateMarginValueIsZeroRiskEvent() (err error) {
	this.AccountSnapshot.MarginValueLeft = 0
	return nil
}

func (this *CrossExchangeArbitrager) SimulateTotalMarginRatioKeepLowRiskEvent() (er error) {
	timeWindow := this.getTotalMarginRatioLowTimeWindow()
	countLimit := ParamTotalMarginRatioLowCountLimit
	if len(this.Monitors) == 0 {
		return fmt.Errorf("monitor data point not enough, at least 1 point")
	}
	lastSnapshot := this.Monitors[len(this.Monitors)-1]
	lastSnapshotTime := lastSnapshot.CreateTime
	validPointCount := 0
	for _, monitor := range this.Monitors {
		if monitor.CreateTime.Add(timeWindow).Before(lastSnapshotTime) {
			continue
		}
		// 早于 MonitorRelaxAtTime 的数据不检查
		if monitor.CreateTime.Before(this.MonitorRelaxAtTime) {
			continue
		}
		validPointCount++
	}
	if validPointCount < countLimit {
		return fmt.Errorf("monitor data point not enough, at least 10 points in %s, got %d", timeWindow, validPointCount)
	}
	for _, monitor := range this.Monitors {
		monitor.MarginRatioTotal = this.Options.TargetMarginRatio * 0.6
	}
	return nil
}

func (this *CrossExchangeArbitrager) SimulateMarginRatioKeepLowRiskEvent() (er error) {
	timeWindow := ParamMarginRatioLowTimeWindow
	countLimit := ParamMarginRatioLowCountLimit
	if len(this.Monitors) == 0 {
		return fmt.Errorf("monitor data point not enough, at least 1 point")
	}
	lastSnapshot := this.Monitors[len(this.Monitors)-1]
	lastSnapshotTime := lastSnapshot.CreateTime
	validPointCount := 0
	for _, snapshot := range this.Monitors {
		if snapshot.CreateTime.Add(timeWindow).Before(lastSnapshotTime) {
			continue
		}
		// 早于 MonitorRelaxAtTime 的数据不检查
		if snapshot.CreateTime.Before(this.MonitorRelaxAtTime) {
			continue
		}
		validPointCount++
	}
	if validPointCount < countLimit {
		return fmt.Errorf("monitor data point not enough, at least %d points in %s, got %d", countLimit, timeWindow, validPointCount)
	}
	for _, monitor := range this.Monitors {
		monitor.MarginRatioLeft = this.Options.RebalanceMarginRatio * 0.9
	}
	return nil
}

func (this *CrossExchangeArbitrager) SimulateSymbolPriceVolatilityRiskEvent() (er error) {
	if !this.Options.EnablePriceVolatility {
		this.Warnf("stop loss is disabled, skip simulate symbol price volatility risk event")
		return fmt.Errorf("stop loss is disabled, skip simulate symbol price volatility risk event")
	}
	timeWindow := ParamPriceVolatilityTimeWindow
	priceDelta := ParamPriceVolatilityRatio
	validPointCount := 0
	if len(this.Monitors) == 0 {
		return fmt.Errorf("monitor data point not enough, at least 1 point")
	}
	lastSnapshot := this.Monitors[len(this.Monitors)-1]
	for _, monitor := range this.Monitors {
		if monitor.CreateTime.Add(timeWindow).Before(lastSnapshot.CreateTime) {
			continue
		}
		validPointCount++
	}
	if validPointCount < 2 {
		return fmt.Errorf("monitor data point not enough, at least 2 points in %s", timeWindow)
	}
	previousMonitor := this.Monitors[len(this.Monitors)-2]
	// 只修改其中一个品种的价格即可
	previousMonitor.SymbolContextLeft.Range(func(symbol string, price *SymbolContext) bool {
		price.LastPrice = price.LastPrice * (1 - priceDelta) * 0.9
		return true
	})
	return nil
}

func (this *CrossExchangeArbitrager) SimulateSymbolPriceDiffTooLargeRiskEvent() (er error) {
	timeWindow := ParamPriceDiffTimeWindow
	priceDiffDelta := this.Options.PriceDiff
	priceDiffCountLimit := ParamPriceDiffCountLimit
	validPointCount := 0
	if len(this.Monitors) == 0 {
		return fmt.Errorf("monitor data point not enough, at least 1 point")
	}
	lastSnapshot := this.Monitors[len(this.Monitors)-1]
	for _, monitor := range this.Monitors {
		if monitor.CreateTime.Add(timeWindow).Before(lastSnapshot.CreateTime) {
			continue
		}
		validPointCount++
	}
	if validPointCount < priceDiffCountLimit {
		return fmt.Errorf("monitor data point not enough, at least 2 points in %s", timeWindow)
	}
	// 随机选择一个 symbol
	symbolLeft := ""
	lastSnapshot.SymbolContextLeft.Range(func(symbol string, ctx *SymbolContext) bool {
		symbolLeft = symbol
		return false
	})
	symbolRight, err := this.ConvertSymbol(symbolLeft, true)
	if err != nil {
		return fmt.Errorf("convert symbol failed, error: %s", err)
	}
	for i := 0; i < priceDiffCountLimit; i++ {
		// 把 symbolLeft 每个 monitor 的价格设置为 symbolRight 的 monitor 的价格的 90%
		monitor := this.Monitors[len(this.Monitors)-priceDiffCountLimit+i]
		contextRight, ok := monitor.SymbolContextRight.Load(symbolRight)
		if !ok {
			return fmt.Errorf("symbol context not found, symbol: %s", symbolRight)
		}
		priceRight := contextRight.LastPrice
		contextLeft, ok := monitor.SymbolContextLeft.Load(symbolLeft)
		if !ok {
			return fmt.Errorf("symbol context not found, symbol: %s", symbolLeft)
		}
		contextLeft.LastPrice = priceRight * (1 - priceDiffDelta) * 0.9
	}
	return nil
}

func (this *CrossExchangeArbitrager) SimulateSymbolMarkPriceDiffTooLargeRiskEvent() (er error) {
	timeWindow := ParamMarkPriceDiffTimeWindow
	priceDiffDelta := this.Options.MarkPriceDiff
	priceDiffCountLimit := ParamMarkPriceDiffCountLimit
	validPointCount := 0
	if len(this.Monitors) == 0 {
		return fmt.Errorf("monitor data point not enough, at least 1 point")
	}
	lastSnapshot := this.Monitors[len(this.Monitors)-1]
	for _, monitor := range this.Monitors {
		if monitor.CreateTime.Add(timeWindow).Before(lastSnapshot.CreateTime) {
			continue
		}
		validPointCount++
	}
	if validPointCount < priceDiffCountLimit {
		return fmt.Errorf("monitor data point not enough, at least 2 points in %s", timeWindow)
	}
	// 随机选择一个 symbol
	symbolLeft := ""
	lastSnapshot.SymbolContextLeft.Range(func(symbol string, ctx *SymbolContext) bool {
		symbolLeft = symbol
		return false
	})
	symbolRight, err := this.ConvertSymbol(symbolLeft, true)
	if err != nil {
		return fmt.Errorf("convert symbol failed, error: %s", err)
	}
	for i := 0; i < priceDiffCountLimit; i++ {
		// 把 symbolLeft 每个 monitor 的价格设置为 symbolRight 的 monitor 的价格的 90%
		monitor := this.Monitors[len(this.Monitors)-priceDiffCountLimit+i]
		contextRight, ok := monitor.SymbolContextRight.Load(symbolRight)
		if !ok {
			return fmt.Errorf("symbol context not found, symbol: %s", symbolRight)
		}
		priceRight := contextRight.MarkPrice
		contextLeft, ok := monitor.SymbolContextLeft.Load(symbolLeft)
		if !ok {
			return fmt.Errorf("symbol context not found, symbol: %s", symbolLeft)
		}
		contextLeft.MarkPrice = priceRight * (1 - priceDiffDelta) * 0.9
	}
	return nil
}

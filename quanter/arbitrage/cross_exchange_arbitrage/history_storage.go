package cross_exchange_arbitrage

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/wizhodl/quanter/common/backscanner"
	"github.com/wizhodl/quanter/exchange"
)

func (this *CrossExchangeArbitrager) GetSnapshotDir() string {
	configPath := this.controller.GetConfigPath()
	configPath = filepath.Join(configPath, "snapshots/cross_exchange_arbitrage")
	// 创建文件夹
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.MkdirAll(configPath, 0755)
	}
	return configPath
}

func (this *CrossExchangeArbitrager) GetAccountSnapshotsPath() string {
	configPath := this.GetSnapshotDir()
	return filepath.Join(configPath, fmt.Sprintf("%s.account_snapshots", this.ID))
}

func (this *CrossExchangeArbitrager) GetPositionSnapshotsPath() string {
	configPath := this.GetSnapshotDir()
	return filepath.Join(configPath, fmt.Sprintf("%s.position_snapshots", this.ID))
}

var saveAccountSnapshotMutex = exchange.NewSyncMapOf[string, sync.Mutex]()

func (this *CrossExchangeArbitrager) SaveAccountSnapshot(snapshot *AccountSnapshot) error {
	path := this.GetAccountSnapshotsPath()
	mutex, _ := saveAccountSnapshotMutex.LoadOrStore(path, sync.Mutex{})
	mutex.Lock()
	defer mutex.Unlock()

	// append snapshot json to file
	json, err := json.Marshal(snapshot)
	if err != nil {
		return err
	}
	// create file if not exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		os.Create(path)
	}
	f, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = f.WriteString(fmt.Sprintf("%s\n", string(json)))
	if err != nil {
		return err
	}
	return nil
}

// 查询快照
// snapshotID: 快照ID，为空则查询所有快照
// snapshotTime: 快照时间，为空则查询所有快照
// limit: 查询数量，为0则查询最近的 100 个快照
// 用 snapshotID 查询时，snapshotTime 不生效 和 limit 不生效
// 用 snapshotTime 查询时，limit 不生效，仅查询 snapshotTime 之前 1 分钟和之后 1 分钟的快照
func (this *CrossExchangeArbitrager) LookupAccountSnapshots(snapshotID string, snapshotTime *time.Time, limit int) (snapshots []*AccountSnapshot) {
	path := this.GetAccountSnapshotsPath()
	count := 0
	if limit == 0 {
		limit = 100
	}
	backscanner.BackScan(path, func(line []byte) bool {
		snapshot := &AccountSnapshot{}
		err := json.Unmarshal(line, snapshot)
		if err == nil {
			if snapshotID == "" {
				// snapshot createTime within before or after 1 minutes of time
				if snapshotTime != nil {
					timeStart := snapshotTime.Add(-SnapshotHistoryTimeWindow)
					timeEnd := snapshotTime.Add(SnapshotHistoryTimeWindow)
					if snapshot.CreateTime.After(timeStart) && snapshot.CreateTime.Before(timeEnd) {
						snapshots = append(snapshots, snapshot)
					}
					if snapshot.CreateTime.Before(timeStart) {
						return false
					}
				} else {
					snapshots = append(snapshots, snapshot)
					count++
					if count >= limit {
						return false
					}
				}
			} else if strings.EqualFold(snapshot.ID, snapshotID) {
				snapshots = append(snapshots, snapshot)
				return false
			}
		}
		return true
	})
	return
}

var savePositionSnapshotMutex = exchange.NewSyncMapOf[string, sync.Mutex]()

func (this *CrossExchangeArbitrager) SavePositionSnapshot(snapshot *PositionSnapshot) error {
	path := this.GetPositionSnapshotsPath()
	mutex, _ := savePositionSnapshotMutex.LoadOrStore(path, sync.Mutex{})
	mutex.Lock()
	defer mutex.Unlock()

	json, err := json.Marshal(snapshot)
	if err != nil {
		return err
	}
	if _, err := os.Stat(path); os.IsNotExist(err) {
		os.Create(path)
	}
	f, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = f.WriteString(fmt.Sprintf("%s\n", string(json)))
	if err != nil {
		return err
	}
	return nil
}

func (this *CrossExchangeArbitrager) LookupPositionSnapshots(snapshotID string, snapshotTime *time.Time, limit int) (snapshots []*PositionSnapshot) {
	path := this.GetPositionSnapshotsPath()
	count := 0
	if limit == 0 {
		limit = 100
	}
	backscanner.BackScan(path, func(line []byte) bool {
		snapshot := &PositionSnapshot{}
		err := json.Unmarshal(line, snapshot)
		if err == nil {
			if snapshotID == "" {
				if snapshotTime != nil {
					timeStart := snapshotTime.Add(-SnapshotHistoryTimeWindow)
					timeEnd := snapshotTime.Add(SnapshotHistoryTimeWindow)
					if snapshot.CreateTime.After(timeStart) && snapshot.CreateTime.Before(timeEnd) {
						snapshots = append(snapshots, snapshot)
					}
				} else {
					snapshots = append(snapshots, snapshot)
					count++
					if count >= limit {
						return false
					}
				}
			} else if strings.EqualFold(snapshot.ID, snapshotID) {
				snapshots = append(snapshots, snapshot)
				return false
			}
		}
		return true
	})
	return
}

func (this *CrossExchangeArbitrager) TruncateSnapshots() error {
	this.Infof("truncating snapshots")
	startTime := time.Now()
	retentionPeriod := SnapshotRetentionPeriod
	path := this.GetAccountSnapshotsPath()
	lock, _ := saveAccountSnapshotMutex.LoadOrStore(path, sync.Mutex{})
	positionPath := this.GetPositionSnapshotsPath()
	positionLock, _ := savePositionSnapshotMutex.LoadOrStore(positionPath, sync.Mutex{})
	lock.Lock()
	positionLock.Lock()
	defer func() {
		lock.Unlock()
		positionLock.Unlock()
		this.Infof("truncating snapshots finished, cost: %s", time.Since(startTime))
	}()
	err := backscanner.TruncateJsonFileByCreateTime(path, retentionPeriod)
	if err != nil {
		return err
	}
	err = backscanner.TruncateJsonFileByCreateTime(positionPath, retentionPeriod)
	if err != nil {
		return err
	}
	return nil
}

package cross_exchange_arbitrage

import (
	"fmt"
	"math"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/jinzhu/copier"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/binance"
	"github.com/wizhodl/quanter/exchange/bybit"
	"github.com/wizhodl/quanter/exchange/hyperliquid"
	"github.com/wizhodl/quanter/utils"
	"golang.org/x/exp/rand"
)

// 创建跨交易所套利机
// implement base.Strategy
func NewCrossExchangeArbitrager(controller base.OrderControllable, depositCoin string, controllerIDLeft, depositAddressLeft, controllerIDRight, depositAddressRight string, optStr string) (*CrossExchangeArbitrager, error) {
	opts, err := NewCrossExchangeArbitragerOptions(optStr)
	if err != nil {
		return nil, err
	}

	arbitrager := &CrossExchangeArbitrager{}
	arbitrager.ID = exchange.NewRandomID()
	arbitrager.priceDiffAlertTime = exchange.NewSyncMapOf[string, time.Time]()
	arbitrager.DepositCoin = depositCoin
	arbitrager.ControllerIDLeft = controllerIDLeft
	arbitrager.DepositAddressLeft = depositAddressLeft
	arbitrager.ControllerIDRight = controllerIDRight
	arbitrager.DepositAddressRight = depositAddressRight
	arbitrager.Options = opts
	arbitrager.UpdateOptions(&opts.Options)
	arbitrager.AccountSnapshot = NewAccountSnapshot("", "") // exchange may not ready, so set empty string
	arbitrager.SymbolLocks = exchange.NewSyncMapOf[string, *SymbolLock]()
	arbitrager.SymbolParams = exchange.NewSyncMapOf[string, *option.Options]()
	arbitrager.MonitorRelaxAtTime = time.Now()
	arbitrager.controller = controller
	arbitrager.counters = NewCounters()

	arbitrager.SetupExchanges()

	err = arbitrager.ValidateOptions(&opts.Options)
	if err != nil {
		return nil, err
	}

	arbitrager.Status = StatusRunning
	arbitrager.StatusUpdateTime = time.Now()
	arbitrager.CreateTime = time.Now()

	return arbitrager, nil
}

func (this *CrossExchangeArbitrager) GetLastUseTime() time.Time {
	return this.LastUseTime
}

func (this *CrossExchangeArbitrager) GetStrategyType() base.StrategyType {
	return base.StrategyTypeCrossExchangeArbitrager
}

func (this *CrossExchangeArbitrager) SetAlias(alias string) {
	this.Alias = alias
}

func (this *CrossExchangeArbitrager) SetController(controller base.OrderControllable) {
	this.controller = controller
}

func (this *CrossExchangeArbitrager) CheckExchanges() error {
	if this.ExchangeLeft == nil || this.ExchangeRight == nil {
		return this.SetupExchanges()
	}
	return nil
}

func (this *CrossExchangeArbitrager) SetupExchanges() error {
	controllerLeft := this.controller.GetController(this.ControllerIDLeft)
	if controllerLeft != nil {
		this.ControllerLeft = controllerLeft
		this.ExchangeLeft = controllerLeft.GetExchange()
		if this.ExchangeLeft == nil {
			return fmt.Errorf("left exchange not ready")
		}
	} else {
		return fmt.Errorf("left controller not found")
	}
	controllerRight := this.controller.GetController(this.ControllerIDRight)
	if controllerRight != nil {
		this.ControllerRight = controllerRight
		this.ExchangeRight = controllerRight.GetExchange()
		if this.ExchangeRight == nil {
			return fmt.Errorf("right exchange not ready")
		}
	} else {
		return fmt.Errorf("right controller not found")
	}
	return nil
}

func (this *CrossExchangeArbitrager) Run() error {
	if this.counters == nil {
		this.counters = NewCounters()
	}
	go func() {
		// 交易所初始化好后，设置全仓模式、订单回调
		for {
			if this.ExchangeLeft == nil || this.ExchangeRight == nil {
				time.Sleep(2 * time.Second)
				continue
			}

			// 交易所保证金模式都要设为全仓
			// 有的交易所(Bybit)是按账号设置，有的(HL)是按交易对设置
			err := this.ExchangeLeft.SetAccountMarginMode(exchange.USDXMarginedFutures, exchange.AccountMarginModeCross)
			if err != nil && !strings.Contains(err.Error(), "not need to be adjusted") {
				this.ErrorMsgf("set left account margin mode failed: %s", err)
			}
			err = this.ExchangeRight.SetAccountMarginMode(exchange.USDXMarginedFutures, exchange.AccountMarginModeCross)
			if err != nil && !strings.Contains(err.Error(), "not need to be adjusted") {
				this.ErrorMsgf("set right account margin mode failed: %s", err)
			}

			this.ExchangeLeft.RegisterOrderUpdatedCallback(this.OnLeftExchangeOrderUpdated)
			this.checkRateLimit()
			this.handleProcessingWithdraw()

			break
		}
	}()

	ticker := time.NewTicker(LoopInterval)
	defer ticker.Stop()

	count := 0
	for range ticker.C {
		if this.DeleteTime != nil {
			this.Infof("cross exchange arbitrager is deleted, stop run loop")
			break
		}

		if err := this.CheckExchanges(); err != nil {
			continue
		}
		finishedMaints := this.Maintenances.AutoFinish()
		if len(finishedMaints) > 0 {
			this.SendMsgf("自动结束维护: \n```%s```", finishedMaints.ToTable())
		}
		if this.Status != StatusRunning {
			this.Infof("cross exchange arbitrager is not running, status: %s, skip loop", this.Status)
			if this.Status != StatusStopped && this.counters.pauseLimiter.Allow() {
				this.AlertMsgf("cross exchange arbitrager is not running, checkRisk and sync positions paused")
			}
			continue
		}
		this.Infof("cross exchange arbitrager is running, status: %s ...", this.Status)

		// 使用 defer 捕获 panic，防止影响其他逻辑
		func() {
			defer func() {
				if r := recover(); r != nil {
					this.AlertMsgf("recovered from panic in Run: %v", r)
					// Log stack trace for debugging
					buf := make([]byte, 4096)
					n := runtime.Stack(buf, false)
					this.Errorf("stack trace:\n%s", buf[:n])
				}
			}()

			// 清理 snapshot 数据，防止文件过大
			if this.counters.truncateSnapshotLimiter.Allow() {
				this.TruncateSnapshots()
			}
			this.ControlRisk()
			this.SyncPositions()
			go this.handleStoplossOrders()
			go this.checkMarginRatioOption(false)
			go this.checkRateLimit()
			this.AutoTakeProfit()
			this.controller.GetStorage().Save()
			count++
		}()
	}
	return nil
}

func (this *CrossExchangeArbitrager) ControlRisk() {
	if !this.mutexes.riskMutex.TryLock() {
		this.Infof("risk controlling is in progress, skip this time")
		return
	}
	defer func() {
		this.mutexes.riskMutex.Unlock()
		// 执行完风险控制后，重置模拟风险事件，并且重置 monitors 数据，防止再次触发
		if this.controller.IsDebug() && this.simulatingRisk != NoRiskCategory {
			this.simulatingRisk = NoRiskCategory
			this.SetMonitorRelaxAtTime(time.Now())
		}
	}()

	this.Infof("controlling risk...")

	err := this.UpdateAccountSnapshot()
	if err != nil {
		if strings.Contains(err.Error(), "ExchangeLeft or ExchangeRight not ready") {
			return
		}
		this.ErrorMsgf("update account snapshot failed, error: %s", err)
		return
	}
	if time.Since(this.AccountSnapshot.CreateTime) > 5*LoopInterval {
		this.AlertMsgf("账户快照时间大于 %s ，系统可能存在异常", 5*LoopInterval)
		return
	}

	// 如果当前不是运行状态，则跳过风险控制
	// 暂时仅跳过风险检查，一样会执行同步仓位，更新止盈止损订单等操作
	if this.Status != StatusRunning {
		this.Infof("arbitrager is not running, skip controlling risk")
		return
	}

	// 如果当前正在模拟风险事件，执行模拟篡改数据的操作
	if this.controller.IsDebug() && this.simulatingRisk != NoRiskCategory {
		err := this.SimulateRiskEvent(this.simulatingRisk)
		if err != nil {
			this.ErrorMsgf("simulate risk event failed: %s", err)
		}
	}

	// 如果某个交易所保证金几乎为 0，则平仓并且暂停
	// 因为可能是刚爆仓，需要重新启动并且开仓
	// 不自动 rebalance 的原因是，不要干扰还有保证金的账户；后续可以通过手工 resume 重新平衡资金
	if this.AccountSnapshot.MarginValueLeft <= MinMarginValue || this.AccountSnapshot.MarginValueRight <= MinMarginValue {
		risk := RiskCategoryMarginValueIsZero
		this.AddRiskEvent(risk, fmt.Sprintf("margin value left: %f, margin value right: %f", this.AccountSnapshot.MarginValueLeft, this.AccountSnapshot.MarginValueRight))
		this.Close(1)
		this.Pause()
		this.AlertMsgf("arbitrager paused, margin value is too low, risk: %s", risk)
		return
	}

	// 三种情况：
	// RiskCategoryMarginRatioFastChange
	// RiskCategoryTotalMarginRatioLow
	// RiskCategoryMarginRatioLow
	// Simulate 的情况下，需要有持仓才可以触发，否则无法模拟
	if dangerous, riskCategory := this.CheckAccountRisk(); dangerous {
		this.WarnMsgf("margin ratio is dangerous, reducing positions, risk: %s", riskCategory)
		// 减到 TargetMarginRatio
		// reduceRatio := this.calculateReduceRatio(this.Options.TargetMarginRatio)
		// 固定减仓 2%，calculateReduceRatio 目前的算法看起来并没有问题，但是没有实测算的对不对，暂时用固定减仓比例
		// TODO: 后面看是否用回 calculateReduceRatio 来计算
		reduceRatio := 0.02
		err := this.ReducePositions(reduceRatio)
		if err != nil {
			this.ErrorMsgf("account at risk: %s, reduce positions failed: %s", riskCategory, err)
		}
		this.SetMonitorRelaxAtTime(time.Now()) // 因为之前的数据还在 monitors 中，所以需要重置 relax time，防止重复触发
		return
	}

	// 检查单个合约的风险水平，如果有危险，则关闭仓位
	// 主要是检查价格波动风险，通过这个方式可以比检查找好的风险水平更快发现异常
	dangerousSymbols, risks := this.CheckSymbolRisk()
	if len(dangerousSymbols) > 0 {
		this.WarnMsgf("some symbols are dangerous, closing positions: %v", dangerousSymbols)
		for _, risk := range risks {
			this.WarnMsgf("risk: %s", risk)
		}
		this.ClosePositions(dangerousSymbols, 1)
		this.SetMonitorRelaxAtTime(time.Now()) // 因为之前的价格数据还在 monitors 中，所以需要重置 relax time，防止重复触发
		return
	}

	// 检查非 U 本位的合约，需要报警
	hasAlien, err := this.HasAlienPositions()
	if err != nil {
		this.ErrorMsgf("check alien positions failed, error: %s", err)
	}
	if hasAlien {
		this.AlertMsgf("发现非 U 本位合约，请检查")
	}

	// 如果仓位不为空，则检查保证金率是否足够，是否需要再平衡
	if !this.AccountSnapshot.IsPositionEmpty() {
		needRebalance := false
		// TODO: 用 monitors 检查最新的记录，不要只检查 accountSnapshot，防止多次检查触发
		if this.AccountSnapshot.GetDiscountedMarginRatioLeft() < this.Options.RebalanceMarginRatio || this.AccountSnapshot.GetDiscountedMarginRatioRight() < this.Options.RebalanceMarginRatio {
			// 如果正在提币中，不要再次触发 RebalanceMarginRatioReached
			// 已经在 rebalance 延迟解锁了，不需要检查 MonitorRelaxAtTime
			if !this.IsRebalancing() {
				needRebalance = true
				comment := fmt.Sprintf("margin ratio left: %.2f%%, margin ratio right: %.2f%%", this.AccountSnapshot.GetDiscountedMarginRatioLeft()*100, this.AccountSnapshot.GetDiscountedMarginRatioRight()*100)
				this.AddRiskEvent(RiskCateogryRebalanceMarginRatioReached, comment)
			} else {
				this.Infof("last withdraw is processing, ignore risk: %s", RiskCateogryRebalanceMarginRatioReached)
			}
		}

		if needRebalance {
			this.SendMsgf("margin ratio is too low, rebalancing...")
			go this.Rebalance(func(w *Withdraw, err error) {
				if err != nil {
					this.ErrorMsgf("Rebalance failed: %s, reducing positions", err)
					// 再平衡失败减仓
					// toMarginRatio := (this.Options.TargetMarginRatio + this.Options.RebalanceMarginRatio) / 2 // 取中间值
					// reduceRatio := this.calculateReduceRatio(toMarginRatio)
					// 固定减仓 2%，calculateReduceRatio 目前的算法看起来并没有问题，但是没有实测算的对不对，暂时用固定减仓比例
					// 并且之前观察发现用中间值，调整幅度可能过大了
					// TODO: 后面看是否用回 calculateReduceRatio 来计算
					reduceRatio := 0.02
					err = this.ReducePositions(reduceRatio)
					if err != nil {
						this.ErrorMsgf("rebalance failed, reduce positions failed: %s", err)
					}
					this.SetMonitorRelaxAtTime(time.Now().Add(5 * time.Second))
				} else {
					this.SetMonitorRelaxAtTime(time.Now().Add(5 * time.Second))
					this.SendMsgf("rebalance success: %s => %s, amount: %.2f", w.FromExchange, w.ToExchange, w.Amount)
				}
			})
			// 不设置过长的 relaxTime，在 CheckAccountRisk 中通过检查 IsRebalancing() 来防止误触发
			this.SetMonitorRelaxAtTime(time.Now().Add(5 * time.Second))
			return
		}
	} else {
		// 两边空仓的情况下，允许 rebalance 自动转账平衡资金
		delta := math.Abs(this.AccountSnapshot.GetRebalanceAmount())
		threshHold := EmptyPositionAutoRebalanceThreshold
		if (delta / this.AccountSnapshot.GetDiscountedMarginValueTotal()) > threshHold {
			if this.lastAutoRebalanceTime != nil && time.Since(*this.lastAutoRebalanceTime) < RebalanceTimeout {
				return
			}
			now := time.Now()
			this.lastAutoRebalanceTime = &now
			this.SendMsgf("空仓情况下，资金差异超过总资金的 %v%%，开始 rebalance", threshHold*100)
			// 资金差异超过总资金的一定比例，才 rebalance
			go this.Rebalance(func(w *Withdraw, err error) {
				if err != nil {
					this.ErrorMsgf("Rebalance failed: %s, reducing positions", err)
				} else {
					this.SendMsgf("rebalance success: %s => %s, amount: %.2f", w.FromExchange, w.ToExchange, w.Amount)
				}
			})
			return
		}
	}
}

func (this *CrossExchangeArbitrager) IsRebalancing() bool {
	locked := this.mutexes.rebalanceMutex.TryLock()
	if locked {
		this.mutexes.rebalanceMutex.Unlock()
	}
	return !locked
}

func (this *CrossExchangeArbitrager) SetMonitorRelaxAtTime(t time.Time) {
	this.Infof("set monitor relax at time: %s", t)
	if t.Before(this.MonitorRelaxAtTime) {
		this.Errorf("monitor relax at time is already after new time, skip")
		return
	}
	this.MonitorRelaxAtTime = t
}

// 计算减仓比例，总是从保证金率较低的交易所计算减仓比例；这也是为什么可以一直使用左侧减仓的原因
// 1. 首先减仓始终会同步到另外一侧，因此不论哪一侧减仓效果是一样的
// 2. 并不会因为某一侧保证金率充足，就不减仓或者少减仓，因此最终达到的效果是保证了保证金率较少的那一侧达到目标仓位
func (this *CrossExchangeArbitrager) calculateReduceRatio(targetMarginRatio float64) float64 {
	var positionValue, marginValue float64
	if this.AccountSnapshot.GetDiscountedMarginRatioLeft() < this.AccountSnapshot.GetDiscountedMarginRatioRight() {
		positionValue = this.AccountSnapshot.PositionValueLeft
		marginValue = this.AccountSnapshot.GetDiscountedMarginValueLeft()
	} else {
		positionValue = this.AccountSnapshot.PositionValueRight
		marginValue = this.AccountSnapshot.GetDiscountedMarginValueRight()
	}
	toPositionValue := marginValue / targetMarginRatio
	return (positionValue - toPositionValue) / positionValue
}

func (this *CrossExchangeArbitrager) AddRiskEvent(category RiskCategory, comment string) (event *RiskEvent) {
	event = &RiskEvent{
		ID:         exchange.NewRandomID(),
		Category:   category,
		Comment:    comment,
		CreateTime: time.Now(),
	}
	this.RiskEvents = append(this.RiskEvents, event)
	this.SendMsgf("risk event: %s, %s", category, comment)
	return
}

func (this *CrossExchangeArbitrager) OnLeftExchangeOrderUpdated(order *exchange.Order) {
	this.Debugf("left exchange order updated, symbol: %s, status: %s, execQty: %v", order.Symbol, order.Status, order.ExecQty)
	if order.ExecQty == 0 {
		// 仅处理有成交的订单
		return
	}

	time.Sleep(1 * time.Second)
	this.Infof("left exchange order traded, symbol: %s, status: %s, execQty: %v", order.Symbol, order.Status, order.ExecQty)
	this.syncPosition(order.InstrumentType, order.Symbol)
}

func (this *CrossExchangeArbitrager) getRebalanceChain() string {
	return exchange.WithdrawChainARBI
}

func getRebalanceTimeout(toExchange string, chain exchange.WithdrawChain) time.Duration {
	if toExchange == exchange.Binance && chain == exchange.WithdrawChainARBI {
		// binance arbi 充值到账后需等 15 分钟才能划转使用
		return 25*time.Minute + RebalanceTimeout
	}
	return RebalanceTimeout
}

func (this *CrossExchangeArbitrager) getTotalMarginRatioLowTimeWindow() time.Duration {
	timeWindow := ParamTotalMarginRatioLowTimeWindow
	if this.ExchangeLeft.GetName() == exchange.Binance || this.ExchangeRight.GetName() == exchange.Binance {
		timeWindow = getRebalanceTimeout(exchange.Binance, exchange.WithdrawChainARBI)
	}
	return timeWindow
}

/*
资金走向流程：
1. 交易账号 -> 充提账号（可选）
2. 子账号 -> 主账号（可选）
3. 链上提币 -> 目标交易所充提账号/钱包
4. 钱包 -> 充提账号（可选）
5. 主账号 -> 子账号（可选）
6. 充提账号 -> 交易账号（可选）

异常情况，都需要转回子账号的交易账号
*/
func (this *CrossExchangeArbitrager) Rebalance(callback func(*Withdraw, error)) {
	if !this.mutexes.rebalanceMutex.TryLock() {
		this.Warnf("rebalace in progress, wait for next time")
		return
	}
	defer func() {
		// 极限情况可能在同一个 loop 中 rebalance success，和触发 RiskEventCategoryRebalanceMarginRatioReached，
		// 提现成功的余额变动并没有反映在当前的 AccountSnapshot，需要在解锁前设置 relaxTime 等待下次更新余额并重新计算 RiskEvent
		this.SetMonitorRelaxAtTime(time.Now().Add(LoopInterval))
		time.Sleep(LoopInterval) // 等待下次更新余额，暂时不解锁
		this.mutexes.rebalanceMutex.Unlock()
	}()

	this.Infof("rebalancing...")

	// 计算需要划转的资金，其中考虑了不同平台保证金使用率的差异
	// amount > 0 表示从 left 划转到 right
	// amount < 0 表示从 right 划转到 left
	amount := this.AccountSnapshot.GetRebalanceAmount()
	var fromExchange, toExchange exchange.Exchange
	var fromControllerID, toControllerID string
	toAddress := ""
	if amount > 0 {
		fromExchange = this.ExchangeLeft
		toExchange = this.ExchangeRight
		fromControllerID = this.ControllerIDLeft
		toControllerID = this.ControllerIDRight
		toAddress = this.DepositAddressRight
	} else {
		fromExchange = this.ExchangeRight
		toExchange = this.ExchangeLeft
		fromControllerID = this.ControllerIDRight
		toControllerID = this.ControllerIDLeft
		toAddress = this.DepositAddressLeft
	}

	amount, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", math.Abs(amount)), 64)
	this.SendMsgf("rebalance start: %s => %s, amount: %.2f", fromExchange.GetName(), toExchange.GetName(), amount)

	// fromExchange 的 MarginRatio 如果低于 TargetMarginRatio 则不划转
	if fromControllerID == this.ControllerIDLeft && this.AccountSnapshot.MarginRatioLeft < this.Options.TargetMarginRatio {
		callback(nil, fmt.Errorf("left exchange margin ratio is lower than target margin ratio"))
		return
	} else if fromControllerID == this.ControllerIDRight && this.AccountSnapshot.MarginRatioRight < this.Options.TargetMarginRatio {
		callback(nil, fmt.Errorf("right exchange margin ratio is lower than target margin ratio"))
		return
	}

	now := time.Now()
	withdraw := &Withdraw{
		ID:               exchange.NewRandomID(),
		FromControllerID: fromControllerID,
		ToControllerID:   toControllerID,
		FromExchange:     fromExchange.GetName(),
		ToExchange:       toExchange.GetName(),
		Coin:             this.DepositCoin,
		Address:          toAddress,
		Chain:            this.getRebalanceChain(),
		Status:           WithdrawStatusNew,
		Amount:           amount,
		CreateTime:       now,
		UpdateTime:       now,
	}
	this.Withdraws = append(this.Withdraws, withdraw)

	defer func() {
		if withdraw.Status != WithdrawStatusSuccess {
			withdraw.Status = WithdrawStatusFailed
		}
		withdraw.UpdateTime = time.Now()
	}()

	if this.controller.IsDebug() && strings.HasSuffix(fromControllerID, "test") {
		// FOR TEST
		if withdraw.FromExchange == exchange.Bybit {
			withdraw.Amount = 20
			// withdraw.Coin = "USDT"
			withdraw.Chain = exchange.WithdrawChainETH
		} else if withdraw.FromExchange == exchange.Binance {
			withdraw.Amount = 10
		} else {
			withdraw.Amount = 10
		}
		this.Debugf("test update amount to %.0f", withdraw.Amount)
	}

	// 可提币资金不足时，使用 rotate 释放盈利资金
	_, available, err := fromExchange.GetBalance(exchange.USDXMarginedFutures, withdraw.Coin)
	if err != nil {
		callback(nil, fmt.Errorf("get balance failed: %s", err))
		withdraw.FailReason = WithdrawFailReasonAPIError
		return
	}
	if available < withdraw.Amount {
		this.WarnMsgf("再平衡可用资金不足，可用: %.2f, 需要: %.2f, 将尝试 rotate 释放盈利资金", available, withdraw.Amount)

		// 按持仓盈利从大到小依次释放，直到可用资金满足再平衡要求
		posLeft, posRight, err := this.GetPositions()
		if err != nil {
			callback(nil, fmt.Errorf("get positions failed: %s", err))
			withdraw.FailReason = WithdrawFailReasonAPIError
			return
		}

		var positions []*exchange.Position
		if fromControllerID == this.ControllerIDLeft {
			positions = posLeft
		} else {
			positions = posRight
		}

		rotatePositions := make([]exchange.Position, 0)
		ExtKeyPNL := "pnl"
		for _, position := range positions {
			if position.Qty == 0 {
				continue
			}

			lastPrice := position.GetLastPrice()
			entryValue, _ := fromExchange.Qty2Size(position.InstrumentType, position.Symbol, position.EntryPrice, math.Abs(position.Qty))
			lastValue, _ := fromExchange.Qty2Size(position.InstrumentType, position.Symbol, lastPrice, math.Abs(position.Qty))
			pnl := 0.0
			if position.Qty > 0 {
				pnl = lastValue - entryValue
			} else {
				pnl = entryValue - lastValue
			}
			if pnl > 0 {
				position.SetFloat64(ExtKeyPNL, pnl)
				rotatePositions = append(rotatePositions, *position)
			}
		}
		sort.Slice(rotatePositions, func(i, j int) bool {
			return rotatePositions[i].GetFloat64(ExtKeyPNL) > rotatePositions[j].GetFloat64(ExtKeyPNL)
		})

		needAmount := (withdraw.Amount - available) * 1.02 // 2% 的额外资金
		rotatedPNL := 0.0
		for _, position := range rotatePositions {
			if rotatedPNL >= needAmount {
				break
			}

			left := needAmount - rotatedPNL
			pnl := position.GetFloat64(ExtKeyPNL)
			ratio := 1.0
			if pnl > left {
				ratio = left / pnl
			}

			err := this.Rotate(position.Symbol, ratio, -1, fromControllerID == this.ControllerIDRight)
			if err != nil {
				this.ErrorMsgf("rebalance rotate error, symbol: %s, ratio: %.2f, error: %s", position.Symbol, ratio, err)
				continue
			}

			rotatedPNL += pnl * ratio
		}

		if rotatedPNL < needAmount {
			withdraw.Amount = (available + rotatedPNL) * 0.99
			withdraw.Amount, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", withdraw.Amount), 64)
			this.WarnMsgf("rotate 释放盈利资金不足，释放: %.2f, 需要: %.2f, 将减少再平衡资金到 %.2f", rotatedPNL, needAmount, withdraw.Amount)
		}
	}

	withdrawFee, err := fromExchange.GetWithdrawFee(withdraw.Coin, withdraw.Chain)
	if err != nil {
		callback(nil, fmt.Errorf("get withdraw fee failed: %s", err))
		withdraw.FailReason = WithdrawFailReasonAPIError
		return
	}
	withdraw.Fee = withdrawFee

	amountWithFee, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", withdraw.Amount+withdrawFee), 64)

	// 1. 交易账号 -> 充提账号
	if fromExchange.GetName() == exchange.Bybit {
		// 需要先转到资金账号
		err = fromExchange.(*bybit.Bybit).InterTransfer(withdraw.Coin, amountWithFee, bybit.AccountTypeUnified, bybit.AccountTypeFund)
		if err != nil {
			this.ErrorMsgf("从 %s 交易账号转出到资金账号失败，error: %s", fromExchange.GetName(), err)
			callback(nil, fmt.Errorf("inter transfer failed: %s", err))
			withdraw.FailReason = WithdrawFailReasonTransferError
			return
		}
		this.SendMsgf("已将 %.2f %s 从 %s 交易账号转到资金账号", amountWithFee, withdraw.Coin, fromExchange.GetName())
	} else if fromExchange.GetName() == exchange.Binance {
		// 需要先转到现货账号
		err = fromExchange.TransferAsset(exchange.USDXMarginedFutures, exchange.Spot, withdraw.Coin, amountWithFee)
		if err != nil {
			callback(nil, fmt.Errorf("transfer asset failed: %s", err))
			withdraw.FailReason = WithdrawFailReasonTransferError
			return
		}
		this.SendMsgf("已将 %.2f %s 从 %s U本位合约账号转到现货账号", amountWithFee, withdraw.Coin, fromExchange.GetName())
	}

	time.Sleep(InterTransferWait)

	// 2. 子账号 -> 主账号
	err = fromExchange.SubaccountTransfer(withdraw.Coin, amountWithFee, false)
	if err != nil {
		this.ErrorMsgf("从 %s 子账号转到主账号出现错误，error: %s", fromExchange.GetName(), err)
		callback(nil, fmt.Errorf("sub account transfer failed: %s", err))
		withdraw.FailReason = WithdrawFailReasonSubaccountTransferError
		return
	} else {
		this.SendMsgf("已将 %.2f %s 从 %s 子账号转到主账号", amountWithFee, withdraw.Coin, fromExchange.GetName())
	}

	time.Sleep(InterTransferWait)

	// 3. 链上提币 -> 目标交易所充提账号/钱包
	withdrawID, err := fromExchange.Withdraw(withdraw.Coin, toAddress, amountWithFee, withdraw.Chain)
	if err != nil {
		// 提现失败，退回 -> 子账号 -> 交易账号
		if fromExchange.IsSubaccount() {
			if err := fromExchange.SubaccountTransfer(withdraw.Coin, amountWithFee, true); err != nil {
				this.SendMsgf("链上提币操作失败，%s 资金从 %s 主账号退回子账号失败: %s", this.GetAliasOrID(), fromExchange.GetName(), err)
			} else {
				this.SendMsgf("链上提币操作失败，%s 已将资金从 %s 主账号退回子账号", this.GetAliasOrID(), fromExchange.GetName())
			}
			time.Sleep(InterTransferWait)
		}

		if fromExchange.GetName() == exchange.Bybit {
			if err := fromExchange.(*bybit.Bybit).InterTransfer(withdraw.Coin, amountWithFee, bybit.AccountTypeFund, bybit.AccountTypeUnified); err != nil {
				this.SendMsgf("链上提币操作失败，%s 资金从 %s 资金账号退回交易账号，退回失败: %s", this.GetAliasOrID(), fromExchange.GetName(), err)
			} else {
				this.SendMsgf("链上提币操作失败，%s 已将资金从 %s 资金账号退回交易账号", this.GetAliasOrID(), fromExchange.GetName())
			}
		} else if fromExchange.GetName() == exchange.Binance {

			if err := fromExchange.TransferAsset(exchange.Spot, exchange.USDXMarginedFutures, withdraw.Coin, amountWithFee); err != nil {
				this.SendMsgf("链上提币操作失败，%s 资金从 %s 现货账号退回 U 本位合约账号，退回失败: %s", this.GetAliasOrID(), fromExchange.GetName(), err)
			} else {
				this.SendMsgf("链上提币操作失败，%s 已将资金从 %s 现货账号退回 U 本位合约账号", this.GetAliasOrID(), fromExchange.GetName())
			}
		}

		callback(nil, fmt.Errorf("withdraw failed: %s", err))
		withdraw.FailReason = WithdrawFailReasonAPIError
		return
	}
	withdraw.Status = WithdrawStatusPending
	withdraw.TxID = withdrawID

	withdrawStartTime := time.Now()

	// 检测充提账号/钱包可用余额大于等于提币金额，即默认到账
	isFundsReceived := false
	getBalanceErrorCount := 0
	for {
		time.Sleep(5 * time.Second)

		if toExchange.GetName() == exchange.Bybit || toExchange.GetName() == exchange.Binance {
			_, available, err := toExchange.GetWithdrawAccountBalance(withdraw.Coin)
			if err != nil {
				getBalanceErrorCount++
				this.ErrorMsgf("%s get fund account balances failed: %s", toExchange.GetName(), err)
			} else if available >= withdraw.Amount {
				isFundsReceived = true
			}
		} else if toExchange.GetName() == exchange.Hyperliquid {
			ex := toExchange.(*hyperliquid.Hyperliquid)
			balance, err := ex.GetWalletBalance(withdraw.Coin)
			if err != nil {
				getBalanceErrorCount++
				this.ErrorMsgf("hyperliquid get wallet balance failed: %s", err)
			} else if balance >= withdraw.Amount {
				isFundsReceived = true
			}
		}

		if isFundsReceived {
			break
		}

		if getBalanceErrorCount > 5 {
			// 失败超过 5 次，直接失败
			callback(withdraw, fmt.Errorf("get balance failed too many times"))
			withdraw.FailReason = WithdrawFailReasonTransferError
			return
		}

		if time.Since(withdrawStartTime) > getRebalanceTimeout(withdraw.ToExchange, withdraw.Chain) {
			callback(withdraw, fmt.Errorf("rebalance timeout while waiting for deposit"))
			withdraw.FailReason = WithdrawFailReasonTimeout
			return
		}
	}

	// 4. 钱包 -> 充提账号
	if toExchange.GetName() == exchange.Hyperliquid {
		withdraw.Status = WithdrawStatusBridging

		ex := toExchange.(*hyperliquid.Hyperliquid)
		tx, err := ex.Deposit(withdraw.Amount)
		if err != nil {
			callback(withdraw, fmt.Errorf("hyperliquid deposit failed: %s", err))
			withdraw.FailReason = WithdrawFailReasonTransferError
			return
		}

		withdraw.BridgeTxID = tx

		for {
			time.Sleep(10 * time.Second)

			success, pending, err := ex.IsTransactionSuccess(tx)
			if err != nil {
				callback(withdraw, fmt.Errorf("hyperliquid query deposit transaction failed: %s", err))
				withdraw.FailReason = WithdrawFailReasonTransferError
				return
			}

			if time.Since(withdrawStartTime) > getRebalanceTimeout(withdraw.ToExchange, withdraw.Chain) {
				callback(withdraw, fmt.Errorf("rebalance timeout while waiting for bridge tx"))
				withdraw.FailReason = WithdrawFailReasonTimeout
				return
			}

			if pending {
				continue
			}

			if !success {
				callback(withdraw, fmt.Errorf("hyperliquid deposit transaction failed"))
				withdraw.FailReason = WithdrawFailReasonTransferError
				return
			}

			break
		}

		// Bridge 之后，需要确认交易所账号到账
		for {
			time.Sleep(10 * time.Second)
			_, available, err := ex.GetWithdrawAccountBalance(withdraw.Coin)
			if err != nil {
				callback(withdraw, fmt.Errorf("hyperliquid get withdraw account balances failed: %s", err))
				withdraw.FailReason = WithdrawFailReasonTransferError
				return
			} else if available >= withdraw.Amount {
				break
			}

			if time.Since(withdrawStartTime) > getRebalanceTimeout(withdraw.ToExchange, withdraw.Chain) {
				callback(withdraw, fmt.Errorf("rebalance timeout while waiting for bridge deposit"))
				withdraw.FailReason = WithdrawFailReasonTimeout
				return
			}
		}
	}

	// 5. 主账号 -> 子账号
	err = toExchange.SubaccountTransfer(withdraw.Coin, withdraw.Amount, true)
	if err != nil {
		callback(withdraw, fmt.Errorf("%s transfer to sub account failed: %s", toExchange.GetName(), err))
		withdraw.FailReason = WithdrawFailReasonSubaccountTransferError
		return
	}

	time.Sleep(InterTransferWait)

	// 6. 充提账号 -> 交易账号
	if toExchange.GetName() == exchange.Bybit {
		withdraw.Status = WithdrawStatusInner

		ex := toExchange.(*bybit.Bybit)
		err = ex.InterTransfer(withdraw.Coin, withdraw.Amount, bybit.AccountTypeFund, bybit.AccountTypeUnified)
		if err != nil {
			callback(withdraw, fmt.Errorf("bybit inter transfer failed: %s", err))
			withdraw.FailReason = WithdrawFailReasonTransferError
			return
		}
	} else if toExchange.GetName() == exchange.Binance {
		withdraw.Status = WithdrawStatusInner

		err = toExchange.TransferAsset(exchange.Spot, exchange.USDXMarginedFutures, withdraw.Coin, withdraw.Amount)
		if err != nil {
			callback(withdraw, fmt.Errorf("binance transfer asset failed: %s", err))
			withdraw.FailReason = WithdrawFailReasonTransferError
			return
		}
	}

	withdraw.Status = WithdrawStatusSuccess
	callback(withdraw, nil)
}

func (this *AccountSnapshot) IsPositionEmpty() bool {
	return this.PositionValueLeft == 0 && this.PositionValueRight == 0
}

/*
Monitors 中保存了约 5 分钟内的的 AccountBalance 数据
1. 如果 marginRatio 在短时间内快速变化，可能会触发直接减仓，而不是再平衡资金
2. 如果 marginRatio 在最近一段时间内，低于 rebalanceMarginRatio 的次数超过 80%，则认为是有危险的
3. 如果总保证金率低于 targetMarginRatio 的次数超过 80%，则认为是有危险的
*/
func (this *CrossExchangeArbitrager) CheckAccountRisk() (dangerous bool, riskCategory RiskCategory) {
	comment := ""
	defer func() {
		if dangerous {
			this.AddRiskEvent(riskCategory, comment)
		}
	}()
	this.Infof("checking account risk...")
	this.Infof("MonitorRelaxAtTime: %s", this.MonitorRelaxAtTime)

	// 如果仓位为空，则认为没有危险
	if this.AccountSnapshot.IsPositionEmpty() {
		dangerous = false
		riskCategory = NoRiskCategory
		return
	}
	// 如果 marginRatio 在短时间内快速变小，可能会触发直接减仓，而不是再平衡资金
	// 具体的，如果在 5 分钟内，marginRatio 变化超过 30%，则认为是有危险的
	// 可能是整体市场行情变化，也可能是交易所账户风险，比如人工转走了资金
	lastSnapshot := this.Monitors[len(this.Monitors)-1]
	lastSnapshotTime := lastSnapshot.CreateTime
	if lastSnapshot.GetDiscountedMarginRatioLeft() < this.Options.TargetMarginRatio || lastSnapshot.GetDiscountedMarginRatioRight() < this.Options.TargetMarginRatio {
		for _, snapshot := range this.Monitors {
			// 早于 5 分钟的数据不检查
			if snapshot.CreateTime.Add(ParamMarginRatioFastChangeTimeWindow).Before(lastSnapshotTime) {
				continue
			}
			// 早于 MonitorRelaxAtTime 的数据不检查
			if snapshot.CreateTime.Before(this.MonitorRelaxAtTime) {
				continue
			}
			if lastSnapshot.GetDiscountedMarginRatioLeft()/snapshot.GetDiscountedMarginRatioLeft() < (1-ParamMarginRatioFastChangeDangerDelta) || lastSnapshot.GetDiscountedMarginRatioRight()/snapshot.GetDiscountedMarginRatioRight() < (1-ParamMarginRatioFastChangeDangerDelta) {
				if EnableMarginRatioFastChange {
					dangerous = true
					riskCategory = RiskCategoryMarginRatioFastChange
					comment = fmt.Sprintf("margin ratio fast change, left: %.2f%% -> %.2f%%; right: %.2f%% -> %.2f%%", lastSnapshot.MarginRatioLeft*100, snapshot.MarginRatioLeft*100, lastSnapshot.MarginRatioRight*100, snapshot.MarginRatioRight*100)
					return
				} else {
					this.Warnf("margin ratio fast change not enabled, skip risk: %s", RiskCategoryMarginRatioFastChange)
				}
			}
		}
	}

	// 如果 marginRatio 在最近一段时间内(10 分钟），低于 rebalanceMarginRatio 的次数超过 80%，则认为是有危险的
	// 这种情况可能出现在仓位开始出现不平衡，已经提币，但是因为某种原因，没有及时平衡资金的情况
	// 提币的正常处理时间约为 5 分钟，为了防止误报，检查时间放宽到 10 分钟
	// 如果浮盈部分的资金无法划转出来用于再平衡资金，也可能导致 marginRatio 低于 rebalanceMarginRatio
	dangerCount := 0

	totalCount := 0
	for _, snapshot := range this.Monitors {
		if snapshot.CreateTime.Add(ParamMarginRatioLowTimeWindow).Before(lastSnapshotTime) {
			continue
		}
		// 早于 MonitorRelaxAtTime 的数据不检查
		if snapshot.CreateTime.Before(this.MonitorRelaxAtTime) {
			continue
		}
		totalCount++
		if snapshot.GetDiscountedMarginRatioLeft() < this.Options.RebalanceMarginRatio || snapshot.GetDiscountedMarginRatioRight() < this.Options.RebalanceMarginRatio {
			dangerCount++
		}
	}
	this.Infof("check risk: %s; margin ratio low count: %d, total count: %d", RiskCategoryMarginRatioLow, dangerCount, totalCount)
	// 防止误报，至少需要 10 个数据
	// 排除掉 rebalancing 时导致的情况，属于误报
	if totalCount >= ParamTotalMarginRatioLowCountLimit {
		if dangerCount > int(float64(totalCount)*0.8) {
			if this.IsRebalancing() {
				this.Warnf("rebalancing, skip risk: %s", RiskCategoryMarginRatioLow)
			} else {
				dangerous = true
				riskCategory = RiskCategoryMarginRatioLow
				comment = fmt.Sprintf("margin ratio low count: %d, total count: %d", dangerCount, totalCount)
				return
			}
		}
	}

	// 正常情况下，总保证金率应该接近或高于 targetMarginRatio；
	// 如果开仓头寸过大时（可能是手工开仓过大，也可能是因为币价上涨），会出现总保证金率低于 targetMarginRatio 的情况
	// 如果最近的一段时间内（5 分钟），总保证金率低于 targetMarginRatio 的次数超过 80%，则认为是有危险的
	dangerCount = 0
	totalCount = 0
	timeWindow := this.getTotalMarginRatioLowTimeWindow()
	for _, snapshot := range this.Monitors {
		if snapshot.CreateTime.Add(timeWindow).Before(lastSnapshotTime) {
			continue
		}
		// 早于 MonitorRelaxAtTime 的数据不检查
		if snapshot.CreateTime.Before(this.MonitorRelaxAtTime) {
			continue
		}
		totalCount++
		// 如果保证金率低于 targetMarginRatio 的 90%，则认为持仓过大
		// 主要为了应对币价上涨导致的头寸增长，90% 对应的就是币价整体上涨 10%
		if snapshot.GetDiscountedMarginRatioTotal() < this.Options.TargetMarginRatio*0.90 {
			dangerCount++
		}
	}
	this.Infof("check risk: %s; total margin ratio low count: %d, total count: %d", RiskCategoryTotalMarginRatioLow, dangerCount, totalCount)
	// 防止误报，至少需要 10 个数据
	if totalCount >= ParamMarginRatioLowCountLimit {
		if dangerCount > int(float64(totalCount)*0.8) {
			if this.IsRebalancing() {
				this.Warnf("rebalancing, skip risk: %s", RiskCategoryTotalMarginRatioLow)
			} else {
				dangerous = true
				riskCategory = RiskCategoryTotalMarginRatioLow
				comment = fmt.Sprintf("total margin ratio low count: %d, total count: %d", dangerCount, totalCount)
				return
			}
		}
	}
	dangerous = false
	riskCategory = NoRiskCategory
	return
}

// 检查价格波动风险
// 如果某个合约的价格在 5 分钟内波动超过 20%，则认为是有风险的
// 如果 ExchangeRight 上的某个合约价格波动超过 20%，同样在 ExchangeLeft 上平仓
// 所以，结果中包含的 symbol 可能同时存在于 ExchangeLeft 和 ExchangeRight
// 这个函数内都是独立的品种风险，不受 MonitorRelaxAtTime 的影响
func (this *CrossExchangeArbitrager) CheckSymbolRisk() (dangerousSymbols []string, risks []RiskCategory) {
	this.Infof("checking symbol risk...")

	dangerousSymbols = []string{}
	diffRiskSymbols := []string{}        // 价差风险的品种
	markRiskSymbols := []string{}        // 标记价格和指数价格价差风险的品种
	stoplossExpiredSymbols := []string{} // 止损止盈订单已经失效的品种，不返回到结果中，不要求平仓

	defer func() {
		dangerousSymbols = utils.Unique(dangerousSymbols)
		diffRiskSymbols = utils.Unique(diffRiskSymbols)
		markRiskSymbols = utils.Unique(markRiskSymbols)
		stoplossExpiredSymbols = utils.Unique(stoplossExpiredSymbols)
		if len(diffRiskSymbols) > 0 {
			this.AddRiskEvent(RiskCategoryPriceDiff, fmt.Sprintf("symbols: %v", diffRiskSymbols))
		}
		if len(markRiskSymbols) > 0 {
			this.AddRiskEvent(RiskCategoryMarkPriceDiff, fmt.Sprintf("symbols: %v", markRiskSymbols))
		}
		// 不将 stoplossExpiredSymbols 作为风险事件，因为万一发生该事件，可能是因为 API 频率限制引起，无法处理；强行平仓会导致损失很大
		// 仅仅是提醒用户，止损止盈订单可能失效
		if len(stoplossExpiredSymbols) > 0 {
			this.ErrorMsgf("stoploss expired symbols: %v", stoplossExpiredSymbols)
		}
	}()

	allSymbolCodes, allSymbols, _, _, _, _, err := this.GetSymbolCodesForAccountSnapshot(this.AccountSnapshot)
	if err != nil {
		this.Errorf("get symbol codes for account snapshot failed, error: %s", err)
		return
	}

	timeWindow := ParamPriceVolatilityTimeWindow
	priceDiffCountLimit := ParamPriceDiffCountLimit
	symbolLastPricesLeft := map[string][]float64{}
	symbolLastPricesRight := map[string][]float64{}
	for _, symbol := range allSymbols {
		pricesLeft := []float64{}
		pricesRight := []float64{}
		for _, snapshot := range this.Monitors {
			// 早于 5 分钟的数据不检查
			if snapshot.CreateTime.Add(timeWindow).Before(time.Now()) {
				continue
			}
			if symbolCtx, ok := snapshot.SymbolContextLeft.Load(symbol); ok && symbolCtx.LastPrice > 0 {
				pricesLeft = append(pricesLeft, symbolCtx.LastPrice)
			}
			if symbolCtx, ok := snapshot.SymbolContextRight.Load(symbol); ok && symbolCtx.LastPrice > 0 {
				pricesRight = append(pricesRight, symbolCtx.LastPrice)
			}
		}
		symbolLastPricesLeft[symbol] = pricesLeft
		symbolLastPricesRight[symbol] = pricesRight
	}

	// 检查品种价差风险，不用单个价格值，而是用 MA 值来判断，避免价格波动导致误报
	timeWindow = ParamPriceDiffTimeWindow
	for _, code := range allSymbolCodes {
		symbolLeft, symbolRight, err := this.TranslateBothSideSymbols(code)
		if err != nil {
			this.Errorf("get both side symbols failed, error: %s", err)
			continue
		}
		maLeft, countLeft, err := this.CalculateSymbolPriceMA(PriceTypeLastPrice, symbolLeft, timeWindow, false)
		if err != nil {
			if strings.Contains(err.Error(), "not enough data") {
				this.Debugf("not enough data to calculate symbol price ma, error: %s", err)
			} else {
				this.Errorf("calculate symbol price ma failed, error: %s", err)
			}
			continue
		}

		maRight, countRight, err := this.CalculateSymbolPriceMA(PriceTypeLastPrice, symbolRight, timeWindow, true)
		if err != nil {
			if strings.Contains(err.Error(), "not enough data") {
				this.Debugf("not enough data to calculate symbol price ma, error: %s", err)
			} else {
				this.Errorf("calculate symbol price ma failed, error: %s", err)
			}
			continue
		}
		if countLeft > priceDiffCountLimit && countRight > priceDiffCountLimit {
			// TODO: 高费率品种价差通常能达到 0.2%，priceDiffDelta 可能得根据情况调大一些
			realDiff := math.Abs(maLeft - maRight)
			priceDiffRatio := this.GetParam(ParamTypePriceDiff, code).(float64)
			targetDiff := maLeft * priceDiffRatio
			if realDiff > targetDiff {
				dangerousSymbols = append(dangerousSymbols, symbolLeft)
				diffRiskSymbols = append(diffRiskSymbols, symbolLeft)
				this.Warnf("symbol price diff: %s, ma left: %.2f, ma right: %.2f, real diff: %.2f, target diff: %.2f", symbolLeft, maLeft, maRight, realDiff, targetDiff)
			}
		}
	}
	if len(diffRiskSymbols) > 0 {
		risks = append(risks, RiskCategoryPriceDiff)
	}

	// 检查标记价格和指数价格的价差风险，标记价格和指数价格差别过大，可能导致额外爆仓
	timeWindow = ParamMarkPriceDiffTimeWindow
	countLimit := ParamMarkPriceDiffCountLimit
	for _, code := range allSymbolCodes {
		symbolLeft, symbolRight, err := this.TranslateBothSideSymbols(code)
		if err != nil {
			this.Errorf("get both side symbols failed, error: %s", err)
			continue
		}
		// 检查 ExchangeLeft 上的标记价格和指数价格的价差风险
		markLeft, markCountLeft, err := this.CalculateSymbolPriceMA(PriceTypeMarkPrice, symbolLeft, timeWindow, false)
		if err != nil {
			this.Errorf("calculate symbol price ma failed, error: %s", err)
			continue
		}
		indexLeft, indexCountLeft, err := this.CalculateSymbolPriceMA(PriceTypeIndexPrice, symbolLeft, timeWindow, false)
		if err != nil {
			this.Errorf("calculate symbol price ma failed, error: %s", err)
			continue
		}
		if markCountLeft > countLimit && indexCountLeft > countLimit {
			realDiff := math.Abs(markLeft - indexLeft)
			markPriceDiffRatio := this.GetParam(ParamTypeMarkPriceDiff, code).(float64)
			targetDiff := indexLeft * markPriceDiffRatio
			if realDiff > targetDiff {
				dangerousSymbols = append(dangerousSymbols, symbolLeft)
				markRiskSymbols = append(markRiskSymbols, symbolLeft)
				this.Warnf("exchange left, symbol mark price diff: %s, mark left: %.2f, index left: %.2f, real diff: %.2f, target diff: %.2f", symbolLeft, markLeft, indexLeft, realDiff, targetDiff)
			}
		}
		// 检查 ExchangeRight 上的标记价格和指数价格的价差风险
		markRight, markCountRight, err := this.CalculateSymbolPriceMA(PriceTypeMarkPrice, symbolRight, timeWindow, true)
		if err != nil {
			this.Errorf("calculate symbol price ma failed, error: %s", err)
			continue
		}
		indexRight, indexCountRight, err := this.CalculateSymbolPriceMA(PriceTypeIndexPrice, symbolRight, timeWindow, true)
		if err != nil {
			this.Errorf("calculate symbol price ma failed, error: %s", err)
			continue
		}
		if markCountRight > countLimit && indexCountRight > countLimit {
			realDiff := math.Abs(markRight - indexRight)
			markPriceDiffRatio := this.GetParam(ParamTypeMarkPriceDiff, code).(float64)
			targetDiff := indexRight * markPriceDiffRatio
			if realDiff > targetDiff {
				dangerousSymbols = append(dangerousSymbols, symbolRight)
				markRiskSymbols = append(markRiskSymbols, symbolRight)
				this.Warnf("exchange right, symbol mark price diff: %s, mark right: %.2f, index right: %.2f, real diff: %.2f, target diff: %.2f", symbolRight, markRight, indexRight, realDiff, targetDiff)
			}
		}
	}
	if len(markRiskSymbols) > 0 {
		risks = append(risks, RiskCategoryMarkPriceDiff)
	}

	// 检查止损止盈订单的创建时间是否已经失效
	// 如果止损止盈订单在启动后的 StoplossMAWindow 内没有生效，也认为是有风险的
	// 不将 stoplossExpiredSymbols 作为风险事件，因为万一发生该事件，可能是因为 API 频率限制引起，无法处理；强行平仓会导致损失很大
	for _, code := range allSymbolCodes {
		symbolLeft, symbolRight, err := this.TranslateBothSideSymbols(code)
		if err != nil {
			this.Errorf("get both side symbols failed, error: %s", err)
			continue
		}
		if this.StopLossOrdersLeft == nil || this.StopLossOrdersRight == nil {
			continue
		}
		order, ok := this.StopLossOrdersLeft.Load(symbolLeft)
		if ok && order != nil {
			// 检查 ExchangeLeft 上的止损止盈订单
			if order.UpdateTime.Add(StoplossMAWindow).Before(time.Now()) {
				stoplossExpiredSymbols = append(stoplossExpiredSymbols, symbolLeft)
			}
		}
		order, ok = this.StopLossOrdersRight.Load(symbolRight)
		if ok && order != nil {
			// 检查 ExchangeRight 上的止损止盈订单
			if order.UpdateTime.Add(StoplossMAWindow).Before(time.Now()) {
				stoplossExpiredSymbols = append(stoplossExpiredSymbols, symbolRight)
			}
		}
	}
	// 不将 stoplossExpiredSymbols 作为风险事件，因为万一发生该事件，可能是因为 API 频率限制引起，无法处理；强行平仓会导致损失很大
	// 因此不把 RiskCategoryStoplossExpired 放入 risks 中

	return
}

func (this *CrossExchangeArbitrager) CalculateSymbolPriceMA(priceType PriceType, symbol string, timeWindow time.Duration, isRight bool) (ma float64, count int, er error) {
	prices := []float64{}
	for _, snapshot := range this.Monitors {
		if snapshot.CreateTime.Add(timeWindow).Before(time.Now()) {
			continue
		}
		// 早于 MonitorRelaxAtTime 的数据不检查
		if snapshot.CreateTime.Before(this.MonitorRelaxAtTime) {
			continue
		}
		if isRight {
			symbolCtx, ok := snapshot.SymbolContextRight.Load(symbol)
			if ok {
				realPrice := 0.0
				switch priceType {
				case PriceTypeMarkPrice:
					realPrice = symbolCtx.MarkPrice
				case PriceTypeIndexPrice:
					realPrice = symbolCtx.IndexPrice
				case PriceTypeLastPrice:
					realPrice = symbolCtx.LastPrice
				}
				if realPrice > 0 {
					prices = append(prices, realPrice)
				}
			}
		} else {
			symbolCtx, ok := snapshot.SymbolContextLeft.Load(symbol)
			if ok {
				realPrice := 0.0
				switch priceType {
				case PriceTypeMarkPrice:
					realPrice = symbolCtx.MarkPrice
				case PriceTypeIndexPrice:
					realPrice = symbolCtx.IndexPrice
				case PriceTypeLastPrice:
					realPrice = symbolCtx.LastPrice
				}
				if realPrice > 0 {
					prices = append(prices, realPrice)
				}
			}
		}
	}
	if len(prices) > 0 {
		total := 0.0
		for _, price := range prices {
			total += price
		}
		ma = total / float64(len(prices))
		count = len(prices)
	} else {
		er = fmt.Errorf("not enough data to calculate ma")
		return
	}
	return
}

func (this *CrossExchangeArbitrager) UpdateAccountSnapshot() (er error) {
	this.mutexes.monitorsMutex.Lock()
	defer this.mutexes.monitorsMutex.Unlock()

	this.Infof("updating account snapshot...")

	// update account snapshot
	worthLeft, worthRight, err := this.GetAccountWorth()
	if err != nil {
		er = fmt.Errorf("get account worth failed, error: %s", err)
		return
	}
	positionsLeft, positionsRight, err := this.GetPositions()
	if err != nil || positionsLeft == nil || positionsRight == nil {
		er = fmt.Errorf("get positions failed, error: %s", err)
		return
	}
	accountSnapshot := NewAccountSnapshot(this.ExchangeLeft.GetName(), this.ExchangeRight.GetName()) // 不直接更新 this.AccountSnapshot，因为那样做会导致在更新过程中(时间较长）出现空值，导致打印状态全部是空值
	this.PositionSnapshot = &PositionSnapshot{
		ID:             accountSnapshot.ID,
		PositionsLeft:  positionsLeft,
		PositionsRight: positionsRight,
		CreateTime:     time.Now(),
	}

	for _, position := range positionsLeft {
		symbolCtx, _ := accountSnapshot.SymbolContextLeft.LoadOrStore(position.Symbol, NewSymbolContext())
		symbolCtx.LastPrice = position.GetLastPrice()
		instrument, err := this.ExchangeLeft.GetInstrument(exchange.USDXMarginedFutures, position.Symbol)
		if err != nil {
			this.Errorf("get instrument failed on left exchange, error: %s", err)
			continue
		}
		symbolCtx.MarkPrice = instrument.MarkPrice
		symbolCtx.IndexPrice = instrument.IndexPrice
		symbolCtx.FundingRate = instrument.FundingRate
		symbolCtx.OpenInterest = instrument.OpenInterest
		value, err := this.GetExchange(position.ExchangeName).Qty2Size(position.InstrumentType, position.Symbol, position.Qty, symbolCtx.LastPrice)
		if err == nil {
			symbolCtx.PositionValue = value
		} else {
			this.Errorf("calcualte hedge pnl failed, qty2size failed, error: %s", err)
		}
		accountSnapshot.SymbolContextLeft.Store(position.Symbol, symbolCtx)
	}
	for _, position := range positionsRight {
		symbolCtx, _ := accountSnapshot.SymbolContextRight.LoadOrStore(position.Symbol, NewSymbolContext())
		symbolCtx.LastPrice = position.GetLastPrice()
		instrument, err := this.ExchangeRight.GetInstrument(exchange.USDXMarginedFutures, position.Symbol)
		if err != nil {
			this.Errorf("get instrument failed on right exchange, error: %s", err)
			continue
		}
		symbolCtx.MarkPrice = instrument.MarkPrice
		symbolCtx.IndexPrice = instrument.IndexPrice
		symbolCtx.FundingRate = instrument.FundingRate
		symbolCtx.OpenInterest = instrument.OpenInterest
		ex := this.GetExchange(position.ExchangeName)
		value, err := ex.Qty2Size(position.InstrumentType, position.Symbol, position.Qty, symbolCtx.LastPrice)
		if err == nil {
			symbolCtx.PositionValue = value
		}

		// calculate hedge pnl
		hedgePnl := 0.0
		if symbolCtx.PositionValue != 0 {
			symbolLeft, err := this.ConvertSymbol(position.Symbol, false)
			if err == nil {
				symbolContextLeft, _ := accountSnapshot.SymbolContextLeft.Load(symbolLeft)
				if symbolContextLeft != nil {
					positionValueLeft := symbolContextLeft.PositionValue
					if positionValueLeft != 0 {
						hedgePnl = -(symbolCtx.PositionValue + positionValueLeft)
					}
				} else {
					this.Errorf("calcualte hedge pnl failed, symbol context left is nil")
				}
			} else {
				this.Errorf("calcualte hedge pnl failed, translate symbol failed, error: %s", err)
			}
		}
		_, symbolCodeRight, err := ex.TranslateFutureSymbol(position.InstrumentType, position.Symbol, this.ControllerRight.GetBaseConfig().USDXSymbol)
		if err == nil {
			accountSnapshot.HedgePNLs.Store(symbolCodeRight.Code, hedgePnl)
		}
		accountSnapshot.SymbolContextRight.Store(position.Symbol, symbolCtx)
	}

	positionValueLeft, positionLeverageLeft, err := this.CalcuatePositionValue(this.ExchangeLeft, positionsLeft)
	if err != nil {
		er = err
		return
	}
	positionValueRight, positionLeverageRight, err := this.CalcuatePositionValue(this.ExchangeRight, positionsRight)
	if err != nil {
		er = err
		return
	}

	accountSnapshot.PositionValueLeft = positionValueLeft
	accountSnapshot.PositionValueRight = positionValueRight
	accountSnapshot.PositionLeverageLeft = positionLeverageLeft
	accountSnapshot.PositionLeverageRight = positionLeverageRight

	if positionLeverageLeft != 0 {
		accountSnapshot.MarginAvailableLeft = worthLeft - positionValueLeft/positionLeverageLeft
	} else {
		accountSnapshot.MarginAvailableLeft = worthLeft
	}

	if accountSnapshot.MarginAvailableLeft < 0 {
		accountSnapshot.MarginAvailableLeft = 0
	}

	if positionLeverageRight != 0 {
		accountSnapshot.MarginAvailableRight = worthRight - positionValueRight/positionLeverageRight
	} else {
		accountSnapshot.MarginAvailableRight = worthRight
	}

	if accountSnapshot.MarginAvailableRight < 0 {
		accountSnapshot.MarginAvailableRight = 0
	}

	accountSnapshot.MarginValueLeft = worthLeft
	accountSnapshot.MarginValueRight = worthRight
	if accountSnapshot.PositionValueLeft > 0 {
		accountSnapshot.MarginRatioLeft = accountSnapshot.MarginValueLeft / accountSnapshot.PositionValueLeft
	} else {
		accountSnapshot.MarginRatioLeft = 100
	}
	if accountSnapshot.PositionValueRight > 0 {
		accountSnapshot.MarginRatioRight = accountSnapshot.MarginValueRight / accountSnapshot.PositionValueRight
	} else {
		accountSnapshot.MarginRatioRight = 100
	}
	accountSnapshot.MarginValueTotal = accountSnapshot.MarginValueLeft + accountSnapshot.MarginValueRight
	accountSnapshot.PositionValueTotal = accountSnapshot.PositionValueLeft + accountSnapshot.PositionValueRight
	if accountSnapshot.PositionValueTotal > 0 {
		accountSnapshot.MarginRatioTotal = accountSnapshot.MarginValueTotal / accountSnapshot.PositionValueTotal
	} else {
		accountSnapshot.MarginRatioTotal = 100
	}
	accountSnapshot.CreateTime = time.Now()
	this.AccountSnapshot = accountSnapshot

	// update monitors
	this.Monitors = append(this.Monitors, this.AccountSnapshot)
	// 约 5 分钟的高频数据
	if len(this.Monitors) > MonitorLimit {
		this.Monitors = this.Monitors[len(this.Monitors)-MonitorLimit:]
	}
	// update account snapshots
	hourTimestamp := this.AccountSnapshot.CreateTime.Truncate(time.Hour).Unix()
	if this.AccountSnapshots == nil {
		this.AccountSnapshots = exchange.NewSyncMapOf[int64, *AccountSnapshot]()
	}
	this.AccountSnapshots.Store(hourTimestamp, this.AccountSnapshot)

	this.PositionMonitors = append(this.PositionMonitors, this.PositionSnapshot)
	// 约 5 分钟的高频数据
	if len(this.PositionMonitors) > MonitorLimit {
		this.PositionMonitors = this.PositionMonitors[len(this.PositionMonitors)-MonitorLimit:]
	}

	// update position history
	hourTimestamp = this.PositionSnapshot.CreateTime.Truncate(time.Hour).Unix()
	if this.PositionHistory == nil {
		this.PositionHistory = exchange.NewSyncMapOf[int64, *PositionSnapshot]()
	}
	this.PositionHistory.Store(hourTimestamp, this.PositionSnapshot)
	// 删除较早的数据
	this.PositionHistory.Range(func(timestamp int64, snapshot *PositionSnapshot) bool {
		if timestamp < hourTimestamp-PositionHistoryLimit {
			this.PositionHistory.Delete(timestamp)
		}
		return true
	})
	go this.SaveAccountSnapshot(this.AccountSnapshot)
	go this.SavePositionSnapshot(this.PositionSnapshot)
	go this.controller.GetStorage().Save()
	return nil
}

// 仅获取 USDX 合约的资产价值，因为其他账号如现货可能是独立的，无法做保证金
func (this *CrossExchangeArbitrager) GetAccountWorth() (worthLeft, worthRight float64, er error) {
	if err := this.CheckExchanges(); err != nil {
		er = err
		return
	}

	var errLeft, errRight error
	wg := sync.WaitGroup{}
	wg.Add(2)

	go func() {
		defer wg.Done()
		worthLeft, errLeft = this.ExchangeLeft.GetAccountWorth(this.ControllerIDLeft, []exchange.InstrumentType{exchange.USDXMarginedFutures}, 0, nil)
	}()

	go func() {
		defer wg.Done()
		worthRight, errRight = this.ExchangeRight.GetAccountWorth(this.ControllerIDRight, []exchange.InstrumentType{exchange.USDXMarginedFutures}, 0, nil)
	}()

	wg.Wait()

	// Handle errors after all goroutines complete
	if errLeft != nil {
		return 0, 0, fmt.Errorf("get account worth of left exchange failed: %w", errLeft)
	}
	if errRight != nil {
		return 0, 0, fmt.Errorf("get account worth of right exchange failed: %w", errRight)
	}

	return
}

// 获取所有合约持仓，而不是只有 USDX 合约，因为需要计算非 U 本位的合约价值
func (this *CrossExchangeArbitrager) GetPositions() (positionsLeft []*exchange.Position, positionsRight []*exchange.Position, er error) {
	wg := sync.WaitGroup{}
	wg.Add(2)

	var errLeft, errRight error
	go func() {
		defer wg.Done()
		positionsLeft = []*exchange.Position{}
		for _, instrumentType := range this.ExchangeLeft.GetSupportedInstrumentTypes() {
			if !instrumentType.IsFuture() {
				continue
			}
			iPositions, err := this.ExchangeLeft.GetPositions(instrumentType, "", false)
			if err != nil {
				errLeft = err
				return
			}
			positionsLeft = append(positionsLeft, iPositions...)
		}
	}()
	go func() {
		defer wg.Done()
		positionsRight = []*exchange.Position{}
		for _, instrumentType := range this.ExchangeRight.GetSupportedInstrumentTypes() {
			if !instrumentType.IsFuture() {
				continue
			}
			iPositions, err := this.ExchangeRight.GetPositions(instrumentType, "", false)
			if err != nil {
				errRight = err
				return
			}
			positionsRight = append(positionsRight, iPositions...)
		}
	}()
	wg.Wait()
	if errLeft != nil {
		return nil, nil, fmt.Errorf("get positions on exchange left failed, error: %s", errLeft)
	}
	if errRight != nil {
		return nil, nil, fmt.Errorf("get positions on exchange right failed, error: %s", errRight)
	}
	return
}

// 检查非 U 本位的合约，需要报警，可能是操作失误
func (this *CrossExchangeArbitrager) HasAlienPositions() (yes bool, er error) {
	alienSymbolsLeft := []string{}
	alienSymbolsRight := []string{}
	defer func() {
		if yes {
			this.AddRiskEvent(RiskCategoryAlienPosition, fmt.Sprintf("symbols, left: %v, right: %v", alienSymbolsLeft, alienSymbolsRight))
		}
	}()
	this.Infof("checking alien positions...")

	// 使用快照数据来判断，尽量提高效率
	if this.PositionSnapshot == nil {
		return false, nil
	}
	positionsLeft := this.PositionSnapshot.PositionsLeft
	positionsRight := this.PositionSnapshot.PositionsRight
	alienPositionsLeft := []*exchange.Position{}
	alienPositionsRight := []*exchange.Position{}
	for _, position := range positionsLeft {
		if position.InstrumentType != exchange.USDXMarginedFutures {
			alienPositionsLeft = append(alienPositionsLeft, position)
			alienSymbolsLeft = append(alienSymbolsLeft, position.Symbol)
		}
	}
	for _, position := range positionsRight {
		if position.InstrumentType != exchange.USDXMarginedFutures {
			alienPositionsRight = append(alienPositionsRight, position)
			alienSymbolsRight = append(alienSymbolsRight, position.Symbol)
		}
	}
	if len(alienPositionsLeft) > 0 || len(alienPositionsRight) > 0 {
		return true, nil
	}
	return false, nil
}

func (this *CrossExchangeArbitrager) CalcuatePositionValue(ex exchange.Exchange, positions []*exchange.Position) (value, leverage float64, er error) {
	usedMargin := 0.0
	for _, position := range positions {
		if position.Qty == 0 {
			continue
		}
		lastPrice := position.LastPrice
		if lastPrice == 0 {
			lastPrice = position.MarkPrice
		}
		if lastPrice == 0 {
			return 0, 0, fmt.Errorf("calculate position value failed, position last price is 0")
		}
		if position.InstrumentType == exchange.USDXMarginedFutures {
			v, err := ex.Qty2Size(position.InstrumentType, position.Symbol, lastPrice, math.Abs(position.Qty))
			if err != nil {
				er = fmt.Errorf("calculate position value failed, error: %s", err)
				return
			}
			value += v
			initialMargin := position.InitialMargin
			if initialMargin == 0 {
				initialValue, err := ex.Qty2Size(position.InstrumentType, position.Symbol, position.EntryPrice, math.Abs(position.Qty))
				if err != nil {
					er = fmt.Errorf("calculate position value failed, error: %s", err)
					return
				}
				initialMargin = initialValue / position.Leverage
			}
			usedMargin += initialMargin
		} else if position.InstrumentType == exchange.CoinMarginedFutures {
			v, err := ex.Qty2Size(position.InstrumentType, position.Symbol, lastPrice, math.Abs(position.Qty))
			if err != nil {
				er = fmt.Errorf("calculate position value failed, error: %s", err)
				return
			}
			value += v * lastPrice
			initialMargin := position.InitialMargin
			if initialMargin == 0 {
				initialValue, err := ex.Qty2Size(position.InstrumentType, position.Symbol, position.EntryPrice, math.Abs(position.Qty))
				if err != nil {
					er = fmt.Errorf("calculate position value failed, error: %s", err)
					return
				}
				initialMargin = initialValue / position.Leverage
				initialMargin *= position.EntryPrice
			}
			usedMargin += initialMargin
		} else {
			this.Errorf("calculate position value failed, unsupported instrument type: %s", position.InstrumentType)
		}
	}
	if usedMargin > 0 {
		leverage = value / usedMargin
	}
	return
}

// 检查最近一次提币是否还在处理中，防止重复提币
func (this *CrossExchangeArbitrager) LastWithdrawProcessing() bool {
	if len(this.Withdraws) == 0 {
		return false
	}
	return this.Withdraws[len(this.Withdraws)-1].IsProcessing()
}

func (this *CrossExchangeArbitrager) Debugf(format string, args ...any) {
	this.controller.Debugf("(%s) %s", this.ID, fmt.Sprintf(format, args...))
}

func (this *CrossExchangeArbitrager) Infof(format string, args ...any) {
	this.controller.Infof("(%s) %s", this.ID, fmt.Sprintf(format, args...))
}

func (this *CrossExchangeArbitrager) Warnf(format string, args ...any) {
	this.controller.Warnf("(%s) %s", this.ID, fmt.Sprintf(format, args...))
}

func (this *CrossExchangeArbitrager) Errorf(format string, args ...any) {
	this.controller.Errorf("(%s) %s", this.ID, fmt.Sprintf(format, args...))
}

func (this *CrossExchangeArbitrager) SendMsgf(format string, args ...any) {
	this.controller.SendMsgf("(%s) %s", this.GetAliasOrID(), fmt.Sprintf(format, args...))
}

func (this *CrossExchangeArbitrager) ErrorMsgf(format string, args ...any) {
	this.controller.ErrorMsgf("(%s) %s", this.GetAliasOrID(), fmt.Sprintf(format, args...))
}

func (this *CrossExchangeArbitrager) WarnMsgf(format string, args ...any) {
	this.controller.WarnMsgf("(%s) %s", this.GetAliasOrID(), fmt.Sprintf(format, args...))
}

func (this *CrossExchangeArbitrager) AlertMsgf(format string, args ...any) {
	this.controller.AlertMsgf("(%s) %s", this.GetAliasOrID(), fmt.Sprintf(format, args...))
}

func (this *CrossExchangeArbitrager) Stop() {
	this.Status = StatusStopped
	this.StatusUpdateTime = time.Now()
	this.MonitorRelaxAtTime = time.Now()
	this.controller.GetStorage().Save()
}

func (this *CrossExchangeArbitrager) Pause() {
	this.Status = StatusPaused
	this.StatusUpdateTime = time.Now()
	this.MonitorRelaxAtTime = time.Now()
	this.controller.GetStorage().Save()
}

func (this *CrossExchangeArbitrager) Resume() {
	this.Status = StatusRunning
	this.StatusUpdateTime = time.Now()
	this.MonitorRelaxAtTime = time.Now()
	this.controller.GetStorage().Save()
}

func (this *CrossExchangeArbitrager) Close(splits int) error {
	posLeft, _, err := this.GetPositions()
	if err != nil {
		return fmt.Errorf("获取持仓失败: %s", err)
	}
	symbols := []string{}
	for _, pos := range posLeft {
		symbols = append(symbols, pos.Symbol)
	}
	this.ClosePositions(symbols, splits)
	return nil
}

func (this *CrossExchangeArbitrager) CheckLoanAndPay() {
	exchanges := []string{exchange.Bybit, exchange.Binance}
	var ex exchange.Exchange
	var err error
	for _, exchangeName := range exchanges {
		ex, _, err = this.parseExchange(exchangeName)
		if err != nil {
			continue
		}
		break
	}

	if ex == nil {
		this.Warnf("no exchange available to check loan and pay")
		return
	}

	// 检查是否有 USDT 欠款，如果有，卖出 USDC-USDT 还款
	// 反之如果有浮盈，全部买入 USDC-USDT
	balance, err := ex.GetAccountBalances(exchange.USDXMarginedFutures)
	if err != nil {
		this.Errorf("检查 USDT 欠款失败: %s", err)
		return
	}
	usdtBalance := 0.0
	for _, b := range balance {
		if b.Currency == "USDT" {
			usdtBalance = b.Total
			break
		}
	}

	if usdtBalance == 0 {
		this.Infof("USDT 无欠款")
		return
	}

	MinValue := 10.0
	// 太小可能不满足最小交易量
	if usdtBalance > 0 && usdtBalance < MinValue {
		this.Infof("USDT 余额 %.2f，小于最小交易量，不处理", usdtBalance)
		return
	} else if usdtBalance < 0 && math.Abs(usdtBalance) < MinValue {
		usdtBalance = -MinValue
	}

	var symbol string
	if ex.GetName() == exchange.Binance {
		symbol = "USDCUSDT"

		// bn 还需要在现货、合约之间转账
		if usdtBalance > 0 {
			// 转 USDT 到现货买 USDC
			err := ex.TransferAsset(exchange.USDXMarginedFutures, exchange.Spot, "USDT", usdtBalance)
			if err != nil {
				this.ErrorMsgf("USDT 兑换 USDC 转账失败: %s", err)
				return
			}
		} else {
			// 转 USDC 到现货卖出
			price, err := ex.GetLastPrice(exchange.Spot, symbol, false)
			if err != nil {
				this.ErrorMsgf("获取 USDCUSDT 价格失败: %s", err)
				return
			}
			usdcAmount := math.Abs(usdtBalance) / price * 1.005 // 多转一点
			err = ex.TransferAsset(exchange.USDXMarginedFutures, exchange.Spot, "USDC", usdcAmount)
			if err != nil {
				this.ErrorMsgf("USDC 还款转账失败: %s", err)
				return
			}
		}
	} else if ex.GetName() == exchange.Bybit {
		symbol = "USDCUSDT"
	}

	orderArgs := exchange.CreateOrderArgs{
		InstrumentType: exchange.Spot,
		Symbol:         symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCross,
		QuoteQty:       math.Abs(usdtBalance - 0.1),
		TimeInForce:    exchange.GTC,
		Side:           exchange.OrderSideSell,
		ReduceOnly:     true,
	}

	if usdtBalance > 0 {
		orderArgs.Side = exchange.OrderSideBuy
		orderArgs.QuoteQty = usdtBalance * 0.999
	}

	orderArgs.QuoteQty = math.Floor(orderArgs.QuoteQty*100) / 100
	order, err := ex.CreateOrder(orderArgs)
	if err != nil {
		if usdtBalance > 0 {
			this.ErrorMsgf("USDT 兑换 USDC 订单失败: %s", err)
		} else {
			this.ErrorMsgf("USDT 还款订单失败: %s", err)
		}
	} else {
		if usdtBalance > 0 {
			this.SendMsgf("USDT 兑换 USDC 成功，兑换金额: %.2f", usdtBalance)
		} else {
			this.SendMsgf("USDT 还款成功，还款金额: %.2f", usdtBalance)
		}
	}

	time.Sleep(5 * time.Second)

	if ex.GetName() == exchange.Binance {
		exeOrder, err := ex.GetOrderByOrig(*order)
		if err != nil {
			this.ErrorMsgf("获取还款订单失败: %s", err)
			return
		}
		if exeOrder.Side == exchange.OrderSideSell {
			// 兑换的 USDT 转回合约账号还款
			_, available, err := ex.GetBalance(exchange.Spot, "USDT")
			if err != nil {
				this.ErrorMsgf("获取 USDT 余额失败: %s", err)
				return
			}
			err = ex.TransferAsset(exchange.Spot, exchange.USDXMarginedFutures, "USDT", math.Min(exeOrder.QuoteQty, available))
			if err != nil {
				this.ErrorMsgf("USDT 还款转账到合约失败: %s", err)
				return
			}
		} else {
			// 买入的 USDC 转回合约账号
			_, available, err := ex.GetBalance(exchange.USDXMarginedFutures, "USDC")
			if err != nil {
				this.ErrorMsgf("获取 USDC 余额失败: %s", err)
				return
			}
			err = ex.TransferAsset(exchange.Spot, exchange.USDXMarginedFutures, "USDC", math.Min(exeOrder.ExecQty, available))
			if err != nil {
				this.ErrorMsgf("USDC 转账到合约失败: %s", err)
				return
			}
		}
	}
}

// 检查订单在当前资金费率下是否赚钱
// CreateOrderArgs 是 ExchangeLeft 上的操作，同时检查 Left 和 Right 上的资金费率
func (this *CrossExchangeArbitrager) CheckOrderProfitAgainstCurrentRate(symbolCode *exchange.SymbolCode, orderArgs exchange.CreateOrderArgs) (profitRate float64, leftFundingRate, rightFundingRate float64, er error) {
	leftSymbol, rightSymbol, err := this.TranslateBothSideSymbols(symbolCode.String())
	if err != nil {
		er = fmt.Errorf("get both side symbols failed: %s", err)
		return
	}
	leftInstrument, err := this.ExchangeLeft.GetInstrument(symbolCode.InstrumentType(), leftSymbol)
	if err != nil {
		er = fmt.Errorf("get instrument of left exchange failed: %s", err)
		return
	}
	rightInstrument, err := this.ExchangeRight.GetInstrument(symbolCode.InstrumentType(), rightSymbol)
	if err != nil {
		er = fmt.Errorf("get instrument of right exchange failed: %s", err)
		return
	}
	leftFundingRate = leftInstrument.GetFundingRateAPR(orderArgs.Side == exchange.OrderSideBuy)
	rightFundingRate = rightInstrument.GetFundingRateAPR(orderArgs.Side != exchange.OrderSideBuy)
	profitRate = (leftFundingRate + rightFundingRate)

	return
}

func (this *CrossExchangeArbitrager) GetCreateOrderArgs(longOrShort string, symbolCodeStr string, qtyStr string) (symbolCode *exchange.SymbolCode, orderArgs exchange.CreateOrderArgs, er error) {
	_, _, err := this.TranslateBothSideSymbols(symbolCodeStr)
	if err != nil {
		er = fmt.Errorf("translate both side symbols failed: %s", err)
		return
	}
	uSymbol := this.ControllerLeft.GetBaseConfig().USDXSymbol
	symbolCode, err = exchange.NewSymbolCode(symbolCodeStr, uSymbol)
	if err != nil {
		er = fmt.Errorf("parse symbol code failed: %s", err)
		return
	}
	if !symbolCode.IsFuture() {
		er = fmt.Errorf("symbol code is not future: %s", symbolCode)
		return
	}
	symbol, err := this.ExchangeLeft.TranslateSymbolCodeToFutureSymbol(symbolCode)
	if err != nil {
		er = fmt.Errorf("translate symbol code failed: %s", err)
		return
	}
	orderType := exchange.Market
	tradeMode := exchange.TradeModeCross

	quoteQty := 0.0
	qty := 0.0
	if len(qtyStr) > 2 && exchange.SliceContains([]string{".u", ".d", ".y"}, qtyStr[len(qtyStr)-2:]) {
		if symbolCode.IsFuture() {
			suffix := qtyStr[len(qtyStr)-2:]
			validSuffix := symbolCode.InstrumentType().GetSuffix()
			if !strings.EqualFold(suffix, validSuffix) {
				er = fmt.Errorf("%s future symbol code only support %s suffix", symbolCode, strings.ToLower(validSuffix))
				return
			}
		}
		qtyStr = qtyStr[:len(qtyStr)-2]
		quoteQty, err = strconv.ParseFloat(qtyStr, 64)
		if err != nil {
			er = fmt.Errorf("qty args invalid: %s", err)
			return
		}
	} else {
		qty, err = strconv.ParseFloat(qtyStr, 64)
		if err != nil {
			er = fmt.Errorf("qty args invalid: %s", err)
			return
		}
	}

	side := exchange.OrderSideBuy
	if longOrShort == "short" {
		side = exchange.OrderSideSell
	}
	orderArgs = exchange.CreateOrderArgs{
		InstrumentType: symbolCode.InstrumentType(),
		Symbol:         symbol,
		Type:           orderType,
		TradeMode:      tradeMode,
		Qty:            qty,
		QuoteQty:       quoteQty,
		TimeInForce:    exchange.GTC,
		Side:           side,
	}
	return
}

func (this *CrossExchangeArbitrager) GetExchange(name string) exchange.Exchange {
	if this.ExchangeLeft.GetName() == name {
		return this.ExchangeLeft
	}
	if this.ExchangeRight.GetName() == name {
		return this.ExchangeRight
	}
	return nil
}

func (this *CrossExchangeArbitrager) GetAliasOrID() string {
	if this.Alias != "" {
		return this.Alias
	}
	return this.ID
}

// 对所有仓位挂止盈止损单
// 先挂新的止盈止损单，再取消旧的
func (this *CrossExchangeArbitrager) handleStoplossOrders() {
	if !this.mutexes.handleStoplossOrdersMutex.TryLock() {
		return
	}

	defer this.mutexes.handleStoplossOrdersMutex.Unlock()

	if this.StopLossOrdersLeft == nil {
		this.StopLossOrdersLeft = exchange.NewSyncMapOf[string, *exchange.Order]()
	}
	if this.StopLossOrdersRight == nil {
		this.StopLossOrdersRight = exchange.NewSyncMapOf[string, *exchange.Order]()
	}
	if this.TakeProfitOrdersLeft == nil {
		this.TakeProfitOrdersLeft = exchange.NewSyncMapOf[string, *exchange.Order]()
	}
	if this.TakeProfitOrdersRight == nil {
		this.TakeProfitOrdersRight = exchange.NewSyncMapOf[string, *exchange.Order]()
	}

	if this.Maintenances.HasActiveMaintainance() {
		// 维护期间不挂止损止盈单，取消止盈止损单
		this.StopLossOrdersLeft.Range(func(symbol string, order *exchange.Order) bool {
			err := this.ExchangeLeft.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel stop loss order failed, error: %s", err)
			}
			this.StopLossOrdersLeft.Delete(symbol)
			this.Infof("remove left stop loss order during maintenance: %s", symbol)
			return true
		})

		this.TakeProfitOrdersLeft.Range(func(symbol string, order *exchange.Order) bool {
			err := this.ExchangeLeft.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel take profit order failed, error: %s", err)
			}
			this.TakeProfitOrdersLeft.Delete(symbol)
			this.Infof("remove left take profit order during maintenance: %s", symbol)
			return true
		})

		this.StopLossOrdersRight.Range(func(symbol string, order *exchange.Order) bool {
			err := this.ExchangeRight.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel stop loss order failed, error: %s", err)
			}
			this.StopLossOrdersRight.Delete(symbol)
			this.Infof("remove right stop loss order during maintenance: %s", symbol)
			return true
		})

		this.TakeProfitOrdersRight.Range(func(symbol string, order *exchange.Order) bool {
			err := this.ExchangeRight.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel take profit order failed, error: %s", err)
			}
			this.TakeProfitOrdersRight.Delete(symbol)
			this.Infof("remove right take profit order during maintenance: %s", symbol)
			return true
		})
		return
	}

	positionsLeft, positionsRight, err := this.GetPositions()
	if err != nil || positionsLeft == nil || positionsRight == nil {
		this.Errorf("handle close orders failed, get positions err: %s", err)
		return
	}

	// 删除不在持仓中的止盈止损单 BEGIN
	this.StopLossOrdersLeft.Range(func(symbol string, order *exchange.Order) bool {
		posFound := false
		for _, position := range positionsLeft {
			if position.Symbol == symbol && position.Qty != 0 {
				posFound = true
				break
			}
		}
		if !posFound {
			err := this.ExchangeLeft.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel stop loss order failed, error: %s", err)
			}
			this.StopLossOrdersLeft.Delete(symbol)
			this.Infof("remove left stop loss order: %s", symbol)
		}
		return true
	})

	this.TakeProfitOrdersLeft.Range(func(symbol string, order *exchange.Order) bool {
		posFound := false
		for _, position := range positionsLeft {
			if position.Symbol == symbol && position.Qty != 0 {
				posFound = true
				break
			}
		}
		if !posFound {
			err := this.ExchangeLeft.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel take profit order failed, error: %s", err)
			}
			this.TakeProfitOrdersLeft.Delete(symbol)
			this.Infof("remove left take profit order: %s", symbol)
		}
		return true
	})

	this.StopLossOrdersRight.Range(func(symbol string, order *exchange.Order) bool {
		posFound := false
		for _, position := range positionsRight {
			if position.Symbol == symbol && position.Qty != 0 {
				posFound = true
				break
			}
		}
		if !posFound {
			err := this.ExchangeRight.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel stop loss order failed, error: %s", err)
			}
			this.StopLossOrdersRight.Delete(symbol)
			this.Infof("remove right stop loss order: %s", symbol)

			// 无持仓但有止损单，则可能触发了平仓，需平仓左边的仓位
			code, err := this.GetSymbolCodeRight(symbol)
			if err != nil {
				this.Errorf("get symbol code failed, error: %s", err)
				return true
			}
			lock, _ := this.SymbolLocks.Load(code.String())
			if lock == nil {
				return true
			}
			// 需要再获取一次仓位检查，防止 rotate 等可能导致的仓位变动
			lock.RatatingPosition.Lock()
			defer lock.RatatingPosition.Unlock()

			position, err := this.ExchangeRight.GetPosition(exchange.USDXMarginedFutures, symbol, exchange.UnknownPositionSide, false)
			if err != nil {
				this.Errorf("get position failed, error: %s", err)
				return true
			}
			if position != nil && position.Qty != 0 {
				this.Warnf("right position not closed, symbol: %s, qty: %.2f", symbol, position.Qty)
				return true
			}

			symbolLeft, err := this.ConvertSymbol(symbol, false)
			if err != nil {
				this.Errorf("convert symbol failed, error: %s", err)
			} else {
				this.Infof("close left position: %s, because right position closed by stop loss", symbolLeft)
				this.ClosePositions([]string{symbolLeft}, 1)
			}
		}
		return true
	})

	this.TakeProfitOrdersRight.Range(func(symbol string, order *exchange.Order) bool {
		posFound := false
		for _, position := range positionsRight {
			if position.Symbol == symbol && position.Qty != 0 {
				posFound = true
				break
			}
		}
		if !posFound {
			err := this.ExchangeRight.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel take profit order failed, error: %s", err)
			}
			this.TakeProfitOrdersRight.Delete(symbol)
			this.Infof("remove right take profit order: %s", symbol)

			// 无持仓但有止盈单，则可能触发了平仓，需平仓左边的仓位
			code, err := this.GetSymbolCodeRight(symbol)
			if err != nil {
				this.Errorf("get symbol code failed, error: %s", err)
				return true
			}
			lock, _ := this.SymbolLocks.Load(code.String())
			if lock == nil {
				return true
			}
			// 需要再获取一次仓位检查，防止 rotate 等可能导致的仓位变动
			lock.RatatingPosition.Lock()
			defer lock.RatatingPosition.Unlock()

			position, err := this.ExchangeRight.GetPosition(exchange.USDXMarginedFutures, symbol, exchange.UnknownPositionSide, false)
			if err != nil {
				this.Errorf("get position failed, error: %s", err)
				return true
			}
			if position != nil && position.Qty != 0 {
				this.Warnf("right position not closed, symbol: %s, qty: %.2f", symbol, position.Qty)
				return true
			}

			symbolLeft, err := this.ConvertSymbol(symbol, false)
			if err != nil {
				this.Errorf("convert symbol failed, error: %s", err)
			} else {
				this.Infof("close left position: %s, because right position closed by take profit", symbolLeft)
				this.ClosePositions([]string{symbolLeft}, 1)
			}
		}
		return true
	})
	// 删除不在持仓中的止盈止损单 END

	// 每 15 秒更新一次止损止盈订单
	if time.Since(this.StoplossOrdersLastHandleTime) <= time.Duration(this.Options.StoplossUpdateInterval)*time.Second {
		this.Infof("skip handle stoploss orders, last handle time: %s", this.StoplossOrdersLastHandleTime)
		return
	}

	this.SymbolLocks.Range(func(_ string, lock *SymbolLock) bool {
		// 随机 0 ~ StoplossUpdateInterval 的时间，避免同时更新
		shorterInterval := int(float64(this.Options.StoplossUpdateInterval) * 0.8)
		lock.UpdateStoplossDelay = time.Duration(rand.Intn(shorterInterval)) * time.Second
		return true
	})

	// 找到同一品种的仓位，同时处理止损止盈单
	for _, positionLeft := range positionsLeft {
		if positionLeft.Qty == 0 {
			continue
		}

		var positionRight *exchange.Position
		for _, position := range positionsRight {
			if this.IsTheSameSymbolCode(positionLeft.InstrumentType, positionLeft.Symbol, position.Symbol) {
				positionRight = position
				break
			}
		}
		if positionRight == nil || positionRight.Qty == 0 {
			continue
		}

		// 如果有止损止盈单，且价差在一定范围内，则无需重新挂单，仅更新修改时间
		slArgsLeft, tpArgsLeft, err := this.getStoplossOrderArgs(positionLeft, false)
		if err != nil {
			this.Errorf("get stoploss order args failed, error: %s", err)
			continue
		}

		slArgsRight, tpArgsRight, err := this.getStoplossOrderArgs(positionRight, true)
		if err != nil {
			this.Errorf("get stoploss order args failed, error: %s", err)
			continue
		}

		if ctx, load := this.AccountSnapshot.SymbolContextLeft.Load(positionLeft.Symbol); load {
			ctx.StopLossPrice = slArgsLeft.TriggerPrice
			ctx.TakeProfitPrice = tpArgsLeft.TriggerPrice
		}
		if ctx, load := this.AccountSnapshot.SymbolContextRight.Load(positionRight.Symbol); load {
			ctx.StopLossPrice = slArgsRight.TriggerPrice
			ctx.TakeProfitPrice = tpArgsRight.TriggerPrice
		}

		code, _ := this.GetSymbolCodeLeft(positionLeft.Symbol)
		priceRatio := this.GetParam(ParamTypeStoploss, code.String()).(float64)
		priceDeltaRatio := priceRatio * this.Options.StoplossDiffTolerance

		needUpdateOrderCount := 4 // 任一订单需要更新，则同时更新左右两边的订单
		now := time.Now()
		lastSlOrderLeft, ok := this.StopLossOrdersLeft.Load(positionLeft.Symbol)
		if ok && lastSlOrderLeft != nil {
			if math.Abs(slArgsLeft.TriggerPrice/lastSlOrderLeft.TriggerPrice-1) <= priceDeltaRatio &&
				slArgsLeft.Qty == lastSlOrderLeft.Qty {
				needUpdateOrderCount--
				lastSlOrderLeft.UpdateTime = &now
			}
		}
		lastTpOrderLeft, ok := this.TakeProfitOrdersLeft.Load(positionLeft.Symbol)
		if ok && lastTpOrderLeft != nil {
			if math.Abs(tpArgsLeft.TriggerPrice/lastTpOrderLeft.TriggerPrice-1) <= priceDeltaRatio &&
				tpArgsLeft.Qty == lastTpOrderLeft.Qty {
				needUpdateOrderCount--
				lastTpOrderLeft.UpdateTime = &now
			}
		}
		lastSlOrderRight, ok := this.StopLossOrdersRight.Load(positionRight.Symbol)
		if ok && lastSlOrderRight != nil {
			if math.Abs(slArgsRight.TriggerPrice/lastSlOrderRight.TriggerPrice-1) <= priceDeltaRatio &&
				slArgsRight.Qty == lastSlOrderRight.Qty {
				needUpdateOrderCount--
				lastSlOrderRight.UpdateTime = &now
			}
		}
		lastTpOrderRight, ok := this.TakeProfitOrdersRight.Load(positionRight.Symbol)
		if ok && lastTpOrderRight != nil {
			if math.Abs(tpArgsRight.TriggerPrice/lastTpOrderRight.TriggerPrice-1) <= priceDeltaRatio &&
				tpArgsRight.Qty == lastTpOrderRight.Qty {
				needUpdateOrderCount--
				lastTpOrderRight.UpdateTime = &now
			}
		}
		if needUpdateOrderCount > 0 {
			go this.updateStoplossOrder(slArgsLeft, tpArgsLeft, false)
			go this.updateStoplossOrder(slArgsRight, tpArgsRight, true)
		} else {
			this.Infof("price delta is within tolerance, skip update stoploss order, code: %s", code)
		}
	}

	this.StoplossOrdersLastHandleTime = time.Now()
}

func (this *CrossExchangeArbitrager) getStoplossOrderArgs(position *exchange.Position, isRight bool) (slArgs, tpArgs *exchange.CreateOrderArgs, err error) {
	ma, _, err := this.CalculateSymbolPriceMA(PriceTypeLastPrice, position.Symbol, StoplossMAWindow, isRight)
	if err != nil {
		return nil, nil, fmt.Errorf("calculate symbol price ma failed, error: %s", err)
	}

	var code *exchange.SymbolCode
	if isRight {
		code, err = this.GetSymbolCodeRight(position.Symbol)
		if err != nil {
			return nil, nil, fmt.Errorf("get symbol code failed: %s", err)
		}
	} else {
		code, err = this.GetSymbolCodeLeft(position.Symbol)
		if err != nil {
			return nil, nil, fmt.Errorf("get symbol code failed: %s", err)
		}
	}

	// 根据价格范围调整止损止盈比例，价格越低，止损止盈比例越大
	priceRatio := this.GetParam(ParamTypeStoploss, code.String()).(float64) // 如果启用 stddev 算法，则使用 stddev 算法计算价格波动比例

	var stopLossTriggerPrice, takeProfitTriggerPrice float64
	var stopLossPrice, takeProfitPrice float64
	if position.Qty > 0 {
		stopLossTriggerPrice = ma * (1 - priceRatio)
		takeProfitTriggerPrice = ma * (1 + priceRatio)
		stopLossPrice = stopLossTriggerPrice * (1 - ParamStoplossSlippage)
		takeProfitPrice = takeProfitTriggerPrice * (1 - ParamStoplossSlippage)
	} else {
		stopLossTriggerPrice = ma * (1 + priceRatio)
		takeProfitTriggerPrice = ma * (1 - priceRatio)
		stopLossPrice = stopLossTriggerPrice * (1 + ParamStoplossSlippage)
		takeProfitPrice = takeProfitTriggerPrice * (1 + ParamStoplossSlippage)
	}

	args := exchange.CreateOrderArgs{
		InstrumentType: position.InstrumentType,
		Symbol:         position.Symbol,
		Type:           exchange.StopLimit,
		TradeMode:      exchange.TradeModeCross,
		Qty:            math.Abs(position.Qty),
		TimeInForce:    exchange.GTC,
		ClosePosition:  true,
		ReduceOnly:     true,
	}

	if position.Qty > 0 {
		args.Side = exchange.OrderSideSell
	} else {
		args.Side = exchange.OrderSideBuy
	}
	tpArgs = &exchange.CreateOrderArgs{}
	copier.Copy(tpArgs, &args)
	slArgs = &exchange.CreateOrderArgs{}
	copier.Copy(slArgs, &args)

	slArgs.TriggerPrice = stopLossTriggerPrice
	slArgs.Price = stopLossPrice
	if position.Qty > 0 {
		slArgs.TriggerDirection = exchange.TriggerDirectionLower
	} else {
		slArgs.TriggerDirection = exchange.TriggerDirectionHigher
	}

	tpArgs.TriggerPrice = takeProfitTriggerPrice
	tpArgs.Price = takeProfitPrice
	if position.Qty > 0 {
		tpArgs.TriggerDirection = exchange.TriggerDirectionHigher
	} else {
		tpArgs.TriggerDirection = exchange.TriggerDirectionLower
	}

	return
}

func (this *CrossExchangeArbitrager) updateStoplossOrder(slArgs, tpArgs *exchange.CreateOrderArgs, isRight bool) {
	var code *exchange.SymbolCode
	var err error
	if isRight {
		code, err = this.GetSymbolCodeRight(slArgs.Symbol)
		if err != nil {
			this.Errorf("update stoploss order error, get symbol code failed: %s", err)
			return
		}
	} else {
		code, err = this.GetSymbolCodeLeft(slArgs.Symbol)
		if err != nil {
			this.Errorf("update stoploss order error, get symbol code failed: %s", err)
			return
		}
	}
	lock, _ := this.SymbolLocks.LoadOrStore(code.String(), &SymbolLock{
		SymbolCode: code.String(),
	})
	if isRight {
		if !lock.UpdatingStoplossRight.TryLock() {
			return
		}
		defer lock.UpdatingStoplossRight.Unlock()
	} else {
		if !lock.UpdatingStoplossLeft.TryLock() {
			return
		}
		defer lock.UpdatingStoplossLeft.Unlock()
	}

	this.Debugf("updating stoploss order for symbol: %s, isRight: %v, delay: %v", code.String(), isRight, lock.UpdateStoplossDelay)

	if lock.UpdateStoplossDelay > 0 {
		time.Sleep(lock.UpdateStoplossDelay)
	} else {
		time.Sleep(time.Duration(rand.Intn(10000)) * time.Millisecond)
	}

	ex := this.ExchangeLeft
	if isRight {
		ex = this.ExchangeRight
	}

	// 创建止损单
	stopLossOrder, err := ex.CreateOrder(*slArgs)
	if err != nil {
		this.Errorf("create stop loss order failed, error: %s", err)
		return
	}

	// 创建止盈单
	takeProfitOrder, err := ex.CreateOrder(*tpArgs)
	if err != nil {
		this.Errorf("create take profit order failed, error: %s", err)
		return
	}

	if isRight {
		this.StopLossOrdersRight.Store(slArgs.Symbol, stopLossOrder)
		this.TakeProfitOrdersRight.Store(slArgs.Symbol, takeProfitOrder)
	} else {
		this.StopLossOrdersLeft.Store(slArgs.Symbol, stopLossOrder)
		this.TakeProfitOrdersLeft.Store(slArgs.Symbol, takeProfitOrder)
	}

	// 取消新订单除外的所有平仓单
	openOrders, err := ex.GetOpenOrders(slArgs.InstrumentType, exchange.StopLimit, slArgs.Symbol)
	if err != nil {
		this.Errorf("get open orders failed, error: %s", err)
		return
	}

	if len(openOrders) > 5 {
		// 理论上不会超过 4 个
		this.AlertMsgf("open orders count is too large, symbol: %s, count: %d, isRight: %v", slArgs.Symbol, len(openOrders), isRight)
	}

	for _, order := range openOrders {
		if order.OrderID != stopLossOrder.OrderID && order.OrderID != takeProfitOrder.OrderID && order.IsCloseOrder() {
			err := ex.CancelOrder(slArgs.InstrumentType, exchange.StopLimit, order.Symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel order failed, error: %s", err)
			}
		}
	}
}

// hyperliquid 可能碰到频率限制，当接近频率限制时，自动刷量增加上限
func (this *CrossExchangeArbitrager) checkRateLimit() {
	ex, isRight, err := this.parseExchange(exchange.Hyperliquid)
	if err != nil {
		this.Errorf("parse exchange failed: %s", err)
		return
	}

	// 防止同时检查 rate limit，导致重复 wash 交易金额
	if !this.mutexes.checkLimitMutex.TryLock() {
		return
	}
	defer this.mutexes.checkLimitMutex.Unlock()

	hyper := ex.(*hyperliquid.Hyperliquid)
	used, cap, err := hyper.GetRateLimit()
	if err != nil {
		this.Errorf("get rate limit failed: %s", err)
		return
	}

	left := cap - used
	if left <= int64(this.Options.WashRequestLimit) {
		this.Infof("rate limit is low, used: %d, cap: %d, left: %d", used, cap, left)
		this.Infof("wash trade to increase rate limit...")
		this.SendMsgf("%s 即将达到请求限制(%d)，准备刷量 %.f USDC 以提高请求上限", this.GetAliasOrID(), int(this.Options.WashRequestLimit), this.Options.WashRequestLimit)

		symbolLeft, symbolRight, err := this.TranslateBothSideSymbols("BTC00.U")
		if err != nil {
			this.Errorf("get both side symbols failed: %s", err)
			return
		}
		symbol := symbolLeft
		if isRight {
			symbol = symbolRight
		}
		washQty := this.Options.WashRequestLimit / 2
		err = this.Wash(symbol, 0, washQty, -1, isRight) // 每 1 USDC 恢复 1 次请求
		if err != nil {
			this.ErrorMsgf("wash trade failed: %s", err)
		} else {
			this.SendMsgf("刷量成功，交易所：%s，品种：%s，金额：%.f USDC", ex.GetName(), symbol, this.Options.WashRequestLimit)
		}
	}
}

func (this *CrossExchangeArbitrager) parseExchange(name string) (ex exchange.Exchange, isRight bool, err error) {
	// 除此之外，还支持 left，right 作为 exchange 名称参数
	lowerCaseName := strings.ToLower(name)
	aliases := map[string]string{
		"hyper": "hyperliquid",
		"hl":    "hyperliquid",
		"hype":  "hyperliquid",
		"by":    "bybit",
		"bb":    "bybit",
		"bn":    "binance",
	}
	alias, ok := aliases[lowerCaseName]
	if ok {
		lowerCaseName = alias
	}
	if strings.EqualFold(this.ExchangeLeft.GetName(), lowerCaseName) || strings.EqualFold("left", lowerCaseName) {
		return this.ExchangeLeft, false, nil
	}
	if strings.EqualFold(this.ExchangeRight.GetName(), lowerCaseName) || strings.EqualFold("right", lowerCaseName) {
		return this.ExchangeRight, true, nil
	}
	return nil, false, fmt.Errorf("exchange not found: %s", name)
}

// 处理可能因程序退出导致的未完成提币
func (this *CrossExchangeArbitrager) handleProcessingWithdraw() {
	this.mutexes.rebalanceMutex.Lock()
	defer this.mutexes.rebalanceMutex.Unlock()

	if !this.LastWithdrawProcessing() {
		return
	}

	withdraw := this.Withdraws[len(this.Withdraws)-1]
	msg := ""
	withdrawStr := fmt.Sprintf("未完成提币[%s => %s, %.2f]", withdraw.FromExchange, withdraw.ToExchange, withdraw.Amount)
	defer func() {
		if withdraw.Status == WithdrawStatusSuccess {
			this.SendMsgf("%s处理成功，%s", withdrawStr, msg)
		} else {
			withdraw.Status = WithdrawStatusFailed
			this.ErrorMsgf("%s处理完成，%s", withdrawStr, msg)
		}
	}()

	this.SendMsgf("检测到%s，开始处理...", withdrawStr)

	fromExchange, _, err := this.parseExchange(withdraw.FromExchange)
	if err != nil {
		msg = fmt.Sprintf("解析交易所失败: %s", err)
		return
	}

	toExchange, _, err := this.parseExchange(withdraw.ToExchange)
	if err != nil {
		msg = fmt.Sprintf("解析交易所失败: %s", err)
		return
	}

	if withdraw.Status == WithdrawStatusNew {
		// 可能还在主账号，则转到子账号
		_, available, err := fromExchange.GetWithdrawAccountBalance(withdraw.Coin)
		if err != nil {
			msg = fmt.Sprintf("获取充值账号余额失败: %s", err)
			return
		} else if available >= withdraw.Amount {
			withdraw.Status = WithdrawStatusInner

			err = fromExchange.SubaccountTransfer(withdraw.Coin, withdraw.Amount, true)
			if err != nil {
				msg = fmt.Sprintf("子账号转账失败: %s", err)
				return
			}

			time.Sleep(InterTransferWait)
		}

		if fromExchange.GetName() == exchange.Bybit {
			withdrawFee, err := fromExchange.(*bybit.Bybit).GetWithdrawFee(withdraw.Coin, withdraw.Chain)
			if err != nil {
				msg = fmt.Sprintf("获取提币手续费失败: %s", err)
				return
			}

			if err := fromExchange.(*bybit.Bybit).InterTransfer(withdraw.Coin, withdraw.Amount+withdrawFee, bybit.AccountTypeFund, bybit.AccountTypeUnified); err != nil {
				msg = fmt.Sprintf("资金账号转回统一账号失败: %s", err)
			} else {
				msg = "资金已转回统一账号"
			}

			return
		} else if fromExchange.GetName() == exchange.Binance {
			withdrawFee, err := fromExchange.(*binance.Binance).GetWithdrawFee(withdraw.Coin, withdraw.Chain)
			if err != nil {
				msg = fmt.Sprintf("获取提币手续费失败: %s", err)
				return
			}

			if err := fromExchange.TransferAsset(exchange.Spot, exchange.USDXMarginedFutures, withdraw.Coin, withdraw.Amount+withdrawFee); err != nil {
				msg = fmt.Sprintf("现货账号转回合约账号失败: %s", err)
			} else {
				msg = "资金已转回合约账号"
			}

			return
		}
	}

	if withdraw.Status == WithdrawStatusPending || withdraw.Status == WithdrawStatusInner {
		if toExchange.GetName() == exchange.Bybit {
			_, available, err := toExchange.GetWithdrawAccountBalance(withdraw.Coin)
			if err != nil {
				msg = fmt.Sprintf("获取充值账号余额失败: %s", err)
				return
			} else if available >= withdraw.Amount {
				withdraw.Status = WithdrawStatusInner

				err = toExchange.SubaccountTransfer(withdraw.Coin, withdraw.Amount, true)
				if err != nil {
					msg = fmt.Sprintf("子账号转账失败: %s", err)
					return
				}

				time.Sleep(InterTransferWait)

				err = toExchange.(*bybit.Bybit).InterTransfer(withdraw.Coin, withdraw.Amount, bybit.AccountTypeFund, bybit.AccountTypeUnified)
				if err != nil {
					msg = fmt.Sprintf("资金账号转入统一账号失败: %s", err)
				} else {
					msg = "资金已成功转入"
					withdraw.Status = WithdrawStatusSuccess
				}
				return
			}

		} else if toExchange.GetName() == exchange.Binance {
			_, available, err := toExchange.GetWithdrawAccountBalance(withdraw.Coin)
			if err != nil {
				msg = fmt.Sprintf("获取充值账号余额失败: %s", err)
				return
			} else if available >= withdraw.Amount {
				withdraw.Status = WithdrawStatusInner

				err = toExchange.SubaccountTransfer(withdraw.Coin, withdraw.Amount, true)
				if err != nil {
					msg = fmt.Sprintf("子账号转账失败: %s", err)
					return
				}

				time.Sleep(InterTransferWait)

				err = toExchange.TransferAsset(exchange.Spot, exchange.USDXMarginedFutures, withdraw.Coin, withdraw.Amount)
				if err != nil {
					msg = fmt.Sprintf("现货账号转入合约账号失败: %s", err)
				} else {
					msg = "资金已成功转入"
					withdraw.Status = WithdrawStatusSuccess
				}
				return
			}

		} else if toExchange.GetName() == exchange.Hyperliquid {
			ex := toExchange.(*hyperliquid.Hyperliquid)
			balance, err := ex.GetWalletBalance(withdraw.Coin)
			if err != nil {
				msg = fmt.Sprintf("获取钱包余额失败: %s", err)
				return
			}
			if balance >= withdraw.Amount {
				// 到钱包了，转入交易账号
				withdraw.Status = WithdrawStatusBridging

				tx, err := ex.Deposit(withdraw.Amount)
				if err != nil {
					msg = fmt.Sprintf("钱包转入交易账号失败: %s", err)
					return
				}

				withdraw.BridgeTxID = tx
			}
		}
	}

	if toExchange.GetName() == exchange.Hyperliquid && withdraw.Status == WithdrawStatusBridging && withdraw.BridgeTxID != "" {
		startTime := time.Now()
		for {
			time.Sleep(10 * time.Second)

			success, pending, err := toExchange.(*hyperliquid.Hyperliquid).IsTransactionSuccess(withdraw.BridgeTxID)
			if err != nil {
				msg = fmt.Sprintf("检查 Bridge 交易状态失败: %s", err)
				withdraw.FailReason = WithdrawFailReasonTransferError
				return
			}

			if time.Since(startTime) > getRebalanceTimeout(withdraw.ToExchange, withdraw.Chain) {
				msg = "Bridge 交易超时"
				withdraw.FailReason = WithdrawFailReasonTimeout
				return
			}

			if pending {
				continue
			}

			if !success {
				msg = "Bridge 交易失败"
				withdraw.FailReason = WithdrawFailReasonTransferError
				return
			}

			err = toExchange.SubaccountTransfer(withdraw.Coin, withdraw.Amount, true)
			if err != nil {
				msg = fmt.Sprintf("转账失败: %s", err)
				withdraw.FailReason = WithdrawFailReasonTransferError
				return
			}

			msg = "提币成功"
			withdraw.Status = WithdrawStatusSuccess

			break
		}
		return
	}

	msg = "未知提币状态，请手动检查处理"
}

// 根据实际杠杆检查 TargetMarginRatio, RebalanceMarginRatio 的设置是否合理
// 仅检查 hyperliquid 的保证金率，因为通常 hyperliquid 的保证金率最低
// 每 5 分钟检查一次 margin ratio 选项，降低检查和报警频率
func (this *CrossExchangeArbitrager) checkMarginRatioOption(force bool) {
	if !EnableCheckMarginRatioOption {
		return
	}

	if force || this.counters.checkMarginRatioLimiter.Allow() {
		maintMarginRatio, marginRatio, suggestedReblanceMarginRatio, suggestedTargetMarginRatio, er := this.getMinMarginRatio()
		if er != nil {
			this.Errorf("checking margin ratio option, get min margin ratio failed: %s", er)
			return
		}

		if this.Options.RebalanceMarginRatio < suggestedReblanceMarginRatio {
			this.AlertMsgf("RebalanceMarginRatio %.2f%% 设置过低，Hyperliquid 当前需要维持保证金比例为 %.2f%%，建议设置为 %.2f%% 以上", this.Options.RebalanceMarginRatio*100, maintMarginRatio*100, suggestedReblanceMarginRatio*100)
		}
		if this.Options.TargetMarginRatio < suggestedTargetMarginRatio {
			this.AlertMsgf("TargetMarginRatio %.2f%% 设置过低，Hyperliquid 当前需要保证金比例为 %.2f%%，建议设置为 %.2f%% 以上", this.Options.TargetMarginRatio*100, marginRatio*100, suggestedTargetMarginRatio*100)
		}
	}
}

// 仅检查 hyperliquid 的保证金率，因为通常 hyperliquid 的保证金率最低
func (this *CrossExchangeArbitrager) getMinMarginRatio() (maintMarginRatio, marginRatio, suggestedReblanceMarginRatio, suggestedTargetMarginRatio float64, er error) {
	if this.ExchangeLeft == nil || this.ExchangeRight == nil {
		er = fmt.Errorf("exchange not found")
		return
	}
	if this.AccountSnapshot == nil {
		er = fmt.Errorf("account snapshot not found")
		return
	}
	if time.Since(this.AccountSnapshot.CreateTime) > LoopInterval {
		er = fmt.Errorf("account snapshot is too old")
		return
	}

	if this.AccountSnapshot.PositionValueLeft == 0 || this.AccountSnapshot.PositionValueRight == 0 {
		er = fmt.Errorf("position value is 0")
		return
	}

	if time.Now().Before(this.MonitorRelaxAtTime) {
		this.Infof("monitor is in relax mode, skip this time. MonitorRelaxAtTime=%s", this.MonitorRelaxAtTime.Format(time.RFC3339))
		er = fmt.Errorf("monitor is in relax mode")
		return
	}

	_, isRight, er := this.parseExchange(exchange.Hyperliquid)
	if er != nil {
		this.Errorf("parse exchange failed: %s", er)
		er = fmt.Errorf("parse exchange failed: %s", er)
		return
	}

	rebalanceMultiplier := 1.2
	targetMultiplier := 1.0

	marginRatio = 1 / this.AccountSnapshot.PositionLeverageLeft
	if isRight {
		marginRatio = 1 / this.AccountSnapshot.PositionLeverageRight
	}
	// 因为维持保证金一般是 50%，所以资金不足 leftMarginRatio * 0.5 时就会爆仓
	maintMarginRatio = marginRatio * 0.5

	suggestedReblanceMarginRatio = maintMarginRatio * rebalanceMultiplier
	suggestedTargetMarginRatio = marginRatio * targetMultiplier

	return
}

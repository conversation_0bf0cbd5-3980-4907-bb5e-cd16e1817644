package cross_exchange_arbitrage

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/jinzhu/copier"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/common/rate"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type RiskCategory string

const (
	NoRiskCategory                          RiskCategory = ""
	RiskCateogryRebalanceMarginRatioReached RiskCategory = "RebalanceMarginRatioReached"
	RiskCategoryMarginRatioFastChange       RiskCategory = "MarginRatioFastChange" // 单个交易所保证金率快速变化，可能因为整体行情出现较大波动
	RiskCategoryMarginValueIsZero           RiskCategory = "MarginValueIsZero"     // 单个交易所保证金价值接近 0，可能已经爆仓
	RiskCategoryTotalMarginRatioLow         RiskCategory = "TotalMarginRatioLow"   // 总保证金率持续较低，可能因为转账出现延迟或失败导致；也有可能因为某个交易所爆仓导致；也有可能因为开仓头寸太大；也有可能因为币价正常上涨导致仓位增加
	RiskCategoryMarginRatioLow              RiskCategory = "MarginRatioLow"        // 单个交易所 marginRatio 持续较低，可能因为转账出现延迟或失败导致
	RiskCategoryPriceVolatility             RiskCategory = "PriceVolatility"       // 单个品种价格波动超过一定限度，比如 20%
	RiskCategoryPriceDiff                   RiskCategory = "PriceDiff"             // 两个交易所同一个品种的价格差超过一定限度，比如 0.5%
	RiskCategoryMarkPriceDiff               RiskCategory = "MarkPriceDiff"         // 某个品种的合约标记价格价差超过一定限度，比如 0.5%
	RiskCategoryAlienPosition               RiskCategory = "AlienPosition"         // 发现非 U 本位合约
)

type CrossExchangeArbitragerStatus string

const (
	StatusRunning CrossExchangeArbitragerStatus = "running"
	StatusPaused  CrossExchangeArbitragerStatus = "paused"
	StatusStopped CrossExchangeArbitragerStatus = "stopped"
)

type WithdrawStatus string

const (
	WithdrawStatusNew      WithdrawStatus = "new"
	WithdrawStatusBridging WithdrawStatus = "bridging" // 从交易所转到钱包中（或者相反），仅对 dex 有这个状态
	WithdrawStatusPending  WithdrawStatus = "pending"  // 链上转账中
	WithdrawStatusInner    WithdrawStatus = "inner"    // 交易所内部转账中
	WithdrawStatusSuccess  WithdrawStatus = "success"
	WithdrawStatusFailed   WithdrawStatus = "failed"
)

type WithdrawFailReason string

const (
	WithdrawFailReasonUnknown                 WithdrawFailReason = ""
	WithdrawFailReasonTimeout                 WithdrawFailReason = "timeout"
	WithdrawFailReasonAPIError                WithdrawFailReason = "api_error"
	WithdrawFailReasonTransferError           WithdrawFailReason = "transfer_error"
	WithdrawFailReasonSubaccountTransferError WithdrawFailReason = "subaccount_transfer_error"
)

type PriceType string

const (
	PriceTypeLastPrice  PriceType = "LastPrice"
	PriceTypeMarkPrice  PriceType = "MarkPrice"
	PriceTypeIndexPrice PriceType = "IndexPrice"
)

type SymbolContext struct {
	LastPrice       float64
	MarkPrice       float64
	IndexPrice      float64
	FundingRate     float64
	OpenInterest    float64
	StopLossPrice   float64
	TakeProfitPrice float64
	PositionValue   float64 // 仓位价值
	CreateTime      time.Time
}

func NewSymbolContext() *SymbolContext {
	return &SymbolContext{
		CreateTime: time.Now(),
	}
}

func (this *SymbolContext) IsUpToDate() bool {
	return this.CreateTime.Add(10 * time.Second).After(time.Now())
}

// 提币记录
type Withdraw struct {
	ID               string
	FromControllerID string
	ToControllerID   string
	FromExchange     string
	ToExchange       string
	Coin             string
	Address          string
	Chain            string
	TxID             string  // 交易所提币的 txid，不是链上交易，注意不是所有交易所都有值，如 hyperliquid
	BridgeTxID       string  // 跨链桥的交易 txid，主要是对 dex 有效
	ChainTxID        string  // 链上交易的 txid
	Amount           float64 // 不含手续费
	Fee              float64
	Status           WithdrawStatus
	FailReason       WithdrawFailReason
	CreateTime       time.Time
	UpdateTime       time.Time
}

func (this *Withdraw) IsProcessing() bool {
	return this.Status != WithdrawStatusSuccess && this.Status != WithdrawStatusFailed
}

type Withdraws []*Withdraw

// 资产和仓位快照，一小时记录一次
type AccountSnapshot struct {
	ID                    string
	ExchangeNameLeft      string
	ExchangeNameRight     string
	PositionValueLeft     float64
	PositionValueRight    float64
	PositionLeverageLeft  float64
	PositionLeverageRight float64
	MarginValueLeft       float64
	MarginValueRight      float64
	MarginAvailableLeft   float64
	MarginAvailableRight  float64
	MarginRatioLeft       float64
	MarginRatioRight      float64
	MarginValueTotal      float64
	PositionValueTotal    float64
	MarginRatioTotal      float64
	HedgePNLs             *exchange.SyncMapOf[string, float64] // symbolCode => hedge pnl, 对冲 PNL 是正数表示以当前价格平仓盈利，负数表示亏损
	SymbolContextLeft     *exchange.SyncMapOf[string, *SymbolContext]
	SymbolContextRight    *exchange.SyncMapOf[string, *SymbolContext]
	CreateTime            time.Time
}

func NewAccountSnapshot(exchangeNameLeft, exchangeNameRight string) *AccountSnapshot {
	return &AccountSnapshot{
		ID:                 exchange.NewRandomID(),
		ExchangeNameLeft:   exchangeNameLeft,
		ExchangeNameRight:  exchangeNameRight,
		SymbolContextLeft:  exchange.NewSyncMapOf[string, *SymbolContext](),
		SymbolContextRight: exchange.NewSyncMapOf[string, *SymbolContext](),
		HedgePNLs:          exchange.NewSyncMapOf[string, float64](),
	}
}

func (this *AccountSnapshot) IsEmpty() bool {
	return this.CreateTime.IsZero()
}

// 根据打折后的保证金计算再平衡金额
// amount > 0, 表示从 left 划转到 right
// amount < 0, 表示从 right 划转到 left
// 因为 hyperliquid 的爆仓价是理论爆仓价的 50%，也就是说保证金利用率是 50%，需要给 hyperliquid 实际的保证金打折
func (this *AccountSnapshot) GetRebalanceAmount() float64 {
	// 总折扣率，用于计算 left 的保证金目标占比
	// 例如：如果 left 是 bybit, right 是 hyperliquid，则 discountTotal = 1 + 0.5 = 1.5
	// left 的保证金目标占比 = 0.5 / 1.5 = 0.33 (33%)，也就是总保证金中应该有 33% 是分配给 left 的
	// 推导：
	// a 是 left 的 discountRatio，b 是 right 的 discountRatio
	// a * x = b * y
	// x + y = z
	// x + a / b * y = z
	// b / b * x + a / b * y = z
	// x * (a + b) / b = z
	// x = b / (a + b) * z
	discountTotal := this.GetDiscountRatioLeft() + this.GetDiscountRatioRight()
	totalMarginValue := this.MarginValueLeft + this.MarginValueRight // 总保证金价值
	shareLeft := this.GetDiscountRatioRight() / discountTotal        // 平衡后，左边交易所的保证金价值占总保证金价值的比例
	return this.MarginValueLeft - totalMarginValue*shareLeft         // 左边交易所的保证金价值 - 平衡后左边交易所的保证金价值，等于需要平衡的资金
}

// 因为 hyperliquid 的爆仓价是理论爆仓价的 50%，也就是说保证金利用率是 50%，需要给 hyperliquid 实际的保证金打折
func (this *AccountSnapshot) GetDiscountedMarginValueLeft() float64 {
	return this.MarginValueLeft * this.GetDiscountRatioLeft()
}

func (this *AccountSnapshot) GetDiscountRatioLeft() float64 {
	if this.ExchangeNameLeft == exchange.Hyperliquid {
		return 0.5
	}
	return 1.0
}

func (this *AccountSnapshot) GetDiscountRatioRight() float64 {
	if this.ExchangeNameRight == exchange.Hyperliquid {
		return 0.5
	}
	return 1.0
}

// 因为 hyperliquid 的爆仓价是理论爆仓价的 50%，也就是说保证金利用率是 50%，需要给 hyperliquid 实际的保证金打折
func (this *AccountSnapshot) GetDiscountedMarginValueRight() float64 {
	return this.MarginValueRight * this.GetDiscountRatioRight()
}

func (this *AccountSnapshot) GetDiscountedMarginRatioLeft() float64 {
	return this.GetDiscountedMarginValueLeft() / this.PositionValueLeft
}

func (this *AccountSnapshot) GetDiscountedMarginRatioRight() float64 {
	return this.GetDiscountedMarginValueRight() / this.PositionValueRight
}

func (this *AccountSnapshot) GetDiscountedMarginValueTotal() float64 {
	return this.GetDiscountedMarginValueLeft() + this.GetDiscountedMarginValueRight()
}

func (this *AccountSnapshot) GetDiscountedMarginRatioTotal() float64 {
	return this.GetDiscountedMarginValueTotal() / this.PositionValueTotal
}

// 获取当前持仓的对冲 PNL，如果 symbolCode 为空，则返回所有对冲 PNL 的和
func (this *AccountSnapshot) GetHedgePNL(symbolCode string) float64 {
	pnl := 0.0
	if symbolCode == "" {
		this.HedgePNLs.Range(func(key string, value float64) bool {
			pnl += value
			return true
		})
	} else {
		pnl, _ = this.HedgePNLs.Load(symbolCode)
	}
	return pnl
}

type PositionSnapshot struct {
	ID             string // 关联的 AccountSnapshot 的 ID，用于查询同一个时刻的仓位快照
	PositionsLeft  []*exchange.Position
	PositionsRight []*exchange.Position
	CreateTime     time.Time
}

type PositionSnapshots []*PositionSnapshot

func (this *PositionSnapshot) HasPositions() bool {
	return len(this.PositionsLeft) > 0 || len(this.PositionsRight) > 0
}

type RiskEvent struct {
	ID         string
	Category   RiskCategory
	Comment    string
	CreateTime time.Time
}

type AccountSnapshots []*AccountSnapshot

type LockMutex struct {
	sync.Mutex
}

func (this *LockMutex) IsLocked() bool {
	locked := this.TryLock()
	if locked {
		this.Unlock()
	}
	return !locked
}

type SymbolLock struct {
	SymbolCode            string
	UpdatingStoplossLeft  LockMutex
	UpdatingStoplossRight LockMutex
	UpdateStoplossDelay   time.Duration
	SyncingPosition       LockMutex
	ClosingPosition       LockMutex
	ReducingPosition      LockMutex
	AddingPosition        LockMutex
	RatatingPosition      LockMutex
	AtCapUntil            *time.Time
}

func (this *SymbolLock) String() string {
	updatingStoplossLeft := this.UpdatingStoplossLeft.IsLocked()
	updatingStoplossRight := this.UpdatingStoplossRight.IsLocked()
	syncingPosition := this.SyncingPosition.IsLocked()
	closingPosition := this.ClosingPosition.IsLocked()
	reducingPosition := this.ReducingPosition.IsLocked()
	addingPosition := this.AddingPosition.IsLocked()
	ratatingPosition := this.RatatingPosition.IsLocked()
	return fmt.Sprintf("symbol code locks: %s, updatingStoplossLeft: %t, updatingStoplossRight: %t, syncingPosition: %t, closingPosition: %t, reducingPosition: %t, addingPosition: %t, ratatingPosition: %t", this.SymbolCode, updatingStoplossLeft, updatingStoplossRight, syncingPosition, closingPosition, reducingPosition, addingPosition, ratatingPosition)
}

// 没有 rotate 的情况下都允许 sync 仓位
// rotate 本来就只希望在单侧执行，不希望 sync 仓位变动
func (this *SymbolLock) AllowSync() bool {
	ratating := this.RatatingPosition.IsLocked()
	return !ratating
}

// 没有加仓减仓和平仓的情况下都允许 rotate 仓位
// 在加仓和减仓的过程中 rotate 可能仓位数量不准确
// 在平仓的过程中 rotate 没有必要了，因为平仓后仓位数量为 0
func (this *SymbolLock) AllowRatate() bool {
	reducing := this.ReducingPosition.IsLocked()
	closing := this.ClosingPosition.IsLocked()
	adding := this.AddingPosition.IsLocked()
	return !reducing && !closing && !adding
}

func (this *SymbolLock) IsAtCapNow() bool {
	return this.AtCapUntil != nil && time.Now().Before(*this.AtCapUntil)
}

type Maintenance struct {
	ID               string
	ExchangeName     string
	EffectiveTime    time.Time
	EffectiveSeconds int
	FinishTime       *time.Time
	Message          string
	Comment          string
	CreateTime       time.Time
	UpdateTime       time.Time
}

func (this *Maintenance) IsActive() bool {
	if this.FinishTime != nil {
		return false
	}
	// 维护开始前 1 分钟认为维护开始
	return time.Now().After(this.EffectiveTime.Add(-1*time.Minute)) && this.EndTime().After(time.Now())
}

func (this *Maintenance) Prolong(seconds int) {
	this.EffectiveTime = this.EffectiveTime.Add(time.Duration(seconds) * time.Second)
}

func (this *Maintenance) Finish(reason string) {
	this.FinishTime = utils.Ptr(time.Now())
	this.Comment = reason
}

func (this *Maintenance) EndTime() *time.Time {
	return utils.Ptr(this.EffectiveTime.Add(time.Duration(this.EffectiveSeconds) * time.Second))
}

func (this *Maintenance) IsFinished() bool {
	return this.FinishTime != nil
}

func (this *Maintenance) String() string {
	return fmt.Sprintf("[MAINTANANCE] exchange: %s, time: %s ~ %s, message: %s, comment: %s", this.ExchangeName, utils.FormatShortTimeStr(&this.EffectiveTime, false), utils.FormatShortTimeStr(this.EndTime(), false), this.Message, this.Comment)
}

func (this *Maintenance) Row() []string {
	timeSpan := fmt.Sprintf("%s ~ %s", utils.FormatShortTimeStr(&this.EffectiveTime, false), utils.FormatShortTimeStr(this.EndTime(), false))
	row := []string{}
	row = append(row, this.ID)
	row = append(row, this.ExchangeName)
	row = append(row, timeSpan)
	activeStr := ""
	if this.IsActive() {
		activeStr = "Yes"
	}
	row = append(row, activeStr)
	row = append(row, utils.FormatShortTimeStr(this.FinishTime, false))
	row = append(row, this.Message)
	row = append(row, this.Comment)
	row = append(row, utils.FormatShortTimeStr(&this.UpdateTime, false))
	return row
}

func (this *Maintenance) ToTable() string {
	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Exchange", "Time", "Active", "FinishTime", "Message", "Comment", "UpdateTime"})
	t.AddRow(this.Row())
	return t.Render()
}

type Maintenances []*Maintenance

func (this Maintenances) HasActiveMaintainance() bool {
	for _, maint := range this {
		if maint.IsActive() {
			return true
		}
	}
	return false
}

func (this *Maintenances) Set(exchangeName string, startTime time.Time, minutes int, message string, comment string) (maintenance *Maintenance, updated bool) {
	for i := len(*this) - 1; i >= 0; i-- {
		maint := (*this)[i]
		if maint.ExchangeName == exchangeName && !maint.IsFinished() {
			// 如果 minutes 为 0，则表示删除维护
			if minutes == 0 {
				maint.Finish("manually finished")
			} else { // 如果存在维护，则更新维护
				maint.EffectiveTime = startTime
				maint.EffectiveSeconds = minutes * 60
				maint.Message = message
				maint.Comment = comment
				maint.UpdateTime = time.Now()
			}
			maintenance = maint
			updated = true
			return
		}
	}
	// 如果不存在当前维护，则添加维护
	nowTime := time.Now()
	maintenance = &Maintenance{
		ID:               exchange.NewRandomID(),
		ExchangeName:     exchangeName,
		CreateTime:       nowTime,
		EffectiveTime:    startTime,
		EffectiveSeconds: minutes * 60,
		Message:          message,
		Comment:          comment,
		UpdateTime:       nowTime,
	}
	*this = append(*this, maintenance)
	return
}

func (this Maintenances) ToTable() string {
	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Exchange", "Time Span", "Active", "FinishTime", "Message", "Comment", "UpdateTime"})
	for i := len(this) - 1; i >= 0; i-- {
		maint := this[i]
		t.AddRow(maint.Row())
	}
	if len(t.Rows) > 1 {
		return t.Render()
	}
	return "[No Maintenances]"
}

func (this Maintenances) AutoFinish() (finished Maintenances) {
	for _, maint := range this {
		if maint.IsActive() && maint.EndTime().Before(time.Now()) {
			maint.Finish("auto finished")
			finished = append(finished, maint)
		}
	}
	return
}

func (this Maintenances) Copy() Maintenances {
	copied := make(Maintenances, len(this))
	copier.Copy(&copied, &this)
	return copied
}

type Counters struct {
	syncPositionErrorCounter atomic.Int64         // 同步仓位出错次数
	pauseLimiter             *rate.CounterLimiter // 暂停检查次数
	checkMarginRatioLimiter  *rate.CounterLimiter // 检查保证金比例的次数，用于防止频繁检查；不需要初始化可以直接使用
	truncateSnapshotLimiter  *rate.CounterLimiter // 清理历史快照的次数
}

func NewCounters() *Counters {
	return &Counters{
		checkMarginRatioLimiter: rate.NewCounterLimiter(SlowMod),
		pauseLimiter:            rate.NewCounterLimiter(HourlySlowMod),
		truncateSnapshotLimiter: rate.NewCounterLimiter(DailySlowMod),
	}
}

type Mutexes struct {
	monitorsMutex             sync.Mutex
	riskMutex                 sync.Mutex
	rebalanceMutex            sync.Mutex
	syncPositionMutex         sync.Mutex
	reduceMutex               sync.Mutex
	handleStoplossOrdersMutex sync.Mutex
	checkLimitMutex           sync.Mutex
}

type SymbolPriceDiff struct {
	Exchange    string
	SymbolCode  string
	PriceLeft   float64
	PriceRight  float64
	RealRatio   float64
	TargetRatio float64
	CreateTime  time.Time
}

type CrossExchangeArbitrager struct {
	ID                  string
	controller          base.OrderControllable `json:"-"`
	Alias               string
	DepositCoin         string
	ControllerIDLeft    string
	ControllerIDRight   string
	ControllerLeft      base.Controllable `json:"-"`
	ControllerRight     base.Controllable `json:"-"`
	ExchangeLeft        exchange.Exchange `json:"-"`
	ExchangeRight       exchange.Exchange `json:"-"`
	DepositAddressLeft  string
	DepositAddressRight string

	StopLossOrdersLeft           *exchange.SyncMapOf[string, *exchange.Order] // symbol => order
	TakeProfitOrdersLeft         *exchange.SyncMapOf[string, *exchange.Order]
	StopLossOrdersRight          *exchange.SyncMapOf[string, *exchange.Order]
	TakeProfitOrdersRight        *exchange.SyncMapOf[string, *exchange.Order]
	StoplossOrdersLastHandleTime time.Time

	SymbolParams *exchange.SyncMapOf[string, *option.Options] // symbolCode => risk params

	Options *Options

	Maintenances     Maintenances // 交易所维护
	AccountSnapshot  *AccountSnapshot
	PositionSnapshot *PositionSnapshot

	Status           CrossExchangeArbitragerStatus
	StatusUpdateTime time.Time
	CreateTime       time.Time
	LastUseTime      time.Time
	DeleteTime       *time.Time

	MonitorRelaxAtTime time.Time

	RiskEvents       []*RiskEvent
	Withdraws        Withdraws
	Monitors         AccountSnapshots
	PositionMonitors PositionSnapshots
	AccountSnapshots *exchange.SyncMapOf[int64, *AccountSnapshot]
	FundingRates     []*exchange.FundingHistory
	PositionHistory  *exchange.SyncMapOf[int64, *PositionSnapshot]
	SymbolLocks      *exchange.SyncMapOf[string, *SymbolLock] `json:"-"` // symbol code string => symbol lock

	simulatingRisk             RiskCategory
	lastAutoRebalanceTime      *time.Time
	counters                   *Counters
	mutexes                    Mutexes
	priceDiffAlertTime         *exchange.SyncMapOf[string, time.Time]
	autoTakeProfitCoolingUntil time.Time
}

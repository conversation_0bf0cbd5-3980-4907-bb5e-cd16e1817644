package cross_exchange_arbitrage

import (
	"fmt"
	"strings"
	"time"

	"github.com/mattn/go-runewidth"
	"github.com/spf13/cast"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/hyperliquid"
	"github.com/wizhodl/quanter/utils"
)

const ALERT_ORDER_VALUE = 100000.0

var CrossExchangeArbitragerStatusHeader = []string{"ID", "Alias", "ExchangeLeft", "Position", "Margin", "MarginRatio", "  ", "ExchangeRight", "Position", "Margin", "MarginRatio", "  ", "TotalMargin", "HedgePNL", "Status", "UpdateTime"}

type ArbitrageBaseCommand struct {
	command.Command
	controller base.OrderControllable
	arbitrager *CrossExchangeArbitrager
}

type CrossExchangeArbitrageCommand struct {
	ArbitrageBaseCommand
}

func (this *ArbitrageBaseCommand) CheckCurrentArbitrager() bool {
	subcommand := this.GetSubcommand()
	if utils.SliceContainsEqualFold([]string{"statusall", "new", "remove", "use", "alias"}, subcommand) {
		return true
	}
	if this.arbitrager == nil {
		this.ErrorMsgf("当前没有设置跨交易所套利机，请先用 `.cea use [ID/Alias]` 设置")
		return false
	}
	return true
}

func (this *ArbitrageBaseCommand) PrePrepare() bool {
	if this.RequiresConfirm {
		if !this.CheckCurrentArbitrager() {
			return false
		}
		if this.arbitrager != nil {
			this.SendMsgf("当前跨交易所套利机: %s", this.arbitrager.GetAliasOrID())
		}
		return true
	}
	return true
}

func (this *ArbitrageBaseCommand) PreDo() bool {
	if !this.RequiresConfirm {
		if !this.CheckCurrentArbitrager() {
			return false
		}
		if this.arbitrager != nil {
			this.SendMsgf("当前跨交易所套利机: %s", this.arbitrager.GetAliasOrID())
		}
		return true
	}
	return true
}

func NewCrossExchangeArbitrageCommand(controller base.OrderControllable) *CrossExchangeArbitrageCommand {
	cmd := &CrossExchangeArbitrageCommand{
		ArbitrageBaseCommand: ArbitrageBaseCommand{
			Command: command.Command{
				Name: "cea",
				Instructions: []string{
					"`.cea use [ID/Alias]`, 跨交易所套利，设置当前使用的跨交易所套利机",
					"`.cea info`, 跨交易所套利，打印当前跨交易所套利机信息",
					"`.cea statusAll`, 跨交易所套利，打印所有控制器状态",
					"`.cea status/s`, 跨交易所套利，打印当前控制器状态",
					"`.cea report/r`, 跨交易所套利，打印控制器报告",
					"`.cea holdings/h`, 跨交易所套利，打印持仓",
					"`.cea new ID1 ID2 DepositAddress1 DepositAddress2 DepositCoin [TargetMarginRatio=xxx,RebalanceMarginRatio=xxx] ?`, 跨交易所套利，添加控制器",
					"`.cea edit ID1 ID2 DepositAddress1 DepositAddress2 DepositCoin ?`, 跨交易所套利，编辑控制器",
					"`.cea remove ?`, 跨交易所套利，移除控制器",
					"`.cea stop AuthCode`, 跨交易所套利，停止控制器运行，不自动平仓",
					"`.cea pause AuthCode`, 跨交易所套利，暂停控制器运行，不自动平仓",
					"`.cea resume AuthCode`, 跨交易所套利，恢复控制器运行",
					"`.cea long SymbolCode qty/xxx.u [splits] ?`, 跨交易所套利，开仓做多某个品种",
					"`.cea short SymbolCode qty/xxx.u [splits] ?`, 跨交易所套利，开仓做空某个品种",
					"`.cea reduceAll ratio ?`, 跨交易所套利，减仓所有持仓的百分比",
					"`.cea reduce ratio SymbolCode1,SymbolCode2... ?`, 跨交易所套利，减仓某些品种的持仓",
					"`.cea close SymbolCode1,SymbolCode2... [splits] ?`, 跨交易所套利，平仓某些品种",
					"`.cea closeAll [splits] ?`, 跨交易所套利，平仓所有品种",
					"`.cea wash exchangeName/left/right SymbolCode qty/xxx.u [splits] ?`, 跨交易所套利，刷量交易某个品种",
					"`.cea rotate exchangeName/left/right SymbolCode percent [splits] ?`, 跨交易所套利，平、开仓释放保证金",
					"`.cea maintenance/maint exchangeName startTime/now endTime/minutes ?`, 跨交易所套利，添加维护，0 minutes 表示删除",
					"`.cea maintenances/maints`, 跨交易所套利，打印维护",
					"`.cea rebalance AuthCode`, 跨交易所套利，平衡两个交易所的保证金",
					"`.cea position/pos [ID]`, 跨交易所套利，打印持仓",
					"`.cea risks`, 跨交易所套利，打印风险事件",
					"`.cea withdraws`, 跨交易所套利，打印提币记录",
					"`.cea monitors`, 跨交易所套利，打印监控数据",
					"`.cea prices SymbolCode [exactTime]`, 跨交易所套利，打印监控价格数据",
					"`.cea pricediff`, 跨交易所套利，打印价格差异",
					"`.cea snapshots [daily/exactTime]`, 跨交易所套利，打印资产快照",
					"`.cea snapshot/snap [ID]`, 跨交易所套利，打印资产快照详情",
					"`.cea fundings [symbolCodes]`, 跨交易所套利，打印品种的资金费率",
					"`.cea alias ID Alias AuthCode`, 跨交易所套利，设置控制器别名",
					"`.cea set TargetMarginRatio=xxx,RebalanceMarginRatio=xxx ?`, 跨交易所套利，设置控制器配置",
					"`.cea setParams symbolCode:PriceDiff=xxx,MarkPriceDiff=xxx,Stoploss=xxx,LeverageLeft=xxx,LeverageRight=xxx|... ?`, 跨交易所套利，设置品种参数",
					"`.cea params`, 跨交易所套利，打印品种参数",
					"`.cea simulate riskCategory`, 跨交易所套利，模拟风险事件（仅 Debug 模式下有效）",
					"`.cea options`, 跨交易所套利，打印控制器配置",
				},
			},
			controller: controller,
		},
	}
	arbitragers := cmd.controller.ListStrategies()
	lastUseTime := time.Time{}
	for _, arbitrager := range arbitragers {
		if arbitrager.GetStrategyType() == base.StrategyTypeCrossExchangeArbitrager {
			if arbitrager.GetLastUseTime().After(lastUseTime) {
				cmd.arbitrager = arbitrager.(*CrossExchangeArbitrager)
				lastUseTime = arbitrager.GetLastUseTime()
			}
		}
	}
	return cmd
}

func (this *CrossExchangeArbitrageCommand) Use(idOrAlias string) (arbitrager *CrossExchangeArbitrager, er error) {
	strategy := this.controller.GetStrategy(base.StrategyTypeCrossExchangeArbitrager, idOrAlias)
	if strategy == nil {
		er = fmt.Errorf("arbitrager not found")
		return
	}

	arbitrager = strategy.(*CrossExchangeArbitrager)
	this.arbitrager = arbitrager
	arbitrager.LastUseTime = time.Now()
	this.controller.GetStorage().Save()
	return
}

func (this *CrossExchangeArbitrageCommand) Prepare() bool {
	subcommand := this.GetSubcommand()
	subcommand = strings.ToLower(subcommand)
	args := this.GetArgs()
	switch subcommand {
	case "new":
		arbitrager, err := this.NewCrossExchangeArbitrager(args)
		if err != nil {
			this.ErrorMsgf("创建跨交易所套利机失败: %s", err)
			return false
		}
		preview := arbitrager.PreviewCreation()
		this.SendMsgf("新增跨交易所套利：\n```%s```", preview)
	case "edit":
		if this.arbitrager.Status == StatusRunning {
			this.ErrorMsgf("跨交易所套利机 %s 正在运行，无法编辑", this.arbitrager.ID)
			return false
		}
		if this.arbitrager.IsRebalancing() {
			this.ErrorMsgf("跨交易所套利机 %s 正在 rebalancing，无法编辑", this.arbitrager.ID)
			return false
		}
		newArbitrager, err := this.NewCrossExchangeArbitrager(args)
		if err != nil {
			this.ErrorMsgf("尝试用新参数创建套利机失败: %s", err)
			return false
		}
		preview := newArbitrager.PreviewEdit(this.arbitrager)
		this.SendMsgf("编辑跨交易所套利：\n```%s```", preview)
		if this.arbitrager.PositionSnapshot.HasPositions() {
			this.SendHighlightWarningf("当前有持仓，修改可能导致严重后果")
		}
	case "remove":
		this.SendMsgf("移除跨交易所套利机：\n```%s```", this.arbitrager.RenderStatus())
	case "close":
		positions := []*exchange.Position{}
		posLeft, _, err := this.arbitrager.GetPositions()
		if err != nil {
			this.ErrorMsgf("获取持仓失败: %s", err)
			return false
		}
		uSymbol := this.arbitrager.ControllerLeft.GetBaseConfig().USDXSymbol
		_, _, futureSymbols, err := exchange.ParseSymbolsFromSymbolCodes(this.arbitrager.ExchangeLeft, args[0], uSymbol)
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}
		for _, symbol := range futureSymbols {
			for _, pos := range posLeft {
				if pos.Symbol == symbol {
					positions = append(positions, pos)
				}
			}
		}

		_, err = this.parseSplitArgs(1)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		posStr, err := exchange.RenderPositions(this.arbitrager.ExchangeLeft, positions)
		if err != nil {
			this.ErrorMsgf("打印持仓出错: %s", err)
			return false
		}
		this.SendMsgf("平仓以下仓位：\n```%s```", posStr)
	case "closeall":
		_, err := this.parseSplitArgs(0)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		posLeft, _, err := this.arbitrager.GetPositions()
		if err != nil {
			this.ErrorMsgf("获取持仓失败: %s", err)
			return false
		}
		posStr, err := exchange.RenderPositions(this.arbitrager.ExchangeLeft, posLeft)
		if err != nil {
			this.ErrorMsgf("打印持仓出错: %s", err)
			return false
		}
		this.SendMsgf("平仓以下仓位：\n```%s```", posStr)
	case "reduce":
		ratio, _, err := utils.ParseFloatOrPercentage(args[0], false, false)
		if err != nil {
			this.ErrorMsgf("解析百分比失败: %s", err)
			return false
		}
		posLeft, _, err := this.arbitrager.GetPositions()
		if err != nil {
			this.ErrorMsgf("获取持仓失败: %s", err)
			return false
		}
		_, _, _, symbolsLeft, _, err := this.arbitrager.ParseSymbolCodes(args[1])
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}
		result := this.arbitrager.RenderReducePositions(posLeft, ratio, symbolsLeft...)
		this.SendMsgf("减仓预览：\n```%s```", result)
	case "reduceall":
		ratio, _, err := utils.ParseFloatOrPercentage(args[0], false, false)
		if err != nil {
			this.ErrorMsgf("解析百分比失败: %s", err)
			return false
		}
		posLeft, _, err := this.arbitrager.GetPositions()
		if err != nil {
			this.ErrorMsgf("获取持仓失败: %s", err)
			return false
		}
		result := this.arbitrager.RenderReducePositions(posLeft, ratio)
		this.SendMsgf("减仓预览：\n```%s```", result)
	case "long", "short":
		symbolCodeStr := args[0]
		qtyStr := args[1]

		_, _, err := this.arbitrager.TranslateBothSideSymbols(symbolCodeStr)
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}
		symbolCode, orderArgs, er := this.arbitrager.GetCreateOrderArgs(subcommand, symbolCodeStr, qtyStr)
		if er != nil {
			this.ErrorMsgf("解析创建订单参数失败: %s", er)
			return false
		}
		splits, err := this.parseSplitArgs(2)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		if orderArgs.QuoteQty >= ALERT_ORDER_VALUE {
			this.SendHighlightWarningf("订单价值较大: %.0f.U, 请注意是否输入有误", orderArgs.QuoteQty)
		} else {
			ex := this.arbitrager.ExchangeLeft
			price, err := ex.GetLastPrice(exchange.USDXMarginedFutures, orderArgs.Symbol, true)
			if err != nil {
				this.ErrorMsgf("获取最新价格失败: %s", err)
				return false
			}
			value, _ := ex.Qty2Size(exchange.USDXMarginedFutures, orderArgs.Symbol, price, orderArgs.Qty)
			if value >= ALERT_ORDER_VALUE {
				this.SendHighlightWarningf("订单价值较大: %.0f, 请注意是否输入有误", value)
			}
		}

		// 检查 OI 满了无法开仓的情况
		hl, isRight, er := this.arbitrager.parseExchange(exchange.Hyperliquid)
		if er == nil {
			symbolHl, _ := this.arbitrager.ConvertSymbol(orderArgs.Symbol, isRight)
			isAtCap, err := hl.(*hyperliquid.Hyperliquid).IsAtOpenInterestCap(symbolHl)
			if err != nil {
				this.ErrorMsgf("获取 OI 限制失败: %s", err)
				return false
			}
			if isAtCap {
				this.ErrorMsgf("Hyperliquid %s OI 已满，无法开仓", symbolHl)
				return false
			}
		}

		// 检查当前费率情况，如果用当前订单方向开仓，是否会亏钱，如果会亏钱，则警告
		profitRate, leftFundingRate, rightFundingRate, err := this.arbitrager.CheckOrderProfitAgainstCurrentRate(symbolCode, orderArgs)
		if err != nil {
			this.ErrorMsgf("检查订单在当前资金费率下是否赚钱失败，error: %s", err)
			return false
		}
		if profitRate > 0 {
			this.SendMsgf("当前费率差：%.2f%%", profitRate*100)
		} else {
			this.SendHighlightWarningf("当前费率下，开仓会导致亏钱，请谨慎操作。%s: %.2f%%，%s: %.2f%%", this.arbitrager.ExchangeLeft.GetName(), leftFundingRate*100, this.arbitrager.ExchangeRight.GetName(), rightFundingRate*100)
		}

		this.checkAvailableMargin(orderArgs, false)
		this.checkAvailableMargin(orderArgs, true)

		this.SendMsgf("创建订单：\n```%s```", this.arbitrager.RenderCreateOrderArgs(symbolCode, orderArgs, splits))
	case "set":
		configStr := args[0]
		if strings.Contains(configStr, ":") {
			this.ErrorMsgf("请不要输入 params 的配置，请输入 options 的配置")
			return false
		}
		oldOpts := this.arbitrager.GetOptions()
		newOpts := oldOpts.Copy()
		updatedNames, err := newOpts.UpdateFromString(configStr)
		if err != nil {
			this.ErrorMsgf("解析选项错误: %s", err)
			return false
		}
		if err := this.arbitrager.ValidateOptions(newOpts); err != nil {
			this.ErrorMsgf("选项验证失败: %s", err)
			return false
		}
		this.SendMsgf("更新配置项：\n```%s```", oldOpts.DiffToTable(newOpts, updatedNames))
		return true
	case "setparam", "setparams":
		paramStr := args[0]
		if !strings.Contains(paramStr, ":") {
			this.ErrorMsgf("请输入 symbolCode:params 的格式")
			return false
		}
		parts := strings.Split(paramStr, "|")
		symbolParams := &exchange.SyncMapOf[string, *option.Options]{}
		for _, symbolCodeStr := range parts {
			subParts := strings.Split(symbolCodeStr, ":")
			symbolCode := subParts[0]
			symbolCodes, _, _, err := exchange.ParseSymbolsFromSymbolCodes(this.arbitrager.ExchangeLeft, symbolCode, this.arbitrager.ControllerLeft.GetBaseConfig().USDXSymbol)
			if err != nil {
				this.ErrorMsgf("解析品种代码失败: %s", err)
				return false
			}
			for _, symbolCode := range symbolCodes {
				params := subParts[1]
				// 如果当前已经有配置，则在现有配置上更新
				options, ok := this.arbitrager.SymbolParams.Load(symbolCode)
				if ok && options != nil && !options.IsZero() {
					optionsCopy := options.Copy()
					symbolParams.Store(symbolCode, optionsCopy)
				}
				this.arbitrager._updateParams(symbolParams, symbolCode, params)
			}
		}
		this.SendMsgf("更新参数: \n```%s```", this.arbitrager._renderParams(symbolParams))
		return true
	case "rotate":
		direction := args[0]
		if strings.ToLower(direction) != "left" && strings.ToLower(direction) != "right" {
			this.ErrorMsgf("交易所方向必须是 left 或 right")
			return false
		}

		symbolLeft, symbolRight, err := this.arbitrager.TranslateBothSideSymbols(args[1])
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}

		ratio, _, err := utils.ParseFloatOrPercentage(args[2], false, false)
		if err != nil {
			this.ErrorMsgf("解析百分比失败: %s", err)
			return false
		}

		splits, err := this.parseSplitArgs(3)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		posLeft, posRight, err := this.arbitrager.GetPositions()
		if err != nil {
			this.ErrorMsgf("获取持仓失败: %s", err)
			return false
		}

		var positions []*exchange.Position
		var position *exchange.Position
		var ex exchange.Exchange
		if strings.ToLower(direction) == "left" {
			positions = posLeft
			ex = this.arbitrager.ExchangeLeft
		} else {
			positions = posRight
			ex = this.arbitrager.ExchangeRight
		}
		for _, pos := range positions {
			if pos.Symbol == symbolLeft && strings.ToLower(direction) == "left" {
				position = pos
				break
			}
			if pos.Symbol == symbolRight && strings.ToLower(direction) == "right" {
				position = pos
				break
			}
		}
		if position == nil || position.Qty == 0 {
			this.ErrorMsgf("没有找到对应的持仓")
			return false
		}

		this.SendMsgf("平、开仓释放保证金：\n```%s```", this.arbitrager.RenderRotate(ex, position, ratio, splits))

		return true
	case "wash":
		ex, isRight, err := this.arbitrager.parseExchange(args[0])
		if err != nil {
			this.ErrorMsgf("解析交易所名称失败: %s", err)
			return false
		}

		symbolLeft, symbolRight, err := this.arbitrager.TranslateBothSideSymbols(args[1])
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}
		symbol := symbolLeft
		if isRight {
			symbol = symbolRight
		}

		qty, quoteQty, err := this.parseQty(2)
		if err != nil {
			this.ErrorMsgf("解析数量失败: %s", err)
			return false
		}
		if quoteQty >= ALERT_ORDER_VALUE {
			this.SendHighlightWarningf("刷量交易数量较大: %.0f.U, 请注意是否输入有误", quoteQty)
		} else {
			price, err := ex.GetLastPrice(exchange.USDXMarginedFutures, symbol, true)
			if err != nil {
				this.ErrorMsgf("获取最新价格失败: %s", err)
				return false
			}
			value, _ := ex.Qty2Size(exchange.USDXMarginedFutures, symbol, price, qty)
			if value >= ALERT_ORDER_VALUE {
				this.SendHighlightWarningf("刷量交易价值较大: %.0f, 请注意是否输入有误", value)
			}
		}

		_, err = this.parseSplitArgs(3)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		return true
	case "maintenance", "maint":
		maints := this.arbitrager.Maintenances.Copy()
		maintenance, updated, er := this.setMaintenance(&maints, args)
		if er != nil {
			this.ErrorMsgf("参数错误: %s", er)
			return false
		}
		if updated {
			this.SendMsgf("更新交易所维护: \n```%s```", maintenance.ToTable())
		} else {
			this.SendMsgf("添加交易所维护: \n```%s```", maintenance.ToTable())
		}
		return true
	}
	return true
}

func (this *CrossExchangeArbitrageCommand) checkAvailableMargin(orderArgs exchange.CreateOrderArgs, isRight bool) {
	// 检查两边是否有足够的保证金开仓，不够则警告
	ex := this.arbitrager.ExchangeLeft
	symbol := orderArgs.Symbol
	if isRight {
		ex = this.arbitrager.ExchangeRight
		symbol, _ = this.arbitrager.ConvertSymbol(symbol, true)
	}

	_, available, err := ex.GetBalance(orderArgs.InstrumentType, this.arbitrager.DepositCoin)
	if err != nil {
		this.ErrorMsgf("获取交易所余额失败: %s", err)
		return
	}
	leverage, err := this.arbitrager.GetLeverage(symbol, isRight)
	if err != nil {
		this.ErrorMsgf("获取杠杆倍数失败: %s", err)
		return
	}
	orderValue := orderArgs.QuoteQty
	if orderValue == 0 {
		lastPrice, err := ex.GetLastPrice(orderArgs.InstrumentType, symbol, true)
		if err != nil {
			this.ErrorMsgf("获取最新价格失败: %s", err)
			return
		}
		orderValue, _ = ex.Qty2Size(orderArgs.InstrumentType, symbol, lastPrice, orderArgs.Qty)
	}
	needMargin := orderValue / leverage
	if needMargin > available {
		this.SendHighlightWarningf("%s 保证金不足，开仓可能会失败。当前可用保证金: %.2f，需要保证金: %.2f", ex.GetName(), available, needMargin)
	}
}

func (this *CrossExchangeArbitrageCommand) SendHighlightWarningf(format string, a ...any) {
	message := fmt.Sprintf(format, a...)
	displayWidth := 0
	for _, r := range message {

		if runewidth.RuneWidth(r) == 3 {
			displayWidth += 3
		} else if runewidth.RuneWidth(r) == 2 {
			displayWidth += 2
		} else {
			displayWidth++
		}
	}
	stars := strings.Repeat("*", displayWidth+12) // +12 for the padding spaces and borders

	this.SendMsgf("\n%s\n*  %s  *\n%s\n", stars, message, stars)
}

func (this *CrossExchangeArbitrageCommand) ListCrossExchangeArbitrages() {
	var strategies []base.Strategy
	for _, arbitrager := range this.controller.ListStrategies() {
		if arbitrager.GetStrategyType() == base.StrategyTypeCrossExchangeArbitrager {
			strategies = append(strategies, arbitrager)
		}
	}

	t := exchange.NewTable()
	t.SetHeader(CrossExchangeArbitragerStatusHeader)
	for _, arbitrager := range strategies {
		t.AddRow(arbitrager.(*CrossExchangeArbitrager).RenderStatusRow())
	}
	this.SendMsgf("跨交易所套利机:\n```%s```", t.Render())
}

func (this *CrossExchangeArbitrageCommand) Do() bool {
	subcommand := this.GetSubcommand()
	subcommand = strings.ToLower(subcommand)
	args := this.GetArgs()
	switch subcommand {
	case "use":
		if len(this.GetArgs()) > 0 {
			_, er := this.Use(this.GetArgs()[0])
			if er != nil {
				this.ErrorMsgf("设置当前跨交易所套利机失败: %s", er)
				return false
			}
			this.SendMsgf("设置当前跨交易所套利机为: %s", this.arbitrager.GetAliasOrID())
		} else {
			this.ListCrossExchangeArbitrages()
			return true
		}
	case "statusall":
		this.ListCrossExchangeArbitrages()
	case "status", "s":
		this.SendMsgf("```%s```", this.arbitrager.RenderStatus())
	case "report", "r":
		result := this.arbitrager.RenderReport()
		this.SendFileMessage(this.arbitrager.ID, result, "")
	case "holdings", "h":
		balances, err := this.arbitrager.ControllerLeft.GetAccountBalances(true)
		if err != nil {
			this.ErrorMsgf("[get holdings error: %s]", err)
			return false
		}

		this.SendMsgf("控制器 *%s* 资产汇总：\n```%s```", this.arbitrager.ControllerLeft.GetID(), balances.RenderTotalCombined())
		this.SendMsgf("控制器 *%s* 资产明细：\n```%s```", this.arbitrager.ControllerLeft.GetID(), balances.Render())
		balances, err = this.arbitrager.ControllerRight.GetAccountBalances(true)
		if err != nil {
			this.ErrorMsgf("[get holdings error: %s]", err)
			return false
		}
		this.SendMsgf("控制器 *%s* 资产汇总：\n```%s```", this.arbitrager.ControllerRight.GetID(), balances.RenderTotalCombined())
		this.SendMsgf("控制器 *%s* 资产明细：\n```%s```", this.arbitrager.ControllerRight.GetID(), balances.Render())
		return true
	case "new":
		arbitrager, err := this.NewCrossExchangeArbitrager(args)
		if err != nil {
			this.ErrorMsgf("创建跨交易所套利机失败: %s", err)
			return false
		}
		this.controller.AddStrategy(base.StrategyTypeCrossExchangeArbitrager, arbitrager)
		this.Use(arbitrager.ID)
		go arbitrager.Run()
		this.SendMsgf("新增跨交易所套利机: %s", arbitrager.ID)
		arbitrager.UpdateAccountSnapshot()
		this.SendMsgf("```%s```", arbitrager.RenderStatus())
	case "edit":
		if this.arbitrager.Status == StatusRunning {
			this.ErrorMsgf("跨交易所套利机 %s 正在运行，无法编辑", this.arbitrager.ID)
			return false
		}
		if this.arbitrager.IsRebalancing() {
			this.ErrorMsgf("跨交易所套利机 %s 正在 rebalancing，无法编辑", this.arbitrager.ID)
			return false
		}

		newArbitrager, err := this.NewCrossExchangeArbitrager(args)
		if err != nil {
			this.ErrorMsgf("编辑跨交易所套利机失败: %s", err)
			return false
		}
		this.arbitrager.ControllerIDLeft = newArbitrager.ControllerIDLeft
		this.arbitrager.ControllerIDRight = newArbitrager.ControllerIDRight
		this.arbitrager.DepositAddressLeft = newArbitrager.DepositAddressLeft
		this.arbitrager.DepositAddressRight = newArbitrager.DepositAddressRight
		this.arbitrager.DepositCoin = newArbitrager.DepositCoin
		this.arbitrager.SetupExchanges()
		this.SendMsgf("跨交易所套利机 %s 已编辑", this.arbitrager.ID)
	case "info":
		this.SendMsgf("```%s```", this.arbitrager.RenderInfo())
	case "remove":
		err := this.controller.RemoveStrategy(base.StrategyTypeCrossExchangeArbitrager, this.arbitrager.ID)
		if err != nil {
			return false
		}
		this.SendMsgf("跨交易所套利机 %s 已移除", this.arbitrager.ID)
	case "stop":
		this.arbitrager.Stop()
		this.SendMsgf("跨交易所套利机 %s 已关闭", this.arbitrager.ID)
	case "pause":
		this.arbitrager.Pause()
		this.SendMsgf("跨交易所套利机 %s 已暂停", this.arbitrager.ID)
	case "resume":
		this.arbitrager.Resume()
		this.SendMsgf("跨交易所套利机 %s 已恢复", this.arbitrager.ID)
	case "long", "short":
		symbolCodeStr := args[0]
		qtyStr := args[1]
		_, _, err := this.arbitrager.TranslateBothSideSymbols(symbolCodeStr)
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}
		splits, err := this.parseSplitArgs(2)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		err = this.arbitrager.OpenPosition(subcommand, symbolCodeStr, qtyStr, splits)
		if err != nil {
			this.ErrorMsgf("开仓失败: %s", err)
			return false
		}
		// 设置监控数据放松的时间点，防止由于发送订单导致 marginRatio 快速变化
		// 5 秒后放松，太快的话，可能订单还没有同步完成
		this.arbitrager.MonitorRelaxAtTime = time.Now().Add(time.Second * 5)
		this.SendMsgf("跨交易所套利机 %s 已开仓", this.arbitrager.GetAliasOrID())
	case "close":
		uSymbol := this.arbitrager.ControllerLeft.GetBaseConfig().USDXSymbol
		_, _, futureSymbols, err := exchange.ParseSymbolsFromSymbolCodes(this.arbitrager.ExchangeLeft, args[0], uSymbol)
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}

		splits, err := this.parseSplitArgs(1)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		this.arbitrager.ClosePositions(futureSymbols, splits)
		this.arbitrager.MonitorRelaxAtTime = time.Now().Add(time.Second * 5)
		this.SendMsgf("跨交易所套利机 %s 已平仓", this.arbitrager.GetAliasOrID())
	case "rebalance":
		this.SendMsgf("跨交易所套利机 %s 开始平衡保证金", this.arbitrager.GetAliasOrID())
		go this.arbitrager.Rebalance(func(w *Withdraw, err error) {
			if err != nil {
				this.arbitrager.ErrorMsgf("手工平衡保证金失败: %s, 但是不自动减仓", err)
			} else {
				this.arbitrager.MonitorRelaxAtTime = time.Now()
				this.SendMsgf("手工平衡保证金成功: %s => %s, 金额: %.2f", w.FromExchange, w.ToExchange, w.Amount)
			}
		})
	case "closeall":
		splits, err := this.parseSplitArgs(0)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		this.arbitrager.Close(splits)
		this.arbitrager.MonitorRelaxAtTime = time.Now().Add(time.Second * 5)
		this.SendMsgf("跨交易所套利机 %s 已平仓所有仓位", this.arbitrager.ID)

		this.arbitrager.CheckLoanAndPay()
	case "reduce":
		ratio, _, err := utils.ParseFloatOrPercentage(args[0], false, false)
		if err != nil {
			this.ErrorMsgf("解析百分比失败: %s", err)
			return false
		}
		_, _, _, symbolsLeft, _, err := this.arbitrager.ParseSymbolCodes(args[1])
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}
		this.arbitrager.ReducePositions(ratio, symbolsLeft...)
		this.SendMsgf("%s 已减仓完成", this.arbitrager.GetAliasOrID())
	case "reduceall":
		ratio, _, err := utils.ParseFloatOrPercentage(args[0], false, false)
		if err != nil {
			this.ErrorMsgf("解析百分比失败: %s", err)
			return false
		}
		this.arbitrager.ReducePositions(ratio)
		this.SendMsgf("%s 已减仓完成", this.arbitrager.GetAliasOrID())
	case "position", "pos":
		if len(args) > 0 {
			id := args[0]
			snapshots := this.arbitrager.LookupPositionSnapshots(id, nil, 0)
			if len(snapshots) == 0 {
				this.ErrorMsgf("未找到仓位快照: %s", id)
				return false
			} else if len(snapshots) > 1 {
				this.ErrorMsgf("找到多个仓位快照: %s", id)
				return false
			}
			this.SendMsgf("```%s```", this.arbitrager.RenderPositions(snapshots[0]))
		} else {
			this.SendMsgf("```%s```", this.arbitrager.RenderPositions(this.arbitrager.PositionSnapshot))
		}
	case "risks":
		limit := 20
		if this.IsMore() {
			limit = 0
		}
		this.SendMsgf("```%s```", this.arbitrager.RenderRiskEvents(limit))
	case "withdraws":
		limit := 20
		if this.IsMore() {
			limit = 0
		}
		this.SendMsgf("```%s```", this.arbitrager.RenderWithdraws(limit))
	case "monitors":
		limit := 20
		if this.IsMore() {
			limit = 0
		}
		this.SendMsgf("MonitorRelaxAtTime: `%s`\n\n```%s```", utils.FormatShortTimeStr(&this.arbitrager.MonitorRelaxAtTime, true), this.arbitrager.RenderMonitors(limit))
	case "snapshots":
		isDaily := false
		var exactTime *time.Time
		if len(args) > 0 {
			if args[0] == "daily" {
				isDaily = true
			} else {
				exactTime = utils.ParseTimeBeijing(args[0])
				if exactTime == nil {
					this.ErrorMsgf("解析时间失败: %s", args[0])
					return false
				}
			}
		}
		if exactTime != nil {
			snapshots := this.arbitrager.LookupAccountSnapshots("", exactTime, 0)
			this.SendMsgf("```%s```", this.arbitrager.RenderAccountSnapshots(snapshots))
			return true
		}
		hourlySnapshots := []*AccountSnapshot{}
		this.arbitrager.AccountSnapshots.Range(func(key int64, snapshot *AccountSnapshot) bool {
			hourlySnapshots = append(hourlySnapshots, snapshot)
			return true
		})
		if isDaily {
			keptSnapshots := this.arbitrager.FilterDailySnapshots(hourlySnapshots)
			this.SendMsgf("```%s```", this.arbitrager.RenderAccountSnapshots(keptSnapshots))
		} else {
			this.SendMsgf("```%s```", this.arbitrager.RenderAccountSnapshots(hourlySnapshots))
		}
	case "snapshot", "snap":
		id := ""
		if len(args) > 0 {
			id = args[0]
		}
		if id == "" {
			this.SendMsgf("```%s```", this.arbitrager.RenderAccountSnapshotWithContext(this.arbitrager.AccountSnapshot))
		} else {
			snapshots := this.arbitrager.LookupAccountSnapshots(id, nil, 0)
			this.SendMsgf("```%s```", this.arbitrager.RenderAccountSnapshots(snapshots))
		}
	case "prices":
		symbolCode := args[0]
		limit := 20
		if this.IsMore() {
			limit = 0
		}
		var exactTime *time.Time
		timeStr := "now"
		if len(args) > 1 {
			timeStr = args[1]
		}
		exactTime = utils.ParseTimeBeijing(timeStr)
		if exactTime == nil {
			this.ErrorMsgf("解析时间失败: %s", args[1])
			return false
		}
		snapshots := this.arbitrager.LookupAccountSnapshots("", exactTime, limit)
		this.SendMsgf("```%s```", this.arbitrager.RenderPrices(snapshots, symbolCode, limit))
	case "fundings":
		symbolCodeStr := ""
		if len(args) > 0 {
			symbolCodeStr = args[0]
		}
		this.SendMsgf("```Funding APR: \n\n%s```", this.arbitrager.RenderFundings(symbolCodeStr))
	case "alias":
		idOrAlias := args[0]
		alias := args[1]
		err := this.controller.SetStrategyAlias(base.StrategyTypeCrossExchangeArbitrager, idOrAlias, alias)
		if err != nil {
			return false
		}
	case "set":
		configStr := args[0]
		if strings.Contains(configStr, ":") {
			this.ErrorMsgf("请不要输入 params 的配置，请输入 options 的配置")
			return false
		}
		err := this.arbitrager.SetOptions(configStr)
		if err != nil {
			return false
		}
		this.SendMsgf("更新跨交易所套利机 %s 配置项成功", this.arbitrager.ID)
		this.SendMsgf("```%s```", this.arbitrager.Options.ToTable())
	case "param", "params":
		this.SendMsgf("```%s\n\n%s```\n\n```%s```", this.arbitrager.RenderParams(), this.arbitrager.FormatParamsParsable(), this.arbitrager.FormatAllSymbolCodes())
		this.SendMsgf("\n默认配置\n```%s```", this.arbitrager.Options.ToTable())
	case "setparam", "setparams":
		paramStr := args[0]
		if !strings.Contains(paramStr, ":") {
			this.ErrorMsgf("请输入 symbolCode:params 的格式")
			return false
		}
		parts := strings.Split(paramStr, "|")
		for _, symbolCodeStr := range parts {
			subParts := strings.Split(symbolCodeStr, ":")
			symbolCode := subParts[0]
			params := subParts[1]
			allSymbolCodes, _, _, err := exchange.ParseSymbolsFromSymbolCodes(this.arbitrager.ExchangeLeft, symbolCode, this.arbitrager.ControllerLeft.GetBaseConfig().USDXSymbol)
			if err != nil {
				this.ErrorMsgf("解析品种代码失败: %s", err)
				return false
			}
			for _, symbolCode := range allSymbolCodes {
				this.arbitrager.UpdateParams(symbolCode, params)
				this.SendMsgf("跨交易所套利机 %s 设置参数成功: ```%s```", this.arbitrager.ID, this.arbitrager.RenderParamsForSymbol(symbolCode))
			}
		}
		this.controller.GetStorage().Save()
	case "simulate":
		riskCategory := RiskCategory(args[0])
		// 这里不要直接调用 SimulateRiskEvent，而是设置模拟风险事件标志，在风险控制函数中，updateAccountSnapshot 之后统一执行
		if this.controller.IsDebug() {
			this.arbitrager.simulatingRisk = riskCategory
			this.SendMsgf("跨交易所套利机 %s 开始模拟风险事件: %s", this.arbitrager.GetAliasOrID(), riskCategory)
		} else {
			this.ErrorMsgf("当前不是 debug 模式，不能模拟风险事件")
		}
	case "options":
		ex, _, err := this.arbitrager.parseExchange(exchange.Hyperliquid)
		if err != nil {
			this.ErrorMsgf("parse exchange failed: %s", err)
			return false
		}

		hyper := ex.(*hyperliquid.Hyperliquid)
		used, cap, err := hyper.GetRateLimit()
		if err != nil {
			this.ErrorMsgf("get rate limit failed: %s", err)
			return false
		}
		this.SendMsgf("```%s```\nMonitorRelaxAtTime: `%s`\n\nHyperliquid 请求限额: `%d / %d 剩余：%d`", this.arbitrager.GetOptions().ToTable(), utils.FormatShortTimeStr(&this.arbitrager.MonitorRelaxAtTime, true), used, cap, cap-used)
	case "rotate":
		_, isRight, err := this.arbitrager.parseExchange(args[0])
		if err != nil {
			this.ErrorMsgf("解析交易所名称失败: %s", err)
			return false
		}

		symbolLeft, symbolRight, err := this.arbitrager.TranslateBothSideSymbols(args[1])
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}

		var symbol string
		if isRight {
			symbol = symbolRight
		} else {
			symbol = symbolLeft
		}

		ratio, _, err := utils.ParseFloatOrPercentage(args[2], false, false)
		if err != nil {
			this.ErrorMsgf("解析百分比失败: %s", err)
			return false
		}

		splits, err := this.parseSplitArgs(3)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		err = this.arbitrager.Rotate(symbol, ratio, splits, isRight)
		if err != nil {
			this.ErrorMsgf("平、开仓释放保证金失败: %s", err)
			return false
		} else {
			this.SendMsgf("跨交易所套利机 %s 平、开仓释放保证金成功", this.arbitrager.GetAliasOrID())
		}
	case "wash":
		_, isRight, err := this.arbitrager.parseExchange(args[0])
		if err != nil {
			this.ErrorMsgf("解析交易所名称失败: %s", err)
			return false
		}

		symbolCode := args[1]
		symbolLeft, symbolRight, err := this.arbitrager.TranslateBothSideSymbols(symbolCode)
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}
		symbol := symbolLeft
		if isRight {
			symbol = symbolRight
		}

		qty, quoteQty, err := this.parseQty(2)
		if err != nil {
			this.ErrorMsgf("解析数量失败: %s", err)
			return false
		}

		splits, err := this.parseSplitArgs(3)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		err = this.arbitrager.Wash(symbol, qty/2, quoteQty/2, splits, isRight)
		if err != nil {
			this.ErrorMsgf("刷量交易失败: %s", err)
			return false
		} else {
			this.SendMsgf("跨交易所套利机 %s 刷量交易成功", this.arbitrager.GetAliasOrID())
		}
	case "maintenance", "maint":
		maints := this.arbitrager.Maintenances
		maintenance, updated, er := this.setMaintenance(&maints, args)
		if er != nil {
			this.ErrorMsgf("设置交易所维护失败: %s", er)
			return false
		}
		this.arbitrager.Maintenances = maints // 更新维护列表，否则加不进去
		if updated {
			this.SendMsgf("%s 交易所维护，更新成功：\n```%s```", this.arbitrager.GetAliasOrID(), maintenance.ToTable())
		} else {
			this.SendMsgf("%s 交易所维护，添加成功：\n```%s```", this.arbitrager.GetAliasOrID(), maintenance.ToTable())
		}
		this.controller.GetStorage().Save()
	case "maintenances", "maints":
		this.SendMsgf("```%s```", this.arbitrager.Maintenances.ToTable())
	case "pricediff":
		this.arbitrager.PrintSymbolPriceDiff(false)
	}
	return true
}

func (this *CrossExchangeArbitrageCommand) setMaintenance(maints *Maintenances, args []string) (maintenance *Maintenance, updated bool, er error) {
	exchangeName := args[0]
	startTimeStr := args[1]
	endTimeStr := args[2]
	message := ""
	if len(args) > 3 {
		message = args[3]
	}

	ex, _, err := this.arbitrager.parseExchange(exchangeName)
	if err != nil {
		return nil, false, fmt.Errorf("parse exchange failed: %s", err)
	}
	startTime := utils.ParseTimeBeijing(startTimeStr)
	if startTime == nil {
		return nil, false, fmt.Errorf("parse start time failed: %s", startTimeStr)
	}
	var endTime *time.Time
	minutes, err := cast.ToIntE(endTimeStr)
	if err != nil {
		endTime = utils.ParseTimeBeijing(endTimeStr)
		if endTime == nil {
			return nil, false, fmt.Errorf("parse end time failed: %s", endTimeStr)
		}
		minutes = int(endTime.Sub(*startTime).Seconds() / 60)
	} else {
		endTime = utils.Ptr(startTime.Add(time.Duration(minutes) * time.Minute))
	}
	if minutes > 0 && endTime.Before(time.Now()) {
		return nil, false, fmt.Errorf("end time is before now: %s, now: %s", utils.FormatShortTimeStr(endTime, true), utils.FormatShortTimeStr(utils.Ptr(time.Now()), true))
	}

	maintenance, updated = maints.Set(ex.GetName(), *startTime, minutes, message, "")
	return
}

func (this *CrossExchangeArbitrageCommand) parseQty(pos int) (qty float64, quoteQty float64, er error) {
	args := this.GetArgs()
	if len(args) <= pos {
		return 0, 0, fmt.Errorf("args length is less than %d", pos)
	}
	qtyStr := args[pos]
	if len(qtyStr) > 2 && exchange.SliceContains([]string{".u"}, qtyStr[len(qtyStr)-2:]) {
		qtyStr = qtyStr[:len(qtyStr)-2]
		quoteQty, er = cast.ToFloat64E(qtyStr)
	} else {
		qty, er = cast.ToFloat64E(qtyStr)
	}
	return
}

func (this *CrossExchangeArbitrageCommand) NewCrossExchangeArbitrager(args []string) (*CrossExchangeArbitrager, error) {
	// ID1 ID2 DepositAddress1 DepositAddress2 [options]
	controller1 := this.controller.GetController(args[0])
	controller2 := this.controller.GetController(args[1])
	if controller1 == nil || controller2 == nil {
		return nil, fmt.Errorf("controller not found")
	}
	exchange1 := controller1.GetExchange()
	exchange2 := controller2.GetExchange()
	if exchange1 == nil || exchange2 == nil {
		return nil, fmt.Errorf("exchange not found")
	}
	if exchange1.GetName() == exchange2.GetName() {
		return nil, fmt.Errorf("exchange1 and exchange2 are the same")
	}
	var controllerIDLeft, controllerIDRight string
	var depositAddressLeft, depositAddressRight string
	if exchange1.GetName() == exchange.Bybit || exchange1.GetName() == exchange.Binance {
		controllerIDLeft = args[0]
		controllerIDRight = args[1]
		depositAddressLeft = args[2]
		depositAddressRight = args[3]
	} else {
		controllerIDLeft = args[1]
		controllerIDRight = args[0]
		depositAddressLeft = args[3]
		depositAddressRight = args[2]
	}
	depositCoin := strings.ToUpper(args[4])
	optStr := ""
	if len(args) > 5 {
		optStr = args[5]
	}
	arbitrager, err := NewCrossExchangeArbitrager(this.controller, depositCoin, controllerIDLeft, depositAddressLeft, controllerIDRight, depositAddressRight, optStr)
	if err != nil {
		return nil, fmt.Errorf("new cross exchange arbitrage failed: %s", err)
	}
	return arbitrager, nil
}

func (this *CrossExchangeArbitrager) SetOptions(options string) (er error) {
	opts := this.GetOptions().Copy()
	updatedNames, err := opts.UpdateFromString(options)
	if err != nil {
		return fmt.Errorf("update options failed: %s", err)
	}
	if err := this.ValidateOptions(opts); err != nil {
		return fmt.Errorf("validate options failed: %s", err)
	}

	// 如果更新了目标保证金率或再平衡保证金率，则需要放松监控
	// 否则会可能因为 marginRatio 快速变化，导致监控触发风险事件
	targetMarginRatioUpdated := utils.SliceContains(updatedNames, string(OptionTargetMarginRatio))
	rebalanceMarginRatioUpdated := utils.SliceContains(updatedNames, string(OptionRebalanceMarginRatio))
	if targetMarginRatioUpdated || rebalanceMarginRatioUpdated {
		this.MonitorRelaxAtTime = time.Now().Add(time.Second * 5)
	}

	this.UpdateOptions(opts)
	this.controller.GetStorage().Save()
	this.UpdateOptionsCallback(updatedNames)
	return
}

func (this *CrossExchangeArbitrager) UpdateOptionsCallback(updatedNames []string) {
	if utils.SliceContains(updatedNames, string(OptionTargetMarginRatio)) || utils.SliceContains(updatedNames, string(OptionRebalanceMarginRatio)) {
		this.checkMarginRatioOption(true)
	}
}

func (this *CrossExchangeArbitrageCommand) parseSplitArgs(pos int) (splits int, err error) {
	args := this.GetArgs()
	splits = -1
	if len(args) > pos {
		splits, err = cast.ToIntE(args[pos])
		if err != nil {
			return 0, err
		}
		if splits < 1 && splits != -1 {
			return 0, fmt.Errorf("splits must be greater than 0, or equal to -1")
		}
	}
	return
}

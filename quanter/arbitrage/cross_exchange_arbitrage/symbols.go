package cross_exchange_arbitrage

import (
	"errors"
	"fmt"
	"sort"

	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

func (this *CrossExchangeArbitrager) IsTheSameSymbolCode(instrumnet exchange.InstrumentType, symbolLeft string, symbolRight string) bool {
	uSymbol := this.ControllerLeft.GetBaseConfig().USDXSymbol
	_, symbolCodeLeft, _ := this.ExchangeLeft.TranslateFutureSymbol(instrumnet, symbolLeft, uSymbol)
	_, symbolCodeRight, _ := this.ExchangeRight.TranslateFutureSymbol(instrumnet, symbolRight, uSymbol)
	return symbolCodeLeft != nil && symbolCodeRight != nil && symbolCodeLeft.String() == symbolCodeRight.String()
}

func (this *CrossExchangeArbitrager) GetSymbolCodeLeft(symbol string) (symbolCode *exchange.SymbolCode, er error) {
	uSymbol := this.ControllerLeft.GetBaseConfig().USDXSymbol
	_, symbolCode, er = this.ExchangeLeft.TranslateFutureSymbol(exchange.USDXMarginedFutures, symbol, uSymbol)
	return
}

func (this *CrossExchangeArbitrager) GetSymbolCodeRight(symbol string) (symbolCode *exchange.SymbolCode, er error) {
	uSymbol := this.ControllerRight.GetBaseConfig().USDXSymbol
	_, symbolCode, er = this.ExchangeRight.TranslateFutureSymbol(exchange.USDXMarginedFutures, symbol, uSymbol)
	return
}

func (this *CrossExchangeArbitrager) GetSymbolCode(symbol string, isRight bool) (symbolCode *exchange.SymbolCode, er error) {
	if isRight {
		symbolCode, er = this.GetSymbolCodeRight(symbol)
	} else {
		symbolCode, er = this.GetSymbolCodeLeft(symbol)
	}
	return
}

func (this *CrossExchangeArbitrager) ParseSymbolCodes(symbolCodeStr string) (allSymbolCodeStrs []string, symbolCodesLeft []*exchange.SymbolCode, symbolCodesRight []*exchange.SymbolCode, symbolsLeft []string, symbolsRight []string, er error) {
	var err error
	allSymbolCodeStrs, _, symbolsLeft, err = exchange.ParseSymbolsFromSymbolCodes(this.ExchangeLeft, symbolCodeStr, this.ControllerLeft.GetBaseConfig().USDXSymbol)
	if err != nil {
		er = err
		return
	}
	_, _, symbolsRight, err = exchange.ParseSymbolsFromSymbolCodes(this.ExchangeRight, symbolCodeStr, this.ControllerRight.GetBaseConfig().USDXSymbol)
	if err != nil {
		er = err
		return
	}
	for _, symbol := range symbolsLeft {
		_, symbolCode, err := this.ExchangeLeft.TranslateFutureSymbol(exchange.USDXMarginedFutures, symbol, this.ControllerLeft.GetBaseConfig().USDXSymbol)
		if err != nil {
			continue
		}
		symbolCodesLeft = append(symbolCodesLeft, symbolCode)
	}
	for _, symbol := range symbolsRight {
		_, symbolCode, err := this.ExchangeRight.TranslateFutureSymbol(exchange.USDXMarginedFutures, symbol, this.ControllerRight.GetBaseConfig().USDXSymbol)
		if err != nil {
			continue
		}
		symbolCodesRight = append(symbolCodesRight, symbolCode)
	}
	if len(symbolCodesLeft) != len(symbolsLeft) || len(symbolCodesRight) != len(symbolsRight) {
		er = errors.New("symbol codes and symbols length mismatch")
	}
	if len(symbolCodesLeft) != len(symbolCodesRight) {
		er = errors.New("symbol codes length mismatch")
	}
	return
}

func (this *CrossExchangeArbitrager) GetSymbolCodesForAccountSnapshot(snapshot *AccountSnapshot) (allSymbolCodeStrs []string, allSymbols []string, symbolCodesLeft []*exchange.SymbolCode, symbolCodesRight []*exchange.SymbolCode, symbolsLeft []string, symbolsRight []string, er error) {
	snapshot.SymbolContextLeft.Range(func(symbol string, ctx *SymbolContext) bool {
		symbolCode, err := this.GetSymbolCodeLeft(symbol)
		if err != nil {
			return true
		}
		allSymbolCodeStrs = append(allSymbolCodeStrs, symbolCode.Code)
		return true
	})
	snapshot.SymbolContextRight.Range(func(symbol string, ctx *SymbolContext) bool {
		symbolCode, err := this.GetSymbolCodeRight(symbol)
		if err != nil {
			return true
		}
		allSymbolCodeStrs = append(allSymbolCodeStrs, symbolCode.Code)
		return true
	})
	allSymbolCodeStrs = utils.Unique(allSymbolCodeStrs)
	sort.Strings(allSymbolCodeStrs)

	for _, symbolCodeStr := range allSymbolCodeStrs {
		symbolLeft, symbolRight, err := this.TranslateBothSideSymbols(symbolCodeStr)
		if err != nil {
			continue
		}
		symbolsLeft = append(symbolsLeft, symbolLeft)
		symbolsRight = append(symbolsRight, symbolRight)
		allSymbols = append(allSymbols, symbolLeft, symbolRight)

		symbolCodeLeft, err := exchange.NewSymbolCode(symbolCodeStr, this.ControllerLeft.GetBaseConfig().USDXSymbol)
		if err != nil {
			this.Errorf("get symbol codes for account snapshot, new symbol code left failed, error: %s", err)
			continue
		}
		symbolCodesLeft = append(symbolCodesLeft, symbolCodeLeft)
		symbolCodeRight, err := exchange.NewSymbolCode(symbolCodeStr, this.ControllerRight.GetBaseConfig().USDXSymbol)
		if err != nil {
			this.Errorf("get symbol codes for account snapshot, new symbol code right failed, error: %s", err)
			continue
		}
		symbolCodesRight = append(symbolCodesRight, symbolCodeRight)
	}
	// 去重并且排序保持 symbol 的稳定性
	allSymbols = utils.Unique(allSymbols)
	sort.Strings(allSymbols)
	if len(symbolCodesLeft) != len(symbolsLeft) || len(symbolCodesRight) != len(symbolsRight) {
		er = errors.New("symbol codes and symbols length mismatch")
	}
	return
}

func (this *CrossExchangeArbitrager) TranslateBothSideSymbols(symbolCodeStr string) (symbolLeft, symbolRight string, er error) {
	symbolCodeLeft, err := exchange.NewSymbolCode(symbolCodeStr, this.ControllerLeft.GetBaseConfig().USDXSymbol)
	if err != nil {
		er = fmt.Errorf("new symbol code failed, error: %s", err)
		return
	}
	symbolLeft, err = this.ExchangeLeft.TranslateSymbolCodeToFutureSymbol(symbolCodeLeft)
	if err != nil {
		er = fmt.Errorf("translate left symbol code to future symbol failed, error: %s", err)
		return
	}
	symbolCodeRight, err := exchange.NewSymbolCode(symbolCodeStr, this.ControllerRight.GetBaseConfig().USDXSymbol)
	if err != nil {
		er = fmt.Errorf("new symbol code failed, error: %s", err)
		return
	}
	symbolRight, err = this.ExchangeRight.TranslateSymbolCodeToFutureSymbol(symbolCodeRight)
	if err != nil {
		er = fmt.Errorf("translate right symbol code to future symbol failed, error: %s", err)
		return
	}
	return
}

// 获取交易对，检查 symbolCode 在交易所是否存在
func (this *CrossExchangeArbitrager) GetSymbolPair(symbolCodeStr string) (symbolPair *exchange.SymbolPair, er error) {
	usdxSymbolLeft := this.ControllerLeft.GetBaseConfig().USDXSymbol
	usdxSymbolRight := this.ControllerRight.GetBaseConfig().USDXSymbol
	symbolCodeLeft, err := exchange.NewSymbolCode(symbolCodeStr, usdxSymbolLeft)
	if err != nil {
		er = fmt.Errorf("new symbol code failed: %s", err)
		return
	}
	symbolCodeRight, err := exchange.NewSymbolCode(symbolCodeStr, usdxSymbolRight)
	if err != nil {
		er = fmt.Errorf("new symbol code failed: %s", err)
		return
	}
	if !symbolCodeLeft.IsPerp() || !symbolCodeRight.IsPerp() {
		er = fmt.Errorf("symbol code is not perp: %s", symbolCodeRight)
		return
	}
	symbolLeft, symbolRight, err := this.TranslateBothSideSymbols(symbolCodeStr)
	if err != nil {
		er = fmt.Errorf("translate both side symbols failed, error: %s", err)
		return
	}
	symbolPair = exchange.NewCommonSymbolPair(symbolCodeLeft, symbolCodeRight, symbolLeft, symbolRight)
	return
}

// 在 ExchangeLeft 和 ExchangeRight 之间转换 symbol
// 因为 left 和 right USDX 未必一样，需要指定清楚
func (this *CrossExchangeArbitrager) ConvertSymbol(symbol string, leftToRight bool) (convertedSymbol string, er error) {
	if err := this.CheckExchanges(); err != nil {
		er = fmt.Errorf("check exchanges failed, error: %s", err)
		return
	}
	usdxSymbolLeft := this.ControllerLeft.GetBaseConfig().USDXSymbol
	usdxSymbolRight := this.ControllerRight.GetBaseConfig().USDXSymbol
	if leftToRight {
		_, futureCode, err := this.ExchangeLeft.TranslateFutureSymbol(exchange.USDXMarginedFutures, symbol, usdxSymbolLeft)
		if err != nil {
			return "", fmt.Errorf("translate to symbol code failed, error: %s", err)
		}
		futureCode.USDXSymbol = usdxSymbolRight
		convertedSymbol, err = this.ExchangeRight.TranslateSymbolCodeToFutureSymbol(futureCode)
		if err != nil {
			return "", fmt.Errorf("translate to future symbol failed, error: %s", err)
		}
	} else {
		_, futureCode, err := this.ExchangeRight.TranslateFutureSymbol(exchange.USDXMarginedFutures, symbol, usdxSymbolRight)
		if err != nil {
			return "", fmt.Errorf("translate to symbol code failed, error: %s", err)
		}
		futureCode.USDXSymbol = usdxSymbolLeft
		convertedSymbol, err = this.ExchangeLeft.TranslateSymbolCodeToFutureSymbol(futureCode)
		if err != nil {
			return "", fmt.Errorf("translate to future symbol failed, error: %s", err)
		}
	}
	return
}

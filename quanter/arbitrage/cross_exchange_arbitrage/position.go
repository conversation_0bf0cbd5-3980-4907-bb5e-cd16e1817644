package cross_exchange_arbitrage

import (
	"fmt"
	"math"
	"slices"
	"strings"
	"time"

	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

func (this *CrossExchangeArbitrager) SyncPositions() {
	if !this.mutexes.syncPositionMutex.TryLock() {
		return
	}
	defer this.mutexes.syncPositionMutex.Unlock()

	if this.PositionSnapshot == nil {
		return
	}
	this.Infof("syncing positions...")

	if time.Since(this.PositionSnapshot.CreateTime) > LoopInterval {
		this.Warnf("position snapshot is too old, ignore")
		return
	}

	// 检查 PositionsRight 的仓位是否与 PositionsLeft 一致，如果不一致，则同步
	for _, positionLeft := range this.PositionSnapshot.PositionsLeft {
		// 检查 PositionsRight 是否有相同的仓位
		var positionRight *exchange.Position
		for _, p := range this.PositionSnapshot.PositionsRight {
			if this.IsTheSameSymbolCode(positionLeft.InstrumentType, positionLeft.Symbol, p.Symbol) {
				positionRight = p
				break
			}
		}
		if positionRight == nil {
			go this.syncPosition(positionLeft.InstrumentType, positionLeft.Symbol)
			continue
		}

		targetPosition := this.ExchangeRight.RoundQty(positionRight.InstrumentType, positionRight.Symbol, -positionLeft.Qty)
		if positionRight.Qty != targetPosition {
			go this.syncPosition(positionLeft.InstrumentType, positionLeft.Symbol)
		}
	}

	// PositionsRight 有仓位，PositionsLeft 为 0 的情况
	for _, positionRight := range this.PositionSnapshot.PositionsRight {
		var positionLeft *exchange.Position
		for _, p := range this.PositionSnapshot.PositionsLeft {
			if this.IsTheSameSymbolCode(positionRight.InstrumentType, p.Symbol, positionRight.Symbol) {
				positionLeft = p
				break
			}
		}
		if positionLeft == nil || positionLeft.Qty == 0 {
			symbolLeft, err := this.ConvertSymbol(positionRight.Symbol, false)
			if err != nil {
				this.ErrorMsgf("convert symbol failed: %s", err)
				return
			}
			go this.syncPosition(positionRight.InstrumentType, symbolLeft)
		}
	}
}

// Right 交易所的仓位与 Left 交易所的仓位同步
func (this *CrossExchangeArbitrager) syncPosition(instrumentType exchange.InstrumentType, symbolLeft string) {
	if instrumentType != exchange.USDXMarginedFutures {
		// 暂时只支持 U 本位合约
		return
	}

	code, err := this.GetSymbolCodeLeft(symbolLeft)
	if err != nil {
		this.Errorf("sync position error, get symbol code failed: %s", err)
		return
	}
	lock, _ := this.SymbolLocks.LoadOrStore(code.String(), &SymbolLock{
		SymbolCode: code.String(),
	})
	if !lock.SyncingPosition.TryLock() {
		this.Infof("syncing position try lock failed, symbol: %s", symbolLeft)
		return
	}
	defer lock.SyncingPosition.Unlock()

	leftPosition, err := this.ExchangeLeft.GetPosition(instrumentType, symbolLeft, exchange.UnknownPositionSide, false)
	if err != nil {
		this.ErrorMsgf("get left position failed: %s", err)
		return
	}

	// 通过 symbolCode 找到相同的 symbol
	uSymbol := this.ControllerLeft.GetBaseConfig().USDXSymbol
	_, symbolCode, err := this.ExchangeLeft.TranslateFutureSymbol(instrumentType, symbolLeft, uSymbol)
	if err != nil {
		this.ErrorMsgf("translate future symbol failed: %s", err)
		return
	}
	symbol, err := this.ExchangeRight.TranslateSymbolCodeToFutureSymbol(symbolCode)
	if err != nil {
		this.ErrorMsgf("translate symbol code to future symbol failed: %s", err)
		return
	}

	this.syncPositionOnRight(instrumentType, symbol, -leftPosition.Qty)
}

// 在右侧同步仓位
func (this *CrossExchangeArbitrager) syncPositionOnRight(instrumentType exchange.InstrumentType, symbol string, targetPostion float64) {
	this.Infof("syncing right position: %s, %v", symbol, targetPostion)
	syncPostionSuccess := false
	isOpen := false

	var deltaRatio float64

	defer func() {
		if !isOpen {
			// 加仓出错时才需要减仓
			return
		}
		if syncPostionSuccess {
			this.counters.syncPositionErrorCounter.Store(0)
		} else {
			this.counters.syncPositionErrorCounter.Add(1)
		}
		if this.counters.syncPositionErrorCounter.Load() >= SyncPositionErrorLimit {
			this.SendMsgf("sync position error count reach limit, reduce positions.")
			futureSymbolLeft, err := this.ConvertSymbol(symbol, false)
			if err != nil {
				this.ErrorMsgf("convert symbol failed: %s", err)
			} else {
				// 保守起见，减仓比例始终不大于 SyncPositionErrorReduceRatio
				// 下个循环可以重试，再次减仓
				reduceRatio := SyncPositionErrorReduceRatio
				if deltaRatio > 0 {
					reduceRatio = math.Min(deltaRatio, SyncPositionErrorReduceRatio)
				}
				err = this.ReducePositions(reduceRatio, futureSymbolLeft)
				if err != nil {
					this.ErrorMsgf("sync right position reach error limit, reduce positions failed: %s", err)
				}
				this.counters.syncPositionErrorCounter.Store(0)
			}
		}
	}()

	rightPosition, err := this.ExchangeRight.GetPosition(instrumentType, symbol, exchange.UnknownPositionSide, false)
	if err != nil {
		this.ErrorMsgf("get right position failed: %s", err)
		return
	}

	targetPostion = this.ExchangeRight.RoundQty(instrumentType, symbol, targetPostion)
	if rightPosition.Qty == targetPostion {
		this.Infof("position is already synced: %s, %s, %v = %v", instrumentType, symbol, rightPosition.Qty, targetPostion)
		syncPostionSuccess = true
		return
	}

	if math.Abs(targetPostion) > math.Abs(rightPosition.Qty) {
		isOpen = true
	}

	symbolLeft, err := this.ConvertSymbol(symbol, false)
	if err != nil {
		this.ErrorMsgf("convert symbol failed: %s", err)
		return
	}

	// 如果当前仓位为 0，但有止损止盈单，则需要平仓左边的仓位
	if rightPosition.Qty == 0 {
		if (this.StopLossOrdersRight != nil && this.StopLossOrdersRight.Exist(symbol)) ||
			(this.TakeProfitOrdersRight != nil && this.TakeProfitOrdersRight.Exist(symbol)) {
			this.Infof("right position is 0, but has stop loss or take profit order, close left position: %s", symbol)
			this.ClosePositions([]string{symbolLeft}, 1)
			return
		}
	}

	origPosition := rightPosition.Qty
	this.Infof("syncing right position: %s, %s, %v => %v", instrumentType, symbol, origPosition, targetPostion)

	if this.ExchangeRight.GetName() == exchange.Hyperliquid {
		// 设置全仓模式
		if err := this.ExchangeRight.SetMarginMode(instrumentType, symbol, exchange.Cross); err != nil {
			if !strings.Contains(err.Error(), "No need to change") {
				this.ErrorMsgf("Hyperliquid set margin mode failed: %s", err)
				return
			}
		}
	}

	symbolCode, err := this.GetSymbolCodeRight(symbol)
	if err != nil {
		this.Errorf("sync right position error, get symbol code failed: %s", err)
		return
	}

	lock, _ := this.SymbolLocks.LoadOrStore(symbolCode.String(), &SymbolLock{
		SymbolCode: symbolCode.String(),
	})
	if lock.IsAtCapNow() && isOpen {
		this.Warnf("open interest is at cap, skip syncing open position: %s", symbol)
		return
	}

	leverage, err := this.GetLeverage(symbol, true)
	if err != nil {
		this.ErrorMsgf("sync right position, get leverage failed: %s", err)
		return
	}
	err = this.ExchangeRight.SetLeverage(instrumentType, symbol, exchange.Cross, exchange.UnknownPositionSide, leverage)
	if err != nil {
		this.ErrorMsgf("sync right position, set leverage failed: %s", err)
		return
	}

	qty := targetPostion - rightPosition.Qty
	deltaRatio = math.Abs(qty) / math.Abs(targetPostion) // 计算仓位差异比例

	var side exchange.OrderSide
	if qty > 0 {
		side = exchange.OrderSideBuy
	} else {
		side = exchange.OrderSideSell
	}

	orderArgs := exchange.CreateOrderArgs{
		InstrumentType: instrumentType,
		Symbol:         symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCross,
		TimeInForce:    exchange.GTC,
		Qty:            this.ExchangeRight.RoundQty(instrumentType, symbol, math.Abs(qty)),
		Side:           side,
	}
	if targetPostion == 0 {
		orderArgs.ReduceOnly = true
	}

	if isOpen && !this.isLeftPositionChanged(symbolLeft) {
		// 左边没有仓位变动，则右边不需要加仓，将左边的仓位减掉
		this.SendMsgf("left position is not changed, reduce left position: %s", symbolLeft)
		reduceRatio := math.Abs(qty / targetPostion)
		err = this.ReducePositions(reduceRatio, symbolLeft)
		if err != nil {
			this.ErrorMsgf("sync right position, reduce positions failed: %s", err)
		}
		return
	}

	order, err := this.ExchangeRight.CreateOrder(orderArgs)
	if err != nil {
		this.ErrorMsgf("sync position failed: %s, %s, %v => %v, create order failed: %s", instrumentType, symbol, origPosition, targetPostion, err)

		// 如果出现同步失败，锁定自动止盈 2 分钟
		this.autoTakeProfitCoolingUntil = time.Now().Add(2 * time.Minute)

		if strings.Contains(err.Error(), "open interest is at cap") {
			symbolLeft, err := this.ConvertSymbol(symbol, false)
			if err != nil {
				this.ErrorMsgf("convert symbol failed: %s", err)
				return
			}

			lock, _ := this.SymbolLocks.LoadOrStore(symbolCode.String(), &SymbolLock{
				SymbolCode: symbolCode.String(),
			})
			lock.AtCapUntil = utils.Ptr(time.Now().Add(AtCapDuration))

			this.SendMsgf("open interest is at cap, reduce left positions: %s - %s", instrumentType, symbolLeft)
			reduceRatio := math.Abs(qty / targetPostion)
			err = this.ReducePositions(reduceRatio, symbolLeft)
			if err != nil {
				this.ErrorMsgf("sync right position, reduce positions failed: %s", err)
				return
			}
		}

		return
	}

	code, err := this.GetSymbolCodeRight(symbol)
	if err != nil {
		this.Errorf("sync right position error, get symbol code failed: %s", err)
		return
	}
	this.controller.AppendOrderRecord(order, this.ControllerIDRight, this.ExchangeRight.GetName(), code)

	tryCount := 10
	for i := 0; i < tryCount; i++ {
		time.Sleep(2 * time.Second)

		// 检查是否到目标仓位
		rightPosition, err = this.ExchangeRight.GetPosition(instrumentType, symbol, exchange.UnknownPositionSide, false)
		if err != nil {
			this.ErrorMsgf("get right position failed: %s", err)
			continue
		}

		if rightPosition.Qty == targetPostion {
			this.SendMsgf("Right position is synced: %s - %s, %v => %v", instrumentType, symbol, origPosition, targetPostion)
			syncPostionSuccess = true
			break
		}

		if i == tryCount-1 {
			this.ErrorMsgf("sync position failed, position is not synced: [%s] %s - %s, current: %v, target: %v", this.ExchangeRight.GetName(), instrumentType, symbol, rightPosition.Qty, targetPostion)
			break
		} else {
			this.Warnf("check position failed, retrying: %s, %s, current: %v, target: %v", instrumentType, symbol, rightPosition.Qty, targetPostion)
		}
	}
}

func (this *CrossExchangeArbitrager) isLeftPositionChanged(symbol string) bool {
	// 检查左侧仓位是否有变化
	if len(this.PositionMonitors) < 3 {
		return true
	}
	positionsLeft := []*exchange.Position{}
	// PositionMonitors 里最后 3 次仓位没有变化，则认为没有变化
	for i := len(this.PositionMonitors) - 1; i >= len(this.PositionMonitors)-3; i-- {
		positionSnapshot := this.PositionMonitors[i]
		for _, position := range positionSnapshot.PositionsLeft {
			if position.Symbol == symbol {
				positionsLeft = append(positionsLeft, position)
			}
		}
	}
	if len(positionsLeft) < 3 {
		return true
	}
	if positionsLeft[0].Qty == positionsLeft[1].Qty && positionsLeft[1].Qty == positionsLeft[2].Qty {
		return false
	}
	return true
}

/* 开仓，仅在左侧执行，右侧通过同步仓位达到一致
 */

func (this *CrossExchangeArbitrager) OpenPosition(longOrShort string, symbolCodeStr string, qtyStr string, splits int) (er error) {
	symbolCode, orderArgs, err := this.GetCreateOrderArgs(longOrShort, symbolCodeStr, qtyStr)
	if err != nil {
		er = fmt.Errorf("get create order args failed: %s", err)
		return
	}
	if symbolCode.IsFuture() {
		positions, err := this.ExchangeLeft.GetPositions(symbolCode.InstrumentType(), orderArgs.Symbol, false)
		if err != nil {
			er = fmt.Errorf("get positions failed, instrumentType: %s, symbol: %s, error: %s", symbolCode.InstrumentType(), orderArgs.Symbol, err)
			return
		}
		for _, position := range positions {
			if orderArgs.Side == exchange.OrderSideBuy && position.Side == exchange.PositionSideShort {
				orderArgs.ReduceOnly = true
				break
			} else if orderArgs.Side == exchange.OrderSideSell && position.Side == exchange.PositionSideLong {
				orderArgs.ReduceOnly = true
				break
			}
		}
	}

	leverage, err := this.GetLeverage(orderArgs.Symbol, false)
	if err != nil {
		this.ErrorMsgf("open position, get leverage failed: %s", err)
		return
	}
	err = this.ExchangeLeft.SetLeverage(orderArgs.InstrumentType, orderArgs.Symbol, exchange.Cross, exchange.UnknownPositionSide, leverage)
	if err != nil {
		this.ErrorMsgf("open position, set leverage failed: %s", err)
		return
	}

	successSplits, failedSplits, err := this.splitOrder(this.ExchangeLeft, symbolCode, orderArgs, splits, false, func(successOrders []*exchange.Order) {
		for _, order := range successOrders {
			this.controller.AppendOrderRecord(order, this.ControllerIDLeft, this.ExchangeLeft.GetName(), symbolCode)
		}
	})
	if err != nil {
		er = fmt.Errorf("split order failed: %s", err)
		return
	}
	if failedSplits > 0 {
		this.ErrorMsgf("open position partially failed: %s, %s, %d/%d", this.ExchangeLeft.GetName(), symbolCodeStr, failedSplits, failedSplits+successSplits)
	}

	return
}

// 平仓某些品种，暂时仅在 ExchangeLeft 上操作
// TODO: 可能需要指定在哪一侧平仓，主要为了应对左侧交易所维护，但是还是需要平仓的情况
func (this *CrossExchangeArbitrager) ClosePositions(symbols []string, splits int) {
	this.Infof("closing positions: %v", symbols)

	positions, err := this.ExchangeLeft.GetPositions(exchange.USDXMarginedFutures, "", false)
	if err != nil {
		this.ErrorMsgf("获取持仓失败: %s", err)
		return
	}
	if len(positions) == 0 {
		return
	}
	closedSymbols := []string{}
	erroredSymbols := []string{}
	for _, position := range positions {
		if slices.Contains(symbols, position.Symbol) {
			successSplits, failedSplits, err := this.closePosition(position, splits)
			if err != nil {
				this.Errorf("close position failed: %s", err)
				erroredSymbols = append(erroredSymbols, position.Symbol)
				continue
			}
			if successSplits > 0 {
				closedSymbols = append(closedSymbols, position.Symbol)
			}
			if failedSplits > 0 {
				this.ErrorMsgf("部分仓位平仓失败: %s, 成功平仓数量: %d/%d", position.Symbol, successSplits, successSplits+failedSplits)
			}
		}
	}
	// 把没有平仓的仓位都放到 erroredSymbols 中
	// TODO: 看起来有 bug
	// diffSymbols := utils.SliceSubtract(symbols, closedSymbols)
	// extraFailedSymbols := utils.SliceSubtract(diffSymbols, erroredSymbols)
	// erroredSymbols = append(erroredSymbols, extraFailedSymbols...)

	if len(closedSymbols) > 0 {
		this.SendMsgf("跨交易所套利机 %s 已平仓仓位: %s", this.GetAliasOrID(), strings.Join(closedSymbols, ","))
	}
	if len(erroredSymbols) > 0 {
		this.ErrorMsgf("跨交易所套利机 %s 平仓失败仓位: %s", this.GetAliasOrID(), strings.Join(erroredSymbols, ","))
	}
}

func (this *CrossExchangeArbitrager) closePosition(position *exchange.Position, splits int) (successSplits, failedSplits int, er error) {
	symbolCode, err := this.GetSymbolCodeLeft(position.Symbol)
	if err != nil {
		er = fmt.Errorf("get symbol code left failed: %s", err)
		return
	}

	lock, _ := this.SymbolLocks.LoadOrStore(symbolCode.String(), &SymbolLock{
		SymbolCode: symbolCode.String(),
	})
	if !lock.ClosingPosition.TryLock() {
		er = fmt.Errorf("closing position try lock failed, symbol: %s", position.Symbol)
		return
	}
	defer lock.ClosingPosition.Unlock()

	side := exchange.OrderSideBuy
	if position.Qty > 0 {
		side = exchange.OrderSideSell
	} else if position.Qty == 0 {
		return
	}
	orderArgs := exchange.CreateOrderArgs{
		InstrumentType: position.InstrumentType,
		Symbol:         position.Symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCross,
		Qty:            math.Abs(position.Qty),
		TimeInForce:    exchange.GTC,
		Side:           side,
		ReduceOnly:     true,
	}
	return this.splitOrder(this.ExchangeLeft, symbolCode, orderArgs, splits, false, func(successOrders []*exchange.Order) {
		for _, order := range successOrders {
			this.controller.AppendOrderRecord(order, this.ControllerIDLeft, this.ExchangeLeft.GetName(), symbolCode)
		}
	})
}

// 根据比例进行减仓操作，只需要在左侧交易所操作；即使左侧交易所保证金率充足，也会通过右侧交易所的保证金率算出安全的减仓比例，然后在左侧减仓
// 但是，当左侧交易所因为系统维护，无法减仓的时候，唯一的手段就是在右侧交易所平仓了
func (this *CrossExchangeArbitrager) ReducePositions(ratio float64, specificSymbols ...string) (er error) {
	// 减仓时，需要锁住 reduceMutex，防止同时操作，每个品种本身也有锁，但是为了防止太频繁获取仓位，所以需要再锁一次
	if !this.mutexes.reduceMutex.TryLock() {
		return fmt.Errorf("reduce positions in progress")
	}
	defer func() {
		this.mutexes.reduceMutex.Unlock()
		this.SetMonitorRelaxAtTime(time.Now().Add(LoopInterval))
	}()
	this.Infof("reducing positions: %v", ratio)

	if ratio <= 0 || ratio > 1 {
		return fmt.Errorf("invalid reduce ratio: %v", ratio)
	}

	positions, err := this.ExchangeLeft.GetPositions(exchange.USDXMarginedFutures, "", false)
	if err != nil {
		return fmt.Errorf("get positions failed: %s", err)
	}

	for _, position := range positions {
		if position.Qty == 0 {
			continue
		}

		if len(specificSymbols) > 0 && !utils.SliceContains(specificSymbols, position.Symbol) {
			continue
		}
		reducedQty, err := this.reducePosition(position, ratio)
		if err != nil {
			this.ErrorMsgf("reduce position failed, symbol: %s, reduceQty: %v, error: %s", position.Symbol, reducedQty, err)
			continue // 继续减仓下一个仓位
		}
		if reducedQty == 0 {
			this.Infof("reduce position qty is 0, symbol: %s", position.Symbol)
			continue
		}
		this.SendMsgf("减仓订单提交成功: %s - %s, %v", position.InstrumentType, position.Symbol, reducedQty)
	}
	return
}

func (this *CrossExchangeArbitrager) reducePosition(position *exchange.Position, ratio float64) (reducedQty float64, er error) {
	symbolCode, err := this.GetSymbolCodeLeft(position.Symbol)

	if err != nil {
		er = fmt.Errorf("get symbol code failed, symbol: %s, error: %s", position.Symbol, err)
		return
	}
	symbolCodeStr := symbolCode.String()
	lock, _ := this.SymbolLocks.LoadOrStore(symbolCodeStr, &SymbolLock{
		SymbolCode: symbolCodeStr,
	})
	if !lock.ReducingPosition.TryLock() {
		er = fmt.Errorf("reducing position try lock failed, symbol: %s", position.Symbol)
		return
	}
	defer lock.ReducingPosition.Unlock()

	reducedQty = math.Abs(position.Qty * ratio)
	reducedQty = this.ExchangeLeft.RoundQty(exchange.USDXMarginedFutures, position.Symbol, reducedQty)
	if reducedQty == 0 {
		return
	}
	side := exchange.OrderSideBuy
	if position.Qty > 0 {
		side = exchange.OrderSideSell
	}
	orderArgs := exchange.CreateOrderArgs{
		InstrumentType: position.InstrumentType,
		Symbol:         position.Symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCross,
		Qty:            reducedQty,
		TimeInForce:    exchange.GTC,
		Side:           side,
		ReduceOnly:     true,
	}
	order, err := this.ExchangeLeft.CreateOrder(orderArgs)
	if err != nil {
		er = fmt.Errorf("create order failed: %s", err)
		return
	}

	this.controller.AppendOrderRecord(order, this.ControllerIDLeft, this.ExchangeLeft.GetName(), symbolCode)
	return
}

func (this *CrossExchangeArbitrager) Rotate(symbol string, ratio float64, splits int, isRight bool) error {
	code, err := this.GetSymbolCode(symbol, isRight)
	if err != nil {
		return fmt.Errorf("get symbol code failed: %s", err)
	}
	ex := this.ExchangeLeft
	controllerID := this.ControllerIDLeft
	if isRight {
		ex = this.ExchangeRight
		controllerID = this.ControllerIDRight
	}
	lock, _ := this.SymbolLocks.Load(code.String())
	if lock == nil {
		return fmt.Errorf("symbol lock not found: %s", code.String())
	}

	lock.SyncingPosition.Lock()
	lock.ClosingPosition.Lock()
	lock.RatatingPosition.Lock()
	defer lock.SyncingPosition.Unlock()
	defer lock.ClosingPosition.Unlock()
	defer lock.RatatingPosition.Unlock()

	position, err := ex.GetPosition(exchange.USDXMarginedFutures, symbol, exchange.UnknownPositionSide, false)
	if err != nil {
		return fmt.Errorf("get position failed: %s", err)
	}

	if position.Qty == 0 {
		return fmt.Errorf("position is 0")
	}

	reduceQty := ex.RoundQty(exchange.USDXMarginedFutures, symbol, math.Abs(position.Qty*ratio))
	closeSide := exchange.OrderSideBuy
	openSide := exchange.OrderSideSell
	if position.Qty > 0 {
		closeSide = exchange.OrderSideSell
		openSide = exchange.OrderSideBuy
	}

	orderArgs := exchange.CreateOrderArgs{
		InstrumentType: exchange.USDXMarginedFutures,
		Symbol:         symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCross,
		Qty:            reduceQty,
		TimeInForce:    exchange.GTC,
		Side:           closeSide,
		ReduceOnly:     true,
	}

	successSplits, failedSplits, err := this.splitOrder(ex, code, orderArgs, splits, false, func(successOrders []*exchange.Order) {
		for _, order := range successOrders {
			this.controller.AppendOrderRecord(order, controllerID, ex.GetName(), code)
		}
	})
	if err != nil {
		return fmt.Errorf("split order failed: %s", err)
	}
	if failedSplits > 0 {
		this.ErrorMsgf("rotate close order partially failed: %s, %s, %d/%d", ex.GetName(), symbol, failedSplits, successSplits+failedSplits)
	}

	orderArgs.Side = openSide
	orderArgs.ReduceOnly = false

	successSplits, failedSplits, err = this.splitOrder(ex, code, orderArgs, splits, false, func(successOrders []*exchange.Order) {
		for _, order := range successOrders {
			this.controller.AppendOrderRecord(order, controllerID, ex.GetName(), code)
		}
	})
	if err != nil {
		return fmt.Errorf("split order failed: %s", err)
	}
	if failedSplits > 0 {
		this.ErrorMsgf("rotate open order partially failed: %s, %s, %d/%d", ex.GetName(), symbol, failedSplits, successSplits+failedSplits)
	}

	return nil
}

// 拆单，拆单时，如果拆单数量大于 1，则需要等待拆单间隔时间
// 如果是刷量交易，则需要额外创建一个反向的拆单订单；也就是说，执行两个订单，其他情况执行一个订单
func (this *CrossExchangeArbitrager) splitOrder(ex exchange.Exchange, code *exchange.SymbolCode, orderArgs exchange.CreateOrderArgs, splits int, isWashOrder bool, callback func(successOrders []*exchange.Order)) (successSplits, failedSplits int, err error) {
	orderSplitValue := this.GetParam(ParamTypeSplitValue, code.String()).(float64)

	successOrders := []*exchange.Order{}
	if splits == -1 {
		orderValue := orderArgs.QuoteQty
		if orderValue == 0 {
			lastPrice, err := ex.GetLastPrice(orderArgs.InstrumentType, orderArgs.Symbol, false)
			if err != nil {
				return 0, 0, fmt.Errorf("get last price failed: %s", err)
			}
			orderValue, _ = ex.Qty2Size(exchange.USDXMarginedFutures, orderArgs.Symbol, lastPrice, orderArgs.Qty)
		}
		splits = int(math.Ceil(orderValue / orderSplitValue))
	}

	orderArgsArr := []exchange.CreateOrderArgs{}
	if splits > 1 {
		var splitQty, splitQuoteQty float64
		var lastQty, lastQuoteQty float64
		if orderArgs.Qty > 0 {
			splitQty = orderArgs.Qty / float64(splits)
			splitQty = ex.FloorQty(exchange.USDXMarginedFutures, orderArgs.Symbol, splitQty)
			lastQty = orderArgs.Qty - splitQty*float64(splits-1)
			lastQty = ex.RoundQty(exchange.USDXMarginedFutures, orderArgs.Symbol, lastQty)
		} else {
			splitQuoteQty = orderArgs.QuoteQty / float64(splits)
			splitQuoteQty = math.Floor(splitQuoteQty)
			lastQuoteQty = orderArgs.QuoteQty - splitQuoteQty*float64(splits-1)
			lastQuoteQty = math.Floor(lastQuoteQty)
		}

		for i := 0; i < splits-1; i++ {
			splitOrderArgs := orderArgs
			if splitOrderArgs.Qty > 0 && splitQty > 0 {
				splitOrderArgs.Qty = splitQty
				orderArgsArr = append(orderArgsArr, splitOrderArgs)
			} else if splitOrderArgs.QuoteQty > 0 && splitQuoteQty > 0 {
				splitOrderArgs.QuoteQty = splitQuoteQty
				orderArgsArr = append(orderArgsArr, splitOrderArgs)
			}
		}
		lastOrderArgs := orderArgs
		if lastOrderArgs.Qty > 0 && lastQty > 0 {
			lastOrderArgs.Qty = lastQty
			orderArgsArr = append(orderArgsArr, lastOrderArgs)
		} else if lastOrderArgs.QuoteQty > 0 && lastQuoteQty > 0 {
			lastOrderArgs.QuoteQty = lastQuoteQty
			orderArgsArr = append(orderArgsArr, lastOrderArgs)
		}
	} else {
		orderArgsArr = []exchange.CreateOrderArgs{orderArgs}
	}

	for i, orderArgs := range orderArgsArr {
		if !orderArgs.ReduceOnly {
			// 检查当前品种是否在 AtCap 状态
			lock, _ := this.SymbolLocks.Load(code.String())
			if lock != nil && lock.IsAtCapNow() {
				this.controller.SendMsgf("open interest is at cap, stop creating open order: %s", code)
				return
			}
		}

		order, err := ex.CreateOrder(orderArgs)
		if err != nil {
			this.Errorf("split order, split %d, create order failed: %s", i+1, err)
			failedSplits++
			continue
		}
		successOrders = append(successOrders, order)

		// 如果是刷量交易，则需要额外创建一个反向的拆单订单；主要是为了尽量减小一组刷量订单的价差
		if isWashOrder {
			if orderArgs.Side == exchange.OrderSideBuy {
				orderArgs.Side = exchange.OrderSideSell
			} else {
				orderArgs.Side = exchange.OrderSideBuy
			}
			order, err := ex.CreateOrder(orderArgs)
			if err != nil {
				this.Errorf("split order, split %d, create wash order failed: %s", i+1, err)
				failedSplits++
				continue
			}
			successOrders = append(successOrders, order)
		}

		successSplits++
		if splits > 1 {
			time.Sleep(time.Duration(this.Options.SplitInterval) * time.Second)
		}
	}
	if callback != nil {
		callback(successOrders)
	}
	return
}

func (this *CrossExchangeArbitrager) Wash(symbol string, qty, quoteQty float64, splits int, isRight bool) error {
	ex := this.ExchangeLeft
	symbolCode, err := this.GetSymbolCode(symbol, isRight)
	if err != nil {
		return fmt.Errorf("get symbol code failed: %s", err)
	}
	controllerID := this.ControllerIDLeft
	if isRight {
		ex = this.ExchangeRight
		controllerID = this.ControllerIDRight
	}
	symbolItem := &exchange.SymbolItem{
		Code:   symbolCode,
		Symbol: symbol,
	}
	leverage, err := this.GetLeverage(symbolItem.Symbol, isRight)
	if err != nil {
		return fmt.Errorf("get leverage failed: %s", err)
	}
	err = ex.SetLeverage(symbolItem.Code.InstrumentType(), symbolItem.Symbol, exchange.Cross, exchange.UnknownPositionSide, leverage)
	if err != nil {
		return fmt.Errorf("set leverage failed: %s", err)
	}
	return this._wash(ex, symbolItem, qty, quoteQty, splits, func(successOrders []*exchange.Order) {
		for _, order := range successOrders {
			this.controller.AppendOrderRecord(order, controllerID, ex.GetName(), symbolItem.Code)
		}
	})
}

func (this *CrossExchangeArbitrager) _wash(ex exchange.Exchange, symbolItem *exchange.SymbolItem, qty, quoteQty float64, splits int, callback func(successOrders []*exchange.Order)) error {
	lock, _ := this.SymbolLocks.LoadOrStore(symbolItem.Code.Code, &SymbolLock{
		SymbolCode: symbolItem.Code.Code,
	})
	if lock == nil {
		return fmt.Errorf("symbol lock not found: %s", symbolItem.Code.Code)
	}

	lock.SyncingPosition.Lock()
	lock.ClosingPosition.Lock()
	defer lock.SyncingPosition.Unlock()
	defer lock.ClosingPosition.Unlock()

	orderArgs := exchange.CreateOrderArgs{
		InstrumentType: exchange.USDXMarginedFutures,
		Symbol:         symbolItem.Symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCross,
		Qty:            qty,
		QuoteQty:       quoteQty,
		TimeInForce:    exchange.GTC,
		Side:           exchange.OrderSideBuy,
	}

	successSplits, failedSplits, err := this.splitOrder(ex, symbolItem.Code, orderArgs, splits, true, callback)
	if err != nil {
		return fmt.Errorf("split order failed: %s", err)
	}
	if failedSplits > 0 {
		this.ErrorMsgf("wash order partially failed: %s, %s, %d/%d", ex.GetName(), symbolItem.Symbol, failedSplits, successSplits+failedSplits)
	}

	return nil
}

func (this *CrossExchangeArbitrager) GetLeverage(symbol string, isRight bool) (leverage float64, err error) {
	var ex exchange.Exchange
	if isRight {
		ex = this.ExchangeRight
	} else {
		ex = this.ExchangeLeft
	}

	enableMaxLeverage := false
	if ex.GetName() == exchange.Hyperliquid {
		enableMaxLeverage = true
	}
	if enableMaxLeverage {
		return ex.MaxLeverage(exchange.USDXMarginedFutures, symbol, 0), nil
	} else {
		maxLeverage := ex.MaxLeverage(exchange.USDXMarginedFutures, symbol, 0)
		code, err := this.GetSymbolCode(symbol, isRight)
		if err != nil {
			return 0, fmt.Errorf("get symbol code failed: %s", err)
		}

		paramLeverage := this.GetParam(ParamTypeLeverageLeft, code.String()).(float64)
		if isRight {
			paramLeverage = this.GetParam(ParamTypeLeverageRight, code.String()).(float64)
		}
		return math.Min(maxLeverage, paramLeverage), nil
	}
}

// 如果价差是有利的（开空的仓位价格低于开多的）并且超过配置，则自动减仓 2%
func (this *CrossExchangeArbitrager) AutoTakeProfit() {
	defer func() {
		if r := recover(); r != nil {
			this.AlertMsgf("recovered from panic in AutoTakeProfit: %v", r)
		}
	}()

	if this.Options.TakeProfitPriceDiff == 0 {
		this.Infof("take profit price diff is 0, skip auto take profit")
		return
	}
	if time.Now().Before(this.autoTakeProfitCoolingUntil) {
		this.Infof("auto take profit is locked until %s, skip this round", this.autoTakeProfitCoolingUntil.Format(time.RFC3339))
		return
	}
	this.AccountSnapshot.SymbolContextLeft.Range(func(symbolLeft string, symbolContextLeft *SymbolContext) bool {
		if symbolContextLeft == nil {
			return true
		}
		symbolRight, err := this.ConvertSymbol(symbolLeft, true)
		if err != nil {
			this.ErrorMsgf("convert symbol failed: %s", err)
			return true
		}
		symbolContextRight, ok := this.AccountSnapshot.SymbolContextRight.Load(symbolRight)
		if !ok {
			this.ErrorMsgf("symbol context not found: %s", symbolRight)
			return true
		}
		if symbolContextRight == nil {
			return true
		}

		// check if symbol context is up to date
		if !symbolContextLeft.IsUpToDate() || !symbolContextRight.IsUpToDate() {
			return true
		}

		if symbolContextLeft.PositionValue == 0 || symbolContextRight.PositionValue == 0 {
			return true
		}

		var longPrice, shortPrice float64
		if symbolContextLeft.PositionValue > 0 {
			longPrice = symbolContextLeft.LastPrice
			shortPrice = symbolContextRight.LastPrice
		} else {
			longPrice = symbolContextRight.LastPrice
			shortPrice = symbolContextLeft.LastPrice
		}
		if longPrice == 0 || shortPrice == 0 {
			return true
		}

		code, err := this.GetSymbolCodeRight(symbolRight)
		if err != nil {
			this.ErrorMsgf("auto take profit, get symbol code failed: %s", err)
			return true
		}

		if shortPrice < longPrice {
			priceDiff := math.Abs(shortPrice-longPrice) / longPrice
			if priceDiff > this.GetParam(ParamTypeTakeProfitPriceDiff, code.String()).(float64) {
				this.Infof("auto take profit: %s, %s, %v", symbolLeft, symbolRight, priceDiff)
				this.SendMsgf("auto take profit: symbol: %s - %s, price diff: %.2f%%", symbolLeft, symbolRight, priceDiff*100)
				reduceRatio := 0.02
				err = this.ReducePositions(reduceRatio, symbolLeft)
				if err != nil {
					this.ErrorMsgf("auto take profit, reduce positions failed: %s", err)
				}
				this.autoTakeProfitCoolingUntil = time.Now().Add(10 * time.Second)
			}
		}

		return true
	})
}

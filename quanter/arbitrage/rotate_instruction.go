package arbitrage

import (
	"errors"
	"fmt"
	"math"
	"strings"
	"sync"
	"time"

	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/exchange"

	"github.com/stevedomin/termtable"
)

type RotateInstruction struct {
	Instruction
}

func NewRotateInstruction(arbitrager *ArbitragerController, isForce bool, fromFutureCode *exchange.SymbolCode, toFutureCode *exchange.SymbolCode, qty float64, percentage float64, targetBasisRatio float64, configStr string) (*RotateInstruction, error) {
	if fromFutureCode.IsWildcard() || toFutureCode.IsWildcard() {
		return nil, errors.New("wildcard symbol code not allowed")
	}
	if arbitrager.Executer == nil {
		return nil, errors.New("exchange not initialized, please wait")
	}
	if fromFuturePairs, err := arbitrager.Exchange.TranslateSymbolCode(fromFutureCode); len(fromFuturePairs) == 1 {
		if toFuturePairs, err := arbitrager.Exchange.TranslateSymbolCode(toFutureCode); len(toFuturePairs) == 1 {
			fromPair := NewArbiPair(fromFuturePairs[0], false, 0)
			toPair := NewArbiPair(toFuturePairs[0], true, 0)
			_, fromFutureSymbol := fromPair.Left.Symbol, fromPair.Right.Symbol

			qtySnapshot := 0.0
			availableQty := 0.0
			// 此处获取数量，仅仅是为了复制 qtySnapshot，并不检查这个数量
			if _, q, err := arbitrager.Exchange.GetHoldingQty(arbitrager.FutureInstrumentType(), fromFutureSymbol); err != nil {
				return nil, fmt.Errorf("check future available qty error: %s", err)
			} else {
				availableQty = q
			}
			qtySnapshot = availableQty

			if qty > 0 && percentage == 0 {
				percentage = qty / math.Abs(availableQty)
			} else if qty == 0 && percentage > 0 {
				qty = math.Abs(availableQty) * percentage
			} else {
				return nil, fmt.Errorf("can not set qty and percentage at the same time")
			}

			if qty == 0 {
				return nil, fmt.Errorf("qty can not be 0")
			}
			if qty < 0 {
				return nil, fmt.Errorf("qty can not be < 0")
			}

			direction := ArbiDirectionRotate

			// 如果目标溢价率低于当前溢价率，可能是误操作，返回错误
			// 使用 TargetBasisRatioTolerance 主要是因为这个判断不能过于敏感，否则正常情况的输入也很容易报错
			refBasisRatio := 0.0     // from 和 to 合约的溢价率差
			fromBasisSnapshot := 0.0 // from 交易对本身的溢价率
			toBasisSnapshot := 0.0   // to 交易对本身的溢价率
			if fbr, err := arbitrager.Executer.GetBasisRatio(fromPair); err != nil {
				return nil, fmt.Errorf("get from basis ratio error: %s", err)
			} else {
				fromBasisSnapshot = fbr
				fromPair.BasisSnapshot = fromBasisSnapshot
				if tbr, err := arbitrager.Executer.GetBasisRatio(toPair); err != nil {
					return nil, fmt.Errorf("get to basis ratio error: %s", err)
				} else {
					toBasisSnapshot = tbr
					toPair.BasisSnapshot = toBasisSnapshot
					refBasisRatio = tbr - fbr
					if targetBasisRatio == 0.0 {
						targetBasisRatio = tbr - fbr
					} else {
						if !CheckBasisTolerance(direction, targetBasisRatio, refBasisRatio, TargetBasisRatioTolerance) {
							return nil, fmt.Errorf("target basis ratio %.2f %% < ref basis ratio %.2f %%, greater than %.2f %%", targetBasisRatio*100, refBasisRatio*100, TargetBasisRatioTolerance*100)
						}
					}
				}
			}

			instructionID := arbitrager.NewInstructionID(direction, isForce)
			// 在 FreeOrder 模式下，可能会出现 percentage = +Inf 的情况
			// 而 +Inf 无法被保存到 storage json 中，因此，强制将其转换为 100%
			if percentage > 1 {
				percentage = 1
				arbitrager.Warnf("%s percentage > 100%%, reset it to 100%%", instructionID)
			}

			instruction := &RotateInstruction{
				Instruction: Instruction{
					arbitrager:     arbitrager,
					ID:             instructionID,
					RefID:          exchange.NewRandomID(),
					Direction:      direction,
					Pairs:          []*ArbiPair{fromPair, toPair},
					Percentage:     percentage,
					Qty:            qty,
					QtySnapshot:    qtySnapshot,
					Config:         arbitrager.Config.snapshotArbitragerConfig(),
					ConfigSnapshot: arbitrager.Config.snapshotArbitragerConfig(),
					Force:          isForce,
					mutex:          &sync.Mutex{},
				},
			}
			instruction.SetupMoveInstructionReport()

			if _, _, err := baseconfig.SetConfigWithString(instruction.Config, configStr); err != nil {
				return nil, err
			}
			instruction.TargetBasisRatio = targetBasisRatio
			instruction.RefBasisRatio = refBasisRatio
			return instruction, nil
		} else {
			return nil, fmt.Errorf("to future code %s translate to no symbols or more than one symbol, error: %s", toFutureCode, err)
		}
	} else {
		return nil, fmt.Errorf("source future code %s translate to no symbols or more than one symbol, error: %s", fromFutureCode, err)
	}
}

func (this *RotateInstruction) RenderRequest() string {
	t := NewTable()
	var row []string
	t.SetHeader(SimpleInstructionRequestHeader)
	row = this.GetRequestRow(false)
	t.AddRow(row)
	request := t.Render()
	if configDiff, err := baseconfig.RenderConfigDiff(this.Config, this.ConfigSnapshot, true); err != nil {
		return fmt.Sprintf("打印配置文件更改出现错误：%s", err)
	} else {
		if configDiff != "" {
			return fmt.Sprintf("%s\n\n%s", request, configDiff)
		} else {
			return request
		}
	}
}

func (this *RotateInstruction) GetRequestRow(isDetail bool) (row []string) {
	row = []string{
		this.ID,
		this.arbitrager.Exchange.GetName(),
		this.GetFromPair().GetBuyItem().Code.Code,
		this.GetToPair().GetSellItem().Code.Code,
		fmt.Sprintf("%.2f%%", this.Percentage*100),
		fmt.Sprintf("%s cnt", this.FormatFutureQty(this.GetFromPair().GetBuyItem().Symbol, this.Qty)),
		fmt.Sprintf("%+.2f%%", 100*this.TargetBasisRatio),
		fmt.Sprintf("%+.2f%%", 100*this.RefBasisRatio),
	}
	if isDetail {
		row = append([]string{this.arbitrager.ID}, row...)
	}
	return row
}

func (this *RotateInstruction) RenderStatus(isDetail bool) string {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	if isDetail {
		t.SetHeader(DetailMoveInstructionStatusHeader)
	} else {
		t.SetHeader(SimpleMoveInstructionStatusHeader)
	}

	t.AddRow(this.GetStatusRow(isDetail))
	return t.Render()
}

func (this *RotateInstruction) GetStatusRow(isDetail bool) (row []string) {
	endTimeStr := "--"
	if this.IsEnded() {
		endTimeStr = this.Report.EndTime.Format("2006-01-02 15:04:05")
	}
	row = []string{}

	if isDetail {
		row = append(row, this.ID)
		row = append(row, this.arbitrager.ID)
		row = append(row, this.arbitrager.Exchange.GetName())
		row = append(row, fmt.Sprintf("%.2f", this.Qty))
		row = append(row, fmt.Sprintf("%.2f%%", this.GetFinishProgress()*100))
		row = append(row, this.GetFutureTradeString(true))
		row = append(row, this.GetFutureTradeString(false))
		row = append(row, fmt.Sprintf("%+.2f%%", this.TargetBasisRatio*100))
		row = append(row, fmt.Sprintf("%+.2f%%", this.GetBasisRatio()*100))
		row = append(row, fmt.Sprintf("%+.2f%%", (this.GetBasisRatio()-this.TargetBasisRatio)*100))
		row = append(row, string(this.Report.CancelReason))
		if this.IsStarted() {
			row = append(row, this.Report.StartTime.Format("2006-01-02 15:04:05"))
		} else {
			row = append(row, "--")
		}
		row = append(row, endTimeStr)
		row = append(row, this.FormatDuration())
		row = append(row, fmt.Sprintf("%.0f USDT", this.Report.AccountWorthBefore))
		row = append(row, fmt.Sprintf("%.0f USDT", this.Report.AccountWorthAfter))
		row = append(row, this.GetComment())
	} else {
		row = append(row, this.ID)
		row = append(row, this.GetFromPair().GetBuyItem().Code.Code)
		row = append(row, this.GetToPair().GetSellItem().Code.Code)
		row = append(row, fmt.Sprintf("%.2f", this.Qty))
		row = append(row, fmt.Sprintf("%.2f%%", this.GetFinishProgress()*100))
		row = append(row, "-")
		row = append(row, fmt.Sprintf("%+.2f%%", this.GetBasisRatio()*100))
		row = append(row, fmt.Sprintf("%+.2f%%", (this.GetBasisRatio()-this.TargetBasisRatio)*100))
		row = append(row, this.FormatDuration())
		row = append(row, fmt.Sprintf("%.0f USDT", this.Report.AccountWorthAfter))
	}
	return row
}

func (this *RotateInstruction) GetFinishProgress() float64 {
	return this.GetQtyFilled() / math.Abs(this.Qty)
}

func (this *RotateInstruction) GetFutureTradeString(isFrom bool) string {
	execution := this.GetToExecution()
	pair := this.GetToPair()
	if isFrom {
		execution = this.GetFromExecution()
		pair = this.GetFromPair()
	}
	if isFrom {
		priceStr := this.formatPrice(this.arbitrager.FutureInstrumentType(), pair.GetBuyItem().Symbol, execution.BuyAvgPrice)
		qtyStr := this.FormatFutureQty(pair.GetBuyItem().Symbol, execution.BuyQty)
		return fmt.Sprintf("%s %s @%s", qtyStr, pair.GetBuyItem().Code.Code, priceStr)
	} else {
		priceStr := this.formatPrice(this.arbitrager.FutureInstrumentType(), pair.GetSellItem().Symbol, execution.SellAvgPrice)
		qtyStr := this.FormatFutureQty(pair.GetSellItem().Symbol, execution.SellQty)
		return fmt.Sprintf("-%s %s @%s", qtyStr, pair.GetSellItem().Code.Code, priceStr)
	}
}

func (this *RotateInstruction) GetBasisRatio() float64 {
	if this.GetToExecution().SellAvgPrice > 0 {
		return this.GetToExecution().SellAvgPrice/this.GetFromExecution().BuyAvgPrice - 1
	}
	return 0
}

func (this *RotateInstruction) FormatDuration() string {
	if this.Report.StartTime != nil {
		if this.IsEnded() {
			return fmtDuration(this.Report.EndTime.Sub(*this.Report.StartTime))
		} else {
			return fmtDuration(time.Since(*this.Report.StartTime))
		}
	} else {
		return "--"
	}
}

func (this *RotateInstruction) FormatRetryCounters() string {
	retryComments := []string{}
	for _, execution := range []*InstructionExecution{this.GetFromExecution(), this.GetToExecution()} {
		s := ""
		if execution.BuyRetryCounter > 0 {
			s += fmt.Sprintf("B^%d", execution.BuyRetryCounter)
		}
		if execution.TransferRetryCounter > 0 {
			s += fmt.Sprintf("T^%d", execution.TransferRetryCounter)
		}
		if execution.SellRetryCounter > 0 {
			s += fmt.Sprintf("S^%d", execution.SellRetryCounter)
		}
	}
	return strings.Join(retryComments, "\n")
}

func (this *RotateInstruction) GetComment() string {
	retryCounts := this.FormatRetryCounters()
	configDiff := ""
	if diff, err := baseconfig.RenderConfigDiff(this.Config, this.ConfigSnapshot, false); err != nil {
		this.Errorf("get config diff error: %s", err)
		configDiff = fmt.Sprintf("<ERROR!get config diff: %s>", err)
	} else {
		configDiff = diff
	}
	if configDiff != "" {
		return fmt.Sprintf("%s | %s", configDiff, retryCounts)
	} else {
		return retryCounts
	}
}

func (this *RotateInstruction) GetRelatedSymbolCodes() []*exchange.SymbolCode {
	return []*exchange.SymbolCode{this.GetFromPair().GetBuyItem().Code, this.GetToPair().GetSellItem().Code}
}

func (this *RotateInstruction) GetCloseSymbolCode() *exchange.SymbolCode {
	return this.GetFromPair().GetBuyItem().Code
}

func (this *RotateInstruction) GetArbiSymbolCode() *exchange.SymbolCode {
	return this.GetToPair().GetSellItem().Code
}

func (this *RotateInstruction) RenderOrderReport() string {
	t := NewTable()
	t.SetHeader([]string{"Split", "Order ID", "Symbol", "Qty", "Limit Price", "Exec Price", "Slippage", "Filled", "Duration"})

	for i := 0; i < 2; i++ {
		execution := this.GetToExecution()
		seperator := []string{"", "To", "", "", "", "", "", "", ""}
		orders := execution.SellOrders
		slippage := execution.SellSlippage
		side := "Sell"
		if i == 0 {
			execution = this.GetFromExecution()
			seperator = []string{"", "From", "", "", "", "", "", "", ""}
			orders = execution.BuyOrders
			slippage = execution.BuySlippage
			side = "Buy"
		}
		t.AddRow(seperator)

		for i, split := range orders {
			for _, o := range *split {
				row := this.getReportOrderRow(execution, o)
				row = append([]string{fmt.Sprintf("%s %d", side, i+1)}, row...)
				t.AddRow(row)
			}
		}

		t.AddRow([]string{
			"",
			"Slippage:",
			"",
			"",
			"",
			"",
			fmt.Sprintf("%.2f %%", slippage*100),
			"",
			"",
		})
	}

	return t.Render()
}

func (this *RotateInstruction) RenderDurationReport() string {
	t := NewTable()
	t.SetHeader([]string{"Type", "Duration", "Percentage"})

	for i := 0; i < 2; i++ {

		seperator := []string{"> To", "", ""}
		execution := this.GetToExecution()
		if i == 0 {
			execution = this.GetFromExecution()
			seperator = []string{"> From", "", ""}
		}

		t.AddRow(seperator)

		totalDuration := time.Duration(time.Millisecond * 0)
		for _, v := range execution.DurationStats {
			totalDuration += v
		}
		if total, found := execution.DurationStats[ExecutionDurationTypeTotal]; found {
			if total == 0 {
				execution.DurationStats[ExecutionDurationTypeTotal] = totalDuration
			}
		} else {
			execution.DurationStats[ExecutionDurationTypeTotal] = totalDuration
		}

		typeList := []ExecutionDurationType{
			ExecutionDurationTypeGetBasisRatio,
			ExecutionDurationTypeGetSpotPrice,
			ExecutionDurationTypeGetFuturePrice,
			ExecutionDurationTypeSubmitSpotOrder,
			ExecutionDurationTypeSubmitFutureOrder,
			ExecutionDurationTypeTransfer,
			ExecutionDurationTypeSplitWait,
			ExecutionDurationTypeBuyRetryInterval,
			ExecutionDurationTypeSellRetryInterval,
			ExecutionDurationTypeTransferRetryInterval,
			ExecutionDurationTypeBasisRatioRetryInterval,
			ExecutionDurationTypeBasisToleranceWait,
			ExecutionDurationTypePauseWait,
		}
		for _, ty := range typeList {
			ms := float64(execution.DurationStats[ty] / time.Millisecond)
			total := float64(totalDuration / time.Millisecond)
			t.AddRow([]string{
				string(ty),
				execution.DurationStats[ty].String(),
				fmt.Sprintf("%.2f %%", ms/total*100),
			})
		}
		t.AddRow([]string{
			string(ExecutionDurationTypeTotal),
			execution.DurationStats[ExecutionDurationTypeTotal].String(),
			"--",
		})
	}
	return t.Render()
}

func (this *RotateInstruction) UpdateReportOrderPriceSlippage() {
	var buyType, sellType exchange.InstrumentType
	var buySymbol, sellSymbol string
	pair := this.GetFromPair()
	buyType = pair.GetBuyItem().Code.InstrumentType()
	buySymbol = pair.GetBuyItem().Symbol
	sellType = pair.GetSellItem().Code.InstrumentType()
	sellSymbol = pair.GetSellItem().Symbol
	this.GetFromExecution().UpdateOrderPriceSlippage(this.arbitrager, buyType, buySymbol, sellType, sellSymbol)

	pair = this.GetToPair()
	buyType = pair.GetBuyItem().Code.InstrumentType()
	buySymbol = pair.GetBuyItem().Symbol
	sellType = pair.GetSellItem().Code.InstrumentType()
	sellSymbol = pair.GetSellItem().Symbol
	this.GetToExecution().UpdateOrderPriceSlippage(this.arbitrager, buyType, buySymbol, sellType, sellSymbol)
}

func (this *RotateInstruction) GetFromExecution() *InstructionExecution {
	if this.Direction == ArbiDirectionRotate {
		return this.Report.Executions[0]
	} else {
		return nil
	}
}

func (this *RotateInstruction) GetToExecution() *InstructionExecution {
	if this.Direction == ArbiDirectionRotate {
		return this.Report.Executions[1]
	} else {
		return nil
	}
}

func (this *RotateInstruction) GetFromPair() *ArbiPair {
	if this.Direction == ArbiDirectionRotate {
		return this.Pairs[0]
	} else {
		return nil
	}
}

func (this *RotateInstruction) GetToPair() *ArbiPair {
	if this.Direction == ArbiDirectionRotate {
		return this.Pairs[1]
	} else {
		return nil
	}
}

func (this *RotateInstruction) Execute() {
	if this.IsDirty() {
		this.Errorf("trying to execute a dirty instruction, please check invocation")
		return
	}
	this.WarnBNB() // 因为要检查是否有 instruction 在运行，放在 startTime 设置之前，否则正在运行的 instruction 就会包括自己
	now := time.Now()
	this.Report.StartTime = &now
	this.SetTimeout(this.Config.Timeout)
	if this.Direction == ArbiDirectionRotate {
		this.arbitrager.Executer.Rotate(this.context, this)
	} else {
		this.Errorf("execute direction %s not supported", this.Direction)
	}
}

func (this *RotateInstruction) EndNow() {
	now := time.Now()
	this.Report.EndTime = &now
	this.UpdateReportOrderPriceSlippage() // 保证订单平均价和滑点都计算
	this.UpdateAccountWorth(AccountWorthTypeAfterInstruction, true)
	// instruction 指令执行结束，写缓存
	this.arbitrager.storage.Save()
	go this.arbitrager.UpdateBalance(false)
	this.arbitrager.SendStatus(InstructionStatusAll, false, false, this.ID)
}

func (this *RotateInstruction) GetMainSymbol() string {
	return this.GetFromPair().GetBuyItem().Symbol
}

func (this *RotateInstruction) GetQtyFilled() float64 {
	if this.Direction == ArbiDirectionRotate {
		return math.Abs(this.GetFromExecution().BuyQty)
	}
	return 0.0
}

func (this *RotateInstruction) GetOrderRow() (row []string) {
	row = []string{}
	if this.Direction == ArbiDirectionRotate {
		row = []string{
			this.GetID(),
			fmt.Sprintf("%s -> %s", this.GetFromPair().GetBuyItem().Symbol, this.GetToPair().GetSellItem().Symbol),
			string(this.GetDirection()),
			fmt.Sprintf("%+.2f%%", this.GetTargetBasisRatio()*100),
			fmt.Sprintf("-%s cnt", this.FormatFutureQty(this.GetFromPair().GetBuyItem().Symbol, this.GetQty())),
			fmt.Sprintf("-%s cnt", this.FormatFutureQty(this.GetFromPair().GetBuyItem().Symbol, this.GetQtyFilled())),
			fmt.Sprintf("%.2f%%", this.GetQtyFilled()/this.GetQty()*100),
		}
	}
	return
}

func (this *RotateInstruction) RenderSummaryReport() string {
	summary := ""
	status := this.RenderStatus(true)
	summary += status
	summary += "\n\n"
	t := NewTable()
	t.SetHeader([]string{"Trade", "Future Qty", "BNB Qty", "Buy Avg. Price", "Sell Avg. Price", "Basis %"})

	for i := 0; i < 2; i++ {
		executionName := "To"
		execution := this.GetToExecution()
		pair := this.GetToPair()
		if i == 0 {
			executionName = "From"
			execution = this.GetFromExecution()
			pair = this.GetFromPair()
		}
		bnbQty := 0.0
		for _, splitOrders := range execution.BNBOrders {
			for _, o := range *splitOrders {
				bnbQty += o.ExecQty
			}
		}
		var qty, buyPrice, sellPrice string
		if executionName == "To" {
			qty = this.FormatFutureQty(pair.GetSellItem().Symbol, execution.SellQty)
			buyPrice = this.formatPrice(pair.GetBuyItem().Code.InstrumentType(), pair.GetBuyItem().Symbol, execution.BuyAvgPrice)
			sellPrice = this.formatPrice(pair.GetSellItem().Code.InstrumentType(), pair.GetSellItem().Symbol, execution.SellAvgPrice)
		} else {
			qty = this.FormatFutureQty(pair.GetBuyItem().Symbol, execution.BuyQty)
			buyPrice = this.formatPrice(pair.GetBuyItem().Code.InstrumentType(), pair.GetBuyItem().Symbol, execution.BuyAvgPrice)
			sellPrice = this.formatPrice(pair.GetSellItem().Code.InstrumentType(), pair.GetSellItem().Symbol, execution.SellAvgPrice)
		}
		t.AddRow([]string{
			executionName,
			qty,
			fmt.Sprintf("%.4f", bnbQty),
			buyPrice,
			sellPrice,
			"-",
		})
	}
	summary += t.Render()
	return summary
}

func (this *RotateInstruction) RenderSplitReport() string {
	if this.GetFinishProgress() > 0 {
		lastStartTime := this.GetReport().StartTime
		t := NewTable()
		t.SetHeader([]string{"Trade", "Split", "Basis Check", "Basis Acutal", "Order Start Time", "Duration"})
		for i := 0; i < 2; i++ {
			executionName := "To"
			execution := this.GetToExecution()
			if i == 0 {
				executionName = "From"
				execution = this.GetFromExecution()
			}
			if len(execution.BasisCheck) > 0 {
				for splitID, basisCheck := range execution.BasisCheck {
					startTimeStr := "--"
					durationStr := "--"
					if len(execution.BuyOrders) > splitID {
						splitOrders := execution.BuyOrders[splitID]
						if len(*splitOrders) > 0 {
							if startTime := (*splitOrders)[0].GetTime(ExtKeyStartTime); startTime != nil {
								durationStr = startTime.Sub(*lastStartTime).String()
								lastStartTime = startTime
								startTimeStr = (*lastStartTime).Format(ISOTimeFormat)
							}
						}
					}
					// 打印报告时指令可能正在执行中，这时可能 BasisActual 的值尚未写入，即 BasisActual 的长度和 BasisCheck 的长度不同
					// 这样会导致 BasisActual[splitID] 越界崩溃，因此打印前检查一下数组长度
					// 如果打印报告的操作和指令执行过程用锁保护起来，也可以同样解决这个问题
					acutalBasisStr := "-"
					if len(execution.BasisActual) > splitID {
						acutalBasisStr = fmt.Sprintf("%.2f%%", execution.BasisActual[splitID]*100)

					}
					t.AddRow([]string{
						executionName,
						fmt.Sprintf("%d", splitID),
						fmt.Sprintf("%.2f%%", basisCheck*100),
						acutalBasisStr,
						startTimeStr,
						durationStr,
					})
				}

			}
		}
		return t.Render()
	}
	return ""
}

package arbitrage

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strings"
	"sync"
	"time"

	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"

	"github.com/shopspring/decimal"
)

const MAX_EXECUTE_TIMES = 1500

type ArbitrageExchangeExecuter interface {
	GetInstrument(instrumentType exchange.InstrumentType, futureSymbol string) *exchange.Instrument
	GetBasisRatio(pair *ArbiPair) (basisRatio float64, er error)    // 获取溢价比率
	Arbi(context context.Context, instruction *Instruction)         // 开仓一定数量 USDx 的 Pair.FutureSymbol
	Close(context context.Context, instruction *Instruction)        // 平仓一定数量的 Pair.FutureSymbol
	Move(context context.Context, instruction *MoveInstruction)     // 转移一定数量 FromPair.FutureSymbol 到 ToPair.FutureSymbol
	Rotate(context context.Context, instruction *RotateInstruction) // 展期 FromPair.FutureSymbol 到 ToPair.FutureSymbol
	Transfer(from, to exchange.SymbolCode, amount float64) error
	IsExchange(string) bool
}

type BasisRatioCacheItem struct {
	BasisRatio float64
	CacheTime  time.Time
}

type BasisRatioCacheMap map[string]*BasisRatioCacheItem
type LocksMap map[string]*sync.Mutex

type Executer struct {
	Arbitrager        *ArbitragerController
	cachedBasisRatios BasisRatioCacheMap
	basisRatiosMutex  LocksMap
	SimpleLocks       LocksMap
}

func NewExecuter(arbitrager *ArbitragerController) (*Executer, error) {
	if arbitrager.Exchange == nil {
		return nil, fmt.Errorf("controller exchange not initialized")
	}
	executer := &Executer{
		Arbitrager:        arbitrager,
		cachedBasisRatios: BasisRatioCacheMap{},
		basisRatiosMutex:  LocksMap{},
		SimpleLocks: LocksMap{
			LockKeyAccountWorth: &sync.Mutex{},
		},
	}
	arbitrager.Exchange.CacheInstruments(false)
	return executer, nil
}

func isLastSplit(index, splits int) bool {
	return index == splits-1
}

func (this *Executer) IsExchange(name string) bool {
	exchangeName := this.Arbitrager.Config.ExchangeName
	if this.Arbitrager.Exchange != nil {
		exchangeName = this.Arbitrager.Exchange.GetName()
	}
	return strings.EqualFold(exchangeName, name)
}

// 套利开仓，instruction 数量单位为 USDx
func (this *Executer) Arbi(ctx context.Context, instruction *Instruction) {
	instruction.Debugf("run Arbi(%#v)", instruction)
	pair := instruction.GetPair()
	this.setInitLeverage(pair.GetSellItem().Symbol)
	if pair.IsF2F() {
		// 期期套利两个合约都需设置杠杆率
		this.setInitLeverage(pair.GetBuyItem().Symbol)
	}
	this.splitLoop(ctx, instruction)
}

func (this *Executer) roundFutureQty(symbol string, qty float64) float64 {
	ins := this.GetInstrument(this.Arbitrager.FutureInstrumentType(), symbol)
	if ins == nil || ins.MinSize == 0 {
		return math.Floor(qty)
	}
	minQty := ins.MinSize
	// 需要 decimal 处理否则会有精度问题
	qtyDec := decimal.NewFromFloat(qty)
	minQtyDec := decimal.NewFromFloat(minQty)
	qtyDec = qtyDec.Div(minQtyDec).Floor().Mul(minQtyDec)
	qty, _ = qtyDec.Float64()
	return qty
}

// 各指令 split 循环执行函数
func (this *Executer) splitLoop(ctx context.Context, instruction Instructable) {
	qty := instruction.GetQty()
	direction := instruction.GetDirection()
	config := instruction.GetConfig()

	isUsdxQty := false // 指令数量单位是否为 USDx
	if direction.IsOpen() {
		isUsdxQty = true
	}
	if isUsdxQty && this.needBuyBNB(config) {
		// 指令数量单位为 USDx & 配置自动购买 BNB 时，实际执行数量减去买 BNB 消耗数量
		qty = qty / (BNBFeeSpotRatioVIP0 + 1)
	}

	splitLimit := config.Splits
	splitQty := qty / float64(splitLimit) // 当前 split 执行数量
	leftQty := qty                        // 剩余未执行数量

	if direction.IsClose() || direction.IsMove() || direction.IsRotate() {
		// 平仓买入数量为合约张数，splitQty 向下取整
		pairItemBuy, _, _ := getPairItemAndExecution(instruction, false)
		splitQty = this.roundFutureQty(pairItemBuy.Symbol, splitQty)
	}

	leftBNBValue := 0.0 // 剩余 BNB 价值 = 已购买的价值 - 成功执行消耗的价值，单位 USDx

	for i := 0; i < splitLimit; i++ {
		select {
		case <-ctx.Done(): // 收到指令取消信号
			switch ctx.Err() {
			case context.DeadlineExceeded: // 超时取消
				instruction.Infof("%s timeout, current split: %v", direction, i)
				instruction.Cancel(CancelReasonDeadline)
			case context.Canceled: // 人工取消
				instruction.Infof("%s canceled, current split: %v", direction, i)
			}
			instruction.EndNow() // 收到取消信号后结束指令
			return
		default:
			if isLastSplit(i, splitLimit) {
				splitQty = leftQty // 最后处理所有剩余
			}

			instruction.GetReport().SplitCounter = i

			// 如果 split 执行成功就应该减去该 split 已经使用的 BNB
			shouldReduceSplitUsedBNB := false

			if direction.IsOpen() { // 开仓指令
				if this.checkBasisRatio(i, instruction.(*Instruction)) { // 检查当前溢价率是否满足目标
					boughtBNBValue := this.buyBNB(i, instruction, splitQty, leftBNBValue) // 买入该 split 所需 BNB
					leftBNBValue += boughtBNBValue
					var success bool
					if instruction.IsFuture2Future() {
						success = this.arbiF2FSplit(i, instruction, splitQty)
					} else {
						success = this.arbiSplit(i, instruction, splitQty)
					}
					if success {
						shouldReduceSplitUsedBNB = true
					}
				}

			} else if direction.IsClose() { // 平仓指令
				if this.checkBasisRatio(i, instruction.(*Instruction)) {
					boughtBNBValue := this.buyBNB(i, instruction, splitQty, leftBNBValue)
					leftBNBValue += boughtBNBValue
					var success bool
					if instruction.IsFuture2Future() {
						success = this.closeF2FSplit(i, instruction, splitQty)
					} else {
						usdtQty := this.closeSplit(i, instruction, splitQty)
						if usdtQty > 0 {
							success = true
						}
					}
					if success {
						shouldReduceSplitUsedBNB = true
					}
				}

			} else if direction.IsMove() { // 移仓指令
				if this.checkMoveBasisRatio(i, instruction.(*MoveInstruction)) {
					boughtBNBValue := this.buyBNB(i, instruction, splitQty, leftBNBValue)
					leftBNBValue += boughtBNBValue
					// 移仓指令依次执行平仓(closeSplit)、开仓(arbiSplit)
					usdtQty := this.closeSplit(i, instruction, splitQty)
					instruction.Infof("move split[%v] close got %v USDx", i, usdtQty)
					ok := this.arbiSplit(i, instruction, usdtQty)
					if ok {
						shouldReduceSplitUsedBNB = true
					}
				}

			} else if direction.IsRotate() {
				if this.checkRotateBasisRatio(i, instruction.(*RotateInstruction)) {
					this.rotateSplit(i, instruction, splitQty)
				}
			}

			// 如果 split 执行成功，就需要减去已经使用的 BNB 数量；
			// 并不真正的计算 split 已经使用的 BNB，而是假设 splitNeededBNBValue 就是实际已经使用的 BNB 手续费，即 usedBNBValue
			if shouldReduceSplitUsedBNB {
				usedBNBValue := this.getNeededBNBValue(instruction, splitQty)
				leftBNBValue -= usedBNBValue
			}

			leftBNBValue = math.Max(leftBNBValue, 0) // 保证剩余 BNB 价值不是负数
			leftQty -= splitQty

			instruction.UpdateReportOrderPriceSlippage() // 更新计算指令成交均价
			if err := this.Arbitrager.storage.Save(); err != nil {
				this.Arbitrager.ErrorMsgf("storage 存储失败: %s", err)
			}

			if isLastSplit(i, splitLimit) {
				instruction.EndNow() // 最后一个 split 执行完成，结束指令
			} else {
				// split 执行间隔等待，同时记录等待时间
				if direction.IsMove() {
					ins := instruction.(*MoveInstruction)
					ins.GetToExecution().SleepDuration(i, ExecutionDurationTypeSplitWait, time.Millisecond*time.Duration(instruction.GetConfig().SplitWait), "")
				} else if direction.IsRotate() {
					ins := instruction.(*RotateInstruction)
					ins.GetToExecution().SleepDuration(i, ExecutionDurationTypeSplitWait, time.Millisecond*time.Duration(instruction.GetConfig().SplitWait), "")
				} else {
					if ins, ok := instruction.(*Instruction); ok {
						ins.GetExecution().SleepDuration(i, ExecutionDurationTypeSplitWait, time.Millisecond*time.Duration(instruction.GetConfig().SplitWait), "")
					}
				}
			}
		}
	}
}

// 根据指令和数量估算所需 BNB 价值，单位 USDx
func (this *Executer) getNeededBNBValue(instruction Instructable, qty float64) float64 {
	var usdtQty float64 // 现货交易的 USDx 数量
	direction := instruction.GetDirection()

	if direction == ArbiDirectionOpen || direction == ArbiDirectionClose {
		if direction == ArbiDirectionOpen {
			usdtQty = qty // 开仓时，qty 即为 USDx
		} else {
			// 平仓时，用 qty 张数对应价值估算 USDx
			ins := instruction.(*Instruction)
			symbol := ins.GetPair().GetBuyItem().Symbol
			if fPrice, err := this.Arbitrager.Exchange.GetLastPrice(this.Arbitrager.FutureInstrumentType(), symbol, false); err != nil {
				usdtQty = 0
			} else {
				usdtQty = this.Arbitrager.Qty2Size(symbol, fPrice, qty) * (1 - ins.TargetBasisRatio)
				if this.Arbitrager.FutureInstrumentType() == exchange.CoinMarginedFutures {
					// 币本位币价值 * 价格 = USDx
					usdtQty = usdtQty * fPrice
				}
			}
		}
	} else if direction == ArbiDirectionMove {
		// 移仓指令用 2 倍 qty 张数对应价值估算 USDx
		ins := instruction.(*MoveInstruction)
		symbol := ins.GetFromPair().GetBuyItem().Symbol // move 平仓买合约的 symbol
		if fPrice, err := this.Arbitrager.Exchange.GetLastPrice(this.Arbitrager.FutureInstrumentType(), symbol, false); err != nil {
			usdtQty = 0
		} else {
			usdtQty = 2 * this.Arbitrager.Qty2Size(symbol, fPrice, qty)
			if this.Arbitrager.FutureInstrumentType() == exchange.CoinMarginedFutures {
				// 币本位币价值 * 价格 = USDx
				usdtQty = usdtQty * fPrice
			}
		}
	}

	// USDx 交易数量 * 折扣率
	return usdtQty * BNBFeeSpotRatioVIP0
}

// 根据交易所和配置返回是否需要购买 BNB
func (this *Executer) needBuyBNB(config *ArbitragerConfig) bool {
	if !this.IsExchange(exchange.Binance) {
		return false
	}
	if !config.BNBFee || !config.BNBAutoBuy {
		return false
	}
	return true
}

// 根据指令、split 数量和剩余 BNB 价值购买 BNB，返回本次购买的 BNB 价值
func (this *Executer) buyBNB(index int, instruction Instructable, splitQty float64, leftBNBValue float64) (boughtBNBValue float64) {
	boughtBNBValue = 0
	config := instruction.GetConfig()
	if !this.needBuyBNB(config) {
		return
	}

	var execution *InstructionExecution
	direction := instruction.GetDirection()
	if direction == ArbiDirectionMove {
		ins := instruction.(*MoveInstruction)
		execution = ins.GetFromExecution()
	} else if direction == ArbiDirectionOpen || direction == ArbiDirectionClose {
		ins := instruction.(*Instruction)
		execution = ins.GetExecution()
	}
	// 为当前 split 创建 BNB 订单存储切片
	execution.BNBOrders = append(execution.BNBOrders, &[]*exchange.Order{})

	leftBNBValue = math.Max(leftBNBValue, 0)                             // 保证剩余 BNB 价值不是负数
	splitNeededBNBValue := this.getNeededBNBValue(instruction, splitQty) // split 需要的 BNB 价值
	if leftBNBValue > splitNeededBNBValue {
		// 剩余 BNB 够用，无需购买
		return
	}

	shouldBuyBNBValue := splitNeededBNBValue - leftBNBValue // 实际需要购买价值
	symbolCodes := instruction.GetRelatedSymbolCodes()
	BNBSymbol := fmt.Sprintf("BNB%s", symbolCodes[0].USDXSymbol)
	if instrument := this.GetInstrument(exchange.Spot, BNBSymbol); instrument == nil {
		return
	} else {
		minNotional := instrument.MinNotional // BNB 交易对订单最小价值
		if shouldBuyBNBValue < minNotional {
			// 剩余 split 总共需要购买的 U 小于配置则不买
			totalNeedBuyLeft := this.getNeededBNBValue(instruction, splitQty*float64(config.Splits-index)) - leftBNBValue
			if totalNeedBuyLeft < config.BNBAutoBuyLastSplitThreshold {
				return
			} else {
				shouldBuyBNBValue = minNotional
			}
		}

		if err := this._buyBNB(index, BNBSymbol, shouldBuyBNBValue, execution); err != nil {
			instruction.Errorf("buy BNB error: %s", err)
			return
		} else {
			boughtBNBValue = shouldBuyBNBValue
			return
		}
	}
}

// 按价值下单购买 BNB
func (this *Executer) _buyBNB(index int, BNBSymbol string, BNBValue float64, execution *InstructionExecution) error {
	BNBValueDec := decimal.NewFromFloat(BNBValue)
	sizeDec := decimal.NewFromFloat(0.01)
	BNBValueDec = BNBValueDec.Div(sizeDec).Floor().Mul(sizeDec)
	BNBValue, _ = BNBValueDec.Float64()

	orderArgs := exchange.CreateOrderArgs{
		InstrumentType: exchange.Spot,
		Symbol:         BNBSymbol,
		Side:           exchange.OrderSideBuy,
		Type:           exchange.Market,
		QuoteQty:       BNBValue,
		TradeMode:      exchange.TradeModeCash,
	}

	// 记录 BNB 订单
	spotOrders := execution.BNBOrders[index]

	retried := false // 下单失败时重新尝试一次
	for {
		reportOrder := &exchange.Order{
			InstrumentType: exchange.Spot,
			RefID:          exchange.NewRandomID(),
			Symbol:         BNBSymbol,
			QuoteQty:       BNBValue,
		}
		*spotOrders = append(*spotOrders, reportOrder)
		requestStartTime := time.Now()
		reportOrder.SetTime(ExtKeyStartTime, &requestStartTime)

		orderRes, err := this.Arbitrager.Exchange.CreateOrder(orderArgs)
		execution.endOrderRequest(reportOrder, index)
		this.Arbitrager.Debugf("buy bnb order resp: %#v", orderRes)
		if err != nil {
			if retried {
				return err
			}
			retried = true
			continue
		}

		updateReportOrder(reportOrder, orderRes)

		orderTradeValue := 0.0
		orderTradeQty := 0.0
		for _, trade := range orderRes.Trades {
			orderTradeValue += trade.Qty * trade.Price
			orderTradeQty += trade.Qty
		}
		if orderTradeQty != 0 {
			reportOrder.ExecPrice = orderTradeValue / orderTradeQty
			reportOrder.ExecQty = orderTradeQty
		}
		if orderTradeValue == reportOrder.QuoteQty {
			reportOrder.Status = exchange.OrderStatusFilled
		} else if orderTradeValue > 0 {
			reportOrder.Status = exchange.OrderStatusPartialCancelled
		} else {
			reportOrder.Status = exchange.OrderStatusCancelled
		}
		(*spotOrders)[len(*spotOrders)-1] = reportOrder // 更新订单成交信息

		break
	}

	return nil
}

// 单币种保证金模式设置合约杠杠率为 1，跨币种模式设置杠杠率为 20
func (this *Executer) setInitLeverage(futureSymbol string) {
	leverage := 1.0
	if this.Arbitrager.IsCrossMode() {
		leverage = 20
		maxLeverage := this.Arbitrager.Exchange.MaxLeverage(this.Arbitrager.FutureInstrumentType(), futureSymbol, 0)
		leverage = math.Min(leverage, maxLeverage)
	}

	err := this.Arbitrager.Exchange.SetLeverage(this.Arbitrager.FutureInstrumentType(), futureSymbol, exchange.Cross, "", leverage)
	if err != nil {
		this.Arbitrager.Errorf("set leverage err, symbol: %s, err: %v", futureSymbol, err)
	}

	if err := this.Arbitrager.Exchange.SetMarginMode(this.Arbitrager.FutureInstrumentType(), futureSymbol, exchange.Cross); err != nil {
		if !strings.Contains(err.Error(), "No need to change") {
			this.Arbitrager.Errorf("set cross margin err, symbol: %s, err: %v", futureSymbol, err)
		}
	}
}

// 稍微减少合约张数，减少数量为原数量的 0.001，至少减少 1 张，返回新数量
func reduceCntSlightly(origCnt, minQty float64) float64 {
	if minQty == 0 {
		minQty = 1
	}
	newCnt := origCnt
	reduce := math.Floor(origCnt * 0.001)
	reduce = math.Max(minQty, reduce)
	newCnt -= reduce
	zlog.Infof("reduce cnt slightly from %v to %v", origCnt, newCnt)
	return newCnt
}

// 确保 pair symbol 都能获取到 instrument
func (this *Executer) checkPairItemInstrument(pairItems []*exchange.SymbolItem) (bool, error) {
	for _, pairItem := range pairItems {
		if instrument := this.GetInstrument(pairItem.Code.InstrumentType(), pairItem.Symbol); instrument == nil {
			return false, fmt.Errorf("%s instrument not exist", pairItem.Symbol)
		}
	}

	return true, nil
}

// 根据指令返回 pairItem, execution；用于获取交易对和填充报告数据
// forOpen 表示是否是指令的开仓阶段，对所有指令有效
// 套利开仓时，买 Left 卖 Right
// 套利平仓时，买 Right 卖 Left
func getPairItemAndExecution(instruction Instructable, forOpen bool) (pairItemBuy *exchange.SymbolItem, pairItemSell *exchange.SymbolItem, execution *InstructionExecution) {
	var pair *ArbiPair
	direction := instruction.GetDirection()

	if direction == ArbiDirectionMove {
		moveInstruction := instruction.(*MoveInstruction)
		if forOpen {
			pair = moveInstruction.GetToPair()
			execution = moveInstruction.GetToExecution()
		} else {
			pair = moveInstruction.GetFromPair()
			execution = moveInstruction.GetFromExecution()
		}
	} else if direction == ArbiDirectionRotate {
		ins := instruction.(*RotateInstruction)
		if forOpen {
			pair = ins.GetToPair()
			execution = ins.GetToExecution()
		} else {
			pair = ins.GetFromPair()
			execution = ins.GetFromExecution()
		}
	} else if direction == ArbiDirectionOpen || direction == ArbiDirectionClose {
		ins := instruction.(*Instruction)
		pair = ins.GetPair()
		execution = ins.GetExecution()
	}

	if forOpen {
		pairItemBuy = pair.Left
		pairItemSell = pair.Right
	} else {
		pairItemBuy = pair.Right
		pairItemSell = pair.Left
	}

	return
}

func (this *Executer) queryLimitOrder(instruction Instructable, origOrder *exchange.Order) (tradePrice, tradeQty float64, end bool) {
	// 定时查询直到订单最终状态或指令被取消
	instruction.Debugf("limit order %s waiting...", origOrder.OrderID)
	time.Sleep(time.Second * 10)

	// 查询成交情况
	orderInfo, err := this.Arbitrager.Exchange.GetOrderByOrig(*origOrder)
	if err == nil {
		// orderInfo 里没有 Trades 数据，有 ExecutedPrice & ExecutedAmount
		tradePrice = orderInfo.ExecPrice
		tradeQty = orderInfo.ExecQty
		if !orderInfo.IsOpen() {
			// 订单已是最终状态
			end = true
			return
		}
	} else {
		instruction.Errorf("query limit order %s err: %v", origOrder.OrderID, err)
	}

	if instruction.IsCanceled() {
		if err := this.Arbitrager.Exchange.CancelOrder(origOrder.InstrumentType, exchange.Limit, origOrder.Symbol, origOrder.OrderID); err != nil {
			instruction.Errorf("cancel limit order %s err: %v", origOrder.OrderID, err)
		}
		end = true
	}
	return
}

func (this *Executer) submitOrder(args exchange.CreateOrderArgs) (*exchange.Order, *SubmitOrderError) {
	resp, err := this.Arbitrager.Exchange.CreateOrder(args)
	if err == nil {
		return resp, nil
	}

	submitErr := &SubmitOrderError{
		error:     err,
		ErrorCode: SubmitOrderErrorCodeUnknown,
	}
	if this.IsExchange(exchange.Binance) {
		if strings.Contains(err.Error(), "insufficient balance") || strings.Contains(err.Error(), "Margin is insufficient") {
			submitErr.ErrorCode = SubmitOrderErrorCodeInsufficientBalance
		} else if strings.Contains(err.Error(), "\"code\":-4061") { // Order's position side does not match user's setting.
			submitErr.ErrorCode = SubmitOrderErrorCodeUserSettingsErr
		} else if strings.Contains(err.Error(), "ReduceOnly Order is rejected") {
			submitErr.ErrorCode = SubmitOrderErrorCodeOrderRejected
		}
	} else if this.IsExchange(exchange.OKEx) {
		if strings.Contains(err.Error(), "insufficient balance") || strings.Contains(err.Error(), "you can only reduce the positions you have") || strings.Contains(err.Error(), "Insufficient") {
			submitErr.ErrorCode = SubmitOrderErrorCodeInsufficientBalance
		} else if strings.Contains(err.Error(), "Parameter posSide  error") {
			submitErr.ErrorCode = SubmitOrderErrorCodeUserSettingsErr
		}
	}
	return resp, submitErr
}

// 买现货 split
func (this *Executer) buySpotSplit(index int, instruction Instructable, usdtQty float64, pairItem *exchange.SymbolItem, execution *InstructionExecution) (avgPrice float64, boughtQty float64) {
	instruction.Infof("buy spot split %v start", index)
	direction, config := instruction.GetDirection(), instruction.GetConfig()
	// pair, execution := getPairAndExecution(instruction, true)

	coin := pairItem.Code.Coin() // 基础币种
	shouldBuyQty := 0.0          // 现货应该买的数量
	tradeValue := 0.0            // 实际成交价值
	tradeQty := 0.0              // 实际成交数量
	feeQty := 0.0                // 交易费数量，仅记录以基础币种收取的
	usdtLeft := usdtQty          // 剩余待交易 USDx
	minNotional := 0.0           // 交易对最小下单价值
	minQty := 0.0

	if instrument := this.GetInstrument(exchange.Spot, pairItem.Symbol); instrument != nil {
		minNotional = instrument.MinNotional
		minQty = instrument.MinSize
	} else {
		instruction.Errorf("%s instrument not exist", pairItem.Symbol)
		return
	}

	spotOrders := &[]*exchange.Order{} // 用于记录当前 split 现货订单
	execution.BuyOrders = append(execution.BuyOrders, spotOrders)

	retryCountAfterCanceled := 0 // 指令取消后异常重试次数
	executeCount := 0            // 下单请求执行次数
	for {
		executeCount += 1
		if executeCount > MAX_EXECUTE_TIMES {
			// 超过最大执行次数，中断执行并取消指令
			instruction.SetSingleSideReason(SingleSideReasonArbiSpot)
			instruction.Cancel(CancelReasonMaxExecuteTimes)
			this.Arbitrager.ErrorMsgf("指令 %s Split[%v] 买入现货异常终止：超过最大重试次数 %v。当前已成交 %v %s。", instruction.GetID(), index, MAX_EXECUTE_TIMES, tradeQty, coin)
			return
		}

		if retryCountAfterCanceled == 3 {
			// 指令取消后重试 3 次则中断执行
			instruction.SetSingleSideReason(SingleSideReasonArbiSpot)
			this.Arbitrager.ErrorMsgf("指令 %s 取消后 Split[%v] 买入现货异常终止。当前已成交 %v %s。", instruction.GetID(), index, tradeQty, coin)
			return
		}

		limitPrice := instruction.GetPrice()
		submit, lastPrice, err := this.getCreateOrderArgs(exchange.Spot, pairItem.Symbol, usdtLeft, limitPrice, exchange.OrderSideBuy, direction, config, execution)
		if err != nil {
			// 获取下单参数失败，稍等 SpotRetryInterval 后重试
			instruction.Errorf("spot get order submission err: %v", err)
			execution.SleepDuration(index, ExecutionDurationTypeBuyRetryInterval, time.Millisecond*time.Duration(config.SpotRetryInterval), "")
			if instruction.IsCanceled() {
				retryCountAfterCanceled += 1
			}
			continue
		} else if shouldBuyQty == 0 {
			// 首次算得 submit.Amount 即为 split 应该买的数量
			shouldBuyQty = submit.Qty
		}

		instruction.Debugf("submit spot order: %#v", submit)
		instruction.Infof("prepare to buy %v %v at price %v", submit.Qty, coin, submit.Price)

		if (submit.Price*submit.Qty < minNotional) || submit.Qty == 0 || submit.Qty < minQty {
			// 下单价值小于最小价值，不继续执行
			instruction.Warnf("spot order value/amount(%v/%v) too small to place", submit.Price*submit.Qty, submit.Qty)
			break
		}

		reportOrder := &exchange.Order{
			RefID:          exchange.NewRandomID(),
			Symbol:         pairItem.Symbol,
			InstrumentType: exchange.Spot,
			Qty:            submit.Qty,
			LastPrice:      lastPrice,
		}
		*spotOrders = append(*spotOrders, reportOrder)
		requestStartTime := time.Now()
		reportOrder.SetTime(ExtKeyStartTime, &requestStartTime)

		spotOrderResp, oErr := this.submitOrder(submit) // 提交订单
		execution.endOrderRequest(reportOrder, index)   // 记录订单结束
		if oErr != nil {
			instruction.Errorf("spot SubmitOrder err: %v", oErr)
			if oErr.ErrorCode == SubmitOrderErrorCodeInsufficientBalance {
				// 下单返回余额不足，获取最新可用 USDx 数量
				_, availableUsdt, err := this.Arbitrager.Exchange.GetHoldingQty(exchange.Spot, pairItem.Code.USDXSymbol)
				if err != nil {
					instruction.Errorf("insufficient balance, get usdt qty err: %v", err)
				} else {
					instruction.Infof("insufficient balance, left usdt: %v", availableUsdt)
					usdtLeft = math.Min(availableUsdt, usdtLeft)
				}
			}

			execution.SleepDuration(index, ExecutionDurationTypeBuyRetryInterval, time.Millisecond*time.Duration(config.SpotRetryInterval), "")
			if instruction.IsCanceled() {
				retryCountAfterCanceled += 1
			}
			continue
		}
		instruction.Debugf("spot order resp: %#v", spotOrderResp)

		updateReportOrder(reportOrder, spotOrderResp)

		// 记录订单数量、价值，同时更新 execution
		isLimitOrder := limitPrice != 0
		if isLimitOrder {
			// 限价单
			var tradePrice float64
			for {
				var orderEnded bool
				tradePrice, tradeQty, orderEnded = this.queryLimitOrder(instruction, spotOrderResp)

				execution.UsdtQty = tradeQty * tradePrice
				execution.BuyQty = tradeQty

				if tradeQty != 0 {
					reportOrder.ExecPrice = tradePrice
					reportOrder.ExecQty = tradeQty
				}
				(*spotOrders)[len(*spotOrders)-1] = reportOrder

				if orderEnded {
					break
				}
			}

			usdtLeft -= tradeQty * tradePrice
			tradeValue = tradeQty * tradePrice
		} else {
			// IOC 订单及时成交
			orderTradeValue := 0.0
			orderTradeQty := 0.0

			// bn 直接返回了 trades, 其他交易所需要单独查询成交
			if !this.IsExchange(exchange.Binance) {
				for {
					orderInfo, err := this.Arbitrager.Exchange.GetOrderByOrig(*spotOrderResp)
					if err != nil {
						instruction.Errorf("spot GetOrderInfo(%s) err: %v", spotOrderResp.OrderID, err)

						executeCount += 1
						if executeCount > MAX_EXECUTE_TIMES {
							instruction.SetSingleSideReason(SingleSideReasonArbiSpot)
							instruction.Cancel(CancelReasonMaxExecuteTimes)
							this.Arbitrager.ErrorMsgf("指令 %s Split[%v] 买入现货异常终止：超过最大重试次数 %v。当前已成交 %v %s。", instruction.GetID(), index, MAX_EXECUTE_TIMES, tradeQty, coin)
							return
						}

						continue
					}

					if orderInfo.IsOpen() {
						instruction.Infof("order(%s) still open", spotOrderResp.OrderID)
						continue
					}

					trade := exchange.TradeHistory{
						Qty:      orderInfo.ExecQty,
						Price:    orderInfo.ExecPrice,
						Fee:      orderInfo.Fee,
						FeeAsset: orderInfo.FeeAsset,
					}
					spotOrderResp.Trades = []exchange.TradeHistory{trade}
					break
				}
			}

			for _, trade := range spotOrderResp.Trades {
				usdtLeft -= trade.Qty * trade.Price // 剩余待交易 USDx 减去本次成交价值

				tradeQty += trade.Qty
				if trade.FeeAsset == coin {
					feeQty += trade.Fee
					execution.SpotFee += trade.Fee * trade.Price
					execution.BuyQty -= trade.Fee
				} else if trade.FeeAsset == pairItem.Code.USDXSymbol {
					execution.SpotFee += trade.Fee
				}

				orderTradeValue += trade.Qty * trade.Price
				orderTradeQty += trade.Qty

				execution.UsdtQty += trade.Qty * trade.Price
				execution.BuyQty += trade.Qty
			}

			instruction.Infof("spot traded: %v %v, split total: %v %v", orderTradeQty, coin, tradeQty, coin)
			tradeValue += orderTradeValue
			if orderTradeQty != 0 {
				reportOrder.ExecPrice = orderTradeValue / orderTradeQty
				reportOrder.ExecQty = orderTradeQty
			}
			(*spotOrders)[len(*spotOrders)-1] = reportOrder

			if (shouldBuyQty-tradeQty)/shouldBuyQty > config.SplitSpotQtyTolerance {
				// 已成交数量比例大于 SplitSpotQtyTolerance，继续执行
				instruction.Infof("%s not enough traded, tradeAmount: %v, shouldBuyAmount: %v, try later", pairItem.Symbol, tradeQty, shouldBuyQty)
				continue
			}
		}
		break
	}

	if tradeQty == 0 {
		instruction.Warnf("no %s can transfer, end split", coin)
		return
	}

	avgPrice = tradeValue / tradeQty // 成交均价
	boughtQty = tradeQty - feeQty    // 可划转数量 = 交易数量 - 交易费
	instruction.Infof("split[%v] spot bought %v %v(fee: %v), avg price %v", index, tradeQty, coin, feeQty, avgPrice)
	return
}

// 划转 split
// isOpen 为 true 时从现货划转到期货账号，否则从期货划转到现货
func (this *Executer) transferSplit(index int, instruction Instructable, origAmount float64, isOpen bool) float64 {
	config := instruction.GetConfig()
	// 这里无论 pairItemBuy/pairItemSell Code.Coin() 是相同的
	pairItemBuy, _, execution := getPairItemAndExecution(instruction, isOpen)

	coin := pairItemBuy.Code.Coin()
	retryCountAfterCanceled := 0
	executeCount := 0
	transferAmount := origAmount

	if this.Arbitrager.FutureInstrumentType() != exchange.CoinMarginedFutures {
		coin = this.Arbitrager.Exchange.GetSymbolCodeQuote(pairItemBuy.Code)
	}

	for {
		if transferAmount == 0 && origAmount > 0 {
			instruction.Warnf("transfer %v transferAmount is 0", coin)
			if isOpen {
				instruction.SetSingleSideReason(SingleSideReasonArbiTransfer)
			} else {
				instruction.SetSingleSideReason(SingleSideReasonCloseTransfer)
			}
			instruction.Cancel(CancelReasonQtyInsufficient)
			this.Arbitrager.ErrorMsgf("指令 %s Split[%v] 转账异常终止：可划账数量为 0", instruction.GetID(), index)
			return 0
		}

		executeCount += 1
		if executeCount > MAX_EXECUTE_TIMES {
			// 超过最大执行次数，中断执行并取消指令
			if isOpen {
				instruction.SetSingleSideReason(SingleSideReasonArbiTransfer)
			} else {
				instruction.SetSingleSideReason(SingleSideReasonCloseTransfer)
			}
			instruction.Cancel(CancelReasonMaxExecuteTimes)
			this.Arbitrager.ErrorMsgf("指令 %s Split[%v] 转账异常终止：超过最大重试次数 %v。", instruction.GetID(), index, MAX_EXECUTE_TIMES)
			return 0
		}

		if retryCountAfterCanceled == 3 {
			// 指令取消后重试 3 次则中断执行
			if isOpen {
				instruction.SetSingleSideReason(SingleSideReasonArbiTransfer)
			} else {
				instruction.SetSingleSideReason(SingleSideReasonCloseTransfer)
			}
			this.Arbitrager.ErrorMsgf("指令 %s 取消后 Split[%v] 转账异常终止。", instruction.GetID(), index)
			return 0
		}

		tick := time.Now()
		from := exchange.Spot
		to := this.Arbitrager.FutureInstrumentType()
		if !isOpen {
			from = this.Arbitrager.FutureInstrumentType()
			to = exchange.Spot
		}
		err := this.Arbitrager.Exchange.TransferAsset(from, to, coin, transferAmount)
		execution.AddDuration(index, ExecutionDurationTypeTransfer, time.Since(tick), "")
		if err != nil {
			instruction.Errorf("transfer %v %v err: %v", transferAmount, coin, err)
			if strings.Contains(err.Error(), "Amount must be greater than zero") {
				// 无可划账余额
				instruction.Warnf("transfer amount(%v) too small, end split", transferAmount)
				return 0
			}

			if strings.Contains(err.Error(), "insufficient balance") {
				// 划账余额小于指定数量，重新获取可用数量
				if from == exchange.Spot {
					_, available, _ := this.Arbitrager.Exchange.GetHoldingQty(exchange.Spot, coin)
					transferAmount = math.Min(available, transferAmount)
				} else {
					available, _ := this.getFutureCoinAvailableBalance(coin)
					transferAmount = math.Min(available, transferAmount)
				}
			}

			execution.SleepDuration(index, ExecutionDurationTypeTransferRetryInterval, time.Millisecond*time.Duration(config.TransferRetryInterval), "")

			if instruction.IsCanceled() {
				retryCountAfterCanceled += 1
			}
			continue
		}

		instruction.Infof("split[%v] transfered %v %v from %s to %s", index, transferAmount, coin, from, to)
		break
	}
	return transferAmount
}

// 卖出合约 split
func (this *Executer) sellFutureSplit(index int, qty float64, instruction Instructable, pairItem *exchange.SymbolItem, execution *InstructionExecution) (futureAvgPrice float64) {
	instruction.Infof("sell future split %v start", index)
	direction, config := instruction.GetDirection(), instruction.GetConfig()
	// pair, execution := getPairAndExecution(instruction, true)

	futureSymbol := pairItem.Symbol
	baseCoin := pairItem.Code.Coin()

	minQty := 0.0
	if instrument := this.GetInstrument(this.Arbitrager.FutureInstrumentType(), futureSymbol); instrument != nil {
		minQty = instrument.MinSize
	} else {
		instruction.Errorf("%s instrument not exist", futureSymbol)
		return 0
	}

	shouldSellBaseQty := qty // qty 在 ArbiDirectionOpen 时表示币数量，在 ArbiDirectionSell 时表示合约张数
	leftCnt := 0.0           // 仅在 ArbiDirectionSell 时使用剩余张数来下单
	if index != 0 {
		futureTradedQty := 0.0 // 目前指令已成交合约对应基础币种数量
		if execution.SellAvgPrice != 0 {
			futureTradedQty = this.Arbitrager.FutureQtyToSpotQty(futureSymbol, execution.SellQty, execution.SellAvgPrice)
		}
		if direction.IsRotate() {
			// rotate 剩余应开仓价值 = 已平仓价值 - 已开仓价值
			rIns := instruction.(*RotateInstruction)
			fromExecution := rIns.GetFromExecution()
			fromTradedQty := this.Arbitrager.FutureQtyToSpotQty(futureSymbol, fromExecution.BuyQty, fromExecution.BuyAvgPrice)
			shouldSellBaseQty = math.Abs(fromTradedQty) - math.Abs(futureTradedQty)
		} else {
			buyQty := execution.BuyQty
			if instruction.IsFuture2Future() {
				buyQty = this.Arbitrager.FutureQtyToSpotQty(futureSymbol, execution.BuyQty, execution.BuyAvgPrice)
			}
			// 不直接用 qty 而用 [已买入数量 - 已卖出数量] 的原因：split 合约可能因为张数不足一张，累积后的数量差值较大
			shouldSellBaseQty = buyQty - math.Abs(futureTradedQty)*(1+0.0005) // 已买入数量 - 已卖出数量(1 + 0.0005) (含预估手续费)
		}
		if shouldSellBaseQty <= 0 {
			instruction.Cancel(CancelReasonFutureQtyError)
			instruction.Errorf("should sell qty %v must be bigger than 0", shouldSellBaseQty)
			this.Arbitrager.ErrorMsgf("指令 %s Split[%v] 计算合约卖出数量异常，请检查日志", instruction.GetID(), index)
			return 0
		}
	}
	instruction.Infof("should sell %v %s value of future", shouldSellBaseQty, baseCoin)

	tradeValue := 0.0                // 已成交价值
	tradeCnt := 0.0                  // 已成交张数
	leftBaseQty := shouldSellBaseQty // 剩余应成交
	reducedCnt := -1.0               // 由于余额不足而稍微减少后的张数，为 -1 时表示没有减少处理

	futureOrders := &[]*exchange.Order{}
	execution.SellOrders = append(execution.SellOrders, futureOrders)
	retryCountAfterCanceled := 0
	executeCount := 0
	for {
		executeCount += 1
		if executeCount > MAX_EXECUTE_TIMES {
			// 超过最大执行次数，中断执行并取消指令
			instruction.SetSingleSideReason(SingleSideReasonArbiFuture)
			instruction.Cancel(CancelReasonMaxExecuteTimes)
			this.Arbitrager.ErrorMsgf("指令 %s Split[%v] 卖出合约异常终止：超过最大重试次数 %v。当前已成交 %v 张。", instruction.GetID(), index, MAX_EXECUTE_TIMES, tradeCnt)
			return
		}

		if retryCountAfterCanceled == 3 {
			// 指令取消后重试 3 次则中断执行
			instruction.SetSingleSideReason(SingleSideReasonArbiFuture)
			this.Arbitrager.ErrorMsgf("指令 %s 取消后 Split[%v] 卖出合约异常终止。当前已成交 %v 张。", instruction.GetID(), index, tradeCnt)
			return 0
		}

		submitQty := leftBaseQty // 默认获取 submit 数量参数用币种价值
		limitPrice := instruction.GetPrice()
		submit, lastPrice, err := this.getCreateOrderArgs(this.Arbitrager.FutureInstrumentType(), futureSymbol, submitQty, limitPrice, exchange.OrderSideSell, direction, config, execution)
		if err != nil {
			instruction.Errorf("future get order submission err: %v", err)
			execution.SleepDuration(index, ExecutionDurationTypeSellRetryInterval, time.Millisecond*time.Duration(config.FutureRetryInterval), "")
			if instruction.IsCanceled() {
				retryCountAfterCanceled += 1
			}
			continue
		}

		instruction.Debugf("submit future order: %#v", submit)

		if reducedCnt >= 0 {
			// 经过减少处理后的张数
			submit.Qty = reducedCnt
		}

		if submit.Qty == 0 || submit.Qty < minQty {
			instruction.Warnf("future order amount(%v) too small to place", submit.Qty)
			break
		}

		instruction.Infof("prepare to sell %v %v at price %v", submit.Qty, futureSymbol, submit.Price)

		reportOrder := &exchange.Order{
			RefID:          exchange.NewRandomID(),
			Symbol:         futureSymbol,
			InstrumentType: this.Arbitrager.FutureInstrumentType(),
			Qty:            submit.Qty,
			LastPrice:      lastPrice,
			Price:          submit.Price,
		}
		*futureOrders = append(*futureOrders, reportOrder)
		requestStartTime := time.Now()
		reportOrder.SetTime(ExtKeyStartTime, &requestStartTime)

		futureOrderResp, oErr := this.submitOrder(submit) // 提交订单
		execution.endOrderRequest(reportOrder, index)
		if oErr != nil {
			instruction.Errorf("future SubmitOrder err: %v", oErr)
			if oErr.ErrorCode == SubmitOrderErrorCodeInsufficientBalance {
				// 余额不足错误，稍微减少数量后重新下单
				reducedCnt = reduceCntSlightly(submit.Qty, minQty)
			}
			if oErr.ErrorCode == SubmitOrderErrorCodeUserSettingsErr {
				instruction.Cancel(CancelReasonFutureOrderError)
				instruction.SetSingleSideReason(SingleSideReasonArbiFuture)
				this.Arbitrager.ErrorMsgf("指令 %s Split[%v] 卖出合约异常终止，请检查账号交易模式设置。%s", instruction.GetID(), index, oErr.Error())
				return 0
			}
			execution.SleepDuration(index, ExecutionDurationTypeSellRetryInterval, time.Millisecond*time.Duration(config.FutureRetryInterval), "")
			if instruction.IsCanceled() {
				retryCountAfterCanceled += 1
			}
			continue
		}
		if futureOrderResp.OrderID == "" {
			// 订单没有提交成功
			instruction.Errorf("future SubmitOrder failed: orderID is empty")
			execution.SleepDuration(index, ExecutionDurationTypeSellRetryInterval, time.Millisecond*time.Duration(config.FutureRetryInterval), "")
			if instruction.IsCanceled() {
				retryCountAfterCanceled += 1
			}
			continue
		}
		reducedCnt = -1.0 // 订单提交成功，重置 reducedCnt
		instruction.Infof("future order resp: %#v", futureOrderResp)
		updateReportOrder(reportOrder, futureOrderResp)

		// 接口返回无成交信息，需主动查询
		var futureOrder *exchange.Order
		isLimitOrder := limitPrice != 0
		queryCount := 0
		for {
			if retryCountAfterCanceled == 3 {
				// 指令取消后重试 3 次则中断执行
				instruction.SetSingleSideReason(SingleSideReasonArbiFuture)
				this.Arbitrager.ErrorMsgf("指令 %s 取消后 Split[%v] 卖出合约异常终止。当前已成交 %v 张。", instruction.GetID(), index, tradeCnt)
				return 0
			}

			queryCount += 1
			futureOrder, err = this.Arbitrager.Exchange.GetOrderByOrig(*futureOrderResp)
			if err != nil {
				instruction.Errorf("query future order %s err: %v", futureOrderResp.OrderID, err)
				execution.SleepDuration(index, ExecutionDurationTypeSellRetryInterval, time.Millisecond*time.Duration(config.FutureRetryInterval), "")
				if instruction.IsCanceled() {
					retryCountAfterCanceled += 1
				}

				executeCount += 1
				if executeCount > MAX_EXECUTE_TIMES {
					// 超过最大执行次数，中断执行并取消指令
					instruction.SetSingleSideReason(SingleSideReasonArbiFuture)
					instruction.Cancel(CancelReasonMaxExecuteTimes)
					this.Arbitrager.ErrorMsgf("指令 %s Split[%v] 卖出合约异常终止：超过最大重试次数 %v。当前已成交 %v 张。", instruction.GetID(), index, MAX_EXECUTE_TIMES, tradeCnt)
					return 0
				}

				continue
			}

			if isLimitOrder {
				if futureOrder.ExecQty != 0 {
					execution.SellQty += futureOrder.ExecQty

					reportOrder.ExecPrice = futureOrder.ExecPrice
					reportOrder.ExecQty = futureOrder.ExecQty
					(*futureOrders)[len(*futureOrders)-1] = reportOrder
				}

				if !futureOrder.IsOpen() {
					// 订单已是最终状态
					break
				}

				if instruction.IsCanceled() {
					if err := this.Arbitrager.Exchange.CancelOrder(this.Arbitrager.FutureInstrumentType(), exchange.Limit, submit.Symbol, futureOrderResp.OrderID); err != nil {
						instruction.Errorf("cancel limit order %s err: %v", futureOrderResp.OrderID, err)
					}
					break
				}

				// 继续查询
				instruction.Debugf("limit order %s waiting...", futureOrderResp.OrderID)
				execution.SleepDuration(index, ExecutionDurationTypeSellRetryInterval, time.Second*10, "")
				continue
			} else {
				// IOC 订单
				if futureOrder.IsOpen() {
					instruction.Infof("IOC order %s is still open, waiting...", futureOrderResp.OrderID)
					execution.SleepDuration(index, ExecutionDurationTypeSellRetryInterval, time.Second*3, "")
					continue
				} else if futureOrder.ExecQty == 0 && queryCount == 1 {
					// OKEx IOC 订单会出现实际已成交但返回数量为 0 的情况，稍等再查一次
					instruction.Infof("IOC order %s query got 0 cnt, query one more time.", futureOrderResp.OrderID)
					execution.SleepDuration(index, ExecutionDurationTypeSellRetryInterval, time.Second*3, "")
					continue
				}
			}

			break
		}

		instruction.Infof("%s traded %v cnt at price %v", futureSymbol, futureOrder.ExecQty, futureOrder.ExecPrice)

		// 更新订单成交数据和 execution
		if futureOrder.ExecQty != 0 {
			reportOrder.ExecPrice = futureOrder.ExecPrice
			reportOrder.ExecQty = futureOrder.ExecQty

			if !isLimitOrder {
				execution.SellQty += futureOrder.ExecQty
			}

			orderValue := this.Arbitrager.Qty2Size(futureSymbol, futureOrder.ExecPrice, futureOrder.ExecQty)
			tradeValue += orderValue
			leftBaseQty -= this.Arbitrager.FutureQtyToSpotQty(futureSymbol, futureOrder.ExecQty, futureOrder.ExecPrice)
			leftCnt -= futureOrder.ExecQty
			tradeCnt += futureOrder.ExecQty
			futureAvgPrice = this.Arbitrager.CalcPrice(futureSymbol, tradeCnt, tradeValue)

			if instruction.IsFuture2Future() && direction.IsClose() {
				if this.Arbitrager.FutureInstrumentType() == exchange.USDXMarginedFutures {
					execution.UsdtQty += orderValue
				} else {
					execution.UsdtQty += orderValue * futureOrder.ExecPrice
				}
			}
		}
		(*futureOrders)[len(*futureOrders)-1] = reportOrder

		tradeBaseQty := this.Arbitrager.FutureQtyToSpotQty(futureSymbol, tradeCnt, futureAvgPrice)
		if !isLimitOrder && ((shouldSellBaseQty-tradeBaseQty)/shouldSellBaseQty > config.SplitFutureQtyTolerance) {
			// 交易数量比例大于 SplitFutureQtyTolerance，继续执行
			instruction.Infof("%s not enough traded, traded: %v, should sell: %v, try later", futureSymbol, tradeBaseQty, shouldSellBaseQty)
			continue
		}
		break
	}

	instruction.Infof("split[%v] future sold %v at price %v", index, tradeCnt, futureAvgPrice)
	return futureAvgPrice
}

// 开、平仓时检查溢价率是否满足目标，循环检查直到指令取消
func (this *Executer) checkBasisRatio(index int, instruction *Instruction) bool {
	if instruction.IsForce() {
		return true
	}

	config := instruction.GetConfig()
	pair := instruction.GetPair()
	execution := instruction.GetExecution()
	targetBasisRatio := instruction.GetTargetBasisRatio()
	direction := instruction.GetDirection()
	checkCount := 0

	for {
		if instruction.IsCanceled() {
			instruction.Infof("instruction is canceld, end split[%v]", index)
			return false
		}

		if this.Arbitrager.IsPaused() {
			execution.SleepDuration(index, ExecutionDurationTypePauseWait, time.Second*10, "")
			continue
		}

		tick := time.Now()
		currentRatio, err := this.GetBasisRatio(pair)
		execution.AddDuration(index, ExecutionDurationTypeGetBasisRatio, time.Since(tick), "")

		if err != nil {
			instruction.Errorf("GetBasisRatio err: %v", err)
			execution.SleepDuration(index, ExecutionDurationTypeBasisRatioRetryInterval, time.Millisecond*time.Duration(config.GetBasisRetryInterval), "")
			checkCount = 0
			continue
		}

		if !CheckBasisTolerance(direction, currentRatio, targetBasisRatio, config.BasisTolerance) {
			// 不满足目标，继续检查
			sign := "<"
			if direction == ArbiDirectionClose {
				sign = ">"
			}
			instruction.Infof("[%v] currentRatio[%.5f] %s TargetBasisRatio[%.5f] (tolerance: %.2f%%), wait...", index, currentRatio, sign, targetBasisRatio, config.BasisTolerance*100)
			execution.SleepDuration(index, ExecutionDurationTypeBasisToleranceWait, time.Second*time.Duration(config.BasisWait), "")
			checkCount = 0
			continue
		}

		checkCount += 1
		if index == 0 && config.BasisDoubleCheck && checkCount == 1 {
			// 需要二次检查
			instruction.Infof("basis doublle check")
			execution.SleepDuration(index, ExecutionDurationTypeBasisToleranceWait, time.Millisecond*time.Duration(config.BasisDoubleCheckWait), "")
			continue
		}

		// 记录检查通过时的溢价率
		execution.BasisCheck = append(execution.BasisCheck, currentRatio)
		break
	}

	return true
}

// 期现套利开仓 split
func (this *Executer) arbiSplit(index int, instruction Instructable, usdtQty float64) bool {
	instruction.Infof("start arbit split[%v]", index)

	direction, config := instruction.GetDirection(), instruction.GetConfig()
	pairItemBuy, pairItemSell, execution := getPairItemAndExecution(instruction, true)

	if ok, err := this.checkPairItemInstrument([]*exchange.SymbolItem{pairItemBuy, pairItemSell}); !ok { // 先确保都能获取到，防止因为 instrument 错误而出现单边情况
		instruction.Infof("check pair instrument error: %s", err)
		return false
	}

	// 买入现货，得到成交均价、可划转数量
	spotAvgPrice, boughtSpotQty := this.buySpotSplit(index, instruction, usdtQty, pairItemBuy, execution)
	if spotAvgPrice == 0 {
		return false
	}

	transferAmount := boughtSpotQty
	if this.Arbitrager.FutureInstrumentType() != exchange.CoinMarginedFutures {
		transferAmount = usdtQty // U 本位划转 U
	}

	// 执行划转
	transferedAmount := this.transferSplit(index, instruction, transferAmount, true)
	if transferedAmount == 0 {
		return false
	}

	qtyToSell := transferedAmount
	if this.Arbitrager.FutureInstrumentType() != exchange.CoinMarginedFutures {
		qtyToSell = boughtSpotQty
		if transferedAmount < transferAmount {
			// 可能余额不足导致实际划转少了，按比例减少卖出数量
			qtyToSell = qtyToSell * transferedAmount / transferAmount
		}
	}

	// 卖出期货，得到成交均价
	futureAvgPrice := this.sellFutureSplit(index, qtyToSell, instruction, pairItemSell, execution)
	if futureAvgPrice == 0 {
		return false
	}

	// 计算成交溢价率
	splitBasisRatio := futureAvgPrice/spotAvgPrice - 1
	instruction.Infof("split[%v] arbi done, splitBasisRatio: %.5f (%.2f/%.2f)", index, splitBasisRatio, futureAvgPrice, spotAvgPrice)

	// execution 记录成交溢价率
	execution.BasisActual = append(execution.BasisActual, splitBasisRatio)

	if direction == ArbiDirectionMove {
		// move 指令的溢价率 = 平仓溢价率 - 开仓溢价率
		moveInstruction := instruction.(*MoveInstruction)
		fromExecution := moveInstruction.GetFromExecution()
		toRatio := splitBasisRatio
		fromRatio := fromExecution.BasisActual[index] // 之前记录的是 fromRatio，用 toRatio - fromRatio 才是 move 的 Basis
		splitBasisRatio = toRatio - fromRatio
	}

	this.splitBasisRatioCheck(index, splitBasisRatio, instruction.GetTargetBasisRatio(), config, instruction)
	return true
}

// 期期套利开仓
func (this *Executer) arbiF2FSplit(index int, instruction Instructable, usdtQty float64) bool {
	instruction.Infof("start arbit f2f split[%v]", index)

	config := instruction.GetConfig()
	pairItemBuy, pairItemSell, execution := getPairItemAndExecution(instruction, true)
	price, err := this.Arbitrager.Exchange.GetLastPrice(this.Arbitrager.FutureInstrumentType(), pairItemBuy.Symbol, false)
	if err != nil {
		instruction.Infof("get last price error: %s", err)
		return false
	}

	var arbiSize float64
	if this.Arbitrager.FutureInstrumentType() == exchange.USDXMarginedFutures {
		arbiSize = usdtQty
	} else {
		arbiSize = usdtQty / price
	}

	cnt := this.Arbitrager.Size2Qty(pairItemBuy.Symbol, price, arbiSize)
	fromAvgPrice, amount := this.buyFutureSplit(index, cnt, instruction, pairItemBuy, execution)
	if fromAvgPrice == 0 {
		return false
	}

	toAvgPrice := this.sellFutureSplit(index, amount, instruction, pairItemSell, execution)
	splitBasisRatio := toAvgPrice/fromAvgPrice - 1
	instruction.Infof("split[%v] done, splitBasisRatio: %.5f (%.2f/%.2f)", index, splitBasisRatio, toAvgPrice, fromAvgPrice)

	execution.BasisActual = append(execution.BasisActual, splitBasisRatio)

	this.splitBasisRatioCheck(index, splitBasisRatio, instruction.GetTargetBasisRatio(), config, instruction)
	return true
}

// 检查 split 实际成交溢价率是否满足配置范围
func (this *Executer) splitBasisRatioCheck(index int, splitBasisRatio float64, targetBasisRatio float64, config *ArbitragerConfig, instruction Instructable) {
	if instruction.IsForce() {
		return
	}

	direction := instruction.GetDirection()
	ratioDelta := 0.0
	if direction == ArbiDirectionOpen || direction == ArbiDirectionMove {
		ratioDelta = targetBasisRatio - splitBasisRatio
	} else {
		ratioDelta = splitBasisRatio - targetBasisRatio
	}

	// 如果 SplitBasisErrorTolerance 设置等于 -1 ，那么将 ErrorBasis 作为绝对的报错阈值
	if config.SplitBasisErrorTolerance == -1 {
		errorBasis := config.ErrorBasis // 最小报错溢价率偏差绝对值，不为 0 时有效
		if errorBasis != 0 &&
			(((direction == ArbiDirectionOpen || direction == ArbiDirectionMove || direction == ArbiDirectionRotate) && (splitBasisRatio < errorBasis)) ||
				((direction == ArbiDirectionClose) && (splitBasisRatio > errorBasis))) {
			instruction.Cancel(CancelReasonSplitBasisError)
			this.Arbitrager.ErrorMsgf("Split[%d] 的溢价率 [%.4f] 超出预设值 %.4f，取消指令 %s。", index, splitBasisRatio, errorBasis, instruction.GetID())
		}
	} else {
		splitBasisRatioDiffRatio := ratioDelta / math.Abs(targetBasisRatio)
		if splitBasisRatioDiffRatio > config.SplitBasisErrorTolerance {
			instruction.Warnf("splitBasisRatioDiffRatio %v bigger than SplitBasisErrorTolerance %v", splitBasisRatioDiffRatio, config.SplitBasisErrorTolerance)
			instruction.Cancel(CancelReasonSplitBasisError)
			this.Arbitrager.ErrorMsgf("Split[%d] 的溢价率偏差 [%.2f %%] 超出错误范围 %.2f %%，取消指令 %s。", index, splitBasisRatioDiffRatio*100, config.SplitBasisErrorTolerance*100, instruction.GetID())
		} else if splitBasisRatioDiffRatio > config.SplitBasisWarningTolerance {
			instruction.Warnf("splitBasisRatioDiffRatio %v bigger than SplitBasisWarningTolerance %v", splitBasisRatioDiffRatio, config.SplitBasisWarningTolerance)
			this.Arbitrager.SendMsgf("Split[%d] 的溢价率偏差 [%.2f %%] 超出警告范围 %.2f %%，会在指令 %s 下一个 Split 中再次检查溢价率。", index, splitBasisRatioDiffRatio*100, config.SplitBasisWarningTolerance*100, instruction.GetID())
		}
	}
}

// 套利平仓，instruction 数量单位为合约张数
func (this *Executer) Close(ctx context.Context, instruction *Instruction) {
	instruction.Debugf("run close(%#v)", instruction)

	this.splitLoop(ctx, instruction)
}

// 买入期货 split
func (this *Executer) buyFutureSplit(index int, cnt float64, instruction Instructable, pairItem *exchange.SymbolItem, execution *InstructionExecution) (futureAvgPrice, tradeBaseQty float64) {
	instruction.Infof("buy future split %v start", index)
	direction, config := instruction.GetDirection(), instruction.GetConfig()
	// pair, execution := getPairAndExecution(instruction, false)

	futureSymbol := pairItem.Symbol
	baseCoin := pairItem.Code.Coin()

	shouldBuyCnt := cnt // 合约应成交张数
	tradeCnt := 0.0     // 已成交张数
	tradeValue := 0.0   // 成交价值

	minQty := 0.0
	if instrument := this.GetInstrument(this.Arbitrager.FutureInstrumentType(), futureSymbol); instrument != nil {
		minQty = instrument.MinSize
	} else {
		instruction.Errorf("%s instrument not exist", futureSymbol)
		return 0, 0
	}

	futureOrders := &[]*exchange.Order{}
	execution.BuyOrders = append(execution.BuyOrders, futureOrders)
	reducedCnt := -1.0 // 由于余额不足而稍微减少后的张数，为 -1 时表示没有减少处理

	retryCountAfterCanceled := 0
	executeCount := 0
	for {
		executeCount += 1
		if executeCount > MAX_EXECUTE_TIMES {
			// 超过最大执行次数，中断执行并取消指令
			instruction.SetSingleSideReason(SingleSideReasonCloseFuture)
			instruction.Cancel(CancelReasonMaxExecuteTimes)
			this.Arbitrager.ErrorMsgf("指令 %s Split[%v] 买入合约异常终止：超过最大重试次数 %v。当前已成交 %v 张。", instruction.GetID(), index, MAX_EXECUTE_TIMES, tradeCnt)
			return 0, 0
		}

		if retryCountAfterCanceled == 3 {
			// 指令取消后重试 3 次则中断执行
			instruction.SetSingleSideReason(SingleSideReasonCloseFuture)
			this.Arbitrager.ErrorMsgf("指令 %s 取消后 Split[%v] 买入合约异常终止。当前已成交 %v 张。", instruction.GetID(), index, tradeCnt)
			return 0, 0
		}

		limitPrice := instruction.GetPrice()
		submit, lastPrice, err := this.getCreateOrderArgs(this.Arbitrager.FutureInstrumentType(), futureSymbol, shouldBuyCnt-tradeCnt, limitPrice, exchange.OrderSideBuy, direction, config, execution)
		if err != nil {
			instruction.Errorf("future get order submission err: %v", err)
			execution.SleepDuration(index, ExecutionDurationTypeBuyRetryInterval, time.Millisecond*time.Duration(config.FutureRetryInterval), "")
			if instruction.IsCanceled() {
				retryCountAfterCanceled += 1
			}
			continue
		}
		instruction.Debugf("submit future order: %#v", submit)

		if reducedCnt >= 0 {
			// 经过减少处理后的张数
			submit.Qty = reducedCnt
		}

		if submit.Qty == 0 || submit.Qty < minQty {
			instruction.Warnf("future order amount(%v) too small to place", submit.Qty)
			break
		}

		instruction.Infof("prepare to buy %v %v at price %v", submit.Qty, futureSymbol, submit.Price)

		reportOrder := &exchange.Order{
			RefID:          exchange.NewRandomID(),
			Symbol:         futureSymbol,
			InstrumentType: this.Arbitrager.FutureInstrumentType(),
			Qty:            submit.Qty,
			LastPrice:      lastPrice,
			Price:          submit.Price,
		}
		*futureOrders = append(*futureOrders, reportOrder)
		requestStartTime := time.Now()
		reportOrder.SetTime(ExtKeyStartTime, &requestStartTime)

		futureOrderResp, oErr := this.submitOrder(submit)
		execution.endOrderRequest(reportOrder, index)
		if oErr != nil {
			instruction.Errorf("future SubmitOrder err: %v", oErr)
			useAvailableCnt := false // 平仓可用张数不足时，获取最新持仓张数
			if oErr.ErrorCode == SubmitOrderErrorCodeOrderRejected {
				useAvailableCnt = true
			} else if oErr.ErrorCode == SubmitOrderErrorCodeInsufficientBalance {
				if direction.IsClose() || direction.IsMove() {
					useAvailableCnt = true
				} else {
					// 余额不足，稍微减少处理
					reducedCnt = reduceCntSlightly(submit.Qty, minQty)
				}
			}

			if useAvailableCnt {
				_, positionCnt, err := this.Arbitrager.Exchange.GetHoldingQty(this.Arbitrager.FutureInstrumentType(), futureSymbol)
				if err != nil {
					instruction.Errorf("useAvailableCnt, get future qty err: %v", err)
				} else {
					instruction.Infof("useAvailableCnt, left cnt: %v", positionCnt)
					if positionCnt == 0 {
						break
					}
					if submit.Qty > math.Abs(positionCnt) {
						shouldBuyCnt -= (submit.Qty - math.Abs(positionCnt))
					}
				}
			}

			execution.SleepDuration(index, ExecutionDurationTypeBuyRetryInterval, time.Millisecond*time.Duration(config.FutureRetryInterval), "")
			if instruction.IsCanceled() {
				retryCountAfterCanceled += 1
			}
			continue
		}

		if futureOrderResp.OrderID == "" {
			instruction.Errorf("future SubmitOrder failed: orderID is empty")
			execution.SleepDuration(index, ExecutionDurationTypeBuyRetryInterval, time.Millisecond*time.Duration(config.FutureRetryInterval), "")
			if instruction.IsCanceled() {
				retryCountAfterCanceled += 1
			}
			continue
		}
		reducedCnt = -1.0 // 订单提交成功，重置 reducedCnt
		instruction.Infof("future order resp: %#v", futureOrderResp)
		updateReportOrder(reportOrder, futureOrderResp)

		// 接口返回无成交信息，需主动查询
		var futureOrder *exchange.Order
		isLimitOrder := limitPrice != 0
		queryCount := 0
		for {
			if retryCountAfterCanceled == 3 {
				// 指令取消后重试 3 次则中断执行
				instruction.SetSingleSideReason(SingleSideReasonCloseFuture)
				this.Arbitrager.ErrorMsgf("指令 %s 取消后 Split[%v] 买入合约异常终止。当前已成交 %v 张。", instruction.GetID(), index, tradeCnt)
				return 0, 0
			}

			queryCount += 1
			futureOrder, err = this.Arbitrager.Exchange.GetOrderByOrig(*futureOrderResp)
			if err != nil {
				instruction.Errorf("query future order %s err: %v", futureOrderResp.OrderID, err)
				execution.SleepDuration(index, ExecutionDurationTypeBuyRetryInterval, time.Millisecond*time.Duration(config.FutureRetryInterval), "")
				if instruction.IsCanceled() {
					retryCountAfterCanceled += 1
				}

				executeCount += 1
				if executeCount > MAX_EXECUTE_TIMES {
					// 超过最大执行次数，中断执行并取消指令
					instruction.SetSingleSideReason(SingleSideReasonCloseFuture)
					instruction.Cancel(CancelReasonMaxExecuteTimes)
					this.Arbitrager.ErrorMsgf("指令 %s Split[%v] 买入合约异常终止：超过最大重试次数 %v。当前已成交 %v 张。", instruction.GetID(), index, MAX_EXECUTE_TIMES, tradeCnt)
					return 0, 0
				}

				continue
			}

			if isLimitOrder {
				if futureOrder.ExecQty != 0 {
					execution.BuyQty = futureOrder.ExecQty

					reportOrder.ExecPrice = futureOrder.ExecPrice
					reportOrder.ExecQty = futureOrder.ExecQty
					(*futureOrders)[len(*futureOrders)-1] = reportOrder
				}

				if !futureOrder.IsOpen() {
					// 订单已是最终状态
					break
				}

				if instruction.IsCanceled() {
					if err := this.Arbitrager.Exchange.CancelOrder(this.Arbitrager.FutureInstrumentType(), exchange.Limit, submit.Symbol, futureOrderResp.OrderID); err != nil {
						instruction.Errorf("cancel limit order %s err: %v", futureOrderResp.OrderID, err)
					}
					break
				}

				// 继续查询
				instruction.Debugf("limit order %s waiting...", futureOrderResp.OrderID)
				execution.SleepDuration(index, ExecutionDurationTypeBuyRetryInterval, time.Second*10, "")
				continue
			} else {
				// IOC 订单
				if futureOrder.IsOpen() {
					instruction.Infof("IOC order %s is still open, waiting...", futureOrderResp.OrderID)
					execution.SleepDuration(index, ExecutionDurationTypeBuyRetryInterval, time.Second*3, "")
					continue
				} else if futureOrder.ExecQty == 0 && queryCount == 1 {
					// OKEx IOC 订单会出现实际已成交但返回数量为 0 的情况，稍等再查一次
					instruction.Infof("IOC order %s query got 0 cnt, query one more time.", futureOrderResp.OrderID)
					execution.SleepDuration(index, ExecutionDurationTypeBuyRetryInterval, time.Second*3, "")
					continue
				}
			}

			break
		}

		instruction.Infof("%s traded %v cnt at price %v", futureSymbol, futureOrder.ExecQty, futureOrder.ExecPrice)

		// 记录订单成交数据 & execution
		if futureOrder.ExecQty != 0 {
			reportOrder.ExecPrice = futureOrder.ExecPrice
			reportOrder.ExecQty = futureOrder.ExecQty

			if !isLimitOrder {
				execution.BuyQty += futureOrder.ExecQty
			}

			tradeCnt += futureOrder.ExecQty
			orderValue := this.Arbitrager.Qty2Size(futureSymbol, futureOrder.ExecPrice, futureOrder.ExecQty)
			tradeValue += orderValue
			futureAvgPrice = this.Arbitrager.CalcPrice(futureSymbol, tradeCnt, tradeValue)

			if instruction.IsFuture2Future() && direction.IsOpen() {
				if this.Arbitrager.FutureInstrumentType() == exchange.USDXMarginedFutures {
					execution.UsdtQty += orderValue
				} else {
					execution.UsdtQty += orderValue * futureOrder.ExecPrice
				}
			}
		}
		(*futureOrders)[len(*futureOrders)-1] = reportOrder

		if !isLimitOrder && ((shouldBuyCnt-tradeCnt)/shouldBuyCnt > config.SplitFutureQtyTolerance) {
			// 买入不足 SplitFutureQtyTolerance，继续执行
			instruction.Infof("%s not enough traded, tradeCnt: %v, shouldBuyCnt: %v, try later", futureSymbol, tradeCnt, shouldBuyCnt)
			continue
		}
		break
	}

	tradeBaseQty = tradeValue
	if this.Arbitrager.FutureInstrumentType() != exchange.CoinMarginedFutures && tradeValue > 0 {
		// USD/USDx 本位价值是 USD/USDx，除以价格得到币数量
		tradeBaseQty = tradeValue / futureAvgPrice
	}

	instruction.Infof("split[%v] future bought %v cnt(%v %s) at price %v", index, tradeCnt, tradeBaseQty, baseCoin, futureAvgPrice)
	return
}

// 卖出现货 split，传入 shouldSellQty 为币种数量
// 返回成交均价、卖得 USDx 数量
func (this *Executer) sellSpotSplit(index int, shouldSellQty float64, instruction Instructable, pairItem *exchange.SymbolItem, execution *InstructionExecution) (spotAvgPrice, usdtQty float64) {
	instruction.Infof("sell spot split %v start", index)
	direction, config := instruction.GetDirection(), instruction.GetConfig()
	// pair, execution := getPairAndExecution(instruction, false)

	minNotional := 0.0
	minQty := 0.0
	if instrument := this.GetInstrument(exchange.Spot, pairItem.Symbol); instrument != nil {
		minNotional = instrument.MinNotional
		minQty = instrument.MinSize
	} else {
		instruction.Errorf("%s instrument not exist", pairItem.Symbol)
		return 0, 0
	}

	spotOrders := &[]*exchange.Order{}
	execution.SellOrders = append(execution.SellOrders, spotOrders)

	spotQty := 0.0    // 已成交现货数量
	tradeValue := 0.0 // 已成交 USDx 价值
	feeAmount := 0.0  // 交易费数量
	retryCountAfterCanceled := 0
	coin := pairItem.Code.Coin()

	executeCount := 0
	for {
		executeCount += 1
		if executeCount > MAX_EXECUTE_TIMES {
			// 超过最大执行次数，中断执行并取消指令
			instruction.SetSingleSideReason(SingleSideReasonCloseSpot)
			instruction.Cancel(CancelReasonMaxExecuteTimes)
			this.Arbitrager.ErrorMsgf("指令 %s Split[%v] 卖出现货异常终止：超过最大重试次数 %v。当前已成交 %v %s。", instruction.GetID(), index, MAX_EXECUTE_TIMES, spotQty, coin)
			return
		}

		if retryCountAfterCanceled == 3 {
			// 指令取消后重试 3 次则中断执行
			instruction.SetSingleSideReason(SingleSideReasonCloseSpot)
			this.Arbitrager.ErrorMsgf("指令 %s 取消后 Split[%v] 卖出现货异常终止。当前已成交 %v %s。", instruction.GetID(), index, spotQty, coin)
			return 0, 0
		}

		limitPrice := instruction.GetPrice()
		submit, lastPrice, err := this.getCreateOrderArgs(exchange.Spot, pairItem.Symbol, shouldSellQty-spotQty, limitPrice, exchange.OrderSideSell, direction, config, execution)
		if err != nil {
			instruction.Errorf("spot get order submission err: %v", err)
			execution.SleepDuration(index, ExecutionDurationTypeSellRetryInterval, time.Millisecond*time.Duration(config.SpotRetryInterval), "")
			if instruction.IsCanceled() {
				retryCountAfterCanceled += 1
			}
			continue
		}

		instruction.Debugf("submit spot order: %#v", submit)
		instruction.Infof("prepare to sell %v %v at price %v", submit.Qty, coin, submit.Price)

		if (submit.Price*submit.Qty < minNotional) || submit.Qty == 0 || submit.Qty < minQty {
			// 下单价值小于最小价值，不继续执行
			instruction.Warnf("spot order value/amount(%v/%v) too small to place", submit.Price*submit.Qty, submit.Qty)
			break
		}

		reportOrder := &exchange.Order{
			RefID:          exchange.NewRandomID(),
			Symbol:         pairItem.Symbol,
			InstrumentType: exchange.Spot,
			Qty:            submit.Qty,
			LastPrice:      lastPrice,
			Price:          submit.Price,
		}
		*spotOrders = append(*spotOrders, reportOrder)
		requestStartTime := time.Now()
		reportOrder.SetTime(ExtKeyStartTime, &requestStartTime)

		spotOrderResp, oErr := this.submitOrder(submit)
		execution.endOrderRequest(reportOrder, index)
		if oErr != nil {
			instruction.Errorf("spot SubmitOrder err: %v", oErr)
			if oErr.ErrorCode == SubmitOrderErrorCodeInsufficientBalance {
				// 余额不足，获取最新可以余额
				_, availableQty, err := this.Arbitrager.Exchange.GetHoldingQty(exchange.Spot, coin)
				if err != nil {
					instruction.Errorf("insufficient balance, get coin qty err: %v", err)
				} else {
					instruction.Infof("insufficient balance, left qty: %v", availableQty)
					if availableQty == 0 {
						break
					}
					if submit.Qty > availableQty {
						shouldSellQty -= (submit.Qty - availableQty)
					}
				}
			}
			execution.SleepDuration(index, ExecutionDurationTypeSellRetryInterval, time.Millisecond*time.Duration(config.SpotRetryInterval), "")
			if instruction.IsCanceled() {
				retryCountAfterCanceled += 1
			}
			continue
		}
		instruction.Debugf("spot order resp: %#v", spotOrderResp)
		updateReportOrder(reportOrder, spotOrderResp)

		isLimitOrder := limitPrice != 0
		if isLimitOrder {
			// 限价单
			var tradePrice float64
			for {
				var orderEnded bool
				tradePrice, spotQty, orderEnded = this.queryLimitOrder(instruction, spotOrderResp)

				execution.UsdtQty = spotQty * tradePrice
				execution.SellQty = spotQty

				if spotQty != 0 {
					reportOrder.ExecPrice = tradePrice
					reportOrder.ExecQty = spotQty
				}
				(*spotOrders)[len(*spotOrders)-1] = reportOrder

				if orderEnded {
					break
				}
			}
			tradeValue = spotQty * tradePrice
			usdtQty += spotQty * tradePrice
		} else {
			// IOC 订单
			orderTradeValue := 0.0
			orderTradeAmount := 0.0

			// bn 直接返回了 trades, 其他交易所需要单独查询成交
			if !this.IsExchange(exchange.Binance) {
				for {
					orderInfo, err := this.Arbitrager.Exchange.GetOrderByOrig(*spotOrderResp)
					if err != nil {
						instruction.Errorf("spot GetOrderInfo(%s) err: %v", spotOrderResp.OrderID, err)

						executeCount += 1
						if executeCount > MAX_EXECUTE_TIMES {
							instruction.SetSingleSideReason(SingleSideReasonCloseSpot)
							instruction.Cancel(CancelReasonMaxExecuteTimes)
							this.Arbitrager.ErrorMsgf("指令 %s Split[%v] 卖出现货异常终止：超过最大重试次数 %v。当前已成交 %v %s。", instruction.GetID(), index, MAX_EXECUTE_TIMES, spotQty, coin)
							return
						}

						continue
					}

					if orderInfo.IsOpen() {
						instruction.Infof("order(%s) still open", spotOrderResp.OrderID)
						continue
					}

					trade := exchange.TradeHistory{
						Qty:      orderInfo.ExecQty,
						Price:    orderInfo.ExecPrice,
						Fee:      orderInfo.Fee,
						FeeAsset: orderInfo.FeeAsset,
					}
					spotOrderResp.Trades = []exchange.TradeHistory{trade}
					break
				}
			}

			for _, trade := range spotOrderResp.Trades {
				spotQty += trade.Qty
				tradeValue += trade.Qty * trade.Price
				usdtQty += trade.Qty * trade.Price

				if trade.FeeAsset == coin {
					feeAmount += trade.Fee
					execution.SpotFee += trade.Fee * trade.Price
					execution.SellQty -= trade.Fee
				} else if trade.FeeAsset == pairItem.Code.USDXSymbol {
					execution.SpotFee += trade.Fee
					usdtQty -= trade.Fee
				}

				orderTradeValue += trade.Qty * trade.Price
				orderTradeAmount += trade.Qty

				execution.UsdtQty += trade.Qty * trade.Price
				execution.SellQty += trade.Qty
			}

			instruction.Infof("spot traded %v %v", spotQty, coin)
			if orderTradeAmount != 0 {
				reportOrder.ExecPrice = orderTradeValue / orderTradeAmount
				reportOrder.ExecQty = orderTradeAmount
			}
			(*spotOrders)[len(*spotOrders)-1] = reportOrder

			if (shouldSellQty-spotQty)/shouldSellQty > config.SplitSpotQtyTolerance {
				// 成交数量比例大于 SplitSpotQtyTolerance，继续执行
				instruction.Infof("%s not enough traded, tradeAmount: %v, shouldSellAmount: %v, try later", pairItem.Symbol, spotQty, shouldSellQty)
				continue
			}
		}

		break
	}

	if spotQty != 0 {
		spotAvgPrice = tradeValue / spotQty
	}
	instruction.Infof("split[%v] spot sold %v %v, avg price %v", index, spotQty, coin, spotAvgPrice)
	return
}

// 期现套利平仓 split
func (this *Executer) closeSplit(index int, instruction Instructable, cnt float64) (usdtQty float64) {
	instruction.Infof("start close split[%v]", index)
	direction, config := instruction.GetDirection(), instruction.GetConfig()
	pairItemBuy, pairItemSell, execution := getPairItemAndExecution(instruction, false)

	// 平仓期货
	futureAvgPrice, tradeBaseQty := this.buyFutureSplit(index, cnt, instruction, pairItemBuy, execution)
	if futureAvgPrice == 0 {
		return
	}

	transferAmount := tradeBaseQty
	coin := pairItemSell.Code.Coin()
	if this.Arbitrager.FutureInstrumentType() != exchange.CoinMarginedFutures {
		coin = this.Arbitrager.Exchange.GetSymbolCodeQuote(pairItemSell.Code)
		transferAmount = futureAvgPrice * tradeBaseQty
	}

	var transferedAmount float64

	isOKCross := this.IsExchange(exchange.OKEx) && this.Arbitrager.IsCrossMode()
	if isOKCross {
		// OK 跨币种保证金模式不用走划转流程，直接卖现货
		// 比如 U 本位全仓平仓期货后，可以 U 可能还是负数
		transferedAmount = transferAmount
	} else {
		available, _ := this.getFutureCoinAvailableBalance(coin)
		if available < transferAmount {
			// 实际可划账数量小于平仓交易的数量，取实际可划账数量
			if available < (transferAmount * 0.8) {
				instruction.Warnf("%s available amount(%v) too small than future traded amount %v", coin, available, transferAmount)
			}
			transferAmount = available
		}

		if transferAmount == 0 {
			instruction.Warnf("no %s can transfer, end split", coin)
			return
		}

		// 划账到现货
		transferedAmount = this.transferSplit(index, instruction, transferAmount, false)
		if transferedAmount == 0 {
			return
		}
	}

	qtyToSell := transferedAmount
	if this.Arbitrager.FutureInstrumentType() != exchange.CoinMarginedFutures {
		qtyToSell = tradeBaseQty
		if transferedAmount < transferAmount {
			// 可能余额不足导致实际划转少了，按比例减少卖出数量
			qtyToSell = qtyToSell * transferedAmount / transferAmount
		}
	}

	// 卖出现货
	spotAvgPrice, usdtQty := this.sellSpotSplit(index, qtyToSell, instruction, pairItemSell, execution)
	if spotAvgPrice == 0 {
		return
	}

	splitBasisRatio := futureAvgPrice/spotAvgPrice - 1
	instruction.Infof("split[%v] close done, splitBasisRatio: %.5f (%.2f/%.2f)", index, splitBasisRatio, futureAvgPrice, spotAvgPrice)

	// 记录实际成交溢价率
	execution.BasisActual = append(execution.BasisActual, splitBasisRatio)

	if direction == ArbiDirectionMove {
		// move 指令不检查成交溢价率
		return
	}

	this.splitBasisRatioCheck(index, splitBasisRatio, instruction.GetTargetBasisRatio(), config, instruction)
	return
}

// 期期套利平仓 split
func (this *Executer) closeF2FSplit(index int, instruction Instructable, cnt float64) bool {
	instruction.Infof("start close f2f split[%v]", index)

	config := instruction.GetConfig()
	pairItemBuy, pairItemSell, execution := getPairItemAndExecution(instruction, false)

	fromAvgPrice, amount := this.buyFutureSplit(index, cnt, instruction, pairItemBuy, execution)
	if fromAvgPrice == 0 {
		return false
	}

	toAvgPrice := this.sellFutureSplit(index, amount, instruction, pairItemSell, execution)
	if toAvgPrice == 0 {
		return false
	}

	splitBasisRatio := fromAvgPrice/toAvgPrice - 1
	instruction.Infof("split[%v] done, splitBasisRatio: %.5f (%.2f/%.2f)", index, splitBasisRatio, toAvgPrice, fromAvgPrice)

	execution.BasisActual = append(execution.BasisActual, splitBasisRatio)

	this.splitBasisRatioCheck(index, splitBasisRatio, instruction.GetTargetBasisRatio(), config, instruction)
	return true
}

// 展期 split
func (this *Executer) rotateSplit(index int, instruction Instructable, cnt float64) {
	// 平仓展期原合约
	pairItemBuy, _, execution := getPairItemAndExecution(instruction, false)
	fromAvgPrice, amount := this.buyFutureSplit(index, cnt, instruction, pairItemBuy, execution)
	if fromAvgPrice == 0 {
		return
	}
	// 开仓展期目标合约
	_, pairItemSell, executionSell := getPairItemAndExecution(instruction, true)
	toAvgPrice := this.sellFutureSplit(index, amount, instruction, pairItemSell, executionSell)

	splitBasisRatio := toAvgPrice/fromAvgPrice - 1
	instruction.Infof("split[%v] rotate done, splitBasisRatio: %.5f (%.2f/%.2f)", index, splitBasisRatio, toAvgPrice, fromAvgPrice)

	// 记录展期成交溢价率
	execution.BasisActual = append(execution.BasisActual, splitBasisRatio)
}

// 获取 SymbolPair 对应 Left~Right 溢价率
// 这里不用 GetBuyItem/GetSellItem 函数，是因为 Left~Right 的用法更加纯粹，并且不在 XXXInstruction 的上下文中
func (this *Executer) GetBasisRatio(pair *ArbiPair) (basisRatio float64, er error) {
	allowCache := false
	// 所有永续合约共用 lock ，交割合约每个合约一个 lock
	// OK 的频率限制很严格，使用缓存
	if this.IsExchange(exchange.OKEx) {
		allowCache = true
	}

	var keyLeft, keyRight string
	if pair.Left.Code.IsSpot() {
		keyLeft = "spot"
	} else if pair.Left.Code.IsPerp() {
		keyLeft = "perp"
	} else {
		keyLeft = pair.Left.Code.Code
	}

	if pair.Right.Code.IsSpot() {
		keyRight = "spot"
	} else if pair.Right.Code.IsPerp() {
		keyRight = "perp"
	} else {
		keyRight = pair.Right.Code.Code
	}

	mutexKey := fmt.Sprintf("%s~%s", keyLeft, keyRight)

	var mutex *sync.Mutex = nil
	if m := this.basisRatiosMutex[mutexKey]; m != nil {
		mutex = m
	} else {
		this.basisRatiosMutex[mutexKey] = &sync.Mutex{}
		mutex = this.basisRatiosMutex[mutexKey]
	}
	mutex.Lock()
	defer mutex.Unlock()

	leftPrice := 0.0
	rightPrice := 0.0
	leftSymbol := pair.Left.Symbol
	rightSymbol := pair.Right.Symbol
	cacheKey := fmt.Sprintf("%s||%s", leftSymbol, rightSymbol)

	if item, found := this.cachedBasisRatios[cacheKey]; found && allowCache {
		since := time.Since(item.CacheTime)
		discountedSplitWait := int(float64(this.Arbitrager.Config.SplitWait) * 0.4)
		timeDiff := since - time.Duration(time.Duration(discountedSplitWait)*time.Millisecond)
		if timeDiff < 0 {
			basisRatio = item.BasisRatio
			this.Arbitrager.Debugf("get basis ratio cache for %s, basis ratio %.4f, cache time: %s", cacheKey, basisRatio, item.CacheTime)
			return
		}
	}

	if price, err := this.Arbitrager.Exchange.GetLastPrice(pair.Left.Code.InstrumentType(), leftSymbol, false); err != nil {
		er = fmt.Errorf("get left price failed for get basis ratio, left symbol (%s), error: %s", leftSymbol, err)
		return
	} else {
		leftPrice = price

		if price, err := this.Arbitrager.Exchange.GetLastPrice(pair.Right.Code.InstrumentType(), rightSymbol, false); err != nil {
			er = fmt.Errorf("get right price failed for get basis ratio, right symbol (%s), error: %s", rightSymbol, err)
			return
		} else {
			rightPrice = price
		}
	}

	basisRatio = rightPrice/leftPrice - 1
	nowTime := time.Now()
	if item, found := this.cachedBasisRatios[cacheKey]; found {
		item.BasisRatio = basisRatio
		item.CacheTime = nowTime
	} else {
		if !math.IsNaN(basisRatio) {
			item := &BasisRatioCacheItem{BasisRatio: basisRatio, CacheTime: nowTime}
			this.cachedBasisRatios[cacheKey] = item
		} else {
			this.Arbitrager.Debugf("write basis ratio cache error, basisRatio is NaN")
		}
	}
	return
}

// 获取期货账号可用币种余额
func (this *Executer) getFutureCoinAvailableBalance(coinCode string) (available float64, er error) {
	total, available, er := this.Arbitrager.Exchange.GetBalance(this.Arbitrager.FutureInstrumentType(), coinCode)
	if total > available {
		// 可能还有持仓，可划账余额是动态的，稍微减少点
		available *= 0.9999
	}
	return available, er
}

// 获取现货账号所有持仓
func (this *Executer) ListSpotBalances() (balances []*exchange.AccountBalance, er error) {
	if this.Arbitrager.Exchange == nil {
		return balances, errors.New("exchange not initialized")
	}
	return this.Arbitrager.Exchange.GetAccountBalances(exchange.Spot)
}

// 移仓，instruction 数量单位为平仓合约张数
func (this *Executer) Move(ctx context.Context, instruction *MoveInstruction) {
	this.setInitLeverage(instruction.GetToPair().GetSellItem().Symbol)
	this.splitLoop(ctx, instruction)
}

// 持仓展期，instruction 数量单位为合约张数
func (this *Executer) Rotate(ctx context.Context, instruction *RotateInstruction) {
	instruction.Debugf("rotate with instruction: %#v", instruction)
	this.setInitLeverage(instruction.GetToPair().GetSellItem().Symbol)
	this.splitLoop(ctx, instruction)
}

// 检测 move 溢价率是否满足目标，循环检查直到指令取消
func (this *Executer) checkMoveBasisRatio(splitIndex int, instruction *MoveInstruction) bool {
	if instruction.IsForce() {
		return true
	}

	config := instruction.Config
	execution := instruction.GetFromExecution()
	checkCount := 0
	// 获取当前价差与现价差需 小于 BasisTolerence
	for {
		if instruction.IsCanceled() {
			instruction.Infof("instruction is canceld, end split[%v]", splitIndex)
			return false
		}

		if this.Arbitrager.IsPaused() {
			execution.SleepDuration(splitIndex, ExecutionDurationTypePauseWait, time.Second*10, "")
			continue
		}

		tick := time.Now()
		fromRatio, err := this.GetBasisRatio(instruction.GetFromPair())
		execution.AddDuration(splitIndex, ExecutionDurationTypeGetBasisRatio, time.Since(tick), "")

		if err != nil {
			instruction.Errorf("get from basisRatio err: %v", err)
			execution.SleepDuration(splitIndex, ExecutionDurationTypeBasisRatioRetryInterval, time.Millisecond*time.Duration(config.GetBasisRetryInterval), "")
			continue
		}

		tick = time.Now()
		toRatio, err := this.GetBasisRatio(instruction.GetToPair())
		instruction.GetToExecution().AddDuration(splitIndex, ExecutionDurationTypeGetBasisRatio, time.Since(tick), "")

		if err != nil {
			instruction.Errorf("get to basisRatio err: %v", err)
			instruction.GetToExecution().SleepDuration(splitIndex, ExecutionDurationTypeBasisRatioRetryInterval, time.Millisecond*time.Duration(config.GetBasisRetryInterval), "")
			continue
		}

		currentRatio := toRatio - fromRatio
		targetBasisRatio := instruction.TargetBasisRatio

		if !CheckBasisTolerance(ArbiDirectionMove, currentRatio, targetBasisRatio, config.MoveBasisTolerance) {
			instruction.Infof("[%v] currentRatio[%.5f] < TargetBasisRatio[%.5f] (tolerance: %.2f%%), wait...", splitIndex, currentRatio, targetBasisRatio, config.MoveBasisTolerance*100)
			execution.SleepDuration(splitIndex, ExecutionDurationTypeBasisToleranceWait, time.Second*time.Duration(config.BasisWait), "")
			continue
		}

		checkCount += 1
		if splitIndex == 0 && config.BasisDoubleCheck && checkCount == 1 {
			// 二次检查
			instruction.Infof("basis doublle check")
			execution.SleepDuration(splitIndex, ExecutionDurationTypeBasisToleranceWait, time.Millisecond*time.Duration(config.BasisDoubleCheckWait), "")
			continue
		}

		execution.BasisCheck = append(execution.BasisCheck, fromRatio)
		toExecution := instruction.GetToExecution()
		toExecution.BasisCheck = append(toExecution.BasisCheck, toRatio)
		break
	}

	return true
}

// 检测 rotate 溢价率是否满足目标，循环检查直到指令取消
func (this *Executer) checkRotateBasisRatio(splitIndex int, instruction *RotateInstruction) bool {
	if instruction.IsForce() {
		return true
	}

	config := instruction.Config
	execution := instruction.GetFromExecution()
	checkCount := 0
	// 获取当前价差与现价差需 小于 BasisTolerence
	for {
		if instruction.IsCanceled() {
			instruction.Infof("instruction is canceld, end split[%v]", splitIndex)
			return false
		}

		if this.Arbitrager.IsPaused() {
			execution.SleepDuration(splitIndex, ExecutionDurationTypePauseWait, time.Second*10, "")
			continue
		}

		tick := time.Now()
		fromRatio, err := this.GetBasisRatio(instruction.GetFromPair())
		execution.AddDuration(splitIndex, ExecutionDurationTypeGetBasisRatio, time.Since(tick), "")

		if err != nil {
			instruction.Errorf("get from basisRatio err: %v", err)
			execution.SleepDuration(splitIndex, ExecutionDurationTypeBasisRatioRetryInterval, time.Millisecond*time.Duration(config.GetBasisRetryInterval), "")
			continue
		}

		tick = time.Now()
		toRatio, err := this.GetBasisRatio(instruction.GetToPair())
		instruction.GetToExecution().AddDuration(splitIndex, ExecutionDurationTypeGetBasisRatio, time.Since(tick), "")

		if err != nil {
			instruction.Errorf("get to basisRatio err: %v", err)
			instruction.GetToExecution().SleepDuration(splitIndex, ExecutionDurationTypeBasisRatioRetryInterval, time.Millisecond*time.Duration(config.GetBasisRetryInterval), "")
			continue
		}

		currentRatio := toRatio - fromRatio
		targetBasisRatio := instruction.TargetBasisRatio

		if !CheckBasisTolerance(ArbiDirectionRotate, currentRatio, targetBasisRatio, config.RotateBasisTolerance) {
			instruction.Infof("[%v] currentRatio[%.5f] < TargetBasisRatio[%.5f] (tolerance: %.2f%%), wait...", splitIndex, currentRatio, targetBasisRatio, config.RotateBasisTolerance*100)
			execution.SleepDuration(splitIndex, ExecutionDurationTypeBasisToleranceWait, time.Second*time.Duration(config.BasisWait), "")
			continue
		}

		checkCount += 1
		if splitIndex == 0 && config.BasisDoubleCheck && checkCount == 1 {
			// 二次检查
			instruction.Infof("basis doublle check")
			execution.SleepDuration(splitIndex, ExecutionDurationTypeBasisToleranceWait, time.Millisecond*time.Duration(config.BasisDoubleCheckWait), "")
			continue
		}

		execution.BasisCheck = append(execution.BasisCheck, currentRatio)
		break
	}

	return true
}

// 生成订单提交参数
func (this *Executer) getCreateOrderArgs(instrumentType exchange.InstrumentType, symbol string, usdtOrQtyOrCnt, limitPrice float64, orderSide exchange.OrderSide, direction ArbiDirection, config *ArbitragerConfig, execution *InstructionExecution) (args exchange.CreateOrderArgs, _ float64, _ error) {
	var price float64
	tick := time.Now()
	if instrumentType == exchange.Spot {
		// 现货订单，获取现货现价
		sPrice, err := this.Arbitrager.Exchange.GetLastPrice(instrumentType, symbol, false)
		execution.AddDuration(-1, ExecutionDurationTypeGetSpotPrice, time.Since(tick), "")
		if err != nil {
			return args, 0, fmt.Errorf("get price err: %v", err)
		}
		price = sPrice
	} else {
		// 期货订单，获取期货现价
		fPrice, err := this.Arbitrager.Exchange.GetLastPrice(instrumentType, symbol, false)
		execution.AddDuration(-1, ExecutionDurationTypeGetFuturePrice, time.Since(tick), "")
		if err != nil {
			return args, 0, fmt.Errorf("get price err: %v", err)
		}
		price = fPrice
	}
	lastPrice := price
	this.Arbitrager.Debugf("%s last price: %v", symbol, lastPrice)

	var tickSize float64
	var stepSize float64

	if instrument := this.GetInstrument(instrumentType, symbol); instrument != nil {
		tickSize = instrument.TickSize
		stepSize = instrument.LotSize
	} else {
		return args, lastPrice, fmt.Errorf("%s instrument not exist", symbol)
	}

	slippage := config.SpotSlippage
	if instrumentType.IsFuture() {
		slippage = config.FutureSlippage
	}

	// 价格根据买卖方向进行滑点
	if orderSide == exchange.OrderSideBuy {
		price = price * (1 + slippage)
	} else {
		price = price * (1 - slippage)
	}

	if limitPrice != 0 {
		price = limitPrice
	}

	var qty float64 // 订单提交数量，现货为币种数量，期货为张数

	// qty 根据是否现货、指令不同所表示的数量单位不同
	if instrumentType == exchange.Spot {
		if direction == ArbiDirectionOpen {
			qty = usdtOrQtyOrCnt / price // 开仓 usdtOrQtyOrCnt 为 USDx 数量
		} else if direction == ArbiDirectionClose {
			qty = usdtOrQtyOrCnt // 平仓 usdtOrQtyOrCnt 为币种数量
		} else if direction == ArbiDirectionMove {
			if orderSide == exchange.OrderSideBuy {
				qty = usdtOrQtyOrCnt / price // move 开仓时买入 usdtOrQtyOrCnt 为 USDx 数量
			} else {
				qty = usdtOrQtyOrCnt // move 平仓时卖出 usdtOrQtyOrCnt 为币种数量
			}
		}
	} else {
		if direction == ArbiDirectionOpen {
			if orderSide == exchange.OrderSideBuy {
				qty = usdtOrQtyOrCnt // F2F 是开仓买合约，usdtOrQtyOrCnt 为合约张数; S2F 开仓买入的是 Spot
			} else {
				qty = this.Arbitrager.SpotQtyToFutureQty(symbol, usdtOrQtyOrCnt, price) // 开仓 usdtOrQtyOrCnt 为币种数量
			}
		} else if direction == ArbiDirectionClose {
			if orderSide == exchange.OrderSideSell {
				qty = this.Arbitrager.SpotQtyToFutureQty(symbol, usdtOrQtyOrCnt, price) // F2F 平仓卖合约，usdtOrQtyOrCnt 为币种数量；S2F 平仓卖 Spot
			} else {
				qty = usdtOrQtyOrCnt // 平仓 usdtOrQtyOrCnt 为合约张数
			}
		} else if direction == ArbiDirectionMove || direction == ArbiDirectionRotate {
			if orderSide == exchange.OrderSideBuy {
				qty = usdtOrQtyOrCnt // move 或 rotate 买入平仓 usdtOrQtyOrCnt 为合约张数
			} else {
				qty = this.Arbitrager.SpotQtyToFutureQty(symbol, usdtOrQtyOrCnt, price) // move 或 rotate 卖出开仓 usdtOrQtyOrCnt 为币种数量
			}
		}
	}

	// qty 按 stepSize 为最小单位向下取其整数倍
	qtyDec := decimal.NewFromFloat(qty)
	stepSizeDec := decimal.NewFromFloat(stepSize)
	qtyDec = qtyDec.Div(stepSizeDec).Floor().Mul(stepSizeDec)
	qty, _ = qtyDec.Float64()

	// price 按 tickSize 为最小单位向下取其整数倍
	priceDec := decimal.NewFromFloat(price)
	tickSizeDec := decimal.NewFromFloat(tickSize)
	priceDec = priceDec.Div(tickSizeDec).Floor().Mul(tickSizeDec)
	price, _ = priceDec.Float64()

	args.InstrumentType = instrumentType
	args.Symbol = symbol
	args.Side = orderSide
	args.Type = exchange.Limit
	args.Qty = math.Abs(qty)
	args.Price = price
	args.TimeInForce = exchange.GTC
	args.ReduceOnly = direction == ArbiDirectionClose

	if instrumentType == exchange.Spot && !this.Arbitrager.IsCrossMode() {
		args.TradeMode = exchange.TradeModeCash
	} else {
		args.TradeMode = exchange.TradeModeCross
	}

	if limitPrice == 0 {
		args.TimeInForce = exchange.IOC
	}

	return args, lastPrice, nil
}

// 从缓存获取 instrument
func (this *Executer) GetInstrument(instrumentType exchange.InstrumentType, symbol string) *exchange.Instrument {
	i, err := this.Arbitrager.Exchange.GetInstrument(instrumentType, symbol)
	if err != nil {
		this.Arbitrager.Errorf("GetInstrument %s err: %s", symbol, err)
		return nil
	}
	return i
}

// targetRatio 比 refRatio 更差时，对比 tolerance，不通过则返回 false
func CheckBasisTolerance(direction ArbiDirection, targetRatio, refRatio, tolerance float64) bool {
	// NaN 始终比任何 Non-NaN 大，会导致误判，NaN 出现表示之前的流程中出现了错误
	if math.IsNaN(targetRatio) || math.IsNaN(refRatio) {
		return false
	}
	if direction == ArbiDirectionOpen || direction == ArbiDirectionMove || direction == ArbiDirectionRotate {
		if targetRatio >= refRatio {
			return true
		}

		if ((refRatio - targetRatio) / math.Abs(refRatio)) > tolerance {
			return false
		}
	} else {
		if refRatio >= targetRatio {
			return true
		}

		if ((targetRatio - refRatio) / math.Abs(refRatio)) > tolerance {
			return false
		}
	}
	return true
}

// 现货、期货之间划转指令
func (this *Executer) Transfer(from, to exchange.SymbolCode, amount float64) error {
	var fromAsset exchange.InstrumentType
	var toAsset exchange.InstrumentType
	transferCoin := from.Coin()
	if from.InstrumentType() == exchange.USDXMarginedFutures {
		transferCoin = from.USDXSymbol
	}
	if from.IsSpot() && to.IsFuture() {
		fromAsset = exchange.Spot
		toAsset = to.InstrumentType()
	} else if from.IsFuture() && to.IsSpot() {
		fromAsset = from.InstrumentType()
		toAsset = exchange.Spot
	} else {
		return fmt.Errorf("cannot tranfer from %s to %s", from, to)
	}

	return this.Arbitrager.Exchange.TransferAsset(fromAsset, toAsset, transferCoin, amount)
}

func updateReportOrder(reportOrder *exchange.Order, order *exchange.Order) {
	order.RefID = reportOrder.RefID
	order.LastPrice = reportOrder.LastPrice
	order.SetTime(ExtKeyStartTime, reportOrder.GetTime(ExtKeyStartTime))
	order.SetTime(ExtKeyEndTime, reportOrder.GetTime(ExtKeyEndTime))
	*reportOrder = *order
}

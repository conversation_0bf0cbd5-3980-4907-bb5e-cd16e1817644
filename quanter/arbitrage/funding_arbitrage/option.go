package funding_arbitrage

import (
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

const MonitorLimit = 120                    // 5 秒钟一个数据点的话，总共约 10 分钟的高频数据
const PositionHistoryLimit = 7 * 24 * 3600  // 每小时记录一次，最多记录 7 天的仓位数据
const SyncPositionErrorLimit = 5            // 同步仓位出错指定次数时，减少仓位
const SyncPositionErrorReduceRatio = 0.02   // 同步仓位出错减仓比例
const PositionQtyDiffRatioThreshold = 0.001 // 仓位数量差异比例阈值，超过这个比例则认为仓位不同步
const SnapshotHistoryTimeWindow = 2 * time.Minute

const EnableCheckMarginRatioOption = false

type Status string

const (
	StatusRunning Status = "running"
	StatusPaused  Status = "paused"
	StatusStopped Status = "stopped"
)

const (
	OrderSplitInterval = 10
	OrderSplitValue    = 1000.0
	WashRequestLimit   = 1000.0
	StoplossMAWindow   = 5 * time.Minute

	LoopInterval       = 5 * time.Second
	SlowInterval       = 5 * time.Minute                    // Slow 低频模式下的时间频率，用于执行一些不需频繁执行的任务
	SlowMod            = int64(SlowInterval / LoopInterval) // 低频模式下的 loop 次数
	HourlySlowInterval = 1 * time.Hour                      // Slow 低频模式下的时间频率，用于执行一些不需频繁执行的任务
	HourlySlowMod      = int64(HourlySlowInterval / LoopInterval)
	DailySlowInterval  = 24 * time.Hour
	DailySlowMod       = int64(DailySlowInterval / LoopInterval)

	SnapshotRetentionPeriod = 24 * time.Hour * 14

	MinMarginValue   = 10.0
	MinPositionValue = 10.0
)

const (
	ParamMarginRatioLowTimeWindow = 2 * time.Minute
	ParamMarginRatioLowCountLimit = 5
	ParamPriceDiffTimeWindow      = 2 * time.Minute // 两分钟内价格差超过 0.5% 则认为价格波动较大
	ParamPriceDiffRatio           = 0.005           // 0.5%
	ParamAlertPriceDiffRatio      = 0.005           // 0.5%
	ParamTakeProfitPriceDiff      = 0.005           // 止盈价差，0.5%
	ParamPriceDiffCountLimit      = 5
	ParamMarkPriceDiffTimeWindow  = 1 * time.Minute // 一分钟内价格差超过 0.5% 则认为价格波动较大
	ParamMarkPriceDiffRatio       = 0.005           // 0.5%
	ParamMarkPriceDiffCountLimit  = 5
	// 默认杠杆，这个杠杆率对部分交易所有效，比如 binance/bybit 部分品种需要使用更小的杠杆率来保证可以开更大的头寸
	// hyperliquid 统一限制了高风险币种的杠杆率，一般较低的是 3x，因此 hyperliquid 不太需需要单独设置品种的杠杆率，只需要默认调到最大即可
	// bybit 和 binance，对于高风险币种，默认给的杠杆率可能很高，比如 50x，但是使用 10x 以上杠杆的时候，可能只能开很小的头寸，这和我们的需求就会有冲突；也就是说为了在 bybit/binance 上开更大的头寸，需要使用更小的杠杆率
	// 因此，对于 bybit/binance，我们设置默认杠杆率为 5x，并且允许用户使用 SetParam 来设置单个品种的杠杆率
	ParamLeverage         = 5.0
	ParamStoplossRatio    = 0.05  // 止损止盈订单的价格波动比例
	StoplossDiffTolerance = 0.1   // 止损止盈订单的价格差容忍度，10% 的止损止盈价格差
	ParamStoplossSlippage = 0.001 // 止损止盈订单的价格滑点
	ParamStoplossInterval = 30    // 止损止盈订单的更新间隔
)

type FundingMode string

const (
	FundingModeSpot2Future   FundingMode = "Spot2Future"
	FundingModeFuture2Future FundingMode = "Future2Future"
)

type ArbitragerOption string

const (
	OptionMarginMode            ArbitragerOption = "MarginMode"
	OptionTargetMarginRatio     ArbitragerOption = "TargetMarginRatio"
	OptionRebalanceMarginRatio  ArbitragerOption = "RebalanceMarginRatio"
	OptionSplitInterval         ArbitragerOption = "SplitInterval"
	OptionStoplossInterval      ArbitragerOption = "StoplossInterval"
	OptionSplitValue            ArbitragerOption = "SplitValue"
	OptionWashRequestLimit      ArbitragerOption = "WashRequestLimit"
	OptionStoplossDiffTolerance ArbitragerOption = "StoplossDiffTolerance"
	OptionValidCoins            ArbitragerOption = "ValidCoins"
	OptionEnableStoploss        ArbitragerOption = "EnableStoploss"
)

type ParamType string

const (
	ParamTypePriceDiff           ParamType = "PriceDiff"
	ParamTypeStoploss            ParamType = "Stoploss"
	ParamTypeStoplossSlippage    ParamType = "StoplossSlippage"
	ParamTypeMarkPriceDiff       ParamType = "MarkPriceDiff"
	ParamTypeLeverageLeft        ParamType = "LeverageLeft"
	ParamTypeLeverageRight       ParamType = "LeverageRight"
	ParamTypeSplitValue          ParamType = "SplitValue"
	ParamTypeAlertPriceDiff      ParamType = "AlertPriceDiff"
	ParamTypeTakeProfitPriceDiff ParamType = "TakeProfitPriceDiff"
)

type Options struct {
	option.Options
	MarginMode             string
	TargetMarginRatio      float64
	RebalanceMarginRatio   float64
	SplitInterval          int
	SplitValue             float64
	StoplossUpdateInterval int
	StoplossRatio          float64
	LeverageLeft           float64
	LeverageRight          float64
	PriceDiff              float64
	MarkPriceDiff          float64
	WashRequestLimit       float64
	StoplossDiffTolerance  float64
	ValidCoins             []string
	EnableStoploss         bool
	AlertPriceDiff         float64
	TakeProfitPriceDiff    float64
}

func getCrossExchangeArbitragerOptionDefinitions() []*option.TypedOption {
	return []*option.TypedOption{
		option.NewTypedOption(string(OptionMarginMode), option.String, string(exchange.Cross), "", false),
		option.NewTypedOption(string(OptionTargetMarginRatio), option.Float64, 0.2, "", true),
		option.NewTypedOption(string(OptionRebalanceMarginRatio), option.Float64, 0.1, "", true),
		option.NewTypedOption(string(OptionStoplossInterval), option.Int, ParamStoplossInterval, "s", false),
		option.NewTypedOption(string(OptionSplitInterval), option.Int, OrderSplitInterval, "s", false),
		option.NewTypedOption(string(OptionSplitValue), option.Float64, OrderSplitValue, "", false),
		option.NewTypedOption(string(ParamTypePriceDiff), option.Float64, ParamPriceDiffRatio, "", true),
		option.NewTypedOption(string(ParamTypeMarkPriceDiff), option.Float64, ParamMarkPriceDiffRatio, "", true),
		option.NewTypedOption(string(ParamTypeStoploss), option.Float64, ParamStoplossRatio, "", true),
		option.NewTypedOption(string(ParamTypeLeverageLeft), option.Float64, 1.0, "", false),
		option.NewTypedOption(string(ParamTypeLeverageRight), option.Float64, ParamLeverage, "", false),
		option.NewTypedOption(string(OptionStoplossDiffTolerance), option.Float64, StoplossDiffTolerance, "", true),
		option.NewTypedOption(string(OptionWashRequestLimit), option.Float64, WashRequestLimit, "", false),
		option.NewTypedOption(string(OptionValidCoins), option.String, "", "", false),
		option.NewTypedOption(string(OptionEnableStoploss), option.Bool, false, "", false),
		option.NewTypedOption(string(ParamTypeAlertPriceDiff), option.Float64, ParamAlertPriceDiffRatio, "", true),
		option.NewTypedOption(string(ParamTypeTakeProfitPriceDiff), option.Float64, ParamTakeProfitPriceDiff, "", true),
	}
}

func NewFundingArbitragerOptions(optStr string) (*Options, error) {
	opts, err := option.NewOptions(optStr, getCrossExchangeArbitragerOptionDefinitions()...)
	if err != nil {
		return nil, err
	}
	return &Options{Options: *opts}, nil
}

func (this *FundingArbitrager) GetOptions() *Options {
	return this.Options
}

func (this *FundingArbitrager) GetOptionDefinitions() []*option.TypedOption {
	return this.Options.GetDefinitions()
}

func (this *FundingArbitrager) UpdateOptions(opts *option.Options) {
	this.Options.Options = *opts
	this.Options.MarginMode = opts.Get(string(OptionMarginMode)).(string)
	this.Options.TargetMarginRatio = opts.Get(string(OptionTargetMarginRatio)).(float64)
	this.Options.RebalanceMarginRatio = opts.Get(string(OptionRebalanceMarginRatio)).(float64)
	this.Options.SplitInterval = opts.Get(string(OptionSplitInterval)).(int)
	this.Options.SplitValue = opts.Get(string(OptionSplitValue)).(float64)
	this.Options.StoplossUpdateInterval = opts.Get(string(OptionStoplossInterval)).(int)
	this.Options.StoplossRatio = opts.Get(string(ParamTypeStoploss)).(float64)
	this.Options.StoplossDiffTolerance = opts.Get(string(OptionStoplossDiffTolerance)).(float64)
	this.Options.PriceDiff = opts.Get(string(ParamTypePriceDiff)).(float64)
	this.Options.MarkPriceDiff = opts.Get(string(ParamTypeMarkPriceDiff)).(float64)
	this.Options.LeverageLeft = opts.Get(string(ParamTypeLeverageLeft)).(float64)
	this.Options.LeverageRight = opts.Get(string(ParamTypeLeverageRight)).(float64)
	this.Options.WashRequestLimit = opts.Get(string(OptionWashRequestLimit)).(float64)
	this.Options.EnableStoploss = opts.Get(string(OptionEnableStoploss)).(bool)
	this.Options.AlertPriceDiff = opts.Get(string(ParamTypeAlertPriceDiff)).(float64)
	this.Options.TakeProfitPriceDiff = opts.Get(string(ParamTypeTakeProfitPriceDiff)).(float64)

	oldValidCoins := []string{}
	copier.Copy(oldValidCoins, this.Options.ValidCoins)

	coins := opts.Get(string(OptionValidCoins)).(string)
	if coins != "" {
		var parts []string
		this.Options.ValidCoins = []string{}
		if strings.Contains(coins, "/") {
			parts = strings.Split(coins, "/")
		} else {
			parts = []string{coins}
		}
		for _, part := range parts {
			if part != "" {
				this.Options.ValidCoins = append(this.Options.ValidCoins, strings.ToUpper(part))
			}
		}
	} else {
		this.Options.ValidCoins = []string{}
	}

	removedCoins := []string{}
	for _, coin := range oldValidCoins {
		if !utils.SliceContains(this.Options.ValidCoins, coin) {
			removedCoins = append(removedCoins, coin)
		}
	}
	if len(removedCoins) > 0 {
		leftWatches, rightWatches := this.GetPriceWatches(removedCoins)
		for _, watch := range leftWatches {
			this.ExchangeLeft.DeletePriceWatch(&watch.SymbolCode)
		}
		for _, watch := range rightWatches {
			this.ExchangeRight.DeletePriceWatch(&watch.SymbolCode)
		}
	}

	addedCoins := []string{}
	for _, coin := range this.Options.ValidCoins {
		if !utils.SliceContains(oldValidCoins, coin) {
			addedCoins = append(addedCoins, coin)
		}
	}
	if len(addedCoins) > 0 {
		leftWatches, rightWatches := this.GetPriceWatches(addedCoins)
		for _, watch := range leftWatches {
			this.ExchangeLeft.AddPriceWatch(watch)
		}
		for _, watch := range rightWatches {
			this.ExchangeRight.AddPriceWatch(watch)
		}
	}

}

func (this *FundingArbitrager) LoadOptions() error {
	defs := getCrossExchangeArbitragerOptionDefinitions()
	err := this.Options.Load(defs...)
	if err != nil {
		return fmt.Errorf("load options failed: %s", err)
	}
	this.UpdateOptions(&this.Options.Options)
	return nil
}

func (this *FundingArbitrager) ValidateOptions(opts *option.Options) error {
	if this.ControllerRight == nil {
		return fmt.Errorf("controller right not initialized")
	}
	baseConfig := this.ControllerRight.GetBaseConfig()
	if baseConfig.FutureInstrumentType != exchange.USDXMarginedFutures {
		return fmt.Errorf("option FundingMode is only supported USDXMarginedFutures")
	}
	if opts.Get(string(OptionMarginMode)) != string(exchange.Portfolio) && opts.Get(string(OptionMarginMode)) != string(exchange.Cross) {
		return fmt.Errorf("option MarginMode must be Portfolio or Cross")
	}
	if opts.Get(string(OptionTargetMarginRatio)).(float64) <= 0 || opts.Get(string(OptionTargetMarginRatio)).(float64) > 1 {
		return fmt.Errorf("option TargetMarginRatio must be between 0 and 1")
	}
	if opts.Get(string(OptionRebalanceMarginRatio)).(float64) <= 0 || opts.Get(string(OptionRebalanceMarginRatio)).(float64) > 1 {
		return fmt.Errorf("option RebalanceMarginRatio must be between 0 and 1")
	}
	if opts.Get(string(OptionRebalanceMarginRatio)).(float64) >= opts.Get(string(OptionTargetMarginRatio)).(float64) {
		return fmt.Errorf("option RebalanceMarginRatio must be less than TargetMarginRatio")
	}
	if opts.Get(string(OptionSplitInterval)).(int) <= 0 {
		return fmt.Errorf("option SplitInterval must be greater than 0")
	}
	if opts.Get(string(OptionSplitValue)).(float64) <= 0 || opts.Get(string(OptionSplitValue)).(float64) > 10000 {
		return fmt.Errorf("option SplitValue must be greater than 0 and less than 10000")
	}
	if opts.Get(string(OptionStoplossInterval)).(int) <= 0 || opts.Get(string(OptionStoplossInterval)).(int) > int(StoplossMAWindow/time.Second) {
		return fmt.Errorf("option StoplossUpdateInterval must be greater than 0 and less than %d", int(StoplossMAWindow/time.Second))
	}
	if opts.Get(string(ParamTypeStoploss)).(float64) <= 0 || opts.Get(string(ParamTypeStoploss)).(float64) > 1 {
		return fmt.Errorf("option Stoploss must be between 0 and 1")
	}
	if opts.Get(string(OptionStoplossDiffTolerance)).(float64) <= 0 || opts.Get(string(OptionStoplossDiffTolerance)).(float64) > 1 {
		return fmt.Errorf("option StoplossDiffTolerance must be between 0 and 1")
	}
	if opts.Get(string(ParamTypePriceDiff)).(float64) <= 0 || opts.Get(string(ParamTypePriceDiff)).(float64) > 1 {
		return fmt.Errorf("option PriceDiff must be between 0 and 1")
	}
	if opts.Get(string(ParamTypeAlertPriceDiff)).(float64) <= 0 || opts.Get(string(ParamTypeAlertPriceDiff)).(float64) > 1 {
		return fmt.Errorf("option AlertPriceDiff must be between 0 and 1")
	}
	if opts.Get(string(ParamTypeTakeProfitPriceDiff)).(float64) < 0 || opts.Get(string(ParamTypeTakeProfitPriceDiff)).(float64) > 1 {
		return fmt.Errorf("option TakeProfitPriceDiff must be between 0 and 1")
	}
	if opts.Get(string(ParamTypeMarkPriceDiff)).(float64) <= 0 || opts.Get(string(ParamTypeMarkPriceDiff)).(float64) > 1 {
		return fmt.Errorf("option MarkPriceDiff must be between 0 and 1")
	}
	if opts.Get(string(ParamTypeLeverageLeft)).(float64) <= 0 || opts.Get(string(ParamTypeLeverageLeft)).(float64) > 100 {
		return fmt.Errorf("option LeverageLeft must be between 0 and 100")
	}
	if opts.Get(string(ParamTypeLeverageRight)).(float64) <= 0 || opts.Get(string(ParamTypeLeverageRight)).(float64) > 100 {
		return fmt.Errorf("option LeverageRight must be between 0 and 100")
	}

	validCoinsStr := opts.Get(string(OptionValidCoins)).(string)

	if validCoinsStr != "" {
		var coins []string
		if strings.Contains(validCoinsStr, "/") {
			coins = strings.Split(validCoinsStr, "/")
		} else {
			coins = []string{validCoinsStr}
		}
		for _, coin := range coins {
			if coin == "" {
				return fmt.Errorf("option ValidCoins must be a / separated list of coin symbols")
			}
		}
	}
	return nil
}

/* Params
* 用户可以首先设置全局的 option，作为 Params 的默认值，Params 主要是给品种自定义配置使用
 */

func (this *FundingArbitrager) getParamDefinitions() []*option.TypedOption {
	return []*option.TypedOption{
		{Name: string(ParamTypePriceDiff), Type: option.Float64, Default: this.Options.PriceDiff},
		{Name: string(ParamTypeStoploss), Type: option.Float64, Default: this.Options.StoplossRatio},
		{Name: string(ParamTypeMarkPriceDiff), Type: option.Float64, Default: this.Options.MarkPriceDiff},
		{Name: string(ParamTypeSplitValue), Type: option.Float64, Default: this.Options.SplitValue},
		{Name: string(ParamTypeLeverageLeft), Type: option.Float64, Default: 1.0},
		{Name: string(ParamTypeLeverageRight), Type: option.Float64, Default: this.Options.LeverageRight},
		{Name: string(ParamTypeAlertPriceDiff), Type: option.Float64, Default: this.Options.AlertPriceDiff},
		{Name: string(ParamTypeTakeProfitPriceDiff), Type: option.Float64, Default: this.Options.TakeProfitPriceDiff},
	}
}

// 加载风险参数，必须在 LoadOptions 之后，因为其中的值依赖于 options
func (this *FundingArbitrager) LoadParams() {
	this.SymbolParams.Range(func(key string, value *option.Options) bool {
		value.Load(this.getParamDefinitions()...)
		return true
	})
}

func (this *FundingArbitrager) UpdateParams(symbolCode string, params string) *option.Options {
	return this._updateParams(this.SymbolParams, symbolCode, params)
}

func (this *FundingArbitrager) _updateParams(symbolParams *exchange.SyncMapOf[string, *option.Options], symbolCode string, params string) *option.Options {
	if symbolParams == nil {
		symbolParams = &exchange.SyncMapOf[string, *option.Options]{}
	}

	options, ok := symbolParams.Load(symbolCode)
	if ok && options != nil && !options.IsZero() {
		options.UpdateFromString(params)
	} else {
		var err error
		options, err = this.NewParams(params)
		if err != nil {
			this.Errorf("set risk params failed, error: %s", err)
			return nil
		}
	}
	symbolParams.Store(symbolCode, options)
	return options
}

func (this *FundingArbitrager) GetParam(paramType ParamType, symbolCode string) (value any) {
	return this._getParam(this.SymbolParams, paramType, symbolCode)
}

func (this *FundingArbitrager) _getParam(params *exchange.SyncMapOf[string, *option.Options], paramType ParamType, symbolCode string) (value any) {
	options, ok := params.Load(symbolCode)
	if ok && options != nil && !options.IsZero() {
		value = options.Get(string(paramType))
	} else {
		defaultOptions, _ := this.NewParams("")
		value = defaultOptions.Default(string(paramType))
	}
	return
}

func (this *FundingArbitrager) NewParams(params string) (*option.Options, error) {
	return option.NewOptions(
		params,
		this.getParamDefinitions()...,
	)
}

func (this *FundingArbitrager) SetOptions(options string) (er error) {
	opts := this.GetOptions().Copy()
	updatedNames, err := opts.UpdateFromString(options)
	if err != nil {
		return fmt.Errorf("update options failed: %s", err)
	}
	if err := this.ValidateOptions(opts); err != nil {
		return fmt.Errorf("validate options failed: %s", err)
	}

	this.UpdateOptions(opts)
	this.orderController.GetStorage().Save()
	this.UpdateOptionsCallback(updatedNames)
	return
}

func (this *FundingArbitrager) UpdateOptionsCallback(updatedNames []string) {
	if utils.SliceContains(updatedNames, string(OptionTargetMarginRatio)) || utils.SliceContains(updatedNames, string(OptionRebalanceMarginRatio)) {
		this.checkMarginRatioOption(true)
	}
	// 如果更新了目标保证金率或再平衡保证金率，则需要放松监控
	// 否则会可能因为 marginRatio 快速变化，导致监控触发风险事件
	targetMarginRatioUpdated := utils.SliceContains(updatedNames, string(OptionTargetMarginRatio))
	rebalanceMarginRatioUpdated := utils.SliceContains(updatedNames, string(OptionRebalanceMarginRatio))
	if targetMarginRatioUpdated || rebalanceMarginRatioUpdated {
		this.MonitorRelaxAtTime = time.Now().Add(time.Second * 5)
	}
	if utils.SliceContains(updatedNames, string(OptionMarginMode)) {
		if this.ExchangeRight.GetName() == exchange.Hyperliquid {
			this.Options.Set(string(OptionMarginMode), string(exchange.Cross))
		}
		if this.ExchangeRight.GetName() == exchange.Bybit {
			this.Options.Set(string(OptionMarginMode), string(exchange.Portfolio))
		}
	}
}

// 根据实际杠杆检查 TargetMarginRatio, RebalanceMarginRatio 的设置是否合理
// 仅检查 hyperliquid 的保证金率，因为通常 hyperliquid 的保证金率最低
// 每 5 分钟检查一次 margin ratio 选项，降低检查和报警频率
func (this *FundingArbitrager) checkMarginRatioOption(force bool) {
	if !EnableCheckMarginRatioOption {
		return
	}

	if force || this.counters.checkMarginRatioLimiter.Allow() {
		maintMarginRatio, marginRatio, suggestedReblanceMarginRatio, suggestedTargetMarginRatio, er := this.getMinMarginRatio()
		if er != nil {
			if !strings.Contains(er.Error(), "exchange not supported") {
				this.Errorf("checking margin ratio option, get min margin ratio failed: %s", er)
			}
			return
		}

		if this.Options.RebalanceMarginRatio < suggestedReblanceMarginRatio {
			this.AlertMsgf("RebalanceMarginRatio %.2f%% 设置过低，Hyperliquid 当前需要维持保证金比例为 %.2f%%，建议设置为 %.2f%% 以上", this.Options.RebalanceMarginRatio*100, maintMarginRatio*100, suggestedReblanceMarginRatio*100)
		}
		if this.Options.TargetMarginRatio < suggestedTargetMarginRatio {
			this.AlertMsgf("TargetMarginRatio %.2f%% 设置过低，Hyperliquid 当前需要保证金比例为 %.2f%%，建议设置为 %.2f%% 以上", this.Options.TargetMarginRatio*100, marginRatio*100, suggestedTargetMarginRatio*100)
		}
	}
}

// 仅检查 hyperliquid 的保证金率，因为通常 hyperliquid 的保证金率最低
func (this *FundingArbitrager) getMinMarginRatio() (maintMarginRatio, marginRatio, suggestedReblanceMarginRatio, suggestedTargetMarginRatio float64, er error) {
	if this.ExchangeRight.GetName() != exchange.Hyperliquid {
		return 0, 0, 0, 0, fmt.Errorf("exchange not supported: %s", this.ExchangeRight.GetName())
	}
	if this.AccountSnapshot == nil {
		er = fmt.Errorf("account snapshot not found")
		return
	}
	if time.Since(this.AccountSnapshot.CreateTime) > LoopInterval {
		er = fmt.Errorf("account snapshot is too old")
		return
	}

	if this.AccountSnapshot.PositionValueLeft == 0 || this.AccountSnapshot.PositionValueRight == 0 {
		er = fmt.Errorf("position value is 0")
		return
	}

	if time.Now().Before(this.MonitorRelaxAtTime) {
		this.Infof("monitor is in relax mode, skip this time. MonitorRelaxAtTime=%s", this.MonitorRelaxAtTime.Format(time.RFC3339))
		er = fmt.Errorf("monitor is in relax mode")
		return
	}

	rebalanceMultiplier := 1.2
	targetMultiplier := 1.01

	marginRatio = 1 / this.AccountSnapshot.PositionLeverageRight
	// 因为维持保证金一般是 50%，所以资金不足 leftMarginRatio * 0.5 时就会爆仓
	maintMarginRatio = marginRatio * 0.5

	suggestedReblanceMarginRatio = maintMarginRatio * rebalanceMultiplier
	suggestedTargetMarginRatio = marginRatio * targetMultiplier

	return
}

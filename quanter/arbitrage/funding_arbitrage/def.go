package funding_arbitrage

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/jinzhu/copier"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/common/rate"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type RiskCategory string

const (
	NoRiskCategory                          RiskCategory = ""
	RiskCateogryRebalanceMarginRatioReached RiskCategory = "RebalanceMarginRatioReached"
	RiskCategoryMarginValueIsZero           RiskCategory = "MarginValueIsZero"   // 单个交易所保证金价值接近 0，可能已经爆仓
	RiskCategoryPositionValueIsZero         RiskCategory = "PositionValueIsZero" // 单个交易所仓位价值接近 0，可能已经爆仓
	RiskCategoryMarginRatioLow              RiskCategory = "MarginRatioLow"      // 单个交易所 marginRatio 持续较低，可能因为转账出现延迟或失败导致
	RiskCategoryPriceDiff                   RiskCategory = "PriceDiff"           // 两个交易所同一个品种的价格差超过一定限度，比如 0.5%
	RiskCategoryMarkPriceDiff               RiskCategory = "MarkPriceDiff"       // 某个品种的合约标记价格价差超过一定限度，比如 0.5%
	RiskCategoryAlienPosition               RiskCategory = "AlienPosition"       // 发现非 U 本位合约
)

type PriceType string

const (
	PriceTypeLastPrice  PriceType = "LastPrice"
	PriceTypeMarkPrice  PriceType = "MarkPrice"
	PriceTypeIndexPrice PriceType = "IndexPrice"
)

type SymbolContext struct {
	LastPrice       float64
	MarkPrice       float64
	IndexPrice      float64
	FundingRate     float64
	OpenInterest    float64
	StopLossPrice   float64
	TakeProfitPrice float64
	PositionValue   float64 // 仓位价值
	CreateTime      time.Time
}

func NewSymbolContext() *SymbolContext {
	return &SymbolContext{
		CreateTime: time.Now(),
	}
}

func (this *SymbolContext) IsUpToDate() bool {
	return this.CreateTime.Add(10 * time.Second).After(time.Now())
}

// 资产和仓位快照，一小时记录一次
type AccountSnapshot struct {
	ID                    string
	PositionValueLeft     float64
	PositionValueRight    float64
	PositionLeverageLeft  float64
	PositionLeverageRight float64
	MarginValueLeft       float64
	MarginValueRight      float64
	MarginAvailableLeft   float64
	MarginAvailableRight  float64
	MarginRatioLeft       float64
	MarginRatioRight      float64
	MarginValueTotal      float64
	PositionValueTotal    float64
	MarginRatioTotal      float64
	HedgePNLs             *exchange.SyncMapOf[string, float64] // symbolCode => hedge pnl, 对冲 PNL 是正数表示以当前价格平仓盈利，负数表示亏损
	SymbolContextLeft     *exchange.SyncMapOf[string, *SymbolContext]
	SymbolContextRight    *exchange.SyncMapOf[string, *SymbolContext]
	CreateTime            time.Time
}

func NewAccountSnapshot() *AccountSnapshot {
	return &AccountSnapshot{
		ID:                 exchange.NewRandomID(),
		SymbolContextLeft:  exchange.NewSyncMapOf[string, *SymbolContext](),
		SymbolContextRight: exchange.NewSyncMapOf[string, *SymbolContext](),
		HedgePNLs:          exchange.NewSyncMapOf[string, float64](),
	}
}

func (this *AccountSnapshot) IsEmpty() bool {
	return this.CreateTime.IsZero()
}

func (this *AccountSnapshot) IsPositionEmpty() bool {
	return this.PositionValueLeft == 0 && this.PositionValueRight == 0
}

// 获取当前持仓的对冲 PNL，如果 symbolCode 为空，则返回所有对冲 PNL 的和
func (this *AccountSnapshot) GetHedgePNL(symbolCode string) float64 {
	pnl := 0.0
	if symbolCode == "" {
		this.HedgePNLs.Range(func(key string, value float64) bool {
			pnl += value
			return true
		})
	} else {
		pnl, _ = this.HedgePNLs.Load(symbolCode)
	}
	return pnl
}

func (this *AccountSnapshot) GetPrice(priceType PriceType, symbol string, isRight bool) (price float64, er error) {
	symbolContext, _ := this.SymbolContextLeft.Load(symbol)
	if isRight {
		symbolContext, _ = this.SymbolContextRight.Load(symbol)
	}
	if symbolContext == nil {
		return 0, fmt.Errorf("symbol context not found")
	}
	switch priceType {
	case PriceTypeLastPrice:
		return symbolContext.LastPrice, nil
	case PriceTypeMarkPrice:
		return symbolContext.MarkPrice, nil
	case PriceTypeIndexPrice:
		return symbolContext.IndexPrice, nil
	}
	return 0, fmt.Errorf("invalid price type: %s", priceType)
}

type PositionSnapshot struct {
	ID             string // 关联的 AccountSnapshot 的 ID，用于查询同一个时刻的仓位快照
	PositionsLeft  []*exchange.Position
	PositionsRight []*exchange.Position
	CreateTime     time.Time
}

func (this *PositionSnapshot) HasPositions() bool {
	return len(this.PositionsLeft) > 0 || len(this.PositionsRight) > 0
}

type RiskEvent struct {
	ID         string
	Category   RiskCategory
	Comment    string
	CreateTime time.Time
}

type AccountSnapshots []*AccountSnapshot

func (this AccountSnapshots) Copy() AccountSnapshots {
	copied := make(AccountSnapshots, len(this))
	copier.Copy(&copied, &this)
	return copied
}

type LockMutex struct {
	sync.Mutex
}

func (this *LockMutex) IsLocked() bool {
	locked := this.TryLock()
	if locked {
		this.Unlock()
	}
	return !locked
}

type SymbolLock struct {
	SymbolCode            string
	UpdatingStoplossLeft  LockMutex
	UpdatingStoplossRight LockMutex
	UpdateStoplossDelay   time.Duration
	SyncingPosition       LockMutex
	ClosingPosition       LockMutex
	ReducingPosition      LockMutex
	AddingPosition        LockMutex
	RatatingPosition      LockMutex
	AtCapUntil            *time.Time
}

func (this *SymbolLock) String() string {
	updatingStoplossLeft := this.UpdatingStoplossLeft.IsLocked()
	updatingStoplossRight := this.UpdatingStoplossRight.IsLocked()
	syncingPosition := this.SyncingPosition.IsLocked()
	closingPosition := this.ClosingPosition.IsLocked()
	reducingPosition := this.ReducingPosition.IsLocked()
	addingPosition := this.AddingPosition.IsLocked()
	ratatingPosition := this.RatatingPosition.IsLocked()
	return fmt.Sprintf("symbol code locks: %s, updatingStoplossLeft: %t, updatingStoplossRight: %t, syncingPosition: %t, closingPosition: %t, reducingPosition: %t, addingPosition: %t, ratatingPosition: %t", this.SymbolCode, updatingStoplossLeft, updatingStoplossRight, syncingPosition, closingPosition, reducingPosition, addingPosition, ratatingPosition)
}

// 没有 rotate 的情况下都允许 sync 仓位
// rotate 本来就只希望在单侧执行，不希望 sync 仓位变动
func (this *SymbolLock) AllowSync() bool {
	ratating := this.RatatingPosition.IsLocked()
	return !ratating
}

// 没有加仓减仓和平仓的情况下都允许 rotate 仓位
// 在加仓和减仓的过程中 rotate 可能仓位数量不准确
// 在平仓的过程中 rotate 没有必要了，因为平仓后仓位数量为 0
func (this *SymbolLock) AllowRatate() bool {
	reducing := this.ReducingPosition.IsLocked()
	closing := this.ClosingPosition.IsLocked()
	adding := this.AddingPosition.IsLocked()
	return !reducing && !closing && !adding
}

func (this *SymbolLock) IsAtCapNow() bool {
	return this.AtCapUntil != nil && time.Now().Before(*this.AtCapUntil)
}

type Maintenance struct {
	ID               string
	ExchangeName     string
	EffectiveTime    time.Time
	EffectiveSeconds int
	FinishTime       *time.Time
	Message          string
	Comment          string
	CreateTime       time.Time
	UpdateTime       time.Time
}

func (this *Maintenance) IsActive() bool {
	if this.FinishTime != nil {
		return false
	}
	// 维护开始前 1 分钟认为维护开始
	return time.Now().After(this.EffectiveTime.Add(-1*time.Minute)) && this.EndTime().After(time.Now())
}

func (this *Maintenance) Prolong(seconds int) {
	this.EffectiveTime = this.EffectiveTime.Add(time.Duration(seconds) * time.Second)
}

func (this *Maintenance) Finish(reason string) {
	this.FinishTime = utils.Ptr(time.Now())
	this.Comment = reason
}

func (this *Maintenance) EndTime() *time.Time {
	return utils.Ptr(this.EffectiveTime.Add(time.Duration(this.EffectiveSeconds) * time.Second))
}

func (this *Maintenance) IsFinished() bool {
	return this.FinishTime != nil
}

func (this *Maintenance) String() string {
	return fmt.Sprintf("[MAINTANANCE] exchange: %s, time: %s ~ %s, message: %s, comment: %s", this.ExchangeName, utils.FormatShortTimeStr(&this.EffectiveTime, false), utils.FormatShortTimeStr(this.EndTime(), false), this.Message, this.Comment)
}

func (this *Maintenance) Row() []string {
	timeSpan := fmt.Sprintf("%s ~ %s", utils.FormatShortTimeStr(&this.EffectiveTime, false), utils.FormatShortTimeStr(this.EndTime(), false))
	row := []string{}
	row = append(row, this.ID)
	row = append(row, this.ExchangeName)
	row = append(row, timeSpan)
	activeStr := ""
	if this.IsActive() {
		activeStr = "Yes"
	}
	row = append(row, activeStr)
	row = append(row, utils.FormatShortTimeStr(this.FinishTime, false))
	row = append(row, this.Message)
	row = append(row, this.Comment)
	row = append(row, utils.FormatShortTimeStr(&this.UpdateTime, false))
	return row
}

func (this *Maintenance) ToTable() string {
	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Exchange", "Time", "Active", "FinishTime", "Message", "Comment", "UpdateTime"})
	t.AddRow(this.Row())
	return t.Render()
}

type Maintenances []*Maintenance

func (this Maintenances) HasActiveMaintainance() bool {
	for _, maint := range this {
		if maint.IsActive() {
			return true
		}
	}
	return false
}

func (this *Maintenances) Set(exchangeName string, startTime time.Time, minutes int, message string, comment string) (maintenance *Maintenance, updated bool) {
	for i := len(*this) - 1; i >= 0; i-- {
		maint := (*this)[i]
		if maint.ExchangeName == exchangeName && !maint.IsFinished() {
			// 如果 minutes 为 0，则表示删除维护
			if minutes == 0 {
				maint.Finish("manually finished")
			} else { // 如果存在维护，则更新维护
				maint.EffectiveTime = startTime
				maint.EffectiveSeconds = minutes * 60
				maint.Message = message
				maint.Comment = comment
				maint.UpdateTime = time.Now()
			}
			maintenance = maint
			updated = true
			return
		}
	}
	// 如果不存在当前维护，则添加维护
	nowTime := time.Now()
	maintenance = &Maintenance{
		ID:               exchange.NewRandomID(),
		ExchangeName:     exchangeName,
		CreateTime:       nowTime,
		EffectiveTime:    startTime,
		EffectiveSeconds: minutes * 60,
		Message:          message,
		Comment:          comment,
		UpdateTime:       nowTime,
	}
	*this = append(*this, maintenance)
	return
}

func (this Maintenances) ToTable() string {
	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Exchange", "Time Span", "Active", "FinishTime", "Message", "Comment", "UpdateTime"})
	for i := len(this) - 1; i >= 0; i-- {
		maint := this[i]
		t.AddRow(maint.Row())
	}
	if len(t.Rows) > 1 {
		return t.Render()
	}
	return "[No Maintenances]"
}

func (this Maintenances) AutoFinish() (finished Maintenances) {
	for _, maint := range this {
		if maint.IsActive() && maint.EndTime().Before(time.Now()) {
			maint.Finish("auto finished")
			finished = append(finished, maint)
		}
	}
	return
}

func (this Maintenances) Copy() Maintenances {
	copied := make(Maintenances, len(this))
	copier.Copy(&copied, &this)
	return copied
}

type Counters struct {
	syncPositionErrorCounter    atomic.Int64         // 同步仓位出错次数
	manualRebalanceAlertLimiter *rate.CounterLimiter // 提醒手动 rebalance 次数
	pauseAlertLimiter           *rate.CounterLimiter // 暂停检查次数
	checkMarginRatioLimiter     *rate.CounterLimiter // 检查保证金比例的次数，用于防止频繁检查
	truncateSnapshotLimiter     *rate.CounterLimiter // 清理历史快照的次数
}

func NewCounters() *Counters {
	c := &Counters{
		manualRebalanceAlertLimiter: rate.NewCounterLimiter(SlowMod),
		checkMarginRatioLimiter:     rate.NewCounterLimiter(SlowMod),
		pauseAlertLimiter:           rate.NewCounterLimiter(HourlySlowMod),
		truncateSnapshotLimiter:     rate.NewCounterLimiter(DailySlowMod),
	}
	return c
}

type Mutexes struct {
	monitorsMutex             LockMutex
	riskMutex                 LockMutex
	reduceMutex               LockMutex
	rebalanceMutex            LockMutex
	syncPositionMutex         LockMutex
	handleStoplossOrdersMutex LockMutex
	checkLimitMutex           LockMutex
}

type FundingArbitrager struct {
	orderController base.OrderControllable
	counters        *Counters
	mutexes         Mutexes

	ControllerID      string
	RightControllerID string
	// 同交易所的情况，ControllerLeft == ControllerRight，ExchangeLeft == ExchangeRight
	ExchangeLeft    exchange.Exchange `json:"-"`
	ExchangeRight   exchange.Exchange `json:"-"`
	ControllerLeft  base.Controllable `json:"-"`
	ControllerRight base.Controllable `json:"-"`

	ID                           string
	Alias                        string
	Status                       Status
	StatusUpdateTime             time.Time
	MonitorRelaxAtTime           time.Time
	simulatingRisk               RiskCategory
	RiskEvents                   []*RiskEvent
	Maintenances                 Maintenances
	Options                      *Options
	SymbolLocks                  *exchange.SyncMapOf[string, *SymbolLock] // 使用 futureCode 作为 key
	SymbolParams                 *exchange.SyncMapOf[string, *option.Options]
	AccountSnapshot              *AccountSnapshot
	PositionSnapshot             *PositionSnapshot
	Monitors                     AccountSnapshots
	AccountSnapshots             *exchange.SyncMapOf[int64, *AccountSnapshot]
	PositionHistory              *exchange.SyncMapOf[int64, *PositionSnapshot]
	TakeProfitOrdersLeft         *exchange.SyncMapOf[string, *exchange.Order] // 现货暂时先不止损止盈，期货平仓后同步卖出
	StopLossOrdersLeft           *exchange.SyncMapOf[string, *exchange.Order]
	TakeProfitOrdersRight        *exchange.SyncMapOf[string, *exchange.Order]
	StopLossOrdersRight          *exchange.SyncMapOf[string, *exchange.Order]
	StoplossOrdersLastHandleTime time.Time

	CreateTime  time.Time
	LastUseTime time.Time
	DeleteTime  *time.Time

	priceDiffAlertTime         *exchange.SyncMapOf[string, time.Time] // symbolCode => time, 用于防止重复报警
	autoTakeProfitCoolingUntil time.Time
}

type SymbolPriceDiff struct {
	Exchange    string
	SymbolCode  string
	PriceLeft   float64
	PriceRight  float64
	RealRatio   float64
	TargetRatio float64
	CreateTime  time.Time
}

package funding_arbitrage

import (
	"errors"
	"fmt"
	"sort"

	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

func (this *FundingArbitrager) GetUSDXSymbol() string {
	if this.ControllerRight != nil {
		return this.ControllerRight.GetBaseConfig().USDXSymbol
	}
	return ""
}

func (this *FundingArbitrager) GetSymbolCode(symbol string, isRight bool) (symbolCode *exchange.SymbolCode, er error) {
	uSymbol := this.GetUSDXSymbol()
	if uSymbol == "" {
		er = errors.New("usdx symbol is empty")
		return
	}
	if isRight {
		_, symbolCode, er = this.ExchangeRight.TranslateFutureSymbol(exchange.USDXMarginedFutures, symbol, uSymbol)
		return
	} else {
		symbolCode, er = this.ExchangeLeft.TranslateSpotSymbol(symbol, uSymbol)
		return
	}
}

func (this *FundingArbitrager) GetSymbolCodesForAccountSnapshot(snapshot *AccountSnapshot) (allSymbolCodeStrs []string, allSymbols []string, symbolCodesLeft []*exchange.SymbolCode, symbolCodesRight []*exchange.SymbolCode, symbolsLeft []string, symbolsRight []string, er error) {
	snapshot.SymbolContextRight.Range(func(symbol string, ctx *SymbolContext) bool {
		uSymbol := this.GetUSDXSymbol()
		_, symbolCode, err := this.ExchangeRight.TranslateFutureSymbol(exchange.USDXMarginedFutures, symbol, uSymbol)
		if err != nil {
			return true
		}
		allSymbolCodeStrs = append(allSymbolCodeStrs, symbolCode.String())
		return true
	})

	allSymbolCodeStrs = utils.Unique(allSymbolCodeStrs)
	sort.Strings(allSymbolCodeStrs)

	for _, symbolCodeStr := range allSymbolCodeStrs {
		symbolLeft, symbolRight, err := this.TranslateBothSideSymbols(symbolCodeStr)
		if err != nil {
			continue
		}
		symbolsLeft = append(symbolsLeft, symbolLeft)
		symbolsRight = append(symbolsRight, symbolRight)
		allSymbols = append(allSymbols, symbolLeft, symbolRight)

		symbolCodeRight, err := exchange.NewSymbolCode(symbolCodeStr, this.GetUSDXSymbol())
		if err != nil {
			this.Errorf("get symbol codes for account snapshot, new symbol code left failed, error: %s", err)
			continue
		}
		symbolCodeLeft, err := exchange.NewSymbolCode(fmt.Sprintf("%s--", symbolCodeRight.Coin()), this.GetUSDXSymbol())
		if err != nil {
			this.Errorf("get symbol codes for account snapshot, new symbol code left failed, error: %s", err)
			continue
		}
		symbolCodesLeft = append(symbolCodesLeft, symbolCodeLeft)
		symbolCodesRight = append(symbolCodesRight, symbolCodeRight)
	}
	// 去重并且排序保持 symbol 的稳定性
	allSymbols = utils.Unique(allSymbols)
	sort.Strings(allSymbols)
	if len(symbolCodesLeft) != len(symbolsLeft) || len(symbolCodesRight) != len(symbolsRight) {
		er = errors.New("symbol codes and symbols length mismatch")
	}
	return
}

func (this *FundingArbitrager) ConvertSymbol(symbol string, leftToRight bool) (convertedSymbol string, er error) {
	if err := this.CheckExchanges(); err != nil {
		er = fmt.Errorf("check exchanges failed, error: %s", err)
		return
	}
	usdxSymbol := this.GetUSDXSymbol()

	if leftToRight {
		symbolCode, err := this.ExchangeLeft.TranslateSpotSymbol(symbol, usdxSymbol)
		if err != nil {
			return "", fmt.Errorf("translate symbol to symbol code failed, error: %s", err)
		}
		convertedSymbol, err = this.ExchangeRight.TranslateSymbolCodeToFutureSymbol(symbolCode.ToUDSXPerp())
		if err != nil {
			return "", fmt.Errorf("translate symbol code to symbol failed, error: %s", err)
		}
	} else {
		_, symbolCode, err := this.ExchangeRight.TranslateFutureSymbol(exchange.USDXMarginedFutures, symbol, usdxSymbol)
		if err != nil {
			return "", fmt.Errorf("translate future symbol failed, error: %s", err)
		}
		convertedSymbol, err = this.ExchangeLeft.TranslateSymbolCodeToSpotSymbol(symbolCode.ToSpot())
		if err != nil {
			return "", fmt.Errorf("translate symbol code to symbol failed, error: %s", err)
		}
	}
	return
}

// 获取现货/期货交易对，检查 symbolCode 在交易所是否存在
func (this *FundingArbitrager) TranslateBothSideSymbols(symbolCodeStr string) (symbolLeft, symbolRight string, er error) {
	if this.ExchangeLeft == nil {
		er = fmt.Errorf("exchange left not ready")
		return
	}
	if this.ExchangeRight == nil {
		er = fmt.Errorf("exchange right not ready")
		return
	}
	symbolCode, err := exchange.NewSymbolCode(symbolCodeStr, this.GetUSDXSymbol())
	if err != nil {
		er = fmt.Errorf("new symbol code failed, error: %s", err)
		return
	}
	if !symbolCode.IsPerp() {
		er = fmt.Errorf("symbol code is not perp: %s", symbolCode)
		return
	}
	symbolLeft, err = this.ExchangeLeft.TranslateSymbolCodeToSpotSymbol(symbolCode)
	if err != nil {
		er = fmt.Errorf("translate left symbol code to spot symbol failed, error: %s", err)
		return
	}

	symbolRight, err = this.ExchangeRight.TranslateSymbolCodeToFutureSymbol(symbolCode)
	if err != nil {
		er = fmt.Errorf("translate right symbol code to future symbol failed, error: %s", err)
		return
	}
	return
}

func (this *FundingArbitrager) ParseSymbolCodes(symbolCodeStr string) (allSymbolCodeStrs []string, symbolCodesLeft []*exchange.SymbolCode, symbolCodesRight []*exchange.SymbolCode, symbolsLeft []string, symbolsRight []string, er error) {
	var err error
	allSymbolCodeStrs, symbolsLeft, symbolsRight, err = exchange.ParseSymbolsFromSymbolCodes(this.ExchangeLeft, symbolCodeStr, this.GetUSDXSymbol())
	if err != nil {
		er = err
		return
	}
	for _, symbol := range symbolsLeft {
		symbolCodeLeft, err := this.ExchangeLeft.TranslateSpotSymbol(symbol, this.GetUSDXSymbol())
		if err != nil {
			continue
		}
		symbolCodesLeft = append(symbolCodesLeft, symbolCodeLeft)
	}
	for _, symbol := range symbolsRight {
		_, symbolCode, err := this.ExchangeRight.TranslateFutureSymbol(exchange.USDXMarginedFutures, symbol, this.GetUSDXSymbol())
		if err != nil {
			continue
		}
		symbolCodesRight = append(symbolCodesRight, symbolCode)
	}
	if len(symbolCodesLeft) != len(symbolsLeft) || len(symbolCodesRight) != len(symbolsRight) {
		er = errors.New("symbol codes and symbols length mismatch")
	}
	if len(symbolCodesLeft) != len(symbolCodesRight) {
		er = errors.New("symbol codes length mismatch")
	}
	return
}

// 获取现货/期货交易对，不检查 symbolCode 在交易所是否存在
func (this *FundingArbitrager) GetSymbolPair(symbolCodeStr string) (symbolPair *exchange.SymbolPair, er error) {
	symbolCodeRight, err := exchange.NewSymbolCode(symbolCodeStr, this.GetUSDXSymbol())
	if err != nil {
		er = fmt.Errorf("new symbol code failed: %s", err)
		return
	}
	if !symbolCodeRight.IsPerp() {
		er = fmt.Errorf("symbol code is not perp: %s", symbolCodeRight)
		return
	}
	symbolLeft, symbolRight, err := this.TranslateBothSideSymbols(symbolCodeStr)
	if err != nil {
		er = fmt.Errorf("translate both side symbols failed, error: %s", err)
		return
	}
	symbolCodeLeft := symbolCodeRight.ToSpot()
	symbolPair = exchange.NewCommonSymbolPair(symbolCodeLeft, symbolCodeRight, symbolLeft, symbolRight)
	return
}

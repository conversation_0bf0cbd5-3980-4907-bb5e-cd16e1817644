package funding_arbitrage

import (
	"fmt"
)

func (this *FundingArbitrager) SimulateRiskEvent(riskCategory RiskCategory) (er error) {
	if !this.orderController.IsDebug() {
		this.WarnMsgf("当前不是 debug 模式，不执行模拟风险事件")
		return
	}
	this.Debugf("模拟风险事件: %s, equal: %v", riskCategory, riskCategory == RiskCategoryMarginRatioLow)

	switch riskCategory {
	case RiskCategoryMarginValueIsZero:
		er = this.SimulateMarginValueIsZeroRiskEvent()
	case RiskCategoryMarginRatioLow:
		er = this.SimulateMarginRatioKeepLowRiskEvent()
	case RiskCategoryPriceDiff:
		er = this.SimulateSymbolPriceDiffTooLargeRiskEvent()
	case RiskCategoryMarkPriceDiff:
		er = this.SimulateSymbolMarkPriceDiffTooLargeRiskEvent()
	default:
		return fmt.Errorf("unknown risk category: %s", riskCategory)
	}
	return
}

func (this *FundingArbitrager) SimulateMarginValueIsZeroRiskEvent() (err error) {
	this.AccountSnapshot.MarginValueLeft = 0
	return nil
}

func (this *FundingArbitrager) SimulateMarginRatioKeepLowRiskEvent() (er error) {
	return nil
}

func (this *FundingArbitrager) SimulateSymbolPriceDiffTooLargeRiskEvent() (er error) {
	return nil
}

func (this *FundingArbitrager) SimulateSymbolMarkPriceDiffTooLargeRiskEvent() (er error) {
	return nil
}

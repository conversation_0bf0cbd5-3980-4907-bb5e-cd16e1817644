package funding_arbitrage

import (
	"fmt"
	"math"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/hyperliquid"
	"github.com/wizhodl/quanter/utils"
	"golang.org/x/exp/rand"
)

// checkMinValue checks if the given quantity has sufficient value to meet minimum trading requirements
func (this *FundingArbitrager) checkMinValue(symbol string, qty float64, isRight bool) bool {
	if qty <= 0 {
		return false
	}

	var ex exchange.Exchange
	var instrumentType exchange.InstrumentType
	if isRight {
		ex = this.ExchangeRight
		instrumentType = exchange.USDXMarginedFutures
	} else {
		ex = this.ExchangeLeft
		instrumentType = exchange.Spot
	}

	// Try to get price from cached symbol context first
	var lastPrice float64
	if isRight {
		if symbolContext, ok := this.AccountSnapshot.SymbolContextRight.Load(symbol); ok && symbolContext != nil && symbolContext.LastPrice > 0 {
			lastPrice = symbolContext.LastPrice
		}
	} else {
		if symbolContext, ok := this.AccountSnapshot.SymbolContextLeft.Load(symbol); ok && symbolContext != nil && symbolContext.LastPrice > 0 {
			lastPrice = symbolContext.LastPrice
		}
	}

	// If no cached price, get fresh price from exchange
	if lastPrice <= 0 {
		price, err := ex.GetLastPrice(instrumentType, symbol, false)
		if err != nil || price <= 0 {
			// If we can't get price, use conservative approach: assume small qty means small value
			return qty >= 0.0001
		}
		lastPrice = price
	}

	positionValue := qty * lastPrice
	return positionValue >= MinPositionValue
}

func (this *FundingArbitrager) GetCreateOrderArgs(longOrShort string, symbolCodeStr string, qtyStr string) (symbolCode *exchange.SymbolCode, orderArgs exchange.CreateOrderArgs, er error) {
	_, symbolRight, err := this.TranslateBothSideSymbols(symbolCodeStr)
	if err != nil {
		er = fmt.Errorf("translate both side symbols failed: %s", err)
		return
	}

	uSymbol := this.ControllerRight.GetBaseConfig().USDXSymbol
	symbolCode, err = exchange.NewSymbolCode(symbolCodeStr, uSymbol)
	if err != nil {
		er = fmt.Errorf("parse symbol code failed: %s", err)
		return
	}
	if !symbolCode.IsFuture() {
		er = fmt.Errorf("symbol code is not future: %s", symbolCode)
		return
	}

	orderType := exchange.Market
	tradeMode := exchange.TradeModeCross

	quoteQty := 0.0
	qty := 0.0
	if len(qtyStr) > 2 && exchange.SliceContains([]string{".u", ".d", ".y"}, qtyStr[len(qtyStr)-2:]) {
		if symbolCode.IsFuture() {
			suffix := qtyStr[len(qtyStr)-2:]
			validSuffix := symbolCode.InstrumentType().GetSuffix()
			if !strings.EqualFold(suffix, validSuffix) {
				er = fmt.Errorf("%s future symbol code only support %s suffix", symbolCode, strings.ToLower(validSuffix))
				return
			}
		}
		qtyStr = qtyStr[:len(qtyStr)-2]
		quoteQty, err = strconv.ParseFloat(qtyStr, 64)
		if err != nil {
			er = fmt.Errorf("qty args invalid: %s", err)
			return
		}
	} else {
		qty, err = strconv.ParseFloat(qtyStr, 64)
		if err != nil {
			er = fmt.Errorf("qty args invalid: %s", err)
			return
		}
	}

	side := exchange.OrderSideBuy
	if longOrShort == "short" {
		side = exchange.OrderSideSell
	}
	orderArgs = exchange.CreateOrderArgs{
		InstrumentType: symbolCode.InstrumentType(),
		Symbol:         symbolRight,
		Type:           orderType,
		TradeMode:      tradeMode,
		Qty:            qty,
		QuoteQty:       quoteQty,
		TimeInForce:    exchange.GTC,
		Side:           side,
	}
	if longOrShort == "long" {
		orderArgs.ReduceOnly = true
	}
	return
}

func (this *FundingArbitrager) OpenPosition(longOrShort string, symbolCodeStr string, qtyStr string, splits int) (er error) {
	symbolCode, orderArgs, err := this.GetCreateOrderArgs(longOrShort, symbolCodeStr, qtyStr)
	if err != nil {
		er = fmt.Errorf("get create order args failed: %s", err)
		return
	}
	if symbolCode.IsFuture() {
		positions, err := this.ExchangeRight.GetPositions(symbolCode.InstrumentType(), orderArgs.Symbol, false)
		if err != nil {
			er = fmt.Errorf("get positions failed, instrumentType: %s, symbol: %s, error: %s", symbolCode.InstrumentType(), orderArgs.Symbol, err)
			return
		}
		for _, position := range positions {
			if orderArgs.Side == exchange.OrderSideBuy && position.Side == exchange.PositionSideShort {
				orderArgs.ReduceOnly = true
				break
			} else if orderArgs.Side == exchange.OrderSideSell && position.Side == exchange.PositionSideLong {
				orderArgs.ReduceOnly = true
				break
			}
		}
	}

	// 开仓用右侧做空期货，现货会自动跟单
	leverage, err := this.GetLeverage(orderArgs.Symbol, true)
	if err != nil {
		this.ErrorMsgf("open position, get leverage failed: %s", err)
		return
	}
	err = this.ExchangeRight.SetLeverage(orderArgs.InstrumentType, orderArgs.Symbol, exchange.Cross, exchange.UnknownPositionSide, leverage)
	if err != nil {
		this.ErrorMsgf("open position, set leverage failed: %s", err)
		return
	}

	successSplits, failedSplits, err := this.splitOrder(this.ExchangeRight, symbolCode, orderArgs, splits, false, func(successOrders []*exchange.Order) {
		for _, order := range successOrders {
			this.orderController.AppendOrderRecord(order, this.RightControllerID, this.ExchangeRight.GetName(), symbolCode)
		}
	})
	if err != nil {
		er = fmt.Errorf("split order failed: %s", err)
		return
	}
	if failedSplits > 0 {
		this.ErrorMsgf("open position partially failed: %s, %s, %d/%d", this.ExchangeRight.GetName(), symbolCodeStr, failedSplits, failedSplits+successSplits)
	}

	return
}

func (this *FundingArbitrager) Wash(ex exchange.Exchange, symbolItem *exchange.SymbolItem, qty, quoteQty float64, splits int, callback func(successOrders []*exchange.Order)) error {
	lock, _ := this.SymbolLocks.LoadOrStore(symbolItem.Code.String(), &SymbolLock{
		SymbolCode: symbolItem.Code.String(),
	})
	if lock == nil {
		return fmt.Errorf("symbol lock not found: %s", symbolItem.Code.String())
	}

	lock.ClosingPosition.Lock()
	defer lock.ClosingPosition.Unlock()

	orderArgs := exchange.CreateOrderArgs{
		InstrumentType: exchange.USDXMarginedFutures,
		Symbol:         symbolItem.Symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCross,
		Qty:            qty,
		QuoteQty:       quoteQty,
		TimeInForce:    exchange.GTC,
		Side:           exchange.OrderSideBuy,
	}

	successSplits, failedSplits, err := this.splitOrder(ex, symbolItem.Code, orderArgs, splits, true, callback)
	if err != nil {
		return fmt.Errorf("split order failed: %s", err)
	}
	if failedSplits > 0 {
		this.ErrorMsgf("wash order partially failed: %s, %s, %d/%d", ex.GetName(), symbolItem.Symbol, failedSplits, successSplits+failedSplits)
	}

	return nil
}

func (this *FundingArbitrager) splitOrder(ex exchange.Exchange, code *exchange.SymbolCode, orderArgs exchange.CreateOrderArgs, splits int, isWashOrder bool, callback func(successOrders []*exchange.Order)) (successSplits, failedSplits int, err error) {
	orderSplitValue := this.GetParam(ParamTypeSplitValue, code.String()).(float64)

	successOrders := []*exchange.Order{}
	if splits == -1 {
		orderValue := orderArgs.QuoteQty
		if orderValue == 0 {
			lastPrice, err := ex.GetLastPrice(orderArgs.InstrumentType, orderArgs.Symbol, false)
			if err != nil {
				return 0, 0, fmt.Errorf("get last price failed: %s", err)
			}
			orderValue, _ = ex.Qty2Size(exchange.USDXMarginedFutures, orderArgs.Symbol, lastPrice, orderArgs.Qty)
		}
		splits = int(math.Ceil(orderValue / orderSplitValue))
	}

	orderArgsArr := []exchange.CreateOrderArgs{}
	if splits > 1 {
		var splitQty, splitQuoteQty float64
		var lastQty, lastQuoteQty float64
		if orderArgs.Qty > 0 {
			splitQty = orderArgs.Qty / float64(splits)
			splitQty = ex.FloorQty(exchange.USDXMarginedFutures, orderArgs.Symbol, splitQty)
			lastQty = orderArgs.Qty - splitQty*float64(splits-1)
			lastQty = ex.RoundQty(exchange.USDXMarginedFutures, orderArgs.Symbol, lastQty)
		} else {
			splitQuoteQty = orderArgs.QuoteQty / float64(splits)
			splitQuoteQty = math.Floor(splitQuoteQty)
			lastQuoteQty = orderArgs.QuoteQty - splitQuoteQty*float64(splits-1)
			lastQuoteQty = math.Floor(lastQuoteQty)
		}

		for i := 0; i < splits-1; i++ {
			splitOrderArgs := orderArgs
			if splitOrderArgs.Qty > 0 && splitQty > 0 {
				splitOrderArgs.Qty = splitQty
				orderArgsArr = append(orderArgsArr, splitOrderArgs)
			} else if splitOrderArgs.QuoteQty > 0 && splitQuoteQty > 0 {
				splitOrderArgs.QuoteQty = splitQuoteQty
				orderArgsArr = append(orderArgsArr, splitOrderArgs)
			}
		}
		lastOrderArgs := orderArgs
		if lastOrderArgs.Qty > 0 && lastQty > 0 {
			lastOrderArgs.Qty = lastQty
			orderArgsArr = append(orderArgsArr, lastOrderArgs)
		} else if lastOrderArgs.QuoteQty > 0 && lastQuoteQty > 0 {
			lastOrderArgs.QuoteQty = lastQuoteQty
			orderArgsArr = append(orderArgsArr, lastOrderArgs)
		}
	} else {
		orderArgsArr = []exchange.CreateOrderArgs{orderArgs}
	}

	for i, orderArgs := range orderArgsArr {
		if !orderArgs.ReduceOnly {
			// 检查当前品种是否在 AtCap 状态
			lock, _ := this.SymbolLocks.Load(code.String())
			if lock != nil && lock.IsAtCapNow() {
				this.SendMsgf("open interest is at cap, stop creating open order: %s", code)
				return
			}
		}

		order, err := ex.CreateOrder(orderArgs)
		if err != nil {
			this.Errorf("split order, split %d, create order failed: %s", i+1, err)
			failedSplits++
			continue
		}
		successOrders = append(successOrders, order)

		// 如果是刷量交易，则需要额外创建一个反向的拆单订单；主要是为了尽量减小一组刷量订单的价差
		if isWashOrder {
			if orderArgs.Side == exchange.OrderSideBuy {
				orderArgs.Side = exchange.OrderSideSell
			} else {
				orderArgs.Side = exchange.OrderSideBuy
			}
			order, err := ex.CreateOrder(orderArgs)
			if err != nil {
				this.Errorf("split order, split %d, create wash order failed: %s", i+1, err)
				failedSplits++
				continue
			}
			successOrders = append(successOrders, order)
		}

		successSplits++
		if splits > 1 {
			time.Sleep(time.Duration(this.Options.SplitInterval) * time.Second)
		}
	}
	if callback != nil {
		callback(successOrders)
	}
	return
}

func (this *FundingArbitrager) Rotate(symbolCodeStr string, ratio float64, splits int) error {
	code, err := exchange.NewSymbolCode(symbolCodeStr, this.GetUSDXSymbol())
	if err != nil {
		return fmt.Errorf("get symbol code failed: %s", err)
	}
	ex := this.ExchangeRight
	lock, _ := this.SymbolLocks.Load(code.String())
	if lock == nil {
		return fmt.Errorf("symbol lock not found: %s", code.String())
	}

	lock.SyncingPosition.Lock()
	lock.ClosingPosition.Lock()
	lock.RatatingPosition.Lock()
	defer lock.SyncingPosition.Unlock()
	defer lock.ClosingPosition.Unlock()
	defer lock.RatatingPosition.Unlock()

	symbol, err := ex.TranslateSymbolCodeToFutureSymbol(code)
	if err != nil {
		return fmt.Errorf("translate symbol code to future symbol failed: %s", err)
	}

	position, err := ex.GetPosition(exchange.USDXMarginedFutures, symbol, exchange.UnknownPositionSide, false)
	if err != nil {
		return fmt.Errorf("get position failed: %s", err)
	}

	if position.Qty == 0 {
		return fmt.Errorf("position is 0")
	}

	reduceQty := ex.RoundQty(exchange.USDXMarginedFutures, symbol, math.Abs(position.Qty*ratio))
	closeSide := exchange.OrderSideBuy
	openSide := exchange.OrderSideSell
	if position.Qty > 0 {
		closeSide = exchange.OrderSideSell
		openSide = exchange.OrderSideBuy
	}

	orderArgs := exchange.CreateOrderArgs{
		InstrumentType: exchange.USDXMarginedFutures,
		Symbol:         symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCross,
		Qty:            reduceQty,
		TimeInForce:    exchange.GTC,
		Side:           closeSide,
		ReduceOnly:     true,
	}

	successSplits, failedSplits, err := this.splitOrder(ex, code, orderArgs, splits, false, nil)
	if err != nil {
		return fmt.Errorf("split order failed: %s", err)
	}
	if failedSplits > 0 {
		this.ErrorMsgf("rotate close order partially failed: %s, %s, %d/%d", ex.GetName(), symbol, failedSplits, successSplits+failedSplits)
	}

	orderArgs.Side = openSide
	orderArgs.ReduceOnly = false

	successSplits, failedSplits, err = this.splitOrder(ex, code, orderArgs, splits, false, nil)
	if err != nil {
		return fmt.Errorf("split order failed: %s", err)
	}
	if failedSplits > 0 {
		this.ErrorMsgf("rotate open order partially failed: %s, %s, %d/%d", ex.GetName(), symbol, failedSplits, successSplits+failedSplits)
	}

	return nil
}

func (this *FundingArbitrager) IsTheSamePositionQty(leftQty, rightQty float64) bool {
	if leftQty == 0 && rightQty == 0 {
		return true
	}
	// 如果左右两边仓位都小于 1e-4，则认为仓位相同，避免浮点数精度问题
	if math.Abs(leftQty) < 1e-4 && math.Abs(rightQty) < 1e-4 {
		return true
	}
	diffRatio := math.Abs(leftQty-rightQty) / math.Max(math.Abs(leftQty), math.Abs(rightQty))
	return diffRatio < PositionQtyDiffRatioThreshold
}

// 右边仓位同步到左边
func (this *FundingArbitrager) SyncPositions() {
	if !this.mutexes.syncPositionMutex.TryLock() {
		return
	}
	defer this.mutexes.syncPositionMutex.Unlock()

	if this.PositionSnapshot == nil {
		return
	}

	this.Infof("syncing positions...")

	if time.Since(this.PositionSnapshot.CreateTime) > LoopInterval {
		this.Warnf("position snapshot is too old, ignore")
		return
	}

	// 检查 PositionsLeft 的仓位是否与 PositionsRight 一致，如果不一致，则同步
	for _, positionRight := range this.PositionSnapshot.PositionsRight {
		// 检查 PositionsLeft 是否有相同的仓位
		var positionLeft *exchange.Position
		for _, p := range this.PositionSnapshot.PositionsLeft {
			symbol, err := this.ConvertSymbol(p.Symbol, true)
			if err != nil {
				this.ErrorMsgf("sync positions, convert symbol failed: %s", err)
				continue
			}
			if symbol == positionRight.Symbol {
				positionLeft = p
				break
			}
		}
		needSync := true
		if positionLeft != nil {
			targetPosition := this.ExchangeLeft.RoundQty(positionLeft.InstrumentType, positionLeft.Symbol, -positionRight.Qty)
			if this.IsTheSamePositionQty(positionLeft.Qty, targetPosition) {
				needSync = false
			}
		}
		if needSync {
			go this.syncPosition(positionRight.Symbol)
		}
	}

	// PositionsLeft 有仓位，PositionsRight 为 0 的情况
	for _, positionLeft := range this.PositionSnapshot.PositionsLeft {
		value, err := this.ExchangeLeft.Qty2Size(positionLeft.InstrumentType, positionLeft.Symbol, positionLeft.LastPrice, positionLeft.Qty)
		if err != nil {
			this.Errorf("sync positions, qty2size failed: %s", err)
			continue
		}
		// 有时候现货会有一些小价值的空投，忽略
		if value < MinPositionValue {
			continue
		}
		code, err := this.GetSymbolCode(positionLeft.Symbol, false)
		if err != nil {
			this.Errorf("sync positions, get symbol code failed: %s", err)
			continue
		}
		if !utils.SliceContainsEqualFold(this.Options.ValidCoins, code.Coin()) {
			continue
		}
		var positionRight *exchange.Position
		for _, p := range this.PositionSnapshot.PositionsRight {
			symbolLeft, err := this.ConvertSymbol(p.Symbol, false)
			if err != nil {
				this.ErrorMsgf("sync positions, convert symbol right to left failed: %s", err)
				continue
			}
			if symbolLeft == positionLeft.Symbol {
				positionRight = p
				break
			}
		}
		if positionRight == nil || positionRight.Qty == 0 {
			symbolRight, err := this.ConvertSymbol(positionLeft.Symbol, true)
			if err != nil {
				this.ErrorMsgf("sync positions, convert symbol left to right failed: %s", err)
				return
			}
			go this.syncPosition(symbolRight)
		}
	}
}

// Left 的仓位与 Right 的仓位同步，即获取合约仓位，同步到现货
func (this *FundingArbitrager) syncPosition(symbolRight string) {
	code, err := this.GetSymbolCode(symbolRight, true)
	if err != nil {
		this.Errorf("sync position error, get symbol code failed: %s", err)
		return
	}
	lock, _ := this.SymbolLocks.LoadOrStore(code.String(), &SymbolLock{
		SymbolCode: code.String(),
	})
	if !lock.SyncingPosition.TryLock() {
		this.Infof("syncing position try lock failed, symbol: %s", symbolRight)
		return
	}
	defer lock.SyncingPosition.Unlock()

	position, err := this.ExchangeRight.GetPosition(exchange.USDXMarginedFutures, symbolRight, exchange.UnknownPositionSide, false)
	if err != nil {
		this.ErrorMsgf("get position failed: %s", err)
		return
	}

	symbolLeft, err := this.ConvertSymbol(symbolRight, false)
	if err != nil {
		this.ErrorMsgf("sync position, convert symbol failed: %s", err)
		return
	}

	this.syncPositionOnLeft(symbolLeft, -position.Qty)
}

func (this *FundingArbitrager) syncPositionOnLeft(symbol string, targetPostion float64) {
	if targetPostion < 0 {
		this.ErrorMsgf("sync position error, target position is negative: %s, %v", symbol, targetPostion)
		return
	}

	this.Infof("syncing spot position: %s, %v", symbol, targetPostion)
	syncPostionSuccess := false
	isOpen := false

	symbolRight, err := this.ConvertSymbol(symbol, true)
	if err != nil {
		this.ErrorMsgf("convert symbol failed: %s", err)
		return
	}

	var deltaRatio float64

	defer func() {
		if !isOpen {
			// 加仓出错时才需要减仓
			return
		}
		if syncPostionSuccess {
			this.counters.syncPositionErrorCounter.Store(0)
		} else {
			this.counters.syncPositionErrorCounter.Add(1)
		}
		if this.counters.syncPositionErrorCounter.Load() >= SyncPositionErrorLimit {
			this.SendMsgf("sync position error count reach limit, reduce positions.")
			// 保守起见，减仓比例始终不大于 SyncPositionErrorReduceRatio
			// 下个循环可以重试，再次减仓
			reduceRatio := SyncPositionErrorReduceRatio
			if deltaRatio > 0 {
				reduceRatio = math.Min(deltaRatio, SyncPositionErrorReduceRatio)
			}
			err := this.ReducePositions(reduceRatio, symbolRight)
			if err != nil {
				this.ErrorMsgf("reduce positions failed: %s", err)
			}
			this.counters.syncPositionErrorCounter.Store(0)
		}
	}()

	spotCode, err := this.GetSymbolCode(symbol, false)
	if err != nil {
		this.Errorf("sync position error, get symbol code failed: %s", err)
		return
	}

	balance, _, err := this.ExchangeLeft.GetBalance(exchange.Spot, spotCode.Coin())
	if err != nil {
		this.ErrorMsgf("sync position, get balance failed: %s", err)
		return
	}

	targetPostion = this.ExchangeLeft.RoundQty(exchange.Spot, symbol, targetPostion)

	// Skip very small positions when target is 0 to avoid trading dust amounts
	if targetPostion == 0 && balance > 0 {
		if !this.checkMinValue(symbol, balance, false) {
			this.Infof("skipping small position when target is 0: %s, balance: %v, value < %.2f", symbol, balance, MinPositionValue)
			syncPostionSuccess = true
			return
		}
	}

	if this.IsTheSamePositionQty(balance, targetPostion) {
		this.Infof("position is already synced: %s, %s, %v = %v", exchange.Spot, symbol, balance, targetPostion)
		syncPostionSuccess = true
		return
	}

	if math.Abs(targetPostion) > math.Abs(balance) {
		isOpen = true
	}

	// 如果当前仓位为 0，但有止损止盈单，则需要平仓左边的仓位
	if balance == 0 {
		if (this.StopLossOrdersLeft != nil && this.StopLossOrdersLeft.Exist(symbol)) ||
			(this.TakeProfitOrdersLeft != nil && this.TakeProfitOrdersLeft.Exist(symbol)) {
			this.Infof("left position is 0, but has stop loss or take profit order, close right position: %s", symbol)
			this.ClosePositions([]string{symbolRight}, 1)
			return
		}
	}

	origPosition := balance
	this.Infof("syncing spot position: %s, %v => %v", symbol, origPosition, targetPostion)

	qty := targetPostion - balance
	deltaRatio = math.Abs(qty) / math.Abs(targetPostion) // 计算仓位差异比例

	var side exchange.OrderSide
	if qty > 0 {
		side = exchange.OrderSideBuy
	} else {
		side = exchange.OrderSideSell
	}

	orderArgs := exchange.CreateOrderArgs{
		InstrumentType: exchange.Spot,
		Symbol:         symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCross,
		TimeInForce:    exchange.GTC,
		Qty:            this.ExchangeLeft.FloorQty(exchange.Spot, symbol, math.Abs(qty)),
		Side:           side,
	}
	if targetPostion == 0 {
		orderArgs.ReduceOnly = true
	}

	minNotional := MinPositionValue
	ins, _ := this.ExchangeLeft.GetInstrument(exchange.Spot, symbol)
	if ins != nil && ins.MinNotional > 0 {
		minNotional = ins.MinNotional
	}

	// 现货可能有很小数量的余额，检查 qty 是否太小不足以发送订单
	symbolContextLeft, _ := this.AccountSnapshot.SymbolContextLeft.Load(symbol)
	if symbolContextLeft != nil && symbolContextLeft.LastPrice > 0 && orderArgs.Qty*symbolContextLeft.LastPrice < minNotional {
		this.Infof("sync position failed, order value is too small: %s, %s, %v => %v, qty: %v, price: %v", this.ExchangeLeft.GetName(), symbol, origPosition, targetPostion, orderArgs.Qty, symbolContextLeft.LastPrice)
		return
	}

	_, err = this.ExchangeLeft.CreateOrder(orderArgs)
	if err != nil {
		// 只要是同步失败，都暂停自动止盈 2 分钟，主要是 Only post-only orders allowed immediately after network upgrade 的情况
		this.autoTakeProfitCoolingUntil = time.Now().Add(2 * time.Minute)
		this.ErrorMsgf("sync position failed: %s, %s, %v => %v, create order failed: %s", this.ExchangeLeft.GetName(), symbol, origPosition, targetPostion, err)
		return
	}

	tryCount := 10
	for i := 0; i < tryCount; i++ {
		time.Sleep(2 * time.Second)

		// 检查是否到目标仓位
		balance, _, err = this.ExchangeLeft.GetBalance(exchange.Spot, spotCode.Coin())
		if err != nil {
			this.ErrorMsgf("get balance failed: %s", err)
			continue
		}

		if this.IsTheSamePositionQty(balance, targetPostion) {
			this.SendMsgf("Spot position is synced: %s, %v => %v", symbol, origPosition, balance)
			syncPostionSuccess = true
			break
		}

		if i == tryCount-1 {
			this.ErrorMsgf("sync spot position failed, position is not synced: [%s] %s, current: %v, target: %v", this.ExchangeLeft.GetName(), symbol, balance, targetPostion)
			break
		} else {
			this.Warnf("check spot position failed, retrying: %s, current: %v, target: %v", symbol, balance, targetPostion)
		}
	}
}

// 因为交易所订单支持原因，现货暂时先不止损止盈，期货平仓后同步卖出
func (this *FundingArbitrager) handleStoplossOrders() {
	if !this.mutexes.handleStoplossOrdersMutex.TryLock() {
		return
	}

	defer this.mutexes.handleStoplossOrdersMutex.Unlock()

	if this.StopLossOrdersLeft == nil {
		this.StopLossOrdersLeft = exchange.NewSyncMapOf[string, *exchange.Order]()
	}
	if this.StopLossOrdersRight == nil {
		this.StopLossOrdersRight = exchange.NewSyncMapOf[string, *exchange.Order]()
	}
	if this.TakeProfitOrdersLeft == nil {
		this.TakeProfitOrdersLeft = exchange.NewSyncMapOf[string, *exchange.Order]()
	}
	if this.TakeProfitOrdersRight == nil {
		this.TakeProfitOrdersRight = exchange.NewSyncMapOf[string, *exchange.Order]()
	}

	if this.Maintenances.HasActiveMaintainance() {
		// 维护期间不挂止损止盈单，取消止盈止损单
		this.cancelAllStoplossOrders()
		return
	}

	positionsLeft, positionsRight, err := this.GetPositions()
	if err != nil || positionsLeft == nil || positionsRight == nil {
		this.Errorf("handle close orders failed, get positions err: %s", err)
		return
	}

	// 删除不在持仓中的止盈止损单 BEGIN
	this.StopLossOrdersLeft.Range(func(symbol string, order *exchange.Order) bool {
		posFound := false
		for _, position := range positionsLeft {
			if position.Symbol == symbol && position.Qty != 0 {
				posFound = true
				break
			}
		}
		if !posFound {
			err := this.ExchangeLeft.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel stop loss order failed, error: %s", err)
			}
			this.StopLossOrdersLeft.Delete(symbol)
			this.Infof("remove left stop loss order: %s", symbol)
		}
		return true
	})

	this.TakeProfitOrdersLeft.Range(func(symbol string, order *exchange.Order) bool {
		posFound := false
		for _, position := range positionsLeft {
			if position.Symbol == symbol && position.Qty != 0 {
				posFound = true
				break
			}
		}
		if !posFound {
			err := this.ExchangeLeft.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel take profit order failed, error: %s", err)
			}
			this.TakeProfitOrdersLeft.Delete(symbol)
			this.Infof("remove left take profit order: %s", symbol)
		}
		return true
	})

	this.StopLossOrdersRight.Range(func(symbol string, order *exchange.Order) bool {
		posFound := false
		for _, position := range positionsRight {
			if position.Symbol == symbol && position.Qty != 0 {
				posFound = true
				break
			}
		}
		if !posFound {
			err := this.ExchangeRight.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel stop loss order failed, error: %s", err)
			}
			this.StopLossOrdersRight.Delete(symbol)
			this.Infof("remove right stop loss order: %s", symbol)
		}
		return true
	})

	this.TakeProfitOrdersRight.Range(func(symbol string, order *exchange.Order) bool {
		posFound := false
		for _, position := range positionsRight {
			if position.Symbol == symbol && position.Qty != 0 {
				posFound = true
				break
			}
		}
		if !posFound {
			err := this.ExchangeRight.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel take profit order failed, error: %s", err)
			}
			this.TakeProfitOrdersRight.Delete(symbol)
			this.Infof("remove right take profit order: %s", symbol)
		}
		return true
	})
	// 删除不在持仓中的止盈止损单 END

	if time.Since(this.StoplossOrdersLastHandleTime) <= time.Duration(this.Options.StoplossUpdateInterval)*time.Second {
		this.Infof("skip handle stoploss orders, last handle time: %s", this.StoplossOrdersLastHandleTime)
		return
	}

	this.SymbolLocks.Range(func(_ string, lock *SymbolLock) bool {
		// 随机 0 ~ StoplossUpdateInterval 的时间，避免同时更新
		shorterInterval := int(float64(this.Options.StoplossUpdateInterval) * 0.8)
		lock.UpdateStoplossDelay = time.Duration(rand.Intn(shorterInterval)) * time.Second
		return true
	})

	// 暂时仅处理右边期货止损止盈单
	for _, positionRight := range positionsRight {
		if positionRight.Qty == 0 {
			continue
		}

		// 如果有止损止盈单，且价差在一定范围内，则无需重新挂单，仅更新修改时间

		slArgsRight, tpArgsRight, err := this.getStoplossOrderArgs(positionRight, true)
		if err != nil {
			this.Errorf("get stoploss order args failed, error: %s", err)
			continue
		}

		if ctx, load := this.AccountSnapshot.SymbolContextRight.Load(positionRight.Symbol); load {
			ctx.StopLossPrice = slArgsRight.TriggerPrice
			ctx.TakeProfitPrice = tpArgsRight.TriggerPrice
		}

		needUpdateOrderCount := 2
		now := time.Now()
		code, _ := this.GetSymbolCode(positionRight.Symbol, true)
		priceRatio := this.GetParam(ParamTypeStoploss, code.String()).(float64)
		priceDeltaRatio := priceRatio * this.Options.StoplossDiffTolerance
		lastSlOrderRight, ok := this.StopLossOrdersRight.Load(positionRight.Symbol)
		if ok && lastSlOrderRight != nil {
			if math.Abs(slArgsRight.TriggerPrice/lastSlOrderRight.TriggerPrice-1) <= priceDeltaRatio &&
				slArgsRight.Qty == lastSlOrderRight.Qty {
				needUpdateOrderCount--
				lastSlOrderRight.UpdateTime = &now
			}
		}
		lastTpOrderRight, ok := this.TakeProfitOrdersRight.Load(positionRight.Symbol)
		if ok && lastTpOrderRight != nil {
			if math.Abs(tpArgsRight.TriggerPrice/lastTpOrderRight.TriggerPrice-1) <= priceDeltaRatio &&
				tpArgsRight.Qty == lastTpOrderRight.Qty {
				needUpdateOrderCount--
				lastTpOrderRight.UpdateTime = &now
			}
		}
		if needUpdateOrderCount > 0 {
			go this.updateStoplossOrder(slArgsRight, tpArgsRight, true)
		} else {
			this.Infof("price delta is within tolerance, skip update stoploss order, code: %s", code)
		}
	}

	this.StoplossOrdersLastHandleTime = time.Now()
}

func (this *FundingArbitrager) updateStoplossOrder(slArgs, tpArgs *exchange.CreateOrderArgs, isRight bool) {
	var code *exchange.SymbolCode
	var err error
	ex := this.ExchangeLeft
	if isRight {
		ex = this.ExchangeRight
		code, err = this.GetSymbolCode(slArgs.Symbol, true)
		if err != nil {
			this.Errorf("update stoploss order error, get symbol code failed: %s", err)
			return
		}
	} else {
		code, err = this.GetSymbolCode(slArgs.Symbol, false)
		if err != nil {
			this.Errorf("update stoploss order error, get symbol code failed: %s", err)
			return
		}
	}
	lock, _ := this.SymbolLocks.LoadOrStore(code.String(), &SymbolLock{
		SymbolCode: code.String(),
	})
	if isRight {
		if !lock.UpdatingStoplossRight.TryLock() {
			return
		}
		defer lock.UpdatingStoplossRight.Unlock()
	} else {
		if !lock.UpdatingStoplossLeft.TryLock() {
			return
		}
		defer lock.UpdatingStoplossLeft.Unlock()
	}

	this.Debugf("updating stoploss order for symbol: %s, isRight: %v, delay: %v", code.String(), isRight, lock.UpdateStoplossDelay)

	if lock.UpdateStoplossDelay > 0 {
		time.Sleep(lock.UpdateStoplossDelay)
	} else {
		time.Sleep(time.Duration(rand.Intn(10000)) * time.Millisecond)
	}

	// 创建止损单
	stopLossOrder, err := ex.CreateOrder(*slArgs)
	if err != nil {
		this.Errorf("create stop loss order failed, error: %s", err)
		return
	}

	// 创建止盈单
	takeProfitOrder, err := ex.CreateOrder(*tpArgs)
	if err != nil {
		this.Errorf("create take profit order failed, error: %s", err)
		return
	}

	if isRight {
		this.StopLossOrdersRight.Store(slArgs.Symbol, stopLossOrder)
		this.TakeProfitOrdersRight.Store(slArgs.Symbol, takeProfitOrder)
	} else {
		this.StopLossOrdersLeft.Store(slArgs.Symbol, stopLossOrder)
		this.TakeProfitOrdersLeft.Store(slArgs.Symbol, takeProfitOrder)
	}

	// 取消新订单除外的所有平仓单
	openOrders, err := ex.GetOpenOrders(slArgs.InstrumentType, exchange.StopLimit, slArgs.Symbol)
	if err != nil {
		this.Errorf("get open orders failed, error: %s", err)
		return
	}

	if len(openOrders) > 5 {
		// 理论上不会超过 4 个
		this.AlertMsgf("open orders count is too large, symbol: %s, count: %d, isRight: %v", slArgs.Symbol, len(openOrders), isRight)
	}

	for _, order := range openOrders {
		if order.OrderID != stopLossOrder.OrderID && order.OrderID != takeProfitOrder.OrderID && order.IsCloseOrder() {
			err := ex.CancelOrder(slArgs.InstrumentType, exchange.StopLimit, order.Symbol, order.OrderID)
			if err != nil {
				this.Errorf("cancel order failed, error: %s", err)
			}
		}
	}
}

func (this *FundingArbitrager) getStoplossOrderArgs(position *exchange.Position, isRight bool) (slArgs, tpArgs *exchange.CreateOrderArgs, err error) {
	ma, _, err := this.CalculateSymbolPriceMA(PriceTypeLastPrice, position.Symbol, StoplossMAWindow, isRight)
	if err != nil {
		return nil, nil, fmt.Errorf("calculate symbol price ma failed, error: %s", err)
	}

	var code *exchange.SymbolCode
	if isRight {
		code, err = this.GetSymbolCode(position.Symbol, true)
		if err != nil {
			return nil, nil, fmt.Errorf("get symbol code failed: %s", err)
		}
	} else {
		code, err = this.GetSymbolCode(position.Symbol, false)
		if err != nil {
			return nil, nil, fmt.Errorf("get symbol code failed: %s", err)
		}
	}

	// 根据价格范围调整止损止盈比例，价格越低，止损止盈比例越大
	priceRatio := this.GetParam(ParamTypeStoploss, code.String()).(float64) // 如果启用 stddev 算法，则使用 stddev 算法计算价格波动比例

	var stopLossTriggerPrice, takeProfitTriggerPrice float64
	var stopLossPrice, takeProfitPrice float64
	if position.Qty > 0 {
		stopLossTriggerPrice = ma * (1 - priceRatio)
		takeProfitTriggerPrice = ma * (1 + priceRatio)
		stopLossPrice = stopLossTriggerPrice * (1 - ParamStoplossSlippage)
		takeProfitPrice = takeProfitTriggerPrice * (1 - ParamStoplossSlippage)
	} else {
		stopLossTriggerPrice = ma * (1 + priceRatio)
		takeProfitTriggerPrice = ma * (1 - priceRatio)
		stopLossPrice = stopLossTriggerPrice * (1 + ParamStoplossSlippage)
		takeProfitPrice = takeProfitTriggerPrice * (1 + ParamStoplossSlippage)
	}

	args := exchange.CreateOrderArgs{
		InstrumentType: position.InstrumentType,
		Symbol:         position.Symbol,
		Type:           exchange.StopLimit,
		TradeMode:      exchange.TradeModeCross,
		Qty:            math.Abs(position.Qty),
		TimeInForce:    exchange.GTC,
		ClosePosition:  true,
		ReduceOnly:     true,
	}

	if position.Qty > 0 {
		args.Side = exchange.OrderSideSell
	} else {
		args.Side = exchange.OrderSideBuy
	}
	tpArgs = &exchange.CreateOrderArgs{}
	copier.Copy(tpArgs, &args)
	slArgs = &exchange.CreateOrderArgs{}
	copier.Copy(slArgs, &args)

	slArgs.TriggerPrice = stopLossTriggerPrice
	slArgs.Price = stopLossPrice
	if position.Qty > 0 {
		slArgs.TriggerDirection = exchange.TriggerDirectionLower
	} else {
		slArgs.TriggerDirection = exchange.TriggerDirectionHigher
	}

	tpArgs.TriggerPrice = takeProfitTriggerPrice
	tpArgs.Price = takeProfitPrice
	if position.Qty > 0 {
		tpArgs.TriggerDirection = exchange.TriggerDirectionHigher
	} else {
		tpArgs.TriggerDirection = exchange.TriggerDirectionLower
	}

	return
}

func (this *FundingArbitrager) cancelAllStoplossOrders() {
	if this.StopLossOrdersLeft == nil {
		this.StopLossOrdersLeft = exchange.NewSyncMapOf[string, *exchange.Order]()
	}
	if this.StopLossOrdersRight == nil {
		this.StopLossOrdersRight = exchange.NewSyncMapOf[string, *exchange.Order]()
	}
	if this.TakeProfitOrdersLeft == nil {
		this.TakeProfitOrdersLeft = exchange.NewSyncMapOf[string, *exchange.Order]()
	}
	if this.TakeProfitOrdersRight == nil {
		this.TakeProfitOrdersRight = exchange.NewSyncMapOf[string, *exchange.Order]()
	}

	this.StopLossOrdersLeft.Range(func(symbol string, order *exchange.Order) bool {
		err := this.ExchangeLeft.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
		if err != nil {
			this.Errorf("cancel stop loss order failed, error: %s", err)
		}
		this.StopLossOrdersLeft.Delete(symbol)
		this.Infof("remove left stop loss order during maintenance: %s", symbol)
		return true
	})

	this.TakeProfitOrdersLeft.Range(func(symbol string, order *exchange.Order) bool {
		err := this.ExchangeLeft.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
		if err != nil {
			this.Errorf("cancel take profit order failed, error: %s", err)
		}
		this.TakeProfitOrdersLeft.Delete(symbol)
		this.Infof("remove left take profit order during maintenance: %s", symbol)
		return true
	})

	this.StopLossOrdersRight.Range(func(symbol string, order *exchange.Order) bool {
		err := this.ExchangeRight.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
		if err != nil {
			this.Errorf("cancel stop loss order failed, error: %s", err)
		}
		this.StopLossOrdersRight.Delete(symbol)
		this.Infof("remove right stop loss order during maintenance: %s", symbol)
		return true
	})

	this.TakeProfitOrdersRight.Range(func(symbol string, order *exchange.Order) bool {
		err := this.ExchangeRight.CancelOrder(order.InstrumentType, exchange.StopLimit, symbol, order.OrderID)
		if err != nil {
			this.Errorf("cancel take profit order failed, error: %s", err)
		}
		this.TakeProfitOrdersRight.Delete(symbol)
		this.Infof("remove right take profit order during maintenance: %s", symbol)
		return true
	})
}

func (this *FundingArbitrager) CalculateSymbolPriceMA(priceType PriceType, symbol string, timeWindow time.Duration, isRight bool) (ma float64, count int, er error) {
	prices := []float64{}
	for _, snapshot := range this.Monitors {
		if snapshot.CreateTime.Add(timeWindow).Before(time.Now()) {
			continue
		}
		// 早于 MonitorRelaxAtTime 的数据不检查
		if snapshot.CreateTime.Before(this.MonitorRelaxAtTime) {
			continue
		}
		if isRight {
			symbolCtx, ok := snapshot.SymbolContextRight.Load(symbol)
			if ok {
				realPrice := 0.0
				switch priceType {
				case PriceTypeMarkPrice:
					realPrice = symbolCtx.MarkPrice
				case PriceTypeIndexPrice:
					realPrice = symbolCtx.IndexPrice
				case PriceTypeLastPrice:
					realPrice = symbolCtx.LastPrice
				}
				if realPrice > 0 {
					prices = append(prices, realPrice)
				}
			}
		} else {
			symbolCtx, ok := snapshot.SymbolContextLeft.Load(symbol)
			if ok {
				realPrice := 0.0
				switch priceType {
				case PriceTypeMarkPrice:
					realPrice = symbolCtx.MarkPrice
				case PriceTypeIndexPrice:
					realPrice = symbolCtx.IndexPrice
				case PriceTypeLastPrice:
					realPrice = symbolCtx.LastPrice
				}
				if realPrice > 0 {
					prices = append(prices, realPrice)
				}
			}
		}
	}
	if len(prices) > 0 {
		total := 0.0
		for _, price := range prices {
			total += price
		}
		ma = total / float64(len(prices))
		count = len(prices)
	} else {
		er = fmt.Errorf("not enough data to calculate ma")
		return
	}
	return
}

func (this *FundingArbitrager) ClosePositions(symbols []string, splits int) {
	this.Infof("closing positions: %v", symbols)

	positions, err := this.ExchangeRight.GetPositions(exchange.USDXMarginedFutures, "", false)
	if err != nil {
		this.ErrorMsgf("获取持仓失败: %s", err)
		return
	}
	if len(positions) == 0 {
		return
	}
	closedSymbols := []string{}
	erroredSymbols := []string{}
	for _, position := range positions {
		if slices.Contains(symbols, position.Symbol) {
			successSplits, failedSplits, err := this.closePosition(position, splits)
			if err != nil {
				this.Errorf("close position failed: %s", err)
				erroredSymbols = append(erroredSymbols, position.Symbol)
				continue
			}
			if successSplits > 0 {
				closedSymbols = append(closedSymbols, position.Symbol)
			}
			if failedSplits > 0 {
				this.ErrorMsgf("部分仓位平仓失败: %s, 成功平仓数量: %d/%d", position.Symbol, successSplits, successSplits+failedSplits)
			}
		}
	}
	// 把没有平仓的仓位都放到 erroredSymbols 中
	// TODO: 看起来有 bug
	// diffSymbols := utils.SliceSubtract(symbols, closedSymbols)
	// extraFailedSymbols := utils.SliceSubtract(diffSymbols, erroredSymbols)
	// erroredSymbols = append(erroredSymbols, extraFailedSymbols...)

	if len(closedSymbols) > 0 {
		this.SendMsgf("资金费套利机 %s 已平仓仓位: %s", this.GetAliasOrID(), strings.Join(closedSymbols, ","))
	}
	if len(erroredSymbols) > 0 {
		this.ErrorMsgf("资金费套利机 %s 平仓失败仓位: %s", this.GetAliasOrID(), strings.Join(erroredSymbols, ","))
	}
}

func (this *FundingArbitrager) closePosition(position *exchange.Position, splits int) (successSplits, failedSplits int, er error) {
	symbolCode, err := this.GetSymbolCode(position.Symbol, true)
	if err != nil {
		er = fmt.Errorf("get symbol code left failed: %s", err)
		return
	}

	lock, _ := this.SymbolLocks.LoadOrStore(symbolCode.String(), &SymbolLock{
		SymbolCode: symbolCode.String(),
	})
	if !lock.ClosingPosition.TryLock() {
		er = fmt.Errorf("closing position try lock failed, symbol: %s", position.Symbol)
		return
	}
	defer lock.ClosingPosition.Unlock()

	side := exchange.OrderSideBuy
	if position.Qty > 0 {
		side = exchange.OrderSideSell
	} else if position.Qty == 0 {
		return
	}
	orderArgs := exchange.CreateOrderArgs{
		InstrumentType: position.InstrumentType,
		Symbol:         position.Symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCross,
		Qty:            math.Abs(position.Qty),
		TimeInForce:    exchange.GTC,
		Side:           side,
		ReduceOnly:     true,
	}
	return this.splitOrder(this.ExchangeRight, symbolCode, orderArgs, splits, false, func(successOrders []*exchange.Order) {
		for _, order := range successOrders {
			this.orderController.AppendOrderRecord(order, this.RightControllerID, this.ExchangeRight.GetName(), symbolCode)
		}
	})
}

func (this *FundingArbitrager) CloseAllPositions(splits int) {
	positions, err := this.ExchangeRight.GetPositions(exchange.USDXMarginedFutures, "", false)
	if err != nil {
		this.ErrorMsgf("close all positions failed, get positions failed: %s", err)
		return
	}
	symbols := []string{}
	for _, pos := range positions {
		symbols = append(symbols, pos.Symbol)
	}
	this.ClosePositions(symbols, splits)
}

func (this *FundingArbitrager) CheckLoanAndPay() {
	exchanges := []string{exchange.Bybit, exchange.Binance}
	ex := this.ExchangeRight
	if !slices.Contains(exchanges, ex.GetName()) {
		this.Warnf("no exchange available to check loan and pay")
		return
	}

	// 检查是否有 USDT 欠款，如果有，卖出 USDC-USDT 还款
	// 反之如果有浮盈，全部买入 USDC-USDT
	balance, err := ex.GetAccountBalances(exchange.USDXMarginedFutures)
	if err != nil {
		this.Errorf("检查 USDT 欠款失败: %s", err)
		return
	}
	usdtBalance := 0.0
	for _, b := range balance {
		if b.Currency == "USDT" {
			usdtBalance = b.Total
			break
		}
	}

	if usdtBalance == 0 {
		return
	}

	MinValue := 10.0
	// 太小可能不满足最小交易量
	if usdtBalance > 0 && usdtBalance < MinValue {
		return
	} else if usdtBalance < 0 && math.Abs(usdtBalance) < MinValue {
		usdtBalance = -MinValue
	}

	var symbol string
	if ex.GetName() == exchange.Binance {
		symbol = "USDCUSDT"

		// bn 还需要在现货、合约之间转账
		if usdtBalance > 0 {
			// 转 USDT 到现货买 USDC
			err := ex.TransferAsset(exchange.USDXMarginedFutures, exchange.Spot, "USDT", usdtBalance)
			if err != nil {
				this.ErrorMsgf("USDT 兑换 USDC 转账失败: %s", err)
				return
			}
		} else {
			// 转 USDC 到现货卖出
			price, err := ex.GetLastPrice(exchange.Spot, symbol, false)
			if err != nil {
				this.ErrorMsgf("获取 USDCUSDT 价格失败: %s", err)
				return
			}
			usdcAmount := math.Abs(usdtBalance) / price * 1.005 // 多转一点
			err = ex.TransferAsset(exchange.USDXMarginedFutures, exchange.Spot, "USDC", usdcAmount)
			if err != nil {
				this.ErrorMsgf("USDC 还款转账失败: %s", err)
				return
			}
		}
	} else if ex.GetName() == exchange.Bybit {
		symbol = "USDCUSDT"
	}

	orderArgs := exchange.CreateOrderArgs{
		InstrumentType: exchange.Spot,
		Symbol:         symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCross,
		QuoteQty:       math.Abs(usdtBalance - 0.1),
		TimeInForce:    exchange.GTC,
		Side:           exchange.OrderSideSell,
		ReduceOnly:     true,
	}

	if usdtBalance > 0 {
		orderArgs.Side = exchange.OrderSideBuy
		orderArgs.QuoteQty = usdtBalance * 0.999
	}

	orderArgs.QuoteQty = math.Floor(orderArgs.QuoteQty*100) / 100
	order, err := ex.CreateOrder(orderArgs)
	if err != nil {
		if usdtBalance > 0 {
			this.ErrorMsgf("USDT 兑换 USDC 订单失败: %s", err)
		} else {
			this.ErrorMsgf("USDT 还款订单失败: %s", err)
		}
	} else {
		if usdtBalance > 0 {
			this.SendMsgf("USDT 兑换 USDC 成功，兑换金额: %.2f", usdtBalance)
		} else {
			this.SendMsgf("USDT 还款成功，还款金额: %.2f", usdtBalance)
		}
	}

	time.Sleep(5 * time.Second)

	if ex.GetName() == exchange.Binance {
		exeOrder, err := ex.GetOrderByOrig(*order)
		if err != nil {
			this.ErrorMsgf("获取还款订单失败: %s", err)
			return
		}
		if exeOrder.Side == exchange.OrderSideSell {
			// 兑换的 USDT 转回合约账号还款
			_, available, err := ex.GetBalance(exchange.Spot, "USDT")
			if err != nil {
				this.ErrorMsgf("获取 USDT 余额失败: %s", err)
				return
			}
			err = ex.TransferAsset(exchange.Spot, exchange.USDXMarginedFutures, "USDT", math.Min(exeOrder.QuoteQty, available))
			if err != nil {
				this.ErrorMsgf("USDT 还款转账到合约失败: %s", err)
				return
			}
		} else {
			// 买入的 USDC 转回合约账号
			_, available, err := ex.GetBalance(exchange.USDXMarginedFutures, "USDC")
			if err != nil {
				this.ErrorMsgf("获取 USDC 余额失败: %s", err)
				return
			}
			err = ex.TransferAsset(exchange.Spot, exchange.USDXMarginedFutures, "USDC", math.Min(exeOrder.ExecQty, available))
			if err != nil {
				this.ErrorMsgf("USDC 转账到合约失败: %s", err)
				return
			}
		}
	}
}

func (this *FundingArbitrager) ReducePositions(ratio float64, specificSymbols ...string) (er error) {
	// 减仓时，需要锁住 reduceMutex，防止同时操作，每个品种本身也有锁，但是为了防止太频繁获取仓位，所以需要再锁一次
	if !this.mutexes.reduceMutex.TryLock() {
		return fmt.Errorf("reduce positions in progress")
	}
	defer func() {
		this.mutexes.reduceMutex.Unlock()
		this.SetMonitorRelaxAtTime(time.Now().Add(LoopInterval))
	}()
	this.Infof("reducing positions: %v", ratio)

	if ratio <= 0 || ratio > 1 {
		return fmt.Errorf("invalid reduce ratio: %v", ratio)
	}

	positions, err := this.ExchangeRight.GetPositions(exchange.USDXMarginedFutures, "", false)
	if err != nil {
		return fmt.Errorf("reduce positions failed, get positions failed: %s", err)
	}

	for _, position := range positions {
		if position.Qty == 0 {
			continue
		}

		if len(specificSymbols) > 0 && !slices.Contains(specificSymbols, position.Symbol) {
			continue
		}
		reducedQty, err := this.reducePosition(position, ratio)
		if err != nil {
			this.ErrorMsgf("reduce position failed, symbol: %s, reduceQty: %v, error: %s", position.Symbol, reducedQty, err)
			continue
		}
		this.SendMsgf("减仓订单提交成功: %s - %s, %v", position.InstrumentType, position.Symbol, reducedQty)
	}
	return
}

func (this *FundingArbitrager) reducePosition(position *exchange.Position, ratio float64) (reducedQty float64, er error) {
	symbolCode, err := this.GetSymbolCode(position.Symbol, true)
	if err != nil {
		er = fmt.Errorf("get symbol code failed, symbol: %s, error: %s", position.Symbol, err)
		return
	}
	symbolCodeStr := symbolCode.String()
	lock, _ := this.SymbolLocks.LoadOrStore(symbolCodeStr, &SymbolLock{
		SymbolCode: symbolCodeStr,
	})
	if !lock.ReducingPosition.TryLock() {
		er = fmt.Errorf("reducing position try lock failed, symbol: %s", position.Symbol)
		return
	}
	defer lock.ReducingPosition.Unlock()

	reducedQty = math.Abs(position.Qty * ratio)
	reducedQty = this.ExchangeRight.RoundQty(exchange.USDXMarginedFutures, position.Symbol, reducedQty)
	if reducedQty == 0 {
		return
	}
	side := exchange.OrderSideBuy
	if position.Qty > 0 {
		side = exchange.OrderSideSell
	}
	// Check if order value meets minimum requirements before creating order
	if !this.checkMinValue(position.Symbol, reducedQty, true) {
		this.Infof("skipping reduce position due to small order value: %s, qty: %v, value < %.2f", position.Symbol, reducedQty, MinPositionValue)
		return
	}

	orderArgs := exchange.CreateOrderArgs{
		InstrumentType: position.InstrumentType,
		Symbol:         position.Symbol,
		Type:           exchange.Market,
		TradeMode:      exchange.TradeModeCross,
		Qty:            reducedQty,
		TimeInForce:    exchange.GTC,
		Side:           side,
		ReduceOnly:     true,
	}
	order, err := this.ExchangeRight.CreateOrder(orderArgs)
	if err != nil {
		er = fmt.Errorf("create order failed: %s", err)
		return
	}

	this.orderController.AppendOrderRecord(order, this.RightControllerID, this.ExchangeRight.GetName(), symbolCode)
	return
}

func (this *FundingArbitrager) Rebalance() error {
	if this.Options.MarginMode == string(exchange.Portfolio) {
		this.Infof("margin mode is portfolio, skip rebalance")
		return nil
	}

	if !this.mutexes.rebalanceMutex.TryLock() {
		this.Warnf("rebalace in progress, wait for next time")
		return nil
	}
	defer func() {
		this.SetMonitorRelaxAtTime(time.Now().Add(LoopInterval))
		this.mutexes.rebalanceMutex.Unlock()
	}()

	// 计算仓位达到 TargetMarginRatio 所需的保证金，差值从现货转入
	worth, err := this.ExchangeRight.GetAccountWorth(this.ID, []exchange.InstrumentType{exchange.USDXMarginedFutures}, 0, nil)
	if err != nil {
		return fmt.Errorf("get account worth failed: %s", err)
	}

	positions, err := this.ExchangeRight.GetPositions(exchange.USDXMarginedFutures, "", false)
	if err != nil {
		return fmt.Errorf("get positions failed: %s", err)
	}

	positionValue, _, err := this.CalcuatePositionValue(this.ExchangeRight, positions)
	if err != nil {
		return fmt.Errorf("calculate position value failed: %s", err)
	}

	if positionValue == 0 {
		this.Warnf("position value is 0, skip rebalance")
		return nil
	}

	marginRatio := worth / positionValue
	if marginRatio >= this.Options.TargetMarginRatio {
		this.Infof("margin ratio is %f, no need to rebalance", marginRatio)
		return nil
	}

	needMargin := positionValue*this.Options.TargetMarginRatio - worth
	if needMargin <= 0 {
		this.Infof("need margin is %f, no need to rebalance", needMargin)
		return nil
	}

	// 如果现货可用余额不足，则全部转入
	_, available, err := this.ExchangeLeft.GetBalance(exchange.Spot, this.GetUSDXSymbol())
	if err != nil {
		return fmt.Errorf("get %s balance failed: %s", this.GetUSDXSymbol(), err)
	}

	if available <= 0 {
		this.Infof("available balance is %f, no need to rebalance", available)
		return nil
	}

	tranferAmount := math.Min(needMargin, available)
	tranferAmount, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", tranferAmount), 64)

	//  对于是同一个交易所，同时提醒两侧不能划转的问题
	if this.ExchangeLeft.GetID() != this.ExchangeRight.GetID() {
		if this.counters.manualRebalanceAlertLimiter.Allow() {
			this.SendMsgf("两侧交易所不是同一个交易所，无法自动 rebalance，请手工划转。")
		}
	}

	// 对于 hyperliquid ，如果没有配置 withdrawKey，减仓后无法自动 rebalance，需要手动 rebalance，需要发送消息提醒
	if this.ExchangeRight.GetName() == exchange.Hyperliquid && !this.ExchangeRight.(*hyperliquid.Hyperliquid).HasWithdrawAPI() {
		if this.counters.manualRebalanceAlertLimiter.Allow() {
			this.SendMsgf("左侧 Hyperliquid 未配置 withdrawKey，无法自动 rebalance，请手动划转 %.2f %s 到合约账户", tranferAmount, this.GetUSDXSymbol())
		}
		return nil
	}

	err = this.ExchangeLeft.TransferAsset(exchange.Spot, exchange.USDXMarginedFutures, this.GetUSDXSymbol(), tranferAmount)
	if err != nil {
		return fmt.Errorf("transfer asset failed: %s", err)
	}

	return nil
}

func (this *FundingArbitrager) GetLeverage(symbol string, isRight bool) (leverage float64, err error) {
	ex := this.ExchangeLeft
	if isRight {
		ex = this.ExchangeRight
	}

	enableMaxLeverage := false
	if ex.GetName() == exchange.Hyperliquid {
		enableMaxLeverage = true
	}
	if enableMaxLeverage {
		return ex.MaxLeverage(exchange.USDXMarginedFutures, symbol, 0), nil
	} else {
		maxLeverage := ex.MaxLeverage(exchange.USDXMarginedFutures, symbol, 0)
		code, err := this.GetSymbolCode(symbol, isRight)
		if err != nil {
			return 0, fmt.Errorf("get symbol code failed: %s", err)
		}

		paramLeverage := 1.0
		if isRight {
			paramLeverage = this.GetParam(ParamTypeLeverageRight, code.String()).(float64)
		}
		return math.Min(maxLeverage, paramLeverage), nil
	}
}

// 如果价差是有利的（开空的仓位价格低于开多的）并且超过配置，则自动减仓 2%
func (this *FundingArbitrager) AutoTakeProfit() {
	defer func() {
		if r := recover(); r != nil {
			this.AlertMsgf("recovered from panic in AutoTakeProfit: %v", r)
		}
	}()

	if this.Options.TakeProfitPriceDiff == 0 {
		this.Infof("take profit price diff is 0, skip auto take profit")
		return
	}
	if time.Now().Before(this.autoTakeProfitCoolingUntil) {
		this.Infof("auto take profit is locked until %s, skip this round", this.autoTakeProfitCoolingUntil.Format(time.RFC3339))
		return
	}
	this.AccountSnapshot.SymbolContextLeft.Range(func(symbolLeft string, symbolContextLeft *SymbolContext) bool {
		if symbolContextLeft == nil {
			return true
		}

		// Check if the symbol's coin is in validCoins
		code, err := this.GetSymbolCode(symbolLeft, false)
		if err != nil {
			this.ErrorMsgf("auto take profit, get symbol code failed: %s", err)
			return true
		}
		if !utils.SliceContainsEqualFold(this.Options.ValidCoins, code.Coin()) {
			return true // Skip symbols not in validCoins
		}

		symbolRight, err := this.ConvertSymbol(symbolLeft, true)
		if err != nil {
			this.ErrorMsgf("convert symbol failed: %s", err)
			return true
		}
		symbolContextRight, ok := this.AccountSnapshot.SymbolContextRight.Load(symbolRight)
		if !ok {
			this.ErrorMsgf("symbol context not found: %s", symbolRight)
			return true
		}
		if symbolContextRight == nil {
			return true
		}
		if symbolContextLeft.PositionValue == 0 || symbolContextRight.PositionValue == 0 {
			return true
		}

		// check if symbol context is up to date
		if !symbolContextLeft.IsUpToDate() || !symbolContextRight.IsUpToDate() {
			return true
		}

		var longPrice, shortPrice float64
		if symbolContextLeft.PositionValue > 0 {
			longPrice = symbolContextLeft.LastPrice
			shortPrice = symbolContextRight.LastPrice
		} else {
			longPrice = symbolContextRight.LastPrice
			shortPrice = symbolContextLeft.LastPrice
		}
		if longPrice == 0 || shortPrice == 0 {
			return true
		}

		codeRight, err := this.GetSymbolCode(symbolRight, true)
		if err != nil {
			this.ErrorMsgf("auto take profit, get symbol code right failed: %s", err)
			return true
		}

		if shortPrice < longPrice {
			priceDiff := math.Abs(shortPrice-longPrice) / longPrice
			takeProfitDiff := this.GetParam(ParamTypeTakeProfitPriceDiff, codeRight.String()).(float64)
			if takeProfitDiff <= 0 {
				this.Infof("take profit price diff is 0, skip auto take profit for %s", symbolLeft)
				return true
			}
			if priceDiff > takeProfitDiff {
				this.Infof("auto take profit: %s, %s, %v", symbolLeft, symbolRight, priceDiff)
				this.SendMsgf("auto take profit: symbol: %s - %s, price diff: %.2f%%", symbolLeft, symbolRight, priceDiff*100)
				reduceRatio := 0.02
				err = this.ReducePositions(reduceRatio, symbolRight)
				if err != nil {
					this.ErrorMsgf("auto take profit, reduce positions failed: %s", err)
				}
				this.autoTakeProfitCoolingUntil = time.Now().Add(10 * time.Second)
			}
		}

		return true
	})
}

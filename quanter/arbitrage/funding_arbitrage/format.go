package funding_arbitrage

import (
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

var StatusHeader = []string{"ID", "Alias", "Left", "Position", "Margin", "MarginRatio", "  ", "Right", "Position", "Margin", "MarginRatio", "  ", "TotalMargin", "HedgePNL", "Status", "UpdateTime"}
var SnapshotHeader = []string{"Time", "ID", "Left", "Position", "Margin", "MarginRatio", "  ", "Right", "Position", "Margin", "MarginRatio", "  ", "Total Position", "Total Margin", "HedgePNL"}
var SymbolContextHeader = []string{"Time", "ID", "Left", "Symbol", "LastPrice", "M<PERSON> Price", "I. Price", "FundingRate", "OI", "  ", "Right", "Symbol", "LastPrice", "M. Price", "I. Price", "FundingRate", "OI", "  ", "PriceDelta"}

func formatAmount(amount float64, coin string) string {
	if amount == 0 {
		return "-"
	}
	decimals := 4
	if strings.HasPrefix(coin, "USD") {
		decimals = 2
	} else if strings.EqualFold(coin, "CNY") {
		decimals = 0
	}
	return decimal.NewFromFloat(amount).Round(int32(decimals)).String() + " " + coin
}

func (this *FundingArbitrager) PreviewCreation() string {
	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Alias", "Controller", "Options"})
	t.AddRow([]string{this.ID, this.Alias, this.RightControllerID, this.Options.String()})
	return t.Render()
}

func (this *FundingArbitrager) RenderStatus() string {
	if this.AccountSnapshot == nil || this.AccountSnapshot.IsEmpty() {
		return "[No Account Snapshot, or current snapshot is empty]"
	}
	t := exchange.NewTable()
	t.SetHeader(StatusHeader)
	t.AddRow(this.RenderStatusRow())
	return t.Render()
}

func (this *FundingArbitrager) RenderInfo() string {
	t := exchange.NewTable()
	t.SetHeader([]string{"ID", "Alias", "Controller", "Exchange"})
	t.AddRow([]string{this.ID, this.Alias, this.ControllerID, this.ExchangeLeft.GetName()})
	return t.Render()
}

func (this *FundingArbitrager) RenderStatusRow() []string {
	row := []string{this.ID, this.Alias}

	row = append(row, this.GetAccountSnapshotRow(this.AccountSnapshot, false)...)
	row = append(row, "  ")
	row = append(row, this.GetAccountSnapshotRow(this.AccountSnapshot, true)...)
	row = append(row, "  ")

	totalMargin := fmt.Sprintf("%.2f", this.AccountSnapshot.MarginValueTotal)
	totalMarginRatio := fmt.Sprintf("%.2f%%", this.AccountSnapshot.MarginRatioTotal*100)
	row = append(row, fmt.Sprintf("%s / %s", totalMargin, totalMarginRatio))
	row = append(row, fmt.Sprintf("%.2f", this.AccountSnapshot.GetHedgePNL("")))
	row = append(row, string(this.Status))
	row = append(row, utils.FormatShortTimeStr(&this.AccountSnapshot.CreateTime, true))
	return row
}

func (this *FundingArbitrager) RenderReport() string {
	status := this.RenderStatus()
	positions := this.RenderPositions(this.PositionSnapshot)
	riskEvents := this.RenderRiskEvents(20)
	monitors := this.RenderMonitors(20)
	return fmt.Sprintf("%s\n\n%s\n\n%s\n\n%s", status, positions, riskEvents, monitors)
}

func (this *FundingArbitrager) RenderPositions(positionSnapshot *PositionSnapshot) string {
	controllerLeft := this.orderController
	controllerRight := this.orderController
	// 获取所有持仓的 symbolCode, 去重，排序，保持仓位的顺序
	allSymbolCodes := []*exchange.SymbolCode{}
	for _, pos := range positionSnapshot.PositionsRight {
		_, symbolCode, err := this.ExchangeRight.TranslateFutureSymbol(pos.InstrumentType, pos.Symbol, controllerRight.GetBaseConfig().USDXSymbol)
		if err != nil {
			continue
		}
		allSymbolCodes = append(allSymbolCodes, symbolCode)
	}
	uniqueSymbolCodes := []string{} // SymbolCode.Code 的字符串形式，方便排序
	for _, symbolCode := range allSymbolCodes {
		found := false
		for _, s := range uniqueSymbolCodes {
			if s == symbolCode.Code {
				found = true
				break
			}
		}
		if !found {
			uniqueSymbolCodes = append(uniqueSymbolCodes, symbolCode.Code)
		}
	}
	sortedSymbolCodes := sort.StringSlice(uniqueSymbolCodes)

	// 渲染表格
	t := exchange.NewTable()
	t.SetHeader([]string{"SymbolCode", "Left", "Symbol", "Qty", "Value", "Last Price", "TP/SL", "APR", "    ", "Right", "Symbol", "Qty", "Value", "Last Price", "TP/SL", "APR", "    ", "PriceDelta", "HedgePNL", "ProfitAPR"})
	for _, code := range sortedSymbolCodes {
		row := []string{}
		symbolCode, err := exchange.NewSymbolCode(code, controllerLeft.GetBaseConfig().USDXSymbol)
		if err != nil {
			continue
		}
		leftSymbol, err := this.ExchangeLeft.TranslateSymbolCodeToSpotSymbol(symbolCode)
		if err != nil {
			continue
		}
		rightSymbol, err := this.ExchangeRight.TranslateSymbolCodeToFutureSymbol(symbolCode)
		if err != nil {
			continue
		}
		leftPosition := &exchange.Position{}
		rightPosition := &exchange.Position{}
		// TODO: check why value in positionLef/positionRight can be nil
		// found one bug, not tested yet
		for _, pos := range this.PositionSnapshot.PositionsLeft {
			if pos.Symbol == leftSymbol {
				leftPosition = pos
			}
		}
		for _, pos := range this.PositionSnapshot.PositionsRight {
			if pos.Symbol == rightSymbol {
				rightPosition = pos
			}
		}

		row = append(row, code)
		leftRow := this.GetPositionRow(leftPosition, false)
		row = append(row, leftRow...)

		fundingRateLeft := 0.0
		row = append(row, fmt.Sprintf("%.2f%%", fundingRateLeft*100))

		row = append(row, "    ")

		rightRow := this.GetPositionRow(rightPosition, true)
		row = append(row, rightRow...)
		instrumentRight, err := this.ExchangeRight.GetInstrument(rightPosition.InstrumentType, rightSymbol)
		fundingRateRight := instrumentRight.GetFundingRateAPR(rightPosition.Qty > 0)
		if err != nil {
			this.Errorf("get right instrument funding rate APR failed: %s", err)
			row = append(row, "-")
		} else {
			row = append(row, fmt.Sprintf("%.2f%%", fundingRateRight*100))
		}

		priceDeltaStr := "-"
		priceDelta := leftPosition.GetLastPrice() - rightPosition.GetLastPrice()
		if leftPosition.Qty > 0 {
			priceDelta = rightPosition.GetLastPrice() - leftPosition.GetLastPrice()
		}
		row = append(row, "    ")
		priceDeltaStr = fmt.Sprintf("%.2f%%", priceDelta/leftPosition.GetLastPrice()*100)
		row = append(row, priceDeltaStr)
		row = append(row, fmt.Sprintf("%.2f", this.AccountSnapshot.GetHedgePNL(code)))
		annual := fmt.Sprintf("%.2f%%", (fundingRateLeft+fundingRateRight)*100)
		row = append(row, annual)
		t.AddRow(row)
	}
	if len(t.Rows) > 1 {
		return t.Render()
	}
	return "[no positions]"
}

func (this *FundingArbitrager) GetPositionRow(position *exchange.Position, isRight bool) (row []string) {
	// "Exchange", "Symbol", "Qty", "Value", "Last Price", "TP/SL"
	row = []string{}
	if position == nil {
		row = []string{"", "", "", "", "", ""}
		return
	}
	exch := this.ExchangeLeft
	if isRight {
		exch = this.ExchangeRight
	}
	row = append(row, exch.GetName())
	row = append(row, position.Symbol)
	row = append(row, exch.FormatQty(position.InstrumentType, position.Symbol, position.Qty))
	valueStr := "-"
	value, err := exch.Qty2Size(position.InstrumentType, position.Symbol, position.Qty, position.GetLastPrice())
	if err == nil {
		valueStr = fmt.Sprintf("%.2f", value)
	}
	row = append(row, valueStr)
	row = append(row, exch.FormatPrice(position.InstrumentType, position.Symbol, position.GetLastPrice()))

	// 获取止盈止损价格
	tpOrders := this.TakeProfitOrdersLeft
	slOrders := this.StopLossOrdersLeft
	if isRight {
		tpOrders = this.TakeProfitOrdersRight
		slOrders = this.StopLossOrdersRight
	}
	tpOrder, tpOk := tpOrders.Load(position.Symbol)
	slOrder, slOk := slOrders.Load(position.Symbol)
	tpPrice := "-"
	slPrice := "-"
	if tpOk {
		tpPrice = exch.FormatPrice(position.InstrumentType, position.Symbol, tpOrder.TriggerPrice)
	}
	if slOk {
		slPrice = exch.FormatPrice(position.InstrumentType, position.Symbol, slOrder.TriggerPrice)
	}
	row = append(row, fmt.Sprintf("%s / %s", tpPrice, slPrice))
	return row
}

func (this *FundingArbitrager) RenderRiskEvents(limit int) string {
	t := exchange.NewTable()
	t.SetHeader([]string{"Time", "ID", "RiskCategory", "Comment"})
	if limit == 0 {
		limit = len(this.RiskEvents)
	}
	if limit > len(this.RiskEvents) {
		limit = len(this.RiskEvents)
	}
	// backwards
	for i := len(this.RiskEvents) - 1; i >= 0 && i >= len(this.RiskEvents)-limit; i-- {
		event := this.RiskEvents[i]
		row := []string{}
		row = append(row, utils.FormatToBeijingTimeStr(event.CreateTime))
		row = append(row, event.ID)
		row = append(row, string(event.Category))
		row = append(row, event.Comment)
		t.AddRow(row)
	}
	if len(t.Rows) > 1 {
		return t.Render()
	}
	return "[no risk events]"
}

func (this *FundingArbitrager) RenderMonitors(limit int) string {
	t := exchange.NewTable()
	t.SetHeader([]string{"Time", "ID", "Left", "Position", "Margin", "MarginRatio", "  ", "Right", "Position", "Margin", "MarginRatio", "  ", "Total Position", "TotalMargin", "Hedge PNL"})
	if limit == 0 {
		limit = len(this.Monitors)
	}
	if limit > len(this.Monitors) {
		limit = len(this.Monitors)
	}
	// backwards
	if len(this.Monitors) > 0 {
		// show only the latest N  monitors
		for i := len(this.Monitors) - 1; i >= 0 && i >= len(this.Monitors)-limit; i-- {
			monitor := this.Monitors[i]
			row := []string{}
			row = append(row, utils.FormatShortTimeStr(&monitor.CreateTime, true))
			row = append(row, monitor.ID)
			row = append(row, this.GetAccountSnapshotRow(monitor, false)...)
			row = append(row, "  ")
			row = append(row, this.GetAccountSnapshotRow(monitor, true)...)
			row = append(row, "  ")
			row = append(row, fmt.Sprintf("%.2f", monitor.PositionValueTotal))
			row = append(row, fmt.Sprintf("%.2f / %.2f%%", monitor.MarginValueTotal, monitor.MarginRatioTotal*100))
			row = append(row, fmt.Sprintf("%.2f", monitor.GetHedgePNL("")))
			t.AddRow(row)
		}
	}
	return t.Render()
}

func (this *FundingArbitrager) RenderPrices(snapshots []*AccountSnapshot, symbolCodeStr string, limit int) string {
	t := exchange.NewTable()
	t.SetHeader(SymbolContextHeader)
	if limit == 0 {
		limit = len(snapshots)
	}
	if limit > len(snapshots) {
		limit = len(snapshots)
	}

	// backwards
	if len(snapshots) > 0 {
		// show only the latest N  monitors
		for i := 0; i < len(snapshots) && i < limit; i++ {
			snapshot := snapshots[i]
			row := []string{}
			row = append(row, utils.FormatShortTimeStr(&snapshot.CreateTime, true))
			row = append(row, snapshot.ID)
			row = append(row, this.GetSymbolContextRow(snapshot, symbolCodeStr, false)...)
			row = append(row, "  ")
			row = append(row, this.GetSymbolContextRow(snapshot, symbolCodeStr, true)...)
			row = append(row, "  ")
			priceDeltaRatio, err := this.GetPriceDeltaRatioFromSnapshot(snapshot, PriceTypeLastPrice, symbolCodeStr)
			if err != nil {
				row = append(row, "-")
			} else {
				row = append(row, fmt.Sprintf("%.2f%%", priceDeltaRatio*100))
			}
			t.AddRow(row)
		}
	}
	return t.Render()
}

func (this *FundingArbitrager) GetPriceDeltaRatioFromSnapshot(snapshot *AccountSnapshot, priceType PriceType, symbolCodeStr string) (priceDeltaRatio float64, er error) {
	symbolLeft, symbolRight, err := this.TranslateBothSideSymbols(symbolCodeStr)
	if err != nil {
		er = fmt.Errorf("translate both side symbols failed, error: %s", err)
		return
	}
	priceDeltaRatio = 0.0
	priceLeft := 0.0
	priceRight := 0.0
	snapshot.SymbolContextLeft.Range(func(symbol string, ctx *SymbolContext) bool {
		if symbol == symbolLeft {
			switch priceType {
			case PriceTypeLastPrice:
				priceLeft = ctx.LastPrice
			case PriceTypeMarkPrice:
				priceLeft = ctx.MarkPrice
			case PriceTypeIndexPrice:
				priceLeft = ctx.IndexPrice
			}
			return false
		}
		return true
	})
	snapshot.SymbolContextRight.Range(func(symbol string, ctx *SymbolContext) bool {
		if symbol == symbolRight {
			switch priceType {
			case PriceTypeLastPrice:
				priceRight = ctx.LastPrice
			case PriceTypeMarkPrice:
				priceRight = ctx.MarkPrice
			case PriceTypeIndexPrice:
				priceRight = ctx.IndexPrice
			}
			return false
		}
		return true
	})
	if priceLeft == 0 || priceRight == 0 {
		er = fmt.Errorf("price left or right is 0")
		return
	}
	priceDeltaRatio = (priceRight - priceLeft) / priceLeft
	return
}

func (this *FundingArbitrager) GetSymbolContextRow(monitor *AccountSnapshot, symbolCodeStr string, isRight bool) (row []string) {
	row = []string{}
	usdxSymbol := this.GetUSDXSymbol()
	exch := this.ExchangeLeft
	if isRight {
		exch = this.ExchangeRight
	}
	symbolCode, err := exchange.NewSymbolCode(symbolCodeStr, usdxSymbol)
	if err != nil {
		return
	}

	symbolContext := monitor.SymbolContextLeft
	if isRight {
		symbolContext = monitor.SymbolContextRight
	}
	symbolContext.Range(func(symbol string, ctx *SymbolContext) bool {
		targetSymbol, err := exch.TranslateSymbolCodeToSpotSymbol(symbolCode)
		if err != nil {
			return true
		}
		if isRight {
			targetSymbol, err = exch.TranslateSymbolCodeToFutureSymbol(symbolCode)
			if err != nil {
				return true
			}
		}
		if symbol == targetSymbol {
			row = append(row, exch.GetName())
			row = append(row, symbol)
			row = append(row, exch.FormatPrice(exchange.USDXMarginedFutures, symbol, ctx.LastPrice))
			row = append(row, exch.FormatPrice(exchange.USDXMarginedFutures, symbol, ctx.MarkPrice))
			row = append(row, exch.FormatPrice(exchange.USDXMarginedFutures, symbol, ctx.IndexPrice))
			row = append(row, fmt.Sprintf("%.4f%%", ctx.FundingRate*100))
			row = append(row, fmt.Sprintf("%.2f", ctx.OpenInterest))
		}
		return true
	})
	if len(row) == 0 {
		row = []string{"", "", "", "", "", "", ""}
	}
	return row
}

func (this *FundingArbitrager) FilterDailySnapshots(snapshots []*AccountSnapshot) []*AccountSnapshot {
	// sort by create time, from new to old
	sort.Slice(snapshots, func(i, j int) bool {
		return snapshots[i].CreateTime.After(snapshots[j].CreateTime)
	})
	lastDay := ""
	keptSnapshots := []*AccountSnapshot{}
	for _, snapshot := range snapshots {
		// 如果创建时间是新的一天，则记录
		// timeStr: 2025-01-15T15:04:05Z08:00
		timeStr := utils.FormatToBeijingTimeStr(snapshot.CreateTime)
		currentDay := timeStr[:10] // 2025-01-15，只取日期
		if lastDay == "" || currentDay != lastDay {
			keptSnapshots = append(keptSnapshots, snapshot)
			lastDay = currentDay
		}
	}
	return keptSnapshots
}

func (this *FundingArbitrager) RenderAccountSnapshots(snapshots []*AccountSnapshot) string {
	sort.Slice(snapshots, func(i, j int) bool {
		return snapshots[i].CreateTime.After(snapshots[j].CreateTime)
	})

	t := exchange.NewTable()
	// "Time", "ID", "Left", "Position", "Margin", "MarginRatio", "  ", "Right", "Position", "Margin", "MarginRatio", "  ", "Total Position", "Total Margin", "HedgePNL"
	t.SetHeader(SnapshotHeader)
	for _, snapshot := range snapshots {
		row := []string{}
		row = append(row, utils.FormatShortTimeStr(&snapshot.CreateTime, true))
		row = append(row, snapshot.ID)
		row = append(row, this.GetAccountSnapshotRow(snapshot, false)...)
		row = append(row, "  ")
		row = append(row, this.GetAccountSnapshotRow(snapshot, true)...)
		row = append(row, "  ")
		row = append(row, fmt.Sprintf("%.2f", snapshot.PositionValueTotal))
		row = append(row, fmt.Sprintf("%.2f / %.2f%%", snapshot.MarginValueTotal, snapshot.MarginRatioTotal*100))
		row = append(row, fmt.Sprintf("%.2f", snapshot.GetHedgePNL("")))
		t.AddRow(row)
	}

	if len(t.Rows) > 1 {
		return t.Render()
	}
	return "[no snapshots]"
}

func (this *FundingArbitrager) GetAccountSnapshotRow(accountSnapshot *AccountSnapshot, isRight bool) []string {
	// "Exchange", "Position", "Margin", "MarginRatio"
	row := []string{}
	ex := this.ExchangeLeft
	if isRight {
		ex = this.ExchangeRight
	}
	positionValue := accountSnapshot.PositionValueLeft
	marginValue := accountSnapshot.MarginValueLeft
	marginRatio := accountSnapshot.MarginRatioLeft
	marginAvailable := accountSnapshot.MarginAvailableLeft
	if isRight {
		positionValue = accountSnapshot.PositionValueRight
		marginValue = accountSnapshot.MarginValueRight
		marginRatio = accountSnapshot.MarginRatioRight
		marginAvailable = accountSnapshot.MarginAvailableRight
	}
	if ex != nil {
		row = append(row, ex.GetName())
	} else {
		row = append(row, "-")
	}
	row = append(row, fmt.Sprintf("%.2f USD", positionValue))

	occupiedMarginRatio := (marginValue - marginAvailable) / positionValue
	if marginAvailable > 0 {
		row = append(row, fmt.Sprintf("%.2f (%.2f)", marginValue, marginAvailable))
	} else {
		row = append(row, fmt.Sprintf("%.2f (0)", marginValue))
	}
	occupiedMarginRatioStr := "-"
	if !math.IsNaN(occupiedMarginRatio) && !math.IsInf(occupiedMarginRatio, 0) {
		occupiedMarginRatioStr = fmt.Sprintf("%.2f%%", occupiedMarginRatio*100)
	}
	row = append(row, fmt.Sprintf("%.2f%% (%s)", marginRatio*100, occupiedMarginRatioStr))
	return row
}

func (this *FundingArbitrager) RenderAccountSnapshotWithContext(snapshot *AccountSnapshot) string {
	t := exchange.NewTable()
	// "Time", "ID", "Left", "Position", "Margin", "MarginRatio", "  ", "Right", "Position", "Margin", "MarginRatio", "  ", "Total Position", "Total Margin", "HedgePNL"
	t.SetHeader(SnapshotHeader)
	row := []string{}
	row = append(row, utils.FormatShortTimeStr(&snapshot.CreateTime, true))
	row = append(row, snapshot.ID)
	row = append(row, this.GetAccountSnapshotRow(snapshot, false)...)
	row = append(row, "  ")
	row = append(row, this.GetAccountSnapshotRow(snapshot, true)...)
	row = append(row, "  ")
	row = append(row, fmt.Sprintf("%.2f", snapshot.PositionValueTotal))
	row = append(row, fmt.Sprintf("%.2f / %.2f%%", snapshot.MarginValueTotal, snapshot.MarginRatioTotal*100))
	row = append(row, fmt.Sprintf("%.2f", snapshot.GetHedgePNL("")))
	t.AddRow(row)

	t2 := this.RenderSymbolContext(snapshot)

	return "Account Snapshot\n" + t.Render() + "\n\nSymbol Context\n" + t2
}

func (this *FundingArbitrager) RenderSymbolContext(snapshot *AccountSnapshot) string {
	t := exchange.NewTable()
	t.SetHeader(SymbolContextHeader)

	allSymbolCodeStrs, _, _, _, _, _, err := this.GetSymbolCodesForAccountSnapshot(snapshot)
	if err != nil {
		return "[error: " + err.Error() + "]"
	}
	for _, symbolCodeStr := range allSymbolCodeStrs {
		row := []string{}
		row = append(row, utils.FormatShortTimeStr(&snapshot.CreateTime, true))
		row = append(row, snapshot.ID)
		row = append(row, this.GetSymbolContextRow(snapshot, symbolCodeStr, false)...)
		row = append(row, "  ")
		row = append(row, this.GetSymbolContextRow(snapshot, symbolCodeStr, true)...)
		row = append(row, "  ")
		priceDeltaRatio, err := this.GetPriceDeltaRatioFromSnapshot(snapshot, PriceTypeLastPrice, symbolCodeStr)
		if err != nil {
			row = append(row, "-")
		} else {
			row = append(row, fmt.Sprintf("%.2f%%", priceDeltaRatio*100))
		}
		t.AddRow(row)
	}
	if len(t.Rows) > 1 {
		return t.Render()
	}
	return "[no symbol context]"
}

func (this *FundingArbitrager) RenderFundings(symbolCodeStr string) string {
	t := exchange.NewTable()
	t.SetHeader([]string{"SymbolCode", "Left", "Symbol", "Curr. APR", "APR 1d", "APR 3d", "APR 7d", "APR 14d", "  ", "Right", "Symbol", "Curr. APR", "APR 1d", "APR 3d", "APR 7d", "APR 14d"})
	allSymbolCodeStrs, _, symbolCodesLeft, symbolCodesRight, symbolsLeft, symbolsRight, err := this.GetSymbolCodesForAccountSnapshot(this.AccountSnapshot)
	if err != nil {
		return "[error: " + err.Error() + "]"
	}
	if symbolCodeStr != "" {
		allSymbolCodeStrs, symbolCodesLeft, symbolCodesRight, symbolsLeft, symbolsRight, err = this.ParseSymbolCodes(symbolCodeStr)
		if err != nil {
			return "[error: " + err.Error() + "]"
		}
	}
	for i, symbolCodeStr := range allSymbolCodeStrs {
		row := []string{}
		row = append(row, symbolCodeStr)
		row = append(row, this.ExchangeLeft.GetName())
		row = append(row, symbolsLeft[i])
		instrumentLeft, err := this.ExchangeLeft.GetInstrument(exchange.Spot, symbolsLeft[i])
		if err != nil {
			row = append(row, "-")
		} else {
			// 因为资金费率历史是按做空方向的资金费率，当前资金费率也都按做空方向
			row = append(row, fmt.Sprintf("%.2f%%", instrumentLeft.GetFundingRateAPR(false)*100))
		}
		row = append(row, this.FormatFundingAPR(this.ExchangeLeft, symbolCodesLeft[i], 1))
		row = append(row, this.FormatFundingAPR(this.ExchangeLeft, symbolCodesLeft[i], 3))
		row = append(row, this.FormatFundingAPR(this.ExchangeLeft, symbolCodesLeft[i], 7))
		row = append(row, this.FormatFundingAPR(this.ExchangeLeft, symbolCodesLeft[i], 14))
		row = append(row, "  ")
		row = append(row, this.ExchangeRight.GetName())
		row = append(row, symbolsRight[i])
		instrumentRight, err := this.ExchangeRight.GetInstrument(exchange.USDXMarginedFutures, symbolsRight[i])
		if err != nil {
			row = append(row, "-")
		} else {
			row = append(row, fmt.Sprintf("%.2f%%", instrumentRight.GetFundingRateAPR(false)*100))
		}
		row = append(row, this.FormatFundingAPR(this.ExchangeRight, symbolCodesRight[i], 1))
		row = append(row, this.FormatFundingAPR(this.ExchangeRight, symbolCodesRight[i], 3))
		row = append(row, this.FormatFundingAPR(this.ExchangeRight, symbolCodesRight[i], 7))
		row = append(row, this.FormatFundingAPR(this.ExchangeRight, symbolCodesRight[i], 14))
		t.AddRow(row)
	}
	return t.Render()
}

func (this *FundingArbitrager) FormatFundingAPR(exch exchange.Exchange, symbolCode *exchange.SymbolCode, days int) string {
	apr, err := this.GetFundingAPR(exch, symbolCode, days)
	if err != nil {
		return "-"
	}
	return fmt.Sprintf("%.2f%%", apr*100)
}

func (this *FundingArbitrager) GetFundingAPR(exch exchange.Exchange, symbolCode *exchange.SymbolCode, days int) (apr float64, er error) {
	his, err := exch.GetFundingHistory(symbolCode, false, true)
	if err != nil {
		er = fmt.Errorf("get funding history for %s, error: %s", symbolCode.Code, err)
		return
	}
	daysBack := time.Now().AddDate(0, 0, -days)
	dayCount := 0
	for _, funding := range his {
		if funding.Time.Before(daysBack) {
			continue
		}
		dayCount++
		apr += funding.Rate
	}
	apr = apr / float64(dayCount) * 365
	return
}

func (this *FundingArbitrager) FormatParamsParsable() string {
	return this._formatParamsParsable(this.SymbolParams)
}

func (this *FundingArbitrager) FormatAllSymbolCodes() string {
	allSymbolCodes := []string{}
	this.AccountSnapshot.SymbolContextRight.Range(func(symbol string, _ *SymbolContext) bool {
		symbolCode, err := this.GetSymbolCode(symbol, true)
		if err != nil {
			return true
		}
		allSymbolCodes = append(allSymbolCodes, symbolCode.String())
		return true
	})
	allSymbolCodes = utils.Unique(allSymbolCodes)
	sort.Strings(allSymbolCodes)
	if len(allSymbolCodes) == 0 {
		return "[no symbol codes]"
	}
	return strings.Join(allSymbolCodes, ",")
}

func (this *FundingArbitrager) _formatParamsParsable(symbolParams *exchange.SyncMapOf[string, *option.Options]) string {
	symbolCodes := []string{}
	symbolParams.Range(func(symbolCode string, params *option.Options) bool {
		symbolCodes = append(symbolCodes, symbolCode)
		return true
	})
	symbolCodes = utils.Unique(symbolCodes)
	sort.Strings(symbolCodes)
	rows := []string{}
	for _, symbolCode := range symbolCodes {
		params, ok := symbolParams.Load(symbolCode)
		if ok {
			rows = append(rows, fmt.Sprintf("%s:%s", symbolCode, params.String()))
		}
	}
	return strings.Join(rows, "|")
}

func (this *FundingArbitrager) RenderParamsForSymbol(symbolCode string) string {
	params, ok := this.SymbolParams.Load(symbolCode)
	if !ok {
		return "[no risk params]"
	}
	symbolParams := exchange.NewSyncMapOf[string, *option.Options]()
	symbolParams.Store(symbolCode, params)
	return this._renderParams(symbolParams)
}

func (this *FundingArbitrager) RenderParams() string {
	return this._renderParams(this.SymbolParams)
}

func (this *FundingArbitrager) _renderParams(symbolParams *exchange.SyncMapOf[string, *option.Options]) string {
	t := exchange.NewTable()
	t.SetHeader([]string{"SymbolCode", "PriceDiff", "Stoploss", "MarkPriceDiff", "SplitValue", "Leverage", "Params"})
	symbolCodes := []string{}
	symbolParams.Range(func(symbolCode string, params *option.Options) bool {
		symbolCodes = append(symbolCodes, symbolCode)
		return true
	})
	symbolCodes = utils.Unique(symbolCodes)
	sort.Strings(symbolCodes)
	for _, symbolCode := range symbolCodes {
		row := []string{}
		row = append(row, symbolCode)
		row = append(row, fmt.Sprintf("%.2f%%", this._getParam(symbolParams, ParamTypePriceDiff, symbolCode).(float64)*100))
		row = append(row, fmt.Sprintf("%.2f%%", this._getParam(symbolParams, ParamTypeStoploss, symbolCode).(float64)*100))
		row = append(row, fmt.Sprintf("%.2f%%", this._getParam(symbolParams, ParamTypeMarkPriceDiff, symbolCode).(float64)*100))
		row = append(row, fmt.Sprintf("%.f", this._getParam(symbolParams, ParamTypeSplitValue, symbolCode).(float64)))
		leverageLeft := fmt.Sprintf("%dx", int(this._getParam(symbolParams, ParamTypeLeverageLeft, symbolCode).(float64)))
		leverageRight := fmt.Sprintf("%dx", int(this._getParam(symbolParams, ParamTypeLeverageRight, symbolCode).(float64)))
		row = append(row, fmt.Sprintf("%s - %s", leverageLeft, leverageRight))
		params, ok := symbolParams.Load(symbolCode)
		if ok {
			row = append(row, fmt.Sprintf("%s:%s", symbolCode, params.String()))
		} else {
			row = append(row, "-")
		}
		t.AddRow(row)
	}
	if len(t.Rows) > 1 {
		return t.Render()
	}
	return "[no risk ratios]"
}

func (this *FundingArbitrager) RenderRotate(ex exchange.Exchange, position *exchange.Position, ratio float64, splits int) string {
	t := exchange.NewTable()
	t.SetHeader([]string{"Symbol", "Qty", "EntryPrice", "LastPrice", "PnL", "RotationQty", "RotationPnL", "Splits"})
	lastPrice := position.GetLastPrice()
	entryValue, _ := ex.Qty2Size(position.InstrumentType, position.Symbol, position.EntryPrice, math.Abs(position.Qty))
	lastValue, _ := ex.Qty2Size(position.InstrumentType, position.Symbol, lastPrice, math.Abs(position.Qty))
	pnl := 0.0
	if position.Qty > 0 {
		pnl = lastValue - entryValue
	} else {
		pnl = entryValue - lastValue
	}
	if splits == -1 {
		splits = int(math.Ceil(lastValue * ratio / OrderSplitValue))
	}
	t.AddRow([]string{
		position.Symbol,
		fmt.Sprintf("%v", position.Qty),
		fmt.Sprintf("%v", ex.FormatPrice(position.InstrumentType, position.Symbol, position.EntryPrice)),
		fmt.Sprintf("%v", ex.FormatPrice(position.InstrumentType, position.Symbol, lastPrice)),
		fmt.Sprintf("%.2f", pnl),
		fmt.Sprintf("%v", ex.RoundQty(position.InstrumentType, position.Symbol, position.Qty*ratio)),
		fmt.Sprintf("%.2f", pnl*ratio),
		fmt.Sprintf("%d", splits),
	})
	return t.Render()
}

func (this *FundingArbitrager) RenderCreateOrderArgs(symbolCode *exchange.SymbolCode, orderArgs exchange.CreateOrderArgs, splits int) (result string) {
	t := exchange.NewTable()
	t.SetHeader([]string{"controller", "exchange", "Side", "SymbolCode", "Symbol", "Qty", "Value", "Price", "Splits"})
	row := []string{}
	row = append(row, this.RightControllerID)
	row = append(row, this.ExchangeRight.GetName())
	row = append(row, string(orderArgs.Side))
	row = append(row, symbolCode.String())
	row = append(row, orderArgs.Symbol)

	priceStr := "-"
	lastPrice, err := this.ExchangeRight.GetLastPrice(symbolCode.InstrumentType(), orderArgs.Symbol, false)
	if err != nil {
		this.Errorf("get last price failed: %s", err)
	} else {
		priceStr = fmt.Sprintf("market ~%s", this.ExchangeRight.FormatPrice(symbolCode.InstrumentType(), orderArgs.Symbol, lastPrice))
	}

	settleCurrency := ""
	if ins, _ := this.ExchangeLeft.GetInstrument(symbolCode.InstrumentType(), orderArgs.Symbol); err == nil {
		settleCurrency = ins.SettleCurrency
	}

	qtyStr := "-"
	valueStr := "-"
	if orderArgs.Qty > 0 {
		if symbolCode.IsSpot() {
			qtyStr = fmt.Sprintf("%v", orderArgs.Qty)
			valueStr = formatAmount(orderArgs.Qty*lastPrice, this.ExchangeRight.GetSymbolCodeQuote(symbolCode))
		} else {
			qtyStr = fmt.Sprintf("%v", orderArgs.Qty)
			if lastPrice > 0 {
				v, _ := this.ExchangeRight.Qty2Size(symbolCode.InstrumentType(), orderArgs.Symbol, lastPrice, orderArgs.Qty)
				if v > 0 {
					valueStr = formatAmount(v, settleCurrency)
				}
			}
		}
	} else if orderArgs.QuoteQty > 0 {
		valueStr = fmt.Sprintf("%v %s", orderArgs.QuoteQty, this.ExchangeRight.GetSymbolCodeQuote(symbolCode))
		price := lastPrice
		if price == 0 {
			price = lastPrice
		}
		estQty := orderArgs.QuoteQty / price
		if symbolCode.IsFuture() {
			var err error
			estQty, err = this.ExchangeRight.Size2Qty(symbolCode.InstrumentType(), orderArgs.Symbol, price, orderArgs.QuoteQty)
			if err != nil {
				this.Errorf("size to qty failed: %s", err)
			}
		}
		qtyStr = fmt.Sprintf("%v", this.ExchangeRight.FloorQty(symbolCode.InstrumentType(), orderArgs.Symbol, estQty))
	}

	row = append(row, qtyStr)
	row = append(row, valueStr)
	row = append(row, priceStr)
	row = append(row, fmt.Sprintf("%d  @ %ds", splits, this.Options.SplitInterval))
	t.AddRow(row)
	return t.Render()
}

func (this *FundingArbitrager) PrintSymbolPriceDiff(alert bool) {
	symbolPriceDiffs := this.GetPriceDiffs()
	if len(symbolPriceDiffs) == 0 {
		if !alert {
			this.AlertMsgf("```[无价差数据]```")
		}
		return
	}

	alertTable := exchange.NewTable()
	alertTable.SetHeader([]string{"Exchange", "SymbolCode", "Price Left", "Price Right", "Delta", "Target Delta", "Alert Time"})

	for _, symbolPriceDiff := range symbolPriceDiffs {
		code := symbolPriceDiff.SymbolCode
		// 防止重复报警，如果上次报警时间小于 2 分钟，则不报警
		// 放在这里，而不是放在 for 循环的最开始，是因为希望记录下价差的真实数据，方便研究价格波动规律
		if alert {
			lastAlertTime, ok := this.priceDiffAlertTime.Load(code)
			if ok && time.Since(lastAlertTime) < 2*time.Minute {
				continue
			}
		}
		row := []string{}
		row = append(row, this.ExchangeLeft.GetName())
		row = append(row, code)
		row = append(row, utils.FormatNum(symbolPriceDiff.PriceLeft, 4))
		row = append(row, utils.FormatNum(symbolPriceDiff.PriceRight, 4))
		row = append(row, utils.FormatPercentWithSign(symbolPriceDiff.RealRatio, 2, true))
		row = append(row, utils.FormatPercent(symbolPriceDiff.TargetRatio, 2))

		// 如果 alert 为 true，则只打印有价差超过目标价差的品种
		if alert {
			if math.Abs(symbolPriceDiff.RealRatio) > symbolPriceDiff.TargetRatio {
				row = append(row, utils.FormatShortTimeStr(&symbolPriceDiff.CreateTime, false))
				alertTable.AddRow(row)
			}
		} else {
			// 如果 alert 为 false，则打印所有品种
			alertText := ""
			if math.Abs(symbolPriceDiff.RealRatio) > symbolPriceDiff.TargetRatio {
				alertText = utils.FormatShortTimeStr(&symbolPriceDiff.CreateTime, false)
			}
			row = append(row, alertText)
			alertTable.AddRow(row)
		}
		if alert {
			this.priceDiffAlertTime.Store(code, time.Now())
		}
	}

	if len(alertTable.Rows) > 1 {
		this.AlertMsgf("fa 价差警告: \n```%s```", alertTable.Render())
	}
}

func (this *FundingArbitrager) GetPriceDiffs() (symbolPriceDiffs []*SymbolPriceDiff) {
	if this.priceDiffAlertTime == nil {
		this.priceDiffAlertTime = exchange.NewSyncMapOf[string, time.Time]()
	}

	_, _, _, symbolCodesRight, _, _, err := this.GetSymbolCodesForAccountSnapshot(this.AccountSnapshot)
	if err != nil {
		this.Errorf("get symbol codes for account snapshot failed, error: %s", err)
		return
	}

	// 检查品种价差，不用 snapshot 中的数据，因为可能没持仓
	timeWindow := ParamPriceDiffTimeWindow / 2
	priceDiffCountLimit := ParamPriceDiffCountLimit / 2
	for _, code := range symbolCodesRight {
		symbolLeft, symbolRight, err := this.TranslateBothSideSymbols(code.String())
		if err != nil {
			this.Errorf("get both side symbols failed, error: %s", err)
			continue
		}
		maLeft, countLeft, err := this.CalculateSymbolPriceMA(PriceTypeLastPrice, symbolLeft, timeWindow, false)
		if err != nil {
			if strings.Contains(err.Error(), "not enough data") {
				this.Debugf("not enough data to calculate symbol price ma, error: %s", err)
			} else {
				this.Errorf("calculate symbol price ma failed, error: %s", err)
			}
			continue
		}

		maRight, countRight, err := this.CalculateSymbolPriceMA(PriceTypeLastPrice, symbolRight, timeWindow, true)
		if err != nil {
			if strings.Contains(err.Error(), "not enough data") {
				this.Debugf("not enough data to calculate symbol price ma, error: %s", err)
			} else {
				this.Errorf("calculate symbol price ma failed, error: %s", err)
			}
			continue
		}
		if countLeft > priceDiffCountLimit && countRight > priceDiffCountLimit {
			// 计算价差
			realDiff := maRight - maLeft
			priceDiffRatio := this.GetParam(ParamTypeAlertPriceDiff, code.String()).(float64)
			realRatio := realDiff / maRight
			this.Debugf("check symbol price diff: %s, price diff ratio: %.4f, price left: %.2f, price right: %.2f, real ratio: %s, target ratio: %s", symbolRight, priceDiffRatio, maLeft, maRight, utils.FormatPercentWithSign(realRatio, 2, true), utils.FormatPercent(priceDiffRatio, 2))

			symbolPriceDiffs = append(symbolPriceDiffs, &SymbolPriceDiff{
				Exchange:    this.ExchangeLeft.GetName(),
				SymbolCode:  code.String(),
				PriceLeft:   maLeft,
				PriceRight:  maRight,
				RealRatio:   realRatio,
				TargetRatio: priceDiffRatio,
				CreateTime:  time.Now(),
			})
		}
	}
	return
}

func (this *FundingArbitrager) PreviewEdit(oldArbitrager *FundingArbitrager) string {
	if oldArbitrager == nil {
		return "[No old arbitrager]"
	}
	t := exchange.NewTable()
	// compare old and new arbitrager, show the difference
	t.SetHeader([]string{"Field", "From", "  ", "To"})
	t.AddRow([]string{"ID", oldArbitrager.ID, "->", oldArbitrager.ID})
	t.AddRow([]string{"Alias", oldArbitrager.Alias, "->", oldArbitrager.Alias})
	t.AddRow([]string{"ControllerLeft", oldArbitrager.ControllerID, "->", this.ControllerID})
	t.AddRow([]string{"ControllerRight", oldArbitrager.RightControllerID, "->", this.RightControllerID})
	return t.Render()
}

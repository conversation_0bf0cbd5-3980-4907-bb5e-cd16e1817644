package funding_arbitrage

import (
	"fmt"
	"math"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/hyperliquid"
	"github.com/wizhodl/quanter/utils"
)

func NewFundingArbitrager(orderController base.OrderControllable, controllerID string, rightControllerID string, optStr string) (*FundingArbitrager, error) {
	opts, err := NewFundingArbitragerOptions(optStr)
	if err != nil {
		return nil, err
	}

	arbitrager := &FundingArbitrager{
		ID:                 exchange.NewRandomID(),
		orderController:    orderController,
		ControllerID:       controllerID,
		RightControllerID:  rightControllerID,
		Options:            opts,
		MonitorRelaxAtTime: time.Now(),
		Status:             StatusRunning,
		StatusUpdateTime:   time.Now(),
		CreateTime:         time.Now(),
		counters:           NewCounters(),
		priceDiffAlertTime: exchange.NewSyncMapOf[string, time.Time](),
	}
	arbitrager.SetupExchanges()

	if arbitrager.ExchangeLeft.GetName() != arbitrager.ExchangeRight.GetName() {
		return nil, fmt.Errorf("must be same exchange")
	}

	arbitrager.UpdateOptions(&opts.Options)
	arbitrager.AccountSnapshot = NewAccountSnapshot()
	arbitrager.SymbolLocks = exchange.NewSyncMapOf[string, *SymbolLock]()
	arbitrager.SymbolParams = exchange.NewSyncMapOf[string, *option.Options]()
	return arbitrager, nil
}
func (this *FundingArbitrager) SetController(controller base.OrderControllable) {
	this.orderController = controller
}

func (this *FundingArbitrager) GetStrategyType() base.StrategyType {
	return base.StrategyTypeFundingArbitrager
}

func (this *FundingArbitrager) SetAlias(alias string) {
	this.Alias = alias
}

func (this *FundingArbitrager) GetAliasOrID() string {
	if this.Alias != "" {
		return this.Alias
	}
	return this.ID
}

func (this *FundingArbitrager) GetLastUseTime() time.Time {
	return this.LastUseTime
}

func (this *FundingArbitrager) Run() {
	if this.counters == nil {
		this.counters = NewCounters()
	}
	go func() {
		// 交易所初始化好后，设置全仓模式、订单回调
		for {
			if this.ExchangeLeft == nil {
				time.Sleep(2 * time.Second)
				continue
			}
			if this.ExchangeRight == nil {
				time.Sleep(2 * time.Second)
				continue
			}
			// 添加价格监控，给 priceDiffAlert 使用，通过 getTicker 拿价格
			leftWatches, rightWatches := this.GetPriceWatches(this.Options.ValidCoins)
			for _, watch := range leftWatches {
				this.ExchangeLeft.AddPriceWatch(watch) // 不会重复添加，不用处理已有 watch 的情况
			}
			for _, watch := range rightWatches {
				this.ExchangeRight.AddPriceWatch(watch) // 不会重复添加，不用处理已有 watch 的情况
			}
			// 交易所保证金模式都要设为全仓
			// 有的交易所(Bybit)是按账号设置，有的(HL)是按交易对设置
			err := this.ExchangeRight.SetAccountMarginMode(exchange.USDXMarginedFutures, exchange.AccountMarginModeCross)
			if err != nil && !strings.Contains(err.Error(), "not need to be adjusted") {
				this.ErrorMsgf("set left account margin mode failed: %s", err)
			}

			this.checkRateLimit(this.ExchangeLeft)
			this.checkRateLimit(this.ExchangeRight)

			break
		}
	}()

	ticker := time.NewTicker(LoopInterval)
	defer ticker.Stop()

	for range ticker.C {
		if this.DeleteTime != nil {
			this.Infof("funding arbitrager is deleted, stop run loop")
			break
		}

		if err := this.CheckExchanges(); err != nil {
			this.Errorf("check exchanges failed, error: %s", err)
			continue
		}
		finishedMaints := this.Maintenances.AutoFinish()
		if len(finishedMaints) > 0 {
			this.SendMsgf("自动结束维护: \n```%s```", finishedMaints.ToTable())
		}

		// 不管是否暂停，都会更新 accountSnapshot
		err := this.UpdateAccountSnapshot()
		if err != nil {
			this.ErrorMsgf("update account snapshot failed, error: %s", err)
			continue
		}

		if this.Status != StatusRunning {
			this.Infof("funding arbitrager is not running, status: %s, skip loop", this.Status)
			if this.Status != StatusStopped && this.counters.pauseAlertLimiter.Allow() {
				this.AlertMsgf("funding arbitrager is not running, checkRisk and sync positions paused")
			}
			this.PrintSymbolPriceDiff(true)
			continue
		}
		this.Infof("funding arbitrager is running, status: %s...", this.Status)

		// 使用 defer 捕获 panic，防止影响其他逻辑
		func() {
			defer func() {
				if r := recover(); r != nil {
					this.AlertMsgf("recovered from panic in Run: %v", r)
					// Log stack trace for debugging
					buf := make([]byte, 4096)
					n := runtime.Stack(buf, false)
					this.Errorf("stack trace:\n%s", buf[:n])
				}
			}()
			// 清理 snapshot 数据，防止文件过大
			if this.counters.truncateSnapshotLimiter.Allow() {
				this.TruncateSnapshots()
			}
			this.ControlRisk()
			this.SyncPositions()
			if this.Options.EnableStoploss {
				go this.handleStoplossOrders()
			} else {
				go this.cancelAllStoplossOrders()
			}
			go this.checkMarginRatioOption(false)
			go this.checkRateLimit(this.ExchangeLeft)
			go this.checkRateLimit(this.ExchangeRight)
			this.AutoTakeProfit()
			this.orderController.GetStorage().Save()
		}()
	}
}

func (this *FundingArbitrager) CheckExchanges() error {
	// 同交易所的情况，ControllerLeft == ControllerRight，ExchangeLeft == ExchangeRight
	// 对于同交易所控制器，较早的存储中可能没有 RightControllerID，需要自动补上
	if this.ExchangeLeft == nil || this.ExchangeRight == nil {
		return this.SetupExchanges()
	}
	return nil
}

func (this *FundingArbitrager) SetupExchanges() error {
	if this.RightControllerID == "" {
		this.RightControllerID = this.ControllerID
	}
	leftController := this.orderController.GetController(this.ControllerID)
	if leftController != nil {
		this.ControllerLeft = leftController
		this.ExchangeLeft = leftController.GetExchange()
		if this.ExchangeLeft == nil {
			return fmt.Errorf("left exchange not ready")
		}
	} else {
		return fmt.Errorf("left controller not found")
	}

	rightController := this.orderController.GetController(this.RightControllerID)
	if rightController != nil {
		this.ControllerRight = rightController
		this.ExchangeRight = rightController.GetExchange()
		if this.ExchangeRight == nil {
			return fmt.Errorf("right exchange not ready")
		}
	} else {
		return fmt.Errorf("right controller not found")
	}
	return nil
}

func (this *FundingArbitrager) ControlRisk() {
	if !this.mutexes.riskMutex.TryLock() {
		this.Infof("risk controlling is in progress, skip this time")
		return
	}
	defer func() {
		this.mutexes.riskMutex.Unlock()
		// 执行完风险控制后，重置模拟风险事件，并且重置 monitors 数据，防止再次触发
		if this.orderController.IsDebug() && this.simulatingRisk != NoRiskCategory {
			this.simulatingRisk = NoRiskCategory
			this.SetMonitorRelaxAtTime(time.Now())
		}
	}()

	this.Infof("controlling risk...")

	if time.Since(this.AccountSnapshot.CreateTime) > 5*LoopInterval {
		this.AlertMsgf("账户快照时间大于 %s ，系统可能存在异常", 5*LoopInterval)
		return
	}

	// 如果当前不是运行状态，则跳过风险控制
	// 暂时仅跳过风险检查，一样会执行同步仓位，更新止盈止损订单等操作
	if this.Status != StatusRunning {
		this.Infof("arbitrager is not running, skip controlling risk")
		return
	}

	// 如果当前正在模拟风险事件，执行模拟篡改数据的操作
	if this.orderController.IsDebug() && this.simulatingRisk != NoRiskCategory {
		err := this.SimulateRiskEvent(this.simulatingRisk)
		if err != nil {
			this.ErrorMsgf("simulate risk event failed: %s", err)
		}
	}

	// 检查期货持仓是否包含非有效币种
	symbols, ok := this.checkValidCoinsForPositions()
	if !ok {
		this.WarnMsgf("检查到非有效币种，请检查: %v。检查持仓和配置 %s 是否正确。", symbols, OptionValidCoins)
	}

	// 检查品种价差，如果价差过大，则报警提醒套利机会
	this.PrintSymbolPriceDiff(true)

	// 三种情况：
	// RiskCategoryMarginRatioLow
	// Simulate 的情况下，需要有持仓才可以触发，否则无法模拟
	if dangerous, riskCategory := this.CheckAccountRisk(); dangerous {
		// 减到 TargetMarginRatio 和 RebalanceMarginRatio 的中间值，没必要一次减到 TargetMarginRatio，下个循环会再次检查，如果还有问题，会再次减仓
		reduceRatio := 0.05
		this.WarnMsgf("margin ratio is dangerous, reducing positions, risk: %s, reduce ratio: %.2f%%", riskCategory, reduceRatio*100)
		err := this.ReducePositions(reduceRatio)
		if err != nil {
			this.ErrorMsgf("reduce positions failed: %s", err)
		}
		time.Sleep(LoopInterval) // 稍等减仓、同步完成
		err = this.Rebalance()
		if err != nil {
			this.ErrorMsgf("rebalance failed: %s", err)
		}
		this.SetMonitorRelaxAtTime(time.Now()) // 因为之前的数据还在 monitors 中，所以需要重置 relax time，防止重复触发
		return
	}

	// 检查单个合约的风险水平，如果有危险，则关闭仓位
	// 主要是检查价格波动风险，通过这个方式可以比检查找好的风险水平更快发现异常
	dangerousSymbols, risks := this.CheckSymbolRisk()
	if len(dangerousSymbols) > 0 {
		this.WarnMsgf("some symbols are dangerous, closing positions: %v", dangerousSymbols)
		for _, risk := range risks {
			this.WarnMsgf("risk: %s", risk)
		}
		this.ClosePositions(dangerousSymbols, 1)
		this.SetMonitorRelaxAtTime(time.Now()) // 因为之前的价格数据还在 monitors 中，所以需要重置 relax time，防止重复触发
		return
	}

	// 检查非 U 本位的合约，需要报警
	hasAlien, err := this.HasAlienPositions()
	if err != nil {
		this.ErrorMsgf("check alien positions failed, error: %s", err)
	}
	if hasAlien {
		this.AlertMsgf("发现非 U 本位合约，请检查")
	}

}

func (this *FundingArbitrager) checkValidCoinsForPositions() (symbols []string, ok bool) {
	symbols = []string{}
	ok = true
	for _, position := range this.PositionSnapshot.PositionsRight {
		code, err := this.GetSymbolCode(position.Symbol, true)
		if err != nil {
			this.Errorf("check valid coins for positions, get symbol code failed: %s", err)
			continue
		}
		if !utils.SliceContainsEqualFold(this.Options.ValidCoins, code.Coin()) {
			symbols = append(symbols, position.Symbol)
			ok = false
		}
	}
	return
}

func (this *FundingArbitrager) CheckSymbolRisk() (dangerousSymbols []string, risks []RiskCategory) {
	if this.Status != StatusRunning {
		this.Infof("not running, checking symbol risk... (skipped)")
		return
	}
	this.Infof("checking symbol risk...")
	priceDiffCountLimit := ParamPriceDiffCountLimit

	dangerousSymbols = []string{}
	diffRiskSymbols := []string{}        // 价差风险的品种
	markRiskSymbols := []string{}        // 标记价格和指数价格价差风险的品种
	stoplossExpiredSymbols := []string{} // 止损止盈订单已经失效的品种，不返回到结果中，不要求平仓

	defer func() {
		dangerousSymbols = utils.Unique(dangerousSymbols)
		diffRiskSymbols = utils.Unique(diffRiskSymbols)
		markRiskSymbols = utils.Unique(markRiskSymbols)
		stoplossExpiredSymbols = utils.Unique(stoplossExpiredSymbols)
		if len(diffRiskSymbols) > 0 {
			this.AddRiskEvent(RiskCategoryPriceDiff, fmt.Sprintf("symbols: %v", diffRiskSymbols))
		}
		if len(markRiskSymbols) > 0 {
			this.AddRiskEvent(RiskCategoryMarkPriceDiff, fmt.Sprintf("symbols: %v", markRiskSymbols))
		}
		// 不将 stoplossExpiredSymbols 作为风险事件，因为万一发生该事件，可能是因为 API 频率限制引起，无法处理；强行平仓会导致损失很大
		// 仅仅是提醒用户，止损止盈订单可能失效
		if len(stoplossExpiredSymbols) > 0 {
			this.ErrorMsgf("stoploss expired symbols: %v", stoplossExpiredSymbols)
		}
	}()

	_, _, _, symbolCodesRight, _, _, err := this.GetSymbolCodesForAccountSnapshot(this.AccountSnapshot)
	if err != nil {
		this.Errorf("get symbol codes for account snapshot failed, error: %s", err)
		return
	}

	// 检查品种价差风险，不用单个价格值，而是用 MA 值来判断，避免价格波动导致误报
	timeWindow := ParamPriceDiffTimeWindow
	for _, code := range symbolCodesRight {
		symbolLeft, symbolRight, err := this.TranslateBothSideSymbols(code.String())
		if err != nil {
			this.Errorf("get both side symbols failed, error: %s", err)
			continue
		}
		maLeft, countLeft, err := this.CalculateSymbolPriceMA(PriceTypeLastPrice, symbolLeft, timeWindow, false)
		if err != nil {
			if strings.Contains(err.Error(), "not enough data") {
				this.Debugf("not enough data to calculate symbol price ma, error: %s", err)
			} else {
				this.Errorf("calculate symbol price ma failed, error: %s", err)
			}
			continue
		}

		maRight, countRight, err := this.CalculateSymbolPriceMA(PriceTypeLastPrice, symbolRight, timeWindow, true)
		if err != nil {
			if strings.Contains(err.Error(), "not enough data") {
				this.Debugf("not enough data to calculate symbol price ma, error: %s", err)
			} else {
				this.Errorf("calculate symbol price ma failed, error: %s", err)
			}
			continue
		}
		if countLeft > priceDiffCountLimit && countRight > priceDiffCountLimit {
			// TODO: 高费率品种价差通常能达到 0.2%，priceDiffDelta 可能得根据情况调大一些
			realDiff := math.Abs(maRight - maLeft)
			priceDiffRatio := this.GetParam(ParamTypePriceDiff, code.String()).(float64)
			targetDiff := maRight * priceDiffRatio
			this.Debugf("check symbol price diff: %s, price diff ratio: %.4f, ma left: %.2f, ma right: %.2f, target diff: %.4f, real diff: %.4f", symbolRight, priceDiffRatio, maLeft, maRight, targetDiff, realDiff)
			if realDiff > targetDiff {
				dangerousSymbols = append(dangerousSymbols, symbolRight)
				diffRiskSymbols = append(diffRiskSymbols, symbolRight)
				this.Warnf("symbol price diff: %s, ma left: %.2f, ma right: %.2f, real diff: %.2f, target diff: %.2f", symbolRight, maLeft, maRight, realDiff, targetDiff)
			}
		}
	}
	if len(diffRiskSymbols) > 0 {
		risks = append(risks, RiskCategoryPriceDiff)
	}

	// 检查标记价格和指数价格的价差风险，标记价格和指数价格差别过大，可能导致额外爆仓
	timeWindow = ParamMarkPriceDiffTimeWindow
	countLimit := ParamMarkPriceDiffCountLimit
	for _, code := range symbolCodesRight {
		_, symbolRight, err := this.TranslateBothSideSymbols(code.String())
		if err != nil {
			this.Errorf("get both side symbols failed, error: %s", err)
			continue
		}
		// Left 因为是现货，所以不检查
		// 检查 Right 上的标记价格和指数价格的价差风险
		markRight, markCountRight, err := this.CalculateSymbolPriceMA(PriceTypeMarkPrice, symbolRight, timeWindow, true)
		if err != nil {
			this.Errorf("calculate symbol price ma failed, error: %s", err)
			continue
		}
		indexRight, indexCountRight, err := this.CalculateSymbolPriceMA(PriceTypeIndexPrice, symbolRight, timeWindow, true)
		if err != nil {
			this.Errorf("calculate symbol price ma failed, error: %s", err)
			continue
		}
		if markCountRight > countLimit && indexCountRight > countLimit {
			realDiff := math.Abs(markRight - indexRight)
			priceDiffRatio := this.GetParam(ParamTypeMarkPriceDiff, code.String()).(float64)
			targetDiff := indexRight * priceDiffRatio
			if realDiff > targetDiff {
				dangerousSymbols = append(dangerousSymbols, symbolRight)
				markRiskSymbols = append(markRiskSymbols, symbolRight)
				this.Warnf("exchange right, symbol mark price diff: %s, mark right: %.2f, index right: %.2f, real diff: %.2f, target diff: %.2f", symbolRight, markRight, indexRight, realDiff, targetDiff)
			}
		}
	}
	if len(markRiskSymbols) > 0 {
		risks = append(risks, RiskCategoryMarkPriceDiff)
	}

	// 检查止损止盈订单的创建时间是否已经失效
	// 如果止损止盈订单在启动后的 StoplossMAWindow 内没有生效，也认为是有风险的
	// 不将 stoplossExpiredSymbols 作为风险事件，因为万一发生该事件，可能是因为 API 频率限制引起，无法处理；强行平仓会导致损失很大
	for _, code := range symbolCodesRight {
		_, symbolRight, err := this.TranslateBothSideSymbols(code.String())
		if err != nil {
			this.Errorf("get both side symbols failed, error: %s", err)
			continue
		}
		if this.StopLossOrdersRight == nil {
			continue
		}
		order, ok := this.StopLossOrdersRight.Load(symbolRight)
		if ok && order != nil {
			// 检查 ExchangeRight 上的止损止盈订单
			if order.UpdateTime.Add(StoplossMAWindow).Before(time.Now()) {
				stoplossExpiredSymbols = append(stoplossExpiredSymbols, symbolRight)
			}
		}
	}
	// 不将 stoplossExpiredSymbols 作为风险事件，因为万一发生该事件，可能是因为 API 频率限制引起，无法处理；强行平仓会导致损失很大
	// 因此不把 RiskCategoryStoplossExpired 放入 risks 中
	return
}

// Cross 模式下，需要检查账户风险；
// 但是不直接对 Portfolio 模式跳过检查，因为账户实际设置可能 options.MarginMode 设置的不同
// 如果 marginRatio 在最近一段时间内(10 分钟），低于 rebalanceMarginRatio 的次数超过 80%，则认为是有危险的
// RiskCategoryMarginRatioLow，需要提前减仓，防止爆仓发生
func (this *FundingArbitrager) CheckAccountRisk() (dangerous bool, riskCategory RiskCategory) {
	if this.Status != StatusRunning {
		this.Infof("not running, checking account risk... (skipped)")
		return
	}
	comment := ""
	defer func() {
		if dangerous {
			this.AddRiskEvent(riskCategory, comment)
		}
	}()
	this.Infof("checking account risk...")
	this.Infof("MonitorRelaxAtTime: %s", this.MonitorRelaxAtTime)

	// 如果仓位为空，则认为没有危险
	if this.AccountSnapshot.IsPositionEmpty() {
		dangerous = false
		riskCategory = NoRiskCategory
		return
	}

	lastSnapshot := this.Monitors[len(this.Monitors)-1]
	lastSnapshotTime := lastSnapshot.CreateTime
	// 如果 marginRatio 在最近一段时间内(10 分钟），低于 rebalanceMarginRatio 的次数超过 80%，则认为是有危险的
	// 这种情况可能出现在仓位开始出现不平衡，已经提币，但是因为某种原因，没有及时平衡资金的情况
	// 提币的正常处理时间约为 5 分钟，为了防止误报，检查时间放宽到 10 分钟
	// 如果浮盈部分的资金无法划转出来用于再平衡资金，也可能导致 marginRatio 低于 rebalanceMarginRatio
	dangerCount := 0

	totalCount := 0
	for _, snapshot := range this.Monitors {
		if snapshot.CreateTime.Add(ParamMarginRatioLowTimeWindow).Before(lastSnapshotTime) {
			continue
		}
		// 早于 MonitorRelaxAtTime 的数据不检查
		if snapshot.CreateTime.Before(this.MonitorRelaxAtTime) {
			continue
		}
		totalCount++
		if snapshot.MarginRatioLeft < this.Options.RebalanceMarginRatio || snapshot.MarginRatioRight < this.Options.RebalanceMarginRatio {
			dangerCount++
		}
	}
	this.Infof("check risk: %s; margin ratio low count: %d, total count: %d", RiskCategoryMarginRatioLow, dangerCount, totalCount)
	// 防止误报，至少需要 10 个数据
	// 排除掉 rebalancing 时导致的情况，属于误报
	if totalCount >= ParamMarginRatioLowCountLimit {
		if dangerCount > int(float64(totalCount)*0.5) {
			if this.mutexes.reduceMutex.IsLocked() {
				this.Warnf("reducing positions, skip risk: %s", RiskCategoryMarginRatioLow)
			} else {
				dangerous = true
				riskCategory = RiskCategoryMarginRatioLow
				comment = fmt.Sprintf("margin ratio low count: %d, total count: %d", dangerCount, totalCount)
				return
			}
		}
	}

	dangerous = false
	riskCategory = NoRiskCategory
	return
}

func (this *FundingArbitrager) AddRiskEvent(category RiskCategory, comment string) (event *RiskEvent) {
	event = &RiskEvent{
		ID:         exchange.NewRandomID(),
		Category:   category,
		Comment:    comment,
		CreateTime: time.Now(),
	}
	this.RiskEvents = append(this.RiskEvents, event)
	this.SendMsgf("risk event: %s, %s", category, comment)
	return
}

func (this *FundingArbitrager) SetMonitorRelaxAtTime(t time.Time) {
	this.Infof("set monitor relax at time: %s", t)
	this.MonitorRelaxAtTime = t
}

func (this *FundingArbitrager) checkRateLimit(exch exchange.Exchange) {
	if exch.GetName() != exchange.Hyperliquid {
		this.Errorf("check rate limit only support hyperliquid")
		return
	}

	// 防止同时检查 rate limit，导致重复 wash 交易金额
	if !this.mutexes.checkLimitMutex.TryLock() {
		return
	}
	defer this.mutexes.checkLimitMutex.Unlock()

	hyper := exch.(*hyperliquid.Hyperliquid)
	used, cap, err := hyper.GetRateLimit()
	if err != nil {
		this.Errorf("get rate limit failed: %s", err)
		return
	}

	left := cap - used
	if left <= int64(this.Options.WashRequestLimit) {
		this.Infof("rate limit is low, used: %d, cap: %d, left: %d", used, cap, left)
		this.Infof("wash trade to increase rate limit...")
		this.SendMsgf("即将达到请求限制(%d)，准备刷量 %.f USDC 以提高请求上限", int(this.Options.WashRequestLimit), this.Options.WashRequestLimit)

		uSymbol := "USDC"
		symbolCode, err := exchange.NewSymbolCode("BTC00.U", uSymbol)
		if err != nil {
			this.Errorf("new symbol code failed: %s", err)
			return
		}
		futureSymbol, err := exch.TranslateSymbolCodeToFutureSymbol(symbolCode)
		if err != nil {
			this.Errorf("translate future symbol failed: %s", err)
			return
		}
		symbolItem := &exchange.SymbolItem{
			Symbol: futureSymbol,
			Code:   symbolCode,
		}
		washQty := this.Options.WashRequestLimit / 2
		err = this.Wash(exch, symbolItem, 0, washQty, -1, nil) // 每 1 USDC 恢复 1 次请求
		if err != nil {
			this.ErrorMsgf("wash trade failed: %s", err)
		} else {
			this.SendMsgf("刷量成功，交易所：%s，品种：%s，金额：%.f USDC", exch.GetName(), futureSymbol, this.Options.WashRequestLimit)
		}
	}
}

func (this *FundingArbitrager) GetPriceWatches(coins []string) (leftWatches []*exchange.PriceWatch, rightWatches []*exchange.PriceWatch) {
	// 如果没有持仓，也拼凑永续合约的品种，检查价差
	validCoinsSymbolCodes := []*exchange.SymbolCode{}
	usymbol := this.GetUSDXSymbol()
	for _, coin := range coins {
		code, err := exchange.NewSymbolCode(fmt.Sprintf("%s00.U", coin), usymbol)
		if err != nil {
			this.Errorf("new symbol code failed, error: %s", err)
			continue
		}
		validCoinsSymbolCodes = append(validCoinsSymbolCodes, code)
	}
	validCoinsSymbolCodes = utils.UniqueAny(validCoinsSymbolCodes)
	for _, code := range validCoinsSymbolCodes {
		symbolLeft, symbolRight, err := this.TranslateBothSideSymbols(code.String())
		if err != nil {
			continue
		}
		leftWatches = append(leftWatches, &exchange.PriceWatch{
			SymbolCode: *code,
			Symbol:     symbolLeft,
		})
		rightWatches = append(rightWatches, &exchange.PriceWatch{
			SymbolCode: *code,
			Symbol:     symbolRight,
		})
	}
	return
}

// 不管是否暂停，都会更新 accountSnapshot
// 记录 account 的情况，以及 symbol 的价格数据，priceDiffAlert 需要用到
func (this *FundingArbitrager) UpdateAccountSnapshot() (er error) {
	this.mutexes.monitorsMutex.Lock()
	defer this.mutexes.monitorsMutex.Unlock()

	this.Infof("updating account snapshot...")

	worthLeft, worthRight, err := this.GetAccountWorth()
	if err != nil {
		this.Errorf("get account worth failed: %s", err)
		return fmt.Errorf("get account worth failed: %s", err)
	}

	positionsLeft, positionsRight, err := this.GetPositions()
	if err != nil {
		this.Errorf("get positions failed: %s", err)
		return fmt.Errorf("get positions failed: %s", err)
	}

	accountSnapshot := NewAccountSnapshot()
	this.PositionSnapshot = &PositionSnapshot{
		ID:             accountSnapshot.ID,
		PositionsLeft:  positionsLeft,
		PositionsRight: positionsRight,
		CreateTime:     time.Now(),
	}

	//  mockPosition 中包含真是的 Position，也包含 ValidCoins 对应的 mockPosition，生成 symbolContext 的数据
	mockPositionsLeft := append([]*exchange.Position{}, positionsLeft...)
	mockPositionsRight := append([]*exchange.Position{}, positionsRight...)

	// 获取有真实持仓的 symbol，用于后面检查重复
	symbolsLeft := utils.Map(mockPositionsLeft, func(position *exchange.Position) string {
		return position.Symbol
	})
	symbolsRight := utils.Map(mockPositionsRight, func(position *exchange.Position) string {
		return position.Symbol
	})
	// 如果没有持仓，也拼凑永续合约的品种，检查价差
	validCoinsSymbolCodes := []*exchange.SymbolCode{}
	validCoins := this.Options.ValidCoins
	usymbol := this.GetUSDXSymbol()
	for _, coin := range validCoins {
		code, err := exchange.NewSymbolCode(fmt.Sprintf("%s00.U", coin), usymbol)
		if err != nil {
			this.Errorf("new symbol code failed, error: %s", err)
			continue
		}
		validCoinsSymbolCodes = append(validCoinsSymbolCodes, code)
	}
	validCoinsSymbolCodes = utils.UniqueAny(validCoinsSymbolCodes)
	for _, code := range validCoinsSymbolCodes {
		symbolLeft, symbolRight, err := this.TranslateBothSideSymbols(code.String())
		if err != nil {
			continue
		}
		if !utils.SliceContains(symbolsLeft, symbolLeft) {
			mockPositionsLeft = append(mockPositionsLeft, &exchange.Position{
				Symbol:         symbolLeft,
				InstrumentType: exchange.Spot,
				Qty:            0,
			})
		}
		if !utils.SliceContains(symbolsRight, symbolRight) {
			mockPositionsRight = append(mockPositionsRight, &exchange.Position{
				Symbol:         symbolRight,
				InstrumentType: exchange.USDXMarginedFutures,
				Qty:            0,
			})
		}
	}

	for _, position := range mockPositionsLeft {
		symbolCtx, _ := accountSnapshot.SymbolContextLeft.LoadOrStore(position.Symbol, NewSymbolContext())
		symbolCtx.LastPrice = position.GetLastPrice()
		instrument, err := this.ExchangeLeft.GetInstrument(exchange.Spot, position.Symbol)
		if err != nil {
			this.Errorf("get instrument failed on left exchange, error: %s", err)
			continue
		}
		if symbolCtx.LastPrice == 0 {
			// GetLastPrice 接口开销很高，通过 AddPriceWatch 可以获取到 ticker 的价格，这里用 ticker 的价格
			ticker, found := this.ExchangeLeft.GetTicker(exchange.Spot, position.Symbol)
			if found {
				symbolCtx.LastPrice = ticker.Close
			}
		}
		symbolCtx.MarkPrice = symbolCtx.LastPrice  // not updated on instrument, use last price as mark price
		symbolCtx.IndexPrice = symbolCtx.LastPrice // not updated on instrument, use last price as index price
		symbolCtx.FundingRate = instrument.FundingRate
		symbolCtx.OpenInterest = instrument.OpenInterest
		symbolCtx.PositionValue = position.Qty * symbolCtx.LastPrice
		accountSnapshot.SymbolContextLeft.Store(position.Symbol, symbolCtx)
	}

	for _, position := range mockPositionsRight {
		symbolCtx, _ := accountSnapshot.SymbolContextRight.LoadOrStore(position.Symbol, NewSymbolContext())
		symbolCtx.LastPrice = position.GetLastPrice()
		instrument, err := this.ExchangeRight.GetInstrument(exchange.USDXMarginedFutures, position.Symbol)
		if err != nil {
			this.Errorf("get instrument failed on right exchange, error: %s", err)
			continue
		}
		if symbolCtx.LastPrice == 0 {
			ticker, found := this.ExchangeRight.GetTicker(exchange.USDXMarginedFutures, position.Symbol)
			if found {
				symbolCtx.LastPrice = ticker.Close
			}
		}
		symbolCtx.MarkPrice = instrument.MarkPrice
		symbolCtx.IndexPrice = instrument.IndexPrice
		symbolCtx.FundingRate = instrument.FundingRate
		symbolCtx.OpenInterest = instrument.OpenInterest
		value, err := this.ExchangeRight.Qty2Size(position.InstrumentType, position.Symbol, position.Qty, symbolCtx.LastPrice)
		if err == nil {
			symbolCtx.PositionValue = value
		}

		// calculate hedge pnl
		hedgePnl := 0.0
		if symbolCtx.PositionValue != 0 {
			spotSymbol, err := this.ConvertSymbol(position.Symbol, false)
			if err == nil {
				symbolContextLeft, _ := accountSnapshot.SymbolContextLeft.Load(spotSymbol)
				if symbolContextLeft != nil {
					positionValueLeft := symbolContextLeft.PositionValue
					if positionValueLeft != 0 {
						hedgePnl = -(symbolCtx.PositionValue + positionValueLeft)
					}
				} else {
					this.Errorf("calcualte hedge pnl failed, symbol context left is nil")
				}
			} else {
				this.Errorf("calcualte hedge pnl failed, translate symbol failed, error: %s", err)
			}
		}
		_, symbolCodeRight, err := this.ExchangeRight.TranslateFutureSymbol(position.InstrumentType, position.Symbol, this.GetUSDXSymbol())
		if err == nil {
			accountSnapshot.HedgePNLs.Store(symbolCodeRight.Code, hedgePnl)
		}
		accountSnapshot.SymbolContextRight.Store(position.Symbol, symbolCtx)
	}

	accountSnapshot.SymbolContextLeft.Range(func(key string, value *SymbolContext) bool {
		this.Debugf("left symbol: %s, symbolCtx.LastPrice: %f", key, value.LastPrice)
		return true
	})
	accountSnapshot.SymbolContextRight.Range(func(key string, value *SymbolContext) bool {
		this.Debugf("right symbol: %s, symbolCtx.LastPrice: %f", key, value.LastPrice)
		return true
	})

	positionValueLeft, positionLeverageLeft, err := this.CalcuatePositionValue(this.ExchangeLeft, positionsLeft)
	if err != nil {
		er = err
		return
	}
	positionValueRight, positionLeverageRight, err := this.CalcuatePositionValue(this.ExchangeRight, positionsRight)
	if err != nil {
		er = err
		return
	}

	accountSnapshot.PositionValueLeft = positionValueLeft
	accountSnapshot.PositionValueRight = positionValueRight
	accountSnapshot.PositionLeverageLeft = positionLeverageLeft
	accountSnapshot.PositionLeverageRight = positionLeverageRight

	if positionLeverageLeft != 0 {
		accountSnapshot.MarginAvailableLeft = worthLeft - positionValueLeft/positionLeverageLeft
	} else {
		accountSnapshot.MarginAvailableLeft = worthLeft
	}

	if accountSnapshot.MarginAvailableLeft < 0 {
		accountSnapshot.MarginAvailableLeft = 0
	}

	if positionLeverageRight != 0 {
		accountSnapshot.MarginAvailableRight = worthRight - positionValueRight/positionLeverageRight
	} else {
		accountSnapshot.MarginAvailableRight = worthRight
	}

	if accountSnapshot.MarginAvailableRight < 0 {
		accountSnapshot.MarginAvailableRight = 0
	}

	accountSnapshot.MarginValueLeft = worthLeft
	accountSnapshot.MarginValueRight = worthRight
	if accountSnapshot.PositionValueLeft > 0 {
		accountSnapshot.MarginRatioLeft = accountSnapshot.MarginValueLeft / accountSnapshot.PositionValueLeft
	} else {
		accountSnapshot.MarginRatioLeft = 1
	}
	if accountSnapshot.PositionValueRight > 0 {
		accountSnapshot.MarginRatioRight = accountSnapshot.MarginValueRight / accountSnapshot.PositionValueRight
	} else {
		accountSnapshot.MarginRatioRight = 1
	}
	if this.Options.MarginMode == string(exchange.Portfolio) {
		accountSnapshot.MarginValueTotal = accountSnapshot.MarginValueRight
		accountSnapshot.PositionValueTotal = accountSnapshot.PositionValueRight
	} else {
		accountSnapshot.MarginValueTotal = accountSnapshot.MarginValueLeft + accountSnapshot.MarginValueRight
		accountSnapshot.PositionValueTotal = accountSnapshot.PositionValueLeft + accountSnapshot.PositionValueRight
	}
	if accountSnapshot.PositionValueTotal > 0 {
		accountSnapshot.MarginRatioTotal = accountSnapshot.MarginValueTotal / accountSnapshot.PositionValueTotal
	} else {
		accountSnapshot.MarginRatioTotal = 1
	}
	accountSnapshot.CreateTime = time.Now()
	this.AccountSnapshot = accountSnapshot
	// update monitors
	this.Monitors = append(this.Monitors, this.AccountSnapshot)
	// 约 5 分钟的高频数据
	if len(this.Monitors) > MonitorLimit {
		this.Monitors = this.Monitors[len(this.Monitors)-MonitorLimit:]
	}
	// update account snapshots
	hourTimestamp := this.AccountSnapshot.CreateTime.Truncate(time.Hour).Unix()
	if this.AccountSnapshots == nil {
		this.AccountSnapshots = exchange.NewSyncMapOf[int64, *AccountSnapshot]()
	}
	this.AccountSnapshots.Store(hourTimestamp, this.AccountSnapshot)

	// update position history
	hourTimestamp = this.PositionSnapshot.CreateTime.Truncate(time.Hour).Unix()
	if this.PositionHistory == nil {
		this.PositionHistory = exchange.NewSyncMapOf[int64, *PositionSnapshot]()
	}
	this.PositionHistory.Store(hourTimestamp, this.PositionSnapshot)
	// 删除较早的数据
	this.PositionHistory.Range(func(timestamp int64, snapshot *PositionSnapshot) bool {
		if timestamp < hourTimestamp-PositionHistoryLimit {
			this.PositionHistory.Delete(timestamp)
		}
		return true
	})
	go this.SaveAccountSnapshot(this.AccountSnapshot)
	go this.SavePositionSnapshot(this.PositionSnapshot)
	go this.orderController.GetStorage().Save()
	return nil
}

// 仅获取 USDX 合约的资产价值，因为其他账号如现货可能是独立的，无法做保证金
func (this *FundingArbitrager) GetAccountWorth() (worthLeft, worthRight float64, er error) {
	if err := this.CheckExchanges(); err != nil {
		er = err
		return
	}
	wg := sync.WaitGroup{}
	wg.Add(2)
	var errLeft, errRight error
	go func() {
		defer wg.Done()
		worthLeft, errLeft = this.ExchangeLeft.GetAccountWorth(this.ID, []exchange.InstrumentType{exchange.Spot}, 0, nil)
	}()
	go func() {
		defer wg.Done()
		worthRight, errRight = this.ExchangeRight.GetAccountWorth(this.ID, []exchange.InstrumentType{exchange.USDXMarginedFutures}, 0, nil)
	}()
	wg.Wait()
	if errLeft != nil {
		er = fmt.Errorf("get account worth of left exchange failed, error: %s", errLeft)
		return
	}
	if errRight != nil {
		er = fmt.Errorf("get account worth of right exchange failed, error: %s", errRight)
		return
	}
	return
}

func (this *FundingArbitrager) GetPositions() (positionsLeft []*exchange.Position, positionsRight []*exchange.Position, er error) {
	// get positions simultaneously
	wg := sync.WaitGroup{}
	wg.Add(2)
	var errLeft, errRight error
	go func() {
		defer wg.Done()
		positionsLeft = []*exchange.Position{}

		holdings, err := this.ExchangeLeft.GetAccountHoldings([]exchange.InstrumentType{exchange.Spot})
		if err != nil {
			errLeft = fmt.Errorf("get account holdings failed, error: %s", err)
			return
		}

		for _, holding := range holdings {
			if holding.CoinOrSymbol == this.GetUSDXSymbol() {
				continue
			}
			symbolCode, err := exchange.NewSymbolCode(fmt.Sprintf("%s--", holding.CoinOrSymbol), this.GetUSDXSymbol())
			if err != nil {
				this.Errorf("new symbol code failed, error: %s", err)
				continue
			}
			spotSymbol, err := this.ExchangeLeft.TranslateSymbolCodeToSpotSymbol(symbolCode)
			if err != nil {
				this.Errorf("translate symbol code to future symbol failed, error: %s", err)
				continue
			}
			lastPrice, err := this.ExchangeLeft.GetLastPrice(holding.InstrumentType, spotSymbol, false)
			if err != nil {
				this.Errorf("get last price failed, error: %s", err)
				continue
			}

			// Filter out small value positions (dust, airdrops, etc.)
			value := holding.Total * lastPrice
			if value < MinPositionValue {
				this.Debugf("skipping small position: %s, value: %.6f < %.2f", holding.CoinOrSymbol, value, MinPositionValue)
				continue
			}

			pos := &exchange.Position{
				InstrumentType: holding.InstrumentType,
				Symbol:         spotSymbol,
				Qty:            holding.Total,
				Available:      holding.Available,
				EntryPrice:     lastPrice,
				MarkPrice:      lastPrice,
				LastPrice:      lastPrice,
				Side:           exchange.PositionSideLong,
				UpdateTime:     utils.Ptr(time.Now()),
			}
			positionsLeft = append(positionsLeft, pos)
		}
	}()
	go func() {
		defer wg.Done()
		positionsRight = []*exchange.Position{}
		for _, instrumentType := range this.ExchangeRight.GetSupportedInstrumentTypes() {
			if !instrumentType.IsFuture() {
				continue
			}
			iPositions, err := this.ExchangeRight.GetPositions(instrumentType, "", false)
			if err != nil {
				errRight = fmt.Errorf("get positions on exchange right failed, error: %s", err)
				return
			}
			positionsRight = append(positionsRight, iPositions...)
		}
	}()
	wg.Wait()
	if errLeft != nil {
		er = errLeft
		return
	}
	if errRight != nil {
		er = errRight
		return
	}
	return
}

func (this *FundingArbitrager) CalcuatePositionValue(ex exchange.Exchange, positions []*exchange.Position) (value, leverage float64, er error) {
	usedMargin := 0.0
	for _, position := range positions {
		if position.Qty == 0 {
			continue
		}
		lastPrice := position.LastPrice
		if lastPrice == 0 {
			lastPrice = position.MarkPrice
		}
		if lastPrice == 0 {
			return 0, 0, fmt.Errorf("calculate position value failed, position last price is 0")
		}
		if position.InstrumentType == exchange.USDXMarginedFutures {
			v, err := ex.Qty2Size(position.InstrumentType, position.Symbol, lastPrice, math.Abs(position.Qty))
			if err != nil {
				er = fmt.Errorf("calculate position value failed, error: %s", err)
				return
			}
			value += v
			usedMargin += v / position.Leverage
		} else if position.InstrumentType == exchange.CoinMarginedFutures {
			v, err := ex.Qty2Size(position.InstrumentType, position.Symbol, lastPrice, math.Abs(position.Qty))
			if err != nil {
				er = fmt.Errorf("calculate position value failed, error: %s", err)
				return
			}
			value += v * lastPrice
			usedMargin += v / position.Leverage
		} else {
			value += position.Qty * lastPrice
			usedMargin += value
		}
	}
	if usedMargin > 0 {
		leverage = value / usedMargin
	}
	return
}

func (this *FundingArbitrager) Pause() {
	this.Status = StatusPaused
	this.StatusUpdateTime = time.Now()
	this.MonitorRelaxAtTime = time.Now()
	this.orderController.GetStorage().Save()
}

func (this *FundingArbitrager) Resume() {
	this.Status = StatusRunning
	this.StatusUpdateTime = time.Now()
	this.MonitorRelaxAtTime = time.Now()
	this.orderController.GetStorage().Save()
}

func (this *FundingArbitrager) Stop() {
	this.Status = StatusStopped
	this.StatusUpdateTime = time.Now()
	this.MonitorRelaxAtTime = time.Now()
	this.orderController.GetStorage().Save()
}

// 检查非 U 本位的合约，需要报警，可能是操作失误
func (this *FundingArbitrager) HasAlienPositions() (yes bool, er error) {
	alienSymbolsRight := []string{}
	defer func() {
		if yes {
			this.AddRiskEvent(RiskCategoryAlienPosition, fmt.Sprintf("symbols, right: %v", alienSymbolsRight))
		}
	}()
	this.Infof("checking alien positions...")

	// 使用快照数据来判断，尽量提高效率
	if this.PositionSnapshot == nil {
		return false, nil
	}
	positionsRight := this.PositionSnapshot.PositionsRight
	alienPositionsRight := []*exchange.Position{}
	for _, position := range positionsRight {
		if position.InstrumentType != exchange.USDXMarginedFutures {
			alienPositionsRight = append(alienPositionsRight, position)
			alienSymbolsRight = append(alienSymbolsRight, position.Symbol)
		}
	}
	if len(alienPositionsRight) > 0 {
		return true, nil
	}
	return false, nil
}

func (this *FundingArbitrager) Debugf(format string, args ...any) {
	this.orderController.Debugf("(%s) %s", this.ID, fmt.Sprintf(format, args...))
}

func (this *FundingArbitrager) Infof(format string, args ...any) {
	this.orderController.Infof("(%s) %s", this.ID, fmt.Sprintf(format, args...))
}

func (this *FundingArbitrager) Warnf(format string, args ...any) {
	this.orderController.Warnf("(%s) %s", this.ID, fmt.Sprintf(format, args...))
}

func (this *FundingArbitrager) Errorf(format string, args ...any) {
	this.orderController.Errorf("(%s) %s", this.ID, fmt.Sprintf(format, args...))
}

func (this *FundingArbitrager) SendMsgf(format string, args ...any) {
	this.orderController.SendMsgf("(%s) %s", this.GetAliasOrID(), fmt.Sprintf(format, args...))
}

func (this *FundingArbitrager) ErrorMsgf(format string, args ...any) {
	this.orderController.ErrorMsgf("(%s) %s", this.GetAliasOrID(), fmt.Sprintf(format, args...))
}

func (this *FundingArbitrager) WarnMsgf(format string, args ...any) {
	this.orderController.WarnMsgf("(%s) %s", this.GetAliasOrID(), fmt.Sprintf(format, args...))
}

func (this *FundingArbitrager) AlertMsgf(format string, args ...any) {
	this.orderController.AlertMsgf("(%s) %s", this.GetAliasOrID(), fmt.Sprintf(format, args...))
}

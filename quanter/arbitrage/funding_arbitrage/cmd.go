package funding_arbitrage

import (
	"fmt"
	"strings"
	"time"

	"github.com/mattn/go-runewidth"
	"github.com/spf13/cast"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/option"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/hyperliquid"
	"github.com/wizhodl/quanter/utils"
)

const ALERT_ORDER_VALUE = 100000.0

type ArbitrageBaseCommand struct {
	command.Command
	controller base.OrderControllable
	arbitrager *FundingArbitrager
}

type FundingArbitrageCommand struct {
	ArbitrageBaseCommand
}

func (this *ArbitrageBaseCommand) CheckCurrentArbitrager() bool {
	subcommand := this.GetSubcommand()
	if utils.SliceContainsEqualFold([]string{"statusall", "new", "remove", "use", "alias"}, subcommand) {
		return true
	}
	if this.arbitrager == nil {
		this.ErrorMsgf("当前没有设置套利机，请先用 `.fa use [ID/Alias]` 设置")
		return false
	}
	return true
}

func (this *ArbitrageBaseCommand) PrePrepare() bool {
	if this.RequiresConfirm {
		if !this.CheckCurrentArbitrager() {
			return false
		}
		if this.arbitrager != nil {
			this.SendMsgf("当前资金费套利机: %s", this.arbitrager.GetAliasOrID())
		}
		return true
	}
	return true
}

func (this *ArbitrageBaseCommand) PreDo() bool {
	if !this.RequiresConfirm {
		if !this.CheckCurrentArbitrager() {
			return false
		}
		if this.arbitrager != nil {
			this.SendMsgf("当前资金费套利机: %s", this.arbitrager.GetAliasOrID())
		}
		return true
	}
	return true
}

func NewFundingArbitragerCommand(controller base.OrderControllable) *FundingArbitrageCommand {
	cmd := &FundingArbitrageCommand{
		ArbitrageBaseCommand: ArbitrageBaseCommand{
			Command: command.Command{
				Name: "fa",
				Instructions: []string{
					"`.fa use [ID/Alias]`, 资金费套利，设置当前控制器",
					"`.fa info`, 资金费套利，打印当前控制器信息",
					"`.fa new ControllerID[,RightControllerID] [TargetMarginRatio=xxx,RebalanceMarginRatio=xxx] ?`, 资金费套利，创建新的控制器",
					"`.fa edit ControllerID[,RightControllerID] ?`, 资金费套利，编辑当前控制器",
					"`.fa remove ?`, 资金费套利，移除当前控制器",
					"`.fa status/s`, 资金费套利，打印控制器状态",
					"`.fa report/r`, 资金费套利，打印控制器报告",
					"`.fa holdings/h`, 资金费套利，打印资产详情",
					"`.fa stop AuthCode`, 资金费套利，停止控制器运行，不自动平仓",
					"`.fa pause AuthCode`, 资金费套利，暂停控制器运行，不自动平仓",
					"`.fa resume AuthCode`, 资金费套利，恢复控制器运行",
					"`.fa rebalance AuthCode`, 资金费套利，平衡期现账号余额",
					"`.fa open SymbolCode qty/xxx.u [splits] ?`, 资金费套利，开仓某个品种",
					"`.fa reduce SymbolCode qty/xxx.u [splits] ?`, 资金费套利，减仓某个品种",
					"`.fa close SymbolCode1,SymbolCode2... [splits] ?`, 资金费套利，平仓某些品种",
					"`.fa closeAll [splits] ?`, 资金费套利，平仓所有品种",
					"`.fa wash SymbolCode qty/xxx.u [splits] ?`, 资金费套利，刷量交易某个品种",
					"`.fa maintenance/maint startTime/now endTime/minutes ?`, 资金费套利，添加维护，0 minutes 表示删除",
					"`.fa maintenances/maints`, 资金费套利，打印维护",
					"`.fa position/pos [ID]`, 资金费套利，打印持仓",
					"`.fa risks`, 资金费套利，打印风险事件",
					"`.fa monitors`, 资金费套利，打印监控数据",
					"`.fa prices SymbolCode [exactTime]`, 资金费套利，打印监控价格数据",
					"`.fa priceDiff`, 资金费套利，打印品种价差",
					"`.fa snapshots [daily/exactTime]`, 资金费套利，打印资产快照",
					"`.fa snapshot/snap [ID]`, 资金费套利，打印资产快照详情",
					"`.fa fundings [symbolCodes]`, 资金费套利，打印品种的资金费率",
					"`.fa alias ID Alias AuthCode`, 资金费套利，设置控制器别名",
					"`.fa set TargetMarginRatio=xxx,RebalanceMarginRatio=xxx ?`, 资金费套利，设置控制器配置",
					"`.fa setParams symbolCode:PriceDiff=xxx,MarkPriceDiff=xxx,Stoploss=xxx,LeverageLeft=xxx,LeverageRight=xxx|... ?`, 资金费套利，设置品种参数",
					"`.fa params`, 资金费套利，打印品种参数",
					"`.fa simulate riskCategory`, 资金费套利，模拟风险事件（仅 Debug 模式下有效）",
					"`.fa options`, 资金费套利，打印控制器配置",
				},
			},
			controller: controller,
		},
	}
	arbitragers := cmd.controller.ListStrategies()
	lastUseTime := time.Time{}
	for _, arbitrager := range arbitragers {
		if arbitrager.GetStrategyType() == base.StrategyTypeFundingArbitrager {
			if arbitrager.GetLastUseTime().After(lastUseTime) {
				cmd.arbitrager = arbitrager.(*FundingArbitrager)
				lastUseTime = arbitrager.GetLastUseTime()
			}
		}
	}
	return cmd
}

func (this *FundingArbitrageCommand) Prepare() bool {
	subcommand := this.GetSubcommand()
	subcommand = strings.ToLower(subcommand)
	args := this.GetArgs()
	switch subcommand {
	case "new":
		arbitrager, err := this.NewFundingArbitrager(args)
		if err != nil {
			this.ErrorMsgf("创建资金费套利机失败: %s", err)
			return false
		}
		preview := arbitrager.PreviewCreation()
		this.SendMsgf("新增资金费套利：\n```%s```", preview)
	case "edit":
		if this.arbitrager.Status == StatusRunning {
			this.ErrorMsgf("资金费套利机 %s 正在运行，无法编辑", this.arbitrager.ID)
			return false
		}

		newArbitrager, err := this.NewFundingArbitrager(args)
		if err != nil {
			this.ErrorMsgf("尝试用新参数创建资金费套利机失败: %s", err)
			return false
		}
		preview := newArbitrager.PreviewEdit(this.arbitrager)
		this.SendMsgf("编辑资金费套利：\n```%s```", preview)

		if this.arbitrager.PositionSnapshot.HasPositions() {
			this.SendHighlightWarningf("当前有持仓，修改可能导致严重后果")
		}
	case "remove":
		this.SendMsgf("移除资金费套利机：\n```%s```", this.arbitrager.RenderStatus())
	case "open", "reduce":
		if subcommand == "open" {
			subcommand = "short"
		} else if subcommand == "reduce" {
			subcommand = "long"
		}
		symbolCodeStr := args[0]
		qtyStr := args[1]

		// 检查 symbolCodeStr 是否正确
		_, _, err := this.arbitrager.TranslateBothSideSymbols(symbolCodeStr)
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}

		symbolCode, orderArgs, er := this.arbitrager.GetCreateOrderArgs(subcommand, symbolCodeStr, qtyStr)
		if er != nil {
			this.ErrorMsgf("解析创建订单参数失败: %s", er)
			return false
		}

		if subcommand == "short" && !utils.SliceContainsEqualFold(this.arbitrager.Options.ValidCoins, symbolCode.Coin()) {
			this.ErrorMsgf("非有效币种: %s，请检查配置 %s 是否正确", symbolCode.Coin(), OptionValidCoins)
			return false
		}
		splits, err := this.parseSplitArgs(2)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		// 检查 OI 满了无法开仓的情况
		if this.arbitrager.ExchangeRight.GetName() == exchange.Hyperliquid && subcommand == "open" {
			isAtCap, err := this.arbitrager.ExchangeRight.(*hyperliquid.Hyperliquid).IsAtOpenInterestCap(orderArgs.Symbol)
			if err != nil {
				this.ErrorMsgf("获取 OI 限制失败: %s", err)
				return false
			}
			if isAtCap {
				this.ErrorMsgf("Hyperliquid %s OI 已满，无法开仓", orderArgs.Symbol)
				return false
			}
		}

		if orderArgs.QuoteQty >= ALERT_ORDER_VALUE {
			this.SendHighlightWarningf("订单价值较大: %.0f.U, 请注意是否输入有误", orderArgs.QuoteQty)
		} else {
			ex := this.arbitrager.ExchangeRight
			price, err := ex.GetLastPrice(exchange.USDXMarginedFutures, orderArgs.Symbol, true)
			if err != nil {
				this.ErrorMsgf("获取最新价格失败: %s", err)
				return false
			}
			value, _ := ex.Qty2Size(exchange.USDXMarginedFutures, orderArgs.Symbol, price, orderArgs.Qty)
			if value >= ALERT_ORDER_VALUE {
				this.SendHighlightWarningf("订单价值较大: %.0f, 请注意是否输入有误", value)
			}
		}

		if subcommand == "short" {
			this.checkAvailableMargin(orderArgs, true)
		}
		this.SendMsgf("创建订单：\n```%s```", this.arbitrager.RenderCreateOrderArgs(symbolCode, orderArgs, splits))
	case "close":
		positions := []*exchange.Position{}
		_, posRight, err := this.arbitrager.GetPositions()
		if err != nil {
			this.ErrorMsgf("获取持仓失败: %s", err)
			return false
		}
		uSymbol := this.arbitrager.GetUSDXSymbol()
		_, _, futureSymbols, err := exchange.ParseSymbolsFromSymbolCodes(this.arbitrager.ExchangeRight, args[0], uSymbol)
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}
		for _, symbol := range futureSymbols {
			for _, pos := range posRight {
				if pos.Symbol == symbol {
					positions = append(positions, pos)
				}
			}
		}

		_, err = this.parseSplitArgs(1)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		posStr, err := exchange.RenderPositions(this.arbitrager.ExchangeRight, positions)
		if err != nil {
			this.ErrorMsgf("打印持仓出错: %s", err)
			return false
		}
		this.SendMsgf("平仓以下仓位：\n```%s```", posStr)
	case "closeall":
		_, err := this.parseSplitArgs(0)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		_, posRight, err := this.arbitrager.GetPositions()
		if err != nil {
			this.ErrorMsgf("获取持仓失败: %s", err)
			return false
		}
		posStr, err := exchange.RenderPositions(this.arbitrager.ExchangeRight, posRight)
		if err != nil {
			this.ErrorMsgf("打印持仓出错: %s", err)
			return false
		}
		this.SendMsgf("平仓以下仓位：\n```%s```", posStr)
	case "set":
		oldOpts := this.arbitrager.GetOptions()
		newOpts := oldOpts.Copy()
		updatedNames, err := newOpts.UpdateFromString(args[0])
		if err != nil {
			this.ErrorMsgf("解析选项错误: %s", err)
			return false
		}
		if err := this.arbitrager.ValidateOptions(newOpts); err != nil {
			this.ErrorMsgf("选项验证失败: %s", err)
			return false
		}
		this.SendMsgf("更新配置项：\n```%s```", oldOpts.DiffToTable(newOpts, updatedNames))
		return true
	case "setparam", "setparams":
		parts := strings.Split(args[0], "|")
		symbolParams := &exchange.SyncMapOf[string, *option.Options]{}
		for _, symbolCodeStr := range parts {
			subParts := strings.Split(symbolCodeStr, ":")
			symbolCode := subParts[0]
			symbolCodes, _, _, err := exchange.ParseSymbolsFromSymbolCodes(this.arbitrager.ExchangeLeft, symbolCode, this.arbitrager.GetUSDXSymbol())
			if err != nil {
				this.ErrorMsgf("解析品种代码失败: %s", err)
				return false
			}
			for _, symbolCode := range symbolCodes {
				params := subParts[1]
				// 如果当前已经有配置，则在现有配置上更新
				options, ok := this.arbitrager.SymbolParams.Load(symbolCode)
				if ok && options != nil && !options.IsZero() {
					optionsCopy := options.Copy()
					symbolParams.Store(symbolCode, optionsCopy)
				}
				this.arbitrager._updateParams(symbolParams, symbolCode, params)
			}
		}
		this.SendMsgf("更新参数: \n```%s```", this.arbitrager._renderParams(symbolParams))
		return true
	case "rotate":
		_, symbolRight, err := this.arbitrager.TranslateBothSideSymbols(args[1])
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}

		ratio, _, err := utils.ParseFloatOrPercentage(args[2], false, false)
		if err != nil {
			this.ErrorMsgf("解析百分比失败: %s", err)
			return false
		}

		splits, err := this.parseSplitArgs(3)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		_, posRight, err := this.arbitrager.GetPositions()
		if err != nil {
			this.ErrorMsgf("获取持仓失败: %s", err)
			return false
		}

		var positions []*exchange.Position
		var position *exchange.Position
		var ex exchange.Exchange
		positions = posRight
		ex = this.arbitrager.ExchangeRight
		for _, pos := range positions {
			if pos.Symbol == symbolRight {
				position = pos
				break
			}
		}
		if position == nil || position.Qty == 0 {
			this.ErrorMsgf("没有找到对应的持仓")
			return false
		}

		this.SendMsgf("平开仓释放保证金：\n```%s```", this.arbitrager.RenderRotate(ex, position, ratio, splits))

		return true
	case "wash":
		ex := this.arbitrager.ExchangeRight

		_, symbolRight, err := this.arbitrager.TranslateBothSideSymbols(args[0])
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}
		symbol := symbolRight

		qty, quoteQty, err := this.parseQty(1)
		if err != nil {
			this.ErrorMsgf("解析数量失败: %s", err)
			return false
		}
		if quoteQty >= ALERT_ORDER_VALUE {
			this.SendHighlightWarningf("刷量交易数量较大: %.0f.U, 请注意是否输入有误", quoteQty)
		} else {
			price, err := ex.GetLastPrice(exchange.USDXMarginedFutures, symbol, true)
			if err != nil {
				this.ErrorMsgf("获取最新价格失败: %s", err)
				return false
			}
			value, _ := ex.Qty2Size(exchange.USDXMarginedFutures, symbol, price, qty)
			if value >= ALERT_ORDER_VALUE {
				this.SendHighlightWarningf("刷量交易价值较大: %.0f, 请注意是否输入有误", value)
			}
		}

		_, err = this.parseSplitArgs(2)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		return true
	case "maintenance", "maint":
		maints := this.arbitrager.Maintenances.Copy()
		maintenance, updated, er := this.setMaintenance(&maints, args)
		if er != nil {
			this.ErrorMsgf("参数错误: %s", er)
			return false
		}
		if updated {
			this.SendMsgf("更新交易所维护: \n```%s```", maintenance.ToTable())
		} else {
			this.SendMsgf("添加交易所维护: \n```%s```", maintenance.ToTable())
		}
		return true
	}
	return true
}

func (this *FundingArbitrageCommand) checkAvailableMargin(orderArgs exchange.CreateOrderArgs, isRight bool) (ok bool) {
	// 检查两边是否有足够的保证金开仓，不够则警告
	ex := this.arbitrager.ExchangeLeft
	controller := this.arbitrager.ControllerLeft
	if isRight {
		ex = this.arbitrager.ExchangeRight
		controller = this.arbitrager.ControllerRight
	}
	symbol := orderArgs.Symbol
	usymbol := controller.GetBaseConfig().USDXSymbol
	_, available, err := ex.GetBalance(orderArgs.InstrumentType, usymbol)
	if err != nil {
		this.ErrorMsgf("获取交易所余额失败: %s", err)
		return
	}
	leverage, err := this.arbitrager.GetLeverage(symbol, isRight)
	if err != nil {
		this.ErrorMsgf("获取杠杆倍数失败: %s", err)
		return
	}
	orderValue := orderArgs.QuoteQty
	if orderValue == 0 {
		lastPrice, err := ex.GetLastPrice(orderArgs.InstrumentType, symbol, true)
		if err != nil {
			this.ErrorMsgf("获取最新价格失败: %s", err)
			return
		}
		orderValue, _ = ex.Qty2Size(orderArgs.InstrumentType, symbol, lastPrice, orderArgs.Qty)
	}
	needMargin := orderValue / leverage
	if needMargin > available {
		this.SendHighlightWarningf("%s 保证金不足，开仓可能会失败。当前可用保证金: %.2f，需要保证金: %.2f", ex.GetName(), available, needMargin)
		return false
	}
	return true
}

func (this *FundingArbitrageCommand) SendHighlightWarningf(format string, a ...any) {
	message := fmt.Sprintf(format, a...)
	displayWidth := 0
	for _, r := range message {

		if runewidth.RuneWidth(r) == 3 {
			displayWidth += 3
		} else if runewidth.RuneWidth(r) == 2 {
			displayWidth += 2
		} else {
			displayWidth++
		}
	}
	stars := strings.Repeat("*", displayWidth+12) // +12 for the padding spaces and borders

	this.SendMsgf("\n%s\n*  %s  *\n%s\n", stars, message, stars)
}

func (this *FundingArbitrageCommand) ListArbitragers() {
	var strategies []base.Strategy
	for _, arbitrager := range this.controller.ListStrategies() {
		if arbitrager.GetStrategyType() == base.StrategyTypeFundingArbitrager {
			strategies = append(strategies, arbitrager)
		}
	}

	t := exchange.NewTable()
	t.SetHeader(StatusHeader)
	for _, arbitrager := range strategies {
		t.AddRow(arbitrager.(*FundingArbitrager).RenderStatusRow())
	}
	this.SendMsgf("资金费套利机:\n```%s```", t.Render())
}

func (this *FundingArbitrageCommand) Do() bool {
	subcommand := this.GetSubcommand()
	subcommand = strings.ToLower(subcommand)
	args := this.GetArgs()
	if this.arbitrager != nil {
		this.arbitrager.Infof("subcommand: %s", subcommand)
	}
	switch subcommand {
	case "new":
		arbitrager, err := this.NewFundingArbitrager(args)
		if err != nil {
			this.ErrorMsgf("创建资金费套利机失败: %s", err)
			return false
		}
		this.controller.AddStrategy(base.StrategyTypeFundingArbitrager, arbitrager)
		this.Use(arbitrager.ID)
		go arbitrager.Run()
		this.SendMsgf("新增资金费套利：\n```%s```", this.arbitrager.PreviewCreation())
		arbitrager.UpdateAccountSnapshot()
		this.SendMsgf("```%s```", arbitrager.RenderStatus())
	case "edit":
		if this.arbitrager.Status == StatusRunning {
			this.ErrorMsgf("资金费套利机 %s 正在运行，无法编辑", this.arbitrager.ID)
			return false
		}
		newArbitrager, err := this.NewFundingArbitrager(args)
		if err != nil {
			this.ErrorMsgf("编辑资金费套利机失败: %s", err)
			return false
		}
		this.arbitrager.ControllerID = newArbitrager.ControllerID
		this.arbitrager.RightControllerID = newArbitrager.RightControllerID
		this.arbitrager.SetupExchanges()
		this.SendMsgf("资金费套利机 %s 已编辑", this.arbitrager.ID)
	case "use":
		if len(this.GetArgs()) > 0 {
			_, er := this.Use(this.GetArgs()[0])
			if er != nil {
				this.ErrorMsgf("设置当前跨交易所套利机失败: %s", er)
				return false
			}
			this.SendMsgf("设置当前跨交易所套利机为: %s", this.arbitrager.GetAliasOrID())
		} else {
			this.ListArbitragers()
			return true
		}
	case "info":
		this.SendMsgf("```%s```", this.arbitrager.RenderInfo())
	case "remove":
		err := this.controller.RemoveStrategy(base.StrategyTypeFundingArbitrager, this.arbitrager.ID)
		if err != nil {
			return false
		}
		this.SendMsgf("资金费套利机 %s 已移除", this.arbitrager.ID)
	case "statusall":
		this.ListArbitragers()
	case "status", "s":
		this.SendMsgf("```%s```", this.arbitrager.RenderStatus())
	case "holdings", "h":
		balances, err := this.arbitrager.ControllerLeft.GetAccountBalances(true)
		if err != nil {
			this.ErrorMsgf("[get holdings error: %s]", err)
			return false
		}

		this.SendMsgf("控制器 *%s* 资产汇总：\n```%s```", this.arbitrager.ControllerLeft.GetID(), balances.RenderTotalCombined())
		this.SendMsgf("控制器 *%s* 资产明细：\n```%s```", this.arbitrager.ControllerLeft.GetID(), balances.Render())

		if this.arbitrager.ExchangeRight.GetID() != this.arbitrager.ExchangeLeft.GetID() {
			this.SendMsgf("--------------------------------")
			balances, err = this.arbitrager.ControllerRight.GetAccountBalances(true)
			if err != nil {
				this.ErrorMsgf("[get holdings error: %s]", err)
				return false
			}

			this.SendMsgf("控制器 *%s* 资产汇总：\n```%s```", this.arbitrager.ControllerRight.GetID(), balances.RenderTotalCombined())
			this.SendMsgf("控制器 *%s* 资产明细：\n```%s```", this.arbitrager.ControllerRight.GetID(), balances.Render())
		}

	case "report", "r":
		result := this.arbitrager.RenderReport()
		this.SendFileMessage(this.arbitrager.ID, result, "")
	case "stop":
		this.arbitrager.Stop()
		this.SendMsgf("资金费套利机 %s 已关闭", this.arbitrager.ID)
	case "pause":
		this.arbitrager.Pause()
		this.SendMsgf("资金费套利机 %s 已暂停", this.arbitrager.ID)
	case "resume":
		this.arbitrager.Resume()
		this.SendMsgf("资金费套利机 %s 已恢复", this.arbitrager.ID)
	case "open", "reduce":
		if subcommand == "open" {
			subcommand = "short"
		} else if subcommand == "reduce" {
			subcommand = "long"
		}
		symbolCodeStr := args[0]
		qtyStr := args[1]
		_, _, err := this.arbitrager.TranslateBothSideSymbols(symbolCodeStr)
		if err != nil {
			this.ErrorMsgf("解析现货/期货交易对失败: %s", err)
			return false
		}
		splits, err := this.parseSplitArgs(2)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		err = this.arbitrager.OpenPosition(subcommand, symbolCodeStr, qtyStr, splits)
		if err != nil {
			this.ErrorMsgf("开仓失败: %s", err)
			return false
		}
		// 设置监控数据放松的时间点，防止由于发送订单导致 marginRatio 快速变化
		// 5 秒后放松，太快的话，可能订单还没有同步完成
		this.arbitrager.MonitorRelaxAtTime = time.Now().Add(time.Second * 5)
		if subcommand == "short" {
			this.SendMsgf("资金费套利机 %s 已开仓", this.arbitrager.GetAliasOrID())
		} else {
			this.SendMsgf("资金费套利机 %s 已减仓", this.arbitrager.GetAliasOrID())
		}
	case "close":
		uSymbol := this.arbitrager.GetUSDXSymbol()
		_, _, futureSymbols, err := exchange.ParseSymbolsFromSymbolCodes(this.arbitrager.ExchangeRight, args[0], uSymbol)
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}

		splits, err := this.parseSplitArgs(1)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		this.arbitrager.ClosePositions(futureSymbols, splits)
		this.arbitrager.MonitorRelaxAtTime = time.Now().Add(time.Second * 5)
		this.SendMsgf("资金费套利机 %s 已平仓", this.arbitrager.GetAliasOrID())
	case "rebalance":
		this.SendMsgf("资金费套利机 %s 开始平衡保证金", this.arbitrager.GetAliasOrID())
		err := this.arbitrager.Rebalance()
		if err != nil {
			this.arbitrager.ErrorMsgf("手工平衡保证金失败: %s, 但是不自动减仓", err)
		} else {
			this.arbitrager.MonitorRelaxAtTime = time.Now()
			this.SendMsgf("手工平衡保证金成功")
		}
	case "closeall":
		splits, err := this.parseSplitArgs(0)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		this.arbitrager.CloseAllPositions(splits)
		this.arbitrager.MonitorRelaxAtTime = time.Now().Add(time.Second * 5)
		this.SendMsgf("资金费套利机 %s 已平仓所有仓位", this.arbitrager.ID)

		this.arbitrager.CheckLoanAndPay()
	case "position", "pos":
		if len(args) > 0 {
			id := args[0]
			snapshots := this.arbitrager.LookupPositionSnapshots(id, nil, 0)
			if len(snapshots) == 0 {
				this.ErrorMsgf("未找到仓位快照: %s", id)
				return false
			} else if len(snapshots) > 1 {
				this.ErrorMsgf("找到多个仓位快照: %s", id)
				return false
			}
			this.SendMsgf("```%s```", this.arbitrager.RenderPositions(snapshots[0]))
		} else {
			this.SendMsgf("```%s```", this.arbitrager.RenderPositions(this.arbitrager.PositionSnapshot))
		}
	case "risks":
		limit := 20
		if this.IsMore() {
			limit = 0
		}
		this.SendMsgf("```%s```", this.arbitrager.RenderRiskEvents(limit))
	case "monitors":
		limit := 20
		if this.IsMore() {
			limit = 0
		}
		this.SendMsgf("MonitorRelaxAtTime: `%s`\n\n```%s```", utils.FormatShortTimeStr(&this.arbitrager.MonitorRelaxAtTime, true), this.arbitrager.RenderMonitors(limit))
	case "snapshots":
		isDaily := false
		var exactTime *time.Time
		if len(args) > 0 {
			if args[0] == "daily" {
				isDaily = true
			} else {
				exactTime = utils.ParseTimeBeijing(args[0])
				if exactTime == nil {
					this.ErrorMsgf("解析时间失败: %s", args[0])
					return false
				}
			}
		}
		if exactTime != nil {
			snapshots := this.arbitrager.LookupAccountSnapshots("", exactTime, 0)
			this.SendMsgf("```%s```", this.arbitrager.RenderAccountSnapshots(snapshots))
			return true
		}
		hourlySnapshots := []*AccountSnapshot{}
		this.arbitrager.AccountSnapshots.Range(func(key int64, snapshot *AccountSnapshot) bool {
			hourlySnapshots = append(hourlySnapshots, snapshot)
			return true
		})
		if isDaily {
			keptSnapshots := this.arbitrager.FilterDailySnapshots(hourlySnapshots)
			this.SendMsgf("```%s```", this.arbitrager.RenderAccountSnapshots(keptSnapshots))
		} else {
			this.SendMsgf("```%s```", this.arbitrager.RenderAccountSnapshots(hourlySnapshots))
		}
	case "snapshot", "snap":
		id := ""
		if len(args) > 0 {
			id = args[0]
		}
		if id == "" {
			this.SendMsgf("```%s```", this.arbitrager.RenderAccountSnapshotWithContext(this.arbitrager.AccountSnapshot))
		} else {
			snapshots := this.arbitrager.LookupAccountSnapshots(id, nil, 0)
			this.SendMsgf("```%s```", this.arbitrager.RenderAccountSnapshots(snapshots))
		}
	case "prices":
		symbolCode := args[0]
		limit := 20
		if this.IsMore() {
			limit = 0
		}
		var exactTime *time.Time
		timeStr := "now"
		if len(args) > 1 {
			timeStr = args[1]
		}
		exactTime = utils.ParseTimeBeijing(timeStr)
		if exactTime == nil {
			this.ErrorMsgf("解析时间失败: %s", args[1])
			return false
		}
		snapshots := this.arbitrager.LookupAccountSnapshots("", exactTime, limit)
		this.SendMsgf("```%s```", this.arbitrager.RenderPrices(snapshots, symbolCode, limit))
	case "pricediff":
		this.arbitrager.PrintSymbolPriceDiff(false)
	case "fundings":
		symbolCodeStr := ""
		if len(args) > 0 {
			symbolCodeStr = args[0]
		}
		this.SendMsgf("```Funding APR: \n\n%s```", this.arbitrager.RenderFundings(symbolCodeStr))
	case "alias":
		idOrAlias := args[0]
		alias := args[1]
		err := this.controller.SetStrategyAlias(base.StrategyTypeFundingArbitrager, idOrAlias, alias)
		if err != nil {
			return false
		}
	case "set":
		options := args[0]
		err := this.arbitrager.SetOptions(options)
		if err != nil {
			return false
		}
		this.SendMsgf("更新资金费套利机 %s 配置项成功", this.arbitrager.ID)
		this.SendMsgf("```%s```", this.arbitrager.Options.ToTable())
	case "param", "params":
		this.SendMsgf("```%s\n\n%s```\n\n```%s```", this.arbitrager.RenderParams(), this.arbitrager.FormatParamsParsable(), this.arbitrager.FormatAllSymbolCodes())
		this.SendMsgf("\n默认配置\n```%s```", this.arbitrager.Options.ToTable())
	case "setparam", "setparams":
		parts := strings.Split(args[0], "|")
		for _, symbolCodeStr := range parts {
			if symbolCodeStr == "" {
				continue
			}
			subParts := strings.Split(symbolCodeStr, ":")
			if len(subParts) != 2 {
				this.ErrorMsgf("解析参数失败: %s，格式：`SymbolCode1,SymbolCode2:Param1=Value1,Param2=Value2,...|SymbolCode3:Param1=Value1,Param2=Value2,...`", symbolCodeStr)
				return false
			}
			symbolCode := subParts[0]
			params := subParts[1]
			allSymbolCodes, _, _, err := exchange.ParseSymbolsFromSymbolCodes(this.arbitrager.ExchangeLeft, symbolCode, this.arbitrager.GetUSDXSymbol())
			if err != nil {
				this.ErrorMsgf("解析品种代码失败: %s", err)
				return false
			}
			for _, symbolCode := range allSymbolCodes {
				this.arbitrager.UpdateParams(symbolCode, params)
				this.SendMsgf("资金费套利机 %s 设置参数成功: ```%s```", this.arbitrager.ID, this.arbitrager.RenderParamsForSymbol(symbolCode))
			}
		}
		this.arbitrager.orderController.GetStorage().Save()
	case "simulate":
		riskCategory := RiskCategory(args[0])
		// 这里不要直接调用 SimulateRiskEvent，而是设置模拟风险事件标志，在风险控制函数中，updateAccountSnapshot 之后统一执行
		if this.arbitrager.orderController.IsDebug() {
			this.arbitrager.simulatingRisk = riskCategory
			this.SendMsgf("资金费套利机 %s 开始模拟风险事件: %s", this.arbitrager.GetAliasOrID(), riskCategory)
		} else {
			this.ErrorMsgf("当前不是 debug 模式，不能模拟风险事件")
		}
	case "options":
		if this.arbitrager.ExchangeLeft.GetName() == exchange.Hyperliquid {
			this.SendMsgf("```%s```\nMonitorRelaxAtTime: `%s`\n\n", this.arbitrager.GetOptions().ToTable(), utils.FormatShortTimeStr(&this.arbitrager.MonitorRelaxAtTime, true))

			hyper := this.arbitrager.ExchangeLeft.(*hyperliquid.Hyperliquid)
			used, cap, err := hyper.GetRateLimit()
			if err != nil {
				this.ErrorMsgf("get rate limit failed: %s", err)
				return false
			}
			this.SendMsgf("Left Hyperliquid 请求限额: `%d / %d 剩余：%d`", used, cap, cap-used)

			if this.arbitrager.ExchangeRight.GetID() != this.arbitrager.ExchangeLeft.GetID() {
				hyper := this.arbitrager.ExchangeRight.(*hyperliquid.Hyperliquid)
				used, cap, err := hyper.GetRateLimit()
				if err != nil {
					this.ErrorMsgf("get rate limit failed: %s", err)
					return false
				}
				this.SendMsgf("Right Hyperliquid 请求限额: `%d / %d 剩余：%d`", used, cap, cap-used)
			}
		} else {
			this.SendMsgf("```%s```\nMonitorRelaxAtTime: `%s`", this.arbitrager.GetOptions().ToTable(), utils.FormatShortTimeStr(&this.arbitrager.MonitorRelaxAtTime, true))
		}
	case "rotate":
		symbolCodeStr := args[1]
		_, _, err := this.arbitrager.TranslateBothSideSymbols(symbolCodeStr)
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}

		ratio, _, err := utils.ParseFloatOrPercentage(args[2], false, false)
		if err != nil {
			this.ErrorMsgf("解析百分比失败: %s", err)
			return false
		}

		splits, err := this.parseSplitArgs(3)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}

		err = this.arbitrager.Rotate(symbolCodeStr, ratio, splits)
		if err != nil {
			this.ErrorMsgf("平、开仓释放保证金失败: %s", err)
			return false
		} else {
			this.SendMsgf("资金费套利机 %s 平、开仓释放保证金成功", this.arbitrager.GetAliasOrID())
		}
	case "wash":
		symbolCodeStr := args[1]
		_, symbolRight, err := this.arbitrager.TranslateBothSideSymbols(symbolCodeStr)
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}
		symbol := symbolRight

		qty, quoteQty, err := this.parseQty(2)
		if err != nil {
			this.ErrorMsgf("解析数量失败: %s", err)
			return false
		}

		splits, err := this.parseSplitArgs(3)
		if err != nil {
			this.ErrorMsgf("解析 splits 失败: %s", err)
			return false
		}
		symbolCode, err := exchange.NewSymbolCode(symbolCodeStr, this.arbitrager.GetUSDXSymbol())
		if err != nil {
			this.ErrorMsgf("解析品种代码失败: %s", err)
			return false
		}
		symbolItem := &exchange.SymbolItem{
			Code:   symbolCode,
			Symbol: symbol,
		}
		err = this.arbitrager.Wash(this.arbitrager.ExchangeRight, symbolItem, qty, quoteQty, splits, nil)
		if err != nil {
			this.ErrorMsgf("刷量交易失败: %s", err)
			return false
		} else {
			this.SendMsgf("资金费套利机 %s 刷量交易成功", this.arbitrager.GetAliasOrID())
		}
	case "maintenance", "maint":
		maints := this.arbitrager.Maintenances
		maintenance, updated, er := this.setMaintenance(&maints, args)
		if er != nil {
			this.ErrorMsgf("设置交易所维护失败: %s", er)
			return false
		}
		this.arbitrager.Maintenances = maints // 更新维护列表，否则加不进去
		if updated {
			this.SendMsgf("%s 交易所维护，更新成功：\n```%s```", this.arbitrager.GetAliasOrID(), maintenance.ToTable())
		} else {
			this.SendMsgf("%s 交易所维护，添加成功：\n```%s```", this.arbitrager.GetAliasOrID(), maintenance.ToTable())
		}
		this.arbitrager.orderController.GetStorage().Save()
	case "maintenances", "maints":
		this.SendMsgf("```%s```", this.arbitrager.Maintenances.ToTable())
	}
	return true
}

func (this *FundingArbitrageCommand) Use(idOrAlias string) (arbitrager *FundingArbitrager, er error) {
	strategy := this.controller.GetStrategy(base.StrategyTypeFundingArbitrager, idOrAlias)
	if strategy == nil {
		er = fmt.Errorf("arbitrager not found")
		return
	}

	arbitrager = strategy.(*FundingArbitrager)
	arbitrager.LastUseTime = time.Now()
	this.arbitrager = arbitrager
	return
}

func (this *FundingArbitrageCommand) NewFundingArbitrager(args []string) (*FundingArbitrager, error) {
	// ControllerID [options]
	controllerID := args[0]
	rightControllerID := args[0]
	if strings.Contains(controllerID, ",") {
		parts := strings.Split(controllerID, ",")
		controllerID = parts[0]
		rightControllerID = parts[1]
	}
	controller := this.controller.GetController(controllerID)
	if controller == nil {
		return nil, fmt.Errorf("controller not found")
	}
	ex := controller.GetExchange()
	if ex == nil {
		return nil, fmt.Errorf("exchange not found")
	}
	if !utils.SliceContains([]string{exchange.Bybit, exchange.Binance, exchange.Hyperliquid}, ex.GetName()) {
		return nil, fmt.Errorf("exchange not supported")
	}

	optStr := ""
	if len(args) > 1 {
		optStr = args[1]
	}
	arbitrager, err := NewFundingArbitrager(this.controller, controllerID, rightControllerID, optStr)
	if err != nil {
		return nil, fmt.Errorf("new funding arbitrage failed: %s", err)
	}
	arbitrager.SetController(this.controller)
	return arbitrager, nil
}

func (this *FundingArbitrageCommand) setMaintenance(maints *Maintenances, args []string) (maintenance *Maintenance, updated bool, er error) {
	startTimeStr := args[0]
	endTimeStr := args[1]
	message := ""
	if len(args) > 2 {
		message = args[2]
	}

	ex := this.arbitrager.ExchangeLeft
	startTime := utils.ParseTimeBeijing(startTimeStr)
	if startTime == nil {
		return nil, false, fmt.Errorf("parse start time failed: %s", startTimeStr)
	}
	var endTime *time.Time
	minutes, err := cast.ToIntE(endTimeStr)
	if err != nil {
		endTime = utils.ParseTimeBeijing(endTimeStr)
		if endTime == nil {
			return nil, false, fmt.Errorf("parse end time failed: %s", endTimeStr)
		}
		minutes = int(endTime.Sub(*startTime).Seconds() / 60)
	} else {
		endTime = utils.Ptr(startTime.Add(time.Duration(minutes) * time.Minute))
	}
	if minutes > 0 && endTime.Before(time.Now()) {
		return nil, false, fmt.Errorf("end time is before now: %s, now: %s", utils.FormatShortTimeStr(endTime, true), utils.FormatShortTimeStr(utils.Ptr(time.Now()), true))
	}

	maintenance, updated = maints.Set(ex.GetName(), *startTime, minutes, message, "")
	return
}

func (this *FundingArbitrageCommand) parseQty(pos int) (qty float64, quoteQty float64, er error) {
	args := this.GetArgs()
	if len(args) <= pos {
		return 0, 0, fmt.Errorf("args length is less than %d", pos)
	}
	qtyStr := args[pos]
	if len(qtyStr) > 2 && exchange.SliceContains([]string{".u"}, qtyStr[len(qtyStr)-2:]) {
		qtyStr = qtyStr[:len(qtyStr)-2]
		quoteQty, er = cast.ToFloat64E(qtyStr)
	} else {
		qty, er = cast.ToFloat64E(qtyStr)
	}
	return
}

func (this *FundingArbitrageCommand) parseSplitArgs(pos int) (splits int, err error) {
	args := this.GetArgs()
	splits = -1
	if len(args) > pos {
		splits, err = cast.ToIntE(args[pos])
		if err != nil {
			return 0, err
		}
		if splits < 1 && splits != -1 {
			return 0, fmt.Errorf("splits must be greater than 0, or equal to -1")
		}
	}
	return
}

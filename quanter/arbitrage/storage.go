package arbitrage

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path"
	"path/filepath"
	"sync"
	"time"

	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type ArbitragerStorage struct {
	base.BaseStorage
	ExchangeName       string
	controller         *ArbitragerController
	Counter            int
	Instructions       []*Instruction
	MoveInstructions   []*MoveInstruction
	RotateInstructions []*RotateInstruction
	mutex              sync.Mutex
}

func SetupArbitragerStroage(arbitragerController *ArbitragerController) {
	if arbitragerController.Config == nil {
		zlog.Panicf("config is nil, please init config before storage for arbitrager")
		return
	}

	storage := &ArbitragerStorage{
		BaseStorage: base.BaseStorage{
			Controller: arbitragerController,
		},
		controller: arbitragerController,
	}
	// 紧接着赋值给 Storager，防止没有赋值前调用 Storager 相关的方法导致崩溃
	storage.Storager = storage
	if ok := storage.ReadFrom(arbitragerController.ConfigPath, arbitragerController.ID); !ok {
		storage.Instructions = []*Instruction{}
		storage.MoveInstructions = []*MoveInstruction{}
		storage.RotateInstructions = []*RotateInstruction{}
		storage.Assets = map[int64]float64{}
		storage.Counter = 1000
		storage.ExchangeName = arbitragerController.Config.ExchangeName
	} else {
		if storage.Assets == nil {
			storage.Assets = map[int64]float64{}
		}
		counter := storage.Counter
		now := time.Now()

		// 分别读取 Instruction 和 MoveInstruction 初始化 ArbitragerController.Instructions
		for _, instruction := range storage.Instructions {
			if !instruction.IsEnded() {
				instruction.dirty = true
				instruction.Report.EndTime = &now
			}
			if num, _, _, err := ParseInstructionID(instruction.ID); err != nil {
				zlog.Panicf("[%s] parse instruction id from storage error: %s", arbitragerController.ID, err)
			} else {
				if int(num) > counter {
					counter = int(num)
				}
			}
			instruction.arbitrager = arbitragerController
			arbitragerController.Instructions = append(arbitragerController.Instructions, instruction)
		}
		for _, instruction := range storage.MoveInstructions {
			if !instruction.IsEnded() {
				instruction.dirty = true
				instruction.Report.EndTime = &now
			}
			if num, _, _, err := ParseInstructionID(instruction.ID); err != nil {
				zlog.Panicf("[%s] parse instruction id from storage error: %s", arbitragerController.ID, err)
			} else {
				if int(num) > counter {
					counter = int(num)
				}
			}
			instruction.arbitrager = arbitragerController
			arbitragerController.Instructions = append(arbitragerController.Instructions, instruction)

		}
		for _, instruction := range storage.RotateInstructions {
			if !instruction.IsEnded() {
				instruction.dirty = true
				instruction.Report.EndTime = &now
			}
			if num, _, _, err := ParseInstructionID(instruction.ID); err != nil {
				zlog.Panicf("[%s] parse instruction id from storage error: %s", arbitragerController.ID, err)
			} else {
				if int(num) > counter {
					counter = int(num)
				}
			}
			instruction.arbitrager = arbitragerController
			arbitragerController.Instructions = append(arbitragerController.Instructions, instruction)

		}
		storage.Counter = counter
		storage.Save()
	}
	arbitragerController.storage = storage
}

func (this *ArbitragerStorage) ReadFrom(configPath, id string) bool {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".arbi_storage")
	f, _ := os.OpenFile(path, os.O_RDWR|os.O_CREATE, 0755)
	defer f.Close()
	file, err := io.ReadAll(f)
	if err != nil {
		this.controller.Errorf("[%s] read storage file error: %s", this.Controller.GetID(), err.Error())
		return false
	}
	this.controller.Debugf("local storage: \n%s", file)
	this.controller.Infof("local storage file loaded")
	err = json.Unmarshal(file, this)
	if err != nil {
		this.controller.Debugf("json.Unmarshal err: %#v", err)
		this.controller.Errorf("read json file error")
		return false
	}
	return true
}

// 清理 Order 中的扩展字段，仅保留需要的 keys，在持久化的清理函数中调用
// 如果新加了字段必须放到以下的白名单中，否则持久化中就会看不到
func CleanOrderExtStruct(order *exchange.Order) {
	order.CleanString([]string{ExtKeyStartTime, ExtKeyEndTime}, true)
}

// 写入本地存储到文件
func (this *ArbitragerStorage) SaveTo(configPath string, id string, overwrite bool) error {
	this.mutex.Lock()
	startTime := time.Now()
	defer func() {
		zlog.Infof("[%s] save to storage took %s", this.Controller.GetID(), time.Since(startTime))
		this.mutex.Unlock()
	}()

	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".arbi_storage")
	if _, err := os.Stat(path); !os.IsNotExist(err) && !overwrite {
		return fmt.Errorf("[%s] %s already exist, can not overwrite", this.Controller.GetID(), path)
	}

	// backup old storage file, if the old file is valid json
	oldData, err := os.ReadFile(path)
	if err != nil {
		zlog.Errorf("[%s] read old storage file error: %s", this.Controller.GetID(), err)
		return err
	}
	if gjson.Valid(string(oldData)) {
		backupPath := filepath.Join(configPath, id+".arbi_storage.bak")
		if err := utils.CopyFile(path, backupPath); err != nil {
			zlog.Errorf("[%s] backup old storage file error: %s", this.Controller.GetID(), err)
			return err
		}
	}

	// 将 Instruable 转换为 concrete 的 Instruction 和 MoveInstruction 存起来
	allOrders := []*exchange.Order{}
	for _, instructable := range this.controller.Instructions {
		for _, execution := range instructable.GetReport().Executions {
			for _, orders := range execution.BuyOrders {
				allOrders = append(allOrders, *orders...)
			}
			for _, orders := range execution.SellOrders {
				allOrders = append(allOrders, *orders...)
			}
			for _, orders := range execution.BNBOrders {
				allOrders = append(allOrders, *orders...)
			}
		}
		// 清理 Trades 和 ExtStruct 和其他不用的字段
		for _, order := range allOrders {
			order.Trades = []exchange.TradeHistory{}
			order.Type = exchange.UnknownOrderType
			order.Side = exchange.UnknownOrderSide
			order.Status = exchange.UnknownOrderStatus
			order.TimeInForce = exchange.UnknownTimeInForce
			order.CreateTime = nil
			order.UpdateTime = nil
			order.InstrumentType = exchange.UnknownInstrumentType
			CleanOrderExtStruct(order)
		}

		if instructable.GetDirection() == ArbiDirectionMove {
			instruction := instructable.(*MoveInstruction)
			exist := false
			for _, mi := range this.MoveInstructions {
				if mi.GetID() == instructable.GetID() {
					exist = true
				}
			}
			if !exist {
				this.MoveInstructions = append(this.MoveInstructions, instruction)
			}
			// migrate Durations，写入 DurationStats，不再写入 Durations
			instruction.GetFromExecution().Durations = []*ExecutionDuration{}
			instruction.GetToExecution().Durations = []*ExecutionDuration{}
		} else if instructable.GetDirection() == ArbiDirectionRotate {
			instruction := instructable.(*RotateInstruction)
			exist := false
			for _, mi := range this.RotateInstructions {
				if mi.GetID() == instructable.GetID() {
					exist = true
				}
			}
			if !exist {
				this.RotateInstructions = append(this.RotateInstructions, instruction)
			}
			// migrate Durations，写入 DurationStats，不再写入 Durations
			instruction.GetFromExecution().Durations = []*ExecutionDuration{}
			instruction.GetToExecution().Durations = []*ExecutionDuration{}
		} else if instructable.GetDirection() == ArbiDirectionOpen || instructable.GetDirection() == ArbiDirectionClose {
			instruction := instructable.(*Instruction)
			exist := false
			for _, i := range this.Instructions {
				if i.GetID() == instructable.GetID() {
					exist = true
				}
			}
			if !exist {
				this.Instructions = append(this.Instructions, instruction)
			}
			// migrate Durations，写入 DurationStats，不再写入 Durations
			instruction.GetExecution().Durations = []*ExecutionDuration{}
		}
		// 清理遗留的 Execution，migrate 完成后删除
		instructable.MigrateClearData()
	}

	// Wrap marshal in a single recover
	// marshal 的过程中，如果其中的子结构中的 slices 可能发生变化，导致 crash
	// 因此，需要手工 recover 忽略错误
	data, err := func() (data []byte, err error) {
		defer func() {
			if r := recover(); r != nil {
				err = fmt.Errorf("[%s] panic during storage marshal: %v", this.Controller.GetID(), r)
			}
		}()

		data, err = json.MarshalIndent(this, "", "    ")
		if err != nil {
			return nil, fmt.Errorf("marshal error: %s", err)
		}
		return data, nil
	}()

	if err != nil {
		zlog.Errorf("[%s] marshal storage error: %s", this.Controller.GetID(), err)
		return err
	}

	if err := os.WriteFile(path, data, 0755); err != nil {
		zlog.Errorf("[%s] save storage to file, error: %s", this.Controller.GetID(), err)
		return err
	}
	return nil
}

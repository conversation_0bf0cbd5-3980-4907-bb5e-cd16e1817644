package arbitrage

import (
	"fmt"
	"math"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/wizhodl/quanter/base"
	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/binance"
	"github.com/wizhodl/quanter/exchange/bybit"
	"github.com/wizhodl/quanter/exchange/hyperliquid"
	"github.com/wizhodl/quanter/exchange/okex"
	"github.com/wizhodl/quanter/messenger"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"

	"github.com/stevedomin/termtable"
)

type ArbitragerController struct {
	base.BaseController

	Executer ArbitrageExchangeExecuter
	Config   *ArbitragerControllerConfig

	pauseStartAt *time.Time
	pauseMinutes int
	storage      *ArbitragerStorage // 套利机的内部缓存

	Instructions      []Instructable `json:"-"` // 交易指令
	instructionsMutex sync.Mutex
}

func NewArbitragerController(id string, debug bool, configPath, commitHash, buildTime, releaseBinaryDirPath, logDirPath string, parentMessenger *messenger.SlackMessenger, managerID string) (*ArbitragerController, error) {

	arbitrager := &ArbitragerController{
		BaseController: base.BaseController{
			BaseResponder: command.NewBaseResponder(debug, commitHash, buildTime, id, nil, ""),
			ID:            id,
			RefID:         exchange.NewRandomID(),
			ConfigPath:    configPath,
		},
	}
	arbitrager.Controllable = arbitrager
	arbitrager.Setup(debug, parentMessenger, managerID)

	config, err := NewArbitragerControllerConfig(arbitrager)
	if err != nil {
		return nil, err
	}
	arbitrager.Config = config
	// setup storage 时可能用到 config 的值，始终在 config 之后初始化 storage
	SetupArbitragerStroage(arbitrager)

	if logDirPath != "" {
		config.LogDirPath = logDirPath
	}

	arbitrager.AddCommands([]command.Commander{
		NewStatusArbitragerCommand(arbitrager),
		NewParametersArbitragerCommand(arbitrager),
		NewMarginRatioControllerCommand(arbitrager),
		NewArbiArbitragerCommand(arbitrager),
		NewArbiForceArbitragerCommand(arbitrager),
		NewCloseArbitragerCommand(arbitrager),
		NewCloseForceArbitragerCommand(arbitrager),
		NewRotateArbitragerCommand(arbitrager),
		NewRotateForceArbitragerCommand(arbitrager),
		NewMoveArbitragerCommand(arbitrager),
		NewMoveForceArbitragerCommand(arbitrager),
		NewCloseAllArbitragerCommand(arbitrager),
		NewCancelArbitragerCommand(arbitrager),
		NewPauseArbitragerCommand(arbitrager),
		NewPositionArbitragerCommand(arbitrager),
		cmds.NewHoldingsCommand(arbitrager),
		cmds.NewAssetsCommand(arbitrager, arbitrager.storage),
		NewReportArbitragerCommand(arbitrager),
		NewOrdersArbitragerCommand(arbitrager),
		NewTransferArbitragerCommand(arbitrager),
		NewBatchCloseArbitragerCommand(arbitrager),
		NewUpdateBalanceArbitragerCommand(arbitrager),
		NewCleanupInstructionsArbitragerCommand(arbitrager),
		NewSetInstructionConfigArbitragerCommand(arbitrager),
		NewBasisRankArbitragerCommand(arbitrager),
		NewFundingHistoryArbitragerCommand(arbitrager),
		NewSetCoinsArbitragerCommand(arbitrager),
		NewSaveConfigArbitragerCommand(arbitrager),
		NewDeleteConfigSaveArbitragerCommand(arbitrager),
		cmds.NewPagerCommand(),
		NewSetConfigArbitragerCommand(arbitrager),
		cmds.NewMuteCommand(),
		cmds.NewDebugCommand(),
		cmds.NewLogCommand(arbitrager.Config.LogDirPath, arbitrager.GetLogFilename()),
		cmds.NewDownloadLogCommand(arbitrager.Config.LogDirPath, arbitrager.GetLogFilename()),
		cmds.NewDownloadStorageCommand(arbitrager.ConfigPath, fmt.Sprintf("%s.arbi_storage", arbitrager.ID)),
		cmds.NewStackTraceCommand(),
		cmds.NewDeleteErrorsCommand(),
	})

	arbitrager.SetAdditionalHelpText("`SymbolPair` 期现套利时格式为 `FutureCode`；期期套利格式为 `FutureCodeA~FutureCodeB`，开仓时： ~ 左边的 A 是买入的品种，右边是卖出的品种，平仓时只需要用和开仓相同的 Pair 即可。")

	if arbitrager.Standalone() {
		// 注册 commander 要在
		// 根据命令行参数更新 config 中的部分字段
		if releaseBinaryDirPath != "" {
			config.ReleaseBinaryDirPath = releaseBinaryDirPath
		}
		// 初始化时检查 ReleaseBinaryDir 是否可以写，以免后续 releases 相关命令每次都要检查
		if utils.CheckReleaseBinaryDirPath(arbitrager.Config.ReleaseBinaryDirPath) {
			arbitrager.AddCommands([]command.Commander{
				cmds.NewReleaseCommand(arbitrager.Config.ReleaseBinaryDirPath, arbitrager.ID),
				cmds.NewListVersionCommand(arbitrager.Config.ReleaseBinaryDirPath, arbitrager.ID),
				cmds.NewUseVersionCommand(arbitrager.Config.ReleaseBinaryDirPath, arbitrager.ID),
			})
		}
	}

	// 尝试以 Debug 模式启动，如果不能以 Debug 模式启动，会要求以 .launch 模式启动
	debugLaunched := arbitrager.TryLaunchWithDebug()
	if debugLaunched {
		go arbitrager.InitArbitrager()
	} else {
		arbitrager.AddCommands([]command.Commander{NewLaunchArbitragerCommand(arbitrager)})
	}

	// 只能全部的 commands 都加完了才能调用 SetSensitiveCommands
	arbitrager.Messenger.SetSensitiveCommands(arbitrager.GetCommandProcessor().SensitiveCommands)

	return arbitrager, nil
}

func (this *ArbitragerController) FutureInstrumentType() exchange.InstrumentType {
	// bn, ok 支持 币本位 和 U 本位合约
	if this.Config.FutureInstrumentType == exchange.USDXMarginedFutures {
		return exchange.USDXMarginedFutures
	}
	return exchange.CoinMarginedFutures
}

func (this *ArbitragerController) IsCrossMode() bool {
	if this.IsExchange(exchange.Binance) && this.FutureInstrumentType() != exchange.USDXMarginedFutures {
		// BN 仅U合约支持联合保证金模式, 其他即使设置了也不生效
		return false
	}
	return this.Config.MarginMode.IsCross()
}

func (this *ArbitragerController) InitAPI(apiSecret, withdrawApiSecret secrets.SecretString) error {
	opts := &exchange.Options{
		ApiKey:            this.Config.ApiKey,
		ApiSecret:         apiSecret,
		WithdrawApiKey:    this.Config.WithdrawApiKey,
		WithdrawApiSecret: withdrawApiSecret,
		IsTestnet:         this.Config.IsTestnet,
		ProxyUrl:          this.Config.ProxyUrl,
		ControllerID:      this.ID,
		DataPath:          this.ConfigPath,
	}

	useCrossMode := this.Config.MarginMode.IsCross()

	if this.IsExchange(exchange.OKEx) {
		client, err := okex.NewOKEx(opts)
		if err != nil {
			return fmt.Errorf("初始化 OKEx 失败，error: %s", err)
		}

		if cfg, err := client.GetAccountConfig(this.FutureInstrumentType()); err != nil {
			return fmt.Errorf("初始化 OKEx 失败，获取用户配置错误：%s", err)
		} else {
			if useCrossMode {
				if cfg.MarginMode != exchange.AccountMarginModeCross {
					return fmt.Errorf("账户模式错误，请设置为跨币种保证金模式")
				}
			} else if cfg.MarginMode != exchange.AccountMarginModeIsolated {
				return fmt.Errorf("账户模式错误，请设置为单币种保证金模式")
			}

			if cfg.DualPositionSide {
				if err := client.SetDualPositionSide(this.FutureInstrumentType(), false); err != nil {
					// 不要返回错误，因为如果有仓位，无法设置仓位模式
					this.Warnf("error setting position mode: %s", err)
					this.WarnMsgf("设置持仓模式错误: %s", err)
				}
			}
		}
		this.Exchange = client

	} else if this.IsExchange(exchange.Binance) {
		client, err := binance.NewBinance(opts)
		if err != nil {
			return fmt.Errorf("初始化 Binance 失败，error: %s", err)
		}

		if err := client.SetDualPositionSide(this.FutureInstrumentType(), false); err != nil {
			if strings.Contains(err.Error(), "-4059") {
				this.Debugf("position side is dual, no need to set.")
			} else {
				return fmt.Errorf("账户设置单向持仓模式错误，请检测 API 是否正确或已有持仓或挂单: %s", err)
			}
		}

		cfg, err := client.GetAccountConfig(this.FutureInstrumentType())
		if err != nil {
			return fmt.Errorf("交易所初始化失败，获取用户配置错误：%s", err)
		}
		if useCrossMode {
			// 目前仅U合约支持联合保证金模式
			if this.FutureInstrumentType() == exchange.USDXMarginedFutures && cfg.MarginMode != exchange.AccountMarginModeCross {
				if err := client.SetAccountMarginMode(this.FutureInstrumentType(), exchange.AccountMarginModeCross); err != nil {
					// 可能会因为有些合约是逐仓模式无法修改，手动修改时可同时设置
					return fmt.Errorf("账户设置联合保证金模式错误，请尝试手动修改: %s", err)
				}
			} else if this.FutureInstrumentType() != exchange.USDXMarginedFutures {
				this.WarnMsgf("Binance 仅 U 本位合约支持跨币种模式, 当前设置 CrossMode 无效")
			}
		} else if cfg.MarginMode != exchange.AccountMarginModeIsolated {
			if err := client.SetAccountMarginMode(this.FutureInstrumentType(), exchange.AccountMarginModeIsolated); err != nil {
				return fmt.Errorf("账户设置单币保证金模式错误: %s", err)
			}
		}

		this.Exchange = client

	} else if this.Config.ExchangeName == exchange.Bybit {
		client, err := bybit.NewBybit(opts)
		if err != nil {
			return fmt.Errorf("初始化 BYBIT 失败，error: %s", err)
		}
		// 检查并设置保证金模式
		if config, err := client.GetAccountConfig(this.FutureInstrumentType()); err != nil {
			return fmt.Errorf("初始化 BYBIT 失败，获取用户配置错误：%s", err)
		} else if useCrossMode && config.MarginMode != exchange.AccountMarginModeCross {
			err := client.SetAccountMarginMode(this.FutureInstrumentType(), exchange.AccountMarginModeCross)
			if err != nil {
				return fmt.Errorf("设置全仓保证金模式失败：%s", err)
			}
		} else if !useCrossMode && config.MarginMode != exchange.AccountMarginModeIsolated {
			err := client.SetAccountMarginMode(this.FutureInstrumentType(), exchange.AccountMarginModeIsolated)
			if err != nil {
				return fmt.Errorf("设置逐仓保证金模式失败：%s", err)
			}
		}
		this.Exchange = client

	} else if this.Config.ExchangeName == exchange.Hyperliquid {
		client, err := hyperliquid.NewHyperliquid(opts)
		if err != nil {
			return fmt.Errorf("初始化 Hyperliquid 失败，error: %s", err)
		}
		if _, err := client.GetAccountBalances(this.FutureInstrumentType()); err != nil {
			return fmt.Errorf("获取余额失败，请检查 API 是否正确. error: %s", err)
		}
		this.Exchange = client

	} else {
		return fmt.Errorf("unsuported exchange (%s)", this.Config.ExchangeName)
	}

	this.Exchange.SetHttpDebug(this.Debug)
	go this.Exchange.CheckPriceTriggerTimeLoop()

	if executer, err := NewExecuter(this); err != nil {
		this.Errorf("init executer error: %s", err.Error())
	} else {
		this.Executer = executer
	}
	return nil
}

// debug 状态运行 和 定时任务，有多次运行防呆机制
func (this *ArbitragerController) Run() {
	tasks := []base.CronTask{
		{
			Spec: "1 12 * * *",
			Cmd: func() {
				this.storage.RecordAssets()
				this.SendStatus(InstructionStatusAll, false, false, "")
				this.Exchange.CacheInstruments(true)
				this.CleanupInstructions(48)
			},
		},

		{
			Spec: "@every 5m",
			Cmd: func() {
				this.CheckMarginRatio(0, false)
			},
		},
	}
	this.BaseController.Run(tasks)
}

func (this *ArbitragerController) CheckMarginRatio(requestPosition float64, print bool) {
	if this.Exchange == nil {
		return
	}

	// 仅跨币种模式检查
	if !this.IsCrossMode() {
		if this.IsExchange(exchange.Binance) && this.FutureInstrumentType() != exchange.USDXMarginedFutures && this.Config.MarginMode.IsCross() {
			this.WarnMsgf("Binance 仅 U 本位合约支持跨币种模式, 当前设置 MarginMode 无效")
		}
		return
	}

	// 总资产价值（in usd）/ 头寸价值（in usd），不能低于 MinMarginRatio
	minMarginRatio := this.Config.MinMarginRatio
	if minMarginRatio == 0 {
		minMarginRatio = 1
	}

	accountWorth := 0.0
	if this.IsExchange(exchange.Binance) && this.FutureInstrumentType() == exchange.USDXMarginedFutures {
		// Binance 只有 U 本位合约支持跨币种模式，并且仅 U 本位合约账号资产计算联合保证
		worth, err := this.Exchange.GetAccountWorth(this.RefID, []exchange.InstrumentType{this.FutureInstrumentType()}, base.AccountWorthCacheTime, func(coin string) bool {
			return this.CheckSymbolPrefixAllowed(coin)
		})
		if err != nil {
			this.ErrorMsgf("get account worth error: %v", err)
			return
		}
		accountWorth = worth
	} else {
		worth, err := this.GetAccountWorth(false)
		if err != nil {
			this.ErrorMsgf("get account worth error: %v", err)
			return
		}
		accountWorth = worth
	}

	positionWorth := this.GetAccountPositionWorth()
	positionWorth += requestPosition
	marginRatio := 0.0
	if positionWorth > 0 {
		marginRatio = accountWorth / positionWorth
		if marginRatio < minMarginRatio {
			this.WarnMsgf("当前账号总资产价值(%.2f) / 头寸价值(%.2f) < MinMarginRatio(%v)", accountWorth, positionWorth, minMarginRatio)
		}
	}

	if !print {
		return
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"Asset", "Required Margin", "Min Margin", "Acutal Margin", "Ratio", "Delta"})
	ratio := "-"
	minMargin := positionWorth * minMarginRatio
	if minMargin > 0 {
		ratio = fmt.Sprintf("%.2f%%", accountWorth/minMargin*100)
	}
	t.AddRow([]string{
		"USD",
		fmt.Sprintf("%.2f", positionWorth),
		fmt.Sprintf("%.2f", minMargin),
		fmt.Sprintf("%.2f", accountWorth),
		ratio,
		fmt.Sprintf("%.2f", accountWorth-minMargin),
	})

	this.SendMsgf("```%s```", t.Render())
}

func (this *ArbitragerController) GetAccountPositionWorth() (valueInUSD float64) {
	positions, err := this.Exchange.GetPositions(this.FutureInstrumentType(), "", false)
	if err != nil {
		this.ErrorMsgf("get positions error: %v", err)
		return
	}
	for _, p := range positions {
		price := p.LastPrice
		if price == 0 {
			price = p.MarkPrice
		}
		qty := this.FutureQtyToSpotQty(p.Symbol, math.Abs(p.Qty), price)
		valueInUSD += price * qty
	}
	return
}

func (this *ArbitragerController) SendStatus(status InstructionStatus, isDetail bool, isMore bool, instructionID string) {
	instructions := FilterInstructions(this.Instructions, status, nil, &instructionID)
	if len(instructions) > 0 {
		statusStr := RenderStatusForInstructions(instructions, isDetail)
		if isDetail {
			this.SendFileMessage("套利机状态", statusStr, "")
		} else {
			if isMore {
				this.SendFileMessage("套利机状态", statusStr, "")
			} else {
				this.SendMsgf("套利机状态：\n```%s```", statusStr)
			}
		}
	} else {
		this.SendMsgf("没有查询到任何状态为 %s 的指令。", status)
	}
}

func RenderStatusForInstructions(instructions []Instructable, isDetail bool) (status string) {
	status = ""
	if len(instructions) > 0 {
		t := termtable.NewTable(nil, &termtable.TableOptions{
			Padding:      2,
			UseSeparator: true,
		})
		if isDetail {
			t.SetHeader(DetailInstructionStatusHeader)
		} else {
			t.SetHeader(SimpleInstructionStatusHeader)
		}
		// 按创建时间倒序排序
		sort.Slice(instructions, func(i, j int) bool {
			diff := instructions[i].GetReport().CreateTime.Sub(*instructions[j].GetReport().CreateTime)
			return diff > time.Duration(0)
		})
		for _, instruction := range instructions {
			t.AddRow(instruction.GetStatusRow(isDetail))
		}
		status = t.Render()
	}
	return status
}

func RenderRequestForInstructions(instructions []Instructable, isDetail bool) (request string) {
	request = ""
	if len(instructions) > 0 {
		t := termtable.NewTable(nil, &termtable.TableOptions{
			Padding:      2,
			UseSeparator: true,
		})
		if isDetail {
			t.SetHeader(DetailInstructionRequestHeader)
		} else {
			t.SetHeader(SimpleInstructionRequestHeader)
		}

		for _, instruction := range instructions {
			t.AddRow(instruction.GetRequestRow(isDetail))
		}
		request = t.Render()
	}
	return request
}

// 同一个币种下不允许有同时运行的指令，如果想查询是否所有币种下有正在运行的指令
// coins 可以是逗号分割的 coin
func (this *ArbitragerController) CheckRunningInstruction(coins string) bool {
	running := false
	for _, instruction := range this.Instructions {
		if instruction.IsEnded() || instruction.IsDirty() {
			continue
		}
		if coins != "" {
			for _, fc := range instruction.GetRelatedSymbolCodes() {
				if strings.Contains(coins, fc.Coin()) {
					running = true
					break
				}
			}
		} else {
			running = true
			break
		}
	}
	return running
}

// FilterInstructions 过滤指令列表，如果参数设为 nil ，表示不按此参数过滤；如果设置了参数不为 nil，那么所有条件做”AND“查询
// instructionIDs 可以是多个 instructionID 逗号拼接的字符串
// 因为这个函数可能被其他包调用，所以不作为实例方法
func FilterInstructions(instructions []Instructable, status InstructionStatus, symbolCode *exchange.SymbolCode, instructionIDs *string) (result []Instructable) {
	result = []Instructable{}
	flags := 0       // 三个条件设了哪些，就会把对应的 flag 或操作上去
	stFlag := 1 << 1 // 设了 status 参数
	scFlag := 1 << 2 // 设了 symbolCode 参数
	iiFlag := 1 << 3 // 设了 instructionID 参数

	for _, instruction := range instructions {
		flagResults := map[int]bool{}
		if status != InstructionStatusAll {
			flags |= stFlag // status 不为全部，将 status 参数对应的 flag 或操作上去
			if status == InstructionStatusRunning && !instruction.IsEnded() && !instruction.IsDirty() {
				flagResults[stFlag] = true // 满足 running 的条件
			}
			if status == InstructionStatusEnded && instruction.IsEnded() && instruction.GetFinishProgress() > 0.0 {
				flagResults[stFlag] = true // 满足 ended 的条件
			}
		}

		if symbolCode != nil {
			flags |= scFlag
			if symbolCode.IsWildcard() {
				for _, fc := range instruction.GetRelatedSymbolCodes() {
					if strings.EqualFold(symbolCode.Coin(), fc.Coin()) {
						flagResults[scFlag] = true
					}
				}

			} else {
				for _, fc := range instruction.GetRelatedSymbolCodes() {
					if strings.EqualFold(symbolCode.Code, fc.Code) {
						flagResults[scFlag] = true
					}
				}
			}
		}

		if instructionIDs != nil && *instructionIDs != "" {
			flags |= iiFlag
			if strings.Contains(*instructionIDs, instruction.GetID()) {
				flagResults[iiFlag] = true
			}
		}

		// 三个条件和运算后的结果
		combinedResult := true
		// 遍历三个条件的 flag，看哪些被设上了；
		// 如果给了某条件，但是其结果是 false，combinedResult 就会是 false；
		// 只有在所有条件的结果是 true 的情况下，combinedResult 才会是 true，即同时满足多个条件
		for _, flag := range []int{stFlag, scFlag, iiFlag} {
			if flags&flag == flag {
				combinedResult = combinedResult && flagResults[flag]
			}
		}

		if combinedResult {
			result = append(result, instruction)
		}
	}
	return result
}

func (this *ArbitragerController) Close() {
	this.BaseController.Close(func() {
		this.CancelAllInstructions()
	})
}

func (this *ArbitragerController) CancelAllInstructions() {
	for _, instruction := range this.Instructions {
		instruction.Cancel(CancelReasonSystemExit)
	}
}

func (this *ArbitragerController) ProcessInstruction(instruction Instructable) {
	if this.CheckInstructionExist(instruction) {
		this.Errorf("instruction %s already been processed", instruction.GetID())
		return
	}
	this.instructionsMutex.Lock()
	this.Instructions = append(this.Instructions, instruction)
	this.instructionsMutex.Unlock()

	if err := this.storage.Save(); err != nil {
		this.ErrorMsgf("写本地存储出错：%s", err)
	}
	go this._processInstructions()
}

func (this *ArbitragerController) _processInstructions() {
	for _, instruction := range FilterInstructions(this.Instructions, InstructionStatusRunning, nil, nil) {
		// TODO: 用互斥锁保护起来
		// 如果指令没有执行，则执行指令
		if !instruction.IsStarted() {
			instruction.Execute()
		}
	}
}

func (this *ArbitragerController) SaveConfigsTo(arbiID string) error {
	if err := this.Config.SaveTo(this.ConfigPath, arbiID, false); err != nil {
		return err
	}
	return nil
}

func (this *ArbitragerController) SaveStorageTo(arbiID string) error {
	if err := this.storage.SaveTo(this.ConfigPath, arbiID, true); err != nil {
		return err
	}
	return nil
}

// 停止运行，并删除套利机对应的配置文件
// stopAll 和 launched 设为 false
func (this *ArbitragerController) DangerousDelete() {
	this.Close()
	this.Config.Delete()
}

func ParseNumOrPercentage(numOrPercentage string, percentOnly bool, allowNegative bool) (num float64, percent float64, er error) {
	percent = 0.0
	num = 0.0
	if result, isPercent, err := utils.ParseFloatOrPercentage(numOrPercentage, percentOnly, allowNegative); err == nil {
		if isPercent {
			percent = result
		} else {
			num = result
		}
	} else {
		er = err
	}
	return num, percent, er
}

func (this *ArbitragerController) GetPositionRows(spotOrFuture string, assets string) (rows [][]string, er error) {
	rows = [][]string{}

	if strings.Contains(strings.ToLower(spotOrFuture), "spot") {
		if bs, err := this.Exchange.GetAccountBalances(exchange.Spot); err != nil {
			return rows, fmt.Errorf("list spot positions error: %s", err)
		} else {
			for _, b := range bs {
				if b.Total == 0 {
					continue
				}
				if this.CheckSymbolPrefixAllowed(b.Currency) {
					if assets == "" || utils.CSVContains(assets, b.Currency, ",") {
						rows = append(rows, []string{b.Currency, "Spot", fmt.Sprintf("%.3f", b.Total), fmt.Sprintf("%.3f", b.Available), "-", "-"})
					}
				}
			}
		}
	}

	// 合约余额是否单独获取
	needFutureBalance := false
	if strings.Contains(strings.ToLower(spotOrFuture), "future") && this.IsExchange(exchange.Binance) {
		needFutureBalance = true
	}

	if needFutureBalance {
		if bs, err := this.Exchange.GetAccountBalances(this.FutureInstrumentType()); err != nil {
			return rows, fmt.Errorf("list future balance error: %s", err)
		} else {
			for _, b := range bs {
				if b.Total == 0 {
					continue
				}
				if this.CheckSymbolPrefixAllowed(b.Currency) {
					if assets == "" || utils.CSVContains(assets, b.Currency, ",") {
						rows = append(rows, []string{b.Currency, string(this.FutureInstrumentType()), fmt.Sprintf("%.3f", b.Total), fmt.Sprintf("%.3f", b.Available), "-", "-"})
					}
				}
			}
		}
	}

	if strings.Contains(strings.ToLower(spotOrFuture), "future") {
		if ps, err := this.Exchange.GetPositions(this.FutureInstrumentType(), "", false); err != nil {
			return rows, fmt.Errorf("list future positions error: %s", err)
		} else {
			uSymbol := this.Config.USDXSymbol
			for _, p := range ps {
				if _, symbolCode, err := this.Exchange.TranslateFutureSymbol(this.FutureInstrumentType(), p.Symbol, uSymbol); err != nil {
					er = fmt.Errorf("translate future symbol failed (%s), error: %s", p.Symbol, err)
					return
				} else {
					if this.CheckSymbolPrefixAllowed(symbolCode.Coin()) {
						if assets == "" || utils.CSVContains(assets, symbolCode.Coin(), ",") {
							rows = append(rows, []string{
								p.Symbol,
								"Future",
								this.Exchange.FormatQty(this.FutureInstrumentType(), p.Symbol, p.Qty),
								this.Exchange.FormatQty(this.FutureInstrumentType(), p.Symbol, p.Available),
								fmt.Sprintf("%.2f", p.EntryPrice),
								fmt.Sprintf("%.4f", p.UnrealisedPNL),
							})
						}
					}
				}
			}
		}
	}
	return rows, nil
}

func (this *ArbitragerController) NewInstructionID(direction ArbiDirection, isForce bool) string {
	prefix := ""
	if direction.IsOpen() {
		prefix = "A"
	} else if direction.IsClose() {
		prefix = "C"
	} else if direction.IsMove() {
		prefix = "M"
	} else if direction.IsRotate() {
		prefix = "R"
	} else {
		zlog.Panicf("[%s] direction %s not supported", this.ID, direction)
	}
	if isForce {
		prefix += "F"
	}
	return fmt.Sprintf("%s%d", prefix, this.increaseCounter())
}

func (this *ArbitragerController) increaseCounter() int {
	this.storage.Counter += 1
	return this.storage.Counter
}

func (this *ArbitragerController) CheckInstructionExist(instruction Instructable) bool {
	for _, instr := range this.Instructions {
		if strings.EqualFold(instr.GetID(), instruction.GetID()) {
			return true
		}
	}
	return false
}

func (this *ArbitragerController) CleanupInstructions(hours int) (cleanInstructionCount int, cleanCanceledOrderCount int) {
	before := time.Duration(time.Duration(hours) * time.Hour)
	this.instructionsMutex.Lock()
	newInstructions := []Instructable{}

	for _, instruction := range this.Instructions {
		since := time.Duration(time.Millisecond * 0)
		if instruction.GetReport().EndTime != nil {
			since = time.Since(*instruction.GetReport().EndTime)
		}
		if (instruction.IsEnded() && instruction.GetFinishProgress() == 0 && since > before) || (instruction.IsDirty() && instruction.GetFinishProgress() == 0) {
			instruction.DeleteConfigSnapshot()
			cleanInstructionCount += 1
			this.Debugf("clean instruction %s, ended %v, progress %.5f, dirty %v, since %s", instruction.GetID(), instruction.IsEnded(), instruction.GetFinishProgress(), instruction.IsDirty(), since)
		} else {
			newInstructions = append(newInstructions, instruction)
			this.Debugf("do not cleanup %s, ended %v, progress %.5f, dirty %v, since %s", instruction.GetID(), instruction.IsEnded(), instruction.GetFinishProgress(), instruction.IsDirty(), since)
		}
	}
	this.Instructions = newInstructions
	cleanCanceledOrderCount = this.cleanupFinishedCanceledOrders()
	this.instructionsMutex.Unlock()
	return
}

func (this *ArbitragerController) IsPaused() bool {
	if this.pauseStartAt == nil {
		return false
	}
	endAt := this.pauseStartAt.Add(time.Duration(time.Duration(this.pauseMinutes) * time.Minute))
	return time.Now().After(*this.pauseStartAt) && time.Now().Before(endAt)
}

func (this *ArbitragerController) cleanupFinishedCanceledOrders() int {
	cleanCount := 0
	for _, instruction := range this.Instructions {
		if instruction.IsEnded() {
			for _, execution := range instruction.GetReport().Executions {
				orderIDs := []string{}
				for _, orders := range execution.BuyOrders {
					for _, order := range *orders {
						if order.ExecPrice == 0 {
							orderIDs = append(orderIDs, order.OrderID)
						}
					}
				}
				for _, orders := range execution.SellOrders {
					for _, order := range *orders {
						if order.ExecPrice == 0 {
							orderIDs = append(orderIDs, order.OrderID)
						}
					}
				}
				for _, orders := range execution.BNBOrders {
					for _, order := range *orders {
						if order.ExecPrice == 0 {
							orderIDs = append(orderIDs, order.OrderID)
						}
					}
				}
				removedIDs := execution.removeOrderByIDs(orderIDs)
				cleanCount += len(removedIDs)
				this.Debugf("clean up canceled orders for finished instruction (%s): %v, %v", instruction.GetID(), orderIDs, removedIDs)
			}
		}
	}
	return cleanCount
}

func (this *ArbitragerController) GetInstrumentTypes() []exchange.InstrumentType {
	return []exchange.InstrumentType{exchange.Spot, this.FutureInstrumentType()}
}

func (this *ArbitragerController) GetReviewRows() [][]string {
	return this.Config.GetArbitragerConfigRows()
}

func (this *ArbitragerController) GetAllSymbolCodes() (codes []*exchange.SymbolCode, er error) {
	positions, err := this.Exchange.GetPositions(this.FutureInstrumentType(), "", false)
	if err != nil {
		er = fmt.Errorf("get positions failed, error: %s", err)
		return
	} else {
		uSymbol := this.Config.USDXSymbol
		for _, pos := range positions {
			futureSymbol := pos.Symbol
			_, futureCode, err := this.Exchange.TranslateFutureSymbol(this.FutureInstrumentType(), futureSymbol, uSymbol)
			if err != nil {
				// 如果不在允许品种列表中，是正常的；此外报错返回
				if !strings.Contains(err.Error(), "invalid future code") {
					er = fmt.Errorf("translate SymbolCode failed (%s), error：%s", futureSymbol, err)
					return
				}
			}
			codes = append(codes, futureCode)
		}
	}
	return
}

func (this *ArbitragerController) CheckValidSymbolCode(futureCode string) bool {
	prefix, _, valid := exchange.CheckValidSymbolCode(futureCode)
	if valid {
		if prefix != "" {
			return this.CheckSymbolPrefixAllowed(prefix)
		} else {
			return true
		}

	} else {
		return false
	}
}

func (this *ArbitragerController) GetBaseConfig() *baseconfig.BaseConfig {
	return &this.Config.BaseConfig
}

func (this *ArbitragerController) SaveConfig(configStr string) (correctedConfigStr string, er error) {
	config := this.Config
	if configPairs, _correctedConfigStr, err := baseconfig.SetConfigWithString(config, configStr); err != nil {
		this.ErrorMsgf("设置配置错误：%s", err)
		return "", err
	} else {
		correctedConfigStr = _correctedConfigStr
		for _, pair := range configPairs {
			fieldName := pair.Field
			valueStr := pair.Value

			if strings.EqualFold(fieldName, "EnableRealtimePrice") {
				enable := strings.EqualFold(valueStr, "true")
				this.Exchange.SetEnableRealtimePrice(enable)
			}

			if strings.EqualFold(fieldName, "MarginMode") {
				if !this.SetAccountMarginMode(this.FutureInstrumentType(), this.Config.MarginMode) {
					this.ErrorMsgf("设置账户模式错误，请手动设置保证金模式")
					return "", err
				}
			}
		}
		this.Config.Save()
	}
	return correctedConfigStr, nil
}

func (this *ArbitragerController) GetStorage() base.Storager {
	return this.storage
}

func (this *ArbitragerController) Qty2Size(symbol string, price float64, qty float64) (size float64) {
	if size, err := this.Exchange.Qty2Size(this.FutureInstrumentType(), symbol, price, qty); err != nil {
		this.ErrorMsgf("convert qty 2 size error: %s", err)
		return 0
	} else {
		return size
	}
}

func (this *ArbitragerController) Size2Qty(symbol string, price float64, size float64) (qty float64) {
	if qty, err := this.Exchange.Size2Qty(this.FutureInstrumentType(), symbol, price, size); err != nil {
		this.ErrorMsgf("convert size 2 qty error: %s", err)
		return 0
	} else {
		return qty
	}
}

func (this *ArbitragerController) CalcPrice(symbol string, qty float64, size float64) (price float64) {
	if price, err := this.Exchange.CalcPrice(this.FutureInstrumentType(), symbol, qty, size); err != nil {
		this.ErrorMsgf("convert qty and size 2 price error: %s", err)
		return 0
	} else {
		return price
	}
}

func (this *ArbitragerController) SpotQtyToFutureQty(symbol string, spotQty, price float64) float64 {
	size := 0.0
	switch this.FutureInstrumentType() {
	case exchange.CoinMarginedFutures:
		size = spotQty
	case exchange.USDXMarginedFutures:
		size = spotQty * price
	default:
		this.ErrorMsgf("spot to future qty err, unsuported future type: %s", this.FutureInstrumentType())
		return 0
	}
	return this.Size2Qty(symbol, price, size)
}

func (this *ArbitragerController) FutureQtyToSpotQty(symbol string, futureQty, price float64) float64 {
	switch this.FutureInstrumentType() {
	case exchange.CoinMarginedFutures:
		return this.Qty2Size(symbol, price, futureQty)
	case exchange.USDXMarginedFutures:
		if price == 0 {
			return 0
		}
		return this.Qty2Size(symbol, price, futureQty) / price
	default:
		this.ErrorMsgf("future to spot qty err, unsuported future type: %s", this.FutureInstrumentType())
		return 0
	}
}

// TODO: implement
func (this *ArbitragerController) GetAvailableQty(instrumentType exchange.InstrumentType, symbol string) (qty float64) {
	return 0
}

func (this *ArbitragerController) UpdateBalance(isForce bool) error {
	return nil
}

func (this *ArbitragerController) CheckSingleSide() bool {
	return false
}

// 原 RenderBook 函数
func (this *ArbitragerController) RenderInstructions(futureCodeStr string) string {
	return ""
}

func (this *ArbitragerController) GetEstimatedBNB() float64 {
	return 0
}

func (this *ArbitragerController) GetInstructionRows(futureCodeStr string) [][]string {
	return [][]string{}
}

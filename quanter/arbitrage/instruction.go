package arbitrage

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strings"
	"sync"
	"time"

	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"

	"github.com/stevedomin/termtable"
)

type Instructable interface {
	Execute()
	GetStatusRow(isDetail bool) (row []string)
	RenderStatus(isDetail bool) (status string)
	GetRequestRow(isDetail bool) (row []string)
	RenderRequest() string
	IsEnded() bool
	IsDirty() bool
	GetRelatedSymbolCodes() []*exchange.SymbolCode
	// 获取指令中的平仓操作的代号，没有平仓操作的返回 nil；比如：Arbi 指令中无平仓操作，所以返回 nil；Move 指令中有平仓操作，返回 FromPair 中的 SymbolCode
	// 这个接口主要是为了提供给 BatchClose 指令使用，用于自动取消之前的平仓指令；如果没有这个接口，自动取消平仓指令时无法知道该指令是否受影响
	GetCloseSymbolCode() *exchange.SymbolCode
	GetArbiSymbolCode() *exchange.SymbolCode // 获取指令中的开仓操作的代号，其他同 GetCloseSymbolCode
	GetDirection() ArbiDirection
	Cancel(CancelReason)
	Debugf(format string, args ...any)
	Infof(format string, args ...any)
	Warnf(format string, args ...any)
	Errorf(format string, args ...any)
	GetID() string
	GetRefID() string
	GetFullID() string
	GetArbitrager() *ArbitragerController
	GetInstrument(instrumentType exchange.InstrumentType, futureSymbol string) *exchange.Instrument
	IsCanceled() bool
	GetConfig() *ArbitragerConfig
	GetConfigSnapshot() *ArbitragerConfig
	SetTimeout(int)
	IsForce() bool
	SetSingleSideReason(reason SingleSideReason)
	IsStarted() bool
	UpdateAccountWorth(worthType AccountWorthType, showErrorMsg bool) (float64, error)
	MigrateClearData()
	GetReport() *InstructionReport
	GetTargetBasisRatio() float64 // 获取目标溢价率，book 渲染需要按 TargetBasisRatio 排序
	GetPrice() float64            // 获取指令价格
	GetQty() float64              // 获取指令数量，book 渲染需要展示数量
	GetQtyFilled() float64        // 获取成交数量，book 渲染需要展示数量
	GetFinishProgress() float64   // 获取完成进度
	EndNow()
	UpdateReportOrderPriceSlippage()
	GetOrderRow() []string // 获取订单行
	Lock()
	Unlock()
	RenderOrderReport() string
	RenderDurationReport() string
	RenderSummaryReport() string
	RenderSplitReport() string
	NeedBuyBNB() bool
	GetEstimatedBNB() (estimatedBNB, estimatedUSDT float64, er error)
	DeleteConfigSnapshot()
	IsFuture2Future() bool
}

type ExecutionDurationType string

const ExecutionDurationTypeGetBasisRatio ExecutionDurationType = "BasisRatio"
const ExecutionDurationTypeGetSpotPrice ExecutionDurationType = "SpotPrice"
const ExecutionDurationTypeGetFuturePrice ExecutionDurationType = "FuturePrice"
const ExecutionDurationTypeSubmitSpotOrder ExecutionDurationType = "SpotOrder"
const ExecutionDurationTypeSubmitFutureOrder ExecutionDurationType = "FutureOrder"
const ExecutionDurationTypeTransfer ExecutionDurationType = "Transfer"
const ExecutionDurationTypeSplitWait ExecutionDurationType = "SplitWait"
const ExecutionDurationTypeBuyRetryInterval ExecutionDurationType = "BuyRetryInterval"
const ExecutionDurationTypeSellRetryInterval ExecutionDurationType = "SellRetryInterval"
const ExecutionDurationTypeTransferRetryInterval ExecutionDurationType = "TransferRetryInterval"
const ExecutionDurationTypeBasisRatioRetryInterval ExecutionDurationType = "BasisRatioRetryInterval"
const ExecutionDurationTypeBasisToleranceWait ExecutionDurationType = "BasisToleranceWait"
const ExecutionDurationTypePauseWait ExecutionDurationType = "PauseWait"
const ExecutionDurationTypeTotal ExecutionDurationType = "Total"

type ExecutionDuration struct {
	SplitCounter int
	Type         ExecutionDurationType
	Duration     time.Duration
	Comment      string
	CreateTime   time.Time
}

type InstructionExecution struct {
	UsdtQty              float64                                 // 买入或卖出现货的 USDT 数量，始终是正数
	BuyQty               float64                                 // 买入数量
	SellQty              float64                                 // 卖出数量
	BuyAvgPrice          float64                                 // 买入均价
	SellAvgPrice         float64                                 // 卖出均价
	BuySlippage          float64                                 // 买入滑点
	SellSlippage         float64                                 // 卖出滑点
	BuyRetryCounter      int                                     // 买入接口错误后的重试次数
	TransferRetryCounter int                                     // 划转接口错误后的重试次数
	SellRetryCounter     int                                     // 卖出接口错误后的重试次数
	BuyOrders            []*[]*exchange.Order                    // 每个 Split 买入订单，用于分析订单个数、执行时间等
	SellOrders           []*[]*exchange.Order                    // 每个 Split 卖出订单，用于分析订单个数、执行时间等
	BNBOrders            []*[]*exchange.Order                    // 购买用于 BNB 抵扣交易费的的订单
	SpotFee              float64                                 // 现货手续费，以 USDT 计价
	FutureFee            float64                                 // 期货交易手续费，币币期货以对应币种计价
	FeeInCny             float64                                 // CNY 计价的总手续费
	FeeInUSDT            float64                                 // USDT 计价的总手续费
	Durations            []*ExecutionDuration                    // 所有请求和等待耗时
	DurationStats        map[ExecutionDurationType]time.Duration // 请求和等待耗时统计
	BasisCheck           []float64                               // 每个 split 检查溢价率通过时，得到的值
	BasisActual          []float64                               // 每个 split 成交的实际溢价率
}

const OrderRefIDLength = 6

func getOrderDuration(order *exchange.Order) time.Duration {
	duration := time.Duration(0)
	if startTime := order.GetTime(ExtKeyStartTime); startTime != nil {
		if endTime := order.GetTime(ExtKeyEndTime); endTime != nil {
			duration = endTime.Sub(*startTime)
		}
	}
	return duration
}

func (this *InstructionExecution) endOrderRequest(order *exchange.Order, splitIndex int) {
	endTime := time.Now()
	order.SetTime(ExtKeyEndTime, &endTime)
	durationType := ExecutionDurationTypeSubmitSpotOrder
	if order.IsFuture() {
		durationType = ExecutionDurationTypeSubmitFutureOrder
	}

	this.AddDuration(splitIndex, durationType, getOrderDuration(order), "")
}

type InstructionReport struct {
	Executions         []*InstructionExecution // 处理结果，尽量不要直接访问这个属性，通过 GetXXXExecution 函数获取
	SplitCounter       int                     // 处理到第几个 Split
	CreateTime         *time.Time              // 指令创建时间
	StartTime          *time.Time              // Execute 开始时间
	EndTime            *time.Time              // 结束时间，Cancel 时间或者正常完成的时间
	CancelReason       CancelReason            // 取消原因
	SingleSideReason   SingleSideReason        // 单边原因
	AccountWorthBefore float64                 // 指令执行前整个账户的价值，以 USDT 计价，更新这个数据可能在 instruction.Execute 之后，因此不保证其完全准确，仅做参考
	AccountWorthAfter  float64                 // 指令执行后整个账户的价值，以 USDT 计价，更新这个数据可能在下一个指令 instruction.Execute 之后，不保证完全准确，仅做参考
	EstimatedBNB       float64                 // 预计需要的 BNB 数量
}

// 每个非通配符的 FutureCode 对应一个 Instruction
// 通配符 FutureCode 应该预先被翻译为具体的 SpotSymbol 和 FutureSymbol
type Instruction struct {
	arbitrager     *ArbitragerController // 套利机器
	ID             string                // 序列号，对套利机唯一
	RefID          string                // 参考号，全局唯一
	Direction      ArbiDirection
	Force          bool // 是否为强制指令
	Percentage     float64
	Price          float64           // 仅限价单指令需要
	Qty            float64           // Qty = QtySnapshot * Percentage，Qty 的数量始终是正数，指令初始化时已经做了防错检查
	QtySnapshot    float64           // Open 时的 USDx 快照数量，Close 时的合约快照数量，原始数量，带正负号
	ConfigSnapshot *ArbitragerConfig // 运行配置的快照
	Config         *ArbitragerConfig // 指令实际使用的配置

	context           context.Context // 用于取消操作
	contextCancelFunc context.CancelFunc

	dirty        bool        // 如果是非正常结束的 instruction
	timeoutTimer *time.Timer // 到期计时器

	Pairs []*ArbiPair // 交易对
	// 套利开仓时，买 SymbolPair.Left.Code，卖 SymbolPair.Right.Code
	// 套利平仓时，买 SymbolPair.Right.Code，卖 SymbolPair.Left.Code

	TargetBasisRatio float64            // 目标溢价率，对于 MoveInstruction 来说，是合约间的价差
	RefBasisRatio    float64            // 参考溢价率，如果不是用户显式设置，该溢价率是在指令发出时自动读取的
	Funding          bool               // 是否为永续套取资金费率
	Report           *InstructionReport // 运行报告
	mutex            *sync.Mutex        // 指令锁
}

func NewInstruction(arbitrager *ArbitragerController, direction ArbiDirection, isForce bool, symbolCode *exchange.SymbolCode, qty float64, percentage float64, targetBasisRatio float64, configStr string) (*Instruction, error) {
	if symbolCode.IsWildcard() {
		return nil, errors.New("wildcard symbol code not allowed")
	}
	if arbitrager.Executer == nil {
		return nil, errors.New("exchange not initialized, please wait")
	}

	if symbolPairs, err := arbitrager.Exchange.TranslateSymbolCode(symbolCode); len(symbolPairs) == 1 {
		symbolPair := NewArbiPair(symbolPairs[0], direction == ArbiDirectionOpen, 0)
		spotSymbol, futureSymbol := symbolPair.Left.Symbol, symbolPair.Right.Symbol

		qtySnapshot := 0.0
		availableQty := 0.0
		if direction == ArbiDirectionOpen {
			// 此处获取数量，仅仅是为了复制 qtySnapshot，并不检查这个数量
			if _, q, err := arbitrager.Exchange.GetHoldingQty(exchange.Spot, symbolCode.USDXSymbol); err != nil {
				return nil, fmt.Errorf("check spot available qty error: %s", err)
			} else {
				availableQty = q
			}
		} else {
			if _, q, err := arbitrager.Exchange.GetHoldingQty(arbitrager.FutureInstrumentType(), futureSymbol); err != nil {
				return nil, fmt.Errorf("check spot available qty error: %s", err)
			} else {
				availableQty = q
			}
		}

		qtySnapshot = availableQty
		if qty > 0 && percentage == 0 {
			percentage = qty / math.Abs(availableQty)
		} else if qty == 0 && percentage > 0 {
			qty = math.Abs(availableQty) * percentage
		} else {
			return nil, fmt.Errorf("can not set qty and percentage at the same time")
		}

		if qty == 0 {
			return nil, fmt.Errorf("qty can not be 0")
		}
		if qty < 0 {
			return nil, fmt.Errorf("qty can not be < 0")
		}

		// 如果目标溢价率低于当前溢价率，可能是误操作，返回错误
		// 使用 TargetBasisRatioTolerance 主要是因为这个判断不能过于敏感，否则正常情况的输入也很容易报错
		refBasisRatio := 0.0
		if br, err := arbitrager.Executer.GetBasisRatio(symbolPair); err != nil {
			return nil, fmt.Errorf("get basis ratio error for %s and %s: %s", spotSymbol, futureSymbol, err)
		} else {
			refBasisRatio = br
			if targetBasisRatio == 0.0 {
				targetBasisRatio = br
			} else {
				if direction == ArbiDirectionOpen {
					// 值是负数的时候这个判断可能会失效
					if !CheckBasisTolerance(direction, targetBasisRatio, refBasisRatio, TargetBasisRatioTolerance) {
						return nil, fmt.Errorf("target basis ratio %.2f %% < ref basis ratio %.2f %%, greater than %.1f %%", targetBasisRatio*100, refBasisRatio*100, TargetBasisRatioTolerance*100)
					}
				} else {
					// 值是负数的时候这个判断可能会失效
					if !CheckBasisTolerance(direction, targetBasisRatio, refBasisRatio, TargetBasisRatioTolerance) {
						return nil, fmt.Errorf("target basis ratio %.2f %% > ref basis ratio %.2f %%, greater than %.1f %%", targetBasisRatio*100, refBasisRatio*100, TargetBasisRatioTolerance*100)
					}
				}
			}
		}

		isFunding := false
		if symbolCode.IsPerp() {
			isFunding = true
		}
		instructionID := arbitrager.NewInstructionID(direction, isForce)
		// 在 FreeOrder 模式下，可能会出现 percentage = +Inf 的情况
		// 而 +Inf 无法被保存到 storage json 中，因此，强制将其转换为 100%
		if percentage > 1 {
			percentage = 1
			arbitrager.Warnf(" %s percentage > 100%%, reset it to 100%%", instructionID)
		}

		p := exchange.NewSymbolPair(symbolCode, spotSymbol, futureSymbol)
		pair := NewArbiPair(p, direction == ArbiDirectionOpen, refBasisRatio)

		instruction := &Instruction{
			arbitrager:     arbitrager,
			ID:             instructionID,
			RefID:          exchange.NewRandomID(),
			Direction:      direction,
			Percentage:     percentage,
			Qty:            qty,
			QtySnapshot:    qtySnapshot,
			Config:         arbitrager.Config.snapshotArbitragerConfig(),
			ConfigSnapshot: arbitrager.Config.snapshotArbitragerConfig(),
			Pairs:          []*ArbiPair{pair},
			Funding:        isFunding,
			Force:          isForce,
			mutex:          &sync.Mutex{},
		}
		instruction.SetupInstructionReport()

		if _, _, err := baseconfig.SetConfigWithString(instruction.Config, configStr); err != nil {
			return nil, err
		}

		instruction.TargetBasisRatio = targetBasisRatio
		instruction.RefBasisRatio = refBasisRatio
		return instruction, nil
	} else {
		return nil, fmt.Errorf("future code %s translate to no symbols or more than one symbol, error: %s", symbolCode, err)
	}
}

func NewFuture2FutureInstruction(arbitrager *ArbitragerController, direction ArbiDirection, isForce bool, leftFutureCode *exchange.SymbolCode, rightFutureCode *exchange.SymbolCode, qty float64, percentage float64, targetBasisRatio float64, configStr string) (*Instruction, error) {
	if leftFutureCode.IsWildcard() || rightFutureCode.IsWildcard() {
		return nil, errors.New("wildcard symbol code not allowed")
	}
	if arbitrager.Executer == nil {
		return nil, errors.New("exchange not initialized, please wait")
	}

	if !arbitrager.IsCrossMode() {
		return nil, errors.New("future2future instruction only allowed in cross mode")
	}

	if leftFuturePairs, err := arbitrager.Exchange.TranslateSymbolCode(leftFutureCode); len(leftFuturePairs) == 1 {
		if rightFuturePairs, err := arbitrager.Exchange.TranslateSymbolCode(rightFutureCode); len(rightFuturePairs) == 1 {
			// TranslateSymbolCode 转的都是 Spot~Future，这里需取 RightXXX 即是 Future
			leftPair := leftFuturePairs[0]
			rightPair := rightFuturePairs[0]
			p := exchange.NewCommonSymbolPair(leftPair.Right.Code, rightPair.Right.Code, leftPair.Right.Symbol, rightPair.Right.Symbol)
			pair := NewArbiPair(p, direction == ArbiDirectionOpen, 0)

			if pair.Left.Code.Coin() != pair.Right.Code.Coin() {
				return nil, fmt.Errorf("left future code %s and right future code %s coin not match", leftFutureCode, rightFutureCode)
			}

			qtySnapshot := 0.0
			availableQty := 0.0
			if direction == ArbiDirectionOpen {
				// 此处获取数量，仅仅是为了复制 qtySnapshot，并不检查这个数量
				if _, q, err := arbitrager.Exchange.GetHoldingQty(arbitrager.FutureInstrumentType(), leftFutureCode.USDXSymbol); err != nil {
					return nil, fmt.Errorf("check available qty error: %s", err)
				} else {
					availableQty = q
				}
			} else {
				rightFutureSymbol := pair.Right.Symbol
				if _, q, err := arbitrager.Exchange.GetHoldingQty(arbitrager.FutureInstrumentType(), rightFutureSymbol); err != nil {
					return nil, fmt.Errorf("check available qty error: %s", err)
				} else {
					availableQty = q
				}
			}

			qtySnapshot = availableQty
			if qty > 0 && percentage == 0 {
				percentage = qty / math.Abs(availableQty)
			} else if qty == 0 && percentage > 0 {
				qty = math.Abs(availableQty) * percentage
			} else {
				return nil, fmt.Errorf("can not set qty and percentage at the same time")
			}

			if qty == 0 {
				return nil, fmt.Errorf("qty can not be 0")
			}
			if qty < 0 {
				return nil, fmt.Errorf("qty can not be < 0")
			}

			refBasisRatio := 0.0
			if br, err := arbitrager.Executer.GetBasisRatio(pair); err != nil {
				return nil, fmt.Errorf("get basis ratio error for %s and %s: %s", pair.Left.Symbol, pair.Right.Symbol, err)
			} else {
				refBasisRatio = br
				if targetBasisRatio == 0.0 {
					targetBasisRatio = br
				} else {
					if !CheckBasisTolerance(direction, targetBasisRatio, refBasisRatio, TargetBasisRatioTolerance) {
						if direction == ArbiDirectionOpen {
							return nil, fmt.Errorf("target basis ratio %.2f %% < ref basis ratio %.2f %%, greater than %.2f %%", targetBasisRatio*100, refBasisRatio*100, TargetBasisRatioTolerance*100)
						} else {
							return nil, fmt.Errorf("target basis ratio %.2f %% > ref basis ratio %.2f %%, greater than %.2f %%", targetBasisRatio*100, refBasisRatio*100, TargetBasisRatioTolerance*100)
						}
					}
				}
			}

			pair.BasisSnapshot = refBasisRatio

			instructionID := arbitrager.NewInstructionID(direction, isForce)
			// 在 FreeOrder 模式下，可能会出现 percentage = +Inf 的情况
			// 而 +Inf 无法被保存到 storage json 中，因此，强制将其转换为 100%
			if percentage > 1 {
				percentage = 1
				arbitrager.Warnf("%s percentage > 100%%, reset it to 100%%", instructionID)
			}

			instruction := &Instruction{
				arbitrager:     arbitrager,
				ID:             instructionID,
				RefID:          exchange.NewRandomID(),
				Direction:      direction,
				Pairs:          []*ArbiPair{pair},
				Percentage:     percentage,
				Qty:            qty,
				QtySnapshot:    qtySnapshot,
				Config:         arbitrager.Config.snapshotArbitragerConfig(),
				ConfigSnapshot: arbitrager.Config.snapshotArbitragerConfig(),
				Force:          isForce,
				mutex:          &sync.Mutex{},
			}
			instruction.SetupInstructionReport()

			if _, _, err := baseconfig.SetConfigWithString(instruction.Config, configStr); err != nil {
				return nil, err
			}
			instruction.TargetBasisRatio = targetBasisRatio
			instruction.RefBasisRatio = refBasisRatio
			return instruction, nil
		} else {
			return nil, fmt.Errorf("right future code %s translate to no symbols or more than one symbol, error: %s", rightFutureCode, err)
		}
	} else {
		return nil, fmt.Errorf("left future code %s translate to no symbols or more than one symbol, error: %s", leftFutureCode, err)
	}
}

func (this *Instruction) SetupInstructionReport() {
	now := time.Now()
	r := &InstructionReport{
		Executions: []*InstructionExecution{
			{
				BuyOrders:     []*[]*exchange.Order{},
				SellOrders:    []*[]*exchange.Order{},
				BNBOrders:     []*[]*exchange.Order{},
				DurationStats: map[ExecutionDurationType]time.Duration{},
				BasisCheck:    []float64{},
				BasisActual:   []float64{},
			}},
		CreateTime:         &now,
		CancelReason:       CancelReasonNone,
		SingleSideReason:   SingleSideReasonNone,
		EstimatedBNB:       -1,
		AccountWorthBefore: -1,
		AccountWorthAfter:  -1,
	}
	this.Report = r
}

func (this *Instruction) Lock() {
	if this.mutex != nil {
		this.mutex.Lock()
	}
}

func (this *Instruction) Unlock() {
	if this.mutex != nil {
		this.mutex.Unlock()
	}
}

func (this *Instruction) SetupMoveInstructionReport() {
	now := time.Now()
	r := &InstructionReport{
		Executions: []*InstructionExecution{
			{
				BuyOrders:     []*[]*exchange.Order{},
				SellOrders:    []*[]*exchange.Order{},
				BNBOrders:     []*[]*exchange.Order{},
				DurationStats: map[ExecutionDurationType]time.Duration{},
				BasisCheck:    []float64{},
				BasisActual:   []float64{},
			},
			{
				BuyOrders:     []*[]*exchange.Order{},
				SellOrders:    []*[]*exchange.Order{},
				BNBOrders:     []*[]*exchange.Order{},
				DurationStats: map[ExecutionDurationType]time.Duration{},
				BasisCheck:    []float64{},
				BasisActual:   []float64{},
			},
		},
		CreateTime:         &now,
		CancelReason:       CancelReasonNone,
		SingleSideReason:   SingleSideReasonNone,
		EstimatedBNB:       -1,
		AccountWorthBefore: -1,
		AccountWorthAfter:  -1,
	}
	this.Report = r
}

func NewTable() *termtable.Table {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	return t
}

func (this *Instruction) RenderRequest() string {
	t := NewTable()
	var row []string
	t.SetHeader(SimpleInstructionRequestHeader)
	row = this.GetRequestRow(false)

	t.AddRow(row)
	request := t.Render()

	if this.NeedBuyBNB() {
		if estimatedBNB, estimatedUSDx, err := this.GetEstimatedBNB(); err != nil {
			request += fmt.Sprintf("\n[ERROR!get estimated bnb error: %s]\n", err)
		} else {
			uSymbol := this.GetPair().GetBuyItem().Code.USDXSymbol // 这里买或卖的 USDx 都可以，是相同的
			request += fmt.Sprintf("\n使用 BNB 抵扣，需要购买 [%.3f] BNB，花费 %.1f %s\n", estimatedBNB, estimatedUSDx, uSymbol)
		}
	}

	if configDiff, err := baseconfig.RenderConfigDiff(this.Config, this.ConfigSnapshot, true); err != nil {
		return fmt.Sprintf("打印配置文件更改出现错误：%s", err)
	} else {
		if configDiff != "" {
			return fmt.Sprintf("%s\n\n%s", request, configDiff)
		} else {
			return request
		}
	}
}

func (this *Instruction) NeedBuyBNB() bool {
	if !this.arbitrager.Executer.IsExchange(exchange.Binance) {
		return false
	}
	if !this.Config.BNBFee || !this.Config.BNBAutoBuy {
		return false
	}
	dir := this.Direction
	if dir.IsRotate() {
		return false
	}

	if this.IsFuture2Future() {
		// TODO bn U 本位也可以抵扣 10%，但需要划转到 U 本位账号
		return false
	}
	return true
}

func (this *Instruction) GetEstimatedBNB() (estimatedBNB, estimatedUSDx float64, er error) {
	estimatedBNB = 0.0
	estimatedUSDx = 0.0
	if this.Config.BNBFee && this.Report.EstimatedBNB == -1 {
		uSymbol := this.GetPair().GetSellItem().Code.USDXSymbol
		if bnbPrice, err := this.arbitrager.Exchange.GetSpotPrice("BNB"+uSymbol, 2*time.Second); err != nil {
			er = fmt.Errorf("get bnb spot price error: %s", err)
		} else {
			if this.IsOpen() {
				estimatedUSDx = this.Qty * BNBFeeSpotRatioVIP0
			} else {
				symbol := this.GetPair().GetBuyItem().Symbol
				if fPrice, err := this.arbitrager.Exchange.GetLastPrice(this.arbitrager.FutureInstrumentType(), symbol, false); err != nil {
					er = fmt.Errorf("get future price error: %s", err)
				} else {
					estimatedUSDx = this.arbitrager.Qty2Size(symbol, fPrice, this.Qty) * (1 - this.TargetBasisRatio) * BNBFeeSpotRatioVIP0
					if this.arbitrager.FutureInstrumentType() == exchange.CoinMarginedFutures {
						// 币本位币价值 * 价格 = USDx
						estimatedUSDx = estimatedUSDx * fPrice
					}
				}
			}
			estimatedBNB = estimatedUSDx / bnbPrice
			this.Report.EstimatedBNB = estimatedBNB
		}
	}
	return
}

func (this *Instruction) FormatFutureQty(symbol string, qty float64) string {
	return this.arbitrager.Exchange.FormatQty(this.arbitrager.FutureInstrumentType(), symbol, qty)
}

func (this *Instruction) GetRequestRow(isDetail bool) (row []string) {
	qty := ""
	if this.Direction == ArbiDirectionOpen {
		qty = fmt.Sprintf("%.0f %s", this.Qty, this.GetPair().GetBuyItem().Code.USDXSymbol)
	} else if this.Direction == ArbiDirectionClose {
		qty = fmt.Sprintf("%s cnt", this.FormatFutureQty(this.GetPair().GetBuyItem().Symbol, this.Qty))
	}
	row = []string{
		this.ID,
		this.arbitrager.Exchange.GetName(),
		this.GetPair().GetBuyItem().Code.Code,
		this.GetPair().GetSellItem().Code.Code,
		fmt.Sprintf("%.2f%%", this.Percentage*100),
		qty,
		fmt.Sprintf("%+.2f%%", 100*this.TargetBasisRatio),
		fmt.Sprintf("%+.2f%%", 100*this.RefBasisRatio),
	}
	if isDetail {
		row = append([]string{this.arbitrager.ID}, row...)
	}
	return row
}

func (this *Instruction) RenderStatus(isDetail bool) string {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	if isDetail {
		t.SetHeader(DetailInstructionStatusHeader)
	} else {
		t.SetHeader(SimpleInstructionStatusHeader)
	}

	t.AddRow(this.GetStatusRow(isDetail))
	return t.Render()
}

func (this *Instruction) GetStatusRow(isDetail bool) (row []string) {
	endTimeStr := "--"
	if this.IsEnded() {
		endTimeStr = this.Report.EndTime.Format(ISOTimeFormat)
	}
	row = []string{}

	if isDetail {
		row = append(row, this.ID)
		row = append(row, this.arbitrager.ID)
		row = append(row, this.arbitrager.Exchange.GetName())
		row = append(row, fmt.Sprintf("%.2f", this.Qty))
		row = append(row, fmt.Sprintf("%.2f%%", this.GetFinishProgress()*100))
		row = append(row, this.GetBuyTradeString())
		row = append(row, this.GetSellTradeString())
		row = append(row, fmt.Sprintf("%+.2f%%", this.TargetBasisRatio*100))
		row = append(row, fmt.Sprintf("%+.2f%%", this.GetBasisRatio()*100))
		row = append(row, fmt.Sprintf("%+.2f%%", (this.GetBasisRatio()-this.TargetBasisRatio)*100))
		row = append(row, string(this.Report.CancelReason))
		if this.IsStarted() {
			row = append(row, this.Report.StartTime.Format(ISOTimeFormat))
		} else {
			row = append(row, "--")
		}
		row = append(row, endTimeStr)
		row = append(row, this.FormatDuration())
		row = append(row, fmt.Sprintf("%.0f USDT", this.Report.AccountWorthBefore))
		row = append(row, fmt.Sprintf("%.0f USDT", this.Report.AccountWorthAfter))
		row = append(row, this.GetComment())
	} else {
		row = append(row, this.ID)
		row = append(row, this.GetPair().GetBuyItem().Code.Code)
		row = append(row, this.GetPair().GetSellItem().Code.Code)
		row = append(row, fmt.Sprintf("%.2f", this.Qty))
		row = append(row, fmt.Sprintf("%.2f%%", this.GetFinishProgress()*100))
		row = append(row, fmt.Sprintf("%s & %s", this.GetBuyTradeString(), this.GetSellTradeString()))
		row = append(row, fmt.Sprintf("%+.2f%%", this.GetBasisRatio()*100))
		row = append(row, fmt.Sprintf("%+.2f%%", (this.GetBasisRatio()-this.TargetBasisRatio)*100))
		row = append(row, this.FormatDuration())
		row = append(row, fmt.Sprintf("%.0f USDT", this.Report.AccountWorthAfter))
	}
	return row
}

func (this *Instruction) GetFinishProgress() float64 {
	return this.GetQtyFilled() / math.Abs(this.Qty)
}

func (this *Instruction) GetBuyTradeString() string {
	buyCode := this.GetPair().GetBuyItem().Code
	buySymbol := this.GetPair().GetBuyItem().Symbol
	priceStr := this.formatPrice(buyCode.InstrumentType(), buySymbol, this.GetExecution().BuyAvgPrice)
	if buyCode.IsSpot() {
		return fmt.Sprintf("%.4f %s @%s", this.GetExecution().BuyQty, buyCode.Coin(), priceStr)
	} else {
		qtyStr := this.arbitrager.Exchange.FormatQty(buyCode.InstrumentType(), buySymbol, this.GetExecution().BuyQty)
		return fmt.Sprintf("%s %s @%s", qtyStr, buyCode.Code, priceStr)
	}
}

func (this *Instruction) GetSellTradeString() string {
	sellCode := this.GetPair().GetSellItem().Code
	sellSymbol := this.GetPair().GetSellItem().Symbol
	priceStr := this.formatPrice(sellCode.InstrumentType(), sellSymbol, this.GetExecution().SellAvgPrice)
	if sellCode.IsSpot() {
		return fmt.Sprintf("-%.4f %s @%s", this.GetExecution().SellQty, sellCode.Coin(), priceStr)
	} else {
		qtyStr := this.arbitrager.Exchange.FormatQty(sellCode.InstrumentType(), sellSymbol, this.GetExecution().SellQty)
		return fmt.Sprintf("-%s %s @%s", qtyStr, sellCode.Code, priceStr)
	}
}

func (this *Instruction) GetComment() string {
	parts := []string{}
	retryCounts := this.FormatRetryCounters()
	parts = append(parts, retryCounts)
	configDiff := ""
	if diff, err := baseconfig.RenderConfigDiff(this.Config, this.ConfigSnapshot, false); err != nil {
		this.Errorf("get config diff error: %s", err)
		configDiff = fmt.Sprintf("<ERROR!get config diff: %s>", err)
	} else {
		configDiff = diff
	}
	if configDiff != "" {
		parts = append(parts, configDiff)

	}
	if this.Report.SingleSideReason != SingleSideReasonNone {
		singleSide := fmt.Sprintf("SingleSide: %s", this.Report.SingleSideReason)
		parts = append(parts, singleSide)
	}
	return strings.Join(parts, " | ")
}

func (this *Instruction) GetBasisRatio() float64 {
	buyAvgPrice := this.GetExecution().BuyAvgPrice
	sellAvgPrice := this.GetExecution().SellAvgPrice
	if this.IsOpen() && buyAvgPrice > 0 {
		return sellAvgPrice/buyAvgPrice - 1
	} else if !this.IsOpen() && sellAvgPrice > 0 {
		return buyAvgPrice/sellAvgPrice - 1
	} else {
		return 0
	}
}

func fmtDuration(d time.Duration) string {
	d = d.Round(time.Second)
	m := d / time.Minute
	d -= m * time.Minute
	s := d / time.Second
	return fmt.Sprintf("%02dm:%02ds", m, s)
}

func (this *Instruction) FormatDuration() string {
	if this.Report.StartTime != nil {
		if this.IsEnded() {
			return fmtDuration(this.Report.EndTime.Sub(*this.Report.StartTime))
		} else {
			return fmtDuration(time.Since(*this.Report.StartTime))
		}
	} else {
		return "--"
	}
}

func (this *Instruction) FormatRetryCounters() string {
	s := ""
	if this.GetExecution().BuyRetryCounter > 0 {
		s += fmt.Sprintf("B^%d", this.GetExecution().BuyRetryCounter)
	}
	if this.GetExecution().TransferRetryCounter > 0 {
		s += fmt.Sprintf("T^%d", this.GetExecution().TransferRetryCounter)
	}
	if this.GetExecution().SellRetryCounter > 0 {
		s += fmt.Sprintf("S^%d", this.GetExecution().SellRetryCounter)
	}
	return s
}

func (this *Instruction) IsOpen() bool {
	return this.Direction.IsOpen()
}

func (this *Instruction) Execute() {
	if this.IsDirty() {
		this.Errorf("trying to execute a dirty instruction, please check invocation")
		return
	}
	if this.IsStarted() {
		this.Errorf("instruction already executed")
		return
	}
	now := time.Now()
	this.Report.StartTime = &now
	this.SetTimeout(this.Config.Timeout)
	this.WarnBNB()
	if this.Direction == ArbiDirectionOpen {
		this.arbitrager.Executer.Arbi(this.context, this)
	} else if this.Direction == ArbiDirectionClose {
		this.arbitrager.Executer.Close(this.context, this)
	}
}

// 取消指令
func (this *Instruction) Cancel(reason CancelReason) {
	if !this.IsStarted() {
		this.Errorf("instruction %s isn't started, can not cancel", this.ID)
		return
	}
	if this.IsCanceled() {
		this.Errorf("instruction already canceled")
		return
	}
	if !this.IsEnded() && !this.IsDirty() {
		this.contextCancelFunc()
		this.Report.CancelReason = reason
		if reason == CancelReasonNone {
			this.EndNow()
		}
	} else {
		this.Infof("instruction %s already canceled", this.ID)
	}
}

func (this *Instruction) IsFuture2Future() bool {
	return this.Pairs[0].IsF2F()
}

func (this *Instruction) IsEnded() bool {
	return this.Report.EndTime != nil
}

func (this *Instruction) GetRelatedSymbolCodes() []*exchange.SymbolCode {
	return []*exchange.SymbolCode{this.GetPair().GetSellItem().Code}
}

func (this *Instruction) GetCloseSymbolCode() *exchange.SymbolCode {
	if this.Direction == ArbiDirectionClose {
		return this.GetPair().GetBuyItem().Code
	} else {
		return nil
	}
}

func (this *Instruction) GetArbiSymbolCode() *exchange.SymbolCode {
	if this.Direction == ArbiDirectionOpen {
		return this.GetPair().GetSellItem().Code
	} else {
		return nil
	}
}

func (this *Instruction) GetDirection() ArbiDirection {
	return this.Direction
}

func (this *Instruction) GetID() string {
	return this.ID
}

func (this *Instruction) GetRefID() string {
	return this.RefID
}

func (this *Instruction) GetFullID() string {
	return fmt.Sprintf("%s_%s", this.arbitrager.ID, this.ID)
}

func (this *Instruction) Debugf(format string, args ...any) {
	this.arbitrager.Debugf("(%s) %s", this.ID, fmt.Sprintf(format, args...))
}

func (this *Instruction) Infof(format string, args ...any) {
	this.arbitrager.Infof("(%s) %s", this.ID, fmt.Sprintf(format, args...))
}

func (this *Instruction) Warnf(format string, args ...any) {
	this.arbitrager.Warnf("(%s) %s", this.ID, fmt.Sprintf(format, args...))
}

func (this *Instruction) Errorf(format string, args ...any) {
	this.arbitrager.Errorf("(%s) %s", this.ID, fmt.Sprintf(format, args...))
}

func (this *Instruction) GetArbitrager() *ArbitragerController {
	return this.arbitrager
}

func (this *Instruction) GetInstrument(instrumentType exchange.InstrumentType, futureSymbol string) *exchange.Instrument {
	return this.arbitrager.Executer.GetInstrument(instrumentType, futureSymbol)
}

func (this *Instruction) EndNow() {
	now := time.Now()
	this.Report.EndTime = &now
	this.UpdateReportOrderPriceSlippage() // 保证订单平均价和滑点都计算
	this.UpdateAccountWorth(AccountWorthTypeAfterInstruction, true)
	// instruction 指令执行结束，写缓存
	this.arbitrager.storage.Save()
	go this.arbitrager.UpdateBalance(false)
	this.arbitrager.SendStatus(InstructionStatusAll, false, false, this.ID)
	this.checkSpotAndFutureQty()
}

func (this *Instruction) checkSpotAndFutureQty() {
	var buyQty, sellQty, deltaRatio float64
	execution := this.GetExecution()
	pair := this.GetPair()
	if this.Direction == ArbiDirectionOpen {
		if pair.IsF2F() {
			buyQty = this.arbitrager.FutureQtyToSpotQty(pair.GetBuyItem().Symbol, execution.BuyQty, execution.BuyAvgPrice)
		} else {
			buyQty = execution.BuyQty
		}
		sellQty = this.arbitrager.FutureQtyToSpotQty(pair.GetSellItem().Symbol, execution.SellQty, execution.SellAvgPrice)
	} else if this.Direction == ArbiDirectionClose {
		buyQty = this.arbitrager.FutureQtyToSpotQty(pair.GetBuyItem().Symbol, execution.BuyQty, execution.BuyAvgPrice)
		if pair.IsF2F() {
			sellQty = this.arbitrager.FutureQtyToSpotQty(pair.GetSellItem().Symbol, execution.SellQty, execution.SellAvgPrice)
		} else {
			sellQty = execution.SellQty
		}
	}

	if buyQty != 0 {
		deltaRatio = (math.Abs(buyQty) - math.Abs(sellQty)) / math.Abs(buyQty)
	}

	deltaRatio = math.Abs(deltaRatio)
	tolerance := this.Config.SpotFutureQtyDeltaTolerance
	if tolerance == 0 {
		tolerance = 0.02
	}
	if deltaRatio > tolerance {
		this.arbitrager.WarnMsgf("套利品种成交数量之差 %.2f%% 超过配置 SpotFutureQtyDeltaTolerance %.2f%%。", deltaRatio*100, tolerance*100)
	}
}

// sleep 并且记录 duration 和 counter
func (this *InstructionExecution) SleepDuration(splitCounter int, durationType ExecutionDurationType, duration time.Duration, comment string) {
	time.Sleep(duration)
	this.AddDuration(splitCounter, durationType, duration, comment)
	// 对于重试的，同时记录 counter
	switch durationType {
	case ExecutionDurationTypeBuyRetryInterval:
		this.BuyRetryCounter += 1
	case ExecutionDurationTypeSellRetryInterval:
		this.SellRetryCounter += 1
	case ExecutionDurationTypeTransferRetryInterval:
		this.TransferRetryCounter += 1
	}
}

func (this *InstructionExecution) AddDuration(splitCounter int, durationType ExecutionDurationType, duration time.Duration, comment string) {
	this.DurationStats[durationType] += duration
	this.DurationStats[ExecutionDurationTypeTotal] += duration
}

func (this *Instruction) getReportOrderRow(e *InstructionExecution, o *exchange.Order) []string {
	d := getOrderDuration(o)
	duration := d.String()
	if int64(d/time.Millisecond) < 0 {
		duration = ""
	}
	slippage := fmt.Sprintf("%.2f %%", (o.ExecPrice-o.LastPrice)/o.LastPrice*100)
	execPrice := this.formatPrice(o.InstrumentType, o.Symbol, o.ExecPrice)
	execQty := fmt.Sprintf("%.5f", o.ExecQty)
	qty := fmt.Sprintf("%.5f", o.Qty)
	price := fmt.Sprintf("%v", o.Price)
	if o.IsFuture() {
		execQty = this.FormatFutureQty(o.Symbol, o.ExecQty)
		qty = this.FormatFutureQty(o.Symbol, o.Qty)
	}
	if o.QuoteQty > 0 {
		qty = fmt.Sprintf("%.2f %s", o.QuoteQty, this.GetPair().GetSellItem().Code.USDXSymbol)
		price = "-"
		slippage = "-"
	}

	orderID := o.OrderID
	if o.ExecQty == 0 {
		slippage = ""
		execPrice = ""
		execQty = ""
	}
	row := []string{
		orderID,
		o.Symbol,
		qty,
		price,
		execPrice,
		slippage,
		execQty,
		duration,
	}
	return row
}

func (this *Instruction) RenderOrderReport() string {
	t := NewTable()
	t.SetHeader([]string{"Split", "Order ID", "Symbol", "Qty", "Limit Price", "Exec Price", "Slippage", "Filled", "Duration"})

	execution := this.GetExecution()
	if this.Config.BNBFee && this.Config.BNBAutoBuy {
		bnbOrderFound := false
		for i, split := range execution.BNBOrders {
			for _, o := range *split {
				bnbOrderFound = true
				row := this.getReportOrderRow(execution, o)
				row = append([]string{fmt.Sprintf("%d", i+1)}, row...)
				t.AddRow(row)
			}
		}
		if bnbOrderFound {
			t.AddRow([]string{
				"",
				"-------------",
				"",
				"",
				"",
				"",
				"",
				"",
				"",
			})
		}
	}

	for i, split := range execution.BuyOrders {
		for _, o := range *split {
			row := this.getReportOrderRow(execution, o)
			row = append([]string{fmt.Sprintf("Buy %d", i+1)}, row...)
			t.AddRow(row)
		}
	}

	for i, split := range execution.SellOrders {
		for _, o := range *split {
			row := this.getReportOrderRow(execution, o)
			row = append([]string{fmt.Sprintf("Sell %d", i+1)}, row...)
			t.AddRow(row)
		}
	}

	t.AddRow([]string{
		"",
		"Buy Slippage:",
		"",
		"",
		"",
		"",
		fmt.Sprintf("%.2f %%", this.GetExecution().BuySlippage*100),
		"",
		"",
	})
	t.AddRow([]string{
		"",
		"Sell Slippage:",
		"",
		"",
		"",
		"",
		fmt.Sprintf("%.2f %%", this.GetExecution().SellSlippage*100),
		"",
		"",
	})
	return t.Render()
}

func (this *Instruction) RenderDurationReport() string {
	t := NewTable()
	t.SetHeader([]string{"Type", "Duration", "Percentage"})

	execution := this.GetExecution()
	totalDuration := time.Duration(time.Millisecond * 0)
	for _, v := range execution.DurationStats {
		totalDuration += v
	}
	if total, found := execution.DurationStats[ExecutionDurationTypeTotal]; found {
		if total == 0 {
			execution.DurationStats[ExecutionDurationTypeTotal] = totalDuration
		}
	} else {
		execution.DurationStats[ExecutionDurationTypeTotal] = totalDuration
	}

	typeList := []ExecutionDurationType{
		ExecutionDurationTypeGetBasisRatio,
		ExecutionDurationTypeGetSpotPrice,
		ExecutionDurationTypeGetFuturePrice,
		ExecutionDurationTypeSubmitSpotOrder,
		ExecutionDurationTypeSubmitFutureOrder,
		ExecutionDurationTypeTransfer,
		ExecutionDurationTypeSplitWait,
		ExecutionDurationTypeBuyRetryInterval,
		ExecutionDurationTypeSellRetryInterval,
		ExecutionDurationTypeTransferRetryInterval,
		ExecutionDurationTypeBasisRatioRetryInterval,
		ExecutionDurationTypeBasisToleranceWait,
		ExecutionDurationTypePauseWait,
	}
	for _, ty := range typeList {
		ms := float64(execution.DurationStats[ty] / time.Millisecond)
		total := float64(totalDuration / time.Millisecond)
		t.AddRow([]string{
			string(ty),
			execution.DurationStats[ty].String(),
			fmt.Sprintf("%.2f %%", ms/total*100),
		})
	}
	t.AddRow([]string{
		string(ExecutionDurationTypeTotal),
		execution.DurationStats[ExecutionDurationTypeTotal].String(),
		"--",
	})
	return t.Render()
}

func (this *Instruction) RenderSummaryReport() string {
	summary := ""
	status := this.RenderStatus(true)
	summary += status
	summary += "\n\n"
	t := NewTable()
	uSymbol := this.GetPair().GetSellItem().Code.USDXSymbol
	t.SetHeader([]string{uSymbol + " Qty", "Buy Qty", "Sell Qty", "BNB Qty", "B Avg. Price", "S Avg. Price", "Basis %"})
	execution := this.GetExecution()
	bnbQty := 0.0
	for _, splitOrders := range execution.BNBOrders {
		for _, o := range *splitOrders {
			bnbQty += o.ExecQty
		}
	}

	var buyQty, sellQty, buyPrice, sellPrice string
	pair := this.GetPair()
	if this.IsOpen() {
		buyQty = fmt.Sprintf("%.4f", execution.BuyQty)
		if pair.IsF2F() {
			buyQty = this.FormatFutureQty(pair.GetBuyItem().Symbol, execution.BuyQty)
		}
		sellQty = this.FormatFutureQty(pair.GetSellItem().Symbol, execution.SellQty)
		buyPrice = this.formatPrice(pair.GetBuyItem().Code.InstrumentType(), pair.GetBuyItem().Symbol, execution.BuyAvgPrice)
		sellPrice = this.formatPrice(pair.GetSellItem().Code.InstrumentType(), pair.GetSellItem().Symbol, execution.SellAvgPrice)
	} else {
		buyQty = this.FormatFutureQty(pair.GetBuyItem().Symbol, execution.BuyQty)
		sellQty = fmt.Sprintf("%.4f", execution.SellQty)
		if pair.IsF2F() {
			sellQty = this.FormatFutureQty(pair.GetSellItem().Symbol, execution.SellQty)
		}
		buyPrice = this.formatPrice(pair.GetBuyItem().Code.InstrumentType(), pair.GetBuyItem().Symbol, execution.BuyAvgPrice)
		sellPrice = this.formatPrice(pair.GetSellItem().Code.InstrumentType(), pair.GetSellItem().Symbol, execution.SellAvgPrice)
	}

	t.AddRow([]string{
		fmt.Sprintf("%.0f", execution.UsdtQty),
		buyQty,
		sellQty,
		fmt.Sprintf("%.4f", bnbQty),
		buyPrice,
		sellPrice,
		fmt.Sprintf("%.2f%%", this.GetBasisRatio()*100),
	})
	summary += t.Render()
	return summary
}

func (this *Instruction) RenderSplitReport() string {
	if this.GetFinishProgress() > 0 {
		execution := this.GetExecution()
		if len(execution.BasisCheck) > 0 {
			t := NewTable()
			t.SetHeader([]string{"Split", "Basis Check", "Basis Acutal", "Order Start Time"})
			for splitID, basisCheck := range execution.BasisCheck {
				startTimeStr := "--"
				if len(execution.BuyOrders) > splitID {
					splitOrders := execution.BuyOrders[splitID]
					if len(*splitOrders) > 0 {
						if startTime := (*splitOrders)[0].GetTime(ExtKeyStartTime); startTime != nil {
							startTimeStr = (*startTime).Format(ISOTimeFormat)
						}
					}
				}
				// 打印报告时指令可能正在执行中，这时可能 BasisActual 的值尚未写入，即 BasisActual 的长度和 BasisCheck 的长度不同
				// 这样会导致 BasisActual[splitID] 越界崩溃，因此打印前检查一下数组长度
				// 如果打印报告的操作和指令执行过程用锁保护起来，也可以同样解决这个问题
				acutalBasisStr := "-"
				if len(execution.BasisActual) > splitID {
					acutalBasisStr = fmt.Sprintf("%.2f%%", execution.BasisActual[splitID]*100)

				}
				t.AddRow([]string{
					fmt.Sprintf("%d", splitID+1),
					fmt.Sprintf("%.2f%%", basisCheck*100),
					acutalBasisStr,
					startTimeStr,
				})

			}
			return t.Render()
		}
	}
	return ""
}

func (this *Instruction) IsCanceled() bool {
	if this.IsDirty() {
		this.Errorf("instruction is dirty, please check invocation")
		return true
	}
	if this.context == nil || this.context.Err() != nil {
		return true
	}
	return !(this.Report.CancelReason == CancelReason("") || this.Report.CancelReason == CancelReasonNone)
}

func (this *Instruction) GetConfig() *ArbitragerConfig {
	return this.Config
}

func (this *Instruction) IsDirty() bool {
	return this.dirty
}

func (this *Instruction) SetTimeout(timeout int) {
	// 如果已经结束，不再允许设置 timeout
	if this.IsEnded() {
		return
	}
	this.context, this.contextCancelFunc = context.WithCancel(context.Background())
	// 如果没有设置过 timer，设置 timer
	if this.timeoutTimer == nil {
		this.timeoutTimer = time.AfterFunc(time.Duration(timeout)*time.Minute, func() {
			this.Cancel(CancelReasonDeadline)
		})
	} else {
		// 如果已经设置过 timer，停止之前的 timer，重新设置 timer
		this.timeoutTimer.Stop()
		this.timeoutTimer = time.AfterFunc(time.Duration(timeout)*time.Minute, func() {
			this.Cancel(CancelReasonDeadline)
		})
	}
}

func (this *Instruction) IsForce() bool {
	return this.Force
}

func (this *Instruction) UpdateReportOrderPriceSlippage() {
	pair := this.GetPair()
	buyType := pair.GetBuyItem().Code.InstrumentType()
	buySymbol := pair.GetBuyItem().Symbol
	sellType := pair.GetSellItem().Code.InstrumentType()
	sellSymbol := pair.GetSellItem().Symbol
	this.GetExecution().UpdateOrderPriceSlippage(this.arbitrager, buyType, buySymbol, sellType, sellSymbol)
}

func calculateSpotAvgPrice(orders []*[]*exchange.Order) (price, slippage float64) {
	var (
		spotValue          float64
		spotAmount         float64
		lastPriceTotalCost float64
	)
	for _, spotOrders := range orders {
		for _, spotOrder := range *spotOrders {
			if spotOrder.ExecQty != 0 {
				spotValue += spotOrder.ExecPrice * spotOrder.ExecQty
				spotAmount += spotOrder.ExecQty

				lastPriceTotalCost += spotOrder.ExecQty * spotOrder.LastPrice
			}

			if spotOrder.ExecQty == spotOrder.Qty {
				spotOrder.Status = exchange.OrderStatusFilled
			} else if spotOrder.ExecQty > 0 {
				spotOrder.Status = exchange.OrderStatusPartialCancelled
			} else {
				spotOrder.Status = exchange.OrderStatusCancelled
			}
		}
	}

	if spotAmount == 0 {
		return 0, 0
	}

	price = spotValue / spotAmount
	if math.IsNaN(price) {
		price = -1
		zlog.Warnf("spot avg price == -1. spot amount: %.5f, spot value: %.2f", spotAmount, spotValue)
	}

	slippage = (price - lastPriceTotalCost/spotAmount) / (lastPriceTotalCost / spotAmount)
	if math.IsNaN(slippage) {
		slippage = -1
	}

	return price, slippage
}

func calculateFutureAvgPrice(controller *ArbitragerController, futureSymbol string, orders []*[]*exchange.Order) (price, slippage float64) {
	var (
		futureValue        float64
		futureCnt          float64
		lastPriceTotalCost float64
	)
	for _, futureOrders := range orders {
		for _, futureOrder := range *futureOrders {
			if futureOrder.ExecQty != 0 {
				futureValue += controller.Qty2Size(futureSymbol, futureOrder.ExecPrice, futureOrder.ExecQty)
				futureCnt += futureOrder.ExecQty

				lastPriceTotalCost += controller.Qty2Size(futureSymbol, futureOrder.LastPrice, futureOrder.ExecQty)
			}

			if futureOrder.ExecQty == futureOrder.Qty {
				futureOrder.Status = exchange.OrderStatusFilled
			} else if futureOrder.ExecQty > 0 {
				futureOrder.Status = exchange.OrderStatusPartialCancelled
			} else {
				futureOrder.Status = exchange.OrderStatusCancelled
			}
		}
	}
	if futureCnt == 0 {
		return 0, 0
	}

	price = controller.CalcPrice(futureSymbol, futureCnt, futureValue)
	if math.IsNaN(price) {
		price = -1
		zlog.Warnf("future avg price == -1. future cnt: %.5f, future value: %.5f", futureCnt, futureValue)
	}

	lastPrice := controller.CalcPrice(futureSymbol, futureCnt, lastPriceTotalCost)
	slippage = (price - lastPrice) / lastPrice
	if math.IsNaN(slippage) {
		slippage = -1
	}

	return price, slippage
}

func (this *InstructionExecution) UpdateOrderPriceSlippage(controller *ArbitragerController, buyType exchange.InstrumentType, buySymbol string, sellType exchange.InstrumentType, sellSymbol string) {
	if buyType == exchange.Spot {
		this.BuyAvgPrice, this.BuySlippage = calculateSpotAvgPrice(this.BuyOrders)
	} else {
		this.BuyAvgPrice, this.BuySlippage = calculateFutureAvgPrice(controller, buySymbol, this.BuyOrders)
	}

	if sellType == exchange.Spot {
		this.SellAvgPrice, this.SellSlippage = calculateSpotAvgPrice(this.SellOrders)
	} else {
		this.SellAvgPrice, this.SellSlippage = calculateFutureAvgPrice(controller, sellSymbol, this.SellOrders)
	}
}

func (this *Instruction) SetSingleSideReason(reason SingleSideReason) {
	this.Report.SingleSideReason = reason
}

func (this *Instruction) IsStarted() bool {
	return this.Report.StartTime != nil
}

func (this *Instruction) UpdateAccountWorth(worthType AccountWorthType, showErrorMsg bool) (float64, error) {
	if worth, err := this.arbitrager.GetAccountWorth(false); err == nil {
		if worthType == AccountWorthTypeBeforeInstruction {
			this.Report.AccountWorthBefore = worth
		} else if worthType == AccountWorthTypeAfterInstruction {
			this.Report.AccountWorthAfter = worth
		}
		return worth, nil
	} else {
		if worthType == AccountWorthTypeBeforeInstruction {
			this.Report.AccountWorthBefore = -1
			if showErrorMsg {
				this.arbitrager.SendMsgf("指令执行前，更新账户总价值时出错：%s", err)
			}
		} else if worthType == AccountWorthTypeAfterInstruction {
			this.Report.AccountWorthAfter = -1
			if showErrorMsg {
				this.arbitrager.SendMsgf("指令执行完成后，更新账户总价值时出错：%s", err)
			}
		}
		return -1, err
	}
}

func (this *Instruction) IsFunding() bool {
	return this.Funding
}

func (this *Instruction) GetExecution() *InstructionExecution {
	if this.Direction == ArbiDirectionClose || this.Direction == ArbiDirectionOpen {
		return this.Report.Executions[0]
	} else {
		return nil
	}
}

func (this *Instruction) formatPrice(instrumentType exchange.InstrumentType, symbol string, price float64) string {
	return this.arbitrager.Exchange.FormatPrice(instrumentType, symbol, price)
}

func (this *Instruction) GetPair() *ArbiPair {
	if this.Direction == ArbiDirectionClose || this.Direction == ArbiDirectionOpen {
		return this.Pairs[0]
	} else {
		return nil
	}
}

func (this *Instruction) GetFromPair() *ArbiPair {
	panic("instruciton not implemented get from pair")
}

func (this *Instruction) GetToPair() *ArbiPair {
	panic("instruction not implemented get to pair")
}

func UpdateDurationStatsFromDurations(execution *InstructionExecution) {
	totalDuration := time.Duration(time.Millisecond * 0)
	for _, v := range execution.Durations {
		execution.DurationStats[v.Type] += v.Duration
		totalDuration += v.Duration
	}
	execution.DurationStats[ExecutionDurationTypeTotal] = totalDuration
}

// 清除 Execution，防止写入 storage
func (this *Instruction) MigrateClearData() {
}

func (this *Instruction) GetReport() *InstructionReport {
	return this.Report
}

// 原 GetBookName 函数
func (this *Instruction) GetMainSymbol() string {
	return this.GetPair().GetSellItem().Symbol
}

func (this *Instruction) GetTargetBasisRatio() float64 {
	return this.TargetBasisRatio
}

func (this *Instruction) GetQty() float64 {
	return this.Qty
}

func (this *Instruction) GetPrice() float64 {
	return this.Price
}

func (this *Instruction) GetQtyFilled() float64 {
	if this.Direction == ArbiDirectionOpen {
		return math.Abs(this.GetExecution().UsdtQty)
	} else if this.Direction == ArbiDirectionClose {
		return math.Abs(this.GetExecution().BuyQty)
	}
	return 0.0
}

func (this *Instruction) GetConfigSnapshot() *ArbitragerConfig {
	return this.ConfigSnapshot
}

func (this *Instruction) GetOrderRow() (row []string) {
	row = []string{}
	if this.Direction == ArbiDirectionOpen {
		row = []string{
			this.GetID(),
			this.GetPair().GetSellItem().Symbol,
			string(this.GetDirection()),
			fmt.Sprintf("%+.2f%%", this.GetTargetBasisRatio()*100),
			fmt.Sprintf("+%.0f %s", this.GetQty(), this.GetPair().GetSellItem().Code.USDXSymbol),
			fmt.Sprintf("+%.0f %s", this.GetQtyFilled(), this.GetPair().GetSellItem().Code.USDXSymbol),
			fmt.Sprintf("%.2f%%", this.GetQtyFilled()/this.GetQty()*100),
		}
	} else if this.Direction == ArbiDirectionClose {
		row = []string{
			this.GetID(),
			this.GetPair().GetBuyItem().Symbol,
			string(this.GetDirection()),
			fmt.Sprintf("%+.2f%%", this.GetTargetBasisRatio()*100),
			fmt.Sprintf("-%s cnt", this.FormatFutureQty(this.GetPair().GetBuyItem().Symbol, this.GetQty())),
			fmt.Sprintf("-%s cnt", this.FormatFutureQty(this.GetPair().GetBuyItem().Symbol, this.GetQtyFilled())),
			fmt.Sprintf("%.2f%%", this.GetQtyFilled()/this.GetQty()*100),
		}
	}
	return
}

func (this *Instruction) DeleteConfigSnapshot() {
	this.ConfigSnapshot = nil
}

func (this *InstructionExecution) removeOrderByIDs(orderIDs []string) (removedIDs []string) {
	for _, orderID := range orderIDs {
		for i, orders := range this.BuyOrders {
			index := -1
			for j, order := range *orders {
				if order.OrderID == orderID {
					index = j
					break
				}
			}
			if index >= 0 {
				former := (*orders)[:index]
				later := (*orders)[index+1:]
				former = append(former, later...)
				this.BuyOrders[i] = &former
				removedIDs = append(removedIDs, orderID)
			}
		}

		for i, orders := range this.SellOrders {
			index := -1
			for j, order := range *orders {
				if order.OrderID == orderID {
					index = j
					break
				}
			}
			if index >= 0 {
				former := (*orders)[:index]
				later := (*orders)[index+1:]
				former = append(former, later...)
				this.SellOrders[i] = &former
				removedIDs = append(removedIDs, orderID)
			}
		}

		for i, orders := range this.BNBOrders {
			index := -1
			for j, order := range *orders {
				if order.OrderID == orderID {
					index = j
					break
				}
			}
			if index >= 0 {
				former := (*orders)[:index]
				later := (*orders)[index+1:]
				former = append(former, later...)
				this.BNBOrders[i] = &former
				removedIDs = append(removedIDs, orderID)
			}
		}
	}
	return
}

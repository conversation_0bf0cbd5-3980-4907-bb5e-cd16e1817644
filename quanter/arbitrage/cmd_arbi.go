package arbitrage

import (
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type InstructionCommand struct {
	command.Command
	arbitrager   *ArbitragerController
	instructions []Instructable
}

type ArbiArbitragerCommand struct {
	InstructionCommand
	isForce bool
}

func (this *InstructionCommand) PrePrepare() bool {
	this.instructions = []Instructable{}
	// 第1个位置是 FutureCode ，允许3种格式：
	// A. FutureCode1,FutureCode2
	// B. FromFutureCode1>ToFutureCode1,FromFutureCode2>ToFutureCode2
	// C. FutureCode1~FutureCode2,FutureCode3~FutureCode4
	// 此处检查 FutureCode 的合法性时，兼容以上格式
	parts := strings.Split(this.Args[0], ",")
	futureCodes := []string{}
	for _, part := range parts {
		var codes []string
		if strings.Contains(part, ">") {
			codes = strings.Split(part, ">")
		} else {
			codes = strings.Split(part, "~")
		}
		futureCodes = append(futureCodes, codes...)
	}
	for _, futureCode := range futureCodes {
		// 不做错误处理，不检查 futureCode 合法性，因为 Prepare 中会检查
		if symbolCode, err := NewSymbolCode(this.arbitrager, futureCode); err == nil {
			commandName := this.GetName()
			if commandName != "buy" && commandName != "sell" && symbolCode.IsSpot() {
				this.ErrorMsgf("参数错误：不允许输入现货品种代码 %s 。", symbolCode)
				return false
			}

			if symbolCode.IsFuture() && symbolCode.InstrumentType() != this.arbitrager.FutureInstrumentType() {
				this.ErrorMsgf("当前仅允许 %s 类型的合约", this.arbitrager.FutureInstrumentType())
				return false
			}
		}
	}

	if CheckCommandExecuterAndUpdateBalance(this.arbitrager) {
		// 尝试重新获取数量快照
		// 如果发现 Book 中的 balance 和 available 数量出现问题，只需要取消全部挂单重新挂单，即可触发重新获取数量快照
		// 函数内会检查是否有正在运行的指令，如果有正在运行的指令，会直接返回什么也不做
		go this.arbitrager.UpdateBalance(false)
		return true
	} else {
		return false
	}
}

// 统一打印指令列表
func (this *InstructionCommand) PostPrepare() bool {

	if len(this.instructions) > 0 {
		firstInstruction := this.instructions[0]

		// 如果需要购买 BNB，检查是否有足够的 USDx 用于购买 BNB
		totalEstimatedBNB := 0.0
		totalEstimatedUSDx := 0.0
		warnEstimatedUSDx := false
		bnbMsg := ""
		for _, instruction := range this.instructions {
			dir := instruction.GetDirection()
			if dir.IsRotate() {
				continue
			}
			if instruction.NeedBuyBNB() {
				if estimatedBNB, estimatedUSDx, err := instruction.GetEstimatedBNB(); err != nil {
					this.WarnMsgf("\n计算需要的 BNB 数量出错。%s\n", err)
				} else {
					totalEstimatedBNB += estimatedBNB
					totalEstimatedUSDx += estimatedUSDx
				}
			}
		}
		if totalEstimatedUSDx > 0 {
			uSymbol := this.arbitrager.Config.USDXSymbol
			if _, availableUSDx, err := this.arbitrager.Exchange.GetHoldingQty(exchange.Spot, uSymbol); err == nil {
				if totalEstimatedUSDx > availableUSDx {
					warnEstimatedUSDx = true
					bnbMsg = fmt.Sprintf("%s 不足。需要 %.1f 购买 BNB，实际只有 %.1f。\n", uSymbol, totalEstimatedUSDx, availableUSDx)
				} else {
					bnbMsg = fmt.Sprintf("\n使用 BNB 抵扣，需要购买 [%.3f] BNB，花费 %.1f %s\n", totalEstimatedBNB, totalEstimatedUSDx, uSymbol)
				}
			}
		}

		configDiff := ""
		if diff, err := baseconfig.RenderConfigDiff(firstInstruction.GetConfig(), firstInstruction.GetConfigSnapshot(), true); err != nil {
			configDiff = fmt.Sprintf("打印配置文件更改出现错误：%s", err)
		} else {
			configDiff = diff
		}

		if configDiff == "" {
			this.SendMsgf("\n请确认以下指令：\n```%s```", RenderRequestForInstructions(this.instructions, false))
		} else {
			this.SendMsgf("\n请确认以下指令：\n```%s\n\n%s```", RenderRequestForInstructions(this.instructions, false), configDiff)
		}

		if warnEstimatedUSDx {
			this.ErrorMsgf(bnbMsg)
		} else {
			if !strings.EqualFold(bnbMsg, "") {
				this.SendMsgf(bnbMsg)
			}
		}
		return true
	} else {
		this.ErrorMsgf("没有需要执行的命令。")
		return false
	}
}

// InstructionCommand 的 Do 都是执行命令，共用这部分代码
func (this *InstructionCommand) Do() bool {
	instructionIDs := []string{}

	if len(this.instructions) == 0 {
		this.ErrorMsgf("命令没有准备好。")
		return false
	} else {
		for _, instruction := range this.instructions {
			this.arbitrager.ProcessInstruction(instruction)
			instructionIDs = append(instructionIDs, instruction.GetID())
		}
		this.SendMsgf("已执行指令：[%s]", strings.Join(instructionIDs, ","))
		return true
	}
}

func NewArbiArbitragerCommand(arbitrager *ArbitragerController) *ArbiArbitragerCommand {
	cmd := &ArbiArbitragerCommand{
		InstructionCommand: InstructionCommand{
			Command: command.Command{
				Name:            "arbi",
				Alias:           []string{"a"},
				Instruction:     "`.arbi SymbolPair1,SymbolPair2 100%%/qty targetBasisRatio(可选) config1=value1,config2=value2(可选)` 开仓套利",
				RequiresConfirm: true,
				ArgMin:          2,
				ArgMax:          4,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *ArbiArbitragerCommand) Prepare() bool {
	configStr := ""
	targetRatioStr := ""
	targetBasisRatio := 0.0

	if len(this.Args) == 4 {
		configStr = this.Args[3]
		targetRatioStr = this.Args[2]
	} else if len(this.Args) == 3 && (strings.Contains(this.Args[2], "=") || strings.Contains(this.Args[2], "_")) {
		configStr = this.Args[2]
	} else if len(this.Args) == 3 && !strings.Contains(this.Args[2], "=") && !strings.Contains(this.Args[2], "_") {
		targetRatioStr = this.Args[2]
	}

	if strings.HasPrefix(configStr, "_") {
		if saveValue, err := this.arbitrager.Config.GetConfigSave(configStr); err != nil {
			this.ErrorMsgf("配置模板错误：%s", err)
			return false
		} else {
			configStr = saveValue
		}
	}

	if targetRatioStr != "" {
		if r, _, err := utils.ParseFloatOrPercentage(targetRatioStr, false, true); err != nil {
			this.ErrorMsgf("参数错误：targetBasisRatio %s", err)
			return false
		} else {
			targetBasisRatio = r
			this.arbitrager.Infof("parsed target basis ratio %.4f", r)
		}
	}

	if num, percent, err := ParseNumOrPercentage(this.Args[1], false, false); err != nil {
		this.ErrorMsgf("参数错误：%s", err.Error())
		return false
	} else {
		// FutureCode 参数允许填多个，共用后续的参数
		futureCodes := strings.Split(this.Args[0], ",")
		for _, futureCode := range futureCodes {
			// 找不到品种不要直接报错，报告问题就可以了
			// 可能在不同交易所使用相同的命令，个别品种在其他平台上没有影响并不大
			if strings.Contains(futureCode, "~") {
				codes := strings.Split(futureCode, "~")
				if len(codes) != 2 {
					this.ErrorMsgf("请输入正确的期期套利品种格式，\"%s\" 中只能有一个\"~\"。", futureCode)
					return false
				}
				leftCode := codes[0]
				rightCode := codes[1]

				if leftSymbolCode, err := NewSymbolCode(this.arbitrager, leftCode); err != nil {
					this.WarnMsgf("找不到品种代码 %s：%s", leftCode, err.Error())
				} else {
					if rightSymbolCode, err := NewSymbolCode(this.arbitrager, rightCode); err != nil {
						this.WarnMsgf("找不到品种代码 %s：%s", rightCode, err.Error())
					} else {
						if instruction, err := NewFuture2FutureInstruction(this.arbitrager, ArbiDirectionOpen, this.isForce, leftSymbolCode, rightSymbolCode, num, percent, targetBasisRatio, configStr); err != nil {
							if strings.Contains(err.Error(), "qty can not be 0") {
								continue
							}
							if strings.Contains(err.Error(), "qty not enough") {
								this.ErrorMsgf("没有足够可用的 %s。%s", err, leftSymbolCode.USDXSymbol)
								return false
							}
							this.ErrorMsgf("创建指令出现错误：%s", err.Error())
							return false
						} else {
							this.instructions = append(this.instructions, instruction)
						}
					}
				}

				continue
			}

			if symbolCode, err := NewSymbolCode(this.arbitrager, futureCode); err != nil {
				this.WarnMsgf("找不到品种代码 %s：%s", futureCode, err.Error())
				continue
			} else {
				if instruction, err := NewInstruction(this.arbitrager, ArbiDirectionOpen, this.isForce, symbolCode, num, percent, targetBasisRatio, configStr); err != nil {
					if strings.Contains(err.Error(), "qty can not be 0") {
						continue
					}
					if strings.Contains(err.Error(), "qty not enough") {
						this.ErrorMsgf("没有足够可用的 %s。%s", err, symbolCode.USDXSymbol)
						return false
					}
					this.ErrorMsgf("创建指令出现错误：%s", err.Error())
					return false
				} else {
					this.instructions = append(this.instructions, instruction)
					go instruction.UpdateAccountWorth(AccountWorthTypeBeforeInstruction, true)
				}
			}
		}
	}
	return true
}

type ArbiForceArbitragerCommand struct {
	ArbiArbitragerCommand
}

func NewArbiForceArbitragerCommand(arbitrager *ArbitragerController) *ArbiForceArbitragerCommand {
	cmd := &ArbiForceArbitragerCommand{
		ArbiArbitragerCommand: ArbiArbitragerCommand{
			InstructionCommand: InstructionCommand{
				Command: command.Command{
					Name:            "arbiForce",
					Alias:           []string{"af"},
					Instruction:     "`.arbiForce SymbolPair1,SymbolPair2 100%%/qty config1=value1,config2=value2` 开仓套利，不检查目标价差",
					RequiresConfirm: true,
					ArgMin:          2,
					ArgMax:          3,
					AuthcodePos:     -1,
				},
				arbitrager: arbitrager,
			},
			isForce: true,
		},
	}
	return cmd
}

type CloseArbitragerCommand struct {
	InstructionCommand
	isForce bool
}

func NewCloseArbitragerCommand(arbitrager *ArbitragerController) *CloseArbitragerCommand {
	cmd := &CloseArbitragerCommand{
		InstructionCommand: InstructionCommand{
			Command: command.Command{
				Name:            "close",
				Alias:           []string{"c"},
				Instruction:     "`.close SymbolPair1,SymbolPair2 100%%/qty targetBasisRatio(可选) config1=value1,config2=value2(可选)` 平仓套利",
				RequiresConfirm: true,
				ArgMin:          2,
				ArgMax:          4,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
		isForce: false,
	}
	return cmd
}

func (this *CloseArbitragerCommand) Prepare() bool {
	futureCodeStr := this.Args[0]
	configStr := ""
	targetRatioStr := ""
	targetBasisRatio := 0.0

	if len(this.Args) == 4 {
		configStr = this.Args[3]
		targetRatioStr = this.Args[2]
	} else if len(this.Args) == 3 && (strings.Contains(this.Args[2], "=") || strings.Contains(this.Args[2], "_")) {
		configStr = this.Args[2]
	} else if len(this.Args) == 3 && !strings.Contains(this.Args[2], "=") && !strings.Contains(this.Args[2], "_") {
		targetRatioStr = this.Args[2]
	}
	if strings.HasPrefix(configStr, "_") {
		if saveValue, err := this.arbitrager.Config.GetConfigSave(configStr); err != nil {
			this.ErrorMsgf("配置模板错误：%s", err)
			return false
		} else {
			configStr = saveValue
		}
	}
	if targetRatioStr != "" {
		if r, _, err := utils.ParseFloatOrPercentage(targetRatioStr, false, true); err != nil {
			this.ErrorMsgf("参数错误：targetBasisRatio %s", err)
			return false
		} else {
			targetBasisRatio = r
			this.arbitrager.Infof("parsed target basis ratio %.4f", r)
		}
	}

	if num, percent, err := ParseNumOrPercentage(this.Args[1], false, false); err != nil {
		this.ErrorMsgf("参数错误：%s", err.Error())
		return false
	} else {
		futureCodes := strings.Split(futureCodeStr, ",")
		for _, codeStr := range futureCodes {
			if strings.Contains(codeStr, "~") {
				codes := strings.Split(codeStr, "~")
				if len(codes) != 2 {
					this.ErrorMsgf("请输入正确的期期套利品种格式，\"%s\" 中只能有一个\"~\"。", codeStr)
					return false
				}
				leftCode := codes[0]
				rightCode := codes[1]

				if leftSymbolCode, err := NewSymbolCode(this.arbitrager, leftCode); err != nil {
					this.WarnMsgf("找不到品种代码 %s：%s", leftCode, err.Error())
				} else {
					if rightSymbolCode, err := NewSymbolCode(this.arbitrager, rightCode); err != nil {
						this.WarnMsgf("找不到品种代码 %s：%s", rightCode, err.Error())
					} else {
						if instruction, err := NewFuture2FutureInstruction(this.arbitrager, ArbiDirectionClose, this.isForce, leftSymbolCode, rightSymbolCode, num, percent, targetBasisRatio, configStr); err != nil {
							if strings.Contains(err.Error(), "qty can not be 0") {
								continue
							}
							if strings.Contains(err.Error(), "qty not enough") {
								this.ErrorMsgf("品种 %s 下没有足够的可平仓位。%s", rightSymbolCode, err)
								return false
							}
							this.ErrorMsgf("创建指令出现错误：%s", err.Error())
							return false
						} else {
							this.instructions = append(this.instructions, instruction)
						}
					}
				}

				continue
			}

			if futureCode, err := NewSymbolCode(this.arbitrager, codeStr); err != nil {
				this.WarnMsgf("找不到品种代码 %s：%s", codeStr, err.Error())
				continue
			} else {
				if instruction, err := NewInstruction(this.arbitrager, ArbiDirectionClose, this.isForce, futureCode, num, percent, targetBasisRatio, configStr); err != nil {
					if strings.Contains(err.Error(), "qty can not be 0") {
						this.arbitrager.Warnf("qty is 0 for %s", futureCode)
						continue
					}
					if strings.Contains(err.Error(), "qty not enough") {
						this.ErrorMsgf("品种 %s 下没有足够的可平仓位。%s", futureCode, err)
						return false
					}
					this.ErrorMsgf("创建指令出现错误：%s", err.Error())
					return false
				} else {
					this.instructions = append(this.instructions, instruction)
				}
			}
		}
	}
	return true
}

type CloseForceArbitragerCommand struct {
	CloseArbitragerCommand
}

func NewCloseForceArbitragerCommand(arbitrager *ArbitragerController) *CloseForceArbitragerCommand {
	cmd := &CloseForceArbitragerCommand{
		CloseArbitragerCommand: CloseArbitragerCommand{
			InstructionCommand: InstructionCommand{
				Command: command.Command{
					Name:            "closeForce",
					Alias:           []string{"cf"},
					Instruction:     "`.closeForce SymbolPair1,SymbolPair2 100%%/qty config1=value1,config2=value2` 平仓套利，不检查目标价差",
					RequiresConfirm: true,
					ArgMin:          2,
					ArgMax:          3,
					AuthcodePos:     -1,
				},
				arbitrager: arbitrager,
			},
			isForce: true,
		},
	}
	return cmd
}

type CloseAllArbitragerCommand struct {
	InstructionCommand
}

func (this *CloseAllArbitragerCommand) PrePrepare() bool {
	return CheckCommandExecuterAndUpdateBalance(this.arbitrager)
}

func NewCloseAllArbitragerCommand(arbitrager *ArbitragerController) *CloseAllArbitragerCommand {
	cmd := &CloseAllArbitragerCommand{
		InstructionCommand: InstructionCommand{
			Command: command.Command{
				Name:            "closeAll",
				Instruction:     "`.closeAll config1=value1,config2=value2(可选)` 强制平仓所有品种，不检查溢价率",
				RequiresConfirm: true,
				ArgMin:          0,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *CloseAllArbitragerCommand) Prepare() bool {
	configStr := ""
	this.instructions = []Instructable{}

	if len(this.Args) == 1 {
		configStr = this.Args[0]
	}
	if strings.HasPrefix(configStr, "_") {
		if saveValue, err := this.arbitrager.Config.GetConfigSave(configStr); err != nil {
			this.ErrorMsgf("配置模板错误：%s", err)
			return false
		} else {
			configStr = saveValue
		}
	}

	futureCodes, err := this.arbitrager.GetAllSymbolCodes()
	if err != nil {
		this.ErrorMsgf("获取品种代码出错，error: %s", err)
		return false
	}
	for _, futureCode := range futureCodes {
		if instruction, err := NewInstruction(this.arbitrager, ArbiDirectionClose, true, futureCode, 0, 1, 0, configStr); err != nil {
			if strings.Contains(err.Error(), "qty not enough") {
				this.ErrorMsgf("品种 %s 下没有足够的可平仓位。%s", futureCode, err)
				return false
			}
			this.ErrorMsgf("创建指令出现错误：%s", err.Error())
			return false
		} else {
			this.instructions = append(this.instructions, instruction)
		}
	}

	this.WarnMsgf("将以期现模式平仓所有品种")
	return true
}

type BatchCloseArbitragerCommand struct {
	ArbitragerExecuterCommand
	instructions        []Instructable
	affactedFutureCodes []string
}

func (this *BatchCloseArbitragerCommand) PrePrepare() bool {
	return CheckCommandExecuterAndUpdateBalance(this.arbitrager)
}

func NewBatchCloseArbitragerCommand(arbitrager *ArbitragerController) *BatchCloseArbitragerCommand {
	cmd := &BatchCloseArbitragerCommand{
		ArbitragerExecuterCommand: ArbitragerExecuterCommand{
			Command: command.Command{
				Name:            "batchClose",
				Instruction:     "`.batchClose SymbolPair1,SymbolPair2:Basis1=Ratio1,Basis2=Ratio2...@_ConfigSave|...` 批量平仓",
				RequiresConfirm: true,
				ArgMin:          0,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
		instructions:        []Instructable{},
		affactedFutureCodes: []string{},
	}
	return cmd
}

func (this *BatchCloseArbitragerCommand) Prepare() bool {
	this.instructions = []Instructable{}
	this.affactedFutureCodes = []string{}

	// 检查持仓保存所有有持仓的品种代码，后续处理过程中自动跳过无持仓的品种，而不是报错
	codesHasPosition := []string{}
	if positions, err := this.arbitrager.Exchange.GetPositions(this.arbitrager.FutureInstrumentType(), "", false); err != nil {
		this.ErrorMsgf("获取期货持仓出错：%s", err)
		return false
	} else {
		for _, pos := range positions {
			futureSymbol := pos.Symbol
			uSymbol := this.arbitrager.Config.USDXSymbol
			if _, futureCode, err := this.arbitrager.Exchange.TranslateFutureSymbol(this.arbitrager.FutureInstrumentType(), futureSymbol, uSymbol); err != nil {
				// 如果不在允许品种列表中，是正常的；此外报错返回
				if !strings.Contains(err.Error(), "invalid future code") {
					this.ErrorMsgf("翻译 FutureSymbol %s 出错：%s", futureSymbol, err)
					return false
				}
			} else {
				codesHasPosition = append(codesHasPosition, futureCode.Code)
			}
		}
	}

	batchInstructions := strings.Split(this.Args[0], "|")
	mergedInstructions := [][]Instructable{}

	if len(batchInstructions) == 0 {
		this.ErrorMsgf("指令格式错误，不能是一个空的指令。")
		return false
	}

	for _, batchInstruction := range batchInstructions {
		sameTemplateInstructions := []Instructable{}
		parts := strings.Split(batchInstruction, "@")
		instructionPart := parts[0]
		configStr := ""
		if len(parts) > 1 {
			configStr = parts[1]
		}

		if saveValue, err := this.arbitrager.Config.GetConfigSave(configStr); err != nil {
			this.ErrorMsgf("配置模板错误：%s", err)
			return false
		} else {
			configStr = saveValue
		}

		if codeRatioParts := strings.Split(instructionPart, ":"); len(codeRatioParts) != 2 {
			this.ErrorMsgf("指令格式错误，\"%s\"中有且仅能有一个\":\"。", instructionPart)
			return false
		} else {
			codePart := codeRatioParts[0]
			ratioPart := codeRatioParts[1]
			codes := strings.Split(codePart, ",")
			ratios := strings.Split(ratioPart, ",")

			if len(ratios) == 0 {
				this.ErrorMsgf("指令格式错误，\"%s\" 中有且仅能有一个\",\"。", ratioPart)
				return false
			}

			// 先循环 codes ，可以让同一个品种的指令打印的时候在一起
			sleepForEachCode := false
			if this.arbitrager.Exchange.GetName() == exchange.OKEx && len(codes)*len(ratios) > 8 {
				this.SendMsgf("因为 OKEx 频率限制，并且批量平仓指令数量超过 8 个，每个品种需要额外等待 2 秒。")
				sleepForEachCode = true
			}
			for i, code := range codes {
				if sleepForEachCode && i > 0 {
					this.SendMsgf("等待处理品种 (%s)，2 秒...", code)
					time.Sleep(2 * time.Second)
				}

				var leftCode, rightCode *exchange.SymbolCode
				if strings.Contains(code, "~") {
					codes := strings.Split(code, "~")
					if len(codes) != 2 {
						this.ErrorMsgf("指令格式错误，\"%s\" 中有且仅能有一个\"~\"。", code)
						return false
					}
					leftCodeStr := codes[0]
					rightCodeStr := codes[1]

					if leftSymbolCode, err := NewSymbolCode(this.arbitrager, leftCodeStr); err != nil {
						this.WarnMsgf("找不到品种代码 %s：%s", leftCodeStr, err.Error())
						continue
					} else {
						if rightSymbolCode, err := NewSymbolCode(this.arbitrager, rightCodeStr); err != nil {
							this.WarnMsgf("找不到品种代码 %s：%s", rightCodeStr, err.Error())
							continue
						} else {
							leftCode = leftSymbolCode
							rightCode = rightSymbolCode
						}
					}
				} else {
					if symbolCode, err := NewSymbolCode(this.arbitrager, code); err != nil {
						this.WarnMsgf("找不到品种代码 %s：%s", code, err.Error())
						continue
					} else {
						rightCode = symbolCode
					}
				}

				// 如果 code 品种没有持仓不要生成 Instruction，跳过，否则会 qty = 0 的错误
				if !utils.SliceContainsEqualFold(codesHasPosition, rightCode.Code) {
					continue
				}
				if leftCode != nil && !utils.SliceContainsEqualFold(codesHasPosition, leftCode.Code) {
					continue
				}

				this.affactedFutureCodes = append(this.affactedFutureCodes, rightCode.Code)

				for _, ratioStr := range ratios {
					if ratioParts := strings.Split(ratioStr, "="); len(ratioParts) != 2 {
						this.ErrorMsgf("指令格式错误，\"%s\"中有且仅能有一个\"=\"。", ratioStr)
						return false
					} else {
						basis := ratioParts[0]
						percentage := ratioParts[1]
						if basisNum, _, err := utils.ParseFloatOrPercentage(basis, false, true); err != nil {
							this.ErrorMsgf("指令格式错误，不能解析 %s 为数字或百分比。", basis)
							return false
						} else {
							if percentageNum, _, err := utils.ParseFloatOrPercentage(percentage, false, true); err != nil {
								this.ErrorMsgf("指令格式错误，不能解析 %s 为数字或百分比。", percentage)
								return false
							} else {
								var instruction *Instruction
								var err error
								if leftCode != nil {
									instruction, err = NewFuture2FutureInstruction(this.arbitrager, ArbiDirectionClose, false, leftCode, rightCode, 0, percentageNum, basisNum, configStr)
								} else {
									instruction, err = NewInstruction(this.arbitrager, ArbiDirectionClose, false, rightCode, 0, percentageNum, basisNum, configStr)
								}
								if err != nil {
									this.ErrorMsgf("创建指令 (%s) 出现错误：%s，跳过", rightCode, err.Error())
								} else {
									this.instructions = append(this.instructions, instruction)
									sameTemplateInstructions = append(sameTemplateInstructions, instruction)
								}
							}
						}
					}
				}
			}
		}
		mergedInstructions = append(mergedInstructions, sameTemplateInstructions)
	}

	// 如果需要购买 BNB，检查是否有足够的 USDx 用于购买 BNB
	totalEstimatedBNB := 0.0
	totalEstimatedUSDx := 0.0
	warnEstimatedUSDx := false
	bnbMsg := ""
	for _, group := range mergedInstructions {
		for _, instruction := range group {
			if instruction.NeedBuyBNB() {
				if estimatedBNB, estimatedUSDx, err := instruction.GetEstimatedBNB(); err != nil {
					this.WarnMsgf("\n计算需要的 BNB 数量出错。%s\n", err)
				} else {
					totalEstimatedBNB += estimatedBNB
					totalEstimatedUSDx += estimatedUSDx
				}
			}
		}
	}
	if totalEstimatedUSDx > 0 {
		uSymbol := this.arbitrager.Config.USDXSymbol
		if _, availableUSDx, err := this.arbitrager.Exchange.GetHoldingQty(exchange.Spot, uSymbol); err == nil {
			if totalEstimatedUSDx > availableUSDx {
				warnEstimatedUSDx = true
				bnbMsg = fmt.Sprintf("%s 不足。需要 %.1f，实际只有 %.1f。\n", uSymbol, totalEstimatedUSDx, availableUSDx)
			} else {
				bnbMsg = fmt.Sprintf("\n使用 BNB 抵扣，需要购买 [%.3f] BNB，花费 %.1f %s\n", totalEstimatedBNB, totalEstimatedUSDx, uSymbol)
			}
		}
	}

	for _, group := range mergedInstructions {
		configDiff := ""
		if len(group) > 0 {
			groupFirst := group[0]
			if diff, err := baseconfig.RenderConfigDiff(groupFirst.GetConfig(), groupFirst.GetConfigSnapshot(), true); err != nil {
				configDiff = fmt.Sprintf("打印配置文件梗概出现错误：%s", err)
			} else {
				configDiff = diff
			}
			if configDiff == "" {
				this.SendMsgf("\n\n请确认以下平仓指令：\n```%s```", RenderRequestForInstructions(group, false))
			} else {
				this.SendMsgf("\n\n请确认以下平仓指令：\n```%s\n\n%s```", RenderRequestForInstructions(group, false), configDiff)
			}
			if warnEstimatedUSDx {
				this.ErrorMsgf(bnbMsg)
			} else {
				if !strings.EqualFold(bnbMsg, "") {
					this.SendMsgf(bnbMsg)
				}
			}
		}
	}
	return true
}

func (this *BatchCloseArbitragerCommand) Do() bool {
	allInstructionIDs := ""
	if instructions := FilterInstructions(this.arbitrager.Instructions, InstructionStatusRunning, nil, &allInstructionIDs); len(instructions) == 0 {
		this.SendMsgf("没有正在执行的指令。通过 `.status all` 可以查询命令状态。")
	} else {
		for _, instruction := range instructions {
			affacted := false // 仅有平仓指令受影响，不取消 move 和 rotate 指令
			for _, affactedCode := range this.affactedFutureCodes {
				if symbolCode := instruction.GetCloseSymbolCode(); symbolCode != nil {
					if instruction.GetDirection() == ArbiDirectionClose && strings.EqualFold(symbolCode.Code, strings.ToUpper(affactedCode)) {
						affacted = true
					}
				}
			}
			if affacted {
				instruction.Cancel(CancelReasonBatchClose)
				this.SendMsgf("指令 `%s` 已取消.", instruction.GetID())
			}
		}
	}

	instructionIDs := []string{}
	for _, instruction := range this.instructions {
		this.arbitrager.ProcessInstruction(instruction)
		instructionIDs = append(instructionIDs, instruction.GetID())
	}
	this.SendMsgf("已执行平仓指令：[%s]", strings.Join(instructionIDs, ","))
	return true
}

type MoveArbitragerCommand struct {
	InstructionCommand
	isForce bool
}

func NewMoveArbitragerCommand(arbitrager *ArbitragerController) *MoveArbitragerCommand {
	cmd := &MoveArbitragerCommand{
		InstructionCommand: InstructionCommand{
			Command: command.Command{
				Name:            "move",
				Alias:           []string{"m"},
				Instruction:     "`.move FromFutureCode1>ToFutureCode1,FromFutureCode2>ToFutureCode2 100%%/qty targetBasisRatio(可选) config1=value1,config2=value2(可选)` 转移套利品种",
				RequiresConfirm: true,
				ArgMin:          2,
				ArgMax:          4,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
		isForce: false,
	}
	return cmd
}

func (this *MoveArbitragerCommand) Prepare() bool {
	configStr := ""
	targetRatioStr := ""
	targetBasisRatio := 0.0

	if len(this.Args) == 4 {
		configStr = this.Args[3]
		targetRatioStr = this.Args[2]
	} else if len(this.Args) == 3 && (strings.Contains(this.Args[2], "=") || strings.Contains(this.Args[2], "_")) {
		configStr = this.Args[2]
	} else if len(this.Args) == 3 && !strings.Contains(this.Args[2], "=") && !strings.Contains(this.Args[2], "_") {
		targetRatioStr = this.Args[2]
	}
	if strings.HasPrefix(configStr, "_") {
		if saveValue, err := this.arbitrager.Config.GetConfigSave(configStr); err != nil {
			this.ErrorMsgf("配置模板错误：%s", err)
			return false
		} else {
			configStr = saveValue
		}
	}

	if targetRatioStr != "" {
		if r, _, err := utils.ParseFloatOrPercentage(targetRatioStr, false, true); err != nil {
			this.ErrorMsgf("参数错误：targetBasisRatio %s", err)
			return false
		} else {
			targetBasisRatio = r
			this.arbitrager.Infof("parsed target basis ratio %.4f", r)
		}
	}

	if num, percent, err := ParseNumOrPercentage(this.Args[1], false, false); err != nil {
		this.ErrorMsgf("参数错误：%s", err.Error())
		return false
	} else {
		codePairs := strings.Split(this.Args[0], ",")
		for _, codePair := range codePairs {
			pair := strings.Split(codePair, "&gt;")
			if len(pair) != 2 {
				this.ErrorMsgf("参数错误：%s 格式错误。", codePair)
				return false
			}
			fromCodeStr := pair[0]
			toCodeStr := pair[1]
			if fromFutureCode, err := NewSymbolCode(this.arbitrager, fromCodeStr); err != nil {
				this.WarnMsgf("找不到品种代码 %s：%s", fromCodeStr, err.Error())
				continue
			} else {
				if toFutureCode, err := NewSymbolCode(this.arbitrager, toCodeStr); err != nil {
					this.WarnMsgf("找不到品种代码 %s：%s", toCodeStr, err.Error())
					continue
				} else {
					if strings.EqualFold(toFutureCode.Coin(), fromFutureCode.Coin()) {
						this.ErrorMsgf("参数错误：源品种和目标品种基础币种不能一样。")
						return false
					}
					if instruction, err := NewMoveInstruction(this.arbitrager, this.isForce, fromFutureCode, toFutureCode, num, percent, targetBasisRatio, configStr); err != nil {
						if strings.Contains(err.Error(), "qty can not be 0") {
							continue
						}
						if strings.Contains(err.Error(), "qty not enough") {
							this.ErrorMsgf("品种 %s 下没有足够的可平仓位。%s", fromFutureCode, err)
							return false
						}
						this.ErrorMsgf("创建指令出现错误：%s", err.Error())
						return false
					} else {
						this.instructions = append(this.instructions, instruction)
					}
				}
			}
		}
	}
	return true
}

type MoveForceArbitragerCommand struct {
	MoveArbitragerCommand
}

func NewMoveForceArbitragerCommand(arbitrager *ArbitragerController) *MoveForceArbitragerCommand {
	cmd := &MoveForceArbitragerCommand{
		MoveArbitragerCommand: MoveArbitragerCommand{
			InstructionCommand: InstructionCommand{
				Command: command.Command{
					Name:            "moveForce",
					Alias:           []string{"mf"},
					Instruction:     "`.moveForce FromFutureCode>ToFutureCode 100%%/qty config1=value1,config2=value2` 转移套利品种",
					RequiresConfirm: true,
					ArgMin:          2,
					ArgMax:          3,
					AuthcodePos:     -1,
				},
				arbitrager: arbitrager,
			},
			isForce: true,
		},
	}
	return cmd
}

type RotateArbitragerCommand struct {
	InstructionCommand
	isForce bool
}

func NewRotateArbitragerCommand(arbitrager *ArbitragerController) *RotateArbitragerCommand {
	cmd := &RotateArbitragerCommand{
		InstructionCommand: InstructionCommand{
			Command: command.Command{
				Name:            "rotate",
				Alias:           []string{"r"},
				Instruction:     "`.rotate FromFutureCode1>ToFutureCode1,FromFutureCode2>ToFutureCode2 100%%/qty targetBasisRatio(可选) config1=value1,config2=value2(可选)` 展期套利品种",
				RequiresConfirm: true,
				ArgMin:          2,
				ArgMax:          4,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
		isForce: false,
	}
	return cmd
}

func (this *RotateArbitragerCommand) Prepare() bool {
	configStr := ""
	targetRatioStr := ""
	targetBasisRatio := 0.0

	if len(this.Args) == 4 {
		configStr = this.Args[3]
		targetRatioStr = this.Args[2]
	} else if len(this.Args) == 3 && (strings.Contains(this.Args[2], "=") || strings.Contains(this.Args[2], "_")) {
		configStr = this.Args[2]
	} else if len(this.Args) == 3 && !strings.Contains(this.Args[2], "=") && !strings.Contains(this.Args[2], "_") {
		targetRatioStr = this.Args[2]
	}
	if strings.HasPrefix(configStr, "_") {
		if saveValue, err := this.arbitrager.Config.GetConfigSave(configStr); err != nil {
			this.ErrorMsgf("配置模板错误：%s", err)
			return false
		} else {
			configStr = saveValue
		}
	}

	if targetRatioStr != "" {
		if r, _, err := utils.ParseFloatOrPercentage(targetRatioStr, false, true); err != nil {
			this.ErrorMsgf("参数错误：targetBasisRatio %s", err)
			return false
		} else {
			targetBasisRatio = r
			this.arbitrager.Infof("parsed target basis ratio %.4f", r)
		}
	}

	if num, percent, err := ParseNumOrPercentage(this.Args[1], false, false); err != nil {
		this.ErrorMsgf("参数错误：%s", err.Error())
		return false
	} else {
		codePairs := strings.Split(this.Args[0], ",")
		for _, codePair := range codePairs {
			pair := strings.Split(codePair, "&gt;")
			if len(pair) != 2 {
				this.ErrorMsgf("参数错误：%s 格式错误。", codePair)
				return false
			}
			fromCodeStr := pair[0]
			toCodeStr := pair[1]
			if fromFutureCode, err := NewSymbolCode(this.arbitrager, fromCodeStr); err != nil {
				this.WarnMsgf("找不到品种代码 %s：%s", fromCodeStr, err.Error())
				continue
			} else {
				if toFutureCode, err := NewSymbolCode(this.arbitrager, toCodeStr); err != nil {
					this.WarnMsgf("找不到品种代码 %s：%s", toCodeStr, err.Error())
					continue
				} else {
					if strings.EqualFold(toFutureCode.Code, fromFutureCode.Code) {
						this.ErrorMsgf("参数错误：源品种和目标品种不能一样。")
						return false
					}
					if !strings.EqualFold(toFutureCode.Coin(), fromFutureCode.Coin()) {
						this.ErrorMsgf("参数错误：源品种和目标品种基础币种需相同。")
						return false
					}
					if instruction, err := NewRotateInstruction(this.arbitrager, this.isForce, fromFutureCode, toFutureCode, num, percent, targetBasisRatio, configStr); err != nil {
						if strings.Contains(err.Error(), "qty can not be 0") {
							continue
						}
						if strings.Contains(err.Error(), "qty not enough") {
							this.ErrorMsgf("品种 %s 下没有足够的可平仓位。%s", fromFutureCode, err)
							return false
						}
						this.ErrorMsgf("创建指令出现错误：%s", err.Error())
						return false
					} else {
						this.instructions = append(this.instructions, instruction)
					}
				}
			}
		}
	}
	return true
}

type RotateForceArbitragerCommand struct {
	RotateArbitragerCommand
}

func NewRotateForceArbitragerCommand(arbitrager *ArbitragerController) *RotateForceArbitragerCommand {
	cmd := &RotateForceArbitragerCommand{
		RotateArbitragerCommand: RotateArbitragerCommand{
			InstructionCommand: InstructionCommand{
				Command: command.Command{
					Name:            "rotateForce",
					Alias:           []string{"rf"},
					Instruction:     "`.rotateForce FromFutureCode>ToFutureCode 100%%/qty config1=value1,config2=value2` 展期套利品种",
					RequiresConfirm: true,
					ArgMin:          2,
					ArgMax:          3,
					AuthcodePos:     -1,
				},
				arbitrager: arbitrager,
			},
			isForce: true,
		},
	}
	return cmd
}

type CancelArbitragerCommand struct {
	command.Command
	arbitrager     *ArbitragerController
	instructionIDs []string // 只有 prepare 确认过的 instructionIDs 才可以取消，避免 prepare 和 do 之间的时间差导致意外取消
}

func (this *CancelArbitragerCommand) PreDo() bool {
	return CheckCommandExecuterAndUpdateBalance(this.arbitrager)
}

func NewCancelArbitragerCommand(arbitrager *ArbitragerController) *CancelArbitragerCommand {
	cmd := &CancelArbitragerCommand{
		Command: command.Command{
			Name:            "cancel",
			Instruction:     "`.cancel all/InstructionID` 取消执行指令",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		arbitrager:     arbitrager,
		instructionIDs: []string{},
	}
	return cmd
}

func (this *CancelArbitragerCommand) Prepare() bool {
	this.instructionIDs = []string{}
	instructionID := ""
	if !strings.EqualFold(this.Args[0], "all") {
		instructionID = strings.ToUpper(this.Args[0])
	}
	if instructions := FilterInstructions(this.arbitrager.Instructions, InstructionStatusRunning, nil, &instructionID); len(instructions) == 0 {
		this.ErrorMsgf("没有正在执行的指令： %s。通过 `.status %s` 可以查询命令状态。", instructionID, instructionID)
		return false
	} else {
		if len(instructions) > 0 {
			for _, instruction := range instructions {
				this.instructionIDs = append(this.instructionIDs, instruction.GetID())
			}
			statusStr := RenderStatusForInstructions(instructions, false)
			this.SendMsgf("```%s```", statusStr)
		} else {
			this.ErrorMsgf("没有查询到任何状态为 %s 的指令。", InstructionStatusRunning)
			return false
		}
	}
	return true
}

func (this *CancelArbitragerCommand) Do() bool {
	instructionIDs := strings.Join(this.instructionIDs, ",")
	if instructions := FilterInstructions(this.arbitrager.Instructions, InstructionStatusRunning, nil, &instructionIDs); len(instructions) == 0 {
		this.SendMsgf("没有正在执行的指令： %s。通过 `.status %s` 可以查询命令状态。", instructionIDs, instructionIDs)
	} else {
		for _, instruction := range instructions {
			instruction.Cancel(CancelReasonManually)
			this.SendMsgf("指令 `%s` 已取消.", instruction.GetID())
		}
	}
	return true
}

type BasisRankArbitragerCommand struct {
	ArbitragerExecuterCommand
}

func NewBasisRankArbitragerCommand(arbitrager *ArbitragerController) *BasisRankArbitragerCommand {
	cmd := &BasisRankArbitragerCommand{
		ArbitragerExecuterCommand: ArbitragerExecuterCommand{
			Command: command.Command{
				Name:            "basis",
				Alias:           []string{"b"},
				Instruction:     "`.basis` 溢价排行榜",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *BasisRankArbitragerCommand) Do() bool {
	type SymbolBasis struct {
		FutureSymbol string
		FutureCode   *exchange.SymbolCode
		BasisRatio   float64
	}
	basisList := []*SymbolBasis{}
	uSymbol := this.arbitrager.Config.USDXSymbol
	if futures, err := this.arbitrager.Exchange.GetTradablePairs(this.arbitrager.FutureInstrumentType(), uSymbol); err != nil {
		this.ErrorMsgf("获取合约列表出错：%s", err)
		return false
	} else {
		for _, futureSymbol := range futures {
			if spotSymbol, futureCode, err := this.arbitrager.Exchange.TranslateFutureSymbol(this.arbitrager.FutureInstrumentType(),
				futureSymbol, uSymbol); err == nil {
				for _, coin := range this.arbitrager.Config.AllowedSymbolPrefixs {
					if strings.EqualFold(futureCode.Coin(), coin) && spotSymbol != "" {
						tempPair := NewArbiPair(exchange.NewSymbolPair(futureCode, spotSymbol, futureSymbol), false, 0)
						if basisRatio, err := this.arbitrager.Executer.GetBasisRatio(tempPair); err == nil {
							basisList = append(basisList, &SymbolBasis{FutureSymbol: futureSymbol, FutureCode: futureCode, BasisRatio: basisRatio})
						} else {
							this.WarnMsgf("获取溢价率排行榜，内部错误：%s - %s, %s, ", spotSymbol, futureSymbol, err)
						}
					}
				}
			} else {
				if !strings.Contains(err.Error(), "invalid future code") {
					this.WarnMsgf("获取 %s 溢价率排行榜，内部错误 %s", futureSymbol, err)
				}
			}
		}
		sort.Slice(basisList, func(i, j int) bool {
			return basisList[i].BasisRatio > basisList[j].BasisRatio
		})
		t := NewTable()
		t.SetHeader([]string{"FutureCode", "FutureSymbol", "Basis Ratio"})
		for _, b := range basisList {
			t.AddRow([]string{
				b.FutureCode.String(),
				b.FutureSymbol,
				fmt.Sprintf("%+.2f%%", b.BasisRatio*100),
			})
		}
		this.SendMsgf("溢价率排行：\n```%s```", t.Render())
		return true
	}
}

type FundingHistoryArbitragerCommand struct {
	ArbitragerExecuterCommand
}

func NewFundingHistoryArbitragerCommand(arbitrager *ArbitragerController) *FundingHistoryArbitragerCommand {
	cmd := &FundingHistoryArbitragerCommand{
		ArbitragerExecuterCommand: ArbitragerExecuterCommand{
			Command: command.Command{
				Name:            "funding",
				Instruction:     "`.funding all/FutureCode latest/history[可选] daily[可选]` 资金费率历史",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          3,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *FundingHistoryArbitragerCommand) Do() bool {
	return cmds.DoFundingHistoryCommand(this.Command, this.arbitrager, this.arbitrager.Exchange)
}

type PositionArbitragerCommand struct {
	ArbitragerExecuterCommand
}

func NewPositionArbitragerCommand(arbitrager *ArbitragerController) *PositionArbitragerCommand {
	cmd := &PositionArbitragerCommand{
		ArbitragerExecuterCommand: ArbitragerExecuterCommand{
			Command: command.Command{
				Name:            "position",
				Alias:           []string{"p", "pos"},
				Instruction:     "`.position Asset1,Asset2[可选]` 查看现货和期货持仓",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *PositionArbitragerCommand) Do() bool {
	t := NewTable()
	t.SetHeader([]string{"Symbol", "Type", "Qty", "Available", "Entry Price", "PNL"})

	assets := ""
	if len(this.Args) == 1 {
		assets = this.Args[0]
	}

	if rows, err := this.arbitrager.GetPositionRows("spot/future", assets); err != nil {
		this.ErrorMsgf("获取仓位出错：%s", err)
		return false
	} else {
		for _, row := range rows {
			t.AddRow(row)
		}
		this.SendMsgf("当前持仓：\n```%s```", t.Render())
		return true
	}
}

type ReportArbitragerCommand struct {
	ArbitragerExecuterCommand
}

func NewReportArbitragerCommand(arbitrager *ArbitragerController) *ReportArbitragerCommand {
	cmd := &ReportArbitragerCommand{
		ArbitragerExecuterCommand: ArbitragerExecuterCommand{
			Command: command.Command{
				Name:            "report",
				Instruction:     "`.report InstructionID` 查看某个指令的详细报告",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *ReportArbitragerCommand) Do() bool {
	option := "summary/split/order/duration/config"
	if len(this.Args) == 2 {
		option = this.Args[1]
		option = strings.ToLower(option)
	}
	instructionID := strings.ToUpper(this.Args[0])

	if instructions := FilterInstructions(this.arbitrager.Instructions, InstructionStatusAll, nil, &instructionID); len(instructions) != 1 {
		this.ErrorMsgf("没有找到指令 %s", instructionID)
		return false
	} else {
		instruction := instructions[0]

		instruction.Lock()
		if strings.EqualFold(option, "") {
			option = "summary/split/order/duration/config"
		}
		reports := ""
		if strings.Contains(option, "summary") {
			summaryReport := instruction.RenderSummaryReport()
			reports += summaryReport
		}
		reports += "\n\n"
		if strings.Contains(option, "split") {
			splitReport := instruction.RenderSplitReport()
			reports += splitReport
		}
		reports += "\n\n"
		if strings.Contains(option, "order") {
			orderReport := instruction.RenderOrderReport()
			reports += orderReport
		}
		reports += "\n\n"
		if strings.Contains(option, "duration") {
			durationReport := instruction.RenderDurationReport()
			reports += durationReport
		}
		reports += "\n\n"
		if strings.Contains(option, "config") {
			if configReport, err := baseconfig.RenderTable(instruction.GetConfig()); err == nil {
				reports += configReport
			} else {
				reports += fmt.Sprintf("\n[ERROR:get config diff error: %s]", err)
			}
		}
		instruction.Unlock()
		this.SendFileMessage(fmt.Sprintf("指令 %s 的报告", instruction.GetID()), reports, "")
		return true
	}
}

type SetCoinsArbitragerCommand struct {
	ArbitragerExecuterCommand
}

func NewSetCoinsArbitragerCommand(arbitrager *ArbitragerController) *SetCoinsArbitragerCommand {
	cmd := &SetCoinsArbitragerCommand{
		ArbitragerExecuterCommand: ArbitragerExecuterCommand{
			Command: command.Command{
				Name:            "setCoins",
				Instruction:     "`.setCoins coin1,coin2` 设置允许套利的币种",
				RequiresConfirm: true,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *SetCoinsArbitragerCommand) Prepare() bool {
	coins := strings.Split(this.Args[0], ",")
	if _, invalidCoins, err := this.arbitrager.Exchange.CheckValidCoins(coins); err != nil {
		this.ErrorMsgf("检查币种请求错误：%s", err)
		return false
	} else {
		if len(invalidCoins) != 0 {
			this.ErrorMsgf("交易所 %s 不支持币种 %s ", this.arbitrager.Exchange.GetName(), strings.Join(invalidCoins, ","))
			return false
		}
		return true
	}
}

func (this *SetCoinsArbitragerCommand) Do() bool {
	coins := strings.Split(this.Args[0], ",")
	if validCoins, invalidCoins, err := this.arbitrager.Exchange.CheckValidCoins(coins); err != nil {
		this.ErrorMsgf("检查币种请求错误：%s", err)
		return false
	} else {
		if len(invalidCoins) != 0 {
			this.ErrorMsgf("交易所 %s 不支持币种 %s ", this.arbitrager.Exchange.GetName(), strings.Join(invalidCoins, ","))
			return false
		}
		this.arbitrager.Config.AllowedSymbolPrefixs = validCoins
		this.arbitrager.Config.Save()
		this.SendMsgf("允许的币种已设为：%s，可以通过 `.config` 查询。", strings.Join(validCoins, ","))
		return true
	}
}

type OrdersArbitragerCommand struct {
	ArbitragerExecuterCommand
}

func NewOrdersArbitragerCommand(arbitrager *ArbitragerController) *OrdersArbitragerCommand {
	cmd := &OrdersArbitragerCommand{
		ArbitragerExecuterCommand: ArbitragerExecuterCommand{
			Command: command.Command{
				Name:            "orders",
				Alias:           []string{"o"},
				Instruction:     "`.orders FutureCode[可选]` 查看指令挂单情况",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *OrdersArbitragerCommand) Do() bool {
	futureCodeStr := ""
	if len(this.Args) == 1 {
		futureCodeStr = this.Args[0]
	}
	if futureCodeStr == "" {
		book := this.arbitrager.RenderInstructions(futureCodeStr)
		if book != "" {
			this.SendMsgf("所有合约的挂单：\n```%s```", book)
			this.WarnBNB()
			if this.arbitrager.CheckSingleSide() {
				this.WarnMsgf("指令中有单边的情形，以上报告的可用数量可能有误。")
			}
		} else {
			this.SendMsgf("没有任何挂单。")
		}
		return true
	}
	if futureCode, err := NewSymbolCode(this.arbitrager, futureCodeStr); err != nil {
		this.ErrorMsgf("合约代码解析错误。%s", err)
		return false
	} else {
		if spotFuturePairs, err := this.arbitrager.Exchange.TranslateSymbolCode(futureCode); err != nil {
			this.arbitrager.Errorf("translate symbol code %s error: %s", futureCode.Code, err)
			return false
		} else {
			if len(spotFuturePairs) != 1 {
				this.ErrorMsgf("合约代码翻译错误，%s 对应了 %d 个交易对。", futureCode, len(spotFuturePairs))
				return false
			}
			pair := spotFuturePairs[0]
			book := this.arbitrager.RenderInstructions(pair.Right.Symbol)
			if book != "" {
				this.SendMsgf("合约 %s 的挂单：\n```%s```", futureCode, book)
			} else {
				this.SendMsgf("合约 %s 没有挂单。", futureCode)
			}
			return true
		}
	}
}

func (this *OrdersArbitragerCommand) WarnBNB() {
	if this.arbitrager.Config.BNBFee {
		if _, bnbQty, err := this.arbitrager.Exchange.GetHoldingQty(exchange.Spot, "BNB"); err != nil {
			this.arbitrager.Warnf("get bnb avaiable qty error: %s", err)
		} else {
			totalEstimatedBNB := this.arbitrager.GetEstimatedBNB()
			if bnbQty-totalEstimatedBNB < -totalEstimatedBNB*0.001 {
				this.arbitrager.WarnMsgf("没有足够的 BNB 用于支付手续费抵扣。需要 %.4f ，实际有 %.4f 。", totalEstimatedBNB, bnbQty)
			}
		}
	} else {
		this.arbitrager.Debugf("no need to warn BNB, BNBFee=false")
	}
}

type TransferArbitragerCommand struct {
	ArbitragerExecuterCommand
}

func NewTransferArbitragerCommand(arbitrager *ArbitragerController) *TransferArbitragerCommand {
	cmd := &TransferArbitragerCommand{
		ArbitragerExecuterCommand: ArbitragerExecuterCommand{
			Command: command.Command{
				Name:            "transfer",
				Instruction:     "`.transfer fromCode toCode 100%%/qty` 划转余额，如 .transfer ETH-- ETH09 1 表示从现货划转 1 ETH 到期货",
				RequiresConfirm: true,
				ArgMin:          3,
				ArgMax:          3,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *TransferArbitragerCommand) Prepare() bool {
	fromCode := this.Args[0]
	fromSymbolCode, err := NewSymbolCode(this.arbitrager, fromCode)
	if err != nil {
		this.ErrorMsgf("fromCode 参数错误：%s", err)
		return false
	}

	toCode := this.Args[1]
	toSymbolCode, err := NewSymbolCode(this.arbitrager, toCode)
	if err != nil {
		this.ErrorMsgf("toCode 参数错误：%s", err)
		return false
	}

	qty, percent, err := ParseNumOrPercentage(this.Args[2], false, false)
	if err != nil {
		this.ErrorMsgf("qty 参数错误：%s", err)
		return false
	}

	transferCoin := fromSymbolCode.Coin()
	if fromSymbolCode.InstrumentType() == exchange.USDXMarginedFutures {
		transferCoin = fromSymbolCode.USDXSymbol
	}

	var availableQty float64
	if fromSymbolCode.IsSpot() {
		_, availableQty, err = this.arbitrager.Exchange.GetHoldingQty(exchange.Spot, transferCoin)
		if err != nil {
			this.ErrorMsgf("获取可划账余额失败：%s", err)
			return false
		}
	} else {
		_, availableQty, err = this.arbitrager.Exchange.GetBalance(this.arbitrager.FutureInstrumentType(), transferCoin)
		if err != nil {
			this.ErrorMsgf("获取可划账余额失败：%s", err)
			return false
		}
	}

	if availableQty == 0 {
		this.ErrorMsgf("当前余额为 0，无法划转")
		return false
	}

	if qty == 0 && percent > 0 {
		qty = math.Abs(availableQty) * percent
	}

	if availableQty < qty {
		this.ErrorMsgf("可划账余额不足，当前余额：%v", availableQty)
		return false
	}

	this.Args[2] = fmt.Sprintf("%v", qty)

	if fromSymbolCode.IsSpot() && toSymbolCode.IsFuture() {
		this.SendMsgf("\n请确认划转指令：从 `现货` 划转 `%v %s` 到 `期货` ", qty, transferCoin)
	} else if fromSymbolCode.IsFuture() && toSymbolCode.IsSpot() {
		this.SendMsgf("\n请确认划转指令：从 `期货` 划转 `%v %s` 到 `现货` ", qty, transferCoin)
	} else {
		this.ErrorMsgf("参数错误：必须从现货划转到期货 或 从期货划转到现货")
		return false
	}

	return true
}

func (this *TransferArbitragerCommand) Do() bool {
	fromCode := this.Args[0]
	fromSymbolCode, err := NewSymbolCode(this.arbitrager, fromCode)
	if err != nil {
		this.ErrorMsgf("fromCode 参数错误：%s", err.Error())
		return false
	}

	toCode := this.Args[1]
	toSymbolCode, err := NewSymbolCode(this.arbitrager, toCode)
	if err != nil {
		this.ErrorMsgf("toCode 参数错误：%s", err.Error())
		return false
	}

	qty, _, err := ParseNumOrPercentage(this.Args[2], false, false)
	if err != nil {
		this.ErrorMsgf("qty 参数错误：%s", err.Error())
		return false
	}

	transferCoin := fromSymbolCode.Coin()
	if fromSymbolCode.InstrumentType() == exchange.USDXMarginedFutures {
		transferCoin = fromSymbolCode.USDXSymbol
	}

	// 如果有持仓，期货可用数量是变动的，需要再获取一次
	if fromSymbolCode.IsFuture() {
		total, available, err := this.arbitrager.Exchange.GetBalance(this.arbitrager.FutureInstrumentType(), transferCoin)
		if err != nil {
			this.ErrorMsgf("获取可划账余额失败：%s", err)
			return false
		}
		if qty >= available {
			if total > available {
				available *= 0.9999
			}
			qty = available
		}
	}

	if err := this.arbitrager.Executer.Transfer(*fromSymbolCode, *toSymbolCode, qty); err != nil {
		this.ErrorMsgf("划转失败：%s", err)
		return false
	}
	this.SendMsgf("划转执行成功")
	return true
}

type UpdateBalanceArbitragerCommand struct {
	ArbitragerExecuterCommand
}

func NewUpdateBalanceArbitragerCommand(arbitrager *ArbitragerController) *UpdateBalanceArbitragerCommand {
	cmd := &UpdateBalanceArbitragerCommand{
		ArbitragerExecuterCommand: ArbitragerExecuterCommand{
			Command: command.Command{
				Name:            "updateBalance",
				Alias:           []string{"ub"},
				Instruction:     "`.updateBalance force[可选]` 更新内部持仓数据",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *UpdateBalanceArbitragerCommand) Do() bool {
	force := false
	if len(this.Args) == 1 && strings.EqualFold(strings.ToLower(this.Args[0]), "force") {
		force = true
	}
	if err := this.arbitrager.UpdateBalance(force); err != nil {
		if err.Error() == "couldn't update balance where there are instruction running" {
			this.ErrorMsgf("当前有在运行的指令，不能更新内部持仓数据。")
		} else {
			this.ErrorMsgf("更新内部持仓数据出错：%s", err)
		}
		return false
	} else {
		this.SendMsgf("更新内部持仓数据成功。")
		return true
	}
}

type CleanupInstructionsArbitragerCommand struct {
	ArbitragerExecuterCommand
}

func NewCleanupInstructionsArbitragerCommand(arbitrager *ArbitragerController) *CleanupInstructionsArbitragerCommand {
	cmd := &CleanupInstructionsArbitragerCommand{
		ArbitragerExecuterCommand: ArbitragerExecuterCommand{
			Command: command.Command{
				Name:            "cleanInstructions",
				Alias:           []string{"clean"},
				Instruction:     "`.cleanInstructions hours(默认48)` 清除48小时前结束并且无完成进度的指令",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *CleanupInstructionsArbitragerCommand) Do() bool {
	hours := 48
	if len(this.Args) == 1 {
		if h, err := strconv.ParseInt(this.Args[0], 10, 64); err != nil {
			hours = int(h)
		}
	}

	cleanInstructionCount, cleanCanceledOrderCount := this.arbitrager.CleanupInstructions(hours)
	this.arbitrager.storage.Save()
	this.SendMsgf("清理了 %d 个指令和 %d 个未成交订单。", cleanInstructionCount, cleanCanceledOrderCount)
	return true
}

type PauseArbitragerCommand struct {
	ArbitragerExecuterCommand
}

func NewPauseArbitragerCommand(arbitrager *ArbitragerController) *PauseArbitragerCommand {
	cmd := &PauseArbitragerCommand{
		ArbitragerExecuterCommand: ArbitragerExecuterCommand{
			Command: command.Command{
				Name:            "pause",
				Instruction:     "`.pause DateTime minutes` 暂停运行指令，指定开始时间和暂停时长 e.g. .pause 10-01T12:00 60",
				RequiresConfirm: true,
				ArgMin:          2,
				ArgMax:          2,
				AuthcodePos:     -1,
			},
			arbitrager: arbitrager,
		},
	}
	return cmd
}

func (this *PauseArbitragerCommand) Prepare() bool {
	timeStr := this.Args[0]
	t := utils.ParseTimeBeijing(timeStr)
	if t == nil {
		this.ErrorMsgf("请输入正确的时间格式如 10-01T12:00")
		return false
	}

	_, err := strconv.ParseInt(this.Args[1], 10, 32)
	if err != nil {
		this.ErrorMsgf("请输入正确的时长")
		return false
	}

	return true
}

func (this *PauseArbitragerCommand) Do() bool {
	timeStr := this.Args[0]
	startTime := utils.ParseTimeBeijing(timeStr)
	minutes, _ := strconv.ParseInt(this.Args[1], 10, 32)
	this.arbitrager.pauseStartAt = startTime
	this.arbitrager.pauseMinutes = int(minutes)
	this.SendMsgf("设置暂停时间成功")
	return true
}

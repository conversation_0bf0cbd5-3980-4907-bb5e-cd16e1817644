package arbitrage

import (
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"github.com/wizhodl/quanter/exchange"
)

const LockKeyAccountWorth = "AccountWorthLock"

const ExtKeyEndTime = "EndTime"
const ExtKeyStartTime = "StartTime"

// const ExtKeyBasisSnapshot = "BasisSnapshot"

const ISOTimeFormat = "2006-01-02 15:04:05"

const BNBFeeSpotRatioVIP0 = 0.00075 // VIP0 的费率为 0.001，BNB 折扣 25%
const BNBFeeFutureRatioVIP0 = 0.005 // 合约费率
const FeeSpotRatioVIP0 = 0.001

const TargetBasisRatioTolerance = 0.10
const BasisDoubleCheckBasisWaitThreshold = 3 // 当 BasisWait 小于该值时，不允许设置 BasisDoubleCheck

type AccountWorthType string

const AccountWorthTypeBeforeInstruction = "WorthBefore"
const AccountWorthTypeAfterInstruction = "WorthAfter"

type OrderType string

const OrderTypeSpot OrderType = "Spot"
const OrderTypeFuture OrderType = "Future"

type ArbiDirection string

const ArbiDirectionNone ArbiDirection = ""
const ArbiDirectionOpen ArbiDirection = "Open"
const ArbiDirectionClose ArbiDirection = "Close"
const ArbiDirectionMove ArbiDirection = "Move"
const ArbiDirectionRotate ArbiDirection = "Rotate"

type CancelReason string

const CancelReasonNone CancelReason = "No"                           // 没有取消
const CancelReasonManually CancelReason = "Manually"                 // 人工取消
const CancelReasonDeadline CancelReason = "Deadline"                 // 执行总时间达到最长期限，15分钟
const CancelReasonSplitBasisError CancelReason = "SplitBasisError"   // Split 溢价超过警戒值
const CancelReasonQtyInsufficient CancelReason = "Qty Insuf."        // 可用数量不足，比如套利时 USDT 不足，或者平仓时可平仓位不足
const CancelReasonSystemExit CancelReason = "SystemExit"             // 系统退出
const CancelReasonFutureOrderError CancelReason = "FutureOrderError" // 创建合约订单错误
const CancelReasonFutureQtyError CancelReason = "FutureQtyError"     // 合约数量计算错误
const CancelReasonMaxExecuteTimes CancelReason = "MaxExecuteTimes"   // 超过最大重试次数
const CancelReasonBatchClose CancelReason = "BatchClose"             // 因批量平仓自动取消

type SingleSideReason string

const SingleSideReasonNone SingleSideReason = "--"
const SingleSideReasonArbiSpot SingleSideReason = "ArbiSpot"
const SingleSideReasonArbiTransfer SingleSideReason = "ArbiTransfer"
const SingleSideReasonArbiFuture SingleSideReason = "ArbiFuture"
const SingleSideReasonCloseFuture SingleSideReason = "ArbiFuture"
const SingleSideReasonCloseTransfer SingleSideReason = "CloseTransfer"
const SingleSideReasonCloseSpot SingleSideReason = "CloseSpot"
const SingleSideReasonMoveCloseFuture SingleSideReason = "MoveCloseFuture"
const SingleSideReasonMoveCloseTransfer SingleSideReason = "MoveCloseTransfer"
const SingleSideReasonMoveCloseSpot SingleSideReason = "MoveCloseSpot"
const SingleSideReasonMoveArbiSpot SingleSideReason = "MoveArbiSpot"
const SingleSideReasonMoveArbiTransfer SingleSideReason = "MoveArbiTransfer"
const SingleSideReasonMoveArbiFuture SingleSideReason = "MoveArbiFuture"

type InstructionStatus string

const InstructionStatusAll InstructionStatus = "all"
const InstructionStatusRunning InstructionStatus = "running"
const InstructionStatusEnded InstructionStatus = "ended"
const InstructionStatusNone InstructionStatus = "-"

var SimpleInstructionStatusHeader = []string{"ID", "Buy", "Sell", "Qty", "Finish %", "Trade", "Basis %", "Δ Basis %", "Duration", "Worth After"}
var DetailInstructionStatusHeader = []string{"ID", "Name", "Exchange", "Qty", "Finish %", "Buy Trade", "Sell Trade", "Target Basis %", "Basis %", "Δ Basis %", "Canceled", "Start Time", "End Time", "Duration", "Worth Before", "Worth After", "Comment"}

var SimpleMoveInstructionStatusHeader = []string{"ID", "FutureCode", "Qty", "Finish %", "Basis %", "Δ Basis %", "Duration", "Worth After"}
var DetailMoveInstructionStatusHeader = []string{"ID", "Name", "Exchange", "Qty", "Finish %", "Close Trade", "Open Trade", "Target Basis %", "Basis %", "Δ Basis %", "Canceled", "Start Time", "End Time", "Duration", "Worth Before", "Worth After", "Comment"}

var SimpleInstructionRequestHeader = []string{"ID", "Exchange", "Buy", "Sell", "Percentage", "Qty", "Basis %", "Ref Basis %"}
var DetailInstructionRequestHeader = []string{"ID", "Arbitrager", "Exchange", "Buy", "Sell", "Percentage", "Qty", "Basis %", "Ref Basis %"}

var SimpleTradeInstructionRequestHeader = []string{"ID", "Exchange", "Symbol", "Direction", "Qty", "Price"}

var ConfigWithDiffHeader = []string{"Config", "Value", "Default"}
var ConfigWithoutDiffHeader = []string{"Config", "Value"}

type SubmitOrderError struct {
	error
	ErrorCode SubmitOrderErrorCode
}

type SubmitOrderErrorCode string

const (
	SubmitOrderErrorCodeInsufficientBalance SubmitOrderErrorCode = "InsufficientBalance"
	SubmitOrderErrorCodeUserSettingsErr     SubmitOrderErrorCode = "UserSettingsErr"
	SubmitOrderErrorCodeOrderRejected       SubmitOrderErrorCode = "OrderRejected"
	SubmitOrderErrorCodeUnknown             SubmitOrderErrorCode = "Unknown"
)

func NewSymbolCode(arbiCtl *ArbitragerController, code string) (*exchange.SymbolCode, error) {
	symbolCode, err := arbiCtl.NewSymbolCode(code)
	if err != nil {
		return nil, err
	}

	if arbiCtl.CheckValidSymbolCode(symbolCode.Code) {
		return symbolCode, err
	} else {
		return nil, fmt.Errorf("invalid future code %s", code)
	}
}

func (this *ArbiDirection) IsOpen() bool {
	return *this == ArbiDirectionOpen
}

func (this *ArbiDirection) IsClose() bool {
	return *this == ArbiDirectionClose
}

func (this *ArbiDirection) IsMove() bool {
	return *this == ArbiDirectionMove
}

func (this *ArbiDirection) IsRotate() bool {
	return *this == ArbiDirectionRotate
}

func ConvertInstructionStatus(statusStr string) (InstructionStatus, error) {
	statusStr = strings.ToLower(statusStr)
	switch statusStr {
	case "all":
		return InstructionStatusAll, nil
	case "running":
		return InstructionStatusRunning, nil
	case "run":
		return InstructionStatusRunning, nil
	case "ended":
		return InstructionStatusEnded, nil
	case "end":
		return InstructionStatusEnded, nil
	default:
		return InstructionStatusNone, fmt.Errorf("invalid status %s", statusStr)
	}
}

func CheckValidExchangeName(exchangeName string) bool {
	validExchanges := []string{"*", exchange.Binance, exchange.OKEx, exchange.Bybit, exchange.Hyperliquid}
	for _, ex := range validExchanges {
		if ex == exchangeName {
			return true
		}
	}
	return false
}

func ParseInstructionID(instructionID string) (num int64, direction ArbiDirection, isForce bool, er error) {
	num = 0
	re := regexp.MustCompile("[0-9]+")
	parts := re.FindAllString(instructionID, -1)
	if len(parts) != 1 {
		er = errors.New("instruction id format error")
	} else {
		if n, err := strconv.ParseInt(parts[0], 10, 64); err != nil {
			er = err
		} else {
			num = n
		}
	}
	if strings.HasPrefix(instructionID, "MF") {
		return num, ArbiDirectionMove, true, er
	}
	if strings.HasPrefix(instructionID, "RF") {
		return num, ArbiDirectionRotate, true, er
	}
	if strings.HasPrefix(instructionID, "AF") {
		return num, ArbiDirectionOpen, true, er
	}
	if strings.HasPrefix(instructionID, "CF") {
		return num, ArbiDirectionClose, true, er
	}
	if strings.HasPrefix(instructionID, "M") {
		return num, ArbiDirectionMove, false, er
	}
	if strings.HasPrefix(instructionID, "R") {
		return num, ArbiDirectionRotate, false, er
	}
	if strings.HasPrefix(instructionID, "A") {
		return num, ArbiDirectionOpen, false, er
	}
	if strings.HasPrefix(instructionID, "C") {
		return num, ArbiDirectionClose, false, er
	}
	return 0, ArbiDirectionNone, false, fmt.Errorf("instruction format error for %s", instructionID)
}

// 嵌套而不是使用 SymbolPair 的别名，是为了增加一些特有的方法
type ArbiPair struct {
	exchange.SymbolPair
	IsOpen        bool
	BasisSnapshot float64
}

// 必须要正确的初始化 IsOpen ，才可能正确的使用 GetBuyItem 和 GetSellItem 函数
func NewArbiPair(pair *exchange.SymbolPair, isOpen bool, basisSnapshot float64) *ArbiPair {
	return &ArbiPair{
		SymbolPair:    *pair,
		IsOpen:        isOpen,
		BasisSnapshot: basisSnapshot,
	}
}

// TradeInstruction 可以调用这个函数，因为 Left == Right，结果不会有错
func (this *ArbiPair) GetBuyItem() *exchange.SymbolItem {
	if this.IsOpen {
		return this.Left
	} else {
		return this.Right
	}
}

// TradeInstruction 可以调用这个函数，因为 Left == Right，结果不会有错
func (this *ArbiPair) GetSellItem() *exchange.SymbolItem {
	if this.IsOpen {
		return this.Right
	} else {
		return this.Left
	}
}

// 这个函数仅当 Left == Right 的时候（即 TradeInstruction）才可以调用，否则返回空值，可能导致崩溃
func (this *ArbiPair) GetTradeItem() *exchange.SymbolItem {
	if this.Left.Symbol == this.Right.Symbol {
		return this.Left
	} else {
		return nil
	}
}

// 返回该交易对是不是期期套利的交易对
func (this *ArbiPair) IsF2F() bool {
	return (this.Left.Code.Code != this.Right.Code.Code) && (this.Left.Code.IsFuture() && this.Right.Code.IsFuture())
}

package arbitrage

import (
	"errors"
	"fmt"
	"os"
	"path"
	"strings"

	"github.com/wizhodl/quanter/base/baseconfig"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/utils"

	"github.com/spf13/viper"
)

type ArbitragerConfig struct {
	Splits                       int     // 多少个分片
	SplitWait                    int     // 每个分片中间间隔多久，in milliseconds
	BasisTolerance               float64 // 溢价率容差，ratio print in percent, 2% default
	MoveBasisTolerance           float64 // 移仓溢价率容差，因为移仓溢价率较低，通常设置的比 BasisTolerance 更高
	RotateBasisTolerance         float64 // 展期溢价率容差
	BasisWait                    int     // 不满足溢价率容差时等待时间，in seconds
	SpotSlippage                 float64 // 现货订单滑点率，print in percent, 0.001 deault
	FutureSlippage               float64 // 期货订单滑点率，print in percent, 0.0015 default
	SplitSpotQtyTolerance        float64 // 分片内现货数量容差，ratio print in percent, spot qty tolerance in every split
	SplitFutureQtyTolerance      float64 // 分片内期货数量容差，ratio print in percent, future qty tolerance in every split
	SplitBasisWarningTolerance   float64 // 分片内溢价率警告容差，ratio print in percent, 15% default
	SplitBasisErrorTolerance     float64 // 分片内溢价率错误容差，达到会取消指令，ratio print in percent, 30% default
	SpotFutureQtyDeltaTolerance  float64 // 结束时现货期货数量警告容差，ratio print in percent, 2% default
	SpotRetryInterval            int     // 现货网络请求重试等待时间，in milliseconds
	TransferRetryInterval        int     // 转账网络请求重试等待时间，in milliseconds
	FutureRetryInterval          int     // 期货网络请求重试等待时间，in milliseconds
	GetBasisRetryInterval        int     // 获取溢价率网络请求重试等待时间，in milliseconds
	Timeout                      int     // 指令期限，in minutes
	BNBFee                       bool    // 是否BNB抵扣
	BNBAutoBuy                   bool    // 是否自动购买BNB
	BNBAutoBuyLastSplitThreshold float64 // 自动购买BNB时，最后一个分片的买入阈值，0-10 之间的浮点数；在最后一个分片时，如果剩余数量超过这个阈值，那么就购买最小单位的 BNB （10刀）
	ErrorBasis                   float64 // 最小报错溢价率偏差绝对值，不为 0 时有效
	BasisDoubleCheck             bool    // 二次检查 Basis，暂时仅在第一片上做
	BasisDoubleCheckWait         int     // 二次检查 Basis 等待时间，in milliseconds
}

type ArbitragerControllerConfig struct {
	baseconfig.BaseConfig
	ArbitragerConfig
	arbitrager *ArbitragerController

	ConfigSaves []string
}

var ArbitragerConfigOptions = &baseconfig.ConfigOptions{
	"SplitWait":                    {IsPercent: false, Unit: "ms"},
	"BasisTolerance":               {IsPercent: true},
	"MoveBasisTolerance":           {IsPercent: true},
	"RotateBasisTolerance":         {IsPercent: true},
	"BasisWait":                    {IsPercent: false, Unit: "s"},
	"SpotSlippage":                 {IsPercent: true},
	"FutureSlippage":               {IsPercent: true},
	"SplitSpotQtyTolerance":        {IsPercent: true},
	"SplitFutureQtyTolerance":      {IsPercent: true},
	"SplitBasisWarningTolerance":   {IsPercent: true},
	"SplitBasisErrorTolerance":     {IsPercent: true},
	"SpotFutureQtyDeltaTolerance":  {IsPercent: true},
	"SpotRetryInterval":            {IsPercent: false, Unit: "ms"},
	"TransferRetryInterval":        {IsPercent: false, Unit: "ms"},
	"FutureRetryInterval":          {IsPercent: false, Unit: "ms"},
	"GetBasisRetryInterval":        {IsPercent: false, Unit: "ms"},
	"Timeout":                      {IsPercent: false, Unit: "m"},
	"BNBAutoBuyLastSplitThreshold": {IsPercent: false, Unit: "USD"},
	"BasisDoubleCheckWait":         {IsPercent: false, Unit: "ms"},
	"ErrorBasis":                   {IsPercent: false},
}

func NewArbitragerControllerConfig(arbitrager *ArbitragerController) (*ArbitragerControllerConfig, error) {
	var config ArbitragerControllerConfig

	configFilePath := path.Join(arbitrager.ConfigPath, arbitrager.ID+".arbi.toml")
	if _, err := os.Stat(configFilePath); !os.IsNotExist(err) {
		viper.SetConfigName(arbitrager.ID + ".arbi")
		viper.AddConfigPath(arbitrager.ConfigPath)
		err := viper.ReadInConfig()
		if err == nil {
			err := viper.Unmarshal(&config)
			if err != nil {
				arbitrager.Errorf("unable to decode arbitrager config into struct, %v", err)
				return nil, err
			}
			if err := config.Validate(); err != nil {
				arbitrager.Errorf("arbitrager config validate error: %s, config: %#v", err, config)
				return nil, err
			}
			arbitrager.Infof("load config from local file")
			config.arbitrager = arbitrager
			if config.MoveBasisTolerance == 0 {
				config.MoveBasisTolerance = config.BasisTolerance
			}
			return &config, nil
		} else { // 如果配置文件存在，但是解析出现错误，报错退出
			zlog.Panicf("[%s] read config file error：%s", arbitrager.ID, err)
			return nil, fmt.Errorf("read config error: %v", err)
		}
	} else {
		// 本地无配置文件
		arbitrager.Errorf("arbitrager config not found.")
		return nil, errors.New("arbitrager config not found")
	}
}

func (this *ArbitragerControllerConfig) snapshotArbitragerConfig() *ArbitragerConfig {
	c := this
	s := &ArbitragerConfig{
		Splits:                       c.Splits,
		SplitWait:                    c.SplitWait,
		BasisTolerance:               c.BasisTolerance,
		MoveBasisTolerance:           c.MoveBasisTolerance,
		RotateBasisTolerance:         c.RotateBasisTolerance,
		BasisWait:                    c.BasisWait,
		SpotSlippage:                 c.SpotSlippage,
		FutureSlippage:               c.FutureSlippage,
		SplitSpotQtyTolerance:        c.SplitSpotQtyTolerance,
		SplitFutureQtyTolerance:      c.SplitFutureQtyTolerance,
		SplitBasisWarningTolerance:   c.SplitBasisWarningTolerance,
		SplitBasisErrorTolerance:     c.SplitBasisErrorTolerance,
		SpotFutureQtyDeltaTolerance:  c.SpotFutureQtyDeltaTolerance,
		SpotRetryInterval:            c.SpotRetryInterval,
		TransferRetryInterval:        c.TransferRetryInterval,
		FutureRetryInterval:          c.FutureRetryInterval,
		GetBasisRetryInterval:        c.GetBasisRetryInterval,
		Timeout:                      c.Timeout,
		BNBFee:                       c.BNBFee,
		BNBAutoBuy:                   c.BNBAutoBuy,
		BNBAutoBuyLastSplitThreshold: c.BNBAutoBuyLastSplitThreshold,
		ErrorBasis:                   c.ErrorBasis,
		BasisDoubleCheck:             c.BasisDoubleCheck,
		BasisDoubleCheckWait:         c.BasisDoubleCheckWait,
	}
	return s
}

func (this *ArbitragerControllerConfig) Validate() error {
	if err := this.BaseConfig.Validate(); err != nil {
		return err
	}

	return this.ArbitragerConfig.Validate()
}

func (this *ArbitragerControllerConfig) GetConfigOptions() *baseconfig.ConfigOptions {
	return ArbitragerConfigOptions
}

func (this *ArbitragerControllerConfig) GetChildValidatableConfig() baseconfig.ValidatableConfig {
	return &this.ArbitragerConfig
}

func (this *ArbitragerControllerConfig) GetBaseConfig() *baseconfig.BaseConfig {
	return &this.BaseConfig
}

func CheckConfigBasisDoubleCheck(basisWait int, basisDoubleCheck bool) error {
	if basisWait < BasisDoubleCheckBasisWaitThreshold && basisDoubleCheck {
		return errors.New("BasisDoubleCheck = true is not allowed when BasisWait <= 3")
	}
	return nil
}

func (this *ArbitragerControllerConfig) ToTomlContent(hideSecret bool) string {
	result := fmt.Sprintf(`ConfigSaves = [%v]`, utils.SliceStringJoin(this.ConfigSaves, ", ", true)) + "\n" +
		"\n[BaseConfig]\n" +
		this.BaseConfig.ToTomlContent(hideSecret) +
		"\n[ArbitragerConfig]\n" +
		fmt.Sprintf(`Splits = %v`, this.Splits) + "\n" +
		fmt.Sprintf(`SplitWait = %v`, this.SplitWait) + "\n" +
		fmt.Sprintf(`BasisTolerance = %v`, this.BasisTolerance) + "\n" +
		fmt.Sprintf(`MoveBasisTolerance = %v`, this.MoveBasisTolerance) + "\n" +
		fmt.Sprintf(`RotateBasisTolerance = %v`, this.RotateBasisTolerance) + "\n" +
		fmt.Sprintf(`BasisWait = %v`, this.BasisWait) + "\n" +
		fmt.Sprintf(`SpotSlippage = %v`, this.SpotSlippage) + "\n" +
		fmt.Sprintf(`FutureSlippage = %v`, this.FutureSlippage) + "\n" +
		fmt.Sprintf(`SplitSpotQtyTolerance = %v`, this.SplitSpotQtyTolerance) + "\n" +
		fmt.Sprintf(`SplitFutureQtyTolerance = %v`, this.SplitFutureQtyTolerance) + "\n" +
		fmt.Sprintf(`SplitBasisWarningTolerance = %v`, this.SplitBasisWarningTolerance) + "\n" +
		fmt.Sprintf(`SplitBasisErrorTolerance = %v`, this.SplitBasisErrorTolerance) + "\n" +
		fmt.Sprintf(`SpotFutureQtyDeltaTolerance = %v`, this.SpotFutureQtyDeltaTolerance) + "\n" +
		fmt.Sprintf(`SpotRetryInterval = %v`, this.SpotRetryInterval) + "\n" +
		fmt.Sprintf(`TransferRetryInterval = %v`, this.TransferRetryInterval) + "\n" +
		fmt.Sprintf(`FutureRetryInterval = %v`, this.FutureRetryInterval) + "\n" +
		fmt.Sprintf(`GetBasisRetryInterval = %v`, this.GetBasisRetryInterval) + "\n" +
		fmt.Sprintf(`Timeout = %v`, this.Timeout) + "\n" +
		fmt.Sprintf(`BNBFee = %v`, this.BNBFee) + "\n" +
		fmt.Sprintf(`BNBAutoBuy = %v`, this.BNBAutoBuy) + "\n" +
		fmt.Sprintf(`BNBAutoBuyLastSplitThreshold = %v`, this.BNBAutoBuyLastSplitThreshold) + "\n" +
		fmt.Sprintf(`ErrorBasis = %v`, this.ErrorBasis) + "\n" +
		fmt.Sprintf(`BasisDoubleCheck = %v`, this.BasisDoubleCheck) + "\n" +
		fmt.Sprintf(`BasisDoubleCheckWait = %v`, this.BasisDoubleCheckWait) + "\n"
	return result
}

func (this *ArbitragerControllerConfig) ToTable(includeConfigSaves bool) string {
	apiKey := utils.HideSecret(this.ApiKey)
	apiSecret := utils.HideSecret(this.ApiSecret)

	ts := NewTable()
	ts.SetHeader([]string{"AllowedSymbolPrefixs"})
	ts.AddRow([]string{strings.Join(this.AllowedSymbolPrefixs, ",")})

	t := NewTable()
	t.SetHeader(ConfigWithoutDiffHeader)
	debugStr := "false"
	if this.arbitrager != nil && this.arbitrager.Debug {
		debugStr = "true"
	}
	t.AddRow([]string{"Debug*", debugStr})
	t.AddRow([]string{"", ""})
	t.AddRow([]string{"ExchangeName", fmt.Sprintf(`%v`, this.ExchangeName)})
	t.AddRow([]string{"ApiKey", fmt.Sprintf(`%v`, apiKey)})
	t.AddRow([]string{"ApiSecret", fmt.Sprintf(`%v`, apiSecret)})
	if this.WithdrawApiKey != "" {
		t.AddRow([]string{"WithdrawApiKey", fmt.Sprintf(`%v`, utils.HideSecret(this.WithdrawApiKey))})
		t.AddRow([]string{"WithdrawApiSecret", fmt.Sprintf(`%v`, utils.HideSecret(this.WithdrawApiSecret))})
	}

	if exp, _ := this.arbitrager.GetAPIExpireTime(); exp != nil {
		t.AddRow([]string{"ApiKeyExpire", utils.FormatShortTimeStr(exp, true)})
	}

	t.AddRow([]string{"ReleaseBinaryDirPath", fmt.Sprintf(`%v`, this.ReleaseBinaryDirPath)})
	t.AddRow([]string{"LogDirPath", fmt.Sprintf(`%v`, this.LogDirPath)})
	t.AddRow([]string{"IsTestnet", fmt.Sprintf(`%v`, this.IsTestnet)})
	t.AddRow([]string{"EnableRealtimePrice", fmt.Sprintf(`%v`, this.EnableRealtimePrice)})
	t.AddRow([]string{"ProxyUrl", this.ProxyUrl})
	t.AddRow([]string{"MinMarginRatio", fmt.Sprintf(`%v`, this.MinMarginRatio)})
	t.AddRow([]string{"FutureInstrumentType", fmt.Sprintf(`%v`, this.FutureInstrumentType)})
	t.AddRow([]string{"USDXSymbol", this.USDXSymbol})
	t.AddRow([]string{"MarginMode", fmt.Sprintf(`%v`, this.MarginMode)})
	t.AddRow([]string{"-------------------------", ""})

	for _, row := range this.GetArbitragerConfigRows() {
		t.AddRow(row)
	}

	if includeConfigSaves {
		return fmt.Sprintf("%s\n\n%s\n%s", ts.Render(), t.Render(), this.ConfigSaveToTable())
	} else {
		return fmt.Sprintf("%s\n\n%s", ts.Render(), t.Render())
	}
}

func (this *ArbitragerControllerConfig) GetArbitragerConfigRows() (rows [][]string) {
	if baseConfigRows, err := baseconfig.GetTableRows(this.snapshotArbitragerConfig()); err != nil {
		rows = append(rows, []string{"ERROR", fmt.Sprintf("[ERROR!>base config table error: %s]", err)})
		return
	} else {
		rows = append(rows, baseConfigRows...)
	}
	return
}

func GetFirestoreCollectionName(id string) string {
	return fmt.Sprintf("arbi||%s", id)
}

func (this *ArbitragerControllerConfig) Upload() {
	if err := this.arbitrager.Config.Validate(); err != nil {
		this.arbitrager.Errorf("config validate error: %s", err)
		return
	}
}

func (this *ArbitragerControllerConfig) SaveTo(configPath string, id string, overwrite bool) error {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.Mkdir(configPath, 0755)
	}
	path := path.Join(configPath, id+".arbi.toml")
	if _, err := os.Stat(path); !os.IsNotExist(err) && !overwrite {
		return fmt.Errorf("%s already exist, can not overwrite", path)
	}
	if err := os.WriteFile(path, []byte(this.ToTomlContent(false)), 0755); err != nil {
		this.arbitrager.Errorf("write arbitrager config %s error: %v", id, err)
		return err
	}
	return nil
}

func (this *ArbitragerControllerConfig) Save() {
	this.SaveTo(this.arbitrager.ConfigPath, this.arbitrager.ID, true)
}

func (this *ArbitragerControllerConfig) Delete() {
	// 删除本地配置
	if err := os.Remove(path.Join(this.arbitrager.ConfigPath, this.arbitrager.ID+".arbi.toml")); err != nil {
		this.arbitrager.AlertMsgf("本地配置文件删除失败: %s", err)
		return
	}
}

func (this *ArbitragerConfig) Validate() error {
	if this.Splits == 0 {
		return errors.New("Splits cannot be 0")
	}
	if err := CheckConfigBasisDoubleCheck(this.BasisWait, this.BasisDoubleCheck); err != nil {
		return err
	}
	return nil
}

func (this *ArbitragerConfig) GetConfigOptions() *baseconfig.ConfigOptions {
	return ArbitragerConfigOptions
}

func (this *ArbitragerConfig) GetChildValidatableConfig() baseconfig.ValidatableConfig {
	return nil
}

func (this *ArbitragerConfig) GetBaseConfig() *baseconfig.BaseConfig {
	return nil
}

func (this *ArbitragerControllerConfig) SaveConfig(saveName, configStr string) (correctedConfigStr string, er error) {
	if !strings.HasPrefix(saveName, "_") {
		er = errors.New("config save name must start with _")
		return
	}
	if strings.Contains(saveName, "|") {
		er = errors.New("config name can't have |")
		return
	}
	if _, cfgStr, err := baseconfig.ParseConfigsFromString(this.snapshotArbitragerConfig(), configStr); err != nil {
		er = err
		return
	} else {
		correctedConfigStr = cfgStr
	}
	saveStr := fmt.Sprintf("%s|%s", saveName, correctedConfigStr)
	existingSaveIndex := -1
	for i, save := range this.GetConfigSaves() {
		if strings.EqualFold(save[0], saveName) {
			existingSaveIndex = i
		}
	}
	// 如果 Save 已经存在，替换掉原有的 save
	if existingSaveIndex >= 0 {
		lastParts := append([]string{saveStr}, this.ConfigSaves[existingSaveIndex+1:]...)
		this.ConfigSaves = append(this.ConfigSaves[:existingSaveIndex], lastParts...)
	} else {
		this.ConfigSaves = append(this.ConfigSaves, saveStr)
	}
	return
}

func (this *ArbitragerControllerConfig) DeleteConfigSave(saveName string) (er error) {
	existingSaveIndex := -1
	for i, save := range this.GetConfigSaves() {
		if strings.EqualFold(save[0], saveName) {
			existingSaveIndex = i
		}
	}
	// 如果 Save 已经存在，替换掉原有的 save
	if existingSaveIndex >= 0 {
		this.ConfigSaves = append(this.ConfigSaves[:existingSaveIndex], this.ConfigSaves[existingSaveIndex+1:]...)
	} else {
		er = fmt.Errorf("config save not found %s", saveName)
	}
	return
}

func (this *ArbitragerControllerConfig) GetConfigSaves() (saves [][2]string) {
	saves = [][2]string{}
	for _, save := range this.ConfigSaves {
		parts := strings.Split(save, "|")
		if len(parts) != 2 {
			this.arbitrager.Errorf("invalid config save: %s", save)
			return
		}
		saves = append(saves, [2]string{parts[0], parts[1]})
	}
	return
}

func (this *ArbitragerControllerConfig) GetConfigSave(saveName string) (saveValue string, er error) {
	if !strings.HasPrefix(saveName, "_") {
		saveName = fmt.Sprintf("_%s", saveName)
	}
	saveValue = ""
	for _, save := range this.GetConfigSaves() {
		if strings.EqualFold(saveName, save[0]) {
			saveValue = save[1]
		}
	}
	if saveValue == "" {
		er = fmt.Errorf("config save not exist: %s", saveName)
	}
	return
}

func (this *ArbitragerControllerConfig) ConfigSaveToTable() string {
	t := NewTable()
	t.AddRow([]string{"Config Saves:", ""})
	for _, save := range this.GetConfigSaves() {
		t.AddRow([]string{save[0], save[1]})
	}
	return t.Render()
}


[参考文章](https://weibo.com/ttarticle/p/show?id=2309404410640662921352&mod=z<PERSON><PERSON><PERSON>)

### 约定

1. A, B为期货
2. 价差为 Diff = A - B
3. 价差范围为 [min, max]
4. 标准线为 ref
5. 网格区间为 step


### 操作流程

#### 做多A

当 diff 在 [min, ref] 区间内，如果往min走，买A, 卖B
当 diff 在 [min, ref] 区间内，如果往ref走，卖A, 买B

#### 做空A

当 diff 在 [ref, max] 区间内, 如果往max走，卖A，买B
当 diff 在 [ref, max] 区间走，如果往ref走，买A, 卖B


### 例子

假设都是购买一个BTC

```
0: {QuarterPrice:8486.11 NextWeekPrice:8425.45 Step:60 Diff:60.659999999999854 Time:1572021000}
1: {QuarterPrice:8768.03 NextWeekPrice:8663.57 Step:105 Diff:104.46000000000095 Time:1572032700}
2: {QuarterPrice:9385.74 NextWeekPrice:9324.75 Step:60 Diff:60.98999999999978 Time:1572176700}
3: {QuarterPrice:9788.35 NextWeekPrice:9684.03 Step:105 Diff:104.31999999999971 Time:1572201000}
4: {QuarterPrice:9618.55 NextWeekPrice:9469.1 Step:150 Diff:149.4499999999989 Time:1572905700}
5: {QuarterPrice:8943.57 NextWeekPrice:8838.63 Step:105 Diff:104.94000000000051 Time:1573220700}
6: {QuarterPrice:8843.19 NextWeekPrice:8783.86 Step:60 Diff:59.32999999999993 Time:1573316100}


1： sell quarter, buy next week
2:  buy quarter, sell next week, 8768.03 - 9385.74 + 9324.75 - 8663
3:  sell quarter, buy next week
4:  sell quarter, buy next week
5:  buy quarter, sell next week, 9618.55 - 8943.57 + 8838.63 - 9469.1
6:  buy quarter, sell next week, 9788.35 - 8843.19 + 8783.86 - 9684.03
```

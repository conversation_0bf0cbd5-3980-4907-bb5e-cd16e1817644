{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [

        {
            "name": "Debug Manager",
            "type": "go",
            "request": "launch",
            "mode": "debug",
            "program": "${workspaceRoot}", 
            "args": [
                "-httpPort", "7070", "-debug", "true", "-configPath", "/Users/<USER>/bin/turtles/wintur/", "-logDirPath", "/Users/<USER>/bin/turtles", ">", "/Users/<USER>/bin/turtles/testur.log", "2>&1"
            ],
            "env": {
                "QUANTER_LOG_COLORED_LEVEL": "1",
                "QUANTER_LOG_CALLER": "1",
                "QUANTER_ARBI_FAKE_EXECUTE": "1",
                "QUANTER_DEBUG_SLACK_ROBOT_TOKEN": "",
                "QUANTER_DEBUG_AUTH_SECRET": "OF2WC3TUMVZGIZLCOVTQ====", // base32("quanterdebug")
                "QUANTER_DEBUG_turtleID_API_SECRET": "",
                "QUANTER_DEBUG_arbiID_API_SECRET": "",
            }
        },
        {
            "name": "Debug Turtle Standalone",
            "type": "go",
            "request": "launch",
            "mode": "debug",
            "program": "${workspaceRoot}",
            "args": [
                "-turtleID", "testur", 
                "-httpPort", "7070", "-debug", "true", "-configPath", "/Users/<USER>/bin/turtles/wintur/", "-logDirPath", "/Users/<USER>/bin/turtles", ">", "/Users/<USER>/bin/turtles/testur.log", "2>&1"
            ],
            "env": {
                "QUANTER_LOG_COLORED_LEVEL": "1",
                "QUANTER_LOG_CALLER": "1",
                "QUANTER_DEBUG_SLACK_ROBOT_TOKEN": "",
                "QUANTER_DEBUG_AUTH_SECRET": "OF2WC3TUMVZGIZLCOVTQ====", // base32("quanterdebug")
                "QUANTER_DEBUG_{testur}_API_SECRET": "",
            }
        },
        {
            "name": "Debug Arbitrager Standalone",
            "type": "go",
            "request": "launch",
            "mode": "debug",
            "program": "${workspaceRoot}",
            "args": [
                "-arbiID", "testbiter", 
                "-httpPort", "7070", "-debug", "true", "-configPath", "/Users/<USER>/bin/turtles/wintur/", "-logDirPath", "/Users/<USER>/bin/turtles", ">", "/Users/<USER>/bin/turtles/testur.log", "2>&1"
            ],
            "env": {
                "QUANTER_GCT_LOG_ENABLE": "1",
                "QUANTER_ARBI_FAKE_EXECUTE": "0",
                "QUANTER_LOG_COLORED_LEVEL": "1",
                "QUANTER_LOG_CALLER": "1",
                "QUANTER_DEBUG_SLACK_ROBOT_TOKEN": "",
                "QUANTER_DEBUG_AUTH_SECRET": "OF2WC3TUMVZGIZLCOVTQ====", // base32("quanterdebug")
                "QUANTER_DEBUG_{testbiter}_API_SECRET": "",
            }
        }
    ]
}

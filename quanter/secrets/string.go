package secrets

import (
	"encoding/json"
	"fmt"
	"strings"
)

type SecretString string

// custom stringer to hide the secret
func (this SecretString) String() string {
	length := len(this)
	b := []byte(this)
	if length > 14 {
		return fmt.Sprintf("%s***%s", b[:5], b[len(this)-5:])
	}
	if length > 8 {
		return fmt.Sprintf("%s***%s", b[:3], b[len(this)-3:])
	}
	if length > 4 {
		return fmt.Sprintf("%s***%s", b[:1], b[len(this)-1:])
	}
	return "***"
}

func (this SecretString) Reveal() string {
	return string(this)
}

// custom json marshaler to reveal the secret
func (this SecretString) MarshalJSON() ([]byte, error) {
	return json.Marshal(string(this))
}

func (this SecretString) Bytes() []byte {
	return []byte(string(this))
}

func (this SecretString) Contains(substr string) bool {
	return strings.Contains(string(this), substr)
}

func (this SecretString) Split(sep string) []SecretString {
	parts := strings.Split(string(this), sep)
	result := []SecretString{}
	for _, part := range parts {
		result = append(result, SecretString(part))
	}
	return result
}

func (this SecretString) IsEmpty() bool {
	return this == ""
}

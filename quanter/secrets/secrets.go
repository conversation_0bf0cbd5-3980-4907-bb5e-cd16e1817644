package secrets

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/md5"
	cryptorand "crypto/rand"
	"crypto/sha256"
	_ "embed"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"log"
	"strings"
	"sync/atomic"
	"time"

	_ "github.com/wizhodl/encembed/pkg/encembed" // 防止 go mod tidy 删除依赖
	"github.com/wizhodl/quanter/common/rate"
	"github.com/wizhodl/quanter/common/zlog"
	yaml "gopkg.in/yaml.v2"
)

var BREAK_ATTEMPTS_LIMIT = 600 // 10分钟内最多尝试 600 次
var BREAK_ATTEMPTS_DURATION = 10 * time.Minute

//go:generate go run github.com/wizhodl/encembed -i secrets.yaml -decvarname yamlStr -pkgname secrets

func _getEncryptSalt() string {
	return s.EncryptSalt
}

var s Secrets = Secrets{
	BreakAttemptLimiter: rate.NewDurationLimiter(BREAK_ATTEMPTS_LIMIT, BREAK_ATTEMPTS_DURATION),
}

type Secrets struct {
	EncryptedAuthSecret string       `yaml:"ENCRYPTED_AUTH_SECRET"`
	AuthSecret          string       `yaml:"AUTH_SECRET"`
	MinLeverage         float64      `yaml:"MIN_LEVERAGE"`
	SlackRobotToken     SecretString `yaml:"SLACK_ROBOT_TOKEN"`
	SlackCommander      string       `yaml:"SLACK_COMMANDER"`
	EncryptSalt         string       `yaml:"ENCRYPT_SALT"`
	Password            string
	SkipAuthCode        *atomic.Bool // 用于调试模式下跳过 auth code 验证，仅在 debug 模式下使用，仅可以设置一次，不可更改

	// 以下字段用于防止暴力破解
	BreakAttemptLimiter *rate.DurationLimiter
}

func init() {
	err := yaml.Unmarshal(yamlStr, &s)
	if s.EncryptSalt != "" && s.SlackRobotToken != "" {
		slackToken, err := _decryptSlackToken(s.SlackRobotToken)
		if err != nil {
			zlog.Panicf("decrypt slack token failed: %s", err)
		}
		s.SlackRobotToken = slackToken
	}
	if err != nil {
		log.Printf("unmarsal secrets yaml failed")
	}
}

func GetSlackCommander() string {
	return s.SlackCommander
}

func GetSlackRobotToken() SecretString {
	return s.SlackRobotToken
}

func GetMinLeverage() float64 {
	return s.MinLeverage
}

// 根据配置产生一个稳定的 sign key，但是不暴露原始值
func GetSignKey() []byte {
	hash := md5.Sum([]byte(s.Password + s.SlackCommander + s.EncryptSalt))
	return hash[:]
}

// 是用 CheckPassword 或者 Authenticate 之后才能正确加解密
func _setPassword(password string) (er error) {
	_authSecret, err := _decryptWithPassword(s.EncryptedAuthSecret, password)
	if err != nil {
		er = fmt.Errorf("decrypt AuthSecret with password failed, error: %s", err)
		return
	}

	s.AuthSecret = string(_authSecret)
	s.Password = password
	return
}

func HasPassword() bool {
	return s.Password != ""
}

func DangerouslyCallbackWithPassword(callback func(password SecretString) error) error {
	if s.Password == "" {
		return fmt.Errorf("password is empty")
	}
	return callback(SecretString(s.Password))
}

// 用 CheckPassword 或者 CheckPassword 之后才能正确加解密
func CheckPassword(password, authCode string) (er error) {
	// 防止暴力破解
	// 记录当前尝试
	waitTime, ok := s.BreakAttemptLimiter.Allow()
	if !ok {
		return fmt.Errorf("too many attempts, wait %s", waitTime)
	}

	err := _setPassword(password)
	if err != nil {
		er = fmt.Errorf("set password failed, error: %s", err)
		return
	}

	if !ValidateAuthCode(authCode) {
		return fmt.Errorf("check auth code failed")
	}
	return nil
}

func _decryptSlackToken(token SecretString) (SecretString, error) {
	decrypted, err := _decryptWithPassword(string(token), "")
	if err != nil {
		return "", err
	}
	return SecretString(decrypted), nil
}

// 用于调试模式下跳过 auth code 验证
// 仅在 debug 模式下使用，仅可以设置一次，不可更改
func SkipAuthCode(skip bool) {
	if s.SkipAuthCode == nil {
		s.SkipAuthCode = &atomic.Bool{}
		s.SkipAuthCode.Store(skip)
	}
}

func ValidateAuthCode(authCode string) bool {
	authCode = strings.TrimPrefix(authCode, "g")
	// 如果设置了跳过验证，直接返回 true
	if s.SkipAuthCode != nil && s.SkipAuthCode.Load() {
		return true
	}
	if s.AuthSecret == "" {
		zlog.Errorf("AuthSecret is empty, auth always return false")
		return false
	}
	gAuthCode := GetGoogleAuthCode(s.AuthSecret)
	ok := gAuthCode == authCode
	return ok
}

func _decryptWithPassword(encryptedStr string, password string) ([]byte, error) {
	hash := []byte(password + s.SlackCommander[:8] + _getEncryptSalt()[:8])
	return AesDecrypt(encryptedStr, hash[:])
}

func _encryptWithPassword(plainBytes []byte, password string) (string, error) {
	hash := []byte(password + s.SlackCommander[:8] + _getEncryptSalt()[:8])
	return AesEncrypt(plainBytes, hash[:])
}

func Decrypt(encryptedStr string) (SecretString, error) {
	s, err := _decryptWithPassword(encryptedStr, s.Password)
	return SecretString(s), err
}

// 对称加密数据，无法保证加密后的数据的稳定性
func Encrypt(plainBytes []byte) (string, error) {
	return _encryptWithPassword(plainBytes, s.Password)
}

func CheckEncryptSalt() bool {
	return len(_getEncryptSalt()) >= 8
}

// 为了不暴露 EncryptSalt，使用单独的函数加密数据，并指定 slackCommander
// 因为 genkeys bootstrap 的时候，需要指定 slackCommander 和 password，需要用这个函数
// 加密 robot token 的时候，不要传入 password，因为 robot token 使用时还没有 password
func EncryptForCommander(slackCommander string, plain string, password string) (string, error) {
	hash := []byte(password + slackCommander[:8] + _getEncryptSalt()[:8])
	return AesEncrypt([]byte(plain), hash[:])
}

func SignHMAC(plainBytes []byte) string {
	// calculate HMAC signature with password
	// if password changes the signature will change
	hash := hmac.New(sha256.New, GetSignKey())
	hash.Write(plainBytes)
	return hex.EncodeToString(hash.Sum(nil))
}

func SignMD5(plainStr string) string {
	pMD5 := md5.Sum([]byte(s.Password))
	plainStr += hex.EncodeToString(pMD5[:])
	hash := md5.Sum([]byte(plainStr))
	sign := hex.EncodeToString(hash[:])
	return sign
}

func AesDecrypt(cryptoText string, key []byte) ([]byte, error) {
	ciphertext, _ := base64.URLEncoding.DecodeString(cryptoText)

	block, err := aes.NewCipher(key)
	if err != nil {
		return []byte{}, err
	}

	// The IV needs to be unique, but not secure. Therefore it's common to
	// include it at the beginning of the ciphertext.
	if len(ciphertext) < aes.BlockSize {
		return []byte{}, errors.New("ciphertext too short")
	}
	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]

	stream := cipher.NewCFBDecrypter(block, iv)

	// XORKeyStream can work in-place if the two arguments are the same.
	stream.XORKeyStream(ciphertext, ciphertext)

	return ciphertext, nil
}

func AesEncrypt(plaintext, key []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// The IV needs to be unique, but not secure. Therefore it's common to
	// include it at the beginning of the ciphertext.
	ciphertext := make([]byte, aes.BlockSize+len(plaintext))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(cryptorand.Reader, iv); err != nil {
		return "", err
	}

	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], plaintext)

	// convert to base64
	return base64.URLEncoding.EncodeToString(ciphertext), nil
}

func PKCS5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func PKCS5UnPadding(origData []byte) []byte {
	length := len(origData)
	// 去掉最后一个字节 unpadding 次
	unpadding := int(origData[length-1])
	if length <= unpadding {
		return []byte{}
	}
	return origData[:(length - unpadding)]
}

// 调整 key 的长度为 blockSize 的倍数
// 如果 blockSize 为 0，则根据 key 的长度自动调整
// 如果 blockSize 不为 0，则将 key 的长度调整为 blockSize 的倍数
func AdjustKey(key []byte, blockSize int) []byte {
	if blockSize != 0 && blockSize != 16 && blockSize != 24 && blockSize != 32 {
		panic("blockSize must be 16, 24, or 32")
	}
	if blockSize == 0 {
		switch {
		case len(key) > 32:
			return key[:32]
		case len(key) > 24:
			return key[:24]
		case len(key) > 16:
			return key[:16]
		case len(key) < 16:
			return PKCS5Padding(key, 16)
		default:
			return key
		}
	} else {
		return PKCS5Padding(key, blockSize)
	}
}

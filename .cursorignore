
# 编译的 key
quanter/secrets/prd_keys/
quanter/secrets/secrets.yaml
quanter/secrets/encembedded
quanter/secrets/zencembed.go

quanter/backtest/output/*

*.exe
licenses.json
balancer.yaml

*/dist/*
pgate/dist/*


.idea
*/output/*
conf.go
data.json
main
.vscode
__debug_bin*
main*.exe
main*.exe~
main_linux
main_osx
main_osx_as
main_osx_arm64
main_osx_amd64
genkeys_linux
genkeys_osx_arm64
genkeys_osx_amd64
genkeys_win.exe
conf.toml
apikeys.go
fabfile.py
*.pyc
go.sum
go.work.sum
bin
build
__debug_bin

releases
logs

*/contrib/*.yaml
fastur
wintur
.DS_Store
firebase.json
*.toml
encryptsalt.go
*.data.json
.cache
simu_result
*.arbi_storage
*.turtle_storage
*.grid_storage
*.order_storage
*.trader_storage
*.stack
proxy_storage.json
proxy.log
*.log

# goctp

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

.idea*/
log*/
pkg*/
data*/
releases/


goctp/cmds/gateway/*.so
goctp/cmds/gateway/*.dylib
goctp/demo/*
goctp/ctp/tests/*.so
goctp/ctp/tests/*.dylib

#!/bin/bash

# Define distribution directory
dist_dir="/Users/<USER>/Desktop/releases/copier_dist"

# Create dist directory and copier_ext subdirectory if they don't exist
mkdir -p "$dist_dir/copier_ext"

# Copy extension files to dist directory
cp -r extension/* "$dist_dir/copier_ext/"

# Generate version number in MMDDHHMMSS format
version=$(date +"%m%d%H%M%S")

# Update version.js with new version number
echo "export const version = \"$version\";" > "$dist_dir/copier_ext/version.js"

# Change to backend directory and run build script
echo "building backend..."
cd backend
sh ./build.sh

# Copy server executables to dist directory
cp copier_server.exe "$dist_dir/"
cp copier_server "$dist_dir/"

# Create zip file with relative paths
cd "$dist_dir"
# Remove existing zip if it exists
rm -f copier.zip
zip -r copier.zip copier_ext copier_server.exe

echo "Build completed successfully!" 
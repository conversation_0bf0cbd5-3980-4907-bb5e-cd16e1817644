
exclude sensitive data
====
- ~/.copier/data contains sensitive data, don't upload files from this dir

copier backend breakdown
====
- backend services can be categorized into four: a. backend for copier extension interaction; b. console for copier backend to manage copier projects; c. balancer console to dealing with funds on platform and chains; d. automation backend code to run the projects automatically everyday

test build
====
- if the changes not backend related, you shouldn't build the code
- do not use build.sh to test build
- if there is a need to test build the project, you can build the binary to the name: "test_build"
- if you need to build the code, remember to cleanup it every time

git 
====
- you shouldn't git add and commit any files automatically, unless I give you clear instruction to do it

import { updateOrderSymbol, showSuccess, showError } from "./common.js";

// Global variables
let isSubmitting = false;
let lastPrice = null;
let priceUpdateInterval = null;

// Function to update the price input with the latest price
async function updatePriceInput() {
    const priceInput = document.getElementById("price");
    const symbol = document.getElementById("symbol").value;
    const marketCheckbox = document.getElementById("marketCheckbox");
    const positionInput = document.querySelector('input[name="position"]:checked');

    console.log("[order] update price input", priceInput, symbol, marketCheckbox, positionInput);
    if (!priceInput || !symbol || !marketCheckbox || !positionInput) {
        console.error("[order] Required elements not found for price update");
        return;
    }

    // Only update if market checkbox is checked
    if (!marketCheckbox.checked) {
        console.log("[order] Market checkbox not checked, skipping price update");
        return;
    }

    try {
        const data = await window.app.platform.GetLastPrice(symbol);
        if (data) {
            lastPrice = data.lastPrice;
            // Adjust price based on position
            const adjustedPrice = window.app.platform.CalculateOrderPrice(symbol, positionInput.value, lastPrice);
            priceInput.value = adjustedPrice.toFixed(2);
            console.log("[order] Price updated:", adjustedPrice);
        }
    } catch (error) {
        console.error("[order] Error updating price:", error);
    }
}

// Order tab functionality
export function initializeOrderTab() {
    console.log("[order] initializing order tab");
    updateOrderSymbol();
    
    // Initialize cancel all orders button
    const cancelAllOrdersButton = document.getElementById("cancelAllOrders");
    if (cancelAllOrdersButton) {
        cancelAllOrdersButton.addEventListener("click", handleCancelAllOrders);
    }

    // Initialize close all positions button
    const closeAllPositionsButton = document.getElementById("closeAllPositions");
    if (closeAllPositionsButton) {
        closeAllPositionsButton.addEventListener("click", handleCloseAllPositions);
    }

    // Initialize update stats button
    const updateStatsButton = document.getElementById("updateStats");
    if (updateStatsButton) {
        updateStatsButton.addEventListener("click", handleUpdateStats);
    }

    // Initialize withdraw test button
    const withdrawTestButton = document.getElementById("withdrawTest");
    if (withdrawTestButton) {
        withdrawTestButton.addEventListener("click", handleWithdrawTest);
    }

    // Initialize reload extension button
    const reloadExtensionButton = document.getElementById("reloadExtension");
    if (reloadExtensionButton) {
        reloadExtensionButton.addEventListener("click", handleReloadExtension);
    }

    // Initialize set stop loss button
    const setStopLossButton = document.getElementById("setStopLoss");
    if (setStopLossButton) {
        setStopLossButton.addEventListener("click", handleSetStopLoss);
    }

    // Initialize position radio buttons
    const positionRadios = document.querySelectorAll('input[name="position"]');
    const submitButton = document.getElementById("submitOrder");
    
    positionRadios.forEach(radio => {
        radio.addEventListener("change", (e) => {
            if (submitButton) {
                if (e.target.value === "short") {
                    submitButton.classList.add("short-mode");
                } else {
                    submitButton.classList.remove("short-mode");
                }
            }
        });
    });

    // Initialize market checkbox
    const marketCheckbox = document.getElementById("marketCheckbox");
    if (marketCheckbox) {
        console.log("[order] market checkbox found, adding event listener");
        marketCheckbox.addEventListener("change", handleMarketCheckboxChange);

        // Start periodic price updates when market checkbox is checked
        marketCheckbox.addEventListener("change", (e) => {
            if (e.target.checked) {
                // Start updates immediately and then every 5 seconds
                updatePriceInput();
                priceUpdateInterval = setInterval(updatePriceInput, 5000);
            } else {
                // Stop updates when unchecked
                if (priceUpdateInterval) {
                    clearInterval(priceUpdateInterval);
                    priceUpdateInterval = null;
                }
            }
        });

        // Start updates if checkbox is already checked
        if (marketCheckbox.checked) {
            updatePriceInput();
            priceUpdateInterval = setInterval(updatePriceInput, 5000);
        }
    } else {
        console.error("[order] market checkbox not found in dom");
    }

    // Order form handling
    const orderForm = document.getElementById("orderForm");
    const sizeUnitLabel = document.getElementById("sizeUnit");

    // Debug log to check if the Order form elements exist
    console.log("[order] all checkboxes in dom:", document.querySelectorAll('input[type="checkbox"]'));

    // Only log if elements are found
    if (orderForm || document.getElementById("symbol") || sizeUnitLabel) {
        console.log("[order] order form elements initialized:");
        console.log("[order] order form:", orderForm);
        console.log("[order] symbol input:", document.getElementById("symbol"));
        console.log("[order] size unit label:", sizeUnitLabel);
    } else {
        console.log("[order] order form elements not found in dom");
    }

    // Add event listeners
    if (orderForm) {
        orderForm.addEventListener("submit", handleOrderFormSubmit);
    }
}

// Event handler for market checkbox change
function handleMarketCheckboxChange(e) {
    console.log("[order] Market checkbox changed:", e.target.checked);
    const priceInput = document.getElementById("price");
    if (!priceInput) {
        console.error("[order] Price input not found");
        return;
    }

    if (e.target.checked) {
        // Get the last price from the platform
        window.app.platform
            .GetLastPrice(document.getElementById("symbol").value)
            .then((price) => {
                if (price) {
                    lastPrice = price.lastPrice;
                    priceInput.value = lastPrice;
                    showSuccess("Market price set successfully!");
                } else {
                    showError("Failed to get market price");
                    e.target.checked = false;
                }
            })
            .catch((error) => {
                console.error("[order] Error getting market price:", error);
                showError("Failed to get market price: " + error.message);
                e.target.checked = false;
            });
    }
}

// Event handler for order form submission
function handleOrderFormSubmit(e) {
    e.preventDefault();

    // Prevent multiple submissions
    if (isSubmitting) {
        console.log("[order] Order submission already in progress");
        return;
    }

    // Get form values
    const symbol = document.getElementById("symbol").value;
    const side = document.querySelector('input[name="position"]:checked').value;
    const price = parseFloat(document.getElementById("price").value);
    const size = parseFloat(document.getElementById("size").value);
    const reduceOnly = document.getElementById("reduceOnly").checked;

    // Validate form
    if (!symbol) {
        showError("Please set a symbol in settings");
        return;
    }

    if (!price || price <= 0) {
        showError("Please enter a valid price");
        return;
    }

    if (!size || size <= 0) {
        showError("Please enter a valid size");
        return;
    }

    // Set submitting flag
    isSubmitting = true;

    // Disable submit button and show loading state
    const submitButton = document.getElementById("submitOrder");
    const originalText = submitButton.textContent;
    submitButton.disabled = true;
    submitButton.textContent = "Placing Order...";

    // Create the order
    let orderFunc = window.app.platform.CreateOrder;
    const marketCheckbox = document.getElementById("marketCheckbox");
    if (window.app.platform.CreateMarketOrder && marketCheckbox && marketCheckbox.checked) {
        orderFunc = window.app.platform.CreateMarketOrder;
    }
    orderFunc({
        symbol: symbol,
        side: side,
        price: price,
        size: size,
        reduceOnly: reduceOnly,
    })
    .then((result) => {
        if (result.success) {
            showSuccess("order placed successfully");
            updateOrderSymbol();
        } else {
            throw new Error(result.error || "Failed to place order");
        }
    })
    .catch((error) => {
        console.error("[order] Error placing order:", error);
        showError(`error placing order: ${error.message}`);
    })
    .finally(() => {
        // Reset button and submitting flag
        submitButton.disabled = false;
        submitButton.textContent = originalText;
        isSubmitting = false;
    });
}

// Event handler for cancel all orders button
function handleCancelAllOrders() {
    console.log("[order] Cancel all orders clicked");
    window.app.platform.CloseAllOpenOrders();
}

// Event handler for close all positions button
function handleCloseAllPositions() {
    console.log("[order] Close all positions clicked");
    window.app.executor?.closePositions();
}

// Event handler for update stats button
function handleUpdateStats() {
    console.log("[order] Update stats clicked");
    // Empty implementation for now
    window.app.updateStats();
}

// Event handler for withdraw test button
function handleWithdrawTest() {
    console.log("[order] Withdraw test clicked");
    window.app._testWithdraw();
}

// Event handler for reload extension button
function handleReloadExtension() {
    console.log("[order] Reload extension clicked");
    window.app.reloadExtension();    
}

// Event handler for set stop loss button
function handleSetStopLoss() {
    console.log("[order] Set stop loss clicked");
    // Empty implementation for now
    window.app.setStoploss();
}

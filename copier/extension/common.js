// Common utility functions
// import { resumeApp, initApp, stopApp } from './bg_ctrl.js';
import { resumeApp, initApp, stopApp } from "./fg_ctrl.js";
import { STATUS } from "./app.js";

export function showError(message) {
    const errorMessage = document.getElementById("errorMessage");
    const appTitle = document.querySelector(".app-title");

    if (errorMessage && appTitle) {
        // Hide the title
        appTitle.style.opacity = "0";

        // Show the error message
        errorMessage.textContent = message;
        errorMessage.style.display = "block";
        errorMessage.style.backgroundColor = "#ffebee";
        errorMessage.style.color = "#d32f2f";
        errorMessage.style.border = "1px solid #ffcdd2";

        // Hide error message and show title after 5 seconds
        setTimeout(() => {
            errorMessage.style.display = "none";
            appTitle.style.opacity = "1";
        }, 5000);
    }
}

export function showSuccess(message) {
    const errorMessage = document.getElementById("errorMessage");
    const appTitle = document.querySelector(".app-title");

    if (errorMessage && appTitle) {
        // Hide the title
        appTitle.style.opacity = "0";

        // Show the success message
        errorMessage.textContent = message;
        errorMessage.style.display = "block";
        errorMessage.style.backgroundColor = "#e6f4ea";
        errorMessage.style.color = "#1e7e34";
        errorMessage.style.border = "1px solid #c3e6cb";

        // Hide success message and show title after 5 seconds
        setTimeout(() => {
            errorMessage.style.display = "none";
            appTitle.style.opacity = "1";
        }, 5000);
    }
}

// Function to update the symbol in the order form
export async function updateOrderSymbol() {
    let settings = await window.app.getSettings();
    const symbol = settings.defaultSymbol;

    console.log("[common] update order symbol called with symbol:", symbol);
    const symbolInput = document.getElementById("symbol");
    if (symbolInput) {
        console.log("[common] setting symbol input value to:", symbol);
        symbolInput.value = symbol;
    } else {
        console.error("[common] symbol input element not found in updateOrderSymbol");
    }

    // Update size unit label
    const sizeUnitLabel = document.getElementById("sizeUnitLabel");
    if (sizeUnitLabel && symbol) {
        const baseSymbol = symbol.split("-")[0];
        console.log("[common] setting size unit label to:", baseSymbol);
        sizeUnitLabel.textContent = baseSymbol;
    } else {
        console.log("[common] symbol is empty or undefined, not updating");
    }
}

export async function updateAppStatus() {
    let status = await window.app.getStatus();
    console.log("[common] update app status called with status:", status);
    const statusElement = document.getElementById("statusDisplay");
    const retryButton = document.getElementById("startAppRetryButton");

    if (statusElement) {
        if (!status) {
            statusElement.textContent = "Status: Not initialized";
            if (retryButton) {
                retryButton.textContent = "Init";
                retryButton.style.display = "block";
            }
            return;
        }

        const runningStatus = status.status;

        statusElement.textContent = `Status: ${runningStatus}, backend: ${status.backendStatus}`;

        // Add color indication based on status
        statusElement.className = "status-display";
        if (status.status == STATUS.RUNNING) {
            statusElement.classList.add("status-active");
            if (retryButton) {
                retryButton.textContent = "Stop";
                retryButton.style.display = "block";
            }
        } else {
            if (status.status == STATUS.UNINITIALIZED) {
                statusElement.textContent = "Status: Not initialized";
                if (retryButton) {
                    retryButton.textContent = "Init";
                    retryButton.style.display = "block";
                }
            } else {
                statusElement.textContent = "Status: Stopped";
                if (retryButton) {
                    retryButton.textContent = "Start";
                    retryButton.style.display = "block";
                }
            }
        }
    }
}

export async function setupStatusDisplay() {
    // Initialize status display
    const statusElement = document.getElementById("statusDisplay");
    if (statusElement) {
        statusElement.textContent = "Status: initializing...";
    }

    // Setup retry button
    const retryButton = document.getElementById("startAppRetryButton");
    if (retryButton) {
        retryButton.addEventListener("click", async () => {
            const status = await window.app.getStatus();
            if (!status || status.status == STATUS.UNINITIALIZED) {
                // If app is not initialized, initialize it
                await initApp();
                retryButton.textContent = "Start";
                retryButton.style.display = "block";
            } else if (status.status == STATUS.RUNNING) {
                // If app is running, stop it
                await stopApp();
                retryButton.textContent = "Start";
                retryButton.style.display = "block";
            } else {
                // If app is initialized but not running, start it
                retryButton.textContent = "Stop";
                retryButton.style.display = "block";
                await resumeApp();
            }
            updateAppStatus();
        });
    }
}

import { showError, showSuccess, updateAppStatus } from "./common.js";
import { version } from "./version.js";

// Positions tab functionality
let statusUpdateInterval = null;
let isRefreshing = false;
let refreshButton = null;

// Function to shorten ETH address
function shortenEthAddress(address) {
    if (!address || address.length < 10) return address;
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).catch(err => {
        console.error('Failed to copy text: ', err);
    });
}

export async function initializePositionsTab() {
    try {
        // Get refresh button and add click handler
        refreshButton = document.getElementById("refreshPositions");
        if (refreshButton) {
            refreshButton.addEventListener("click", handleRefreshPositions);
        }

        console.log("[positions] initializing positions tab");
        console.log("[positions] app:", window.app);

        // Add delay before initializing positions
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Initialize positions and status
        updateStatusAndPositions();

        // Start periodic updates
        startPeriodicUpdates();
    } catch (error) {
        console.error("[positions] error initializing positions tab:", error);
        showError("Please navigate to Lighter platform");
    }
}

function startPeriodicUpdates() {
    // Clear any existing interval
    if (statusUpdateInterval) {
        clearInterval(statusUpdateInterval);
    }

    // Update immediately and then every 5 seconds
    updateStatusAndPositions();
    statusUpdateInterval = setInterval(updateStatusAndPositions, 1000);
}

async function updateStatusAndPositions() {
    // Get DOM elements first
    const statusInfo = document.querySelector('.status-info');
    const debugBadge = document.getElementById('debugBadge');
    const modeBadge = document.getElementById('modeBadge');
    const statusSymbol = document.getElementById('statusSymbol');
    const statusUserIdElement = document.getElementById("statusUserId");
    const statusLastPrice = document.getElementById("statusLastPrice");
    const statusTotalMargin = document.getElementById("statusTotalMargin");
    const statusAvailableMargin = document.getElementById("statusAvailableMargin");

    try {
        let settings = await window.app.getSettings();
        updateAppStatus();

        // Update User ID with shortened format and alias if available
        const shortenedUserId = shortenEthAddress(settings.userId);
        const displayText = settings.alias 
            ? `${shortenedUserId} [${settings.alias}]`
            : shortenedUserId;
        statusUserIdElement.textContent = displayText || "Not set";
        
        // Make the shortened address clickable
        if (settings.userId) {
            statusUserIdElement.classList.add('eth-address');
            statusUserIdElement.style.cursor = 'pointer';
            statusUserIdElement.title = settings.userId;
            statusUserIdElement.onclick = () => copyToClipboard(settings.userId);
        }

        // Update symbol
        statusSymbol.textContent = settings.defaultSymbol || 'Not set';
        
        // Update mode badge
        modeBadge.textContent = settings.manualMode ? 'MANUAL' : 'AUTO';
        modeBadge.className = settings.manualMode ? 'manual-badge' : 'auto-badge';
        modeBadge.style.display = 'inline-block';  // Show the appropriate mode badge
        
        // Show/hide debug badge based on debug mode
        debugBadge.style.display = settings.debug ? 'inline-block' : 'none';

        // Update cooling time indicator in status info
        let coolingTimeItem = statusInfo.querySelector('.cooling-time-item');
        
        if (window.app.executor && window.app.executor.coolingTime > 0 && !settings.manualMode) {
            if (!coolingTimeItem) {
                coolingTimeItem = document.createElement('div');
                coolingTimeItem.className = 'status-item cooling-time-item';
                statusInfo.appendChild(coolingTimeItem);
            }
            
            // Convert milliseconds to a Date object and format to UTC+08:00
            const date = new Date(window.app.executor.coolingTime);
            const formattedTime = date.toLocaleString('en-US', {
                timeZone: 'Asia/Singapore',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            }).replace(/(\d+)\/(\d+)\/(\d+)/, '$3-$1-$2');
            
            coolingTimeItem.innerHTML = `
                <span class="status-label">Cooling Time</span>
                <span class="status-value">${formattedTime}</span>
            `;
        } else if (coolingTimeItem) {
            coolingTimeItem.remove();
        }

        // Add version display at the bottom
        let versionItem = statusInfo.querySelector('.version-item');
        if (!versionItem) {
            versionItem = document.createElement('div');
            versionItem.className = 'status-item version-item';
            statusInfo.appendChild(versionItem);
        }
        versionItem.innerHTML = `
            <span class="status-label">Version</span>
            <span class="status-value">${version}</span>
        `;

        // Update Last Price - moved to end since it can throw exceptions
        const symbol = settings.defaultSymbol;
        if (!symbol) {
            statusLastPrice.textContent = "No symbol set";
        } else {
            const priceInfo = await window.app.platform.GetLastPrice(symbol);
            statusLastPrice.textContent = priceInfo?.lastPrice
                ? `$${Number(priceInfo.lastPrice).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
                : "No price available";
        }

        // Get margin information
        const margin = await window.app.platform.GetMargin();
        const formatNumber = (num) => {
            if (num === 0) return "0.00";
            return Number(num).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        };

        statusTotalMargin.textContent = `$${formatNumber(margin.totalMargin)}`;
        statusAvailableMargin.textContent = `$${formatNumber(margin.availableMargin)}`;

        // Refresh positions
        handleRefreshPositions();
    } catch (error) {
        console.error("[positions] error updating status info:", error);
        statusLastPrice.textContent = "Error";
        statusTotalMargin.textContent = "Error";
        statusAvailableMargin.textContent = "Error";
    }
}

// Event handler for refresh button click
async function handleRefreshPositions() {
    if (isRefreshing) {
        console.log("[positions] refresh already in progress");
        return;
    }

    isRefreshing = true;
    refreshButton.disabled = true;

    try {
        // Get positions from the platform
        const agents = await window.app.getAgents();
        console.log("[positions] agents:", agents);

        // Render positions
        renderPositions(agents);
    } catch (error) {
        console.error("[positions] error refreshing positions:", error);
        showError("Failed to refresh positions: " + error.message);
    } finally {
        refreshButton.disabled = false;
        isRefreshing = false;
    }
}

// Function to render positions
async function renderPositions(agents) {
    try {
        console.log("[positions] Fetching positions...");
        let masterAgent = agents.masterAgent;
        let copierAgents = agents.copierAgents;

        console.log("[positions] master agent:", masterAgent);
        console.log("[positions] copier agents:", copierAgents);

        let role = window.app.role;
        let settings = await window.app.getSettings();
        const currentUserId = settings.userId;

        const positionsList = document.querySelector(".positions-list");
        const errorMessage = document.getElementById("errorMessage");
        if (!positionsList) {
            console.error("[positions] Positions list element not found");
            return;
        }

        let html = "";
        let hasError = false;

        // Clear error message if no errors
        if (!hasError) {
            errorMessage.style.display = "none";
        }

        const renderPositionCard = (position, agent) => {
            const showUserId = agent?.userId && agent.userId !== currentUserId;
            const isSymbolMismatch = position.symbol !== settings.defaultSymbol;
            const shortenedUserId = shortenEthAddress(agent?.userId);
            const displayUserId = agent?.alias 
                ? `${shortenedUserId} [${agent.alias}]`
                : shortenedUserId;

            const userIdElement = document.createElement("span");
            userIdElement.textContent = displayUserId;
            
            // Make the shortened address clickable
            if (agent?.userId) {
                userIdElement.classList.add('eth-address');
                userIdElement.style.cursor = 'pointer';
                userIdElement.title = agent.userId;
                userIdElement.onclick = () => copyToClipboard(agent.userId);
            }

            return `
        <div class="position-card ${agent === masterAgent ? "master" : ""}">
          <div class="position-header">
            <span class="position-symbol ${isSymbolMismatch ? "symbol-mismatch" : ""}">${position.symbol}</span>
            <span class="position-side ${position.side}">${position.side.toUpperCase()}</span>
          </div>
          <div class="position-details">
            <div class="detail-item">
              <span class="detail-label">Entry Price</span>
              <span class="detail-value">$${position.entryPrice.toLocaleString()}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Mark Price</span>
              <span class="detail-value">$${position.markPrice.toLocaleString()}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Size</span>
              <span class="detail-value">${position.size}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Margin</span>
              <span class="detail-value">$${position.margin.toLocaleString()}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">PnL</span>
              <span class="detail-value pnl-value ${position.pnl >= 0 ? "positive" : "negative"}">
                $${position.pnl.toLocaleString()} (${position.pnlPercentage.toFixed(2)}%)
              </span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Liquidation Price</span>
              <span class="detail-value">$${position.liquidationPrice.toLocaleString()}</span>
            </div>
            ${
                showUserId
                    ? `
              <div class="detail-item full-width">
                <span class="detail-label">User ID</span>
                <span class="detail-value">${userIdElement.outerHTML}</span>
              </div>
            `
                    : ""
            }
          </div>
        </div>
      `;
        };

        // Render positions based on role
        if (role === "master") {
            // Master role: show master positions first, then copier positions
            if (masterAgent?.positions?.length > 0) {
                masterAgent.positions.forEach((position) => {
                    html += renderPositionCard(position, masterAgent);
                });
            }
            if (copierAgents?.length > 0) {
                html += `
          <div class="positions-separator">
            <span class="separator-label">Copier Positions</span>
          </div>
        `;
                copierAgents.forEach((agent) => {
                    agent.positions?.forEach((position) => {
                        html += renderPositionCard(position, agent);
                    });
                });
            }
        } else {
            // Copier role: show copier positions first, then master positions
            if (copierAgents?.length > 0) {
                copierAgents.forEach((agent) => {
                    agent.positions?.forEach((position) => {
                        html += renderPositionCard(position, agent);
                    });
                });
            }
            if (masterAgent?.positions?.length > 0) {
                html += `
          <div class="positions-separator">
            <span class="separator-label">Master Positions</span>
          </div>
        `;
                masterAgent.positions.forEach((position) => {
                    html += renderPositionCard(position, masterAgent);
                });
            }
        }

        // If no positions exist, show message
        const hasMasterPositions = masterAgent?.positions?.length > 0;
        const hasCopierPositions = copierAgents?.some((agent) => agent.positions?.length > 0);
        if (!hasMasterPositions && !hasCopierPositions) {
            html = '<div class="no-positions">No open positions</div>';
        }

        positionsList.innerHTML = html;
        console.log("[positions] Positions rendered successfully");
    } catch (error) {
        console.error("[positions] Error rendering positions:", error);
        const positionsList = document.querySelector(".positions-list");
        if (positionsList) {
            positionsList.innerHTML = '<div class="error-message">Error loading positions</div>';
        }
    }
}

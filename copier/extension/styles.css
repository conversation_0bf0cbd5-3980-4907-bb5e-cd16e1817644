body {
    width: 100%;
    height: 100vh;
    padding: 20px;
    padding-bottom: 30px;
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    margin: 0;
    box-sizing: border-box;
    overflow: hidden;
}

.container {
    background-color: white;
    border-radius: 12px;
    padding: 30px;
    padding-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: calc(100% - 40px);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 10px;
}

#errorMessage {
    background-color: #ffebee;
    color: #d32f2f;
    padding: 12px;
    margin-bottom: 15px;
    border-radius: 4px;
    font-size: 14px;
    display: none;
    border: 1px solid #ffcdd2;
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    margin-bottom: 30px;
    border-bottom: 1px solid #ddd;
    flex-shrink: 0;
}

.nav-tab {
    padding: 12px 24px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 15px;
    color: #666;
    position: relative;
}

.nav-tab.active {
    color: #4caf50;
    font-weight: bold;
}

.nav-tab.active::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #4caf50;
}

/* Tab Content */
.tab-content {
    display: none;
    flex: 1;
    overflow: hidden;
}

.tab-content.active {
    display: flex;
    flex-direction: column;
}

/* Form container */
.content {
    flex: 1;
    overflow-y: auto;
    padding: 0 30px;
    margin: 0 -30px;
}

/* Settings Form Styles */
#settings .content {
    padding-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group:last-child {
    margin-bottom: 0;
}

/* Adjust form elements spacing */
input[type="text"],
input[type="number"],
select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 15px;
    box-sizing: border-box;
}

.input-with-button {
    display: flex;
    align-items: center;
    gap: 8px;
}

.percentage-input-container {
    margin-bottom: 0;
}

label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-size: 15px;
    font-weight: 500;
}

button[type="submit"] {
    width: 100%;
    background-color: #4caf50;
    color: white;
    padding: 14px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 15px;
    font-weight: bold;
    margin-top: 25px;
    transition: background-color 0.2s;
}

button[type="submit"].short-mode {
    background-color: #d32f2f;
}

button[type="submit"].short-mode:hover {
    background-color: #b71c1c;
}

button[type="submit"]:hover {
    background-color: #45a049;
}

.cancel-order-button {
    width: 100%;
    background-color: transparent;
    color: #666;
    padding: 14px;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    font-size: 15px;
    font-weight: bold;
    margin-top: 20px;
    transition: all 0.2s;
}

.cancel-order-button:hover {
    background-color: #f5f5f5;
    border-color: #ccc;
}

/* Position Card Styles */
.position-card {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
}

.position-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.position-symbol {
    font-size: 18px;
    font-weight: bold;
}

.position-symbol.symbol-mismatch {
    color: #d32f2f;
}

.position-side {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: bold;
}

.position-side.long {
    background-color: #e6f4ea;
    color: #1e7e34;
}

.position-side.short {
    background-color: #fbe9e7;
    color: #d32f2f;
}

.position-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-label {
    font-size: 13px;
    color: #666;
    margin-bottom: 6px;
}

.detail-value {
    font-size: 15px;
    font-weight: 500;
}

.pnl-value.positive {
    color: #1e7e34;
}

.pnl-value.negative {
    color: #d32f2f;
}

.position-actions {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.close-position-btn {
    width: 100%;
    padding: 8px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
}

.close-position-btn:hover {
    background-color: #e9ecef;
    color: #d32f2f;
}

/* Positions separator */
.positions-separator {
    display: flex;
    align-items: center;
    margin: 20px 0;
    padding: 5px 0;
}

.positions-separator::before,
.positions-separator::after {
    content: "";
    flex: 1;
    border-bottom: 1px solid #e0e0e0;
}

.separator-label {
    padding: 0 10px;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    background-color: white;
}

.position-card.master {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
}

.position-card.master .position-header {
    background-color: transparent;
}

/* Form Styles */
input[type="text"]:focus,
input[type="number"]:focus,
select:focus {
    outline: none;
    border-color: #4caf50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.toggle-switch {
    display: flex;
    gap: 15px;
    margin-top: 8px;
}

.toggle-switch input[type="radio"] {
    display: none;
}

.toggle-switch label {
    padding: 10px 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    flex: 1;
    text-align: center;
}

.toggle-switch input[type="radio"]:checked + label {
    background-color: #4caf50;
    color: white;
    border-color: #4caf50;
}

.toggle-switch input[type="radio"][value="short"]:checked + label {
    background-color: #d32f2f;
    border-color: #d32f2f;
}

select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
}

.input-with-button button {
    padding: 8px 12px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    white-space: nowrap;
    transition: all 0.2s;
}

.input-with-button button:hover {
    background-color: #e9ecef;
}

.percentage-input-container {
    position: relative;
    width: 100%;
}

.percentage-input-container input {
    width: 100%;
    padding-right: 25px;
}

.percentage-input-container .percentage-symbol {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    pointer-events: none;
}

/* Messages */
.no-positions {
    text-align: center;
    color: #666;
    padding: 20px;
    font-size: 14px;
}

.error-message {
    text-align: center;
    color: #d32f2f;
    padding: 20px;
    font-size: 14px;
}

/* Positions Header */
.positions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.positions-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.refresh-button {
    display: flex;
    align-items: center;
    gap: 6px;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    color: #555;
    cursor: pointer;
    transition: all 0.2s;
}

.refresh-button:hover {
    background-color: #e0e0e0;
}

.refresh-button svg {
    transition: transform 0.3s;
}

.refresh-button:active svg {
    transform: rotate(180deg);
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.spinner {
    animation: spin 1s linear infinite;
}

/* Input with unit label */
.input-with-unit {
    display: flex;
    align-items: center;
    position: relative;
}

.input-with-unit input {
    flex: 1;
    padding-right: 50px; /* Make room for the unit label */
}

.unit-label {
    position: absolute;
    right: 10px;
    color: #888;
    font-size: 14px;
    pointer-events: none;
}

/* Checkbox label */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 8px;
}

.checkbox-text {
    font-size: 14px;
    color: #333;
}

/* Readonly input */
input[readonly] {
    background-color: #f5f5f5;
    cursor: not-allowed;
}

/* Header and title styles */
.header {
    margin-bottom: 15px;
    min-height: 40px; /* Match the height of error message */
    position: relative;
}

.app-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: #333;
    text-align: center;
    position: absolute;
    width: 100%;
    top: 50%;
    transform: translateY(-50%);
    transition: opacity 0.3s ease;
}

/* Error message styles */
.error-message {
    padding: 8px 12px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
    display: none;
    background-color: #ffebee;
    color: #d32f2f;
    border: 1px solid #ffcdd2;
    position: absolute;
    width: 100%;
    top: 50%;
    transform: translateY(-50%);
    text-align: center;
    z-index: 1;
}

.input-with-checkbox {
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-left: 10px;
}

.checkbox-container input[type="checkbox"] {
    margin: 0;
}

.checkbox-container label {
    font-size: 14px;
    cursor: pointer;
}

.market-button {
    background-color: #4caf50;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 5px;
    transition: background-color 0.3s;
}

.market-button:hover {
    background-color: #45a049;
}

.market-button:active {
    background-color: #3d8b40;
}

.generate-id-button {
    margin-top: 8px;
    padding: 4px 8px;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.generate-id-button:hover {
    background-color: #45a049;
}

.status-info {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-label {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.status-value {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.status-value.highlight {
    color: #4caf50;
    animation: highlight 1s ease-in-out;
}

@keyframes highlight {
    0%,
    100% {
        background-color: transparent;
    }
    50% {
        background-color: rgba(76, 175, 80, 0.1);
    }
}

/* Footer styles */
.footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    margin-top: auto;
    border-radius: 0 0 12px 12px;
    margin-left: -30px;
    margin-right: -30px;
    margin-bottom: -15px;
    padding-bottom: 0;
}

.status-display {
    font-size: 14px;
    color: #555;
    flex: 1;
    padding: 10px 0;
}

.status-display.status-active {
    color: #28a745;
    font-weight: 500;
}

.retry-button {
    display: flex;
    align-items: center;
    background-color: #e9ecef;
    color: #495057;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 14px;
    margin: 10px 0;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.retry-button svg {
    margin-right: 6px;
}

.retry-button:hover {
    background-color: #dee2e6;
}

.eth-address {
    font-family: monospace;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
}

.eth-address:hover {
    text-decoration: underline;
}

.eth-address:active {
    transform: scale(0.95);
    opacity: 0.8;
}

.auto-badge {
    display: inline-block;
    background-color: #4caf50;
    color: white;
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    margin-right: 4px;
}

.manual-badge {
    display: inline-block;
    background-color: #ff4444;
    color: white;
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    margin-right: 4px;
}

.debug-badge {
    display: inline-block;
    background-color: #4444ff;
    color: white;
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    margin-left: 4px;
}

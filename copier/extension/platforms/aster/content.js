function randomNumber() {
    let min = 1000;
    let max = 9999;
    return Math.floor(Math.random() * (max - min) + min);
}

let refid = randomNumber();

// debug log function
function log(...args) {
    let now = new Date().toISOString();
    console.log(`#${refid} ${now}: `, ...args);
}

function logError(...args) {
    let now = new Date().toISOString();
    console.error(`#${refid} ${now}: `, ...args);
}

// Content script for Lighter platform
log("[aster_content] ==========================================");
log("[aster_content] Aster content script loaded - TEST LOG");
log("[aster_content] ==========================================");

let symbolPriceDecimals = {
    BTCUSDT: 1,
    ETHUSDT: 1,
};

let symbolAmountDecimals = {
    BTCUSDT: 3,
    ETHUSDT: 3,
};

// Function to wait for an element to be present in the DOM
function waitForElement(selector, timeout = 100) {
    try {
        log("[aster_content] waiting for element:", selector);
        log("[aster_content] DEBUG: waitForElement - starting...");

        return new Promise((resolve, reject) => {
            log("[aster_content] DEBUG: waitForElement - checking if element exists...");
            if (document.querySelector(selector)) {
                log("[aster_content] DEBUG: waitForElement - element found immediately");
                return resolve(document.querySelector(selector));
            }

            log("[aster_content] DEBUG: waitForElement - setting up observer...");
            const observer = new MutationObserver((mutations) => {
                log("[aster_content] DEBUG: waitForElement - mutation observed");
                if (document.querySelector(selector)) {
                    log("[aster_content] DEBUG: waitForElement - element found after mutation");
                    observer.disconnect();
                    resolve(document.querySelector(selector));
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true,
            });

            // Timeout after specified time
            setTimeout(() => {
                log("[aster_content] DEBUG: waitForElement - timeout reached");
                observer.disconnect();
                reject(new Error(`timeout waiting for element: ${selector}`));
            }, timeout);
        });
    } catch (error) {
        logError("[aster_content] error waiting for element:", error);
        throw error;
    }
}

async function switchToPositionsTab() {
    try {
        log("[aster_content] switching to positions tab...");
        log("[aster_content] DEBUG: switchToPositionsTab - starting...");

        // Find the positions button
        const positionsButton = document.getElementById('tour-guide-close-position');
        log("[aster_content] DEBUG: switchToPositionsTab - positionsButton:", positionsButton);
        if (!positionsButton) {
            logError("[aster_content] positions button not found");
            throw new Error("positions button not found");
        }

        // Extract the number of positions from the button text
        const buttonText = positionsButton.textContent.trim();
        log("[aster_content] DEBUG: switchToPositionsTab - buttonText:", buttonText);
        const positionsMatch = buttonText.match(/Positions\((\d+)\)/);
        log("[aster_content] DEBUG: switchToPositionsTab - positionsMatch:", positionsMatch);
        const positionCount = positionsMatch ? parseInt(positionsMatch[1]) : 0;
        log("[aster_content] DEBUG: switchToPositionsTab - positionCount:", positionCount);

        // Use the helper function to select the button
        await selectRadixButton(positionsButton, "positions");

        log("[aster_content] switched to positions tab");
        return positionCount;
    } catch (error) {
        logError("[aster_content] error switching to positions tab:", error);
        throw error;
    }
}

// Helper function to find elements by pattern
function findElementByPattern(pattern) {
    try {
        log("[aster_content] finding element by pattern:", pattern);
        log("[aster_content] DEBUG: findElementByPattern - starting...");

        // Convert pattern like "radix-«»-trigger-openOrders" to a regex
        const regexPattern = pattern.replace(/«»/g, '[^"]+');
        log("[aster_content] DEBUG: findElementByPattern - regexPattern:", regexPattern);
        const regex = new RegExp(`^${regexPattern}$`);
        log("[aster_content] DEBUG: findElementByPattern - regex:", regex);
        
        // Find all elements with id attribute
        const elements = Array.from(document.querySelectorAll('[id]'));
        log("[aster_content] DEBUG: findElementByPattern - elements:", elements);
        
        // Find the first element whose id matches the pattern
        const element = elements.find(element => regex.test(element.id));
        log("[aster_content] DEBUG: findElementByPattern - element:", element);

        return element;
    } catch (error) {
        logError("[aster_content] error finding element by pattern:", error);
        return null;
    }
}

function getOpenOrdersButton() {
    try {
        log("[aster_content] getting open orders button...");
        log("[aster_content] DEBUG: getOpenOrdersButton - starting...");

        // Find the open orders button using the pattern
        const button = findElementByPattern('radix-«»-trigger-openOrders');
        log("[aster_content] DEBUG: getOpenOrdersButton - button:", button);
        if (!button) {
            logError("[aster_content] open orders button not found");
            throw new Error("open orders button not found");
        }

        // Get the number of open orders from the button text
        const buttonText = button.textContent.trim();
        log("[aster_content] DEBUG: getOpenOrdersButton - buttonText:", buttonText);
        const match = buttonText.match(/\((\d+)\)/);
        log("[aster_content] DEBUG: getOpenOrdersButton - match:", match);
        const count = match ? parseInt(match[1]) : 0;
        log("[aster_content] DEBUG: getOpenOrdersButton - count:", count);

        return { button, count };
    } catch (error) {
        logError("[aster_content] error getting open orders button:", error);
        throw error;
    }
}

async function switchToOpenOrdersTab() {
    try {
        log("[aster_content] switching to open orders tab...");
        log("[aster_content] DEBUG: switchToOpenOrdersTab - starting...");

        // Get the open orders button
        const { button } = getOpenOrdersButton();
        log("[aster_content] DEBUG: switchToOpenOrdersTab - button:", button);

        // Use the helper function to select the button
        await selectRadixButton(button, "open orders");

        log("[aster_content] switched to open orders tab");
    } catch (error) {
        logError("[aster_content] error switching to open orders tab:", error);
        throw error;
    }
}

async function closeAllOpenOrders() {
    try {
        log("[aster_content] closing all open orders...");
        log("[aster_content] DEBUG: closeAllOpenOrders - starting...");

        // Get the current open orders count
        const { button, count } = getOpenOrdersButton();
        log("[aster_content] DEBUG: closeAllOpenOrders - current order count:", count);

        // If there are no orders, return early
        if (count === 0) {
            log("[aster_content] DEBUG: closeAllOpenOrders - no open orders to cancel");
            return 0;
        } else {
            // Switch to the open orders tab only if there are orders
            await switchToOpenOrdersTab();
            log("[aster_content] DEBUG: closeAllOpenOrders - switched to open orders tab");
        }

        // Find the cancel all button by text content
        const cancelAllButton = Array.from(document.querySelectorAll('button')).find(button => 
            button.textContent.trim() === 'Cancel All'
        );
        log("[aster_content] DEBUG: closeAllOpenOrders - cancelAllButton:", cancelAllButton);
        if (!cancelAllButton) {
            logError("[aster_content] cancel all button not found");
            throw new Error("cancel all button not found");
        }

        // Click the cancel all button
        cancelAllButton.click();
        log("[aster_content] clicked cancel all button");

        // Wait for the confirmation dialog to appear
        await new Promise(resolve => setTimeout(resolve, 1000));
        log("[aster_content] DEBUG: closeAllOpenOrders - waiting for confirmation dialog...");

        // Find the confirmation dialog by its text content
        const confirmationDialog = Array.from(document.querySelectorAll('div')).find(div => 
            div.textContent.includes('Are you sure you want to cancel all ?')
        );
        log("[aster_content] DEBUG: closeAllOpenOrders - confirmationDialog:", confirmationDialog);
        if (!confirmationDialog) {
            logError("[aster_content] confirmation dialog not found");
            throw new Error("confirmation dialog not found");
        }

        // Find the confirm button in the dialog (it's the second button)
        const buttons = confirmationDialog.querySelectorAll('button');
        const confirmButton = buttons[1]; // First button is Cancel, second is Confirm
        log("[aster_content] DEBUG: closeAllOpenOrders - confirmButton:", confirmButton);
        if (!confirmButton) {
            logError("[aster_content] confirm button not found");
            throw new Error("confirm button not found");
        }

        // Click the confirm button
        confirmButton.click();
        log("[aster_content] clicked confirm button");

        // Wait for the orders to be canceled
        await new Promise(resolve => setTimeout(resolve, 1000));
        log("[aster_content] DEBUG: closeAllOpenOrders - waiting for orders to be canceled...");

        // Get the number of orders that were canceled
        const finalCount = await getOpenOrdersButton();
        log("[aster_content] DEBUG: closeAllOpenOrders - remaining orders:", finalCount.count);

        return finalCount.count;
    } catch (error) {
        logError("[aster_content] error closing all open orders:", error);
        throw error;
    }
}

// Function to extract positions from the webpage
async function extractPositions() {
    try {
        log("[aster_content] starting position extraction...");
        log("[aster_content] DEBUG: extractPositions - starting...");

        await switchToPositionsTab();

        // Check for "No position found" message first
        const noPositionsElements = document.querySelectorAll("div");
        log("[aster_content] DEBUG: extractPositions - noPositionsElements:", noPositionsElements);
        for (const element of noPositionsElements) {
            if (element.textContent.trim() === "No position found") {
                log('[aster_content] found "No position found" message, returning empty array');
                return [];
            }
        }

        // Find the positions table body
        const positionsTableBody = document.querySelector('.list-body');
        log("[aster_content] DEBUG: extractPositions - positionsTableBody:", positionsTableBody);
        if (!positionsTableBody) {
            logError("[aster_content] positions table body not found");
            throw new Error("positions table body not found");
        }

        // Get all position rows (each row has class 'list-row')
        const positionRows = positionsTableBody.querySelectorAll('.list-row');
        log("[aster_content] found position rows:", positionRows.length);
        log("[aster_content] DEBUG: extractPositions - positionRows:", positionRows);

        if (!positionRows || positionRows.length === 0) {
            log("[aster_content] no positions found in table");
            return [];
        }

        // Extract position data from each row
        const positions = Array.from(positionRows).map((row, index) => {
            log("[aster_content] DEBUG: extractPositions - processing row:", index);
            
            // Extract symbol and side from the first cell
            const symbolCell = row.querySelector('.row-cell:first-child');
            log("[aster_content] DEBUG: extractPositions - symbolCell:", symbolCell);
            let symbol = "";
            let side = "";
            if (symbolCell) {
                const symbolText = symbolCell.textContent.trim();
                log("[aster_content] DEBUG: extractPositions - symbolText:", symbolText);
                
                // Split by Buy/Sell to get the symbol
                if (symbolText.includes("Buy")) {
                    symbol = symbolText.split("Buy")[0];
                    side = "long";
                } else if (symbolText.includes("Sell")) {
                    symbol = symbolText.split("Sell")[0];
                    side = "short";
                }
            }
            log("[aster_content] symbol:", symbol, "side:", side);

            // Extract size from the second cell
            const sizeCell = row.querySelector('.row-cell:nth-child(2)');
            log("[aster_content] DEBUG: extractPositions - sizeCell:", sizeCell);
            let size = 0;
            if (sizeCell) {
                const sizeText = sizeCell.textContent.trim();
                log("[aster_content] DEBUG: extractPositions - sizeText:", sizeText);
                // Get the currency from the symbol (e.g., BTC from BTCUSDT)
                const currency = symbol.replace("USDT", "");
                // Remove the currency from the size text before parsing
                size = parseFloat(sizeText.replace(currency, ""));
            }
            log("[aster_content] size:", size);

            // Extract entry price from the third cell
            const entryPriceCell = row.querySelector('.row-cell:nth-child(3)');
            log("[aster_content] DEBUG: extractPositions - entryPriceCell:", entryPriceCell);
            let entryPrice = 0;
            if (entryPriceCell) {
                const priceText = entryPriceCell.textContent.trim();
                log("[aster_content] DEBUG: extractPositions - priceText:", priceText);
                entryPrice = parseFloat(priceText.replace(/,/g, ""));
            }
            log("[aster_content] entry price:", entryPrice);

            // Extract mark price from the fourth cell
            const markPriceCell = row.querySelector('.row-cell:nth-child(4)');
            log("[aster_content] DEBUG: extractPositions - markPriceCell:", markPriceCell);
            let markPrice = 0;
            if (markPriceCell) {
                const priceText = markPriceCell.textContent.trim();
                log("[aster_content] DEBUG: extractPositions - priceText:", priceText);
                markPrice = parseFloat(priceText.replace(/,/g, ""));
            }
            log("[aster_content] mark price:", markPrice);

            // Extract margin from the fifth cell
            const marginCell = row.querySelector('.row-cell:nth-child(5)');
            log("[aster_content] DEBUG: extractPositions - marginCell:", marginCell);
            let margin = 0;
            if (marginCell) {
                const marginText = marginCell.textContent.trim();
                log("[aster_content] DEBUG: extractPositions - marginText:", marginText);
                margin = parseFloat(marginText.replace("USDT", "").replace(/,/g, ""));
            }
            log("[aster_content] margin:", margin);

            // Extract liquidation price from the sixth cell
            const liquidationPriceCell = row.querySelector('.row-cell:nth-child(6)');
            log("[aster_content] DEBUG: extractPositions - liquidationPriceCell:", liquidationPriceCell);
            let liquidationPrice = 0;
            if (liquidationPriceCell) {
                const priceText = liquidationPriceCell.textContent.trim();
                log("[aster_content] DEBUG: extractPositions - priceText:", priceText);
                if (priceText !== "--") {
                    liquidationPrice = parseFloat(priceText.replace(/,/g, ""));
                }
            }
            log("[aster_content] liquidation price:", liquidationPrice);

            // Extract PnL from the seventh cell
            const pnlCell = row.querySelector('.row-cell:nth-child(7)');
            log("[aster_content] DEBUG: extractPositions - pnlCell:", pnlCell);
            let pnl = 0;
            let pnlPercentage = 0;
            if (pnlCell) {
                const pnlText = pnlCell.textContent.trim();
                log("[aster_content] DEBUG: extractPositions - pnlText:", pnlText);

                // Split the text into parts
                const parts = pnlText.split("Share");
                log("[aster_content] DEBUG: extractPositions - pnl parts:", parts);
                
                if (parts.length >= 2) {
                    // Parse the dollar amount (remove USDT and convert to number)
                    const dollarPart = parts[0].replace("USDT", "").replace(/,/g, "").trim();
                    log("[aster_content] DEBUG: extractPositions - dollarPart:", dollarPart);
                    pnl = parseFloat(dollarPart);

                    // Parse the percentage (remove %)
                    const percentPart = parts[1].replace("%", "").trim();
                    log("[aster_content] DEBUG: extractPositions - percentPart:", percentPart);
                    pnlPercentage = parseFloat(percentPart);
                }
            }
            log("[aster_content] pnl:", pnl, "pnl percentage:", pnlPercentage);

            const position = {
                symbol: symbol,
                side: side,
                entryPrice: entryPrice,
                markPrice: markPrice,
                size: Math.abs(size),
                margin: margin,
                liquidationPrice: liquidationPrice,
                pnl: pnl,
                pnlPercentage: pnlPercentage,
                createTime: Date.now(),
            };

            log("[aster_content] created position object:", position);
            return position;
        });

        log("[aster_content] extracted positions:", positions);
        return positions;
    } catch (error) {
        logError("[aster_content] error extracting positions:", error);
        throw error;
    }
}

// Function to check if service is unavailable
async function extractServiceUnavailable() {
    return false;
}

async function extractOpenOrders() {
    return [];
}

// Helper function to select a Radix UI button
async function selectRadixButton(button, buttonName) {
    try {
        log(`[aster_content] selecting ${buttonName} button`);
        
        if (!button) {
            logError(`[aster_content] ${buttonName} button not found`);
            throw new Error(`${buttonName} button not found`);
        }

        const isActive = button.getAttribute('data-state') === 'active';
        if (isActive) {
            log(`[aster_content] ${buttonName} button already active`);
            return;
        }

        // Find the tab list and get all tabs
        const tabList = button.closest('[role="tablist"]');
        const tabs = Array.from(tabList?.querySelectorAll('[role="tab"]') || []);
        const buttonIndex = tabs.indexOf(button);
        
        if (buttonIndex === -1) {
            logError(`[aster_content] ${buttonName} button not found in tab list`);
            throw new Error(`${buttonName} button not found in tab list`);
        }

        // Focus and select the button
        tabList.focus();
        button.focus();
        
        // Create keyboard events
        const createKeyEvent = (key) => new KeyboardEvent('keydown', {
            key,
            code: key,
            bubbles: true,
            cancelable: true
        });
        
        // Select the button
        button.dispatchEvent(createKeyEvent('Enter'));
        
        // Wait for the UI to update
        await new Promise(resolve => setTimeout(resolve, 500));

        // Verify the state after click
        const newState = button.getAttribute('data-state');
        if (newState !== 'active') {
            logError(`[aster_content] ${buttonName} button did not become active after click`);
            throw new Error(`${buttonName} button did not become active after click`);
        }

        log(`[aster_content] ${buttonName} button selected successfully`);
    } catch (error) {
        logError(`[aster_content] error selecting ${buttonName} button:`, error);
        throw error;
    }
}

async function createOrder(orderData) {
    try {
        log("[aster_content] creating order with data:", orderData);
        log("[aster_content] DEBUG: createOrder - input orderData:", orderData);

        // check symbol is supported
        if (!symbolPriceDecimals[orderData.symbol]) {
            log("[aster_content] DEBUG: createOrder - symbol price decimals not supported:", orderData.symbol);
            throw new Error("symbol price decimals not supported: " + orderData.symbol);
        }
        if (!symbolAmountDecimals[orderData.symbol]) {
            log("[aster_content] DEBUG: createOrder - symbol amount decimals not supported:", orderData.symbol);
            throw new Error("symbol amount decimals not supported: " + orderData.symbol);
        }

        // 1. Select "Limit" order type if not already selected
        const limitButton = Array.from(document.querySelectorAll('button')).find(button => 
            button.textContent.trim() === 'Limit'
        );
        if (!limitButton) {
            logError("[aster_content] limit button not found");
            throw new Error("limit button not found");
        }
        limitButton.click();
        // Wait for the UI to update
        await new Promise(resolve => setTimeout(resolve, 100));

        // 2. Select long/short position
        const longButton = Array.from(document.querySelectorAll('button')).find(button => 
            button.textContent.trim() === 'Buy'
        );
        const shortButton = Array.from(document.querySelectorAll('button')).find(button => 
            button.textContent.trim() === 'Sell'
        );
        
        if (longButton && shortButton) {
            const targetButton = orderData.side === "long" ? longButton : shortButton;
            targetButton.click();
            // Wait for the UI to update
            await new Promise(resolve => setTimeout(resolve, 100));
        } else {
            log("[aster_content] DEBUG: createOrder - position buttons not found");
            throw new Error("position buttons not found");
        }

        // 3. Fill in the limit price
        const limitPriceInput = document.getElementById('form_price-input');
        log("[aster_content] DEBUG: createOrder - limitPriceInput:", limitPriceInput);
        if (limitPriceInput) {
            log("[aster_content] setting limit price:", orderData.price);
            limitPriceInput.value = Number(orderData.price).toFixed(symbolPriceDecimals[orderData.symbol]);
            // Trigger input event to update the UI
            limitPriceInput.dispatchEvent(new Event("input", { bubbles: true }));
            // Wait for the UI to update
            await new Promise((resolve) => setTimeout(resolve, 100));
        } else {
            log("[aster_content] DEBUG: createOrder - limit price input not found");
            throw new Error("limit price input not found");
        }

        // 4. Fill in the amount
        const amountInput = document.getElementById('form_qty_input');
        log("[aster_content] DEBUG: createOrder - amountInput:", amountInput);
        if (amountInput) {
            log("[aster_content] setting order amount:", orderData.size);
            amountInput.value = Number(orderData.size).toFixed(symbolAmountDecimals[orderData.symbol]);
            // Trigger input event to update the UI
            amountInput.dispatchEvent(new Event("input", { bubbles: true }));
            // Wait for the UI to update
            await new Promise((resolve) => setTimeout(resolve, 100));
        } else {
            log("[aster_content] DEBUG: createOrder - amount input not found");
            throw new Error("amount input not found");
        }

        // 5. Set reduce only if specified
        const reduceOnlyCheckbox = document.getElementById('reduce-only');
        log("[aster_content] DEBUG: createOrder - reduceOnlyCheckbox:", reduceOnlyCheckbox);
        if (reduceOnlyCheckbox) {
            const isCurrentlyChecked = reduceOnlyCheckbox.getAttribute('data-state') === 'checked';
            log("[aster_content] DEBUG: createOrder - isCurrentlyChecked:", isCurrentlyChecked);
            
            // Click the checkbox if it's not in the desired state
            if (orderData.reduceOnly !== isCurrentlyChecked) {
                log(`[aster_content] setting reduce only to ${orderData.reduceOnly}`);
                reduceOnlyCheckbox.click();
                // Wait for the UI to update
                await new Promise((resolve) => setTimeout(resolve, 100));
            } else {
                log(`[aster_content] reduce only already set to ${orderData.reduceOnly}, no action needed`);
            }
        }

        // 6. Place the order
        const placeOrderButton = document.querySelector('button[type="submit"]');
        log("[aster_content] DEBUG: createOrder - placeOrderButton:", placeOrderButton);
        if (placeOrderButton) {
            log("[aster_content] placing order");
            placeOrderButton.click();
            // Wait for the order to be placed and UI to update
            await new Promise((resolve) => setTimeout(resolve, 300));

            // Clear inputs and trigger events to update UI
            if (amountInput) {
                amountInput.value = "";
                amountInput.dispatchEvent(new Event("input", { bubbles: true }));
            }
            if (limitPriceInput) {
                limitPriceInput.value = "";
                limitPriceInput.dispatchEvent(new Event("input", { bubbles: true }));
            }

            return { success: true };
        } else {
            log("[aster_content] DEBUG: createOrder - place order button not found");
            throw new Error("place order button not found");
        }
    } catch (error) {
        logError("[aster_content] DEBUG: createOrder - error creating limit order:", error);
        return { success: false, error: error.message };
    }
}

// Function to check if the symbol matches the current trading pair
function checkSymbol(symbol) {
    try {
        log("[aster_content] checking symbol:", symbol);
        log("[aster_content] DEBUG: checkSymbol - input symbol:", symbol);

        // Get the current URL path
        const path = window.location.pathname;
        log("[aster_content] current path:", path);
        log("[aster_content] DEBUG: checkSymbol - current path:", path);

        // Extract the trading pair from the URL (e.g., /en/futures/v1/BTCUSDT -> BTCUSDT)
        const urlSymbol = path.split("/").pop().toUpperCase();
        log("[aster_content] url symbol:", urlSymbol);
        log("[aster_content] DEBUG: checkSymbol - url symbol:", urlSymbol);

        // Check if the requested symbol matches the URL symbol
        const matches = symbol.toUpperCase() === urlSymbol;
        log("[aster_content] symbol match:", matches);
        log("[aster_content] DEBUG: checkSymbol - symbol match:", matches);

        return matches;
    } catch (error) {
        logError("[aster_content] error checking symbol:", error);
        return false;
    }
}

// Function to get the last price from the Index Price element
async function getLastPrice(symbol) {
    try {
        log("[aster_content] getting last price for symbol:", symbol);
        log("[aster_content] DEBUG: getLastPrice - input symbol:", symbol);

        // Check if the symbol matches the current trading pair
        if (!checkSymbol(symbol)) {
            logError("[aster_content] symbol mismatch");
            return null;
        }

        // Find the div containing "Mark" text
        const markDiv = Array.from(document.querySelectorAll("div")).find(div => 
            div.textContent.trim() === "Mark"
        );
        log("[aster_content] DEBUG: getLastPrice - markDiv:", markDiv);

        if (!markDiv) {
            logError("[aster_content] mark price div not found");
            return null;
        }

        // Get the parent dl element that contains all price information
        const dlContainer = markDiv.closest("dl");
        log("[aster_content] DEBUG: getLastPrice - dlContainer:", dlContainer);

        if (!dlContainer) {
            logError("[aster_content] dl container not found");
            return null;
        }

        // Get the parent div that contains the dl
        const divContainer = dlContainer.closest("div");
        log("[aster_content] DEBUG: getLastPrice - divContainer:", divContainer);

        if (!divContainer) {
            logError("[aster_content] div container not found");
            return null;
        }

        // Find the div before the dl container that contains the last price
        const lastPriceDiv = Array.from(divContainer.parentElement.querySelectorAll("div")).find(div => {
            const nextDiv = div.nextElementSibling;
            return nextDiv && nextDiv.contains(dlContainer);
        });
        log("[aster_content] DEBUG: getLastPrice - lastPriceDiv:", lastPriceDiv);

        if (!lastPriceDiv) {
            logError("[aster_content] last price div not found");
            return null;
        }

        // Get the price text from the span inside the div
        const priceSpan = lastPriceDiv.querySelector("span");
        log("[aster_content] DEBUG: getLastPrice - priceSpan:", priceSpan);

        if (!priceSpan) {
            logError("[aster_content] price span not found");
            return null;
        }

        // Extract the price value (remove any commas)
        const priceText = priceSpan.textContent.trim();
        log("[aster_content] DEBUG: getLastPrice - priceText:", priceText);
        const price = parseFloat(priceText.replace(/,/g, ""));
        log("[aster_content] DEBUG: getLastPrice - parsed price:", price);

        if (isNaN(price)) {
            logError("[aster_content] invalid price value:", priceText);
            return null;
        }

        log("[aster_content] last price:", price);
        return { lastPrice: price, spread: 0 };
    } catch (error) {
        logError("[aster_content] error getting last price:", error);
        return null;
    }
}

// Function to get the user ID from the Lighter platform
async function getUserId() {
    try {
        log("[aster_content] getting user id...");
        log("[aster_content] DEBUG: getUserId - starting...");

        // Find the user menu button
        const userMenuButton = document.querySelector('[data-testid="user-menu-button"]');
        log("[aster_content] DEBUG: getUserId - userMenuButton:", userMenuButton);
        if (!userMenuButton) {
            logError("[aster_content] user menu button not found");
            throw new Error("user menu button not found");
        }

        // Get the user ID from the button's data attribute
        const userId = userMenuButton.getAttribute('data-user-id');
        log("[aster_content] DEBUG: getUserId - userId:", userId);
        if (!userId) {
            logError("[aster_content] user ID not found");
            throw new Error("user ID not found");
        }

        log("[aster_content] user ID:", userId);
        return userId;
    } catch (error) {
        logError("[aster_content] error getting user ID:", error);
        throw error;
    }
}

// Function to get user margin
async function getUserMargin() {
    try {
        log("[aster_content] DEBUG: starting getUserMargin...");

        // Find the div containing "Account Equity" text
        const accountEquityDiv = Array.from(document.querySelectorAll("div")).find(div => 
            div.textContent.trim() === "Account equity"
        );
        log("[aster_content] DEBUG: accountEquityDiv:", accountEquityDiv);

        if (!accountEquityDiv) {
            logError("[aster_content] account equity div not found");
            return {
                totalMargin: 0,
                availableMargin: 0,
            };
        }

        // Get the wrapper div
        const equityWrapper = accountEquityDiv.parentElement;
        log("[aster_content] DEBUG: equityWrapper:", equityWrapper);

        if (!equityWrapper) {
            logError("[aster_content] equity wrapper not found");
            return {
                totalMargin: 0,
                availableMargin: 0,
            };
        }

        // Find the value element (should be the last div in the wrapper)
        const equityValueDiv = equityWrapper.lastElementChild;
        log("[aster_content] DEBUG: equityValueDiv:", equityValueDiv);

        if (!equityValueDiv) {
            logError("[aster_content] equity value div not found");
            return {
                totalMargin: 0,
                availableMargin: 0,
            };
        }

        // Extract the total margin (Account Equity)
        const totalMarginText = equityValueDiv.textContent.trim();
        log("[aster_content] DEBUG: totalMarginText:", totalMarginText);
        const totalMargin = parseFloat(totalMarginText.replace("USD", "").replace(/,/g, ""));
        log("[aster_content] DEBUG: parsed totalMargin:", totalMargin);

        // Find the div containing "Unrealized PNL" text
        const unrealizedPnlDiv = Array.from(document.querySelectorAll("div")).find(div => 
            div.textContent.trim() === "Unrealized PNL"
        );
        log("[aster_content] DEBUG: unrealizedPnlDiv:", unrealizedPnlDiv);

        let unrealizedPnl = 0;
        if (unrealizedPnlDiv) {
            // Get the parent div that contains both the label and value
            const pnlContainer = unrealizedPnlDiv.closest("div");
            log("[aster_content] DEBUG: pnlContainer:", pnlContainer);

            if (pnlContainer) {
                // Get the wrapper div
                const pnlWrapper = pnlContainer.parentElement;
                log("[aster_content] DEBUG: pnlWrapper:", pnlWrapper);

                if (pnlWrapper) {
                    // Find the value element (should be the last div in the wrapper)
                    const pnlValueDiv = pnlWrapper.lastElementChild;
                    log("[aster_content] DEBUG: pnlValueDiv:", pnlValueDiv);

                    if (pnlValueDiv) {
                        // Extract the unrealized PNL value
                        const pnlText = pnlValueDiv.textContent.trim();
                        log("[aster_content] DEBUG: pnlText:", pnlText);
                        unrealizedPnl = parseFloat(pnlText.replace("USD", "").replace(/,/g, ""));
                        log("[aster_content] DEBUG: parsed unrealizedPnl:", unrealizedPnl);
                    }
                }
            }
        }

        // Find the div containing "Account Maintenance Margin" text
        const maintenanceMarginDiv = Array.from(document.querySelectorAll("div")).find(div => 
            div.textContent.trim() === "Account Maintenance Margin"
        );
        log("[aster_content] DEBUG: maintenanceMarginDiv:", maintenanceMarginDiv);

        let maintenanceMargin = 0;
        if (maintenanceMarginDiv) {
            // Get the parent div that contains both the label and value
            const marginContainer = maintenanceMarginDiv.closest("div");
            log("[aster_content] DEBUG: marginContainer:", marginContainer);

            if (marginContainer) {
                // Get the wrapper div
                const marginWrapper = marginContainer.parentElement;
                log("[aster_content] DEBUG: marginWrapper:", marginWrapper);

                if (marginWrapper) {
                    // Find the value element (should be the last div in the wrapper)
                    const marginValueDiv = marginWrapper.lastElementChild;
                    log("[aster_content] DEBUG: marginValueDiv:", marginValueDiv);

                    if (marginValueDiv) {
                        // Extract the maintenance margin value
                        const marginText = marginValueDiv.textContent.trim();
                        log("[aster_content] DEBUG: marginText:", marginText);
                        maintenanceMargin = parseFloat(marginText.replace("USD", "").replace(/,/g, ""));
                        log("[aster_content] DEBUG: parsed maintenanceMargin:", maintenanceMargin);
                    }
                }
            }
        }

        // Calculate available margin (Account Equity - Maintenance Margin)
        const availableMargin = totalMargin - maintenanceMargin;
        log("[aster_content] DEBUG: calculated availableMargin:", availableMargin);

        log("[aster_content] total margin:", totalMargin, "available margin:", availableMargin, "unrealized pnl:", unrealizedPnl);
        return {
            totalMargin: totalMargin,
            availableMargin: availableMargin,
            unrealizedPnl: unrealizedPnl
        };
    } catch (error) {
        logError("[aster_content] error getting user margin:", error);
        return {
            totalMargin: 0,
            availableMargin: 0,
        };
    }
}

// Listen for messages from the extension
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    log("[aster_content] received message:", request);

    if (request.action === "createOrder") {
        log("[aster_content] create order, start");
        createOrder(request.orderData)
            .then((result) => {
                log("[aster_content] create order, send response:", result);
                sendResponse({
                    success: true,
                });
            })
            .catch((error) => {
                logError("[aster_content] create order, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getLastPrice") {
        log("[aster_content] get last price, start");
        getLastPrice(request.symbol)
            .then((price) => {
                log("[aster_content] get last price, send response:", price);
                sendResponse({
                    success: true,
                    price: price,
                });
            })
            .catch((error) => {
                logError("[aster_content] get last price, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getUserId") {
        log("[aster_content] get user id, start");
        getUserId()
            .then((userId) => {
                log("[aster_content] get user id, send response:", userId);
                sendResponse({
                    success: true,
                    userId: userId,
                });
            })
            .catch((error) => {
                logError("[aster_content] get user id, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getUserMargin") {
        log("[aster_content] get margin, start");
        getUserMargin()
            .then((marginInfo) => {
                log("[aster_content] get margin, send response:", marginInfo);
                sendResponse({
                    success: true,
                    margin: marginInfo,
                });
            })
            .catch((error) => {
                logError("[aster_content] get margin, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getPositions") {
        log("[aster_content] get positions, start");
        extractPositions()
            .then((positions) => {
                log("[aster_content] get positions, send response:", { positions });
                sendResponse({
                    success: true,
                    positions: positions,
                });
            })
            .catch((error) => {
                logError("[aster_content] get positions, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getOpenOrders") {
        log("[aster_content] get open orders, start");
        extractOpenOrders().then((orders) => {
            log("[aster_content] get open orders, send response:", { orders });
            sendResponse({
                success: true,
                orders: orders,
            });
        })
        .catch((error) => {
            logError("[aster_content] get open orders, error:", error);
            sendResponse({
                success: false,
                error: error.message,
            });
        });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getServiceUnavailable") {
        log("[aster_content] get service unavailable, start");
        extractServiceUnavailable().then((isUnavailable) => {
            log("[aster_content] get service unavailable, send response:", isUnavailable);
            sendResponse({
                success: true,
                isUnavailable: isUnavailable,
            });
        })
        .catch((error) => {
            logError("[aster_content] get service unavailable, error:", error);
            sendResponse({
                success: false,
                error: error.message,
            });
        });
        return true; // Keep the message channel open for async response
    } else if (request.action === "closeAllOpenOrders") {
        log("[aster_content] close all open orders, start");
        closeAllOpenOrders()
            .then((count) => {
                log("[aster_content] close all open orders, send response:", count);
                sendResponse({
                    success: true,
                    count: count,
                });
            })
            .catch((error) => {
                logError("[aster_content] close all open orders, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getOpenOrdersCount") {
        log("[aster_content] get open orders count, start");
        try {
            const { count } = getOpenOrdersButton();
            log("[aster_content] get open orders count, send response:", count);
            sendResponse({
                success: true,
                count: count,
            });
        } catch (error) {
            logError("[aster_content] get open orders count, error:", error);
            sendResponse({
                success: false,
                error: error.message,
            });
        }
        return true; // Keep the message channel open for async response
    }
});
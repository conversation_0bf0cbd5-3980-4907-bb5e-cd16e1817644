reduce only not checked:

<form data-gtm-form-interact-id="0"><div class="w-full pb-4 md:pb-0"><div dir="ltr" data-orientation="horizontal" name="orderType" class="flex justify-between items-center mr-4"><div role="tablist" aria-orientation="horizontal" class="h-10 rounded-md bg-muted text-muted-foreground flex ml-2 justify-start items-center p-0" id="tour-guide-order-type-switch" tabindex="0" data-orientation="horizontal" style="outline: none;"><button type="button" role="tab" aria-selected="true" aria-controls="radix-«r6j»-content-MARKET" data-state="active" id="radix-«r6j»-trigger-MARKET" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm py-1.5 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground text-subtitle1 px-2 cursor-pointer hover:text-t-primary font-normal text-t-primary" tabindex="0" data-orientation="horizontal" data-radix-collection-item="">Market</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«r6j»-content-LIMIT" data-state="inactive" id="radix-«r6j»-trigger-LIMIT" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm py-1.5 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground text-subtitle1 px-2 cursor-pointer hover:text-t-primary font-normal text-t-third" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Limit</button><div class="px-2 py-1.5 flex group"><button type="button" role="tab" aria-selected="false" aria-controls="radix-«r6j»-content-STOP" data-state="inactive" id="radix-«r6j»-trigger-STOP" class="justify-center whitespace-nowrap rounded-sm ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground flex items-center max-w-36 p-0 text-subtitle1 cursor-pointer overflow-hidden group-hover:text-t-primary font-normal text-t-third" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Stop Limit</button><button type="button" role="combobox" aria-controls="radix-«r6n»" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="group flex items-center justify-between border border-input bg-transparent text-body2 ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 border-none rounded-none w-auto focus:ring-offset-0 py-0 px-0 h-auto focus:ring-0 group font-normal"><span class="sr-only">Toggle order type menu</span><svg width="1em" height="1em" fill="currentColor" viewBox="0 0 24 24" class="ml-0.5 group-data-[state=open]:rotate-x-180 text-t-third w-3.5 h-3.5 group-data-[state=open]:text-t-primary group-hover:text-t-primary" aria-hidden="true" style="transition: transform 300ms ease-in-out;"><path d="M17.879 7.707a1 1 0 111.414 1.414l-6.586 6.586a1 1 0 01-1.414 0L4.707 9.121a1 1 0 111.415-1.414L12 13.586l5.879-5.879z"></path></svg></button><select aria-hidden="true" tabindex="-1" style="position: absolute; border: 0px; width: 1px; height: 1px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; overflow-wrap: normal;"><option value="STOP" selected="">Stop Limit</option><option value="STOP_MARKET">Stop Market</option><option value="TRAILING_STOP_MARKET">Trailing Stop</option><option value="POST_ONLY">Post Only</option></select></div></div><span type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«r6o»" data-state="closed" class="cursor-pointer"><svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-[14px] h-[14px] text-t-third cursor-pointer"><path fill-rule="evenodd" clip-rule="evenodd" d="M11 6.5C11 9.26142 8.76142 11.5 6 11.5C3.23858 11.5 1 9.26142 1 6.5C1 3.73858 3.23858 1.5 6 1.5C8.76142 1.5 11 3.73858 11 6.5ZM12 6.5C12 9.81371 9.31371 12.5 6 12.5C2.68629 12.5 0 9.81371 0 6.5C0 3.18629 2.68629 0.5 6 0.5C9.31371 0.5 12 3.18629 12 6.5ZM5.50002 4.53125V3.78125H6.50002V4.53125H5.50002ZM5.50002 9.21875V5.5739H6.50002L6.50002 9.21875H5.50002Z" fill="currentColor"></path></svg></span></div><div dir="ltr" data-orientation="horizontal" name="orderDir"><div role="tablist" aria-orientation="horizontal" class="items-center justify-center text-muted-foreground flex p-[1px] mx-4 mt-4 text-body2 bg-background-bg1 text-center leading-8 rounded-none h-auto" tabindex="0" data-orientation="horizontal" style="outline: none;"><button type="button" role="tab" aria-selected="true" aria-controls="radix-«r6p»-content-BUY" data-state="active" id="radix-«r6p»-trigger-BUY" class="inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground flex-1 cursor-pointer rounded-none text-t-buy bg-interactive-buy bg-opacity-20" tabindex="0" data-orientation="horizontal" data-radix-collection-item="">Buy</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«r6p»-content-SELL" data-state="inactive" id="radix-«r6p»-trigger-SELL" class="inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground flex-1 cursor-pointer rounded-none text-t-primary" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Sell</button></div></div><div class="w-full px-4 mt-4"><div class="flex items-center justify-between text-body2 h-6"><div><button type="button" class="flex gap-1 items-center"><p class="text-t-third flex gap-1">Avbl<span class="text-t-primary">199.67 USDT</span></p><svg aria-hidden="true" focusable="false" data-prefix="fasl" data-icon="circle-plus" class="svg-inline--fa fa-circle-plus text-interactive-primary w-3 h-3" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M256 32a224 224 0 1 1 0 448 224 224 0 1 1 0-448zm0 480A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm16-352l-32 0 0 16 0 64-64 0-16 0 0 32 16 0 64 0 0 64 0 16 32 0 0-16 0-64 64 0 16 0 0-32-16 0-64 0 0-64 0-16z"></path></svg><span class="sr-only">Deposit</span></button></div><div class="flex items-center justify-between text-t-link text-subtitle2"><p class="cursor-pointer hover:text-t-linkHover">Cross</p><p class="mx-2 h-3 border-border-line border-l"></p><p class="cursor-pointer hover:text-t-linkHover">20x</p></div></div></div><div class="w-full px-4 mt-2"><div class="w-full flex items-center justify-between w-full border border-border-line text-body2 mt-2 px-2" id="tour-guide-place-order" data-state="closed"><label for="form_qty_input" class="text-t-third cursor-text">Size</label><input class="flex w-full border-none autofill:bg-interactive-interactive01 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:bg-interactive-interactive01 disabled:cursor-not-allowed disabled:opacity-50 rounded bg-interactive-interactive02 focus-visible:ring-0 ring-0 focus-visible:ring-offset-0 placeholder:text-t-placeholder input-text-field h-10 py-2 px-4 flex-1 pl-4 pr-0 text-right !bg-transparent text-body2 border-transparent" id="form_qty_input" autocomplete="off" inputmode="decimal" type="text" value="" name="orderSize" data-gtm-form-interact-field-id="0"><button type="button" role="combobox" aria-controls="radix-«r70»" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="group flex h-10 items-center justify-between border border-input bg-transparent text-body2 ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 border-none rounded-none w-auto focus:ring-offset-0 focus:ring-0 p-0 pl-3 group">BTC<svg xmlns="http://www.w3.org/2000/svg" width="10" height="6" viewBox="0 0 10 6" fill="none" class="h-4 ml-0.5 group-data-[state=open]:rotate-x-180 group-data-[state=open]:rotate-x-180 text-t-third w-2.5 group-data-[state=open]:text-t-primary" aria-hidden="true" style="transition: transform 300ms ease-in-out;"><path d="M5 5.5L10 0.5L0 0.5L5 5.5Z" fill="currentColor"></path></svg></button><select aria-hidden="true" tabindex="-1" name="orderUnit" style="position: absolute; border: 0px; width: 1px; height: 1px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; overflow-wrap: normal;"><option value="usdtFuturesUSDT">USDT</option><option value="usdtFuturesCoin" selected="">BTC</option></select></div></div><div class="relative w-full h-14 min-h-14 mb-1 px-4 pt-10"><span dir="ltr" data-orientation="horizontal" aria-disabled="false" class="relative flex w-full touch-none select-none items-center" style="--radix-slider-thumb-transform: translateX(-50%);"><span data-orientation="horizontal" class="relative h-1 w-full grow overflow-hidden rounded-full cursor-pointer bg-border-line"><span data-orientation="horizontal" class="slider-range absolute h-full bg-interactive-primaryAccent01" style="left: 0%; right: 100%;"></span></span><button value="0" class="absolute cursor-pointer -translate-y-1/2 top-[2px] w-1 h-1 rounded-none bg-interactive-primaryActive dark:bg-t-onColor" type="button" style="left: 0px;"></button><button value="25" class="absolute cursor-pointer -translate-y-1/2 top-[2px] w-1 h-1 rounded-none bg-interactive-primary" type="button" style="left: calc(25% - 2px);"></button><button value="50" class="absolute cursor-pointer -translate-y-1/2 top-[2px] w-1 h-1 rounded-none bg-interactive-primary" type="button" style="left: calc(50% - 2px);"></button><button value="75" class="absolute cursor-pointer -translate-y-1/2 top-[2px] w-1 h-1 rounded-none bg-interactive-primary" type="button" style="left: calc(75% - 2px);"></button><button value="100" class="absolute cursor-pointer -translate-y-1/2 top-[2px] w-1 h-1 rounded-none bg-interactive-primary" type="button" style="left: calc(100% - 4px);"></button><span style="transform: var(--radix-slider-thumb-transform); position: absolute; left: calc(0% + 4px);"><span role="slider" aria-valuemin="0" aria-valuemax="100" aria-orientation="horizontal" data-orientation="horizontal" tabindex="0" class="block cursor-pointer box-content border-0 bg-border-lineStrong dark:bg-t-onColor ring-offset-background transition-colors disabled:pointer-events-none disabled:opacity-50 border-interactive-primary focus-within:outline-none rounded-none w-2 h-[0.625rem]" data-radix-collection-item="" aria-valuenow="0" style=""></span><input value="0" style="display: none;"></span></span><div class="text-t-third text-caption1 flex items-center justify-between pt-2"><div class="flex flex-row"><div class="cursor-pointer">0%</div><div>=$0</div></div><div class="cursor-pointer">Max<span class="text-t-primary pl-2">0.045</span></div></div></div><div class="flex items-center justify-between mt-8 mx-4 h-[18px]"><div class="cursor-pointer flex items-center" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«r71»" data-state="closed"><button type="button" role="checkbox" aria-checked="false" data-state="unchecked" value="false" class="checkbox peer h-4 w-4 shrink-0 border border-border-line ring-offset-background focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 hover:border-t-primary data-[state=checked]:hover:enabled:border-t-primary data-[state=checked]:hover:enabled:bg-t-primary hover:text-t-primary bg-transparent rounded-none data-[state=checked]:text-transparent data-[state=checked]:bg-t-third data-[state=checked]:shadow-[inset_0_0_0_2px_#151515] data-[state=checked]:border-border-line" id="reduce-only"></button><input aria-hidden="true" tabindex="-1" type="checkbox" value="false" name="reduceOnly" style="transform: translateX(-100%); position: absolute; pointer-events: none; opacity: 0; margin: 0px; width: 16px; height: 16px;"><label for="reduce-only" class="text-body2 leading-[18px] pl-2 peer-disabled:cursor-not-allowed cursor-pointer select-none peer-disabled:opacity-70">Reduce-Only</label></div></div><div class="mt-2.5 mx-4"><div class="flex items-center justify-between h-[18px]"><div class="cursor-pointer flex items-center" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«r72»" data-state="closed"><button type="button" role="checkbox" aria-checked="false" data-state="unchecked" value="false" class="checkbox peer h-4 w-4 shrink-0 border border-border-line ring-offset-background focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 hover:border-t-primary data-[state=checked]:hover:enabled:border-t-primary data-[state=checked]:hover:enabled:bg-t-primary hover:text-t-primary bg-transparent rounded-none data-[state=checked]:text-transparent data-[state=checked]:bg-t-third data-[state=checked]:shadow-[inset_0_0_0_2px_#151515] data-[state=checked]:border-border-line" id="tpsl"></button><input aria-hidden="true" tabindex="-1" type="checkbox" value="false" name="showTpSl" style="transform: translateX(-100%); position: absolute; pointer-events: none; opacity: 0; margin: 0px; width: 16px; height: 16px;"><label for="tpsl" class="text-body2 leading-[18px] pl-2 peer-disabled:cursor-not-allowed cursor-pointer select-none peer-disabled:opacity-70">TP/SL</label></div></div></div><div class="mt-4 px-4 min-h-9"><button class="button text-subtitle1 inline-flex items-center justify-center rounded focus-visible:outline-none disabled:cursor-not-allowed button disabled:bg-none hover:bg-none disabled:bg-primary-gradient disabled:text-t-disabled disabled:opacity-[0.7] [&amp;_.inactive]:opacity-[0.7] py-2 px-4 w-full h-[2.25rem] text-t-inverse font-normal bg-interactive-buy hover:bg-interactive-buyHover active:bg-interactive-buyActive" type="submit">Buy/Long</button></div><div class="my-4 mx-4 text-body2"><div class="flex justify-between items-center text-caption1"><div class="text-t-third">Margin</div><div>--</div></div><div class="flex justify-between items-center text-caption1 mt-2"><div class="text-t-third">Est.Liq.Price</div><div>--</div></div><div class="mt-1.5 md:mt-2 flex justify-between items-center text-caption1"><div class="text-t-third">Fee</div><span type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«r73»" data-state="closed" class="cursor-pointer">0.0350% / 0.0100%</span></div></div></div></form>

reduce only checked:

<div class="cursor-pointer flex items-center" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«r71»" data-state="closed"><button type="button" role="checkbox" aria-checked="true" data-state="checked" value="true" class="checkbox peer h-4 w-4 shrink-0 border border-border-line ring-offset-background focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 hover:border-t-primary data-[state=checked]:hover:enabled:border-t-primary data-[state=checked]:hover:enabled:bg-t-primary hover:text-t-primary bg-transparent rounded-none data-[state=checked]:text-transparent data-[state=checked]:bg-t-third data-[state=checked]:shadow-[inset_0_0_0_2px_#151515] data-[state=checked]:border-border-line" id="reduce-only"><span data-state="checked" class="flex items-center justify-center text-current" style="pointer-events: none;"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4"><polyline points="20 6 9 17 4 12"></polyline></svg></span></button><input aria-hidden="true" tabindex="-1" type="checkbox" value="true" name="reduceOnly" style="transform: translateX(-100%); position: absolute; pointer-events: none; opacity: 0; margin: 0px; width: 16px; height: 16px;" checked=""><label for="reduce-only" class="text-body2 leading-[18px] pl-2 peer-disabled:cursor-not-allowed cursor-pointer select-none peer-disabled:opacity-70">Reduce-Only</label></div>


<div class="h-full rounded-lg relative"><div dir="ltr"
        data-orientation="horizontal"
        class="flex flex-col min-h-0 h-full relative"><div
            class="flex justify-between border-border-line border-b border-solid"><div
                role="tablist" aria-orientation="horizontal"
                class="items-center bg-muted text-muted-foreground gap-6 p-0 w-full rounded-none h-auto flex justify-start px-4"
                tabindex="0" data-orientation="horizontal"
                style="outline: none;"><button type="button" role="tab"
                    aria-selected="false"
                    aria-controls="radix-«rbs»-content-positions"
                    data-state="inactive" id="tour-guide-close-position"
                    class="inline-flex items-center justify-center whitespace-nowrap py-1.5 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=inactive]:hover:text-t-primary px-0 h-10 data-[state=inactive]:text-t-third data-[state=active]:text-t-primary text-subtitle2 font-normal border-b-2 border-b-transparent rounded-none"
                    tabindex="-1" data-orientation="horizontal"
                    data-radix-collection-item>Positions(1)</button><button
                    type="button" role="tab" aria-selected="true"
                    aria-controls="radix-«rbs»-content-openOrders"
                    data-state="active" id="radix-«rbs»-trigger-openOrders"
                    class="inline-flex items-center justify-center whitespace-nowrap py-1.5 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=inactive]:hover:text-t-primary px-0 h-10 data-[state=inactive]:text-t-third data-[state=active]:text-t-primary text-subtitle2 font-normal border-b-2 border-b-transparent rounded-none"
                    tabindex="0" data-orientation="horizontal"
                    data-radix-collection-item>Open Orders(1)</button><button
                    type="button" role="tab" aria-selected="false"
                    aria-controls="radix-«rbs»-content-orderHistory"
                    data-state="inactive" id="radix-«rbs»-trigger-orderHistory"
                    class="inline-flex items-center justify-center whitespace-nowrap py-1.5 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=inactive]:hover:text-t-primary px-0 h-10 data-[state=inactive]:text-t-third data-[state=active]:text-t-primary text-subtitle2 font-normal border-b-2 border-b-transparent rounded-none"
                    tabindex="-1" data-orientation="horizontal"
                    data-radix-collection-item>Order History</button><button
                    type="button" role="tab" aria-selected="false"
                    aria-controls="radix-«rbs»-content-tradeHistory"
                    data-state="inactive" id="radix-«rbs»-trigger-tradeHistory"
                    class="inline-flex items-center justify-center whitespace-nowrap py-1.5 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=inactive]:hover:text-t-primary px-0 h-10 data-[state=inactive]:text-t-third data-[state=active]:text-t-primary text-subtitle2 font-normal border-b-2 border-b-transparent rounded-none"
                    tabindex="-1" data-orientation="horizontal"
                    data-radix-collection-item>Trade History</button><button
                    type="button" role="tab" aria-selected="false"
                    aria-controls="radix-«rbs»-content-transactionHistory"
                    data-state="inactive"
                    id="radix-«rbs»-trigger-transactionHistory"
                    class="inline-flex items-center justify-center whitespace-nowrap py-1.5 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=inactive]:hover:text-t-primary px-0 h-10 data-[state=inactive]:text-t-third data-[state=active]:text-t-primary text-subtitle2 font-normal border-b-2 border-b-transparent rounded-none"
                    tabindex="-1" data-orientation="horizontal"
                    data-radix-collection-item>Transaction
                    History</button></div><div
                id="userinfo_filter_condition_container"
                class="absolute right-0 top-0 bg-interactive-interactiveBg px-4 after:pointer-events-none after:content-[''] after:absolute after:left-[-16px] after:top-0 after:h-full after:w-4 after:bg-gradient-to-l after:from-interactive-interactiveBg"><div
                    class="flex items-center h-10 gap-2 text-caption text-t-third "><div
                        class="group flex items-center gap-2 text-body2 leading-[18px] text-t-third"><button
                            type="button" role="checkbox" aria-checked="false"
                            data-state="unchecked" value="hideOtherPair"
                            class="checkbox peer h-4 w-4 shrink-0 border border-border-line ring-offset-background focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 hover:border-t-primary data-[state=checked]:hover:enabled:border-t-primary data-[state=checked]:hover:enabled:bg-t-primary hover:text-t-primary bg-transparent rounded-none data-[state=checked]:text-transparent data-[state=checked]:bg-t-third data-[state=checked]:shadow-[inset_0_0_0_2px_#151515] data-[state=checked]:border-border-line"
                            id="hideOtherPair"></button><label
                            for="hideOtherPair"
                            class="cursor-pointer text-nowrap group-hover:text-t-primary">Hide
                            Other Symbols</label></div></div></div></div><div
            data-state="inactive" data-orientation="horizontal" role="tabpanel"
            aria-labelledby="radix-«rbs»-trigger-positions"
            id="radix-«rbs»-content-positions" tabindex="0"
            class="ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 mt-0 h-[calc(100%-48px)] sm:max-h-[400px] md:max-h-none"
            style hidden></div><div data-state="active"
            data-orientation="horizontal" role="tabpanel"
            aria-labelledby="radix-«rbs»-trigger-openOrders"
            id="radix-«rbs»-content-openOrders" tabindex="0"
            class="ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 mt-0 h-[calc(100%-48px)] sm:max-h-[400px] md:max-h-none"><div
                class="overflow-auto md:overscroll-contain relative h-full min-h-20 sm:w-screen md:w-auto grid grid-cols-[repeat(10,minmax(max-content,1fr))] gap-x-6 auto-rows-max"
                style="content-visibility: auto;"><div
                    class="list-header bg-interactive-interactiveBg top-0 z-[20] w-full h-8 flex-row sticky items-center box-border text-caption1 pl-4 justify-between grid grid-cols-subgrid col-span-10"><div
                        data-id="time"
                        class="header-cell flex items-center text-nowrap text-t-third">Time</div><div
                        data-id="symbol"
                        class="header-cell flex items-center text-nowrap text-t-third">Symbol</div><div
                        data-id="type"
                        class="header-cell flex items-center text-nowrap text-t-third">Type</div><div
                        data-id="price"
                        class="header-cell flex items-center text-nowrap text-t-third">Price</div><div
                        data-id="amountAndTotal"
                        class="header-cell flex items-center text-nowrap text-t-third">Filled/Amount</div><div
                        data-id="triggerConditions"
                        class="header-cell flex items-center text-nowrap text-t-third">Trigger
                        Conditions</div><div data-id="reduceOnly"
                        class="header-cell flex items-center text-nowrap text-t-third">Reduce
                        Only</div><div data-id="postOnly"
                        class="header-cell flex items-center text-nowrap text-t-third">Post
                        Only</div><div data-id="tpsl"
                        class="header-cell flex items-center text-nowrap text-t-third">TP/SL</div><div
                        data-id="action"
                        class="header-cell flex items-center sticky bg-interactive-interactiveBg !pl-0 right-0 h-8 after:pointer-events-none after:content-[''] after:absolute after:left-[-16px] after:h-8 after:w-4 after:bg-gradient-to-l after:from-interactive-interactiveBg text-nowrap text-t-third"><div
                            class="flex justify-end w-full"><button
                                type="button"
                                class="text-t-link hover:text-t-linkHover text-body2 sm:px-4 cursor-pointer h-4 sm:h-8 sm:text-subtitle3">Cancel
                                All</button></div></div></div><div
                    class="list-body list-container relative min-h-20 grid grid-cols-subgrid col-span-10 auto-rows-max"><div
                        data-id="0" data-state="false"
                        class="list-row w-full items-center relative hover:bg-interactive-interactive01 [&amp;_.row-cell.sticky]:hover:bg-interactive-interactive01 [&amp;:hover_.row-cell.sticky:after]:from-interactive-interactive01 h-12 content-center pl-4 grid grid-cols-subgrid col-span-10 auto-rows-max"><div
                            data-id="0_time"
                            class="row-cell flex-row flex text-t-primary items-center text-body2 leading-[18px]"><div
                                class="flex flex-row sm:flex-col gap-1 sm:gap-0"><p>2025-04-20</p><p>14:15:50</p></div></div><div
                            data-id="0_symbol"
                            class="row-cell flex-row flex text-t-primary items-center text-body2 leading-[18px]"><div
                                class="flex flex-row sm:flex-col gap-1 sm:gap-0"><div>BTCUSDT</div><div
                                    class="flex gap-1 text-t-buy"><div>Buy</div></div></div></div><div
                            data-id="0_type"
                            class="row-cell flex-row flex text-t-primary items-center text-body2 leading-[18px]"><div
                                class="flex flex-col"><p>Limit</p></div></div><div
                            data-id="0_price"
                            class="row-cell flex-row flex text-t-primary items-center text-body2 leading-[18px]"><div>80,000.0</div></div><div
                            data-id="0_amountAndTotal"
                            class="row-cell flex-row flex text-t-primary items-center text-body2 leading-[18px]"><div
                                class="flex flex-row sm:flex-col"><div>0.000
                                    BTC</div><div
                                    class="sm:hidden">/</div><div>0.001
                                    BTC</div></div></div><div
                            data-id="0_triggerConditions"
                            class="row-cell flex-row flex text-t-primary items-center text-body2 leading-[18px]"><div
                                class="flex flex-col"><div><p>–</p></div></div></div><div
                            data-id="0_reduceOnly"
                            class="row-cell flex-row flex text-t-primary items-center text-body2 leading-[18px]"><div
                                class="flex flex-col"><p>No</p></div></div><div
                            data-id="0_postOnly"
                            class="row-cell flex-row flex text-t-primary items-center text-body2 leading-[18px]"><div
                                class="flex flex-col"><p>No</p></div></div><div
                            data-id="0_tpsl"
                            class="row-cell flex-row flex text-t-primary items-center text-body2 leading-[18px]"><div
                                class="strategy">--</div></div><div
                            data-id="0_action"
                            class="row-cell flex-row flex text-t-primary items-center text-body2 leading-[18px] sticky bg-interactive-interactiveBg !pl-0 right-0 h-full after:pointer-events-none after:content-[''] after:absolute after:left-[-16px] after:h-full after:w-4 after:bg-gradient-to-l after:from-interactive-interactiveBg pr-4 md:pr-0"><div
                                class="contents md:flex w-full h-full justify-end pr-0 md:pr-4 items-center min-w-[75px]"><button
                                    class="button inline-flex items-center justify-center rounded focus-visible:outline-none disabled:cursor-not-allowed button disabled:bg-none hover:bg-none disabled:bg-primary-gradient disabled:text-t-disabled disabled:opacity-[0.7] [&amp;_.inactive]:opacity-[0.7] px-4 bg-interactive-interactive01 border border-border-line text-caption1 py-1 h-auto hover:bg-interactive-interactive01Hover active:bg-interactive-interactive01Hover text-center md:h-6 md:bg-interactive-interactiveBg md:border-border-line md:border md:border-solid md:text-caption1 md:font-light md:px-2 md:py-1 md:text-t-primary md:hover:bg-interactive-interactiveBgHover md:hover:text-t-primary md:active:bg-interactive-interactiveBgActive w-full md:w-auto">Cancel</button></div></div></div></div></div></div><div
            data-state="inactive" data-orientation="horizontal" role="tabpanel"
            aria-labelledby="radix-«rbs»-trigger-orderHistory" hidden
            id="radix-«rbs»-content-orderHistory" tabindex="0"
            class="ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 mt-0 h-[calc(100%-48px)] sm:max-h-[400px] md:max-h-none"></div><div
            data-state="inactive" data-orientation="horizontal" role="tabpanel"
            aria-labelledby="radix-«rbs»-trigger-tradeHistory" hidden
            id="radix-«rbs»-content-tradeHistory" tabindex="0"
            class="ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 mt-0 h-[calc(100%-48px)] sm:max-h-[400px] md:max-h-none"></div><div
            data-state="inactive" data-orientation="horizontal" role="tabpanel"
            aria-labelledby="radix-«rbs»-trigger-transactionHistory" hidden
            id="radix-«rbs»-content-transactionHistory" tabindex="0"
            class="ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 mt-0 h-[calc(100%-48px)] sm:max-h-[400px] md:max-h-none"></div></div></div>


            confirmation dialog:

            <div class="sm:border border-border-line sm:shadow-elevation3 sm:py-4 sm:px-6 rounded-none bg-interactive-interactiveBg sm:w-[360px] w-full"><svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mt-2 text-t-sell mx-auto"><path fill-rule="evenodd" clip-rule="evenodd" d="M34 20C34 27.732 27.732 34 20 34C12.268 34 6 27.732 6 20C6 12.268 12.268 6 20 6C27.732 6 34 12.268 34 20ZM35 20C35 28.2843 28.2843 35 20 35C11.7157 35 5 28.2843 5 20C5 11.7157 11.7157 5 20 5C28.2843 5 35 11.7157 35 20ZM25.6569 15.0503L20.7071 20L25.6569 24.9498L24.9497 25.6569L20 20.7071L15.0503 25.6569L14.3431 24.9498L19.2929 20L14.3431 15.0503L15.0503 14.3432L20 19.2929L24.9497 14.3432L25.6569 15.0503Z" fill="currentColor"></path></svg><p class="text-body2 text-t-primary mt-3 text-center">Are you sure you want to cancel all ?</p><div class="flex gap-2.5 items-center mt-6"><button class="button text-subtitle1 inline-flex items-center justify-center rounded focus-visible:outline-none disabled:cursor-not-allowed button border bg-transparent border-interactive-primary text-interactive-primary hover:bg-transparent hover:border-t-white hover:text-t-white h-10 py-2 px-4 flex-1">Cancel</button><button class="button text-subtitle1 inline-flex items-center justify-center rounded focus-visible:outline-none disabled:cursor-not-allowed button bg-primary-gradient hover:bg-interactive-primaryActive disabled:bg-none hover:bg-none active:bg-interactive-primaryActive disabled:bg-primary-gradient disabled:text-t-disabled disabled:opacity-[0.7] [&amp;_.inactive]:opacity-[0.7] h-10 py-2 px-4 flex-1" type="submit">Confirm</button></div></div>
// Aster platform implementation


let symbolPriceDecimals = {
    BTCUSDT: 1,
    ETHUSDT: 1,
};

let symbolAmountDecimals = {
    BTCUSDT: 3,
    ETHUSDT: 3,
};


export class AsterPlatform {
    static async GetName() {
        return "aster";
    }

    static getAmountDigits(symbol) {
        return symbolAmountDecimals[symbol] || 3;
    }

    static getPriceDigits(symbol) {
        return symbolPriceDecimals[symbol] || 1;
    }

    static GetTradeWaitingTime() {
        return 1000*10;
    }

    static CalculateOrderPrice(symbol, side, lastPrice) {
        if (side === "long") {
            return lastPrice * 1.019;
        } else if (side === "short") {
            return lastPrice * 0.981;
        } else {
            throw new Error("invalid side specified. must be 'long' or 'short', got: " + side);
        }
    }

    static async GetUserID() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("asterdex.com")) {
                throw new Error("not on aster platform");
            }

            // Send message to content script to get user ID
            const response = await chrome.tabs.sendMessage(tab.id, { action: "getUserId" });

            if (response && response.userId) {
                return response.userId;
            } else {
                throw new Error("Failed to get user ID");
            }
        } catch (error) {
            console.error("[lighter] error getting user id:", error);
            throw error;
        }
    }

    static async GetPositions() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("asterdex.com")) {
                throw new Error("not on aster platform");
            }

            // Send message to content script to get positions
            const response = await chrome.tabs.sendMessage(tab.id, { action: "getPositions" });

            if (response && response.positions) {
                return response.positions;
            } else {
                throw new Error("failed to get positions");
            }
        } catch (error) {
            console.error("[aster] error getting positions:", error);
            throw error;
        }
    }

    static async GetOpenOrders() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("asterdex.com")) {
                throw new Error("not on aster platform");
            }

            // Send message to content script to get open orders
            const response = await chrome.tabs.sendMessage(tab.id, { action: "getOpenOrders" });

            if (response && response.orders) {
                return response.orders;
            } else {
                throw new Error("failed to get open orders");
            }
        } catch (error) {
            console.error("[aster] error getting open orders:", error);
            throw error;
        }
    }

    // Variable to track if an order is being processed
    static _lastOrderTime = null;

    static async CreateOrder(orderData) {
        console.log("[aster] create order:", orderData);
        // Check if an order is already in progress
        if (AsterPlatform._lastOrderTime && AsterPlatform._lastOrderTime > Date.now() - 1000 * 1) {
            console.log("[aster] order already in progress, ignoring request");
            return { success: false, error: "an order is already in progress" };
        }

        // Set the flag to indicate an order is in progress
        AsterPlatform._lastOrderTime = Date.now();

        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("asterdex.com")) {
                throw new Error("not on aster platform");
            }

            // Send message to content script to create limit order
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: "createOrder",
                orderData: {
                    symbol: orderData.symbol,
                    side: orderData.side,
                    price: orderData.price,
                    size: orderData.size,
                    reduceOnly: orderData.reduceOnly || false,
                },
            });
            console.log("[aster] create limit order response:", response);
            if (response && response.success) {
                return { success: true };
            } else {
                console.error("[aster] failed to create limit order:", response);
                throw new Error(response?.error || "failed to create limit order");
            }
        } catch (error) {
            console.error("[aster] error creating limit order:", error);
            return { success: false, error: error.message };
        } finally {
            // Reset the flag when done
            AsterPlatform._lastOrderTime = null;
        }
    }

    static async GetMargin() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("asterdex.com")) {
                throw new Error("not on aster platform");
            }

            // Send message to content script to get margin
            const response = await chrome.tabs.sendMessage(tab.id, { action: "getUserMargin" });
            console.log("[aster] get margin response:", response);

            if (response && response.success && response.margin) {
                return response.margin;
            } else {
                console.error("[aster] failed to get margin:", response);
                throw new Error("Failed to get margin");
            }
        } catch (error) {
            console.error("[aster] error getting margin:", error);
            throw error;
        }
    }

    static async GetLastPrice(symbol) {
        if (!symbol) {
            throw new Error("Symbol is required");
        }
        try {
            console.log("[aster] get last price for symbol:", symbol);

            // Query the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // Check if we're on the Lighter platform
            if (!tab.url.includes("asterdex.com")) {
                throw new Error("not on aster platform");
            }

            // Send message to content script to get the last price
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: "getLastPrice",
                symbol: symbol,
            });
            console.log("[aster] get last price response:", response);
            if (response && response.success && response.price) {
                console.log("[aster] got last price:", response.price);
                return response.price;
            } else {
                console.error("[aster] failed to get last price:", response);
                throw new Error("failed to get last price");
            }
        } catch (error) {
            console.error("[aster] error in get last price:", error);
            throw error;
        }
    }

    static async GetServiceUnavailable() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("asterdex.com")) {
                throw new Error("not on aster platform");
            }

            // Send message to content script to get service unavailable status
            const response = await chrome.tabs.sendMessage(tab.id, { action: "getServiceUnavailable" });
            console.log("[aster] get service unavailable response:", response);
            if (response && response.success) {
                return response.isUnavailable;
            } else {
                console.error("[aster] failed to get service unavailable status:", response);
                throw new Error("failed to get service unavailable status");
            }
        } catch (error) {
            console.error("[aster] error getting service unavailable status:", error);
            throw error;
        }
    }

    static async CloseAllOpenOrders() {
        try {
            console.log("[aster] closing all open orders...");
            
            // Send message to content script
            const response = await chrome.tabs.query({ active: true, currentWindow: true })
                .then(tabs => {
                    return chrome.tabs.sendMessage(tabs[0].id, { action: "closeAllOpenOrders" });
                });

            if (!response.success) {
                throw new Error(response.error || "Failed to close all open orders");
            }

            console.log("[aster] closed all open orders, count:", response.count);
            return response.count;
        } catch (error) {
            console.error("[aster] error closing all open orders:", error);
            throw error;
        }
    }

    static async GetOpenOrdersCount() {
        try {
            console.log("[aster] getting open orders count...");
            
            // Send message to content script
            const response = await chrome.tabs.query({ active: true, currentWindow: true })
                .then(tabs => {
                    return chrome.tabs.sendMessage(tabs[0].id, { action: "getOpenOrdersCount" });
                });

            if (!response.success) {
                throw new Error(response.error || "Failed to get open orders count");
            }

            console.log("[aster] open orders count:", response.count);
            return response.count;
        } catch (error) {
            console.error("[aster] error getting open orders count:", error);
            throw error;
        }
    }

    static async GetPoints() {
        return 0;
    }

    static async SetTakeProfitStopLoss(symbol, tpPrice, slPrice) {
        return { success: true };
    }

    static async FastWithdraw(address, amount) {
        return { success: true };
    }

    static async GetVersion() {
        return ""
    }

    static async GetVolumeAndProfit() {
        return { volume: 0, profit: 0 };
    }
}

// Lighter platform implementation

let symbolPriceDecimals = {
    BTC: 1,
    ETH: 2,
};


let symbolAmountDecimals = {
    BTC: 5,
    ETH: 4,
};


async function getSettings() {
    return new Promise((resolve) => {
        chrome.storage.sync.get(["platform", "userId", "projectId", "defaultSymbol", "copyFromId", "copyPercentage", "serverUrl", "minMargin", "defaultSize"], (result) => {
            resolve(result);
        });
    });
}


export class LighterPlatform {
    static async GetName() {
        return "lighter";
    }

    static GetTradeWaitingTime() {
        return 1000*10;
    }

    static getAmountDigits(symbol) {
        return symbolAmountDecimals[symbol] || 3;
    }

    static getPriceDigits(symbol) {
        return symbolPriceDecimals[symbol] || 1;
    }

    static CalculateOrderPrice(symbol, side, lastPrice) {
        if (side === "long") {
            return lastPrice * 1.02;
        } else if (side === "short") {
            return lastPrice * 0.98;
        } else {
            throw new Error("invalid side specified. must be 'long' or 'short', got: " + side);
        }
    }

    static async NavigateHome(url) {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab ) {
                return;
            }
            
            if (tab.url.includes("chrome://whats-new/")) {
                // close tab
                await chrome.tabs.remove(tab.id);
            }
            if (url) {
                // open new tab
                await chrome.tabs.create({ url: url });
            } else {
                const settings = await getSettings();
                const newUrl = "https://app.lighter.xyz/trade/" + settings.defaultSymbol;
                if (tab.url !== newUrl) {
                    await chrome.tabs.update(tab.id, { url: newUrl });
                }
            }
            // wait for new tab to load
            await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
            console.error("[lighter] error navigating to home:", error);
            throw error;
        }
    }

    static async GetUserID() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }

            // Send message to content script to get user ID
            const response = await chrome.tabs.sendMessage(tab.id, { action: "getUserId" });

            if (response && response.userId) {
                return response.userId;
            } else {
                throw new Error("Failed to get user ID");
            }
        } catch (error) {
            console.error("[lighter] error getting user id:", error);
            throw error;
        }
    }

    static async GetPositions() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }

            // Send message to content script to get positions
            const response = await chrome.tabs.sendMessage(tab.id, { action: "getPositions" });

            if (response && response.positions) {
                return response.positions;
            } else {
                throw new Error("failed to get positions");
            }
        } catch (error) {
            console.error("[lighter] error getting positions:", error);
            throw error;
        }
    }

    static async GetOpenOrders() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }

            // Send message to content script to get open orders
            const response = await chrome.tabs.sendMessage(tab.id, { action: "getOpenOrders" });

            if (response && response.orders) {
                return response.orders;
            } else {
                throw new Error("failed to get open orders");
            }
        } catch (error) {
            console.error("[lighter] error getting open orders:", error);
            throw error;
        }
    }

    // Variable to track if an order is being processed
    static _lastOrderTime = null;

    static async CreateOrder(orderData) {
        console.log("[lighter] create order:", orderData);
        // Check if an order is already in progress
        if (LighterPlatform._lastOrderTime && LighterPlatform._lastOrderTime > Date.now() - 1000 * 1) {
            console.log("[lighter] order already in progress, ignoring request");
            return { success: false, error: "an order is already in progress" };
        }

        // Set the flag to indicate an order is in progress
        LighterPlatform._lastOrderTime = Date.now();

        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }

            // Send message to content script to create limit order
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: "createOrder",
                orderData: {
                    symbol: orderData.symbol,
                    side: orderData.side,
                    price: orderData.price,
                    size: orderData.size,
                    reduceOnly: orderData.reduceOnly || false,
                },
            });
            console.log("[lighter] create limit order response:", response);
            if (response && response.success) {
                return { success: true };
            } else {
                console.error("[lighter] failed to create limit order:", response);
                throw new Error(response?.error || "failed to create limit order");
            }
        } catch (error) {
            console.error("[lighter] error creating limit order:", error);
            return { success: false, error: error.message };
        } finally {
            // Reset the flag when done
            LighterPlatform._lastOrderTime = null;
        }
    }

    static async CreateMarketOrder(orderData) {
        console.log("[lighter] create market order:", orderData);
        // Check if an order is already in progress
        if (LighterPlatform._lastOrderTime && LighterPlatform._lastOrderTime > Date.now() - 1000 * 1) {
            console.log("[lighter] order already in progress, ignoring request");
            return { success: false, error: "an order is already in progress" };
        }

        // Set the flag to indicate an order is in progress
        LighterPlatform._lastOrderTime = Date.now();

        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }

            // Send message to content script to create market order
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: "createMarketOrder",
                orderData: {
                    symbol: orderData.symbol,
                    side: orderData.side,
                    size: orderData.size,
                    reduceOnly: orderData.reduceOnly || false,
                },
            });
            console.log("[lighter] create market order response:", response);
            if (response && response.success) {
                return { success: true };
            } else {
                console.error("[lighter] failed to create market order:", response);
                throw new Error(response?.error || "failed to create market order");
            }
        } catch (error) {
            console.error("[lighter] error creating market order:", error);
            return { success: false, error: error.message };
        } finally {
            // Reset the flag when done
            LighterPlatform._lastOrderTime = null;
        }
    }

    static async GetMargin() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }

            // Send message to content script to get margin
            const response = await chrome.tabs.sendMessage(tab.id, { action: "getUserMargin" });
            console.log("[lighter] get margin response:", response);

            if (response && response.success && response.margin) {
                return response.margin;
            } else {
                console.error("[lighter] failed to get margin:", response);
                throw new Error("Failed to get margin");
            }
        } catch (error) {
            console.error("[lighter] error getting margin:", error);
            throw error;
        }
    }

    static async GetLastPrice(symbol) {
        if (!symbol) {
            throw new Error("Symbol is required");
        }
        try {
            console.log("[lighter] get last price for symbol:", symbol);

            // Query the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // Check if we're on the Lighter platform
            if (!tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }

            // Send message to content script to get the last price
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: "getLastPrice",
                symbol: symbol,
            });
            console.log("[lighter] get last price response:", response);
            if (response && response.success && response.price) {
                console.log("[lighter] got last price:", response.price);
                return response.price;
            } else {
                console.error("[lighter] failed to get last price:", response);
                throw new Error("failed to get last price");
            }
        } catch (error) {
            console.error("[lighter] error in get last price:", error);
            throw error;
        }
    }

    static async GetServiceUnavailable() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }

            // Send message to content script to get service unavailable status
            const response = await chrome.tabs.sendMessage(tab.id, { action: "getServiceUnavailable" });
            console.log("[lighter] get service unavailable response:", response);
            if (response && response.success) {
                return response.isUnavailable;
            } else {
                console.error("[lighter] failed to get service unavailable status:", response);
                throw new Error("failed to get service unavailable status");
            }
        } catch (error) {
            console.error("[lighter] error getting service unavailable status:", error);
            throw error;
        }
    }

    static async CloseAllOpenOrders() {
        try {
            console.log("[lighter] closing all open orders...");
            
            // Send message to content script
            const response = await chrome.tabs.query({ active: true, currentWindow: true })
                .then(tabs => {
                    return chrome.tabs.sendMessage(tabs[0].id, { action: "closeAllOpenOrders" });
                });

            if (!response.success) {
                throw new Error(response.error || "Failed to close all open orders");
            }

            console.log("[lighter] closed all open orders, count:", response.count);
            return response.count;
        } catch (error) {
            console.error("[lighter] error closing all open orders:", error);
            throw error;
        }
    }

    static async GetOpenOrdersCount() {
        try {
            console.log("[lighter] getting open orders count...");
            
            // Send message to content script
            const response = await chrome.tabs.query({ active: true, currentWindow: true })
                .then(tabs => {
                    return chrome.tabs.sendMessage(tabs[0].id, { action: "getOpenOrdersCount" });
                });

            if (!response.success) {
                throw new Error(response.error || "Failed to get open orders count");
            }

            console.log("[lighter] open orders count:", response.count);
            return response.count;
        } catch (error) {
            console.error("[lighter] error getting open orders count:", error);
            throw error;
        }
    }

    static async GetPoints() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }

            // Send message to content script to get points
            const response = await chrome.tabs.sendMessage(tab.id, { action: "getPoints" });
            console.log("[lighter] get points response:", response);

            if (response && response.success) {
                return response.points;
            } else {
                console.error("[lighter] failed to get points:", response);
                throw new Error("Failed to get points");
            }
        } catch (error) {
            console.error("[lighter] error getting points:", error);
            throw error;
        }
    }

    static async SetTakeProfitStopLoss(symbol, tpPrice, slPrice) {
        try {
            console.log("[lighter] setting take profit and stop loss for symbol:", symbol, "tp:", tpPrice, "sl:", slPrice);

            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }

            // Send message to content script to set take profit and stop loss
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: "setTakeProfitStopLoss",
                symbol: symbol,
                tpPrice: tpPrice,
                slPrice: slPrice
            });
            console.log("[lighter] set take profit stop loss response:", response);

            if (response && response.success) {
                return { success: true };
            } else {
                console.error("[lighter] failed to set take profit and stop loss:", response);
                throw new Error(response?.error || "Failed to set take profit and stop loss");
            }
        } catch (error) {
            console.error("[lighter] error setting take profit and stop loss:", error);
            throw error;
        }
    }

    static async FastWithdraw(address, amount) {
        try {
            console.log("[lighter] fast withdraw, start");

            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }

            // Send message to content script to fast withdraw
            const response = await chrome.tabs.sendMessage(tab.id, { action: "fastWithdraw", address: address, amount: amount });
            console.log("[lighter] fast withdraw response:", response);

            if (response && response.success) {
                return { success: true, amount: response.amount };
            } else {
                console.error("[lighter] failed to fast withdraw:", response);
                throw new Error(response?.error || "Failed to fast withdraw");
            }
        } catch (error) {
            console.error("[lighter] error fast withdrawing:", error);
            throw error;
        }
    }

    static async GetVersion() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }
            
            // Send message to content script to get version
            const response = await chrome.tabs.sendMessage(tab.id, { action: "getVersion" });
            console.log("[lighter] get version response:", response);

            if (response && response.success) {
                return response.version;
            } else {
                console.error("[lighter] failed to get version:", response);
                throw new Error("Failed to get version");
            }
        } catch (error) {
            console.error("[lighter] error getting version:", error);
            throw error;
        }
    }

    static async GetVolumeAndProfit2() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }
            
            // Send message to content script to get stats
            const response = await chrome.tabs.sendMessage(tab.id, { action: "getVolumeAndProfit" });
            console.log("[lighter] get volume and profit response:", response);

            if (response && response.success) {
                return { volume: response.volume, profit: response.profit };
            } else {
                console.error("[lighter] failed to get volume and profit:", response);
                throw new Error("Failed to get volume and profit");
            }
        } catch (error) {
            console.error("[lighter] error getting volume and profit:", error);
            throw error;
        }
    }

    // TODO: get real volume and profit
    static async GetVolumeAndProfit() {
        return { volume: 0, profit: 0 };
    }

    static async NeedReload() {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url.includes("lighter.xyz")) {
                throw new Error("not on lighter platform");
            }

            // Send message to content script to get need reload
            const response = await chrome.tabs.sendMessage(tab.id, { action: "needReload" });
            console.log("[lighter] need reload response:", response);

            if (response && response.success) {
                return response.result;
            } else {
                console.error("[lighter] failed to get need reload:", response);
                throw new Error("Failed to get need reload");
            }
        } catch (error) {
            console.error("[lighter] error getting need reload:", error);
            throw error;
        }
    }

    static GetURLs(symbol) {
        return {
            trade: "https://app.lighter.xyz/trade/"+symbol,
            portfolio: "https://app.lighter.xyz/portfolio",
            points: "https://app.lighter.xyz/leaderboard",
        };
    }

}

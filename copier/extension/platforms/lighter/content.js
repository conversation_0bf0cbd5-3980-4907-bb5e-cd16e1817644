const version = "C051400";

function randomNumber() {
    let min = 1000;
    let max = 9999;
    return Math.floor(Math.random() * (max - min) + min);
}

let refid = randomNumber();

// debug log function
function log(...args) {
    let now = new Date().toISOString();
    console.log(`#${refid} ${now}: `, ...args);
}

function logError(...args) {
    let now = new Date().toISOString();
    console.error(`#${refid} ${now}: `, ...args);
}

// Content script for Lighter platform
log("[lighter_content] ==========================================");
log("[lighter_content] Lighter content script loaded - TEST LOG");
log("[lighter_content] ==========================================");

let symbolPriceDecimals = {
    BTC: 1,
    ETH: 2,
    SOL: 3,
    HYPE: 4,
    BNB: 4,
};

let symbolAmountDecimals = {
    BTC: 5,
    ETH: 4,
    SOL: 3,
    HYPE: 2,
    BNB: 2,
};

// Function to wait for an element to be present in the DOM
function waitForElement(selector, timeout = 100) {
    return new Promise((resolve, reject) => {
        if (document.querySelector(selector)) {
            return resolve(document.querySelector(selector));
        }

        const observer = new MutationObserver((mutations) => {
            if (document.querySelector(selector)) {
                observer.disconnect();
                resolve(document.querySelector(selector));
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
        });

        // Timeout after specified time
        setTimeout(() => {
            observer.disconnect();
            reject(new Error(`timeout waiting for element: ${selector}`));
        }, timeout);
    });
}

async function switchToPositionsTab() {
    try {
        log("[lighter_content] switching to positions tab...");
        
        // Find the Positions button by its text content pattern
        const positionsButton = Array.from(document.querySelectorAll('button')).find(button => 
            button.textContent.trim().startsWith('Positions')
        );
        
        if (!positionsButton) {
            logError("[lighter_content] positions button not found");
            throw new Error("positions button not found");
        }

        // Extract the number of positions from the button text
        const buttonText = positionsButton.textContent.trim();
        const positionsMatch = buttonText.match(/Positions \((\d+)\)/);
        const positionCount = positionsMatch ? parseInt(positionsMatch[1]) : 0;
        log("[lighter_content] found positions count:", positionCount);

        // Click the button
        positionsButton.click();
        log("[lighter_content] clicked positions button");

        // Wait a short moment for the UI to update
        await new Promise(resolve => setTimeout(resolve, 100));
        
        return positionCount;
    } catch (error) {
        logError("[lighter_content] error switching to positions tab:", error);
        throw error;
    }
}

function getOpenOrdersButton() {
    try {
        log("[lighter_content] getting open orders button...");
        
        // Find the Open Orders button by its text content pattern
        const openOrdersButton = Array.from(document.querySelectorAll('button')).find(button => 
            button.textContent.trim().startsWith('Open Orders')
        );
        log("[lighter_content] openOrdersButton:", openOrdersButton);
        if (!openOrdersButton) {
            logError("[lighter_content] open orders button not found");
            throw new Error("open orders button not found");
        }

        // Extract the number of orders from the button text
        const buttonText = openOrdersButton.textContent.trim();
        const ordersMatch = buttonText.match(/Open Orders \((\d+)\)/);
        const orderCount = ordersMatch ? parseInt(ordersMatch[1]) : 0;
        log("[lighter_content] found open orders count:", orderCount);
        
        return {
            button: openOrdersButton,
            count: orderCount
        };
    } catch (error) {
        logError("[lighter_content] error getting open orders button:", error);
        throw error;
    }
}

async function switchToOpenOrdersTab() {
    try {
        log("[lighter_content] switching to open orders tab...");
        
        const { button, count } = getOpenOrdersButton();

        // Click the button
        button.click();
        log("[lighter_content] clicked open orders button");

        // Wait a short moment for the UI to update
        await new Promise(resolve => setTimeout(resolve, 100));
        
        return count;
    } catch (error) {
        logError("[lighter_content] error switching to open orders tab:", error);
        throw error;
    }
}

async function closeAllOpenOrders() {
    try {
        log("[lighter_content] closing all open orders...");

        const { button, count } = getOpenOrdersButton();

        if (count === 0) {
            log("[lighter_content] no open orders found");
            return 0;
        }

        await switchToOpenOrdersTab();
        
        // Find and click the Cancel All button
        const cancelAllButton = Array.from(document.querySelectorAll('button')).find(button => 
            button.textContent.trim() === 'Cancel All'
        );
        
        if (!cancelAllButton) {
            logError("[lighter_content] cancel all button not found");
            throw new Error("cancel all button not found");
        }

        cancelAllButton.click();
        log("[lighter_content] clicked cancel all button");

        // Wait for the confirmation dialog to appear
        await new Promise(resolve => setTimeout(resolve, 100));

        // Find the confirmation dialog by looking for the text about canceling orders
        const confirmationDialogs = document.querySelectorAll('div');
        log("[lighter_content] found confirmation dialogs:", confirmationDialogs);
        const confirmationDialog = Array.from(confirmationDialogs).find(div => 
            div.textContent.includes(`cancel all`) && div.textContent.includes(`open orders`)
        );

        if (!confirmationDialog) {
            logError("[lighter_content] confirmation dialog not found");
            throw new Error("confirmation dialog not found");
        }

        // Find the Confirm button within the dialog
        const confirmButton = Array.from(confirmationDialog.querySelectorAll('button')).find(button => 
            button.textContent.trim() === 'Confirm'
        );

        if (!confirmButton) {
            logError("[lighter_content] confirm button not found");
            throw new Error("confirm button not found");
        }

        confirmButton.click();
        log("[lighter_content] clicked confirm button");

        // Wait for the orders to be canceled
        await new Promise(resolve => setTimeout(resolve, 500));

        return count;
    } catch (error) {
        logError("[lighter_content] error closing all open orders:", error);
        throw error;
    }
}

// Function to extract position rows from the table
async function extractPositionRows() {
    try {
        log("[lighter_content] starting position rows extraction...");

        // Find the table by looking for the "Avg Entry" column header
        const tableHeaders = document.querySelectorAll('th');
        log("[lighter_content] found table headers:", tableHeaders);
        let positionsTable = null;
        for (const header of tableHeaders) {
            if (header.textContent.trim() === 'Entry Price') {
                positionsTable = header.closest('table');
                break;
            }
        }

        if (!positionsTable) {
            logError("[lighter_content] positions table not found");
            throw new Error("no positions found in table");
        }

        // Get all position rows
        const positionRows = positionsTable.querySelectorAll("tr[data-index]");
        log("[lighter_content] found position rows:", positionRows.length);

        if (!positionRows || positionRows.length === 0) {
            log("[lighter_content] no positions found in table");
            throw new Error("no positions found in table");
        }

        return positionRows;
    } catch (error) {
        logError("[lighter_content] error extracting position rows:", error);
        throw error;
    }
}

async function extractPositions() {
    try {
        log("[lighter_content] starting position extraction...");

        await switchToPositionsTab();

        // Check for "No Open Positions" message first
        const noPositionsElements = document.querySelectorAll("p");
        for (const element of noPositionsElements) {
            if (element.textContent.trim() === "No Open Positions") {
                log('[lighter_content] extract positions, found "No Open Positions" message, returning empty array');
                return [];
            }
        }

        // Get position rows using the new function
        const positionRows = await extractPositionRows();

        // Extract position data from each row
        const positions = Array.from(positionRows).map((row, index) => {
            // Extract symbol
            const symbolCell = row.querySelector("td:first-child span");
            const symbol = symbolCell ? symbolCell.textContent.trim() : "";
            log("[lighter_content] extract positions, symbol:", symbol);

            // Extract side - look for the div containing Long/Short text
            const sideDiv = Array.from(row.querySelectorAll('div')).find(div => {
                const span = div.querySelector('span');
                return span && (span.textContent.trim() === 'Long' || span.textContent.trim() === 'Short');
            });
            const side = sideDiv ? sideDiv.querySelector('span').textContent.trim().toLowerCase() : "";
            log("[lighter_content] extract positions, side:", side);

            // Extract size - find the div in the third td that contains the size value
            const sizeCell = row.querySelector("td:nth-child(2)");
            const sizeContainer = sizeCell ? Array.from(sizeCell.querySelectorAll('div')).find(div => {
                const spans = div.querySelectorAll('span');
                if (spans.length !== 2) return false;
                const firstSpan = spans[0];
                const secondSpan = spans[1];
                return !isNaN(parseFloat(firstSpan.textContent.trim())) && 
                       secondSpan.textContent.trim() === symbol;
            }) : null;
            const size = sizeContainer ? parseFloat(sizeContainer.querySelector('span').textContent.trim()) : 0;
            log("[lighter_content] extract positions, size:", size);

            // Extract entry price
            const entryPriceCell = row.querySelector("td:nth-child(4)");
            const entryPrice = entryPriceCell ? parseFloat(entryPriceCell.textContent.trim().replace("$", "").replace(",", "")) : 0;
            log("[lighter_content] extract positions, entry price:", entryPrice);

            // Extract mark price
            const markPriceCell = row.querySelector("td:nth-child(5)");
            const markPrice = markPriceCell ? parseFloat(markPriceCell.textContent.trim().replace("$", "").replace(",", "")) : 0;
            log("[lighter_content] extract positions, mark price:", markPrice);

            // Extract liquidation price
            const liquidationPriceCell = row.querySelector("td:nth-child(6)");
            let liquidationPrice = 0;
            if (liquidationPriceCell && liquidationPriceCell.textContent.trim() !== "N/A") {
                liquidationPrice = parseFloat(liquidationPriceCell.textContent.trim().replace("$", "").replace(",", ""));
            }
            log("[lighter_content] extract positions, liquidation price:", liquidationPrice);

            // Extract margin
            const marginCell = row.querySelector("td:nth-child(8)");
            const margin = marginCell ? parseFloat(marginCell.textContent.trim().replace("$", "").replace(",", "")) : 0;
            log("[lighter_content] extract positions, margin:", margin);

            // Extract PnL
            const pnlCell = row.querySelector("td:nth-child(7) span");
            let pnl = 0;
            let pnlPercentage = 0;
            if (pnlCell) {
                const pnlText = pnlCell.textContent.trim();
                log("[lighter_content] extract positions, pnl text:", pnlText);

                // Split the text into parts
                const parts = pnlText.split(" ");
                if (parts.length >= 2) {
                    // Parse the dollar amount (remove $ and convert to number)
                    const dollarPart = parts[0].replace("$", "");
                    pnl = parseFloat(dollarPart);

                    // Parse the percentage (remove parentheses and %)
                    const percentPart = parts[1].replace("(", "").replace(")", "").replace("%", "");
                    pnlPercentage = parseFloat(percentPart);
                }
            }
            log("[lighter_content] extract positions, pnl:", pnl, "pnl percentage:", pnlPercentage);

            // Extract TP/SL prices
            const tpSlCell = row.querySelector("td:nth-child(10)");
            let takeProfitPrice = 0;
            let stopLossPrice = 0;
            log("[lighter_content] extract positions, extract tp/sl, tpSlCell:", tpSlCell);
            if (tpSlCell) {
                // Find the second p element which contains the TP/SL values
                const tpSlValueElement = tpSlCell.querySelectorAll("p")[0];
                if (tpSlValueElement) {
                    const tpSlText = tpSlValueElement.textContent.trim();
                    log("[lighter_content] extract positions, tp/sl text:", tpSlText);

                    // Split the text into TP and SL parts
                    const [tpPart, slPart] = tpSlText.split("/");
                    
                    // Parse TP price if not "-"
                    if (tpPart && tpPart !== "__") {
                        // Remove $ and commas, then parse
                        takeProfitPrice = parseFloat(tpPart.replace("$", "").replace(/,/g, ""));
                    }
                    
                    // Parse SL price if not "-"
                    if (slPart && slPart !== "__") {
                        // Remove $ and commas, then parse
                        stopLossPrice = parseFloat(slPart.replace("$", "").replace(/,/g, ""));
                    }
                }
            } else {
                log("[lighter_content] extract positions, extract tp/sl, tpSlCell not found");
            }
            log("[lighter_content] extract positions, take profit price:", takeProfitPrice, "stop loss price:", stopLossPrice);

            const position = {
                symbol: symbol,
                side: side,
                entryPrice: entryPrice,
                markPrice: markPrice,
                size: size,
                margin: margin,
                liquidationPrice: liquidationPrice,
                pnl: pnl,
                pnlPercentage: pnlPercentage,
                takeProfitPrice: takeProfitPrice,
                stopLossPrice: stopLossPrice,
                createTime: new Date().toISOString(),
            };

            log("[lighter_content] extract positions, created position object:", position);
            return position;
        });

        log("[lighter_content] extract positions, extracted positions:", positions);
        return positions;
    } catch (error) {
        logError("[lighter_content] extract positions, error extracting positions:", error);
        throw error;
    }
}

async function extractOpenOrders() {
    // orders = [
    //   {
    //     symbol: 'BTC',
    //     side: 'long',
    //     size: 0.001,
    //     price: 50000,
    //     status: 'open',
    //     createdAt: '2021-01-01 12:00:00'
    //   }
    // ];
    orders = [];
    return orders;
}

// Function to create a limit order
async function createOrder(orderData) {
    try {
        log("[lighter_content] creating limit order with data:", orderData);

        // check symbol is supported
        if (!symbolPriceDecimals[orderData.symbol]) {
            throw new Error("symbol price decimals not supported: " + orderData.symbol);
        }
        if (!symbolAmountDecimals[orderData.symbol]) {
            throw new Error("symbol amount decimals not supported: " + orderData.symbol);
        }

        // 1. Select "Limit" order type if not already selected
        const limitButton = document.querySelector('button[data-testid="select-order-type-limit"]');
        if (limitButton && !limitButton.classList.contains("bg-white/10")) {
            log("[lighter_content] selecting limit order type");
            limitButton.click();
            // Wait for the UI to update
            await new Promise((resolve) => setTimeout(resolve, 100));
        }

        // 2. Select long/short position
        const longButton = Array.from(document.querySelectorAll("button")).find((button) => button.textContent.includes("Buy / Long"));
        const shortButton = Array.from(document.querySelectorAll("button")).find((button) => button.textContent.includes("Sell / Short"));

        log("[lighter_content] create limit order, longButton:", longButton);
        log("[lighter_content] create limit order, shortButton:", shortButton);

        if (longButton && shortButton) {
            if (orderData.side === "long") {
                const isCurrentlyChecked = longButton.classList.contains("active:bg-green-7");
                log("[lighter_content] selecting long position, isCurrentlyChecked:", isCurrentlyChecked);
                if (!isCurrentlyChecked) {
                    longButton.click();
                    await new Promise((resolve) => setTimeout(resolve, 100));
                }
            } else if (orderData.side === "short") {
                const isCurrentlyChecked = shortButton.classList.contains("active:bg-red-7");
                log("[lighter_content] selecting short position, isCurrentlyChecked:", isCurrentlyChecked);
                if (!isCurrentlyChecked) {
                    shortButton.click();
                    await new Promise((resolve) => setTimeout(resolve, 100));
                }
            } else {
                throw new Error('invalid side specified. must be "long" or "short", got: ' + orderData.side);
            }
        } else {
            throw new Error("position buttons not found");
        }

        // 3. Fill in the limit price
        const limitPriceInput = document.querySelector('input[data-testid="limit-order-limit-input"]');
        if (limitPriceInput) {
            log("[lighter_content] setting limit price:", orderData.price, ", limitPriceInput:", limitPriceInput);
            limitPriceInput.value = Number(orderData.price).toFixed(symbolPriceDecimals[orderData.symbol]);
            // Trigger input event to update the UI
            limitPriceInput.dispatchEvent(new Event("input", { bubbles: true }));
            // Wait for the UI to update
            await new Promise((resolve) => setTimeout(resolve, 100));
        } else {
            throw new Error("limit price input not found");
        }

        // 4. Fill in the amount
        const amountInput = document.querySelector('input[data-testid="place-order-size-input"]');
        if (amountInput) {
            log("[lighter_content] setting order amount:", orderData.size, ", amountInput:", amountInput);
            amountInput.value = Number(orderData.size).toFixed(symbolAmountDecimals[orderData.symbol]);
            // Trigger input event to update the UI
            amountInput.dispatchEvent(new Event("input", { bubbles: true }));
            // Wait for the UI to update
            await new Promise((resolve) => setTimeout(resolve, 100));
        } else {
            throw new Error("amount input not found");
        }

        // 5. Set reduce only if specified
        const reduceOnlyButton = Array.from(document.querySelectorAll("button")).find((button) => {
            const spans = button.querySelectorAll("span");
            return Array.from(spans).some(span => span.textContent.trim() === "Reduce Only");
        });
        
        log("[lighter_content] reduceOnlyButton:", reduceOnlyButton);
        if (reduceOnlyButton) {
            // Check if the button is already in the desired state by checking if Take Profit / Stop Loss button exists
            const isCurrentlyChecked = !(await checkTakeProfitStopLossButton());
            log("[lighter_content] isCurrentlyChecked:", isCurrentlyChecked);
            // Click the button if it's not in the desired state
            if (orderData.reduceOnly !== isCurrentlyChecked) {
                log(`[lighter_content] setting reduce only to ${orderData.reduceOnly}`);
                reduceOnlyButton.click();
                // Wait for the UI to update
                await new Promise((resolve) => setTimeout(resolve, 100));
            } else {
                log(`[lighter_content] reduce only already set to ${orderData.reduceOnly}, no action needed`);
            }
        }
        
        // 6. Place the order
        const placeOrderButton = document.querySelector('button[data-testid="place-order-button"]');
        if (placeOrderButton) {
            log("[lighter_content] placing order");
            placeOrderButton.click();
            // Wait for the order to be placed and UI to update
            await new Promise((resolve) => setTimeout(resolve, 300));
            
            // Clear inputs and trigger events to update UI
            amountInput.value = "";
            amountInput.dispatchEvent(new Event("input", { bubbles: true }));
            limitPriceInput.value = "";
            limitPriceInput.dispatchEvent(new Event("input", { bubbles: true }));
            
            return { success: true };
        } else {
            throw new Error("place order button not found");
        }
    } catch (error) {
        logError("[lighter_content] error creating limit order:", error);
        return { success: false, error: error.message };
    }
}

async function createMarketOrder(orderData) {
    try {
        log("[lighter_content] creating market order with data:", orderData);

        // check symbol is supported
        if (!symbolAmountDecimals[orderData.symbol]) {
            throw new Error("symbol amount decimals not supported: " + orderData.symbol);
        }

        // 1. Select "Market" order type if not already selected
        const marketButton = document.querySelector('button[data-testid="select-order-type-market"]');
        if (marketButton && !marketButton.classList.contains("bg-white/10")) {
            log("[lighter_content] selecting market order type");
            marketButton.click();
            // Wait for the UI to update
            await new Promise((resolve) => setTimeout(resolve, 100));
        }

        // 2. Select long/short position
        const longButton = Array.from(document.querySelectorAll("button")).find((button) => button.textContent.includes("Buy / Long"));
        const shortButton = Array.from(document.querySelectorAll("button")).find((button) => button.textContent.includes("Sell / Short"));

        log("[lighter_content] longButton:", longButton);
        log("[lighter_content] shortButton:", shortButton);

        if (longButton && shortButton) {
            if (orderData.side === "long") {
                log("[lighter_content] selecting long position");
                if (!longButton.classList.contains("active:bg-green-7")) {
                    longButton.click();
                    await new Promise((resolve) => setTimeout(resolve, 100));
                }
            } else if (orderData.side === "short") {
                log("[lighter_content] selecting short position");
                if (!shortButton.classList.contains("active:bg-red-7")) {
                    shortButton.click();
                    await new Promise((resolve) => setTimeout(resolve, 100));
                }
            } else {
                throw new Error('invalid side specified. must be "long" or "short", got: ' + orderData.side);
            }
        } else {
            throw new Error("position buttons not found");
        }

        // 3. Fill in the amount
        const amountInput = document.querySelector('input[data-testid="place-order-size-input"]');
        if (amountInput) {
            log("[lighter_content] setting order amount:", orderData.size, ", amountInput:", amountInput);
            amountInput.value = Number(orderData.size).toFixed(symbolAmountDecimals[orderData.symbol]);
            // Trigger input event to update the UI
            amountInput.dispatchEvent(new Event("input", { bubbles: true }));
            // Wait for the UI to update
            await new Promise((resolve) => setTimeout(resolve, 100));
        } else {
            throw new Error("amount input not found");
        }

        // 4. Set reduce only if specified
        const reduceOnlyButton = Array.from(document.querySelectorAll("button")).find((button) => {
            const spans = button.querySelectorAll("span");
            return Array.from(spans).some(span => span.textContent.trim() === "Reduce Only");
        });
        
        log("[lighter_content] reduceOnlyButton:", reduceOnlyButton);
        if (reduceOnlyButton) {
            // Check if the button is already in the desired state by checking if Take Profit / Stop Loss button exists
            const isCurrentlyChecked = !(await checkTakeProfitStopLossButton());
            log("[lighter_content] isCurrentlyChecked:", isCurrentlyChecked);
            // Click the button if it's not in the desired state
            if (orderData.reduceOnly !== isCurrentlyChecked) {
                log(`[lighter_content] setting reduce only to ${orderData.reduceOnly}`);
                reduceOnlyButton.click();
                // Wait for the UI to update
                await new Promise((resolve) => setTimeout(resolve, 100));
            } else {
                log(`[lighter_content] reduce only already set to ${orderData.reduceOnly}, no action needed`);
            }
        }
        
        // 5. Place the order
        const placeOrderButton = document.querySelector('button[data-testid="place-order-button"]');
        if (placeOrderButton) {
            log("[lighter_content] placing order");
            placeOrderButton.click();
            // Wait for the order to be placed and UI to update
            await new Promise((resolve) => setTimeout(resolve, 300));
            
            // Check for confirmation dialog with specific title
            const confirmationDialog = Array.from(document.querySelectorAll('div[role="dialog"]')).find(dialog => {
                const titleElement = dialog.querySelector('h2');
                return titleElement && titleElement.textContent.trim() === 'Confirm Market Order';
            });
            
            if (confirmationDialog) {
                log("[lighter_content] found market order confirmation dialog");
                
                // Find and check "Don't show again" checkbox
                const dontShowAgainLabel = Array.from(confirmationDialog.querySelectorAll('div')).find(div => 
                    div.textContent.trim() === 'Don\'t show again'
                );
                if (dontShowAgainLabel) {
                    log("[lighter_content] found don't show again label");
                    const checkbox = dontShowAgainLabel.querySelector('span');
                    if (checkbox) {
                        log("[lighter_content] clicking don't show again checkbox");
                        checkbox.click();
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                }

                // Find and click the Confirm button
                const confirmButton = Array.from(confirmationDialog.querySelectorAll('button')).find(button => 
                    button.textContent.trim() === 'Confirm'
                );
                if (confirmButton) {
                    log("[lighter_content] clicking confirm button");
                    confirmButton.click();
                    await new Promise(resolve => setTimeout(resolve, 300));
                }
            } else {
                log("[lighter_content] no market order confirmation dialog found, skipped");
            }
            
            // Clear inputs and trigger events to update UI
            amountInput.value = "";
            amountInput.dispatchEvent(new Event("input", { bubbles: true }));
            
            return { success: true };
        } else {
            throw new Error("place order button not found");
        }
    } catch (error) {
        logError("[lighter_content] error creating market order:", error);
        return { success: false, error: error.message };
    }
}

// Function to check if the symbol matches the current trading pair
function checkSymbol(symbol) {
    // Get the current URL path
    const path = window.location.pathname;
    log("[lighter_content] current path:", path);

    // Extract the trading pair from the URL (e.g., /trade/ETH -> ETH)
    const urlSymbol = path.split("/").pop().toUpperCase();
    log("[lighter_content] url symbol:", urlSymbol);

    // Check if the requested symbol matches the URL symbol
    const matches = symbol.toUpperCase() === urlSymbol;
    log("[lighter_content] symbol match:", matches);

    return matches;
}

// Function to get the last price from the Index Price element
async function getLastPrice(symbol) {
    try {
        log("[lighter_content] getting last price for symbol:", symbol);

        // Check if the symbol matches the current trading pair
        if (!checkSymbol(symbol)) {
            logError("[lighter_content] symbol mismatch");
            return null;
        }

        await connectWallet();

        // Find all elements containing "Index Price" text
        const indexPriceElements = Array.from(document.querySelectorAll("p")).filter(p => 
            p.textContent.includes("Index Price")
        );

        log("[lighter_content] found index price elements:", indexPriceElements.length);

        if (indexPriceElements.length === 0) {
            logError("[lighter_content] no index price elements found");
            return null;
        }

        // For each index price element, check its parent container for the price
        for (const indexPriceElement of indexPriceElements) {
            const priceContainer = indexPriceElement.parentElement?.querySelector("div");
            if (!priceContainer) continue;

            const priceElement = priceContainer.querySelector("p");
            if (!priceElement) continue;

            // Extract the price value (remove the $ and any commas)
            const priceText = priceElement.textContent.trim();
            const price = parseFloat(priceText.replace("$", "").replace(/,/g, ""));

            // If we found a valid price, return it
            if (!isNaN(price)) {
                log("[lighter_content] last price:", price);
                return { lastPrice: price, spread: 0 };
            }
        }

        logError("[lighter_content] no valid price found in any index price element");
        return null;
    } catch (error) {
        logError("[lighter_content] error getting last price:", error);
        return null;
    }
}

async function connectWallet() {
    try {
        log("[lighter_content] connecting wallet...");

        // Find the Connect Wallet button by looking for a span with the text
        const connectWalletButton = Array.from(document.querySelectorAll('button')).find(button => {
            const span = button.querySelector('span');
            return span && span.textContent.trim().startsWith('Connect Wallet to ');
        });
        
        if (!connectWalletButton) {
            logError("[lighter_content] connect wallet button not found");
            return false;
        }
        // Click the button
        connectWalletButton.click();
        log("[lighter_content] clicked connect wallet button");

        // Wait for the wallet connection dialog to appear
        await new Promise(resolve => setTimeout(resolve, 300));

        await clickOneKeyButton();
        return true;
    } catch (error) {
        logError("[lighter_content] error connecting wallet:", error);
        return false;
    }
}


async function needReload() {
    try {
        log("[lighter_content] connecting wallet...");

        // Find the Connect Wallet button by looking for a span with the text
        const connectWalletButton = Array.from(document.querySelectorAll('button')).find(button => {
            const span = button.querySelector('span');
            return span && span.textContent.trim() === 'Connect Wallet to Trade';
        });
        
        if (!connectWalletButton) {
            logError("[lighter_content] connect wallet button not found");
            return false;
        }

        return true;
    } catch (error) {
        logError("[lighter_content] error connecting wallet:", error);
        return false;
    }
}

async function clickOneKeyButton() {
    try {
        log("[lighter_content] clicking onekey button...");

        // First find the dynamic modal
        const modal = document.querySelector('#dynamic-modal');
        log("[lighter_content] clicking onekey button, dynamic modal:", modal);
        
        if (!modal) {
            logError("[lighter_content] clicking onekey button, dynamic modal not found");
            return false;
        }

        // Get the shadow root
        const shadowRoot = modal.querySelector('div[data-testid="dynamic-modal-shadow"]')?.shadowRoot;
        log("[lighter_content] clicking onekey button, shadow root:", shadowRoot);
        
        if (!shadowRoot) {
            logError("[lighter_content] clicking onekey button, shadow root not found");
            return false;
        }

        // Find the div with class list-tile__children that contains the OneKey text within the shadow root
        const textDiv = Array.from(shadowRoot.querySelectorAll('div.list-tile__children')).find(div => {
            const span = div.querySelector('span');
            return span && span.textContent.trim() === 'OneKey';
        });
        
        if (!textDiv) {
            logError("[lighter_content] clicking onekey button, onekey text div not found");
            return false;
        }

        // Get the parent button
        const oneKeyButton = textDiv.closest('button[data-testid="ListTile"]');
        if (!oneKeyButton) {
            logError("[lighter_content] clicking onekey button, onekey button not found");
            return false;
        }

        // Click the button
        oneKeyButton.click();
        log("[lighter_content] clicked onekey button");

        // Wait for the next dialog to appear
        await new Promise(resolve => setTimeout(resolve, 200));

        return true;
    } catch (error) {
        logError("[lighter_content] error clicking onekey button:", error);
        return false;
    }
}


// Function to get the user ID from the Lighter platform
async function getUserId() {
    log("[lighter_content] getting user id...");
    return "";
}

// Function to check if the Take Profit / Stop Loss button exists
async function checkTakeProfitStopLossButton() {
    try {
        log("[lighter_content] checking for take profit / stop loss button...");

        // Find the button by looking for a span with the exact text
        const button = Array.from(document.querySelectorAll("button")).find((button) => {
            const spans = button.querySelectorAll("span");
            return Array.from(spans).some(span => span.textContent.trim() === "Take Profit / Stop Loss");
        });

        if (button) {
            log("[lighter_content] take profit / stop loss button found:", button);
            return true;
        } else {
            log("[lighter_content] take profit / stop loss button not found");
            return false;
        }
    } catch (error) {
        logError("[lighter_content] error checking for take profit / stop loss button:", error);
        return false;
    }
}

// Function to get user margin
async function getUserMargin() {
    try {
        // Get total margin
        const totalMargin = extractTotalMargin();

        log("[lighter_content] get user margin, total margin:", totalMargin);

        // Get available margin
        const availableBalanceDiv = Array.from(document.querySelectorAll("div")).find((div) => {
            const pElement = div.querySelector("span");
            return pElement && pElement.textContent.includes("Available Balance");
        });

        const parseNumber = (text) => {
            if (!text) return 0;
            return parseFloat(text.replace(/[^0-9.-]+/g, "")) || 0;
        };

        log("[lighter_content] available balance div:", availableBalanceDiv);

        // Find the available margin value within the div
        let availableMargin = 0;
        if (availableBalanceDiv) {
            const valueElement = availableBalanceDiv.querySelector("span:last-child");
            if (valueElement) {
                availableMargin = parseNumber(valueElement.textContent);
            }
        }

        return {
            totalMargin: totalMargin,
            availableMargin: availableMargin,
        };
    } catch (error) {
        logError("[lighter_content] error getting user margin:", error);
        throw error;
    }
}

function extractTotalMargin() {
    // Find the span containing "Portfolio Value:"
    const portfolioValueSpan = Array.from(document.querySelectorAll('span')).find(span => 
        span.textContent.trim() === 'Trading Equity:'
    );

    log("[lighter_content] portfolio value span:", portfolioValueSpan);

    if (portfolioValueSpan) {
        // Get the parent div that contains both the label and value
        const container = portfolioValueSpan.parentElement.parentElement;
        log("[lighter_content] portfolio value container:", container);
        
        if (container) {
            // Find the value span (it should be the other span in the container)
            const valueSpan = Array.from(container.querySelectorAll('span')).find(span => 
                span !== portfolioValueSpan
            );
            log("[lighter_content] portfolio value element:", valueSpan);
            
            if (valueSpan) {
                // Extract the numeric value from the string (remove $ and convert to float)
                const value = parseFloat(valueSpan.textContent.replace("$", "").replace(",", ""));
                return value;
            }
        }
    }
    throw new Error("total margin not found");
}

// Function to check if service is unavailable
async function extractServiceUnavailable() {
    try {
        log("[lighter_content] checking for service unavailable message...");

        // Find the element containing "503 - Service Unavailable" text
        const serviceUnavailableElement = Array.from(document.querySelectorAll("p")).find((p) => p.textContent.includes("503 - Service Unavailable"));

        if (serviceUnavailableElement) {
            log("[lighter_content] service unavailable message found");
            return true;
        }

        log("[lighter_content] service unavailable message not found");
        return false;
    } catch (error) {
        logError("[lighter_content] error checking for service unavailable:", error);
        return false;
    }
}

async function extractPoints() {
    try {
        log("[lighter_content] extracting points...");
        
        // Find the element containing "Your Total Points" text
        const pointsLabel = Array.from(document.querySelectorAll("p")).find(p => 
            p.textContent.trim() === "Your Total Points"
        );

        if (!pointsLabel) {
            log("[lighter_content] points label not found");
            return 0;
        }

        // Get the parent div that contains both the label and value
        const container = pointsLabel.parentElement.parentElement;
        if (!container) {
            log("[lighter_content] points container not found");
            return 0;
        }

        // Find the points value element (the second p element in the container)
        const valueElement = container.querySelector("p:last-child");
        if (!valueElement) {
            log("[lighter_content] points value element not found");
            return 0;
        }

        // Extract the points value
        const points = parseFloat(valueElement.textContent.trim());
        if (isNaN(points)) {
            log("[lighter_content] invalid points value");
            return 0;
        }

        log("[lighter_content] points:", points);
        return points;
    } catch (error) {
        logError("[lighter_content] error extracting points:", error);
        return 0;
    }
}

async function setTakeProfitStopLoss(symbol, tpPrice, slPrice) {
    try {
        log("[lighter_content] setting take profit and stop loss for symbol:", symbol);

        // Check if symbol is supported
        if (!symbolPriceDecimals[symbol]) {
            throw new Error("symbol price decimals not supported: " + symbol);
        }

        // Get position rows using the new function
        const positionRows = await extractPositionRows();
        
        // Find the target row that matches the symbol
        let targetRow = null;
        for (const row of positionRows) {
            const symbolCell = row.querySelector("td:first-child span");
            if (symbolCell && symbolCell.textContent.trim() === symbol) {
                targetRow = row;
                break;
            }
        }

        if (!targetRow) {
            logError("[lighter_content] position row not found for symbol:", symbol);
            throw new Error(`position row not found for symbol: ${symbol}`);
        }

        // Find the edit button within the target row
        const editButton = targetRow.querySelector('button svg[viewBox="0 0 256 256"]')?.closest('button');

        if (!editButton) {
            logError("[lighter_content] edit button not found in position row for symbol:", symbol);
            throw new Error(`edit button not found in position row for symbol: ${symbol}`);
        }

        // Click the edit button
        editButton.click();
        log("[lighter_content] clicked edit button for symbol:", symbol);

        // Wait for the dialog to appear
        await new Promise(resolve => setTimeout(resolve, 100));

        // Find the dialog by looking for the TP/SL for Position text
        const tpSlLabel = Array.from(document.querySelectorAll('span')).find(span => 
            span.textContent.trim() === 'TP/SL for Position'
        );
        if (!tpSlLabel) {
            throw new Error('TP/SL dialog not found');
        }
        log("[lighter_content] found TP/SL dialog label");

        // Get the parent div that contains both the label and close button
        const tpslHeader = tpSlLabel.parentElement;
        if (!tpslHeader) {
            throw new Error('TP/SL dialog header not found');
        }
        log("[lighter_content] found TP/SL dialog header");

        // Find the main container div that contains all TP/SL UI elements
        const tpslContainer = tpslHeader.parentElement;
        if (!tpslContainer) {
            throw new Error('TP/SL container not found');
        }
        log("[lighter_content] found TP/SL container", tpslContainer);

        // Find the edit UI div (the div after tpslHeader)
        const editUIDiv = tpslContainer.children[2]; // The third child div contains the edit UI
        if (!editUIDiv) {
            throw new Error('TP/SL edit UI not found');
        }
        log("[lighter_content] found TP/SL edit UI");

        // Find the TP Price input field within the dialog
        const tpPriceLabel = Array.from(editUIDiv.querySelectorAll('label')).find(p => p.textContent.trim() === 'TP Price');
        if (!tpPriceLabel) {
            throw new Error('TP Price label not found');
        }
        log("[lighter_content] found TP Price label:", tpPriceLabel);

        // the input is the next sibling of the label
        const tpPriceInput = tpPriceLabel.nextElementSibling;
        if (!tpPriceInput) {
            logError("[lighter_content] TP Price input not found in container");
            throw new Error('TP Price input not found');
        }
        log("[lighter_content] found TP Price input:", tpPriceInput);

        // Find the SL Price input field within the dialog
        const slPriceLabel = Array.from(editUIDiv.querySelectorAll('label')).find(p => p.textContent.trim() === 'SL Price');
        if (!slPriceLabel) {
            throw new Error('SL Price label not found');
        }
        log("[lighter_content] found SL Price label:", slPriceLabel);

        // the input is the next sibling of the label
        const slPriceInput = slPriceLabel.nextElementSibling;
        if (!slPriceInput) {
            logError("[lighter_content] SL Price input not found in container");
            throw new Error('SL Price input not found');
        }
        log("[lighter_content] found SL Price input:", slPriceInput);

        // Set the TP price
        tpPriceInput.value = Number(tpPrice).toFixed(symbolPriceDecimals[symbol]);
        tpPriceInput.dispatchEvent(new Event('input', { bubbles: true }));
        log("[lighter_content] set TP price:", tpPrice);

        // Set the SL price
        slPriceInput.value = Number(slPrice).toFixed(symbolPriceDecimals[symbol]);
        slPriceInput.dispatchEvent(new Event('input', { bubbles: true }));
        log("[lighter_content] set SL price:", slPrice);

        // Wait for the UI to update
        await new Promise(resolve => setTimeout(resolve, 100));

        // Find and click the submit button within the dialog
        const submitButton = Array.from(editUIDiv.querySelectorAll('button')).find(button => 
            // the button wraps a <span> with text "Submit"
            button.querySelector('span')?.textContent.trim() === 'Submit'
        );
        
        if (!submitButton) {
            throw new Error('Submit button not found');
        }

        // Click the submit button
        submitButton.click();
        log("[lighter_content] clicked submit button");

        // Wait for the dialog to close
        await new Promise(resolve => setTimeout(resolve, 100));

        try {
            // close the dialog if the close button is found
            const closeButton = tpslHeader.querySelector('button');
            if (!closeButton) {
                log("[lighter_content] TP/SL dialog close button not found");
                return true;
            }
            closeButton.click();
            await new Promise(resolve => setTimeout(resolve, 100));
            log("[lighter_content] closed TP/SL dialog");
        } catch (error) {
            logError("[lighter_content] error closing TP/SL dialog:", error);
        }

        return true;
    } catch (error) {
        logError("[lighter_content] error setting take profit and stop loss:", error);
        throw error;
    }
}

async function fastWithdraw(targetAddress, targetAmount) {
    try {
        log("[lighter_content] fast withdraw, start, targetAddress:", targetAddress, "targetAmount:", targetAmount);

        let withdrawAmount = 0;
        // cancel open orders and close positions can be done with app.stop()
        
        // Click wallet button
        const walletButton = document.querySelector('button[data-testid="wallet-button"]');
        if (!walletButton) {
            throw new Error('wallet button not found');
        }
        walletButton.click();
        log("[lighter_content] clicked wallet button");

        // Wait for the dialog to appear
        await new Promise(resolve => setTimeout(resolve, 500));

        // Find the dialog, dialog1
        const dialog = document.querySelector('div[role="dialog"]');
        if (!dialog) {
            throw new Error('wallet dialog not found');
        }

        // Find the withdraw button within the dialog
        const withdrawButton = Array.from(dialog.querySelectorAll('button')).find(button => 
            button.textContent.trim() === 'Withdraw'
        );
        if (!withdrawButton) {
            throw new Error('withdraw button not found');
        }
        withdrawButton.click();
        log("[lighter_content] clicked withdraw button");

        // Wait for the next dialog to appear
        await new Promise(resolve => setTimeout(resolve, 500));

        // Find the next dialog, dialog2
        const withdrawDialog = document.querySelector('div[role="dialog"]');
        if (!withdrawDialog) {
            throw new Error('withdraw dialog not found');
        }

        log("[lighter_content] withdraw dialog:", withdrawDialog);
        // Find the fast withdrawal button
        const fastWithdrawLabel = Array.from(withdrawDialog.querySelectorAll('p')).find(p => 
            p.textContent.trim() === 'Fast Withdrawal'
        );
        log("[lighter_content] fast withdrawal label:", fastWithdrawLabel);
        if (!fastWithdrawLabel) {
            throw new Error('fast withdrawal label not found');
        }
        const fastWithdrawButton = fastWithdrawLabel.closest('button');
        if (!fastWithdrawButton) {
            throw new Error('fast withdrawal button not found');
        }
        log("[lighter_content] fast withdrawal button:", fastWithdrawButton);
        fastWithdrawButton.click();
        log("[lighter_content] clicked fast withdrawal button");

        // Wait for the next dialog to appear
        await new Promise(resolve => setTimeout(resolve, 500));

        // Find the next dialog, dialog3
        const amountDialog = document.querySelector('div[role="dialog"]');
        if (!amountDialog) {
            throw new Error('amount dialog not found');
        }

        log("[lighter_content] amount dialog:", amountDialog);
        // Find the maximum withdrawal amount
        const maxAmountLabel = Array.from(amountDialog.querySelectorAll('span')).find(span => 
            span.textContent.trim() === 'Available Balance'
        );
        if (!maxAmountLabel) {
            throw new Error('maximum withdrawal amount label not found');
        }
        log("[lighter_content] maximum withdrawal amount label:", maxAmountLabel);
        // Get the amount span (next sibling)
        const amountSpan = maxAmountLabel.nextElementSibling;
        if (!amountSpan) {
            throw new Error('amount span not found');
        }
        log("[lighter_content] amount span:", amountSpan);

        // Wait for amount text to be populated
        let amountText = '';
        let retries = 0;
        const maxRetries = 15;
        while (retries < maxRetries) {
            amountText = amountSpan.textContent.trim();
            if (amountText && amountText !== '') {
                break;
            }
            log("[lighter_content] waiting for amount text to be populated, retry:", retries + 1);
            await new Promise(resolve => setTimeout(resolve, 500));
            retries++;
        }
        if (!amountText) {
            throw new Error('amount text not populated after retries');
        }

        // Extract the amount (remove quotes, spaces, USDC, and commas, then convert to number)
        amountText = amountText.replace(/["\s]/g, '').replace('USDC', '').replace(/,/g, '');
        let amount = parseFloat(amountText);
        if (isNaN(amount)) {
            throw new Error('invalid amount format');
        }
        log("[lighter_content] maximum withdrawal amount:", amount);

        // if target amount is less than the maximum withdrawal amount, use the target amount
        // if target amount is 0, use the maximum withdrawal amount
        if (targetAmount > 0) {
            amount = targetAmount;
        }

        // Find and set the withdraw amount input
        const amountInput = amountDialog.querySelector('input[placeholder="0.00"]');
        if (!amountInput) {
            throw new Error('amount input not found');
        }
        log("[lighter_content] amount input:", amountInput);
        amountInput.value = amount.toFixed(0);
        amountInput.dispatchEvent(new Event('input', { bubbles: true }));
        await new Promise(resolve => setTimeout(resolve, 500));
        log("[lighter_content] set withdraw amount:", amount);

        // Find and check the target address input
        const addressInput = amountDialog.querySelector('input[placeholder="0x..."]');
        if (!addressInput) {
            throw new Error('address input not found');
        }
        log("[lighter_content] address input:", addressInput);
        const address = addressInput.value.trim();
        if (address !== targetAddress) {
            throw new Error(`target address mismatch: expected ${targetAddress}, got ${address}`);
        }
        log("[lighter_content] target address verified:", address);

        // IMPORTANT: fast withdraw requires signature, which can't be automated, so don't hit the withdraw button
        // 
        // Find and click the withdraw button
        // const confirmWithdrawButton = Array.from(amountDialog.querySelectorAll('button')).find(button => 
        //     button.textContent.trim() === 'Withdraw'
        // );
        // if (!confirmWithdrawButton) {
        //     throw new Error('withdraw button not found');
        // }
        // log("[lighter_content] confirm withdraw button:", confirmWithdrawButton);
        // confirmWithdrawButton.click();
        // log("[lighter_content] clicked withdraw button");

        // // Wait for the dialog to close
        // await new Promise(resolve => setTimeout(resolve, 1000));
        log("[lighter_content] withdraw completed");

        return amount;
    } catch (error) {
        logError("[lighter_content] error fast withdrawing:", error);
        throw error;
    }
}

async function getVolumeAndProfit() {
    try {
        log("[lighter_content] get volume and profit, start");
        // Find and click the Performance button
        const performanceButton = Array.from(document.querySelectorAll('button')).find(button => 
            button.textContent.trim() === 'Performance'
        );
        
        if (performanceButton) {
            log("[lighter_content] found performance button, clicking...");
            performanceButton.click();
            // Wait for the UI to update
            await new Promise(resolve => setTimeout(resolve, 500));
        } else {
            logError("[lighter_content] performance button not found");
        }

        // Find the All-time Volume paragraph
        const volumeLabel = Array.from(document.querySelectorAll('p')).find(p => 
            p.textContent.trim() === 'Volume'
        );
        
        if (!volumeLabel) {
            logError("[lighter_content] All-time Volume label not found");
            return { volume: 0, profit: 0 };
        }

        // Get the parent div that contains both the label and value
        const volumeContainer = volumeLabel.parentElement.parentElement;
        if (!volumeContainer) {
            logError("[lighter_content] volume container not found");
            return { volume: 0, profit: 0 };
        }

        // Find the value paragraph (it should be the last p element in the container)
        const volumeValue = volumeContainer.querySelector('p:last-child');
        if (!volumeValue) {
            logError("[lighter_content] volume value not found");
            return { volume: 0, profit: 0 };
        }

        // Retry getting the volume value a few times if it's initially "-"
        let volume = 0;
        let retries = 0;
        const maxRetries = 5;
        const retryDelay = 300; // 300 ms between retries

        while (retries < maxRetries) {
            const volumeText = volumeValue.textContent.trim();
            log("[lighter_content] get volume and profit, attempt", retries + 1, "volume text:", volumeText);
            
            if (volumeText !== "-") {
                // Extract the volume value (remove $ and commas, then convert to number)
                volume = parseFloat(volumeText.replace('$', '').replace(/,/g, ''));
                if (!isNaN(volume)) {
                    log("[lighter_content] get volume and profit, extracted volume:", volume);
                    break;
                }
            }
            
            retries++;
            if (retries < maxRetries) {
                log("[lighter_content] get volume and profit, retrying in", retryDelay, "ms...");
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }

        const profit = 0.0;
        return { volume, profit };
    } catch (error) {
        logError("[lighter_content] error extracting volume and profit:", error);
        return { volume: 0, profit: 0 };
    }
}

// Function to reload the page
async function reloadPage() {
    try {
        log("[lighter_content] reloading page...");
        window.location.reload();
        return true;
    } catch (error) {
        logError("[lighter_content] error reloading page:", error);
        throw error;
    }
}

// Listen for messages from the extension
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    log("[lighter_content] received message:", request);

    if (request.action === "createOrder") {
        log("[lighter_content] create order, start");
        createOrder(request.orderData)
            .then((result) => {
                log("[lighter_content] create order, send response:", result);
                sendResponse({
                    success: true,
                });
            })
            .catch((error) => {
                logError("[lighter_content] create order, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "createMarketOrder") {
        log("[lighter_content] create market order, start");
        createMarketOrder(request.orderData)
            .then((result) => {
                log("[lighter_content] create market order, send response:", result);
                sendResponse({
                    success: true,
                });
            })
            .catch((error) => {
                logError("[lighter_content] create market order, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getLastPrice") {
        log("[lighter_content] get last price, start");
        getLastPrice(request.symbol)
            .then((price) => {
                log("[lighter_content] get last price, send response:", price);
                sendResponse({
                    success: true,
                    price: price,
                });
            })
            .catch((error) => {
                logError("[lighter_content] get last price, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getUserId") {
        log("[lighter_content] get user id, start");
        getUserId()
            .then((userId) => {
                log("[lighter_content] get user id, send response:", userId);
                sendResponse({
                    success: true,
                    userId: userId,
                });
            })
            .catch((error) => {
                logError("[lighter_content] get user id, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getUserMargin") {
        log("[lighter_content] get margin, start");
        getUserMargin()
            .then((marginInfo) => {
                log("[lighter_content] get margin, send response:", marginInfo);
                sendResponse({
                    success: true,
                    margin: marginInfo,
                });
            })
            .catch((error) => {
                logError("[lighter_content] get margin, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getPositions") {
        log("[lighter_content] get positions, start");
        extractPositions()
            .then((positions) => {
                log("[lighter_content] get positions, send response:", { positions });
                sendResponse({
                    success: true,
                    positions: positions,
                });
            })
            .catch((error) => {
                logError("[lighter_content] get positions, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getOpenOrders") {
        log("[lighter_content] get open orders, start");
        extractOpenOrders()
            .then((orders) => {
                log("[lighter_content] get open orders, send response:", { orders });
                sendResponse({
                    success: true,
                    orders: orders,
                });
            })
            .catch((error) => {
                logError("[lighter_content] get open orders, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getServiceUnavailable") {
        log("[lighter_content] get service unavailable, start");
        extractServiceUnavailable().then((isUnavailable) => {
            log("[lighter_content] get service unavailable, send response:", isUnavailable);
            sendResponse({
                success: true,
                isUnavailable: isUnavailable,
            });
        })
        .catch((error) => {
            logError("[lighter_content] get service unavailable, error:", error);
            sendResponse({
                success: false,
                error: error.message,
            });
        });
        return true; // Keep the message channel open for async response
    } else if (request.action === "closeAllOpenOrders") {
        log("[lighter_content] close all open orders, start");
        closeAllOpenOrders()
            .then((count) => {
                log("[lighter_content] close all open orders, send response:", count);
                sendResponse({
                    success: true,
                    count: count,
                });
            })
            .catch((error) => {
                logError("[lighter_content] close all open orders, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getOpenOrdersCount") {
        log("[lighter_content] get open orders count, start");
        try {
            const { count } = getOpenOrdersButton();
            log("[lighter_content] get open orders count, send response:", count);
            sendResponse({
                success: true,
                count: count,
            });
        } catch (error) {
            logError("[lighter_content] get open orders count, error:", error);
            sendResponse({
                success: false,
                error: error.message,
            });
        }
        return true; // Keep the message channel open for async response
    } else if (request.action === "getPoints") {
        log("[lighter_content] get points, start");
        extractPoints()
            .then((points) => {
                log("[lighter_content] get points, send response:", points);
                sendResponse({
                    success: true,
                    points: points,
                });
            })
            .catch((error) => {
                logError("[lighter_content] get points, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "setTakeProfitStopLoss") {
        log("[lighter_content] set take profit stop loss, start");
        setTakeProfitStopLoss(request.symbol, request.tpPrice, request.slPrice)
            .then((result) => {
                log("[lighter_content] set take profit stop loss, send response:", result);
                sendResponse({
                    success: true,
                });
            })
            .catch((error) => {
                logError("[lighter_content] set take profit stop loss, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "fastWithdraw") {
        log("[lighter_content] fast withdraw, start, address:", request.address, "amount:", request.amount);
        fastWithdraw(request.address, request.amount)
            .then((amount) => {
                log("[lighter_content] fast withdraw, send response:", amount);
                sendResponse({
                    success: true,
                    amount: amount,
                });
            })
            .catch((error) => {
                logError("[lighter_content] fast withdraw, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "getVersion") {
        log("[lighter_content] get version, start");
        sendResponse({
            success: true,
            version: version,
        });
    } else if (request.action === "getVolumeAndProfit") {
        log("[lighter_content] get volume and profit, start");
        getVolumeAndProfit()
            .then(({ volume, profit }) => {
                log("[lighter_content] get volume and profit, send response:", { volume, profit });
                sendResponse({
                    success: true,
                    volume: volume,
                    profit: profit,
                });
            })
            .catch((error) => {
                logError("[lighter_content] get volume and profit, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    } else if (request.action === "needReload") {
        log("[lighter_content] need reload, start");
        needReload()
            .then((result) => {
                log("[lighter_content] need reload, send response:", result);
                sendResponse({
                    success: true,
                    result: result,
                });
            })
            .catch((error) => {
                logError("[lighter_content] need reload, error:", error);
                sendResponse({
                    success: false,
                    error: error.message,
                });
            });
        return true; // Keep the message channel open for async response
    }
});

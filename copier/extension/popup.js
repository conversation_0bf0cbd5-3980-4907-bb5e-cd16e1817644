import { initializeOrderTab } from "./order.js";
import { initializePositionsTab } from "./positions.js";
import { initializeSettingsTab } from "./settings.js";
import { updateOrderSymbol, updateAppStatus, setupStatusDisplay } from "./common.js";

// import { initApp, setupAppListener, getAppInstance } from './bg_ctrl.js';
import { initApp, setupAppListener, getAppInstance } from "./fg_ctrl.js";


// Initialize app
document.addEventListener("DOMContentLoaded", function () {
    console.log("[popup] popup dom loaded");

    // Check if app instance exists and reassign to window.app
    // looks like DOM<PERSON>ontentLoaded didn't trigger when the popup is opened
    // window.app reassignment isn't taking effect, keep the code anyway
    const existingApp = getAppInstance();
    console.log("[popup] existing app", existingApp);
    if (existingApp) {
        console.log("[popup] found existing app instance, reassigning to window.app");
        window.app = existingApp;
    }

    // Initialize the app when the popup is opened
    console.log("[popup] setting up background listener...");
    setupAppListener();

    setupStatusDisplay();

    // Only initialize if no existing app
    if (!existingApp) {
        initApp();
    }

    // Tab navigation
    const navTabs = document.querySelectorAll(".nav-tab");
    const tabContents = document.querySelectorAll(".tab-content");

    // Load the default symbol from settings and set it immediately
    updateOrderSymbol();

    navTabs.forEach((tab) => {
        tab.addEventListener("click", () => {
            const targetTab = tab.dataset.tab;

            // Update active states
            navTabs.forEach((t) => t.classList.remove("active"));
            tabContents.forEach((c) => c.classList.remove("active"));

            tab.classList.add("active");
            document.getElementById(targetTab).classList.add("active");

            // Initialize the appropriate tab
            switch (targetTab) {
                case "order":
                    initializeOrderTab();
                    break;
                case "positions":
                    initializePositionsTab();
                    break;
                case "settings":
                    initializeSettingsTab();
                    break;
            }
        });
    });

    // Initialize the default tab (positions)
    initializePositionsTab();
    updateAppStatus();
});

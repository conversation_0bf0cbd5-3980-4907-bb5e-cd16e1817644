import { updateAppStatus, showError } from "./common.js";
import { LighterPlatform } from "./platforms/lighter/lighter.js";
import { STATUS } from "./app.js";

let app = null;

export function getAppInstance() {
    return app;
}

async function getSettings() {
    return new Promise((resolve) => {
        chrome.storage.sync.get(["platform", "userId", "projectId", "defaultSymbol", "copyFromId", "copyPercentage", "serverUrl", "minMargin", "defaultSize"], (result) => {
            resolve(result);
        });
    });
}

// CopierCompatibleApp is a class that is compatible with the CopierApp class
// It is used to communicate with the background script
class CopierCompatibleApp {
    constructor(data) {
        this.role = data.role;
        this.platform = data.platform;
    }

    async getStatus() {
        try {
            const statusResponse = await sendCommand("status");
            if (statusResponse.success) {
                return statusResponse.data;
            }
        } catch (error) {
            console.error("[bg_ctrl] failed to get status:", error);
            showError(`Failed to get status: ${error.message}`);
        }
        return { status: STATUS.UNINITIALIZED, platform: this.getSettings().platform, backendStatus: "offline" };
    }

    async getSettings() {
        return await getSettings();
    }
}

export function sendCommand(command, data) {
    console.log(`[bg_ctrl] sending command to background: ${command}`, data);
    return new Promise((resolve, reject) => {
        // Add timeout
        const timeoutId = setTimeout(() => {
            console.error("[bg_ctrl] timeout waiting for response for command: ${command}");
            reject(new Error("Timeout waiting for background response"));
        }, 5000); // 5 second timeout

        chrome.runtime.sendMessage(
            {
                type: command,
                data: data,
            },
            function (response) {
                clearTimeout(timeoutId);

                if (chrome.runtime.lastError) {
                    console.error("[bg_ctrl] chrome runtime error, command:", command, ", error:", chrome.runtime.lastError);
                    reject(new Error(chrome.runtime.lastError.message || "background connection error"));
                    return;
                }

                console.log("[bg_ctrl] received response for ${command}:", response);

                // Handle undefined response or null response
                if (!response) {
                    console.error("[bg_ctrl] no response received for ${command}");
                    reject(new Error("No response from background script"));
                    return;
                }

                if (!response.success) {
                    const message = response.error || response.message || "Unknown error";
                    console.error("[bg_ctrl] error in response for ${command}:", message);
                    reject(new Error(message));
                } else {
                    console.log("[bg_ctrl] success response for ${command}:", response);
                    resolve(response);
                }
            },
        );
    });
}

export function setupAppListener() {
    const port = chrome.runtime.connect({ name: "popup" });

    port.onMessage.addListener(function (msg) {
        switch (msg.type) {
            case "error":
                showError(msg.data);
                break;
            // Add more cases as needed
        }
        console.log("[bg_ctrl] received message from background:", msg);
    });
}

export async function initApp() {
    try {
        const initResponse = await sendCommand("init");
        if (initResponse.success) {
            const settings = getSettings();
            let platform = null;
            switch (settings.platform) {
                case "lighter":
                    platform = new LighterPlatform();
                    break;
                default:
                    break;
            }
            app = new CopierCompatibleApp({
                role: settings.role,
                platform: platform,
            });
            window.app = app;
            console.log("[bg_ctrl] app initialized:", app);
            updateAppStatus();
            return true;
        }
    } catch (error) {
        console.error("[bg_ctrl] failed to init app:", error);
        showError(`Failed to init app: ${error.message}`);
    }
    return false;
}

export async function resumeApp() {
    try {
        const resumeResponse = await sendCommand("resume");
        if (resumeResponse.success) {
            updateAppStatus();
            return true;
        }
    } catch (error) {
        console.error("[bg_ctrl] failed to resume app:", error);
        showError(`Failed to resume app: ${error.message}`);
    }
    return false;
}

export async function stopApp() {
    try {
        await sendCommand("stop");
        updateAppStatus();
    } catch (error) {
        console.error("[bg_ctrl] failed to stop app:", error);
        showError("Failed to stop app: " + error.message);
    }
}

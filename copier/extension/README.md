# Lighter Chrome Extension

A template Chrome extension that provides a basic structure for building browser extensions.

## Features

- Popup interface
- Content script injection
- Background service worker
- Message passing between components
- Basic styling

## Installation

1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right corner
4. Click "Load unpacked" and select the `lighter` directory

## Development

- `manifest.json`: Extension configuration
- `popup.html` & `popup.js`: Extension popup interface
- `content.js`: Content script that runs on web pages
- `background.js`: Background service worker
- `styles.css`: Styling for the popup

## Usage

1. Click the extension icon in your Chrome toolbar
2. Use the popup interface to interact with the extension
3. The content script will run on all web pages
4. Check the console for background script logs

## Customization

- Modify the manifest.json to change permissions and settings
- Update the popup interface in popup.html and popup.js
- Add your content script logic in content.js
- Customize the styling in styles.css

## Note

You'll need to add your own icon files in the following sizes:
- 16x16 pixels (icon16.png)
- 48x48 pixels (icon48.png)
- 128x128 pixels (icon128.png)

Place these in an `icons` directory within the extension folder. 
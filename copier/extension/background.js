console.log("[background] background script starting...", new Date().toISOString());

import { CopierApp, STATUS } from "./app.js";

let app = null;

// Handle sidebar
chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true }).catch((error) => console.error("[background] sidebar error:", error));

console.log("[background] script initialized", new Date().toISOString());

// Message handling
chrome.runtime.onMessage.addListener(async function (request, sender, sendResponse) {
    console.log("[background] received message:", request);
    try {
        switch (request.type) {
            case "init":
                console.log("[background] init requested");
                if (!app) {
                    console.log("[background] creating new app instance");
                    app = new CopierApp();
                    try {
                        console.log("[background] initializing app...");
                        await app.init();
                        await app.run();
                        console.log("[background] app initialized successfully");
                        sendResponse({ success: true, message: "App initialized successfully" });
                    } catch (error) {
                        console.error("[background] app initialization failed:", error);
                        sendResponse({ success: false, message: error.message });
                    }
                } else {
                    console.log("[background] app already initialized");
                    sendResponse({ success: true, message: "App already initialized" });
                }
                break;

            case "test":
                console.log("[background] test requested");
                sendResponse({ success: true, message: "Test successful" });
                break;

            case "resume":
                console.log("[background] start requested");
                if (app) {
                    const status = await app.getStatus();
                    console.log("[background] checking app status:", status);
                    if (status.status == STATUS.RUNNING) {
                        console.log("[background] app already running");
                        sendResponse({ success: true, message: "App already running", data: status });
                    } else {
                        console.log("[background] resume app...");
                        // Start the app in a non-blocking way
                        await app.resume();
                        const updatedStatus = await app.getStatus();
                        console.log("[background] app resumed with status:", updatedStatus);
                        sendResponse({ success: true, message: "App started", data: updatedStatus });
                    }
                } else {
                    console.log("[background] no app instance, init it first");
                    sendResponse({ success: false, message: "No app instance, init it first", data: null });
                }
                break;

            case "stop":
                console.log("[background] stop requested");
                if (app && app.status == STATUS.RUNNING) {
                    console.log("[background] stopping app...");
                    await app.stop();
                    console.log("[background] app stopped");
                    sendResponse({ success: true, message: "App stopped" });
                } else {
                    console.log("[background] app not running, nothing to stop");
                    sendResponse({ success: true, message: "App not running" });
                }
                break;

            case "status":
                console.log("[background] status requested");
                if (app) {
                    const status = await app.getStatus();
                    console.log("[background] current status:", status);
                    sendResponse({ success: true, message: "Status retrieved", data: status });
                } else {
                    console.log("[background] no app instance, returning not running status");
                    sendResponse({ success: true, message: "No app instance", data: { status: STATUS.UNINITIALIZED, platform: "" } });
                }
                break;

            default:
                console.warn("[background] unknown message type:", request.type);
                sendResponse({ success: false, error: "Unknown message type" });
        }
    } catch (error) {
        console.error("[background] error handling message:", error);
        sendResponse({ success: false, error: error.message });
    }

    console.log("[background] message handling complete for:", request.type);
    return true;
});

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "navigateTo") {
        // Get the current tab
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs[0]) {
                // Update the tab's URL
                chrome.tabs.update(tabs[0].id, { url: request.url });
            }
        });
    }
});

// Log that we're ready to receive messages
console.log("[background] message listener registered successfully", new Date().toISOString());

// Add heartbeat log to confirm background script is still running
setInterval(() => {
    console.log("[background] heartbeat check - script running", new Date().toISOString());
    if (app) {
        console.log("[background] app status:", app.isInitialized ? "initialized" : "not initialized");
    } else {
        console.log("[background] no app instance");
    }
}, 60000); // Log every minute

chrome.runtime.onInstalled.addListener(() => {
    console.log("[background] extension installed");
});

chrome.runtime.onStartup.addListener(() => {
    console.log("[background] extension started");
});

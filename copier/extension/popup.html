<!doctype html>
<html>
    <head>
        <title>Copier Extension</title>
        <link rel="stylesheet" type="text/css" href="styles.css" />
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 class="app-title">Trade Copier</h1>
                <div id="errorMessage" class="error-message" style="display: none"></div>
            </div>
            <nav class="nav-tabs">
                <button class="nav-tab active" data-tab="positions">Status</button>
                <button class="nav-tab" data-tab="order">Order</button>
                <button class="nav-tab" data-tab="settings">Settings</button>
            </nav>

            <div id="positions" class="tab-content active">
                <div class="positions-header">
                    <h2>Status</h2>
                    <button id="refreshPositions" class="refresh-button">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        >
                            <path d="M23 4v6h-6"></path>
                            <path d="M1 20v-6h6"></path>
                            <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                        </svg>
                        Refresh
                    </button>
                </div>
                <div class="status-info">
                    <div class="status-item symbol-item">
                        <span class="status-label">Symbol</span>
                        <span class="status-value" id="statusSymbol"></span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">User ID:</span>
                        <span class="status-value" id="statusUserId"></span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Last Price:</span>
                        <span class="status-value" id="statusLastPrice"></span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Total Margin:</span>
                        <span class="status-value" id="statusTotalMargin"></span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Available Margin:</span>
                        <span class="status-value" id="statusAvailableMargin"></span>
                    </div>
                    <div class="status-item manual-mode-item">
                        <span class="status-label">Mode</span>
                        <span class="status-value">
                            <span class="auto-badge" id="modeBadge" style="display: none">AUTO</span>
                            <span class="debug-badge" id="debugBadge" style="display: none">DEBUG</span>
                        </span>
                    </div>
                </div>
                <div class="positions-list">
                    <!-- Positions will be inserted here -->
                </div>
            </div>

            <div id="order" class="tab-content">
                <div class="content">
                    <form id="orderForm">
                        <div class="form-group">
                            <label for="symbol">Symbol:</label>
                            <input type="text" id="symbol" readonly />
                        </div>

                        <div class="form-group">
                            <label>Position:</label>
                            <div class="toggle-switch">
                                <input type="radio" id="long" name="position" value="long" checked />
                                <label for="long">Long</label>
                                <input type="radio" id="short" name="position" value="short" />
                                <label for="short">Short</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="price">Limit Price:</label>
                            <div class="input-with-checkbox">
                                <input type="number" id="price" step="0.01" required />
                                <label class="checkbox-label">
                                    <input type="checkbox" id="marketCheckbox" tabindex="-1" />
                                    <span class="checkbox-text">Market</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="size">Size:</label>
                            <div class="input-with-unit">
                                <input type="number" id="size" step="0.001" required />
                                <span class="unit-label" id="sizeUnit"></span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="reduceOnly" />
                                <span class="checkbox-text">Reduce Only</span>
                            </label>
                        </div>

                        <button type="submit" id="submitOrder">Place Order</button>
                        <button type="button" id="cancelAllOrders" class="cancel-order-button">Cancel All Orders</button>
                        <button type="button" id="closeAllPositions" class="cancel-order-button">Close All Positions</button>
                        <button type="button" id="setStopLoss" class="cancel-order-button">Set Stoploss</button>
                        <button type="button" id="updateStats" class="cancel-order-button">Update Stats</button>
                        <button type="button" id="withdrawTest" class="cancel-order-button">Withdraw 10 USDC</button>
                        <button type="button" id="reloadExtension" class="cancel-order-button">Reload Extension</button>
                    </form>
                </div>
            </div>

            <div id="settings" class="tab-content">
                <div class="content">
                    <form id="settingsForm">
                        <div class="form-group">
                            <label for="platform">Platform:</label>
                            <select id="platform" name="platform" required>
                                <option value="lighter">Lighter</option>
                                <option value="aster">Aster</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="serverUrl">Server URL:</label>
                            <input type="text" id="serverUrl" name="serverUrl" required />
                        </div>

                        
                        <div class="form-group">
                            <label for="projectId">Project ID:</label>
                            <input type="text" id="projectId" name="projectId" required />
                        </div>

                        <div class="form-group">
                            <label for="alias">Alias (Optional):</label>
                            <input type="text" id="alias" name="alias" />
                            <small class="form-text">Optional display name for this user</small>
                        </div>

                        <div class="form-group">
                            <label for="userId">User ID:</label>
                            <div class="input-with-button">
                                <input type="text" id="userId" name="userId" required />
                                <!-- <button type="button" id="generateId" class="generate-id-button">Generate ID</button> -->
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="copyFromId">Copy From ID:</label>
                            <input type="text" id="copyFromId" name="copyFromId" />
                        </div>

                        <div class="form-group">
                            <label for="copyPercentage">Copy Percentage:</label>
                            <input type="number" id="copyPercentage" name="copyPercentage" min="0" max="100" value="0" required />
                        </div>

                        <div class="form-group">
                            <label for="defaultSymbol">Symbol:</label>
                            <input type="text" id="defaultSymbol" name="defaultSymbol" value="ETH" required />
                        </div>

                        <div class="form-group master-only">
                            <label for="defaultSize">Default Size:</label>
                            <input type="number" id="defaultSize" name="defaultSize" step="any" value="0.1" required />
                        </div>

                        <div class="form-group master-only">
                            <label for="longRatio">Long Ratio:</label>
                            <input type="number" id="longRatio" name="longRatio" min="0" max="1" step="0.01" value="0.5" required />
                            <small class="form-text">Probability of opening long positions (0-1)</small>
                        </div>

                        <div class="form-group">
                            <label for="minMargin">Minimum Margin:</label>
                            <input type="number" id="minMargin" name="minMargin" value="50" required />
                        </div>

                        <div class="form-group master-only" style="display: none;">
                            <label for="stopLossBuffer">Stop Loss Buffer:</label>
                            <input type="number" id="stopLossBuffer" name="stopLossBuffer" min="0" max="0.05" step="0.0001" value="0.01" required />
                            <small class="form-text">Range: 0 to 0.05 (0% to 5% of liquidation price, 0 means no stop loss)</small>
                        </div>

                        <div class="form-group master-only">
                            <label for="coolingHour">Cooling (hours):</label>
                            <input type="number" id="coolingHour" name="coolingHour" min="-0.5" max="48.0" step="0.01" value="1.0" required />
                            <small class="form-text">Range: -0.5 to 0 (exact hour mode) or 0.01 to 48.0 (interval mode)</small>
                        </div>

                        <div class="form-group">
                            <label for="refreshInterval">Refresh Interval (minutes):</label>
                            <input type="number" id="refreshInterval" name="refreshInterval" min="0" max="120" step="1" value="30" required />
                            <small class="form-text">Range: 0-120 minutes (0 = no refresh)</small>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="debug" name="debug" />
                                <span class="checkbox-text">Debug Mode</span>
                            </label>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="manualMode" name="manualMode" />
                                <span class="checkbox-text">Manual Mode</span>
                            </label>
                        </div>

                        <button type="submit" id="saveSettings">Save Settings</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="footer">
            <div id="statusDisplay" class="status-display"></div>
            <button id="startAppRetryButton" class="retry-button" style="display: none">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M23 4v6h-6"></path>
                    <path d="M1 20v-6h6"></path>
                    <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                </svg>
                Retry
            </button>
        </div>

        <script type="module" src="models/position.js"></script>
        <script type="module" src="platforms/lighter/lighter.js"></script>
        <script type="module" src="common.js"></script>
        <script type="module" src="order.js"></script>
        <script type="module" src="positions.js"></script>
        <script type="module" src="settings.js"></script>
        <script type="module" src="popup.js"></script>
    </body>
</html>

import { CopierApp } from "./app.js";

let app = null;

export function getAppInstance() {
    return app;
}

export function sendCommand(command, data) {
    console.log(`[fg_ctrl] sending command to foreground: ${command}`, data);
    return new Promise((resolve, reject) => {
        resolve({ success: true, message: "Test successful" });
    });
}

export function setupAppListener() {
    console.log("[fg_ctrl] setting up app listener");
}

export async function initApp() {
    if (app) {
        console.log("[fg_ctrl] app is already running");
        window.app = app;
        return;
    }
    console.log("[fg_ctrl] initializing app");
    app = new CopierApp();
    window.app = app;
    await app.init();
    await app.run();
    console.log("[fg_ctrl] app initialized:", app);
}

export async function resumeApp() {
    if (!app) {
        console.log("[fg_ctrl] app is not initialized");
        return;
    }
    await app.resume(true);
    console.log("[fg_ctrl] app resumed:", app);
}

export async function stopApp() {
    console.log("[fg_ctrl] stopping app");
    if (app) {
        await app.stop(true);
        console.log("[fg_ctrl] app stopped:", app);
    } else {
        console.log("[fg_ctrl] app is not running");
    }
}


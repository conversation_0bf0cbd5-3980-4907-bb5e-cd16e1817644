// Low-level HTTP request functions
export let BASE_URL = "http://localhost:8080";

async function makeRequest(baseUrl, method, endpoint, data = null, headers = {}) {
    if (!baseUrl) {
        baseUrl = BASE_URL;
    }
    const url = `${baseUrl}${endpoint}`;
    const options = {
        method,
        headers: {
            "Content-Type": "application/json",
            ...headers,
        },
    };

    if (data) {
        options.body = JSON.stringify(data);
    }

    try {
        const response = await fetch(url, options);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error("[request] Request failed:", error);
        throw error;
    }
}

// Health check
export async function httpCheckHealth(baseUrl, projectId) {
    const endpoint = "/health?projectID=" + projectId;
    return makeRequest(baseUrl, "GET", endpoint);
}

// Update an account
export async function httpUpdateAgent(baseUrl, agent) {
    const projectId = agent.projectId;
    const endpoint = `/update/${projectId}`;
    console.log("[request] updating agent:", endpoint, agent);
    return makeRequest(baseUrl, "POST", endpoint, agent);
}

export async function httpGetProject(baseUrl, projectId) {
    const endpoint = `/project/${projectId}`;
    console.log("[request] getting project:", endpoint);
    return makeRequest(baseUrl, "GET", endpoint);
}

export async function httpUpdateStatus(baseUrl, projectIds, status, comment) {
    return makeRequest(baseUrl, 'POST', '/update-status', {
        projectIds,
        status,
        comment
    });
}

export async function httpSay(baseUrl, message) {
    const endpoint = `/say?message=${message}`;
    console.log("[request] saying:", endpoint);    
    return makeRequest(baseUrl, "GET", endpoint);
}

export async function httpGetExtensionWithdraw(baseUrl, projectId, userId) {
    const endpoint = `/balancer/extension/withdraw?project_id=${projectId}&user_id=${userId}`;
    console.log("[request] getting extension withdraw:", endpoint);
    return makeRequest(baseUrl, "GET", endpoint);
}

export async function httpMarkExtensionWithdrawDone(baseUrl, projectId, txRefId) {
    const endpoint = `/balancer/extension/withdraw/done?projectId=${projectId}&txRefId=${txRefId}`;
    console.log("[request] marking extension withdraw done:", endpoint);
    return makeRequest(baseUrl, "GET", endpoint);
}

export async function httpGetProjectRisks(baseUrl, projectId) {
    const endpoint = `/risks/${projectId}`;
    console.log("[request] getting project risks:", endpoint);
    return makeRequest(baseUrl, "GET", endpoint);
}

export async function httpFinishServerRequest(baseUrl, requestId, status, comment) {
    const endpoint = `/server-request/finish`;
    console.log("[request] finishing server request:", endpoint, requestId, status, comment);
    return makeRequest(baseUrl, "POST", endpoint, { requestId, status, comment });
}

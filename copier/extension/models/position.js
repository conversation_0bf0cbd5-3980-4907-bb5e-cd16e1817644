// Position class definition
class Position {
    constructor(data) {
        this.symbol = data.symbol;
        this.side = data.side;
        this.size = data.size;
        this.entryPrice = data.entryPrice;
        this.markPrice = data.markPrice;
        this.liquidationPrice = data.liquidationPrice;
        this.margin = data.margin;
        this.pnl = data.pnl;
        this.pnlPercentage = data.pnlPercentage;
        this.createTime = data.createTime;
    }

    calculatePnL() {
        const multiplier = this.side === "long" ? 1 : -1;
        return multiplier * (this.markPrice - this.entryPrice) * this.size;
    }

    calculatePnLPercentage() {
        return (this.pnl / this.margin) * 100;
    }
}

import { Position } from "./position.js";

export class Platform {
    constructor() {
        if (this.constructor === Platform) {
            throw new Error("Platform is an abstract class and cannot be instantiated directly");
        }
    }

    async getPositions() {
        throw new Error("Method getPositions() must be implemented");
    }

    async getMasterPositions() {
        throw new Error("Method getMasterPositions() must be implemented");
    }

    async getLastPrice(symbol) {
        throw new Error("Method getLastPrice() must be implemented");
    }

    async getMargin() {
        throw new Error("Method getMargin() must be implemented");
    }

    async createOrder(order) {
        throw new Error("Method createOrder() must be implemented");
    }

    async cancelOrder(orderId) {
        throw new Error("Method cancelOrder() must be implemented");
    }
}

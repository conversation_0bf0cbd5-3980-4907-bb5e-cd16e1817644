// Types matching backend
export const Role = {
    Master: "master",
    Copier: "copier",
};

export const PlatformType = {
    Lighter: "lighter",
};

export const PositionSide = {
    Long: "long",
    Short: "short",
};

export const OrderStatus = {
    Pending: "pending",
    Filled: "filled",
    Cancelled: "cancelled",
};

export const TriggerDirection = {
    Above: "above",
    Below: "below",
};

// Re-export Position from position.js
export { Position } from "./position.js";

export class Order {
    constructor({ symbol, side, qty, size, price, triggerPrice, triggerDirection, status, createTime, expireTime }) {
        this.symbol = symbol;
        this.side = side;
        this.qty = qty;
        this.size = size;
        this.price = price;
        this.triggerPrice = triggerPrice;
        this.triggerDirection = triggerDirection;
        this.status = status;
        this.createTime = createTime;
        this.expireTime = expireTime;
    }
}

export class Account {
    constructor({ role, platform, projectId, userId, symbol, copyFromUserId, copyPercentage, lastPrice, spread, totalMargin, availableMargin, positions, openOrders, createTime }) {
        this.role = role;
        this.platform = platform;
        this.projectId = projectId;
        this.userId = userId;
        this.symbol = symbol;
        this.copyFromUserId = copyFromUserId;
        this.copyPercentage = copyPercentage;
        this.lastPrice = lastPrice;
        this.spread = spread;
        this.totalMargin = totalMargin;
        this.availableMargin = availableMargin;
        this.positions = positions || [];
        this.openOrders = openOrders || [];
        this.createTime = createTime || new Date();
    }
}

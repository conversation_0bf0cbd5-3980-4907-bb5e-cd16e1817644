import { updateOrderSymbol } from "./common.js";

// Event handler for settings form submission
function handleSettingsSubmit(e) {
    console.log("[settings] handle settings submit");
    e.preventDefault();

    // Validate cooling hour range
    const coolingHour = parseFloat(document.getElementById("coolingHour").value);
    if (isNaN(coolingHour)) {
        alert("Cooling must be a valid number");
        return;
    }
    if (coolingHour < 0) {
        // For exact hour mode, only allow -0.5 to 0
        if (coolingHour < -0.5) {
            alert("For exact hour mode, cooling must be between -0.5 and 0");
            return;
        }
    } else {
        // For interval mode, allow 0.01 to 48.0
        if (coolingHour < 0.01 || coolingHour > 48.0) {
            alert("For interval mode, cooling must be between 0.01 and 48.0 hours");
            return;
        }
    }

    // Validate refresh interval range
    const refreshInterval = parseInt(document.getElementById("refreshInterval").value);
    if (isNaN(refreshInterval) || refreshInterval < 0 || refreshInterval > 120) {
        alert("Refresh interval must be between 0 and 120 minutes (0 = no refresh)");
        return;
    }

    const settings = {
        platform: document.getElementById("platform").value.trim(),
        userId: document.getElementById("userId").value.trim(),
        projectId: document.getElementById("projectId").value.trim(),
        defaultSymbol: document.getElementById("defaultSymbol").value.trim(),
        copyFromId: document.getElementById("copyFromId").value.trim(),
        copyPercentage: parseInt(document.getElementById("copyPercentage").value),
        serverUrl: document.getElementById("serverUrl").value.trim(),
        defaultSize: parseFloat(document.getElementById("defaultSize").value),
        minMargin: parseInt(document.getElementById("minMargin").value),
        coolingHour: coolingHour,
        refreshInterval: refreshInterval,
        debug: document.getElementById("debug").checked,
        manualMode: document.getElementById("manualMode").checked,
        alias: document.getElementById("alias").value.trim(),
        stopLossBuffer: parseFloat(document.getElementById("stopLossBuffer").value),
        longRatio: parseFloat(document.getElementById("longRatio").value),
        version: Date.now(),
    };

    console.log("[settings] submit settings:", settings);

    // Save settings to chrome.storage
    chrome.storage.sync.set(settings, function () {
        console.log("[settings] settings saved:", settings);
        // Show success message
        const saveButton = document.getElementById("saveSettings");
        const originalText = saveButton.textContent;
        saveButton.textContent = "Saved!";
        saveButton.disabled = true;

        // Update the symbol in the order form
        updateOrderSymbol();

        // Reset button after 2 seconds
        setTimeout(() => {
            saveButton.textContent = originalText;
            saveButton.disabled = false;
        }, 2000);
    });
}

// Initialize settings tab
export function initializeSettingsTab() {
    // Load saved settings
    chrome.storage.sync.get(["platform", "userId", "projectId", "defaultSymbol", "copyFromId", "copyPercentage", "serverUrl", "defaultSize", "minMargin", "coolingHour", "refreshInterval", "debug", "manualMode", "alias", "stopLossBuffer", "longRatio"], (result) => {
        console.log("[settings] settings loaded:", result);
        document.getElementById("platform").value = result.platform || "lighter";
        document.getElementById("userId").value = result.userId || "";
        document.getElementById("projectId").value = result.projectId || "";
        document.getElementById("defaultSymbol").value = result.defaultSymbol || "";
        document.getElementById("copyFromId").value = result.copyFromId || "";
        document.getElementById("copyPercentage").value = result.copyPercentage || 0;
        document.getElementById("serverUrl").value = result.serverUrl || "http://127.0.0.1:8080";
        document.getElementById("defaultSize").value = result.defaultSize || 0.1;
        document.getElementById("minMargin").value = result.minMargin || 1000;
        document.getElementById("coolingHour").value = result.coolingHour || 1.0;
        document.getElementById("refreshInterval").value = result.refreshInterval || 30;
        document.getElementById("debug").checked = result.debug || false;
        document.getElementById("manualMode").checked = result.manualMode || false;
        document.getElementById("alias").value = result.alias || "";
        document.getElementById("stopLossBuffer").value = result.stopLossBuffer || 0;
        document.getElementById("longRatio").value = result.longRatio || 0.5;
        
        // Show/hide master-only fields based on copyFromId
        const masterOnlyFields = document.querySelectorAll(".master-only");
        const isMaster = !result.copyFromId;
        masterOnlyFields.forEach(field => {
            field.style.display = isMaster ? "block" : "none";
        });
    });

    // Settings form submission
    document.getElementById("settingsForm").addEventListener("submit", handleSettingsSubmit);
}

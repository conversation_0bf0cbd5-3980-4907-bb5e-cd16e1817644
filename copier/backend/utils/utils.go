package utils

import (
	"chaintools/utils/ulog"
	"fmt"
	"io"
	"math"
	"math/rand"
	"os"
	"strings"
	"time"

	"github.com/avast/retry-go"
	"github.com/stevedomin/termtable"
)

// 时间转换为北京时间字符串
func FormatToBeijingTimeStr(t time.Time) string {
	// loc, _ := time.LoadLocation("Asia/Shanghai")
	return t.In(time.FixedZone("CST", 8*60*60)).Format(time.RFC3339)
}

func FormatShortTimeStr(t *time.Time, withYear bool) string {
	if t == nil {
		return "-"
	}
	if t.Before(time.Unix(86400, 0)) {
		return "-"
	} else {
		if withYear {
			return t.In(time.FixedZone("CST", 8*60*60)).Format("06-01-02 15:04:05")
		}
		return t.In(time.FixedZone("CST", 8*60*60)).Format("01-02 15:04:05")
	}
}

func NewRandomID() string {
	rand.Seed(time.Now().UnixNano())
	randStr := "1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ"
	var letterRunes = []rune(randStr)

	b := make([]rune, 6)
	for i := range b {
		b[i] = letterRunes[rand.Intn(len(letterRunes))]
	}
	return string(b)
}

// CopyFile copies a file from src to dst.
// If dst exists, it will be overwritten.
func CopyFile(src, dst string) error {
	// Open source file
	sourceFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("failed to open source file: %w", err)
	}
	defer sourceFile.Close()

	// Create destination file
	destFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("failed to create destination file: %w", err)
	}
	defer destFile.Close()

	// Copy the contents
	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return fmt.Errorf("failed to copy file contents: %w", err)
	}

	// Sync to ensure write is complete
	err = destFile.Sync()
	if err != nil {
		return fmt.Errorf("failed to sync destination file: %w", err)
	}

	return nil
}

func IsAllSame(arr []string) bool {
	if len(arr) == 0 {
		return true
	}

	for i := 1; i < len(arr); i++ {
		if arr[i] != arr[0] {
			return false
		}
	}
	return true
}

func NewTable() *termtable.Table {
	t := termtable.NewTable(nil, &termtable.TableOptions{
		Padding:      2,
		UseSeparator: true,
	})
	return t
}

func Now() *time.Time {
	t := time.Now().In(time.FixedZone("CST", 8*60*60))
	return &t
}

// reverse order of a slice of T
func Reverse[T any](arr []T) []T {
	newSlice := make([]T, len(arr))
	for i := 0; i < len(arr); i++ {
		newSlice[i] = arr[len(arr)-i-1]
	}
	return newSlice
}

func Unique[T comparable](slice []T) []T {
	uniqueMap := make(map[T]bool)
	for _, item := range slice {
		uniqueMap[item] = true
	}
	uniqueSlice := make([]T, 0, len(uniqueMap))
	for item := range uniqueMap {
		uniqueSlice = append(uniqueSlice, item)
	}
	return uniqueSlice
}

// SplitAndTrim splits input separated by a comma
// and trims excessive white space from the substrings.
func SplitAndTrim(input string, sep string) (ret []string) {
	l := strings.Split(input, sep)
	for _, r := range l {
		if r = strings.TrimSpace(r); r != "" {
			ret = append(ret, r)
		}
	}
	return ret
}

func Round(f float64, n int, roundUp bool) float64 {
	pow := math.Pow10(n)
	if roundUp {
		return math.Ceil(f*pow) / pow
	}
	return math.Floor(f*pow) / pow
}

// Filter returns a new slice containing only the elements for which the predicate returns true
func Filter[T any](slice []T, predicate func(T) bool) []T {
	result := make([]T, 0)
	for _, item := range slice {
		if predicate(item) {
			result = append(result, item)
		}
	}
	return result
}

// Map applies a function to each element of a slice and returns a new slice with the results
func Map[T any, R any](slice []T, mapper func(T) R) []R {
	result := make([]R, len(slice))
	for i, item := range slice {
		result[i] = mapper(item)
	}
	return result
}

func GetBeijingTimezone() *time.Location {
	// Try to load the "Asia/Shanghai" location first.
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		// If loading fails (e.g., on Windows), fall back to a fixed UTC+8 timezone.
		return time.FixedZone("CST", 8*60*60)
	}
	return location
}

func roundToNthPlace(num float64, n int) float64 {
	if n > 0 {
		p := math.Pow10(n) // computes 10^n
		return math.Round(num/p) * p
	} else {
		p := math.Pow10(n) // computes 10^n
		return math.Round(num*p) / p
	}
}

func RetryDo(do func() error, maxTry int, sleep int) error {
	return retry.Do(do, retry.Attempts(uint(maxTry)), retry.Delay(time.Second*time.Duration(sleep)), retry.OnRetry(func(n uint, err error) {
		ulog.Errorf("retrying %d, error: %v", n, err)
	}))
}

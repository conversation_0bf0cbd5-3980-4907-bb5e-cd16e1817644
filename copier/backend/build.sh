#!/bin/bash

PROOT=$(dirname $0)/../../pgate
export PROOT

go generate $PROOT/license/time_lock_license.go

# Cross compile for Windows
GOOS=windows GOARCH=amd64 go build -ldflags="-s -w -X main.buildTime=$(date +%Y-%m-%d.%H:%M:%S) -X main.commitHash=$(git rev-parse --short HEAD)"  -o copier_server.exe
# build for mac 
GOOS=darwin GOARCH=arm64 go build -ldflags="-s -w -X main.buildTime=$(date +%Y-%m-%d.%H:%M:%S) -X main.commitHash=$(git rev-parse --short HEAD)"  -o copier_server

echo "backend build done"
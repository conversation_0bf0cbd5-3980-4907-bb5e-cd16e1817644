package main

import (
	"copier/backend/copier"
	"copier/backend/logger"
	"flag"
	"fmt"
	"log"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
	"time"
)

var (
	buildTime  string // 编译时间
	commitHash string // git commit hash
)

func main() {
	// add a flag to specify the data directory
	dataDir := flag.String("dataDir", "data", "the directory to store the data")
	port := flag.Int("port", 8080, "the port to listen on")
	debug := flag.Bool("debug", false, "enable debug mode (overrides yaml setting)")

	// Add multiproxy flag
	mappings := flag.String("mappings", "", "Comma-separated list of port mappings (e.g., '9001:************:8080,9002:************:8080'). If provided, runs as multiproxy.")
	watchdog := flag.Bool("watchdog", false, "enable watchdog mode, check projects abnormal status")

	flag.Parse()

	// If mappings are provided, run as multiproxy
	if *mappings != "" {
		if *watchdog {
			logger.Infof(">>> watchdog enabled")
			go func() {
				proxyMappings := parseMappings(*mappings)
				watchdog := copier.NewWatchdog(proxyMappings, debug)
				watchdog.Run()
			}()
		}
		runMultiProxy(*mappings)
		return
	}

	// Otherwise, run as normal copier
	server, err := copier.NewServer(buildTime, commitHash, *dataDir, *port, debug)
	if err != nil {
		panic(err)
	}
	server.Run()

}

func runMultiProxy(mappingsStr string) {
	// Parse the mappings
	proxyMappings := parseMappings(mappingsStr)

	// Start proxy servers for each mapping
	for _, mapping := range proxyMappings {
		go startProxyServer(mapping)
	}

	// Keep the main goroutine running
	select {}
}

func parseMappings(mappingsStr string) []copier.ProxyMapping {
	var mappings []copier.ProxyMapping
	pairs := strings.Split(mappingsStr, ",")

	for _, pair := range pairs {
		parts := strings.Split(pair, ":")
		if len(parts) != 3 {
			log.Fatalf("invalid mapping format: %s. Expected format: localPort:targetIP:targetPort", pair)
		}
		mappings = append(mappings, copier.ProxyMapping{
			LocalPort:  parts[0],
			TargetAddr: fmt.Sprintf("%s:%s", parts[1], parts[2]),
		})
	}

	return mappings
}

func startProxyServer(mapping copier.ProxyMapping) {
	targetURL, err := url.Parse(fmt.Sprintf("http://%s", mapping.TargetAddr))
	if err != nil {
		log.Fatalf("failed to parse target URL: %v", err)
	}

	proxy := httputil.NewSingleHostReverseProxy(targetURL)

	// Customize the proxy director to preserve the original request
	originalDirector := proxy.Director
	proxy.Director = func(req *http.Request) {
		originalDirector(req)
		req.Host = targetURL.Host
		log.Printf("proxying request to %s: %s %s", mapping.TargetAddr, req.Method, req.URL.Path)
	}

	// Add error handling
	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		log.Printf("proxy error for %s: %v", mapping.TargetAddr, err)
		http.Error(w, fmt.Sprintf("proxy error: %v", err), http.StatusBadGateway)
	}

	// Add transport with timeout
	proxy.Transport = &http.Transport{
		MaxIdleConns:          100,
		IdleConnTimeout:       1200 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 5 * time.Second,
		ResponseHeaderTimeout: 1200 * time.Second,
	}

	server := &http.Server{
		Addr:    ":" + mapping.LocalPort,
		Handler: proxy,
	}

	log.Printf("starting proxy server on port %s -> %s", mapping.LocalPort, mapping.TargetAddr)
	if err := server.ListenAndServe(); err != nil {
		log.Fatalf("failed to start proxy server: %v", err)
	}
}

package rate

import (
	"sync"
	"time"
)

type DurationLimiter struct {
	events   []time.Time
	Limit    int
	Duration time.Duration
	mu       sync.Mutex
}

// 限制在 duration 内最多 limit 次事件，如果不允许(allow)，返回需要等待的时间
// 如果 resetOnAllow 为 true，则每次允许时重置计数器
func NewDurationLimiter(limit int, duration time.Duration) *DurationLimiter {
	if limit <= 0 || duration <= 0 {
		panic("limit and duration must be greater than 0")
	}
	return &DurationLimiter{
		events:   make([]time.Time, 0),
		Limit:    limit,
		Duration: duration,
	}
}

// 记录当前事件，并返回是否允许当前操作
func (this *DurationLimiter) AllowTime(tm time.Time) (waitTime time.Duration, ok bool) {
	return this._allow(tm)
}

// 记录当前事件，并返回是否允许当前操作
func (this *DurationLimiter) Allow() (waitTime time.Duration, ok bool) {
	return this._allow(time.Now())
}

func (this *DurationLimiter) _allow(tm time.Time) (waitTime time.Duration, ok bool) {
	this.mu.Lock()
	defer this.mu.Unlock()

	// Add new event
	this.events = append(this.events, tm)

	// Trim old events that are outside the duration window
	cutoff := tm.Add(-this.Duration)
	for len(this.events) > 0 && this.events[0].Before(cutoff) {
		this.events = this.events[1:]
	}

	// Check if we're over the limit
	if len(this.events) > this.Limit {
		// Remove oldest event to stay within limit
		this.events = this.events[1:]
		// Calculate wait time based on oldest remaining event
		oldest := this.events[0]
		waitTime = this.Duration - tm.Sub(oldest)
		return waitTime, false
	}

	return 0, true
}

// 获取当前计数
func (this *DurationLimiter) GetCount() int {
	this.mu.Lock()
	defer this.mu.Unlock()
	return len(this.events)
}

// 获取 events
func (this *DurationLimiter) GetEvents() []time.Time {
	return this.events
}

func (this *DurationLimiter) Reset() {
	this.mu.Lock()
	defer this.mu.Unlock()
	this.events = make([]time.Time, 0)
}

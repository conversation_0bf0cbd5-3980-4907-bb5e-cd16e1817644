package rate

import (
	"sync"
	"time"
)

type OnceIntervalLimiter struct {
	events    []time.Time
	Duration  time.Duration
	HardLimit int
	mu        sync.Mutex
	lastAllow time.Time
}

// 每隔 duration 允许(allow)一次操作，hardLimit 是允许的最大事件数量
// 如果 hardLimit 为 0，则 hardLimit 为 1000
// 不允许时，返回从上次允许到现在的事件个数
// 主要用在对 slack sendMsg 做限速处理
func NewOnceIntervalLimiter(interval time.Duration, hardLimit int) *OnceIntervalLimiter {
	if interval <= 0 {
		panic("duration must be greater than 0")
	}
	// 用于保证清理 events slice
	if hardLimit <= 0 {
		hardLimit = 1000
	}
	return &OnceIntervalLimiter{
		events:    make([]time.Time, 0),
		Duration:  interval,
		HardLimit: hardLimit,
	}
}

// duration 内发生 n 次事件，只要上次允许的事件到当前时间不超过 duration，都可以继续添加 event，但是 notAllow
// 因此有 n 个 notAllow，然后再一次 Allow

func (this *OnceIntervalLimiter) AllowTime(tm time.Time) (count int, allow bool) {
	return this._allow(tm)
}

func (this *OnceIntervalLimiter) Allow() (count int, allow bool) {
	return this._allow(time.Now())
}

func (this *OnceIntervalLimiter) _allow(tm time.Time) (count int, allow bool) {
	if tm.IsZero() {
		tm = time.Now()
	}
	this.mu.Lock()
	defer func() {
		this.mu.Unlock()
		if allow {
			this.lastAllow = tm
		}
	}()

	this.events = append(this.events, tm)
	count = 0
	// 初始化的情况
	if this.lastAllow.IsZero() {
		allow = true
		count = 1
		return
	}
	length := len(this.events)
	// 如果事件数量超过 hard limit，限制事件数量
	if length > this.HardLimit {
		this.events = this.events[length-this.HardLimit:]
	}
	// 统计从上次 allow 到现在的事件数量
	for _, event := range this.events {
		if event.After(this.lastAllow) {
			count += 1
		}
	}
	// 只要上次 allow 的事件离现在还不到 duration，都可以继续添加 event，但是 allow 为 false
	if tm.Sub(this.lastAllow) < this.Duration {
		allow = false
		return
	}
	return count, true
}

// 获取当前计数
func (this *OnceIntervalLimiter) GetCount() int {
	this.mu.Lock()
	defer this.mu.Unlock()
	return len(this.events)
}

// 获取 events
func (this *OnceIntervalLimiter) GetEvents() []time.Time {
	return this.events
}

func (this *OnceIntervalLimiter) Reset() {
	this.mu.Lock()
	defer this.mu.Unlock()
	this.events = make([]time.Time, 0)
}

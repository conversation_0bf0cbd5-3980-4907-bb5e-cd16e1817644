package rate

import (
	"testing"
	"time"
)

func TestNewDurationLimiter(t *testing.T) {
	// Test valid parameters
	limiter := NewDurationLimiter(5, time.Second)
	if limiter == nil {
		t.<PERSON>r("Expected non-nil limiter")
	}
	if limiter.Limit != 5 {
		t.<PERSON><PERSON>("Expected limit 5, got %d", limiter.Limit)
	}
	if limiter.Duration != time.Second {
		t.<PERSON>rf("Expected duration 1s, got %v", limiter.Duration)
	}

	// Test invalid parameters
	defer func() {
		if r := recover(); r == nil {
			t.<PERSON>rror("Expected panic for invalid parameters")
		}
	}()
	NewDurationLimiter(0, time.Second)
}

func TestDurationLimiter_Allow(t *testing.T) {
	limiter := NewDurationLimiter(3, time.Second)

	// Test initial allows
	for i := 0; i < 3; i++ {
		_, ok := limiter.Allow()
		if !ok {
			t.Errorf("Expected allow on attempt %d", i+1)
		}
	}

	// Test rate limit
	_, ok := limiter.Allow()
	if ok {
		t.Error("Expected rate limit to be exceeded")
	}

	// Test with custom time
	now := time.Now()
	limiter.Reset()
	limiter.AllowTime(now)
	limiter.AllowTime(now.Add(500 * time.Millisecond))
	limiter.AllowTime(now.Add(800 * time.Millisecond))

	// Should be rate limited
	_, ok = limiter.AllowTime(now.Add(900 * time.Millisecond))
	if ok {
		t.Error("Expected rate limit to be exceeded")
	}
}

func TestDurationLimiter_GetCount(t *testing.T) {
	limiter := NewDurationLimiter(3, time.Second)

	// Test empty count
	if count := limiter.GetCount(); count != 0 {
		t.Errorf("Expected count 0, got %d", count)
	}

	// Test after adding events
	now := time.Now()
	limiter.AllowTime(now)
	limiter.AllowTime(now.Add(100 * time.Millisecond))

	if count := limiter.GetCount(); count != 2 {
		t.Errorf("Expected count 2, got %d", count)
	}

	// Test after exceeding limit
	limiter.AllowTime(now.Add(200 * time.Millisecond))
	limiter.AllowTime(now.Add(300 * time.Millisecond))

	if count := limiter.GetCount(); count != 3 {
		t.Errorf("Expected count 3, got %d", count)
	}
}

func TestDurationLimiter_Reset(t *testing.T) {
	limiter := NewDurationLimiter(3, time.Second)

	// Add some events
	now := time.Now()
	limiter.AllowTime(now)
	limiter.AllowTime(now.Add(100 * time.Millisecond))

	// Reset and verify
	limiter.Reset()
	if count := limiter.GetCount(); count != 0 {
		t.Errorf("Expected count 0 after reset, got %d", count)
	}

	// Verify we can add new events after reset
	_, ok := limiter.Allow()
	if !ok {
		t.Error("Expected allow after reset")
	}
}

func TestDurationLimiter_GetEvents(t *testing.T) {
	limiter := NewDurationLimiter(3, time.Second)

	// Add events
	now := time.Now()
	events := []time.Time{
		now,
		now.Add(100 * time.Millisecond),
		now.Add(200 * time.Millisecond),
	}

	for _, event := range events {
		limiter.AllowTime(event)
	}

	// Get events and verify
	retrievedEvents := limiter.GetEvents()
	if len(retrievedEvents) != 3 {
		t.Errorf("Expected 3 events, got %d", len(retrievedEvents))
	}

	for i, event := range events {
		if !event.Equal(retrievedEvents[i]) {
			t.Errorf("Event %d mismatch: expected %v, got %v", i, event, retrievedEvents[i])
		}
	}
}

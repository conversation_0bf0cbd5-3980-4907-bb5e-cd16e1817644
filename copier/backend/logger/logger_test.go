package logger

import (
	"bytes"
	"strings"
	"testing"
	"time"
)

func TestLogger(t *testing.T) {
	// Create a buffer to capture output
	var buf bytes.Buffer
	logger := NewLogger("test", DEBUG)
	logger.Logger.SetOutput(&buf)

	// Test different log levels
	logger.Debugf("debug message")
	logger.Infof("info message")
	logger.Warnf("warn message")
	logger.Errorf("error message")

	output := buf.String()
	lines := strings.Split(output, "\n")

	// Verify log format
	for _, line := range lines {
		if line == "" {
			continue
		}
		// Check timestamp format
		if !strings.Contains(line, time.Now().Format("2006-01-02 15:04")) {
			t.<PERSON>("Expected timestamp in log line: %s", line)
		}
		// Check log level
		if !strings.Contains(line, "[") || !strings.Contains(line, "]") {
			t.<PERSON>("Expected log level in brackets: %s", line)
		}
		// Check file and line number
		if !strings.Contains(line, "logger_test.go:") {
			t.<PERSON>("Expected file and line number: %s", line)
		}
		// Check prefix
		if !strings.Contains(line, "<test>") {
			t.<PERSON>("Expected prefix in log line: %s", line)
		}
	}
}

func TestLogLevel(t *testing.T) {
	var buf bytes.Buffer
	logger := NewLogger("", INFO)
	logger.Logger.SetOutput(&buf)

	// Debug message should not appear
	logger.Debugf("debug message")
	if buf.String() != "" {
		t.Error("Debug message should not appear at INFO level")
	}

	// Info message should appear
	logger.Infof("info message")
	if !strings.Contains(buf.String(), "info message") {
		t.Error("Info message should appear at INFO level")
	}
}

func TestDefaultLogger(t *testing.T) {
	var buf bytes.Buffer
	SetLevel(DEBUG)
	defaultLogger.Logger.SetOutput(&buf)

	// Test package-level functions
	Debugf("debug message")
	Infof("info message")
	Warnf("warn message")
	Errorf("error message")

	output := buf.String()
	if !strings.Contains(output, "debug message") ||
		!strings.Contains(output, "info message") ||
		!strings.Contains(output, "warn message") ||
		!strings.Contains(output, "error message") {
		t.Error("Expected all log levels to be present in output")
	}
}

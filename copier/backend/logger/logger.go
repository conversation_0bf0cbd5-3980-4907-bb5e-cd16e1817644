package logger

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"gopkg.in/natefinch/lumberjack.v2"
)

type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARN
	ERROR
)

var (
	levelNames = map[LogLevel]string{
		DEBUG: "DEBUG",
		INFO:  "INFO",
		WARN:  "WARN",
		ERROR: "ERROR",
	}
)

type Logger struct {
	*log.Logger
	level      LogLevel
	mu         sync.Mutex
	prefix     string
	fileLogger *log.Logger
}

var defaultLogger *Logger

func init() {
	defaultLogger = NewLogger("", INFO)
}

func InitFileLogger(dataDir string) {
	logDir := filepath.Join(dataDir, "logs")
	if err := os.MkdirAll(logDir, 0755); err != nil {
		log.Fatalf("failed to create log directory: %v", err)
	}

	fileLogger := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, "copier.log"),
		MaxSize:    20, // megabytes
		MaxBackups: 3,
		MaxAge:     28, //days
		Compress:   true,
	}

	defaultLogger.mu.Lock()
	defaultLogger.fileLogger = log.New(fileLogger, "", 0)
	defaultLogger.mu.Unlock()
}

func NewLogger(prefix string, level LogLevel) *Logger {
	return &Logger{
		Logger: log.New(os.Stdout, "", 0),
		level:  level,
		prefix: prefix,
	}
}

func (l *Logger) SetLevel(level LogLevel) {
	l.mu.Lock()
	defer l.mu.Unlock()
	l.level = level
}

func (l *Logger) SetPrefix(prefix string) {
	l.mu.Lock()
	defer l.mu.Unlock()
	l.prefix = prefix
}

func (l *Logger) log(level LogLevel, format string, args ...interface{}) {
	if level < l.level {
		return
	}

	l.mu.Lock()
	defer l.mu.Unlock()

	// Get caller info
	_, file, line, ok := runtime.Caller(3)
	if !ok {
		file = "unknown"
		line = 0
	} else {
		file = filepath.Base(file)
	}

	// Format message
	msg := fmt.Sprintf(format, args...)
	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	levelName := levelNames[level]

	// Construct log line
	logLine := fmt.Sprintf("%s [%s] %s:%d <%s> %s",
		timestamp,
		levelName,
		file,
		line,
		l.prefix,
		msg,
	)

	l.Print(logLine)
	if l.fileLogger != nil {
		l.fileLogger.Print(logLine)
	}
}

func (l *Logger) Debugf(format string, args ...interface{}) {
	l.log(DEBUG, format, args...)
}

func (l *Logger) Infof(format string, args ...interface{}) {
	l.log(INFO, format, args...)
}

func (l *Logger) Warnf(format string, args ...interface{}) {
	l.log(WARN, format, args...)
}

func (l *Logger) Errorf(format string, args ...interface{}) {
	l.log(ERROR, format, args...)
}

// Package-level convenience functions
func SetLevel(level LogLevel) {
	defaultLogger.SetLevel(level)
}

func SetPrefix(prefix string) {
	defaultLogger.SetPrefix(prefix)
}

func Debugf(format string, args ...interface{}) {
	defaultLogger.Debugf(format, args...)
}

func Infof(format string, args ...interface{}) {
	defaultLogger.Infof(format, args...)
}

func Warnf(format string, args ...interface{}) {
	defaultLogger.Warnf(format, args...)
}

func Errorf(format string, args ...interface{}) {
	defaultLogger.Errorf(format, args...)
}

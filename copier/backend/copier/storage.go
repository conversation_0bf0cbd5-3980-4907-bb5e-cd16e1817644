package copier

import (
	"copier/backend/logger"
	"copier/backend/utils"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path"
	"path/filepath"
	"sync"
	"time"

	"github.com/tidwall/gjson"
)

type Storage struct {
	controller          *CopierController
	Projects            []*Project
	AutomationSchedules []*AutomationSchedule
	mutex               sync.Mutex
}

func NewStorage(controller *CopierController) *Storage {
	storage := &Storage{
		controller: controller,
		Projects:   []*Project{},
	}

	storage.LoadFromFile()

	return storage
}

func (this *Storage) LoadFromFile() error {
	this.mutex.Lock()
	defer this.mutex.Unlock()

	jsonFile, err := os.Open(this.controller.DataDir + "/copier_projects.json")
	if err != nil {
		logger.Errorf("failed to open copier_projects.json: %v", err)
		return err
	}
	defer jsonFile.Close()

	byteValue, _ := io.ReadAll(jsonFile)
	err = json.Unmarshal(byteValue, &this.Projects)
	if err != nil {
		logger.Errorf("failed to unmarshal projects: %s", err)
		return err
	}

	// populate controller with projects
	for _, project := range this.Projects {
		if project.BornTime.IsZero() {
			project.BornTime, _ = time.Parse("2006-01-02", "2025-04-01")
		}
		// migrate to new ServerSettings.ProjectID
		// TODO: crash here with prd data, need to fix later
		// for _, agent := range project.Agents {
		// 	if agent != nil && agent.ServerSettings != nil && agent.ServerSettings.ProjectID == "" {
		// 		agent.ServerSettings.ProjectID = project.ProjectID
		// 	}
		// }
		this.controller.Projects.Store(project.ProjectID, project)
	}

	schedulesFile, err := os.Open(this.controller.DataDir + "/automation_schedules.json")
	if err != nil {
		logger.Errorf("failed to open automation_schedules.json: %v", err)
		return err
	}
	defer schedulesFile.Close()

	scheduleByteValue, _ := io.ReadAll(schedulesFile)
	err = json.Unmarshal(scheduleByteValue, &this.AutomationSchedules)
	if err != nil {
		logger.Errorf("failed to unmarshal automation schedules: %s", err)
		return err
	}

	// set automation schedules to controller
	this.controller.automationSchedules = this.AutomationSchedules

	return nil
}

func (this *Storage) Save() error {
	this.mutex.Lock()
	defer this.mutex.Unlock()

	// update accounts from controller
	this.Projects = []*Project{}
	this.controller.Projects.Range(func(key string, value *Project) bool {
		this.Projects = append(this.Projects, value)
		return true
	})

	projectsPath := path.Join(this.controller.DataDir, "copier_projects.json")

	// backup old storage file, if the old file is valid json
	oldData, err := os.ReadFile(projectsPath)
	if err == nil && gjson.Valid(string(oldData)) {
		backupPath := filepath.Join(this.controller.DataDir, "copier_projects.json.bak")
		if err := utils.CopyFile(projectsPath, backupPath); err != nil {
			logger.Errorf("backup old storage file error: %s", err)
			return err
		}
	}

	data, err := func() (data []byte, err error) {
		defer func() {
			if r := recover(); r != nil {
				err = fmt.Errorf("panic: %v", r)
			}
		}()

		data, err = json.MarshalIndent(this.Projects, "", "  ")
		if err != nil {
			return nil, fmt.Errorf("failed to marshal projects: %w", err)
		}
		return data, nil
	}()

	if err != nil {
		logger.Errorf("failed to save projects: %s", err)
		return err
	}

	if err := os.WriteFile(projectsPath, data, 0755); err != nil {
		logger.Errorf("failed to save projects: %s", err)
		return err
	}

	schedulesPath := path.Join(this.controller.DataDir, "automation_schedules.json")

	// backup old automation schedules file, if the old file is valid json
	schedulesOldData, err := os.ReadFile(schedulesPath)
	if err == nil && gjson.Valid(string(schedulesOldData)) {
		backupPath := filepath.Join(this.controller.DataDir, "automation_schedules.json.bak")
		if err := utils.CopyFile(schedulesPath, backupPath); err != nil {
			logger.Errorf("backup old automation schedules file error: %s", err)
			return err
		}
	}

	schedulesData, err := func() (data []byte, err error) {
		defer func() {
			if r := recover(); r != nil {
				err = fmt.Errorf("panic: %v", r)
			}
		}()

		automationSchedules := this.controller.automationSchedules.CloneWithoutCurrentBatch()
		data, err = json.MarshalIndent(automationSchedules, "", "  ")
		if err != nil {
			return nil, fmt.Errorf("failed to marshal automation schedules: %w", err)
		}
		return data, nil
	}()

	if err != nil {
		logger.Errorf("failed to marshal automation schedules: %s", err)
		return err
	}
	if err := os.WriteFile(schedulesPath, schedulesData, 0755); err != nil {
		logger.Errorf("failed to save automation schedules: %s", err)
		return err
	}

	return nil
}

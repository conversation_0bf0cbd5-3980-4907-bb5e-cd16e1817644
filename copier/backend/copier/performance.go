package copier

import (
	"copier/backend/utils"
	"fmt"
	"math"
	"sort"
	"time"
)

type Performance struct {
	Volume      float64        `json:"volume"`
	Cost        float64        `json:"cost"`
	Ratio       float64        `json:"ratio"`
	Date        time.Time      `json:"date"`
	Trades      int            `json:"trades"`
	AgentTrades map[string]int `json:"agentTrades"`
}

type ProjectPerformance struct {
	ProjectID        string         `json:"projectId"`
	Performance      *Performance   `json:"performance"`
	DailyPerformance []*Performance `json:"dailyPerformance"`
	StartTime        time.Time      `json:"startTime"`
	EndTime          time.Time      `json:"endTime"`
}

// CalculateCutoffTime returns the current or previous cutoff time based on the specified weekday and time
// If the current time is after or equal to this week's cutoff, it returns this week's cutoff time
// If the current time is before this week's cutoff, it returns last week's cutoff time
// weekday: 0-6 (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
// hour, minute, second: the specific time of day for the cutoff
// The returned time is in UTC
// currentDate: the date to calculate the cutoff time for
func CalculateCutoffTime(currentDate time.Time, weekday int, hour, minute, second int) time.Time {
	now := currentDate.UTC()

	// Calculate days to subtract to get to the most recent specified weekday
	currentWeekday := int(now.Weekday())
	daysToTarget := (currentWeekday - weekday) % 7
	if daysToTarget < 0 {
		daysToTarget += 7
	}

	// Calculate this week's target day at the specified time in UTC
	thisCutoff := time.Date(now.Year(), now.Month(), now.Day(), hour, minute, second, 0, time.UTC)
	thisCutoff = thisCutoff.AddDate(0, 0, -daysToTarget)

	// Determine if we should use this week's or last week's cutoff
	if now.After(thisCutoff) || now.Equal(thisCutoff) {
		// Current time is after or equal to this week's cutoff, use this week's
		return thisCutoff
	} else {
		// Current time is before this week's cutoff, use last week's
		return thisCutoff.AddDate(0, 0, -7)
	}
}

// isWithinTodayRange checks if the given date is within today's range (00:00:00 to 23:59:59 UTC)
func isWithinTodayRange(date time.Time) bool {
	date = date.UTC()
	now := time.Now().UTC()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	todayEnd := todayStart.Add(24 * time.Hour)
	result := !date.Before(todayStart) && date.Before(todayEnd)
	return result
}

func isWithinWeekRange(date time.Time) bool {
	// Convert input date to UTC for consistent comparison
	date = date.UTC()
	weekStart := CalculateCutoffTime(time.Now(), 3, 0, 0, 0) // 3 = Wednesday, 00:00:00
	weekEnd := weekStart.Add(7 * 24 * time.Hour)
	result := !date.Before(weekStart) && date.Before(weekEnd)
	return result
}

func isWithinLastWeekRange(date time.Time) bool {
	date = date.UTC()
	thisWeekStart := CalculateCutoffTime(time.Now(), 3, 0, 0, 0) // 3 = Wednesday, 00:00:00
	lastWeekStart := CalculateCutoffTime(time.Now().AddDate(0, 0, -7), 3, 0, 0, 0)
	result := !date.Before(lastWeekStart) && date.Before(thisWeekStart)
	return result
}

func (this *CopierController) GetProjectPerformance(projectID string, startTime *time.Time, endTime *time.Time) ProjectPerformance {
	_, ok := this.Projects.Load(projectID)
	if !ok {
		return ProjectPerformance{}
	}

	// Calculate startTime as Wednesday 00:00:00 cutoff
	if startTime == nil {
		_startTime := CalculateCutoffTime(time.Now(), 3, 0, 0, 0) // 3 = Wednesday, 00:00:00
		startTime = &_startTime
	}
	if endTime == nil {
		_endTime := time.Now().UTC()
		endTime = &_endTime
	}

	performance := &ProjectPerformance{
		ProjectID: projectID,
		StartTime: *startTime,
		EndTime:   *endTime,
		Performance: &Performance{
			Volume: 0,
			Cost:   0,
			Ratio:  0,
			Date:   *startTime, // Use start time as the overall period date
			Trades: 0,
		},
	}

	snapshots := this.LookupProjectSnapshots(projectID, startTime, endTime)

	// Handle the case of no snapshots
	if len(snapshots) == 0 {
		return *performance
	}

	// for older records without snapshotTime, the createTime may be messed up, so we need to reverse the order of snapshots
	// for newer records, we need to compare the newly added snapshotTime
	firstSnapshot := snapshots[0]
	if firstSnapshot.SnapshotTime.IsZero() {
		snapshots = utils.Reverse(snapshots)
	} else {
		// sort snapshots by createTime，从早到晚排列
		sort.Slice(snapshots, func(i, j int) bool {
			return snapshots[i].SnapshotTime.Before(snapshots[j].SnapshotTime)
		})
	}

	// 获取第一个快照和最后一个快照，计算整个期间内的 volume 和 cost
	lastSnapshot := snapshots[len(snapshots)-1]
	firstSnapshot = snapshots[0]

	performance.Performance.Volume = lastSnapshot.Volume - firstSnapshot.Volume
	performance.Performance.Cost = lastSnapshot.TotalMargin - firstSnapshot.TotalMargin

	// Prevent NaN or Inf values in Ratio calculation
	if performance.Performance.Volume > 0 {
		performance.Performance.Ratio = performance.Performance.Cost / performance.Performance.Volume
	} else {
		performance.Performance.Ratio = 0
	}

	// Get all position changes for the period
	allPositionChanges := this.LookupPositionChanges(projectID, "", startTime, endTime, 0) // 0 means no limit
	performance.Performance.Trades = len(allPositionChanges)

	// Calculate trades per agent for overall performance
	agentTrades := make(map[string]int)
	for _, change := range allPositionChanges {
		agentTrades[change.UserID]++
	}
	performance.Performance.AgentTrades = agentTrades

	// Revised approach for daily performance - handle single day case
	// If we have at least two snapshots, we can calculate incremental performance
	if len(snapshots) >= 2 {
		// Initialize with a map for tracking the last snapshot we've seen for each date
		lastSeenSnapshotByDate := make(map[string]*ProjectSnapshot)
		firstSeenSnapshotByDate := make(map[string]*ProjectSnapshot)
		dateFormat := "2006-01-02" // YYYY-MM-DD format

		// First pass - record first and last snapshot of each day
		for _, snapshot := range snapshots {
			// old data uses createTime instead of snapshotTime
			dateStr := snapshot.SnapshotTime.UTC().Format(dateFormat)
			if snapshot.SnapshotTime.IsZero() {
				dateStr = snapshot.CreateTime.UTC().Format(dateFormat)
			}

			// Update last snapshot of this day
			lastSeenSnapshotByDate[dateStr] = snapshot

			// Record first snapshot of this day if not already set
			if _, exists := firstSeenSnapshotByDate[dateStr]; !exists {
				firstSeenSnapshotByDate[dateStr] = snapshot
			}
		}

		// Get dates in order
		dates := make([]string, 0, len(lastSeenSnapshotByDate))
		for date := range lastSeenSnapshotByDate {
			dates = append(dates, date)
		}
		sort.Strings(dates)

		// Calculate performance for each day
		for _, currentDate := range dates {
			firstSnapshot := firstSeenSnapshotByDate[currentDate]
			lastSnapshot := lastSeenSnapshotByDate[currentDate]

			// Calculate daily performance using first and last snapshot of the day
			dailyVolume := lastSnapshot.Volume - firstSnapshot.Volume
			dailyCost := lastSnapshot.TotalMargin - firstSnapshot.TotalMargin
			dailyRatio := 0.0

			// Prevent NaN or Inf values
			if dailyVolume > 0 {
				dailyRatio = dailyCost / dailyVolume
			}

			// Check for NaN or Inf in ratio only
			if math.IsNaN(dailyRatio) || math.IsInf(dailyRatio, 0) {
				dailyRatio = 0
			}

			// Parse the current date string back to time.Time in UTC
			parsedDate, _ := time.ParseInLocation(dateFormat, currentDate, time.UTC)

			// Filter position changes for this day
			dayStart := time.Date(parsedDate.Year(), parsedDate.Month(), parsedDate.Day(), 0, 0, 0, 0, time.UTC)
			dayEnd := dayStart.Add(24 * time.Hour)
			dailyPositionChanges := make([]*PositionChange, 0)
			dailyAgentTrades := make(map[string]int)
			for _, change := range allPositionChanges {
				if !change.CreateTime.Before(dayStart) && change.CreateTime.Before(dayEnd) {
					dailyPositionChanges = append(dailyPositionChanges, change)
					dailyAgentTrades[change.UserID]++
				}
			}

			// Add daily performance entry
			performance.DailyPerformance = append(performance.DailyPerformance, &Performance{
				Volume:      dailyVolume,
				Cost:        dailyCost,
				Ratio:       dailyRatio,
				Date:        parsedDate,
				Trades:      len(dailyPositionChanges),
				AgentTrades: dailyAgentTrades,
			})
		}
	}

	// Final check for NaN/Inf in overall ratio only
	if math.IsNaN(performance.Performance.Ratio) || math.IsInf(performance.Performance.Ratio, 0) {
		performance.Performance.Ratio = 0
	}

	return *performance
}

func (this *CopierController) UpdateProjectPerformance(projectID string) error {
	// this week's performance
	thisWeekPerformance := this.GetProjectPerformance(projectID, nil, nil)

	project, _ := this.Projects.Load(projectID)
	if project == nil {
		return fmt.Errorf("project not found: %s", projectID)
	}

	latestPerformance := &Performance{}
	withinWeek := isWithinWeekRange(thisWeekPerformance.Performance.Date)
	if withinWeek {
		latestPerformance = thisWeekPerformance.Performance
	}
	project.LatestPerformance = latestPerformance
	todayPerformance := &Performance{}
	if len(thisWeekPerformance.DailyPerformance) > 0 {
		lastPerformance := thisWeekPerformance.DailyPerformance[len(thisWeekPerformance.DailyPerformance)-1]
		withinToday := isWithinTodayRange(lastPerformance.Date)
		if withinToday {
			todayPerformance = lastPerformance
		}
	}
	project.LatestPerformance = latestPerformance
	project.TodayPerformance = todayPerformance

	// last week's performance
	lastWeekStart := CalculateCutoffTime(time.Now().AddDate(0, 0, -7), 3, 0, 0, 0)
	lastWeekEnd := CalculateCutoffTime(time.Now(), 3, 0, 0, 0)
	lastWeekPerformance := this.GetProjectPerformance(projectID, &lastWeekStart, &lastWeekEnd)
	lastPerformance := &Performance{}
	withinLastWeek := isWithinLastWeekRange(lastWeekPerformance.Performance.Date)
	if withinLastWeek {
		lastPerformance = lastWeekPerformance.Performance
	}
	project.LastPerformance = lastPerformance

	this.Projects.Store(projectID, project)
	return nil
}

package copier

import (
	"copier/backend/logger"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"copier/backend/backscanner"

	"github.com/puzpuzpuz/xsync"
)

func (this *CopierController) GetSnapshotDir() string {
	configPath := this.DataDir
	configPath = filepath.Join(configPath, "snapshots")
	// 创建文件夹
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		os.MkdirAll(configPath, 0755)
	}
	return configPath
}

func (this *CopierController) GetProjectSnapshotsPath(projectID string) string {
	configPath := this.GetSnapshotDir()
	return filepath.Join(configPath, fmt.Sprintf("%s.project_snapshots", projectID))
}

func (this *CopierController) GetPositionChangesPath(projectID string) string {
	configPath := this.GetSnapshotDir()
	return filepath.Join(configPath, fmt.Sprintf("%s.position_changes", projectID))
}

var saveProjectSnapshotMutex = xsync.NewMapOf[sync.Mutex]()

func (this *CopierController) SaveProjectSnapshot(snapshot *ProjectSnapshot) error {
	path := this.GetProjectSnapshotsPath(snapshot.ProjectID)
	mutex, _ := saveProjectSnapshotMutex.LoadOrStore(path, sync.Mutex{})
	mutex.Lock()
	defer mutex.Unlock()

	// append snapshot json to file
	json, err := json.Marshal(snapshot)
	if err != nil {
		return err
	}
	// create file if not exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		os.Create(path)
	}
	f, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = f.WriteString(fmt.Sprintf("%s\n", string(json)))
	if err != nil {
		return err
	}
	return nil
}

const SnapshotHistoryTimeWindow = time.Minute

// 查询快照
// snapshotID: 快照ID，为空则查询所有快照
// snapshotTime: 快照时间，为空则查询所有快照
// limit: 查询数量，为0则查询最近的 100 个快照
// 用 snapshotID 查询时，snapshotTime 不生效 和 limit 不生效
// 用 snapshotTime 查询时，limit 不生效，仅查询 snapshotTime 之前 1 分钟和之后 1 分钟的快照
func (this *CopierController) LookupProjectSnapshots(projectID string, startTime *time.Time, endTime *time.Time) (snapshots []*ProjectSnapshot) {
	if startTime == nil {
		aweekAgo := time.Now().Add(-7 * 24 * time.Hour)
		startTime = &aweekAgo
	}
	if endTime == nil {
		_endTime := time.Now().UTC()
		endTime = &_endTime
	}
	path := this.GetProjectSnapshotsPath(projectID)
	project, _ := this.Projects.Load(projectID)
	if project == nil {
		return
	}

	backscanner.BackScan(path, func(line []byte) bool {
		snapshot := &ProjectSnapshot{}
		err := json.Unmarshal(line, snapshot)
		recordTime := snapshot.SnapshotTime
		if recordTime.IsZero() {
			recordTime = snapshot.CreateTime // old data uses createTime instead of snapshotTime
		}
		if err == nil {
			// snapshot createTime within before or after 1 minutes of time
			if startTime != nil {
				if recordTime.Before(*startTime) {
					return false
				}
			}
			// 仅查询 project 开始时间之后的快照，因为 project 删除（重置）后可能用同样的 projectID，
			// 查询的时候，仅查询 project 重置时间之后的快照，以免使用旧的数据导致计算 volume 和 cost 都不准
			if recordTime.Before(project.BornTime) {
				return false
			}
			if !recordTime.After(*endTime) {
				snapshots = append(snapshots, snapshot)
			}
		}
		return true
	})
	return
}

var savePositionChangeMutex = xsync.NewMapOf[sync.Mutex]()

func (this *CopierController) SavePositionChange(positionChange *PositionChange) error {
	path := this.GetPositionChangesPath(positionChange.ProjectID)
	mutex, _ := savePositionChangeMutex.LoadOrStore(path, sync.Mutex{})
	mutex.Lock()
	defer mutex.Unlock()

	// append snapshot json to file
	json, err := json.Marshal(positionChange)
	if err != nil {
		return err
	}
	// create file if not exist
	if _, err := os.Stat(path); os.IsNotExist(err) {
		os.Create(path)
	}
	f, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = f.WriteString(fmt.Sprintf("%s\n", string(json)))
	if err != nil {
		return err
	}
	return nil
}

// 查询快照
// snapshotID: 快照ID，为空则查询所有快照
// snapshotTime: 快照时间，为空则查询所有快照
// limit: 查询数量，为0则查询最近的 100 个快照
// 用 snapshotID 查询时，snapshotTime 不生效 和 limit 不生效
// 用 snapshotTime 查询时，limit 不生效，仅查询 snapshotTime 之前 1 分钟和之后 1 分钟的快照
func (this *CopierController) LookupPositionChanges(projectID string, userID string, startTime *time.Time, endTime *time.Time, limit int) (changes []*PositionChange) {
	if startTime == nil {
		aweekAgo := time.Now().Add(-7 * 24 * time.Hour)
		startTime = &aweekAgo
	}
	if endTime == nil {
		_endTime := time.Now().UTC()
		endTime = &_endTime
	}
	path := this.GetPositionChangesPath(projectID)
	project, _ := this.Projects.Load(projectID)
	if project == nil {
		return
	}

	backscanner.BackScan(path, func(line []byte) bool {
		positionChange := &PositionChange{}
		err := json.Unmarshal(line, positionChange)
		if err == nil {
			// snapshot createTime within before or after 1 minutes of time
			if startTime != nil {
				if positionChange.CreateTime.Before(*startTime) {
					return false
				}
			}
			// 仅查询 project 开始时间之后的快照，因为 project 删除（重置）后可能用同样的 projectID，
			// 查询的时候，仅查询 project 重置时间之后的快照，以免使用旧的数据导致计算 volume 和 cost 都不准
			if positionChange.CreateTime.Before(project.BornTime) {
				return false
			}

			if !positionChange.CreateTime.After(*endTime) {
				if userID == "" || positionChange.UserID == userID {
					changes = append(changes, positionChange)
					// If limit is set and we've reached it, stop scanning
					if limit > 0 && len(changes) >= limit {
						return false
					}
				}
			}
		}
		return true
	})
	return
}

const SnapshotRetentionPeriod = 24 * time.Hour * 30 // 30 days

func (this *CopierController) TruncateSnapshots() error {
	logger.Infof("truncating snapshots")
	startTime := time.Now()
	retentionPeriod := SnapshotRetentionPeriod
	for _, project := range this.GetProjects(false) {
		path := this.GetProjectSnapshotsPath(project.ProjectID)
		lock, _ := saveProjectSnapshotMutex.LoadOrStore(path, sync.Mutex{})
		lock.Lock()
		path2 := this.GetPositionChangesPath(project.ProjectID)
		lock2, _ := savePositionChangeMutex.LoadOrStore(path2, sync.Mutex{})
		lock2.Lock()
		defer func() {
			lock.Unlock()
			lock2.Unlock()
			logger.Infof("truncating snapshots finished, cost: %s", time.Since(startTime))
		}()
		err := backscanner.TruncateJsonFileByCreateTime(path, retentionPeriod)
		if err != nil {
			return err
		}
		err = backscanner.TruncateJsonFileByCreateTime(path2, retentionPeriod)
		if err != nil {
			return err
		}
	}
	return nil
}

var copyHistoriesMutex = xsync.NewMapOf[sync.Mutex]()

func (this *CopierController) copyHistories(oldProjectID string, newProjectID string) error {
	mutex, _ := copyHistoriesMutex.LoadOrStore(newProjectID, sync.Mutex{})
	mutex.Lock()
	defer mutex.Unlock()

	oldPath := this.GetProjectSnapshotsPath(oldProjectID)
	oldPath2 := this.GetPositionChangesPath(oldProjectID)

	oldPathMutex, _ := saveProjectSnapshotMutex.LoadOrStore(oldPath, sync.Mutex{})
	oldPathMutex.Lock()
	defer oldPathMutex.Unlock()

	oldPath2Mutex, _ := savePositionChangeMutex.LoadOrStore(oldPath2, sync.Mutex{})
	oldPath2Mutex.Lock()
	defer oldPath2Mutex.Unlock()

	backscanner.Scan(oldPath, func(line []byte) bool {
		snapshot := &ProjectSnapshot{}
		err := json.Unmarshal(line, snapshot)
		if err == nil {
			snapshot.ProjectID = newProjectID
			this.SaveProjectSnapshot(snapshot)
		}
		return true
	})
	backscanner.Scan(oldPath2, func(line []byte) bool {
		positionChange := &PositionChange{}
		err := json.Unmarshal(line, positionChange)
		if err == nil {
			positionChange.ProjectID = newProjectID
			this.SavePositionChange(positionChange)
		}
		return true
	})

	return nil
}

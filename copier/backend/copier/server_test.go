package copier

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupTestServer(t *testing.T) (*httptest.Server, func()) {
	// Create a temporary directory for test data
	tempDir, err := os.MkdirTemp("", "copier-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}

	// Create the controller using the factory function
	controller, err := NewCopierController(tempDir, &CopierOptions{
		Debug: true,
	})
	if err != nil {
		t.Fatalf("Failed to create controller: %v", err)
	}

	// Start the server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		router := gin.Default()

		// Health check endpoint
		router.GET("/health", func(c *gin.Context) {
			c.<PERSON>(http.StatusOK, gin.H{
				"status": "healthy",
			})
		})

		// Get accounts endpoint
		router.GET("/agents/:project_id", func(c *gin.Context) {
			projectID := c.Param("project_id")

			agents, ok := controller.GetAgents(projectID)
			if !ok {
				agents = Agents{}
			}

			c.JSON(http.StatusOK, GetAgentsResponse{
				Agents: agents,
			})
		})

		// Update account endpoint
		router.POST("/update/:project_id", func(c *gin.Context) {
			projectID := c.Param("project_id")

			var agent UpdateAgentRequest
			if err := c.ShouldBindJSON(&agent); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}

			controller.UpdateAgent(projectID, &agent)

			c.JSON(http.StatusOK, UpdateAgentResponse{
				Agent: agent,
			})
		})

		router.ServeHTTP(w, r)
	}))

	// Return cleanup function
	cleanup := func() {
		server.Close()
		os.RemoveAll(tempDir)
	}

	return server, cleanup
}

func TestHealthCheck(t *testing.T) {
	server, cleanup := setupTestServer(t)
	defer cleanup()

	resp, err := http.Get(server.URL + "/health")
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var response map[string]string
	err = json.NewDecoder(resp.Body).Decode(&response)
	assert.NoError(t, err)
	assert.Equal(t, "healthy", response["status"])
}

func TestGetAccounts(t *testing.T) {
	server, cleanup := setupTestServer(t)
	defer cleanup()

	// Test getting accounts for non-existent platform/project
	resp, err := http.Get(server.URL + "/agents/test_project")
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var response GetAgentsResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	assert.NoError(t, err)
	assert.Empty(t, response.Agents)
}

func TestUpdateAndGetAgent(t *testing.T) {
	server, cleanup := setupTestServer(t)
	defer cleanup()

	// Create a test account
	account := Agent{
		Role:            RoleMaster,
		Platform:        PlatformLighter,
		ProjectID:       "test_project",
		UserID:          "test_user",
		Symbol:          "BTC-USD",
		CopyFromID:      "",
		CopyPercentage:  100,
		LastPrice:       50000,
		Spread:          0.1,
		TotalMargin:     1000,
		AvailableMargin: 1000,
		Positions:       []Position{},
		CreateTime:      time.Now(),
	}

	// Update the account
	accountJSON, err := json.Marshal(account)
	assert.NoError(t, err)

	resp, err := http.Post(
		server.URL+"/update/test_project",
		"application/json",
		bytes.NewBuffer(accountJSON),
	)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	// Get the account and verify it was updated
	resp, err = http.Get(server.URL + "/agents/test_project")
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var getResponse GetAgentsResponse
	err = json.NewDecoder(resp.Body).Decode(&getResponse)
	assert.NoError(t, err)
	assert.Len(t, getResponse.Agents, 1)
	assert.Equal(t, account.UserID, getResponse.Agents[0].UserID)
	assert.Equal(t, account.Symbol, getResponse.Agents[0].Symbol)
}

func TestUpdateAgentInvalidJSON(t *testing.T) {
	server, cleanup := setupTestServer(t)
	defer cleanup()

	// Send invalid JSON
	resp, err := http.Post(
		server.URL+"/update/test_project",
		"application/json",
		bytes.NewBuffer([]byte("invalid json")),
	)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, resp.StatusCode)
}

<html class="bg-neutral-950"><head><meta charset="utf-8"><title>zkLighter Block Explorer</title><meta name="viewport" content="width=device-width, initial-scale=1"><meta name="theme-color" content="#000000"><link href="/static/style.css" rel="stylesheet"><link rel="icon" sizes="16x16" type="image/x-icon" href="/static/favicon-16x16.png"><link rel="icon" sizes="32x32" type="image/x-icon" href="/static/favicon-32x32.png"><script src="https://unpkg.com/htmx.org@1.9.9"></script><script src="https://unpkg.com/hyperscript.org@0.9.12"></script><script src="https://unpkg.com/htmx.org/dist/ext/ws.js"></script><script src="https://unpkg.com/htmx.org/dist/ext/class-tools.js"></script><script src="https://unpkg.com/alpinejs" defer=""></script><script src="https://cdn.jsdelivr.net/npm/dayjs@1/dayjs.min.js"></script><script src="https://cdn.jsdelivr.net/npm/dayjs@1/plugin/relativeTime.js"></script><script src="https://cdn.jsdelivr.net/npm/luxon@3.4.4/build/global/luxon.min.js"></script><script src="https://cdn.jsdelivr.net/npm/@floating-ui/core@1.6.0"></script><script src="https://cdn.jsdelivr.net/npm/@floating-ui/dom@1.6.3"></script><script>
    dayjs.extend(window.dayjs_plugin_relativeTime)
    htmx.config.getCacheBusterParam = true
    htmx.config.wsReconnectDelay = function (retryCount) {
      return 600 // return value in milliseconds
    }

    document.addEventListener('htmx:beforeSwap', (event) => {
      // console.log(event)
    });
    document.addEventListener('htmx:wsAfterMessage', (event) => {
      // console.log(event)
    });

    htmx.on('htmx:oobAfterSwap', (e) => {
      e.detail.target.dispatchEvent(new Event('newElement'))
    });

    htmx.on('htmx:oobBeforeSwap', (e) => {
      e.detail.target.dispatchEvent(new CustomEvent('oob-before-swap', { detail: e }))
    });

    var DateTime = luxon.DateTime;
    var TAIL_LENGTH = 4;
    var LATEST_BLOCK = -1;

    function getWidth(el) {
      return el.getBoundingClientRect().width;
    }


    function addItemToList(ctr, itemContent) {
      const listContainer = ctr
      const newItem = document.createElement('div');
      newItem.classList.add('slide-in');
      newItem.style.position = 'absolute';
      newItem.style.width = '100%';
      newItem.innerHTML = itemContent;

      // Prepend the new item so it slides in from the top
      if (listContainer.firstChild) {
        listContainer.insertBefore(newItem, listContainer.firstChild);
      } else {
        listContainer.appendChild(newItem);
      }

      // Apply the sliding effect to existing items and remove the last one if necessary
      const items = listContainer.childNodes;
      if (items.length > 1) {
        items.forEach((item, index) => {
          if (index > 0) { // Move existing items down
            item.style.transform = `translateY(${item.offsetHeight * index}px)`;
          }
        });
      }

      // Maintain a fixed size list by removing the last item if over the limit
      const maxSize = 4; // Maximum number of items you want to show
      if (items.length > maxSize) {
        listContainer.removeChild(listContainer.lastChild);
      }
    }

    function resizeHash(elem) {
      const parent = elem.parentNode
      if (!parent) {
        return;
      }
      let hash = elem.innerText;

      const shadowEl = document.createElement('span');
      shadowEl.style.opacity = '0';
      parent.appendChild(shadowEl);
      shadowEl.textContent = hash;

      const parentWidth = getWidth(parent);

      if (getWidth(shadowEl) > parentWidth) {
        const tail = hash.slice(-TAIL_LENGTH);
        let leftI = 4;
        let rightI = hash.length - TAIL_LENGTH;

        while (rightI - leftI > 1) {
          const medI = ((rightI - leftI) % 2) ? leftI + (rightI - leftI + 1) / 2 : leftI + (rightI - leftI) / 2;
          const res = hash.slice(0, medI) + '...' + tail;
          shadowEl.textContent = res;
          if (getWidth(shadowEl) < parentWidth) {
            leftI = medI;
          } else {
            rightI = medI;
          }
        }
        elem.innerText = hash.slice(0, rightI - 1) + '...' + tail
      } else {
        elem.innerText = hash
      }
      parent.removeChild(shadowEl);
    }
    document.addEventListener('alpine:init', () => {
      console.log("alpine init")


      Alpine.directive('mlist', (el, { expression }, { cleanup }) => {
        let i = 0
        const t = setInterval(() => {
          addItemToList(el, "item " + i++)
        }, 1000)

        cleanup(() => {
          clearInterval(t)
        })
      })

      Alpine.directive('reltime', (el, { expression }, { cleanup }) => {
        const v = expression
        const time = DateTime.fromISO(v)

        el.innerText = time.toRelative()
        const cancelToken = setInterval(() => {
          el.innerText = time.toRelative()
        }, 1000);
        cleanup(() => {
          clearInterval(cancelToken)
        });
      })

      function removeCustomTooltips() {
        var tooltips = document.querySelectorAll('div.custom-tooltip');
        tooltips.forEach(function (tooltip) {
          tooltip.remove();
        });
      }

      removeCustomTooltips();

      window.addEventListener('hashchange', removeCustomTooltips);

      history.pushState = (f => function pushState() {
        var ret = f.apply(this, arguments);
        window.dispatchEvent(new Event('pushstate'));
        window.dispatchEvent(new Event('locationchange'));
        return ret;
      })(history.pushState);

      history.replaceState = (f => function replaceState() {
        var ret = f.apply(this, arguments);
        window.dispatchEvent(new Event('replacestate'));
        window.dispatchEvent(new Event('locationchange'));
        return ret;
      })(history.replaceState);

      window.addEventListener('popstate', function () {
        window.dispatchEvent(new Event('locationchange'))
      });

      window.addEventListener('locationchange', function () {
        removeCustomTooltips();
      });

      var float = window.FloatingUIDOM;

      Alpine.directive('tooltip', (el, { expression, modifiers }, { cleanup }) => {
        const tooltip = document.createElement('div');
        tooltip.innerHTML = expression;
        tooltip.style += "width: max-content; position: absolute; top: 0; left: 0; display: none;"
        tooltip.classList.add('custom-tooltip');
        const m = document.getElementById('main')
        m.appendChild(tooltip);
        function update() {
          float.computePosition(el, tooltip, {
            middleware: [float.autoPlacement({ alignment: 'start' })]
          }).then(({ x, y }) => {
            Object.assign(tooltip.style, {
              left: `${x}px`,
              top: `${y}px`,
            })
          })
        }

        const isInteractive = modifiers.includes('interactive');
        let tok = null;

        function showTooltip() {
          if (tok) {
            clearTimeout(tok)
          }
          tooltip.style.display = 'block';
          update();
        }

        function hideTooltip() {
          tooltip.style.display = 'none';
        }

        [
          ['mouseenter', showTooltip],
          ['mouseleave', hideTooltip],
          ['focus', showTooltip],
          ['blur', hideTooltip],
          ['click', hideTooltip],
        ].filter(x => x).forEach(([event, listener]) => {
          el.addEventListener(event, listener);
        });
        if (isInteractive) {
          tooltip.addEventListener('mouseleave', hideTooltip);
          tooltip.addEventListener('mouseenter', showTooltip);
        }

        cleanup(() => {
          tooltip.remove();
          el.removeEventListener('mouseenter', showTooltip);
          el.removeEventListener('mouseleave', hideTooltip);
          el.removeEventListener('focus', showTooltip);
          el.removeEventListener('blur', hideTooltip);
          if (isInteractive) {
            tooltip.removeEventListener('mouseleave', hideTooltip);
            tooltip.removeEventListener('mouseenter', showTooltip);
          }
        });
      })

      Alpine.directive('resize', (el, { expression }, { cleanup }) => {
        const resizeHandler = () => {
          el.dispatchEvent(new CustomEvent('resize', { bubbles: true }));
        };

        const resizeObserver = new ResizeObserver(resizeHandler);

        resizeObserver.observe(document.body);
        cleanup(() => {
          resizeObserver.unobserve(document.body);
        });
      })


      Alpine.directive('limited', (el, { expression }, { cleanup }) => {
        const queue = [];
        const maxQueueSize = 10; // Set a maximum queue size to prevent too many items from accumulating

        const handler = (e) => {
          e.detail.detail.shouldSwap = false;
          if (queue.length >= maxQueueSize) {
            // Remove the oldest items to keep the queue size manageable
            queue.shift();
          }
          queue.push(e);
        };

        const moveItemsAndRmExtra = () => {
          const items = el.childNodes;
          if (items.length > 1) {
            items.forEach((item, index) => {
              let ii = index;
              if (index > 0) {
                // Move existing items down
                item.style.transform = `translateY(${item.clientHeight * ii}px)`;
              }
            });
          }
          // Maintain a fixed size list by removing the last item if over the limit
          const maxSize = expression; // Maximum number of items you want to show
          while (items.length > maxSize) {
            el.removeChild(el.lastChild);
          }
        };

        const processQueue = () => {
          // Only process the latest item and discard the rest
          if (queue.length > 0) {
            const e = queue.shift(); // Always get the most recent event
            const newItem = e.detail.detail.fragment.firstChild;

            if (newItem.innerHTML.includes("block")) {
              const match = newItem.innerHTML.match(/href="\/block\/(\d+)"/);
              if (match) {
                const blockNumber = parseInt(match[1]);
                LATEST_BLOCK = Math.max(LATEST_BLOCK, blockNumber);
              }
            }

            newItem.style.opacity = '0';
            if (el.firstChild) {
              el.insertBefore(newItem, el.firstChild);
            } else {
              el.appendChild(newItem);
            }
            newItem.style.opacity = '1';
            moveItemsAndRmExtra();
          }
        };

        const tok = setInterval(() => {
          processQueue();
        }, 180);

        moveItemsAndRmExtra();
        el.addEventListener('oob-before-swap', handler);

        cleanup(() => {
          el.removeEventListener('oob-before-swap', handler);
          clearInterval(tok);
        });
      });

      Alpine.directive('acc-ctr', (el, { expression }, { cleanup }) => {
        const handler = (e) => {
          let c = parseInt(e.detail.detail.fragment.firstChild.innerText)
          let acc = parseInt(el.innerText) || 0
          el.innerText = acc + c
          e.detail.detail.shouldSwap = false
        }
        el.addEventListener('oob-before-swap', handler)

        cleanup(() => {
          el.removeEventListener('oob-before-swap', handler)
        })
      })

      Alpine.directive('total-blocks', (el, { expression }, { cleanup }) => {
        const interval = setInterval(() => {
          if (LATEST_BLOCK != -1) {
            if (parseInt(el.innerText) < LATEST_BLOCK) {
              el.innerText = LATEST_BLOCK
            }
          }
        }, 1000)

        cleanup(() => {
          clearInterval(interval)
        })
      })

    })


  </script><style>                      .htmx-indicator{opacity:0}                      .htmx-request .htmx-indicator{opacity:1; transition: opacity 200ms ease-in;}                      .htmx-request.htmx-indicator{opacity:1; transition: opacity 200ms ease-in;}                    </style></head><body class="bg-primary-900 max-w-[100vw]" hx-boost="true"><div class="hidden absolute" hx-ext="ws" ws-connect="/ws" data-script="
        on htmx:wsOpen if :didReconn call location.reload() end
        on htmx:wsClose set :didReconn to true end"></div><div class="dark flex flex-col container  p-4 md:px-36 pt-0 mx-auto gap-4 text-stone-100"><div class="pt-8 flex w-full gap-8 items-baseline"><a href="/" hx-push-url="true" hx-target="#main" class="font-xl font-bold">zkLighter</a><div class="flex gap-2 *:clickable text-sm opacity-90"><a href="/block" hx-push-url="true" hx-target="#main">Blocks</a> <a href="/tx" hx-push-url="true" hx-target="#main">Transactions</a></div></div><div id="main" class=""><div id="txtable" class="lg:grid grid-cols-[auto_1fr] gap-2"><h1 class="text-2xl font-bold col-span-2">Account Details</h1><div class="mt-2 col-span-2"><span class="font-medium text-primary-300">Address:</span> <span x-data="" x-resize="" @resize.debounce="resizeHash($el)" class="text-ellipsis overflow-hidden">0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9</span></div><div class="mt-2 col-span-2"><span class="font-medium text-primary-300">Collateral Amount:</span> <span class="text-ellipsis overflow-hidden">$701.81</span></div><div class="mt-8 sm:mt-0 min-w-80 w-full h-min px-2 bg-primary-800 border border-primary-700 rounded-lg"><div class="flex gap-2 *:py-1 font-medium"><h1>Open Positions</h1></div><div class=""><div class="flex flex-col *:py-4 divide-y divide-primary-500"><div class="px-2 first:rounded-t-lg last:rounded-b-lg odd:bg-primary-800 even:bg-primary-700"><div class="flex w-full items-center"><div class="flex flex-col w-24 pr-2 mr-6"><span class="text-l text-primary-100">ETH</span></div><div class="flex flex-col text-left "><div class="grid grid-cols-2 gap-y-2"><span class="text-primary-300 mr-2">Size:</span> <span class="text-green-500">0.0100</span> <span class="text-primary-300 mr-2">Side:</span> <span class="text-green-500">LONG </span> <span class="text-primary-300 mr-2">Realized PnL:</span> <span class="text-primary-100">$0</span> <span class="text-primary-300 mr-2">Unrealized PnL:</span> <span class="text-primary-100">$-0.01</span></div></div></div></div></div></div></div><div class="mt-8 sm:mt-0 w-full h-min px-2 bg-primary-800 border border-primary-700 rounded-lg" x-data="{active: 'txs'}"><div class="flex gap-2 *:py-1 font-medium"><h1>Transactions</h1></div><div class=""><div class="flex flex-col *:py-4 divide-y divide-primary-500"><div class="px-2 first:rounded-t-lg last:rounded-b-lg odd:bg-primary-800 even:bg-primary-700"><div class="min-w-0 text-sm gap-2 grid grid-rows-2 grid-cols-[auto,1fr]"><div class="flex gap-2"><div class="tag truncate" data-type="InternalClaimOrder">InternalClaimOrder</div><div id="tx-status-0000000038b03ebe00000196b799d4f5000000000000000000000000000000000000000000000000" data-status="packed" class="tag">packed</div></div><time class="tracking-tight text-stone-400 ml-auto" x-data="" x-reltime="2025-05-10T00:30:34Z">51 seconds ago</time><div class="flex w-full gap-1 items-center"><div class="flex"><a class="flex font-bold truncate mr-auto clickable" href="/account/0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9" hx-push-url="true" hx-target="#main" x-data="" x-tooltip.interactive="<div class=&quot;bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1&quot; x-data @resize.debounce=&quot;resizeHash($el)&quot;>0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9</div>" x-resize="" @resize.debounce="resizeHash($el)">Account 2570</a></div></div><div class="flex gap-1 items-center justify-end overflow-hidden w-full"><div class="flex shrink-0"><svg class="w-4 h-4 text-gray-200 " aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"><path fill-rule="evenodd" d="M6 6a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h3a3 3 0 0 1-3 3H5a1 1 0 1 0 0 2h1a5 5 0 0 0 5-5V8a2 2 0 0 0-2-2H6Zm9 0a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h3a3 3 0 0 1-3 3h-1a1 1 0 1 0 0 2h1a5 5 0 0 0 5-5V8a2 2 0 0 0-2-2h-3Z" clip-rule="evenodd"></path></svg></div><div class="min-w-0"><div class="flex"><a class="flex font-bold truncate mr-auto clickable" href="/tx/0000000038b03ebe00000196b799d4f5000000000000000000000000000000000000000000000000" hx-push-url="true" hx-target="#main" x-data="" x-tooltip.interactive="<div class=&quot;bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1&quot; x-data @resize.debounce=&quot;resizeHash($el)&quot;>0000000038b03ebe00000196b799d4f5000000000000000000000000000000000000000000000000</div>" x-resize="" @resize.debounce="resizeHash($el)">0000000038b03ebe00000196b799d4f5000000000000000000000000000000...0000</a></div></div></div></div></div><div class="px-2 first:rounded-t-lg last:rounded-b-lg odd:bg-primary-800 even:bg-primary-700"><div class="min-w-0 text-sm gap-2 grid grid-rows-2 grid-cols-[auto,1fr]"><div class="flex gap-2"><div class="tag truncate" data-type="L2CreateGroupedOrders">L2CreateGroupedOrders</div><div id="tx-status-3f107c1623f91de24f5c908cb3a9c0d5c6fc00c67d53d46c2f0e68aa83c4085f2df1a8a8d5a4f672" data-status="packed" class="tag">packed</div></div><time class="tracking-tight text-stone-400 ml-auto" x-data="" x-reltime="2025-05-10T00:30:34Z">51 seconds ago</time><div class="flex w-full gap-1 items-center"><div class="flex"><a class="flex font-bold truncate mr-auto clickable" href="/account/0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9" hx-push-url="true" hx-target="#main" x-data="" x-tooltip.interactive="<div class=&quot;bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1&quot; x-data @resize.debounce=&quot;resizeHash($el)&quot;>0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9</div>" x-resize="" @resize.debounce="resizeHash($el)">Account 2570</a></div></div><div class="flex gap-1 items-center justify-end overflow-hidden w-full"><div class="flex shrink-0"><svg class="w-4 h-4 text-gray-200 " aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"><path fill-rule="evenodd" d="M6 6a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h3a3 3 0 0 1-3 3H5a1 1 0 1 0 0 2h1a5 5 0 0 0 5-5V8a2 2 0 0 0-2-2H6Zm9 0a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h3a3 3 0 0 1-3 3h-1a1 1 0 1 0 0 2h1a5 5 0 0 0 5-5V8a2 2 0 0 0-2-2h-3Z" clip-rule="evenodd"></path></svg></div><div class="min-w-0"><div class="flex"><a class="flex font-bold truncate mr-auto clickable" href="/tx/3f107c1623f91de24f5c908cb3a9c0d5c6fc00c67d53d46c2f0e68aa83c4085f2df1a8a8d5a4f672" hx-push-url="true" hx-target="#main" x-data="" x-tooltip.interactive="<div class=&quot;bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1&quot; x-data @resize.debounce=&quot;resizeHash($el)&quot;>3f107c1623f91de24f5c908cb3a9c0d5c6fc00c67d53d46c2f0e68aa83c4085f2df1a8a8d5a4f672</div>" x-resize="" @resize.debounce="resizeHash($el)">3f107c1623f91de24f5c908cb3a9c0d5c6fc00c67d53d46c2f0e68aa83c4085f...f672</a></div></div></div></div></div><div class="px-2 first:rounded-t-lg last:rounded-b-lg odd:bg-primary-800 even:bg-primary-700"><div class="min-w-0 text-sm gap-2 grid grid-rows-2 grid-cols-[auto,1fr]"><div class="flex gap-2"><div class="tag truncate" data-type="L2CreateOrder">L2CreateOrder</div><div id="tx-status-53fd3b83adf18e33ba7a96e4aecf80d422e2194c232ae7d020493a0419ff004626d2690b4ab005d9" data-status="packed" class="tag">packed</div></div><time class="tracking-tight text-stone-400 ml-auto" x-data="" x-reltime="2025-05-10T00:30:28Z">57 seconds ago</time><div class="flex w-full gap-1 items-center"><div class="flex"><a class="flex font-bold truncate mr-auto clickable" href="/account/0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9" hx-push-url="true" hx-target="#main" x-data="" x-tooltip.interactive="<div class=&quot;bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1&quot; x-data @resize.debounce=&quot;resizeHash($el)&quot;>0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9</div>" x-resize="" @resize.debounce="resizeHash($el)">Account 2570</a></div></div><div class="flex gap-1 items-center justify-end overflow-hidden w-full"><div class="flex shrink-0"><svg class="w-4 h-4 text-gray-200 " aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"><path fill-rule="evenodd" d="M6 6a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h3a3 3 0 0 1-3 3H5a1 1 0 1 0 0 2h1a5 5 0 0 0 5-5V8a2 2 0 0 0-2-2H6Zm9 0a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h3a3 3 0 0 1-3 3h-1a1 1 0 1 0 0 2h1a5 5 0 0 0 5-5V8a2 2 0 0 0-2-2h-3Z" clip-rule="evenodd"></path></svg></div><div class="min-w-0"><div class="flex"><a class="flex font-bold truncate mr-auto clickable" href="/tx/53fd3b83adf18e33ba7a96e4aecf80d422e2194c232ae7d020493a0419ff004626d2690b4ab005d9" hx-push-url="true" hx-target="#main" x-data="" x-tooltip.interactive="<div class=&quot;bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1&quot; x-data @resize.debounce=&quot;resizeHash($el)&quot;>53fd3b83adf18e33ba7a96e4aecf80d422e2194c232ae7d020493a0419ff004626d2690b4ab005d9</div>" x-resize="" @resize.debounce="resizeHash($el)">53fd3b83adf18e33ba7a96e4aecf80d422e2194c232ae7d020493a0419ff004626d269...05d9</a></div></div></div></div></div><div class="px-2 first:rounded-t-lg last:rounded-b-lg odd:bg-primary-800 even:bg-primary-700"><div class="min-w-0 text-sm gap-2 grid grid-rows-2 grid-cols-[auto,1fr]"><div class="flex gap-2"><div class="tag truncate" data-type="InternalCancelOrder">InternalCancelOrder</div><div id="tx-status-0000000038afb2ae00000196b7982965000000000000000000000000000000000000000000000000" data-status="packed" class="tag">packed</div></div><time class="tracking-tight text-stone-400 ml-auto" x-data="" x-reltime="2025-05-10T00:28:44Z">2 minutes ago</time><div class="flex w-full gap-1 items-center"><div class="flex"><a class="flex font-bold truncate mr-auto clickable" href="/account/0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9" hx-push-url="true" hx-target="#main" x-data="" x-tooltip.interactive="<div class=&quot;bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1&quot; x-data @resize.debounce=&quot;resizeHash($el)&quot;>0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9</div>" x-resize="" @resize.debounce="resizeHash($el)">Account 2570</a></div></div><div class="flex gap-1 items-center justify-end overflow-hidden w-full"><div class="flex shrink-0"><svg class="w-4 h-4 text-gray-200 " aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"><path fill-rule="evenodd" d="M6 6a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h3a3 3 0 0 1-3 3H5a1 1 0 1 0 0 2h1a5 5 0 0 0 5-5V8a2 2 0 0 0-2-2H6Zm9 0a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h3a3 3 0 0 1-3 3h-1a1 1 0 1 0 0 2h1a5 5 0 0 0 5-5V8a2 2 0 0 0-2-2h-3Z" clip-rule="evenodd"></path></svg></div><div class="min-w-0"><div class="flex"><a class="flex font-bold truncate mr-auto clickable" href="/tx/0000000038afb2ae00000196b7982965000000000000000000000000000000000000000000000000" hx-push-url="true" hx-target="#main" x-data="" x-tooltip.interactive="<div class=&quot;bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1&quot; x-data @resize.debounce=&quot;resizeHash($el)&quot;>0000000038afb2ae00000196b7982965000000000000000000000000000000000000000000000000</div>" x-resize="" @resize.debounce="resizeHash($el)">0000000038afb2ae00000196b798296500000000000000000000000000000...0000</a></div></div></div></div></div><div class="px-2 first:rounded-t-lg last:rounded-b-lg odd:bg-primary-800 even:bg-primary-700"><div class="min-w-0 text-sm gap-2 grid grid-rows-2 grid-cols-[auto,1fr]"><div class="flex gap-2"><div class="tag truncate" data-type="InternalCancelOrder">InternalCancelOrder</div><div id="tx-status-0000000038afb2ad00000196b7982965000000000000000000000000000000000000000000000000" data-status="packed" class="tag">packed</div></div><time class="tracking-tight text-stone-400 ml-auto" x-data="" x-reltime="2025-05-10T00:28:44Z">2 minutes ago</time><div class="flex w-full gap-1 items-center"><div class="flex"><a class="flex font-bold truncate mr-auto clickable" href="/account/0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9" hx-push-url="true" hx-target="#main" x-data="" x-tooltip.interactive="<div class=&quot;bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1&quot; x-data @resize.debounce=&quot;resizeHash($el)&quot;>0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9</div>" x-resize="" @resize.debounce="resizeHash($el)">Account 2570</a></div></div><div class="flex gap-1 items-center justify-end overflow-hidden w-full"><div class="flex shrink-0"><svg class="w-4 h-4 text-gray-200 " aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"><path fill-rule="evenodd" d="M6 6a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h3a3 3 0 0 1-3 3H5a1 1 0 1 0 0 2h1a5 5 0 0 0 5-5V8a2 2 0 0 0-2-2H6Zm9 0a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h3a3 3 0 0 1-3 3h-1a1 1 0 1 0 0 2h1a5 5 0 0 0 5-5V8a2 2 0 0 0-2-2h-3Z" clip-rule="evenodd"></path></svg></div><div class="min-w-0"><div class="flex"><a class="flex font-bold truncate mr-auto clickable" href="/tx/0000000038afb2ad00000196b7982965000000000000000000000000000000000000000000000000" hx-push-url="true" hx-target="#main" x-data="" x-tooltip.interactive="<div class=&quot;bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1&quot; x-data @resize.debounce=&quot;resizeHash($el)&quot;>0000000038afb2ad00000196b7982965000000000000000000000000000000000000000000000000</div>" x-resize="" @resize.debounce="resizeHash($el)">0000000038afb2ad00000196b798296500000000000000000000000000000...0000</a></div></div></div></div></div></div></div><div class="flex space-x-1 p-1 mt-2"><button type="button" disabled="" class="btn opacity-50 cursor-not-allowed"><span aria-hidden="true">«</span> <span class="sr-only">Previous</span></button> <button type="button" hx-target="#txtable" hx-get="/account/0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9?txpage=1&amp;active=txs" class="btn"><span class="sr-only">Next</span> <span aria-hidden="true">»</span></button></div></div></div><div class="custom-tooltip" style="position: absolute; top: 0px; left: 0px; display: none;"><div class="bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1" x-data="" @resize.debounce="resizeHash($el)">0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9</div></div><div class="custom-tooltip" style="position: absolute; top: 295px; left: 1309.46px; display: none;"><div class="bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1" x-data="" @resize.debounce="resizeHash($el)">0000000038b03ebe00000196b799d4f5000000000000000000000000000000000000000000000000</div></div><div class="custom-tooltip" style="position: absolute; top: 0px; left: 0px; display: none;"><div class="bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1" x-data="" @resize.debounce="resizeHash($el)">0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9</div></div><div class="custom-tooltip" style="position: absolute; top: 0px; left: 0px; display: none;"><div class="bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1" x-data="" @resize.debounce="resizeHash($el)">3f107c1623f91de24f5c908cb3a9c0d5c6fc00c67d53d46c2f0e68aa83c4085f2df1a8a8d5a4f672</div></div><div class="custom-tooltip" style="position: absolute; top: 0px; left: 0px; display: none;"><div class="bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1" x-data="" @resize.debounce="resizeHash($el)">0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9</div></div><div class="custom-tooltip" style="position: absolute; top: 473px; left: 916.531px; display: none;"><div class="bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1" x-data="" @resize.debounce="resizeHash($el)">53fd3b83adf18e33ba7a96e4aecf80d422e2194c232ae7d020493a0419ff004626d2690b4ab005d9</div></div><div class="custom-tooltip" style="position: absolute; top: 0px; left: 0px; display: none;"><div class="bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1" x-data="" @resize.debounce="resizeHash($el)">0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9</div></div><div class="custom-tooltip" style="position: absolute; top: 0px; left: 0px; display: none;"><div class="bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1" x-data="" @resize.debounce="resizeHash($el)">0000000038afb2ae00000196b7982965000000000000000000000000000000000000000000000000</div></div><div class="custom-tooltip" style="position: absolute; top: 0px; left: 0px; display: none;"><div class="bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1" x-data="" @resize.debounce="resizeHash($el)">0x65503d0b9C72B1E5EE85bB8c822d96F7Ab20b8d9</div></div><div class="custom-tooltip" style="position: absolute; top: 0px; left: 0px; display: none;"><div class="bg-accent-500 border-accent-400 rounded-lg z-50 text-white text-sm px-2 py-1" x-data="" @resize.debounce="resizeHash($el)">0000000038afb2ad00000196b7982965000000000000000000000000000000000000000000000000</div></div></div></div></body></html>
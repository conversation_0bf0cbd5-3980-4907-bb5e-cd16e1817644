package copier

import (
	"copier/backend/logger"
	"copier/backend/utils"
	"fmt"
	"os/exec"
	"strings"
	"time"
)

type ProxyMapping struct {
	LocalPort  string
	TargetAddr string
}

type Watchdog struct {
	Debug         bool
	ProxyMappings []ProxyMapping
	Clients       []*CopierClient
}

func NewWatchdog(proxyMappings []ProxyMapping, debug *bool) *Watchdog {
	w := &Watchdog{
		ProxyMappings: proxyMappings,
	}
	if debug == nil {
		w.Debug = false
	} else {
		w.Debug = *debug
	}
	for _, mapping := range proxyMappings {
		client := NewCopierClient(fmt.Sprintf("http://127.0.0.1:%s", mapping.LocalPort))
		w.Clients = append(w.Clients, client)
	}
	return w
}

func (w *Watchdog) checkProjects(client *CopierClient) {
	var projects []*Project
	err := utils.RetryDo(func() error {
		var err error
		projects, err = client.GetProjects()
		return err
	}, 3, 1)

	if err != nil {
		logger.Errorf("check projects failed after 3 retries: %s", err)
		exec.Command("say", "watchdog alert: failed to get projects after 3 retries").Run()
		return
	}

	for _, project := range projects {
		if project.DeleteTime != nil {
			continue
		}
		// only check projects that are stopped for more than 1 minutes
		if project.StatusUpdateTime.Before(time.Now().Add(-1 * time.Minute)) {
			// only check projects that are stopped and have remaining positions
			if project.Status == ProjectStatusStopped && !project.IsZeroPositions() {
				shortProjectName := project.ProjectID
				parts := strings.Split(project.ProjectID, "_")
				if len(parts) >= 2 {
					shortProjectName = parts[1]
				}

				// Alert for each agent in the project
				for _, agent := range project.Agents {
					message := fmt.Sprintf("watchdog alert: risk positions, project %s, %s agent", shortProjectName, agent.Role)
					logger.Warnf("%s", message)
					exec.Command("say", message).Run()
				}
			}
		}
	}
}

func (w *Watchdog) Run() {
	interval := 1 * time.Minute
	if w.Debug {
		interval = 3 * time.Second
	}
	ticker := time.NewTicker(interval)
	defer ticker.Stop()
	for range ticker.C {
		for _, client := range w.Clients {
			go w.checkProjects(client)
		}
	}
}

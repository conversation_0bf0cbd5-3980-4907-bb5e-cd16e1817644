// Code generated by "stringer -type=BalancePlanItemStatus"; DO NOT EDIT.

package copier

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[BalancePlanItemStatusPrepareError-0]
	_ = x[BalancePlanItemStatusPrepareDone-1]
	_ = x[BalancePlanItemStatusPlatformWithdrawStart-2]
	_ = x[BalancePlanItemStatusPlatformWithdrawError-3]
	_ = x[BalancePlanItemStatusPlatformWithdrawDone-4]
	_ = x[BalancePlanItemStatusExchangeDepositStart-5]
	_ = x[BalancePlanItemStatusExchangeDepositError-6]
	_ = x[BalancePlanItemStatusExchangeDepositDone-7]
	_ = x[BalancePlanItemStatusExchangeWithdrawStart-8]
	_ = x[BalancePlanItemStatusExchangeWithdrawError-9]
	_ = x[BalancePlanItemStatusExchangeWithdrawDone-10]
	_ = x[BalancePlanItemStatusPlatformDepositStart-11]
	_ = x[BalancePlanItemStatusPlatformDepositError-12]
	_ = x[BalancePlanItemStatusPlatformDepositDone-13]
	_ = x[BalancePlanItemStatusDone-14]
}

const _BalancePlanItemStatus_name = "BalancePlanItemStatusPrepareErrorBalancePlanItemStatusPrepareDoneBalancePlanItemStatusPlatformWithdrawStartBalancePlanItemStatusPlatformWithdrawErrorBalancePlanItemStatusPlatformWithdrawDoneBalancePlanItemStatusExchangeDepositStartBalancePlanItemStatusExchangeDepositErrorBalancePlanItemStatusExchangeDepositDoneBalancePlanItemStatusExchangeWithdrawStartBalancePlanItemStatusExchangeWithdrawErrorBalancePlanItemStatusExchangeWithdrawDoneBalancePlanItemStatusPlatformDepositStartBalancePlanItemStatusPlatformDepositErrorBalancePlanItemStatusPlatformDepositDoneBalancePlanItemStatusDone"

var _BalancePlanItemStatus_index = [...]uint16{0, 33, 65, 107, 149, 190, 231, 272, 312, 354, 396, 437, 478, 519, 559, 584}

func (i BalancePlanItemStatus) String() string {
	if i < 0 || i >= BalancePlanItemStatus(len(_BalancePlanItemStatus_index)-1) {
		return "BalancePlanItemStatus(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _BalancePlanItemStatus_name[_BalancePlanItemStatus_index[i]:_BalancePlanItemStatus_index[i+1]]
}

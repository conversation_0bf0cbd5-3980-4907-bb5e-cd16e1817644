package copier

import (
	"sort"
	"time"
)

type ProjectOverview struct {
	ProjectID         string               `json:"projectId"`
	Status            ProjectStatus        `json:"status"`
	TotalMargin       float64              `json:"totalMargin"`
	AvailableMargin   float64              `json:"availableMargin"`
	Symbol            string               `json:"symbol"`
	DefaultSize       float64              `json:"defaultSize"`
	CoolingHour       float64              `json:"coolingHour"`
	PositionValue     float64              `json:"positionValue"`
	LastPerformance   *Performance         `json:"lastPerformance"`
	LatestPerformance *Performance         `json:"latestPerformance"`
	TodayPerformance  *Performance         `json:"todayPerformance"`
	LastTradeTime     *time.Time           `json:"lastTradeTime"`
	LastRiskTime      *time.Time           `json:"lastRiskTime"`
	LastRiskType      RiskType             `json:"lastRiskType"`
	StatusComment     ProjectStatusComment `json:"statusComment"`
	StatusUpdateTime  *time.Time           `json:"statusUpdateTime"`
}

func (this *CopierController) GetProjectOverviews() []*ProjectOverview {
	overviews := []*ProjectOverview{}
	projectIDs := []string{}
	for _, project := range this.GetProjects(true) {
		projectIDs = append(projectIDs, project.ProjectID)
	}
	sort.Strings(projectIDs)
	for _, projectID := range projectIDs {
		project, _ := this.Projects.Load(projectID)
		if project.DeleteTime != nil {
			continue
		}
		masterAgent := project.GetMasterAgent()
		symbol := ""
		defaultSize := 0.0
		coolingHour := 0.0
		if masterAgent != nil {
			symbol = masterAgent.Settings.DefaultSymbol
			defaultSize = masterAgent.Settings.DefaultSize
			coolingHour = masterAgent.Settings.CoolingHour
		}
		// 因为 project 后续可能没有交易记录，其 latestPerformance 和 todayPerformance 可能是较早日期的记录，这里需要看一下是不是最新的
		// autoStop 依赖 latestPerformance 和 todayPerformance 的值，所以需要确保它们是最新的
		latestPerformance := &Performance{}

		if project.LatestPerformance != nil {
			withinWeek := isWithinWeekRange(project.LatestPerformance.Date)
			if withinWeek {
				latestPerformance = project.LatestPerformance
			}
		}
		todayPerformance := &Performance{}
		if project.TodayPerformance != nil {
			withinToday := isWithinTodayRange(project.TodayPerformance.Date)
			if withinToday {
				todayPerformance = project.TodayPerformance
			}
		}
		lastPerformance := &Performance{}
		if project.LastPerformance != nil {
			withinLastWeek := isWithinLastWeekRange(project.LastPerformance.Date)
			if withinLastWeek {
				lastPerformance = project.LastPerformance
			}
		}
		overview := &ProjectOverview{
			ProjectID:         project.ProjectID,
			Status:            project.Status,
			StatusComment:     project.StatusComment,
			TotalMargin:       project.TotalMargin,
			AvailableMargin:   project.AvailableMargin,
			Symbol:            symbol,
			DefaultSize:       defaultSize,
			CoolingHour:       coolingHour,
			PositionValue:     project.PositionValue,
			LastPerformance:   lastPerformance,
			LatestPerformance: latestPerformance,
			TodayPerformance:  todayPerformance,
			LastTradeTime:     project.LastTradeTime,
			LastRiskTime:      project.LastRiskTime,
			LastRiskType:      project.LastRiskType,
			StatusUpdateTime:  &project.StatusUpdateTime,
		}
		overviews = append(overviews, overview)
	}
	return overviews
}

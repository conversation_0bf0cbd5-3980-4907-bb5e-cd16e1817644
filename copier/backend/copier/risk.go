package copier

import (
	"copier/backend/logger"
	"copier/backend/utils"
	"fmt"
	"math"
	"time"

	"github.com/puzpuzpuz/xsync"
)

func isMarginCall(agent Agent) (bool, float64) {
	threshold := 0.1
	if agent.Platform == PlatformLighter {
		threshold = 0.05
	}
	positionValue := 0.0
	for _, position := range agent.Positions {
		positionValue += position.Size * position.MarkPrice
	}
	ratio := agent.TotalMargin / positionValue
	return ratio < threshold, ratio
}

type RiskType string

const (
	RiskTypeAgentDown           RiskType = "agent_down"
	RiskTypeAgentNotSyncing     RiskType = "agent_not_syncing"
	RiskTypeMarginLow           RiskType = "margin_low"
	RiskTypeMarginCall          RiskType = "margin_call"
	RiskTypePositionNotBalanced RiskType = "position_not_balanced"
	RiskTypeMasterCountWrong    RiskType = "master_count_wrong"
	RiskTypeStoploss            RiskType = "stoploss"
)

type Risk struct {
	Type       RiskType
	ProjectID  string
	UserID     string
	Comment    string
	CreateTime time.Time
	ResetTime  *time.Time
}

func (this Risk) GetKey() string {
	return fmt.Sprintf("%s||%s||%s", this.Type, this.ProjectID, this.UserID)
}

type RiskEventType string

type RiskEvent struct {
	RiskType   RiskType
	ProjectID  string
	UserID     string
	Comment    string
	CreateTime time.Time
}

func almostEqualTo(a, b float64) (bool, float64) {
	if a == b {
		return true, 0
	}
	diff := math.Abs(a - b)
	larger := math.Abs(math.Max(a, b))
	if a < 0 || b < 0 {
		larger = math.Abs(math.Min(a, b))
	}
	return diff/larger < 0.001, diff
}

func (this *CopierController) resetRiskEvent(riskType RiskType, projectID string, userID string) {
	key := fmt.Sprintf("%s||%s||%s", riskType, projectID, userID)
	this.riskEventResetTime.Store(key, time.Now())
}

func (this *CopierController) CheckRisks() (result []*Risk) {
	this.checkRiskMutex.Lock()
	defer this.checkRiskMutex.Unlock()

	projectIDs := []string{}
	// 检查单个风险事件是否发生，如果发生，则记录到 ProjectRiskEvents 中，后续会根据 ProjectRiskEvents 中的事件，判断是否触发报警
	this.Projects.Range(func(projectID string, project *Project) bool {
		if projectID == "" || project.DeleteTime != nil {
			// 如果项目处于删除状态，则不检查风险
			return true
		}

		// 如果项目处于 idle 状态，完全不报警
		// 由用户自己检查 ExtPos 的仓位是否止损
		if project.IsIdle() && time.Since(this.launchTime) > 2*time.Minute {
			return true
		}

		events, _ := this.ProjectRiskEvents.LoadOrStore(projectID, []*RiskEvent{})
		agents := project.Agents
		projectIDs = append(projectIDs, projectID)

		if len(agents) < 2 {
			events = append(events, &RiskEvent{RiskType: RiskTypeAgentDown, ProjectID: projectID, UserID: "", CreateTime: time.Now()})
			this.ProjectRiskEvents.Store(projectID, events)
			return true
		} else {
			this.resetRiskEvent(RiskTypeAgentDown, projectID, "")
		}

		numOfMasters := 0
		masterSize := 0.0
		manualMode := false // 手工模式，不检查 margin_call 事件
		for _, agent := range agents {
			if agent.Role == RoleMaster {
				numOfMasters++
				if agent.Settings.ManualMode {
					manualMode = true
				}
				for _, position := range agent.Positions {
					if position.Side == PositionSideLong {
						masterSize += position.Size
					} else {
						masterSize -= position.Size
					}
				}
			}
		}

		// 如果有不是 1 个 master ，则认为有风险
		if numOfMasters != 1 {
			events = append(events, &RiskEvent{RiskType: RiskTypeMasterCountWrong, ProjectID: projectID, UserID: "", CreateTime: time.Now()})
			this.ProjectRiskEvents.Store(projectID, events)
			return true
		}

		copierSize := 0.0
		relaxBalanceCheck := false
		lastMasterUpdateTime := time.Time{} // used for check if copier agent not syncing
		lastCopierUpdateTime := time.Time{} // used for check if copier agent not syncing
		for _, agent := range agents {
			// if any copier's copyPercentage isn't 100%, then relax the balance check
			if agent.Role == RoleCopier && agent.Settings.CopyPercentage != 100 {
				relaxBalanceCheck = true
			}

			if agent.Role == RoleMaster {
				if agent.CreateTime.After(lastMasterUpdateTime) {
					lastMasterUpdateTime = agent.CreateTime
				}
			} else {
				if agent.CreateTime.After(lastCopierUpdateTime) {
					lastCopierUpdateTime = agent.CreateTime
				}
			}

			// 如果 agent 更新时间超过 2 个周期(6秒），则认为 agent 未同步，触发风险事件
			now := time.Now()

			// 如果项目处于停止状态时，不检查 agent_not_syncing 事件
			// 如果触发 agent_not_syncing 事件，那么某些 agent 关闭（关闭交易平台网页休息的时候），也会一直报警
			// idle 状态下 extension 多数情况已经关闭，不太可能继续上报数据
			if !project.IsIdle() {
				if project.Status == ProjectStatusRunning {
					if agent.CreateTime.Before(now.Add(-12*time.Second)) && agent.CreateTime.After(project.StatusUpdateTime) {
						comment := fmt.Sprintf("最后更新时间：%s", utils.FormatShortTimeStr(&agent.CreateTime, false))
						events = append(events, &RiskEvent{RiskType: RiskTypeAgentNotSyncing, ProjectID: projectID, UserID: agent.UserID, Comment: comment, CreateTime: now})
					} else {
						this.resetRiskEvent(RiskTypeAgentNotSyncing, projectID, agent.UserID)
					}
				}
				// 如果保证金低于最低保证金，则触发风险事件
				if agent.TotalMargin < agent.Settings.MinMargin && agent.CreateTime.After(project.StatusUpdateTime) {
					comment := fmt.Sprintf("保证金：%.2f, 最低保证金：%.2f", agent.TotalMargin, agent.Settings.MinMargin)
					events = append(events, &RiskEvent{RiskType: RiskTypeMarginLow, ProjectID: projectID, UserID: agent.UserID, Comment: comment, CreateTime: now})
				} else {
					this.resetRiskEvent(RiskTypeMarginLow, projectID, agent.UserID)
				}
				// 如果保证金低于最低保证金，则触发风险事件
				marginCalled, ratio := isMarginCall(*agent)
				if marginCalled && !manualMode && agent.CreateTime.After(project.StatusUpdateTime) {
					comment := fmt.Sprintf("保证金率：%.2f%%", ratio*100)
					events = append(events, &RiskEvent{RiskType: RiskTypeMarginCall, ProjectID: projectID, UserID: agent.UserID, Comment: comment, CreateTime: now})
				} else {
					this.resetRiskEvent(RiskTypeMarginCall, projectID, agent.UserID)
				}
			}

			// 虽然 idle 状态下，extension 可能已经关闭不再上报数据，但是最后状态如果是持仓不平衡，还是一样需要警告
			// 但是可能会重置掉 idle 状态，顶多也就是继续报警 agent_not_syncing 之类的，风险较小
			for _, position := range agent.Positions {
				if position.Side == PositionSideLong {
					copierSize += position.Size
				} else {
					copierSize -= position.Size
				}
			}
		}

		// 如果持仓不为 0，则触发风险事件
		// 这里假设只有一种品种的持仓
		diff := math.Abs(copierSize)
		diffRatio := 0.0
		if masterSize != 0 {
			diffRatio = diff / math.Abs(masterSize)
		}
		balanced, _ := almostEqualTo(diff, 0)
		// if any copier's copyPercentage isn't 100%, then relax the check
		if !balanced && relaxBalanceCheck {
			if masterSize != 0 {
				diffRatio = diff / math.Abs(masterSize)
				balanced = diffRatio <= 0.05 // 允许 5% 以内的误差
			} else {
				balanced, _ = almostEqualTo(diff, 0)
			}
		}
		if !balanced {
			// 仅在所有 copier 的更新时间都大于 master 的更新时间，并且仓位不平衡，才触发风险事件
			if lastMasterUpdateTime.Before(lastCopierUpdateTime) {
				comment := fmt.Sprintf("持仓：%.4f, 误差比例：%.4f%%", diff, diffRatio*100)
				events = append(events, &RiskEvent{RiskType: RiskTypePositionNotBalanced, ProjectID: projectID, UserID: "", Comment: comment, CreateTime: time.Now()})
			}
		} else {
			this.resetRiskEvent(RiskTypePositionNotBalanced, projectID, "")
		}

		this.ProjectRiskEvents.Store(projectID, events)
		return true
	})

	// 检查最近一段时间的风险事件，是否发生次数超过限制，如果超过限制，则触发报警
	for _, projectID := range projectIDs {
		project, ok := this.Projects.Load(projectID)
		// 即使项目处于 stopped 状态，也检查风险
		// 处于 stopped 状态时，不会产生 agent_not_syncing 事件，因此不会很骚扰
		// 但是如果 agent 一直还在上报数据，可能会报告其他事件，比如 margin_low, position_not_balanced 等
		// 这些事件，其实有发送报警的需要，以免 stopped 了，某些仓位还一直开着，却不知道
		if !ok {
			continue
		}
		events, _ := this.ProjectRiskEvents.Load(projectID)
		// 仅记录最近 1 分钟内的事件
		validEvents := xsync.NewMapOf[[]*RiskEvent]()
		for _, event := range events {
			key := fmt.Sprintf("%s||%s||%s", event.RiskType, event.ProjectID, event.UserID)

			// 在重置事件之前的事件，不考虑
			if resetTime, ok := this.riskEventResetTime.Load(key); ok {
				if event.CreateTime.Before(resetTime) {
					logger.Debugf("event is before reset time: %s, skip, event: %#v", resetTime, event)
					continue
				}
			}

			events, _ := validEvents.LoadOrStore(key, []*RiskEvent{})
			// 仅考虑 1 分钟内的事件，如果事件发生时间超过 1 分钟，则认为事件已经过期，不再考虑
			if event.CreateTime.After(time.Now().Add(-1 * time.Minute)) {
				events = append(events, event)
			}
			validEvents.Store(key, events)
		}

		// 如果事件发生次数超过限制，则触发报警
		risks, _ := this.ProjectRisks.LoadOrStore(projectID, []*Risk{})
		var lastRisk *Risk = nil
		if len(risks) > 0 {
			lastRisk = risks[len(risks)-1]
		}
		validEvents.Range(func(key string, events []*RiskEvent) bool {
			// 每 5 秒钟检查一次 risks，一分钟内可能有 12 个事件，如果有超过 3 个事件，则认为有风险
			if len(events) > 7 {
				lastEvent := events[len(events)-1]
				if lastRisk == nil || lastEvent.CreateTime.After(lastRisk.CreateTime.Add(1*time.Minute)) {
					risks = append(risks, &Risk{Type: RiskType(lastEvent.RiskType),
						ProjectID:  lastEvent.ProjectID,
						UserID:     lastEvent.UserID,
						Comment:    lastEvent.Comment,
						CreateTime: lastEvent.CreateTime,
					})
				}
			}
			return true
		})

		// 检查 risk 是否早于 resetTime，如果早于 resetTime，则更新 resetTime
		// 如果仓位不平衡 risk 出现两次以上，自动停止运行
		last5Minutes := time.Now().Add(-5 * time.Minute)
		positionNotBalancedCount := 0
		marginLowCount := 0
		for _, risk := range risks {
			key := fmt.Sprintf("%s||%s||%s", risk.Type, projectID, risk.UserID)
			resetTime, exists := this.riskEventResetTime.Load(key)
			if exists && risk.ResetTime == nil {
				if risk.CreateTime.Before(resetTime) || risk.CreateTime.Before(project.StatusUpdateTime) {
					risk.ResetTime = &resetTime
					continue
				}
			}
			// resetTime 为 nil，则认为没有重置事件，需要检查是否需要停止 project
			if risk.ResetTime == nil {
				// 如果在最近 5 分钟有两个以上 PositionNotBalanced 事件，则停止运行 project
				if risk.Type == RiskTypePositionNotBalanced && risk.CreateTime.After(last5Minutes) {
					positionNotBalancedCount++
				}
				if risk.Type == RiskTypeMarginLow && risk.CreateTime.After(last5Minutes) {
					marginLowCount++
				}
				// if resetTime is nil, then update lastRiskTime
				if project.LastRiskTime == nil || risk.CreateTime.After(*project.LastRiskTime) {
					project.LastRiskTime = &risk.CreateTime
					project.LastRiskType = risk.Type
				}
			}
		}

		// 如果最近 5 分钟有两个以上 PositionNotBalanced 事件，或者最近 5 分钟有一个 MarginLow 事件，则停止运行 project
		if positionNotBalancedCount >= 2 || marginLowCount >= 1 {
			this.StopProject(projectID, ProjectStatusCommentRisk)
		}

		this.ProjectRisks.Store(projectID, risks)
		result = append(result, risks...)
	}

	return
}

import pyautogui
import cv2
import numpy as np
import os
import sys
import argparse
from PIL import Image
from collections import namedtuple

default_icon_path = "c:/programs/copier_ext/icons/target_icon.png"

def merge_nearby_points(points, threshold=10):
    """Merge points that are within threshold pixels of each other."""
    if not points:
        return points
    
    merged = []
    used = set()
    
    for i, point in enumerate(points):
        if i in used:
            continue
            
        x1, y1 = point
        group = [point]
        used.add(i)
        
        # Find all points within threshold distance
        for j, other_point in enumerate(points):
            if j in used or j <= i:
                continue
                
            x2, y2 = other_point
            distance = ((x1 - x2) ** 2 + (y1 - y2) ** 2) ** 0.5
            
            if distance <= threshold:
                group.append(other_point)
                used.add(j)
        
        # Calculate average position of grouped points
        avg_x = sum(p[0] for p in group) // len(group)
        avg_y = sum(p[1] for p in group) // len(group)
        merged.append((avg_x, avg_y))
        
        print(f"Merged {len(group)} points into ({avg_x}, {avg_y})")
    
    return merged


def find_extension_icons(extension_dir=None):
    """Find all extension icons on the screen, using dynamic screenshot resizing."""
    screen_width, screen_height = pyautogui.size()
    print(f"Screen resolution: {screen_width}x{screen_height}")

    locations = []
    reference_height = 1440

    # Determine the primary icon path and reference path
    if extension_dir:
        specific_icon_path = os.path.join(extension_dir, "icons", f"target_icon_{screen_height}.png")
        reference_icon_path = os.path.join(extension_dir, "icons", f"target_icon_{reference_height}.png")
    else:
        # Fallback to default paths if no extension_dir is provided
        specific_icon_path = default_icon_path.replace('.png', f'_{screen_height}.png')
        reference_icon_path = default_icon_path.replace('.png', f'_{reference_height}.png')
        # a bit of hack to make it work with the default path
        if not os.path.exists(reference_icon_path):
            reference_icon_path = default_icon_path

    print(f"Looking for specific icon at: {specific_icon_path}")
    if os.path.exists(specific_icon_path):
        print(f"Found resolution-specific icon: {specific_icon_path}")
        confidence_levels = [0.9, 0.8]
        for confidence in confidence_levels:
            try:
                locations = list(pyautogui.locateAllOnScreen(specific_icon_path, confidence=confidence))
                if locations:
                    print(f"Found icons with confidence level: {confidence}")
                    break
            except Exception as e:
                print(f"Warning: Failed to find icons with confidence {confidence}: {e}")
    else:
        print(f"Specific icon not found. Looking for reference icon: {reference_icon_path}")
        if not os.path.exists(reference_icon_path):
            raise FileNotFoundError(f"Neither specific nor reference icon found at {reference_icon_path}")

        # Decide whether to use scaling or direct search
        scale_factor = reference_height / screen_height
        if scale_factor > 1.0:
            print("Using screenshot scaling method.")
            screenshot = pyautogui.screenshot()
            new_width = int(screen_width * scale_factor)
            new_height = int(screen_height * scale_factor)
            
            print(f"Scaling screenshot from {screen_width}x{screen_height} to {new_width}x{new_height}")
            resized_screenshot = screenshot.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            confidence_levels = [0.9, 0.8]
            locations_in_resized = []
            for confidence in confidence_levels:
                try:
                    reference_icon_image = Image.open(reference_icon_path)
                    locations_in_resized = list(pyautogui.locateAll(reference_icon_image, resized_screenshot, confidence=confidence))
                    if locations_in_resized:
                        print(f"Found icons in resized screenshot with confidence level: {confidence}")
                        break
                except Exception as e:
                    print(f"Warning: Failed to find icons in resized screenshot with confidence {confidence}: {e}")
            
            if locations_in_resized:
                Box = namedtuple('Box', ['left', 'top', 'width', 'height'])
                locations = [Box(left=int(loc.left / scale_factor), top=int(loc.top / scale_factor), width=int(loc.width / scale_factor), height=int(loc.height / scale_factor)) for loc in locations_in_resized]
        else:
            print("Screen height is >= reference height. Using normal search.")
            confidence_levels = [0.9, 0.8]
            for confidence in confidence_levels:
                try:
                    locations = list(pyautogui.locateAllOnScreen(reference_icon_path, confidence=confidence))
                    if locations:
                        print(f"Found icons with confidence level: {confidence}")
                        break
                except Exception as e:
                    print(f"Warning: Failed to find icons with confidence {confidence}: {e}")

    if not locations:
        raise Exception("No extension icons found on screen")

    points = [(loc.left, loc.top) for loc in locations]
    for x, y in points:
        print(f"x: {x}, y: {y}")

    merged_points = merge_nearby_points(points)
    print(f"After merging: {len(merged_points)} distinct icons")

    merged_points.sort(key=lambda p: p[1])
    
    return merged_points

def get_button_position(extension_dir=None):
    """Get the position of the extension button (topmost icon)."""
    points = find_extension_icons(extension_dir)
    if not points:
        raise Exception("No extension icons found")
    return points[0]

def click_extension_icon(extension_dir=None):
    """Find and click the extension icon."""
    pos = get_button_position(extension_dir)
    # Add a small delay before clicking
    pyautogui.PAUSE = 0.5
    pyautogui.click(pos[0], pos[1])
    print(f"clicked at position: {pos}")
    # move mouse to center of the screen, to eliminate the effect of the click
    pyautogui.moveTo(pyautogui.size()[0] / 2, pyautogui.size()[1] / 2)
    return True

def check_extension_status(extension_dir=None):
    """Check if exactly 2 extension icons are present."""
    points = find_extension_icons(extension_dir)
    if len(points) != 2:
        raise Exception(f"Expected 2 extension icons, found {len(points)}")
    return True

def main():
    parser = argparse.ArgumentParser(description='Extension automation script')
    parser.add_argument('--check-status', action='store_true', help='Check extension status')
    parser.add_argument('--extension-dir', type=str, help='Path to extension directory')
    args = parser.parse_args()

    if args.check_status:
        try:
            check_extension_status(args.extension_dir)
            print("Extension status check passed")
            sys.exit(0)
        except Exception as e:
            print(f"Error: {e}")
            sys.exit(1)
    else:
        try:
            click_extension_icon(args.extension_dir)
            print("Successfully clicked extension icon")
            sys.exit(0)
        except Exception as e:
            print(f"Error: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()

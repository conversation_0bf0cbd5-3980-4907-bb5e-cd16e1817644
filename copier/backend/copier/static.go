package copier

import (
	"embed"
	"fmt"
	"io/fs"
	"net/http"
)

//go:embed static/* static/index.html
var staticContent embed.FS

// GetStaticFS returns a http.FileSystem for serving static files.
// If debug is true, it serves files from the local filesystem.
// If debug is false, it serves files from the embedded filesystem.
func GetStaticFS(debug bool) (http.FileSystem, error) {
	if debug {
		// In debug mode, serve from the local filesystem
		return http.Dir("copier/static"), nil
	}

	// In production mode, use embedded filesystem
	// Check if static directory exists in the embedded filesystem
	entries, err := fs.ReadDir(staticContent, "static")
	if err != nil {
		return nil, fmt.Errorf("failed to read embedded static directory: %w", err)
	}

	if len(entries) == 0 {
		return nil, fmt.Errorf("no static files found in embedded filesystem")
	}

	// Create a subtree filesystem
	sub, err := fs.Sub(staticContent, "static")
	if err != nil {
		return nil, fmt.Errorf("failed to get subfolder 'static' from embedded filesystem: %w", err)
	}

	return http.FS(sub), nil
}

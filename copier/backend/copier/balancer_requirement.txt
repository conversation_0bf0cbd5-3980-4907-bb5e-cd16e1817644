balancer backend console webpage requirement

like the projects webpage, we are marking a independent webpage console for balancer, using alphine js

files
----
1. balancer.html, for console webpage
2. balancer_app.js, for business logic js code
3. balancer_styles.css, for ui styling
4. server side endpoint in server.go 
5. BlanacePlan data structure in balancer.go 

general rules
----
1. css always put in balancer_styles.css, don't embed css in .html pages
2. use the same frontend tech as projects webpage, existed in index.html

requirement
----

1. navigation design
a. use the same sidebar(on the right), content on the right side, consulting projects for current implmentation
b. on the top is a fixed item "make plans", selecting this item shows a plan making page for multiple projects
c. following the item "make plans" display individual plans, each plan as a selectable item, use refID as selectable item label, order the items backwards in createTime
d. request /balancer/plans to get all plans info all at once

2. we have two modes in make plans page, automatic / targeted, I'll describe them seperately and and then we'll merge two requirements into one page

3. make plans page -- automatic mode
a. a "platform" select, currently only one item "lighter"
b. a group of checkboxes "projects", show all the project ids, make them arrange in flow
c. user have to select at least one project_id
d. making request to /balancer/plan/make, passing platform, project_ids, consulting backend code in server.go

4. make plans page -- targeted mode
leave it blank for now, we'll implement it later
a. a "platform" select, current only one item "lighter"
b. a select "stage"of platform_withraw, exchange_deposit, exchange_withdraw, platform_deposit
c. a group of checkboxes "projects", select at least one project, make them arrange in flow, order the project ids alphbetically
d. an amount input(non-negative, allow empty or 0)
e. use the same /balancer/plan/make, specify stage and amount to make it into targeted mode

5. we need to merge two modes in one single make plans page, just allow select ""(empty) stage in the page is enough

6. plan detail page, display
a. display the plan info on the detail page, with a "start" button(a "cancel" button after started)
b. display a group of info about the plan on the top, like a table
c. show each BalancePlanItem as a table, with as "cancel" button
d. importantly each BalancePlanItem may contains 4 txs: PlatformWithdrawTx, ExchangeDepositTx, ExchangeWithdrawTx, PlatformDepositTx
e. arrange these 4 txs under the item table and group them with item info together visually
f. each tx may have actionable buttons associate with them, I'll specify them on the following docs
g. don't display sensitive info like privatekey on the page
h. please make a design to show the txs and made them easy to determine the status and error if there is one
i. consulting balancer.go to have better understanding of BalancePlan status and BalancePlanItem status

7. plan datail page, actions
a. the plan can be "start" with /balancer/plan/start
a. the whole plan can be canceled with /balancer/plan/cancel
b. every item can be canceled with /balancer/plan/item/cancel
c. every tx can be either retried or marked as done with /balancer/tx/action 
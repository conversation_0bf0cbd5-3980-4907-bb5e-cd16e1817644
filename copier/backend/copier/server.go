package copier

import (
	"chaintools/hopper"
	"copier/backend/logger"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"runtime"
	slices "slices"
	sort "sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"copier/backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/mitchellh/go-homedir"
	"github.com/wizhodl/pgate/license"
)

type HealthResponse struct {
	Status               string               `json:"status"`
	Version              string               `json:"version"`
	ProjectID            string               `json:"projectId"`
	ProjectStatus        ProjectStatus        `json:"projectStatus"`
	ProjectStatusComment ProjectStatusComment `json:"projectStatusComment"`
	StatusUpdateTime     time.Time            `json:"statusUpdateTime"`
	ExtensionVersion     string               `json:"extensionVersion"`
	Options              *CopierOptions       `json:"options"`
}

type GetAgentsResponse struct {
	Agents Agents `json:"agents"`
}

type UpdateAgentRequest = Agent

type UpdateAgentResponse struct {
	Agent Agent `json:"agent"`
}

type GetRisksResponse struct {
	Risks []*Risk `json:"risks"`
}

type GetProjectsResponse struct {
	Projects []*Project `json:"projects"`
}

type UpdateStatusRequest struct {
	ProjectIDs string               `json:"projectIds"` // comma separated project IDs
	Status     ProjectStatus        `json:"status"`
	Comment    ProjectStatusComment `json:"comment"`
}

type UpdateStatusResponse struct {
	Success bool `json:"success"`
}

type GetProjectResponse struct {
	Project *Project `json:"project"`
}

type DeleteProjectResponse struct {
	Success bool `json:"success"`
}

type UpdateSettingsRequest struct {
	UserID   string        `json:"userId"`
	Settings AgentSettings `json:"settings"`
}

type UpdateSettingsResponse struct {
	Success  bool          `json:"success"`
	Settings AgentSettings `json:"settings"`
}

type BatchUpdateSettings struct {
	DefaultSymbol   string  `json:"defaultSymbol"`
	DefaultSize     float64 `json:"defaultSize"`
	MinMargin       float64 `json:"minMargin"`
	CoolingHour     float64 `json:"coolingHour"`
	StopLossBuffer  float64 `json:"stopLossBuffer"`
	RefreshInterval int     `json:"refreshInterval"`
	CopyPercentage  int     `json:"copyPercentage"` // copier only
}

type BatchUpdateSettingsRequest struct {
	ProjectIDs string              `json:"projectIds"`
	Settings   BatchUpdateSettings `json:"settings"`
}

type BatchUpdateSettingsResponse struct {
	Success bool `json:"success"`
}

type GetProjectOverviewsResponse struct {
	Overviews []*ProjectOverview `json:"overviews"`
}

type ChangeProjectIDRequest struct {
	NewProjectID string `json:"newProjectId"`
}

type ChangeProjectIDResponse struct {
	Success bool `json:"success"`
}

type SayRequest struct {
	Message string `json:"message"`
}

type SayResponse struct {
	Success bool `json:"success"`
}

type GetBalancerPlansResponse struct {
	Plans []*BalancePlan `json:"plans"`
}

type AmountDirective string

const (
	AmountDirectiveExact  AmountDirective = "exact"
	AmountDirectiveTarget AmountDirective = "target"
)

type MakeBalancerPlanRequest struct {
	Platform        Platform        `json:"platform"`
	ExchangeID      string          `json:"exchangeId"`
	ProjectIDs      []string        `json:"projectIds"`
	WalletID        string          `json:"walletId"`
	Addresses       []string        `json:"addresses"`
	Coin            string          `json:"coin"`
	WaitSeconds     int             `json:"waitSeconds"` // max wait seconds for each tx, randomnize between 0 and this value, txs will be sent in random order
	Stages          []BalanceStage  `json:"stages"`
	Amount          float64         `json:"amount"`
	AmountRandom    bool            `json:"amountRandom"`
	AmountDirective AmountDirective `json:"amountDirective"`
}

type MakeBalancerPlanResponse struct {
	Plan *BalancePlan `json:"plan"`
}

type CancelBalancerPlanRequest struct {
	PlanRefID string `json:"planRefId"`
}

type CancelBalancerPlanResponse struct {
	Success bool `json:"success"`
}

type ProcessBalancerPlanRequest struct {
	PlanRefID string `json:"planRefId"`
}

type ProcessBalancerPlanResponse struct {
	Success bool `json:"success"`
}

type MarkExtensionWithdrawDoneRequest struct {
	ProjectID string `json:"projectId"`
	TxRefID   string `json:"txRefId"`
}

type CancelBalancerPlanItemRequest struct {
	PlanRefID string `json:"planRefId"`
	ItemRefID string `json:"itemRefId"`
}

type CancelBalancerPlanItemResponse struct {
	Success bool `json:"success"`
}

type TxActionRequest struct {
	PlanRefID string   `json:"planRefId"`
	TxRefID   string   `json:"txRefId"`
	Action    TxAction `json:"action"`
}

type TxActionResponse struct {
	Success bool `json:"success"`
}

type DeleteBalancerPlanRequest struct {
	PlanRefID string `json:"planRefId"`
}

type DeleteBalancerPlanResponse struct {
	Success bool `json:"success"`
}

type SetIdleRequest struct {
	ProjectIDs []string `json:"projectIds"`
}

type SetIdleResponse struct {
	Success bool `json:"success"`
}

type AddServerRequestRequest struct {
	ProjectIDs  string            `json:"projectIds"`
	UserIDs     string            `json:"userIds"`
	RequestType ServerRequestType `json:"requestType"`
}

type AddServerRequestResponse struct {
	Success bool `json:"success"`
}

type FinishServerRequestRequest struct {
	RequestID string `json:"requestId"`
	Status    string `json:"status"`
	Comment   string `json:"comment"`
}

type FinishServerRequestResponse struct {
	Success bool `json:"success"`
}

type UpdateOptionsRequest struct {
	Options *CopierOptions `json:"options"`
}

func readExtensionVersion(extensionDir string) string {
	versionFile := filepath.Join(extensionDir, "version.js")
	versionData, err := os.ReadFile(versionFile)
	if err != nil {
		return ""
	}
	versionStr := string(versionData)
	parts := strings.Split(versionStr, "\"")
	if len(parts) < 2 {
		return ""
	}
	return parts[1]
}

type Server struct {
	buildTime      *time.Time
	router         *gin.Engine
	commitHash     string
	licenseManager *license.TimeLockLicenseManager
	controller     *CopierController
	staticFS       http.FileSystem
	port           int
	licenseExpired bool
	licenseMutex   sync.RWMutex
}

func NewServer(buildTime, commitHash string, dataDir string, port int, debugOverride *bool) (*Server, error) {
	dataDir, _ = homedir.Expand(dataDir)

	licenseManager, err := license.NewTimeLockLicenseManager(license.LicenseProjectCopier, buildTime, nil, 180*24*time.Hour, path.Join(dataDir, "licenses.json"))
	if err != nil {
		return nil, fmt.Errorf("new license manager failed: %w", err)
	}

	if err := licenseManager.Check(); err != nil {
		return nil, fmt.Errorf("license check failed: %v", err)
	}

	pythonExe := findPythonExecutable()
	logger.Infof("using python executable: %s", pythonExe)

	options := &CopierOptions{}
	err = options.Load(dataDir, debugOverride)
	if err != nil {
		return nil, fmt.Errorf("failed to load copier options: %w", err)
	}

	controller, err := NewCopierController(dataDir, options)
	if err != nil {
		return nil, fmt.Errorf("failed to create controller: %w", err)
	}
	// Serve static files
	staticFS, err := GetStaticFS(options.Debug)
	if err != nil {
		return nil, fmt.Errorf("failed to get static fs: %w", err)
	}

	server := &Server{
		buildTime:      licenseManager.BuildTime,
		commitHash:     commitHash,
		licenseManager: licenseManager,
		controller:     controller,
		staticFS:       staticFS,
		port:           port,
		licenseExpired: false,
	}
	server.router = server.SetupHandlers()

	// Start periodic license checking
	go server.startPeriodicLicenseCheck()

	return server, nil
}

func (this *Server) Version() string {
	return fmt.Sprintf("%s/%s", this.commitHash, this.buildTime.Format("2006-01-02.15:04:05"))
}

// startPeriodicLicenseCheck checks license status every minute
func (this *Server) startPeriodicLicenseCheck() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			this.licenseMutex.Lock()
			err := this.licenseManager.Check()
			this.licenseExpired = err != nil
			if err != nil {
				logger.Errorf("License check failed: %v", err)
			}
			this.licenseMutex.Unlock()
		}
	}
}

// LicenseMiddleware checks if license is expired for all endpoints except "/"
func (this *Server) LicenseMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip license check for root path
		if c.Request.URL.Path == "/" {
			c.Next()
			return
		}

		this.licenseMutex.RLock()
		expired := this.licenseExpired
		this.licenseMutex.RUnlock()

		if expired {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "License expired",
				"message": "Your license has expired. Please renew your license to continue using this service.",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

func (this *Server) SetupHandlers() *gin.Engine {
	router := gin.Default()
	// Add CORS middleware
	router.Use(CORSMiddleware())

	// Add license middleware for all routes except "/"
	router.Use(this.LicenseMiddleware())

	// Serve static files under /static path
	router.StaticFS("/static", this.staticFS)

	// Set up a handler for the root path to serve index.html
	router.GET("/", this.IndexHandler)

	// Set up a handler for the balancer console page
	router.GET("/balancer", this.BalancerHandler)

	// Set up a handler for the automation console page
	router.GET("/automation", this.AutomationHandler)

	router.GET("/health", this.HealthHandler)
	router.POST("/options/edit", this.UpdateOptionsHandler)

	// Projects API endpoint - returns JSON list of all projects
	router.GET("/risks/:project_id", this.GetRisksHandler)
	router.GET("/risks/all", this.GetAllRisksHandler)
	router.GET("/overviews", this.GetOverviewsHandler)
	router.GET("/agents/:project_id", this.GetAgentsHandler)
	router.GET("/projects", this.GetProjectsHandler)
	router.GET("/project/:project_id", this.GetProjectHandler)
	router.GET("/performance/:project_id", this.GetPerformanceHandler)
	router.GET("/changes/:project_id", this.GetChangesHandler)
	router.POST("/update/:project_id", this.UpdateAgentHandler)

	router.GET("/say", this.SayHandler)
	router.POST("/update-status", this.UpdateStatusHandler)
	router.DELETE("/delete/:project_id", this.DeleteProjectHandler)
	router.POST("/reset/:project_id", this.ResetProjectHandler)
	router.POST("/change-project-id/:project_id", this.ChangeProjectIDHandler)
	router.POST("/server-request/add", this.AddServerRequestHandler)
	router.POST("/server-request/finish", this.FinishServerRequestHandler)

	router.GET("/close-chrome", this.CloseChromeHandler)
	router.GET("/start-chrome", this.StartChromeHandler)

	router.GET("/automation/schedules", this.AutomationSchedulesHandler)
	router.GET("/automation/current-schedule", this.AutomationCurrentScheduleHandler)
	router.GET("/automation/new-schedule", this.AutomationNewScheduleHandler)
	router.GET("/automation/cleanup-schedules", this.AutomationCleanupSchedulesHandler)
	router.GET("/automation/schedule/pause/:refID", this.AutomationPauseScheduleHandler)
	router.GET("/automation/schedule/resume/:refID", this.AutomationResumeScheduleHandler)
	router.GET("/automation/schedule/cancel/:refID", this.AutomationCancelScheduleHandler)

	// Add update-settings endpoint
	router.POST("/update-settings/:project_id", this.UpdateSettingsHandler)
	router.POST("/batch-settings/update", this.BatchUpdateSettingsHandler)
	router.GET("/activate-chrome/:project_id/:user_id", this.ActivateChromeHandler)
	router.GET("/reload-chrome/group/:project_group", this.ReloadChromeGroupHandler)
	router.GET("/reload-chrome/:project_id/:address", this.ReloadChromeHandler)

	router.GET("/deposit-address/:wallet_name", this.DepositAddressHandler)
	router.GET("/balancer/info", this.BalancerInfoHandler)
	router.GET("/balancer/plans", this.BalancerPlansHandler)
	router.POST("/balancer/plan/make", this.MakeBalancerPlanHandler)
	router.POST("/balancer/plan/cancel", this.CancelBalancerPlanHandler)
	router.POST("/balancer/plan/delete", this.DeleteBalancerPlanHandler)
	router.POST("/balancer/plan/process", this.ProcessBalancerPlanHandler)
	router.POST("/balancer/plan/item/cancel", this.CancelBalancerPlanItemHandler)
	router.POST("/balancer/tx/action", this.TxActionHandler)
	// for lighter platform, we need to wait for the extension to finish the withdraw and mark the tx as done
	// use this endpoint to query if there is any withdraw tx that is not done
	router.GET("/balancer/extension/withdraw", this.BalancerExtensionWithdrawHandler)
	// for lighter platform, we need to wait for the extension to finish the withdraw and mark the tx as done
	// use this endpoint to mark the tx as done
	router.GET("/balancer/extension/withdraw/done", this.BalancerExtensionWithdrawDoneHandler)

	// Add setIdle endpoint
	router.POST("/set-idle", this.SetIdleHandler)

	return router
}

func (this *Server) Run() {
	log.Printf("Starting server on :%d", this.port)
	if err := this.router.Run(fmt.Sprintf(":%d", this.port)); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}

func (this *Server) LicenseExpiredHTML() string {
	return `<!DOCTYPE html>
<html>
<head>
    <title>License Expired</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }
        .error-container { max-width: 500px; margin: 0 auto; padding: 20px; border: 2px solid #ff4444; border-radius: 10px; background-color: #fff5f5; }
        .error-title { color: #ff4444; font-size: 24px; margin-bottom: 20px; }
        .error-message { color: #666; font-size: 16px; line-height: 1.5; }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-title">⚠️ License Expired</div>
        <div class="error-message">
            Your license has expired. Please renew your license to continue using this service.
            <br><br>
            Contact your administrator or license provider for assistance.
        </div>
    </div>
</body>
</html>`
}

func (this *Server) IndexHandler(c *gin.Context) {
	this.licenseMutex.RLock()
	expired := this.licenseExpired
	this.licenseMutex.RUnlock()

	if expired {
		// Show license expired message
		c.Header("Content-Type", "text/html")
		c.String(http.StatusOK, this.LicenseExpiredHTML())
		return
	}

	data, err := this.staticFS.Open("index.html")
	if err != nil {
		c.String(http.StatusInternalServerError, "Failed to load index.html")
		return
	}
	defer data.Close()

	// Get file info to determine content length
	stat, err := data.Stat()
	if err != nil {
		c.String(http.StatusInternalServerError, "Failed to get file stats")
		return
	}

	// Set content type
	c.Header("Content-Type", "text/html")
	c.Header("Content-Length", fmt.Sprintf("%d", stat.Size()))

	// Copy the file to the response
	http.ServeContent(c.Writer, c.Request, "index.html", stat.ModTime(), data.(io.ReadSeeker))
}

func (this *Server) BalancerHandler(c *gin.Context) {
	data, err := this.staticFS.Open("balancer.html")
	if err != nil {
		c.String(http.StatusInternalServerError, "Failed to load balancer.html")
		return
	}
	defer data.Close()

	// Get file info to determine content length
	stat, err := data.Stat()
	if err != nil {
		c.String(http.StatusInternalServerError, "Failed to get file stats")
		return
	}

	// Set content type
	c.Header("Content-Type", "text/html")
	c.Header("Content-Length", fmt.Sprintf("%d", stat.Size()))

	// Copy the file to the response
	http.ServeContent(c.Writer, c.Request, "balancer.html", stat.ModTime(), data.(io.ReadSeeker))
}

func (this *Server) AutomationHandler(c *gin.Context) {
	data, err := this.staticFS.Open("automation.html")
	if err != nil {
		c.String(http.StatusInternalServerError, "Failed to load automation.html")
		return
	}
	defer data.Close()

	// Get file info to determine content length
	stat, err := data.Stat()
	if err != nil {
		c.String(http.StatusInternalServerError, "Failed to get file stats")
		return
	}

	// Set content type
	c.Header("Content-Type", "text/html")
	c.Header("Content-Length", fmt.Sprintf("%d", stat.Size()))

	// Copy the file to the response
	http.ServeContent(c.Writer, c.Request, "automation.html", stat.ModTime(), data.(io.ReadSeeker))
}

func (this *Server) HealthHandler(c *gin.Context) {
	projectID := c.Query("projectID")

	projectStatus := ProjectStatusStopped
	projectStatusComment := ProjectStatusCommentDefault
	statusUpdateTime := time.Time{}

	if projectID != "" {
		project, _ := this.controller.Projects.Load(projectID)
		if project != nil {
			projectStatus = project.Status
			projectStatusComment = project.StatusComment
			statusUpdateTime = project.StatusUpdateTime
		}
	}
	extensionVersion := readExtensionVersion(this.controller.options.ExtensionDir)
	c.JSON(http.StatusOK, HealthResponse{
		Status:               "healthy",
		Version:              this.Version(),
		ProjectID:            projectID,
		ProjectStatus:        projectStatus,
		ProjectStatusComment: projectStatusComment,
		StatusUpdateTime:     statusUpdateTime,
		ExtensionVersion:     extensionVersion,
		Options:              this.controller.options,
	})
}

func (this *Server) UpdateOptionsHandler(c *gin.Context) {
	var req UpdateOptionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	this.controller.options = req.Options
	this.controller.options.Save(this.controller.DataDir)
	this.controller.UpdateScheduleAutoStart()
	c.JSON(http.StatusOK, gin.H{"success": true})
}

func (this *Server) GetAgentsHandler(c *gin.Context) {
	projectID := c.Param("project_id")

	agents, ok := this.controller.GetAgents(projectID)
	if !ok {
		agents = Agents{}
	}

	c.JSON(http.StatusOK, GetAgentsResponse{
		Agents: agents,
	})
}

func (this *Server) UpdateAgentHandler(c *gin.Context) {
	projectID := c.Param("project_id")

	var agent UpdateAgentRequest
	if err := c.ShouldBindJSON(&agent); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := this.controller.UpdateAgent(projectID, &agent)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, UpdateAgentResponse{
		Agent: agent,
	})
}

func (this *Server) GetProjectsHandler(c *gin.Context) {
	var projects []*Project
	this.controller.Projects.Range(func(key string, project *Project) bool {
		if project.DeleteTime == nil {
			projects = append(projects, project)
		}
		return true
	})

	// Sort projects by ID
	sort.Slice(projects, func(i, j int) bool {
		return projects[i].ProjectID < projects[j].ProjectID
	})

	c.JSON(http.StatusOK, GetProjectsResponse{
		Projects: projects,
	})
}

func (this *Server) GetProjectHandler(c *gin.Context) {
	projectID := c.Param("project_id")

	project, ok := this.controller.Projects.Load(projectID)
	if !ok {
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	c.JSON(http.StatusOK, GetProjectResponse{
		Project: project,
	})
}

func (this *Server) GetRisksHandler(c *gin.Context) {
	validOnly := c.Query("validOnly") == "true"
	this.controller.CheckRisks()
	projectID := c.Param("project_id")
	risks := this.controller.getRisks(projectID, validOnly)
	c.JSON(http.StatusOK, GetRisksResponse{
		Risks: risks,
	})
}

func (this *Server) GetAllRisksHandler(c *gin.Context) {
	validOnly := c.Query("validOnly") == "true"
	this.controller.CheckRisks()
	risks := this.controller.getRisks("", validOnly)
	c.JSON(http.StatusOK, GetRisksResponse{
		Risks: risks,
	})
}

func (this *Server) GetOverviewsHandler(c *gin.Context) {
	this.controller.updateProjectPerformance()
	overviews := this.controller.GetProjectOverviews()
	c.JSON(http.StatusOK, GetProjectOverviewsResponse{
		Overviews: overviews,
	})
}

func (this *Server) GetPerformanceHandler(c *gin.Context) {
	projectID := c.Param("project_id")
	lastWeek := c.Query("lastWeek") == "true"

	// Parse query parameters for time range
	var startTime, endTime *time.Time

	if lastWeek {
		_endTime := CalculateCutoffTime(time.Now(), 3, 0, 0, 0)
		_startTime := CalculateCutoffTime(time.Now().AddDate(0, 0, -7), 3, 0, 0, 0)
		startTime = &_startTime
		endTime = &_endTime
	}

	performance := this.controller.GetProjectPerformance(projectID, startTime, endTime)
	c.JSON(http.StatusOK, performance)
}

func (this *Server) GetChangesHandler(c *gin.Context) {
	projectID := c.Param("project_id")
	userID := c.Query("user_id")
	limitStr := c.Query("limit")

	// Parse limit parameter, default to 100
	limit := 100
	if limitStr != "" {
		parsedLimit, err := strconv.Atoi(limitStr)
		if err != nil || parsedLimit <= 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit format. Must be a positive integer."})
			return
		}
		limit = parsedLimit
	}

	// Parse time range parameters
	var startTime, endTime *time.Time
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		parsedTime, err := time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_time format. Use RFC3339 format."})
			return
		}
		startTime = &parsedTime
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		parsedTime, err := time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_time format. Use RFC3339 format."})
			return
		}
		endTime = &parsedTime
	}

	changes := this.controller.LookupPositionChanges(projectID, userID, startTime, endTime, limit)
	c.JSON(http.StatusOK, gin.H{"changes": changes})
}

func (this *Server) UpdateStatusHandler(c *gin.Context) {
	var req UpdateStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	comment := ProjectStatusCommentDefault
	commentStr := req.Comment
	if commentStr != "" {
		comment = ProjectStatusComment(commentStr)
	}

	projectIDs := utils.SplitAndTrim(req.ProjectIDs, ",")

	for _, projectID := range projectIDs {
		// Check if project exists
		_, ok := this.controller.Projects.Load(projectID)
		if !ok {
			c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("Project not found: %s", projectID)})
			return
		}

		logger.Infof("toggle status: %s, %s, %s", projectID, req.Status, comment)

		// Update project status
		if req.Status == ProjectStatusRunning {
			this.controller.ResumeProject(projectID, comment)
			if comment != ProjectStatusCommentIdle {
				this.controller.resetRiskEvent(RiskTypeStoploss, projectID, "")
			}
		} else if req.Status == ProjectStatusStopped {
			this.controller.StopProject(projectID, comment)
			this.controller.resetRiskEvent(RiskTypeStoploss, projectID, "")
		}
	}

	if comment == ProjectStatusCommentConsole {
		if req.Status == ProjectStatusRunning {
			this.controller.startChromesForProjectIDs(projectIDs)
			for _, projectID := range projectIDs {
				project, ok := this.controller.Projects.Load(projectID)
				if !ok {
					continue
				}
				for _, agent := range project.Agents {
					this.controller.activateChromeByAddress(projectID, agent.UserID, true)
					time.Sleep(1000 * time.Millisecond)
					this.controller.ensureExtensionOpened()
					time.Sleep(1000 * time.Millisecond)
					this.controller.AddServerRequest(projectID, agent.UserID, ServerRequestTypeNavigateToTrade, 4)
				}
			}
		} else if req.Status == ProjectStatusStopped {
			this.controller.closeChromeForProjects(projectIDs)
		}
	}

	c.JSON(http.StatusOK, UpdateStatusResponse{
		Success: true,
	})
}

func (this *Server) UpdateSettingsHandler(c *gin.Context) {
	projectID := c.Param("project_id")

	var req UpdateSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// dont allow update projectID in update-settings endpoint
	// it should be updated in change-project-id endpoint, the server will ensure the history is copied
	if req.Settings.ProjectID != "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ProjectID is not allowed to be updated"})
		return
	}

	// Get the project
	project, ok := this.controller.Projects.Load(projectID)
	if !ok {
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	// Find the agent
	var agent *Agent
	for _, a := range project.Agents {
		if a.UserID == req.UserID {
			agent = a
			break
		}
	}

	if agent == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Agent not found"})
		return
	}

	// Update serverSettings
	agent.ServerSettings = &req.Settings
	if req.Settings.ProjectID == "" {
		agent.ServerSettings.ProjectID = projectID
	}
	agent.ServerSettings.Version = int(time.Now().UnixMilli())

	// Save the changes
	this.controller.Storage.Save()

	c.JSON(http.StatusOK, UpdateSettingsResponse{
		Success:  true,
		Settings: *agent.ServerSettings,
	})
}

func (this *Server) BatchUpdateSettingsHandler(c *gin.Context) {
	var req BatchUpdateSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	projectIDs := strings.Split(req.ProjectIDs, ",")
	for _, projectID := range projectIDs {
		project, ok := this.controller.Projects.Load(projectID)
		if !ok {
			continue
		}
		for _, agent := range project.Agents {
			if req.Settings.MinMargin != 0 {
				agent.ServerSettings.MinMargin = req.Settings.MinMargin
			}
			if req.Settings.RefreshInterval != 0 {
				agent.ServerSettings.RefreshInterval = req.Settings.RefreshInterval
			}
			if req.Settings.DefaultSymbol != "" {
				agent.ServerSettings.DefaultSymbol = req.Settings.DefaultSymbol
			}
			if req.Settings.DefaultSize != 0 {
				agent.ServerSettings.DefaultSize = req.Settings.DefaultSize
			}
			if req.Settings.StopLossBuffer != 0 {
				agent.ServerSettings.StopLossBuffer = req.Settings.StopLossBuffer
			}
			if req.Settings.CoolingHour != 0 {
				agent.ServerSettings.CoolingHour = req.Settings.CoolingHour
			}
			if agent.Role == RoleCopier {
				if req.Settings.CopyPercentage != 0 {
					agent.ServerSettings.CopyPercentage = req.Settings.CopyPercentage
				}
			}
			agent.ServerSettings.Version = int(time.Now().UnixMilli())
		}
		this.controller.Storage.Save()
	}
	c.JSON(http.StatusOK, BatchUpdateSettingsResponse{
		Success: true,
	})
}

func (this *Server) CloseChromeHandler(c *gin.Context) {
	projectIDStr := c.Query("project_ids")
	if projectIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project_ids is required"})
		return
	}

	if runtime.GOOS != "windows" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "this function only works on Windows"})
		return
	}

	projectIDs := strings.Split(projectIDStr, ",")

	this.controller.closeChromeForProjects(projectIDs)
	c.JSON(http.StatusOK, gin.H{"success": true})
}

func (this *Server) StartChromeHandler(c *gin.Context) {
	projectIDStr := c.Query("project_ids")
	if projectIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project_ids is required"})
		return
	}

	if runtime.GOOS != "windows" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "this function only works on Windows"})
		return
	}

	projectIDs := strings.Split(projectIDStr, ",")
	if len(projectIDs) > 20 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "too many projects, max 20"})
		return
	}
	this.controller.startChromesForProjectIDs(projectIDs)
	// for each agent:
	// 1. activate chrome for project
	// 2. click on the extension icon
	// 3. send server request to reload page
	logger.Infof("start automation for project: %s", projectIDs)
	for _, projectID := range projectIDs {
		project, ok := this.controller.Projects.Load(projectID)
		if !ok {
			continue
		}
		for _, agent := range project.Agents {
			logger.Infof("start automation for project: %s, agent: %s", projectID, agent.UserID)
			err := this.controller.activateChromeByAddress(projectID, agent.UserID, true)
			if err != nil {
				logger.Errorf("failed to activate chrome: %v", err)
				continue
			}
			time.Sleep(1000 * time.Millisecond)
			err = this.controller.ensureExtensionOpened()
			if err != nil {
				logger.Errorf("failed to ensure extension opened: %v", err)
			}
			time.Sleep(1000 * time.Millisecond)
			this.controller.AddServerRequest(projectID, agent.UserID, ServerRequestTypeNavigateToTrade, 4)
		}
	}
	c.JSON(http.StatusOK, gin.H{"success": true})
}

func (this *Server) ActivateChromeHandler(c *gin.Context) {
	projectID := c.Param("project_id")
	userID := c.Param("user_id")
	err := this.controller.activateChromeByAddress(projectID, userID, true)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	err = this.controller.ensureExtensionOpened()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	time.Sleep(1000 * time.Millisecond)
	this.controller.AddServerRequest(projectID, userID, ServerRequestTypeNavigateToTrade, 4)
	this.controller.reloadChrome(projectID, userID, false)
	c.JSON(http.StatusOK, gin.H{"success": true})
}

func (this *Server) ReloadChromeGroupHandler(c *gin.Context) {
	projectGroup := c.Param("project_group")

	projects := this.controller.GetProjects(true)
	for _, project := range projects {
		if project.GetGroup() == projectGroup {
			for _, agent := range project.Agents {
				this.controller.reloadChrome(project.ProjectID, agent.UserID, false)
				time.Sleep(1000 * time.Millisecond)
			}
		}
	}
	c.JSON(http.StatusOK, gin.H{"success": true})
}

func (this *Server) ReloadChromeHandler(c *gin.Context) {
	projectID := c.Param("project_id")
	address := c.Param("address")
	force := c.Query("force") == "true"
	if projectID == "" || address == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project_id and address are required"})
		return
	}
	err := this.controller.reloadChrome(projectID, address, force)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true})
}

func (this *Server) AutomationSchedulesHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"schedules": this.controller.automationSchedules})
}

func (this *Server) AutomationCurrentScheduleHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"schedule": this.controller.currentSchedule})
}

func (this *Server) AutomationNewScheduleHandler(c *gin.Context) {
	job := ScheduleJob(c.Query("job"))
	var schedule *AutomationSchedule
	if job == ScheduleJobDefault {
		schedule = this.controller.InitAutomationSchedule()
		this.controller.options.EnableAutomation = true
	} else {
		schedule = this.controller.NewAutomationSchedule(job)
		this.controller.automationSchedules = append(this.controller.automationSchedules, schedule)
	}
	go this.controller.RunAutomationSchedule(schedule)
	c.JSON(http.StatusOK, gin.H{"success": true, "schedule": schedule})
}

func (this *Server) AutomationCleanupSchedulesHandler(c *gin.Context) {
	this.controller.CleanupAutomationSchedules()
	c.JSON(http.StatusOK, gin.H{"success": true})
}

func (this *Server) AutomationPauseScheduleHandler(c *gin.Context) {
	refID := c.Param("refID")
	err := this.controller.PauseSchedule(refID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true})
}

func (this *Server) AutomationResumeScheduleHandler(c *gin.Context) {
	refID := c.Param("refID")
	err := this.controller.ResumeSchedule(refID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true})
}

func (this *Server) AutomationCancelScheduleHandler(c *gin.Context) {
	refID := c.Param("refID")
	err := this.controller.CancelSchedule(refID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true})
}

func (this *Server) DeleteProjectHandler(c *gin.Context) {
	projectID := c.Param("project_id")

	// Check if project exists
	project, ok := this.controller.Projects.Load(projectID)
	if !ok {
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	// stop project first and sleep waiting for agent to properly shutdown
	project.Status = ProjectStatusStopped
	project.StatusComment = ProjectStatusCommentDeleted
	project.StatusUpdateTime = time.Now()
	now := time.Now()
	project.DeleteTime = &now
	project.Agents = []*Agent{}
	this.controller.Storage.Save()

	c.JSON(http.StatusOK, DeleteProjectResponse{
		Success: true,
	})
}

func (this *Server) ResetProjectHandler(c *gin.Context) {
	projectID := c.Param("project_id")

	project, ok := this.controller.Projects.Load(projectID)
	if !ok {
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	project.BornTime = time.Now()
	this.controller.Storage.Save()

	c.JSON(http.StatusOK, DeleteProjectResponse{
		Success: true,
	})
}

func (this *Server) ChangeProjectIDHandler(c *gin.Context) {
	projectID := c.Param("project_id")

	// check if the project is stopped
	project, ok := this.controller.Projects.Load(projectID)
	if !ok {
		c.JSON(http.StatusNotFound, gin.H{"error": "project not found"})
		return
	}

	// check if the project is deleted
	if project.DeleteTime != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project is deleted"})
		return
	}

	// check if the project is stopped
	if project.Status != ProjectStatusStopped {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project is not stopped"})
		return
	}

	// check if the new project has no positions
	if !project.IsZeroPositions() {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project has positions"})
		return
	}

	var req ChangeProjectIDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// check if the new projectID is already used by other project
	_, ok = this.controller.Projects.Load(req.NewProjectID)
	if ok {
		c.JSON(http.StatusBadRequest, gin.H{"error": "new projectID is already used by other project"})
		return
	}

	err := this.controller.RenameProject(projectID, req.NewProjectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("failed to rename project: %v", err)})
		return
	}

	c.JSON(http.StatusOK, ChangeProjectIDResponse{
		Success: true,
	})
}

func (this *Server) SayHandler(c *gin.Context) {
	message := c.Query("message")
	if message == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "message is required"})
		return
	}

	this.controller.playSound(message)
	c.JSON(http.StatusOK, SayResponse{
		Success: true,
	})
}

func (this *Server) AddServerRequestHandler(c *gin.Context) {
	var req AddServerRequestRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	projectIDs := strings.Split(req.ProjectIDs, ",")
	projectUserIDs := []string{}
	if req.UserIDs != "" {
		projectUserIDs = strings.Split(req.UserIDs, ",")
	}
	for _, projectID := range projectIDs {
		project, ok := this.controller.Projects.Load(projectID)
		if !ok {
			continue
		}
		for _, agent := range project.Agents {
			validUserID := false
			if len(projectUserIDs) > 0 {
				if slices.Contains(projectUserIDs, agent.UserID) {
					validUserID = true
				}
			} else {
				validUserID = true
			}

			if validUserID {
				this.controller.AddServerRequest(projectID, agent.UserID, req.RequestType, 0)
				logger.Infof("add server request: %s, userId: %s, requestType: %s", projectID, agent.UserID, req.RequestType)
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{"success": true})
}

func (this *Server) FinishServerRequestHandler(c *gin.Context) {
	var req FinishServerRequestRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	this.controller.FinishServerRequest(req.RequestID, ServerRequestFinishStatus(req.Status), req.Comment)
	c.JSON(http.StatusOK, FinishServerRequestResponse{
		Success: true,
	})
}

func (this *Server) BalancerInfoHandler(c *gin.Context) {
	if this.controller.balancer == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "balancer is not enabled"})
		return
	}
	wallets := map[string]*hopper.WalletSummary{}
	for _, wallet := range this.controller.balancer.wallets {
		summary := wallet.GetSummary()
		wallets[wallet.Name] = summary
	}
	exchangeIDs := []string{}
	for _, exchange := range this.controller.balancer.Options.Exchanges {
		exchangeIDs = append(exchangeIDs, exchange.ID)
	}

	platformInfos := map[string]*PlatformInfo{}
	for _, platform := range this.controller.balancer.Options.Platforms {
		platformInfo, err := platform.ToPlatformInfo()
		if err != nil {
			continue
		}
		platformInfos[string(platform.Platform)] = platformInfo
	}
	c.JSON(http.StatusOK, gin.H{"wallets": wallets, "exchanges": exchangeIDs, "platforms": platformInfos})
}

func (this *Server) DepositAddressHandler(c *gin.Context) {
	walletName := c.Param("wallet_name")
	for _, wallet := range this.controller.balancer.wallets {
		if wallet.Name == walletName {
			note := this.controller.getLighterDepositAddressNote(wallet)
			if note == "" {
				c.JSON(http.StatusNotFound, gin.H{"error": "wallet not found"})
				return
			}
			c.JSON(http.StatusOK, gin.H{"note": note})
			return
		}
	}
	c.JSON(http.StatusNotFound, gin.H{"error": "wallet not found"})
}

func (this *Server) BalancerPlansHandler(c *gin.Context) {
	if this.controller.balancer == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "balancer is not enabled"})
		return
	}
	this.controller.balancer.UpdateProjects(this.controller.GetProjects(true))
	balancePlans := this.controller.balancer.GetBalancePlans()
	redactedBalancePlans := []*BalancePlan{}
	for _, plan := range balancePlans {
		redactedBalancePlans = append(redactedBalancePlans, plan.CloneRedacted())
	}
	c.JSON(http.StatusOK, GetBalancerPlansResponse{
		Plans: redactedBalancePlans,
	})
}

func (this *Server) MakeBalancerPlanHandler(c *gin.Context) {
	if this.controller.balancer == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "balancer is not enabled"})
		return
	}

	var req MakeBalancerPlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	platformOpt := this.controller.balancer.Options.GetPlatformOpt(req.Platform)
	if platformOpt == nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "platform not found"})
		return
	}
	if req.WalletID == "" && req.Coin != platformOpt.Coin {
		c.JSON(http.StatusBadRequest, gin.H{"error": "for non-wallet plan, only support coin: " + platformOpt.Coin})
		return
	}

	this.controller.balancer.UpdateProjects(this.controller.GetProjects(true))
	var plan *BalancePlan
	var err error
	if len(req.Stages) > 0 {
		if req.WalletID != "" {
			plan, err = this.controller.balancer.MakeBalancePlan(req.Platform, req.ExchangeID, req.WalletID, req.Addresses, req.Stages, req.Amount, req.Coin, req.WaitSeconds, req.AmountRandom, req.AmountDirective)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
		}
	} else {
		if req.AmountDirective == AmountDirectiveTarget {
			c.JSON(http.StatusBadRequest, gin.H{"error": "automatic mode, amount directive can not be target"})
			return
		}
		plan, err = this.controller.balancer.MakeBalancePlanAuto(req.Platform, req.ExchangeID, req.ProjectIDs, req.Amount, req.WaitSeconds, req.AmountDirective)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
	}

	c.JSON(http.StatusOK, MakeBalancerPlanResponse{
		Plan: plan.CloneRedacted(),
	})
}

func (this *Server) CancelBalancerPlanHandler(c *gin.Context) {
	if this.controller.balancer == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "balancer is not enabled"})
		return
	}

	var req CancelBalancerPlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	this.controller.balancer.UpdateProjects(this.controller.GetProjects(true))
	err := this.controller.balancer.CancelPlan(req.PlanRefID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, CancelBalancerPlanResponse{
		Success: true,
	})
}

func (this *Server) DeleteBalancerPlanHandler(c *gin.Context) {
	if this.controller.balancer == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "balancer is not enabled"})
		return
	}

	var req DeleteBalancerPlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err := this.controller.balancer.DeleteBalancePlan(req.PlanRefID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, DeleteBalancerPlanResponse{
		Success: true,
	})
}

func (this *Server) ProcessBalancerPlanHandler(c *gin.Context) {
	if this.controller.balancer == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "balancer is not enabled"})
		return
	}

	var req ProcessBalancerPlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	plan := this.controller.balancer.GetPlanWithRefID(req.PlanRefID)
	if plan == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "plan not found"})
		return
	}

	this.controller.balancer.UpdateProjects(this.controller.GetProjects(true))
	go this.controller.balancer.ProcessPlan(plan)

	c.JSON(http.StatusOK, ProcessBalancerPlanResponse{
		Success: true,
	})
}

func (this *Server) CancelBalancerPlanItemHandler(c *gin.Context) {
	if this.controller.balancer == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "balancer is not enabled"})
		return
	}

	var req CancelBalancerPlanItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	this.controller.balancer.UpdateProjects(this.controller.GetProjects(true))
	err := this.controller.balancer.CancelPlanItem(req.PlanRefID, req.ItemRefID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, CancelBalancerPlanItemResponse{
		Success: true,
	})
}

func (this *Server) TxActionHandler(c *gin.Context) {
	if this.controller.balancer == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "balancer is not enabled"})
		return
	}

	var req TxActionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	this.controller.balancer.UpdateProjects(this.controller.GetProjects(true))
	err := this.controller.balancer.RequestTxAction(req.PlanRefID, req.TxRefID, req.Action)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, TxActionResponse{
		Success: true,
	})
}

func (this *Server) BalancerExtensionWithdrawHandler(c *gin.Context) {
	if this.controller.balancer == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "balancer is not enabled"})
		return
	}
	this.controller.balancer.UpdateProjects(this.controller.GetProjects(true))
	tx := this.controller.balancer.QueryExtensionWithdraw(c.Query("project_id"), c.Query("user_id"))
	c.JSON(http.StatusOK, gin.H{"tx": tx})
}

func (this *Server) BalancerExtensionWithdrawDoneHandler(c *gin.Context) {
	if this.controller.balancer == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "balancer is not enabled"})
		return
	}
	this.controller.balancer.UpdateProjects(this.controller.GetProjects(true))
	err := this.controller.balancer.MarkExtensionWithdrawDone(c.Query("projectId"), c.Query("txRefId"))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true})
}

func (this *Server) SetIdleHandler(c *gin.Context) {
	var req SetIdleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	for _, projectID := range req.ProjectIDs {
		project, ok := this.controller.Projects.Load(projectID)
		if !ok {
			continue
		}

		if project.Status == ProjectStatusRunning {
			project.StatusComment = ProjectStatusComment("idle")
			project.StatusUpdateTime = time.Now()
		}
	}

	this.controller.Storage.Save()

	c.JSON(http.StatusOK, SetIdleResponse{
		Success: true,
	})
}

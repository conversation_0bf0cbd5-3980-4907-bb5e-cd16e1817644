package copier

import (
	"chaintools/hopper"
	"fmt"
	"time"

	"github.com/tidwall/gjson"
	"gopkg.in/resty.v1"
)

func (this *CopierController) getLighterDepositAddressNote(wallet *hopper.MnemonicWallet) string {
	note := "lighter deposit address:\n"
	note += "------------------------\n"
	depositAddresses, err := this.fetchLighterDepositAddress(wallet.Addresses.Addresses())
	if err != nil {
		return note
	}
	for _, address := range wallet.Addresses {
		note += fmt.Sprintf("%sW%02d: %s\n", wallet.Prefix, address.Index(), depositAddresses[address.Address()])
	}
	return note
}

func (this *CopierController) fetchLighterDepositAddress(addresses []string) (map[string]string, error) {
	pchromes, err := this.lookupPchromeForAddress("lighter", addresses)
	if err != nil {
		return nil, err
	}
	depositAddresses := make(map[string]string)
	for _, pchrome := range pchromes {
		if pchrome.HttpProxy == "" {
			depositAddresses[pchrome.Address] = ""
			continue
		}
		depositAddress := this._fetchLighterDepositAddressWithProxy(pchrome.Address, pchrome.HttpProxy)
		depositAddresses[pchrome.Address] = depositAddress
	}
	return depositAddresses, nil
}

func (this *CopierController) _fetchLighterDepositAddressWithProxy(address string, proxy string) string {
	// fetch info from: https://mainnet.zklighter.elliot.ai/api/v1/deposit/latest?l1_address=xxx
	// response:
	// {"code":200,"source":"Arbitrum One","source_chain_id":"42161","fast_bridge_tx_hash":"","batch_claim_tx_hash":"","cctp_burn_tx_hash":"","amount":"0.000000","intent_address":"xxx","status":"pending","step":"0","description":"Waiting for user to deposit","created_at":1749116048,"updated_at":1749116048,"is_external_deposit":false}
	url := fmt.Sprintf("https://mainnet.zklighter.elliot.ai/api/v1/deposit/latest?l1_address=%s", address)
	client := resty.New()
	client.SetProxy(proxy)
	client.SetTimeout(10 * time.Second)
	resp, err := client.R().Get(url)
	if err != nil {
		return ""
	}
	result := gjson.Parse(resp.String())
	if result.Get("code").Int() != 200 {
		return ""
	}
	return result.Get("intent_address").String()
}

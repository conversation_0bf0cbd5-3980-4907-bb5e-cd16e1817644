/* Global Styles */
[x-cloak] {
    display: none !important;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    color: #2c3e50;
    line-height: 1.5;
}

/* Container Layout */
.container {
    display: flex;
    min-height: 100vh;
    background-color: #f8f9fa;
    position: relative;
}

/* Sidebar Styles */
.sidebar {
    width: 320px;
    background-color: white;
    color: var(--dark-color);
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-width: 320px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

.sidebar-item {
    padding: 10px 15px;
    border-radius: 6px;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1.05em;
    border: none;
    margin: 0 0 10px 0;
    font-weight: 500;
    letter-spacing: 0.01em;
    color: var(--dark-color);
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-item:hover {
    background-color: #e2e8f0;
    color: var(--primary-color);
}

.sidebar-item.active {
    background-color: #3498db;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Main Content Area */
.content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

/* Headers */
h2 {
    margin: 0 0 24px 0;
    color: #2c3e50;
    font-size: 1.5em;
    font-weight: 600;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
}

.form-group select,
.form-group input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 0.95em;
    color: #2c3e50;
    background-color: white;
    transition: border-color 0.2s ease;
}

.form-group select:focus,
.form-group input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Filter Controls Styles */
.grid-filter-controls {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    gap: 12px;
}

.filter-input {
    width: 250px;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 0.9em;
    color: #2c3e50;
    background-color: white;
    transition: border-color 0.2s ease;
    flex-shrink: 0;
}

.filter-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.filter-buttons {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.filter-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.85em;
    transition: all 0.2s ease;
    color: white;
    background-color: #3498db;
    white-space: nowrap;
}

.filter-btn:hover {
    background-color: #2980b9;
    opacity: 1;
}

.filter-btn.reset-btn {
    background-color: #6c757d;
}

.filter-btn.reset-btn:hover {
    background-color: #5a6268;
}

/* Projects Grid */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
    margin: 0;
}

/* Addresses Grid */
.addresses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 12px;
    margin: 0;
}

.project-checkbox {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.project-checkbox:hover {
    border-color: #3498db;
}

.project-checkbox.filtered {
    border-width: 2px;
    border-color: #3498db;
    box-shadow: 0 0 0 1px rgba(52, 152, 219, 0.2);
}

.project-checkbox input[type="checkbox"] {
    margin-right: 10px;
    width: 16px;
    height: 16px;
}

.address-checkbox {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.address-checkbox:hover {
    border-color: #3498db;
}

.address-checkbox input[type="checkbox"] {
    margin-right: 10px;
    width: 16px;
    height: 16px;
}

.address-checkbox label {
    font-family: monospace;
    font-size: 1.1em;
    color: #2c3e50;
    word-break: break-all;
    display: flex;
    align-items: center;
    gap: 8px;
}

.address-short {
    color: #666;
    font-size: 1em;
}

/* Buttons */
button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.95em;
    transition: all 0.2s ease;
    color: white;
}

button:hover {
    opacity: 0.9;
}

button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Process button - Green */
button:not(.delete-btn):not(.item-cancel-btn):not(.tx-actions button):not(.cancel-btn) {
    background-color: #10b981;
}

button:not(.delete-btn):not(.item-cancel-btn):not(.tx-actions button):not(.cancel-btn):hover {
    background-color: #059669;
}

/* Cancel buttons - Orange */
button.item-cancel-btn,
button.cancel-btn {
    background-color: #f59e0b;
}

button.item-cancel-btn:hover,
button.cancel-btn:hover {
    background-color: #d97706;
}

/* Delete button - Red */
button.delete-btn {
    background-color: #ef4444;
}

button.delete-btn:hover {
    background-color: #dc2626;
}

/* Transaction action buttons - Neutral */
.tx-actions button {
    padding: 6px 12px;
    font-size: 0.9em;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
}

.tx-actions button:hover {
    background-color: #e9ecef;
    border-color: #ced4da;
    opacity: 1;
}

/* Plan Detail Page */
.plan-detail {
    max-width: 1200px;
    margin: 0 auto;
}

.plan-info {
    width: 95%;
    padding: 18px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.info-row {
    display: flex;
    margin-bottom: 12px;
    font-size: 0.95em;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-row span:first-child {
    font-weight: 500;
    width: 140px;
    color: #666;
}

.info-row span:last-child {
    flex: 1;
    word-break: break-all;
    color: #2c3e50;
}

.plan-actions {
    margin-bottom: 24px;
    margin-top: 24px;
    display: flex;
    gap: 12px;
}

.plan-items {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* Transaction Styles */
.plan-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 0;
    margin: 0;
}

.sidebar-plan-item {
    padding: 10px 15px;
    border-radius: 6px;
    background-color: #f1f5f9;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    width: 100%;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border: none;
    min-height: 48px;
}

.sidebar-plan-item:hover, .sidebar-plan-item.selected {
    background-color: #e2e8f0;
    color: var(--primary-color);
}

.sidebar-plan-item.selected {
    background-color: #3498db;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sidebar-plan-item.selected .sidebar-plan-info {
    background-color: white;
    border-radius: 4px;
}

.sidebar-plan-item.selected .plan-label {
    color: #2c3e50;
}

.sidebar-plan-item.selected .plan-meta {
    color: #666;
}

.sidebar-plan-item.selected .status-badge {
    border-color: #e2e8f0;
}

.sidebar-plan-item.selected .status-badge.done {
    background-color: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
}

.sidebar-plan-item.selected .status-badge.canceled {
    background-color: #fee2e2;
    color: #991b1b;
    border-color: #fecaca;
}

.sidebar-plan-item.done {
    opacity: 0.7;
}

.sidebar-plan-item.canceled {
    opacity: 0.5;
}

.sidebar-plan-item .sidebar-plan-info {
    width: 92%;
    display: flex;
    flex-direction: column;
    gap: 2px;
    padding: 10px;
}

.plan-item-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding-top: 16px;
    padding-bottom: 16px;
    padding-left: 0;
    padding-right: 0;
}

.item-info {
    width: 1200px;
    padding: 16px;
    background-color: white;
}

.item-info .alias {
    font-weight: 700;
    color: #000;
}

.item-actions {
    padding: 16px;
    display: flex;
    align-items: flex-start;
    background-color: #f8f9fa;
    border-left: 1px solid #e9ecef;
}

.plan-item-transactions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding-top: 16px;
    padding-bottom: 16px;
    padding-left: 0;
    padding-right: 0;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.transaction {
    margin-bottom: 0;
    padding: 15px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    min-width: 0;
    transition: background 0.2s;
    width: 590px;
    box-sizing: border-box;
}

.transaction:hover {
    background-color: #f8f9fa;
    box-shadow: 0 2px 8px rgba(44,62,80,0.06);
}

.transaction:last-child {
    margin-bottom: 0;
}

.tx-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.tx-details {
    margin-top: 8px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.tx-info span:first-child {
    font-weight: 600;
    color: #2c3e50;
}

.tx-status {
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: 500;
    text-transform: capitalize;
}

.transaction.pending .tx-status {
    background-color: #fff3cd;
    color: #856404;
}

.transaction.failed .tx-status {
    background-color: #f8d7da;
    color: #721c24;
}

.transaction.success .tx-status {
    background-color: #d4edda;
    color: #155724;
}

.tx-details .info-row {
    margin-bottom: 8px;
    font-size: 0.9em;
}

.tx-details .info-row:last-child {
    margin-bottom: 0;
}

.tx-details .info-row span:first-child {
    font-weight: 500;
    width: 100px;
    color: #666;
}

.tx-details .info-row span:last-child {
    flex: 1;
    word-break: break-all;
    font-family: monospace;
    color: #2c3e50;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
}

.tx-details .info-row span:last-child > span:first-child {
    flex: 1;
    min-width: 0;
}

.tx-id {
    font-family: monospace;
    font-size: 0.9em;
    color: #666;
    background-color: #f8f9fa;
    padding: 0;
    border-radius: 4px;
}

.tx-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e9ecef;
}

/* Status Colors */
.status-created { color: #2196f3; }
.status-running { color: #4caf50; }
.status-completed { color: #4caf50; }
.status-failed { color: #f44336; }
.status-canceled { color: #9e9e9e; }

.item-ref {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.item-ref .ref-id {
    font-family: monospace;
    font-weight: 600;
    color: #2c3e50;
}

.item-ref .ref-id.large {
    font-size: 1.3em;
    font-weight: 700;
    color: #1a237e;
    margin-right: 16px;
}

.item-ref .ref-id.xlarge {
    font-size: 2em;
    font-weight: 800;
    color: #000;
    letter-spacing: 0.04em;
}

.item-cancel-btn {
    margin-left: 12px;
    padding: 4px 10px;
    font-size: 0.98em;
    background-color: #f44336;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}

.item-cancel-btn:hover {
    background-color: #c62828;
}

.item-cancel-btn.cancel-btn-margin {
    margin-right: 40px;
}

.project-ids {
    font-family: monospace;
    color: #2c3e50;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    display: inline-block;
    line-height: 1.4;
}

.project-link {
    color: #3498db;
    text-decoration: none;
    transition: color 0.2s ease;
}

.project-link:hover {
    color: #2980b9;
    text-decoration: underline;
}

.plan-header {
    width: 100%;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.plan-label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: var(--dark-color);
    font-size: 1.05em;
}

.plan-meta {
    font-size: 0.92em;
    color: var(--gray-color);
    margin-top: 2px;
}

.status-badge {
    font-size: 0.78em;
    font-weight: 700;
    padding: 2px 10px;
    border-radius: 12px;
    text-transform: uppercase;
    margin-left: 8px;
    letter-spacing: 0.04em;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
    border: 1px solid #e2e8f0;
}

.status-badge.done {
    background-color: #dcfce7;
    color: #166534;
    border-color: #bbf7d0;
}

.status-badge.canceled {
    background-color: #fee2e2;
    color: #991b1b;
    border-color: #fecaca;
}

/* Items Summary Styles */
.plan-items-summary {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.plan-items-summary h3 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 1.2em;
    font-weight: 600;
}

.summary-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.summary-item {
    background-color: #f8f9fa;
    padding: 12px 16px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95em;
    transition: all 0.2s ease;
}

.summary-item:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-dot.error {
    background-color: #ef4444;
}

.status-dot.done {
    background-color: #10b981;
}

.status-dot.pending {
    background-color: #f59e0b;
}

.summary-item .from-info,
.summary-item .to-info {
    display: flex;
    align-items: center;
    gap: 4px;
}

.summary-item .alias {
    font-weight: 600;
    color: #2c3e50;
}

.summary-item .amount {
    color: #666;
    font-family: monospace;
}

.summary-item .arrow {
    color: #3498db;
    font-weight: bold;
    margin: 0 4px;
}

.summary-item .separator {
    color: #3498db;
    font-weight: bold;
    margin: 0 4px;
}

.plan-detail-item {
    border-radius: 6px;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    width: 100%;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border: none;
    min-height: 48px;
    padding: 0;
}

.item-ref-flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.item-id-group {
    display: flex;
    align-items: center;
}

/* Stages Selection Styles */
.stages-selection {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
}

.stage-checkbox {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.stage-checkbox:hover {
    border-color: #3498db;
    background-color: #f8f9fa;
}

.stage-checkbox input[type="checkbox"] {
    margin-right: 10px;
    width: 16px;
    height: 16px;
}

.stage-checkbox label {
    font-size: 0.95em;
    color: #2c3e50;
    cursor: pointer;
}

.alias-badge {
    color: #666;
    font-size: 0.9em;
    font-style: italic;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    white-space: nowrap;
    flex-shrink: 0;
    background-color: #f8f9fa;
    padding: 2px 8px;
    margin-left: 180px;
}

/* Dialog Styles */
.balancer-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.balancer-dialog-content {
    background-color: white;
    border-radius: 8px;
    width: 900px;
    max-width: 95%;
    max-height: 95vh;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    display: flex;
    flex-direction: column;
}

.balancer-dialog-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.balancer-dialog-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.3em;
    font-weight: 600;
}

.balancer-dialog-body {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
}

.balancer-dialog-body textarea {
    width: calc(100% - 24px);
    min-height: 540px;
    padding: 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    box-sizing: border-box;
}

.balancer-close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: white;
    cursor: pointer;
    padding: 4px 8px;
    line-height: 1;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.balancer-close-btn:hover {
    color: #f8f9fa;
    background-color: rgba(255, 255, 255, 0.1);
}

.balancer-note-btn {
    padding: 8px 16px;
    background-color: #3498db;
    border: none;
    color: white;
    font-size: 0.9em;
}

.balancer-note-btn:hover {
    background-color: #2980b9;
}

.address-checkbox.filtered {
    border-width: 2px;
    border-color: #3498db;
    box-shadow: 0 0 0 1px rgba(52, 152, 219, 0.2);
}

/* Make Plan Actions */
.make-plan-actions {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
}

/* CSS Variables */
:root {
    --long-color: #10b981;
    --short-color: #ef4444;
    --gray-color: #6b7280;
    --dark-color: #1f2937;
    --master-color: #8b5cf6;
    --copier-color: #06b6d4;
}

/* General Layout */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: #f0f5ff;
    margin: 0;
    padding: 20px;
}

.container {
    display: flex;
    flex-direction: column;
    max-width: 1600px;
    margin: 0 auto;
    gap: 20px;
}

.main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.last-updated {
    font-size: 0.85em;
    color: #6b7280;
}

.pause-btn {
    background: none;
    border: 1px solid #d1d5db;
    color: #6b7280;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.pause-btn:hover {
    background-color: #f3f4f6;
}

.main-header h1 {
    margin: 0;
    font-size: 1.5em;
    font-weight: 600;
}

.global-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.global-actions button {
    padding: 10px 15px;
    border: none;
    border-radius: 6px;
    background-color: #3b82f6;
    color: white;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 500;
    transition: opacity 0.2s, transform 0.2s;
}

.global-actions button.secondary-btn {
    background-color: #6b7280;
}

.global-actions button i,
.action-btn i {
    margin-right: 8px;
}

.fa-spin {
    animation: fa-spin 1s linear infinite;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.global-actions button:hover,
.action-btn:hover {
    opacity: 0.9;
}

.new-schedule-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.job-type-selector {
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    background-color: #fff;
    font-size: 0.9em;
    cursor: pointer;
}

.global-actions button:active,
.action-btn:active {
    transform: scale(0.98);
}

.main-layout {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.main-content-wrapper {
    display: flex;
    gap: 20px;
    width: 100%;
}

/* Loading, Error, Empty States */
.loading-container, .error-container, .empty-state {
    width: 100%;
    text-align: center;
    padding: 40px;
    background-color: #fff;
    border-radius: 8px;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-container button {
    margin-top: 20px;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    background-color: #3b82f6;
    color: white;
    cursor: pointer;
    transition: background-color 0.2s;
}

.error-container button:hover {
    background-color: #2563eb;
}

.sidebar {
    width: 280px;
    flex-shrink: 0;
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    align-self: flex-start;
}

.content {
    flex: 1;
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-width: 0; /* Allow content to shrink */
    overflow-x: auto; /* Add horizontal scroll for overflow */
}

.no-schedule-selected {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #6b7280;
    font-size: 1.2em;
}

/* Sidebar */
.sidebar-item {
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
}

.sidebar-item.active {
    background-color: #3b82f6;
    color: white;
}

.schedule-list {
    margin-top: 15px;
}

.date-group {
    margin-bottom: 10px;
}

.date-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f1f5f9;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid #e5e7eb;
}

.date-header:hover {
    background-color: #e5e7eb;
}

.date-header h4 {
    margin: 0;
    font-size: 1em;
    font-weight: 600;
}

.date-header i {
    transition: transform 0.2s;
}

.sidebar-schedule-item {
    padding: 10px;
    border-radius: 6px;
    margin-top: 5px;
    cursor: pointer;
    border-left: 4px solid #ccc;
    transition: background-color 0.2s;
}

.sidebar-schedule-item:hover {
    background-color: #f3f4f6;
}

.sidebar-schedule-item.selected {
    background-color: #eef2ff;
    border-left-color: #3b82f6;
}

.sidebar-schedule-item.active-schedule {
    border-left-color: #10b981;
    background-color: #f0fdf4;
}

.schedule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.sidebar-schedule-info {
    width: 100%;
}

.schedule-label {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.schedule-meta {
    font-size: 0.8em;
    color: #6b7280;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.job-abbreviation {
    font-weight: bold;
    background-color: #e5e7eb;
    color: #4b5563;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.9em;
}

/* Schedule Detail */
.schedule-detail h2 {
    margin-top: 0;
    font-size: 1.3em;
    font-weight: 600;
}

.schedule-actions {
    margin-bottom: 15px;
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    background-color: #3b82f6;
    transition: opacity 0.2s, transform 0.2s;
}

.action-btn.danger {
    background-color: #ef4444;
}

.schedule-info {
    margin-bottom: 20px;
    background-color: #f8fafc;
    padding: 15px;
    border-radius: 6px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
}

.info-row span:first-child {
    font-weight: 500;
    color: #4b5563;
}

.copy-icon {
    margin-left: 8px;
    cursor: pointer;
    color: #9ca3af;
    transition: color 0.2s;
}

.copy-icon:hover {
    color: #3b82f6;
}

.no-batches-message {
    text-align: center;
    padding: 20px;
    color: #6b7280;
}

/* Batches and Items */
.batches {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.batch-item {
    background-color: #f8fafc;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    transition: border-color 0.2s, background-color 0.2s, box-shadow 0.2s, transform 0.2s;
}

.batch-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.current-batch {
    border-color: #3b82f6;
    background-color: #eef2ff;
}

.batch-errors {
    margin-top: 15px;
    margin-bottom: 15px;
    padding: 10px;
    background-color: #fff5f5;
    border-radius: 4px;
    border: 1px solid #ef4444;
}

.batch-errors-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.batch-errors-header h4 {
    margin: 0;
    color: #ef4444;
}

.toggle-errors-btn {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
}

.error-events-list {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #fecaca;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out;
}

.error-event-header {
    display: flex;
    gap: 15px;
    font-size: 0.9em;
    padding: 5px 0;
    font-weight: 600;
    border-bottom: 1px solid #fecaca;
    margin-bottom: 5px;
}

.error-event {
    display: flex;
    gap: 15px;
    font-size: 0.9em;
    padding: 5px 0;
}

.error-time {
    flex-basis: 150px;
    flex-shrink: 0;
}

.error-project {
    font-weight: 500;
    flex-basis: 150px;
    flex-shrink: 0;
}

.error-user {
    font-family: monospace;
    cursor: pointer;
    color: #3b82f6;
    text-decoration: underline;
    flex-basis: 180px;
    flex-shrink: 0;
    display: flex;
    gap: 5px;
}

.error-user span:first-child {
    color: #6b7280;
    text-decoration: none;
}

.error-type {
    flex-basis: 120px;
    flex-shrink: 0;
}

.error-comment {
    flex-grow: 1;
}

.batch-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e5e7eb;
}

.batch-meta {
    display: flex;
    gap: 15px;
    font-size: 0.9em;
    color: #6b7280;
    align-items: center;
}

.batch-meta .relative {
    position: relative;
}

.batch-meta .absolute {
    position: absolute;
    right: 0;
    margin-top: 0.5rem;
    width: 12rem;
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    ring: 1px solid rgba(0, 0, 0, 0.05);
    z-index: 10;
}

.batch-meta .py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.batch-meta .block {
    display: block;
}

.batch-meta .px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.batch-meta .py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.batch-meta .text-sm {
    font-size: 0.875rem;
}

.batch-meta .text-gray-700 {
    color: #4a5568;
}

.batch-meta .hover\:bg-gray-100:hover {
    background-color: #f7fafc;
}


.batch-action-btn {
    background-color: #3b82f6;
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.batch-action-btn:hover {
    background-color: #2563eb;
}

.batch-action-btn.loading {
    pointer-events: none;
}

.batch-action-btn i {
    margin-right: 5px;
}

.open-chromes-btn {
    background-color: #28a745; /* Green */
}

.open-chromes-btn:hover {
    background-color: #218838;
}

.close-chromes-btn {
    background-color: #dc3545; /* Red */
}

.close-chromes-btn:hover {
    background-color: #c82333;
}

.refresh-chromes-btn {
    background-color: #ffc107; /* Yellow */
}

.refresh-chromes-btn:hover {
    background-color: #e0a800;
}

.batch-running-time {
    font-style: italic;
}


.batch-header h3 {
    margin: 0;
}

.progress-bar-container {
    position: relative;
    margin: 15px 0;
}

.progress-label {
    position: absolute;
    top: 2px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8em;
    font-weight: bold;
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.progress-bar {
    display: flex;
    height: 20px;
    border-radius: 5px;
    overflow: hidden;
    margin-top: 10px;
    background-color: #e5e7eb;
}

.progress-segment {
    height: 100%;
    transition: width 0.5s ease-in-out;
}

.progress-segment.new { background-color: #6b7280; }
.progress-segment.prepared { background-color: #3b82f6; }
.progress-segment.prepare_failed { background-color: #f59e0b; }
.progress-segment.running { background-color: #10b981; }
.progress-segment.success { background-color: #10b981; }
.progress-segment.error { background-color: #ef4444; }
.progress-segment.volume { background-color: #3b82f6; }
.progress-segment.status { background-color: #3b82f6; }


.automation-items-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.automation-item-row {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 8px 12px;
    background-color: #fff;
    border-radius: 4px;
    border-left: 4px solid;
    margin-bottom: 8px;
}

.item-project-id {
    font-weight: 500;
    flex-basis: 130px;
    flex-shrink: 0;
    cursor: pointer;
    color: #3b82f6;
    text-decoration: underline;
}

.item-project-id:hover {
    color: #2563eb;
}

.status-container {
    position: relative;
    display: inline-block;
}

.status-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1e293b;
    color: #f8fafc;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 10;
    display: none;
    min-width: 150px;
    margin-bottom: 5px;
}

.status-container:hover .status-tooltip {
    display: block;
}

.agent-update-time {
    display: flex;
    align-items: center;
    gap: 5px;
    flex-basis: 140px;
    flex-shrink: 0;
}

.daily-volume {
    flex-basis: 80px;
    flex-shrink: 0;
    text-align: right;
    font-weight: 500;
}

.trades-action {
    flex-basis: 100px;
    flex-shrink: 0;
    text-align: center;
}

.trades-btn {
    background-color: #17a2b8;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8em;
    transition: background-color 0.2s;
}

.trades-btn:hover {
    background-color: #138496;
}



.agent-alias {
    color: #6b7280;
}

.timestamp {
    cursor: pointer;
    color: #3b82f6;
    text-decoration: underline;
    transition: color 0.2s;
}

.timestamp:hover {
    color: #2563eb;
}

.timestamp.timestamp-stale {
    color: #6b7280; /* gray-500 */
}

.comment-container {
    position: relative;
    display: inline-block;
    flex-grow: 1;
    min-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.comment-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1e293b;
    color: #f8fafc;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 10;
    display: none;
    min-width: 150px;
    margin-bottom: 5px;
}

.comment-container:hover .comment-tooltip {
    display: block;
}

.item-comment {
    flex-grow: 1;
    font-size: 0.9em;
    color: #6b7280;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.comment-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1e293b;
    color: #f8fafc;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 10;
    display: none;
    min-width: 150px;
    margin-bottom: 5px;
}

.comment-container:hover .comment-tooltip {
    display: block;
}

.item-comment {
    flex-grow: 1;
    font-size: 0.9em;
    color: #6b7280;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-time {
    font-size: 0.8em;
    color: #9ca3af;
    flex-basis: 100px;
    flex-shrink: 0;
    text-align: right;
}

.automation-item-row .status-badge {
    flex-basis: 120px;
    flex-shrink: 0;
    text-align: center;
}

/* Status Badges */
.status-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
    color: #fff !important;
    text-transform: uppercase;
}

.status-badge.new { background-color: #6b7280; }
.status-badge.prepared { background-color: #3b82f6; }
.status-badge.prepare_failed { background-color: #f59e0b; }
.status-badge.running { background-color: #10b981; }
.status-badge.running.idle { background-color: #000; color: #10b981 !important; }
.status-badge.stopped { background-color: #ef4444; }
.status-badge.stopped-norisk { background-color: #fd7e14; }
.status-badge.success { background-color: #10b981; }
.status-badge.error { background-color: #ef4444; }
.status-badge.canceled { background-color: #ef4444; }
.status-badge.paused { background-color: #f59e0b; }
.status-badge.timeout { background-color: #f59e0b; }
.status-badge.done { background-color: #10b981; }

/* Automation Item Border Colors */
.automation-item-row.new { border-left-color: #6b7280; }
.automation-item-row.prepared { border-left-color: #3b82f6; }
.automation-item-row.prepare_failed { border-left-color: #f59e0b; }
.automation-item-row.running { border-left-color: #10b981; }
.automation-item-row.timeout { border-left-color: #f59e0b; }
.automation-item-row.success { border-left-color: #10b981; }
.automation-item-row.error { border-left-color: #ef4444; }

/* Batch Summary Row */
.batch-summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background-color: #f1f5f9;
    border-top: 1px solid #e5e7eb;
    margin-top: 8px;
    border-radius: 0 0 4px 4px;
}

.batch-summary-row strong {
    font-weight: 600;
}

.summary-values {
    display: flex;
    gap: 20px;
    font-weight: 500;
}

/* Schedule Summary */
.schedule-summary {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 10px 15px;
    background-color: #f1f5f9;
    border-radius: 6px;
    border-top: 1px solid #e5e7eb;
}

.schedule-summary strong {
    font-weight: 600;
}

.schedule-summary .summary-values {
    display: flex;
    gap: 10px;
    align-items: center;
}

.schedule-summary .separator {
    color: #9ca3af;
}



/* Toast Styles */
.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 6px;
    color: white;
    font-size: 1em;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 2000;
}

.toast-success {
    background-color: #10b981;
}

.toast-error {
    background-color: #ef4444;
}

.toast-info {
    background-color: #3b82f6;
}


@keyframes flash {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.2;
    }
    100% {
        opacity: 1;
    }
}

.status-changed {
    animation: flash 1s;
}

.loading-spinner-small {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 12px;
    height: 12px;
    animation: spin 1s linear infinite;
    display: inline-block;
}

.toast-error {
    background-color: #ef4444;
}

/* Dialog and Position Changes Styles */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.dialog-content {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 0;
    width: 800px;
    max-width: 90vw;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.dialog-content.narrow {
    width: 500px;
}

.dialog-header {
    padding: 16px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #ffffff;
    position: sticky;
    top: 0;
    z-index: 1;
}

.dialog-header h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.dialog-header .agent-role {
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    margin-left: 8px;
    background-color: var(--master-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dialog-header .agent-role[data-role="master"] {
    background-color: var(--master-color);
    color: white;
}

.dialog-header .agent-role[data-role="copier"] {
    background-color: #06b6d4;
    color: white;
}

.close-button {
    background: none;
    border: none;
    color: #64748b;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s;
}

.close-button:hover {
    color: #ef4444;
}

.position-changes-list {
    padding: 16px;
    overflow-y: auto;
    flex: 1;
}

.position-changes-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.position-change-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background-color: #f8fafc;
    border-radius: 4px;
    font-size: 0.9rem;
}

.position-change-time {
    color: var(--gray-color);
    min-width: 80px;
    font-family: monospace;
}

.position-change-symbol {
    color: var(--dark-color);
    font-weight: 500;
    min-width: 60px;
}

.position-change-side {
    min-width: 2.5rem;
    text-align: center;
    font-weight: 500;
    font-size: 0.8rem;
}

.position-change-side.long {
    color: var(--long-color);
}

.position-change-side.short {
    color: var(--short-color);
}

.position-change-size {
    min-width: 3.5rem;
    text-align: right;
    font-weight: 500;
    font-family: monospace;
    margin-left: auto;
}

.position-change-size.long {
    color: var(--long-color);
}

.position-change-size.short {
    color: var(--short-color);
}

/* Clickable elements */
.clickable {
    cursor: pointer;
    color: #007bff;
    text-decoration: underline;
}

.clickable:hover {
    color: #0056b3;
}

/* Alpine.js specific */
[x-cloak] {
    display: none !important;
}

.position-changes-container.two-column {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.position-changes-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.position-changes-column {
    background-color: white;
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.position-changes-column h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.position-changes-column .position-change-item {
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.position-changes-column .position-change-item:last-child {
    margin-bottom: 0;
}

.text-green-500 {
    color: #10b981;
}

.text-red-500 {
    color: #ef4444;
}

.action-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-basis: 80px;
    flex-shrink: 0;
}

.settings-button, .status-toggle-btn {
    background: none;
    border: 1px solid #d1d5db;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s;
}

.settings-button:hover, .status-toggle-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.settings-icon, .status-icon {
    width: 18px;
    height: 18px;
}

.status-toggle-btn.running {
    color: #ef4444;
}

.status-toggle-btn.stopped,
.status-toggle-btn.paused {
    color: #10b981;
}

.settings-form {
    padding: 16px;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-control {
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    box-sizing: border-box;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.cancel-button, .save-button {
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    border: 1px solid transparent;
    font-weight: 500;
    cursor: pointer;
}

.cancel-button {
    background-color: #6c757d;
    color: white;
}

.save-button {
    background-color: #007bff;
    color: white;
}

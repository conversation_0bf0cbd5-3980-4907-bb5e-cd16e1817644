/* Base styles */
:root {
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --secondary-color: #6b7280;
    --secondary-hover: #4b5563;
    --success-color: #10b981;
    --success-hover: #059669;
    --danger-color: #ef4444;
    --danger-hover: #dc2626;
    --warning-color: #f59e0b;
    --warning-hover: #d97706;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    --master-color: #8b5cf6;
    --copier-color: #06b6d4;

    --long-color: #10b981;
    --short-color: #ef4444;

    --transition-duration: 0.3s;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    color: var(--dark-color);
    background-color: #f0f5ff;
}

.container {
    max-width: 1450px;
    margin: 20px auto 0;
    padding: 15px;
}

header {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h1,
h2,
h3,
h4 {
    margin-bottom: 10px;
}

h1 {
    font-size: 1.7em;
}

h2 {
    font-size: 1.3em;
}

h3 {
    font-size: 1.1em;
}

main {
    display: block;
    width: 1600px;
}

/* Loading, Error, and Empty States */
.loading-container,
.error-container,
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 30px 15px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-left-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.error-icon,
.empty-icon {
    font-size: 40px;
    margin-bottom: 15px;
}

.retry-button {
    display: inline-block;
    margin-top: 20px;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.retry-button:hover {
    background-color: var(--secondary-color);
}

/* Projects Content Layout */
.projects-content {
    display: grid;
    grid-template-columns: 320px 1fr;
    gap: 20px;
}

/* Projects Selection Sidebar */
.projects-selection {
    background-color: white;
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-width: 320px;
}


.projects-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding-right: 5px;
    overflow-x: hidden;
}

.project-item {
    padding: 10px 15px;
    border-radius: 6px;
    background-color: #f1f5f9;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.project-item strong {
    font-weight: 500;
}

.project-item:hover {
    background-color: #e2e8f0;
}

.project-item.selected {
    background-color: var(--primary-color);
    color: white;
}

.project-item.selected strong {
    font-weight: 600;
}

.relative {
    position: relative;
}

.absolute {
    position: absolute;
    right: 0;
    margin-top: 0.5rem;
    width: 12rem;
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    ring: 1px solid rgba(0, 0, 0, 0.05);
    z-index: 10;
}

.projects-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.menu-trigger {
    padding: 5px 10px; /* Smaller padding */
    font-size: 0.9rem; /* Smaller font size */
}


.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.block {
    display: block;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.text-sm {
    font-size: 0.875rem;
}

.text-gray-700 {
    color: #4a5568;
}

.hover\:bg-gray-100:hover {
    background-color: #f7fafc;
}



.project-status {
    font-size: 0.8em;
    padding: 3px 8px;
    border-radius: 12px;
    background-color: var(--gray-color);
    color: white;
    min-width: 75px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    gap: 4px;
    text-transform: uppercase;
}

.project-status.running {
    background-color: var(--success-color);
}

.project-status.stopped {
    background-color: var(--danger-color);
}

.project-status.stopped-norisk {
    background-color: #fd7e14;
}

.project-status.running.idle {
    background-color: #000000;
    color: #28a745;
}

/* Project Header and Status Controls */
.project-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    margin-bottom: 15px;
}

.project-status-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
}

.project-status-controls .status-toggle-btn {
    padding: 6px 16px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 600;
    color: white;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
    height: 30px;
    min-width: 80px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.project-status-controls .status-toggle-btn.running {
    background-color: var(--warning-color);
}

.project-status-controls .status-toggle-btn.running:hover {
    background-color: #e08c00;
}

.project-status-controls .status-toggle-btn.stopped {
    background-color: var(--primary-color);
}

.project-status-controls .status-toggle-btn.stopped:hover {
    color: #ffffff;
    background: #0056b3;
}

.project-status-controls .status-icon {
    width: 12px;
    height: 12px;
}

.delete-btn {
    background-color: #dc2626;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-weight: 500;
    min-width: 120px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.delete-btn:hover {
    background-color: #b91c1c;
}

/* Project items in sidebar */
.project-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

/* Project Details */
.project-details {
    background-color: white;
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.project-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    margin-bottom: 15px;
}

.status-badge.running {
    background-color: var(--success-color);
}

.status-badge.running.idle {
    background-color: #000000;
    color: #28a745;
}

.status-badge.stopped {
    background-color: #dc3545;
}



/* Mode badge styles */
.auto-badge {
    display: inline-block;
    background-color: var(--success-color);
    color: white;
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    margin-right: 4px;
}

.manual-badge {
    display: inline-block;
    background-color: var(--danger-color);
    color: white;
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    margin-right: 4px;
}

.debug-badge {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    margin-left: 4px;
}

/* Project Stats */
.project-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f8fafc;
    border-radius: 6px;
}

.stat-item {
    display: flex;
    flex-direction: column;
}

.stat-label {
    font-size: 0.85em;
    color: var(--gray-color);
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.2em;
    font-weight: 600;
}

/* Agents */
.agents-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 15px;
}

.agent-card {
    min-width: 0;
    background-color: white;
    padding: 12px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.agent-card:hover {
    transform: translateY(-2px);
}

.agent-card.master {
    border-color: var(--master-color);
    background-color: #f5f3ff;
}

.agent-card.copier {
    border-color: var(--copier-color);
    background-color: #ecfeff;
}

.agent-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e8f0;
}

.agent-user {
    font-size: 1.2rem;
    font-weight: 600;
    max-width: 420px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-family: monospace;
}

.agent-role {
    font-size: 0.7em;
    padding: 3px 8px;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    flex-shrink: 0;
}

.master-badge {
    background-color: var(--master-color);
}

.copier-badge {
    background-color: var(--copier-color);
}

.agent-details {
    margin-bottom: 15px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.detail-label {
    color: var(--gray-color);
    font-size: 0.85em;
    width: 90px;
    flex-shrink: 0;
}

.detail-value {
    font-weight: 500;
    font-size: 0.9em;
    max-width: 420px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: right;
}

/* Special handling for ETH addresses */
.detail-value.eth-address {
    font-size: 0.9em;
    font-family: monospace;
    max-width: 420px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.agent-user.eth-address {
    font-size: 1em;
    font-family: monospace;
    max-width: 420px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Positions */
.positions-section {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e2e8f0;
}

.positions-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.position-item {
    padding: 8px;
    border-radius: 6px;
    background-color: #f8fafc;
    border-left: 3px solid;
}

.position-item.long {
    border-color: var(--long-color);
}

.position-item.short {
    border-color: var(--short-color);
}

.position-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.position-symbol {
    font-weight: 600;
}

.position-side {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.75em;
    color: white;
}

.position-side.long {
    background-color: var(--long-color);
}

.position-side.short {
    background-color: var(--short-color);
}

.position-details {
    font-size: 0.9em;
}

.positive {
    color: var(--success-color);
}

.negative {
    color: var(--danger-color);
}

/* Footer */
footer {
    margin-top: 25px;
    font-size: 0.85em;
}

/* Responsive Design */
@media (max-width: 900px) {
    main {
        grid-template-columns: 1fr;
    }

    .projects-content {
        grid-template-columns: 1fr;
    }

    .projects-selection {
        margin-bottom: 20px;
    }

    .projects-list {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 10px;
    }

    .project-item {
        width: calc(50% - 5px);
    }

    .agent-card {
        width: 100%;
    }
}

@media (max-width: 600px) {
    .agents-container {
        flex-direction: column;
    }

    .agent-card {
        width: 100%;
    }

    .project-stats {
        grid-template-columns: 1fr;
    }

    .project-item {
        width: 100%;
    }
}

/* Transitions */
.fade-in {
    transition: opacity var(--transition-duration) ease-in;
    opacity: 0;
}

.fade-in.show {
    opacity: 1;
}

.fade-out {
    transition: opacity var(--transition-duration) ease-out;
}

/* Alpine.js transitions */
[x-cloak] {
    display: none !important;
}

.transition-all {
    transition-property: all;
    transition-duration: var(--transition-duration);
}

.transition-opacity {
    transition-property: opacity;
    transition-duration: var(--transition-duration);
}

.transition-transform {
    transition-property: transform;
    transition-duration: var(--transition-duration);
}

.transition-opacity-transform {
    transition-property: opacity, transform;
    transition-duration: var(--transition-duration);
}

/* Add performance-related styles */
.performance-section {
    margin-top: 20px;
    background-color: white;
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.performance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.performance-period-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.performance-period {
    font-size: 14px;
    color: #666;
}

.performance-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.chart-container {
    height: 300px;
    margin-top: 20px;
    position: relative;
}

.performance-positive {
    color: var(--success-color);
}

.performance-negative {
    color: var(--danger-color);
}

/* Risk Banner Styles */
.risk-banner {
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    border-radius: 4px;
    margin: 0 0 15px 0;
    padding: 8px 12px;
}

.risk-banner-content {
    max-width: 100%;
    margin: 0 auto;
}

.risk-banner-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.risk-banner-title {
    font-weight: bold;
    color: #856404;
    font-size: 0.9em;
}

.risk-toggle-btn {
    background: none;
    border: 1px solid #856404;
    color: #856404;
    padding: 2px 6px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8em;
}

.risk-toggle-btn:hover {
    background-color: #ffeeba;
}

.risk-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.risk-item {
    padding: 6px 8px;
    background-color: #fff8e1;
    border-radius: 4px;
}

.risk-info {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    font-size: 0.9em;
}

.risk-time {
    color: #856404;
    font-size: 0.8em;
    white-space: nowrap;
    padding-top: 2px;
}

.risk-message {
    color: #856404;
    flex: 1;
    white-space: pre-line;
}

.risk-message::first-line {
    font-weight: bold;
    font-size: 0.9em;
}

.risk-type {
    font-weight: bold;
    font-size: 0.9em;
}

.risk-details {
    font-size: 0.85em;
    opacity: 0.9;
}

/* Layout Adjustments */
.agents-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.delete-project-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
    text-align: center;
}


.overview-controls .reset-btn:disabled:hover {
    background-color: #6b7280;
    cursor: not-allowed;
}

.status-bar {
    background-color: #dc2626;
    color: white;
    padding: 8px 16px;
    margin: 8px 0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-message {
    font-weight: 500;
}

.close-chrome-btn {
    background-color: #f97316;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.close-chrome-btn:hover {
    background-color: #ea580c;
}

.close-chrome-btn:disabled {
    background-color: #6b7280;
    cursor: not-allowed;
}

.close-chrome-btn:disabled:hover {
    background-color: #6b7280;
}

.delete-project-section .reset-btn {
    background-color: var(--gray-600);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-weight: 500;
    min-width: 120px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.delete-project-section .reset-btn:hover {
    background-color: var(--gray-700);
}

.delete-project-section .reset-btn:disabled {
    background-color: var(--gray-300);
    cursor: not-allowed;
}

.delete-project-section .reset-btn:disabled:hover {
    background-color: var(--gray-300);
}

/* Risk Indicator */
.risk-indicator {
    color: #f59e0b;
    font-size: 0.8em;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 14px;
    height: 14px;
    border: 1px solid white;
    border-radius: 50%;
    background-color: #f59e0b;
}

/* Dialog and Position Changes Styles */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.dialog-content {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 0;
    width: 800px;
    max-width: 90vw;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.dialog-content.narrow {
    width: 500px;
}

.dialog-header {
    padding: 16px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #ffffff;
    position: sticky;
    top: 0;
    z-index: 1;
}

.dialog-header h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.dialog-header .agent-role {
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    margin-left: 8px;
    background-color: var(--master-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dialog-header .agent-role[data-role="master"] {
    background-color: var(--master-color);
    color: white;
}

.dialog-header .agent-role[data-role="copier"] {
    background-color: #06b6d4;
    color: white;
}

.close-button {
    background: none;
    border: none;
    color: #64748b;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s;
}

.close-button:hover {
    color: #ef4444;
}

.position-changes-list {
    padding: 16px;
    overflow-y: auto;
    flex: 1;
}

.position-changes-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.position-change-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background-color: #f8fafc;
    border-radius: 4px;
    font-size: 0.9rem;
}

.position-change-time {
    color: var(--gray-color);
    min-width: 80px;
    font-family: monospace;
}

.position-change-symbol {
    color: var(--dark-color);
    font-weight: 500;
    min-width: 60px;
}

.position-change-side {
    min-width: 2.5rem;
    text-align: center;
    font-weight: 500;
    font-size: 0.8rem;
}

.position-change-side.long {
    color: var(--long-color);
}

.position-change-side.short {
    color: var(--short-color);
}

.position-change-size {
    min-width: 3.5rem;
    text-align: right;
    font-weight: 500;
    font-family: monospace;
    margin-left: auto;
}

.position-change-size.long {
    color: var(--long-color);
}

.position-change-size.short {
    color: var(--short-color);
}

/* Clickable elements */
.clickable {
    cursor: pointer;
    color: #007bff;
    text-decoration: underline;
}

.clickable:hover {
    color: #0056b3;
}

/* Alpine.js specific */
[x-cloak] {
    display: none !important;
}

.position-changes-container.two-column {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.position-changes-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.position-changes-column {
    background-color: white;
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.position-changes-column h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.position-changes-column .position-change-item {
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.position-changes-column .position-change-item:last-child {
    margin-bottom: 0;
}

.eth-address {
    font-family: monospace;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-block;
}

.eth-address:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.settings-button {
    background: none;
    border: none;
    padding: 0;
    margin-left: 4px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
    width: 20px;
    height: 20px;
}

.settings-icon {
    width: 20px;
    height: 20px;
    color: var(--gray-color);
    transition: all 0.2s ease;
}

.settings-button:hover .settings-icon {
    transform: rotate(90deg);
    color: var(--primary-color);
}

.settings-button:active .settings-icon {
    transform: scale(0.95) rotate(90deg);
    opacity: 0.8;
}

/* Settings Dialog Styles */
.settings-form {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(80vh - 140px); /* 80vh (dialog) - header/footer height */
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--dark-color);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.cancel-button {
    padding: 8px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background-color: white;
    color: var(--gray-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.cancel-button:hover {
    background-color: #f8fafc;
    border-color: #cbd5e1;
}

.save-button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    background-color: var(--primary-color);
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.save-button:hover {
    background-color: var(--secondary-color);
}

.save-button:disabled {
    background-color: #cbd5e1;
    cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .settings-form {
        padding: 15px;
    }

    .form-actions {
        flex-direction: column;
    }

    .cancel-button,
    .save-button {
        width: 100%;
    }
}

/* Project Settings Comparison */
.settings-comparison {
    display: flex;
    gap: 2rem;
    padding: 1rem;
}

.settings-column {
    border-radius: 8px;
    padding: 1rem;
}

.settings-column:first-child {
    flex: 1;
    background: var(--card-bg);
}

.settings-column:last-child {
    flex: 1;
    background: var(--card-bg);
}

.settings-column h4 {
    margin-bottom: 1rem;
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.settings-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.settings-details .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: var(--bg-color);
    border-radius: 4px;
}

.settings-details .detail-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    min-width: 140px;
    white-space: nowrap;
}

.settings-details .detail-value {
    color: var(--text-color);
    font-weight: 500;
    text-align: right;
    word-break: break-all;
    flex: 1;
}

/* Overview Item Styles */
.overview-item {
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 5px;
}

.overview-item:hover {
    background-color: #e9ecef;
}

.overview-item.selected {
    background-color: var(--primary-color);
    border-left-color: #495057;
    color: #ffffff;
}

.overview-item .project-status {
    color: #6c757d;
    font-size: 0.8em;
    padding: 3px 8px;
    border-radius: 12px;
    background-color: #e2e8f0;
    min-width: 75px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.overview-table-container {
    overflow-x: auto;
    margin-top: 1rem;
    width: 100%;
    max-width: 100%;
    -webkit-overflow-scrolling: touch;
}

.overview-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
    table-layout: auto;
}

/* Make the first column (action buttons) fixed width */
.overview-table th:first-child,
.overview-table td:first-child {
    position: sticky;
    left: 0;
    background-color: white;
    z-index: 1;
}

/* Add shadow to indicate scrollable content */
.overview-table-container::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 5px;
    background: linear-gradient(to right, rgba(0,0,0,0.05), rgba(0,0,0,0));
    pointer-events: none;
}

.overview-table th,
.overview-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.overview-table th {
    background-color: #f8fafc;
    font-weight: 600;
    color: #1e293b;
    position: sticky;
    top: 0;
}

.overview-table tr:hover {
    background-color: #f1f5f9;
}

.overview-table .project-id {
    color: #2563eb;
    cursor: pointer;
    text-decoration: underline;
}

.overview-table .project-id:hover {
    color: #1e40af;
}

.overview-table .status-cell {
    position: relative;
    display: inline-block;
}

.overview-table .status-badge {
    padding: 0;
    background: none;
    font-weight: 500;
}

.overview-table .status-badge.running {
    color: var(--success-color);
}

.overview-table .status-badge.stopped {
    color: var(--danger-color);
}

.overview-table .status-badge.stopped-norisk {
    color: #fd7e14;
}

.overview-table .status-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1e293b;
    color: #f8fafc;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 10;
    display: none;
    min-width: 150px;
}

.overview-table .status-cell:hover .status-tooltip {
    display: block;
}

.overview-table .positive {
    color: #10b981;
}

.overview-table .negative {
    color: #ef4444;
}

.symbol-cell {
    position: relative;
    display: inline-block;
}

.symbol-tooltip {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.symbol-tooltip-content {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1e293b;
    color: #f8fafc;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1000;
    display: none;
    min-width: 150px;
    margin-bottom: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.symbol-tooltip:hover .symbol-tooltip-content {
    display: block;
}

.overview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.overview-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.overview-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: 20px;
}

.overview-actions button:not(.idle-toggle-btn):not(.lock-edit-btn) { 
    max-width: 90px;
    white-space: normal;
    text-align: center;
    padding: 4px 6px;
    height: 47px; /* Match select box */
    line-height: 1.2;
    font-size: 0.85rem; /* Slightly smaller font */
    display: flex;
    align-items: center;
    justify-content: center;
}

.overview-actions .start-all-btn,
.overview-actions .stop-all-btn,
.overview-actions .reset-btn {
    max-width: 70px;
    white-space: normal;
    text-align: center;
}

.view-mode-select {
    padding: 8px 12px;
    border: 1px solid #666;
    border-radius: 4px;
    font-size: 0.9rem;
    height: 47px;
    background-color: var(--bg-color);
    color: var(--text-color);
    cursor: pointer;
    min-width: 150px;
}

.view-mode-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.filter-container {
    display: flex;
    align-items: center;
    height: 47px;
}

.project-filter {
    padding: 8px 12px;
    border: none;
    border-bottom: 1px solid #666;
    background-color: var(--bg-color);
    color: var(--text-color);
    font-size: 0.9rem;
    width: 180px;
    outline: none;
    transition: border-color 0.2s;
    margin-left: 10px;
}

.project-filter:focus {
    border-bottom-color: var(--primary-color);
}

.start-all-btn,
.stop-all-btn,
.reset-btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.overview-actions .start-all-btn,
.overview-actions .stop-all-btn,
.overview-actions .reset-btn {
    max-width: 70px;
    white-space: normal;
    text-align: center;
}

.start-all-btn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    margin-left: 12px;
}

.start-all-btn:hover {
    background-color: #218838;
}

.stop-all-btn {
    background-color: #dc3545;
    color: white;
    border: none;
}

.stop-all-btn:hover {
    background-color: #c82333;
}

.idle-toggle-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.2rem;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    transition: all 0.2s ease;
}

.idle-toggle-btn:hover {
    background-color: var(--hover-color);
}

.idle-toggle-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.idle-toggle-btn .idle-icon {
    width: 24px;
    height: 24px;
}

.idle-toggle-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.start-all-btn:disabled,
.stop-all-btn:disabled,
.idle-toggle-btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

.start-all-btn:disabled:hover,
.stop-all-btn:disabled:hover,
.idle-toggle-btn:disabled:hover {
    opacity: 0.5;
    cursor: not-allowed;
}

.overview-table .subtotal-row {
    background-color: #f8fafc;
    border-top: 2px solid #e2e8f0;
}

.overview-table .subtotal-row td {
    padding: 1rem 0.75rem;
    font-size: 0.95rem;
}

.overview-table .subtotal-row strong {
    color: #1e293b;
}

.details-checkbox {
    text-align: right;
    padding-right: 1.5rem !important;
}

.today-performance-toggle,
.details-toggle {
    display: none;
}

.overview-table td.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    white-space: nowrap;
    width: 64px;
    padding: 0.4rem;
    height: 100%;
    border-bottom: none;
}

.overview-table .action-buttons .status-toggle-btn {
    padding: 0;
    background: none;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 36px;
    height: 36px;
    min-width: unset;
}

.overview-table .action-buttons .settings-button {
    padding: 0;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.overview-table .action-buttons .settings-icon,
.overview-table .action-buttons .status-icon {
    width: 21px;
    height: 21px;
}

.overview-table .action-buttons .status-toggle-btn.running {
    background: none;
    color: var(--danger-color);
}

.overview-table .action-buttons .status-toggle-btn.running:hover {
    color: #dc2626;
    background: none;
}

.overview-table .action-buttons .status-toggle-btn.stopped {
    background: none;
    color: var(--success-color);
}

.overview-table .action-buttons .status-toggle-btn.stopped:hover {
    color: #059669;
    background: none;
}

/* Edit Button Styles */
.edit-button {
    background: none;
    border: none;
    padding: 4px;
    margin-left: 8px;
    cursor: pointer;
    color: #666;
    transition: color 0.2s;
}

.edit-button:hover {
    color: #333;
}

.edit-icon {
    width: 16px;
    height: 16px;
}

/* Rename Dialog Styles */
.rename-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.rename-dialog-content {
    background: white;
    padding: 20px;
    border-radius: 8px;
    width: 400px;
    max-width: 90%;
}

.rename-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.rename-dialog-header h3 {
    margin: 0;
}

.rename-dialog-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.rename-dialog-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.rename-dialog-form .form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.rename-dialog-form label {
    font-weight: 500;
}

.rename-dialog-form input {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.rename-dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.rename-dialog-actions button {
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.rename-dialog-cancel {
    background: #f5f5f5;
    border: 1px solid #ddd;
}

.rename-dialog-submit {
    background: #4CAF50;
    border: none;
    color: white;
}

.rename-dialog-submit:disabled {
    background: #cccccc;
    cursor: not-allowed;
}



.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin: -8px 0 12px 0;
    padding: 4px 0;
    font-weight: 500;
    background-color: rgba(220, 53, 69, 0.05);
    border-radius: 4px;
    padding: 8px 12px;
    border-left: 3px solid #dc3545;
}

.today-performance-toggle {
    display: inline-flex;
    align-items: center;
    margin-right: 20px;
    cursor: pointer;
}

.today-performance-toggle input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
    margin-right: 5px;
}

.today-performance-toggle span {
    font-size: 14px;
    color: #666;
    font-weight: bold;
}

.agent-info-header {
    margin: 0 0 15px 0;
    padding: 10px 15px;
    background-color: var(--card-bg);
    border-radius: 6px;
    font-size: 1.1em;
    color: var(--text-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.lock-edit-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    transition: all 0.2s ease;
}

.lock-edit-btn:hover {
    background-color: var(--hover-color);
}

.lock-edit-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.lock-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    transition: all 0.2s ease;
}

.lock-btn:hover {
    background-color: var(--hover-color);
}

.lock-btn.locked {
    color: #28a745;
}

.lock-icon {
    width: 24px;
    height: 24px;
}

.lock-btn.disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

.lock-btn.disabled:hover {
    background-color: transparent;
}

.emergency-stop-btn {
    padding: 10px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 600;
    color: white;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    height: 48px;
    width: 48px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.emergency-stop-btn:hover {
    transform: scale(1.1);
}

.emergency-icon {
    width: 40px;
    height: 40px;
}

.scan-link {
    display: inline-block;
    margin-left: 4px;
    color: #6b7280;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.2s ease;
}

.scan-link:hover {
    color: #3b82f6;
}

.scan-button {
    align-items: center;
    justify-content: center;
    padding: 4px;
    margin-left: 4px;
    border: none;
    background: none;
    cursor: pointer;
    color: var(--text-color);
    transition: color 0.2s;
}

.scan-button:hover {
    color: var(--primary-color);
}

.scan-icon {
    width: 20px;
    height: 20px;
    vertical-align: middle;
}

.extpos-btn {
    background-color: #2171e0;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.extpos-btn:hover {
    background-color: #1b4e9b;
}

.extpos-btn:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
}

.extpos-btn:disabled:hover {
    background-color: var(--gray-300);
}

.external-positions-content {
    padding: 10px;
}

.external-positions-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 5px;
}

.external-positions-table th,
.external-positions-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.external-positions-table th {
    background-color: var(--background-color);
    font-weight: 600;
}

.external-positions-table tr:hover {
    background-color: var(--hover-color);
}

.position-exists {
    color: var(--success-color);
    font-weight: 500;
}

.position-missing {
    color: var(--error-color);
    font-weight: 500;
}

.stoploss-text {
    color: #dc2626;  /* Red color */
    font-weight: 500;
}

.overview-controls .reset-btn {
    background-color: var(--gray-600);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-weight: 500;
    min-width: 80px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.overview-controls .reset-btn:hover {
    background-color: var(--gray-700);
}

.overview-controls .reset-btn:disabled {
    background-color: var(--gray-300);
    cursor: not-allowed;
}

.overview-controls .reset-btn:disabled:hover {
    background-color: var(--gray-300);
}

.delete-project-section .reset-btn {
    background-color: var(--gray-600);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-weight: 500;
    min-width: 120px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.delete-project-section .reset-btn:hover {
    background-color: var(--gray-700);
}

.delete-project-section .reset-btn:disabled {
    background-color: var(--gray-300);
    cursor: not-allowed;
}

.delete-project-section .reset-btn:disabled:hover {
    background-color: var(--gray-300);
}

.start-chrome-btn {
    background-color: #10b981;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.start-chrome-btn:hover {
    background-color: #059669;
}

.start-chrome-btn:disabled {
    background-color: #6b7280;
    cursor: not-allowed;
}

.start-chrome-btn:disabled:hover {
    background-color: #6b7280;
}

.overview-table .performance-positive {
    color: var(--success-color);
}

.overview-table .performance-negative {
    color: var(--danger-color);
}

/* Overview and Agent Columns */
.overview-column {
    display: table-cell;
}

.agent-column {
    display: none;
}

/* When details are shown */
.show-details .overview-column {
    display: none;
}

.show-details .agent-column {
    display: table-cell;
}

/* When in daily view mode */
.daily-view .overview-column {
    display: table-cell;
}

.daily-view .agent-column {
    display: none;
}

.update-stats-btn {
    padding: 8px 16px;
    background-color: #7c3aed;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.update-stats-btn:hover {
    background-color: #6d28d9;
}

.update-stats-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.update-stats-btn:disabled:hover {
    background-color: #ccc;
}

.agent-stats-column {
    display: none;
}

.show-agent-stats .agent-stats-column {
    display: table-cell;
}

.show-agent-stats .overview-column,
.show-agent-stats .agent-column {
    display: none;
}

/* Global Options Dialog Styles */
.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 24px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.toast.success {
    background-color: #4caf50;
}

.toast.error {
    background-color: #f44336;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.risk-time-tooltip {
    position: relative;
    cursor: help;
}

.risk-time-tooltip-content {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1e293b;
    color: #f8fafc;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 10;
    display: none;
    min-width: 150px;
    text-align: center;
    margin-bottom: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.risk-time-tooltip:hover .risk-time-tooltip-content {
    display: block;
}

/* Batch Settings Dialog Styles */
.form-text {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.form-text.text-muted {
    color: #9ca3af;
}

/* Ensure the batch settings button is properly aligned */
.overview-controls .settings-button {
    margin-left: 0.5rem;
}

/* Style for the batch settings icon */
.overview-controls .settings-button svg {
    width: 20px;
    height: 20px;
}

/* Ensure proper spacing in the batch settings form */
.batch-settings-form .form-group {
    margin-bottom: 1.5rem;
}

/* Style for number inputs in batch settings */
.batch-settings-form input[type="number"] {
    -moz-appearance: textfield;
}

.batch-settings-form input[type="number"]::-webkit-outer-spin-button,
.batch-settings-form input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.batch-settings-filtered-projects {
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--bg-secondary);
    border-radius: 8px;
}

.batch-settings-filtered-projects h4 {
    margin: 0 0 10px 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.batch-settings-project-ids-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.batch-settings-project-id-tag {
    padding: 4px 8px;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.875rem;
    color: var(--text-primary);
}

.last-week-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.last-week-toggle input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    cursor: pointer;
}

.last-week-toggle span {
    font-size: 0.875rem;
    color: var(--text-color);
}

.quick-options-widget {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px;
    margin: 8px 0;
}

.quick-options-container {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.quick-option {
    display: flex;
    align-items: center;
    gap: 8px;
}

.quick-option label {
    font-size: 0.9em;
    color: #495057;
    white-space: nowrap;
}

.quick-input {
    width: 120px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.quick-option .checkbox-label {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.9em;
    color: #495057;
    cursor: pointer;
}

.quick-option .checkbox-label input[type="checkbox"] {
    margin: 0;
}

/* Stale time styling */
.stale-time {
    color: var(--gray-400) !important;
}
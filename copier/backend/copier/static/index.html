<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>copier</title>
        <link rel="stylesheet" href="/static/css/styles.css" />
        <!-- Load Alpine.js with defer -->
        <script src="/static/js/alpine_3.x.x.js" defer></script>
        <script src="/static/js/chart.js"></script>
    </head>
    <body>
        <div class="container" x-data="app" x-init="init()" x-cloak>
            <main>
                <!-- Loading State -->
                <div x-show="loading" class="loading-container transition-opacity-transform" x-transition>
                    <div class="loading-spinner"></div>
                    <p>Loading projects...</p>
                </div>

                <!-- Error State -->
                <div x-show="!loading && error" class="error-container transition-opacity-transform" x-transition>
                    <div class="error-icon">⚠️</div>
                    <h2>Error</h2>
                    <p x-text="error"></p>
                    <button @click="fetchProjects" class="retry-button">Retry</button>
                </div>

                <!-- No Projects State -->
                <div x-show="!loading && !error && dataReady && projects.length === 0" class="empty-state transition-opacity-transform" x-transition>
                    <div class="empty-icon">📊</div>
                    <h2>No Projects Found</h2>
                    <p>There are no projects available in the system.</p>
                </div>

                <!-- Version Info -->
                <div x-show="serverVersion" class="version-info" style="position: fixed; bottom: 10px; left: 10px; z-index: 1000; background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px;">
                    <span x-text="'Server: ' + serverVersion"></span>
                </div>

                <!-- Projects Content -->
                <div x-show="!loading && !error && dataReady && projects.length > 0" class="projects-content transition-opacity-transform" x-transition>
                    <!-- Projects Selection -->
                    <div class="projects-selection">
                        <div class="projects-header">
                            <h2>Projects</h2>
                            <div x-data="{ open: false }" class="relative">
                                <div class="project-item menu-trigger" @click="open = !open">
                                    <strong>Menu</strong>
                                    <svg class="w-5 h-5 ml-2 -mr-1" viewBox="0 0 20 20" fill="currentColor" style="width: 1.25rem; height: 1.25rem; margin-left: 0.5rem; margin-right: -0.25rem;">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                                    <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                                        <a href="/automation" target="_blank" @click="open = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Automation</a>
                                        <a href="/balancer" target="_blank" @click="open = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Balancer</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="projects-list">
                            <div class="project-item overview-item" x-on:click="selectProject('overview')" x-bind:class="{ 'selected': selectedProject === 'overview' }">
                                <strong>Overview</strong>
                                <div class="project-controls">
                                    <span class="project-status">
                                        <span x-text="'All Projects'"></span>
                                    </span>
                                </div>
                            </div>
                            <template x-for="project in projects" :key="project.projectId">
                                <div class="project-item" x-on:click="selectProject(project.projectId)" x-bind:class="{ 'selected': selectedProject === project.projectId }">
                                    <strong x-text="project.projectId"></strong>
                                    <div class="project-controls">
                                        <span class="project-status"
                                            x-bind:class="{
                                                'running': project.status.toLowerCase() === 'running',
                                                'idle': project.statusComment === 'idle',
                                                'stopped': project.status.toLowerCase() === 'stopped' && project.statusComment === 'risk',
                                                'stopped-norisk': project.status.toLowerCase() === 'stopped' && project.statusComment !== 'risk'
                                            }">
                                            <span x-show="projectsWithRisks.has(project.projectId)" class="risk-indicator">●</span>
                                            <span x-text="project.status"></span>
                                        </span>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- Project Details -->
                    <div class="project-details" x-show="selectedProject !== null">
                        <!-- Overview Table -->
                        <template x-if="selectedProject === 'overview'">
                            <div class="overview-section">
                                <div class="overview-header">
                                    <div class="overview-controls">
                                        <div class="filter-container">
                                            <button 
                                                class="settings-button" 
                                                @click="$dispatch('show-global-options')"
                                                title="Global Options"
                                            >
                                                <svg class="settings-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <circle cx="12" cy="12" r="3"></circle>
                                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                                </svg>
                                            </button>
                                            <button 
                                                class="settings-button" 
                                                @click="$dispatch('show-batch-settings', { projectIds: filteredProjectIds })"
                                                title="Batch Update Settings"
                                            >
                                                <svg class="settings-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                                </svg>
                                            </button>
                                            <input 
                                                type="text" 
                                                x-model="projectFilter" 
                                                class="project-filter"
                                                placeholder="Filter project"
                                            >
                                            <div x-data="{ open: false }" class="relative inline-block">
                                                <button @click="open = !open" class="settings-button" title="Quick Filters">
                                                    <svg class="settings-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                                                    </svg>
                                                </button>
                                                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                                                    <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                                                        <a href="#" @click.prevent="projectFilter = ''; open = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">reset</a>
                                                        <div class="border-t border-gray-200 my-1"></div>
                                                        <a href="#" @click.prevent="projectFilter = 'hasPosition: true'; open = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">hasPosition: true</a>
                                                        <a href="#" @click.prevent="projectFilter = 'isRunning: true'; open = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">isRunning: true</a>
                                                        <div class="border-t border-gray-200 my-1"></div>
                                                        <a href="#" @click.prevent="projectFilter = 'margin > '; open = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">margin ></a>
                                                        <a href="#" @click.prevent="projectFilter = 'margin < '; open = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">margin <</a>
                                                        <div class="border-t border-gray-200 my-1"></div>
                                                        <template x-for="group in projectGroups" :key="group">
                                                            <a href="#" @click.prevent="projectFilter = group; open = false" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" x-text="group"></a>
                                                        </template>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="overview-actions">
                                            <select class="view-mode-select" x-model="viewMode">
                                                <option value="overview">Overview</option>
                                                <option value="daily">Daily Overview</option>
                                                <option value="last">LastWeek Overview</option>
                                                <option value="agent">Agent Status</option>
                                                <option value="agent-stats">Agent Stats</option>
                                            </select>

                                            <!-- <button 
                                                class="extpos-btn" 
                                                @click="$dispatch('show-external-positions')"
                                                :disabled="isLockEditMode"
                                                :title="isLockEditMode ? 'Disabled in lock edit mode' : 'View External Positions'"
                                            >ExtPos</button> -->
                                            <button 
                                                class="reset-btn" 
                                                @click="resetAllProjects"
                                                :disabled="isLockEditMode"
                                                :title="isLockEditMode ? 'Disabled in lock edit mode' : 'Reset All Projects'"
                                            >Reset</button>
                                            <button 
                                                class="update-stats-btn" 
                                                @click="updateStats"
                                                :disabled="isLockEditMode"
                                                :title="isLockEditMode ? 'Disabled in lock edit mode' : 'Update Stats for Selected Projects'"
                                            >Update Stats</button>
                                            <button 
                                                class="start-chrome-btn" 
                                                @click="startAllChromes"
                                                :disabled="isLockEditMode"
                                                :title="isLockEditMode ? 'Disabled in lock edit mode' : 'Open All Chrome Windows'"
                                            >Open Chrome</button>
                                            <button 
                                                class="close-chrome-btn" 
                                                @click="closeAllChromes"
                                                :disabled="isLockEditMode"
                                                :title="isLockEditMode ? 'Disabled in lock edit mode' : 'Close All Chrome Windows'"
                                            >Close Chrome</button>
                                            <button 
                                                class="refresh-chrome-btn" 
                                                @click="refreshAllChromes"
                                                :disabled="isLockEditMode"
                                                :title="isLockEditMode ? 'Disabled in lock edit mode' : 'Refresh All Chrome Windows'"
                                            >Refresh Chrome</button>
                                            <!-- <button 
                                                class="goto-trade-btn" 
                                                @click="gotoTrade"
                                                :disabled="isLockEditMode"
                                                :title="isLockEditMode ? 'Disabled in lock edit mode' : 'Navigate to Trade Page'"
                                            >Goto Trade</button> -->
                                            <button 
                                                class="start-all-btn" 
                                                @click="updateAllProjects('running')"
                                                :disabled="isLockEditMode"
                                                :title="isLockEditMode ? 'Disabled in lock edit mode' : 'Start All'"
                                            >Start All</button>
                                            <button 
                                                class="stop-all-btn" 
                                                @click="updateAllProjects('stopped')"
                                                :disabled="isLockEditMode"
                                                :title="isLockEditMode ? 'Disabled in lock edit mode' : 'Stop All'"
                                            >Stop All</button>
                                            <button 
                                                class="reset-btn" 
                                                @click="clearProjectStatusComment"
                                                :disabled="isLockEditMode"
                                                :title="isLockEditMode ? 'Disabled in lock edit mode' : 'Clear Status'"
                                            >Clear Status</button>
                                            <!-- <button 
                                                class="idle-toggle-btn"
                                                @click="toggleIdleMode"
                                                :class="{ 'active': isIdleMode }"
                                                :title="isIdleMode ? 'Switch off Idle Mode' : 'Switch on Idle Mode'"
                                            >
                                                <svg class="idle-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <circle cx="12" cy="12" r="10"/>
                                                    <path d="M12 6v6l4 2"/>
                                                </svg>
                                            </button> -->
                                            <button 
                                                class="lock-edit-btn" 
                                                @click="toggleLockEditMode"
                                                :class="{ 'active': isLockEditMode }"
                                                title="Toggle Lock Edit Mode"
                                            >
                                                <svg class="lock-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="quick-options-widget">
                                    <div class="quick-options-container">
                                        <div class="quick-option">
                                            <label class="checkbox-label">
                                                <input type="checkbox" x-model="options.activateChrome" @change="saveGlobalOptions">
                                                <span>Activate Chrome</span>
                                            </label>
                                        </div>
                                        <div class="quick-option">
                                            <label class="checkbox-label">
                                                <input type="checkbox" x-model="options.enableAutoStop" @change="saveGlobalOptions">
                                                <span>AutoStop</span>
                                            </label>
                                        </div>
                                        <div class="quick-option">
                                            <label>Volume</label>
                                            <input type="number" x-model="options.autoStopVolume" @change="saveGlobalOptions" class="quick-input" step="any">
                                        </div>
                                        <div class="quick-option">
                                            <label>Daily Vol</label>
                                            <input type="number" x-model="options.autoStopDailyVolume" @change="saveGlobalOptions" class="quick-input" step="any">
                                        </div>
                                    </div>
                                </div>
                                <div class="status-bar" x-show="!options.enableAutoStop || !options.activateChrome">
                                    <span class="status-message" x-show="!options.enableAutoStop">Autostop not enabled</span>
                                    <span x-show="!options.enableAutoStop && !options.activateChrome" style="margin: 0 0.5rem;">|</span>
                                    <span class="status-message" x-show="!options.activateChrome">Chrome activation not enabled</span>
                                </div>
                                <div class="overview-table-container">
                                    <table class="overview-table" x-bind:class="{
                                        'show-details': viewMode === 'agent',
                                        'daily-view': viewMode === 'daily',
                                        'show-agent-stats': viewMode === 'agent-stats'
                                    }">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>Project ID</th>
                                                <th>Status</th>
                                                <th>Symbol</th>
                                                <th class="overview-column">Total Margin</th>
                                                <th class="overview-column">Position</th>
                                                <th class="overview-column">Volume</th>
                                                <th class="overview-column">Cost</th>
                                                <th class="overview-column">Cost Ratio</th>
                                                <th class="overview-column">Trades</th>
                                                <th class="overview-column">Last Trade</th>
                                                <th class="overview-column">Last Risk</th>
                                                <!-- Agent Columns -->
                                                <th class="agent-column">M. Margin</th>
                                                <th class="agent-column">M. Position</th>
                                                <th class="agent-column">M. Stoploss</th>
                                                <th class="agent-column">M. Updated</th>
                                                <th class="agent-column"></th> <!-- separator -->
                                                <th class="agent-column">C. Margin</th>
                                                <th class="agent-column">C. Position</th>
                                                <th class="agent-column">C. Stoploss</th>
                                                <th class="agent-column">C. Updated</th>
                                                <!-- Agent Stats Columns -->
                                                <th class="agent-stats-column">M. Margin</th>
                                                <th class="agent-stats-column">M. Volume</th>
                                                <th class="agent-stats-column">M. Points</th>
                                                <th class="agent-stats-column">M. Updated</th>
                                                <th class="agent-stats-column">C. Margin</th>
                                                <th class="agent-stats-column">C. Volume</th>
                                                <th class="agent-stats-column">C. Points</th>
                                                <th class="agent-stats-column">C. Updated</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <template x-for="overview in filteredOverviews" :key="overview.projectId">
                                                <tr>
                                                    <td class="action-buttons">
                                                        <button class="settings-button" @click="openMasterSettings(overview.projectId)">
                                                            <svg class="settings-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                <circle cx="12" cy="12" r="3"></circle>
                                                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                                            </svg>
                                                        </button>
                                                        <template x-if="isProjectLocked(overview.projectId) || isLockEditMode">
                                                            <button 
                                                                class="lock-btn" 
                                                                :class="{ 
                                                                    'locked': isProjectLocked(overview.projectId),
                                                                    'disabled': !isLockEditMode && isProjectLocked(overview.projectId)
                                                                }"
                                                                @click="isLockEditMode && toggleProjectLock(overview.projectId)"
                                                                :title="isLockEditMode ? (isProjectLocked(overview.projectId) ? 'Click to unlock project' : 'Click to lock project') : 'Project is locked'"
                                                            >
                                                                <svg class="lock-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                                                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                                                </svg>
                                                            </button>
                                                        </template>
                                                        <template x-if="!isProjectLocked(overview.projectId) && !isLockEditMode">
                                                            <button 
                                                                class="status-toggle-btn" 
                                                                x-bind:class="overview.status.toLowerCase()"
                                                                @click="if(confirm(overview.status === 'running' ? 'Pause ' + overview.projectId + '?' : 'Resume ' + overview.projectId + '?')) toggleProjectStatus(overview.projectId, overview.status)"
                                                                x-bind:title="overview.status === 'running' ? 'Pause' : 'Resume'"
                                                            >
                                                                <template x-if="overview.status === 'running'">
                                                                    <svg class="status-icon" width="24" height="24" viewBox="0 0 16 16" fill="currentColor">
                                                                        <rect x="4" y="2" width="3" height="12"/>
                                                                        <rect x="9" y="2" width="3" height="12"/>
                                                                    </svg>
                                                                </template>
                                                                <template x-if="overview.status !== 'running'">
                                                                    <svg class="status-icon" width="24" height="24" viewBox="0 0 16 16" fill="currentColor">
                                                                        <path d="M4 2l8 6-8 6z"/>
                                                                    </svg>
                                                                </template>
                                                            </button>
                                                        </template>
                                                    </td>
                                                    <td>
                                                        <span class="project-id" @click="selectProject(overview.projectId)" x-text="overview.projectId"></span>
                                                    </td>
                                                    <td>
                                                        <div class="status-cell">
                                                            <span class="status-badge" x-text="overview.status.toUpperCase()"
                                                                x-bind:class="{
                                                                    'running': overview.status.toLowerCase() === 'running',
                                                                    'idle': overview.statusComment === 'idle',
                                                                    'stopped': overview.status.toLowerCase() === 'stopped' && overview.statusComment === 'risk',
                                                                    'stopped-norisk': overview.status.toLowerCase() === 'stopped' && overview.statusComment !== 'risk'
                                                                }"></span>
                                                            <div class="status-tooltip" x-show="overview.statusComment">
                                                                <span x-text="'[' + overview.statusComment + ']'"></span>
                                                                <span x-text="'Last Updated: ' + new Date(overview.statusUpdateTime).toLocaleString()"></span>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="symbol-cell">
                                                            <span class="symbol-tooltip">
                                                                <span x-text="overview.symbol"></span>
                                                                <div class="symbol-tooltip-content" x-show="overview.defaultSize || overview.coolingHour">
                                                                    <span x-text="overview.symbol + ' (DefaultSize: ' + overview.defaultSize + ', CoolingHour: ' + overview.coolingHour + 'h)'"></span>
                                                                </div>
                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td class="overview-column" x-text="'$' + overview.totalMargin.toFixed(0)"></td>
                                                    <td class="overview-column" x-text="'$' + overview.positionValue.toFixed(0)"></td>
                                                    <td class="overview-column" x-text="viewMode === 'daily' ? (overview.todayPerformance?.volume ? '$' + overview.todayPerformance.volume.toFixed(0) : '$0') : (viewMode === 'last' ? (overview.lastPerformance?.volume ? '$' + overview.lastPerformance.volume.toFixed(0) : '$0') : (overview.latestPerformance?.volume ? '$' + overview.latestPerformance.volume.toFixed(0) : '$0'))"></td>
                                                    <td class="overview-column" x-bind:class="viewMode === 'daily' ? (overview.todayPerformance?.cost >= 0 ? 'positive' : 'negative') : (viewMode === 'last' ? (overview.lastPerformance?.cost >= 0 ? 'positive' : 'negative') : (overview.latestPerformance?.cost >= 0 ? 'positive' : 'negative'))" 
                                                        x-text="viewMode === 'daily' ? (overview.todayPerformance?.cost ? '$' + overview.todayPerformance.cost.toFixed(2) : '$0.00') : (viewMode === 'last' ? (overview.lastPerformance?.cost ? '$' + overview.lastPerformance.cost.toFixed(2) : '$0.00') : (overview.latestPerformance?.cost ? '$' + overview.latestPerformance.cost.toFixed(2) : '$0.00'))"></td>
                                                    <td class="overview-column" x-bind:class="viewMode === 'daily' ? (overview.todayPerformance?.ratio >= 0 ? 'positive' : 'negative') : (viewMode === 'last' ? (overview.lastPerformance?.ratio >= 0 ? 'positive' : 'negative') : (overview.latestPerformance?.ratio >= 0 ? 'positive' : 'negative'))"
                                                        x-text="viewMode === 'daily' ? (overview.todayPerformance?.ratio ? (overview.todayPerformance.ratio * 100).toFixed(4) + '%' : '0.0000%') : (viewMode === 'last' ? (overview.lastPerformance?.ratio ? (overview.lastPerformance.ratio * 100).toFixed(4) + '%' : '0.0000%') : (overview.latestPerformance?.ratio ? (overview.latestPerformance.ratio * 100).toFixed(4) + '%' : '0.0000%'))"></td>
                                                    <td class="overview-column">
                                                        <span 
                                                            class="clickable" 
                                                            x-text="(viewMode === 'daily' ? (overview.todayPerformance?.trades || 0) : (viewMode === 'last' ? (overview.lastPerformance?.trades || 0) : (overview.latestPerformance?.trades || 0))) + ' trades'"
                                                            @click="$dispatch('show-position-changes', { projectId: overview.projectId, showMasterAndCopier: true })"
                                                        ></span>
                                                    </td>
                                                    <td class="overview-column">
                                                        <template x-if="overview.lastTradeTime">
                                                            <span 
                                                                x-text="new Date(overview.lastTradeTime).toLocaleTimeString('sv-SE', { timeZone: 'Asia/Shanghai', hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false })"
                                                                :title="new Date(overview.lastTradeTime).toLocaleString('sv-SE', { timeZone: 'Asia/Shanghai' }).replace('.', ' ').slice(0, 19)"
                                                            ></span>
                                                        </template>
                                                        <template x-if="!overview.lastTradeTime">
                                                            <span>N/A</span>
                                                        </template>
                                                    </td>
                                                    <td class="overview-column">
                                                        <template x-if="overview.lastRiskTime">
                                                            <span class="risk-time-tooltip">
                                                                <span x-text="new Date(overview.lastRiskTime).toLocaleTimeString('sv-SE', { timeZone: 'Asia/Shanghai', hour: '2-digit', minute: '2-digit', hour12: false })"></span>
                                                                <div class="risk-time-tooltip-content" x-show="overview.lastRiskType">
                                                                    <span x-text="overview.lastRiskType"></span>
                                                                    <span x-text="new Date(overview.lastRiskTime).toLocaleString('sv-SE', { timeZone: 'Asia/Shanghai' }).replace('.', ' ').slice(0, 19)"></span>
                                                                </div>
                                                            </span>
                                                        </template>
                                                        <template x-if="!overview.lastRiskTime">
                                                            <span>N/A</span>
                                                        </template>
                                                    </td>
                                                    <!-- Agent Columns -->
                                                    <td class="agent-column">
                                                        <span 
                                                            x-text="(() => {
                                                                const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'master');
                                                                return agent ? '$' + (agent.totalMargin || 0).toFixed(0) : '$0';
                                                            })()"
                                                        ></span>
                                                    </td>
                                                    <td class="agent-column"
                                                        :class="(() => {
                                                            const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'master');
                                                            if (!agent || !agent.positions) return '';
                                                            const net = agent.positions.reduce((sum, pos) => sum + (pos.side === 'long' ? pos.size : -pos.size), 0);
                                                            return net > 0 ? 'positive' : net < 0 ? 'negative' : '';
                                                        })()"
                                                        x-text="(() => {
                                                            const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'master');
                                                            if (!agent || !agent.positions) return '$0';
                                                            const total = agent.positions.reduce((sum, pos) => sum + (pos.size * pos.markPrice), 0);
                                                            return '$' + total.toFixed(0);
                                                        })()"></td>
                                                    <td class="agent-column"
                                                        x-text="(() => {
                                                            const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'master');
                                                            if (!agent || !agent.positions) return 'N/A';
                                                            const sls = agent.positions.filter(p => p.stopLossPrice).map(p => '$' + p.stopLossPrice.toFixed(2));
                                                            return sls.length ? sls.join(', ') : 'N/A';
                                                        })()"></td>
                                                    <td class="agent-column">
                                                        <span 
                                                            class="clickable"
                                                            :class="{ 'stale-time': getAgentUpdateTimeInfo(overview.projectId, 'master').isStale }"
                                                            :title="getAgentStatusTime(overview.projectId, 'master', true)"
                                                            x-text="getAgentStatusTime(overview.projectId, 'master', false)"
                                                            @click="activateChrome(overview.projectId, projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'master')?.userId)"
                                                        ></span>
                                                    </td>
                                                    <td class="agent-column"></td> <!-- separator -->
                                                    <td class="agent-column">
                                                        <span 
                                                            x-text="(() => {
                                                                const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'copier');
                                                                return agent ? '$' + (agent.totalMargin || 0).toFixed(0) : '$0';
                                                            })()"
                                                        ></span>
                                                    </td>
                                                    <td class="agent-column"
                                                        :class="(() => {
                                                            const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'copier');
                                                            if (!agent || !agent.positions) return '';
                                                            const net = agent.positions.reduce((sum, pos) => sum + (pos.side === 'long' ? pos.size : -pos.size), 0);
                                                            return net > 0 ? 'positive' : net < 0 ? 'negative' : '';
                                                        })()"
                                                        x-text="(() => {
                                                            const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'copier');
                                                            if (!agent || !agent.positions) return '$0';
                                                            const total = agent.positions.reduce((sum, pos) => sum + (pos.size * pos.markPrice), 0);
                                                            return '$' + total.toFixed(0);
                                                        })()"></td>
                                                    <td class="agent-column"
                                                        x-text="(() => {
                                                            const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'copier');
                                                            if (!agent || !agent.positions) return 'N/A';
                                                            const sls = agent.positions.filter(p => p.stopLossPrice).map(p => '$' + p.stopLossPrice.toFixed(2));
                                                            return sls.length ? sls.join(', ') : 'N/A';
                                                        })()"></td>
                                                    <td class="agent-column">
                                                        <span 
                                                            class="clickable"
                                                            :class="{ 'stale-time': getAgentUpdateTimeInfo(overview.projectId, 'copier').isStale }"
                                                            :title="getAgentStatusTime(overview.projectId, 'copier', true)"
                                                            x-text="getAgentStatusTime(overview.projectId, 'copier', false)"
                                                            @click="activateChrome(overview.projectId, projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'copier')?.userId)"
                                                        ></span>
                                                    </td>
                                                    <!-- Agent Stats Columns -->
                                                    <td class="agent-stats-column" x-text="(() => {
                                                        const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'master');
                                                        return agent?.stats?.portfolioValue ? '$' + agent.stats.portfolioValue.toFixed(0) : '$0';
                                                    })()"></td>
                                                    <td class="agent-stats-column" x-text="(() => {
                                                        const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'master');
                                                        return agent?.stats?.allTimeVolume ? '$' + agent.stats.allTimeVolume.toFixed(0) : '$0';
                                                    })()"></td>
                                                    <td class="agent-stats-column" x-text="(() => {
                                                        const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'master');
                                                        return agent?.stats?.allTimePoints ? agent.stats.allTimePoints.toLocaleString('en-US', { maximumFractionDigits: 0 }) : '0';
                                                    })()"></td>
                                                    <td class="agent-stats-column">
                                                        <span 
                                                            class="clickable"
                                                            :class="{ 'stale-time': getAgentStatsTimeInfo(overview.projectId, 'master').isStale }"
                                                            :title="getAgentStatsTime(overview.projectId, 'master', true)"
                                                            x-text="getAgentStatsTime(overview.projectId, 'master', false)"
                                                            @click="handleActivateAndUpdateStats(overview.projectId, projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'master')?.userId)"
                                                        ></span>
                                                    </td>
                                                    <td class="agent-stats-column" x-text="(() => {
                                                        const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'copier');
                                                        return agent?.stats?.portfolioValue ? '$' + agent.stats.portfolioValue.toFixed(0) : '$0';
                                                    })()"></td>
                                                    <td class="agent-stats-column" x-text="(() => {
                                                        const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'copier');
                                                        return agent?.stats?.allTimeVolume ? '$' + agent.stats.allTimeVolume.toFixed(0) : '$0';
                                                    })()"></td>
                                                    <td class="agent-stats-column" x-text="(() => {
                                                        const agent = projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'copier');
                                                        return agent?.stats?.allTimePoints ? agent.stats.allTimePoints.toLocaleString('en-US', { maximumFractionDigits: 0 }) : '0';
                                                    })()"></td>
                                                    <td class="agent-stats-column">
                                                        <span 
                                                            class="clickable"
                                                            :class="{ 'stale-time': getAgentStatsTimeInfo(overview.projectId, 'copier').isStale }"
                                                            :title="getAgentStatsTime(overview.projectId, 'copier', true)"
                                                            x-text="getAgentStatsTime(overview.projectId, 'copier', false)"
                                                            @click="handleActivateAndUpdateStats(overview.projectId, projects.find(p => p.projectId === overview.projectId)?.agents?.find(a => a.role === 'copier')?.userId)"
                                                        ></span>
                                                    </td>
                                                </tr>
                                            </template>
                                        </tbody>
                                        <tfoot>
                                            <tr class="subtotal-row">
                                                <td></td>
                                                <td><strong>Subtotal</strong></td>
                                                <td></td>
                                                <td></td>
                                                <td class="overview-column">
                                                    <strong x-text="'$' + filteredOverviews.reduce((sum, o) => sum + o.totalMargin, 0).toFixed(0)"></strong>
                                                </td>
                                                <td class="overview-column">
                                                    <strong x-text="'$' + filteredOverviews.reduce((sum, o) => sum + o.positionValue, 0).toFixed(0)"></strong>
                                                </td>
                                                <td class="overview-column">
                                                    <strong x-text="'$' + filteredOverviews.reduce((sum, o) => sum + (viewMode === 'daily' ? (o.todayPerformance?.volume || 0) : (viewMode === 'last' ? (o.lastPerformance?.volume || 0) : (o.latestPerformance?.volume || 0))), 0).toFixed(0)"></strong>
                                                </td>
                                                <td class="overview-column" x-bind:class="filteredOverviews.reduce((sum, o) => sum + (viewMode === 'daily' ? (o.todayPerformance?.cost || 0) : (viewMode === 'last' ? (o.lastPerformance?.cost || 0) : (o.latestPerformance?.cost || 0))), 0) >= 0 ? 'positive' : 'negative'">
                                                    <strong x-text="'$' + filteredOverviews.reduce((sum, o) => sum + (viewMode === 'daily' ? (o.todayPerformance?.cost || 0) : (viewMode === 'last' ? (o.lastPerformance?.cost || 0) : (o.latestPerformance?.cost || 0))), 0).toFixed(2)"></strong>
                                                </td>
                                                <td class="overview-column" x-bind:class="(() => {
                                                    const totalVolume = filteredOverviews.reduce((sum, o) => sum + (viewMode === 'daily' ? (o.todayPerformance?.volume || 0) : (viewMode === 'last' ? (o.lastPerformance?.volume || 0) : (o.latestPerformance?.volume || 0))), 0);
                                                    const totalCost = filteredOverviews.reduce((sum, o) => sum + (viewMode === 'daily' ? (o.todayPerformance?.cost || 0) : (viewMode === 'last' ? (o.lastPerformance?.cost || 0) : (o.latestPerformance?.cost || 0))), 0);
                                                    return totalVolume ? (totalCost / totalVolume) >= 0 ? 'positive' : 'negative' : '';
                                                })()">
                                                    <strong x-text="(() => {
                                                        const totalVolume = filteredOverviews.reduce((sum, o) => sum + (viewMode === 'daily' ? (o.todayPerformance?.volume || 0) : (viewMode === 'last' ? (o.lastPerformance?.volume || 0) : (o.latestPerformance?.volume || 0))), 0);
                                                        const totalCost = filteredOverviews.reduce((sum, o) => sum + (viewMode === 'daily' ? (o.todayPerformance?.cost || 0) : (viewMode === 'last' ? (o.lastPerformance?.cost || 0) : (o.latestPerformance?.cost || 0))), 0);
                                                        const ratio = totalVolume ? (totalCost / totalVolume) : 0;
                                                        return (ratio * 100).toFixed(4) + '%';
                                                    })()"></strong>
                                                </td>
                                                <td class="overview-column">
                                                    <strong x-text="filteredOverviews.reduce((sum, o) => sum + (viewMode === 'daily' ? (o.todayPerformance?.trades || 0) : (viewMode === 'last' ? (o.lastPerformance?.trades || 0) : (o.latestPerformance?.trades || 0))), 0)"></strong>
                                                </td>
                                                <td class="agent-column"></td> <!-- separator -->
                                                <td class="agent-column"></td>
                                                <td class="agent-column"></td>
                                                <td class="agent-column"></td>
                                                <td class="agent-column"></td>
                                                <td class="agent-stats-column"></td>
                                                <td class="agent-stats-column"></td>
                                                <td class="agent-stats-column"></td>
                                                <td class="agent-stats-column"></td>
                                                <td class="agent-stats-column"></td>
                                                <td class="agent-stats-column"></td>
                                                <td class="agent-stats-column"></td>
                                                <td class="agent-stats-column"></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </template>

                        <!-- Regular Project Details -->
                        <template x-if="selectedProject !== 'overview'">
                            <template x-for="project in projects.filter(p => p.projectId === selectedProject)" :key="project.projectId">
                                <div>
                                    <!-- Risk Banner -->
                                    <div x-show="!loading && !error && getLatestRisk()" class="risk-banner" x-transition>
                                        <div class="risk-banner-content">
                                            <div class="risk-banner-header">
                                                <span class="risk-banner-title">⚠️ Risk Alert</span>
                                                <button @click="toggleShowAllRisks" class="risk-toggle-btn">
                                                    <span x-text="showAllRisks ? 'Show Less' : 'Show All (1h)'"></span>
                                                </button>
                                            </div>

                                            <template x-if="!showAllRisks && getLatestRisk()">
                                                <div class="risk-item">
                                                    <div class="risk-info">
                                                        <span class="risk-time" x-text="formatRiskTime(getLatestRisk()?.CreateTime)"></span>
                                                        <span class="risk-message" x-html="formatRiskMessage(getLatestRisk())"></span>
                                                    </div>
                                                </div>
                                            </template>

                                            <template x-if="showAllRisks && getRecentRisks().length > 0">
                                                <div class="risk-list">
                                                    <template x-for="risk in getRecentRisks()" :key="risk.CreateTime">
                                                        <div class="risk-item">
                                                            <div class="risk-info">
                                                                <span class="risk-time" x-text="formatRiskTime(risk.CreateTime)"></span>
                                                                <span class="risk-message" x-html="formatRiskMessage(risk)"></span>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </div>
                                            </template>
                                        </div>
                                    </div>

                                    <div class="project-header">
                                        <h2>
                                            <span x-text="project.projectId"></span>
                                            <button class="edit-button" @click="$dispatch('show-rename-dialog', { projectId: project.projectId })">
                                                <svg class="edit-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                                </svg>
                                            </button>
                                            <button class="settings-button" @click="$dispatch('show-project-settings', { projectId: project.projectId })">
                                                <svg class="settings-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <circle cx="12" cy="12" r="3"></circle>
                                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                                </svg>
                                            </button>
                                        </h2>
                                        <div class="project-status-controls">
                                            <span class="status-badge" x-text="project.status" x-bind:class="[project.status.toLowerCase(), project.statusComment === 'idle' ? 'idle' : '']"></span>
                                            <button class="status-toggle-btn" x-bind:class="project.status.toLowerCase()" x-text="project.status === 'running' ? 'Pause' : 'Resume'" @click.prevent.stop="toggleProjectStatus(project.projectId, project.status)" x-bind:title="project.statusComment ? `Comment: ${project.statusComment}\nLast Updated: ${new Date(project.statusUpdateTime).toLocaleString()}` : `Last Updated: ${new Date(project.statusUpdateTime).toLocaleString()}`"></button>
                                            <!-- <button
                                                class="idle-toggle-btn"
                                                :class="{ 'active': project.statusComment === 'idle' }"
                                                @click.prevent.stop="setProjectIdle(project.projectId, project.statusComment !== 'idle')"
                                                :disabled="project.status === 'stopped'"
                                                :title="project.status === 'stopped' ? 'Cannot set idle when stopped' : (project.statusComment === 'idle' ? 'Switch off Idle Mode' : 'Switch on Idle Mode')"
                                            >
                                                <svg class="idle-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <circle cx="12" cy="12" r="10"/>
                                                    <path d="M12 6v6l4 2"/>
                                                </svg>
                                            </button> -->
                                            <button
                                                class="emergency-stop-btn"
                                                @click.prevent.stop="emergencyStop(project.projectId)"
                                                title="Emergency Stop and Withdraw Funds"
                                            >
                                                <svg class="emergency-icon" width="40" height="40" viewBox="0 0 32 32" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <circle cx="16" cy="16" r="14" fill="#dc2626"/>
                                                    <path d="M16 8v8M16 20v.01" stroke="white" stroke-width="2" stroke-linecap="round"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Project Stats -->
                                    <div class="project-stats">
                                        <div class="stat-item">
                                            <span class="stat-label">Total Margin</span>
                                            <span class="stat-value" x-text="'$' + project.totalMargin.toFixed(2)"></span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-label">Position</span>
                                            <span class="stat-value" x-text="'$' + project.positionValue.toFixed(2)"></span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-label">Volume</span>
                                            <span class="stat-value" x-text="'$' + project.volume.toFixed(2)"></span>
                                        </div>
                                    </div>

                                    <!-- Performance Section -->
                                    <div class="performance-section">
                                        <!-- Performance Loading State -->
                                        <div x-show="performanceLoading" class="loading-container" style="padding: 15px">
                                            <div class="loading-spinner"></div>
                                            <p>Loading performance data...</p>
                                        </div>

                                        <!-- Performance Error State -->
                                        <div x-show="!performanceLoading && performanceError" class="error-container" style="padding: 15px">
                                            <div class="error-icon">⚠️</div>
                                            <p x-text="performanceError"></p>
                                            <button @click="fetchPerformance(selectedProject)" class="retry-button">Retry</button>
                                        </div>

                                        <!-- Performance Data -->
                                        <div x-show="!performanceLoading && !performanceError && performance">
                                            <!-- Overall Performance Stats -->
                                            <div class="performance-header">
                                                <h4>Overall Performance</h4>
                                                <div class="performance-period-container">
                                                    <label class="last-week-toggle">
                                                        <input type="checkbox" x-model="showLastWeekPerformance">
                                                        <span>LastWeek</span>
                                                    </label>
                                                    <span
                                                        class="performance-period"
                                                        x-text="performance ? (new Date(performance.startTime).toLocaleDateString() + ' - ' + new Date(performance.endTime).toLocaleDateString()) : ''"
                                                    ></span>
                                                </div>
                                            </div>
                                            <div class="performance-stats">
                                                <div class="stat-item">
                                                    <span class="stat-label">Volume</span>
                                                    <span class="stat-value" x-text="showLastWeekPerformance ? (project.lastPerformance?.volume ? '$' + project.lastPerformance.volume.toFixed(2) : '$0.00') : (performance?.performance?.volume ? '$' + performance.performance.volume.toFixed(2) : '$0.00')"></span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">Cost</span>
                                                    <span
                                                        class="stat-value"
                                                        x-text="showLastWeekPerformance ? (project.lastPerformance?.cost ? '$' + project.lastPerformance.cost.toFixed(2) : '$0.00') : (performance?.performance?.cost ? '$' + performance.performance.cost.toFixed(2) : '$0.00')"
                                                        x-bind:class="showLastWeekPerformance ? (project.lastPerformance?.cost >= 0 ? 'performance-positive' : 'performance-negative') : (performance?.performance?.cost >= 0 ? 'performance-positive' : 'performance-negative')"
                                                    ></span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">Ratio</span>
                                                    <span
                                                        class="stat-value"
                                                        x-text="showLastWeekPerformance ? (project.lastPerformance?.ratio ? (project.lastPerformance.ratio * 100).toFixed(4) + '%' : '0.0000%') : (performance?.performance?.ratio ? (performance.performance.ratio * 100).toFixed(4) + '%' : '0.0000%')"
                                                        x-bind:class="showLastWeekPerformance ? (project.lastPerformance?.ratio >= 0 ? 'performance-positive' : 'performance-negative') : (performance?.performance?.ratio >= 0 ? 'performance-positive' : 'performance-negative')"
                                                    ></span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">Trades</span>
                                                    <span 
                                                        class="stat-value clickable" 
                                                        x-text="showLastWeekPerformance ? (project.lastPerformance?.trades || 0) : (performance?.performance?.trades || 0)"
                                                        @click="$dispatch('show-position-changes', { projectId: project.projectId, showMasterAndCopier: true })"
                                                    ></span>
                                                </div>
                                            </div>

                                            <!-- Daily Performance Chart -->
                                            <template x-if="performance && performance.dailyPerformance && performance.dailyPerformance.length > 0">
                                                <div>
                                                    <h4>Daily Performance</h4>
                                                    <div class="chart-container">
                                                        <canvas id="performance-chart"></canvas>
                                                    </div>
                                                </div>
                                            </template>

                                            <!-- No Daily Performance Data -->
                                            <template x-if="!performance || !performance.dailyPerformance || performance.dailyPerformance.length === 0">
                                                <div class="empty-state" style="padding: 15px; margin-top: 15px">
                                                    <p>No daily performance data available for this period.</p>
                                                </div>
                                            </template>
                                        </div>
                                    </div>

                                    <!-- Master Agents -->
                                    <div class="agents-section">
                                        <div>
                                            <h3>Master Agents</h3>
                                            <div class="agents-container">
                                                <template x-for="agent in project.agents.filter(a => a.role === 'master')" :key="agent.userId">
                                                    <div class="agent-card master" :data-user-id="agent.userId">
                                                        <div class="agent-header">
                                                            <span class="agent-user">
                                                                <template x-if="agent.settings?.alias">
                                                                    <span class="agent-alias" x-text="'[' + agent.settings.alias + '] '"></span>
                                                                </template>
                                                                <span class="eth-address" 
                                                                    x-text="agent.userId ? agent.userId.slice(0, 6) + '...' + agent.userId.slice(-4) : ''"
                                                                    :title="agent.userId"
                                                                    @click="copyToClipboard(agent.userId)"
                                                                ></span>
                                                                <button class="settings-button" @click="openSettings(project.projectId, agent.userId)">
                                                                    <svg class="settings-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                        <circle cx="12" cy="12" r="3"></circle>
                                                                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                                                    </svg>
                                                                </button>
                                                                <!-- <a :href="'https://scan.lighter.xyz/account/' + agent.userId" target="_blank" class="scan-button" title="View on Lighter Scan">
                                                                    <svg class="scan-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                        <circle cx="11" cy="11" r="8"></circle>
                                                                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                                                                    </svg>
                                                                </a> -->
                                                            </span>
                                                            <span class="agent-role master-badge">MASTER</span>
                                                        </div>
                                                        <div class="agent-details">
                                                            <div class="detail-row">
                                                                <span class="detail-label">Platform</span>
                                                                <span class="detail-value" x-text="agent.platform"></span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Symbol</span>
                                                                <span class="detail-value" x-text="agent.symbol"></span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Default Size</span>
                                                                <span class="detail-value" x-text="agent.settings?.defaultSize || '0'"></span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Cooling Hour</span>
                                                                <span class="detail-value" x-text="agent.settings?.coolingHour || '0'"></span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Margin</span>
                                                                <span class="detail-value" x-text="'$' + agent.totalMargin.toFixed(2) + ' / $' + agent.availableMargin.toFixed(2)"></span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Trades</span>
                                                                <span 
                                                                    class="detail-value clickable" 
                                                                    x-text="performance?.performance?.agentTrades?.[agent.userId] || 0"
                                                                    @click="$dispatch('show-position-changes', { projectId: project.projectId, userId: agent.userId })"
                                                                ></span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Mode</span>
                                                                <span class="detail-value">
                                                                    <span class="auto-badge" x-show="!agent.settings?.manualMode">AUTO</span>
                                                                    <span class="manual-badge" x-show="agent.settings?.manualMode">MANUAL</span>
                                                                    <span class="debug-badge" x-show="agent.settings?.debug">DEBUG</span>
                                                                </span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Updated</span>
                                                                <span class="detail-value" x-text="agent.createTime ? new Date(agent.createTime).toLocaleString('sv-SE', { timeZone: 'Asia/Singapore' }).replace('.', ' ').slice(0, 19) : 'N/A'"></span>
                                                            </div>
                                                        </div>

                                                        <!-- Positions for this agent -->
                                                        <template x-if="agent.positions && agent.positions.length > 0">
                                                            <div class="positions-section">
                                                                <h4>Positions</h4>
                                                                <div class="positions-list">
                                                                    <template x-for="position in agent.positions" :key="position.symbol + position.side">
                                                                        <div class="position-item" x-bind:class="position.side.toLowerCase()">
                                                                            <div class="position-header">
                                                                                <span class="position-symbol" x-text="position.symbol"></span>
                                                                                <span class="position-side" x-text="position.side.toUpperCase()" x-bind:class="position.side.toLowerCase()"></span>
                                                                            </div>
                                                                            <div class="position-details">
                                                                                <div class="detail-row">
                                                                                    <span class="detail-label">Size</span>
                                                                                    <span class="detail-value" x-text="position.size"></span>
                                                                                </div>
                                                                                <div class="detail-row">
                                                                                    <span class="detail-label">Entry Price</span>
                                                                                    <span class="detail-value" x-text="'$' + position.entryPrice.toFixed(2)"></span>
                                                                                </div>
                                                                                <div class="detail-row">
                                                                                    <span class="detail-label">Mark Price</span>
                                                                                    <span class="detail-value" x-text="'$' + position.markPrice.toFixed(2)"></span>
                                                                                </div>
                                                                                <div class="detail-row">
                                                                                    <span class="detail-label">PNL</span>
                                                                                    <span
                                                                                        class="detail-value"
                                                                                        x-text="'$' + position.pnl.toFixed(2) + ' (' + position.pnlPercentage.toFixed(2) + '%)'"
                                                                                        x-bind:class="position.pnl >= 0 ? 'positive' : 'negative'"
                                                                                    ></span>
                                                                                </div>
                                                                                <div class="detail-row">
                                                                                    <span class="detail-label">Liq. Price</span>
                                                                                    <span class="detail-value" x-text="'$' + position.liquidationPrice.toFixed(2) + ' (' + ((position.liquidationPrice - position.markPrice) / position.markPrice * 100).toFixed(2) + '%)'"></span>
                                                                                </div>
                                                                                <div class="detail-row">
                                                                                    <span class="detail-label">SL Price</span>
                                                                                    <span class="detail-value" x-text="position.stopLossPrice ? '$' + position.stopLossPrice.toFixed(2) + ' (' + ((position.stopLossPrice - position.liquidationPrice) / position.liquidationPrice * 100).toFixed(2) + '%)' : 'N/A'"></span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </template>
                                                                </div>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>

                                        <!-- Copier Agents -->
                                        <div>
                                            <h3>Copier Agents</h3>
                                            <div class="agents-container">
                                                <template x-for="agent in project.agents.filter(a => a.role === 'copier')" :key="agent.userId">
                                                    <div class="agent-card copier" :data-user-id="agent.userId">
                                                        <div class="agent-header">
                                                            <span class="agent-user">
                                                                <template x-if="agent.settings?.alias">
                                                                    <span class="agent-alias" x-text="'[' + agent.settings.alias + '] '"></span>
                                                                </template>
                                                                <span class="eth-address" 
                                                                    x-text="agent.userId ? agent.userId.slice(0, 6) + '...' + agent.userId.slice(-4) : ''"
                                                                    :title="agent.userId"
                                                                    @click="copyToClipboard(agent.userId)"
                                                                ></span>
                                                                <button class="settings-button" @click="openSettings(project.projectId, agent.userId)">
                                                                    <svg class="settings-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                        <circle cx="12" cy="12" r="3"></circle>
                                                                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                                                    </svg>
                                                                </button>
                                                                <!-- <a :href="'https://scan.lighter.xyz/account/' + agent.userId" target="_blank" class="scan-button" title="View on Lighter Scan">
                                                                    <svg class="scan-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                        <circle cx="11" cy="11" r="8"></circle>
                                                                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                                                                    </svg>
                                                                </a> -->
                                                            </span>
                                                            <span class="agent-role copier-badge">COPIER</span>
                                                        </div>
                                                        <div class="agent-details">
                                                            <div class="detail-row">
                                                                <span class="detail-label">Platform</span>
                                                                <span class="detail-value" x-text="agent.platform"></span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Symbol</span>
                                                                <span class="detail-value" x-text="agent.symbol"></span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Copy From</span>
                                                                <span class="detail-value eth-address" 
                                                                    x-text="agent.copyFromId ? agent.copyFromId.slice(0, 6) + '...' + agent.copyFromId.slice(-4) : 'N/A'"
                                                                    :title="agent.copyFromId"
                                                                    @click="copyToClipboard(agent.copyFromId)"
                                                                ></span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Copy %</span>
                                                                <span class="detail-value" x-text="agent.copyPercentage + '%'"></span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Margin</span>
                                                                <span class="detail-value" x-text="'$' + agent.totalMargin.toFixed(2) + ' / $' + agent.availableMargin.toFixed(2)"></span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Trades</span>
                                                                <span 
                                                                    class="detail-value clickable" 
                                                                    x-text="performance?.performance?.agentTrades?.[agent.userId] || 0"
                                                                    @click="$dispatch('show-position-changes', { projectId: project.projectId, userId: agent.userId })"
                                                                ></span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Mode</span>
                                                                <span class="detail-value">
                                                                    <span class="auto-badge" x-show="!agent.settings?.manualMode">AUTO</span>
                                                                    <span class="manual-badge" x-show="agent.settings?.manualMode">MANUAL</span>
                                                                    <span class="debug-badge" x-show="agent.settings?.debug">DEBUG</span>
                                                                </span>
                                                            </div>
                                                            <div class="detail-row">
                                                                <span class="detail-label">Updated</span>
                                                                <span class="detail-value" x-text="agent.createTime ? new Date(agent.createTime).toLocaleString('sv-SE', { timeZone: 'Asia/Singapore' }).replace('.', ' ').slice(0, 19) : 'N/A'"></span>
                                                            </div>
                                                        </div>

                                                        <!-- Positions for this agent -->
                                                        <template x-if="agent.positions && agent.positions.length > 0">
                                                            <div class="positions-section">
                                                                <h4>Positions</h4>
                                                                <div class="positions-list">
                                                                    <template x-for="position in agent.positions" :key="position.symbol + position.side">
                                                                        <div class="position-item" x-bind:class="position.side.toLowerCase()">
                                                                            <div class="position-header">
                                                                                <span class="position-symbol" x-text="position.symbol"></span>
                                                                                <span class="position-side" x-text="position.side.toUpperCase()" x-bind:class="position.side.toLowerCase()"></span>
                                                                            </div>
                                                                            <div class="position-details">
                                                                                <div class="detail-row">
                                                                                    <span class="detail-label">Size</span>
                                                                                    <span class="detail-value" x-text="position.size"></span>
                                                                                </div>
                                                                                <div class="detail-row">
                                                                                    <span class="detail-label">Entry Price</span>
                                                                                    <span class="detail-value" x-text="'$' + position.entryPrice.toFixed(2)"></span>
                                                                                </div>
                                                                                <div class="detail-row">
                                                                                    <span class="detail-label">Mark Price</span>
                                                                                    <span class="detail-value" x-text="'$' + position.markPrice.toFixed(2)"></span>
                                                                                </div>
                                                                                <div class="detail-row">
                                                                                    <span class="detail-label">PNL</span>
                                                                                    <span
                                                                                        class="detail-value"
                                                                                        x-text="'$' + position.pnl.toFixed(2) + ' (' + position.pnlPercentage.toFixed(2) + '%)'"
                                                                                        x-bind:class="position.pnl >= 0 ? 'positive' : 'negative'"
                                                                                    ></span>
                                                                                </div>
                                                                                <div class="detail-row">
                                                                                    <span class="detail-label">Liq. Price</span>
                                                                                    <span class="detail-value" x-text="'$' + position.liquidationPrice.toFixed(2) + ' (' + ((position.liquidationPrice - position.markPrice) / position.markPrice * 100).toFixed(2) + '%)'"></span>
                                                                                </div>
                                                                                <div class="detail-row">
                                                                                    <span class="detail-label">SL Price</span>
                                                                                    <span class="detail-value" x-text="position.stopLossPrice ? '$' + position.stopLossPrice.toFixed(2) + ' (' + ((position.stopLossPrice - position.liquidationPrice) / position.liquidationPrice * 100).toFixed(2) + '%)' : 'N/A'"></span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </template>
                                                                </div>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Delete Project Button -->
                                    <div class="delete-project-section">
                                        <button class="reset-btn" @click="resetProject(project.projectId)">Reset Project</button>
                                        <button class="delete-btn" @click="deleteProject(project.projectId)">Delete Project</button>
                                    </div>
                                </div>
                            </template>
                        </template>
                    </div>
                </div>
            </main>

            <!-- Position Changes Dialog -->
            <div 
                x-data="positionChangesDialog"
                x-ref="positionChangesDialog"
                x-show="showPositionChanges" 
                class="dialog-overlay"
                @click.away="showPositionChanges = false"
                @show-position-changes.window="fetchPositionChanges($event.detail.projectId, $event.detail.userId, $event.detail.showMasterAndCopier)"
                x-cloak
            >
                <div class="dialog-content" style="max-width: 1200px;">
                    <div class="dialog-header">
                        <h3 x-text="!showMasterAndCopier ? 'Trades - ' + (singleAgentInfo?.alias ? '[' + singleAgentInfo.alias + ']' : '') : 'Trades - ' + selectedProject"></h3>
                        <button @click="showPositionChanges = false" class="close-button">&times;</button>
                    </div>
                    
                    <div x-show="positionChangesLoading" class="loading-container">
                        <div class="loading-spinner"></div>
                        <p>Loading position changes...</p>
                    </div>

                    <div x-show="!positionChangesLoading && positionChangesError" class="error-container">
                        <div class="error-icon">⚠️</div>
                        <p x-text="positionChangesError"></p>
                        <button @click="fetchPositionChanges(selectedProject, selectedAgent, showMasterAndCopier)" class="retry-button">Retry</button>
                    </div>

                    <div x-show="!positionChangesLoading && !positionChangesError" class="position-changes-list">
                        <template x-if="!showMasterAndCopier && (!positionChanges || positionChanges.length === 0)">
                            <div class="empty-state">
                                <p>No position changes found.</p>
                            </div>
                        </template>
                        <div class="position-changes-container" x-bind:class="{ 'two-column': showMasterAndCopier }">
                            <template x-if="!showMasterAndCopier">
                                <template x-if="positionChanges && positionChanges.length > 0">
                                    <template x-for="(change, index) in positionChanges" :key="index">
                                        <div x-html="formatPositionChange(change)"></div>
                                    </template>
                                </template>
                            </template>
                            <template x-if="showMasterAndCopier">
                                <div class="position-changes-grid">
                                    <div class="position-changes-column">
                                        <h4 x-text="'Master - ' + (masterAgentInfo?.alias ? '[' + masterAgentInfo.alias + '] ' : '') + masterAgentInfo?.address"></h4>
                                        <template x-if="masterPositionChanges && masterPositionChanges.length > 0">
                                            <template x-for="(change, index) in masterPositionChanges" :key="index">
                                                <div x-html="formatPositionChange(change)"></div>
                                            </template>
                                        </template>
                                        <template x-if="!masterPositionChanges || masterPositionChanges.length === 0">
                                            <div class="empty-state">
                                                <p>No position changes for master agent.</p>
                                            </div>
                                        </template>
                                    </div>
                                    <div class="position-changes-column">
                                        <h4 x-text="copierAgentInfo ? 'Copier - ' + (copierAgentInfo.alias ? '[' + copierAgentInfo.alias + '] ' : '') + copierAgentInfo.address : 'No Copier Agent'"></h4>
                                        <template x-if="copierPositionChanges && copierPositionChanges.length > 0">
                                            <template x-for="(change, index) in copierPositionChanges" :key="index">
                                                <div x-html="formatPositionChange(change)"></div>
                                            </template>
                                        </template>
                                        <template x-if="!copierPositionChanges || copierPositionChanges.length === 0">
                                            <div class="empty-state">
                                                <p>No position changes for copier agent.</p>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Dialog -->
            <div 
                x-data="settingsDialog"
                x-ref="settingsDialog"
                x-show="showSettings" 
                class="dialog-overlay"
                @click.away="showSettings = false"
                @show-settings.window="fetchSettings($event.detail.agent)"
                x-cloak
            >
                <div class="dialog-content" style="max-width: 600px;">
                    <div class="dialog-header">
                        <h3>
                            <template x-if="settings.alias">
                                <span x-text="'[' + settings.alias + '] '" class="agent-alias"></span>
                            </template>
                            <span x-text="selectedAgent ? selectedAgent.slice(0, 6) + '...' + selectedAgent.slice(-4) : ''"></span>
                            <span class="agent-role" :data-role="selectedAgentRole.toLowerCase()" x-text="selectedAgentRole"></span>
                        </h3>
                        <button @click="showSettings = false" class="close-button">&times;</button>
                    </div>
                    
                    <div x-show="settingsLoading" class="loading-container">
                        <div class="loading-spinner"></div>
                        <p>Loading settings...</p>
                    </div>

                    <div x-show="!settingsLoading && settingsError" class="error-container">
                        <div class="error-icon">⚠️</div>
                        <p x-text="settingsError"></p>
                        <button @click="fetchSettings(selectedProject, selectedAgent)" class="retry-button">Retry</button>
                    </div>

                    <div x-show="!settingsLoading && !settingsError" class="settings-form">
                        <form @submit.prevent="saveSettings">
                            <div class="form-group">
                                <label for="alias">Alias</label>
                                <input type="text" id="alias" x-model="settings.alias" class="form-control">
                            </div>

                            <div class="form-group">
                                <label for="defaultSymbol">Default Symbol</label>
                                <input type="text" id="defaultSymbol" x-model="settings.defaultSymbol" class="form-control">
                            </div>

                            <div class="form-group" x-show="selectedAgentRole === 'COPIER'">
                                <label for="copyFromId">Copy From ID</label>
                                <input type="text" id="copyFromId" x-model="settings.copyFromId" class="form-control">
                            </div>

                            <div class="form-group" x-show="selectedAgentRole === 'COPIER'">
                                <label for="copyPercentage">Copy Percentage</label>
                                <input type="number" id="copyPercentage" x-model="settings.copyPercentage" class="form-control" min="0" max="100">
                            </div>

                            <div class="form-group">
                                <label for="minMargin">Minimum Margin</label>
                                <input type="number" id="minMargin" x-model="settings.minMargin" class="form-control" step="0.01">
                            </div>

                            <div class="form-group" x-show="selectedAgentRole === 'MASTER'">
                                <label for="defaultSize">Default Size</label>
                                <input type="number" id="defaultSize" x-model="settings.defaultSize" class="form-control" min="0" step="0.001">
                            </div>

                            <div class="form-group" x-show="selectedAgentRole === 'MASTER'">
                                <label for="longRatio">Long Ratio</label>
                                <input type="number" id="longRatio" x-model="settings.longRatio" class="form-control" min="0" max="1" step="0.01">
                                <small class="form-text text-muted">Probability of opening long positions (0-1)</small>
                            </div>

                            <div class="form-group" x-show="selectedAgentRole === 'MASTER'">
                                <label for="coolingHour">Cooling Hour</label>
                                <input type="number" id="coolingHour" x-model="settings.coolingHour" class="form-control" min="-0.5" max="48.0" step="0.01">
                                <small class="form-text text-muted">For exact hour mode: -0.5 to 0, For interval mode: 0.01 to 48.0</small>
                            </div>

                            <div class="form-group">
                                <label for="refreshInterval">Refresh Interval (minutes)</label>
                                <input type="number" id="refreshInterval" x-model="settings.refreshInterval" class="form-control" min="1">
                            </div>

                            <div class="form-group" x-show="selectedAgentRole === 'MASTER'">
                                <label for="stopLossBuffer">Stop Loss Buffer</label>
                                <input type="number" id="stopLossBuffer" x-model="settings.stopLossBuffer" class="form-control" min="0" max="0.05" step="0.0001">
                                <small class="form-text text-muted">Buffer from liquidation price (0-0.05)</small>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" x-model="settings.debug">
                                    <span>Debug Mode</span>
                                </label>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" x-model="settings.manualMode">
                                    <span>Manual Mode</span>
                                </label>
                            </div>

                            <div class="form-actions">
                                <button type="button" @click="showSettings = false" class="cancel-button">Cancel</button>
                                <button type="submit" class="save-button">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Project Settings Comparison Dialog -->
            <div 
                x-data="projectSettingsDialog"
                x-ref="projectSettingsDialog"
                x-show="showProjectSettings" 
                class="dialog-overlay"
                @click.away="showProjectSettings = false"
                @show-project-settings.window="fetchProjectSettings($event.detail.projectId)"
                x-cloak
            >
                <div class="dialog-content" style="max-width: 1200px;">
                    <div class="dialog-header">
                        <h3 x-text="'[' + selectedProject + '] Settings'"></h3>
                        <button @click="showProjectSettings = false" class="close-button">&times;</button>
                    </div>
                    
                    <div x-show="projectSettingsLoading" class="loading-container">
                        <div class="loading-spinner"></div>
                        <p>Loading project settings...</p>
                    </div>

                    <div x-show="!projectSettingsLoading && projectSettingsError" class="error-container">
                        <div class="error-icon">⚠️</div>
                        <p x-text="projectSettingsError"></p>
                        <button @click="fetchProjectSettings(selectedProject)" class="retry-button">Retry</button>
                    </div>

                    <div x-show="!projectSettingsLoading && !projectSettingsError" class="settings-comparison">
                        <div class="settings-column">
                            <h4>Master Agent Settings</h4>
                            <template x-if="masterSettings">
                                <div class="settings-details">
                                    <div class="detail-row">
                                        <span class="detail-label">Version</span>
                                        <span class="detail-value" x-text="masterSettings.version || 'N/A'"></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Default Symbol</span>
                                        <span class="detail-value" x-text="masterSettings.defaultSymbol || 'N/A'"></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Default Size</span>
                                        <span class="detail-value" x-text="masterSettings.defaultSize || '0'"></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Long Ratio</span>
                                        <span class="detail-value" x-text="masterSettings.longRatio || '0.5'"></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Min Margin</span>
                                        <span class="detail-value" x-text="masterSettings.minMargin || '0'"></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Cooling Hour</span>
                                        <span class="detail-value" x-text="masterSettings.coolingHour || '0'"></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Refresh Interval</span>
                                        <span class="detail-value" x-text="masterSettings.refreshInterval || '0'"></span>
                                    </div>
                                </div>
                            </template>
                        </div>

                        <div class="settings-column">
                            <h4>Copier Agent Settings</h4>
                            <template x-if="copierSettings">
                                <div class="settings-details">
                                    <div class="detail-row">
                                        <span class="detail-label">Version</span>
                                        <span class="detail-value" x-text="copierSettings.version || 'N/A'"></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Default Symbol</span>
                                        <span class="detail-value" x-text="copierSettings.defaultSymbol || 'N/A'"></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Copy From</span>
                                        <span class="detail-value eth-address" 
                                            x-text="copierSettings.copyFromId ? copierSettings.copyFromId.slice(0, 6) + '...' + copierSettings.copyFromId.slice(-4) : 'N/A'"
                                            :title="copierSettings.copyFromId"
                                            @click="copyToClipboard(copierSettings.copyFromId)"
                                        ></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Copy Percentage</span>
                                        <span class="detail-value" x-text="copierSettings.copyPercentage + '%' || '0%'"></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Min Margin</span>
                                        <span class="detail-value" x-text="copierSettings.minMargin || '0'"></span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">Refresh Interval</span>
                                        <span class="detail-value" x-text="copierSettings.refreshInterval || '0'"></span>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rename Project Dialog -->
            <div 
                x-data="renameDialog"
                x-ref="renameDialog"
                x-show="showRenameDialog" 
                class="rename-dialog"
                @click.away="showRenameDialog = false"
                x-on:show-rename-dialog.window="openDialog($event.detail.projectId)"
                x-cloak
            >
                <div class="rename-dialog-content">
                    <div class="rename-dialog-header">
                        <h3>Rename Project</h3>
                        <button @click="showRenameDialog = false" class="rename-dialog-close">&times;</button>
                    </div>
                    <form @submit.prevent="renameProject" class="rename-dialog-form">
                        <div class="form-group">
                            <label for="newProjectId">New Project ID</label>
                            <input 
                                type="text" 
                                id="newProjectId" 
                                x-model="newProjectId" 
                                required
                                pattern="[a-zA-Z0-9_-]+"
                                title="Only letters, numbers, underscores and hyphens are allowed"
                            >
                        </div>
                        <div x-show="error" class="error-message" x-text="error"></div>
                        <div class="rename-dialog-actions">
                            <button type="button" @click="showRenameDialog = false" class="rename-dialog-cancel">Cancel</button>
                            <button type="submit" class="rename-dialog-submit" :disabled="!newProjectId || newProjectId === selectedProject">Rename</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- External Positions Dialog -->
            <div 
                x-data="externalPositionsDialog"
                x-ref="externalPositionsDialog"
                x-show="showExternalPositions" 
                class="dialog-overlay"
                @click.away="showExternalPositions = false"
                @show-external-positions.window="showExternalPositions = true"
                x-cloak
            >
                <div class="dialog-content" style="max-width: 1200px;">
                    <div class="dialog-header">
                        <h3>External Positions</h3>
                        <button @click="showExternalPositions = false" class="close-button">&times;</button>
                    </div>
                    
                    <div x-show="externalPositionsLoading" class="loading-container">
                        <div class="loading-spinner"></div>
                        <p>Loading external positions...</p>
                    </div>

                    <div x-show="!externalPositionsLoading && externalPositionsError" class="error-container">
                        <div class="error-icon">⚠️</div>
                        <p x-text="externalPositionsError"></p>
                        <button @click="fetchExternalPositions" class="retry-button">Retry</button>
                    </div>

                    <div x-show="!externalPositionsLoading && !externalPositionsError" class="external-positions-content">
                        <table class="external-positions-table">
                            <thead>
                                <tr>
                                    <th>Project</th>
                                    <th>Master Position</th>
                                    <th>Copier Position</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template x-for="project in projects.filter(p => p.statusComment === 'idle')" :key="project.projectId">
                                    <tr>
                                        <td x-text="project.projectId"></td>
                                        <td>
                                            <template x-if="project.externalSourcePositions && project.externalSourcePositions.some(pos => pos.userId === project.agents.find(a => a.role === 'master')?.userId)">
                                                <span class="position-exists">Yes</span>
                                            </template>
                                            <template x-if="!project.externalSourcePositions || !project.externalSourcePositions.some(pos => pos.userId === project.agents.find(a => a.role === 'master')?.userId)">
                                                <span class="position-missing stoploss-text">StopLoss</span>
                                            </template>
                                        </td>
                                        <td>
                                            <template x-if="project.externalSourcePositions && project.externalSourcePositions.some(pos => pos.userId === project.agents.find(a => a.role === 'copier')?.userId)">
                                                <span class="position-exists">Yes</span>
                                            </template>
                                            <template x-if="!project.externalSourcePositions || !project.externalSourcePositions.some(pos => pos.userId === project.agents.find(a => a.role === 'copier')?.userId)">
                                                <span class="position-missing stoploss-text">StopLoss</span>
                                            </template>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <style>
                /* Remove the rename dialog styles since they are in styles.css */
            </style>

            <footer>
                <p>&copy; 2024 Copier Trader | <span id="current-time"></span></p>
            </footer>
        </div>

        <!-- Load app.js after Alpine.js -->
        <script src="/static/js/app.js"></script>

        <script>
            function updateTime() {
                const timeElement = document.getElementById("current-time");
                if (timeElement) {
                    const now = new Date();
                    timeElement.textContent = now.toLocaleString();
                }
            }

            // Update time every second
            setInterval(updateTime, 1000);
            updateTime();
        </script>

        <!-- Global Options Dialog -->
        <div 
            x-data="globalOptionsDialog"
            x-ref="globalOptionsDialog"
            x-show="showGlobalOptions" 
            class="dialog-overlay"
            @click.away="showGlobalOptions = false"
            @show-global-options.window="fetchGlobalOptions"
            x-cloak
        >
            <div class="dialog-content" style="max-width: 800px;">
                <div class="dialog-header">
                    <h3>Global Options</h3>
                    <button @click="showGlobalOptions = false" class="close-button">&times;</button>
                </div>
                
                <div x-show="globalOptionsLoading" class="loading-container">
                    <div class="loading-spinner"></div>
                    <p>Loading options...</p>
                </div>

                <div x-show="!globalOptionsLoading && globalOptionsError" class="error-container">
                    <div class="error-icon">⚠️</div>
                    <p x-text="globalOptionsError"></p>
                    <button @click="fetchGlobalOptions" class="retry-button">Retry</button>
                </div>

                <div x-show="!globalOptionsLoading && !globalOptionsError" class="settings-form">
                    <form @submit.prevent="saveGlobalOptions">

                        <div class="form-group">
                            <label for="id">ID</label>
                            <input type="text" id="id" x-model="options.id" class="form-control">
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" x-model="options.debug">
                                <span>Debug Mode</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" x-model="options.activateChrome">
                                <span>Activate Chrome for Agent Not Syncing</span>
                            </label>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" x-model="options.enableTradeAlert">
                                <span>Enable Trade Alert</span>
                            </label>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" x-model="options.enableBalancer">
                                <span>Enable Balancer</span>
                            </label>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" x-model="options.enableStoploss">
                                <span>Enable Stoploss</span>
                            </label>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" x-model="options.enableAutoStop">
                                <span>Enable Auto Stop</span>
                            </label>
                        </div>

                        <div class="form-group">
                            <label for="autoStopVolume">Auto Stop Volume</label>
                            <input type="number" id="autoStopVolume" x-model="options.autoStopVolume" class="form-control" step="0.01">
                        </div>

                        <div class="form-group">
                            <label for="autoStopDailyVolume">Auto Stop Daily Volume</label>
                            <input type="number" id="autoStopDailyVolume" x-model="options.autoStopDailyVolume" class="form-control" step="0.01">
                        </div>

                        <div class="form-group">
                            <label for="extensionDir">Extension Directory</label>
                            <input type="text" id="extensionDir" x-model="options.extensionDir" class="form-control">
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" x-model="options.enableAutomation">
                                <span>Enable Automation</span>
                            </label>
                        </div>

                        <div class="form-group" x-show="options.enableAutomation">
                            <label for="automationProjectGroups">Automation Project Groups</label>
                            <input type="text" id="automationProjectGroups" x-model="options.automationProjectGroups" class="form-control" placeholder="Enter comma-separated project groups">
                            <small class="form-text text-muted">Comma-separated list of project groups for automation</small>
                        </div>

                        <div class="form-group" x-show="options.enableAutomation">
                            <label for="automationBatchTimeout">Batch Timeout Hours (0-12)</label>
                            <input type="number" id="automationBatchTimeout" x-model.number="options.automationBatchTimeout" min="0" max="12" step="0.1" class="form-control"
                                   @input="options.automationBatchTimeout = parseFloat(parseFloat($event.target.value).toFixed(1))">
                        </div>

                        <div class="form-group" x-show="options.enableAutomation">
                            <label for="automationBatchWait">Batch Wait (minutes)</label>
                            <input type="number" id="automationBatchWait" x-model.number="options.automationBatchWait" min="0" step="1" class="form-control">
                        </div>

                        <div class="form-group" x-show="options.enableAutomation">
                            <label for="automationBatchLimit">Batch Limit</label>
                            <input type="number" id="automationBatchLimit" x-model.number="options.automationBatchLimit" min="0" step="1" class="form-control">
                        </div>

                        <div class="form-group" x-show="options.enableAutomation">
                            <label class="checkbox-label">
                                <input type="checkbox" x-model="options.automationBatchRandom">
                                <span>Randomize Batch</span>
                            </label>
                        </div>

                        <div class="form-group" x-show="options.enableAutomation">
                            <label for="automationDailyStartTime">Daily Start Time (HH:MM)</label>
                            <input type="text" id="automationDailyStartTime" x-model="options.automationDailyStartTime" class="form-control" placeholder="e.g., 08:30">
                            <small class="form-text text-muted">Set a specific time for automation to start daily. Leave empty to disable.</small>
                        </div>

                        <div class="form-actions">
                            <button type="button" @click="showGlobalOptions = false" class="cancel-button">Cancel</button>
                            <button type="submit" class="save-button">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Batch Settings Dialog -->
        <div 
            x-data="batchSettingsDialog"
            x-ref="batchSettingsDialog"
            x-show="showBatchSettings" 
            class="dialog-overlay"
            @click.away="showBatchSettings = false"
            @show-batch-settings.window="openDialog($event.detail.projectIds)"
            x-cloak
        >
            <div class="dialog-content" style="max-width: 800px;">
                <div class="dialog-header">
                    <h3>Batch Update Settings</h3>
                    <button @click="showBatchSettings = false" class="close-button">&times;</button>
                </div>
                
                <div class="settings-form">
                    <div class="batch-settings-filtered-projects">
                        <h4>Selected Projects</h4>
                        <div class="batch-settings-project-ids-list">
                            <template x-for="projectId in filteredProjects" :key="projectId">
                                <span class="batch-settings-project-id-tag" x-text="projectId"></span>
                            </template>
                        </div>
                    </div>

                    <form @submit.prevent="saveBatchSettings" class="batch-settings-form">
                        <div class="form-group">
                            <label for="defaultSymbol">Default Symbol</label>
                            <input type="text" id="defaultSymbol" x-model="settings.defaultSymbol" class="form-control">
                            <small class="form-text text-muted">Leave empty to keep current value</small>
                        </div>

                        <div class="form-group">
                            <label for="defaultSize">Default Size</label>
                            <input type="number" id="defaultSize" x-model="settings.defaultSize" class="form-control" min="0" step="0.001">
                            <small class="form-text text-muted">Leave empty to keep current value</small>
                        </div>

                        <div class="form-group">
                            <label for="minMargin">Minimum Margin</label>
                            <input type="number" id="minMargin" x-model="settings.minMargin" class="form-control" step="0.01">
                            <small class="form-text text-muted">Leave empty to keep current value</small>
                        </div>

                        <div class="form-group">
                            <label for="coolingHour">Cooling Hour</label>
                            <input type="number" id="coolingHour" x-model="settings.coolingHour" class="form-control" min="-0.5" max="48.0" step="0.01">
                            <small class="form-text text-muted">Leave empty to keep current value</small>
                        </div>

                        <div class="form-group">
                            <label for="stopLossBuffer">Stop Loss Buffer</label>
                            <input type="number" id="stopLossBuffer" x-model="settings.stopLossBuffer" class="form-control" min="0" max="0.05" step="0.0001">
                            <small class="form-text text-muted">Leave empty to keep current value</small>
                        </div>

                        <div class="form-group">
                            <label for="refreshInterval">Refresh Interval (minutes)</label>
                            <input type="number" id="refreshInterval" x-model="settings.refreshInterval" class="form-control" min="1">
                            <small class="form-text text-muted">Leave empty to keep current value</small>
                        </div>

                        <div class="form-group">
                            <label for="copyPercentage">Copy Percentage</label>
                            <input type="number" id="copyPercentage" x-model="settings.copyPercentage" class="form-control" min="0" max="100">
                            <small class="form-text text-muted">Leave empty to keep current value</small>
                        </div>

                        <div class="form-actions">
                            <button type="button" @click="showBatchSettings = false" class="cancel-button">Cancel</button>
                            <button type="submit" class="save-button">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </body>
</html>

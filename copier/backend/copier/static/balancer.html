<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title x-text="options.id ? options.id + ' balancer' : 'balancer'">balancer</title>
    <script defer src="/static/js/alpine_3.x.x.js"></script>
    <link rel="stylesheet" href="/static/css/balancer_styles.css">
</head>
<body>
    <div x-data="balancerApp()" class="container">
        <!-- Version Info -->
        <div x-show="serverVersion" class="version-info" style="position: fixed; bottom: 10px; left: 10px; z-index: 1000; background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px;">
            <span x-text="'Server: ' + serverVersion"></span>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-item" :class="{ 'active': currentView === 'make-plans' }" @click="showMakePlans()">Make Plans</div>
            <div class="plan-list">
                <template x-for="plan in plans" :key="plan.refId">
                    <div class="sidebar-plan-item" :class="{ 'done': plan.status === 'done', 'canceled': plan.cancelTime != null, 'selected': currentPlan && currentPlan.refId === plan.refId }">
                        <div class="plan-header" @click="showPlanDetail(plan.refId)">
                            <div class="sidebar-plan-info">
                                <div class="plan-label">
                                    <span x-text="plan.refId"></span>
                                    <span x-html="getPlanStatusBadge(plan)"></span>
                                </div>
                                <div class="plan-meta">
                                    <span x-text="formatDate(plan.createTime)"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content">
            <!-- Make Plans Page -->
            <div x-show="currentView === 'make-plans'" class="make-plans">
                <h2>Make Plans</h2>
                <div class="form-group">
                    <label>Platform:</label>
                    <select x-model="selectedPlatform">
                        <template x-for="platform in platforms" :key="platform">
                            <option :value="platform" x-text="platform"></option>
                        </template>
                    </select>
                </div>
                <div class="form-group">
                    <label>Exchange:</label>
                    <select x-model="selectedExchangeId">
                        <option value="">No Exchange Selected</option>
                        <template x-for="exchangeId in exchangeIds" :key="exchangeId">
                            <option :value="exchangeId" x-text="exchangeId"></option>
                        </template>
                    </select>
                </div>
                <div class="form-group">
                    <label>Wallet:</label>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <select x-model="selectedWalletId" style="flex: 1;">
                            <option value="">No Wallet Selected</option>
                            <template x-for="wallet in wallets" :key="wallet.name">
                                <option :value="wallet.name" x-text="wallet.name"></option>
                            </template>
                        </select>
                        <button @click="showWalletNote()" x-show="selectedWalletId" class="balancer-note-btn">Note</button>
                        <button @click="showDepositAddress()" x-show="selectedWalletId" class="balancer-note-btn">Deposit Address</button>
                    </div>
                </div>
                <div class="form-group">
                    <label>Stage:</label>
                    <select x-model="selectedStage">
                        <option value="" x-show="!selectedWalletId">Automatic Mode</option>
                        <option value="platform_withdraw" x-show="selectedWalletId">Platform Withdraw</option>
                        <option value="exchange_deposit" x-show="selectedWalletId">Exchange Deposit</option>
                        <option value="exchange_withdraw" x-show="selectedWalletId">Exchange Withdraw</option>
                        <option value="platform_deposit" x-show="selectedWalletId">Platform Deposit</option>
                        <option value="platform_withdraw,exchange_deposit" x-show="selectedWalletId">Platform Withdraw + Exchange Deposit</option>
                        <option value="exchange_withdraw,platform_deposit" x-show="selectedWalletId">Exchange Withdraw + Platform Deposit</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Coin:</label>
                    <select x-model="selectedCoin">
                        <template x-for="coin in getSupportedCoins()" :key="coin">
                            <option :value="coin" x-text="coin"></option>
                        </template>
                    </select>
                </div>
                <div class="form-group">
                    <label>Amount:</label>
                    <div style="display: flex; align-items: center; gap: 24px;">
                        <div x-show="selectedWalletId" style="display: flex; align-items: center; gap: 6px; width: auto; flex: none;">
                            <select x-model="amountDirective">
                                <option value="exact">Exact</option>
                                <option value="target">Target = </option>
                            </select>
                        </div>
                        <input type="number" x-model="amount" step="0.000001" style="width: 200px;">
                        <div x-show="selectedWalletId" style="display: flex; align-items: center; gap: 6px; width: auto; flex: none;">
                            <input type="checkbox" id="amountRandom" x-model="amountRandom" style="margin: 0;">
                            <label for="amountRandom" style="margin: 0;">Random Amount(+20% max)</label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label>Wait (minutes):</label>
                    <input type="number" x-model="waitMinutes" min="0" max="60" step="1" style="width: 200px;">
                </div>
                <div x-show="!selectedWalletId">
                    <div class="grid-filter-controls">
                        <input type="text" x-model="projectFilter" placeholder="Filter projects..." class="filter-input">
                        <div class="filter-buttons">
                            <button @click="selectFilteredProjects()" class="filter-btn">Select Filtered</button>
                            <button @click="resetProjectSelection()" class="filter-btn reset-btn">Reset</button>
                        </div>
                    </div>
                    <div class="projects-grid">
                        <template x-for="project in projects" :key="project">
                            <div class="project-checkbox" :class="{ 'filtered': projectFilter && project.toLowerCase().startsWith(projectFilter.toLowerCase()) }">
                                <input type="checkbox" :id="project" x-model="selectedProjects" :value="project">
                                <label :for="project" x-text="project"></label>
                            </div>
                        </template>
                    </div>
                </div>
                <div x-show="selectedWalletId">
                    <div class="grid-filter-controls">
                        <input type="text" x-model="addressFilter" placeholder="Filter addresses..." class="filter-input">
                        <div class="filter-buttons">
                            <button @click="selectFilteredAddresses()" class="filter-btn">Select Filtered</button>
                            <button @click="resetAddressSelection()" class="filter-btn reset-btn">Reset</button>
                        </div>
                    </div>
                    <div class="addresses-grid">
                        <template x-for="address in getWalletAddresses()" :key="address">
                            <div class="address-checkbox" :class="{ 'filtered': addressFilter && (getWalletAddressAlias(address) && getWalletAddressAlias(address).toLowerCase().startsWith(addressFilter.toLowerCase()) || shortenAddress(address).toLowerCase().startsWith(addressFilter.toLowerCase()) || address.toLowerCase().startsWith(addressFilter.toLowerCase())) }">
                                <input type="checkbox" :id="address" x-model="selectedAddresses" :value="address">
                                <label :for="address">
                                    <span x-text="getWalletAddressAlias(address)"></span>
                                    <span class="address-short" x-text="'(' + shortenAddress(address) + ')'"></span>
                                </label>
                            </div>
                        </template>
                    </div>
                </div>
                <div class="make-plan-actions">
                    <button @click="makePlan()" :disabled="!canMakePlan">Make Plan</button>
                </div>
            </div>

            <!-- Plan Detail Page -->
            <div x-show="currentView === 'plan-detail'" class="plan-detail">
                <h2>Plan Detail</h2>
                <template x-if="currentPlan">
                    <div>
                        <div class="plan-info">
                            <div class="info-row">
                                <span>RefID:</span>
                                <span x-text="currentPlan.refId"></span>
                            </div>
                            <div class="info-row">
                                <span>Exchange:</span>
                                <span x-text="currentPlan.exchangeID + ' / ' + currentPlan.exchangeName"></span>
                            </div>
                            <div class="info-row">
                                <span>Status:</span>
                                <span x-text="currentPlan.status"></span>
                            </div>
                            <div class="info-row" x-show="currentPlan.stages && currentPlan.stages.length">
                                <span>Stages:</span>
                                <span x-text="currentPlan.stages.join(', ')"></span>
                            </div>
                            <div class="info-row">
                                <span>Projects:</span>
                                <span x-html="formatProjectIDs(currentPlan)" class="project-ids"></span>
                            </div>
                            <div class="info-row">
                                <span>Created:</span>
                                <span x-text="formatDate(currentPlan.createTime)"></span>
                            </div>
                        </div>
                        <div class="plan-actions">
                            <button x-show="currentPlan.status === 'prepared' && currentPlan.status !== 'done' && !currentPlan.cancelTime" @click="processPlan()">Process</button>
                            <button x-show="currentPlan.status !== 'done' && !currentPlan.cancelTime" @click="cancelPlan()" class="cancel-btn">Cancel</button>
                            <button @click="deletePlan()" class="delete-btn">Delete</button>
                        </div>
                        <div class="plan-items-summary">
                            <h3>Items Summary</h3>
                            <div class="summary-list">
                                <template x-for="item in currentPlan.balanceItems" :key="item.refId">
                                    <div class="summary-item" @click="scrollToItem(item.refId)" style="cursor: pointer;">
                                        <span class="status-dot" :class="{
                                            'error': item.statusString.endsWith('Error'),
                                            'done': item.statusString === 'BalancePlanItemStatusDone',
                                            'pending': !item.statusString.endsWith('Error') && item.statusString !== 'BalancePlanItemStatusDone'
                                        }"></span>
                                        <span class="from-info">
                                            <span class="alias" x-text="item.fromAddressAlias"></span>
                                            <span class="amount" x-text="'(' + item.fromAddressOriginalAmount + ')'"></span>
                                        </span>
                                        <span class="arrow">→</span>
                                        <span class="to-info">
                                            <span class="alias" x-text="item.toAddressAlias"></span>
                                            <span class="amount" x-text="'(' + item.toAddressOriginalAmount + ')'"></span>
                                            <span class="separator">|</span>
                                            <span class="amount" x-text="item.balanceAmount"></span>
                                        </span>
                                    </div>
                                </template>
                            </div>
                        </div>
                        <div class="plan-items">
                            <template x-for="item in currentPlan.balanceItems" :key="item.refId">
                                <div class="plan-detail-item" :id="'item-' + item.refId">
                                    <div class="plan-item-header">
                                        <div class="item-info">
                                            <div class="info-row item-ref item-ref-flex">
                                                <div class="item-id-group">
                                                    <span class="ref-id xlarge" x-text="item.refId"></span>
                                                </div>
                                                <button x-show="item.statusString !== 'BalancePlanItemStatusDone'" @click="cancelItem(item.refId)" class="item-cancel-btn cancel-btn-margin">Cancel</button>
                                            </div>
                                            <div class="info-row">
                                                <span>Project ID:</span>
                                                <span x-text="item.projectId"></span>
                                            </div>
                                            <div class="info-row">
                                                <span>Balance Amount:</span>
                                                <span x-text="item.balanceAmount"></span>
                                            </div>
                                            <div class="info-row">
                                                <span>From:</span>
                                                <span><span class="alias" x-text="item.fromAddressAlias"></span> (<span x-text="item.fromAddress"></span>)</span>
                                            </div>
                                            <div class="info-row">
                                                <span>From Balance:</span>
                                                <span x-text="item.fromAddressOriginalAmount + ' ' + currentPlan.coin + ' -> ' + (item.fromAddressOriginalAmount - item.balanceAmount) + ' ' + currentPlan.coin + ', current: ' + item.fromAddressCurrentAmount + ' ' + currentPlan.coin"></span>
                                            </div>
                                            <div class="info-row">
                                                <span>To:</span>
                                                <span><span class="alias" x-text="item.toAddressAlias"></span> (<span x-text="item.toAddress"></span>)</span>
                                            </div>
                                            <div class="info-row">
                                                <span>To Balance:</span>
                                                <span x-text="item.toAddressOriginalAmount + ' ' + currentPlan.coin + ' -> ' + (item.toAddressOriginalAmount + item.balanceAmount) + ' ' + currentPlan.coin + ', current: ' + item.toAddressCurrentAmount + ' ' + currentPlan.coin"></span>
                                            </div>
                                            <div class="info-row" x-show="item.intermediateAddress">
                                                <span>Intermediate:</span>
                                                <span x-text="item.intermediateAddress"></span>
                                            </div>
                                            <div class="info-row">
                                                <span>Status:</span>
                                                <span x-text="item.statusString"></span>
                                            </div>
                                            <div class="info-row" x-show="item.statusComment">
                                                <span>Comment:</span>
                                                <span x-text="item.statusComment"></span>
                                            </div>
                                            <div class="info-row">
                                                <span>Created:</span>
                                                <span x-text="formatDate(item.createTime)"></span>
                                            </div>
                                            <div class="info-row" x-show="item.updateTime">
                                                <span>Updated:</span>
                                                <span x-text="formatDate(item.updateTime)"></span>
                                            </div>
                                            <div class="info-row" x-show="item.cancelTime">
                                                <span>Canceled:</span>
                                                <span x-text="formatDate(item.cancelTime)"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="plan-item-transactions">
                                        <template x-for="tx in getTxArray(item)" :key="tx.id">
                                            <div class="transaction" :class="tx.status || 'pending'">
                                                <div class="tx-info">
                                                    <span x-text="tx.type"></span>
                                                    <span x-text="tx.status || 'pending'" class="tx-status"></span>
                                                </div>
                                                <div class="tx-details">
                                                    <div class="info-row">
                                                        <span>ID:</span>
                                                        <span x-text="tx.id" class="tx-id"></span>
                                                    </div>
                                                    <div class="info-row">
                                                        <span>From:</span>
                                                        <span>
                                                            <span x-text="tx.fromAddress"></span>
                                                            <span x-show="tx.fromAddressAlias" class="alias-badge" x-text="'[' + tx.fromAddressAlias + ']'"></span>
                                                        </span>
                                                    </div>
                                                    <div class="info-row">
                                                        <span>To:</span>
                                                        <span>
                                                            <span x-text="tx.toAddress"></span>
                                                            <span x-show="tx.toAddressAlias" class="alias-badge" x-text="'[' + tx.toAddressAlias + ']'"></span>
                                                        </span>
                                                    </div>
                                                    <div class="info-row">
                                                        <span>Chain:</span>
                                                        <span x-text="tx.chain || currentPlan.chainName"></span>
                                                    </div>
                                                    <div class="info-row">
                                                        <span>Amount:</span>
                                                        <span x-text="tx.amount + ' ' + tx.coin"></span>
                                                    </div>
                                                    <div class="info-row" x-show="tx.wait">
                                                        <span>Wait:</span>
                                                        <span x-text="tx.wait + ' seconds'"></span>
                                                    </div>
                                                    <div class="info-row" x-show="tx.txHash">
                                                        <span>Tx Hash:</span>
                                                        <span class="tx-hash-copy"
                                                              @click="$clipboard(tx.txHash)"
                                                              :title="tx.txHash"
                                                              style="cursor:pointer; color:#3498db; text-decoration:underline;">
                                                            <template x-if="tx.txHash.length > 12">
                                                                <span x-text="tx.txHash.slice(0,6) + '...' + tx.txHash.slice(-4)"></span>
                                                            </template>
                                                            <template x-if="tx.txHash.length <= 12">
                                                                <span x-text="tx.txHash"></span>
                                                            </template>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="tx-actions">
                                                    <button @click="retryTx(tx.id)">Retry</button>
                                                    <button @click="markTxDone(tx.id)">Mark Done</button>
                                                    <button @click="cancelTx(tx.id)">Cancel</button>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- Wallet Note Dialog -->
        <template x-if="showNoteDialog">
            <div class="balancer-dialog-overlay" @click.self="closeWalletNote()">
                <div class="balancer-dialog-content">
                    <div class="balancer-dialog-header">
                        <h3>Wallet Note</h3>
                        <button @click="closeWalletNote()" class="balancer-close-btn">&times;</button>
                    </div>
                    <div class="balancer-dialog-body">
                        <textarea x-model="walletNote" rows="10" style="width: 100%; resize: vertical;" readonly></textarea>
                    </div>
                </div>
            </div>
        </template>

        <!-- Deposit Address Dialog -->
        <template x-if="showDepositAddressDialog">
            <div class="balancer-dialog-overlay" @click.self="closeDepositAddress()">
                <div class="balancer-dialog-content">
                    <div class="balancer-dialog-header">
                        <h3>Deposit Address</h3>
                        <button @click="closeDepositAddress()" class="balancer-close-btn">&times;</button>
                    </div>
                    <div class="balancer-dialog-body">
                        <textarea x-model="depositAddress" rows="10" style="width: 100%; resize: vertical;" readonly></textarea>
                    </div>
                </div>
            </div>
        </template>
    </div>
    <script src="/static/js/balancer_app.js"></script>
</body>
</html>

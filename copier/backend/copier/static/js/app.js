document.addEventListener('alpine:init', () => {
    console.log('Initializing Alpine.js app...');
    
    // Add copy to clipboard function
    window.copyToClipboard = function(text) {
        navigator.clipboard.writeText(text).catch(err => {
            console.error('Failed to copy text: ', err);
        });
    };
    
    // Position Changes Dialog Component
    Alpine.data('positionChangesDialog', () => ({
        positionChanges: [],
        showPositionChanges: false,
        masterPositionChanges: [],
        copierPositionChanges: [],
        positionChangesLoading: false,
        positionChangesError: null,
        selectedProject: null,
        selectedAgent: null,
        showMasterAndCopier: false,
        masterAgentInfo: null,
        copierAgentInfo: null,
        singleAgentInfo: null,

        init() {
            console.log('Position Changes Dialog initialized');
        },

        async fetchPositionChanges(projectId, userId, showMasterAndCopier = false) {
            this.showPositionChanges = true;
            this.positionChangesLoading = true;
            this.positionChangesError = null;
            this.selectedProject = projectId;
            this.selectedAgent = userId;
            this.showMasterAndCopier = showMasterAndCopier;
            this.singleAgentInfo = null;

            // Toggle narrow class based on showMasterAndCopier
            const dialogContent = document.querySelector('.dialog-content');
            if (dialogContent) {
                if (!showMasterAndCopier) {
                    dialogContent.classList.add('narrow');
                } else {
                    dialogContent.classList.remove('narrow');
                }
            }

            try {
                if (showMasterAndCopier) {
                    // Get the project from the projects array
                    const project = this.projects.find(p => p.projectId === projectId);
                    if (!project) {
                        throw new Error('Project not found');
                    }

                    // Get master and copier agents
                    const masterAgent = project.agents.find(a => a.role === 'master');
                    const copierAgent = project.agents.find(a => a.role === 'copier');

                    if (!masterAgent) {
                        throw new Error('Master agent not found');
                    }

                    // Store agent info for display
                    this.masterAgentInfo = {
                        alias: masterAgent.settings?.alias || '',
                        address: masterAgent.userId ? masterAgent.userId.slice(0, 6) + '...' + masterAgent.userId.slice(-4) : ''
                    };
                    this.copierAgentInfo = copierAgent ? {
                        alias: copierAgent.settings?.alias || '',
                        address: copierAgent.userId ? copierAgent.userId.slice(0, 6) + '...' + copierAgent.userId.slice(-4) : ''
                    } : null;

                    // Fetch position changes for both agents
                    const [masterChanges, copierChanges] = await Promise.all([
                        this.fetchAgentPositionChanges(projectId, masterAgent.userId),
                        copierAgent ? this.fetchAgentPositionChanges(projectId, copierAgent.userId) : Promise.resolve([])
                    ]);

                    this.masterPositionChanges = masterChanges;
                    this.copierPositionChanges = copierChanges;
                    console.log('Master position changes:', this.masterPositionChanges);
                    console.log('Copier position changes:', this.copierPositionChanges);
                } else {
                    // Get the project and agent info for single agent view
                    const project = this.projects.find(p => p.projectId === projectId);
                    if (project) {
                        const agent = project.agents.find(a => a.userId === userId);
                        if (agent) {
                            this.singleAgentInfo = {
                                role: agent.role,
                                alias: agent.settings?.alias || '',
                                address: agent.userId ? agent.userId.slice(0, 6) + '...' + agent.userId.slice(-4) : ''
                            };
                        }
                    }
                    this.positionChanges = await this.fetchAgentPositionChanges(projectId, userId);
                }
            } catch (error) {
                console.error('Error fetching position changes:', error);
                this.positionChangesError = error.message;
            } finally {
                this.positionChangesLoading = false;
            }
        },

        async fetchAgentPositionChanges(projectId, userId) {
            console.log('Fetching position changes for:', projectId, userId);
            const response = await fetch(`/changes/${projectId}?user_id=${userId}&limit=100`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            console.log('Received position changes:', data);
            return data.changes || [];  // Return the changes array from the response
        },

        formatPositionChange(change) {
            if (!change) return '';
            const date = new Date(change.createTime);
            const timeStr = date.toLocaleString('sv-SE', { 
                timeZone: 'Asia/Singapore',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }).replace('.', ' ');
            const fullTimeStr = date.toLocaleString('sv-SE', { 
                timeZone: 'Asia/Singapore',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }).replace('.', ' ');
            const side = change.side.toUpperCase();
            const sideClass = change.side.toLowerCase();
            
            return `
                <div class="position-change-item ${sideClass}">
                    <span class="position-change-time" title="${fullTimeStr}">${timeStr}</span>
                    <span class="position-change-symbol">${change.symbol}</span>
                    <span class="position-change-side ${sideClass}">${side}</span>
                    <span class="position-change-size ${sideClass}">${parseFloat(change.size).toFixed(5)}</span>
                </div>
            `;
        }
    }));

    // External Positions Dialog Component
    Alpine.data('externalPositionsDialog', () => ({
        showExternalPositions: false,
        externalPositionsLoading: false,
        externalPositionsError: null,
        externalPositions: [],

        init() {
            console.log('External Positions Dialog initialized');
        },

        async fetchExternalPositions() {
            this.externalPositionsLoading = true;
            this.externalPositionsError = null;

            try {
                // This will be implemented later when you provide the data structure
                // const response = await fetch('/external-positions');
                // if (!response.ok) {
                //     throw new Error(`HTTP error! status: ${response.status}`);
                // }
                // const data = await response.json();
                // this.externalPositions = data.positions || [];
            } catch (error) {
                console.error('Error fetching external positions:', error);
                this.externalPositionsError = error.message;
            } finally {
                this.externalPositionsLoading = false;
            }
        }
    }));

    // Main App Component
    Alpine.data('app', () => ({
        loading: true,
        error: null,
        dataReady: false,
        projects: [],
        overviews: [],
        selectedProject: null,
        serverVersion: null,
        performance: null,
        performanceLoading: false,
        performanceError: null,
        performanceChart: null,
        refreshTimer: null,
        isInitialLoad: true,
        risks: [],
        showAllRisks: false,
        risksLoading: false,
        risksError: null,
        projectsWithRisks: new Set(),
        viewMode: 'overview',
        projectFilter: localStorage.getItem('projectFilter') || '',
        isLockEditMode: false,
        lockedProjects: new Set(),
        isIdleMode: false,
        showTodayPerformance: false,
        showLastWeekPerformance: false,
        options: {
            debug: false,
            activateChrome: false,
            enableTradeAlert: false,
            enableBalancer: false,
            checkIdleStoploss: false,
            enableStoploss: false,
            enableAutoStop: false,
            autoStopVolume: 0,
            autoStopDailyVolume: 0,
            extensionDir: '',
            automationBatchTimeout: 0,
            automationBatchWait: 0,
            automationBatchLimit: 0,
            automationBatchRandom: false,
            automationDailyStartTime: ''
        },

        init() {
            console.log('App initialized');
            
            const urlParams = new URLSearchParams(window.location.search);
            const overviewParam = urlParams.get('overview');
            const projectIdFromUrl = urlParams.get('project');

            if (overviewParam) {
                this.selectedProject = 'overview';
                this.projectFilter = overviewParam;
            } else if (projectIdFromUrl) {
                this.selectedProject = projectIdFromUrl;
            }

            // Initialize lockedProjects from localStorage
            try {
                const savedLocks = localStorage.getItem('lockedProjects');
                if (savedLocks) {
                    const parsedLocks = JSON.parse(savedLocks);
                    if (Array.isArray(parsedLocks)) {
                        this.lockedProjects = new Set(parsedLocks);
                        console.log('Loaded locked projects:', Array.from(this.lockedProjects));
                    }
                }
            } catch (e) {
                console.error('Error loading locked projects:', e);
                this.lockedProjects = new Set();
            }

            // Listen for global options updates
            window.addEventListener('update-global-options', (event) => {
                this.options = { ...event.detail };
            });

            this.fetchProjects();
            this.fetchGlobalOptions().then(() => {
                document.title = this.options.id ? this.options.id + ' copier' : 'copier';
            });
            this.fetchAllRisks();
            this.startAutoRefresh();

            // Listen for project renamed event
            this.$el.addEventListener('project-renamed', (event) => {
                this.fetchProjects().then(() => {
                    this.selectProject(event.detail.newProjectId);
                });
            });

            // Watch for filter changes to persist to localStorage
            this.$watch('projectFilter', (value) => {
                localStorage.setItem('projectFilter', value);
            });

            this.$watch('viewMode', (value) => {
                const table = document.querySelector('.overview-table');
                if (value === 'agent') {
                    table.classList.add('show-details');
                } else {
                    table.classList.remove('show-details');
                }
            });

            this.$nextTick(() => {
                if (this.viewMode === 'agent') {
                    const table = document.querySelector('.overview-table');
                    if (table) table.classList.add('show-details');
                }
            });
        },

        saveLockedProjects() {
            const arrayValue = Array.from(this.lockedProjects);
            console.log('Saving locked projects:', arrayValue);
            localStorage.setItem('lockedProjects', JSON.stringify(arrayValue));
        },

        startAutoRefresh() {
            if (this.refreshTimer) {
                clearInterval(this.refreshTimer);
            }
            this.refreshTimer = setInterval(() => {
                if (this.selectedProject) {
                    this.refreshData();
                }
                this.fetchAllRisks();
            }, 2000);
        },

        stopAutoRefresh() {
            if (this.refreshTimer) {
                clearInterval(this.refreshTimer);
                this.refreshTimer = null;
            }
        },

        async refreshData() {
            try {
                this.error = null;
                const response = await fetch('/projects');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                this.projects = data.projects || [];

                if (this.selectedProject) {
                    if (this.selectedProject === 'overview') {
                        const overviewResponse = await fetch('/overviews');
                        if (!overviewResponse.ok) {
                            throw new Error(`HTTP error! status: ${overviewResponse.status}`);
                        }
                        const overviewData = await overviewResponse.json();
                        this.overviews = overviewData.overviews || [];
                    } else {
                        await Promise.all([
                            this.fetchPerformance(this.selectedProject, this.showLastWeekPerformance),
                            this.fetchRisks()
                        ]);
                    }
                }
            } catch (error) {
                console.error('Error refreshing data:', error);
            }
        },

        async fetchProjects() {
            try {
                this.loading = true;
                this.error = null;
                const response = await fetch('/projects');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                this.projects = data.projects || [];
                await this.fetchGlobalOptions();

                if (this.projects.length > 0 && !this.selectedProject) {
                    this.selectProject('overview');
                }
                this.dataReady = true;
                this.loading = false;
                this.isInitialLoad = false;
            } catch (error) {
                this.error = `Failed to load projects: ${error.message}`;
                this.loading = false;
                this.isInitialLoad = false;
                console.error('Error fetching projects:', error);
            }
        },

        async fetchGlobalOptions() {
            try {
                const response = await fetch('/health');
                if (!response.ok) {
                    throw new Error('Failed to fetch options');
                }
                const data = await response.json();
                // Capture server version
                this.serverVersion = data.version;
                // Convert string numeric values to numbers
                this.options = {
                    ...data.options,
                    autoStopVolume: parseFloat(data.options.autoStopVolume) || 0,
                    autoStopDailyVolume: parseFloat(data.options.autoStopDailyVolume) || 0,
                    automationBatchTimeout: parseFloat(data.options.automationBatchTimeout) || 0,
                    automationBatchWait: parseInt(data.options.automationBatchWait) || 0,
                    automationBatchLimit: parseInt(data.options.automationBatchLimit) || 0,
                    automationBatchRandom: data.options.automationBatchRandom || false,
                    automationDailyStartTime: data.options.automationDailyStartTime || ''
                };
            } catch (error) {
                console.error('Error fetching options:', error);
            }
        },

        async toggleProjectStatus(projectId, currentStatus) {
            try {
                const response = await fetch('/update-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        projectIds: projectId,
                        status: currentStatus === 'running' ? 'stopped' : 'running',
                        comment: 'console'
                    }),
                });

                if (!response.ok) {
                    throw new Error('Failed to toggle project status');
                }

                await this.refreshData();
            } catch (error) {
                console.error('Error toggling project status:', error);
                alert('Failed to toggle project status: ' + error.message);
            }
        },

        async emergencyStop(projectId) {
            if (!confirm('⚠️ Are you sure you want to emergency stop this project and withdraw all funds immediately?')) {
                return;
            }

            try {
                const response = await fetch(`/toggle-status/${projectId}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ status: 'stopped', comment: 'emergency' })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                await this.refreshData();
            } catch (error) {
                console.error('Error emergency stopping project:', error);
                alert(`Failed to emergency stop project: ${error.message}`);
            }
        },

        async resetProject(projectId) {
            if (!confirm(`Are you sure you want to reset project ${projectId}?`)) {
                return;
            }

            try {
                const response = await fetch(`/reset/${projectId}`, {
                    method: 'POST'
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                await this.refreshData();
            } catch (error) {
                console.error('Error resetting project:', error);
                alert(`Failed to reset project: ${error.message}`);
            }
        },

        async deleteProject(projectId) {
            const project = this.projects.find(p => p.projectId === projectId);
            if (!project) return;

            if (!confirm(`⚠️ Warning: This will permanently delete project`)) {
                return;
            }

            try {
                const response = await fetch(`/delete/${projectId}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                this.selectedProject = null;
                await this.fetchProjects();
            } catch (error) {
                console.error('Error deleting project:', error);
                alert(`Failed to delete project: ${error.message}`);
            }
        },

        async fetchPerformance(projectId, lastWeek = false) {
            this.performanceLoading = true;
            this.performanceError = null;
            try {
                const response = await fetch(`/performance/${projectId}${lastWeek ? '?lastWeek=true' : ''}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch performance data');
                }
                this.performance = await response.json();
                this.$nextTick(() => {
                    this.updatePerformanceChart();
                });
            } catch (error) {
                console.error('Error fetching performance:', error);
                this.performanceError = error.message;
            } finally {
                this.performanceLoading = false;
            }
        },

        updatePerformanceChart() {
            if (!this.performance || !this.performance.dailyPerformance || this.performance.dailyPerformance.length === 0) {
                return;
            }

            const chartElement = document.getElementById('performance-chart');
            if (!chartElement) return;

            const ctx = chartElement.getContext('2d');

            if (this.performanceChart) {
                this.performanceChart.destroy();
            }

            const labels = this.performance.dailyPerformance.map(p => {
                const date = new Date(p.date);
                return date.toLocaleDateString();
            });

            const volumeData = this.performance.dailyPerformance.map(p => p.volume);
            const ratioData = this.performance.dailyPerformance.map(p => Math.abs(p.ratio * 100));
            const ratioColors = this.performance.dailyPerformance.map(p =>
                p.ratio >= 0 ? 'rgba(40, 167, 69, 0.7)' : 'rgba(220, 53, 69, 0.7)'
            );
            const ratioBorderColors = this.performance.dailyPerformance.map(p =>
                p.ratio >= 0 ? 'rgba(40, 167, 69, 1)' : 'rgba(220, 53, 69, 1)'
            );

            this.performanceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Volume',
                            data: volumeData,
                            backgroundColor: 'rgba(54, 162, 235, 0.5)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1,
                            order: 2,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Cost Ratio (%)',
                            data: ratioData,
                            backgroundColor: ratioColors,
                            borderColor: ratioBorderColors,
                            borderWidth: 1,
                            type: 'line',
                            order: 1,
                            yAxisID: 'y1',
                            pointRadius: 5,
                            pointHoverRadius: 8
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: false,
                    transitions: {
                        active: {
                            animation: {
                                duration: 0
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Volume'
                            },
                            ticks: {
                                callback: function(value) {
                                    return '$' + Math.round(value);
                                }
                            }
                        },
                        y1: {
                            beginAtZero: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Cost Ratio (%)'
                            },
                            grid: {
                                drawOnChartArea: false
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(2) + '%';
                                }
                            }
                        }
                    },
                    tooltips: {
                        callbacks: {
                            label: function(tooltipItem, data) {
                                const datasetIndex = tooltipItem.datasetIndex;
                                const index = tooltipItem.index;

                                if (datasetIndex === 1) {
                                    const ratio = this.performance.dailyPerformance[index].ratio;
                                    const sign = ratio >= 0 ? '+' : '-';
                                    return 'Cost Ratio: ' + sign + (Math.abs(ratio) * 100).toFixed(2) + '%';
                                }

                                return tooltipItem.yLabel;
                            }
                        }
                    }
                }
            });
        },

        selectProject(projectId) {
            this.selectedProject = projectId;
            if (projectId === 'overview') {
                this.refreshData();
            } else {
                this.fetchPerformance(projectId, this.showLastWeekPerformance);
                this.fetchRisks();
            }
        },

        async fetchRisks() {
            if (!this.selectedProject) {
                console.log('No project selected, skipping risk fetch');
                return;
            }

            try {
                this.risksLoading = true;
                this.risksError = null;
                console.log('Fetching risks for project:', this.selectedProject);
                const response = await fetch(`/risks/${this.selectedProject}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                console.log('Received risks data:', data);
                this.risks = data.risks || [];
                this.risksLoading = false;
            } catch (error) {
                this.risksError = `Failed to load risks: ${error.message}`;
                this.risksLoading = false;
                console.error('Error fetching risks:', error);
            }
        },

        getLatestRisk() {
            if (!this.risks || this.risks.length === 0) {
                console.log('No risks available');
                return null;
            }

            const latestRisk = this.risks[this.risks.length - 1];
            const riskTime = new Date(latestRisk.CreateTime);
            const now = new Date();
            const minutesAgo = (now - riskTime) / (1000 * 60);

            console.log('Latest risk:', latestRisk, 'Minutes ago:', minutesAgo);
            return minutesAgo <= 15 ? latestRisk : null;
        },

        getRecentRisks() {
            if (!this.risks || this.risks.length === 0) {
                console.log('No risks available for recent filter');
                return [];
            }

            const now = new Date();
            const recentRisks = this.risks.filter(risk => {
                const riskTime = new Date(risk.CreateTime);
                const hoursAgo = (now - riskTime) / (1000 * 60 * 60);
                return hoursAgo <= 1;
            });

            recentRisks.sort((a, b) => {
                return new Date(b.CreateTime) - new Date(a.CreateTime);
            });

            console.log('Recent risks:', recentRisks);
            return recentRisks;
        },

        toggleShowAllRisks() {
            this.showAllRisks = !this.showAllRisks;
        },

        formatRiskTime(time) {
            if (!time) return '';
            return new Date(time).toLocaleString();
        },

        formatRiskMessage(risk) {
            if (!risk) return '';

            const type = risk.Type.toUpperCase();
            let details = '';

            switch (risk.Type) {
                case 'agent_not_syncing':
                    details = `Agent: ${risk.UserID}, ${risk.Comment}`;
                    break;
                default:
                    let comment = '';
                    if (risk.UserID) {
                        comment = `Agent: ${risk.UserID}, `;
                    }
                    details = comment + risk.Comment || 'Unknown risk type';
            }

            return `${type}\n${details}`;
        },

        async fetchAllRisks() {
            try {
                const response = await fetch('/risks/all?validOnly=true');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                this.projectsWithRisks.clear();
                data.risks.forEach(risk => {
                    if (risk.ResetTime === null) {
                        this.projectsWithRisks.add(risk.ProjectID);
                    }
                });
            } catch (error) {
                console.error('Error fetching all risks:', error);
            }
        },

        openSettings(projectId, userId) {
            const project = this.projects.find(p => p.projectId === projectId);
            if (!project) return;
            
            const agent = project.agents.find(a => a.userId === userId);
            if (!agent) return;
            
            this.$dispatch('show-settings', { projectId, userId, agent });
        },

        get projectGroups() {
            if (!this.projects) return [];
            const groups = new Set();
            this.projects.forEach(p => {
                const parts = p.projectId.split('_');
                if (parts.length > 1) {
                    groups.add(parts[0]);
                }
            });
            return Array.from(groups).sort();
        },

        get filteredOverviews() {
            if (!this.overviews) return [];
            
            const filterText = (this.projectFilter || '').toLowerCase().trim();
            if (!filterText) {
                return this.overviews;
            }

            // New logic for filtering by a list of exact project IDs
            if (filterText.includes(',')) {
                const projectIds = filterText.split(',').map(id => id.trim().toLowerCase());
                return this.overviews.filter(overview => projectIds.includes(overview.projectId.toLowerCase()));
            }

            const optionFilters = [];
            const marginFilters = [];
            const optionRegex = /(hasposition|isrunning):\s*(true|false)/g;
            const marginRegex = /(margin)\s*([><])\s*(\d+)/g;
            
            let match;
            optionRegex.lastIndex = 0; 
            while ((match = optionRegex.exec(filterText)) !== null) {
                optionFilters.push({ key: match[1], value: match[2] });
            }

            marginRegex.lastIndex = 0;
            while ((match = marginRegex.exec(filterText)) !== null) {
                marginFilters.push({
                    key: match[1],
                    operator: match[2],
                    value: parseInt(match[3], 10)
                });
            }

            let filtered = this.overviews;

            if (optionFilters.length > 0) {
                optionFilters.forEach(({ key, value }) => {
                    const boolValue = value === 'true';
                    switch (key) {
                        case 'hasposition':
                            filtered = filtered.filter(overview => (overview.positionValue > 0) === boolValue);
                            break;
                        case 'isrunning':
                            filtered = filtered.filter(overview => (overview.status.toLowerCase() === 'running') === boolValue);
                            break;
                    }
                });
            } else if (marginFilters.length > 0) {
                marginFilters.forEach(({ key, operator, value }) => {
                    filtered = filtered.filter(overview => {
                        const project = this.projects.find(p => p.projectId === overview.projectId);
                        if (!project) return false;

                        const masterAgent = project.agents.find(a => a.role === 'master');
                        const copierAgent = project.agents.find(a => a.role === 'copier');

                        const masterMargin = masterAgent ? masterAgent.totalMargin : 0;
                        const copierMargin = copierAgent ? copierAgent.totalMargin : 0;

                        if (operator === '>') {
                            return masterMargin > value || copierMargin > value;
                        } else if (operator === '<') {
                            return masterMargin < value || copierMargin < value;
                        }
                        return false;
                    });
                });
            } else {
                filtered = filtered.filter(overview => 
                    overview.projectId.toLowerCase().startsWith(filterText)
                );
            }

            return filtered;
        },

        toggleLockEditMode() {
            this.isLockEditMode = !this.isLockEditMode;
            console.log('Lock edit mode:', this.isLockEditMode);
        },

        isProjectLocked(projectId) {
            const isLocked = this.lockedProjects.has(projectId);
            console.log('Checking if project is locked:', projectId, isLocked);
            return isLocked;
        },

        toggleProjectLock(projectId) {
            const project = this.overviews.find(p => p.projectId === projectId);
            if (!project) return;

            if (this.lockedProjects.has(projectId)) {
                this.lockedProjects.delete(projectId);
                console.log('Unlocked project:', projectId);
            } else {
                this.lockedProjects.add(projectId);
                console.log('Locked project:', projectId);
            }
            console.log('Current locked projects:', Array.from(this.lockedProjects));
            this.saveLockedProjects();
        },

        async updateAllProjects(targetStatus) {
            try {
                // Get all projects that are not locked
                const projectsToUpdate = this.filteredOverviews.filter(overview => 
                    !this.lockedProjects.has(overview.projectId)
                );

                if (projectsToUpdate.length === 0) {
                    return;
                }

                // Create confirmation message with project IDs
                const projectIds = projectsToUpdate.map(p => p.projectId).join(', ');
                if (!confirm(`Are you sure ${targetStatus === 'running' ? 'starting' : 'stopping'} ${projectsToUpdate.length} of projects: ${projectIds}?`)) {
                    return;
                }

                // Toggle status for all projects in one request
                const response = await fetch('/update-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        projectIds: projectIds,
                        status: targetStatus,
                        comment: ''
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Refresh the data after all toggles are complete
                await this.refreshData();
            } catch (error) {
                console.error('Error toggling projects:', error);
                alert(`Failed to toggle projects: ${error.message}`);
            }
        },

        async resetAllProjects() {
            if (!confirm('Are you sure you want to reset all projects?')) {
                return;
            }
            try {
                // Get all projects that are not locked
                const projectsToReset = this.filteredOverviews.filter(overview => 
                    !this.lockedProjects.has(overview.projectId)
                );

                if (projectsToReset.length === 0) {
                    return;
                }

                // Create confirmation message with project IDs
                const projectIds = projectsToReset.map(p => p.projectId).join(', ');
                if (!confirm(`Are you sure resetting ${projectsToReset.length} of projects: ${projectIds}?`)) {
                    return;
                }

                // Reset each project
                for (const project of projectsToReset) {
                    const response = await fetch(`/reset/${project.projectId}`, {
                        method: 'POST'
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                }

                // Refresh the data after all resets are complete
                await this.refreshData();
            } catch (error) {
                console.error('Error resetting projects:', error);
                alert(`Failed to reset projects: ${error.message}`);
            }
        },

        async clearProjectStatusComment() {
            try {
                // Get all projects that are not locked and are stopped
                const projectsToUpdate = this.filteredOverviews.filter(overview => 
                    !this.lockedProjects.has(overview.projectId) && overview.status.toLowerCase() === 'stopped'
                );

                if (projectsToUpdate.length === 0) {
                    alert("No stopped projects to clear.");
                    return;
                }

                // Create confirmation message with project IDs
                const projectIds = projectsToUpdate.map(p => p.projectId).join(', ');
                if (!confirm(`Are you sure you want to clear status comment for ${projectsToUpdate.length} projects: ${projectIds}?`)) {
                    return;
                }

                // Update status for all projects in one request
                const response = await fetch('/update-status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        projectIds: projectIds,
                        status: 'stopped', // Keep the current status
                        comment: 'reset'
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Refresh the data after all updates are complete
                await this.refreshData();
            } catch (error) {
                console.error('Error clearing project status comment:', error);
                alert(`Failed to clear project status comment: ${error.message}`);
            }
        },

        async closeAllChromes() {
            if (!confirm('Are you sure you want to close Chrome windows for the filtered projects?')) {
                return;
            }

            try {
                // Get filtered and unlocked project IDs
                const filteredProjectIds = this.filteredOverviews
                    .filter(overview => !this.isProjectLocked(overview.projectId))
                    .map(overview => overview.projectId);

                if (filteredProjectIds.length === 0) {
                    alert('No unlocked projects found in the current filter.');
                    return;
                }

                // Make request to close-chrome endpoint
                const response = await fetch(`/close-chrome?project_ids=${filteredProjectIds.join(',')}`);
                if (!response.ok) {
                    throw new Error('http error: ' + response.status);
                }

                const result = await response.json();
                if (result.success) {
                    alert('Successfully closed Chrome for filtered projects');
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('Error closing Chrome:', error);
                alert('Failed to close Chrome: ' + error.message);
            }
        },

        async refreshAllChromes() {
            if (!confirm('Are you sure you want to refresh Chrome windows for the filtered projects?')) {
                return;
            }

            try {
                const filteredProjects = this.filteredOverviews
                    .filter(overview => !this.isProjectLocked(overview.projectId));

                if (filteredProjects.length === 0) {
                    alert('No unlocked projects found in the current filter.');
                    return;
                }

                const promises = filteredProjects.flatMap(overview => {
                    const project = this.projects.find(p => p.projectId === overview.projectId);
                    if (!project || !project.agents) return [];
                    
                    return project.agents.map(agent => {
                        if (agent.userId) {
                            return fetch(`/reload-chrome/${project.projectId}/${agent.userId}`);
                        }
                        return Promise.resolve();
                    });
                });

                await Promise.all(promises);
                alert('Successfully refreshed Chrome for filtered projects');

            } catch (error) {
                console.error('Error refreshing Chrome:', error);
                alert('Failed to refresh Chrome: ' + error.message);
            }
        },

        async gotoTrade() {
            if (!confirm('Are you sure you want to navigate to the trade page for the filtered projects?')) {
                return;
            }

            try {
                const filteredProjectIds = this.filteredOverviews
                    .filter(overview => !this.isProjectLocked(overview.projectId))
                    .map(overview => overview.projectId);

                if (filteredProjectIds.length === 0) {
                    alert('No unlocked projects found in the current filter.');
                    return;
                }

                const response = await fetch(`/server-request/add`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ projectIds: filteredProjectIds.join(','), userIds: '', requestType: 'navigate_to_trade' })
                });

                if (!response.ok) {
                    throw new Error('http error: ' + response.status);
                }
                alert('Successfully sent request to navigate to trade page.');

            } catch (error) {
                console.error('Error navigating to trade:', error);
                alert('Failed to navigate to trade: ' + error.message);
            }
        },

        async startAllChromes() {
            if (!confirm('Are you sure you want to start Chrome windows for the filtered projects?')) {
                return;
            }

            try {
                // Get filtered and unlocked project IDs
                const filteredProjectIds = this.filteredOverviews
                    .filter(overview => !this.isProjectLocked(overview.projectId))
                    .map(overview => overview.projectId);

                if (filteredProjectIds.length === 0) {
                    alert('No unlocked projects found in the current filter.');
                    return;
                }

                // Make request to start-chrome endpoint
                const response = await fetch(`/start-chrome?project_ids=${filteredProjectIds.join(',')}`);
                if (!response.ok) {
                    throw new Error('http error: ' + response.status);
                }

                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('Error starting Chrome:', error);
                alert('Failed to start Chrome: ' + error.message);
            }
        },

        async setProjectIdle(projectId, isIdle) {
            try {
                const response = await fetch(`/toggle-status/${projectId}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        status: 'running', 
                        comment: isIdle ? 'idle' : '' 
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Refresh the data after setting idle status
                await this.refreshData();
            } catch (error) {
                console.error('Error setting project idle status:', error);
                alert(`Failed to set project idle status: ${error.message}`);
            }
        },

        openMasterSettings(projectId) {
            const project = this.projects.find(p => p.projectId === projectId);
            if (!project) return;
            
            const masterAgent = project.agents.find(a => a.role === 'master');
            if (!masterAgent) return;
            
            this.$dispatch('show-settings', { projectId, userId: masterAgent.userId, agent: masterAgent });
        },

        async toggleIdleMode() {
            if (this.isLockEditMode) return;
            
            this.isIdleMode = !this.isIdleMode;
            try {
                // Get all running projects that are not locked
                const runningProjects = this.filteredOverviews.filter(overview => 
                    overview.status.toLowerCase() === 'running' &&
                    !this.lockedProjects.has(overview.projectId)
                );

                // Set each running project's status comment
                for (const project of runningProjects) {
                    const response = await fetch(`/toggle-status/${project.projectId}/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ 
                            status: 'running', 
                            comment: this.isIdleMode ? 'idle' : 'console' 
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                }

                // Refresh the data after all updates are complete
                await this.refreshData();
            } catch (error) {
                console.error('Error toggling idle mode:', error);
                alert(`Failed to toggle idle mode: ${error.message}`);
                this.isIdleMode = !this.isIdleMode; // Revert the toggle if there was an error
            }
        },

        async updateStats() {
            if (!confirm('Update stats for all filtered and unlocked projects?')) {
                return;
            }

            try {
                // Get filtered and unlocked projects
                const projectIds = this.filteredOverviews
                    .filter(overview => !this.isProjectLocked(overview.projectId))
                    .map(overview => overview.projectId);

                if (projectIds.length === 0) {
                    alert('No projects to update.');
                    return;
                }
                await this.updateStatsForProject(projectIds, []);
            } catch (error) {
                console.error('Error updating stats:', error);
                alert('Failed to update stats: ' + error.message);
            }
        },

        async updateStatsForProject(projectIds, userIds = []) {
            const response = await fetch('/server-request/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    projectIds: projectIds.join(','),
                    userIds: userIds.join(','),
                    requestType: 'update_stats'
                })
            });

            if (!response.ok) {
                throw new Error('Failed to update stats');
            }

            const result = await response.json();
            if (!result.success) {
                throw new Error('Failed to update stats');
            }
        },

        // Handle activate-and-update-stats event
        async handleActivateAndUpdateStats(projectId, userId) {
            if (!projectId || !userId) {
                console.error('Missing projectId or userId');
                return;
            }

            try {
                // First activate Chrome for the agent
                await this.activateChrome(projectId, userId);
                
                // Then update stats for the specific agent
                await this.updateStatsForProject([projectId], [userId]);
                
                // Refresh the data to show updated stats
                await this.refreshData();
            } catch (error) {
                console.error('Error in activate-and-update-stats:', error);
                this.showToast('Failed to activate Chrome and update stats: ' + error.message, 'error');
            }
        },

        getAgentByRole(project, role) {
            if (!project || !project.agents) return null;
            return project.agents.find(a => a.role === role);
        },

        getAgentUpdateTimeInfo(projectId, role) {
            const project = this.projects.find(p => p.projectId === projectId);
            const agent = this.getAgentByRole(project, role);
            if (!agent || !agent.createTime) return { time: 'N/A', isStale: true, fullTime: 'N/A' };

            const updateTime = new Date(agent.createTime);
            const now = new Date();
            const todayUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
            const updateTimeUTC = new Date(Date.UTC(updateTime.getUTCFullYear(), updateTime.getUTCMonth(), updateTime.getUTCDate()));
            const isStale = updateTimeUTC.getTime() < todayUTC.getTime();

            return {
                time: updateTime.toLocaleTimeString('sv-SE', { timeZone: 'Asia/Shanghai' }),
                isStale: isStale,
                fullTime: updateTime.toLocaleString('sv-SE', { timeZone: 'Asia/Shanghai' }).replace(' ', 'T').slice(0, 19).replace('T', ' ')
            };
        },

        getAgentStatusTime(projectId, role, isTooltip) {
            const info = this.getAgentUpdateTimeInfo(projectId, role);
            if (isTooltip) {
                return info.fullTime;
            }
            return info.time;
        },

        getAgentStatsTimeInfo(projectId, role) {
            const project = this.projects.find(p => p.projectId === projectId);
            const agent = this.getAgentByRole(project, role);
            if (!agent || !agent.stats || !agent.stats.createTime) return { time: 'N/A', isStale: true, fullTime: 'N/A' };

            const updateTime = new Date(agent.stats.createTime);
            const now = new Date();
            const todayUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
            const updateTimeUTC = new Date(Date.UTC(updateTime.getUTCFullYear(), updateTime.getUTCMonth(), updateTime.getUTCDate()));
            const isStale = updateTimeUTC.getTime() < todayUTC.getTime();

            return {
                time: updateTime.toLocaleTimeString('sv-SE', { timeZone: 'Asia/Shanghai' }),
                isStale: isStale,
                fullTime: updateTime.toLocaleString('sv-SE', { timeZone: 'Asia/Shanghai' }).replace(' ', 'T').slice(0, 19).replace('T', ' ')
            };
        },

        getAgentStatsTime(projectId, role, isTooltip) {
            const info = this.getAgentStatsTimeInfo(projectId, role);
            if (isTooltip) {
                return info.fullTime;
            }
            return info.time;
        },

        async saveGlobalOptions() {
            try {
                // Ensure numeric values are properly converted and set enableRemoteAlert to false
                const optionsToSave = {
                    ...this.options,
                    enableRemoteAlert: false, // Force enableRemoteAlert to false
                    autoStopVolume: parseFloat(this.options.autoStopVolume) || 0,
                    autoStopDailyVolume: parseFloat(this.options.autoStopDailyVolume) || 0,
                    automationBatchTimeout: parseFloat(this.options.automationBatchTimeout) || 0,
                    automationBatchWait: parseInt(this.options.automationBatchWait) || 0,
                    automationBatchLimit: parseInt(this.options.automationBatchLimit) || 0,
                    automationBatchRandom: this.options.automationBatchRandom || false,
                    automationDailyStartTime: this.options.automationDailyStartTime || ''
                };
 
                // Validate automation timeout hours
                if (optionsToSave.automationBatchTimeout < 0 || optionsToSave.automationBatchTimeout > 12) {
                    throw new Error('Automation Timeout Hours must be between 0 and 12');
                }

                // Validate automation batch wait
                if (optionsToSave.automationBatchWait < 0) {
                    throw new Error('Automation Batch Wait must be a positive number');
                }

                const response = await fetch('/options/edit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        options: optionsToSave
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to save options');
                }

                // Refresh the options after saving
                await this.fetchGlobalOptions();
            } catch (error) {
                console.error('Error saving options:', error);
                alert('Failed to save options: ' + error.message);
            }
        },

        get filteredProjectIds() {
            return this.filteredOverviews
                .filter(overview => !this.isProjectLocked(overview.projectId))
                .map(overview => overview.projectId);
        },

        async activateChrome(projectId, userId) {
            if (!projectId || !userId) {
                console.error('Missing projectId or userId');
                return;
            }

            try {
                const response = await fetch(`/activate-chrome/${projectId}/${userId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                if (!data.success) {
                    throw new Error(data.error || 'Failed to activate Chrome');
                }
            } catch (error) {
                console.error('Error activating Chrome:', error);
                alert(error.message || 'Failed to activate Chrome');
            }
        },
    }));

    // Settings Dialog Component
    Alpine.data('settingsDialog', () => ({
        showSettings: false,
        settingsLoading: false,
        settingsError: null,
        selectedProject: null,
        selectedAgent: null,
        selectedAgentRole: '',
        settings: {
            alias: '',
            defaultSymbol: '',
            copyFromId: '',
            copyPercentage: 0,
            minMargin: 0,
            defaultSize: 0,
            coolingHour: 0,
            refreshInterval: 0,
            debug: false,
            manualMode: false,
            stopLossBuffer: 0.0,
            longRatio: 0.5,
            version: 0
        },

        async fetchSettings(agent) {
            this.showSettings = true;
            this.settingsLoading = true;
            this.settingsError = null;
            this.selectedProject = agent.projectId;
            this.selectedAgent = agent.userId;
            this.selectedAgentRole = agent.role.toUpperCase();

            try {
                // Set the agent role
                this.selectedAgentRole = agent.role.toUpperCase();

                // Update settings with current values, excluding projectId
                const { projectId, ...settingsWithoutProjectId } = agent.settings;
                this.settings = {
                    ...this.settings,  // Keep default values
                    ...settingsWithoutProjectId,  // Override with agent settings
                    stopLossBuffer: settingsWithoutProjectId.stopLossBuffer  // Ensure stopLossBuffer has a default
                };
            } catch (error) {
                console.error('Error fetching settings:', error);
                this.settingsError = error.message;
            } finally {
                this.settingsLoading = false;
            }
        },

        async saveSettings() {
            try {
                // Validate coolingHour range
                const coolingHour = parseFloat(this.settings.coolingHour);
                if (isNaN(coolingHour)) {
                    this.settingsError = "Cooling hour must be a valid number";
                    return;
                }
                if (coolingHour < 0) {
                    // For exact hour mode, only allow -0.5 to 0
                    if (coolingHour < -0.5) {
                        this.settingsError = "For exact hour mode, cooling must be between -0.5 and 0";
                        return;
                    }
                } else {
                    // For interval mode, allow 0.01 to 48.0
                    if (coolingHour < 0.01 || coolingHour > 48.0) {
                        this.settingsError = "For interval mode, cooling must be between 0.01 and 48.0 hours";
                        return;
                    }
                }

                // Validate stopLossBuffer range for master agents
                if (this.selectedAgentRole === 'MASTER') {
                    const stopLossBuffer = parseFloat(this.settings.stopLossBuffer);
                    if (isNaN(stopLossBuffer) || stopLossBuffer < 0 || stopLossBuffer > 0.05) {
                        this.settingsError = "Stop Loss Buffer must be between 0 and 0.05";
                        return;
                    }
                }

                // Convert numeric fields to proper types
                const numericSettings = {
                    ...this.settings,
                    copyPercentage: parseInt(this.settings.copyPercentage) || 0,
                    minMargin: parseFloat(this.settings.minMargin) || 0,
                    defaultSize: parseFloat(this.settings.defaultSize) || 0,
                    coolingHour: coolingHour,
                    refreshInterval: parseInt(this.settings.refreshInterval) || 0,
                    stopLossBuffer: parseFloat(this.settings.stopLossBuffer),
                    longRatio: parseFloat(this.settings.longRatio) || 0.5,
                    version: parseInt(this.settings.version) || 0,
                    manualMode: Boolean(this.settings.manualMode)
                };

                const response = await fetch(`/update-settings/${this.selectedProject}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userId: this.selectedAgent,
                        settings: numericSettings
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                // Close dialog and refresh data
                this.showSettings = false;
                this.$dispatch('refresh-data');
            } catch (error) {
                console.error('Error saving settings:', error);
                this.settingsError = error.message;
            }
        }
    }));

    // Project Settings Dialog Component
    Alpine.data('projectSettingsDialog', () => ({
        showProjectSettings: false,
        projectSettingsLoading: false,
        projectSettingsError: null,
        selectedProject: null,
        masterSettings: null,
        copierSettings: null,

        async fetchProjectSettings(projectId) {
            this.showProjectSettings = true;
            this.projectSettingsLoading = true;
            this.projectSettingsError = null;
            this.selectedProject = projectId;

            try {
                const project = this.projects.find(p => p.projectId === projectId);
                if (!project) {
                    throw new Error('Project not found');
                }

                // Find master and copier agents
                const masterAgent = project.agents.find(a => a.role === 'master');
                const copierAgent = project.agents.find(a => a.role === 'copier');

                if (!masterAgent) {
                    throw new Error('Master agent not found');
                }

                this.masterSettings = masterAgent.settings;
                this.copierSettings = copierAgent?.settings || null;
            } catch (error) {
                console.error('Error fetching project settings:', error);
                this.projectSettingsError = error.message;
            } finally {
                this.projectSettingsLoading = false;
            }
        }
    }));

    Alpine.data('renameDialog', () => ({
        showRenameDialog: false,
        selectedProject: null,
        newProjectId: '',
        error: null,

        init() {
            // Initialize with dialog hidden
            this.showRenameDialog = false;
            this.selectedProject = null;
            this.newProjectId = '';
            this.error = null;
        },

        openDialog(projectId) {
            console.log('Opening dialog for project:', projectId); // Debug log
            this.selectedProject = projectId;
            this.newProjectId = projectId;
            this.showRenameDialog = true;
        },

        closeDialog() {
            this.showRenameDialog = false;
            this.selectedProject = null;
            this.newProjectId = '';
            this.error = null;
        },

        renameProject() {
            this.error = null;
            fetch(`/change-project-id/${this.selectedProject}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    newProjectId: this.newProjectId
                })
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || 'Failed to rename project');
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Close the dialog
                    this.closeDialog();
                    // Refresh the projects list
                    this.fetchProjects();
                }
            })
            .catch(error => {
                console.error('Error renaming project:', error);
                this.error = error.message;
            });
        },
    }));

    // Global Options Dialog Component
    Alpine.data('globalOptionsDialog', () => ({
        showGlobalOptions: false,
        globalOptionsLoading: false,
        globalOptionsError: null,
        options: {
            debug: false,
            activateChrome: false,
            enableTradeAlert: false,
            enableBalancer: false,
            checkIdleStoploss: false,
            enableStoploss: false,
            enableAutoStop: false,
            autoStopVolume: 0,
            autoStopDailyVolume: 0,
            extensionDir: '',
            automationBatchTimeout: 0,
            automationBatchWait: 0,
            automationBatchLimit: 0,
            automationBatchRandom: false,
            automationDailyStartTime: ''
        },

        async fetchGlobalOptions() {
            this.showGlobalOptions = true;
            this.globalOptionsLoading = true;
            this.globalOptionsError = null;

            try {
                const response = await fetch('/health');
                if (!response.ok) {
                    throw new Error('Failed to fetch options');
                }
                const data = await response.json();
                // Convert string numeric values to numbers
                this.options = {
                    ...data.options,
                    autoStopVolume: parseFloat(data.options.autoStopVolume) || 0,
                    autoStopDailyVolume: parseFloat(data.options.autoStopDailyVolume) || 0,
                    automationBatchTimeout: parseFloat(data.options.automationBatchTimeout) || 0,
                    automationBatchWait: parseInt(data.options.automationBatchWait) || 0,
                    automationBatchLimit: parseInt(data.options.automationBatchLimit) || 0,
                    automationBatchRandom: data.options.automationBatchRandom || false,
                    automationDailyStartTime: data.options.automationDailyStartTime || ''
                };
            } catch (error) {
                console.error('Error fetching options:', error);
                this.globalOptionsError = 'Failed to load options. Please try again.';
            } finally {
                this.globalOptionsLoading = false;
            }
        },

        async saveGlobalOptions() {
            try {
                // Ensure numeric values are properly converted and set enableRemoteAlert to false
                const optionsToSave = {
                    ...this.options,
                    enableRemoteAlert: false, // Force enableRemoteAlert to false
                    autoStopVolume: parseFloat(this.options.autoStopVolume) || 0,
                    autoStopDailyVolume: parseFloat(this.options.autoStopDailyVolume) || 0,
                    automationBatchTimeout: parseFloat(this.options.automationBatchTimeout) || 0,
                    automationBatchWait: parseInt(this.options.automationBatchWait) || 0,
                    automationBatchLimit: parseInt(this.options.automationBatchLimit) || 0,
                    automationBatchRandom: this.options.automationBatchRandom || false,
                    automationDailyStartTime: this.options.automationDailyStartTime || ''
                };

                // Validate automation timeout hours
                if (optionsToSave.automationBatchTimeout < 0 || optionsToSave.automationBatchTimeout > 12) {
                    throw new Error('Automation Timeout Hours must be between 0 and 12');
                }

                // Validate automation batch wait
                if (optionsToSave.automationBatchWait < 0) {
                    throw new Error('Automation Batch Wait must be a positive number');
                }

                const response = await fetch('/options/edit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        options: optionsToSave
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to save options');
                }

                // Update page title
                document.title = this.options.id ? this.options.id + ' copier' : 'copier';

                // Dispatch an event to update the main app's options
                this.$dispatch('update-global-options', optionsToSave);

                // Show success message
                const toast = document.createElement('div');
                toast.className = 'toast success';
                toast.textContent = 'Options saved successfully';
                document.body.appendChild(toast);
                setTimeout(() => toast.remove(), 3000);

                // Close the dialog
                this.showGlobalOptions = false;
            } catch (error) {
                console.error('Error saving options:', error);
                alert('Failed to save options: ' + error.message);
            }
        }
    }));

    // Batch Settings Dialog
    Alpine.data('batchSettingsDialog', () => ({
        showBatchSettings: false,
        settings: {
            defaultSymbol: '',
            defaultSize: null,
            minMargin: null,
            coolingHour: null,
            stopLossBuffer: null,
            refreshInterval: null,
            copyPercentage: null
        },
        filteredProjects: [],

        init() {
            this.showBatchSettings = false;
            this.settings = {
                defaultSymbol: '',
                defaultSize: null,
                minMargin: null,
                coolingHour: null,
                stopLossBuffer: null,
                refreshInterval: null,
                copyPercentage: null
            };
            this.filteredProjects = [];
        },

        openDialog(projectIds) {
            this.filteredProjects = projectIds;
            this.showBatchSettings = true;
        },

        async saveBatchSettings() {
            try {
                // Filter out empty values and convert numeric values
                const settings = Object.fromEntries(
                    Object.entries(this.settings)
                        .filter(([_, value]) => value !== null && value !== '')
                        .map(([key, value]) => {
                            // Convert numeric fields to appropriate types
                            if (value !== '') {
                                switch (key) {
                                    case 'defaultSize':
                                    case 'minMargin':
                                    case 'coolingHour':
                                    case 'stopLossBuffer':
                                        return [key, parseFloat(value)];
                                    case 'refreshInterval':
                                    case 'copyPercentage':
                                        return [key, parseInt(value)];
                                    default:
                                        return [key, value];
                                }
                            }
                            return [key, value];
                        })
                );

                if (this.filteredProjects.length === 0) {
                    throw new Error('No unlocked projects available for batch update');
                }

                const response = await fetch('/batch-settings/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        projectIds: this.filteredProjects.join(','),
                        settings: settings
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                if (!result.success) {
                    throw new Error('Failed to update batch settings');
                }

                // Clear the form
                this.settings = {
                    defaultSymbol: '',
                    defaultSize: null,
                    minMargin: null,
                    coolingHour: null,
                    stopLossBuffer: null,
                    refreshInterval: null,
                    copyPercentage: null
                };

                this.showBatchSettings = false;
            } catch (error) {
                console.error('Error saving batch settings:', error);
                alert('Failed to save batch settings: ' + error.message);
            }
        }
    }));
}); 
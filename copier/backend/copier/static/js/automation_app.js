function automationApp() {
    return {
        currentView: 'schedules',
        schedules: [],
        currentSchedule: null,
        projects: new Map(),
        loading: true,
        error: null,
        isRefreshing: false,
        refreshInterval: null,
        isPaused: false,
        lastUpdated: null,
        actionLoading: {},
        batchLoading: {},
        toasts: [],
        collapsedDates: {},
        scheduleFilter: 'all',
        expandedErrors: new Set(),
        copierID: null,
        options: null,
        serverVersion: null,
        selectedJobType: '',

        // Settings Dialog properties
        showSettings: false,
        settingsLoading: false,
        settingsError: null,
        selectedProjectForSettings: null,
        selectedAgentForSettings: null,
        selectedAgentRoleForSettings: '',
        currentSettings: {
            alias: '',
            defaultSymbol: '',
            copyFromId: '',
            copyPercentage: 0,
            minMargin: 0,
            defaultSize: 0,
            coolingHour: 0,
            refreshInterval: 0,
            debug: false,
            manualMode: false,
            stopLossBuffer: 0.0,
            longRatio: 0.5,
            version: 0
        },

        async init() {
            console.log('Initializing automation app...');
            this.loading = true;
            this.error = null;
            try {
                await this.fetchGlobalOptions();
                if (this.options && this.options.id) {
                    document.title = this.options.id + ' Automations';
                } else {
                    document.title = 'Automations';
                }

                await Promise.all([this.loadSchedules(true), this.loadProjects()]);

                if (this.currentSchedule) {
                    this.updateCurrentSchedule();
                } else {
                    this.goToActiveSchedule();
                }
            } catch (error) {
                this.error = error.message;
                console.error('Error initializing app:', error);
            } finally {
                this.loading = false;
                this.lastUpdated = new Date();
            }
            this.startAutoRefresh();
        },

        startAutoRefresh() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }
            this.refreshInterval = setInterval(() => {
                if (!this.isPaused) {
                    this.refreshData();
                }
            }, 5000);
        },

        togglePause() {
            this.isPaused = !this.isPaused;
        },

        async refreshData() {
            this.isRefreshing = true;
            try {
                const oldSchedules = JSON.parse(JSON.stringify(this.schedules));
                await this.loadSchedules(true);
                await this.loadProjects();
                this.triggerStatusAnimations(oldSchedules, this.schedules);
            } catch (error) {
                console.error('Error refreshing data:', error);
            } finally {
                this.isRefreshing = false;
            }
        },

        triggerStatusAnimations(oldSchedules, newSchedules) {
            this.$nextTick(() => {
                newSchedules.forEach(newSchedule => {
                    const oldSchedule = oldSchedules.find(s => s.refID === newSchedule.refID);
                    if (oldSchedule) {
                        newSchedule.batches.forEach(newBatch => {
                            const oldBatch = oldSchedule.batches.find(b => b.refID === newBatch.refID);
                            if (oldBatch) {
                                newBatch.items.forEach(newItem => {
                                    const oldItem = oldBatch.items.find(i => i.refID === newItem.refID);
                                    if (oldItem && oldItem.status !== newItem.status) {
                                        const element = document.querySelector(`[data-item-id="${newItem.refID}"] .status-badge`);
                                        if (element) {
                                            element.classList.add('status-changed');
                                            setTimeout(() => {
                                                element.classList.remove('status-changed');
                                            }, 1000);
                                        }
                                    }
                                });
                            }
                        });
                    }
                });
            });
        },

        async loadSchedules(isRefresh = false) {
            if (!isRefresh) {
                this.loading = true;
            }
            this.error = null;
            try {
                const response = await fetch('/automation/schedules');
                if (!response.ok) {
                    throw new Error('Failed to load schedules');
                }
                const data = await response.json();
                if (data && data.schedules) {
                    const newSchedules = data.schedules.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
                    if (JSON.stringify(this.schedules) !== JSON.stringify(newSchedules)) {
                        this.schedules = newSchedules;
                    }
                    if (this.currentSchedule) {
                        this.updateCurrentSchedule();
                    } else {
                        this.goToActiveSchedule();
                    }
                } else {
                    this.schedules = [];
                }
            } catch (error) {
                this.error = error.message;
                console.error('Failed to load schedules:', error);
            } finally {
                if (!isRefresh) {
                    this.loading = false;
                }
                this.lastUpdated = new Date();
            }
        },

        async loadProjects() {
            try {
                const response = await fetch('/projects');
                if (!response.ok) {
                    console.error('Failed to load projects');
                    return;
                }
                const data = await response.json();
                if (data && data.projects) {
                    const projectMap = new Map();
                    data.projects.forEach(p => projectMap.set(p.projectId, p));
                    this.projects = projectMap;
                }
            } catch (error) {
                console.error('Error loading projects:', error);
            }
        },

        showSchedules() {
            this.currentView = 'schedules';
            this.currentSchedule = null;
        },

        showScheduleDetail(refId) {
            const schedule = this.schedules.find(s => s.refID === refId);
            if (schedule) {
                if (this.currentSchedule && this.currentSchedule.refID === refId) {
                    return;
                }
                this.currentView = 'schedule-detail';
                this.currentSchedule = schedule;
                this.$nextTick(() => {
                    if (this.currentSchedule.currentBatch) {
                        const batchElement = document.getElementById(`batch-${this.currentSchedule.currentBatch.refID}`);
                        if (batchElement) {
                            batchElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    }
                });
            }
        },

        newSchedule() {
            this.performAction(`/automation/new-schedule?job=${this.selectedJobType}`, 'Failed to start new schedule', 'new_schedule');
        },

        cleanupSchedules() {
            this.performAction('/automation/cleanup-schedules', 'Failed to cleanup schedules', 'cleanup_schedules');
        },

        pauseSchedule() {
            if (!this.currentSchedule) return;
            this.performAction(`/automation/schedule/pause/${this.currentSchedule.refID}`, 'Failed to pause schedule', 'pause_schedule')
                .then(() => {
                    this.updateCurrentSchedule();
                })
                .catch(() => {});
        },

        resumeSchedule() {
            if (!this.currentSchedule) return;
            this.performAction(`/automation/schedule/resume/${this.currentSchedule.refID}`, 'Failed to resume schedule', 'resume_schedule')
                .then(() => {
                    this.updateCurrentSchedule();
                })
                .catch(() => {});
        },

        cancelSchedule() {
            if (!this.currentSchedule) return;
            this.performAction(`/automation/schedule/cancel/${this.currentSchedule.refID}`, 'Failed to cancel schedule', 'cancel_schedule')
                .then(() => {
                    this.updateCurrentSchedule();
                })
                .catch(() => {});
        },

        updateCurrentSchedule() {
            if (this.currentSchedule) {
                const updatedSchedule = this.schedules.find(s => s.refID === this.currentSchedule.refID);
                if (updatedSchedule) {
                    this.currentSchedule = { ...updatedSchedule };
                }
            }
        },

        async performAction(endpoint, errorMessage, actionKey, options = {}) {
            if (actionKey) this.actionLoading[actionKey] = true;
            return fetch(endpoint, options)
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || errorMessage);
                        });
                    }
                    this.showToast('Action performed successfully.', 'success');
                    return this.refreshData();
                })
                .catch(error => {
                    this.showToast(error.message, 'error');
                    console.error(errorMessage, error);
                    throw error;
                })
                .finally(() => {
                    if (actionKey) this.actionLoading[actionKey] = false;
                });
        },

        showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        },

        formatDate(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleString('sv-SE', { timeZone: 'Asia/Shanghai' }).replace(' ', 'T').slice(0, 19).replace('T', ' ');
        },

        formatTime(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleTimeString('sv-SE', { timeZone: 'Asia/Shanghai' });
        },

        getProjectStatus(projectId) {
            const project = this.projects.get(projectId);
            if (!project) return 'UNKNOWN';
            return project.status.toUpperCase();
        },

        getProjectStatusClass(projectId) {
            const project = this.projects.get(projectId);
            if (!project) return 'unknown';
            let statusClass = project.status.toLowerCase();
            if (statusClass === 'running' && project.statusComment === 'idle') {
                statusClass += ' idle';
            }
            if (statusClass === 'stopped') {
                if (project.statusComment === 'risk') {
                    return 'stopped';
                } else {
                    return 'stopped-norisk';
                }
            }
            return statusClass;
        },

        getProjectStatusComment(projectId) {
            const project = this.projects.get(projectId);
            if (!project || !project.statusComment) return '';
            return project.statusComment;
        },

        getAgent(projectId, role) {
            const project = this.projects.get(projectId);
            if (!project) return null;
            return project.agents.find(a => a.role === role);
        },

        getAgentAlias(projectId, role) {
            const agent = this.getAgent(projectId, role);
            if (agent && agent.settings && agent.settings.alias) {
                return `[${agent.settings.alias}]`;
            }
            return '';
        },

        getAgentAliasByUserID(projectId, userId) {
            const project = this.projects.get(projectId);
            if (!project) return '';
            const agent = project.agents.find(a => a.userId === userId);
            if (agent && agent.settings && agent.settings.alias) {
                return `[${agent.settings.alias}]`;
            }
            return '';
        },

        getAgentUpdateTimeInfo(projectId, role) {
            const agent = this.getAgent(projectId, role);
            if (!agent || !agent.createTime) return { time: 'N/A', isStale: true };

            const updateTime = new Date(agent.createTime);
            const now = new Date();
            const todayUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
            
            const updateTimeUTC = new Date(Date.UTC(updateTime.getUTCFullYear(), updateTime.getUTCMonth(), updateTime.getUTCDate()));

            const isStale = updateTimeUTC.getTime() < todayUTC.getTime();

            return {
                time: updateTime.toLocaleTimeString('sv-SE', { timeZone: 'Asia/Shanghai' }),
                isStale: isStale,
            };
        },

        getAgentUpdateTime(projectId, role) {
            return this.getAgentUpdateTimeInfo(projectId, role).time;
        },

        getAgentUpdateTimeTooltip(projectId, role) {
            const agent = this.getAgent(projectId, role);
            if (!agent || !agent.createTime) return '';
            return new Date(agent.createTime).toLocaleString('sv-SE', { timeZone: 'Asia/Shanghai' }).replace(' ', 'T').slice(0, 19).replace('T', ' ');
        },

        getAgentStatsTime(projectId, role, isTooltip) {
            const info = this.getAgentStatsTimeInfo(projectId, role);
            if (isTooltip) {
                return info.fullTime;
            }
            return info.time;
        },

        getAgentStatsTimeInfo(projectId, role) {
            const agent = this.getAgent(projectId, role);
            if (!agent || !agent.stats || !agent.stats.createTime) return { time: 'N/A', isStale: true, fullTime: 'N/A' };

            const updateTime = new Date(agent.stats.createTime);
            const now = new Date();
            const todayUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
            const updateTimeUTC = new Date(Date.UTC(updateTime.getUTCFullYear(), updateTime.getUTCMonth(), updateTime.getUTCDate()));
            const isStale = updateTimeUTC.getTime() < todayUTC.getTime();

            return {
                time: updateTime.toLocaleTimeString('sv-SE', { timeZone: 'Asia/Shanghai' }),
                isStale: isStale,
                fullTime: updateTime.toLocaleString('sv-SE', { timeZone: 'Asia/Shanghai' }).replace(' ', 'T').slice(0, 19).replace('T', ' ')
            };
        },

        getProjectStatusTooltip(projectId) {
            const project = this.projects.get(projectId);
            if (!project) return '';
            let tooltip = project.statusComment || '';
            if (project.statusUpdateTime) {
                tooltip += ` (Updated: ${this.formatDate(project.statusUpdateTime)})`;
            }
            return tooltip.trim();
        },

        getProjectDailyVolume(projectId) {
            const project = this.projects.get(projectId);
            if (!project || !project.todayPerformance) return 'N/A';
            return project.todayPerformance.volume.toFixed(0);
        },

        getProjectTradeCount(projectId) {
            const project = this.projects.get(projectId);
            if (!project || !project.todayPerformance) return 0;
            return project.todayPerformance.trades || 0;
        },

        openBatchChromes(batch, button) {
            this.batchLoading[batch.refID] = true;
            const projectIds = batch.items.map(item => item.projectID).join(',');
            this.performAction(`/start-chrome?project_ids=${projectIds}`, 'Failed to open chromes')
                .catch(() => {})
                .finally(() => {
                    this.batchLoading[batch.refID] = false;
                });
        },

        closeBatchChromes(batch, button) {
            this.batchLoading[batch.refID] = true;
            const projectIds = batch.items.map(item => item.projectID).join(',');
            this.performAction(`/close-chrome?project_ids=${projectIds}`, 'Failed to close chromes')
                .catch(() => {
                    // The error is already handled and displayed by performAction.
                    // This catch block is necessary to prevent an "uncaught promise" error in the console.
                })
                .finally(() => {
                    this.batchLoading[batch.refID] = false;
                });
        },

        refreshBatchChromes(batch, button) {
            this.batchLoading[batch.refID] = true;
            const promises = batch.items.map(item => {
                const project = this.projects.get(item.projectID);
                if (!project) return Promise.resolve();
                const masterAgent = project.agents.find(a => a.role === 'master');
                if (!masterAgent) return Promise.resolve();
                return this.performAction(`/reload-chrome/${item.projectID}/${masterAgent.userId}`, `Failed to refresh chrome for ${item.projectID}`);
            });
            Promise.all(promises)
                .catch(() => {})
                .finally(() => {
                    this.batchLoading[batch.refID] = false;
                });
        },

        navigateToTrade(batch, button) {
            this.batchLoading[batch.refID] = true;
            const projectIds = batch.items.map(item => item.projectID).join(',');
            this.performAction(`/server-request/add`, 'Failed to navigate to trade', null, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ projectIds: projectIds, userIds: '', requestType: 'navigate_to_trade' })
            })
            .catch(() => {})
            .finally(() => {
                this.batchLoading[batch.refID] = false;
            });
        },

        async activateAgentChrome(projectId, role) {
            const agent = this.getAgent(projectId, role);
            if (!agent) {
                this.showToast(`Agent with role '${role}' not found for project ${projectId}.`, 'error');
                return;
            }
            await this.performAction(`/activate-chrome/${projectId}/${agent.userId}`, `Failed to activate Chrome for ${role}`, `activate_chrome_${projectId}_${role}`);
        },

        async activateAndRequestStats(projectId, role) {
            const agent = this.getAgent(projectId, role);
            if (!agent) {
                this.showToast(`Agent with role '${role}' not found for project ${projectId}.`, 'error');
                return;
            }
            const actionKey = `activate_and_request_stats_${projectId}_${role}`;
            this.actionLoading[actionKey] = true;
            try {
                await this.performAction(`/activate-chrome/${projectId}/${agent.userId}`, `Failed to activate Chrome for ${role}`);
                await this.performAction(`/server-request/add`, 'Failed to request stats update', null, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ projectIds: projectId, userIds: agent.userId, requestType: 'update_stats' })
                });
            } catch (error) {
            } finally {
                this.actionLoading[actionKey] = false;
            }
        },

        isActiveSchedule(schedule) {
            return schedule.startTime && !schedule.endTime && !schedule.cancelTime;
        },

        goToActiveSchedule() {
            const activeSchedule = this.schedules.find(s => this.isActiveSchedule(s));
            if (activeSchedule) {
                if (this.isDateCollapsed(activeSchedule.date)) {
                    this.toggleDate(activeSchedule.date);
                }
                this.showScheduleDetail(activeSchedule.refID);
            }
        },

        shortenAddress(address) {
            if (!address) return '';
            return address.slice(0, 6) + '...' + address.slice(-4);
        },

        copyToClipboard(text, label = 'Text') {
            navigator.clipboard.writeText(text).then(() => {
                this.showToast(`${label} copied to clipboard.`, 'success');
            }).catch(err => {
                this.showToast(`Failed to copy ${label}.`, 'error');
                console.error(`Failed to copy ${label}:`, err);
            });
        },

        formatDuration(nanoseconds) {
            if (!nanoseconds) return '0s';
            const seconds = Math.floor(nanoseconds / 1e9);
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const remainingSeconds = seconds % 60;
            let duration = '';
            if (hours > 0) duration += `${hours}h `;
            if (minutes > 0) duration += `${minutes}m `;
            if (remainingSeconds > 0 || duration === '') duration += `${remainingSeconds}s`;
            return duration.trim();
        },

        goToOverviewForBatch(batch) {
            if (!batch || !batch.items || batch.items.length === 0) {
                this.showToast('No projects in this batch.', 'info');
                return;
            }
            const projectIds = batch.items.map(item => item.projectID).join(',');
            window.open(`/?overview=${projectIds}`, '_blank');
        },

        goToProject(projectId) {
            window.open(`/?overview=${projectId}`, '_blank');
        },

        get groupedSchedules() {
            return this.schedules.reduce((groups, schedule) => {
                const date = schedule.date;
                if (!groups[date]) {
                    groups[date] = [];
                }
                groups[date].push(schedule);
                return groups;
            }, {});
        },

        toggleDate(date) {
            this.collapsedDates[date] = !this.collapsedDates[date];
        },

        isDateCollapsed(date) {
            return this.collapsedDates[date];
        },

        getJobAbbreviation(job) {
            if (!job) return 'DEF';
            switch (job) {
                case 'upgrade_extension': return 'UPG';
                case 'update_stats': return 'STA';
                default: return 'DEF';
            }
        },

        getJobDisplayName(job) {
            if (!job) return 'Default';
            switch (job) {
                case 'upgrade_extension': return 'Upgrade Extension';
                case 'update_stats': return 'Update Stats';
                default: return 'Default';
            }
        },

        toggleErrorVisibility(batchRefID) {
            if (this.expandedErrors.has(batchRefID)) {
                this.expandedErrors.delete(batchRefID);
            } else {
                this.expandedErrors.add(batchRefID);
            }
        },

        getScheduleStatusBadge(schedule) {
            if (schedule.cancelTime) return '<span class="status-badge canceled">CANCELED</span>';
            if (schedule.endTime) return '<span class="status-badge done">DONE</span>';
            if (schedule.pauseTime) return '<span class="status-badge paused">PAUSED</span>';
            if (schedule.startTime) return '<span class="status-badge running">RUNNING</span>';
            return '<span class="status-badge new">NEW</span>';
        },

        getBatchProgress(batch) {
            const totalItems = batch.items.length;
            if (totalItems === 0) return { volume: 0, status: 0 };
            const doneCount = batch.items.filter(item => ['success', 'error', 'prepare_failed', 'timeout'].includes(item.status)).length;
            const statusProgress = (doneCount / totalItems) * 100;
            if (!this.currentSchedule || !this.currentSchedule.dailyVolumeLimit) return { volume: 0, status: statusProgress };
            const targetVolume = this.currentSchedule.dailyVolumeLimit;
            let totalVolume = 0;
            batch.items.forEach(item => {
                if (item.dailyPerformance) totalVolume += item.dailyPerformance.volume;
            });
            const volumeProgress = (totalVolume / (targetVolume * totalItems)) * 100;
            return { volume: Math.min(volumeProgress, 100), status: statusProgress };
        },

        getVolumeProgress(batch) {
            if (!this.currentSchedule || !this.currentSchedule.dailyVolumeLimit || !this.projects) return 'N/A';
            const targetVolume = this.currentSchedule.dailyVolumeLimit;
            let totalVolume = 0;
            batch.items.forEach(item => {
                if (item.dailyPerformance) totalVolume += item.dailyPerformance.volume;
            });
            return `${totalVolume.toFixed(0)} / ${(targetVolume * batch.items.length).toFixed(0)}`;
        },

        getStatusProgress(batch) {
            const total = batch.items.length;
            if (total === 0) return '0/0';
            const done = batch.items.filter(item => ['success', 'error', 'prepare_failed', 'timeout'].includes(item.status)).length;
            return `${done} / ${total}`;
        },

        getBatchProgressTooltip(batch) {
            const total = batch.items.length;
            if (total === 0) return '';
            const counts = batch.items.reduce((acc, item) => {
                acc[item.status] = (acc[item.status] || 0) + 1;
                return acc;
            }, {});
            return Object.entries(counts).map(([status, count]) => `${status.charAt(0).toUpperCase() + status.slice(1)}: ${count}`).join(', ');
        },

        getBatchSummary(batch) {
            if (!batch || !batch.items || batch.items.length === 0) return { totalVolume: '0', totalCost: '0.00', costRatio: '0.0000' };
            let totalVolume = 0;
            let totalCost = 0;
            for (const item of batch.items) {
                if (item.dailyPerformance) {
                    totalVolume += item.dailyPerformance.volume || 0;
                    totalCost += item.dailyPerformance.cost || 0;
                }
            }
            const costRatio = totalVolume > 0 ? (totalCost / totalVolume) * 100 : 0;
            return { totalVolume: totalVolume.toFixed(0), totalCost: totalCost.toFixed(2), costRatio: costRatio.toFixed(4) };
        },

        getScheduleSummary() {
            if (!this.currentSchedule || !this.currentSchedule.batches || this.currentSchedule.batches.length === 0) return { totalVolume: '0', totalCost: '0.00', costRatio: '0.0000' };
            let totalVolume = 0;
            let totalCost = 0;
            for (const batch of this.currentSchedule.batches) {
                for (const item of batch.items) {
                    if (item.dailyPerformance) {
                        totalVolume += item.dailyPerformance.volume || 0;
                        totalCost += item.dailyPerformance.cost || 0;
                    }
                }
            }
            const costRatio = totalVolume > 0 ? (totalCost / totalVolume) * 100 : 0;
            return { totalVolume: totalVolume.toFixed(0), totalCost: totalCost.toFixed(2), costRatio: costRatio.toFixed(4) };
        },

        isLoading(actionKey) {
            return this.actionLoading[actionKey];
        },

        async fetchGlobalOptions() {
            try {
                const response = await fetch('/health');
                if (!response.ok) throw new Error('Failed to fetch options');
                const data = await response.json();
                this.serverVersion = data.version;
                this.options = data.options || {};
                this.copierID = this.options?.id || window.location.hostname || 'copier';
            } catch (error) {
                console.error('Error fetching options:', error);
                this.options = {};
                this.copierID = window.location.hostname || 'copier';
            }
        },

        openSettings(projectId, role) {
            const project = this.projects.get(projectId);
            if (!project) {
                console.error(`Project with ID ${projectId} not found.`);
                return;
            }
            const agent = project.agents.find(a => a.role === role);
            if (!agent) {
                console.error(`Agent with role ${role} not found for project ${projectId}.`);
                return;
            }
            const agentWithData = { ...agent, projectId: projectId };
            this.fetchSettings(agentWithData);
        },

        async toggleProjectStatus(projectId, currentStatus) {
            const project = this.projects.get(projectId);
            if (!project) {
                console.error(`Project with ID ${projectId} not found.`);
                this.showToast(`Project ${projectId} not found. Cannot change status.`, 'error');
                return;
            }
            const newStatus = currentStatus.toLowerCase() === 'running' ? 'stopped' : 'running';
            if (confirm(`Are you sure you want to ${newStatus === 'running' ? 'resume' : 'pause'} project ${projectId}?`)) {
                await this.performAction('/update-status', `Failed to toggle status for project ${projectId}`, `toggle_status_${projectId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        projectIds: projectId,
                        status: newStatus,
                        comment: 'console'
                    })
                });
            }
        },

        async fetchSettings(agent) {
            this.showSettings = true;
            this.settingsLoading = true;
            this.settingsError = null;
            this.selectedProjectForSettings = agent.projectId;
            this.selectedAgentForSettings = agent.userId;
            this.selectedAgentRoleForSettings = agent.role.toUpperCase();
            try {
                const { projectId, ...settingsWithoutProjectId } = agent.settings;
                this.currentSettings = {
                    ...this.currentSettings,
                    ...settingsWithoutProjectId,
                    stopLossBuffer: settingsWithoutProjectId.stopLossBuffer
                };
            } catch (error) {
                console.error('Error fetching settings:', error);
                this.settingsError = error.message;
            } finally {
                this.settingsLoading = false;
            }
        },

        async saveSettings() {
            try {
                const coolingHour = parseFloat(this.currentSettings.coolingHour);
                if (isNaN(coolingHour) || (coolingHour < -0.5 && coolingHour !== 0) || (coolingHour > 0 && coolingHour < 0.01) || coolingHour > 48.0) {
                    this.settingsError = "Invalid Cooling Hour value.";
                    return;
                }
                if (this.selectedAgentRoleForSettings === 'MASTER') {
                    const stopLossBuffer = parseFloat(this.currentSettings.stopLossBuffer);
                    if (isNaN(stopLossBuffer) || stopLossBuffer < 0 || stopLossBuffer > 0.05) {
                        this.settingsError = "Stop Loss Buffer must be between 0 and 0.05";
                        return;
                    }
                }
                const numericSettings = {
                    ...this.currentSettings,
                    copyPercentage: parseInt(this.currentSettings.copyPercentage) || 0,
                    minMargin: parseFloat(this.currentSettings.minMargin) || 0,
                    defaultSize: parseFloat(this.currentSettings.defaultSize) || 0,
                    coolingHour: coolingHour,
                    refreshInterval: parseInt(this.currentSettings.refreshInterval) || 0,
                    stopLossBuffer: parseFloat(this.currentSettings.stopLossBuffer),
                    longRatio: parseFloat(this.currentSettings.longRatio) || 0.5,
                    version: parseInt(this.currentSettings.version) || 0,
                    manualMode: Boolean(this.currentSettings.manualMode)
                };
                const response = await fetch(`/update-settings/${this.selectedProjectForSettings}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userId: this.selectedAgentForSettings,
                        settings: numericSettings
                    })
                });
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                this.showSettings = false;
                this.refreshData();
            } catch (error) {
                console.error('Error saving settings:', error);
                this.settingsError = error.message;
            }
        }
    };
}

function positionChangesDialog() {
    return {
        positionChanges: [],
        showPositionChanges: false,
        masterPositionChanges: [],
        copierPositionChanges: [],
        positionChangesLoading: false,
        positionChangesError: null,
        selectedProject: null,
        selectedAgent: null,
        showMasterAndCopier: false,
        masterAgentInfo: null,
        copierAgentInfo: null,
        singleAgentInfo: null,

        init() {
            console.log('Position Changes Dialog initialized for automation');
        },

        async fetchPositionChanges(projectId, userId, showMasterAndCopier = false) {
            this.showPositionChanges = true;
            this.positionChangesLoading = true;
            this.positionChangesError = null;
            this.selectedProject = projectId;
            this.selectedAgent = userId;
            this.showMasterAndCopier = showMasterAndCopier;
            this.singleAgentInfo = null;

            const dialogContent = this.$refs.positionChangesDialog.querySelector('.dialog-content');
            if (dialogContent) {
                if (!showMasterAndCopier) {
                    dialogContent.classList.add('narrow');
                } else {
                    dialogContent.classList.remove('narrow');
                }
            }

            try {
                if (showMasterAndCopier) {
                    const project = this.projects.get(projectId);
                    if (!project) throw new Error('Project not found');
                    const masterAgent = project.agents.find(a => a.role === 'master');
                    const copierAgent = project.agents.find(a => a.role === 'copier');
                    if (!masterAgent) throw new Error('Master agent not found');
                    this.masterAgentInfo = { alias: masterAgent.settings?.alias || '', address: masterAgent.userId ? masterAgent.userId.slice(0, 6) + '...' + masterAgent.userId.slice(-4) : '' };
                    this.copierAgentInfo = copierAgent ? { alias: copierAgent.settings?.alias || '', address: copierAgent.userId ? copierAgent.userId.slice(0, 6) + '...' + copierAgent.userId.slice(-4) : '' } : null;
                    const [masterChanges, copierChanges] = await Promise.all([
                        this.fetchAgentPositionChanges(projectId, masterAgent.userId),
                        copierAgent ? this.fetchAgentPositionChanges(projectId, copierAgent.userId) : Promise.resolve([])
                    ]);
                    this.masterPositionChanges = masterChanges;
                    this.copierPositionChanges = copierChanges;
                } else {
                    const project = this.projects.get(projectId);
                    if (project) {
                        const agent = project.agents.find(a => a.userId === userId);
                        if (agent) {
                            this.singleAgentInfo = { role: agent.role, alias: agent.settings?.alias || '', address: agent.userId ? agent.userId.slice(0, 6) + '...' + agent.userId.slice(-4) : '' };
                        }
                    }
                    this.positionChanges = await this.fetchAgentPositionChanges(projectId, userId);
                }
            } catch (error) {
                console.error('Error fetching position changes:', error);
                this.positionChangesError = error.message;
            } finally {
                this.positionChangesLoading = false;
            }
        },

        async fetchAgentPositionChanges(projectId, userId) {
            const response = await fetch(`/changes/${projectId}?user_id=${userId}&limit=100`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();
            return data.changes || [];
        },

        formatPositionChange(change) {
            if (!change) return '';
            const date = new Date(change.createTime);
            const timeStr = date.toLocaleString('sv-SE', { timeZone: 'Asia/Singapore', hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace('.', ' ');
            const fullTimeStr = date.toLocaleString('sv-SE', { timeZone: 'Asia/Singapore', year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace('.', ' ');
            const side = change.side.toUpperCase();
            const sideClass = change.side.toLowerCase();
            return `<div class="position-change-item ${sideClass}"><span class="position-change-time" title="${fullTimeStr}">${timeStr}</span><span class="position-change-symbol">${change.symbol}</span><span class="position-change-side ${sideClass}">${side}</span><span class="position-change-size ${sideClass}">${parseFloat(change.size).toFixed(5)}</span></div>`;
        }
    };
}

document.addEventListener('alpine:init', () => {
    Alpine.data('automationApp', automationApp);
    Alpine.data('positionChangesDialog', positionChangesDialog);
});


function settingsDialog() {
    return {
        showSettings: false,
        settingsLoading: false,
        settingsError: null,
        selectedProject: null,
        selectedAgent: null,
        selectedAgentRole: '',
        settings: {
            alias: '',
            defaultSymbol: '',
            copyFromId: '',
            copyPercentage: 0,
            minMargin: 0,
            defaultSize: 0,
            coolingHour: 0,
            refreshInterval: 0,
            debug: false,
            manualMode: false,
            stopLossBuffer: 0.0,
            longRatio: 0.5,
            version: 0
        },

        async fetchSettings(agent) {
            this.showSettings = true;
            this.settingsLoading = true;
            this.settingsError = null;
            this.selectedProject = agent.projectId;
            this.selectedAgent = agent.userId;
            this.selectedAgentRole = agent.role.toUpperCase();

            try {
                // Set the agent role
                this.selectedAgentRole = agent.role.toUpperCase();

                // Update settings with current values, excluding projectId
                const { projectId, ...settingsWithoutProjectId } = agent.settings;
                this.settings = {
                    ...this.settings,  // Keep default values
                    ...settingsWithoutProjectId,  // Override with agent settings
                    stopLossBuffer: settingsWithoutProjectId.stopLossBuffer  // Ensure stopLossBuffer has a default
                };
            } catch (error) {
                console.error('Error fetching settings:', error);
                this.settingsError = error.message;
            } finally {
                this.settingsLoading = false;
            }
        },

        async saveSettings() {
            try {
                // Validate coolingHour range
                const coolingHour = parseFloat(this.settings.coolingHour);
                if (isNaN(coolingHour)) {
                    this.settingsError = "Cooling hour must be a valid number";
                    return;
                }
                if (coolingHour < 0) {
                    // For exact hour mode, only allow -0.5 to 0
                    if (coolingHour < -0.5) {
                        this.settingsError = "For exact hour mode, cooling must be between -0.5 and 0";
                        return;
                    }
                } else {
                    // For interval mode, allow 0.01 to 48.0
                    if (coolingHour < 0.01 || coolingHour > 48.0) {
                        this.settingsError = "For interval mode, cooling must be between 0.01 and 48.0 hours";
                        return;
                    }
                }

                // Validate stopLossBuffer range for master agents
                if (this.selectedAgentRole === 'MASTER') {
                    const stopLossBuffer = parseFloat(this.settings.stopLossBuffer);
                    if (isNaN(stopLossBuffer) || stopLossBuffer < 0 || stopLossBuffer > 0.05) {
                        this.settingsError = "Stop Loss Buffer must be between 0 and 0.05";
                        return;
                    }
                }

                // Convert numeric fields to proper types
                const numericSettings = {
                    ...this.settings,
                    copyPercentage: parseInt(this.settings.copyPercentage) || 0,
                    minMargin: parseFloat(this.settings.minMargin) || 0,
                    defaultSize: parseFloat(this.settings.defaultSize) || 0,
                    coolingHour: coolingHour,
                    refreshInterval: parseInt(this.settings.refreshInterval) || 0,
                    stopLossBuffer: parseFloat(this.settings.stopLossBuffer),
                    longRatio: parseFloat(this.settings.longRatio) || 0.5,
                    version: parseInt(this.settings.version) || 0,
                    manualMode: Boolean(this.settings.manualMode)
                };

                const response = await fetch(`/update-settings/${this.selectedProject}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userId: this.selectedAgent,
                        settings: numericSettings
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                // Close dialog and refresh data
                this.showSettings = false;
                this.$dispatch('refresh-data');
            } catch (error) {
                console.error('Error saving settings:', error);
                this.settingsError = error.message;
            }
        },

        openSettings(projectId, role) {
            const project = this.projects.get(projectId);
            if (!project) {
                console.error(`Project with ID ${projectId} not found.`);
                return;
            }
            
            const agent = project.agents.find(a => a.role === role);
            if (!agent) {
                console.error(`Agent with role ${role} not found for project ${projectId}.`);
                return;
            }
            
            const agentWithData = { ...agent, projectId: projectId };
            this.$dispatch('show-settings', { agent: agentWithData });
        },

        async toggleProjectStatus(projectId, currentStatus) {
            const project = this.projects.get(projectId);
            if (!project) {
                console.error(`Project with ID ${projectId} not found.`);
                this.showToast(`Project ${projectId} not found. Cannot change status.`, 'error');
                return;
            }
            const newStatus = currentStatus.toLowerCase() === 'running' ? 'stopped' : 'running';
            if (confirm(`Are you sure you want to ${newStatus === 'running' ? 'resume' : 'pause'} project ${projectId}?`)) {
                await this.performAction('/update-status', `Failed to toggle status for project ${projectId}`, `toggle_status_${projectId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        projectIds: projectId,
                        status: newStatus,
                        comment: 'console'
                    })
                });
            }
        }
    };
}

function settingsDialog() {
    return {
        positionChanges: [],
        showPositionChanges: false,
        masterPositionChanges: [],
        copierPositionChanges: [],
        positionChangesLoading: false,
        positionChangesError: null,
        selectedProject: null,
        selectedAgent: null,
        showMasterAndCopier: false,
        masterAgentInfo: null,
        copierAgentInfo: null,
        singleAgentInfo: null,

        init() {
            console.log('Position Changes Dialog initialized for automation');
        },

        async fetchPositionChanges(projectId, userId, showMasterAndCopier = false) {
            this.showPositionChanges = true;
            this.positionChangesLoading = true;
            this.positionChangesError = null;
            this.selectedProject = projectId;
            this.selectedAgent = userId;
            this.showMasterAndCopier = showMasterAndCopier;
            this.singleAgentInfo = null;

            const dialogContent = this.$refs.positionChangesDialog.querySelector('.dialog-content');
            if (dialogContent) {
                if (!showMasterAndCopier) {
                    dialogContent.classList.add('narrow');
                } else {
                    dialogContent.classList.remove('narrow');
                }
            }

            try {
                if (showMasterAndCopier) {
                    const project = this.projects.get(projectId);
                    if (!project) {
                        throw new Error('Project not found');
                    }

                    const masterAgent = project.agents.find(a => a.role === 'master');
                    const copierAgent = project.agents.find(a => a.role === 'copier');

                    if (!masterAgent) {
                        throw new Error('Master agent not found');
                    }

                    this.masterAgentInfo = {
                        alias: masterAgent.settings?.alias || '',
                        address: masterAgent.userId ? masterAgent.userId.slice(0, 6) + '...' + masterAgent.userId.slice(-4) : ''
                    };
                    this.copierAgentInfo = copierAgent ? {
                        alias: copierAgent.settings?.alias || '',
                        address: copierAgent.userId ? copierAgent.userId.slice(0, 6) + '...' + copierAgent.userId.slice(-4) : ''
                    } : null;

                    const [masterChanges, copierChanges] = await Promise.all([
                        this.fetchAgentPositionChanges(projectId, masterAgent.userId),
                        copierAgent ? this.fetchAgentPositionChanges(projectId, copierAgent.userId) : Promise.resolve([])
                    ]);

                    this.masterPositionChanges = masterChanges;
                    this.copierPositionChanges = copierChanges;
                } else {
                    const project = this.projects.get(projectId);
                    if (project) {
                        const agent = project.agents.find(a => a.userId === userId);
                        if (agent) {
                            this.singleAgentInfo = {
                                role: agent.role,
                                alias: agent.settings?.alias || '',
                                address: agent.userId ? agent.userId.slice(0, 6) + '...' + agent.userId.slice(-4) : ''
                            };
                        }
                    }
                    this.positionChanges = await this.fetchAgentPositionChanges(projectId, userId);
                }
            } catch (error) {
                console.error('Error fetching position changes:', error);
                this.positionChangesError = error.message;
            } finally {
                this.positionChangesLoading = false;
            }
        },

        async fetchAgentPositionChanges(projectId, userId) {
            const response = await fetch(`/changes/${projectId}?user_id=${userId}&limit=100`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            return data.changes || [];
        },

        formatPositionChange(change) {
            if (!change) return '';
            const date = new Date(change.createTime);
            const timeStr = date.toLocaleString('sv-SE', { 
                timeZone: 'Asia/Singapore',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }).replace('.', ' ');
            const fullTimeStr = date.toLocaleString('sv-SE', { 
                timeZone: 'Asia/Singapore',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }).replace('.', ' ');
            const side = change.side.toUpperCase();
            const sideClass = change.side.toLowerCase();

            return `
                <div class="position-change-item ${sideClass}">
                    <span class="position-change-time" title="${fullTimeStr}">${timeStr}</span>
                    <span class="position-change-symbol">${change.symbol}</span>
                    <span class="position-change-side ${sideClass}">${side}</span>
                    <span class="position-change-size ${sideClass}">${parseFloat(change.size).toFixed(5)}</span>
                </div>
            `;
        }
    };
}

document.addEventListener('alpine:init', () => {
    Alpine.data('automationApp', automationApp);
    Alpine.data('positionChangesDialog', positionChangesDialog);
    Alpine.data('settingsDialog', settingsDialog);
});

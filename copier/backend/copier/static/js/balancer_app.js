function balancerApp() {
    return {
        currentView: 'make-plans',
        plans: [],
        currentPlan: null,
        projects: [],
        selectedPlatform: '',
        platforms: [],
        platformInfos: {}, // Store platform info objects
        selectedCoin: '',
        selectedStage: '',
        amount: '',
        amountDirective: 'exact',
        waitMinutes: 0,
        selectedProjects: [],
        selectedWalletId: '',
        selectedAddresses: [],
        selectedExchangeId: localStorage.getItem('selectedExchangeId') || '',
        wallets: {},
        exchangeIds: [],
        amountRandom: false,
        refreshInterval: null,
        showNoteDialog: false,
        walletNote: '',
        showDepositAddressDialog: false,
        depositAddress: '',
        options: {},
        serverVersion: null,
        projectFilter: '',
        addressFilter: '',

        init() {
            console.log('Initializing balancer app...');
            this.loadPlans();
            this.loadProjects();
            this.loadWallets();
            this.fetchGlobalOptions().then(() => {
                document.title = this.options.id ? this.options.id + ' balancer' : 'balancer';
            });
            // Start auto-refresh every 5 seconds
            this.startAutoRefresh();
            
            // Watch for changes in selectedExchangeId to persist to localStorage
            this.$watch('selectedExchangeId', (value) => {
                if (value) {
                    localStorage.setItem('selectedExchangeId', value);
                }
            });

            // Watch for changes in selectedPlatform to update selectedCoin
            this.$watch('selectedPlatform', (value) => {
                if (value && this.platformInfos[value]) {
                    const platformInfo = this.platformInfos[value];
                    if (platformInfo.supportedCoins && platformInfo.supportedCoins.length > 0) {
                        this.selectedCoin = platformInfo.coin || platformInfo.supportedCoins[0];
                    } else {
                        this.selectedCoin = '';
                    }
                } else {
                    this.selectedCoin = '';
                }
            });
        },

        async fetchGlobalOptions() {
            try {
                const response = await fetch('/health');
                if (!response.ok) {
                    throw new Error('Failed to fetch options');
                }
                const data = await response.json();
                this.serverVersion = data.version;
                this.options = data.options;
            } catch (error) {
                console.error('Error fetching options:', error);
            }
        },

        startAutoRefresh() {
            // Clear any existing interval
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }
            // Set new interval
            this.refreshInterval = setInterval(() => {
                this.refreshData();
            }, 5000); // Refresh every 5 seconds
        },

        async refreshData() {
            try {
                const response = await fetch('/balancer/plans');
                if (!response.ok) {
                    console.error('Failed to refresh plans');
                    return;
                }
                const data = await response.json();
                if (data && data.plans) {
                    // Sort plans: active plans first (by createTime), then done/canceled plans
                    const newPlans = data.plans.sort((a, b) => {
                        const aDone = a.status === 'done' || a.cancelTime != null;
                        const bDone = b.status === 'done' || b.cancelTime != null;
                        if (aDone === bDone) {
                            return new Date(b.createTime) - new Date(a.createTime);
                        }
                        return aDone ? 1 : -1;
                    });

                    // Update plans array
                    this.plans = newPlans;

                    // If we're viewing a plan detail, update the current plan
                    if (this.currentView === 'plan-detail' && this.currentPlan) {
                        const updatedPlan = newPlans.find(p => p.refId === this.currentPlan.refId);
                        if (updatedPlan) {
                            this.currentPlan = updatedPlan;
                        }
                    }
                }
            } catch (error) {
                console.error('Error refreshing data:', error);
            }
        },

        formatProjectIDs(plan) {
            if (!plan || !plan.balanceItems) return '';
            
            // Get all project IDs
            const projectIDs = plan.balanceItems.map(item => item.projectId);
            
            // Group project IDs
            const groupIDs = {};
            const itemMap = {}; // Map to store item refIds
            for (const item of plan.balanceItems) {
                if (item.projectId.includes('_')) {
                    const [groupID, suffix] = item.projectId.split('_');
                    if (!groupIDs[groupID]) {
                        groupIDs[groupID] = [];
                    }
                    groupIDs[groupID].push(suffix);
                    itemMap[suffix] = item.refId;
                } else {
                    if (!groupIDs[item.projectId]) {
                        groupIDs[item.projectId] = [];
                    }
                    groupIDs[item.projectId].push(item.projectId);
                    itemMap[item.projectId] = item.refId;
                }
            }

            // Sort group IDs and format
            const groupIDsList = Object.keys(groupIDs).sort();
            const groupIDsStrList = groupIDsList.map(groupID => {
                const suffixes = groupIDs[groupID].map(suffix => {
                    const itemId = itemMap[suffix];
                    return `<a href="#" @click.prevent="scrollToItem('${itemId}')" class="project-link">${suffix}</a>`;
                });
                return `${groupID}[${suffixes.join(', ')}]`;
            });

            return groupIDsStrList.join(', ');
        },

        scrollToItem(itemId) {
            const element = document.getElementById(`item-${itemId}`);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                element.classList.add('highlight');
                setTimeout(() => element.classList.remove('highlight'), 2000);
            }
        },

        async loadPlans() {
            try {
                console.log('Loading plans...');
                const response = await fetch('/balancer/plans');
                if (!response.ok) {
                    let errorMsg = 'Failed to load plans';
                    try {
                        const data = await response.json();
                        if (data && data.error) {
                            errorMsg = data.error;
                        }
                    } catch (e) {
                        // ignore JSON parse error
                    }
                    alert(errorMsg);
                    return;
                }
                const data = await response.json();
                console.log('Plans raw response:', data);
                if (data && data.plans) {
                    console.log('First plan structure:', data.plans[0]);
                    // Sort plans: active plans first (by createTime), then done/canceled plans
                    this.plans = data.plans.sort((a, b) => {
                        const aDone = a.status === 'done' || a.cancelTime != null;
                        const bDone = b.status === 'done' || b.cancelTime != null;
                        if (aDone === bDone) {
                            return new Date(b.createTime) - new Date(a.createTime);
                        }
                        return aDone ? 1 : -1;
                    });
                    console.log('Processed plans:', this.plans);
                } else {
                    console.error('Invalid plans data structure:', data);
                    alert('Invalid plans data structure received from server');
                }
            } catch (error) {
                console.error('Failed to load plans:', error);
                alert('Failed to load plans: ' + error.message);
            }
        },

        async loadProjects() {
            try {
                console.log('Loading projects...');
                const response = await fetch('/projects');
                if (!response.ok) {
                    let errorMsg = 'Failed to load projects';
                    try {
                        const data = await response.json();
                        if (data && data.error) {
                            errorMsg = data.error;
                        }
                    } catch (e) {
                        // ignore JSON parse error
                    }
                    alert(errorMsg);
                    return;
                }
                console.log('Projects raw response:', response);
                const data = await response.json();
                console.log('Projects parsed data:', data);
                if (data && data.projects) {
                    this.projects = data.projects.map(p => p.projectId).sort();
                    console.log('Processed projects:', this.projects);
                } else {
                    console.error('Invalid projects data structure:', data);
                    alert('Invalid projects data structure received from server');
                }
            } catch (error) {
                console.error('Failed to load projects:', error);
                alert('Failed to load projects: ' + error.message);
            }
        },

        async loadWallets() {
            try {
                console.log('Loading wallets...');
                const response = await fetch('/balancer/info');
                if (!response.ok) {
                    let errorMsg = 'Failed to load wallets';
                    try {
                        const data = await response.json();
                        if (data && data.error) {
                            errorMsg = data.error;
                        }
                    } catch (e) {
                        // ignore JSON parse error
                    }
                    alert(errorMsg);
                    return;
                }
                const data = await response.json();
                if (data && data.wallets) {
                    this.wallets = data.wallets;
                    console.log('Loaded wallets:', this.wallets);
                } else {
                    console.error('Invalid wallets data structure:', data);
                    alert('Invalid wallets data structure received from server');
                }
                
                // Load exchange IDs
                if (data && data.exchanges) {
                    this.exchangeIds = data.exchanges;
                    console.log('Loaded exchange IDs:', this.exchangeIds);
                    
                    // If there's a stored selection but it's not in the current list, reset it
                    if (this.selectedExchangeId && !this.exchangeIds.includes(this.selectedExchangeId)) {
                        this.selectedExchangeId = '';
                        localStorage.removeItem('selectedExchangeId');
                    }
                    
                    // If there's no selected exchange but we have exchanges, select the first one
                    if (!this.selectedExchangeId && this.exchangeIds.length > 0) {
                        this.selectedExchangeId = this.exchangeIds[0];
                        localStorage.setItem('selectedExchangeId', this.selectedExchangeId);
                    }
                }

                // Load platforms
                if (data && data.platforms) {
                    // Store platform infos and get platform names
                    this.platformInfos = data.platforms;
                    this.platforms = Object.keys(data.platforms);
                    console.log('Loaded platforms:', this.platforms);
                    
                    // If there's no selected platform but we have platforms, select the first one
                    if (!this.selectedPlatform && this.platforms.length > 0) {
                        this.selectedPlatform = this.platforms[0];
                    }
                }
            } catch (error) {
                console.error('Failed to load wallets:', error);
                alert('Failed to load wallets: ' + error.message);
            }
        },

        getWalletAddresses() {
            if (!this.selectedWalletId || !this.wallets[this.selectedWalletId]) {
                return [];
            }
            return this.wallets[this.selectedWalletId].addresses || [];
        },

        getWalletAddressAlias(address) {
            if (!address || !this.selectedWalletId || !this.wallets[this.selectedWalletId]) {
                return null;
            }
            
            const wallet = this.wallets[this.selectedWalletId];
            const addressIndex = wallet.addresses.indexOf(address);
            if (addressIndex !== -1 && wallet.addressAliases && addressIndex < wallet.addressAliases.length) {
                return wallet.addressAliases[addressIndex];
            }
            return null;
        },

        getTxAddressAlias(address) {
            if (!address || !this.currentPlan?.balanceItems) {
                return null;
            }
            
            for (const item of this.currentPlan.balanceItems) {
                if (address === item.fromAddress) return item.fromAddressAlias;
                if (address === item.toAddress) return item.toAddressAlias;
                if (address === item.intermediateAddress) return 'Intermediate';
            }
            return null;
        },

        getAddressAlias(address) {
            if (!address) return null;

            // For transaction display
            if (this.currentPlan?.balanceItems) {
                for (const item of this.currentPlan.balanceItems) {
                    if (address === item.fromAddress) return item.fromAddressAlias;
                    if (address === item.toAddress) return item.toAddressAlias;
                    if (address === item.intermediateAddress) return 'Intermediate';
                }
            }
            
            return null;
        },

        getWalletAddressAlias(address) {
            if (!address) return null;
            
            // For wallet mode
            if (this.selectedWalletId && this.wallets[this.selectedWalletId]) {
                const wallet = this.wallets[this.selectedWalletId];
                const addressIndex = wallet.addresses.indexOf(address);
                if (addressIndex !== -1 && wallet.addressAliases && addressIndex < wallet.addressAliases.length) {
                    return wallet.addressAliases[addressIndex];
                }
            }
            
            return null;
        },

        shortenAddress(address) {
            if (!address) return '';
            return address.slice(0, 6) + '...' + address.slice(-4);
        },

        showMakePlans() {
            this.currentView = 'make-plans';
            this.currentPlan = null;
        },

        async showPlanDetail(refId) {
            console.log('Showing plan detail for:', refId);
            console.log('Available plans:', this.plans);
            this.currentView = 'plan-detail';
            const plan = this.plans.find(p => p.refId === refId);
            console.log('Found plan:', plan);
            if (plan) {
                this.currentPlan = plan;
            } else {
                console.error('Plan not found:', refId);
            }
        },

        get canMakePlan() {
            if (this.selectedWalletId) {
                return this.selectedAddresses.length > 0 && this.selectedExchangeId;
            }
            return this.selectedProjects.length > 0 && this.selectedExchangeId;
        },

        async makePlan() {
            if (!this.canMakePlan) return;

            if (this.selectedWalletId && this.selectedStage === '') {
                alert('Please select a stage when using wallet mode');
                return;
            }

            const payload = {
                platform: this.selectedPlatform,
                exchangeId: this.selectedExchangeId,
                amount: parseFloat(this.amount) || 0,
                waitSeconds: (parseInt(this.waitMinutes) || 0) * 60,
                coin: this.selectedCoin,
            };
            
            if (this.selectedWalletId) {
                payload.walletId = this.selectedWalletId;
                payload.addresses = this.selectedAddresses;
                payload.amountRandom = this.amountRandom;
                payload.amountDirective = this.amountDirective;
            } else {
                payload.projectIds = this.selectedProjects;
            }

            if (this.selectedStage) {
                payload.stages = this.selectedStage.split(',').map(stage => stage.trim());
            }

            try {
                const response = await fetch('/balancer/plan/make', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });

                if (response.ok) {
                    await this.loadPlans();
                    this.showMakePlans();
                } else {
                    let errorMsg = 'Failed to make plan';
                    try {
                        const data = await response.json();
                        if (data && data.error) {
                            errorMsg = data.error;
                        }
                    } catch (e) {
                        // ignore JSON parse error
                    }
                    alert(errorMsg);
                }
            } catch (error) {
                console.error('Error making plan:', error);
                alert('Error making plan: ' + error.message);
            }
        },

        async cancelPlan() {
            try {
                const response = await fetch(`/balancer/plan/cancel`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ planRefId: this.currentPlan.refId })
                });

                if (response.ok) {
                    await this.loadPlans();
                    this.showPlanDetail(this.currentPlan.refId);
                } else {
                    let errorMsg = 'Failed to cancel plan';
                    try {
                        const data = await response.json();
                        if (data && data.error) {
                            errorMsg = data.error;
                        }
                    } catch (e) {
                        // ignore JSON parse error
                    }
                    alert(errorMsg);
                }
            } catch (error) {
                console.error('Error canceling plan:', error);
                alert('Error canceling plan: ' + error.message);
            }
        },

        async cancelItem(itemId) {
            try {
                const response = await fetch(`/balancer/plan/item/cancel`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        planRefId: this.currentPlan.refId,
                        itemRefId: itemId
                    })
                });

                if (response.ok) {
                    await this.loadPlans();
                    this.showPlanDetail(this.currentPlan.refId);
                } else {
                    let errorMsg = 'Failed to cancel item';
                    try {
                        const data = await response.json();
                        if (data && data.error) {
                            errorMsg = data.error;
                        }
                    } catch (e) {
                        // ignore JSON parse error
                    }
                    alert(errorMsg);
                }
            } catch (error) {
                console.error('Error canceling item:', error);
                alert('Error canceling item: ' + error.message);
            }
        },

        async retryTx(txId) {
            await this.txAction(txId, 'retry');
        },

        async markTxDone(txId) {
            if (!confirm('Are you sure you want to mark this transaction as done?')) {
                return;
            }
            await this.txAction(txId, 'done');
        },

        async cancelTx(txId) {
            if (!confirm('Are you sure you want to cancel this transaction?')) {
                return;
            }
            await this.txAction(txId, 'cancel');
        },

        getTxById(txId) {
            if (!this.currentPlan?.balanceItems) return null;
            for (const item of this.currentPlan.balanceItems) {
                const tx = this.getTxArray(item).find(t => t.id === txId);
                if (tx) return tx;
            }
            return null;
        },

        async txAction(txId, action) {
            try {
                const response = await fetch(`/balancer/tx/action`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        planRefId: this.currentPlan.refId,
                        txRefId: txId,
                        action: action
                    })
                });

                if (response.ok) {
                    await this.loadPlans();
                    this.showPlanDetail(this.currentPlan.refId);
                } else {
                    let errorMsg = `Failed to ${action} transaction`;
                    try {
                        const data = await response.json();
                        if (data && data.error) {
                            errorMsg = data.error;
                        }
                    } catch (e) {
                        // ignore JSON parse error
                    }
                    alert(errorMsg);
                }
            } catch (error) {
                console.error(`Error performing ${action} on transaction:`, error);
                alert(`Error performing ${action} on transaction: ` + error.message);
            }
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleString();
        },

        getPlanStatusBadge(plan) {
            if (plan.status === 'done') {
                return '<span class="status-badge done">DONE</span>';
            }
            if (plan.cancelTime != null) {
                return '<span class="status-badge canceled">CANCEL</span>';
            }
            return '';
        },

        getTxArray(item) {
            if (!item.txs) return [];
            return [
                item.txs.platformWithdrawTx && { 
                    ...item.txs.platformWithdrawTx, 
                    type: 'Platform Withdraw',
                    fromAddressAlias: this.getAddressAlias(item.txs.platformWithdrawTx.fromAddress),
                    toAddressAlias: this.getAddressAlias(item.txs.platformWithdrawTx.toAddress)
                },
                item.txs.exchangeDepositTx && { 
                    ...item.txs.exchangeDepositTx, 
                    type: 'Exchange Deposit',
                    fromAddressAlias: this.getAddressAlias(item.txs.exchangeDepositTx.fromAddress),
                    toAddressAlias: this.getAddressAlias(item.txs.exchangeDepositTx.toAddress)
                },
                item.txs.exchangeWithdrawTx && { 
                    ...item.txs.exchangeWithdrawTx, 
                    type: 'Exchange Withdraw',
                    fromAddressAlias: this.getAddressAlias(item.txs.exchangeWithdrawTx.fromAddress),
                    toAddressAlias: this.getAddressAlias(item.txs.exchangeWithdrawTx.toAddress)
                },
                item.txs.platformDepositTx && { 
                    ...item.txs.platformDepositTx, 
                    type: 'Platform Deposit',
                    fromAddressAlias: this.getAddressAlias(item.txs.platformDepositTx.fromAddress),
                    toAddressAlias: this.getAddressAlias(item.txs.platformDepositTx.toAddress)
                }
            ].filter(Boolean);
        },

        async deletePlan() {
            try {
                const response = await fetch('/balancer/plan/delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ planRefId: this.currentPlan.refId })
                });
                if (response.ok) {
                    await this.loadPlans();
                    this.showMakePlans();
                } else {
                    let errorMsg = 'Failed to delete plan';
                    try {
                        const data = await response.json();
                        if (data && data.error) {
                            errorMsg = data.error;
                        }
                    } catch (e) {
                        // ignore JSON parse error
                    }
                    alert(errorMsg);
                }
            } catch (error) {
                console.error('Error deleting plan:', error);
                alert('Error deleting plan: ' + error.message);
            }
        },

        async processPlan() {
            try {
                const response = await fetch(`/balancer/plan/process`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ planRefId: this.currentPlan.refId })
                });

                if (response.ok) {
                    await this.loadPlans();
                    this.showPlanDetail(this.currentPlan.refId);
                } else {
                    let errorMsg = 'Failed to process plan';
                    try {
                        const data = await response.json();
                        if (data && data.error) {
                            errorMsg = data.error;
                        }
                    } catch (e) {
                        // ignore JSON parse error
                    }
                    alert(errorMsg);
                }
            } catch (error) {
                console.error('Error processing plan:', error);
                alert('Error processing plan: ' + error.message);
            }
        },

        getSupportedCoins() {
            if (!this.selectedPlatform || !this.platformInfos[this.selectedPlatform]) {
                return [];
            }
            return this.platformInfos[this.selectedPlatform].supportedCoins || [];
        },

        showWalletNote() {
            if (this.selectedWalletId && this.wallets[this.selectedWalletId]) {
                const wallet = this.wallets[this.selectedWalletId];
                this.walletNote = wallet.addressNote || '';
                this.showNoteDialog = true;
                console.log('Showing note dialog:', this.walletNote); // Debug log
                console.log('Dialog state:', this.showNoteDialog); // Debug log
            }
        },

        closeWalletNote() {
            this.showNoteDialog = false;
        },

        async showDepositAddress() {
            try {
                const response = await fetch(`/deposit-address/${this.selectedWalletId}`);
                if (!response.ok) {
                    throw new Error('Failed to fetch deposit address');
                }
                const data = await response.json();
                this.depositAddress = data.note || 'No deposit address found';
                this.showDepositAddressDialog = true;
            } catch (error) {
                console.error('Error fetching deposit address:', error);
                this.depositAddress = 'Error fetching deposit address';
                this.showDepositAddressDialog = true;
            }
        },

        closeDepositAddress() {
            this.showDepositAddressDialog = false;
        },

        // Filter functions for projects
        selectFilteredProjects() {
            if (!this.projectFilter.trim()) {
                return;
            }
            
            const filterText = this.projectFilter.toLowerCase();
            const filteredProjects = this.projects.filter(project => 
                project.toLowerCase().startsWith(filterText)
            );
            
            // Add filtered projects to selection (without duplicates)
            filteredProjects.forEach(project => {
                if (!this.selectedProjects.includes(project)) {
                    this.selectedProjects.push(project);
                }
            });
        },

        resetProjectSelection() {
            this.selectedProjects = [];
        },

        // Filter functions for addresses
        selectFilteredAddresses() {
            if (!this.addressFilter.trim()) {
                return;
            }
            
            const filterText = this.addressFilter.toLowerCase();
            const addresses = this.getWalletAddresses();
            const filteredAddresses = addresses.filter(address => {
                const alias = this.getWalletAddressAlias(address);
                const shortAddress = this.shortenAddress(address);
                return alias && alias.toLowerCase().startsWith(filterText) ||
                       shortAddress.toLowerCase().startsWith(filterText) ||
                       address.toLowerCase().startsWith(filterText);
            });
            
            // Add filtered addresses to selection (without duplicates)
            filteredAddresses.forEach(address => {
                if (!this.selectedAddresses.includes(address)) {
                    this.selectedAddresses.push(address);
                }
            });
        },

        resetAddressSelection() {
            this.selectedAddresses = [];
        }
    };
}

// Clipboard helper for Alpine
if (!window.Alpine) {
    document.addEventListener('alpine:init', () => {
        window.Alpine.magic('clipboard', () => (text) => {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text);
            } else {
                const textarea = document.createElement('textarea');
                textarea.value = text;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
            }
        });
    });
}

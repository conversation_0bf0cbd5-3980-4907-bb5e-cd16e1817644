package copier

import (
	"context"
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"sort"
	"strings"
	"sync"
	"time"

	"copier/backend/logger"
	"copier/backend/utils"

	"github.com/chromedp/chromedp"
	"github.com/gorilla/websocket"
	"github.com/mitchellh/go-homedir"
	"github.com/puzpuzpuz/xsync"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

func sendChromeCloseCommand(wsURL string) error {
	dialer := websocket.Dialer{
		HandshakeTimeout: 5 * time.Second,
	}

	conn, _, err := dialer.Dial(wsURL, nil)
	if err != nil {
		return fmt.Errorf("failed to connect to WebSocket: %v", err)
	}
	defer conn.Close()

	// Send close command
	closeMsg := `{"id":1,"method":"Browser.close"}`
	err = conn.WriteMessage(websocket.TextMessage, []byte(closeMsg))
	if err != nil {
		return fmt.Errorf("failed to send close command: %v", err)
	}

	// Wait for response
	_, _, err = conn.ReadMessage()
	if err != nil {
		// Ignore read errors as the browser might close before sending response
		return nil
	}

	return nil
}

func closeChromeWithCommand(address string) error {
	if runtime.GOOS != "windows" {
		return fmt.Errorf("only works on Windows")
	}

	// Try to find Chrome's debugging port
	cmd := exec.Command("powershell", "-Command",
		fmt.Sprintf("$chrome = Get-CimInstance Win32_Process -Filter \"name='chrome.exe'\" | Where-Object { $_.CommandLine -like '*%s*' } | Select-Object -First 1; if ($chrome) { $chrome.CommandLine -match '--remote-debugging-port=([0-9]+)' | Out-Null; $matches[1] }", address))

	port, err := cmd.Output()
	if err != nil || len(port) == 0 {
		return fmt.Errorf("failed to get debugging port, address: %s, error: %v", address, err)
	}

	// Try to gracefully shutdown using the debugging protocol
	// First get the WebSocket URL
	wsCmd := exec.Command("C:\\Windows\\System32\\curl.exe", "-s", "--noproxy", "127.0.0.1", fmt.Sprintf("http://127.0.0.1:%s/json/version", strings.TrimSpace(string(port))))
	wsOutput, err := wsCmd.Output()
	if err != nil {
		return fmt.Errorf("failed to get WebSocket URL, address: %s, error: %v", address, err)
	}

	// Extract WebSocket URL from the response
	wsURL := gjson.Get(string(wsOutput), "webSocketDebuggerUrl").String()
	if wsURL == "" {
		return fmt.Errorf("failed to get WebSocket URL, empty wsURL, address: %s", address)
	}

	// Send close command through WebSocket using Go
	if err := sendChromeCloseCommand(wsURL); err != nil {
		return fmt.Errorf("failed to send close command, address: %s, error: %v", address, err)
	}

	logger.Infof("successfully closed chrome with debugging port: %s, address: %s", string(port), address)
	return nil
}

func closeChromeWithKill(address string) error {
	if runtime.GOOS != "windows" {
		return fmt.Errorf("only works on Windows")
	}

	// Fall back to normal process stop
	cmd := exec.Command("powershell", "-Command",
		fmt.Sprintf("Get-CimInstance Win32_Process -Filter \"name='chrome.exe'\" | Where-Object { $_.CommandLine -like '*%s*' } | ForEach-Object { Stop-Process -Id $_.ProcessId }", address))

	logger.Infof("closing chrome with normal process stop, address: %s, cmd: %s", address, cmd.String())
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to close Chrome, address: %s, error: %v", address, err)
	}

	logger.Infof("successfully killed chrome process, address: %s", address)
	return nil
}

func chromeProcessExists(address string) bool {
	if runtime.GOOS != "windows" {
		return false
	}

	cmd := exec.Command("powershell", "-Command",
		fmt.Sprintf("$chrome = Get-CimInstance Win32_Process -Filter \"name='chrome.exe'\" | Where-Object { $_.CommandLine -like '*%s*' } | Select-Object -First 1; if ($chrome) { $chrome.ProcessId }", address))

	pid, err := cmd.Output()
	if err != nil || len(pid) == 0 {
		return false
	}

	result := cast.ToInt(strings.TrimSpace(string(pid)))
	return result > 0
}

var closingChromeLocks = xsync.NewMapOf[sync.Mutex]()

func closeChromeWithAddress(address string) error {
	lock, _ := closingChromeLocks.LoadOrStore(address, sync.Mutex{})
	if !lock.TryLock() {
		return fmt.Errorf("close chrome failed, already closing, address: %s", address)
	}
	defer lock.Unlock()

	if runtime.GOOS != "windows" {
		return fmt.Errorf("only works on Windows")
	}

	// Check if Chrome process exists before attempting to close
	if !chromeProcessExists(address) {
		logger.Infof("Chrome process not found for address: %s, skipping close operation", address)
		return nil
	}

	// Retry command-based close chrome at least 3 times
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		err := closeChromeWithCommand(address)
		if err == nil {
			return nil
		}

		logger.Errorf("command-based close chrome failed (attempt %d/%d), address: %s, error: %v", i+1, maxRetries, address, err)

		// Wait a bit before retrying
		if i < maxRetries-1 {
			time.Sleep(time.Millisecond * 500)
		}
	}

	// If command-based approach failed after all retries, fall back to kill-based approach
	logger.Infof("falling back to kill-based close chrome after %d failed attempts, address: %s", maxRetries, address)
	return closeChromeWithKill(address)
}

func getChromeDebugPort(address string) (string, error) {
	if runtime.GOOS != "windows" {
		return "", fmt.Errorf("only works on Windows")
	}

	cmd := exec.Command("powershell", "-Command",
		fmt.Sprintf("$chrome = Get-CimInstance Win32_Process -Filter \"name='chrome.exe'\" | Where-Object { $_.CommandLine -like '*%s*' } | Select-Object -First 1; if ($chrome) { $chrome.CommandLine -match '--remote-debugging-port=([0-9]+)' | Out-Null; $matches[1] }", address))

	port, err := cmd.Output()
	if err != nil || len(port) == 0 {
		return "", fmt.Errorf("failed to get debugging port, address: %s, error: %v", address, err)
	}

	return strings.TrimSpace(string(port)), nil
}

func getChromeDebugURL(port string) string {
	return fmt.Sprintf("http://127.0.0.1:%s", port)
}

func getChromeDPContext(ctx context.Context, address string) (context.Context, context.CancelFunc, error) {
	port, err := getChromeDebugPort(address)
	if err != nil {
		return nil, nil, err
	}

	debugURL := getChromeDebugURL(port)

	// Create ChromeDP context with our existing Chrome instance
	allocCtx, _ := chromedp.NewRemoteAllocator(ctx, debugURL)

	// Create a new context for the browser
	browserCtx, cancel := chromedp.NewContext(allocCtx, chromedp.WithLogf(logger.Infof))

	return browserCtx, cancel, nil
}

func reloadChrome(address string) error {
	if runtime.GOOS != "windows" {
		return fmt.Errorf("only works on Windows")
	}

	// Find Chrome process for this address
	cmd := exec.Command("powershell", "-Command",
		fmt.Sprintf("$chrome = Get-CimInstance Win32_Process -Filter \"name='chrome.exe'\" | Where-Object { $_.CommandLine -like '*%s*' } | Select-Object -First 1; if ($chrome) { $chrome.ProcessId }", address))

	pidOutput, err := cmd.Output()
	if err != nil || len(pidOutput) == 0 {
		return fmt.Errorf("failed to find Chrome process for address: %s, error: %v", address, err)
	}

	pid := cast.ToInt(strings.TrimSpace(string(pidOutput)))
	if pid == 0 {
		return fmt.Errorf("invalid Chrome process ID for address: %s", address)
	}

	logger.Infof("found Chrome process ID: %d for address: %s", pid, address)

	// Send F5 (reload) to the Chrome window
	reloadCmd := exec.Command("powershell", "-NoProfile", "-NonInteractive", "-Command",
		fmt.Sprintf(`
			$ErrorActionPreference = 'Stop'
			try {
				$process = Get-Process -Id %d -ErrorAction Stop
				if ($process) {
					$hwnd = $process.MainWindowHandle
					if ($hwnd -ne 0) {
						Add-Type @"
							using System;
							using System.Runtime.InteropServices;
							public class Window {
								[DllImport("user32.dll")]
								[return: MarshalAs(UnmanagedType.Bool)]
								public static extern bool SetForegroundWindow(IntPtr hWnd);
								
								[DllImport("user32.dll")]
								public static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);
								
								public const int VK_F5 = 0x74;
								public const int KEYEVENTF_KEYUP = 0x0002;
							}
"@
						# Bring window to foreground
						[Window]::SetForegroundWindow($hwnd)
						Start-Sleep -Milliseconds 100
						
						# Send F5 key (reload)
						[Window]::keybd_event(0x74, 0, 0, [UIntPtr]::Zero)
						Start-Sleep -Milliseconds 50
						[Window]::keybd_event(0x74, 0, 2, [UIntPtr]::Zero)
						
						Write-Output "Success"
					} else {
						throw "Window handle is 0"
					}
				} else {
					throw "Process not found"
				}
			} catch {
				Write-Error $_.Exception.Message
				exit 1
			}
		`, pid))

	output, err := reloadCmd.CombinedOutput()
	if err != nil {
		logger.Errorf("failed to send reload keystroke: %v, output: %s", err, string(output))
		return fmt.Errorf("failed to reload Chrome: %v", err)
	}

	logger.Infof("successfully reloaded chrome with address: %s", address)
	return nil
}

func navigateToURL(address string, url string) error {
	browserCtx, cancel, err := getChromeDPContext(context.Background(), address)
	if err != nil {
		return fmt.Errorf("failed to get ChromeDP context: %v", err)
	}
	defer cancel()

	chromedp.Run(browserCtx,
		chromedp.Navigate(url),
	)

	return nil
}

type PchromeInfo struct {
	Address      string
	AddressIdx   int
	ProxyGateway string
	ProjectID    string
	Tag          string
	HttpProxy    string
	SocksProxy   string
	UID          string
}

func (this *PchromeInfo) CloseChrome() error {
	if runtime.GOOS != "windows" {
		return fmt.Errorf("only works on Windows")
	}

	closeChromeWithAddress(this.SafeUID())
	return nil
}

func (this *PchromeInfo) ReloadChrome() error {
	if runtime.GOOS != "windows" {
		return fmt.Errorf("only works on Windows")
	}

	reloadChrome(this.Address)
	return nil
}

func (this *PchromeInfo) NavigateToURL(url string) error {
	if runtime.GOOS != "windows" {
		return fmt.Errorf("only works on Windows")
	}

	navigateToURL(this.Address, url)
	return nil
}

func (this *PchromeInfo) SafeUID() string {
	return strings.ReplaceAll(this.UID, ":", "")
}

func (this *PchromeInfo) GetUserDataDir() string {
	dirName := fmt.Sprintf("%s_%s", this.ProjectID, this.Tag)
	if this.Tag == "" {
		dirName = this.ProjectID
	}
	userDataDir := fmt.Sprintf("~/pchrome/user_data/%s/%s", dirName, this.SafeUID())
	userDataDir, err := homedir.Expand(userDataDir)
	if err != nil {
		return ""
	}
	return userDataDir
}

func (this *PchromeInfo) GetStartBatContent(url string) (string, error) {
	userDataDir := this.GetUserDataDir()
	// Generate a unique base port using projectID+tag and last 4 digits
	dirName := fmt.Sprintf("%s%s", this.ProjectID, this.Tag)
	if this.Tag == "" {
		dirName = this.ProjectID
	}
	// Use last 4 chars to generate a number between 9222-9999
	basePort := 9222
	if len(dirName) >= 4 {
		// Convert last 4 chars to a number and use modulo to get a port in range
		hash := 0
		last4 := dirName[len(dirName)-4:]
		for i := 0; i < 4; i++ {
			hash = hash*31 + int(last4[i])
		}
		basePort = 9222 + (hash % 777)
	}
	debugPort := basePort + (this.AddressIdx % 100)
	batContent := fmt.Sprintf("start chrome --disable-features=UseEcoQoSForBackgroundProcess --user-data-dir=\"%s\" --proxy-server=\"%s\" --proxy-bypass-list=\"<local>;%s\" --remote-debugging-port=%d --start-maximized", userDataDir, this.SocksProxy, this.ProxyGateway, debugPort)
	restoreChromeSession := true
	if restoreChromeSession {
		batContent += " --restore-last-session"
	} else {
		if url != "" {
			batContent += fmt.Sprintf(" \"%s\"", url)
		}
	}

	batContent += "\n"
	batContent += "ping 127.0.0.1 -n 2 > nul"
	return batContent, nil
}

func (this *PchromeInfo) SortKey() string {
	return fmt.Sprintf("%s_%s_%02d", this.ProjectID, this.Tag, this.AddressIdx)
}

func (this *CopierController) lookupPchromeForAddress(projectID string, addresses []string) (pchromes []*PchromeInfo, er error) {
	hopperDir := "~/pchrome"
	hopperDir, err := homedir.Expand(hopperDir)
	if err != nil {
		return nil, err
	}
	// list json files in pchrome
	files, err := filepath.Glob(filepath.Join(hopperDir, "*.json"))
	if err != nil {
		return nil, err
	}
	for _, file := range files {
		content, err := ioutil.ReadFile(file)
		if err != nil {
			return nil, err
		}
		// parse the json file with gjson
		result := gjson.Parse(string(content))

		// Check if this is the right platform
		if result.Get("project").String() != projectID {
			continue
		}

		proxyGateway := result.Get("proxy_gateway").String()
		gatewayHost := ""
		if proxyGateway != "" {
			parts := strings.Split(proxyGateway, ":")
			gatewayHost = parts[0]
		}
		if gatewayHost == "" {
			return nil, fmt.Errorf("no proxy gateway found for file: %s", file)
		}

		uids := result.Get("uids").Array()

		for _, address := range addresses {
			var pchromeInfo *PchromeInfo
			for _, uid := range uids {
				if strings.Contains(uid.String(), address) {
					parts := strings.Split(uid.String(), ": ")
					var idx = 0
					var address = ""
					if len(parts) != 2 {
						address = parts[0]
					} else {
						idx = cast.ToInt(parts[0])
						address = parts[1]
					}
					pchromeInfo = &PchromeInfo{
						Address:      address,
						AddressIdx:   idx,
						ProxyGateway: proxyGateway,
						ProjectID:    result.Get("project").String(),
						Tag:          result.Get("tag").String(),
						UID:          uid.String(),
					}
					pchromes = append(pchromes, pchromeInfo)
					break
				}
			}

			if pchromeInfo == nil {
				logger.Errorf("no pchrome found for address: %s", address)
				continue
			}

			forcePort := result.Get("force_port").Int()
			if forcePort != 0 {
				pchromeInfo.HttpProxy = fmt.Sprintf("http://%s:%d", gatewayHost, forcePort)
				pchromeInfo.SocksProxy = fmt.Sprintf("socks5://%s:%d", gatewayHost, forcePort)
				continue
			}

			// Look through forwarders to find matching address
			forwarders := result.Get("forwarders")
			var foundPort int64
			forwarders.ForEach(func(key, value gjson.Result) bool {
				// Extract address from key (format: "id: address")
				parts := strings.Split(key.String(), ": ")
				if len(parts) != 2 {
					return true // continue iteration
				}
				addr := parts[1]

				if addr == address {
					foundPort = value.Get("port").Int()
					return false // stop iteration
				}
				return true // continue iteration
			})

			// If we found a matching address, return the port
			if foundPort != 0 {
				pchromeInfo.HttpProxy = fmt.Sprintf("http://%s:%d", gatewayHost, foundPort)
				pchromeInfo.SocksProxy = fmt.Sprintf("socks5://%s:%d", gatewayHost, foundPort)
			}
		}
	}
	if len(pchromes) == 0 {
		return nil, fmt.Errorf("no pchrome found for address: %s", addresses)
	}
	return pchromes, nil
}

func (this *CopierController) startChromesForProjectIDs(projectIDs []string) {
	tempFilePath := fmt.Sprintf("~/pchrome/temp/%s.bat", utils.NewRandomID())
	tempFilePath, err := homedir.Expand(tempFilePath)
	if err != nil {
		logger.Errorf("failed to expand temp file: %s, error: %v", tempFilePath, err)
		return
	}
	// ensure the directory exists
	os.MkdirAll(filepath.Dir(tempFilePath), 0755)
	tempFile, err := os.Create(tempFilePath)
	if err != nil {
		logger.Errorf("failed to create temp file: %s, error: %v", tempFilePath, err)
		return
	}

	defer func() {
		tempFile.Close()
	}()

	batchScript := ""
	addressURLs := map[string]string{}
	for _, projectID := range projectIDs {
		projectID = strings.TrimSpace(projectID)
		if projectID == "" {
			continue
		}
		project, ok := this.Projects.Load(projectID)
		if !ok {
			logger.Errorf("project not found: %s", projectID)
			continue
		}

		projectAddresses := map[string][]string{}
		for _, agent := range project.Agents {
			address := agent.UserID
			if address == "" {
				continue
			}
			projectAddresses[string(agent.Platform)] = append(projectAddresses[string(agent.Platform)], address)
			if agent.Platform == PlatformLighter {
				addressURLs[address] = fmt.Sprintf("https://app.lighter.xyz/trade/%s", agent.Settings.DefaultSymbol)
			}
		}

		for platform, addresses := range projectAddresses {
			pchromes, err := this.lookupPchromeForAddress(string(platform), addresses)
			if err != nil {
				logger.Errorf("failed to lookup pchrome for address: %s, error: %v", addresses, err)
				continue
			}
			// sort pchromes to start them by order
			sort.Slice(pchromes, func(i, j int) bool {
				return pchromes[i].SortKey() < pchromes[j].SortKey()
			})
			for _, pchrome := range pchromes {
				if pchrome.SocksProxy == "" {
					logger.Errorf("no proxy found for address: %s", pchrome.Address)
					continue
				}
				if pchrome.GetChromePID() > 0 {
					logger.Infof("chrome is already running: %s", pchrome.Address)
					continue
				}
				url, ok := addressURLs[pchrome.Address]
				if !ok {
					url = ""
				}
				batContent, err := pchrome.GetStartBatContent(url)
				if err != nil {
					logger.Errorf("failed to start chrome with address: %s, error: %v", pchrome.Address, err)
				}
				batchScript += batContent
				batchScript += "\n"
			}
		}

	}

	tempFile.WriteString(batchScript)
	logger.Infof("temp file: %s, content: %s", tempFilePath, batchScript)

	// execute the temp file
	cmd := exec.Command("cmd.exe", "/c", tempFilePath)
	err = cmd.Run()
	if err != nil {
		logger.Errorf("failed to execute temp file: %s, error: %v", tempFilePath, err)
	}
	logger.Infof("started %d chromes", len(projectIDs))
}

func (this *PchromeInfo) GetChromePID() int {
	cmd := exec.Command("powershell", "-Command",
		fmt.Sprintf("$chrome = Get-CimInstance Win32_Process -Filter \"name='chrome.exe'\" | Where-Object { $_.CommandLine -like '*%s*' } | Select-Object -First 1; if ($chrome) { $chrome.ProcessId }", this.GetUserDataDir()))
	pid, err := cmd.Output()
	if err != nil || len(pid) == 0 {
		return 0
	}
	result := cast.ToInt(strings.TrimSpace(string(pid)))
	return result
}

func (this *PchromeInfo) ActivateChrome() error {
	if runtime.GOOS != "windows" {
		return fmt.Errorf("only works on Windows")
	}

	// First try to find Chrome's process ID
	pid := this.GetChromePID()
	if pid == 0 {
		return fmt.Errorf("failed to find Chrome process for address: %s", this.Address)
	}
	logger.Infof("activate chrome pid: %d, address: %s", pid, this.Address)

	// Activate the window using the process ID
	activateCmd := exec.Command("powershell", "-NoProfile", "-NonInteractive", "-Command",
		fmt.Sprintf(`
			$ErrorActionPreference = 'Stop'
			try {
				$process = Get-Process -Id %d -ErrorAction Stop
				if ($process) {
					$hwnd = $process.MainWindowHandle
					if ($hwnd -ne 0) {
						Add-Type @"
							using System;
							using System.Runtime.InteropServices;
							public class Window {
								[DllImport("user32.dll")]
								[return: MarshalAs(UnmanagedType.Bool)]
								public static extern bool SetForegroundWindow(IntPtr hWnd);
								
								[DllImport("user32.dll")]
								[return: MarshalAs(UnmanagedType.Bool)]
								public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);
								
								[DllImport("user32.dll")]
								[return: MarshalAs(UnmanagedType.Bool)]
								public static extern bool IsIconic(IntPtr hWnd);
							}
"@
						if ([Window]::IsIconic($hwnd)) {
							[Window]::ShowWindow($hwnd, 9) # SW_RESTORE
						}
						[Window]::SetForegroundWindow($hwnd)
						Write-Output "Success"
					} else {
						throw "Window handle is 0"
					}
				} else {
					throw "Process not found"
				}
			} catch {
				Write-Error $_.Exception.Message
				exit 1
			}
		`, pid))

	output, err := activateCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to activate Chrome window: %v, output: %s", err, string(output))
	}

	return nil
}

// 关闭 chrome 时，如果项目状态不是 safely stopped，则等待 project 状态变为 safely stopped 后关闭
// 如果等待 45 秒后，项目状态仍不是 safely stopped，则直接关闭 chrome
func (this *CopierController) closeChromeForProjects(projectIDs []string) {
	logger.Infof("closeChromeForProjects started for %d projects", len(projectIDs))
	for _, projectID := range projectIDs {
		projectID = strings.TrimSpace(projectID)
		if projectID == "" {
			continue
		}
		project, ok := this.Projects.Load(projectID)
		if !ok {
			logger.Errorf("stop chrome, project not found: %s", projectID)
			continue
		}
		// 对于正在运行的项目，不关闭 chrome
		if project.Status == ProjectStatusRunning {
			logger.Infof("Project %s is running, skipping chrome closure", projectID)
			continue
		}
		// 项目状态变成 stopped 之后，可能还有工作未完成，等待 30 秒后才可以关闭 chrome
		startTime := time.Now()
		logger.Infof("Waiting for project %s to be safely stopped. Current status: %s", project.ProjectID, project.Status)
		for !project.IsSafelyStopped() {
			if time.Since(startTime) > 30*time.Second {
				logger.Errorf("close chrome timeout after 30s, project: %s, status: %s, update time: %s", project.ProjectID, project.Status, project.StatusUpdateTime)
				break
			}
			logger.Infof("close chrome, project is not safely stopped: %s, status: %s, update time: %s", project.ProjectID, project.Status, project.StatusUpdateTime)
			time.Sleep(1 * time.Second)
			// try to activate chrome to make sure the project data is updated
			for _, agent := range project.Agents {
				if agent.UserID == "" {
					continue
				}
				logger.Infof("Activating chrome for agent %s in project %s to ensure data is updated", agent.UserID, project.ProjectID)
				err := this.activateChromeByAddress(project.ProjectID, agent.UserID, true)
				if err != nil {
					logger.Errorf("failed to activate chrome with address: %s, error: %v", agent.UserID, err)
				}
			}
		}
		logger.Infof("Project %s is safely stopped or timed out. Proceeding with chrome closure.", project.ProjectID)
		for _, agent := range project.Agents {
			address := agent.UserID
			if address == "" {
				continue
			}
			logger.Infof("Closing chrome for address: %s", address)
			err := closeChromeWithAddress(address)
			if err != nil {
				logger.Errorf("failed to close chrome with address: %s, error: %v", address, err)
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
	logger.Infof("closeChromeForProjects finished")
}

func (this *CopierController) reloadChrome(projectID string, address string, force bool) error {
	project, ok := this.Projects.Load(projectID)
	if !ok {
		return fmt.Errorf("project not found: %s", projectID)
	}
	// For running projects, reload chrome
	for _, agent := range project.Agents {
		if agent.UserID != address {
			continue
		}
		if !force && time.Since(agent.CreateTime) < 15*time.Second {
			logger.Infof("reload chrome, agent is up to date, aborted, project: %s, agent: %s", projectID, address)
			continue
		}

		if runtime.GOOS == "windows" {
			err := reloadChrome(address)
			if err != nil {
				return fmt.Errorf("failed to reload chrome with address: %s, error: %v", address, err)
			}
		} else {
			this.AddServerRequest(projectID, address, ServerRequestTypeCheckReloadPage, 1)
		}
		time.Sleep(50 * time.Millisecond)
	}
	return nil
}

package copier

import (
	"bytes"
	"chaintools/hopper"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

type CopierClient struct {
	Host string
}

func NewCopierClient(host string) *CopierClient {
	return &CopierClient{Host: host}
}

func extractErrorResponse(resp *http.Response) error {
	body, _ := io.ReadAll(resp.Body)
	var errResp struct {
		Error string `json:"error"`
	}
	if err := json.Unmarshal(body, &errResp); err != nil {
		return fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}
	return fmt.Errorf("unexpected status code: %d, error: %s", resp.StatusCode, errResp.Error)
}

func (this *CopierClient) MakeBalancePlan(platform Platform, projectIDs []string, amount float64) (plan *BalancePlan, er error) {
	reqBody := MakeBalancerPlanRequest{
		Platform:   platform,
		ProjectIDs: projectIDs,
		Amount:     amount,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	resp, err := http.Post(fmt.Sprintf("%s/balancer/plan/make", this.Host), "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, extractErrorResponse(resp)
	}

	var response MakeBalancerPlanResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	return response.Plan, nil
}

func (this *CopierClient) CancelBalancePlan(planRefID string) (er error) {
	reqBody := CancelBalancerPlanRequest{
		PlanRefID: planRefID,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %v", err)
	}

	resp, err := http.Post(fmt.Sprintf("%s/balancer/plan/cancel", this.Host), "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return extractErrorResponse(resp)
	}

	var response CancelBalancerPlanResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Errorf("failed to decode response: %v", err)
	}

	if !response.Success {
		return fmt.Errorf("failed to cancel balance plan")
	}

	return nil
}

func (this *CopierClient) ProcessBalancePlan(planRefID string) (er error) {
	reqBody := ProcessBalancerPlanRequest{
		PlanRefID: planRefID,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %v", err)
	}

	resp, err := http.Post(fmt.Sprintf("%s/balancer/plan/process", this.Host), "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return extractErrorResponse(resp)
	}

	var response ProcessBalancerPlanResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Errorf("failed to decode response: %v", err)
	}

	if !response.Success {
		return fmt.Errorf("failed to process balance plan")
	}

	return nil
}

func (this *CopierClient) GetBalancePlans() (plans []*BalancePlan, er error) {
	resp, err := http.Get(fmt.Sprintf("%s/balancer/plans", this.Host))
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, extractErrorResponse(resp)
	}

	var response GetBalancerPlansResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	return response.Plans, nil
}

func (this *CopierClient) GetProjects() (projects []*Project, er error) {
	resp, err := http.Get(fmt.Sprintf("%s/projects", this.Host))
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, extractErrorResponse(resp)
	}

	var response GetProjectsResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	return response.Projects, nil
}

func (this *CopierClient) QueryExtensionWithdraw(projectID string, userID string) (tx *hopper.Tx, er error) {
	resp, err := http.Get(fmt.Sprintf("%s/balancer/extension/withdraw?project_id=%s&user_id=%s", this.Host, projectID, userID))
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, extractErrorResponse(resp)
	}

	var response struct {
		Tx *hopper.Tx `json:"tx"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	return response.Tx, nil
}

func (this *CopierClient) MarkExtensionWithdrawDone(projectID string, txRefID string) (er error) {
	reqBody := MarkExtensionWithdrawDoneRequest{
		ProjectID: projectID,
		TxRefID:   txRefID,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %v", err)
	}

	resp, err := http.Post(fmt.Sprintf("%s/balancer/extension/withdraw/done", this.Host), "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return extractErrorResponse(resp)
	}

	var response struct {
		Success bool `json:"success"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Errorf("failed to decode response: %v", err)
	}

	if !response.Success {
		return fmt.Errorf("failed to mark extension withdraw done")
	}

	return nil
}

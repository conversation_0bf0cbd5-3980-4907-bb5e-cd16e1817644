package copier

import (
	"copier/backend/logger"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"math/rand"
	"os"
	"path/filepath"
	"slices"
	"sort"
	"strings"
	"sync"
	"time"

	"chaintools/hopper"

	"copier/backend/utils"

	"github.com/jinzhu/copier"

	"github.com/puzpuzpuz/xsync"
	"gopkg.in/yaml.v2"
)

// Balancer 负责管理多个钱包和交易所，平衡 project 中的地址的资金
// copier 在管理后台将需要平衡的 project 加进来，然后 balancer 会根据 project 中的地址的资金情况，自动进行平衡
// 平衡的规则是：
// 1. 计算 project 重 master/copier 的资金差额，找出需要转出的地址 addr 和金额 amount（实际从 platform 转出由 extension 完成）
// 2. 根据 balance_plan 中的 subaccount 和 project_group 找到对应的 subaccount 和 subaccountAddr
// 3. balancer 从 wallets 找到 addr 对应的私钥，链上转出 amount 到 subaccountAddr
// 4. 更新 balance_plan 的 update_time

type ExchangeOpt struct {
	ID           string              `yaml:"id"`
	ExchangeName hopper.ExchangeName `yaml:"exchange_name"`
	APIKey       string              `yaml:"api_key"`
	APISecret    string              `yaml:"api_secret"`
}

type WalletOpt struct {
	ID       string `yaml:"id"`
	Prefix   string `yaml:"prefix"`
	Mnemonic string `yaml:"mnemonic"`
}

type SubaccountOpt struct {
	ID               string            `yaml:"id"`
	ExchangeID       string            `yaml:"exchange_id"`
	Username         string            `yaml:"username"`
	APIKey           string            `yaml:"api_key"`
	APISecret        string            `yaml:"api_secret"`
	DepositAddresses map[string]string `yaml:"deposit_addresses"`
}

func (this *SubaccountOpt) UseAPI() bool {
	return this.APIKey != "" && this.APISecret != ""
}

type PlatformDepositOpt struct {
	ID        string            `yaml:"id"`
	Platform  Platform          `yaml:"platform"`
	Addresses map[string]string `yaml:"addresses"`
}

type PlatformOpt struct {
	Platform                  Platform                     `yaml:"platform"`
	ChainName                 string                       `yaml:"chain_name"`
	Coin                      string                       `yaml:"coin"`
	MinDepositAmount          float64                      `yaml:"min_deposit_amount"`
	UseWalletAddressToDeposit bool                         `yaml:"use_wallet_address_to_deposit"`
	PlatformDepositAddress    string                       `yaml:"platform_deposit_address"`
	WalletSubaccountMapping   map[string]map[string]string `yaml:"wallet_subaccount_mapping"` // exchange_id -> { wallet_id -> subaccount_id }
}

func (this *PlatformOpt) GetWalletSubaccount(exchangeID string, walletID string) (string, error) {
	if this.WalletSubaccountMapping[exchangeID] == nil {
		return "", fmt.Errorf("wallet subaccount mapping not found, exchangeID: %s", exchangeID)
	}
	walletSubaccountMapping := this.WalletSubaccountMapping[exchangeID]
	subaccountID, ok := walletSubaccountMapping[walletID]
	if !ok {
		return "", fmt.Errorf("wallet subaccount mapping not found, exchangeID: %s, walletID: %s", exchangeID, walletID)
	}
	return subaccountID, nil
}

func (this *PlatformOpt) ToPlatformInfo() (*PlatformInfo, error) {
	chain := hopper.NewChain(this.ChainName)
	if chain == nil {
		return nil, fmt.Errorf("chain not found: %s", this.ChainName)
	}
	coins := []string{this.Coin, chain.Coin}
	return &PlatformInfo{
		Platform:       this.Platform,
		SupportedCoins: coins,
		Coin:           this.Coin,
		Chain:          this.ChainName,
	}, nil
}

type PlatformInfo struct {
	Platform       Platform `json:"platform"`
	SupportedCoins []string `json:"supportedCoins"`
	Coin           string   `json:"coin"`
	Chain          string   `json:"chain"`
}

// 保存子账号充币地址信息
type SubaccountAddress struct {
	Platform       Platform `yaml:"platform"`
	Coin           string   `yaml:"coin"`
	ChainName      string   `yaml:"chain_name"`
	SubaccountID   string   `yaml:"subaccount_id"`
	WalletIndex    int      `yaml:"wallet_index"`
	DepositAddress string   `yaml:"deposit_address"`
}

type SubaccountAddresses []*SubaccountAddress

func (this *SubaccountAddresses) FindAddress(platform Platform, coin string, chainName string, subaccountID string, walletIndex int) string {
	for _, address := range *this {
		if address.Platform == platform && address.Coin == coin && address.ChainName == chainName && address.SubaccountID == subaccountID && address.WalletIndex == walletIndex {
			return address.DepositAddress
		}
	}
	return ""
}

type BalancerOptions struct {
	Wallets              []*WalletOpt                 `yaml:"wallets"`
	Exchanges            []*ExchangeOpt               `yaml:"exchanges"`
	Subaccounts          []*SubaccountOpt             `yaml:"subaccounts"`
	PlatformDeposits     []*PlatformDepositOpt        `yaml:"platform_deposits"`
	Platforms            []*PlatformOpt               `yaml:"platforms"`
	AutoBalanceThreshold float64                      `yaml:"auto_balance_threshold"`
	platformDepositMap   map[string]string            `yaml:"-"` // platform::alias -> address
	exchangeDepositMap   map[string]map[string]string `yaml:"-"` // exchangeID -> { alias -> address }
}

func (this *BalancerOptions) GetPlatformOpt(platform Platform) *PlatformOpt {
	for _, platformOpt := range this.Platforms {
		if platformOpt.Platform == platform {
			return platformOpt
		}
	}
	return nil
}

func (this *BalancerOptions) GetExchangeOpt(exchangeID string) *ExchangeOpt {
	for _, exchangeOpt := range this.Exchanges {
		if exchangeOpt.ID == exchangeID {
			return exchangeOpt
		}
	}
	return nil
}

func (this *BalancerOptions) Load(optPath string) error {
	this.platformDepositMap = map[string]string{}
	this.exchangeDepositMap = map[string]map[string]string{}
	if this.AutoBalanceThreshold <= 20 {
		this.AutoBalanceThreshold = 20
	}

	optFile, err := os.ReadFile(optPath)
	if err != nil {
		return fmt.Errorf("failed to read balancer options file: %v", err)
	}
	err = yaml.Unmarshal(optFile, this)
	if err != nil {
		return fmt.Errorf("failed to unmarshal balancer options: %v", err)
	}
	platformNames := []Platform{}
	for _, platform := range this.Platforms {
		platformNames = append(platformNames, platform.Platform)
	}

	// check for duplicate exchange deposit address alias
	exchangeIDs := []string{}
	for _, exchange := range this.Exchanges {
		exchangeIDs = append(exchangeIDs, exchange.ID)
	}
	// all deposit addresses, for checking platform deposit address, allow duplicate addresses
	// prevent platform deposit address mistakenly used as exchange deposit address
	exchangeDepositAddresses := []string{}
	for _, exchangeID := range exchangeIDs {
		exchangeDepositMap := map[string]string{}
		for _, subaccount := range this.Subaccounts {
			if subaccount.ExchangeID != exchangeID {
				continue
			}
			for alias, address := range subaccount.DepositAddresses {
				if _, ok := exchangeDepositMap[alias]; ok {
					// checking duplicate deposit address alias config error
					return fmt.Errorf("duplicate deposit address alias: %s", alias)
				} else {
					exchangeDepositMap[alias] = address
					exchangeDepositAddresses = append(exchangeDepositAddresses, address)
				}
			}
		}
		// ensure we have a map as cache for each exchangeID
		this.exchangeDepositMap[exchangeID] = exchangeDepositMap
	}

	// validate opts
	// check for duplicate platform deposit address alias
	for _, platformDepositOpt := range this.PlatformDeposits {
		if !slices.Contains(platformNames, platformDepositOpt.Platform) {
			return fmt.Errorf("platform not found: %s for platform deposit opt: %s", platformDepositOpt.Platform, platformDepositOpt.ID)
		}
		for alias, address := range platformDepositOpt.Addresses {
			if slices.Contains(exchangeDepositAddresses, address) {
				return fmt.Errorf("found deposit address in exchange deposit addresses: %s", address)
			}
			depositKey := string(platformDepositOpt.Platform) + "::" + alias
			if _, ok := this.platformDepositMap[depositKey]; ok {
				return fmt.Errorf("duplicate deposit address alias: %s", alias)
			} else {
				this.platformDepositMap[depositKey] = address
			}
		}
	}
	return nil
}

type BalancePlan struct {
	RefID         string              `json:"refId"`
	Platform      Platform            `json:"platform"` // lighter/hyperliquid
	ChainName     string              `json:"chainName"`
	Coin          string              `json:"coin"`
	WaitSeconds   int                 `json:"waitSeconds"` // max wait seconds for each tx, randomnize between 0 and this value, txs will be sent in random order
	ExchangeID    string              `json:"exchangeID"`
	ExchangeName  hopper.ExchangeName `json:"exchangeName"`
	Stages        []BalanceStage      `json:"stages"` // 平衡计划包含的阶段，如果为空，则默认包含所有阶段，用于手工测试部分阶段
	BalanceItems  []*BalancePlanItem  `json:"balanceItems"`
	Status        BalancePlanStatus   `json:"status"` // 当前状态，没个 item 并发执行，需要一个状态来表示所有 item 是否完成；这里并不反应 item 具体的完成状态
	StatusComment string              `json:"statusComment"`
	CreateTime    time.Time           `json:"createTime"`
	UpdateTime    *time.Time          `json:"updateTime"`
	CancelTime    *time.Time          `json:"cancelTime"`
	DeleteTime    *time.Time          `json:"deleteTime"`
}

func (this *BalancePlan) IsDone() bool {
	return this.Status == BalancePlanStatusDone || this.CancelTime != nil
}

func (this *BalancePlan) IsCanceled() bool {
	return this.CancelTime != nil
}

func (this *BalancePlan) CloneRedacted() *BalancePlan {
	clone := &BalancePlan{}
	copier.CopyWithOption(clone, this, copier.Option{
		IgnoreEmpty: true,
		DeepCopy:    true,
	})
	for _, item := range clone.BalanceItems {
		txs := []*hopper.Tx{}
		if item.Txs != nil {
			txs = append(txs, item.Txs.PlatformWithdrawTx)
			txs = append(txs, item.Txs.ExchangeDepositTx)
			txs = append(txs, item.Txs.ExchangeWithdrawTx)
			txs = append(txs, item.Txs.PlatformDepositTx)
		}
		for _, tx := range txs {
			if tx != nil {
				tx.PrivateKey = "[REDACTED]"
			}
		}
	}
	return clone
}

// format project ids as group[project1, project2, ...], group2[project3, project4, ...], ...
// project1, project2, is the suffix of project id, group is the prefix of project id
// example: lighter[0102, 0203], lighter2[0102, 0203]...
// this function can format project ids for different groups
func (this *BalancePlan) FormatProjectIDs() string {
	projectIDs := utils.Map(this.BalanceItems, func(item *BalancePlanItem) string { return item.ProjectID })
	groupIDs := map[string][]string{}
	for _, projectID := range projectIDs {
		if strings.Contains(projectID, "_") {
			parts := strings.Split(projectID, "_")
			groupID := parts[0]
			suffix := parts[1]
			groupIDs[groupID] = append(groupIDs[groupID], suffix)
		} else {
			groupIDs[projectID] = append(groupIDs[projectID], projectID)
		}
	}
	groupIDsList := []string{}
	for groupID := range groupIDs {
		groupIDsList = append(groupIDsList, groupID)
	}
	sort.Strings(groupIDsList)
	groupIDsStrList := []string{}
	for _, groupID := range groupIDsList {
		groupIDsStrList = append(groupIDsStrList, groupID+"["+strings.Join(groupIDs[groupID], ", ")+"]")
	}
	return strings.Join(groupIDsStrList, ", ")
}

func (this *BalancePlan) FormatShortStatus() string {
	return strings.TrimPrefix(string(this.Status), "BalancePlanStatus")
}

type BalanceStage string

const (
	BalanceStagePlatformWithdraw BalanceStage = "platform_withdraw"
	BalanceStageExchangeDeposit  BalanceStage = "exchange_deposit"
	BalanceStageExchangeWithdraw BalanceStage = "exchange_withdraw"
	BalanceStagePlatformDeposit  BalanceStage = "platform_deposit"
)

type BalancePlanStatus string

const (
	BalancePlanStatusNew        BalancePlanStatus = "new"
	BalancePlanPrepareError     BalancePlanStatus = "prepare_error"
	BalancePlanStatusPrepared   BalancePlanStatus = "prepared"
	BalancePlanStatusProcessing BalancePlanStatus = "processing"
	BalancePlanStatusDone       BalancePlanStatus = "done"
)

type BalancePlanItemStatus int

//go:generate stringer -type=BalancePlanItemStatus
const (
	BalancePlanItemStatusPrepareError BalancePlanItemStatus = iota
	BalancePlanItemStatusPrepareDone
	BalancePlanItemStatusPlatformWithdrawStart
	BalancePlanItemStatusPlatformWithdrawError
	BalancePlanItemStatusPlatformWithdrawDone
	BalancePlanItemStatusExchangeDepositStart
	BalancePlanItemStatusExchangeDepositError
	BalancePlanItemStatusExchangeDepositDone
	BalancePlanItemStatusExchangeWithdrawStart
	BalancePlanItemStatusExchangeWithdrawError
	BalancePlanItemStatusExchangeWithdrawDone
	BalancePlanItemStatusPlatformDepositStart
	BalancePlanItemStatusPlatformDepositError
	BalancePlanItemStatusPlatformDepositDone
	BalancePlanItemStatusDone
)

// StringToBalancePlanItemStatus converts a string to BalancePlanItemStatus
func StringToBalancePlanItemStatus(s string) (BalancePlanItemStatus, error) {
	switch s {
	case "BalancePlanItemStatusPrepareError":
		return BalancePlanItemStatusPrepareError, nil
	case "BalancePlanItemStatusPrepareDone":
		return BalancePlanItemStatusPrepareDone, nil
	case "BalancePlanItemStatusPlatformWithdrawStart":
		return BalancePlanItemStatusPlatformWithdrawStart, nil
	case "BalancePlanItemStatusPlatformWithdrawError":
		return BalancePlanItemStatusPlatformWithdrawError, nil
	case "BalancePlanItemStatusPlatformWithdrawDone":
		return BalancePlanItemStatusPlatformWithdrawDone, nil
	case "BalancePlanItemStatusExchangeDepositStart":
		return BalancePlanItemStatusExchangeDepositStart, nil
	case "BalancePlanItemStatusExchangeDepositError":
		return BalancePlanItemStatusExchangeDepositError, nil
	case "BalancePlanItemStatusExchangeDepositDone":
		return BalancePlanItemStatusExchangeDepositDone, nil
	case "BalancePlanItemStatusExchangeWithdrawStart":
		return BalancePlanItemStatusExchangeWithdrawStart, nil
	case "BalancePlanItemStatusExchangeWithdrawError":
		return BalancePlanItemStatusExchangeWithdrawError, nil
	case "BalancePlanItemStatusExchangeWithdrawDone":
		return BalancePlanItemStatusExchangeWithdrawDone, nil
	case "BalancePlanItemStatusPlatformDepositStart":
		return BalancePlanItemStatusPlatformDepositStart, nil
	case "BalancePlanItemStatusPlatformDepositError":
		return BalancePlanItemStatusPlatformDepositError, nil
	case "BalancePlanItemStatusPlatformDepositDone":
		return BalancePlanItemStatusPlatformDepositDone, nil
	case "BalancePlanItemStatusDone":
		return BalancePlanItemStatusDone, nil
	default:
		return 0, fmt.Errorf("unknown status: %s", s)
	}
}

type AddressType string

const (
	AddressTypeWallet   AddressType = "wallet"
	AddressTypeExchange AddressType = "exchange"
	AddressTypePlatform AddressType = "platform"
)

// 某个地址的原地址，中间地址，目标地址，金额；以及当前状态
// 处理包含钱包操作的状态，还包含平台操作的状态
type BalancePlanItem struct {
	RefID                     string                `json:"refId"`
	ProjectID                 string                `json:"projectId"`
	FromAddressType           AddressType           `json:"fromAddressType"` // 平台余额和钱包余额都是通过同一个地址获取的，通过 address type 来区分
	FromAddressAlias          string                `json:"fromAddressAlias"`
	FromAddress               string                `json:"fromAddress"`
	FromAddressOriginalAmount float64               `json:"fromAddressOriginalAmount"` // 原地址平衡前金额
	FromAddressCurrentAmount  float64               `json:"fromAddressCurrentAmount"`  // 原地址当前金额，可能需要和原金额比较，确定操作是否完成
	IntermediateAddress       string                `json:"intermediateAddress"`
	ToAddressType             AddressType           `json:"toAddressType"` // 平台余额和钱包余额都是通过同一个地址获取的，通过 address type 来区分
	ToAddressAlias            string                `json:"toAddressAlias"`
	ToAddress                 string                `json:"toAddress"`
	ToAddressOriginalAmount   float64               `json:"toAddressOriginalAmount"` // 目标地址平衡前金额
	ToAddressCurrentAmount    float64               `json:"toAddressCurrentAmount"`  // 目标地址当前金额，可能需要和原金额比较，确定操作是否完成
	BalanceAmount             float64               `json:"balanceAmount"`           // 需要平衡的金额
	CreateTime                time.Time             `json:"createTime"`
	Status                    BalancePlanItemStatus `json:"status"`
	StatusString              string                `json:"statusString"`
	StatusComment             string                `json:"statusComment"`
	UpdateTime                *time.Time            `json:"updateTime"`
	CancelTime                *time.Time            `json:"cancelTime"`
	Txs                       *BalancePlanItemTxs   `json:"txs"`
}

func (this *BalancePlanItem) SetStatus(status BalancePlanItemStatus) {
	this.Status = status
	this.StatusString = status.String()
	this.UpdateTime = utils.Now()
}

func (this *BalancePlanItem) FormatShortStatus() string {
	return strings.TrimPrefix(this.StatusString, "BalancePlanItemStatus")
}

func (this *BalancePlanItem) GetTxTypeByRefID(txRefID string) TxType {
	if this.Txs.PlatformWithdrawTx != nil && this.Txs.PlatformWithdrawTx.ID == txRefID {
		return TxTypePlatformWithdraw
	}
	if this.Txs.ExchangeDepositTx != nil && this.Txs.ExchangeDepositTx.ID == txRefID {
		return TxTypeExchangeDeposit
	}
	if this.Txs.ExchangeWithdrawTx != nil && this.Txs.ExchangeWithdrawTx.ID == txRefID {
		return TxTypeExchangeWithdraw
	}
	if this.Txs.PlatformDepositTx != nil && this.Txs.PlatformDepositTx.ID == txRefID {
		return TxTypePlatformDeposit
	}
	return ""
}

// 如果当前的状态是一个错误，并且用户确认了这个错误，可以选择重试一次；
// 这样会重置该错误，让 process loop 重试
func (this *BalancePlanItem) ResetStageError(txRefID string) error {
	currentStatusString := this.StatusString
	txType := this.GetTxTypeByRefID(txRefID)
	if txType == "" {
		return fmt.Errorf("tx type not found")
	}
	switch txType {
	case TxTypeExchangeDeposit:
		this.Status = BalancePlanItemStatusExchangeDepositStart
	case TxTypeExchangeWithdraw:
		this.Status = BalancePlanItemStatusExchangeWithdrawStart
	case TxTypePlatformDeposit:
		this.Status = BalancePlanItemStatusPlatformDepositStart
	case TxTypePlatformWithdraw:
		this.Status = BalancePlanItemStatusPlatformWithdrawStart
	default:
		return fmt.Errorf("tx type not found")
	}

	this.StatusString = this.Status.String()
	this.UpdateTime = utils.Now()
	this.StatusComment = fmt.Sprintf("reset current status: %s to previous status: %s", currentStatusString, this.StatusString)
	return nil
}

// 如果当前的状态是一个错误，并且用户认为错误已经解决，可以选择标记为完成；
// 这样会将状态直接重置为完成，帮助跳出 process loop
func (this *BalancePlanItem) MarkStageDone(txRefID string) error {
	currentStatusString := this.StatusString
	txType := this.GetTxTypeByRefID(txRefID)
	if txType == "" {
		return fmt.Errorf("tx type not found")
	}
	switch txType {
	case TxTypeExchangeDeposit:
		this.Status = BalancePlanItemStatusExchangeDepositDone
	case TxTypeExchangeWithdraw:
		this.Status = BalancePlanItemStatusExchangeWithdrawDone
	case TxTypePlatformDeposit:
		this.Status = BalancePlanItemStatusPlatformDepositDone
	case TxTypePlatformWithdraw:
		this.Status = BalancePlanItemStatusPlatformWithdrawDone
	default:
		return fmt.Errorf("tx type not found")
	}
	this.StatusString = this.Status.String()
	this.UpdateTime = utils.Now()
	this.StatusComment = fmt.Sprintf("mark current status: %s as %s", currentStatusString, this.StatusString)
	return nil
}

func (this *BalancePlanItem) IsDone() bool {
	return this.Status == BalancePlanItemStatusDone || this.CancelTime != nil
}

func (this *BalancePlanItem) IsCanceled() bool {
	return this.CancelTime != nil
}

type TxType string

const (
	TxTypeUnknown          TxType = ""
	TxTypeExchangeDeposit  TxType = "exchange_deposit"
	TxTypeExchangeWithdraw TxType = "exchange_withdraw"
	TxTypePlatformDeposit  TxType = "platform_deposit"
	TxTypePlatformWithdraw TxType = "platform_withdraw"
)

type BalancePlanItemTxs struct {
	PlatformWithdrawTx *hopper.Tx `json:"platformWithdrawTx"`
	ExchangeDepositTx  *hopper.Tx `json:"exchangeDepositTx"`
	ExchangeWithdrawTx *hopper.Tx `json:"exchangeWithdrawTx"`
	PlatformDepositTx  *hopper.Tx `json:"platformDepositTx"`
}

func (this *BalancePlanItemTxs) SetWait(wait int) {
	// don't wait on platform withdraw tx, wee need to send it as soon as possible
	// only set wait on the first valid tx sequencially
	if this.ExchangeDepositTx != nil {
		this.ExchangeDepositTx.Wait = wait
	} else if this.ExchangeWithdrawTx != nil {
		this.ExchangeWithdrawTx.Wait = wait
	} else if this.PlatformDepositTx != nil {
		this.PlatformDepositTx.Wait = wait
	}
}

type Balancer struct {
	DataDir      string
	Options      *BalancerOptions // 配置，包含钱包，交易所，子账号（子账号对应的项目组），具体平衡那个项目，由 balancePlan 决定
	BalancePlans []*BalancePlan   // 平衡计划，由 copier 管理后台发起再平衡计划，balancer 根据 balancePlan 进行平衡，持久化，程序重新启动后，从未完成的部分继续

	// runtime variables
	projects               []*Project
	exchanges              []*hopper.ExchangeAPI
	wallets                []*hopper.MnemonicWallet
	subaccounts            []*hopper.ExchangeAPI
	mutex                  sync.Mutex // lock for plans
	storageMutex           sync.Mutex // lock for storage operations
	subaccountAddressMutex sync.Mutex // lock for wallet subaccount address

	// 钱包子账号和交易所子账号地址的映射，用于将钱包地址映射到子账号和钱包索引
	// 这个映射会做持久化，原因是防止 subaccount api 给的地址顺序发生变化，导致映射关系变化
	subaccountAddresses SubaccountAddresses
}

// NewBalancer creates a new Balancer
// don't panic if any error occurs, just return error
// the caller should handle the error
func NewBalancer(dataDir string) (*Balancer, error) {
	opt := &BalancerOptions{}
	optPath := filepath.Join(dataDir, "balancer.yaml")
	err := opt.Load(optPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load balancer options: %v", err)
	}

	balancer := &Balancer{
		DataDir: dataDir,
		Options: opt,
	}
	balancer.LoadBalancePlans()

	// load wallet subaccount mappings
	err = balancer.LoadSubaccountAddresses()
	if err != nil {
		logger.Errorf("failed to load subaccount addresses: %v", err)
	}

	err = hopper.SetChainConfig(filepath.Join(dataDir, "chains.yaml"))
	if err != nil {
		return nil, fmt.Errorf("failed to set chain config: %v", err)
	}
	err = balancer.SetupWallets()
	if err != nil {
		return nil, fmt.Errorf("failed to setup wallets: %v", err)
	}
	err = balancer.SetupExchanges()
	if err != nil {
		return nil, fmt.Errorf("failed to setup exchanges: %v", err)
	}
	err = balancer.SetupSubaccounts()
	if err != nil {
		return nil, fmt.Errorf("failed to setup subaccounts: %v", err)
	}
	go balancer.ProcessAllPlans()
	go balancer.PeriodicalJobs()
	return balancer, nil
}

func (this *Balancer) PeriodicalJobs() {
	go func() {
		ticker := time.NewTicker(1 * time.Minute)
		defer ticker.Stop()
		for range ticker.C {
			this.TransferSubaccountBalanceToMain()
		}
	}()
}

func (this *Balancer) TransferSubaccountBalanceToMain() {
	for _, coin := range []string{"USDC", "ETH"} {
		for _, exchange := range this.exchanges {
			switch exchange.ExchangeName {
			case hopper.OKX:
				ex := exchange.Exchange.(*hopper.OKx)
				for _, subaccountOpt := range this.Options.Subaccounts {
					if subaccountOpt.ExchangeID == exchange.ID {
						_, balance, err := ex.GetSubaccountBalance(subaccountOpt.Username, coin)
						if err != nil {
							logger.Errorf("failed to get subaccount balance: %v", err)
						}
						if balance > 0 {
							transferID, err := ex.AssetTransfer(coin, hopper.OKxTransferAccountFund, hopper.OKxTransferAccountFund, balance, hopper.OKxTransferTypeSubToMain, subaccountOpt.Username)
							if err != nil {
								logger.Errorf("failed to transfer subaccount balance: %v", err)
							}
							logger.Infof("transfer ID: %s", transferID)
						}
					}
				}
			case hopper.BITGET:
				ex := exchange.Exchange.(*hopper.Bitget)
				for _, subaccountOpt := range this.Options.Subaccounts {
					if subaccountOpt.ExchangeID == exchange.ID {
						_, balance, err := ex.GetSubaccountBalance(subaccountOpt.Username, coin)
						if err != nil {
							logger.Errorf("failed to get subaccount balance: %v", err)
						}
						if balance > 0 {
							transferID, err := ex.AssetTransfer(coin, hopper.BgTransferAccountSpot, hopper.BgTransferAccountSpot, balance, hopper.BgTransferTypeSubToMain, subaccountOpt.Username)
							if err != nil {
								logger.Errorf("failed to transfer subaccount balance: %v", err)
							}
							logger.Infof("transfer ID: %s", transferID)
						}
					}
				}
			}
		}
	}
}

func (this *Balancer) SetupWallets() error {
	for _, walletOpt := range this.Options.Wallets {
		wallet, err := hopper.NewWalletFromMnemonic(walletOpt.Mnemonic, 20)
		wallet.Name = walletOpt.ID
		wallet.Prefix = walletOpt.Prefix
		if err != nil {
			return fmt.Errorf("failed to create wallet: %v", err)
		}
		this.wallets = append(this.wallets, wallet)
	}
	return nil
}

func (this *Balancer) SetupExchanges() error {
	for _, exchangeOpt := range this.Options.Exchanges {
		exchange, err := hopper.NewExchangeAPI(exchangeOpt.ID, exchangeOpt.ExchangeName, exchangeOpt.APIKey, exchangeOpt.APISecret, false)
		if err != nil {
			return fmt.Errorf("failed to create exchange: %v", err)
		}
		_, err = exchange.IsAvailable()
		if err != nil {
			return fmt.Errorf("failed to check if exchange is available: %v", err)
		}
		this.exchanges = append(this.exchanges, exchange)
	}
	return nil
}

func (this *Balancer) SetupSubaccounts() error {
	for _, subaccountOpt := range this.Options.Subaccounts {
		exchange := this.getExchangeByID(subaccountOpt.ExchangeID)
		if exchange == nil {
			return fmt.Errorf("exchange not found: %s", subaccountOpt.ExchangeID)
		}
		subaccount, err := hopper.NewExchangeAPI(subaccountOpt.ID, exchange.ExchangeName, subaccountOpt.APIKey, subaccountOpt.APISecret, false)
		if err != nil {
			return fmt.Errorf("failed to create subaccount: %v", err)
		}
		this.subaccounts = append(this.subaccounts, subaccount)

		// populate subaccount deposit addresses map cache
		// the map cache was used to get the target address for each wallet address
		for _, platformOpt := range this.Options.Platforms {
			depositAddress, err := subaccount.GetDepositAddress(platformOpt.Coin, platformOpt.ChainName)
			if err != nil {
				return fmt.Errorf("failed to get deposit address: %v", err)
			}
			// Bitget will return []string, just ignore it
			for idx, address := range depositAddress {
				// return stable wallet index to deposit address mapping
				existingAddress := this.subaccountAddresses.FindAddress(platformOpt.Platform, platformOpt.Coin, platformOpt.ChainName, subaccountOpt.ID, idx+1)
				if existingAddress != "" {
					continue
				}
				this.subaccountAddresses = append(this.subaccountAddresses, &SubaccountAddress{
					Platform:       platformOpt.Platform,
					Coin:           platformOpt.Coin,
					ChainName:      platformOpt.ChainName,
					SubaccountID:   subaccountOpt.ID,
					WalletIndex:    idx + 1,
					DepositAddress: address,
				})
			}
		}
	}
	this.SaveSubaccountAddresses()
	return nil
}

func (this *Balancer) LoadSubaccountAddresses() error {
	this.subaccountAddressMutex.Lock()
	defer this.subaccountAddressMutex.Unlock()

	subaccountAddressesPath := filepath.Join(this.DataDir, "subaccount_addresses.yaml")
	subaccountAddressesFile, err := os.Open(subaccountAddressesPath)
	if err != nil {
		return fmt.Errorf("failed to open subaccount addresses file: %v", err)
	}
	defer subaccountAddressesFile.Close()
	err = yaml.NewDecoder(subaccountAddressesFile).Decode(&this.subaccountAddresses)
	if err != nil {
		return fmt.Errorf("failed to decode subaccount addresses: %v", err)
	}
	return nil
}

func (this *Balancer) SaveSubaccountAddresses() error {
	this.subaccountAddressMutex.Lock()
	defer this.subaccountAddressMutex.Unlock()

	subaccountAddressesPath := filepath.Join(this.DataDir, "subaccount_addresses.yaml")
	subaccountAddressesFile, err := os.Create(subaccountAddressesPath)
	if err != nil {
		log.Fatalf("Failed to create subaccount addresses file: %v", err)
	}
	defer subaccountAddressesFile.Close()
	yamlData, err := yaml.Marshal(this.subaccountAddresses)
	if err != nil {
		return fmt.Errorf("failed to marshal subaccount addresses: %v", err)
	}
	_, err = subaccountAddressesFile.Write(yamlData)
	if err != nil {
		return fmt.Errorf("failed to write subaccount addresses: %v", err)
	}
	return nil
}

func (this *Balancer) getExchangeByID(id string) *hopper.ExchangeAPI {
	for _, exchange := range this.exchanges {
		if exchange.ID == id {
			return exchange
		}
	}
	return nil
}

func (this *Balancer) LoadBalancePlans() {
	this.storageMutex.Lock()
	defer this.storageMutex.Unlock()

	planPath := filepath.Join(this.DataDir, "balance_plans.json")
	planFile, err := os.Open(planPath)
	if err != nil {
		logger.Warnf("Failed to open balance plans file: %v", err)
		return
	}
	defer planFile.Close()
	json.NewDecoder(planFile).Decode(&this.BalancePlans)
}

func (this *Balancer) SaveBalancePlans() {
	this.storageMutex.Lock()
	defer this.storageMutex.Unlock()

	planPath := filepath.Join(this.DataDir, "balance_plans.json")
	planFile, err := os.Create(planPath)
	if err != nil {
		log.Fatalf("Failed to create balance plans file: %v", err)
	}
	defer planFile.Close()

	json.NewEncoder(planFile).Encode(this.BalancePlans)
}

// projectIDs is optional, if not provided, all projects will be used
func (this *Balancer) MakeBalancePlanAuto(platform Platform, exchangeID string, projectIDs []string, amount float64, waitSeconds int, amountDirective AmountDirective) (*BalancePlan, error) {
	if len(projectIDs) == 0 {
		return nil, fmt.Errorf("projectIDs is empty")
	}
	stages := []BalanceStage{
		BalanceStagePlatformWithdraw,
		BalanceStageExchangeDeposit,
		BalanceStageExchangeWithdraw,
		BalanceStagePlatformDeposit,
	}

	if amount < 0 {
		return nil, fmt.Errorf("amount must be positive")
	}

	platformOpt := this.Options.GetPlatformOpt(platform)
	if platformOpt == nil {
		return nil, fmt.Errorf("platform not found: %s", platform)
	}

	exchangeOpt := this.Options.GetExchangeOpt(exchangeID)
	if exchangeOpt == nil {
		return nil, fmt.Errorf("exchange not found: %s", exchangeID)
	}

	plan := &BalancePlan{
		RefID:        utils.NewRandomID(),
		Stages:       stages,
		Platform:     platform,
		ChainName:    platformOpt.ChainName,
		Coin:         platformOpt.Coin,
		ExchangeID:   exchangeID,
		ExchangeName: exchangeOpt.ExchangeName,
		CreateTime:   time.Now(),
		Status:       BalancePlanStatusNew,
		WaitSeconds:  waitSeconds,
	}
	var projects []*Project
	projectIDMap := map[string]*Project{}
	for _, projectID := range projectIDs {
		for _, project := range this.projects {
			if project.ProjectID == projectID {
				projects = append(projects, project)
				projectIDMap[project.ProjectID] = project
			}
		}
	}
	sort.Strings(projectIDs)

	// check projects stopped
	unstoppedProjects := []*Project{}
	for _, project := range projects {
		if project.Status != ProjectStatusStopped {
			if project.DeleteTime == nil {
				unstoppedProjects = append(unstoppedProjects, project)
			}
		}
	}
	if len(unstoppedProjects) > 0 {
		return nil, fmt.Errorf("some projects are not stopped, please stop them first: %s", strings.Join(utils.Map(unstoppedProjects, func(project *Project) string { return project.ProjectID }), ", "))
	}

	for _, projectID := range projectIDs {
		project := projectIDMap[projectID]
		masterAgent := project.GetMasterAgent()
		if masterAgent == nil {
			return nil, fmt.Errorf("master agent not found for project: %s", project.ProjectID)
		}
		copierAgent := project.GetCopierAgent()
		if copierAgent == nil {
			return nil, fmt.Errorf("copier agent not found for project: %s", project.ProjectID)
		}
		var diff float64
		if amount == 0 {
			diff = (masterAgent.TotalMargin - copierAgent.TotalMargin) / 2
			if math.Abs(diff) < this.Options.AutoBalanceThreshold {
				logger.Infof("skip project: %s, diff: %f, less than threshold: %f", project.ProjectID, diff, this.Options.AutoBalanceThreshold)
				continue
			}
		} else {
			if masterAgent.TotalMargin > copierAgent.TotalMargin {
				diff = amount
			} else {
				diff = -amount
			}
		}
		fromAddress := masterAgent.UserID
		toAddress := copierAgent.UserID
		fromAddressAlias := masterAgent.Alias
		toAddressAlias := copierAgent.Alias
		if diff <= 0 {
			fromAddress = copierAgent.UserID
			toAddress = masterAgent.UserID
			fromAddressAlias = copierAgent.Alias
			toAddressAlias = masterAgent.Alias
		}
		balanceAmount := math.Abs(diff)
		// round it to 10 dollars
		balanceAmount = this.RoundAmount(plan.Coin, balanceAmount)
		plan.BalanceItems = append(plan.BalanceItems, &BalancePlanItem{
			RefID:            utils.NewRandomID(),
			ProjectID:        project.ProjectID,
			FromAddress:      fromAddress,
			FromAddressAlias: fromAddressAlias,
			ToAddress:        toAddress,
			ToAddressAlias:   toAddressAlias,
			BalanceAmount:    balanceAmount,
			CreateTime:       time.Now(),
		})
	}
	if len(plan.BalanceItems) == 0 {
		return nil, fmt.Errorf("no balance items found for projects: %s", strings.Join(utils.Map(projects, func(project *Project) string { return project.ProjectID }), ", "))
	}
	err := this.AddBalancePlan(plan)
	if err != nil {
		return nil, err
	}
	return plan, nil
}

func (this *Balancer) RoundAmount(coin string, amount float64) float64 {
	stables := []string{"USDC", "USDT"}
	if slices.Contains(stables, coin) {
		return math.Round(amount/10) * 10
	}
	return amount
}

func (this *Balancer) MakeBalancePlan(platform Platform, exchangeID string, walletID string, addresses []string, stages []BalanceStage, origAmount float64, coin string, waitSeconds int, amountRandom bool, amountDirective AmountDirective) (plan *BalancePlan, er error) {
	// 注意 platformType 用的是 wallet address，因为要通过 wallet address 才可以获取到钱包和平台余额
	// 用平台充值地址是不行的，因为平台充值地址是平台自己生成的，不是钱包生成的
	// 在 preparePlanItem 中生成的 txs 的时候，会自动根据 stage 来确定真实的 tx 地址，所以这里用 wallet address 就可以了
	/*
		Transaction Type                    From Type    From Address            To Type    To Address
		--------------------------------------------------------------------------------------------
		platform_withdraw                   platform     wallet address          wallet     wallet address
		platform_deposit                    wallet       wallet address          platform   wallet address
		exchange_withdraw                   exchange     exchangeName            wallet     wallet address
		exchange_deposit                    wallet       wallet address          exchange   intermediate address
		platform_withdraw,exchange_deposit  platform     wallet address          exchange   intermediate address
		exchange_withdraw,platform_deposit  exchange     exchangeName            platform   wallet address
	*/

	if origAmount < 0 {
		return nil, fmt.Errorf("amount must be positive")
	}

	platformOpt := this.Options.GetPlatformOpt(platform)
	if platformOpt == nil {
		return nil, fmt.Errorf("platform not found: %s", platform)
	}
	if coin == "" {
		return nil, fmt.Errorf("coin is empty")
	}

	_, err := platformOpt.GetWalletSubaccount(exchangeID, walletID)
	if err != nil {
		return nil, fmt.Errorf("get wallet subaccount failed: %s", err)
	}

	exchangeOpt := this.Options.GetExchangeOpt(exchangeID)
	if exchangeOpt == nil {
		return nil, fmt.Errorf("exchange not found: %s", exchangeID)
	}

	plan = &BalancePlan{
		RefID:        utils.NewRandomID(),
		Stages:       stages,
		Platform:     platform,
		ChainName:    platformOpt.ChainName,
		Coin:         coin,
		WaitSeconds:  waitSeconds,
		ExchangeID:   exchangeID,
		ExchangeName: exchangeOpt.ExchangeName,
		CreateTime:   time.Now(),
		Status:       BalancePlanStatusNew,
	}

	var wallet *hopper.MnemonicWallet
	for _, w := range this.wallets {
		if w.Name == walletID {
			wallet = w
			break
		}
	}
	if wallet == nil {
		return nil, fmt.Errorf("wallet not found: %s", walletID)
	}

	for _, indexedAddress := range wallet.Addresses {
		address := indexedAddress.Address()
		if !slices.Contains(addresses, address) {
			continue
		}

		intermediateAddress, err := this.getIntermediateAddress(platform, exchangeID, address)
		if err != nil {
			return nil, fmt.Errorf("get intermediate address failed: %s", err)
		}

		// Handle different stage combinations
		stagesStr := strings.Join(utils.Map(stages, func(stage BalanceStage) string {
			return string(stage)
		}), ",")

		var fromAddress, fromAddressAlias, toAddress, toAddressAlias string
		var fromAddressType, toAddressType AddressType

		alias := wallet.GetAddressAlias(address)

		switch stagesStr {
		case string(BalanceStagePlatformWithdraw):
			fromAddress, fromAddressAlias = address, alias
			toAddress, toAddressAlias = address, alias
			fromAddressType, toAddressType = AddressTypePlatform, AddressTypeWallet
		case string(BalanceStagePlatformDeposit):
			fromAddress, fromAddressAlias = address, alias
			toAddress, toAddressAlias = address, alias // dont use platform deposit address here, prepare item can determine the address itself
			fromAddressType, toAddressType = AddressTypeWallet, AddressTypePlatform
		case string(BalanceStageExchangeWithdraw):
			fromAddress, fromAddressAlias = intermediateAddress, string(exchangeOpt.ExchangeName)
			toAddress, toAddressAlias = address, alias
			fromAddressType, toAddressType = AddressTypeExchange, AddressTypeWallet
		case string(BalanceStageExchangeDeposit):
			fromAddress, fromAddressAlias = address, alias
			toAddress, toAddressAlias = intermediateAddress, string(exchangeOpt.ExchangeName)
			fromAddressType, toAddressType = AddressTypeWallet, AddressTypeExchange
		case string(BalanceStagePlatformWithdraw) + "," + string(BalanceStageExchangeDeposit):
			fromAddress, fromAddressAlias = address, alias
			toAddress, toAddressAlias = intermediateAddress, string(exchangeOpt.ExchangeName)
			fromAddressType, toAddressType = AddressTypePlatform, AddressTypeExchange
		case string(BalanceStageExchangeWithdraw) + "," + string(BalanceStagePlatformDeposit):
			fromAddress, fromAddressAlias = intermediateAddress, string(exchangeOpt.ExchangeName)
			toAddress, toAddressAlias = address, alias // dont use platform deposit address here, prepare item can determine the address itself
			fromAddressType, toAddressType = AddressTypeExchange, AddressTypePlatform
		default:
			return nil, fmt.Errorf("unsupported stage combination: %s", stagesStr)
		}

		// Get wallet balance if needed
		currentMargin := 0.0
		if amountDirective == AmountDirectiveTarget {
			walletBalance, platformBalance, err := this.GetBalanceForAddress(platform, address, coin)
			if err != nil {
				return nil, fmt.Errorf("get balance failed: %s", err)
			}
			// 如果是平台相关的操作，则需获取平台对应的余额
			if slices.Contains(stages, BalanceStagePlatformWithdraw) || slices.Contains(stages, BalanceStagePlatformDeposit) {
				currentMargin = platformBalance
			} else {
				// 如果是单纯钱包相关的操作，则需获取钱包对应的余额
				currentMargin = walletBalance
			}
		}

		// Calculate amount based on directive
		// for platform withdraw, target amount > current margin, skip
		// for platform deposit, target amount < current margin, skip
		// thus we can simply set a target amount and include all addresses on UI, let the backend to handle the over range problem
		amount := origAmount
		if amountDirective == AmountDirectiveTarget {
			targetAmount := origAmount
			if targetAmount > currentMargin {
				amount = targetAmount - currentMargin
				// for platform withdraw, target amount > current margin, skip
				// for example:
				// target amount: 100, current margin: 70
				// it's impossible to withdraw to make the current margin to be 100
				if slices.Contains(stages, BalanceStagePlatformWithdraw) {
					continue
				}
			} else {
				amount = currentMargin - targetAmount
				// for platform deposit, target amount < current margin, skip
				// for example:
				// target amount: 70, current margin: 100
				// it's impossible to deposit to make the current margin to be 70
				if slices.Contains(stages, BalanceStagePlatformDeposit) {
					continue
				}
			}
		}

		if amountRandom {
			amount = amount * (1 + rand.Float64()*0.2)
		}

		amount = this.RoundAmount(plan.Coin, amount)

		// for platform deposit, if amount is less than min deposit amount, skip
		plan.BalanceItems = append(plan.BalanceItems, &BalancePlanItem{
			RefID:            utils.NewRandomID(),
			FromAddress:      fromAddress,
			FromAddressAlias: fromAddressAlias,
			FromAddressType:  fromAddressType,
			ToAddress:        toAddress,
			ToAddressAlias:   toAddressAlias,
			ToAddressType:    toAddressType,
			BalanceAmount:    amount,
			CreateTime:       time.Now(),
		})
	}

	err = this.AddBalancePlan(plan)
	if err != nil {
		return nil, err
	}
	return plan, nil
}

func (this *Balancer) GetBalanceForAddress(platform Platform, address string, coin string) (walletBalance float64, platformBalance float64, err error) {
	// get project margin
	for _, project := range this.projects {
		for _, agent := range project.Agents {
			if agent.UserID == address && agent.Platform == platform {
				platformBalance = agent.TotalMargin
				break
			}
		}
	}

	platformOpt := this.Options.GetPlatformOpt(platform)
	if platformOpt == nil {
		return 0, 0, fmt.Errorf("platform not found: %s", platform)
	}

	walletID, idx, _ := this.queryWalletByAddress(address)
	var wallet *hopper.MnemonicWallet
	for _, w := range this.wallets {
		if w.Name == walletID {
			wallet = w
			break
		}
	}
	// 如果 wallet 不存在，不返回错误，返回 0，因为 platform address 不在 wallet 中
	if wallet == nil {
		return
	}

	chain := hopper.NewChain(platformOpt.ChainName)
	if chain == nil {
		return 0, 0, fmt.Errorf("get wallet balance failed, chain not found: %s", platformOpt.ChainName)
	}
	walletBalance, err = chain.GetBalance(wallet.PrivateKey(idx), address, coin)
	if err != nil {
		return 0, 0, fmt.Errorf("get wallet balance failed, wallet: %s, address: %s, coin: %s, error: %s", walletID, address, coin, err)
	}

	return walletBalance, platformBalance, nil
}

func (this *Balancer) AddBalancePlan(plan *BalancePlan) error {
	this.mutex.Lock()
	defer this.mutex.Unlock()

	// prepare balance plan, ensure all items are prepared
	// if any item preparation failed, the whole plan preparation will fail
	// preapare including:
	// 1. mapping item.intermediateAddress to subaccountOpt.DepositAddresses
	// 2. prepare txs for each item, including setup exchange/wallet private keys
	// be sure all items are prepared and ready to be processed
	err := this.PrepareBalancePlan(plan)
	if err != nil {
		return fmt.Errorf("prepare balance plan failed: %s", err)
	}
	this.BalancePlans = append(this.BalancePlans, plan)
	this.SaveBalancePlans()
	return nil
}

func (this *Balancer) PrepareBalancePlan(plan *BalancePlan) error {
	// lighter don't use wallet address as platform deposit address
	// validate platform deposit addresse, make sure they are not in project addresses
	if plan.Platform == PlatformLighter {
		projectAddresses := []string{}
		for _, project := range this.projects {
			for _, agent := range project.Agents {
				projectAddresses = append(projectAddresses, agent.UserID)
			}
		}

		for _, platformDepositAddress := range this.Options.platformDepositMap {
			if slices.Contains(projectAddresses, platformDepositAddress) {
				return fmt.Errorf("found platform deposit address in project addresses: %s", platformDepositAddress)
			}
		}
	}

	// validate all configed wallet for the platform, all of the addresses has corresponding exchange deposit address
	err := this.validatePlatformDepositAddress()
	if err != nil {
		return fmt.Errorf("validate platform deposit address failed: %s", err)
	}

	for _, item := range plan.BalanceItems {
		err := this.PrepareBalancePlanItem(plan, item, plan.Stages)
		if err != nil {
			plan.Status = BalancePlanPrepareError
			plan.StatusComment = err.Error()
			this.SaveBalancePlans()
			return fmt.Errorf("prepare balance plan item failed: %s, project id: %s, error: %s", item.RefID, item.ProjectID, err)
		}
	}
	plan.Status = BalancePlanStatusPrepared
	now := time.Now()
	plan.UpdateTime = &now
	this.SaveBalancePlans()
	return nil
}

func (this *Balancer) UnfinishedBalancePlans() []*BalancePlan {
	unfinishedPlans := []*BalancePlan{}
	for _, plan := range this.GetBalancePlans() {
		if plan.Status != BalancePlanStatusDone {
			unfinishedPlans = append(unfinishedPlans, plan)
		}
	}
	return unfinishedPlans
}

func (this *Balancer) UpdateProjects(projects []*Project) {
	this.projects = projects
	// update balance plans with updated projects data
	for _, project := range projects {
		for _, agent := range project.Agents {
			address := agent.UserID
			for _, plan := range this.GetBalancePlans() {
				// if plan is done and update time is more than 30 minutes, skip
				if plan.IsDone() && plan.UpdateTime != nil && time.Since(*plan.UpdateTime) > 30*time.Minute {
					continue
				}
				for _, item := range plan.BalanceItems {
					// if item is done and update time is more than 30 minutes, skip
					if item.IsDone() && item.UpdateTime != nil && time.Since(*item.UpdateTime) > 30*time.Minute {
						continue
					}
					if item.FromAddress == address {
						if item.FromAddressOriginalAmount == 0 {
							item.FromAddressOriginalAmount = agent.TotalMargin
						}
						item.FromAddressCurrentAmount = agent.TotalMargin
					}
					if item.ToAddress == address {
						if item.ToAddressOriginalAmount == 0 {
							item.ToAddressOriginalAmount = agent.TotalMargin
						}
						item.ToAddressCurrentAmount = agent.TotalMargin
					}
				}
			}
		}
	}
}

func (this *Balancer) ProcessAllPlans() {
	waitGroup := sync.WaitGroup{}
	for _, plan := range this.UnfinishedBalancePlans() {
		if plan.Status != BalancePlanStatusProcessing {
			continue
		}
		waitGroup.Add(1)
		go func(plan *BalancePlan) {
			defer waitGroup.Done()
			this.ProcessPlan(plan)
			this.SaveBalancePlans()
		}(plan)
	}
	waitGroup.Wait()
}

var planMutexes = xsync.NewMapOf[sync.Mutex]()

func (this *Balancer) ProcessPlan(plan *BalancePlan) error {
	planMutex, _ := planMutexes.LoadOrStore(plan.RefID, sync.Mutex{})

	ok := planMutex.TryLock()
	if !ok {
		return fmt.Errorf("plan is already being processed: %s", plan.RefID)
	}
	defer planMutex.Unlock()

	if plan.IsCanceled() {
		return nil
	}

	plan.Status = BalancePlanStatusProcessing
	now := time.Now()
	plan.UpdateTime = &now

	// stage 1: platform_withraw
	waitGroup := sync.WaitGroup{}
	for _, item := range plan.BalanceItems {
		waitGroup.Add(1)
		go func(item *BalancePlanItem) {
			defer waitGroup.Done()

		outerLoop:
			for {
				if item.IsDone() {
					break
				}
				// stage 1: platform_withdraw
				// for lighter platform, we need to wait for the extension to finish the withdraw and mark the tx as done
				// balancer could do nothing except for waiting
				for {
					tx := item.Txs.PlatformWithdrawTx
					if tx == nil {
						logger.Warnf("platform withdraw tx not found for item: %s, skip", item.RefID)
						break
					}
					if plan.Platform == PlatformLighter {
						if item.Status < BalancePlanItemStatusPlatformWithdrawStart {
							item.SetStatus(BalancePlanItemStatusPlatformWithdrawStart)
						}
						if item.CancelTime != nil {
							break outerLoop
						}
						if tx.Status == hopper.TxStatusCancelled {
							break
						}
						if item.Status >= BalancePlanItemStatusPlatformWithdrawDone {
							// extension report status isn't reliable, so we need to check item margin to confirm the withdraw is done
							diff := item.FromAddressOriginalAmount - item.FromAddressCurrentAmount
							if diff >= item.BalanceAmount*0.5 {
								break
							}
							logger.Infof("platform withdraw done for item: %s, but margin is less than balance amount, recheck", item.RefID)
						}
						time.Sleep(5 * time.Second)
					} else {
						// TODO: check if platform supports direct withdraw
					}
				}
				// stage 2: exchange_deposit
				// loop until exchange deposit done or error, user can retry or mark as done
				// and also can cancel the whole plan item if needed
				// allow tx to be nil, means there is no need for this stage, which usually happens if you are not doing a automatic balance
				// for example, if you are doing a manual tx withdraw funds from exchange only, you may not need to deposit to exchange
				for {
					tx := item.Txs.ExchangeDepositTx
					if tx == nil {
						logger.Warnf("exchange deposit tx not found for item: %s, skip", item.RefID)
						break
					}
					if item.Status < BalancePlanItemStatusExchangeDepositError {
						item.SetStatus(BalancePlanItemStatusExchangeDepositStart)
					}
					if item.CancelTime != nil {
						break outerLoop
					}
					if tx.Status == hopper.TxStatusCancelled {
						break
					}
					err := this.ProcessTx(plan, item, tx)
					if err != nil {
						item.SetStatus(BalancePlanItemStatusExchangeDepositError)
						item.StatusComment = err.Error()
						this.SaveBalancePlans()
						time.Sleep(1 * time.Minute)
						continue
					} else {
						item.SetStatus(BalancePlanItemStatusExchangeDepositDone)
						break
					}
				}
				// stage 3: exchange_withdraw
				// loop until exchange withdraw done or error, user can retry or mark as done
				// and also can cancel the whole plan item if needed
				// allow tx to be nil, means there is no need for this stage, which usually happens if you are not doing a automatic balance
				// for example, if you are doing a manual tx deposit funds to exchange only, you may not need to withdraw from exchange
				for {
					tx := item.Txs.ExchangeWithdrawTx
					if tx == nil {
						logger.Warnf("exchange withdraw tx not found for item: %s, skip", item.RefID)
						break
					}
					if item.Status < BalancePlanItemStatusExchangeWithdrawError {
						item.SetStatus(BalancePlanItemStatusExchangeWithdrawStart)
					}
					if item.CancelTime != nil {
						break outerLoop
					}
					if tx.Status == hopper.TxStatusCancelled {
						break
					}
					err := this.ProcessTx(plan, item, tx)
					if err != nil {
						item.SetStatus(BalancePlanItemStatusExchangeWithdrawError)
						item.StatusComment = err.Error()
						this.SaveBalancePlans()
						time.Sleep(3 * time.Minute)
						continue
					} else {
						item.SetStatus(BalancePlanItemStatusExchangeWithdrawDone)
						break
					}
				}

				for {
					tx := item.Txs.PlatformDepositTx
					if tx == nil {
						logger.Warnf("platform deposit tx not found for item: %s", item.RefID)
						break
					}
					if item.Status < BalancePlanItemStatusPlatformDepositError {
						item.SetStatus(BalancePlanItemStatusPlatformDepositStart)
						// if there is a exchange withdraw tx, we need to wait for it to be done
						if item.Txs.ExchangeWithdrawTx != nil {
							time.Sleep(2 * time.Minute)
						}
					}
					if item.CancelTime != nil {
						break outerLoop
					}
					if tx.Status == hopper.TxStatusCancelled {
						break
					}
					err := this.ProcessTx(plan, item, tx)
					if err != nil {
						item.SetStatus(BalancePlanItemStatusPlatformDepositError)
						item.StatusComment = err.Error()
						this.SaveBalancePlans()
						time.Sleep(1 * time.Minute)
						continue
					} else {
						item.SetStatus(BalancePlanItemStatusPlatformDepositDone)
						break
					}
				}
				item.SetStatus(BalancePlanItemStatusDone)
				this.SaveBalancePlans()
			}
		}(item)
	}
	waitGroup.Wait()
	plan.Status = BalancePlanStatusDone
	plan.UpdateTime = utils.Now()
	this.SaveBalancePlans()
	return nil
}

func (this *Balancer) CancelPlan(planRefID string) error {
	plan := this.GetPlanWithRefID(planRefID)
	if plan == nil {
		return fmt.Errorf("plan not found")
	}

	// cancel here will make the plan jump out the processing stage, to make it properly shutdown
	now := time.Now()
	plan.CancelTime = &now
	for _, item := range plan.BalanceItems {
		item.CancelTime = &now
	}
	this.SaveBalancePlans()
	return nil
}

func (this *Balancer) GetPlanWithRefID(planRefID string) *BalancePlan {
	for _, plan := range this.BalancePlans {
		if plan.RefID == planRefID {
			return plan
		}
	}
	return nil
}

func (this *Balancer) GetPlatformOpt(platform Platform) *PlatformOpt {
	for _, platformOpt := range this.Options.Platforms {
		if platformOpt.Platform == platform {
			return platformOpt
		}
	}
	return nil
}

func (this *Balancer) GetChain(chainName string) *hopper.Chain {
	for _, chain := range hopper.GetChains() {
		if chain.Name == chainName {
			return chain
		}
	}
	return nil
}

func (this *Balancer) GetExchangePrivateKey(exchangeID string) string {
	for _, exchange := range this.Options.Exchanges {
		if exchange.ID == exchangeID {
			return fmt.Sprintf("%s/%s", exchange.APIKey, exchange.APISecret)
		}
	}
	return ""
}

func (this *Balancer) GetPrivateKey(address string) string {
	for _, wallet := range this.wallets {
		index := -1
		for i, addr := range wallet.Addresses {
			if addr.Address() == address {
				index = i
				break
			}
		}
		if index == -1 {
			continue
		}
		return wallet.PrivateKeys[index].PrivateKey()
	}
	return ""
}

func (this *Balancer) GetProjectWithID(projectID string) *Project {
	for _, project := range this.projects {
		if project.ProjectID == projectID {
			return project
		}
	}
	return nil
}

func (this *Balancer) queryWalletByAddress(address string) (walletID string, idx int, alias string) {
	for _, wallet := range this.wallets {
		for _, addr := range wallet.Addresses {
			if strings.EqualFold(addr.Address(), address) {
				return wallet.Name, addr.Index(), wallet.GetAddressAlias(addr.Address())
			}
		}
	}
	return "", -1, ""
}

func (this *Balancer) validatePlatformDepositAddress() error {
	for _, platformOpt := range this.Options.Platforms {
		if !platformOpt.UseWalletAddressToDeposit && platformOpt.PlatformDepositAddress == "" {
			for exchangeID := range platformOpt.WalletSubaccountMapping {
				for walletID := range platformOpt.WalletSubaccountMapping[exchangeID] {
					for _, wallet := range this.wallets {
						if wallet.Name == walletID {
							addresses := []string{}
							for _, addr := range wallet.Addresses {
								addresses = append(addresses, addr.Address())
							}
							// check if the wallet use api to get deposit address
							useAPI := this.checkWalletUseAPI(platformOpt.Platform, exchangeID, walletID)
							if useAPI {
								depositAddresses, err := this.mappingSubaccountDepositAddresses(platformOpt.Platform, exchangeID, addresses)
								if err != nil {
									if strings.Contains(err.Error(), "subaccount not found for platform and wallet") {
										continue
									}
									return fmt.Errorf("failed to validate platform deposit address: %s", err)
								}
								if len(depositAddresses) == 0 {
									return fmt.Errorf("deposit address not found for wallet: %s", walletID)
								}
								for i, depositAddress := range depositAddresses {
									walletAddress := addresses[i]
									logger.Infof("validated deposit address, [%02d]@%s: %s -> %s", i+1, walletID, walletAddress, depositAddress)
								}
							}
						}
					}
				}
			}
		}
	}
	return nil
}

func (this *Balancer) checkWalletUseAPI(platform Platform, exchangeID, walletID string) bool {
	platformOpt := this.GetPlatformOpt(platform)
	if platformOpt == nil {
		return false
	}
	subaccountID := platformOpt.WalletSubaccountMapping[exchangeID][walletID]
	if subaccountID == "" {
		return false
	}
	var subaccountOpt *SubaccountOpt
	for _, subaccount := range this.Options.Subaccounts {
		if subaccount.ID == subaccountID {
			subaccountOpt = subaccount
			break
		}
	}
	if subaccountOpt == nil {
		return false
	}
	return subaccountOpt.UseAPI()
}

func (this *Balancer) mappingExactSubaccountDepositAddress(platform Platform, exchangeID string, address string) (targetAddress string, err error) {
	addresses := []string{address}
	targetAddresses, err := this.mappingSubaccountDepositAddresses(platform, exchangeID, addresses)
	if err != nil {
		return "", err
	}
	return targetAddresses[0], nil
}

func (this *Balancer) mappingSubaccountDepositAddresses(platform Platform, exchangeID string, addresses []string) (targetAddresses []string, err error) {
	platformOpt := this.GetPlatformOpt(platform)
	if platformOpt == nil {
		return nil, fmt.Errorf("platform not found")
	}

	for _, address := range addresses {
		walletID, idx, _ := this.queryWalletByAddress(address)
		if walletID == "" {
			return nil, fmt.Errorf("wallet not found")
		}

		subaccountID := platformOpt.WalletSubaccountMapping[exchangeID][walletID]
		if subaccountID == "" {
			// omit this errror for caller, it's normal for some platforms
			return nil, fmt.Errorf("subaccount not found for platform and wallet, platform: %s, wallet: %s", platformOpt.Platform, walletID)
		}

		// prevent query exchange subaccount for each address
		// use the map cache to get the target address
		targetAddress := this.subaccountAddresses.FindAddress(platformOpt.Platform, platformOpt.Coin, platformOpt.ChainName, subaccountID, idx)

		if targetAddress == "" {
			return nil, fmt.Errorf("address is empty")
		}
		targetAddresses = append(targetAddresses, targetAddress)
	}
	return targetAddresses, nil
}

func (this *Balancer) PrepareBalancePlanItem(plan *BalancePlan, item *BalancePlanItem, sepecificStages []BalanceStage) (er error) {
	defer func() {
		if er != nil {
			item.SetStatus(BalancePlanItemStatusPrepareError)
			item.StatusComment = er.Error()
			this.SaveBalancePlans()
		}
	}()

	platformOpt := this.GetPlatformOpt(plan.Platform)
	if platformOpt == nil {
		er = fmt.Errorf("platform not found")
		return
	}

	exch := this.getExchangeByID(plan.ExchangeID)
	if exch == nil {
		er = fmt.Errorf("exchange not found: %s", plan.ExchangeID)
		return
	}

	// get balance for from address
	err := this.updateBalanceForPlanItem(plan, item)
	if err != nil {
		er = fmt.Errorf("update balance failed: %s", err)
		return
	}

	txs := &BalancePlanItemTxs{}

	// even if we can't send tx in the platform, we still need to prepare the tx
	// we simply use the same api to mark this tx as done
	if slices.Contains(sepecificStages, BalanceStagePlatformWithdraw) {
		txs.PlatformWithdrawTx = &hopper.Tx{
			ID:          utils.NewRandomID(),
			PrivateKey:  "", // we don't need private key for this tx, it's a fake tx
			FromAddress: item.FromAddress,
			ToAddress:   item.FromAddress,
			Coin:        plan.Coin,
			Amount:      item.BalanceAmount,
			Wait:        0,
		}
	}

	// set intermediate address for exchange deposit
	// use subaccountOpt to map address alias to address
	// ensure one-on-one mapping for each address alias
	if slices.Contains(sepecificStages, BalanceStageExchangeDeposit) {
		intermediateAddress, err := this.getIntermediateAddress(plan.Platform, plan.ExchangeID, item.FromAddress)
		if err != nil {
			er = fmt.Errorf("get intermediate address failed: %s", err)
			return
		}
		if intermediateAddress == "" {
			er = fmt.Errorf("intermediate address not found: %s", item.FromAddressAlias)
			return
		}
		item.IntermediateAddress = intermediateAddress

		fromAddressPrivateKey := this.GetPrivateKey(item.FromAddress)
		if fromAddressPrivateKey == "" {
			er = fmt.Errorf("private key not found for address: %s", item.FromAddress)
			return
		}
		txs.ExchangeDepositTx = &hopper.Tx{
			ID:          utils.NewRandomID(),
			PrivateKey:  fromAddressPrivateKey,
			FromAddress: item.FromAddress,
			ToAddress:   item.IntermediateAddress,
			Coin:        plan.Coin,
			Amount:      item.BalanceAmount,
			Wait:        0,
		}
	}
	if slices.Contains(sepecificStages, BalanceStageExchangeWithdraw) {
		exchangePrivateKey := this.GetExchangePrivateKey(plan.ExchangeID)
		if exchangePrivateKey == "" {
			er = fmt.Errorf("private key not found for address: %s", item.IntermediateAddress)
			return
		}
		// can't do exchange withdraw with amount == 0, it's meaningless
		if item.BalanceAmount > 0 {
			fromAddress := string(exch.ExchangeName)
			txs.ExchangeWithdrawTx = &hopper.Tx{
				ID:          utils.NewRandomID(),
				PrivateKey:  exchangePrivateKey,
				FromAddress: fromAddress,
				ToAddress:   item.ToAddress,
				Coin:        plan.Coin,
				Amount:      item.BalanceAmount,
				Wait:        0,
			}
		} else {
			logger.Warnf("skip exchange withdraw for zero amount: %f", item.BalanceAmount)
		}
	}
	if slices.Contains(sepecificStages, BalanceStagePlatformDeposit) {
		// TODO: check if platform lighter support direct deposit
		if plan.Platform == PlatformLighter {
			useSameDepositAddress := platformOpt.UseWalletAddressToDeposit
			if plan.Coin == platformOpt.Coin && item.BalanceAmount >= platformOpt.MinDepositAmount {
				var depositAddress string
				if !useSameDepositAddress {
					address, err := this.mappingPlatformDepositAddress(plan.Platform, item.ToAddressAlias)
					if err != nil {
						er = fmt.Errorf("mapping deposit address failed: %s", item.ToAddressAlias)
						return
					} else {
						depositAddress = address
					}
				} else {
					depositAddress = item.ToAddress
				}
				if depositAddress == "" {
					er = fmt.Errorf("deposit address not found: %s", item.ToAddressAlias)
					return
				}
				fromAddress := item.ToAddress
				if fromAddress == depositAddress {
					er = fmt.Errorf("to-address must be different from from-address")
					return
				}
				txs.PlatformDepositTx = &hopper.Tx{
					ID:          utils.NewRandomID(),
					PrivateKey:  this.GetPrivateKey(fromAddress),
					FromAddress: fromAddress,
					ToAddress:   depositAddress,
					Coin:        plan.Coin,
					Amount:      item.BalanceAmount,
					Wait:        0,
				}
			} else {
				logger.Warnf("skip platform deposit for amount: %f, min deposit amount: %f", item.BalanceAmount, platformOpt.MinDepositAmount)
			}
		}
	}

	// random wait seconds between 0 and plan.WaitSeconds
	// txs will be sent in parallel and random orders
	if plan.WaitSeconds > 0 {
		wait := rand.Intn(plan.WaitSeconds)
		txs.SetWait(wait) // only set wait on the first valid tx, no need to set wait on other txs
	}

	item.Txs = txs
	item.SetStatus(BalancePlanItemStatusPrepareDone)
	return nil
}

func (this *Balancer) updateBalanceForPlanItem(plan *BalancePlan, item *BalancePlanItem) (er error) {
	// leave alone if from address is exchange address
	if item.FromAddressType == AddressTypeWallet || item.FromAddressType == AddressTypePlatform {
		walletBalance, platformBalance, err := this.GetBalanceForAddress(plan.Platform, item.FromAddress, plan.Coin)
		if err != nil {
			er = fmt.Errorf("get balance failed: %s", err)
			return
		}

		if item.FromAddressType == AddressTypeWallet {
			item.FromAddressOriginalAmount = walletBalance
		} else if item.FromAddressType == AddressTypePlatform {
			item.FromAddressOriginalAmount = platformBalance
		}
	}

	// leave alone if to address is exchange address
	if item.ToAddressType == AddressTypeWallet || item.ToAddressType == AddressTypePlatform {
		// get balance for to address
		walletBalance, platformBalance, err := this.GetBalanceForAddress(plan.Platform, item.ToAddress, plan.Coin)
		if err != nil {
			er = fmt.Errorf("get balance failed: %s", err)
			return
		}

		if item.ToAddressType == AddressTypeWallet {
			item.ToAddressOriginalAmount = walletBalance
		} else if item.ToAddressType == AddressTypePlatform {
			item.ToAddressOriginalAmount = platformBalance
		}
	}
	return
}

func (this *Balancer) mappingPlatformDepositAddress(platform Platform, alias string) (address string, err error) {
	depositKey := string(platform) + "::" + alias
	address, ok := this.Options.platformDepositMap[depositKey]
	if !ok {
		return "", fmt.Errorf("platform deposit address not found: %s", depositKey)
	}
	return address, nil
}

func (this *Balancer) getIntermediateAddress(platform Platform, exchangeID string, walletAddress string) (addr string, er error) {
	platformOpt := this.GetPlatformOpt(platform)
	if platformOpt == nil {
		return "", fmt.Errorf("platform not found")
	}
	var intermediateAddress string
	var err error
	// if using API to get deposit address, mapping it with subaccount address
	// otherwise, mapping it from options manual mappings
	walletID, _, addressAlias := this.queryWalletByAddress(walletAddress)
	if walletID == "" {
		return "", fmt.Errorf("no wallet found for address: %s", walletAddress)
	}
	useAPI := this.checkWalletUseAPI(platform, exchangeID, walletID)
	if useAPI {
		intermediateAddress, err = this.mappingExactSubaccountDepositAddress(platform, exchangeID, walletAddress)
		if err != nil {
			er = fmt.Errorf("mapping intermediate address failed: %s, error: %s", walletAddress, err)
			return
		}
	} else {
		if intermediateAddress == "" && this.Options.exchangeDepositMap != nil {
			exchangeDepositMap := this.Options.exchangeDepositMap[exchangeID] // exchangeID -> { alias -> address }
			if exchangeDepositMap != nil {
				intermediateAddress = exchangeDepositMap[addressAlias]
			}
		}
	}
	return intermediateAddress, nil
}

func (this *Balancer) ProcessTx(plan *BalancePlan, item *BalancePlanItem, tx *hopper.Tx) error {
	if tx == nil {
		return fmt.Errorf("tx not found")
	}
	chain := this.GetChain(plan.ChainName)
	if chain == nil {
		return fmt.Errorf("chain not found: %s", plan.ChainName)
	}
	// random sleep 1-20 seconds
	time.Sleep(time.Duration(rand.Intn(20)+1) * time.Second)
	stop, err := tx.Send(chain, this.SaveBalancePlans)
	if stop && err != nil {
		tx.Status = hopper.TxStatusFailed
		return fmt.Errorf("tx failed, error: %s", err)
	}
	return nil
}

type TxAction string

const (
	TxActionRetry  TxAction = "retry"
	TxActionCancel TxAction = "cancel"
	TxActionDone   TxAction = "done"
)

// RequestTxAction 请求 tx 操作，tx 操作包括重试和完成
func (this *Balancer) RequestTxAction(planRefID string, txRefID string, action TxAction) (er error) {
	plan := this.GetPlanWithRefID(planRefID)
	if plan == nil {
		return fmt.Errorf("plan not found")
	}
	platformOpt := this.GetPlatformOpt(plan.Platform)
	if platformOpt == nil {
		return fmt.Errorf("platform not found")
	}
	chain := this.GetChain(platformOpt.ChainName)
	if chain == nil {
		return fmt.Errorf("chain not found: %s", platformOpt.ChainName)
	}

	item, tx := this.GetTxWithRefID(txRefID)
	if tx == nil {
		return fmt.Errorf("tx not found")
	}

	if action == TxActionRetry {
		// 重置状态为 Start，process loop 会重新处理
		tx.Status = hopper.UnknownTxStatus
		tx.Wait = 0 // reset wait to 0, so tx will be sent immediately
		er = item.ResetStageError(txRefID)
	} else if action == TxActionDone {
		tx.Status = hopper.TxStatusSuccess
		er = item.MarkStageDone(txRefID)
	} else if action == TxActionCancel {
		tx.Cancel()
	}
	this.SaveBalancePlans()
	return
}

// CancelPlanItem 取消计划项，计划项包括 tx 操作
func (this *Balancer) CancelPlanItem(planRefID string, itemRefID string) error {
	plan := this.GetPlanWithRefID(planRefID)
	if plan == nil {
		return fmt.Errorf("plan not found")
	}
	item := this.GetPlanItemWithRefID(itemRefID)
	if item == nil {
		return fmt.Errorf("item not found")
	}
	if item.Status == BalancePlanItemStatusDone {
		return fmt.Errorf("item already done")
	}
	item.CancelTime = utils.Now()
	if item.Txs.ExchangeDepositTx != nil {
		item.Txs.ExchangeDepositTx.Cancel()
	}
	if item.Txs.ExchangeWithdrawTx != nil {
		item.Txs.ExchangeWithdrawTx.Cancel()
	}
	if item.Txs.PlatformWithdrawTx != nil {
		item.Txs.PlatformWithdrawTx.Cancel()
	}
	if item.Txs.PlatformDepositTx != nil {
		item.Txs.PlatformDepositTx.Cancel()
	}
	this.SaveBalancePlans()
	return nil
}

func (this *Balancer) GetPlanItemWithRefID(itemRefID string) *BalancePlanItem {
	for _, plan := range this.BalancePlans {
		for _, item := range plan.BalanceItems {
			if item.RefID == itemRefID {
				return item
			}
		}
	}
	return nil
}

func (this *Balancer) GetTxWithRefID(txRefID string) (item *BalancePlanItem, tx *hopper.Tx) {
	for _, plan := range this.BalancePlans {
		for _, item := range plan.BalanceItems {
			if item.Txs != nil && item.Txs.ExchangeDepositTx != nil && item.Txs.ExchangeDepositTx.ID == txRefID {
				return item, item.Txs.ExchangeDepositTx
			}
			if item.Txs != nil && item.Txs.ExchangeWithdrawTx != nil && item.Txs.ExchangeWithdrawTx.ID == txRefID {
				return item, item.Txs.ExchangeWithdrawTx
			}
			if item.Txs != nil && item.Txs.PlatformWithdrawTx != nil && item.Txs.PlatformWithdrawTx.ID == txRefID {
				return item, item.Txs.PlatformWithdrawTx
			}
			if item.Txs != nil && item.Txs.PlatformDepositTx != nil && item.Txs.PlatformDepositTx.ID == txRefID {
				return item, item.Txs.PlatformDepositTx
			}
		}
	}
	return nil, nil
}

// QueryExtensionWithdraw 查询 extension 的 withdraw tx
func (this *Balancer) QueryExtensionWithdraw(projectID string, userID string) *hopper.Tx {
	for _, plan := range this.GetBalancePlans() {
		if plan.IsDone() {
			continue
		}
		if plan.Status != BalancePlanStatusProcessing {
			continue
		}
		for _, item := range plan.BalanceItems {
			if item.ProjectID == projectID && item.FromAddress == userID {
				if item.Txs.PlatformWithdrawTx != nil {
					if item.Txs.PlatformWithdrawTx.Status != hopper.TxStatusSuccess && item.Txs.PlatformWithdrawTx.Status != hopper.TxStatusCancelled {
						return item.Txs.PlatformWithdrawTx
					}
				}
			}
		}
	}
	return nil
}

// MarkExtensionWithdrawDone 标记 extension 的 withdraw tx 已完成
func (this *Balancer) MarkExtensionWithdrawDone(projectID string, txRefID string) error {
	for _, plan := range this.GetBalancePlans() {
		if plan.IsDone() {
			continue
		}
		for _, item := range plan.BalanceItems {
			if item.ProjectID == projectID && item.Txs.PlatformWithdrawTx != nil && item.Txs.PlatformWithdrawTx.ID == txRefID {
				item.Txs.PlatformWithdrawTx.Status = hopper.TxStatusSuccess
				item.SetStatus(BalancePlanItemStatusPlatformWithdrawDone)
				this.SaveBalancePlans()
				return nil
			}
		}
	}
	return fmt.Errorf("tx not found")
}

func (this *Balancer) DeleteBalancePlan(planRefID string) error {
	plan := this.GetPlanWithRefID(planRefID)
	if plan == nil {
		return fmt.Errorf("plan not found")
	}
	plan.DeleteTime = utils.Now()
	this.SaveBalancePlans()
	return nil
}

func (this *Balancer) GetBalancePlans() []*BalancePlan {
	balancePlans := []*BalancePlan{}
	for _, plan := range this.BalancePlans {
		if plan.DeleteTime == nil {
			balancePlans = append(balancePlans, plan)
		}
	}
	return balancePlans
}

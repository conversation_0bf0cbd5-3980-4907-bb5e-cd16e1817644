package backscanner

import (
	"bytes"
	"fmt"
	"os"
)

// <PERSON><PERSON> reads a file forwards line by line, calling lineCallback for each line.
// The lineCallback function receives the line content and should return true to continue scanning
// or false to stop scanning.
func Scan(filePath string, lineCallback func(line []byte) (continueLoop bool)) error {
	f, err := os.OpenFile(filePath, os.O_RDONLY, os.ModePerm)
	if err != nil {
		return fmt.Errorf("open file error: %s", err)
	}
	defer f.Close()

	// Read the file line by line
	buf := make([]byte, 0, 1024)
	for {
		// Read a chunk of data
		chunk := make([]byte, 1024)
		n, err := f.Read(chunk)
		if err != nil && n == 0 {
			break
		}
		chunk = chunk[:n]

		// Process the chunk
		for {
			// Find the next newline
			i := bytes.IndexByte(chunk, '\n')
			if i < 0 {
				// No newline found, append to buffer
				buf = append(buf, chunk...)
				break
			}

			// Found a newline, process the line
			line := append(buf, chunk[:i]...)
			line = dropCR(line)
			continueLoop := lineCallback(line)
			if !continueLoop {
				return nil
			}

			// Move to next line
			chunk = chunk[i+1:]
			buf = buf[:0]
		}
	}

	// Process any remaining data in buffer
	if len(buf) > 0 {
		buf = dropCR(buf)
		lineCallback(buf)
	}

	return nil
}

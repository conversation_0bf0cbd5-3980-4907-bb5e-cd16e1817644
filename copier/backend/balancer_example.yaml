# Balancer Configuration Example

# Wallet configurations
wallets:
  - id: "wallet1"
    prefix: L1 # 后续会自动加上 W01, 组合成 L2W01 这样的 alias
    mnemonic: "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about"
  - id: "wallet2"
    prefix: L2
    mnemonic: "zoo zoo zoo zoo zoo zoo zoo zoo zoo zoo zoo wrong"

# Exchange configurations
exchanges:
  - id: "bg_main"
    exchange_name: "bitget"
    api_key: "your_bg_api_key_here"
    api_secret: "your_bg_api_secret_here"

  - id: "okx_main"
    exchange_name: "okx"
    api_key: "your_okx_api_key_here"
    api_secret: "your_okx_api_secret_here"

# Subaccount configurations
subaccounts:
  - id: "bg_sub1"
    exchange_id: "bg_main"
    username: 
    api_key: 
    api_secret:
    deposit_addresses:
      master: "******************************************"
      copier: "******************************************"
  - id: "okx_sub1"
    exchange_id: "okx_main"
    username: 
    api_key: 
    api_secret:
    deposit_addresses:
      master: "******************************************"
      copier: "******************************************"


platform_deposits:
  - id: "lighter"
    platform: "lighter"
    addresses:
      L1W01:
      L1W02: 

# Platform configurations
platforms:
  - platform: "lighter"
    chain_name: "arbitrum"
    coin: "USDC"
    min_deposit_amount: 6
    is_platform_same_deposit_address: false
    wallet_subaccount_mapping:
      okx_ken: 
        wallet1: "okx_sub1"
      bg_ken:
        wallet1: "bg_sub1"

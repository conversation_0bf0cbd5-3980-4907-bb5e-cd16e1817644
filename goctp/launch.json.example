{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Test",
            "type": "go",
            "request": "launch",
            "mode": "test",
            "program": "${workspaceFolder}/tests/trade_test.go",
            "env": {
                // "CTP_TRADEFRONT": "tcp://***************:10101",
                // "CTP_QUOTEFRONT": "tcp://***************:10111",
                // "CTP_TRADEFRONT": "tcp://**************:10203", // 移动
                // "CTP_QUOTEFRONT": "tcp://**************:10213", // 移动
                "CTP_TRADEFRONT": "tcp://***************:10201", // 电信1
                "CTP_QUOTEFRONT": "tcp://***************:10211", // 电信1
                // "CTP_TRADEFRONT": "tcp://***************:10202", // 电信2
                // "CTP_QUOTEFRONT": "tcp://***************:10212", // 电信2
                "CTP_BROKERID": "9999",
                "CTP_INVESTORID": "182536",
                "CTP_PASSWORD": "hyGrN@7zkjgKMn6",
            }
        },
        {
            "name": "Debug Main",
            "type": "go",
            "request": "launch",
            "mode": "debug",
            "program": "${workspaceFolder}/cmds/gateway/main.go",
            "env": {
                // "CTP_TRADEFRONT": "tcp://***************:10101",
                // "CTP_QUOTEFRONT": "tcp://***************:10111",
                // "CTP_TRADEFRONT": "tcp://**************:10203", // 移动
                // "CTP_QUOTEFRONT": "tcp://**************:10213", // 移动
                // "CTP_TRADEFRONT": "tcp://***************:10201", // 电信1
                // "CTP_QUOTEFRONT": "tcp://***************:10211", // 电信1
                // "CTP_TRADEFRONT": "tcp://***************:10202", // 电信2
                // "CTP_QUOTEFRONT": "tcp://***************:10212", // 电信2
                "CTP_TRADEFRONT": "tcp://***************:10130", // 7x24
                "CTP_QUOTEFRONT": "tcp://***************:10131", // 7x24
                "CTP_BROKERID": "9999",
                "CTP_INVESTORID": "182536",
                "CTP_PASSWORD": "hyGrN@7zkjgKMn6",
            }
        },
    ]
}
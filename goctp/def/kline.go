package def

import (
	"encoding/binary"
	"fmt"
	"strings"
)

const (
	KLineValueLen = 48
)

type KLineHeader struct {
	Exchange string `json:"exchange" validate:"required" binding:"required"`
	Symbol   string `json:"symbol" validate:"required" binding:"required"`
	Period   string `json:"period" validate:"required,min=4,max=10" binding:"required,min=4,max=10"`
}

// 获取块名
func (k *KLineHeader) Bucket() []byte {
	bucket := fmt.Sprintf("%s-%s-%s", k.Exchange, k.Symbol, k.Period)
	return []byte(strings.ToUpper(bucket))
}

type KLine struct {
	KLineHeader
	Open        float64 `json:"open"`
	High        float64 `json:"high"`
	Low         float64 `json:"low"`
	Close       float64 `json:"close"`
	Volume      float64 `json:"volume"`
	QuoteVolume float64 `json:"quote_volume,omitempty"`
	Time        int64   `json:"time"`
}

// 获取Key
func (k *KLine) Key() []byte {
	return ToKey(k.Time)
}

// 获取Value
func (k *KLine) Value() []byte {
	buf := make([]byte, KLineValueLen)
	Float64bytes(k.Open, buf[:8])
	Float64bytes(k.High, buf[8:16])
	Float64bytes(k.Low, buf[16:24])
	Float64bytes(k.Close, buf[24:32])
	Float64bytes(k.Volume, buf[32:40])
	Float64bytes(k.QuoteVolume, buf[40:])
	return buf
}

func (k *KLine) Unpack(key, val []byte) {
	k.Time = int64(binary.BigEndian.Uint64(key))
	k.Open = Float64frombytes(val[:8])
	k.High = Float64frombytes(val[8:16])
	k.Low = Float64frombytes(val[16:24])
	k.Close = Float64frombytes(val[24:32])
	k.Volume = Float64frombytes(val[32:40])
	k.QuoteVolume = Float64frombytes(val[40:48])
}

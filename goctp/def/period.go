package def

import (
	"strings"
)

type periods map[string]int64

// 获取基础K线以及聚合参数
func (p *periods) Base(period string) (string, int64) {
	if strings.HasSuffix(period, "min") {
		return "1min", (*p)[period] / 60
	} else if strings.HasSuffix(period, "hour") {
		return "1hour", (*p)[period] / 60 / 60
	} else if strings.HasSuffix(period, "day") {
		return "1day", (*p)[period] / 86400
	} else if strings.HasSuffix(period, "week") {
		return "1day", (*p)[period] / 86400
	}
	panic("invalid period " + period)
}

var Periods = periods{
	"1min":   60,
	"3min":   60 * 3,
	"5min":   60 * 5,
	"10min":  60 * 10,
	"15min":  60 * 15,
	"30min":  60 * 30,
	"1hour":  60 * 60,
	"2hour":  60 * 60 * 2,
	"4hour":  60 * 60 * 4,
	"6hour":  60 * 60 * 6,
	"8hour":  60 * 60 * 8,
	"12hour": 60 * 60 * 12,
	"1day":   60 * 60 * 24,
	"2day":   60 * 60 * 24 * 2,
	"3day":   60 * 60 * 24 * 3,
	"1week":  60 * 60 * 24 * 7,
}

package def

import (
	"encoding/binary"
	"math"
)

func Float64bytes(float float64, bytes []byte) {
	bits := math.Float64bits(float)
	binary.LittleEndian.PutUint64(bytes, bits)
}

func Float64frombytes(bytes []byte) float64 {
	bits := binary.LittleEndian.Uint64(bytes)
	float := math.Float64frombits(bits)
	return float
}

func <PERSON><PERSON>ey(timestamp int64) []byte {
	var buf = make([]byte, 8)
	binary.BigEndian.PutUint64(buf, uint64(timestamp))
	return buf
}

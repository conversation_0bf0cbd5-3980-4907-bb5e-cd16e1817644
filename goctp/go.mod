module github.com/wizhodl/goctp

go 1.24

toolchain go1.24.2

require (
	filippo.io/age v1.0.0
	github.com/armon/go-socks5 v0.0.0-20160902184237-e75332964ef5
	github.com/caarlos0/env v3.5.0+incompatible
	github.com/davecgh/go-spew v1.1.1
	github.com/gin-gonic/gin v1.7.7
	github.com/go-resty/resty/v2 v2.7.0
	github.com/jinzhu/copier v0.3.5
	github.com/onrik/logrus v0.9.0
	github.com/orcaman/concurrent-map v1.0.0
	github.com/sirupsen/logrus v1.8.1
	github.com/tidwall/gjson v1.14.0
	github.com/wizhodl/encembed v0.0.0-20220423153945-085b64d1c687
	github.com/wizhodl/quanter v0.0.0-20220314102047-de35810d0628
	go.etcd.io/bbolt v1.3.6
	golang.org/x/text v0.3.6
	gopkg.in/yaml.v2 v2.4.0
)

require (
	github.com/puzpuzpuz/xsync/v2 v2.5.1
	github.com/shopspring/decimal v1.2.0 // indirect
	github.com/stretchr/testify v1.6.1 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	golang.org/x/net v0.0.0-20211029224645-99673261e6eb // indirect
	honnef.co/go/tools v0.0.1-2020.1.4 // indirect
)

require (
	github.com/asaskevich/govalidator v0.0.0-20210307081110-f21760c49a8d // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-playground/locales v0.13.0 // indirect
	github.com/go-playground/universal-translator v0.17.0 // indirect
	github.com/go-playground/validator/v10 v10.4.1 // indirect
	github.com/golang/protobuf v1.4.3 // indirect
	github.com/gorilla/websocket v1.5.0
	github.com/json-iterator/go v1.1.9 // indirect
	github.com/leodido/go-urn v1.2.0 // indirect
	github.com/mattn/go-isatty v0.0.12 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.1 // indirect
	github.com/puzpuzpuz/xsync v1.3.0 // indirect
	github.com/robfig/cron/v3 v3.0.1
	github.com/spf13/cast v1.3.1 // indirect
	github.com/stevedomin/termtable v0.0.0-20150929082024-09d29f3fd628 // indirect
	github.com/ugorji/go/codec v1.1.7 // indirect
	go.uber.org/atomic v1.7.0
	go.uber.org/multierr v1.5.0 // indirect
	go.uber.org/zap v1.16.0
	golang.org/x/crypto v0.0.0-20210817164053-32db794688a5 // indirect
	golang.org/x/sys v0.0.0-20210903071746-97244b99971b // indirect
	golang.org/x/time v0.0.0-20220224211638-0e9765cccd65
	google.golang.org/protobuf v1.25.0 // indirect
)

package test

import (
	"fmt"
	"math"
	"os"
	"sort"
	"strings"
	"testing"
	"time"

	"github.com/puzpuzpuz/xsync/v2"
	goctp "github.com/wizhodl/goctp/ctp"
)

var (
	tradeFront = "tcp://180.168.146.187:10130"
	quoteFront = "tcp://180.168.146.187:10131"
	// tradeFront   = "tcp://180.168.146.187:10202"
	// quoteFront   = "tcp://180.168.146.187:10212"
	brokerID     = "9999"
	investorID   = "182536" // simnow 账号：008107/1
	password     = "hyGrN@7zkjgKMn6"
	appID        = "simnow_client_test"
	authCode     = "0000000000000000"
	instrumentID = "sc2206"
	lastPrice    = 0.0
)

var api = goctp.NewCTPController(true)
var trade *goctp.Trade
var quote *goctp.Quote

func TestTrade(t *testing.T) {
	// setupWithEnvs()
	api.Connect(initQuote, initTrade)
	trade = api.Trade
	quote = api.Quote

	for !trade.IsLogin.Load() {
		fmt.Println("waiting login resp...")
		time.Sleep(1 * time.Second)
	}

	time.Sleep(3 * time.Second)

	fmt.Printf("\n\n%v lastprice: %v\n", instrumentID, lastPrice)

	testQryInstrumentMarginRate(t)
	testInstruments(t)
	testAccount(t)
	testCreateOrder(t)
	testPositions(t)
	testOrders(t)
	testTrades(t)
	testCloseOrder(t)
	testCancelOrder(t)
	testGetOpenOrders(t)

	fmt.Println("\n\nDONE")
}

func initTrade(trade *goctp.Trade) {

	trade.RegOnFrontConnected(func() {
		fmt.Println("connected")
		go func() {
			trade.Login(investorID, password, brokerID, appID, authCode)
		}()
	})

	trade.RegOnRspUserLogin(func(login *goctp.RspUserLoginField, info *goctp.RspInfoField) {
		fmt.Printf("trade login info: %#v, %#v", login, info)
	})

	trade.RegOnRtnOrder(func(field *goctp.OrderField) {
		if field.InstrumentID != instrumentID {
			return
		}
		fmt.Printf("\nRegOnRtnOrder: %#v\n", field)
	})

	trade.RegOnErrRtnOrder(func(field *goctp.OrderField, info *goctp.RspInfoField) {
		fmt.Printf("\nRegOnErrRtnOrder: %#v, %#v\n", field, info)
	})

	trade.RegOnRtnInstrumentStatus(func(field *goctp.InstrumentStatus) {
		if field.InstrumentID != instrumentID {
			return
		}
		fmt.Printf("\nRegOnRtnInstrumentStatus: %#v\n", field)
	})

	trade.ReqConnect(tradeFront)
	fmt.Println("connected to trade...")
}

func onTick(data *goctp.TickField) {
	// fmt.Printf("tick data: %#v", data)
	lastPrice = data.LastPrice
}

func initQuote(quote *goctp.Quote) {
	quote.RegOnFrontConnected(func() {
		fmt.Println("quote connected")
		quote.Login(investorID, password, brokerID)
	})
	quote.RegOnRspUserLogin(func(login *goctp.RspUserLoginField, info *goctp.RspInfoField) {
		fmt.Println("quote login:", info)
		quote.Subscribe(instrumentID)
	})
	quote.RegOnTick(onTick)
	fmt.Println("quote connecting...")
	quote.ReqConnect(quoteFront)
}

func setupWithEnvs() {
	if os.Getenv("CTP_TRADEFRONT") != "" {
		tradeFront = os.Getenv("CTP_TRADEFRONT")
	}
	if os.Getenv("CTP_QUOTEFRONT") != "" {
		quoteFront = os.Getenv("CTP_QUOTEFRONT")
	}
	if os.Getenv("CTP_BROKERID") != "" {
		brokerID = os.Getenv("CTP_BROKERID")
	}
	if os.Getenv("CTP_INVESTORID") != "" {
		investorID = os.Getenv("CTP_INVESTORID") // simnow 账号：008107/1
	}
	if os.Getenv("CTP_PASSWORD") != "" {
		password = os.Getenv("CTP_PASSWORD")
	}
}

func testInstruments(t *testing.T) {
	instruments := trade.Instruments
	count := 0
	count = printProductIDExchangeMap(instruments)
	if count == 0 {
		t.Fatalf("no instruments")
	}
}

func testQryInstrumentMarginRate(t *testing.T) {
	err := trade.QueryInstrumentMarginRate(instrumentID)
	if err != nil {
		t.Fatalf("qry margin rate err: %s", err)
	}
}

func printProductIDExchangeMap(instruments *xsync.MapOf[string, *goctp.InstrumentField]) (count int) {
	count = 0
	productIDMap := map[string]string{}

	instruments.Range(func(key string, instrument *goctp.InstrumentField) bool {
		if instrument.ProductClass == goctp.ProductClassFutures {
			productIDMap[instrument.ProductID] = strings.ToUpper(instrument.ExchangeID)
		}
		count += 1

		// if instrument.InstrumentID == instrumentID {
		// 	fmt.Printf("\nInstrument Info: %#v\n", instrument)
		// }
		return true
	})

	keys := []string{}

	for key := range productIDMap {
		keys = append(keys, key)
	}

	sort.SliceStable(keys, func(i int, j int) bool { return keys[i] < keys[j] })

	for _, k := range keys {
		fmt.Printf("\n\"%s\": \"%s\",", k, productIDMap[k])
	}
	return
}

func testAccount(t *testing.T) {
	account := trade.Account
	fmt.Printf("\nAccount: \n%#v\n", account)
	fmt.Printf("\nBalance: \n%#v\n", account.Balance)
	fmt.Printf("\nAvailable: \n%#v\n", account.Available)
	if account.Balance == 0 {
		t.Fatalf("no balance found")
	}
}

func roundPrice(price, tickSize float64) float64 {
	return math.Round(price/tickSize) * tickSize
}

func testCreateOrder(t *testing.T) {
	order, err := trade.CreateOrder(instrumentID, goctp.DirectionBuy, goctp.OffsetFlagOpen, goctp.TimeConditionGFD, roundPrice(lastPrice*1.01, 0.5), 1, 0, goctp.UnknownTriggerDirection, nil)
	if err != nil {
		orderID := order.GetOrderID()
		fmt.Printf("\nreq high price buy order: %v\n", orderID)
	}

	order, err = trade.CreateOrder(instrumentID, goctp.DirectionBuy, goctp.OffsetFlagOpen, goctp.TimeConditionGFD, roundPrice(lastPrice*0.99, 0.5), 1, 0, goctp.UnknownTriggerDirection, nil)
	if err != nil {
		orderID := order.GetOrderID()
		fmt.Printf("\nreq low price buy order: %v\n", orderID)
	}

	time.Sleep(3 * time.Second)

	// 多空双开
	order, err = trade.CreateOrder(instrumentID, goctp.DirectionSell, goctp.OffsetFlagOpen, goctp.TimeConditionGFD, roundPrice(lastPrice*0.99, 0.5), 1, 0, goctp.UnknownTriggerDirection, nil)
	if err != nil {
		orderID := order.GetOrderID()
		fmt.Printf("\nreq sell open order: %v\n", orderID)
	}

	// 条件单
	order, err = trade.CreateOrder(instrumentID, goctp.DirectionBuy, goctp.OffsetFlagOpen, goctp.TimeConditionGFD, roundPrice(lastPrice*1.02, 0.5), 1, roundPrice(lastPrice*1.01, 0.5), goctp.TriggerDirectionHigher, nil)
	if err != nil {
		orderID := order.GetOrderID()
		fmt.Printf("\nreq stop limit buy order: %v\n", orderID)
	}

	time.Sleep(3 * time.Second)
}

func testPositions(t *testing.T) {
	positions := trade.Positions
	positions.Range(func(key string, value *goctp.PositionField) bool {
		fmt.Printf("\n[Position] %v - %#v\n", key, value)
		return true
	})
}

func testCloseOrder(t *testing.T) {
	positions := trade.Positions
	positions.Range(func(key string, p *goctp.PositionField) bool {
		if p.InstrumentID != instrumentID {
			return true
		}
		if p.Position > 0 && p.PositionDirection == goctp.PosiDirectionLong {
			// orderID := trade.ReqOrderInsert(instrumentID, goctp.DirectionSell, goctp.OffsetFlagClose, roundPrice(lastPrice*0.99, 0.5), p.Position, 0, goctp.UnknownTriggerDirection)
			// 平多
			// 除了上期所 SHFE/能源中心 INE 外，不区分平今平昨
			order, err := trade.CreateOrder(instrumentID, goctp.DirectionSell, goctp.OffsetFlagCloseToday, goctp.TimeConditionGFD, roundPrice(lastPrice*0.99, 0.5), p.Position, 0, goctp.UnknownTriggerDirection, nil)
			if err != nil {
				orderID := order.GetOrderID()
				fmt.Printf("\nclose long position order: %v\n", orderID)
				time.Sleep(3 * time.Second)
			}
		}
		return true
	})
}

func testOrders(t *testing.T) {
	orders := trade.Orders
	orders.Range(func(key string, order *goctp.OrderField) bool {
		if order.InstrumentID == instrumentID {
			fmt.Printf("\n[Order]%v - %#v\n", key, order)
		}
		return true
	})
}

func isOpenOrder(order *goctp.OrderField) bool {
	// return order.OrderStatus == goctp.OrderStatusNoTradeQueueing || order.OrderStatus == goctp.OrderStatusPartTradedQueueing
	return order.OrderStatus != goctp.OrderStatusAllTraded &&
		order.OrderStatus != goctp.OrderStatusPartTradedNotQueueing &&
		order.OrderStatus != goctp.OrderStatusNoTradeNotQueueing &&
		order.OrderStatus != goctp.OrderStatusCanceled
}

func testGetOpenOrders(t *testing.T) {
	orders := trade.Orders
	openOrderCnt := 0
	orders.Range(func(key string, order *goctp.OrderField) bool {
		if order.InstrumentID == instrumentID && isOpenOrder(order) {
			fmt.Printf("\n[Open Order]%v - %#v\n", key, order)
			openOrderCnt += 1
		}
		return true
	})
	fmt.Printf("\nopen order count %v", openOrderCnt)
}

func testTrades(t *testing.T) {
	trades := trade.Trades
	trades.Range(func(key string, value *goctp.TradeField) bool {
		fmt.Printf("\n[Trade]%v - %#v\n", key, value)
		return true
	})
}

func testCancelOrder(t *testing.T) {
	order, err := trade.CreateOrder(instrumentID, goctp.DirectionBuy, goctp.OffsetFlagOpen, goctp.TimeConditionGFD, roundPrice(lastPrice*0.99, 1), 1, 0, goctp.UnknownTriggerDirection, nil)
	if err != nil {
		orderID := order.GetOrderID()
		fmt.Printf("\norder to cancel: %v", orderID)
		time.Sleep(3 * time.Second)
		testGetOrderByID(orderID)

		time.Sleep(1 * time.Second)

		// 取消请求
		if _, err := trade.CancelOrder(orderID, ""); err == nil {
			fmt.Printf("\ncancel order %v success\n", orderID)
		} else {
			t.Fatalf("cancel order %v failed", orderID)
		}

		time.Sleep(3 * time.Second)
	}
}

func testGetOrderByID(id string) {
	if o, ok := trade.Orders.Load(id); ok {
		fmt.Printf("\nGetOrderByID(%s): %#v\n", id, o)
	}
}

package ctp

/*
#cgo CPPFLAGS: -fPIC -I./libs/v6.6.1_P1_20210406
#cgo LDFLAGS: -fPIC -L./ -Wl,-rpath=./ -lctp_quote -lstdc++

#include "ThostFtdcUserApiDataType.h"
#include "ThostFtdcUserApiStruct.h"
void* qCreateApi();
void* qCreateSpi();
void* qRegisterSpi(void*, void*);
void* qRegisterFront(void*, char*);
void* qInit(void*);
void* qJoin(void*);
void* qRelease(void*);
int qReqUserLogin(void*, struct CThostFtdcReqUserLoginField*, int);
void* qSubscribeMarketData(void*, char *ppInstrumentID[], int nCount);

void qSetOnFrontConnected(void*, void*);
int qFrontConnected();
void qSetOnFrontDisconnected(void*, void*);
int qFrontDisConnected(int reason);
void qSetOnRspUserLogin(void*, void*);
int qRspUserLogin(struct CThostFtdcRspUserLoginField *pRspUserLogin, struct CThostFtdcRspInfoField *pRspInfo, int nRequestID, _Bool bIsLast);
void qSetOnRtnDepthMarketData(void*, void*);
int qRtnDepthMarketData(struct CThostFtdcDepthMarketDataField *pDepthMarketData);
#include <stdlib.h>
#include <stdint.h>
*/
import "C"

import (
	"fmt"
	"os"
	"sync"
	"time"
	"unsafe"

	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/goctp/ctp/define"
	"github.com/wizhodl/quanter/common/zlog"
	"go.uber.org/atomic"
)

func (q *Quote) getReqID() int {
	return int(q.reqID.Inc())
}

var q *Quote

// NewQuote new md api instanse
func NewQuote(controller *CTPController) *Quote {
	zlog.Debugf("[goctp quote] new quote")
	// 执行目录下创建 log目录
	_, err := os.Stat("log")
	if err != nil {
		os.Mkdir("log", os.ModePerm)
	}
	q = &Quote{
		Ticks:              sync.Map{},
		rateLimitQuery:     NewRateLimiter(controller, LimiterTypeQuery),
		requestQueue:       make(chan IRequest, 10000),
		requestCloseChan:   make(chan struct{}, 1),
		requestResultChans: xsync.NewMapOf[chan RequestResult](),
		requestIDMap:       xsync.NewMapOf[string](),
		IsLogin:            atomic.NewBool(false),
		reqID:              atomic.NewInt32(0),
	}
	q.controller = controller
	q.api = C.qCreateApi()
	spi := C.qCreateSpi()
	C.qRegisterSpi(q.api, spi)

	C.qSetOnFrontConnected(spi, C.qFrontConnected)
	C.qSetOnFrontDisconnected(spi, C.qFrontDisConnected)
	C.qSetOnRspUserLogin(spi, C.qRspUserLogin)
	C.qSetOnRtnDepthMarketData(spi, C.qRtnDepthMarketData)

	go q.doRequestLoop()
	go q.requestQueueLoop()
	return q
}

func (q *Quote) doRequestLoop() {
	for {
		select {
		case <-q.requestCloseChan: // 主动退出查询循环
			return
		case req := <-q.requestQueue:
			go func(request IRequest) {
				requestID := request.GetID()
				ctpReqID := q.getReqID()
				// zlog.Debugf("[goctp quote] received request: %s (%d:%s)", request.GetType(), ctpReqID, request.GetID())

				// 这里不管是否安静查询，都需要存这个 queryID 和 reqID 的关联，否则定时查询的 Positions 会没有数据
				resultKeys := request.GetResultKeys()
				if len(resultKeys) > 0 {
					for _, resultKey := range resultKeys {
						q.requestIDMap.Store(resultKey, requestID)
					}
				}
				q.requestIDMap.Store(fmt.Sprintf("%d", ctpReqID), requestID)
				// 1分钟后，把映射到 requestID 上的所有 key 都删掉
				time.AfterFunc(1*time.Minute, func() {
					q.requestIDMap.Range(func(resultKey, requestID string) bool {
						if requestID == request.GetID() {
							q.requestIDMap.Delete(resultKey)
							// zlog.Debugf("[goctp trade] delete request result key: %s", resultKey)
						}
						return true
					})
				})

				var reqResult = ReqResultRateLimit
				var err error
				// 集中处理 Query 的限流问题，其他类型的 Request 自己处理限流问题
				if SliceContains([]RequestType{RequestTypeQuoteSubscribe}, request.GetType()) {
					err = q.rateLimitQuery.Wait("", "")
				}
				if err != nil {
					zlog.Errorf("[goctp quote] request rate limit error: %s", err)
					reqResult = ReqResultRateLimit
				} else {
					reqResult = request.Do(ctpReqID)
				}
				// zlog.Debugf("[goctp trade] do (%s-%s) request result: %d", request.GetType(), request.GetID(), reqResult)
				if reqResult < 0 {
					if resultChan, found := q.requestResultChans.Load(requestID); found {
						err := toReqError(reqResult)
						resultChan <- RequestResult{
							Success:  false,
							ErrorMsg: fmt.Sprintf("%s", err),
						}
					}
				}
			}(req)
		}

	}
}

// 循环查询持仓&资金
func (q *Quote) requestQueueLoop() {
	zlog.Debugf("[goctp quote] query loop called")

	for {
		if !q.controller.QuoteConnected.Load() {
			time.Sleep(3 * time.Second)
			continue
		}
		for !q.IsLogin.Load() {
			// zlog.Debugf("[goctp quote] not logged in, wait 5s")
			time.Sleep(5 * time.Second)
			if q.InvestorID != "" {
				q.Login(q.InvestorID, q.Password, q.BrokerID)
			}
		}
	}
}

// Release 接口消毁
func (q *Quote) Release() {
	zlog.Debugf("[goctp quote] release")
	C.qRelease(q.api)
}

// ReqConnect 连接前置;Join阻塞，请用goroutine
func (q *Quote) ReqConnect(addr string) {
	zlog.Debugf("[goctp quote] req connect: %s", addr)
	front := C.CString(addr)
	C.qRegisterFront(q.api, front)
	defer C.free(unsafe.Pointer(front))
	C.qInit(q.api)
	// C.qJoin(q.api)
}

// RegOnFrontConnected 注册前置响应
func (q *Quote) RegOnFrontConnected(on OnFrontConnectedType) {
	q.onFrontConnected = on
}

// RegOnFrontDisConnected 注册连接响应
func (q *Quote) RegOnFrontDisConnected(on OnFrontDisConnectedType) {
	q.onFrontDisConnected = on
}

// RegOnRspUserLogin 注册登录响应
func (q *Quote) RegOnRspUserLogin(on OnRspUserLoginType) {
	q.onRspUserLogin = on
}

// RegOnTick 注册行情响应
func (q *Quote) RegOnTick(on OnTickType) {
	q.onTick = on
}

//export qRtnDepthMarketData
func qRtnDepthMarketData(field *C.struct_CThostFtdcDepthMarketDataField) C.int {
	dataField := (*define.CThostFtdcDepthMarketDataField)(unsafe.Pointer(field))
	instrumentID := Bytes2String(dataField.InstrumentID[:])
	exchangeID := Bytes2String(dataField.ExchangeID[:])
	// zlog.Debugf("[goctp quote] rtn depth market data: %s", instrumentID)
	if q.onTick == nil {
		return 0
	}
	if exchangeID == "" {
		exchangeID = GetExchangeID(instrumentID)
	}
	if exchangeID != "" {
		tick := TickField{
			TradingDay:      Bytes2String(dataField.TradingDay[:]),
			InstrumentID:    instrumentID,
			ExchangeID:      exchangeID,
			LastPrice:       float64(dataField.LastPrice),
			OpenPrice:       float64(dataField.OpenPrice),
			HighestPrice:    float64(dataField.HighestPrice),
			LowestPrice:     float64(dataField.LowestPrice),
			Volume:          int(dataField.Volume),
			Turnover:        float64(dataField.Turnover),
			OpenInterest:    float64(dataField.OpenInterest),
			ClosePrice:      float64(dataField.ClosePrice),
			SettlementPrice: float64(dataField.SettlementPrice),
			UpperLimitPrice: float64(dataField.UpperLimitPrice),
			LowerLimitPrice: float64(dataField.LowerLimitPrice),
			CurrDelta:       float64(dataField.CurrDelta),
			UpdateTime:      Bytes2String(dataField.UpdateTime[:]),
			UpdateMillisec:  int(dataField.UpdateMillisec),
			BidPrice1:       float64(dataField.BidPrice1),
			BidVolume1:      int(dataField.BidVolume1),
			AskPrice1:       float64(dataField.AskPrice1),
			AskVolume1:      int(dataField.AskVolume1),
			BidPrice2:       float64(dataField.BidPrice2),
			BidVolume2:      int(dataField.BidVolume2),
			AskPrice2:       float64(dataField.AskPrice2),
			AskVolume2:      int(dataField.AskVolume2),
			BidPrice3:       float64(dataField.BidPrice3),
			BidVolume3:      int(dataField.BidVolume3),
			AskPrice3:       float64(dataField.AskPrice3),
			AskVolume3:      int(dataField.AskVolume3),
			BidPrice4:       float64(dataField.BidPrice4),
			BidVolume4:      int(dataField.BidVolume4),
			AskPrice4:       float64(dataField.AskPrice4),
			AskVolume4:      int(dataField.AskVolume4),
			BidPrice5:       float64(dataField.BidPrice5),
			BidVolume5:      int(dataField.BidVolume5),
			AskPrice5:       float64(dataField.AskPrice5),
			AskVolume5:      int(dataField.AskVolume5),
			AveragePrice:    float64(dataField.AveragePrice),
			ActionDay:       Bytes2String(dataField.ActionDay[:]),
		}
		processQuoteRequestResult(fmt.Sprintf("subscribe:%s", instrumentID), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success: true,
			}
		})
		// TODO: disable symbol 的时候要把对应的 tick 删掉
		if q.controller.IsTrading(instrumentID) {
			q.Ticks.Store(instrumentID, &tick)
		}
		q.onTick(&tick)
		now := time.Now()
		q.controller.LastTickTime = &now
		return 0
	}
	return 1
}

//export qRspUserLogin
func qRspUserLogin(field *C.struct_CThostFtdcRspUserLoginField, info *C.struct_CThostFtdcRspInfoField, i C.int, b C._Bool) C.int {
	loginField := (*define.CThostFtdcRspUserLoginField)(unsafe.Pointer(field))
	infoField := (*define.CThostFtdcRspInfoField)(unsafe.Pointer(info))
	zlog.Debugf("[goctp quote] rsp user login: %d - %s", int(infoField.ErrorID), Bytes2String(infoField.ErrorMsg[:]))
	if infoField.ErrorID == 0 {
		q.IsLogin.Store(true)
		now := time.Now()
		q.controller.QuoteLoginTime = &now
		processQuoteRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success: true,
			}
		})
	} else {
		processQuoteRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success:  false,
				ErrorID:  int(infoField.ErrorID),
				ErrorMsg: Bytes2String(infoField.ErrorMsg[:]),
			}
		})
	}
	if q.onRspUserLogin != nil {
		q.onRspUserLogin(&RspUserLoginField{
			TradingDay:  string(loginField.TradingDay[:]),
			LoginTime:   string(loginField.LoginTime[:]),
			BrokerID:    string(loginField.BrokerID[:]),
			UserID:      string(loginField.UserID[:]),
			FrontID:     int(loginField.FrontID),
			SessionID:   int(loginField.SessionID),
			MaxOrderRef: string(loginField.MaxOrderRef[:]),
		}, &RspInfoField{
			ErrorID:  int(infoField.ErrorID),
			ErrorMsg: Bytes2String(infoField.ErrorMsg[:]),
		})
	}
	return 0
}

func processQuoteRequestResult(reqIDOrKey string, callback func(resultChan chan RequestResult)) {
	if reqIDOrKey != "0" {
		if queryID, found := q.requestIDMap.Load(reqIDOrKey); found {
			if resultChan, found := q.requestResultChans.Load(queryID); found {
				if callback != nil {
					callback(resultChan)
					zlog.Debugf("[goctp quote] process request result success, callback called, reqID: (%s), queryID: (%s)", reqIDOrKey, queryID)
				}
			}
		}
	}
}

func processQuoteRequestResultMultipleKeys(reqIDOrKey []string, callback func(resultChan chan RequestResult)) {
	for _, key := range reqIDOrKey {
		processRequestResult(key, callback)
	}
}

//export qFrontConnected
func qFrontConnected() C.int {
	zlog.Debugf("[goctp quote] front connected")
	if q.onFrontConnected != nil {
		q.onFrontConnected()
	}
	return 0
}

//export qFrontDisConnected
func qFrontDisConnected(reason C.int) C.int {
	zlog.Debugf("[goctp quote] front disconnected, reason: %d", int(reason))
	q.controller.QuoteConnected.Store(false)
	q.controller.QuoteConnectedTime = nil
	q.controller.Quote.IsLogin.Store(false)
	if q.onFrontDisConnected != nil {
		q.onFrontDisConnected(int(reason))
	}
	return 0
}

type QuoteLoginRequest struct {
	BaseRequest
	InvestorID string
	Password   string
	BrokerID   string
}

func (this QuoteLoginRequest) Do(reqID int) (reqResult int) {
	zlog.Debugf("[goctp quote] req login: %s", this.InvestorID)
	q.InvestorID = this.InvestorID
	q.BrokerID = this.BrokerID
	q.Password = this.Password
	f := define.CThostFtdcReqUserLoginField{}
	copy(f.UserID[:], q.InvestorID)
	copy(f.BrokerID[:], q.BrokerID)
	copy(f.Password[:], []byte(q.Password))
	copy(f.UserProductInfo[:], []byte(ProductInfo))
	ret := C.qReqUserLogin(q.api, (*C.struct_CThostFtdcReqUserLoginField)(unsafe.Pointer(&f)), C.int(reqID))
	reqResult = int(ret)
	return
}

type SubscribeRequest struct {
	BaseRequest
	InstrumentID string
}

func (this SubscribeRequest) GetResultKeys() []string {
	return []string{fmt.Sprintf("subscribe:%s", this.InstrumentID)}
}

func (this SubscribeRequest) Do(reqID int) (reqResult int) {
	zlog.Debugf("[goctp quote] req subscribe: %s", this.InstrumentID)
	instrCStr := C.CString(this.InstrumentID)
	defer C.free(unsafe.Pointer(instrCStr))

	inst := make([]*C.char, 1)
	inst[0] = (*C.char)(unsafe.Pointer(instrCStr))
	C.qSubscribeMarketData(q.api, (**C.char)(unsafe.Pointer(&inst[0])), C.int(1))
	return
}

func (q *Quote) SendRequest(req IRequest, successCallback func(reqResult RequestResult)) (er error) {
	// 请求完成后立即删除 resultChan
	// resultChan 读取一次后，不会再读取，如果有其他回调拿到 resultChan 往里面写 result，会导致卡死
	defer q.requestResultChans.Delete(req.GetID())

	// 重要：resultChan 长度可能需要长一些
	// 如果这个 resultChan 的长度设得太短，因为创建订单会多次读取 resultChan，会导致卡住
	// 上面的 defer 已经处理过，resultChan 的长度暂时还是设为 1
	resultChan := make(chan RequestResult, 5)
	q.requestResultChans.Store(req.GetID(), resultChan)
	q.requestQueue <- req
	// zlog.Debugf("[goctp trade] send request %s - %s", req.GetType(), req.GetID())

	select {
	case result := <-resultChan:
		if result.Success {
			if successCallback != nil {
				successCallback(result)
			}
		} else {
			er = result.Error()
		}
	case <-time.After(time.Duration(req.GetTimeout()) * time.Second):
		er = fmt.Errorf("timed out")
	}
	return
}

func (q *Quote) Login(investorID, password, brokerID string) (er error) {
	req := QuoteLoginRequest{
		BaseRequest: BaseRequest{
			Type: RequestTypeQuoteLogin,
			ID:   NewRandomID(),
		},
		InvestorID: investorID,
		Password:   password,
		BrokerID:   brokerID,
	}
	er = q.SendRequest(req, nil)
	return
}

func (q *Quote) Subscribe(instrumentID string) (er error) {
	req := SubscribeRequest{
		BaseRequest: BaseRequest{
			Type: RequestTypeQuoteSubscribe,
			ID:   NewRandomID(),
		},
		InstrumentID: instrumentID,
	}
	er = q.SendRequest(req, nil)
	return
}

package ctp

import (
	"fmt"
	"time"
)

func (this *OrderField) GetOrderID() string {
	ctpOrderID := fmt.Sprintf("%d_%d_%s", this.FrontID, this.SessionID, this.OrderRef)
	return ctpOrderID
}

func (this *TickField) GetUpdateTime() time.Time {
	t := ParseTimeWithString(this.ActionDay, this.UpdateTime)
	return t
}

func (ins *InstrumentField) IsDelivered() bool {
	now := time.Now()
	if ins.DeliveryYear > now.Year() {
		return false
	}
	if ins.DeliveryYear == now.Year() && ins.DeliveryMonth > int(now.Month()) {
		return false
	}
	return true
}

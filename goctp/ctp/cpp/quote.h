#pragma once

#include "../libs/v6.6.1_P1_20210406/ThostFtdcMdApi.h"

#include <string.h>

class Quote: CThostFtdcMdSpi
{
public:
    Quote(void);
    //针对收到空反馈的处理
    CThostFtdcRspInfoField rif;
    CThostFtdcRspInfoField* repare(CThostFtdcRspInfoField *pRspInfo)
    {
        if (pRspInfo == NULL)
        {
            memset(&rif, 0, sizeof(rif));
            return &rif;
        }
        else
            return pRspInfo;
    }

	///当客户端与交易后台建立起通信连接时（还未登录前），该方法被调用。
	typedef int (*FrontConnected)();
	void *_FrontConnected;
	virtual void OnFrontConnected(){if (_FrontConnected) ((FrontConnected)_FrontConnected)();}

	///当客户端与交易后台通信连接断开时，该方法被调用。当发生这个情况后，API会自动重新连接，客户端可不做处理。
		///@param nReason 错误原因
		///        0x1001 网络读失败
		///        0x1002 网络写失败
		///        0x2001 接收心跳超时
		///        0x2002 发送心跳失败
		///        0x2003 收到错误报文
	typedef int (*FrontDisconnected)(int nReason);
	void *_FrontDisconnected;
	virtual void OnFrontDisconnected(int nReason){if (_FrontDisconnected) ((FrontDisconnected)_FrontDisconnected)(nReason);}

	///心跳超时警告。当长时间未收到报文时，该方法被调用。
		///@param nTimeLapse 距离上次接收报文的时间
	typedef int (*HeartBeatWarning)(int nTimeLapse);
	void *_HeartBeatWarning;
	virtual void OnHeartBeatWarning(int nTimeLapse){if (_HeartBeatWarning) ((HeartBeatWarning)_HeartBeatWarning)(nTimeLapse);}

	///登录请求响应
	typedef int (*RspUserLogin)(CThostFtdcRspUserLoginField *pRspUserLogin, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);
	void *_RspUserLogin;
	virtual void OnRspUserLogin(CThostFtdcRspUserLoginField *pRspUserLogin, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
    {
        if (_RspUserLogin)
        {
            if (pRspUserLogin)
                ((RspUserLogin)_RspUserLogin)(pRspUserLogin, repare(pRspInfo), nRequestID, bIsLast);
            else
            {
                CThostFtdcRspUserLoginField f = {};
                ((RspUserLogin)_RspUserLogin)(&f, repare(pRspInfo), nRequestID, bIsLast);
            }
        }
    }

	///登出请求响应
	typedef int (*RspUserLogout)(CThostFtdcUserLogoutField *pUserLogout, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);
	void *_RspUserLogout;
	virtual void OnRspUserLogout(CThostFtdcUserLogoutField *pUserLogout, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
    {
        if (_RspUserLogout)
        {
            if (pUserLogout)
                ((RspUserLogout)_RspUserLogout)(pUserLogout, repare(pRspInfo), nRequestID, bIsLast);
            else
            {
                CThostFtdcUserLogoutField f = {};
                ((RspUserLogout)_RspUserLogout)(&f, repare(pRspInfo), nRequestID, bIsLast);
            }
        }
    }

	///请求查询组播合约响应
	typedef int (*RspQryMulticastInstrument)(CThostFtdcMulticastInstrumentField *pMulticastInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);
	void *_RspQryMulticastInstrument;
	virtual void OnRspQryMulticastInstrument(CThostFtdcMulticastInstrumentField *pMulticastInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
    {
        if (_RspQryMulticastInstrument)
        {
            if (pMulticastInstrument)
                ((RspQryMulticastInstrument)_RspQryMulticastInstrument)(pMulticastInstrument, repare(pRspInfo), nRequestID, bIsLast);
            else
            {
                CThostFtdcMulticastInstrumentField f = {};
                ((RspQryMulticastInstrument)_RspQryMulticastInstrument)(&f, repare(pRspInfo), nRequestID, bIsLast);
            }
        }
    }

	///错误应答
	typedef int (*RspError)(CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);
	void *_RspError;
	virtual void OnRspError(CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
    {
        if (_RspError)
        {
            if (pRspInfo)
                ((RspError)_RspError)(repare(pRspInfo), nRequestID, bIsLast);
            else
            {
                CThostFtdcRspInfoField f = {};
                ((RspError)_RspError)(repare(&f), nRequestID, bIsLast);
            }
        }
    }

	///订阅行情应答
	typedef int (*RspSubMarketData)(CThostFtdcSpecificInstrumentField *pSpecificInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);
	void *_RspSubMarketData;
	virtual void OnRspSubMarketData(CThostFtdcSpecificInstrumentField *pSpecificInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
    {
        if (_RspSubMarketData)
        {
            if (pSpecificInstrument)
                ((RspSubMarketData)_RspSubMarketData)(pSpecificInstrument, repare(pRspInfo), nRequestID, bIsLast);
            else
            {
                CThostFtdcSpecificInstrumentField f = {};
                ((RspSubMarketData)_RspSubMarketData)(&f, repare(pRspInfo), nRequestID, bIsLast);
            }
        }
    }

	///取消订阅行情应答
	typedef int (*RspUnSubMarketData)(CThostFtdcSpecificInstrumentField *pSpecificInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);
	void *_RspUnSubMarketData;
	virtual void OnRspUnSubMarketData(CThostFtdcSpecificInstrumentField *pSpecificInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
    {
        if (_RspUnSubMarketData)
        {
            if (pSpecificInstrument)
                ((RspUnSubMarketData)_RspUnSubMarketData)(pSpecificInstrument, repare(pRspInfo), nRequestID, bIsLast);
            else
            {
                CThostFtdcSpecificInstrumentField f = {};
                ((RspUnSubMarketData)_RspUnSubMarketData)(&f, repare(pRspInfo), nRequestID, bIsLast);
            }
        }
    }

	///订阅询价应答
	typedef int (*RspSubForQuoteRsp)(CThostFtdcSpecificInstrumentField *pSpecificInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);
	void *_RspSubForQuoteRsp;
	virtual void OnRspSubForQuoteRsp(CThostFtdcSpecificInstrumentField *pSpecificInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
    {
        if (_RspSubForQuoteRsp)
        {
            if (pSpecificInstrument)
                ((RspSubForQuoteRsp)_RspSubForQuoteRsp)(pSpecificInstrument, repare(pRspInfo), nRequestID, bIsLast);
            else
            {
                CThostFtdcSpecificInstrumentField f = {};
                ((RspSubForQuoteRsp)_RspSubForQuoteRsp)(&f, repare(pRspInfo), nRequestID, bIsLast);
            }
        }
    }

	///取消订阅询价应答
	typedef int (*RspUnSubForQuoteRsp)(CThostFtdcSpecificInstrumentField *pSpecificInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);
	void *_RspUnSubForQuoteRsp;
	virtual void OnRspUnSubForQuoteRsp(CThostFtdcSpecificInstrumentField *pSpecificInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
    {
        if (_RspUnSubForQuoteRsp)
        {
            if (pSpecificInstrument)
                ((RspUnSubForQuoteRsp)_RspUnSubForQuoteRsp)(pSpecificInstrument, repare(pRspInfo), nRequestID, bIsLast);
            else
            {
                CThostFtdcSpecificInstrumentField f = {};
                ((RspUnSubForQuoteRsp)_RspUnSubForQuoteRsp)(&f, repare(pRspInfo), nRequestID, bIsLast);
            }
        }
    }

	///深度行情通知
	typedef int (*RtnDepthMarketData)(CThostFtdcDepthMarketDataField *pDepthMarketData);
	void *_RtnDepthMarketData;
	virtual void OnRtnDepthMarketData(CThostFtdcDepthMarketDataField *pDepthMarketData)
    {
        if (_RtnDepthMarketData)
        {
            if (pDepthMarketData)
                ((RtnDepthMarketData)_RtnDepthMarketData)(pDepthMarketData);
            else
            {
                CThostFtdcDepthMarketDataField f = {};
                ((RtnDepthMarketData)_RtnDepthMarketData)(&f);
            }
        }
    }

	///询价通知
	typedef int (*RtnForQuoteRsp)(CThostFtdcForQuoteRspField *pForQuoteRsp);
	void *_RtnForQuoteRsp;
	virtual void OnRtnForQuoteRsp(CThostFtdcForQuoteRspField *pForQuoteRsp)
    {
        if (_RtnForQuoteRsp)
        {
            if (pForQuoteRsp)
                ((RtnForQuoteRsp)_RtnForQuoteRsp)(pForQuoteRsp);
            else
            {
                CThostFtdcForQuoteRspField f = {};
                ((RtnForQuoteRsp)_RtnForQuoteRsp)(&f);
            }
        }
    }
};

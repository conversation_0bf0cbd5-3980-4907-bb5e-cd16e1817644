#include "quote.h"
#include <string.h>
int nReq;

Quote::Quote(void)
{	
	_FrontConnected = NULL;
	_FrontDisconnected = NULL;
	_HeartBeatWarning = NULL;
	_RspUserLogin = NULL;
	_RspUserLogout = NULL;
	_RspQryMulticastInstrument = NULL;
	_RspError = NULL;
	_RspSubMarketData = NULL;
	_RspUnSubMarketData = NULL;
	_RspSubForQuoteRsp = NULL;
	_RspUnSubForQuoteRsp = NULL;
	_RtnDepthMarketData = NULL;
	_RtnForQuoteRsp = NULL;
}

extern "C" void qSetOnFrontConnected(Quote* spi, void* func){spi->_FrontConnected = func;}
extern "C" void qSetOnFrontDisconnected(Quote* spi, void* func){spi->_FrontDisconnected = func;}
extern "C" void qSetOnHeartBeatWarning(Quote* spi, void* func){spi->_HeartBeatWarning = func;}
extern "C" void qSetOnRspUserLogin(Quote* spi, void* func){spi->_RspUserLogin = func;}
extern "C" void qSetOnRspUserLogout(Quote* spi, void* func){spi->_RspUserLogout = func;}
extern "C" void qSetOnRspQryMulticastInstrument(Quote* spi, void* func){spi->_RspQryMulticastInstrument = func;}
extern "C" void qSetOnRspError(Quote* spi, void* func){spi->_RspError = func;}
extern "C" void qSetOnRspSubMarketData(Quote* spi, void* func){spi->_RspSubMarketData = func;}
extern "C" void qSetOnRspUnSubMarketData(Quote* spi, void* func){spi->_RspUnSubMarketData = func;}
extern "C" void qSetOnRspSubForQuoteRsp(Quote* spi, void* func){spi->_RspSubForQuoteRsp = func;}
extern "C" void qSetOnRspUnSubForQuoteRsp(Quote* spi, void* func){spi->_RspUnSubForQuoteRsp = func;}
extern "C" void qSetOnRtnDepthMarketData(Quote* spi, void* func){spi->_RtnDepthMarketData = func;}
extern "C" void qSetOnRtnForQuoteRsp(Quote* spi, void* func){spi->_RtnForQuoteRsp = func;}

extern "C" void* qCreateApi(){return CThostFtdcMdApi::CreateFtdcMdApi("./log/");}
extern "C" void* qCreateSpi(){return new Quote();}

extern "C" void* qRelease(CThostFtdcMdApi *api){api->Release(); return 0;}
extern "C" void* qInit(CThostFtdcMdApi *api){api->Init(); return 0;}
extern "C" void* qJoin(CThostFtdcMdApi *api){api->Join(); return 0;}
extern "C" void* qRegisterFront(CThostFtdcMdApi *api, char *pszFrontAddress){api->RegisterFront(pszFrontAddress); return 0;}
extern "C" void* qRegisterNameServer(CThostFtdcMdApi *api, char *pszNsAddress){api->RegisterNameServer(pszNsAddress); return 0;}
extern "C" void* qRegisterFensUserInfo(CThostFtdcMdApi *api, CThostFtdcFensUserInfoField * pFensUserInfo){api->RegisterFensUserInfo(pFensUserInfo); return 0;}
extern "C" void* qRegisterSpi(CThostFtdcMdApi *api, CThostFtdcMdSpi *pSpi){api->RegisterSpi(pSpi); return 0;}
extern "C" void* qSubscribeMarketData(CThostFtdcMdApi *api, char *ppInstrumentID[], int nCount){api->SubscribeMarketData(ppInstrumentID, nCount); return 0;}
extern "C" void* qUnSubscribeMarketData(CThostFtdcMdApi *api, char *ppInstrumentID[], int nCount){api->UnSubscribeMarketData(ppInstrumentID, nCount); return 0;}
extern "C" void* qSubscribeForQuoteRsp(CThostFtdcMdApi *api, char *ppInstrumentID[], int nCount){api->SubscribeForQuoteRsp(ppInstrumentID, nCount); return 0;}
extern "C" void* qUnSubscribeForQuoteRsp(CThostFtdcMdApi *api, char *ppInstrumentID[], int nCount){api->UnSubscribeForQuoteRsp(ppInstrumentID, nCount); return 0;}
extern "C" int qReqUserLogin(CThostFtdcMdApi *api, CThostFtdcReqUserLoginField *pReqUserLoginField, int nRequestID){return api->ReqUserLogin(pReqUserLoginField, nRequestID);}
extern "C" void* qReqUserLogout(CThostFtdcMdApi *api, CThostFtdcUserLogoutField *pUserLogout, int nRequestID){api->ReqUserLogout(pUserLogout, nRequestID); return 0;}
extern "C" void* qReqQryMulticastInstrument(CThostFtdcMdApi *api, CThostFtdcQryMulticastInstrumentField *pQryMulticastInstrument, int nRequestID){api->ReqQryMulticastInstrument(pQryMulticastInstrument, nRequestID); return 0;}

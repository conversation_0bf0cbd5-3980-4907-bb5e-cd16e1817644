#include "trade.h"
#include <string.h>
int nReq;

Trade::Trade(void)
{	
	_FrontConnected = NULL;
	_FrontDisconnected = NULL;
	_HeartBeatWarning = NULL;
	_RspAuthenticate = NULL;
	_RspUserLogin = NULL;
	_RspUserLogout = NULL;
	_RspUserPasswordUpdate = NULL;
	_RspTradingAccountPasswordUpdate = NULL;
	_RspUserAuthMethod = NULL;
	_RspGenUserCaptcha = NULL;
	_RspGenUserText = NULL;
	_RspOrderInsert = NULL;
	_RspParkedOrderInsert = NULL;
	_RspParkedOrderAction = NULL;
	_RspOrderAction = NULL;
	_RspQryMaxOrderVolume = NULL;
	_RspSettlementInfoConfirm = NULL;
	_RspRemoveParkedOrder = NULL;
	_RspRemoveParkedOrderAction = NULL;
	_RspExecOrderInsert = NULL;
	_RspExecOrderAction = NULL;
	_RspForQuoteInsert = NULL;
	_RspQuoteInsert = NULL;
	_RspQuoteAction = NULL;
	_RspBatchOrderAction = NULL;
	_RspOptionSelfCloseInsert = NULL;
	_RspOptionSelfCloseAction = NULL;
	_RspCombActionInsert = NULL;
	_RspQryOrder = NULL;
	_RspQryTrade = NULL;
	_RspQryInvestorPosition = NULL;
	_RspQryTradingAccount = NULL;
	_RspQryInvestor = NULL;
	_RspQryTradingCode = NULL;
	_RspQryInstrumentMarginRate = NULL;
	_RspQryInstrumentCommissionRate = NULL;
	_RspQryExchange = NULL;
	_RspQryProduct = NULL;
	_RspQryInstrument = NULL;
	_RspQryDepthMarketData = NULL;
	_RspQrySettlementInfo = NULL;
	_RspQryTransferBank = NULL;
	_RspQryInvestorPositionDetail = NULL;
	_RspQryNotice = NULL;
	_RspQrySettlementInfoConfirm = NULL;
	_RspQryInvestorPositionCombineDetail = NULL;
	_RspQryCFMMCTradingAccountKey = NULL;
	_RspQryEWarrantOffset = NULL;
	_RspQryInvestorProductGroupMargin = NULL;
	_RspQryExchangeMarginRate = NULL;
	_RspQryExchangeMarginRateAdjust = NULL;
	_RspQryExchangeRate = NULL;
	_RspQrySecAgentACIDMap = NULL;
	_RspQryProductExchRate = NULL;
	_RspQryProductGroup = NULL;
	_RspQryMMInstrumentCommissionRate = NULL;
	_RspQryMMOptionInstrCommRate = NULL;
	_RspQryInstrumentOrderCommRate = NULL;
	_RspQrySecAgentTradingAccount = NULL;
	_RspQrySecAgentCheckMode = NULL;
	_RspQrySecAgentTradeInfo = NULL;
	_RspQryOptionInstrTradeCost = NULL;
	_RspQryOptionInstrCommRate = NULL;
	_RspQryExecOrder = NULL;
	_RspQryForQuote = NULL;
	_RspQryQuote = NULL;
	_RspQryOptionSelfClose = NULL;
	_RspQryInvestUnit = NULL;
	_RspQryCombInstrumentGuard = NULL;
	_RspQryCombAction = NULL;
	_RspQryTransferSerial = NULL;
	_RspQryAccountregister = NULL;
	_RspError = NULL;
	_RtnOrder = NULL;
	_RtnTrade = NULL;
	_ErrRtnOrderInsert = NULL;
	_ErrRtnOrderAction = NULL;
	_RtnInstrumentStatus = NULL;
	_RtnBulletin = NULL;
	_RtnTradingNotice = NULL;
	_RtnErrorConditionalOrder = NULL;
	_RtnExecOrder = NULL;
	_ErrRtnExecOrderInsert = NULL;
	_ErrRtnExecOrderAction = NULL;
	_ErrRtnForQuoteInsert = NULL;
	_RtnQuote = NULL;
	_ErrRtnQuoteInsert = NULL;
	_ErrRtnQuoteAction = NULL;
	_RtnForQuoteRsp = NULL;
	_RtnCFMMCTradingAccountToken = NULL;
	_ErrRtnBatchOrderAction = NULL;
	_RtnOptionSelfClose = NULL;
	_ErrRtnOptionSelfCloseInsert = NULL;
	_ErrRtnOptionSelfCloseAction = NULL;
	_RtnCombAction = NULL;
	_ErrRtnCombActionInsert = NULL;
	_RspQryContractBank = NULL;
	_RspQryParkedOrder = NULL;
	_RspQryParkedOrderAction = NULL;
	_RspQryTradingNotice = NULL;
	_RspQryBrokerTradingParams = NULL;
	_RspQryBrokerTradingAlgos = NULL;
	_RspQueryCFMMCTradingAccountToken = NULL;
	_RtnFromBankToFutureByBank = NULL;
	_RtnFromFutureToBankByBank = NULL;
	_RtnRepealFromBankToFutureByBank = NULL;
	_RtnRepealFromFutureToBankByBank = NULL;
	_RtnFromBankToFutureByFuture = NULL;
	_RtnFromFutureToBankByFuture = NULL;
	_RtnRepealFromBankToFutureByFutureManual = NULL;
	_RtnRepealFromFutureToBankByFutureManual = NULL;
	_RtnQueryBankBalanceByFuture = NULL;
	_ErrRtnBankToFutureByFuture = NULL;
	_ErrRtnFutureToBankByFuture = NULL;
	_ErrRtnRepealBankToFutureByFutureManual = NULL;
	_ErrRtnRepealFutureToBankByFutureManual = NULL;
	_ErrRtnQueryBankBalanceByFuture = NULL;
	_RtnRepealFromBankToFutureByFuture = NULL;
	_RtnRepealFromFutureToBankByFuture = NULL;
	_RspFromBankToFutureByFuture = NULL;
	_RspFromFutureToBankByFuture = NULL;
	_RspQueryBankAccountMoneyByFuture = NULL;
	_RtnOpenAccountByBank = NULL;
	_RtnCancelAccountByBank = NULL;
	_RtnChangeAccountByBank = NULL;
	_RspQryClassifiedInstrument = NULL;
	_RspQryCombPromotionParam = NULL;
}

extern "C" void SetOnFrontConnected(Trade* spi, void* func){spi->_FrontConnected = func;}
extern "C" void SetOnFrontDisconnected(Trade* spi, void* func){spi->_FrontDisconnected = func;}
extern "C" void SetOnHeartBeatWarning(Trade* spi, void* func){spi->_HeartBeatWarning = func;}
extern "C" void SetOnRspAuthenticate(Trade* spi, void* func){spi->_RspAuthenticate = func;}
extern "C" void SetOnRspUserLogin(Trade* spi, void* func){spi->_RspUserLogin = func;}
extern "C" void SetOnRspUserLogout(Trade* spi, void* func){spi->_RspUserLogout = func;}
extern "C" void SetOnRspUserPasswordUpdate(Trade* spi, void* func){spi->_RspUserPasswordUpdate = func;}
extern "C" void SetOnRspTradingAccountPasswordUpdate(Trade* spi, void* func){spi->_RspTradingAccountPasswordUpdate = func;}
extern "C" void SetOnRspUserAuthMethod(Trade* spi, void* func){spi->_RspUserAuthMethod = func;}
extern "C" void SetOnRspGenUserCaptcha(Trade* spi, void* func){spi->_RspGenUserCaptcha = func;}
extern "C" void SetOnRspGenUserText(Trade* spi, void* func){spi->_RspGenUserText = func;}
extern "C" void SetOnRspOrderInsert(Trade* spi, void* func){spi->_RspOrderInsert = func;}
extern "C" void SetOnRspParkedOrderInsert(Trade* spi, void* func){spi->_RspParkedOrderInsert = func;}
extern "C" void SetOnRspParkedOrderAction(Trade* spi, void* func){spi->_RspParkedOrderAction = func;}
extern "C" void SetOnRspOrderAction(Trade* spi, void* func){spi->_RspOrderAction = func;}
extern "C" void SetOnRspQryMaxOrderVolume(Trade* spi, void* func){spi->_RspQryMaxOrderVolume = func;}
extern "C" void SetOnRspSettlementInfoConfirm(Trade* spi, void* func){spi->_RspSettlementInfoConfirm = func;}
extern "C" void SetOnRspRemoveParkedOrder(Trade* spi, void* func){spi->_RspRemoveParkedOrder = func;}
extern "C" void SetOnRspRemoveParkedOrderAction(Trade* spi, void* func){spi->_RspRemoveParkedOrderAction = func;}
extern "C" void SetOnRspExecOrderInsert(Trade* spi, void* func){spi->_RspExecOrderInsert = func;}
extern "C" void SetOnRspExecOrderAction(Trade* spi, void* func){spi->_RspExecOrderAction = func;}
extern "C" void SetOnRspForQuoteInsert(Trade* spi, void* func){spi->_RspForQuoteInsert = func;}
extern "C" void SetOnRspQuoteInsert(Trade* spi, void* func){spi->_RspQuoteInsert = func;}
extern "C" void SetOnRspQuoteAction(Trade* spi, void* func){spi->_RspQuoteAction = func;}
extern "C" void SetOnRspBatchOrderAction(Trade* spi, void* func){spi->_RspBatchOrderAction = func;}
extern "C" void SetOnRspOptionSelfCloseInsert(Trade* spi, void* func){spi->_RspOptionSelfCloseInsert = func;}
extern "C" void SetOnRspOptionSelfCloseAction(Trade* spi, void* func){spi->_RspOptionSelfCloseAction = func;}
extern "C" void SetOnRspCombActionInsert(Trade* spi, void* func){spi->_RspCombActionInsert = func;}
extern "C" void SetOnRspQryOrder(Trade* spi, void* func){spi->_RspQryOrder = func;}
extern "C" void SetOnRspQryTrade(Trade* spi, void* func){spi->_RspQryTrade = func;}
extern "C" void SetOnRspQryInvestorPosition(Trade* spi, void* func){spi->_RspQryInvestorPosition = func;}
extern "C" void SetOnRspQryTradingAccount(Trade* spi, void* func){spi->_RspQryTradingAccount = func;}
extern "C" void SetOnRspQryInvestor(Trade* spi, void* func){spi->_RspQryInvestor = func;}
extern "C" void SetOnRspQryTradingCode(Trade* spi, void* func){spi->_RspQryTradingCode = func;}
extern "C" void SetOnRspQryInstrumentMarginRate(Trade* spi, void* func){spi->_RspQryInstrumentMarginRate = func;}
extern "C" void SetOnRspQryInstrumentCommissionRate(Trade* spi, void* func){spi->_RspQryInstrumentCommissionRate = func;}
extern "C" void SetOnRspQryExchange(Trade* spi, void* func){spi->_RspQryExchange = func;}
extern "C" void SetOnRspQryProduct(Trade* spi, void* func){spi->_RspQryProduct = func;}
extern "C" void SetOnRspQryInstrument(Trade* spi, void* func){spi->_RspQryInstrument = func;}
extern "C" void SetOnRspQryDepthMarketData(Trade* spi, void* func){spi->_RspQryDepthMarketData = func;}
extern "C" void SetOnRspQrySettlementInfo(Trade* spi, void* func){spi->_RspQrySettlementInfo = func;}
extern "C" void SetOnRspQryTransferBank(Trade* spi, void* func){spi->_RspQryTransferBank = func;}
extern "C" void SetOnRspQryInvestorPositionDetail(Trade* spi, void* func){spi->_RspQryInvestorPositionDetail = func;}
extern "C" void SetOnRspQryNotice(Trade* spi, void* func){spi->_RspQryNotice = func;}
extern "C" void SetOnRspQrySettlementInfoConfirm(Trade* spi, void* func){spi->_RspQrySettlementInfoConfirm = func;}
extern "C" void SetOnRspQryInvestorPositionCombineDetail(Trade* spi, void* func){spi->_RspQryInvestorPositionCombineDetail = func;}
extern "C" void SetOnRspQryCFMMCTradingAccountKey(Trade* spi, void* func){spi->_RspQryCFMMCTradingAccountKey = func;}
extern "C" void SetOnRspQryEWarrantOffset(Trade* spi, void* func){spi->_RspQryEWarrantOffset = func;}
extern "C" void SetOnRspQryInvestorProductGroupMargin(Trade* spi, void* func){spi->_RspQryInvestorProductGroupMargin = func;}
extern "C" void SetOnRspQryExchangeMarginRate(Trade* spi, void* func){spi->_RspQryExchangeMarginRate = func;}
extern "C" void SetOnRspQryExchangeMarginRateAdjust(Trade* spi, void* func){spi->_RspQryExchangeMarginRateAdjust = func;}
extern "C" void SetOnRspQryExchangeRate(Trade* spi, void* func){spi->_RspQryExchangeRate = func;}
extern "C" void SetOnRspQrySecAgentACIDMap(Trade* spi, void* func){spi->_RspQrySecAgentACIDMap = func;}
extern "C" void SetOnRspQryProductExchRate(Trade* spi, void* func){spi->_RspQryProductExchRate = func;}
extern "C" void SetOnRspQryProductGroup(Trade* spi, void* func){spi->_RspQryProductGroup = func;}
extern "C" void SetOnRspQryMMInstrumentCommissionRate(Trade* spi, void* func){spi->_RspQryMMInstrumentCommissionRate = func;}
extern "C" void SetOnRspQryMMOptionInstrCommRate(Trade* spi, void* func){spi->_RspQryMMOptionInstrCommRate = func;}
extern "C" void SetOnRspQryInstrumentOrderCommRate(Trade* spi, void* func){spi->_RspQryInstrumentOrderCommRate = func;}
extern "C" void SetOnRspQrySecAgentTradingAccount(Trade* spi, void* func){spi->_RspQrySecAgentTradingAccount = func;}
extern "C" void SetOnRspQrySecAgentCheckMode(Trade* spi, void* func){spi->_RspQrySecAgentCheckMode = func;}
extern "C" void SetOnRspQrySecAgentTradeInfo(Trade* spi, void* func){spi->_RspQrySecAgentTradeInfo = func;}
extern "C" void SetOnRspQryOptionInstrTradeCost(Trade* spi, void* func){spi->_RspQryOptionInstrTradeCost = func;}
extern "C" void SetOnRspQryOptionInstrCommRate(Trade* spi, void* func){spi->_RspQryOptionInstrCommRate = func;}
extern "C" void SetOnRspQryExecOrder(Trade* spi, void* func){spi->_RspQryExecOrder = func;}
extern "C" void SetOnRspQryForQuote(Trade* spi, void* func){spi->_RspQryForQuote = func;}
extern "C" void SetOnRspQryQuote(Trade* spi, void* func){spi->_RspQryQuote = func;}
extern "C" void SetOnRspQryOptionSelfClose(Trade* spi, void* func){spi->_RspQryOptionSelfClose = func;}
extern "C" void SetOnRspQryInvestUnit(Trade* spi, void* func){spi->_RspQryInvestUnit = func;}
extern "C" void SetOnRspQryCombInstrumentGuard(Trade* spi, void* func){spi->_RspQryCombInstrumentGuard = func;}
extern "C" void SetOnRspQryCombAction(Trade* spi, void* func){spi->_RspQryCombAction = func;}
extern "C" void SetOnRspQryTransferSerial(Trade* spi, void* func){spi->_RspQryTransferSerial = func;}
extern "C" void SetOnRspQryAccountregister(Trade* spi, void* func){spi->_RspQryAccountregister = func;}
extern "C" void SetOnRspError(Trade* spi, void* func){spi->_RspError = func;}
extern "C" void SetOnRtnOrder(Trade* spi, void* func){spi->_RtnOrder = func;}
extern "C" void SetOnRtnTrade(Trade* spi, void* func){spi->_RtnTrade = func;}
extern "C" void SetOnErrRtnOrderInsert(Trade* spi, void* func){spi->_ErrRtnOrderInsert = func;}
extern "C" void SetOnErrRtnOrderAction(Trade* spi, void* func){spi->_ErrRtnOrderAction = func;}
extern "C" void SetOnRtnInstrumentStatus(Trade* spi, void* func){spi->_RtnInstrumentStatus = func;}
extern "C" void SetOnRtnBulletin(Trade* spi, void* func){spi->_RtnBulletin = func;}
extern "C" void SetOnRtnTradingNotice(Trade* spi, void* func){spi->_RtnTradingNotice = func;}
extern "C" void SetOnRtnErrorConditionalOrder(Trade* spi, void* func){spi->_RtnErrorConditionalOrder = func;}
extern "C" void SetOnRtnExecOrder(Trade* spi, void* func){spi->_RtnExecOrder = func;}
extern "C" void SetOnErrRtnExecOrderInsert(Trade* spi, void* func){spi->_ErrRtnExecOrderInsert = func;}
extern "C" void SetOnErrRtnExecOrderAction(Trade* spi, void* func){spi->_ErrRtnExecOrderAction = func;}
extern "C" void SetOnErrRtnForQuoteInsert(Trade* spi, void* func){spi->_ErrRtnForQuoteInsert = func;}
extern "C" void SetOnRtnQuote(Trade* spi, void* func){spi->_RtnQuote = func;}
extern "C" void SetOnErrRtnQuoteInsert(Trade* spi, void* func){spi->_ErrRtnQuoteInsert = func;}
extern "C" void SetOnErrRtnQuoteAction(Trade* spi, void* func){spi->_ErrRtnQuoteAction = func;}
extern "C" void SetOnRtnForQuoteRsp(Trade* spi, void* func){spi->_RtnForQuoteRsp = func;}
extern "C" void SetOnRtnCFMMCTradingAccountToken(Trade* spi, void* func){spi->_RtnCFMMCTradingAccountToken = func;}
extern "C" void SetOnErrRtnBatchOrderAction(Trade* spi, void* func){spi->_ErrRtnBatchOrderAction = func;}
extern "C" void SetOnRtnOptionSelfClose(Trade* spi, void* func){spi->_RtnOptionSelfClose = func;}
extern "C" void SetOnErrRtnOptionSelfCloseInsert(Trade* spi, void* func){spi->_ErrRtnOptionSelfCloseInsert = func;}
extern "C" void SetOnErrRtnOptionSelfCloseAction(Trade* spi, void* func){spi->_ErrRtnOptionSelfCloseAction = func;}
extern "C" void SetOnRtnCombAction(Trade* spi, void* func){spi->_RtnCombAction = func;}
extern "C" void SetOnErrRtnCombActionInsert(Trade* spi, void* func){spi->_ErrRtnCombActionInsert = func;}
extern "C" void SetOnRspQryContractBank(Trade* spi, void* func){spi->_RspQryContractBank = func;}
extern "C" void SetOnRspQryParkedOrder(Trade* spi, void* func){spi->_RspQryParkedOrder = func;}
extern "C" void SetOnRspQryParkedOrderAction(Trade* spi, void* func){spi->_RspQryParkedOrderAction = func;}
extern "C" void SetOnRspQryTradingNotice(Trade* spi, void* func){spi->_RspQryTradingNotice = func;}
extern "C" void SetOnRspQryBrokerTradingParams(Trade* spi, void* func){spi->_RspQryBrokerTradingParams = func;}
extern "C" void SetOnRspQryBrokerTradingAlgos(Trade* spi, void* func){spi->_RspQryBrokerTradingAlgos = func;}
extern "C" void SetOnRspQueryCFMMCTradingAccountToken(Trade* spi, void* func){spi->_RspQueryCFMMCTradingAccountToken = func;}
extern "C" void SetOnRtnFromBankToFutureByBank(Trade* spi, void* func){spi->_RtnFromBankToFutureByBank = func;}
extern "C" void SetOnRtnFromFutureToBankByBank(Trade* spi, void* func){spi->_RtnFromFutureToBankByBank = func;}
extern "C" void SetOnRtnRepealFromBankToFutureByBank(Trade* spi, void* func){spi->_RtnRepealFromBankToFutureByBank = func;}
extern "C" void SetOnRtnRepealFromFutureToBankByBank(Trade* spi, void* func){spi->_RtnRepealFromFutureToBankByBank = func;}
extern "C" void SetOnRtnFromBankToFutureByFuture(Trade* spi, void* func){spi->_RtnFromBankToFutureByFuture = func;}
extern "C" void SetOnRtnFromFutureToBankByFuture(Trade* spi, void* func){spi->_RtnFromFutureToBankByFuture = func;}
extern "C" void SetOnRtnRepealFromBankToFutureByFutureManual(Trade* spi, void* func){spi->_RtnRepealFromBankToFutureByFutureManual = func;}
extern "C" void SetOnRtnRepealFromFutureToBankByFutureManual(Trade* spi, void* func){spi->_RtnRepealFromFutureToBankByFutureManual = func;}
extern "C" void SetOnRtnQueryBankBalanceByFuture(Trade* spi, void* func){spi->_RtnQueryBankBalanceByFuture = func;}
extern "C" void SetOnErrRtnBankToFutureByFuture(Trade* spi, void* func){spi->_ErrRtnBankToFutureByFuture = func;}
extern "C" void SetOnErrRtnFutureToBankByFuture(Trade* spi, void* func){spi->_ErrRtnFutureToBankByFuture = func;}
extern "C" void SetOnErrRtnRepealBankToFutureByFutureManual(Trade* spi, void* func){spi->_ErrRtnRepealBankToFutureByFutureManual = func;}
extern "C" void SetOnErrRtnRepealFutureToBankByFutureManual(Trade* spi, void* func){spi->_ErrRtnRepealFutureToBankByFutureManual = func;}
extern "C" void SetOnErrRtnQueryBankBalanceByFuture(Trade* spi, void* func){spi->_ErrRtnQueryBankBalanceByFuture = func;}
extern "C" void SetOnRtnRepealFromBankToFutureByFuture(Trade* spi, void* func){spi->_RtnRepealFromBankToFutureByFuture = func;}
extern "C" void SetOnRtnRepealFromFutureToBankByFuture(Trade* spi, void* func){spi->_RtnRepealFromFutureToBankByFuture = func;}
extern "C" void SetOnRspFromBankToFutureByFuture(Trade* spi, void* func){spi->_RspFromBankToFutureByFuture = func;}
extern "C" void SetOnRspFromFutureToBankByFuture(Trade* spi, void* func){spi->_RspFromFutureToBankByFuture = func;}
extern "C" void SetOnRspQueryBankAccountMoneyByFuture(Trade* spi, void* func){spi->_RspQueryBankAccountMoneyByFuture = func;}
extern "C" void SetOnRtnOpenAccountByBank(Trade* spi, void* func){spi->_RtnOpenAccountByBank = func;}
extern "C" void SetOnRtnCancelAccountByBank(Trade* spi, void* func){spi->_RtnCancelAccountByBank = func;}
extern "C" void SetOnRtnChangeAccountByBank(Trade* spi, void* func){spi->_RtnChangeAccountByBank = func;}
extern "C" void SetOnRspQryClassifiedInstrument(Trade* spi, void* func){spi->_RspQryClassifiedInstrument = func;}
extern "C" void SetOnRspQryCombPromotionParam(Trade* spi, void* func){spi->_RspQryCombPromotionParam = func;}

extern "C" void* CreateApi(){return CThostFtdcTraderApi::CreateFtdcTraderApi("./log/");}
extern "C" void* CreateSpi(){return new Trade();}

extern "C" void* GetVersion() { return (void*)CThostFtdcTraderApi::GetApiVersion(); }
extern "C" void* Release(CThostFtdcTraderApi *api){api->Release(); return 0;}
extern "C" void* Init(CThostFtdcTraderApi *api){api->Init(); return 0;}
extern "C" void* Join(CThostFtdcTraderApi *api){api->Join(); return 0;}
extern "C" void* RegisterFront(CThostFtdcTraderApi *api, char *pszFrontAddress){api->RegisterFront(pszFrontAddress); return 0;}
extern "C" void* RegisterNameServer(CThostFtdcTraderApi *api, char *pszNsAddress){api->RegisterNameServer(pszNsAddress); return 0;}
extern "C" void* RegisterFensUserInfo(CThostFtdcTraderApi *api, CThostFtdcFensUserInfoField * pFensUserInfo){api->RegisterFensUserInfo(pFensUserInfo); return 0;}
extern "C" void* RegisterSpi(CThostFtdcTraderApi *api, CThostFtdcTraderSpi *pSpi){api->RegisterSpi(pSpi); return 0;}
extern "C" void* SubscribePrivateTopic(CThostFtdcTraderApi *api, THOST_TE_RESUME_TYPE nResumeType){api->SubscribePrivateTopic(nResumeType); return 0;}
extern "C" void* SubscribePublicTopic(CThostFtdcTraderApi *api, THOST_TE_RESUME_TYPE nResumeType){api->SubscribePublicTopic(nResumeType); return 0;}
extern "C" void* RegisterUserSystemInfo(CThostFtdcTraderApi *api, CThostFtdcUserSystemInfoField *pUserSystemInfo){api->RegisterUserSystemInfo(pUserSystemInfo); return 0;}
extern "C" void* SubmitUserSystemInfo(CThostFtdcTraderApi *api, CThostFtdcUserSystemInfoField *pUserSystemInfo){api->SubmitUserSystemInfo(pUserSystemInfo); return 0;}
extern "C" void* ReqUserLogout(CThostFtdcTraderApi *api, CThostFtdcUserLogoutField *pUserLogout, int nRequestID){api->ReqUserLogout(pUserLogout, nRequestID); return 0;}
extern "C" void* ReqUserPasswordUpdate(CThostFtdcTraderApi *api, CThostFtdcUserPasswordUpdateField *pUserPasswordUpdate, int nRequestID){api->ReqUserPasswordUpdate(pUserPasswordUpdate, nRequestID); return 0;}
extern "C" void* ReqTradingAccountPasswordUpdate(CThostFtdcTraderApi *api, CThostFtdcTradingAccountPasswordUpdateField *pTradingAccountPasswordUpdate, int nRequestID){api->ReqTradingAccountPasswordUpdate(pTradingAccountPasswordUpdate, nRequestID); return 0;}
extern "C" void* ReqUserAuthMethod(CThostFtdcTraderApi *api, CThostFtdcReqUserAuthMethodField *pReqUserAuthMethod, int nRequestID){api->ReqUserAuthMethod(pReqUserAuthMethod, nRequestID); return 0;}
extern "C" void* ReqGenUserCaptcha(CThostFtdcTraderApi *api, CThostFtdcReqGenUserCaptchaField *pReqGenUserCaptcha, int nRequestID){api->ReqGenUserCaptcha(pReqGenUserCaptcha, nRequestID); return 0;}
extern "C" void* ReqGenUserText(CThostFtdcTraderApi *api, CThostFtdcReqGenUserTextField *pReqGenUserText, int nRequestID){api->ReqGenUserText(pReqGenUserText, nRequestID); return 0;}
extern "C" void* ReqUserLoginWithCaptcha(CThostFtdcTraderApi *api, CThostFtdcReqUserLoginWithCaptchaField *pReqUserLoginWithCaptcha, int nRequestID){api->ReqUserLoginWithCaptcha(pReqUserLoginWithCaptcha, nRequestID); return 0;}
extern "C" void* ReqUserLoginWithText(CThostFtdcTraderApi *api, CThostFtdcReqUserLoginWithTextField *pReqUserLoginWithText, int nRequestID){api->ReqUserLoginWithText(pReqUserLoginWithText, nRequestID); return 0;}
extern "C" void* ReqUserLoginWithOTP(CThostFtdcTraderApi *api, CThostFtdcReqUserLoginWithOTPField *pReqUserLoginWithOTP, int nRequestID){api->ReqUserLoginWithOTP(pReqUserLoginWithOTP, nRequestID); return 0;}
extern "C" void* ReqParkedOrderInsert(CThostFtdcTraderApi *api, CThostFtdcParkedOrderField *pParkedOrder, int nRequestID){api->ReqParkedOrderInsert(pParkedOrder, nRequestID); return 0;}
extern "C" void* ReqParkedOrderAction(CThostFtdcTraderApi *api, CThostFtdcParkedOrderActionField *pParkedOrderAction, int nRequestID){api->ReqParkedOrderAction(pParkedOrderAction, nRequestID); return 0;}
extern "C" void* ReqRemoveParkedOrder(CThostFtdcTraderApi *api, CThostFtdcRemoveParkedOrderField *pRemoveParkedOrder, int nRequestID){api->ReqRemoveParkedOrder(pRemoveParkedOrder, nRequestID); return 0;}
extern "C" void* ReqRemoveParkedOrderAction(CThostFtdcTraderApi *api, CThostFtdcRemoveParkedOrderActionField *pRemoveParkedOrderAction, int nRequestID){api->ReqRemoveParkedOrderAction(pRemoveParkedOrderAction, nRequestID); return 0;}
extern "C" void* ReqExecOrderInsert(CThostFtdcTraderApi *api, CThostFtdcInputExecOrderField *pInputExecOrder, int nRequestID){api->ReqExecOrderInsert(pInputExecOrder, nRequestID); return 0;}
extern "C" void* ReqExecOrderAction(CThostFtdcTraderApi *api, CThostFtdcInputExecOrderActionField *pInputExecOrderAction, int nRequestID){api->ReqExecOrderAction(pInputExecOrderAction, nRequestID); return 0;}
extern "C" void* ReqForQuoteInsert(CThostFtdcTraderApi *api, CThostFtdcInputForQuoteField *pInputForQuote, int nRequestID){api->ReqForQuoteInsert(pInputForQuote, nRequestID); return 0;}
extern "C" void* ReqQuoteInsert(CThostFtdcTraderApi *api, CThostFtdcInputQuoteField *pInputQuote, int nRequestID){api->ReqQuoteInsert(pInputQuote, nRequestID); return 0;}
extern "C" void* ReqQuoteAction(CThostFtdcTraderApi *api, CThostFtdcInputQuoteActionField *pInputQuoteAction, int nRequestID){api->ReqQuoteAction(pInputQuoteAction, nRequestID); return 0;}
extern "C" void* ReqBatchOrderAction(CThostFtdcTraderApi *api, CThostFtdcInputBatchOrderActionField *pInputBatchOrderAction, int nRequestID){api->ReqBatchOrderAction(pInputBatchOrderAction, nRequestID); return 0;}
extern "C" void* ReqOptionSelfCloseInsert(CThostFtdcTraderApi *api, CThostFtdcInputOptionSelfCloseField *pInputOptionSelfClose, int nRequestID){api->ReqOptionSelfCloseInsert(pInputOptionSelfClose, nRequestID); return 0;}
extern "C" void* ReqOptionSelfCloseAction(CThostFtdcTraderApi *api, CThostFtdcInputOptionSelfCloseActionField *pInputOptionSelfCloseAction, int nRequestID){api->ReqOptionSelfCloseAction(pInputOptionSelfCloseAction, nRequestID); return 0;}
extern "C" void* ReqCombActionInsert(CThostFtdcTraderApi *api, CThostFtdcInputCombActionField *pInputCombAction, int nRequestID){api->ReqCombActionInsert(pInputCombAction, nRequestID); return 0;}
extern "C" int ReqOrderInsert(CThostFtdcTraderApi *api, CThostFtdcInputOrderField *pInputOrder, int nRequestID){return api->ReqOrderInsert(pInputOrder, nRequestID);}
extern "C" int ReqOrderAction(CThostFtdcTraderApi *api, CThostFtdcInputOrderActionField *pInputOrderAction, int nRequestID){return api->ReqOrderAction(pInputOrderAction, nRequestID);}
extern "C" int ReqAuthenticate(CThostFtdcTraderApi *api, CThostFtdcReqAuthenticateField *pReqAuthenticateField, int nRequestID){return api->ReqAuthenticate(pReqAuthenticateField, nRequestID);}
extern "C" int ReqUserLogin(CThostFtdcTraderApi *api, CThostFtdcReqUserLoginField *pReqUserLoginField, int nRequestID){return api->ReqUserLogin(pReqUserLoginField, nRequestID);}
extern "C" int ReqSettlementInfoConfirm(CThostFtdcTraderApi *api, CThostFtdcSettlementInfoConfirmField *pSettlementInfoConfirm, int nRequestID){return api->ReqSettlementInfoConfirm(pSettlementInfoConfirm, nRequestID); }
extern "C" int ReqQryMaxOrderVolume(CThostFtdcTraderApi *api, CThostFtdcQryMaxOrderVolumeField *pQryMaxOrderVolume, int nRequestID){return api->ReqQryMaxOrderVolume(pQryMaxOrderVolume, nRequestID);}
extern "C" int ReqQryOrder(CThostFtdcTraderApi *api, CThostFtdcQryOrderField *pQryOrder, int nRequestID){return api->ReqQryOrder(pQryOrder, nRequestID); }
extern "C" int ReqQryTrade(CThostFtdcTraderApi *api, CThostFtdcQryTradeField *pQryTrade, int nRequestID){return api->ReqQryTrade(pQryTrade, nRequestID); }
extern "C" int ReqQryInvestorPosition(CThostFtdcTraderApi *api, CThostFtdcQryInvestorPositionField *pQryInvestorPosition, int nRequestID){return api->ReqQryInvestorPosition(pQryInvestorPosition, nRequestID);}
extern "C" int ReqQryTradingAccount(CThostFtdcTraderApi *api, CThostFtdcQryTradingAccountField *pQryTradingAccount, int nRequestID){return api->ReqQryTradingAccount(pQryTradingAccount, nRequestID);}
extern "C" int ReqQryInvestor(CThostFtdcTraderApi *api, CThostFtdcQryInvestorField *pQryInvestor, int nRequestID){return api->ReqQryInvestor(pQryInvestor, nRequestID); }
extern "C" int ReqQryTradingCode(CThostFtdcTraderApi *api, CThostFtdcQryTradingCodeField *pQryTradingCode, int nRequestID){return api->ReqQryTradingCode(pQryTradingCode, nRequestID); }
extern "C" int ReqQryInstrumentMarginRate(CThostFtdcTraderApi *api, CThostFtdcQryInstrumentMarginRateField *pQryInstrumentMarginRate, int nRequestID){return api->ReqQryInstrumentMarginRate(pQryInstrumentMarginRate, nRequestID); }
extern "C" int ReqQryInstrumentCommissionRate(CThostFtdcTraderApi *api, CThostFtdcQryInstrumentCommissionRateField *pQryInstrumentCommissionRate, int nRequestID){return api->ReqQryInstrumentCommissionRate(pQryInstrumentCommissionRate, nRequestID); }
extern "C" int ReqQryExchange(CThostFtdcTraderApi *api, CThostFtdcQryExchangeField *pQryExchange, int nRequestID){return api->ReqQryExchange(pQryExchange, nRequestID); }
extern "C" int ReqQryProduct(CThostFtdcTraderApi *api, CThostFtdcQryProductField *pQryProduct, int nRequestID){return api->ReqQryProduct(pQryProduct, nRequestID); }
extern "C" int ReqQryInstrument(CThostFtdcTraderApi *api, CThostFtdcQryInstrumentField *pQryInstrument, int nRequestID){return api->ReqQryInstrument(pQryInstrument, nRequestID); }
extern "C" int ReqQryDepthMarketData(CThostFtdcTraderApi *api, CThostFtdcQryDepthMarketDataField *pQryDepthMarketData, int nRequestID){return api->ReqQryDepthMarketData(pQryDepthMarketData, nRequestID); }
extern "C" int ReqQrySettlementInfo(CThostFtdcTraderApi *api, CThostFtdcQrySettlementInfoField *pQrySettlementInfo, int nRequestID){return api->ReqQrySettlementInfo(pQrySettlementInfo, nRequestID); }
extern "C" int ReqQryTransferBank(CThostFtdcTraderApi *api, CThostFtdcQryTransferBankField *pQryTransferBank, int nRequestID){return api->ReqQryTransferBank(pQryTransferBank, nRequestID); }
extern "C" int ReqQryInvestorPositionDetail(CThostFtdcTraderApi *api, CThostFtdcQryInvestorPositionDetailField *pQryInvestorPositionDetail, int nRequestID){return api->ReqQryInvestorPositionDetail(pQryInvestorPositionDetail, nRequestID); }
extern "C" int ReqQryNotice(CThostFtdcTraderApi *api, CThostFtdcQryNoticeField *pQryNotice, int nRequestID){return api->ReqQryNotice(pQryNotice, nRequestID); }
extern "C" int ReqQrySettlementInfoConfirm(CThostFtdcTraderApi *api, CThostFtdcQrySettlementInfoConfirmField *pQrySettlementInfoConfirm, int nRequestID){return api->ReqQrySettlementInfoConfirm(pQrySettlementInfoConfirm, nRequestID); }
extern "C" int ReqQryInvestorPositionCombineDetail(CThostFtdcTraderApi *api, CThostFtdcQryInvestorPositionCombineDetailField *pQryInvestorPositionCombineDetail, int nRequestID){return api->ReqQryInvestorPositionCombineDetail(pQryInvestorPositionCombineDetail, nRequestID); }
extern "C" int ReqQryCFMMCTradingAccountKey(CThostFtdcTraderApi *api, CThostFtdcQryCFMMCTradingAccountKeyField *pQryCFMMCTradingAccountKey, int nRequestID){return api->ReqQryCFMMCTradingAccountKey(pQryCFMMCTradingAccountKey, nRequestID); }
extern "C" int ReqQryEWarrantOffset(CThostFtdcTraderApi *api, CThostFtdcQryEWarrantOffsetField *pQryEWarrantOffset, int nRequestID){return api->ReqQryEWarrantOffset(pQryEWarrantOffset, nRequestID); }
extern "C" int ReqQryInvestorProductGroupMargin(CThostFtdcTraderApi *api, CThostFtdcQryInvestorProductGroupMarginField *pQryInvestorProductGroupMargin, int nRequestID){return api->ReqQryInvestorProductGroupMargin(pQryInvestorProductGroupMargin, nRequestID); }
extern "C" int ReqQryExchangeMarginRate(CThostFtdcTraderApi *api, CThostFtdcQryExchangeMarginRateField *pQryExchangeMarginRate, int nRequestID){return api->ReqQryExchangeMarginRate(pQryExchangeMarginRate, nRequestID); }
extern "C" int ReqQryExchangeMarginRateAdjust(CThostFtdcTraderApi *api, CThostFtdcQryExchangeMarginRateAdjustField *pQryExchangeMarginRateAdjust, int nRequestID){return api->ReqQryExchangeMarginRateAdjust(pQryExchangeMarginRateAdjust, nRequestID); }
extern "C" int ReqQryExchangeRate(CThostFtdcTraderApi *api, CThostFtdcQryExchangeRateField *pQryExchangeRate, int nRequestID){return api->ReqQryExchangeRate(pQryExchangeRate, nRequestID); }
extern "C" int ReqQrySecAgentACIDMap(CThostFtdcTraderApi *api, CThostFtdcQrySecAgentACIDMapField *pQrySecAgentACIDMap, int nRequestID){return api->ReqQrySecAgentACIDMap(pQrySecAgentACIDMap, nRequestID); }
extern "C" int ReqQryProductExchRate(CThostFtdcTraderApi *api, CThostFtdcQryProductExchRateField *pQryProductExchRate, int nRequestID){return api->ReqQryProductExchRate(pQryProductExchRate, nRequestID); }
extern "C" int ReqQryProductGroup(CThostFtdcTraderApi *api, CThostFtdcQryProductGroupField *pQryProductGroup, int nRequestID){return api->ReqQryProductGroup(pQryProductGroup, nRequestID); }
extern "C" int ReqQryMMInstrumentCommissionRate(CThostFtdcTraderApi *api, CThostFtdcQryMMInstrumentCommissionRateField *pQryMMInstrumentCommissionRate, int nRequestID){return api->ReqQryMMInstrumentCommissionRate(pQryMMInstrumentCommissionRate, nRequestID); }
extern "C" int ReqQryMMOptionInstrCommRate(CThostFtdcTraderApi *api, CThostFtdcQryMMOptionInstrCommRateField *pQryMMOptionInstrCommRate, int nRequestID){return api->ReqQryMMOptionInstrCommRate(pQryMMOptionInstrCommRate, nRequestID); }
extern "C" int ReqQryInstrumentOrderCommRate(CThostFtdcTraderApi *api, CThostFtdcQryInstrumentOrderCommRateField *pQryInstrumentOrderCommRate, int nRequestID){return api->ReqQryInstrumentOrderCommRate(pQryInstrumentOrderCommRate, nRequestID); }
extern "C" int ReqQrySecAgentTradingAccount(CThostFtdcTraderApi *api, CThostFtdcQryTradingAccountField *pQryTradingAccount, int nRequestID){return api->ReqQrySecAgentTradingAccount(pQryTradingAccount, nRequestID); }
extern "C" int ReqQrySecAgentCheckMode(CThostFtdcTraderApi *api, CThostFtdcQrySecAgentCheckModeField *pQrySecAgentCheckMode, int nRequestID){return api->ReqQrySecAgentCheckMode(pQrySecAgentCheckMode, nRequestID); }
extern "C" int ReqQrySecAgentTradeInfo(CThostFtdcTraderApi *api, CThostFtdcQrySecAgentTradeInfoField *pQrySecAgentTradeInfo, int nRequestID){return api->ReqQrySecAgentTradeInfo(pQrySecAgentTradeInfo, nRequestID); }
extern "C" int ReqQryOptionInstrTradeCost(CThostFtdcTraderApi *api, CThostFtdcQryOptionInstrTradeCostField *pQryOptionInstrTradeCost, int nRequestID){return api->ReqQryOptionInstrTradeCost(pQryOptionInstrTradeCost, nRequestID); }
extern "C" int ReqQryOptionInstrCommRate(CThostFtdcTraderApi *api, CThostFtdcQryOptionInstrCommRateField *pQryOptionInstrCommRate, int nRequestID){return api->ReqQryOptionInstrCommRate(pQryOptionInstrCommRate, nRequestID); }
extern "C" int ReqQryExecOrder(CThostFtdcTraderApi *api, CThostFtdcQryExecOrderField *pQryExecOrder, int nRequestID){return api->ReqQryExecOrder(pQryExecOrder, nRequestID); }
extern "C" int ReqQryForQuote(CThostFtdcTraderApi *api, CThostFtdcQryForQuoteField *pQryForQuote, int nRequestID){return api->ReqQryForQuote(pQryForQuote, nRequestID); }
extern "C" int ReqQryQuote(CThostFtdcTraderApi *api, CThostFtdcQryQuoteField *pQryQuote, int nRequestID){return api->ReqQryQuote(pQryQuote, nRequestID); }
extern "C" int ReqQryOptionSelfClose(CThostFtdcTraderApi *api, CThostFtdcQryOptionSelfCloseField *pQryOptionSelfClose, int nRequestID){return api->ReqQryOptionSelfClose(pQryOptionSelfClose, nRequestID); }
extern "C" int ReqQryInvestUnit(CThostFtdcTraderApi *api, CThostFtdcQryInvestUnitField *pQryInvestUnit, int nRequestID){return api->ReqQryInvestUnit(pQryInvestUnit, nRequestID); }
extern "C" int ReqQryCombInstrumentGuard(CThostFtdcTraderApi *api, CThostFtdcQryCombInstrumentGuardField *pQryCombInstrumentGuard, int nRequestID){return api->ReqQryCombInstrumentGuard(pQryCombInstrumentGuard, nRequestID); }
extern "C" int ReqQryCombAction(CThostFtdcTraderApi *api, CThostFtdcQryCombActionField *pQryCombAction, int nRequestID){return api->ReqQryCombAction(pQryCombAction, nRequestID); }
extern "C" int ReqQryTransferSerial(CThostFtdcTraderApi *api, CThostFtdcQryTransferSerialField *pQryTransferSerial, int nRequestID){return api->ReqQryTransferSerial(pQryTransferSerial, nRequestID); }
extern "C" int ReqQryAccountregister(CThostFtdcTraderApi *api, CThostFtdcQryAccountregisterField *pQryAccountregister, int nRequestID){return api->ReqQryAccountregister(pQryAccountregister, nRequestID); }
extern "C" int ReqQryContractBank(CThostFtdcTraderApi *api, CThostFtdcQryContractBankField *pQryContractBank, int nRequestID){return api->ReqQryContractBank(pQryContractBank, nRequestID); }
extern "C" int ReqQryParkedOrder(CThostFtdcTraderApi *api, CThostFtdcQryParkedOrderField *pQryParkedOrder, int nRequestID){return api->ReqQryParkedOrder(pQryParkedOrder, nRequestID); }
extern "C" int ReqQryParkedOrderAction(CThostFtdcTraderApi *api, CThostFtdcQryParkedOrderActionField *pQryParkedOrderAction, int nRequestID){return api->ReqQryParkedOrderAction(pQryParkedOrderAction, nRequestID); }
extern "C" int ReqQryTradingNotice(CThostFtdcTraderApi *api, CThostFtdcQryTradingNoticeField *pQryTradingNotice, int nRequestID){return api->ReqQryTradingNotice(pQryTradingNotice, nRequestID); }
extern "C" int ReqQryBrokerTradingParams(CThostFtdcTraderApi *api, CThostFtdcQryBrokerTradingParamsField *pQryBrokerTradingParams, int nRequestID){return api->ReqQryBrokerTradingParams(pQryBrokerTradingParams, nRequestID); }
extern "C" int ReqQryBrokerTradingAlgos(CThostFtdcTraderApi *api, CThostFtdcQryBrokerTradingAlgosField *pQryBrokerTradingAlgos, int nRequestID){return api->ReqQryBrokerTradingAlgos(pQryBrokerTradingAlgos, nRequestID); }
extern "C" int ReqQryClassifiedInstrument(CThostFtdcTraderApi *api, CThostFtdcQryClassifiedInstrumentField *pQryClassifiedInstrument, int nRequestID){return api->ReqQryClassifiedInstrument(pQryClassifiedInstrument, nRequestID); }
extern "C" int ReqQryCombPromotionParam(CThostFtdcTraderApi *api, CThostFtdcQryCombPromotionParamField *pQryCombPromotionParam, int nRequestID){return api->ReqQryCombPromotionParam(pQryCombPromotionParam, nRequestID); }
extern "C" void* ReqQueryCFMMCTradingAccountToken(CThostFtdcTraderApi *api, CThostFtdcQueryCFMMCTradingAccountTokenField *pQueryCFMMCTradingAccountToken, int nRequestID){api->ReqQueryCFMMCTradingAccountToken(pQueryCFMMCTradingAccountToken, nRequestID); return 0;}
extern "C" void* ReqFromBankToFutureByFuture(CThostFtdcTraderApi *api, CThostFtdcReqTransferField *pReqTransfer, int nRequestID){api->ReqFromBankToFutureByFuture(pReqTransfer, nRequestID); return 0;}
extern "C" void* ReqFromFutureToBankByFuture(CThostFtdcTraderApi *api, CThostFtdcReqTransferField *pReqTransfer, int nRequestID){api->ReqFromFutureToBankByFuture(pReqTransfer, nRequestID); return 0;}
extern "C" void* ReqQueryBankAccountMoneyByFuture(CThostFtdcTraderApi *api, CThostFtdcReqQueryAccountField *pReqQueryAccount, int nRequestID){api->ReqQueryBankAccountMoneyByFuture(pReqQueryAccount, nRequestID); return 0;}
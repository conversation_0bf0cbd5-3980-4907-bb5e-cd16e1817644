package ctp

import "time"

// 响应信息
type RspInfoField struct {
	// 错误代码
	ErrorID int
	// 错误信息
	ErrorMsg string
}

// 用户登录应答
type RspUserLoginField struct {
	// 交易日
	TradingDay string
	// 登录成功时间
	LoginTime string
	// 经纪公司代码
	BrokerID string
	// 用户代码
	UserID string
	// 交易系统名称
	//SystemName string
	// 前置编号
	FrontID int
	// 会话编号
	SessionID int
	// 最大报单引用
	MaxOrderRef string
	// 上期所时间
	SHFETime string
	//// 大商所时间
	DCETime string
	//// 郑商所时间
	CZCETime string
	//// 中金所时间
	FFEXTime string
	//// 能源中心时间
	INETime string
}

// 行情响应
type TickField struct {
	// 交易日
	TradingDay string
	// 合约代码
	InstrumentID string
	// 交易所代码
	ExchangeID string
	// 合约在交易所的代码
	//ExchangeInstID string
	// 最新价
	LastPrice float64
	// 上次结算价
	//PreSettlementPrice float64
	// 昨收盘
	//PreClosePrice float64
	// 昨持仓量
	//PreOpenInterest float64
	// 今开盘
	OpenPrice float64
	// 最高价
	HighestPrice float64
	// 最低价
	LowestPrice float64
	// 数量
	Volume int
	// 成交金额
	Turnover float64
	// 持仓量
	OpenInterest float64
	// 今收盘
	ClosePrice float64
	// 本次结算价
	SettlementPrice float64
	// 涨停板价
	UpperLimitPrice float64
	// 跌停板价
	LowerLimitPrice float64
	// 昨虚实度
	//PreDelta float64
	// 今虚实度
	CurrDelta float64
	// 最后修改时间
	UpdateTime string
	// 最后修改毫秒
	UpdateMillisec int
	// 申买价一
	BidPrice1 float64
	// 申买量一
	BidVolume1 int
	// 申卖价一
	AskPrice1 float64
	// 申卖量一
	AskVolume1 int
	// 申买价二
	BidPrice2 float64
	// 申买量二
	BidVolume2 int
	// 申卖价二
	AskPrice2 float64
	// 申卖量二
	AskVolume2 int
	// 申买价三
	BidPrice3 float64
	// 申买量三
	BidVolume3 int
	// 申卖价三
	AskPrice3 float64
	// 申卖量三
	AskVolume3 int
	// 申买价四
	BidPrice4 float64
	// 申买量四
	BidVolume4 int
	// 申卖价四
	AskPrice4 float64
	// 申卖量四
	AskVolume4 int
	// 申买价五
	BidPrice5 float64
	// 申买量五
	BidVolume5 int
	// 申卖价五
	AskPrice5 float64
	// 申卖量五
	AskVolume5 int
	// 当日均价
	AveragePrice float64
	// 业务日期
	ActionDay string
	TouchTime time.Time
}

// 合约
type InstrumentField struct {
	// 合约代码
	InstrumentID string
	// 交易所代码
	ExchangeID string
	// 产品代码
	ProductID string
	// 产品类型
	ProductClass ProductClassType
	// 交割年份
	DeliveryYear int
	// 交割月
	DeliveryMonth int
	// 市价单最大下单量
	MaxMarketOrderVolume int
	// 市价单最小下单量
	MinMarketOrderVolume int
	// 限价单最大下单量
	MaxLimitOrderVolume int
	// 限价单最小下单量
	MinLimitOrderVolume int
	// 合约数量乘数
	VolumeMultiple int
	// 最小变动价位
	PriceTick float64
	// 持仓类型
	PositionType PositionTypeType
	// 是否使用大额单边保证金算法
	UseMaxMarginSideAlgorithm bool
	// 基础商品代码
	UnderlyingInstrID string
	// 执行价
	StrikePrice float64
	// 期权类型
	OptionsType OptionsTypeType
	// 合约基础商品乘数
	UnderlyingMultiple float64
	// 组合类型
	CombinationType CombinationTypeType
	TouchTime       time.Time
}

// 资金账户
type AccountField struct {
	// 上次质押金额
	PreMortgage float64
	// 上次存款额
	PreDeposit float64
	// 上次结算准备金
	PreBalance float64
	// 上次占用的保证金
	PreMargin float64
	// 利息基数
	InterestBase float64
	// 利息收入
	Interest float64
	// 入金金额
	Deposit float64
	// 出金金额
	Withdraw float64
	// 冻结的保证金
	FrozenMargin float64
	// 冻结的资金
	FrozenCash float64
	// 冻结的手续费
	FrozenCommission float64
	// 当前占用保证金总额
	CurrMargin float64
	// 资金差额
	CashIn float64
	// 手续费
	Commission float64
	// 平仓盈亏
	CloseProfit float64
	// 持仓盈亏
	PositionProfit float64
	// 期货结算准备金
	Balance float64
	// 可用资金
	Available float64
	// 可取资金
	WithdrawQuota float64
	// 基本准备金
	Reserve float64
	// 信用额度
	Credit float64
	// 质押金额
	Mortgage float64
	// 交易所保证金
	ExchangeMargin float64
	// 投资者交割保证金
	DeliveryMargin float64
	// 交易所交割保证金
	ExchangeDeliveryMargin float64
	// 保底期货结算准备金
	ReserveBalance float64
	// 币种代码
	CurrencyID string
	// 上次货币质入金额
	PreFundMortgageIn float64
	// 上次货币质出金额
	PreFundMortgageOut float64
	// 货币质入金额
	FundMortgageIn float64
	// 货币质出金额
	FundMortgageOut float64
	// 货币质押余额
	FundMortgageAvailable float64
	// 可质押货币金额
	MortgageableFund float64
	TouchTime        time.Time
}

// 投资者持仓
type PositionField struct {
	// 合约代码
	InstrumentID string
	// 持仓多空方向
	PositionDirection PosiDirectionType
	// 投机套保标志
	HedgeFlag HedgeFlagType
	// 上日持仓
	YdPosition int
	// 今日持仓
	Position int
	// 多头冻结
	LongFrozen int
	// 空头冻结
	ShortFrozen int
	// 开仓冻结金额
	LongFrozenAmount float64
	// 开仓冻结金额
	ShortFrozenAmount float64
	// 开仓量
	OpenVolume int
	// 平仓量
	CloseVolume int
	// 开仓金额
	OpenAmount float64
	// 平仓金额
	CloseAmount float64
	// 持仓成本
	PositionCost float64
	// 上次占用的保证金
	PreMargin float64
	// 占用的保证金
	UseMargin float64
	// 冻结的保证金
	FrozenMargin float64
	// 冻结的资金
	FrozenCash float64
	// 冻结的手续费
	FrozenCommission float64
	// 资金差额
	CashIn float64
	// 手续费
	Commission float64
	// 平仓盈亏
	CloseProfit float64
	// 持仓盈亏
	PositionProfit float64
	// 上次结算价
	PreSettlementPrice float64
	// 本次结算价
	SettlementPrice float64
	// 开仓成本
	OpenCost float64
	// 交易所保证金
	ExchangeMargin float64
	// 组合成交形成的持仓
	CombPosition int
	// 组合多头冻结
	CombLongFrozen int
	// 组合空头冻结
	CombShortFrozen int
	// 逐日盯市平仓盈亏
	CloseProfitByDate float64
	// 逐笔对冲平仓盈亏
	CloseProfitByTrade float64
	// 今日持仓
	TodayPosition int
	// 执行冻结
	StrikeFrozen int
	// 执行冻结金额
	StrikeFrozenAmount float64
	// 放弃执行冻结
	AbandonFrozen int
	// 交易所代码
	ExchangeID string
	// 执行冻结的昨仓
	YdStrikeFrozen int
	// 大商所持仓成本差值，只有大商所使用
	PositionCostOffset float64
	UpdateTime         time.Time
	TouchTime          time.Time
}

// 报单
type OrderField struct {
	// 合约代码
	InstrumentID string
	// 报单引用
	OrderRef string
	// 买卖方向
	Direction DirectionType
	// 组合开平标志
	OffsetFlag OffsetFlagType
	// 组合投机套保标志
	HedgeFlag HedgeFlagType
	// 价格
	LimitPrice float64
	// 条件单触发价
	StopPrice float64
	// 数量
	VolumeTotalOriginal int
	// 请求编号
	RequestID int
	// 交易所代码
	ExchangeID string
	// 报单编号
	OrderSysID string
	// 对应的条件单编号（由条件单触发生成的订单才有）
	RelativeOrderSysID string
	// 报单提交状态
	OrderSubmitStatus OrderSubmitStatusType
	// 报单状态
	OrderStatus OrderStatusType
	// 今成交数量
	VolumeTraded int
	// 剩余数量
	VolumeLeft int
	// 报单日期
	InsertDate string
	// 委托时间
	InsertTime string
	// 撤销时间
	CancelTime string
	// 更新时间
	UpdateTime string
	// 前置编号
	FrontID int
	// 会话编号
	SessionID int
	// 状态信息
	StatusMsg string
	// 是否本次登录后的委托
	IsLocal bool
	// 成交时间(有OnTrade更新)
	LastTradeTime string
	// 成交日期
	LastTradeDate string
	// 成交均价
	TradePrice float64
	TouchTime  time.Time
}

// 成交
type TradeField struct {
	// 合约代码
	InstrumentID string
	// 交易所代码
	ExchangeID string
	// 成交编号
	TradeID string
	// 买卖方向
	Direction DirectionType
	// 报单引用
	OrderRef string
	// 报单编号
	OrderSysID string
	// 开平标志
	OffsetFlag OffsetFlagType
	// 投机套保标志
	HedgeFlag HedgeFlagType
	// 价格
	Price float64
	// 数量
	Volume int
	// 成交时期
	TradeDate string
	// 成交时间
	TradeTime string
	// 交易日
	TradingDay string
	TouchTime  time.Time
}

// InstrumentStatus 合约状态
type InstrumentStatus struct {
	// 交易所代码
	ExchangeID string
	// 合约代码
	InstrumentID string
	// 合约交易状态
	InstrumentStatus InstrumentStatusType
	// 进入本状态时间
	EnterTime string
	TouchTime time.Time
}

// TransferField 银转响应
type TransferField struct {
	Time       string  // 时间
	CurrencyID string  // 币种
	Amout      float64 // 金额
	ErrorID    int     // 错误码
	ErrorMsg   string  // 错误描述
	TouchTime  time.Time
}

// InstrumentMarginRate 合约保证金
type InstrumentMarginRate struct {
	// 投机套保标志
	HedgeFlag HedgeFlagType
	// 多头保证金率
	LongMarginRatioByMoney float64
	// 多头保证金费
	LongMarginRatioByVolume float64
	// 空头保证金率
	ShortMarginRatioByMoney float64
	// 空头保证金费
	ShortMarginRatioByVolume float64
	// 是否相对交易所收取
	IsRelative bool
	// 交易所代码
	ExchangeID string
	// 投资单元代码
	InvestUnitID string
	// 合约代码
	InstrumentID string
}

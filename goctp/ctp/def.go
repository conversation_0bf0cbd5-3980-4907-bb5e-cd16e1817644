package ctp

import (
	"context"
	"fmt"
	"sync"
	"time"
	"unsafe"

	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/goctp/ctp/define"
	"go.uber.org/atomic"
	"golang.org/x/time/rate"
)

// 自定义的类型

type TriggerDirection string

const UnknownTriggerDirection TriggerDirection = ""
const TriggerDirectionHigher TriggerDirection = "Higher"
const TriggerDirectionLower TriggerDirection = "Lower"

// Quote 行情接口
type Quote struct {
	controller *CTPController
	api        unsafe.Pointer
	// 帐号
	InvestorID string
	Password   string
	// 经纪商
	BrokerID string
	IsLogin  *atomic.Bool

	onFrontConnected    OnFrontConnectedType
	onFrontDisConnected OnFrontDisConnectedType
	onRspUserLogin      OnRspUserLoginType
	onTick              OnTickType
	reqID               *atomic.Int32

	Ticks sync.Map // key: instrumentID, value: *TickField

	requestQueue       chan IRequest
	requestClose<PERSON>han   chan struct{}
	requestResultChans *xsync.MapOf[string, chan RequestResult] // key: resultID, value: chan RequestResult
	requestIDMap       *xsync.MapOf[string, string]             // key: reqID, value: resultID

	rateLimitQuery *RateLimiter
}

type BaseQuote interface {
	ReqConnect(addr string)
	ReqLogin(investor, pwd, broker string)
	Release()
	ReqSubscript(instrument string)

	RegOnFrontConnected(on OnFrontConnectedType)
	RegOnRspUserLogin(on OnRspUserLoginType)
	RegOnTick(on OnTickType)
}

// Trade 交易接口
type Trade struct {
	controller            *CTPController
	api                   unsafe.Pointer
	InvestorID            string                          // 帐号
	BrokerID              string                          // 经纪商
	TradingDay            string                          // 交易日
	ExchangeTimes         *xsync.MapOf[string, time.Time] // key: exchangeID, value: exchangeTime
	InstrumentsReady      *atomic.Bool
	PositionReady         *atomic.Bool
	AccountReady          *atomic.Bool
	SettlementInfoReady   *atomic.Bool
	Instruments           *xsync.MapOf[string, *InstrumentField]                          // 合约列表 (key: InstrumentID)
	InstrumentStatuss     *xsync.MapOf[string, *InstrumentStatus]                         // 合约状态 (key: InstrumentID)
	InstrumentMarginRates *xsync.MapOf[string, *InstrumentMarginRate]                     // 合约状态 (key: InstrumentID)
	PosiDetail            *xsync.MapOf[string, []*define.CThostFtdcInvestorPositionField] // 原始持仓 (key: InstrumentID_long/short/net)
	Positions             *xsync.MapOf[string, *PositionField]                            // 合成后的持仓 (key: instrumentID_long/short)
	Orders                *xsync.MapOf[string, *OrderField]                               // 委托 (key: sessionID_OrderRef)
	Trades                *xsync.MapOf[string, *TradeField]                               // 成交 (key: TradeID_buy/sell)
	SysID4Order           *xsync.MapOf[string, *OrderField]                               // key: OrderSysID
	Account               *AccountField                                                   // 帐户权益
	accountMutex          sync.Mutex
	IsAuthenticated       *atomic.Bool
	IsLogin               *atomic.Bool // 登录成功
	Version               string       // 版本号,如 v6.5.1_20200908 10:25:08

	password  string        // 密码
	FrontID   int           // 前置编号
	SessionID int           // 判断是否自己的委托用
	reqID     *atomic.Int32 // requestid
	cntOrder  int           // 计算order数量

	requestQueue        chan IRequest
	requestCloseChan    chan struct{}
	requestResultChans  *xsync.MapOf[string, chan RequestResult]                        // key: queryID
	requestIDMap        *xsync.MapOf[string, string]                                    // key: requestID, value: queryID
	queryPosiDetails    *xsync.MapOf[string, []*define.CThostFtdcInvestorPositionField] // key: queryID
	queryPositionTicker *time.Ticker                                                    // 循环查询持仓定时器
	queryAccountTicker  *time.Ticker                                                    // 循环查询账户定时器

	onFrontConnected          OnFrontConnectedType // 事件
	onFrontDisConnected       OnFrontDisConnectedType
	onRspUserLogin            OnRspUserLoginType
	onRtnOrder                OnRtnOrderType
	onRtnCancel               OnRtnOrderType
	onErrRtnOrder             OnRtnErrOrderType
	onErrAction               OnRtnErrActionType
	onRtnTrade                OnRtnTradeType
	onRtnInstrumentStatus     OnRtnInstrumentStatusType
	onRtnInstrumentMarginRate OnRtnInstrumentMarginRateType
	onRtnBankToFuture         OnRtnFromBankToFutureByFuture
	onRtnFutureToBank         OnRtnFromFutureToBankByFuture

	// 流控
	rateLimitOrderAction *RateLimiter
	rateLimitOrderInsert *RateLimiter
	rateLimitQuery       *RateLimiter
}

type LimiterType string

const LimiterTypeOrderInsert LimiterType = "OrderInsert"
const LimiterTypeOrderAction LimiterType = "OrderAction"
const LimiterTypeQuery LimiterType = "Query"

type RateLimiter struct {
	controller *CTPController
	Type       LimiterType
	Limiters   *xsync.MapOf[string, *rate.Limiter] // key: exchange_instrument
	Counters   *xsync.MapOf[string, int64]         // key: exchangeID, value: count
}

func NewRateLimiter(controller *CTPController, limiterType LimiterType) *RateLimiter {
	return &RateLimiter{
		controller: controller,
		Type:       limiterType,
		Limiters:   xsync.NewMapOf[*rate.Limiter](),
		Counters:   xsync.NewMapOf[int64](),
	}
}

var exchangeDailyCancelLimits = map[string]int{
	"SHFE":  500,
	"CZCE":  500,
	"INE":   500,
	"DCE":   500,
	"CFFEX": 400, // 按限制较严的品种限制，不对品种做单独处理
}

func (this *Trade) SetExchangeTimes(loginField *RspUserLoginField) {
	this.ExchangeTimes.Store("SHFE", ParseTimeWithString(loginField.TradingDay, loginField.SHFETime))
	this.ExchangeTimes.Store("DCE", ParseTimeWithString(loginField.TradingDay, loginField.DCETime))
	this.ExchangeTimes.Store("CZCE", ParseTimeWithString(loginField.TradingDay, loginField.CZCETime))
	this.ExchangeTimes.Store("FFEX", ParseTimeWithString(loginField.TradingDay, loginField.FFEXTime))
	this.ExchangeTimes.Store("INE", ParseTimeWithString(loginField.TradingDay, loginField.INETime))
}

func (this *Trade) CurrentExchangeTime(exchangeID string) (time.Time, error) {
	delta := time.Since(*this.controller.TradeLoginTime)
	exchangeTime, found := this.ExchangeTimes.Load(exchangeID)
	if !found {
		return time.Time{}, fmt.Errorf("exchange time not found")
	}
	return exchangeTime.Add(delta), nil
}

// 阻塞式等待，key 对应的流控器时间到了就允许继续执行
// 返回错误表示超出限制
func (this *RateLimiter) Wait(exchangeID string, instrumentID string) error {
	// uniqueWaitID := NewRandomID()
	// 按日限制撤单总量
	if this.Type == LimiterTypeOrderAction {
		dailyLimit := 500
		if limit, found := exchangeDailyCancelLimits[exchangeID]; found {
			dailyLimit = limit
		}
		dailyLimitKey := fmt.Sprintf("%s_%s_%s", this.controller.GetTradingDay(), exchangeID, instrumentID)
		if counter, found := this.Counters.Load(dailyLimitKey); found {
			if counter > int64(dailyLimit) {
				return fmt.Errorf("exceed (%s) daily limit (%d)", dailyLimitKey, dailyLimit)
			}
			counter += 1
		} else {
			this.Counters.Store(dailyLimitKey, int64(1))
		}
	}
	// 拼接 “交易所ID + 品种”，作为 “流控器的ID”
	limiterID := fmt.Sprintf("%s_%s", exchangeID, instrumentID)
	// zlog.Debugf("[%s] %s ratelimit (%s), before take", uniqueWaitID, this.Type, limiterID)
	// 如果流控器已经存在了，使用已有流控器
	if limiter, found := this.Limiters.Load(limiterID); found {
		limiter.Wait(context.Background())
	} else {
		// 生成新的流控器
		limiter := this.NewLimiter(exchangeID)
		this.Limiters.Store(limiterID, limiter)
		limiter.Wait(context.Background())
	}
	// zlog.Debugf("[%s] %s ratelimit (%s), after take", uniqueWaitID, this.Type, limiterID)
	return nil
}

// 根据流控类型，生成"流控器"
// 可能需要按交易所单独配置，所以传入 exchangeID
func (this *RateLimiter) NewLimiter(exchangeID string) *rate.Limiter {
	limit := rate.Inf
	switch this.Type {
	case LimiterTypeOrderInsert:
		limit = rate.Every(5 * time.Millisecond)
	case LimiterTypeOrderAction:
		limit = rate.Every(1 * time.Second)
	case LimiterTypeQuery:
		limit = rate.Every(1010 * time.Millisecond)
	}
	return rate.NewLimiter(limit, 1)
}

type BaseTrade interface {
	ReqConnect(addr string)
	ReqLogin(investor, pwd, broker string)
	Release()

	ReqOrderInsert(instrument string, buySell DirectionType, openClose OffsetFlagType, price float64, volume int) string
	ReqOrderInsertMarket(instrument string, buySell DirectionType, openClose OffsetFlagType, price float64, volume int) string
	ReqOrderInsertFOK(instrument string, buySell DirectionType, openClose OffsetFlagType, price float64, volume int) string
	ReqOrderInsertFAK(instrument string, buySell DirectionType, openClose OffsetFlagType, price float64, volume int) string

	RegOnFrontConnected(on OnFrontConnectedType)
	RegOnFrontDisConnected(on OnFrontDisConnectedType)
	RegOnRspUserLogin(on OnRspUserLoginType)
	RegOnRtnOrder(on OnRtnOrderType)
	RegOnErrRtnOrder(on OnRtnErrOrderType)
	RegOnErrAction(on OnRtnOrderType)
	RegOnRtnCancel(on OnRtnOrderType)
	RegOnRtnTrade(on OnRtnTradeType)
	RegOnRtnInstrumentStatus(on OnRtnInstrumentStatusType)
	RegOnRtnInstrumentMarginRate(on OnRtnInstrumentMarginRateType)
}

// 公共-连接
type OnFrontConnectedType func()

// 公共-断开
type OnFrontDisConnectedType func(reason int)

// 公共-登录
type OnRspUserLoginType func(loginField *RspUserLoginField, info *RspInfoField)

// 行情
type OnTickType func(tick *TickField)

// 交易-委托响应
type OnRtnOrderType func(field *OrderField)

// 交易-错误委托
type OnRtnErrOrderType func(field *OrderField, info *RspInfoField)

// 交易-错误撤单
type OnRtnErrActionType func(orderID string, requestID int, info *RspInfoField)

// 交易-成交响应
type OnRtnTradeType func(field *TradeField)

// 交易-合约状态响应
type OnRtnInstrumentStatusType func(field *InstrumentStatus)

// 交易-合约保证金响应
type OnRtnInstrumentMarginRateType func(field *InstrumentMarginRate)

// 银转-银行->期货
type OnRtnFromBankToFutureByFuture func(field *TransferField)

// 银转-期货->银行
type OnRtnFromFutureToBankByFuture func(field *TransferField)

// 产品类型类型
type ProductClassType byte

const (
	// 期货
	ProductClassFutures ProductClassType = '1'
	// 期货期权
	ProductClassOptions ProductClassType = '2'
	// 组合
	ProductClassCombinationProductClassType = '3'
	// 即期
	ProductClassSpot ProductClassType = '4'
	// 期转现
	ProductClassEFP ProductClassType = '5'
	// 现货期权
	ProductClassSpotOption ProductClassType = '6'
)

// 持仓类型类型
type PositionTypeType byte

const (
	// 净持仓
	PositionTypeNet PositionTypeType = '1'
	// 综合持仓
	PositionTypeGross PositionTypeType = '2'
)

// 期权类型
type OptionsTypeType byte

const (
	// 看涨
	OptionsTypeCallOptions OptionsTypeType = '1'
	// 看跌
	OptionsTypePutOptions OptionsTypeType = '2'
)

// 组合类型
type CombinationTypeType byte

const (
	// 期货组合
	CombinationTypeFuture CombinationTypeType = '0'
	// 垂直价差BUL
	CombinationTypeBUL CombinationTypeType = '1'
	// 垂直价差BER
	CombinationTypeBER CombinationTypeType = '2'
	// 跨式组合
	CombinationTypeSTD CombinationTypeType = '3'
	// 宽跨式组合
	CombinationTypeSTG CombinationTypeType = '4'
	// 备兑组合
	CombinationTypePRT CombinationTypeType = '5'
	// 时间价差组合
	CombinationTypeCLD CombinationTypeType = '6'
)

// 持仓多空方向类型
type PosiDirectionType byte

const (
	// 净
	PosiDirectionNet PosiDirectionType = '1'
	// 多头
	PosiDirectionLong PosiDirectionType = '2'
	// 空头
	PosiDirectionShort PosiDirectionType = '3'
)

// 投机套保标志类型
type HedgeFlagType byte

const (
	// 投机
	HedgeFlagSpeculation HedgeFlagType = '1'
	// 套利
	HedgeFlagArbitrage HedgeFlagType = '2'
	// 套保
	HedgeFlagHedge HedgeFlagType = '3'
	// 做市商
	HedgeFlagMarketMaker HedgeFlagType = '5'
	// 第一腿投机第二腿套保 大商所专用
	HedgeFlagSpecHedge HedgeFlagType = '6'
	// 第一腿套保第二腿投机  大商所专用
	HedgeFlagHedgeSpec HedgeFlagType = '7'
)

// 买卖方向类型
type DirectionType byte

const (
	// 买
	DirectionBuy DirectionType = '0'
	// 卖
	DirectionSell DirectionType = '1'
)

type TimeConditionType byte

const (
	TimeConditionIOC = '1'
	TimeConditionGFS = '2'
	TimeConditionGFD = '3'
	TimeConditionGTD = '4'
	TimeConditionGTC = '5'
	TimeConditionGFA = '6'
)

// 开平标志类型
type OffsetFlagType byte

const (
	// 开仓
	OffsetFlagOpen OffsetFlagType = '0'
	// 平仓
	OffsetFlagClose OffsetFlagType = '1'
	// 强平
	OffsetFlagForceClose OffsetFlagType = '2'
	// 平今
	OffsetFlagCloseToday OffsetFlagType = '3'
	// 平昨
	OffsetFlagCloseYesterday OffsetFlagType = '4'
	// 强减
	OffsetFlagForceOff OffsetFlagType = '5'
	// 本地强平
	OffsetFlagLocalForceClose OffsetFlagType = '6'
)

// 报单状态类型
type OrderStatusType byte

const (
	// 全部成交
	OrderStatusAllTraded OrderStatusType = '0'
	// 部分成交还在队列中
	OrderStatusPartTradedQueueing OrderStatusType = '1'
	// 部分成交不在队列中
	OrderStatusPartTradedNotQueueing OrderStatusType = '2'
	// 未成交还在队列中
	OrderStatusNoTradeQueueing OrderStatusType = '3'
	// 未成交不在队列中
	OrderStatusNoTradeNotQueueing OrderStatusType = '4'
	// 撤单
	OrderStatusCanceled OrderStatusType = '5'

	// 这三个状态根据 vnpy 对应修改
	// OrderStatusSubmitted OrderStatusType = 'a'
	// OrderStatusAccepted  OrderStatusType = 'b'
	// OrderStatusRejected  OrderStatusType = 'c'

	// 未知
	OrderStatusUnknown OrderStatusType = 'a'
	// 尚未触发
	OrderStatusNotTouched OrderStatusType = 'b'
	// 已触发
	OrderStatusTouched OrderStatusType = 'c'
)

// 报单提交状态
type OrderSubmitStatusType byte

const (
	// 已经提交
	OrderSubmitStatusInsertSubmitted OrderSubmitStatusType = '0'
	// 撤单已经提交
	OrderSubmitStatusCancelSubmitted OrderSubmitStatusType = '1'
	// 修改已经提交
	OrderSubmitStatusModifySubmitted OrderSubmitStatusType = '2'
	// 已经接受
	OrderSubmitStatusAccepted OrderSubmitStatusType = '3'
	// 报单已经被拒绝
	OrderSubmitStatusInsertRejected OrderSubmitStatusType = '4'
	// 撤单已经被拒绝
	OrderSubmitStatusCancelRejected OrderSubmitStatusType = '5'
	// 改单已经被拒绝
	OrderSubmitStatusModifyRejected OrderSubmitStatusType = '6'
)

// 合约交易状态类型
type InstrumentStatusType byte

const (
	// 开盘前
	InstrumentStatusBeforeTrading InstrumentStatusType = '0'
	// 非交易
	InstrumentStatusNoTrading InstrumentStatusType = '1'
	// 连续交易
	InstrumentStatusContinuous InstrumentStatusType = '2'
	// 集合竞价报单
	InstrumentStatusAuctionOrdering InstrumentStatusType = '3'
	// 集合竞价价格平衡
	InstrumentStatusAuctionBalance InstrumentStatusType = '4'
	// 集合竞价撮合
	InstrumentStatusAuctionMatch InstrumentStatusType = '5'
	// 收盘
	InstrumentStatusClosed InstrumentStatusType = '6'
)

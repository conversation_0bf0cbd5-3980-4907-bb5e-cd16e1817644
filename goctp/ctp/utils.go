package ctp

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"
)

func ParseTimeBeijing(s string) (result *time.Time) {
	if strings.Count(s, "-") == 1 {
		now := time.Now()
		s = fmt.Sprintf("%d-%s", now.Year(), s)
	}
	// 支持的格式如下，如果没有 year，默认是当前 year
	formats := []string{"20060102T15:04:05", "2006-01-02T15:04:05", "2006-01-02T15:04", "06-01-02T15:04:05", "06-01-02T15:04", "01-02T15:04:05", "01-02T15:04", "2006-01-02 15:04:05", "2006-01-02 15:04", "06-01-02 15:04:05", "06-01-02 15:04", "01-02 15:04:05", "01-02 15:04"}
	for _, f := range formats {
		if t, err := time.Parse(f, s); err != nil {
			continue
		} else {
			result = &t
		}
	}
	if result != nil {
		if bjTime, err := time.Parse(time.RFC3339, fmt.Sprintf("%s+08:00", result.Format("2006-01-02T15:04:05"))); err == nil {
			result = &bjTime
		} else {
			result = nil
		}
	}
	return
}

func ParseTimeWithString(date string, tim string) time.Time {
	dt := fmt.Sprintf("%sT%s", strings.Trim(date, "\x00"), strings.Trim(tim, "\x00"))
	t := ParseTimeBeijing(dt)
	if t == nil {
		return time.Time{}
	} else {
		return *t
	}
}

type comparable interface {
	~string | ~int64 | ~int | ~float64 | ~byte
}

func SliceContains[T comparable](slice []T, target T) (exist bool) {
	exist = false
	for _, item := range slice {
		if item == target {
			exist = true
			break
		}
	}
	return
}

func ParseTimeSecond(timeStr string) (second int) {
	parts := strings.Split(timeStr, ":")
	second = 0
	for i, part := range parts {
		part = strings.TrimLeft(part, "0")
		n, err := strconv.ParseInt(part, 10, 32)
		if err != nil {
			return
		}
		second += int(n) * int(math.Pow(60, float64(len(parts)-i-1)))
	}
	return
}

func FormatTimeSecond(second int) string {
	hours := second / (60 * 60)
	second -= hours * 60 * 60
	minutes := second / 60
	second -= minutes * 60
	seconds := second % 60
	return fmt.Sprintf("%02d:%02d:%02d", hours, minutes, seconds)
}

func TimeToSecond(t time.Time) (second int) {
	second = ParseTimeSecond(fmt.Sprintf("%02d:%02d:%02d", t.Hour(), t.Minute(), t.Second()))
	// zlog.Debugf("time to second, time: %s, second: %d", t, second)
	return
}

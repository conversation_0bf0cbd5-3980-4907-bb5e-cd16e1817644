package ctp

var InstrumentExchangeMap = map[string]string{
	"AP": "CZCE",
	"CF": "CZCE",
	"CJ": "CZCE",
	"CY": "CZCE",
	"FG": "CZCE",
	"IC": "CFFEX",
	"IF": "CFFEX",
	"IH": "CFFEX",
	"JR": "CZCE",
	"LR": "CZCE",
	"MA": "CZCE",
	"OI": "CZCE",
	"PF": "CZCE",
	"PK": "CZCE",
	"PM": "CZCE",
	"RI": "CZCE",
	"RM": "CZCE",
	"RS": "CZCE",
	"SA": "CZCE",
	"SF": "CZCE",
	"SM": "CZCE",
	"SR": "CZCE",
	"T":  "CFFEX",
	"TA": "CZCE",
	"TF": "CFFEX",
	"TS": "CFFEX",
	"UR": "CZCE",
	"WH": "CZCE",
	"ZC": "CZCE",
	"a":  "DCE",
	"ag": "SHFE",
	"al": "SHFE",
	"au": "SHFE",
	"b":  "DCE",
	"bb": "DCE",
	"bc": "INE",
	"bu": "SHFE",
	"c":  "DCE",
	"cs": "DCE",
	"cu": "SHFE",
	"eb": "DCE",
	"eg": "DCE",
	"fb": "DCE",
	"fu": "SHFE",
	"hc": "SHFE",
	"i":  "DCE",
	"j":  "DCE",
	"jd": "DCE",
	"jm": "DCE",
	"l":  "DCE",
	"lh": "DCE",
	"lu": "INE",
	"m":  "DCE",
	"ni": "SHFE",
	"nr": "INE",
	"p":  "DCE",
	"pb": "SHFE",
	"pg": "DCE",
	"pp": "DCE",
	"rb": "SHFE",
	"rr": "DCE",
	"ru": "SHFE",
	"sc": "INE",
	"sn": "SHFE",
	"sp": "SHFE",
	"ss": "SHFE",
	"v":  "DCE",
	"wr": "SHFE",
	"y":  "DCE",
	"zn": "SHFE",
}

func CheckValidProductID(productID string) bool {
	if _, found := InstrumentExchangeMap[productID]; !found {
		return false
	}
	return true
}

func GetExchangeID(instrumentID string) (exchangeID string) {
	if len(instrumentID) > 2 {
		prefix := instrumentID[:2]
		if eID, found := InstrumentExchangeMap[prefix]; found {
			exchangeID = eID
		}
		if exchangeID == "" {
			prefix := instrumentID[:1]
			if eID, found := InstrumentExchangeMap[prefix]; found {
				exchangeID = eID
			}
		}
	}
	return
}

package ctp

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/goctp/ctp/define"
	"github.com/wizhodl/quanter/common/zlog"
	"go.uber.org/atomic"
)

type Option struct {
	DefaultTimeout         int
	CreateOrderTimeout     int
	CancelOrderTimeout     int
	QueryInstrumentTimeout int
	QueryTimeout           int
}

func (this *Option) Default() {
	this.DefaultTimeout = 5
	this.CreateOrderTimeout = 5
	this.CancelOrderTimeout = 5
	this.QueryInstrumentTimeout = 30
	this.QueryTimeout = 5
}

type CTPController struct {
	Debug  bool
	Trade  *Trade
	Quote  *Quote
	Option *Option

	tradingSymbols []string

	LaunchTime         *time.Time
	LastTickTime       *time.Time
	TradeConnected     *atomic.Bool
	TradeConnectedTime *time.Time
	TradeLoginTime     *time.Time
	QuoteConnected     *atomic.Bool
	QuoteConnectedTime *time.Time
	QuoteLoginTime     *time.Time
}

func NewCTPController(debug bool) *CTPController {
	controller := &CTPController{
		Debug:          debug,
		Option:         &Option{},
		TradeConnected: atomic.NewBool(false),
		QuoteConnected: atomic.NewBool(false),
	}
	controller.Option.Default()
	return controller
}

func (this *CTPController) IsTrading(instrumentID string) bool {
	return SliceContains(this.tradingSymbols, instrumentID)
}

func (this *CTPController) SetTradingSymbols(symbols []string) {
	if this.Quote != nil {
		this.tradingSymbols = symbols
		this.Quote.Ticks.Range(func(k, v any) bool {
			instrumentID := k.(string)
			if !SliceContains(this.tradingSymbols, instrumentID) {
				this.Quote.Ticks.Delete(instrumentID)
			}
			return true
		})
	}
}

func (this *CTPController) QueryInstrumentMarginRates() {
	for _, symbol := range this.tradingSymbols {
		if err := this.Trade.QueryInstrumentMarginRate(symbol); err != nil {
			zlog.Errorf("qry instrument(%s) margin rate err: %s", symbol, err)
		}
	}
}

func (this *CTPController) Connect(quoteInitFunc func(*Quote), tradeInitFunc func(*Trade)) {
	now := time.Now()
	this.LaunchTime = &now
	if quoteInitFunc != nil {
		this.Quote = NewQuote(this)
		quoteInitFunc(this.Quote)
	}
	if tradeInitFunc != nil {
		this.Trade = NewTrade(this)
		tradeInitFunc(this.Trade)
	}
}

func (this *CTPController) LoginQuote(investorID, password, brokerID string) (er error) {
	if this.Quote == nil {
		return errors.New("quote not initialized")
	}
	er = this.Quote.Login(investorID, password, brokerID)
	return
}

func (this *CTPController) LoginTrade(investorID, password, brokerID, appID, authCode string) (er error) {
	if this.Trade == nil {
		return errors.New("trade not initialized")
	}
	er = this.Trade.Login(investorID, password, brokerID, appID, authCode)
	return
}

func (this *CTPController) IsTradeLogin() bool {
	return this.Trade != nil && this.Trade.IsLogin.Load()
}

func (this *CTPController) IsQuoteLogin() bool {
	return this.Quote != nil && this.Quote.IsLogin.Load()
}

func (this *CTPController) GetSysID4Order() (orders *xsync.MapOf[string, *OrderField], er error) {
	if this.IsTradeLogin() {
		orders = this.Trade.SysID4Order
		return
	}
	er = errors.New("ctp trade not logged in")
	return
}

func (this *CTPController) CurrentExchangeTime(exchangeID string) (t time.Time, er error) {
	if this.IsTradeLogin() {
		return this.Trade.CurrentExchangeTime(exchangeID)
	}
	er = errors.New("ctp trade not logged in")
	return
}

func (this *CTPController) GetTick(instrumentID string) (tick *TickField, er error) {
	if this.IsQuoteLogin() {
		tickValue, found := this.Quote.Ticks.Load(instrumentID)
		if !found {
			return nil, fmt.Errorf("tick not found")
		}
		return tickValue.(*TickField), nil
	}
	er = errors.New("ctp quote not logged in")
	return
}

func (this *CTPController) GetPosiDetail() (positions *xsync.MapOf[string, []*define.CThostFtdcInvestorPositionField], er error) {
	if this.IsTradeLogin() {
		positions = this.Trade.PosiDetail
		return
	}
	er = errors.New("ctp trade not logged in")
	return
}

func (this *CTPController) GetOrders() (orders *xsync.MapOf[string, *OrderField], er error) {
	if this.IsTradeLogin() {
		orders = this.Trade.Orders
		return
	}
	er = errors.New("ctp not logged in")
	return
}

func (this *CTPController) GetPositions() (positions *xsync.MapOf[string, *PositionField], er error) {
	if this.IsPositionReady() {
		positions = this.Trade.Positions
		return
	}
	er = errors.New("position not ready")
	return
}

func (this *CTPController) QueryPositions(exchangeID string, instrumentID string) (positions []PositionField, er error) {
	if this.IsTradeLogin() {
		return this.Trade.QueryPositions(exchangeID, instrumentID)
	}
	er = errors.New("ctp not logged in")
	return
}

func (this *CTPController) QueryOrder(instrumentID, orderSysID string) (order *OrderField, er error) {
	if this.IsTradeLogin() {
		return this.Trade.QueryOrder(instrumentID, orderSysID)
	}
	er = errors.New("ctp not logged in")
	return
}

func (this *CTPController) GetInstruments() (instruments *xsync.MapOf[string, *InstrumentField], er error) {
	if this.IsInstrumentsReady() {
		instruments = this.Trade.Instruments
		return
	}
	er = errors.New("instrument not ready")
	return
}

func (this *CTPController) IsInstrumentsReady() bool {
	if this.IsTradeLogin() {
		return this.Trade.InstrumentsReady.Load()
	}
	return false
}

func (this *CTPController) IsPositionReady() bool {
	if this.IsTradeLogin() {
		return this.Trade.PositionReady.Load()
	}
	return false
}

func (this *CTPController) IsAccountReady() bool {
	if this.IsTradeLogin() {
		return this.Trade.AccountReady.Load()
	}
	return false
}

func (this *CTPController) GetInstrumentStatuss() *xsync.MapOf[string, *InstrumentStatus] {
	if this.IsTradeLogin() {
		statuss := this.Trade.InstrumentStatuss
		return statuss
	}
	return xsync.NewMapOf[*InstrumentStatus]()
}

func (this *CTPController) GetAccount() (account *AccountField, er error) {
	if this.IsAccountReady() {
		this.Trade.accountMutex.Lock()
		account = &AccountField{}
		copier.Copy(&account, this.Trade.Account)
		this.Trade.accountMutex.Unlock()
		return
	}
	er = errors.New("account not ready")
	return
}

func (this *CTPController) GetTrades() *xsync.MapOf[string, *TradeField] {
	if this.IsTradeLogin() {
		trades := this.Trade.Trades
		return trades
	}
	return xsync.NewMapOf[*TradeField]()
}

func (this *CTPController) GetTradingDay() string {
	if this.IsTradeLogin() {
		return this.Trade.TradingDay
	}
	return ""
}

func (this *CTPController) CancelOrder(orderID string, orderSysID string) (order *OrderField, er error) {
	if this.IsTradeLogin() {
		return this.Trade.CancelOrder(orderID, orderSysID)
	}
	er = fmt.Errorf("ctp not logged in")
	return
}

func (this *CTPController) CreateOrder(instrument string, buySell DirectionType, openClose OffsetFlagType, price float64, volume int, stopPrice float64, triggerDirection TriggerDirection, reqCallback func(orderID string, err error)) (order *OrderField, er error) {
	if this.IsTradeLogin() {
		return this.Trade.CreateOrder(instrument, buySell, openClose, TimeConditionGFD, price, volume, stopPrice, triggerDirection, reqCallback)
	}
	er = fmt.Errorf("ctp not logged in")
	return
}

func (this *CTPController) CreateMarketOrder(instrument string, buySell DirectionType, openClose OffsetFlagType, volume int, reqCallback func(orderID string, err error)) (order *OrderField, er error) {
	if this.IsTradeLogin() {
		return this.Trade.CreateMarketOrder(instrument, buySell, openClose, volume, reqCallback)
	}
	er = fmt.Errorf("ctp not logged in")
	return
}

func (this *CTPController) ParseTime(timeStr string) time.Time {
	t := ParseTimeBeijing(fmt.Sprintf("%sT%s", this.Trade.TradingDay, timeStr))
	if t == nil {
		return time.Time{}
	} else {
		return *t
	}
}

func (this *CTPController) GetOrderByTradeID(tradeID string) (order *OrderField, er error) {
	if !strings.HasSuffix(tradeID, "_sell") || !strings.HasSuffix(tradeID, "_buy") {
		return nil, fmt.Errorf("tradeID must be end with _sell or _buy")
	}
	if !this.IsTradeLogin() {
		return nil, fmt.Errorf("trade not logged in")
	}
	if trade, found := this.Trade.Trades.Load(tradeID); found {
		if ord, found := this.Trade.SysID4Order.Load(fmt.Sprintf("%s_%s", trade.ExchangeID, trade.OrderSysID)); found {
			order = ord
			return
		}
	}
	return
}

func (this *CTPController) GetInstrumentByID(instrumentID string) (instrument *InstrumentField, er error) {
	if this.IsInstrumentsReady() {
		this.Trade.Instruments.Range(func(instrID string, instr *InstrumentField) bool {
			if instrID == instrumentID {
				instrument = instr
				return false
			}
			return true
		})
		return
	} else {
		er = fmt.Errorf("instrument not ready")
		return
	}
}

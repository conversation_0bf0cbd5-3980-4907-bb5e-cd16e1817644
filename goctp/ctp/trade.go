package ctp

/*
#cgo CPPFLAGS: -fPIC -I./libs/v6.6.1_P1_20210406
#cgo LDFLAGS: -fPIC -L./ -Wl,-rpath=./ -lctp_trade -lstdc++

#include "ThostFtdcUserApiDataType.h"
#include "ThostFtdcUserApiStruct.h"
void* CreateApi();
void* CreateSpi();
void* RegisterSpi(void*, void*);
void* RegisterFront(void*, char*);
void* SubscribePublicTopic(void*, int);
void* SubscribePrivateTopic(void*, int);
void* Init(void*);
void* Join(void*);
void* GetVersion();
void* Release(void*);

int ReqAuthenticate(void*, struct CThostFtdcReqAuthenticateField*, int);
int ReqUserLogin(void*, struct CThostFtdcReqUserLoginField*, int);
int ReqSettlementInfoConfirm(void*, struct CThostFtdcSettlementInfoConfirmField*, int);
int ReqQryTradingAccount(void*, struct CThostFtdcQryTradingAccountField*, int);
int ReqQryInvestorPosition(void*, struct CThostFtdcQryInvestorPositionField*, int);
int ReqQryInstrument(void*, struct CThostFtdcQryInstrumentField*, int);
int ReqQryInstrumentMarginRate(void*, struct CThostFtdcQryInstrumentMarginRateField*, int);
int ReqQryClassifiedInstrument(void*, struct CThostFtdcQryClassifiedInstrumentField*, int);
int ReqQryOrder(void*, struct CThostFtdcQryOrderField*, int);

int ReqOrderInsert(void*, struct CThostFtdcInputOrderField*, int);
int ReqOrderAction(void*, struct CThostFtdcInputOrderActionField*, int);

void* ReqFromBankToFutureByFuture(void*, struct CThostFtdcReqTransferField *, int);
void* ReqFromFutureToBankByFuture(void*, struct CThostFtdcReqTransferField *, int);


void SetOnFrontConnected(void*, void*);
int tFrontConnected();
void SetOnFrontDisconnected(void*, void*);
int tFrontDisConnected(int reason);
void SetOnRspUserLogin(void*, void*);
int tRspUserLogin(struct CThostFtdcRspUserLoginField *pRspUserLogin, struct CThostFtdcRspInfoField *pRspInfo, int nRequestID, _Bool bIsLast);
void SetOnRspAuthenticate(void*, void*);
int tRspAuthenticate(struct CThostFtdcRspAuthenticateField *pRspAuthenticateField, struct CThostFtdcRspInfoField *pRspInfo, int nRequestID, _Bool bIsLast);
void SetOnRspSettlementInfoConfirm(void*, void*);
int tRspSettlementInfoConfirm(struct CThostFtdcSettlementInfoConfirmField *pSettlementInfoConfirm, struct CThostFtdcRspInfoField *pRspInfo, int nRequestID, _Bool bIsLast);
void SetOnRspQryOrder(void*, void*);
int tRspQryOrder(struct CThostFtdcOrderField *pOrder, struct CThostFtdcRspInfoField *pRspInfo, int nRequestID, _Bool bIsLast);
void SetOnRspQryInstrument(void*, void*);
int tRspQryInstrument(struct CThostFtdcInstrumentField *pInstrument, struct CThostFtdcRspInfoField *pRspInfo, int nRequestID, _Bool bIsLast);
int tRspQryInstrumentMarginRate(struct CThostFtdcInstrumentMarginRateField *pInstrumentMarginRate, struct CThostFtdcRspInfoField *pRspInfo, int nRequestID, _Bool bIsLast);
void SetOnRspQryInstrumentMarginRate(void*, void*);
void SetOnRspQryClassifiedInstrument(void*, void*);
int tRspQryClassifiedInstrument(struct CThostFtdcInstrumentField *pInstrument, struct CThostFtdcRspInfoField *pRspInfo, int nRequestID, _Bool bIsLast);
void SetOnRspQryTradingAccount(void*, void*);
int tRspQryTradingAccount(struct CThostFtdcTradingAccountField *pTradingAccount, struct CThostFtdcRspInfoField *pRspInfo, int nRequestID, _Bool bIsLast);
void SetOnRspQryInvestorPosition(void*, void*);
int tRspQryInvestorPosition(struct CThostFtdcInvestorPositionField *pInvestorPosition, struct CThostFtdcRspInfoField *pRspInfo, int nRequestID, _Bool bIsLast);
void SetOnRspOrderInsert(void*, void*);
int tRspOrderInsert(struct CThostFtdcInputOrderField *pInputOrder, struct CThostFtdcRspInfoField *pRspInfo, int nRequestID, _Bool bIsLast);
void SetOnErrRtnOrderInsert(void*, void*);
int tErrRtnOrderInsert(struct CThostFtdcInputOrderField *pInputOrder, struct CThostFtdcRspInfoField *pRspInfo);
void SetOnRtnOrder(void*, void*);
int tRtnOrder(struct CThostFtdcOrderField *pOrder);
void SetOnRtnTrade(void*, void*);
int tRtnTrade(struct CThostFtdcTradeField *pTrade);
void SetOnRtnInstrumentStatus(void*, void*);
int tRtnInstrumentStatus(struct CThostFtdcInstrumentStatusField *pInstrumentStatus);
void SetOnRspOrderAction(void*, void*);
int tRspOrderAction(struct CThostFtdcOrderActionField *pOrderAction, struct CThostFtdcRspInfoField *pRspInfo, int nRequestID, _Bool bIsLast);
void SetOnErrRtnOrderAction(void*, void*);
int tErrRtnOrderAction(struct CThostFtdcOrderActionField *pOrderAction, struct CThostFtdcRspInfoField *pRspInfo);
void SetOnRtnFromFutureToBankByFuture(void*, void*);
int tRtnFromFutureToBankByFuture(struct CThostFtdcRspTransferField *pRspTransfer);
void SetOnRtnFromBankToFutureByFuture(void*, void*);
int tRtnFromBankToFutureByFuture(struct CThostFtdcRspTransferField *pRspTransfer);

#include <stdlib.h>
#include <stdint.h>
*/
import "C"

import (
	"bytes"
	"encoding/gob"
	"fmt"
	"math/rand"
	"os"
	"strings"
	"sync"
	"time"
	"unsafe"

	"github.com/puzpuzpuz/xsync/v2"
	"github.com/wizhodl/goctp/ctp/define"
	"github.com/wizhodl/quanter/common/zlog"
	"go.uber.org/atomic"
)

var t *Trade

const ProductInfo string = "@wizquant"

func (t *Trade) getReqID() int {
	return int(t.reqID.Inc())
}

// NewTrade 实例化
func NewTrade(controller *CTPController) *Trade {
	zlog.Debugf("[goctp trade] new trade")

	// 执行目录下创建 log目录
	_, err := os.Stat("log")
	if err != nil {
		os.Mkdir("log", os.ModePerm)
	}
	t = &Trade{
		Account:               new(AccountField),
		accountMutex:          sync.Mutex{},
		rateLimitOrderAction:  NewRateLimiter(controller, LimiterTypeOrderAction),
		rateLimitOrderInsert:  NewRateLimiter(controller, LimiterTypeOrderInsert),
		rateLimitQuery:        NewRateLimiter(controller, LimiterTypeQuery),
		requestQueue:          make(chan IRequest, 100),
		requestCloseChan:      make(chan struct{}, 1),
		Instruments:           xsync.NewMapOf[*InstrumentField](),
		InstrumentStatuss:     xsync.NewMapOf[*InstrumentStatus](),
		InstrumentMarginRates: xsync.NewMapOf[*InstrumentMarginRate](),
		PosiDetail:            xsync.NewMapOf[[]*define.CThostFtdcInvestorPositionField](),
		Positions:             xsync.NewMapOf[*PositionField](),
		Orders:                xsync.NewMapOf[*OrderField](),
		Trades:                xsync.NewMapOf[*TradeField](),
		SysID4Order:           xsync.NewMapOf[*OrderField](),
		requestResultChans:    xsync.NewMapOf[chan RequestResult](),
		requestIDMap:          xsync.NewMapOf[string](),
		queryPosiDetails:      xsync.NewMapOf[[]*define.CThostFtdcInvestorPositionField](),
		ExchangeTimes:         xsync.NewMapOf[time.Time](),
		InstrumentsReady:      atomic.NewBool(false),
		PositionReady:         atomic.NewBool(false),
		AccountReady:          atomic.NewBool(false),
		SettlementInfoReady:   atomic.NewBool(false),
		IsAuthenticated:       atomic.NewBool(false),
		IsLogin:               atomic.NewBool(false),
		reqID:                 atomic.NewInt32(0),
	}
	t.controller = controller
	// 初始化变量
	t.IsLogin.Store(false)

	t.api = C.CreateApi()
	spi := C.CreateSpi()
	t.Version = C.GoString((*C.char)(C.GetVersion()))
	C.RegisterSpi(t.api, spi)

	C.SetOnFrontConnected(spi, C.tFrontConnected)
	C.SetOnFrontDisconnected(spi, C.tFrontDisConnected)
	C.SetOnRspUserLogin(spi, C.tRspUserLogin)
	C.SetOnRspAuthenticate(spi, C.tRspAuthenticate)
	C.SetOnRspSettlementInfoConfirm(spi, C.tRspSettlementInfoConfirm)
	C.SetOnRspQryInstrument(spi, C.tRspQryInstrument)
	C.SetOnRspQryClassifiedInstrument(spi, C.tRspQryClassifiedInstrument)
	C.SetOnRspQryInstrumentMarginRate(spi, C.tRspQryInstrumentMarginRate)
	C.SetOnRspQryTradingAccount(spi, C.tRspQryTradingAccount)
	C.SetOnRspQryInvestorPosition(spi, C.tRspQryInvestorPosition)
	C.SetOnRspQryOrder(spi, C.tRspQryOrder)
	C.SetOnRspOrderInsert(spi, C.tRspOrderInsert)
	C.SetOnRspOrderAction(spi, C.tRspOrderAction)
	C.SetOnRtnOrder(spi, C.tRtnOrder)
	C.SetOnRtnTrade(spi, C.tRtnTrade)
	C.SetOnErrRtnOrderInsert(spi, C.tErrRtnOrderInsert)
	C.SetOnErrRtnOrderAction(spi, C.tErrRtnOrderAction)
	C.SetOnRtnInstrumentStatus(spi, C.tRtnInstrumentStatus)
	C.SetOnRtnFromBankToFutureByFuture(spi, C.tRtnFromBankToFutureByFuture)
	C.SetOnRtnFromFutureToBankByFuture(spi, C.tRtnFromFutureToBankByFuture)

	go t.doRequestLoop()
	go t.requestQueueLoop()
	return t
}

func NewRandomID() string {
	rand.Seed(time.Now().UnixNano())
	randStr := "1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ"
	var letterRunes = []rune(randStr)

	b := make([]rune, 6)
	for i := range b {
		b[i] = letterRunes[rand.Intn(len(letterRunes))]
	}
	return string(b)
}

// ********************** 主调函数 ************************

// Release 接口销毁处理
func (t *Trade) Release() {
	zlog.Debugf("[goctp trade] release")

	t.queryPositionTicker.Stop()
	t.queryAccountTicker.Stop()
	t.requestResultChans.Range(func(k string, v chan RequestResult) bool {
		t.requestResultChans.Delete(k)
		return true
	})
	t.IsLogin.Store(false)

	C.Release(t.api)
	tFrontDisConnected(C.int(0))
}

func (t *Trade) doRequestLoop() {
	for {
		select {
		case <-t.requestCloseChan: // 主动退出查询循环
			return
		case req := <-t.requestQueue:
			go func(request IRequest) {
				requestID := request.GetID()
				ctpReqID := t.getReqID()
				zlog.Debugf("[goctp trade] received request: %s (%d:%s)", request.GetType(), ctpReqID, request.GetID())

				// 这里不管是否安静查询，都需要存这个 queryID 和 reqID 的关联，否则定时查询的 Positions 会没有数据
				resultKeys := request.GetResultKeys()
				if len(resultKeys) > 0 {
					for _, resultKey := range resultKeys {
						t.requestIDMap.Store(resultKey, requestID)
					}
				}
				t.requestIDMap.Store(fmt.Sprintf("%d", ctpReqID), requestID)
				// 1分钟后，把映射到 requestID 上的所有 key 都删掉
				time.AfterFunc(1*time.Minute, func() {
					t.requestIDMap.Range(func(resultKey, requestID string) bool {
						if requestID == request.GetID() {
							t.requestIDMap.Delete(resultKey)
							// zlog.Debugf("[goctp trade] delete request result key: %s", resultKey)
						}
						return true
					})
				})

				var reqResult = ReqResultRateLimit
				var err error
				// 集中处理 Query 的限流问题，其他类型的 Request 自己处理限流问题
				if strings.Contains(string(request.GetType()), "Query") {
					err = t.rateLimitQuery.Wait("", "")
				}
				if err != nil {
					zlog.Errorf("[goctp trade] request rate limit error: %s", err)
					reqResult = ReqResultRateLimit
				} else {
					reqResult = request.Do(ctpReqID)
				}
				zlog.Debugf("[goctp trade] do (%s-%s) request result: %d", request.GetType(), request.GetID(), reqResult)
				if reqResult < 0 {
					if resultChan, found := t.requestResultChans.Load(requestID); found {
						err := toReqError(reqResult)
						resultChan <- RequestResult{
							Success:  false,
							ErrorMsg: fmt.Sprintf("%s", err),
						}
					}
				}
			}(req)
		}

	}
}

func toReqError(ret int) error {
	if ret == 0 {
		return nil
	}
	switch ret {
	case -1:
		return fmt.Errorf("[-1] network error")
	case -2:
		return fmt.Errorf("[-2] more than 1 concurrent request")
	case -3:
		return fmt.Errorf("[-3] too much request in 1 second")
	case ReqResultRateLimit:
		return fmt.Errorf("[%d] rate limit", ReqResultRateLimit)
	default:
		return fmt.Errorf("[%d] unknown req return error", ret)
	}
}

// 循环查询持仓&资金
func (t *Trade) requestQueueLoop() {
	zlog.Debugf("[goctp trade] query loop called")
	t.queryPositionTicker = time.NewTicker(2 * time.Second)
	t.queryAccountTicker = time.NewTicker(20 * time.Second)

	for {
		if !t.controller.TradeConnected.Load() || !t.IsAuthenticated.Load() {
			time.Sleep(1 * time.Second)
			continue
		}
		for !t.IsLogin.Load() {
			// zlog.Debugf("[goctp trade] not logged in, wait 5s")
			time.Sleep(1 * time.Second)
			if t.IsAuthenticated.Load() && t.InvestorID != "" {
				err := t._Login(nil)
				zlog.Errorf("repeated login failed, error: %s", err)
			}
		}

		// zlog.Debugf("request queue loop before position & account")
		select {
		case <-t.queryPositionTicker.C:
			// zlog.Debugf("query position interval")
			t.QueryPositions("", "")
		case <-t.queryAccountTicker.C:
			// zlog.Debugf("query account interval")
			t.QueryAccount()
		}

		// 以下查询只需要查询一次，或者每天查询一次
		for !t.SettlementInfoReady.Load() && t.controller.TradeConnected.Load() {
			time.Sleep(3 * time.Second)
			err := t.ConfirmSettlementInfo()
			if err != nil {
				zlog.Errorf("confirm settlement info failed, error: %s", err)
			}
		}

		for !t.InstrumentsReady.Load() && t.controller.TradeConnected.Load() {
			time.Sleep(3 * time.Second)
			err := t.QueryInstrument()
			if err != nil {
				zlog.Errorf("query instrument failed, error: %s", err)
				continue
			}
			t.controller.QueryInstrumentMarginRates()
		}
	}
}

// ReqConnect 连接;Join阻塞，用goroutine
func (t *Trade) ReqConnect(addr string) {
	zlog.Debugf("[goctp trade] reqest connect: %s", addr)

	front := C.CString(addr)
	defer C.free(unsafe.Pointer(front))

	C.RegisterFront(t.api, front)
	C.SubscribePrivateTopic(t.api, C.int(define.THOST_TERT_RESTART))
	C.SubscribePublicTopic(t.api, C.int(define.THOST_TERT_RESTART))
	C.Init(t.api)
	// C.Join(t.api)
}

// ReqBankToFuture 银行转期货
func (t *Trade) ReqBankToFuture(bankID, bankAccount, bankPwd string, amount float64) {
	zlog.Debugf("[goctp trade] reqest bank to future: %s - %s - %v", bankID, bankAccount, amount)
	f := define.CThostFtdcReqTransferField{}
	copy(f.TradeCode[:], "202001")
	copy(f.BankBranchID[:], "0000")
	copy(f.BrokerID[:], []byte(t.BrokerID))
	copy(f.AccountID[:], []byte(t.InvestorID))
	copy(f.Password[:], []byte(t.password))
	copy(f.CurrencyID[:], "CNY")
	f.LastFragment = define.THOST_FTDC_LF_Yes
	f.IdCardType = define.THOST_FTDC_ICT_IDCard
	f.CustType = define.THOST_FTDC_CUSTT_Person
	f.InstallID = 1
	f.FutureSerial = 0
	f.VerifyCertNoFlag = define.THOST_FTDC_YNI_No
	f.FutureFetchAmount = 0
	f.CustFee = 0
	f.BrokerFee = 0
	f.SecuPwdFlag = define.THOST_FTDC_BPWDF_BlankCheck
	f.RequestID = define.TThostFtdcRequestIDType(t.getReqID())
	f.TID = 0
	copy(f.BankID[:], []byte(bankID))
	copy(f.BankAccount[:], []byte(bankAccount))
	copy(f.BankPassWord[:], []byte(bankPwd))
	f.TradeAmount = define.TThostFtdcTradeAmountType(amount)

	C.ReqFromBankToFutureByFuture(t.api, (*C.struct_CThostFtdcReqTransferField)(unsafe.Pointer(&f)), C.int(t.getReqID()))
}

// ReqFutureToBank 期货转银行
func (t *Trade) ReqFutureToBank(bankID, bankAccount string, amount float64) {
	zlog.Debugf("[goctp trade] reqest future to bank: %s - %s - %v", bankID, bankAccount, amount)
	f := define.CThostFtdcReqTransferField{}
	copy(f.TradeCode[:], "202002")
	copy(f.BankBranchID[:], "0000")
	copy(f.BrokerID[:], []byte(t.BrokerID))
	copy(f.AccountID[:], []byte(t.InvestorID))
	copy(f.Password[:], []byte(t.password))
	copy(f.CurrencyID[:], "CNY")
	f.LastFragment = define.THOST_FTDC_LF_Yes
	f.IdCardType = define.THOST_FTDC_ICT_IDCard
	f.CustType = define.THOST_FTDC_CUSTT_Person
	f.InstallID = 1
	f.FutureSerial = 0
	f.VerifyCertNoFlag = define.THOST_FTDC_YNI_No
	f.FutureFetchAmount = 0
	f.CustFee = 0
	f.BrokerFee = 0
	f.SecuPwdFlag = define.THOST_FTDC_BPWDF_BlankCheck
	f.RequestID = define.TThostFtdcRequestIDType(t.getReqID())
	f.TID = 0
	copy(f.BankID[:], []byte(bankID))
	copy(f.BankAccount[:], []byte(bankAccount))
	// copy(f.BankPassWord[:], []byte(bankPwd))
	f.TradeAmount = define.TThostFtdcTradeAmountType(amount)

	C.ReqFromBankToFutureByFuture(t.api, (*C.struct_CThostFtdcReqTransferField)(unsafe.Pointer(&f)), C.int(t.getReqID()))
}

// ********************** 注册客户响应 ************************

// RegOnFrontConnected 注册连接响应
func (t *Trade) RegOnFrontConnected(on OnFrontConnectedType) {
	t.onFrontConnected = on
}

// RegOnFrontDisConnected 注册连接响应
func (t *Trade) RegOnFrontDisConnected(on OnFrontDisConnectedType) {
	t.onFrontDisConnected = on
}

// RegOnRspUserLogin 注册登陆响应
func (t *Trade) RegOnRspUserLogin(on OnRspUserLoginType) {
	t.onRspUserLogin = on
}

// RegOnRtnOrder 注册委托响应
func (t *Trade) RegOnRtnOrder(on OnRtnOrderType) {
	t.onRtnOrder = on
}

// RegOnErrRtnOrder 注册委托响应
func (t *Trade) RegOnErrRtnOrder(on OnRtnErrOrderType) {
	t.onErrRtnOrder = on
}

// RegOnErrAction 注册撤单响应
func (t *Trade) RegOnErrAction(on OnRtnErrActionType) {
	t.onErrAction = on
}

// RegOnRtnCancel 注册撤单响应
func (t *Trade) RegOnRtnCancel(on OnRtnOrderType) {
	t.onRtnCancel = on
}

// RegOnRtnTrade 注册成交响应
func (t *Trade) RegOnRtnTrade(on OnRtnTradeType) {
	t.onRtnTrade = on
}

// RegOnRtnInstrumentStatus 注册合约状态变化
func (t *Trade) RegOnRtnInstrumentStatus(on OnRtnInstrumentStatusType) {
	t.onRtnInstrumentStatus = on
}

func (t *Trade) RegOnRtnInstrumentMarginRate(on OnRtnInstrumentMarginRateType) {
	t.onRtnInstrumentMarginRate = on
}

func (t *Trade) RegOnRtnFromBankToFuture(on OnRtnFromBankToFutureByFuture) {
	t.onRtnBankToFuture = on
}

func (t *Trade) RegOnRtnFromFutureToBank(on OnRtnFromFutureToBankByFuture) {
	t.onRtnFutureToBank = on
}

// ********************** 底层接口响应处理 **********************************

//export tRtnFromBankToFutureByFuture
func tRtnFromBankToFutureByFuture(field *C.struct_CThostFtdcRspTransferField) C.int {
	transField := (*define.CThostFtdcRspTransferField)(unsafe.Pointer(field))
	f := TransferField{
		Amout:      float64(transField.TradeAmount),
		CurrencyID: Bytes2String(transField.CurrencyID[:]),
		ErrorID:    int(transField.ErrorID),
		ErrorMsg:   Bytes2String(transField.ErrorMsg[:]),
	}
	zlog.Debugf("[goctp trade] rtn from bank to future: %#v", f)
	if t.onRtnBankToFuture != nil {
		go t.onRtnBankToFuture(&f)
	}
	return 0
}

//export tRtnFromFutureToBankByFuture
func tRtnFromFutureToBankByFuture(field *C.struct_CThostFtdcRspTransferField) C.int {
	transField := (*define.CThostFtdcRspTransferField)(unsafe.Pointer(field))
	f := TransferField{
		Amout:      float64(transField.TradeAmount),
		CurrencyID: Bytes2String(transField.CurrencyID[:]),
		ErrorID:    int(transField.ErrorID),
		ErrorMsg:   Bytes2String(transField.ErrorMsg[:]),
	}
	zlog.Debugf("[goctp trade] rtn from future to bank: %#v", f)
	if t.onRtnFutureToBank != nil {
		go t.onRtnFutureToBank(&f)
	}
	return 0
}

//export tRtnInstrumentStatus
func tRtnInstrumentStatus(field *C.struct_CThostFtdcInstrumentStatusField) C.int {
	statusField := (*define.CThostFtdcInstrumentStatusField)(unsafe.Pointer(field))
	status, loaded := t.InstrumentStatuss.LoadOrStore(Bytes2String(statusField.InstrumentID[:]), &InstrumentStatus{
		ExchangeID:       Bytes2String(statusField.ExchangeID[:]),
		InstrumentID:     Bytes2String(statusField.InstrumentID[:]),
		InstrumentStatus: InstrumentStatusType(statusField.InstrumentStatus),
		EnterTime:        Bytes2String(statusField.EnterTime[:]),
		TouchTime:        time.Now(),
	})
	if loaded {
		status.InstrumentStatus = InstrumentStatusType(statusField.InstrumentStatus)
		status.EnterTime = Bytes2String(statusField.EnterTime[:])
		status.TouchTime = time.Now()
	}
	zlog.Debugf("[goctp trade] rtn instrument status, %s - %s - %v", Bytes2String(statusField.ExchangeID[:]), Bytes2String(statusField.InstrumentID[:]), InstrumentStatusType(statusField.InstrumentStatus))
	if t.onRtnInstrumentStatus != nil {
		go t.onRtnInstrumentStatus(status)
	}
	return 0
}

//export tRtnTrade
func tRtnTrade(field *C.struct_CThostFtdcTradeField) C.int {
	tradeField := (*define.CThostFtdcTradeField)(unsafe.Pointer(field))
	var key string
	tradeID := Bytes2String(tradeField.TradeID[:])
	if tradeField.Direction == define.THOST_FTDC_D_Buy {
		key = fmt.Sprintf("%s_buy", tradeID)
	} else if tradeField.Direction == define.THOST_FTDC_D_Sell {
		key = fmt.Sprintf("%s_sell", tradeID)
	} else {
		key = "error"
	}
	zlog.Debugf("[goctp trade] rtn trade, key: %s", key)

	tf, _ := t.Trades.LoadOrStore(key, &TradeField{
		OrderRef:     Bytes2String(tradeField.OrderRef[:]),
		Direction:    DirectionType(tradeField.Direction),
		HedgeFlag:    HedgeFlagType(tradeField.HedgeFlag),
		InstrumentID: Bytes2String(tradeField.InstrumentID[:]),
		ExchangeID:   Bytes2String(tradeField.ExchangeID[:]),
		TradingDay:   Bytes2String(tradeField.TradingDay[:]),
		Volume:       int(tradeField.Volume),
		OffsetFlag:   OffsetFlagType(tradeField.OffsetFlag),
		OrderSysID:   Bytes2String(tradeField.OrderSysID[:]),
		Price:        float64(tradeField.Price),
		TradeDate:    Bytes2String(tradeField.TradeDate[:]),
		TradeTime:    Bytes2String(tradeField.TradeTime[:]),
		TradeID:      tradeID,
	})
	var f = tf
	if t.PositionReady.Load() { // 登陆后更新持仓
		if f.OffsetFlag == OffsetFlagOpen { // 开仓
			var key string
			if f.Direction == DirectionBuy {
				key = fmt.Sprintf("%s_long", f.InstrumentID)
			} else {
				key = fmt.Sprintf("%s_short", f.InstrumentID)
			}
			pf, _ := t.Positions.LoadOrStore(key, &PositionField{
				InstrumentID:      f.InstrumentID,
				PositionDirection: PosiDirectionLong,
				HedgeFlag:         f.HedgeFlag,
				ExchangeID:        f.ExchangeID,
			})
			var p = pf
			p.OpenVolume += f.Volume
			p.OpenAmount += f.Price * float64(f.Volume)
			if info, ok := t.Instruments.Load(f.InstrumentID); ok {
				p.OpenCost += f.Price * float64(f.Volume) * float64(info.VolumeMultiple)
			}
			p.Position += f.Volume
			p.TodayPosition += f.Volume
			p.UpdateTime = ParseTimeWithString(f.TradeDate, f.TradeTime)
			p.TouchTime = time.Now()
		} else {
			var key string
			if f.Direction == DirectionBuy {
				key = fmt.Sprintf("%s_short", f.InstrumentID)
			} else {
				key = fmt.Sprintf("%s_long", f.InstrumentID)
			}
			if posi, ok := t.Positions.Load(key); ok {
				var p = posi
				p.OpenVolume -= f.Volume
				p.OpenAmount -= f.Price * float64(f.Volume)
				if info, ok := t.Instruments.Load(f.InstrumentID); ok {
					p.OpenCost -= f.Price * float64(f.Volume) * float64(info.VolumeMultiple)
				}
				p.Position -= f.Volume
				// 解锁冻结,
				if f.Direction == DirectionBuy { // 冻结空头
					p.LongFrozen -= f.Volume
				} else { // 冻结多头
					p.ShortFrozen -= f.Volume
				}
				if f.OffsetFlag == OffsetFlagCloseToday {
					p.TodayPosition -= f.Volume
				} else { // 先平昨
					lots := f.Volume
					if p.YdPosition > 0 {
						if lots >= p.YdPosition { // 昨仓全平
							lots -= p.YdPosition
							p.YdPosition = 0
						} else { // 昨仓减少
							p.YdPosition -= lots
							lots = 0
						}
					}
					p.TodayPosition -= lots
				}
				p.UpdateTime = ParseTimeWithString(f.TradeDate, f.TradeTime)
				p.TouchTime = time.Now()
			}
		}
	}
	// 处理对应的 Order
	// 因为成交数据里没有 FrontID 和 SessionID，所以用 交易所 + OrderSysID 确认唯一订单
	if o, ok := t.SysID4Order.Load(fmt.Sprintf("%s_%s", f.ExchangeID, f.OrderSysID)); ok {
		if o.TradePrice == 0 { // 在 VolumeLeft 前计算
			o.TradePrice = f.Price
		} else { // 计算均价
			o.TradePrice = (o.TradePrice*float64(o.VolumeTotalOriginal-o.VolumeLeft) + f.Price*float64(f.Volume)) / float64(o.VolumeTotalOriginal-o.VolumeLeft+f.Volume)
		}
		o.LastTradeDate = f.TradeDate
		o.LastTradeTime = f.TradeTime
		o.VolumeTraded = f.Volume
		if o.VolumeLeft > 0 { // 如果是本地没有的订单，首次添加时该值就可能已经是 0 了
			o.VolumeLeft -= f.Volume
		}
		if o.VolumeLeft == 0 {
			o.OrderStatus = OrderStatusAllTraded
			o.StatusMsg = "全部成交"
		} else {
			o.OrderStatus = OrderStatusPartTradedQueueing
			o.StatusMsg = "部分成交"
		}
		if t.onRtnOrder != nil {
			go t.onRtnOrder(o)
		}

		if o.RelativeOrderSysID != "" {
			// 更新对应条件单的数据
			if o, ok := t.SysID4Order.Load(fmt.Sprintf("%s_%s", f.ExchangeID, o.RelativeOrderSysID)); ok {
				if o.TradePrice == 0 { // 在 VolumeLeft 前计算
					o.TradePrice = f.Price
				} else { // 计算均价
					o.TradePrice = (o.TradePrice*float64(o.VolumeTotalOriginal-o.VolumeLeft) + f.Price*float64(f.Volume)) / float64(o.VolumeTotalOriginal-o.VolumeLeft+f.Volume)
				}
				o.LastTradeDate = f.TradeDate
				o.LastTradeTime = f.TradeTime

				o.VolumeTraded = f.Volume
				if o.VolumeLeft > 0 {
					o.VolumeLeft -= f.Volume
				}
				if o.VolumeLeft == 0 {
					o.OrderStatus = OrderStatusAllTraded
					o.StatusMsg = "全部成交"
				} else {
					o.OrderStatus = OrderStatusPartTradedQueueing
					o.StatusMsg = "部分成交"
				}
				if t.onRtnOrder != nil {
					go t.onRtnOrder(o)
				}
			}
		}
	}

	// 客户端响应
	if t.onRtnTrade != nil {
		go t.onRtnTrade(f)
	}
	return 0
}

//export tRtnOrder
func tRtnOrder(field *C.struct_CThostFtdcOrderField) C.int {
	// 订单首先通过 CTP 成功后会回调一次，然后到交易所，成功或失败都会再调一次
	// 同一订单会回调多次，例如挂单会先给一个“已提交”的状态数据，然后再给一个“未成交”的数据
	t.cntOrder++
	orderField := (*define.CThostFtdcOrderField)(unsafe.Pointer(field))

	orderID := fmt.Sprintf("%d_%d_%s", orderField.FrontID, orderField.SessionID, Bytes2String(orderField.OrderRef[:]))
	zlog.Debugf("[goctp trade] rtn order, key: %s, status: %v", orderID, OrderStatusType(orderField.OrderStatus))
	if f, exists := t.Orders.LoadOrStore(orderID, convertOrderField(orderField)); !exists { // 不在本地，首次回调
		if t.PositionReady.Load() {
			// 平仓指令, 冻结持仓(随后的持仓查询会进行修正),冻结持仓恢复会滞后 <=2s
			if f.OffsetFlag != OffsetFlagOpen {
				if f.Direction == DirectionBuy { // 冻结空头
					key := fmt.Sprintf("%s_short", f.InstrumentID)
					if posiField, ok := t.Positions.Load(key); ok {
						posiField.LongFrozen += f.VolumeTotalOriginal
						posiField.UpdateTime = ParseTimeWithString(f.InsertDate, f.InsertTime)
						posiField.TouchTime = time.Now()
					}
				} else {
					key := fmt.Sprintf("%s_long", f.InstrumentID)
					if posiField, ok := t.Positions.Load(key); ok { // 冻结多头
						posiField.ShortFrozen += f.VolumeTotalOriginal
						posiField.UpdateTime = ParseTimeWithString(f.InsertDate, f.InsertTime)
						posiField.TouchTime = time.Now()
					}
				}
			}
		}
		// 因为 orderField.RequestID == 0，只能从 OrderRef 中解析原始的 ReqID
		processRequestResult(fmt.Sprintf("CreateOrder:%s", orderID), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success: true,
				Order:   *f,
			}
		})
		zlog.Debugf("[goctp trade] rtn order, order not exist, key: %s", orderID)
		if t.onRtnOrder != nil {
			go t.onRtnOrder(f)
		}
	} else { // 本地已记录的订单
		f.RequestID = int(orderField.RequestID)
		if OrderStatusType(orderField.OrderStatus) == OrderStatusCanceled { // 处理撤单
			zlog.Debugf("[goctp trade] rtn order, order canceled, key: %s", orderID)
			f.OrderStatus = OrderStatusCanceled
			f.StatusMsg = Bytes2String(orderField.StatusMsg[:])
			f.CancelTime = Bytes2String(orderField.CancelTime[:])
			// 错单
			if t.PositionReady.Load() { // 登录后才响应
				// 解锁冻结,
				if f.OffsetFlag != OffsetFlagOpen {
					if f.Direction == DirectionBuy { // 冻结空头
						key := fmt.Sprintf("%s_short", f.InstrumentID)
						if posiField, ok := t.Positions.Load(key); ok {
							posiField.LongFrozen -= f.VolumeLeft
							posiField.UpdateTime = ParseTimeWithString(f.InsertDate, f.CancelTime)
							posiField.TouchTime = time.Now()
						}
					} else {
						key := fmt.Sprintf("%s_long", f.InstrumentID)
						if posiField, ok := t.Positions.Load(key); ok { // 冻结多头
							posiField.ShortFrozen -= f.VolumeLeft
							posiField.UpdateTime = ParseTimeWithString(f.InsertDate, f.CancelTime)
							posiField.TouchTime = time.Now()
						}
					}
				}
			}
			cancelOrderKey := fmt.Sprintf("CancelOrder:%s", orderID)
			cancelOrderSysID := fmt.Sprintf("CancelOrder:%s", f.OrderSysID)
			processRequestResultMultipleKeys([]string{cancelOrderKey, cancelOrderSysID}, func(resultChan chan RequestResult) {
				resultChan <- RequestResult{
					Success: true,
					Order:   *f,
				}
			})

			// 这里可能是区分了被动拒绝，和主动取消；两者的回调不同
			// 如果 OrderInsert 的价格不满足位数要求，会先 onRtnOrder 一个“已提交”，然后随后返回一个“被拒绝”；
			if strings.Contains(f.StatusMsg, "被拒绝") {
				if t.onErrRtnOrder != nil {
					go t.onErrRtnOrder(f, &RspInfoField{
						ErrorID:  -1,
						ErrorMsg: f.StatusMsg,
					})
				}
			} else {
				if t.onRtnCancel != nil {
					go t.onRtnCancel(f)
				}
			}
		} else {
			zlog.Debugf("[goctp trade] rtn order, other order status, key: %s", orderID)
			if OrderStatusType(orderField.OrderStatus) == OrderStatusTouched { // 条件单已触发
				f.OrderStatus = OrderStatusTouched
				f.StatusMsg = Bytes2String(orderField.StatusMsg[:])
			}

			processRequestResult(fmt.Sprintf("CreateOrder:%s", orderID), func(resultChan chan RequestResult) {
				resultChan <- RequestResult{
					Success: true,
					Order:   *f,
				}
			})

			if t.onRtnOrder != nil {
				go t.onRtnOrder(f)
			}

			// 如有 OrderSysID 则更新 sysID4Order，用于成交数据更新
			f.OrderSysID = Bytes2String(orderField.OrderSysID[:])
			if len(f.OrderSysID) > 0 {
				// 交易所 + OrderSysID 确认唯一订单
				key := fmt.Sprintf("%s_%s", Bytes2String(orderField.ExchangeID[:]), f.OrderSysID)
				t.SysID4Order.Store(key, f)
			}
		}
	}
	return 0
}

func convertOrderField(orderField *define.CThostFtdcOrderField) *OrderField {
	return &OrderField{
		InstrumentID:        Bytes2String(orderField.InstrumentID[:]),
		SessionID:           int(orderField.SessionID),
		FrontID:             int(orderField.FrontID),
		RequestID:           int(orderField.RequestID),
		OrderRef:            Bytes2String(orderField.OrderRef[:]),
		Direction:           DirectionType(orderField.Direction),
		OffsetFlag:          OffsetFlagType(orderField.CombOffsetFlag[0]),
		HedgeFlag:           HedgeFlagType(orderField.CombHedgeFlag[0]),
		LimitPrice:          float64(orderField.LimitPrice),
		StopPrice:           float64(orderField.StopPrice),
		VolumeTotalOriginal: int(orderField.VolumeTotalOriginal),
		VolumeLeft:          int(orderField.VolumeTotal), // 原代码应该是 typo ，写成了 orderField.VolumeTotalOriginal
		ExchangeID:          Bytes2String(orderField.ExchangeID[:]),
		InsertDate:          Bytes2String(orderField.InsertDate[:]),
		InsertTime:          Bytes2String(orderField.InsertTime[:]),
		UpdateTime:          Bytes2String(orderField.UpdateTime[:]),
		OrderSubmitStatus:   OrderSubmitStatusType(orderField.OrderSubmitStatus),
		OrderStatus:         OrderStatusType(orderField.OrderStatus),
		StatusMsg:           Bytes2String(orderField.StatusMsg[:]),
		IsLocal:             int(orderField.SessionID) == t.SessionID,
		OrderSysID:          Bytes2String(orderField.OrderSysID[:]),
		RelativeOrderSysID:  Bytes2String(orderField.RelativeOrderSysID[:]),
	}
}

// 只有撤单操作填写错误才会返回
// 操作出错除了会调用这个函数外，还会调用 tErrRtnOrderAction ，并且此处的 RequestID = 0，在这里处理结果没有任何好处
// 因此只用在 tErrRtnOrderAction 即可
//
//export tRspOrderAction
func tRspOrderAction(field *C.struct_CThostFtdcOrderActionField, info *C.struct_CThostFtdcRspInfoField, i C.int, b C._Bool) C.int {
	return 0
}

//export tErrRtnOrderAction
func tErrRtnOrderAction(field *C.struct_CThostFtdcOrderActionField, info *C.struct_CThostFtdcRspInfoField) C.int {
	actionField := (*define.CThostFtdcOrderActionField)(unsafe.Pointer(field))
	infoField := (*define.CThostFtdcRspInfoField)(unsafe.Pointer(info))
	orderRef := Bytes2String(actionField.OrderRef[:])
	orderID := fmt.Sprintf("%d_%d_%s", actionField.FrontID, actionField.SessionID, orderRef)
	orderSysID := Bytes2String(actionField.OrderSysID[:])
	zlog.Debugf("[goctp trade] err rtn order action, orderID: %s, orderSysID: %s", orderID, orderSysID)
	success := false
	// 已撤销的撤单当作成功，
	// 24        | 撤单已报送，不允许重复撤单
	// 26		 | 报单已全成交或已撤销，不能再撤
	if SliceContains([]int{24, 26}, int(infoField.ErrorID)) {
		success = true
	}
	processRequestResultMultipleKeys([]string{fmt.Sprintf("CancelOrder:%s", orderID), fmt.Sprintf("CancelOrder:%s", orderSysID)}, func(resultChan chan RequestResult) {
		resultChan <- RequestResult{
			Success:  success,
			ErrorID:  int(infoField.ErrorID),
			ErrorMsg: Bytes2String(infoField.ErrorMsg[:]),
		}
	})
	if t.onErrAction != nil {
		go t.onErrAction(orderID, int(actionField.RequestID), &RspInfoField{
			ErrorID:  int(infoField.ErrorID),
			ErrorMsg: Bytes2String(infoField.ErrorMsg[:]),
		})
	}
	return 0
}

// ReqOrderInsert报单后，如果在CTP端验资验仓等通不过，则会回调函数OnRspOrderInsert和OnErrRtnOrderInsert；如果通过则先回调一次OnRtnOrder
// OnRspOrderInsert是当前报单者收到的回调，OnErrRtnOrderInsert是该客户名下所有的链接都会收到的回调
//
//export tRspOrderInsert
func tRspOrderInsert(field *C.struct_CThostFtdcInputOrderField, info *C.struct_CThostFtdcRspInfoField, i C.int, b C._Bool) C.int {
	orderField := (*define.CThostFtdcInputOrderField)(unsafe.Pointer(field))
	infoField := (*define.CThostFtdcRspInfoField)(unsafe.Pointer(info))
	// 这里仅处理创建出错的情况，成功的情况在 onRtnOrder 中处理
	if infoField.ErrorID < 0 {
		processRequestResult(fmt.Sprintf("%d", orderField.RequestID), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success:  false,
				ErrorID:  int(infoField.ErrorID),
				ErrorMsg: Bytes2String(infoField.ErrorMsg[:]),
			}
		})
	}
	return 0
}

//export tErrRtnOrderInsert
func tErrRtnOrderInsert(field *C.struct_CThostFtdcInputOrderField, info *C.struct_CThostFtdcRspInfoField) C.int {
	orderField := (*define.CThostFtdcInputOrderField)(unsafe.Pointer(field))
	infoField := (*define.CThostFtdcRspInfoField)(unsafe.Pointer(info))
	orderID := fmt.Sprintf("%d_%d_%s", t.FrontID, t.SessionID, Bytes2String(orderField.OrderRef[:]))
	zlog.Debugf("[goctp trade] err rtn order insert, key: %s", orderID)
	of, _ := t.Orders.LoadOrStore(orderID, &OrderField{
		InstrumentID:        Bytes2String(orderField.InstrumentID[:]),
		SessionID:           t.SessionID,
		FrontID:             0,
		OrderRef:            Bytes2String(orderField.OrderRef[:]),
		Direction:           DirectionType(orderField.Direction),
		OffsetFlag:          OffsetFlagType(orderField.CombOffsetFlag[0]),
		HedgeFlag:           HedgeFlagType(orderField.CombHedgeFlag[0]),
		LimitPrice:          float64(orderField.LimitPrice),
		StopPrice:           float64(orderField.StopPrice),
		VolumeTotalOriginal: int(orderField.VolumeTotalOriginal),
		VolumeLeft:          int(orderField.VolumeTotalOriginal),
		ExchangeID:          Bytes2String(orderField.ExchangeID[:]),
		IsLocal:             true,
	})
	of.OrderStatus = OrderStatusCanceled
	processRequestResult(fmt.Sprintf("%d", orderField.RequestID), func(resultChan chan RequestResult) {
		resultChan <- RequestResult{
			Success:  false,
			ErrorID:  int(infoField.ErrorID),
			ErrorMsg: Bytes2String(infoField.ErrorMsg[:]),
		}
	})
	if t.onErrRtnOrder != nil {
		go t.onErrRtnOrder(of, &RspInfoField{ErrorID: int(infoField.ErrorID), ErrorMsg: Bytes2String(infoField.ErrorMsg[:])})
	}
	return 0
}

const ApplicationError = -10000

// 不会返回任何有意义的结果，可能看起来 onRtnOrder 也没有结果，看起来查询没有任何用处
//
//export tRspQryOrder
func tRspQryOrder(field *C.struct_CThostFtdcOrderField, info *C.struct_CThostFtdcRspInfoField, i C.int, b C._Bool) C.int {
	orderField := (*define.CThostFtdcOrderField)(unsafe.Pointer(field))
	orderRef := Bytes2String(orderField.OrderRef[:])
	if orderField.SessionID == 0 && orderRef == "" {
		processRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success:  false,
				ErrorID:  ApplicationError,
				ErrorMsg: "empty order field",
			}
		})
		zlog.Debugf("[goctp trade] rsp qry order(%d): empty order field", i)
		return 0
	}
	key := fmt.Sprintf("%d_%d_%s", orderField.FrontID, orderField.SessionID, orderRef)
	zlog.Debugf("[goctp trade] rsp qry order(%d, %s)", i, key)
	tRtnOrder(field)

	processRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
		order := convertOrderField(orderField)
		resultChan <- RequestResult{
			Success: true,
			Order:   *order,
		}
	})

	return 0
}

//export tRspQryInvestorPosition
func tRspQryInvestorPosition(field *C.struct_CThostFtdcInvestorPositionField, info *C.struct_CThostFtdcRspInfoField, i C.int, b C._Bool) C.int {
	// zlog.Debugf("[goctp trade] rsp qry investor position")
	tmp := (*define.CThostFtdcInvestorPositionField)(unsafe.Pointer(field))
	// 复制接口中的数据
	var buf bytes.Buffer
	var p = new(define.CThostFtdcInvestorPositionField)
	gob.NewEncoder(&buf).Encode(tmp)
	gob.NewDecoder(bytes.NewBuffer(buf.Bytes())).Decode(p)
	// infoField := (*define.CThostFtdcRspInfoField)(unsafe.Pointer(info))

	// 处理主动查询，所有 posiDetail 都保存到 queryID 下
	requestID := fmt.Sprintf("%d", i)
	if queryID, found := t.requestIDMap.Load(requestID); found {
		ps, _ := t.queryPosiDetails.LoadOrStore(queryID, make([]*define.CThostFtdcInvestorPositionField, 0))
		ps = append(ps, p)
		t.queryPosiDetails.Store(queryID, ps) // append后指针有变化,需重新赋值

		if b {
			// 如果是主动查询，合并查询结果
			// zlog.Debugf("[goctp trade] position ready")
			t.PositionReady.Store(true)

			// 按品种和持仓方向整理 positionField
			positionMap := map[string][]*define.CThostFtdcInvestorPositionField{}
			for _, p := range ps {
				instrumentID := Bytes2String(p.InstrumentID[:])
				if instrumentID == "" {
					continue
				}
				var key string
				if p.PosiDirection == define.THOST_FTDC_PD_Long {
					key = fmt.Sprintf("%s_long", instrumentID)
				} else if p.PosiDirection == define.THOST_FTDC_PD_Short {
					key = fmt.Sprintf("%s_short", instrumentID)
				} else {
					key = fmt.Sprintf("%s_net", instrumentID)
				}
				if v, found := positionMap[key]; found {
					v = append(v, p)
					positionMap[key] = v
				} else {
					positionMap[key] = []*define.CThostFtdcInvestorPositionField{p}
				}
			}
			positions := []PositionField{}
			// 按品种和持仓方向合并查询结果
			for key, posiFieldList := range positionMap {
				position := mergePositionFieldList(posiFieldList)
				positions = append(positions, position)

				t.Positions.Store(key, &position)
				t.PosiDetail.Store(key, posiFieldList)
			}

			if resultChan, found := t.requestResultChans.Load(queryID); found {
				resultChan <- RequestResult{
					Success:   true,
					Positions: positions,
				}
			}
		}
	}
	return 0
}

func mergePositionFieldList(posiDetails []*define.CThostFtdcInvestorPositionField) PositionField {
	pFinal := PositionField{}
	for _, p := range posiDetails {
		pFinal.InstrumentID = Bytes2String(p.InstrumentID[:])
		pFinal.PositionDirection = PosiDirectionType(p.PosiDirection)
		pFinal.HedgeFlag = HedgeFlagType(p.HedgeFlag)
		pFinal.ExchangeID = Bytes2String(p.ExchangeID[:])
		pFinal.PreSettlementPrice = float64(p.PreSettlementPrice)
		pFinal.SettlementPrice = float64(p.SettlementPrice)

		pFinal.Position += int(p.Position)
		pFinal.TodayPosition += int(p.TodayPosition)
		// pFinal.YdPosition += int(p.YdPosition) // 直接取值还需要减去当日平仓
		pFinal.YdPosition = pFinal.Position - pFinal.TodayPosition
		pFinal.LongFrozen += int(p.LongFrozen)
		pFinal.ShortFrozen += int(p.ShortFrozen)
		pFinal.LongFrozenAmount += float64(p.LongFrozenAmount)
		pFinal.ShortFrozenAmount += float64(p.ShortFrozenAmount)
		pFinal.OpenVolume += int(p.OpenVolume)
		pFinal.CloseVolume += int(p.CloseVolume)
		pFinal.OpenAmount += float64(p.OpenAmount)
		pFinal.CloseAmount += float64(p.CloseAmount)
		pFinal.PositionCost += float64(p.PositionCost)
		pFinal.PreMargin += float64(p.PreMargin)
		pFinal.UseMargin += float64(p.UseMargin)
		pFinal.FrozenMargin += float64(p.FrozenMargin)
		pFinal.FrozenCash += float64(p.FrozenCash)
		pFinal.FrozenCommission += float64(p.FrozenCommission)
		pFinal.CashIn += float64(p.CashIn)
		pFinal.Commission += float64(p.Commission)
		pFinal.CloseProfit += float64(p.CloseProfit)
		pFinal.PositionProfit += float64(p.PositionProfit)
		pFinal.OpenCost += float64(p.OpenCost)
		pFinal.ExchangeMargin += float64(p.ExchangeMargin)
		pFinal.CombPosition += int(p.CombPosition)
		pFinal.CombLongFrozen += int(p.CombLongFrozen)
		pFinal.CombShortFrozen += int(p.CombShortFrozen)
		pFinal.CloseProfitByDate += float64(p.CloseProfitByDate)
		pFinal.CloseProfitByTrade += float64(p.CloseProfitByTrade)
		pFinal.StrikeFrozen += int(p.StrikeFrozen)
		pFinal.StrikeFrozenAmount += float64(p.StrikeFrozenAmount)
		pFinal.AbandonFrozen += int(p.AbandonFrozen)
		pFinal.YdStrikeFrozen += int(p.YdStrikeFrozen)
		pFinal.PositionCostOffset += float64(p.PositionCostOffset)

	}
	pFinal.UpdateTime = time.Now()
	pFinal.TouchTime = time.Now()
	return pFinal
}

//export tRspQryTradingAccount
func tRspQryTradingAccount(field *C.struct_CThostFtdcTradingAccountField, info *C.struct_CThostFtdcRspInfoField, i C.int, b C._Bool) C.int {
	t.accountMutex.Lock()
	t.accountMutex.Unlock()

	// zlog.Debugf("[goctp trade] rsp qry trading account")
	accountField := (*define.CThostFtdcTradingAccountField)(unsafe.Pointer(field))
	//infoField := (* define.CThostFtdcRspInfoField)(unsafe.Pointer(info))
	t.Account.PreMortgage = float64(accountField.PreMortgage)
	t.Account.PreDeposit = float64(accountField.PreDeposit)
	t.Account.PreBalance = float64(accountField.PreBalance)
	t.Account.PreMargin = float64(accountField.PreMargin)
	t.Account.InterestBase = float64(accountField.InterestBase)
	t.Account.Interest = float64(accountField.Interest)
	t.Account.Deposit = float64(accountField.Deposit)
	t.Account.Withdraw = float64(accountField.Withdraw)
	t.Account.FrozenMargin = float64(accountField.FrozenMargin)
	t.Account.FrozenCash = float64(accountField.FrozenCash)
	t.Account.FrozenCommission = float64(accountField.FrozenCommission)
	t.Account.CurrMargin = float64(accountField.CurrMargin)
	t.Account.CashIn = float64(accountField.CashIn)
	t.Account.Commission = float64(accountField.Commission)
	t.Account.CloseProfit = float64(accountField.CloseProfit)
	t.Account.PositionProfit = float64(accountField.PositionProfit)
	t.Account.Balance = float64(accountField.Balance)
	t.Account.Available = float64(accountField.Available)
	t.Account.WithdrawQuota = float64(accountField.WithdrawQuota)
	t.Account.Reserve = float64(accountField.Reserve)
	t.Account.Credit = float64(accountField.Credit)
	t.Account.Mortgage = float64(accountField.Mortgage)
	t.Account.ExchangeMargin = float64(accountField.ExchangeMargin)
	t.Account.DeliveryMargin = float64(accountField.DeliveryMargin)
	t.Account.ExchangeDeliveryMargin = float64(accountField.ExchangeDeliveryMargin)
	t.Account.ReserveBalance = float64(accountField.ReserveBalance)
	t.Account.CurrencyID = Bytes2String(accountField.CurrencyID[:])
	t.Account.PreFundMortgageIn = float64(accountField.PreFundMortgageIn)
	t.Account.PreFundMortgageOut = float64(accountField.PreFundMortgageOut)
	t.Account.FundMortgageIn = float64(accountField.FundMortgageIn)
	t.Account.FundMortgageOut = float64(accountField.FundMortgageOut)
	t.Account.FundMortgageAvailable = float64(accountField.FundMortgageAvailable)
	t.Account.MortgageableFund = float64(accountField.MortgageableFund)

	if b {
		// zlog.Debugf("[goctp trade] account ready")
		t.AccountReady.Store(true)
		processRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success: true,
				Account: *t.Account,
			}
		})
	}
	return 0
}

//export tRspQryClassifiedInstrument
func tRspQryClassifiedInstrument(field *C.struct_CThostFtdcInstrumentField, info *C.struct_CThostFtdcRspInfoField, i C.int, b C._Bool) C.int {
	return tRspQryInstrument(field, info, i, b)
}

//export tRspQryInstrument
func tRspQryInstrument(field *C.struct_CThostFtdcInstrumentField, info *C.struct_CThostFtdcRspInfoField, i C.int, b C._Bool) C.int {
	instrumentField := (*define.CThostFtdcInstrumentField)(unsafe.Pointer(field))
	// infoField := (*define.CThostFtdcRspInfoField)(unsafe.Pointer(info))
	instrumentID := Bytes2String(instrumentField.InstrumentID[:])
	zlog.Debugf("[goctp trade] rsp qry instrument: %s", instrumentID)
	if instrumentField != nil {
		t.Instruments.Store(instrumentID, &InstrumentField{
			InstrumentID:              instrumentID,
			ExchangeID:                Bytes2String(instrumentField.ExchangeID[:]),
			ProductID:                 Bytes2String(instrumentField.ProductID[:]),
			ProductClass:              ProductClassType(instrumentField.ProductClass),
			DeliveryYear:              int(instrumentField.DeliveryYear),
			DeliveryMonth:             int(instrumentField.DeliveryMonth),
			MaxMarketOrderVolume:      int(instrumentField.MaxMarketOrderVolume),
			MinMarketOrderVolume:      int(instrumentField.MinMarketOrderVolume),
			MaxLimitOrderVolume:       int(instrumentField.MaxLimitOrderVolume),
			MinLimitOrderVolume:       int(instrumentField.MinLimitOrderVolume),
			VolumeMultiple:            int(instrumentField.VolumeMultiple),
			PriceTick:                 float64(instrumentField.PriceTick),
			PositionType:              PositionTypeType(instrumentField.PositionType),
			UseMaxMarginSideAlgorithm: instrumentField.MaxMarginSideAlgorithm == '1',
			UnderlyingInstrID:         Bytes2String(instrumentField.UnderlyingInstrID[:]),
			StrikePrice:               float64(instrumentField.StrikePrice),
			OptionsType:               OptionsTypeType(instrumentField.OptionsType),
			UnderlyingMultiple:        float64(instrumentField.UnderlyingMultiple),
			CombinationType:           CombinationTypeType(instrumentField.CombinationType),
			TouchTime:                 time.Now(),
		})
	}
	if b {
		zlog.Debugf("[goctp trade] instruments ready")
		t.InstrumentsReady.Store(true)
		processRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success: true,
			}
		})
	}
	return 0
}

//export tRspQryInstrumentMarginRate
func tRspQryInstrumentMarginRate(field *C.struct_CThostFtdcInstrumentMarginRateField, info *C.struct_CThostFtdcRspInfoField, i C.int, b C._Bool) C.int {
	instrumentRateField := (*define.CThostFtdcInstrumentMarginRateField)(unsafe.Pointer(field))
	if instrumentRateField != nil {
		instrumentID := Bytes2String(instrumentRateField.InstrumentID[:])
		zlog.Debugf("[goctp trade] rsp qry instrument margin rate: %s", instrumentID)
		mr := &InstrumentMarginRate{
			HedgeFlag:                HedgeFlagType(instrumentRateField.HedgeFlag),
			LongMarginRatioByMoney:   float64(instrumentRateField.LongMarginRatioByMoney),
			LongMarginRatioByVolume:  float64(instrumentRateField.LongMarginRatioByVolume),
			ShortMarginRatioByMoney:  float64(instrumentRateField.ShortMarginRatioByMoney),
			ShortMarginRatioByVolume: float64(instrumentRateField.ShortMarginRatioByVolume),
			IsRelative:               int(instrumentRateField.IsRelative) == 1,
			ExchangeID:               Bytes2String(instrumentRateField.ExchangeID[:]),
			InvestUnitID:             Bytes2String(instrumentRateField.InvestUnitID[:]),
			InstrumentID:             instrumentID,
		}
		t.InstrumentMarginRates.Store(instrumentID, mr)
		processRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success: true,
			}
		})

		if t.onRtnInstrumentMarginRate != nil {
			go t.onRtnInstrumentMarginRate(mr)
		}
	} else {
		processRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success:  false,
				ErrorID:  ApplicationError,
				ErrorMsg: "instrument rate field is nil",
			}
		})
	}
	return 0
}

func processRequestResult(reqIDOrKey string, callback func(resultChan chan RequestResult)) {
	if reqIDOrKey != "0" {
		if queryID, found := t.requestIDMap.Load(reqIDOrKey); found {
			if resultChan, found := t.requestResultChans.Load(queryID); found {
				if callback != nil {
					callback(resultChan)
					zlog.Debugf("[goctp trade] process request result success, callback called, reqID: (%s), queryID: (%s)", reqIDOrKey, queryID)
				}
			} else {
				zlog.Debugf("[goctp trade] process request result failed, result chan not found, reqID: (%s), queryID: (%s)", reqIDOrKey, queryID)
			}
		} else {
			zlog.Debugf("[goctp trade] process request result failed, queryID not found, reqID: (%s)", reqIDOrKey)
		}
	} else {
		zlog.Debugf("[goctp trade] process request result failed, reqID is 0")
	}
}

func processRequestResultMultipleKeys(reqIDOrKey []string, callback func(resultChan chan RequestResult)) {
	for _, key := range reqIDOrKey {
		processRequestResult(key, callback)
	}
}

//export tRspSettlementInfoConfirm
func tRspSettlementInfoConfirm(field *C.struct_CThostFtdcSettlementInfoConfirmField, info *C.struct_CThostFtdcRspInfoField, i C.int, b C._Bool) C.int {
	infoField := (*define.CThostFtdcRspInfoField)(unsafe.Pointer(info))
	zlog.Debugf("[goctp trade] rsp settlement info confirm, reqID: %d, info: [%d]%s", i, infoField.ErrorID, Bytes2String(infoField.ErrorMsg[:]))
	if info.ErrorID == 0 {
		t.SettlementInfoReady.Store(true)
		processRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success: true,
			}
		})
	} else {
		processRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success:  false,
				ErrorID:  int(info.ErrorID),
				ErrorMsg: Bytes2String(infoField.ErrorMsg[:]),
			}
		})
	}
	return 0
}

//export tRspUserLogin
func tRspUserLogin(field *C.struct_CThostFtdcRspUserLoginField, info *C.struct_CThostFtdcRspInfoField, i C.int, b C._Bool) C.int {
	loginField := (*define.CThostFtdcRspUserLoginField)(unsafe.Pointer(field))
	infoField := (*define.CThostFtdcRspInfoField)(unsafe.Pointer(info))
	zlog.Debugf("[goctp trade] rsp user login: %d - %s", int(infoField.ErrorID), Bytes2String(infoField.ErrorMsg[:]))
	if infoField.ErrorID == 0 {
		t.FrontID = int(loginField.FrontID)
		t.SessionID = int(loginField.SessionID)
		t.TradingDay = Bytes2String(loginField.TradingDay[:])
		now := time.Now()
		t.controller.TradeLoginTime = &now
		loginF := &RspUserLoginField{
			TradingDay:  t.TradingDay,
			LoginTime:   Bytes2String(loginField.LoginTime[:]),
			BrokerID:    t.BrokerID,
			UserID:      t.InvestorID,
			FrontID:     int(loginField.FrontID),
			SessionID:   t.SessionID,
			MaxOrderRef: string(loginField.MaxOrderRef[:]),
			SHFETime:    string(loginField.SHFETime[:]),
			DCETime:     string(loginField.DCETime[:]),
			CZCETime:    string(loginField.CZCETime[:]),
			FFEXTime:    string(loginField.FFEXTime[:]),
			INETime:     string(loginField.INETime[:]),
		}
		t.SetExchangeTimes(loginF)
		if t.onRspUserLogin != nil {
			go func(field *RspUserLoginField) {
				t.IsLogin.Store(true)
				go t.onRspUserLogin(field, &RspInfoField{ErrorID: 0, ErrorMsg: Bytes2String(infoField.ErrorMsg[:])})
				processRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
					resultChan <- RequestResult{
						Success: true,
					}
				})
			}(loginF)
		}
	} else {
		go t.onRspUserLogin(&RspUserLoginField{}, &RspInfoField{ErrorID: int(infoField.ErrorID), ErrorMsg: Bytes2String(infoField.ErrorMsg[:])})
		processRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success:  false,
				ErrorID:  int(infoField.ErrorID),
				ErrorMsg: Bytes2String(infoField.ErrorMsg[:]),
			}
		})
	}
	return 0
}

//export tRspAuthenticate
func tRspAuthenticate(field *C.struct_CThostFtdcRspAuthenticateField, info *C.struct_CThostFtdcRspInfoField, i C.int, b C._Bool) C.int {
	authField := (*define.CThostFtdcRspAuthenticateField)(unsafe.Pointer(field))
	infoField := (*define.CThostFtdcRspInfoField)(unsafe.Pointer(info))
	errInfo := &RspInfoField{ErrorID: int(infoField.ErrorID), ErrorMsg: Bytes2String(infoField.ErrorMsg[:])}

	// 合规需要的打印
	zlog.Debugf("received ctp authenticate response: \n<OnRspAuthenticate>\n\tBrokerID: (%s)\n\tUserID: (%s)\n\tUserProductInfo: (%s)\n\tAppID: (%s)\n\tAppType: (%v)\n\tErrorMsg: (%s)\n\tErrorID: (%d)\n\tnRequestID: (%d)\n\tbIsLast: (%v)\n</OnRspAuthenticate>",
		string(authField.BrokerID[:]), string(authField.UserID[:]), string(authField.UserProductInfo[:]), string(authField.AppID[:]), authField.AppType, Bytes2String(infoField.ErrorMsg[:]), infoField.ErrorID, int(i), bool(b),
	)

	// 客户端认证成功后，开始真实的用户登录
	if info.ErrorID == 0 {
		zlog.Debugf("[goctp trade] rsp authenticate success")
		t.IsAuthenticated.Store(true)
		processRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success: true,
			}
		})
	} else {
		zlog.Debugf("[goctp trade] rsp authenticate failed: %d %s", errInfo.ErrorID, errInfo.ErrorMsg)
		processRequestResult(fmt.Sprintf("%d", i), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success:  false,
				ErrorID:  errInfo.ErrorID,
				ErrorMsg: errInfo.ErrorMsg,
			}
		})
		if t.onRspUserLogin != nil {
			// 客户端认证失败，也要回调用户登录失败
			go t.onRspUserLogin(&RspUserLoginField{}, errInfo)
		}
	}
	return 0
}

//export tFrontDisConnected
func tFrontDisConnected(reason C.int) C.int {
	zlog.Debugf("[goctp trade] front disconnected, reason: %d", int(reason))
	t.controller.TradeConnected.Store(false)
	t.controller.TradeConnectedTime = nil
	t.controller.Trade.IsLogin.Store(false)
	t.controller.Trade.IsAuthenticated.Store(false)
	t.controller.Trade.SettlementInfoReady.Store(false)
	t.controller.Trade.InstrumentsReady.Store(false)
	t.controller.Trade.AccountReady.Store(false)
	t.controller.Trade.PositionReady.Store(false)

	if t.onFrontDisConnected != nil {
		t.onFrontDisConnected(int(reason))
	}
	return 0
}

//export tFrontConnected
func tFrontConnected() C.int {
	zlog.Debugf("[goctp trade] front connected")
	if t.onFrontConnected != nil {
		go t.onFrontConnected()
	}
	return 0
}

/* Query */

type RequestType string

const RequestTypeQueryAccount RequestType = "QueryAccount"
const RequestTypeQueryPosition RequestType = "QueryPosition"
const RequestTypeQueryOrder RequestType = "QueryOrder"
const RequestTypeQueryInstrument RequestType = "QueryInstrument"
const RequestTypeQueryInstrumentMarginRate RequestType = "QueryInstrumentMarginRate"
const RequestTypeTradeAuthenticate RequestType = "Authenticate"
const RequestTypeTradeLogin RequestType = "Login"
const RequestTypeQuoteLogin RequestType = "QuoteLogin"
const RequestTypeQuoteSubscribe RequestType = "QuoteSubscribe"
const RequestTypeCreateOrder RequestType = "CreateOrder"
const RequestTypeCancelOrder RequestType = "CancelOrder"
const RequestTypeConfirmSettlementInfo RequestType = "ConfirmSettlementInfo"

type BaseRequest struct {
	Type    RequestType
	ID      string
	Timeout int // 获取结果的超时时间
}

func (this BaseRequest) GetType() RequestType {
	return this.Type
}

func (this BaseRequest) GetID() string {
	return this.ID
}

func (this BaseRequest) GetResultKeys() []string {
	return []string{}
}

func (this BaseRequest) GetTimeout() int {
	if this.Timeout == 0 {
		return t.controller.Option.DefaultTimeout
	}
	return this.Timeout
}

type IRequest interface {
	GetType() RequestType
	GetID() string
	Do(reqID int) (reqResult int)
	GetResultKeys() []string
	GetTimeout() int
}

type AuthenticateRequest struct {
	BaseRequest
	InvestorID string
	Password   string
	BrokerID   string
	AppID      string
	AuthCode   string
}

func (this AuthenticateRequest) Do(reqID int) (reqResult int) {
	zlog.Debugf("[goctp trade] do authenticate request (%d)", reqID)
	t.IsLogin.Store(false)
	t.InvestorID = this.InvestorID
	t.password = this.Password
	t.BrokerID = this.BrokerID
	f := define.CThostFtdcReqAuthenticateField{}
	copy(f.BrokerID[:], this.BrokerID)
	copy(f.UserID[:], this.InvestorID)
	copy(f.AppID[:], []byte(this.AppID))
	copy(f.AuthCode[:], []byte(this.AuthCode))
	copy(f.UserProductInfo[:], []byte(ProductInfo))
	// 合规需要的打印
	zlog.Debugf("send ctp authenticate request: \n<ReqAuthenticate>\n\tBrokerID: (%s)\n\tUserID: (%s)\n\tUserProductInfo: (%s)\n\tAppID: (%s)\n\tnRequestID: (%d)\n</ReqAuthenticate>", this.BrokerID, this.InvestorID, ProductInfo, this.AppID, int(reqID))
	ret := C.ReqAuthenticate(t.api, (*C.struct_CThostFtdcReqAuthenticateField)(unsafe.Pointer(&f)), C.int(reqID))
	reqResult = int(ret)
	return
}

type LoginRequest struct {
	BaseRequest
}

func (this LoginRequest) Do(reqID int) (reqResult int) {
	f := define.CThostFtdcReqUserLoginField{}
	copy(f.UserID[:], t.InvestorID)
	copy(f.BrokerID[:], t.BrokerID)
	copy(f.Password[:], t.password)
	copy(f.UserProductInfo[:], ProductInfo)
	ret := C.ReqUserLogin(t.api, (*C.struct_CThostFtdcReqUserLoginField)(unsafe.Pointer(&f)), C.int(t.getReqID()))
	reqResult = int(ret)
	return
}

type QueryAccountRequest struct {
	BaseRequest
}

const ReqResultRateLimit = -999

func (this QueryAccountRequest) Do(reqID int) (reqResult int) {
	qryAccount := define.CThostFtdcQryTradingAccountField{}
	copy(qryAccount.InvestorID[:], t.InvestorID)
	copy(qryAccount.BrokerID[:], t.BrokerID)
	reqResult = int(C.ReqQryTradingAccount(t.api, (*C.struct_CThostFtdcQryTradingAccountField)(unsafe.Pointer(&qryAccount)), C.int(reqID)))
	return
}

type QueryOrderRequest struct {
	BaseRequest
	ExchangeID   string
	InstrumentID string
	OrderSysID   string
}

func (this QueryOrderRequest) GetResultKeys() []string {
	return []string{fmt.Sprintf("QueryOrder:%s", this.OrderSysID)}
}

func (this QueryOrderRequest) Do(reqID int) (reqResult int) {
	qryOrder := define.CThostFtdcQryOrderField{}
	copy(qryOrder.InvestorID[:], t.InvestorID)
	copy(qryOrder.BrokerID[:], t.BrokerID)
	copy(qryOrder.OrderSysID[:], this.OrderSysID)
	if this.ExchangeID != "" {
		copy(qryOrder.ExchangeID[:], this.ExchangeID)
	}
	if this.InstrumentID != "" {
		copy(qryOrder.InstrumentID[:], this.InstrumentID)
	}
	reqResult = int(C.ReqQryOrder(t.api, (*C.struct_CThostFtdcQryOrderField)(unsafe.Pointer(&qryOrder)), C.int(reqID)))
	zlog.Debugf("[goctp trade] do query order request: %s - %s, req id: %d, result: %d", this.InstrumentID, this.OrderSysID, reqID, reqResult)
	return
}

type QueryInstrumentRequest struct {
	BaseRequest
}

func (this QueryInstrumentRequest) Do(reqID int) (reqResult int) {
	if strings.Compare(t.Version, "v6.5.1") > 0 {
		req := define.CThostFtdcQryClassifiedInstrumentField{
			TradingType: define.THOST_FTDC_TD_TRADE,
			ClassType:   define.THOST_FTDC_INS_FUTURE, // 仅查询合约的话，用 define.THOST_FTDC_INS_FUTURE，查询全部 define.THOST_FTDC_INS_ALL
		}
		zlog.Debugf("[goctp trade] req qry classified instrument")
		reqResult = int(C.ReqQryClassifiedInstrument(t.api, (*C.struct_CThostFtdcQryClassifiedInstrumentField)(unsafe.Pointer(&req)), C.int(reqID)))
	}
	return
}

type QueryInstrumentMarginRateRequest struct {
	BaseRequest
	ExchangeID   string
	InstrumentID string
}

func (this QueryInstrumentMarginRateRequest) Do(reqID int) (reqResult int) {
	f := define.CThostFtdcQryInstrumentMarginRateField{
		HedgeFlag: define.THOST_FTDC_HF_Speculation,
	}
	copy(f.InvestorID[:], t.InvestorID)
	copy(f.BrokerID[:], t.BrokerID)
	if this.ExchangeID != "" {
		copy(f.ExchangeID[:], this.ExchangeID)
	}
	if this.InstrumentID != "" {
		copy(f.InstrumentID[:], this.InstrumentID)
	}
	zlog.Debugf("[goctp trade] req qry instrument margin rate")
	reqResult = int(C.ReqQryInstrumentMarginRate(t.api, (*C.struct_CThostFtdcQryInstrumentMarginRateField)(unsafe.Pointer(&f)), C.int(reqID)))
	return
}

func (t *Trade) QueryInstrumentMarginRate(instrumentID string) (er error) {
	if !t.InstrumentsReady.Load() {
		er = fmt.Errorf("instrument not ready")
		return
	}
	req := QueryInstrumentMarginRateRequest{
		BaseRequest: BaseRequest{
			Type:    RequestTypeQueryInstrumentMarginRate,
			ID:      NewRandomID(),
			Timeout: t.controller.Option.QueryTimeout,
		},
		ExchangeID:   GetExchangeID(instrumentID),
		InstrumentID: instrumentID,
	}
	er = t.SendRequest(req, nil)
	return
}

type ConfirmSettlementInfoRequest struct {
	BaseRequest
}

func (this ConfirmSettlementInfoRequest) Do(reqID int) (reqResult int) {
	f := define.CThostFtdcSettlementInfoConfirmField{}
	copy(f.InvestorID[:], t.InvestorID)
	copy(f.AccountID[:], t.InvestorID)
	copy(f.BrokerID[:], t.BrokerID)
	reqResult = int(C.ReqSettlementInfoConfirm(t.api, (*C.struct_CThostFtdcSettlementInfoConfirmField)(unsafe.Pointer(&f)), C.int(reqID)))
	return
}

type QueryPositionsRequest struct {
	BaseRequest
	ExchangeID   string
	InstrumentID string
}

func (this QueryPositionsRequest) Do(reqID int) (reqResult int) {
	t.PosiDetail = xsync.NewMapOf[[]*define.CThostFtdcInvestorPositionField]() // 清空原始持仓数据
	qryPosition := define.CThostFtdcQryInvestorPositionField{}
	copy(qryPosition.InvestorID[:], t.InvestorID)
	copy(qryPosition.BrokerID[:], t.BrokerID)
	if this.ExchangeID != "" {
		copy(qryPosition.ExchangeID[:], this.ExchangeID)
	}
	if this.InstrumentID != "" {
		copy(qryPosition.InstrumentID[:], this.InstrumentID)
	}
	reqResult = int(C.ReqQryInvestorPosition(t.api, (*C.struct_CThostFtdcQryInvestorPositionField)(unsafe.Pointer(&qryPosition)), C.int(reqID)))
	return
}

type CreateOrderRequest struct {
	BaseRequest
	InstrumentID     string
	BuySell          DirectionType
	OpenClose        OffsetFlagType
	Price            float64
	Volume           int
	StopPrice        float64
	TriggerDirection TriggerDirection
	RequestCallback  func(orderID string, err error)
	TimeCondition    TimeConditionType
}

func (this CreateOrderRequest) Do(reqID int) (reqResult int) {
	zlog.Debugf("[goctp trade] reqest order insert: %v %v %v %v %v %v %v", this.InstrumentID, this.BuySell, this.OpenClose, this.Price, this.Volume, this.StopPrice, this.TriggerDirection)

	exchangeID := ""
	f := define.CThostFtdcInputOrderField{}
	copy(f.BrokerID[:], t.BrokerID)                            // 经纪公司代码
	if info, ok := t.Instruments.Load(this.InstrumentID); ok { // 根据合约代码找到交易所
		exchangeID = info.ExchangeID
		copy(f.ExchangeID[:], exchangeID)
	} else {
		processRequestResult(fmt.Sprintf("%d", reqID), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success:  false,
				ErrorID:  ApplicationError,
				ErrorMsg: "instrument not found",
			}
		})
		return 0
	}

	// 等待流控器允许继续执行
	// 因为是按交易所和品种限制流控，所以同时传入：交易所ID + 品种
	if err := t.rateLimitOrderInsert.Wait(exchangeID, this.InstrumentID); err != nil {
		zlog.Errorf("create order, rate limit wait failed, error: %s", err)
		return ReqResultRateLimit
	}

	copy(f.UserID[:], t.InvestorID) // 账号 ID
	copy(f.InvestorID[:], t.InvestorID)
	copy(f.AccountID[:], t.InvestorID)
	f.IsAutoSuspend = define.TThostFtdcBoolType(0)           // 自动挂起标志
	f.IsSwapOrder = define.TThostFtdcBoolType(0)             // 互换单标志
	f.ForceCloseReason = define.THOST_FTDC_FCC_NotForceClose // 非强平

	// 参数赋值
	orderRef := fmt.Sprintf("%012d", reqID)
	copy(f.OrderRef[:], orderRef)                              // 自定义报单引用，必须是递增的
	copy(f.InstrumentID[:], this.InstrumentID)                 // 合约代码
	f.Direction = define.TThostFtdcDirectionType(this.BuySell) // 买卖标识
	f.CombOffsetFlag[0] = byte(this.OpenClose)                 // 开平仓标识
	f.CombHedgeFlag[0] = byte(HedgeFlagSpeculation)            // 投机标识
	// 不同类型的Order
	f.OrderPriceType = define.THOST_FTDC_OPT_LimitPrice // 限价单
	// 测试发现 TimeCondition 只有 THOST_FTDC_TC_GFD 当日有效 时才不会被撤销，其他值如果未完成都会被撤销
	// 目前只有 THOST_FTDC_TC_GFD 和 THOST_FTDC_TC_IOC 这两种类型有用
	f.TimeCondition = define.TThostFtdcTimeConditionType(this.TimeCondition) // 当日有效，报单会挂在交易所直到成交或收盘自动撤销
	f.VolumeCondition = define.THOST_FTDC_VC_AV                              // 任何数量
	f.LimitPrice = define.TThostFtdcPriceType(this.Price)                    // 订单价
	f.StopPrice = define.TThostFtdcPriceType(this.StopPrice)                 // 条件单价格
	// 止损价触发方式
	if this.StopPrice > 0 && this.TriggerDirection == TriggerDirectionHigher {
		f.ContingentCondition = define.THOST_FTDC_CC_LastPriceGreaterEqualStopPrice
	} else if this.StopPrice > 0 && this.TriggerDirection == TriggerDirectionLower {
		f.ContingentCondition = define.THOST_FTDC_CC_LastPriceLesserEqualStopPrice
	} else {
		f.ContingentCondition = define.THOST_FTDC_CC_Immediately
	}
	f.VolumeTotalOriginal = define.TThostFtdcVolumeType(this.Volume) // //数量
	reqResult = int(C.ReqOrderInsert(t.api, (*C.struct_CThostFtdcInputOrderField)(unsafe.Pointer(&f)), C.int(reqID)))
	if this.RequestCallback != nil {
		this.RequestCallback(fmt.Sprintf("%d_%d_%s", t.FrontID, t.SessionID, orderRef), toReqError(reqResult))
		this.RequestCallback = nil
	}

	// zlog.Debugf("[goctp trade] reqest order insert sent: %v %v %v %v %v %v %v", this.InstrumentID, this.BuySell, this.OpenClose, this.Price, this.Volume, this.StopPrice, this.TriggerDirection)
	return
}

type CancelOrderRequest struct {
	BaseRequest
	OrderID    string
	OrderSysID string
}

func (this CancelOrderRequest) GetResultKeys() []string {
	return []string{fmt.Sprintf("CancelOrder:%s", this.OrderID), fmt.Sprintf("CancelOrder:%s", this.OrderSysID)}
}

func (this CancelOrderRequest) Do(reqID int) (reqResult int) {
	zlog.Debugf("[goctp trade] do cancel order: %s", this.OrderID)
	if order, ok := t.Orders.Load(this.OrderID); ok {
		// 等待流控器允许继续执行
		// 因为是按交易所和品种限制流控，所以同时传入：交易所ID + 品种
		if err := t.rateLimitOrderAction.Wait(order.ExchangeID, order.InstrumentID); err != nil {
			zlog.Debugf("[goctp trade] do cancel order, wait for rate limit error: %s", err)
			return ReqResultRateLimit
		}

		f := define.CThostFtdcInputOrderActionField{}
		copy(f.BrokerID[:], t.BrokerID)
		copy(f.UserID[:], t.InvestorID)
		copy(f.InvestorID[:], t.InvestorID)
		copy(f.InstrumentID[:], order.InstrumentID)
		copy(f.ExchangeID[:], order.ExchangeID)
		// TODO: 删除按 OrderSysID 撤单的代码，触发后订单的撤单还不能工作
		// 试了各种方法根据 OrderSysID 都没法撤单，不仅仅是已触发的订单，普通订单也无法撤单
		// 参考了 vnpy 和 starquant 的代码，他们都没有按 OrderSysID 撤单的功能
		if false && this.OrderSysID != "" {
			copy(f.OrderSysID[:], this.OrderSysID)
		} else {
			copy(f.OrderRef[:], order.OrderRef)
			f.FrontID = define.TThostFtdcFrontIDType(order.FrontID)
			f.SessionID = define.TThostFtdcSessionIDType(order.SessionID)
		}
		f.ActionFlag = define.THOST_FTDC_AF_Delete
		reqResult = int(C.ReqOrderAction(t.api, (*C.struct_CThostFtdcInputOrderActionField)(unsafe.Pointer(&f)), C.int(reqID)))
		return
	} else {
		processRequestResult(fmt.Sprintf("%d", reqID), func(resultChan chan RequestResult) {
			resultChan <- RequestResult{
				Success:  false,
				ErrorMsg: "order not found",
			}
		})
		return 0
	}
}

type RequestResult struct {
	Success   bool
	ErrorMsg  string
	ErrorID   int
	Positions []PositionField
	Account   AccountField
	Order     OrderField
}

func (this RequestResult) Error() error {
	if this.Success {
		return nil
	}
	if this.ErrorID == 0 {
		return fmt.Errorf("request result error: %s", this.ErrorMsg)
	}
	return fmt.Errorf("request result error: [%d]%s", this.ErrorID, this.ErrorMsg)
}

func (t *Trade) SendRequest(req IRequest, successCallback func(reqResult RequestResult)) (er error) {
	// 请求完成后立即删除 resultChan
	// resultChan 读取一次后，不会再读取，如果有其他回调拿到 resultChan 往里面写 result，会导致卡死
	defer t.requestResultChans.Delete(req.GetID())

	// 重要：resultChan 长度可能需要长一些
	// 如果这个 resultChan 的长度设得太短，因为创建订单会多次读取 resultChan，会导致卡住
	// 上面的 defer 已经处理过，resultChan 的长度暂时还是设为 1
	resultChan := make(chan RequestResult, 5)
	t.requestResultChans.Store(req.GetID(), resultChan)
	t.requestQueue <- req
	// zlog.Debugf("[goctp trade] send request %s - %s", req.GetType(), req.GetID())

	select {
	case result := <-resultChan:
		if result.Success {
			if successCallback != nil {
				successCallback(result)
			}
		} else {
			er = result.Error()
			zlog.Errorf("[goctp trade] request error, (%s-%s), error: %s", req.GetType(), req.GetID(), er)
		}
	case <-time.After(time.Duration(req.GetTimeout()) * time.Second):
		er = fmt.Errorf("timed out")
		zlog.Errorf("[goctp trade] request timeout: (%s-%s)", req.GetType(), req.GetID())
	}
	return
}

func (t *Trade) QueryAccount() (account *AccountField, er error) {
	if !t.IsLogin.Load() {
		er = fmt.Errorf("trade not logged in")
		return
	}
	req := QueryAccountRequest{
		BaseRequest: BaseRequest{
			Type:    RequestTypeQueryAccount,
			ID:      NewRandomID(),
			Timeout: t.controller.Option.QueryTimeout,
		},
	}
	er = t.SendRequest(req, func(result RequestResult) {
		account = &result.Account
	})
	return
}

func (t *Trade) ConfirmSettlementInfo() (er error) {
	req := ConfirmSettlementInfoRequest{
		BaseRequest: BaseRequest{
			Type:    RequestTypeConfirmSettlementInfo,
			ID:      NewRandomID(),
			Timeout: t.controller.Option.DefaultTimeout,
		},
	}
	er = t.SendRequest(req, nil)
	return
}

func (t *Trade) QueryInstrument() (er error) {
	req := QueryInstrumentRequest{
		BaseRequest: BaseRequest{
			Type:    RequestTypeQueryInstrument,
			ID:      NewRandomID(),
			Timeout: t.controller.Option.QueryInstrumentTimeout,
		},
	}
	er = t.SendRequest(req, nil)
	return
}

func (t *Trade) QueryPositions(exchangeID string, instrumentID string) (positions []PositionField, er error) {
	req := QueryPositionsRequest{
		BaseRequest: BaseRequest{
			Type:    RequestTypeQueryPosition,
			ID:      NewRandomID(),
			Timeout: t.controller.Option.QueryTimeout,
		},
		ExchangeID:   exchangeID,
		InstrumentID: instrumentID,
	}
	er = t.SendRequest(req, func(result RequestResult) {
		positions = result.Positions
		// 等待 60 秒后，清理临时 position 查询结果
		time.AfterFunc(60*time.Second, func() {
			t.queryPosiDetails.Delete(req.ID)
			// zlog.Debugf("cleanup query position details for request: %s", req.ID)
		})
	})
	return
}

func (t *Trade) QueryOrder(instrumentID, orderSysID string) (order *OrderField, er error) {
	req := QueryOrderRequest{
		BaseRequest: BaseRequest{
			Type:    RequestTypeQueryOrder,
			ID:      NewRandomID(),
			Timeout: t.controller.Option.QueryTimeout,
		},
		ExchangeID:   GetExchangeID(instrumentID),
		InstrumentID: instrumentID,
		OrderSysID:   orderSysID,
	}
	er = t.SendRequest(req, func(result RequestResult) {
		order = &result.Order
	})
	return
}

func (t *Trade) _Authenticate(investorID, password, brokerID, appID, authCode string, successCallback func(result RequestResult)) (er error) {
	req := AuthenticateRequest{
		BaseRequest: BaseRequest{
			Type:    RequestTypeTradeAuthenticate,
			ID:      NewRandomID(),
			Timeout: t.controller.Option.DefaultTimeout,
		},
		InvestorID: investorID,
		Password:   password,
		BrokerID:   brokerID,
		AppID:      appID,
		AuthCode:   authCode,
	}
	err := t.SendRequest(req, successCallback)
	if err != nil {
		er = fmt.Errorf("step 1 authenticated failed, error: %s", err)
	}
	return
}

func (t *Trade) _Login(successCallback func(result RequestResult)) (er error) {
	req := LoginRequest{
		BaseRequest: BaseRequest{
			Type:    RequestTypeTradeLogin,
			ID:      NewRandomID(),
			Timeout: t.controller.Option.DefaultTimeout,
		},
	}
	err := t.SendRequest(req, successCallback)
	if err != nil {
		er = fmt.Errorf("step 2 login failed, error: %s", err)
	}
	if !t.SettlementInfoReady.Load() {
		err := t.ConfirmSettlementInfo()
		if err != nil {
			zlog.Errorf("query settlement info failed after login")
		}
	}
	return
}

// reqCallback 当请求发出之后立即调用，不会等最终的结果
// 通常在 reqCallback 中记录订单号 ctpOrderID，否则等请求完全返回之后 onRtnOrder 可能已经被调用过好几次了，应用层失去了处理 ctpOrderID 的机会
func (t *Trade) CreateOrder(instrumentID string, buySell DirectionType, openClose OffsetFlagType, timeCondition TimeConditionType, price float64, volume int, stopPrice float64, triggerDirection TriggerDirection, reqCallback func(orderID string, err error)) (order *OrderField, er error) {
	id := NewRandomID()
	req := CreateOrderRequest{
		BaseRequest: BaseRequest{
			Type:    RequestTypeCreateOrder,
			ID:      id,
			Timeout: t.controller.Option.CreateOrderTimeout,
		},
		InstrumentID:     instrumentID,
		BuySell:          buySell,
		OpenClose:        openClose,
		Price:            price,
		Volume:           volume,
		StopPrice:        stopPrice,
		TriggerDirection: triggerDirection,
		TimeCondition:    timeCondition,
		// 这里需要在成功回调时设置，因为在此之前都没办法知道 ctpOrderID 是多少
		// onRtnOrder 时不会带着原始的 reqID，没办法使用 reqID 来处理结果
		// 没办法用 GetRequestKey 来解决
		// 只能在 reqCallback 把处理结果的 resultKey 设置上
		RequestCallback: func(ctpOrderID string, err error) {
			if err == nil {
				resultKey := fmt.Sprintf("CreateOrder:%s", ctpOrderID)
				t.requestIDMap.Store(resultKey, id)
			}
			reqCallback(ctpOrderID, err)
		},
	}
	er = t.SendRequest(req, func(result RequestResult) {
		order = &result.Order
	})
	return
}

func (t *Trade) CreateMarketOrder(instrumentID string, buySell DirectionType, openClose OffsetFlagType, volume int, reqCallback func(orderID string, err error)) (order *OrderField, er error) {
	id := NewRandomID()
	if !t.controller.QuoteConnected.Load() {
		return nil, fmt.Errorf("quote not connected, no upper/lower limit price")
	}
	tick, err := t.controller.GetTick(instrumentID)
	if err != nil {
		return nil, err
	}
	if tick.TradingDay != t.controller.GetTradingDay() {
		return nil, fmt.Errorf("tick is not current trading day")
	}
	price := tick.UpperLimitPrice
	if buySell == DirectionSell {
		price = tick.LowerLimitPrice
	}
	req := CreateOrderRequest{
		BaseRequest: BaseRequest{
			Type:    RequestTypeCreateOrder,
			ID:      id,
			Timeout: t.controller.Option.CreateOrderTimeout,
		},
		InstrumentID:     instrumentID,
		BuySell:          buySell,
		OpenClose:        openClose,
		Price:            price,
		Volume:           volume,
		StopPrice:        0,
		TriggerDirection: UnknownTriggerDirection,
		TimeCondition:    TimeConditionIOC,
		// 这里需要在成功回调时设置，因为在此之前都没办法知道 ctpOrderID 是多少
		// onRtnOrder 时不会带着原始的 reqID，没办法使用 reqID 来处理结果
		// 没办法用 GetRequestKey 来解决
		// 只能在 reqCallback 把处理结果的 resultKey 设置上
		RequestCallback: func(ctpOrderID string, err error) {
			if err == nil {
				resultKey := fmt.Sprintf("CreateOrder:%s", ctpOrderID)
				t.requestIDMap.Store(resultKey, id)
			}
			reqCallback(ctpOrderID, err)
		},
	}
	er = t.SendRequest(req, func(result RequestResult) {
		order = &result.Order
	})
	return
}

var cancelOrderLocks map[string]*sync.Mutex = map[string]*sync.Mutex{}

// CancelOrder 撤单，用 OrderID 或者 OrderSysID 撤单
func (t *Trade) CancelOrder(orderID string, orderSysID string) (order *OrderField, er error) {
	// 按 orderID 锁
	lock := &sync.Mutex{}
	if l, found := cancelOrderLocks[orderID]; found {
		lock = l
	} else {
		cancelOrderLocks[orderID] = lock
	}
	lock.Lock()
	defer lock.Unlock()

	req := CancelOrderRequest{
		BaseRequest: BaseRequest{
			Type:    RequestTypeCancelOrder,
			ID:      NewRandomID(),
			Timeout: t.controller.Option.CancelOrderTimeout,
		},
		OrderID:    orderID,
		OrderSysID: orderSysID,
	}
	er = t.SendRequest(req, func(result RequestResult) {
		order = &result.Order
	})
	return
}

func (t *Trade) Login(investorID, password, brokerID, appID, authCode string) (er error) {
	er = t._Authenticate(investorID, password, brokerID, appID, authCode, func(result RequestResult) {
		er = t._Login(nil)
	})
	if er != nil {
		zlog.Errorf("login failed, error: %s, isAthenticated: %v, isLogin: %v", er, t.IsAuthenticated, t.IsLogin)
	}
	return
}

all:
	g++ -shared -fPIC -Wl,-rpath=. -o ./ctp/libctp_quote.so ./ctp/cpp/quote.cpp  ./thostmduserapi_se.so
	g++ -shared -fPIC -Wl,-rpath=. -o ./ctp/libctp_trade.so ./ctp/cpp/trade.cpp  ./thosttraderapi_se.so
	
	cp ./ctp/libctp_quote.so ctp/tests/
	cp ./ctp/libctp_quote.so ctp/tests/
	cp ./thostmduserapi_se.so ctp/tests/
	cp ./thosttraderapi_se.so ctp/tests/

	cp ./ctp/libctp_quote.so cmds/gateway/
	cp ./ctp/libctp_trade.so cmds/gateway/
	cp ./thostmduserapi_se.so cmds/gateway/
	cp ./thosttraderapi_se.so cmds/gateway/

	sh ./build.sh
zig:
	zig c++ -target x86_64-linux-gnu -shared -fPIC -Wl,-rpath=. -o ./ctp/libctp_quote.so ./ctp/cpp/quote.cpp  ./thostmduserapi_se.so
	zig c++ -target x86_64-linux-gnu -shared -fPIC -Wl,-rpath=. -o ./ctp/libctp_trade.so ./ctp/cpp/trade.cpp  ./thosttraderapi_se.so
	
	cp ./ctp/libctp_quote.so ctp/tests/
	cp ./ctp/libctp_quote.so ctp/tests/
	cp ./thostmduserapi_se.so ctp/tests/
	cp ./thosttraderapi_se.so ctp/tests/

	cp ./ctp/libctp_quote.so cmds/gateway/
	cp ./ctp/libctp_trade.so cmds/gateway/
	cp ./thostmduserapi_se.so cmds/gateway/
	cp ./thosttraderapi_se.so cmds/gateway/

	sh ./build_zig.sh

# goctp

## 介绍

goctp 封装了 cgo 版本的 ctp 库，并且在上层做了流控功能。在 gateway 模块中，提供 exchange.gateway 需要的 http 接口。

## 编译

### linux amd64 环境下编译

ctp 库仅支持 amd64 架构，不能直接支持 aarch64 架构。不过，orbstack / parallels（通过 rosetta 模拟）都可以支持 amd64 的模拟环境。

复制所有 so 文件到系统 lib 下, 或把当前路径加入 LD_LIBRARY_PATH 中。

运行 `make` 即可。

### mac 下交叉编译 linux amd64 版本

M 系列的 mac 下 g++/go 的工具链是无法直接编译 linux amd64 的版本。 但是可以用 zig 的编译器替代 CC 和 CXX 成功编译。

运行：`make zig`，必须提前安装 zig 。

要注意的几个问题：

1. `#cgo LDFLAGS: -fPIC -L./ -Wl,-rpath=./ -lctp_trade -lstdc++` 
quote.go 中的 cgo 指令，-L 和 -Wl,-rpath 这两个参数比较敏感，原值为 `${SRCDIR}`，在 zig 中可能会将当前目录的绝对路径编译进去。因此，改为了 `./`

2. `CC="zig cc -target x86_64-linux-gnu -L."` 

不要设置为 `x86_64-linux` ，而要设置为 `x86_64-linux-gnu` ，设置为前者会使用 muls 库编译，在 linux 中会出现 `rosetta error: failed to open elf at /lib/ld-musl-x86_64.so.1` 的错误。也许和我用的 parallels amd64 模拟环境有关，但是具体不清楚。

另外 zig 的 `-L.` 这个参数非常重要，如果没有这个值，即使上面的 cgo 指令中使用了相对路径，还是会将绝对路径写入最终的二进制文件。进而导致 libctp_trade.so/libctp_quote.so 找不到的问题（无法通过 LD_LIBRARY_PATH 解决）。

具体的表现为：

```
readelf -d /media/psf/goctp/main_linux

Dynamic section at offset 0x22ff3b0 contains 28 entries:
  Tag        Type                         Name/Value
 0x000000000000001d (RUNPATH)            Library runpath: [./]
 0x0000000000000001 (NEEDED)             Shared library: [/Users/<USER>/workspace/quant/goctp/ctp/libctp_quote.so]
 0x0000000000000001 (NEEDED)             Shared library: [/Users/<USER>/workspace/quant/goctp/ctp/libctp_trade.so]
 0x0000000000000001 (NEEDED)             Shared library: [libpthread.so.0]
 0x0000000000000001 (NEEDED)             Shared library: [libc.so.6]
 0x0000000000000001 (NEEDED)             Shared library: [libresolv.so.2]
```

通过 elf 信息可以看到 `/Users/<USER>/workspace/quant/goctp/ctp/libctp_quote.so` 是 NEEDED 的，必须在 `/Users/<USER>/workspace/quant/goctp/ctp/` 放置 libctp_quote.so 才可以， 设置 LD_LIBRARY_PATH 无效。

如果在 `zig cc` 命令中加上 `-L.` ，elf 信息如下

```
readelf -d cmds/gateway/main_linux

Dynamic section at offset 0x22ff370 contains 28 entries:
  Tag        Type                         Name/Value
 0x000000000000001d (RUNPATH)            Library runpath: [./]
 0x0000000000000001 (NEEDED)             Shared library: [./libctp_quote.so]
 0x0000000000000001 (NEEDED)             Shared library: [./libctp_trade.so]
 ```

 这样，就可以在当前路径下找到 ctp_quote 和 ctp_trade 这两个库了。

3. zig 的 libctp_xxx 文件大小会大很多，可能与 zig 将一些 libc 相关的库静态打包进去有关。

现在代码库中提交的 libctp_xxx 都是 zig 交叉编译的产物。

### 有关 -L 和 -rpath 参数

`-L` 参数是在编译的 link 阶段使用的路径；`-rpath` 参数是在运行时搜索的目录



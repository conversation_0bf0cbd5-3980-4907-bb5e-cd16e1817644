package gateway

import (
	"fmt"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/wizhodl/goctp/ctp"
)

func (this *GatewayServer) getCTPPositionsHandler(ctx *gin.Context) {
	ctpPositions, err := this.ctp.GetPositions()
	if err != nil {
		failJSON(ctx, "ctp position data not available, error: %s", err)
	}

	instrumentID := ctx.Query("instrument_id")
	format := ctx.Query("format")
	positions := []*ctp.PositionField{}
	ctpPositions.Range(func(k string, pf *ctp.PositionField) bool {
		if pf.Position == 0 {
			return true
		}
		if instrumentID == "" {
			positions = append(positions, pf)
		}
		longKey := fmt.Sprintf("%s_long", instrumentID)
		shortKey := fmt.Sprintf("%s_short", instrumentID)
		if strings.EqualFold(k, longKey) || strings.EqualFold(k, shortKey) {
			positions = append(positions, pf)
		}
		return true
	})
	if strings.Contains("json", format) {
		okJSON(ctx, positions)
	} else if format == "text" {
		for _, position := range positions {
			ctx.Writer.Write([]byte(spewToTable(position, true)))
		}
		okString(ctx, "")
	}
}

// 方便查看本地 ctp orders
func (this *GatewayServer) getCTPOrdersHandler(ctx *gin.Context) {
	ctpOrders, err := this.ctp.GetOrders()
	if err != nil {
		failJSON(ctx, "get ctp orders failed, error: %s", err)
		return
	}

	format := ctx.Query("format")
	orderIDs := []string{}
	if ctx.Query("order_ids") != "" {
		orderIDs = strings.Split(ctx.Query("order_ids"), ",")
	}
	orders := []*ctp.OrderField{}
	ctpOrders.Range(func(k string, of *ctp.OrderField) bool {
		if len(orderIDs) > 0 {
			if SliceContainsEqualFold(orderIDs, of.GetOrderID()) {
				orders = append(orders, of)
			}
		} else {
			orders = append(orders, of)
		}
		return true
	})
	sort.Slice(orders, func(i, j int) bool {
		return orders[i].InsertTime > orders[j].InsertTime
	})
	if strings.Contains("json", format) {
		okJSON(ctx, orders)
	} else if format == "text" {
		for _, order := range orders {
			ctx.Writer.Write([]byte(spewToTable(order, true)))
		}
		okString(ctx, "")
	}
}

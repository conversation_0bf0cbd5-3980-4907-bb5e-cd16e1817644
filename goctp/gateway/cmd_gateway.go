package gateway

import (
	"fmt"
	"os"
	"sort"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/wizhodl/goctp/ctp"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type GatewayCommand struct {
	command.Command
	controller *GatewayServer
}

type LaunchGatewayCommand GatewayCommand

func NewLaunchGridCommand(controller *GatewayServer) *LaunchGatewayCommand {
	cmd := &LaunchGatewayCommand{
		Command: command.Command{
			Name:            "launch",
			Instruction:     "`.launch Password GoogleAuthCode safe[可选]` 启动程序",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          3,
			AuthcodePos:     -1, // launch 命令不能在解密主密码前校验 authcode，所以把 authCode 当成是普通参数处理，跳过统一的校验流程
		},
		controller: controller,
	}
	return cmd
}

func (this *LaunchGatewayCommand) Do() bool {
	password := this.Args[0]
	authCode := this.Args[1]
	if err := this.controller.launch(password, authCode); err != nil {
		this.ErrorMsgf("启动失败，error: %s", err)
		return false
	}
	this.SendMsgf("启动成功。")
	return true
}

type ExitGatewayCommand GatewayCommand

func NewExitGatewayCommand(controller *GatewayServer) *ExitGatewayCommand {
	cmd := &ExitGatewayCommand{
		Command: command.Command{
			Name:            "restart",
			Instruction:     "`.restart GoogleAuthCode` 退出程序，重新启动",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ExitGatewayCommand) Do() bool {
	this.SendMsgf("准备退出程序...")
	os.Exit(0)
	return true
}

type SubscribeGatewayCommand GatewayCommand

func NewSubscribeGatewayCommand(controller *GatewayServer) *SubscribeGatewayCommand {
	cmd := &SubscribeGatewayCommand{
		Command: command.Command{
			Name:            "subscribe",
			Alias:           []string{"sub"},
			Instruction:     "`.subscribe key` 订阅推送",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1, // launch 命令不能在解密主密码前校验 authcode，所以把 authCode 当成是普通参数处理，跳过统一的校验流程
		},
		controller: controller,
	}
	return cmd
}

func (this *SubscribeGatewayCommand) Do() bool {
	if this.controller.slackConnAddr == "" {
		this.ErrorMsgf("系统错误，Slack 连接没有和 websocket 关联。")
		return false
	}
	// 直接修改 slackChannel 对应的订阅列表
	// 因为命令处理函数无法模拟 slack 适配器的 websocket 连接发送 subscribe 命令
	key := this.Args[0]
	this.controller.AddSubscribeKey(this.controller.slackConnAddr, key)
	if !strings.HasSuffix(key, "slack") {
		instrumentID := strings.Split(key, ".")[0]
		if err := this.controller.EnableTrading(instrumentID); err != nil {
			zlog.Errorf("error enable trading for key: %s", key)
		}
	}
	this.SendMsgf("订阅成功。")
	return true
}

type UnsubscribeGatewayCommand GatewayCommand

func NewUnsubscribeGatewayCommand(controller *GatewayServer) *UnsubscribeGatewayCommand {
	cmd := &UnsubscribeGatewayCommand{
		Command: command.Command{
			Name:            "unsubscribe",
			Alias:           []string{"unsub"},
			Instruction:     "`.unsubscribe key` 取消订阅推送",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1, // launch 命令不能在解密主密码前校验 authcode，所以把 authCode 当成是普通参数处理，跳过统一的校验流程
		},
		controller: controller,
	}
	return cmd
}

func (this *UnsubscribeGatewayCommand) Do() bool {
	if this.controller.slackConnAddr == "" {
		this.ErrorMsgf("系统错误，Slack 连接没有和 websocket 关联。")
		return false
	}
	// 直接修改 slackChannel 对应的订阅列表
	// 因为命令处理函数无法模拟 slack 适配器的 websocket 连接发送 unsubscribe 命令
	this.controller.RemoveSubscribeKey(this.controller.slackConnAddr, this.Args[0])
	this.SendMsgf("取消订阅成功。")
	return true
}

type OrderGatewayCommand GatewayCommand

func NewOrderGatewayCommand(controller *GatewayServer) *OrderGatewayCommand {
	cmd := &OrderGatewayCommand{
		Command: command.Command{
			Name:            "orders",
			Alias:           []string{"o"},
			Instruction:     "`.orders IDs/all summary[可选]` 查看本地订单",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *OrderGatewayCommand) Do() bool {
	ids := this.Args[0]

	ordersToPrint := []*Order{}
	for _, orders := range this.controller.storage.Orders {
		for _, order := range orders {
			if ids != "all" {
				if strings.Contains(ids, order.OrderID) {
					ordersToPrint = append(ordersToPrint, order)
				}
			} else {
				ordersToPrint = append(ordersToPrint, order)
			}
		}
	}

	if len(this.Args) == 2 && strings.EqualFold(this.Args[1], "summary") {
		this.SendMsgf("```%s```", this.controller.GetOrdersSummary(ordersToPrint))
		return true
	}

	for _, order := range ordersToPrint {
		this.SendFileMessage(fmt.Sprintf("本地订单 [%s]", order.OrderID), spewToTable(order, false), "")
	}
	if len(ordersToPrint) == 0 {
		this.SendMsgf("本地无订单")
	}
	return true
}

type PositionsGatewayCommand GatewayCommand

func NewPositionsGatewayCommand(controller *GatewayServer) *PositionsGatewayCommand {
	cmd := &PositionsGatewayCommand{
		Command: command.Command{
			Name:            "positions",
			Alias:           []string{"p"},
			Instruction:     "`.positions` 查看持仓",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *PositionsGatewayCommand) Do() bool {
	ctpPositions, err := this.controller.ctp.GetPositions()
	if err != nil {
		this.ErrorMsgf("获取 CTP 持仓失败，error: %s", err)
		return false
	}

	noPosition := true
	ctpPositions.Range(func(positionID string, positionField *ctp.PositionField) bool {
		if position, err := this.controller.convertPosition(positionField); err != nil {
			this.ErrorMsgf("转换 CTP 持仓出现错误，error: %s", err)
		} else {
			if position.Qty != 0 {
				this.SendMsgf("持仓 [%s]\n```%s```", positionID, toTable(position, false))
				noPosition = false
			}
		}
		return true
	})
	if noPosition {
		this.SendMsgf("当前无持仓")
	}
	return true
}

type GetAccountGatewayCommand GatewayCommand

func NewGetAccountGatewayCommand(controller *GatewayServer) *GetAccountGatewayCommand {
	cmd := &GetAccountGatewayCommand{
		Command: command.Command{
			Name:            "account",
			Alias:           []string{"a"},
			Instruction:     "`.account` 查看账户",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *GetAccountGatewayCommand) Do() bool {
	account, err := this.controller.ctp.GetAccount()
	if err != nil {
		this.ErrorMsgf("获取 CTP 账户数据失败，error: %s", err)
		return false
	}

	userMargin := convertUserMargin(account)
	this.SendMsgf("账户 [%s]\n```%s```", this.controller.opts.Ctp.InvestorID, spewToTable(userMargin, false))
	return true
}

type GetInstrumentsGatewayCommand GatewayCommand

func NewGetInstrumentsGatewayCommand(controller *GatewayServer) *GetInstrumentsGatewayCommand {
	cmd := &GetInstrumentsGatewayCommand{
		Command: command.Command{
			Name:            "instruments",
			Alias:           []string{"i"},
			Instruction:     "`.instruments InstrumentIDs` 查看品种",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *GetInstrumentsGatewayCommand) Do() bool {
	instrumentIDs := strings.Split(this.Args[0], ",")
	if !this.controller.ctp.IsInstrumentsReady() {
		this.SendMsgf("品种数据没有就绪。")
		return false
	}
	count := 0
	this.controller.Instruments.Range(func(instrumentID string, instrument *Instrument) bool {
		if SliceContains(instrumentIDs, instrumentID) {
			count += 1
			this.SendMsgf("品种 [%s]\n```%s```", instrumentID, spewToTable(instrument, false))
		}
		return true
	})
	if count == 0 {
		this.SendMsgf("没有找到品种 %s 的数据。", instrumentIDs)
		return true
	}
	return true
}

type ConfigGatewayCommand GatewayCommand

func NewConfigGatewayCommand(controller *GatewayServer) *ConfigGatewayCommand {
	cmd := &ConfigGatewayCommand{
		Command: command.Command{
			Name:            "config",
			Alias:           []string{"cfg"},
			Instruction:     "`.config` 查看配置",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ConfigGatewayCommand) Do() bool {
	t := exchange.NewTable()
	t.SetHeader([]string{"Config", "Value"})

	checkSign := fmt.Sprintf("*%v*", this.controller.opts.CheckSign)
	if this.controller.opts.CheckSign {
		checkSign = fmt.Sprintf("*[%v]*", this.controller.opts.CheckSign)
	}
	t.AddRow([]string{"CheckSign", checkSign})
	// t.AddRow([]string{"SignKey", fmt.Sprintf("%s", this.controller.opts.SignKey)})
	t.AddRow([]string{"CTP AppID", this.controller.opts.Ctp.AppID})
	t.AddRow([]string{"CTP InvestorID", this.controller.opts.Ctp.InvestorID})
	t.AddRow([]string{"KLineProductIDs", SliceStringJoin(this.controller.storage.KLineProductIDs, ",", false)})
	t.AddRow([]string{"KLineSymbols", SliceStringJoin(this.controller.storage.KLineSymbols, ",", false)})
	t.AddRow([]string{"TradingSymbols", SliceStringJoin(this.controller.storage.TradingSymbols, ",", false)})

	this.SendMsgf("配置\n```%s```", t.Render())
	return true
}

type StatusGatewayCommand GatewayCommand

func NewStatusGatewayCommand(controller *GatewayServer) *StatusGatewayCommand {
	cmd := &StatusGatewayCommand{
		Command: command.Command{
			Name:            "status",
			Alias:           []string{"s"},
			Instruction:     "`.status` 查看运行状态",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *StatusGatewayCommand) Do() bool {
	t := exchange.NewTable()
	t.AddRow([]string{"LaunchTime", utils.FormatShortTimeStr(this.controller.ctp.LaunchTime, false)})
	t.AddRow([]string{"LastTickTime", utils.FormatShortTimeStr(this.controller.ctp.LastTickTime, false)})
	t.AddRow([]string{"Gateway InstrumentsReady", fmt.Sprintf("%v", this.controller.InstrumentsReady.Load())})
	t.AddRow([]string{"", ""})
	t.AddRow([]string{"Quote Connected", fmt.Sprintf("%v", this.controller.ctp.QuoteConnected.Load())})
	t.AddRow([]string{"Quote ConnectedTime", utils.FormatShortTimeStr(this.controller.ctp.QuoteConnectedTime, false)})
	t.AddRow([]string{"Quote LoginTime", utils.FormatShortTimeStr(this.controller.ctp.QuoteLoginTime, false)})
	t.AddRow([]string{"Quote IsLogin", fmt.Sprintf("%v", this.controller.ctp.IsQuoteLogin())})
	t.AddRow([]string{"", ""})
	t.AddRow([]string{"Trade Connected", fmt.Sprintf("%v", this.controller.ctp.TradeConnected.Load())})
	t.AddRow([]string{"Trade ConnectedTime", utils.FormatShortTimeStr(this.controller.ctp.TradeConnectedTime, false)})
	t.AddRow([]string{"Trade LoginTime", utils.FormatShortTimeStr(this.controller.ctp.TradeLoginTime, false)})
	isAuthenticated := false
	if this.controller.ctp.Trade != nil && this.controller.ctp.Trade.IsAuthenticated.Load() {
		isAuthenticated = true
	}
	t.AddRow([]string{"Trade IsAuthenticated", fmt.Sprintf("%v", isAuthenticated)})
	t.AddRow([]string{"Trade IsLogin", fmt.Sprintf("%v", this.controller.ctp.IsTradeLogin())})
	if this.controller.ctp.IsTradeLogin() {
		t.AddRow([]string{"", ""})
		t.AddRow([]string{"InstrumentsReady", fmt.Sprintf("%v", this.controller.ctp.Trade.InstrumentsReady.Load())})
		t.AddRow([]string{"PositionReady", fmt.Sprintf("%v", this.controller.ctp.Trade.PositionReady.Load())})
		t.AddRow([]string{"AccountReady", fmt.Sprintf("%v", this.controller.ctp.Trade.AccountReady.Load())})
		t.AddRow([]string{"SettlementInfoReady", fmt.Sprintf("%v", this.controller.ctp.Trade.SettlementInfoReady.Load())})
	}

	this.SendMsgf("Build: %s\n```%s```", this.BuildInfo(), t.Render())
	return true
}

type AddProductIDGatewayCommand GatewayCommand

func NewAddProductIDGatewayCommand(controller *GatewayServer) *AddProductIDGatewayCommand {
	cmd := &AddProductIDGatewayCommand{
		Command: command.Command{
			Name:            "addProducts",
			Instruction:     "`.addProducts ProductIDs` 增加存储 KLine 的产品ID",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *AddProductIDGatewayCommand) Prepare() bool {
	productIDs := strings.Split(this.Args[0], ",")
	for _, productID := range productIDs {
		if !ctp.CheckValidProductID(productID) {
			this.ErrorMsgf("找不到产品ID (%s)", productID)
			return false
		} else {
			t := exchange.NewTable()
			t.AddRow([]string{fmt.Sprintf("警告：即将保存 (%s) 的 KLine", productID)})
			this.SendMsgf("```%s```", t.Render())
		}
	}
	return true
}

func (this *AddProductIDGatewayCommand) Do() bool {
	productIDs := strings.Split(this.Args[0], ",")
	for _, productID := range productIDs {
		this.controller.GetKLineSymbols(productID, func(symbols []string) {
			this.controller.subscribe(symbols)
			this.controller.storage.AddProductID(productID)
			this.SendMsgf("设置 (%s) 成功。", productID)
		})
	}
	return true
}

type RemoveProductIDGatewayCommand GatewayCommand

func NewRemoveProductIDGatewayCommand(controller *GatewayServer) *RemoveProductIDGatewayCommand {
	cmd := &RemoveProductIDGatewayCommand{
		Command: command.Command{
			Name:            "removeProducts",
			Instruction:     "`.removeProducts ProductIDs` 删除存储 KLine 的产品ID",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *RemoveProductIDGatewayCommand) Prepare() bool {
	productIDs := strings.Split(this.Args[0], ",")
	for _, productID := range productIDs {
		if !ctp.CheckValidProductID(productID) {
			this.ErrorMsgf("找不到产品ID (%s)", productID)
			return false
		} else {
			t := exchange.NewTable()
			t.AddRow([]string{fmt.Sprintf("警告：不再保存 (%s) 的 KLine", productID)})
			this.SendMsgf("```%s```", t.Render())
		}
	}
	return true
}

func (this *RemoveProductIDGatewayCommand) Do() bool {
	productIDs := strings.Split(this.Args[0], ",")

	for _, productID := range productIDs {
		this.controller.storage.RemoveProductID(productID)
		// TODO: unsubscribe symbols
	}
	this.SendMsgf("设置 %s 成功。", productIDs)
	return true
}

type PrintProductIDGatewayCommand GatewayCommand

func NewPrintProductIDGatewayCommand(controller *GatewayServer) *PrintProductIDGatewayCommand {
	cmd := &PrintProductIDGatewayCommand{
		Command: command.Command{
			Name:            "printProducts",
			Instruction:     "`.printProducts` 打印产品列表",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *PrintProductIDGatewayCommand) Do() bool {
	count, result := this.controller.getProductIDExchangeMap()
	this.SendMsgf("产品ID和交易所对应表, (%d) 个品种：\n\n```%s```", count, result)
	return true
}

func (this *GatewayServer) getProductIDExchangeMap() (count int, result string) {
	ctpInstruments, err := this.ctp.GetInstruments()
	if err != nil {
		result = "[no instruments data]"
		return
	}
	count = 0
	productIDMap := map[string]string{}

	ctpInstruments.Range(func(key string, instrument *ctp.InstrumentField) bool {
		if instrument.ProductClass == ctp.ProductClassFutures {
			productIDMap[instrument.ProductID] = strings.ToUpper(instrument.ExchangeID)
		}
		count += 1
		return true
	})

	keys := []string{}
	for key := range productIDMap {
		keys = append(keys, key)
	}

	sort.SliceStable(keys, func(i int, j int) bool { return keys[i] < keys[j] })

	for _, k := range keys {
		result += fmt.Sprintf("\n\"%s\": \"%s\",", k, productIDMap[k])
	}
	return
}

type OpenOrdersGatewayCommand GatewayCommand

func NewOpenOrdersGatewayCommand(controller *GatewayServer) *OpenOrdersGatewayCommand {
	cmd := &OpenOrdersGatewayCommand{
		Command: command.Command{
			Name:            "openOrders",
			Instruction:     "`.openOrders InstrumentID summary[可选]` 打印 Open 的订单",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *OpenOrdersGatewayCommand) Do() bool {
	instrumentID := this.Args[0]
	orders := []*Order{}
	path := fmt.Sprintf("/v1/orders/open?instrument_id=%s&order_type=StopLimit", instrumentID)
	if err := this.controller.sendHTTPRequest(resty.MethodGet, path, nil, &orders); err != nil {
		this.ErrorMsgf("请求获取 Open 的订单出错，error:%s", err)
		return false
	} else {
		if len(this.Args) == 2 && strings.EqualFold(this.Args[1], "summary") {
			this.SendMsgf("```%s```", this.controller.GetOrdersSummary(orders))
			return true
		}

		for _, order := range orders {
			this.SendFileMessage(fmt.Sprintf("本地订单 [%s]", order.OrderID), spewToTable(order, false), "")
		}
		if len(orders) == 0 {
			this.SendMsgf("本地无挂单")
		}
	}
	return true
}

type EnableTradingGatewayCommand GatewayCommand

func NewEnableTradingGatewayCommand(controller *GatewayServer) *EnableTradingGatewayCommand {
	cmd := &EnableTradingGatewayCommand{
		Command: command.Command{
			Name:            "enableTrading",
			Instruction:     "`.enableTrading symbol` 把某个品种加入 TradingSymbols ",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *EnableTradingGatewayCommand) Do() bool {
	symbol := this.Args[0]
	if _, err := this.controller.GetInstrument(symbol); err != nil {
		this.ErrorMsgf("Symbol 查询错误: %s", err)
		return false
	}
	err := this.controller.EnableTrading(symbol)
	if err != nil {
		this.ErrorMsgf("操作出错，error: %s", err)
		return false
	}
	this.SendMsgf("操作成功。")
	return true
}

type DisableTradingGatewayCommand GatewayCommand

func NewDisableTradingGatewayCommand(controller *GatewayServer) *DisableTradingGatewayCommand {
	cmd := &DisableTradingGatewayCommand{
		Command: command.Command{
			Name:            "disableTrading",
			Instruction:     "`.disableTrading symbol` 把某个品种移出 TradingSymbols ",
			RequiresConfirm: true,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *DisableTradingGatewayCommand) Do() bool {
	this.controller.DisbleTrading(this.Args[0])
	this.SendMsgf("操作成功。")
	return true
}

type KlineUpdateGatewayCommand GatewayCommand

func NewKlineUpdateGatewayCommand(controller *GatewayServer) *KlineUpdateGatewayCommand {
	cmd := &KlineUpdateGatewayCommand{
		Command: command.Command{
			Name:            "klineUpdate",
			Alias:           []string{"ku"},
			Instruction:     "`.klineUpdate` 更新 Kline",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *KlineUpdateGatewayCommand) Do() bool {
	this.controller.updateKlinesWithTianqin()
	this.SendMsgf("操作成功。")
	return true
}

type KlineStatusGatewayCommand GatewayCommand

func NewKlineStatusGatewayCommand(controller *GatewayServer) *KlineStatusGatewayCommand {
	cmd := &KlineStatusGatewayCommand{
		Command: command.Command{
			Name:            "klineStatus",
			Alias:           []string{"ks"},
			Instruction:     "`.klineStatus symbol[可选]` 打印某个品种的 Kline 状态",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *KlineStatusGatewayCommand) Do() bool {
	symbol := ""
	if len(this.Args) == 1 {
		symbol = this.Args[0]
	}

	st := exchange.NewTable()
	st.SetHeader([]string{"Symbol", "Last UpdateTime"})

	this.controller.klineUpdateTimes.Range(func(sym string, updateTime time.Time) bool {
		if symbol == "" {
			st.AddRow([]string{sym, utils.FormatShortTimeStr(&updateTime, false)})
		} else if sym == symbol {
			st.AddRow([]string{sym, utils.FormatShortTimeStr(&updateTime, false)})
		}
		return true
	})
	klineUpdateTimeStr := "[ 没有Kline更新时间 ]"

	if len(st.Rows) > 1 {
		klineUpdateTimeStr = st.Render()
	}
	this.SendMsgf("KLine 更新时间:\n```%s```", klineUpdateTimeStr)

	if symbol != "" {
		exchangeName := ctp.GetExchangeID(symbol)
		respKlines := exchange.KLines{}
		url := fmt.Sprintf("/v1/klines/lookup?exchange=%s&instrument_id=%s&period=1day&limit=200", exchangeName, symbol)
		if err := this.controller.sendHTTPRequest(resty.MethodGet, url, nil, &respKlines); err != nil {
			this.ErrorMsgf("获取 K 线失败，error: %s", err)
			return false
		}

		klineStr := "[ 空K线 ]"
		if kt := respKlines.ToTable(0); kt != "" {
			klineStr = kt
		}
		this.SendMsgf("KLines:\n```%s```", klineStr)
	}

	return true
}

func (this *GatewayServer) getCleanedKlines(ctpExchange string, symbol string, limit int) (klines exchange.KLines, err error) {
	respKlines := exchange.KLines{}
	path := fmt.Sprintf("/v1/klines/lookup?exchange=%s&instrument_id=%s&period=1day&limit=%v", ctpExchange, symbol, limit)
	zlog.Debugf("request ctp gateway kline: %s", path)
	if err := this.sendHTTPRequest(resty.MethodGet, path, nil, &respKlines); err != nil {
		return nil, err
	}
	for _, k := range respKlines {
		// 都相同说明是休盘数据，过滤
		if exchange.AlmostEqual(k.Open, k.Close) &&
			exchange.AlmostEqual(k.Open, k.High) &&
			exchange.AlmostEqual(k.Open, k.Low) {
			continue
		}
		klines = append(klines, k)
	}
	sort.SliceStable(klines, func(i, j int) bool {
		return klines[i].Time < klines[j].Time
	})
	return
}

func (this *GatewayServer) GetOrdersSummary(orders []*Order) string {
	summary := "[no order summary]"
	t := exchange.NewTable()
	t.SetHeader([]string{"OrderID", "Symbol", "Type", "Side", "Qty", "Price", "TriggerPrice", "Status", "ExecQty", "ExecPrice", "CreateTime", "UpdateTime", "SubOrders"})
	sort.SliceStable(orders, func(i int, j int) bool {
		return orders[i].CreateTime.Before(*orders[j].CreateTime)
	})

	for _, order := range orders {
		row := []string{}
		row = append(row, order.OrderID)
		row = append(row, order.Symbol)
		row = append(row, string(order.Type))
		if order.ReduceOnly {
			row = append(row, fmt.Sprintf("%s / Reduce", order.Side))
		} else {
			row = append(row, string(order.Side))
		}
		row = append(row, fmt.Sprintf("%.f", order.Qty))
		row = append(row, fmt.Sprintf("%.2f", order.Price))
		row = append(row, fmt.Sprintf("%.2f", order.TriggerPrice))
		row = append(row, string(order.Status))
		row = append(row, fmt.Sprintf("%.f", order.ExecQty))
		row = append(row, fmt.Sprintf("%.f", order.ExecPrice))
		row = append(row, FormatShortTimeStr(order.CreateTime, false))
		row = append(row, FormatShortTimeStr(order.UpdateTime, false))
		row = append(row, fmt.Sprintf("%d", len(order.SubOrders)))
		t.AddRow(row)
	}
	if len(t.Rows) > 1 {
		summary = t.Render()
	}
	return summary
}

package gateway

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/exchange"
)

/* KLine 请求表单 */
type LookupKLineForm struct {
	Exchange     string `form:"exchange" binding:"required"`
	InstrumentID string `form:"instrument_id" binding:"required"`
	Period       string `form:"period" binding:"required"`
	Time         int64  `form:"time"`
	Limit        int64  `form:"limit"`
}

func (form *LookupKLineForm) Default() {
	if form.Limit == 0 {
		form.Limit = 500
	}
	form.Exchange = strings.ToUpper(form.Exchange)
	form.InstrumentID = strings.ToUpper(form.InstrumentID)
}

/* Gateway 请求表单 */

type GetInstrumentsForm struct {
	InstrumentID string `form:"instrument_id"`
}

type GetOpenOrdersForm struct {
	InstrumentID string             `form:"instrument_id" binding:"required"`
	OrderType    exchange.OrderType `form:"order_type" binding:"required"`
}

type GetOrdersForm struct {
	OrderIDs  string             `form:"order_ids" binding:"required"`
	OrderType exchange.OrderType `form:"order_type" binding:"required"`
}

type GetLastPriceForm struct {
	InstrumentID string `form:"instrument_id" binding:"required"`
}

type GetPositionsForm struct {
	InstrumentID string `form:"instrument_id" binding:"required"`
	NoCache      bool   `form:"no_cache"`
}

type CancelOrderForm struct {
	OrderID string `form:"order_id" binding:"required"`
}

type CancelAllOrdersForm struct {
	OrderType    exchange.OrderType `form:"order_type" binding:"required"`
	InstrumentID exchange.OrderType `form:"instrument_id" binding:"required"`
}

// 回复数据结构

type LastPriceResponse struct {
	Price      float64   `json:"price"`
	UpdateTime time.Time `json:"update_time"`
}

/* websocket 数据结构 */

type PacketHeader struct {
	APIKey string `json:"api_key,omitempty"`
	ID     int64  `json:"id,omitempty"`
	Event  string `json:"event,omitempty"`
	Key    string `json:"key,omitempty"`
	Error  string `json:"error,omitempty"`
	Time   int64  `json:"time,omitempty"`
	Sign   string `json:"sign,omitempty"`
}

// 必须用这个结构包装一下，保证 conn 不会并发写，否则会导致奇怪的 crash
type WebsocketWithLock struct {
	gateway *GatewayServer
	Conn    *websocket.Conn
	Mutex   sync.Mutex
}

func (this *WebsocketWithLock) WriteJSON(packet *exchange.Packet) error {
	this.Mutex.Lock()
	defer this.Mutex.Unlock()

	if this.Conn != nil {
		// 如果当前连接是 slack 连接，而发送的 packet 不是 SlackResponse，强制转换为 SlackResponse
		// 主要是满足向 slack 发送实时推送
		remoteAddr := this.RemoteAddr()
		if this.gateway.slackConnAddr == remoteAddr && !packet.CheckIsProtocol() {
			if origPacketJSON, err := json.MarshalIndent(packet, "", "    "); err != nil {
				return fmt.Errorf("convert packet to json as slack response failed (%s)", &packet.PacketHeader)
			} else {
				if len(origPacketJSON) > command.SLACK_MESSAGE_LIMIT {
					packet = exchange.NewSlackFileResponsePacket(packet.APIKey, this.gateway.opts.SlackChannel, string(packet.Event), string(origPacketJSON), "", "")
				}
				packet = exchange.NewSlackResponsePacket(packet.APIKey, this.gateway.opts.SlackChannel, fmt.Sprintf("```%s```", origPacketJSON))
			}
		}
		signKey := this.gateway.opts.getAPISecret(packet.APIKey)
		if packet.Sign == "" && signKey != "" {
			packet.SignWithKey(signKey)
		}
		return this.Conn.WriteJSON(packet)
	} else {
		return errors.New("websocket conn is not ready")
	}
}

func (this *WebsocketWithLock) IsKeySubscribed(key string) bool {
	remoteAddr := this.RemoteAddr()
	keys := this.gateway.connSubscribedKeys[remoteAddr]
	if key == "" {
		return false
	}
	for _, k := range keys {
		if strings.EqualFold(k, "all") || strings.HasPrefix(key, k) || strings.HasSuffix(key, k) {
			return true
		}
	}
	return false
}

func (this *WebsocketWithLock) ReadJSON(packet *exchange.ClientPacket) error {
	return this.Conn.ReadJSON(packet)
}

func (this *WebsocketWithLock) RemoteAddr() string {
	return this.Conn.RemoteAddr().String()
}

func (this *WebsocketWithLock) Close() {
	this.Conn.Close()
}

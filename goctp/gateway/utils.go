package gateway

import (
	"encoding/json"
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

type comparable interface {
	~string | ~int64 | ~int | ~float64 | ~byte
}

// 将在 1～12 月之外的数字折叠为合法的 1～12
// month 因为是通过简单的加/减算术运算，可能有 <1 和 >12  的情况出现
func foldMonth(year, month int) (yearOut, monthOut int) {
	if month > 12 {
		return year + 1, month % 12
	} else if month <= 0 {
		return year - 1, month + 12
	}
	return year, month
}

func min[T comparable](a, b T) T {
	if a <= b {
		return a
	}
	return b
}

func SliceContainsEqualFold[T ~string](list []T, target T) bool {
	for _, item := range list {
		return strings.EqualFold(fmt.Sprintf("%s", item), fmt.Sprintf("%s", target))
	}
	return false
}

func SliceStringJoin[T ~string](list []T, sep string, quote bool) string {
	newList := []string{}
	if quote {
		for _, item := range list {
			newList = append(newList, fmt.Sprintf(`"%s"`, item))
		}
	} else {
		for _, item := range list {
			newList = append(newList, fmt.Sprintf("%s", item))
		}
	}
	return strings.Join(newList, sep)
}

func SliceContains[T comparable](slice []T, target T) (exist bool) {
	exist = false
	for _, item := range slice {
		if item == target {
			exist = true
			break
		}
	}
	return
}

// 这个函数可以兼容 Enum 和 string 的比较，特殊情况下使用，不建议经常用
func EnumEqual[T ~string](first T, second T) (equal bool) {
	if fmt.Sprintf("%s", first) == fmt.Sprintf("%s", second) {
		return true
	}
	return false
}

func SliceStringCountEmpty[T ~string](list []T) int {
	count := 0
	for _, s := range list {
		if s == "" {
			count += 1
		}
	}
	return count
}

func isFieldNotExported(fieldName string) bool {
	if len(fieldName) == 0 {
		return true
	}
	return fieldName[0:1] != strings.ToUpper(fieldName[0:1])
}

func toTable(v any, convertByte bool) string {
	t := exchange.NewTable()
	t.SetHeader([]string{"Field", "Value", "Type"})
	for _, row := range toTableRows(v, convertByte) {
		t.AddRow(row)
	}
	return fmt.Sprintf("%s\n", t.Render())
}

func toTableRows(v any, convertByte bool) [][]string {
	rows := [][]string{}
	s := reflect.ValueOf(v).Elem()
	typeOfT := s.Type()

	for i := 0; i < s.NumField(); i++ {
		f := s.Field(i)
		thisFieldName := typeOfT.Field(i).Name
		if isFieldNotExported(thisFieldName) {
			continue
		}
		fieldValue := f.Interface()
		fieldType := f.Type().String()
		valueStr := fmt.Sprintf("%v", fieldValue)
		// 尝试处理 []byte 打印的问题
		if isByteSlice(valueStr) {
			valueStr = byteSlice2String(valueStr)
		} else {
			// 处理 byte 打印的问题
			if convertByte && !strings.Contains("int/byte/float64/string/time.Time/*time.Time", fieldType) {
				valueStr = byte2String(valueStr)
			}
		}
		rows = append(rows, []string{thisFieldName, valueStr, fieldType})
	}
	return rows
}

func toPrettyJSON(v any) string {
	jsonStr, err := json.MarshalIndent(v, "", "    ")
	if err != nil {
		return fmt.Sprintf("[ERROR!> marshal failed, %v", v)
	}
	return string(jsonStr)
}

func spewToTable(v any, convertByte bool) string {
	s := spew.Sdump(v)
	return fmt.Sprintf("%s\n", spewOutputToTable(s, convertByte))
}

/*

从 spew dump 的字符串中提取 field, value, type 输出成表格

(*gateway.Order)(0xc0002842c0)({
 Order: (exchange.Order) {
  ExtStruct: (extstruct.ExtStruct) {
   ExtFloat64: (map[string]float64) <nil>,
   ExtInt: (map[string]int) <nil>,
   ExtBool: (map[string]bool) <nil>,
   ExtString: (map[string]string) <nil>,
   ExtTime: (map[string]*time.Time) <nil>,
   Tags: (uint) 0
  },
  InstrumentType: (exchange.InstrumentType) (len=18) "CNYMarginedFutures",
  OrderID: (string) (len=6) "rReisc",
  RefID: (string) "",
  Symbol: (string) (len=6) "sc2204",
  Price: (float64) 614.1,
  TriggerPrice: (float64) 0,
  TriggerDirection: (exchange.TriggerDirection) "",
  LastPrice: (float64) 0,
  Qty: (float64) 1,
  QuoteQty: (float64) 0,
  ExecPrice: (float64) 0,
  ExecQty: (float64) 0,
  Fee: (float64) 0,
  FeeAsset: (string) "",
  Type: (exchange.OrderType) (len=5) "Limit",
  Side: (exchange.OrderSide) (len=3) "Buy",
  Status: (exchange.OrderStatus) (len=3) "New",
  TimeInForce: (exchange.TimeInForce) (len=3) "GTC",
  ReduceOnly: (bool) false,
  CreateTime: (*time.Time)(0xc000404fd8)(2022-03-18 17:54:03.671052012 +0800 CST),
  UpdateTime: (*time.Time)(0xc000404ff0)(2022-03-18 17:54:03.671052012 +0800 CST),
  Trades: ([]exchange.TradeHistory) <nil>
 },
 SubOrders: ([]*gateway.SubOrder) (len=1 cap=4) {
  (*gateway.SubOrder)(0xc00011b280)({
   OrderID: (string) (len=25) "1_1451894244_000000000047",
   IsToday: (bool) false,
   ctpOrder: (*goctp.OrderField)(<nil>)
  })
 }
})
*/
func spewOutputToTable(s string, convertByte bool) string {
	t := exchange.NewTable()
	t.SetHeader([]string{"Field", "Value", "Type"})

	// struct 内部的结构会以大括号括起来，用 "}," 分割成不同的 struct 分别处理
	// 后续在不同的 struct 之间添加空行
	structParts := strings.Split(s, "},")

	lastHasRow := false
	for _, s := range structParts {
		if lastHasRow {
			t.AddRow([]string{"", "", ""})
		}
		// 去掉： Symbol: (string) (len=6) "sc2204",
		// 中的 (len=6)，防止后续匹配括号错误
		rRemoveLen, err := regexp.Compile(`\(len=\d+\)\s`)
		if err != nil {
			zlog.Errorf("remove len regex compile error")
			break
		}
		s = rRemoveLen.ReplaceAllString(s, "")

		// 去掉 CreateTime: (*time.Time)(0xc00000f1e8)(2022-03-18 17:54:03.671052012 +0800 CST),
		// 中的 (0xc00000f1e8)(
		// ")," 替换成 ","
		// 目的是去掉日期前后的括号，防止后续匹配括号错误
		rRemoveTime, err := regexp.Compile(`(\(0x\S+\)\S)`)
		if err != nil {
			zlog.Errorf("remove time regex compile error")
			break
		}
		s = rRemoveTime.ReplaceAllString(s, " ")
		s = strings.Replace(s, "),", ",", -1) // 不删除括号，后续的 regex 无法 match 到

		var fieldName string
		var fieldType string
		var fieldValue string
		if rRow, err := regexp.Compile(`(?m)^\s+(\w+):\s\((\S+)\)\s(.+),`); err == nil {
			matchs := rRow.FindAllString(s, -1)
			for _, match := range matchs {
				parts := strings.Split(match, ": (")
				fieldName = strings.Replace(parts[0], " ", "", -1)
				fieldName = strings.Replace(fieldName, "\n", "", -1)
				if len(parts) == 2 {
					secondParts := strings.Split(parts[1], ") ")
					fieldType = secondParts[0]
					if len(secondParts) == 2 {
						fieldValue = strings.Replace(secondParts[1], ",", "", -1)
					}

					// 尝试处理 []byte 打印的问题
					if isByteSlice(fieldValue) {
						fieldValue = byteSlice2String(fieldValue)
					} else {
						// 处理 byte 打印的问题
						if convertByte && !strings.Contains("int/byte/float64/string/time.Time/*time.Time", fieldType) {
							fieldValue = byte2String(fieldValue)
						}
					}
				}
				if fieldValue != "<nil>" {
					t.AddRow([]string{fieldName, fieldValue, fieldType})
					lastHasRow = true
				}
			}
		}
	}
	return t.Render()
}

func byte2String(byteStr string) string {
	result := ""
	if intValue, err := strconv.ParseUint(byteStr, 10, 8); err == nil {
		result = fmt.Sprintf("'%c'", int(intValue))
	}
	if result != "" {
		return result
	} else {
		return byteStr
	}
}

func isByteSlice(s string) bool {
	if rByteSlice, err := regexp.Compile(`^\[(?:\d+\s*)+\d+\]$`); err == nil {
		matchs := rByteSlice.FindAllString(s, -1)
		if len(matchs) == 1 {
			return true
		}
	}
	return false
}

func byteSlice2String(byteSliceStr string) string {
	result := ""
	numbersOnly := byteSliceStr[1 : len(byteSliceStr)-2]
	numbersList := strings.Split(numbersOnly, " ")

	for _, numb := range numbersList {
		if intValue, err := strconv.ParseUint(numb, 10, 8); err == nil {
			if intValue != 0 {
				result += fmt.Sprintf("%c", int(intValue))
			}
		}
	}
	result = fmt.Sprintf(`"%s"`, result)
	return result
}

func derefSlice[T *any](s []T) (result []any) {
	for _, item := range s {
		result = append(result, *item)
	}
	return
}

func FormatShortTimeStr(t *time.Time, withYear bool) string {
	if t == nil {
		return "-"
	}
	if t.Before(time.Unix(86400, 0)) {
		return "-"
	} else {
		if withYear {
			return t.In(time.FixedZone("CST", 8*60*60)).Format("06-01-02 15:04:05")
		}
		return t.In(time.FixedZone("CST", 8*60*60)).Format("01-02 15:04:05")
	}
}

func ParseFloatOrPercentage(numOrPercentage string, percentOnly bool, allowNegative bool) (result float64, isPercent bool, er error) {
	result = 0
	if strings.Contains(numOrPercentage, `%`) {
		if p, err := strconv.ParseFloat(numOrPercentage[:len(numOrPercentage)-1], 64); err == nil {
			result = p / 100.0
			if result > 1 {
				return 0, true, fmt.Errorf("%s percentage > 100%% error", numOrPercentage)
			}
			if !allowNegative {
				if result < 0 {
					return 0, true, fmt.Errorf("%s percentage < 0 error", numOrPercentage)
				}
			}
			if result < -1 {
				return 0, true, fmt.Errorf("%s percentage < -100%% error", numOrPercentage)
			}
		} else {
			return 0, true, fmt.Errorf("%s percentage format error", numOrPercentage)
		}
		return result, true, nil
	} else {
		if percentOnly {
			return 0, false, fmt.Errorf("%s need percent only", numOrPercentage)
		} else {
			if n, err := strconv.ParseFloat(numOrPercentage, 64); err == nil {
				result = n
			} else {
				return 0, false, fmt.Errorf("%s number format error", numOrPercentage)
			}
			if !allowNegative && result < 0 {
				return 0, false, fmt.Errorf("%s number < 0 error", numOrPercentage)
			}
		}
		return result, false, nil
	}
}

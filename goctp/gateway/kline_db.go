package gateway

import (
	"os"
	"time"

	log "github.com/sirupsen/logrus"
	bolt "go.etcd.io/bbolt"
)

/* K 线数据库 */

func (this *GatewayServer) openDB(path string, created bool) *bolt.DB {
	this.Lock()
	defer this.Unlock()

	val, ok := this.dbs.Load(path)
	if ok {
		return val
	}

	if _, err := os.Stat(path); err == nil {
		created = true
	}
	if !created {
		return nil
	}

	db, err := bolt.Open(path, 0600, &bolt.Options{Timeout: 5 * time.Second})
	if err != nil {
		log.WithError(err).Error("open bolt db error")
		return nil
	}
	this.dbs.Store(path, db)
	return db
}

func (this *GatewayServer) closeDB(path string) error {
	val, ok := this.dbs.Load(path)
	if !ok {
		this.dbs.Delete(path)
		log.WithField("path", path).Info("closing...")
		err := val.Close()
		log.WithField("path", path).WithError(err).Info("closing...")
		return err
	}
	return nil
}

func (this *GatewayServer) historyKLine(k, okl *KLine) (ok bool) {
	path := this.opts.PathByTime(k.KLineHeader, k.Time)
	db := this.openDB(path, true)
	if db != nil {
		db.View(func(tx *bolt.Tx) error {
			bkt := tx.Bucket(k.Bucket())
			if bkt == nil {
				return nil
			}
			val := bkt.Get(k.Key())
			if val == nil {
				return nil
			}
			okl.Unpack(k.Key(), val)
			return nil
		})
		return true
	}
	return false
}

package gateway

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/wizhodl/goctp/ctp"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

/* 品种相关的代码 */

const ExtKeyPriceUpdateSecond = "PriceUpdateSecond"

type Instrument = exchange.Instrument

func (this *GatewayServer) isInstrumentExpiredSoon(instrumentID string) bool {
	ctpInstruments, err := this.ctp.GetInstruments()
	if err != nil {
		return false
	}
	ins, found := ctpInstruments.Load(instrumentID)
	if !found {
		return false
	}
	dliveryDate := time.Date(ins.DeliveryYear, time.Month(ins.DeliveryMonth), 1, 0, 0, 0, 0, time.UTC).Add(-time.Hour * 8)
	now := time.Now()
	// zlog.Debugf("%s dliveryDate %s", instrumentID, dliveryDate)
	return !now.After(dliveryDate) && now.Add(time.Hour*24*time.Duration(this.opts.AutoCloseDays)).After(dliveryDate)
}

func (this *GatewayServer) isInstrumentDelivered(instrumentID string) bool {
	ctpInstruments, err := this.ctp.GetInstruments()
	if err != nil {
		return false
	}
	ins, found := ctpInstruments.Load(instrumentID)
	if !found {
		return true
	}
	return ins.IsDelivered()
}

func (this *GatewayServer) updateInstrumentStatus(instrument *Instrument, status *ctp.InstrumentStatus) {
	instrument.Status = convertInstrumentStatus(status.InstrumentStatus)
	if this.isInstrumentExpiredSoon(instrument.Symbol) && instrument.Status == exchange.InstrumentStatusContinuous {
		instrument.Status = exchange.InstrumentStatusExpiredSoon
	}
	instrument.UpdateTime = time.Now()

	// 如果收盘，重置 tickCounter
	if instrument.Status == exchange.InstrumentStatusClosed {
		this.tickCounter.Range(func(instrumentID string, v int64) bool {
			if instrumentID == instrument.Symbol {
				this.tickCounter.Store(instrumentID, int64(0))
			}
			return true
		})
		this.klineUpdateTimes.Delete(instrument.Symbol)
	}

	// 因为开销很高，使用 KLineSymbolsBuffer 缓存并统一请求 k 线
	if instrument.Status == exchange.InstrumentStatusContinuous && this.CheckKLineAllowed(instrument.Symbol) {
		this.instrumentChangeKlineSymbolsBuffer.Add(instrument.Symbol)
	}

	seconds := ctp.ParseTimeSecond(status.EnterTime)
	// 43200 -> 12:00:00, 64800 -> 18:00:00
	isAfternoon := seconds > 43200 && seconds < 64800
	// 日盘结束后为一个交易日结束，挂单会被系统撤销，夜盘不会
	if isAfternoon && instrument.Status == exchange.InstrumentStatusClosed {
		for instrumentID, orders := range this.storage.Orders {
			if instrumentID == instrument.Symbol {
				for _, order := range orders {
					if !order.IsOpen() {
						continue
					}
					// 只有本地挂单的也无需取消
					if len(order.GetSubOrderIDs()) == 0 {
						continue
					}

					// 条件单不取消，如有 ctp 子订单，说明已触发，标记子订单为取消
					// 触发未成交的条件单将由 quanter 吃单机制处理
					if order.Type == exchange.StopLimit {
						this.storage.setSubOrderCanceled(order)
						continue
					}

					this.storage.setOrderCanceled(order)
					zlog.Debugf("order canceled on instrument status closed %s:%s", instrumentID, order.OrderID)
				}
			}
		}
	}
}

func (this *GatewayServer) UpdateInstrumentWithTick(instrument *Instrument, tick *ctp.TickField) {
	if instrument == nil {
		return
	}
	if instrument.Status != exchange.InstrumentStatusContinuous {
		return
	}
	newInstrument := &Instrument{}
	copier.Copy(newInstrument, instrument)
	tickSecond := ctp.ParseTimeSecond(tick.UpdateTime)
	exchangeTime, err := this.ctp.CurrentExchangeTime(instrument.ExchangeID)
	if err != nil {
		return
	}
	exchangeSecond := ctp.TimeToSecond(exchangeTime)
	timeDelta := exchangeSecond - tickSecond
	if timeDelta > 2 {
		zlog.Warnf("tick time too old, exchange: %s, instrument: %s delta: %d seconds, tick second: %d, exchange second: %d, tick update time: %s, exchange time: %s", instrument.ExchangeID, instrument.Symbol, timeDelta, tickSecond, exchangeSecond, tick.UpdateTime, exchangeTime)
	}
	nowTime := time.Now()
	newInstrument.UpdateTime = nowTime
	newInstrument.LastPrice = tick.LastPrice
	newInstrument.LastPriceUpdateTime = nowTime // 仅在此更新价格的时间，由应用层负责检查 lastPrice 是否能用
	newInstrument.MarkPrice = tick.LastPrice
	newInstrument.UpperLimitPrice = tick.UpperLimitPrice
	newInstrument.LowerLimitPrice = tick.LowerLimitPrice
	newInstrument.Volume = float64(tick.Volume)
	newInstrument.OpenInterest = float64(tick.OpenInterest)
	newInstrument.SetInt(ExtKeyPriceUpdateSecond, tickSecond)
	this.Instruments.Store(tick.InstrumentID, newInstrument)
	// zlog.Debugf("update instrument with tick, tick second: %d, exchange second: %d", tickSecond, exchangeSecond)
}

func (this *GatewayServer) subscribe(instrumentIDs []string) {
	go func() {
		for !this.ctp.IsQuoteLogin() {
			time.Sleep(1 * time.Second)
		}
		for _, instrumentID := range instrumentIDs {
			zlog.Debugf("subscribe %s", instrumentID)
			err := this.ctp.Quote.Subscribe(instrumentID)
			if err != nil {
				zlog.Errorf("subscribe failed, (%s), error: %s", instrumentID, err)
			} else {
				zlog.Infof("subscribe success, (%s)", instrumentID)
			}
		}
	}()
}

// 缓存品种数据，blocking
func (this *GatewayServer) cacheInstruments() {
	for !this.Launched.Load() || !this.ctp.IsInstrumentsReady() {
		time.Sleep(1 * time.Second)
	}

	for {
		ctpInstruments, err := this.ctp.GetInstruments()
		if err != nil {
			zlog.Errorf("cache instruments failed, get ctp instruments failed, error: %s", err)
			time.Sleep(1 * time.Second)
			continue
		}
		ctpInstruments.Range(func(k string, field *ctp.InstrumentField) bool {
			instrument := convertInstrument(field)
			if instrument != nil {
				this.ctp.GetInstrumentStatuss().Range(func(k string, status *ctp.InstrumentStatus) bool {
					if strings.EqualFold(field.ProductID, status.InstrumentID) {
						this.updateInstrumentStatus(instrument, status)
					}
					return true
				})
				this.Instruments.LoadOrStore(instrument.Symbol, instrument)
			}
			return true
		})
		this.InstrumentsReady.Store(true)
		zlog.Debugf("cache instrument, instrument ready set to true")
		this.ctp.QueryInstrumentMarginRates()
		this.clearDeliveredSymbol()
		break
	}
}

func (this *GatewayServer) clearDeliveredSymbol() {
	tradingSymbols := this.storage.TradingSymbols
	for _, symbol := range tradingSymbols {
		if this.isInstrumentDelivered(symbol) {
			this.DisbleTrading(symbol)
		}
	}
}

func (this *GatewayServer) GetInstrument(instrumentID string) (result *Instrument, er error) {
	if !this.InstrumentsReady.Load() {
		return nil, errors.New("instruments not ready")
	}
	if instr, found := this.Instruments.Load(instrumentID); !found {
		er = fmt.Errorf("instrument not found (%s)", instrumentID)
	} else {
		result = instr
	}
	return
}

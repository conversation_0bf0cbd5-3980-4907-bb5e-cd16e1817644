package gateway

import (
	"sync"
	"time"

	"github.com/wizhodl/quanter/common/zlog"
)

// 可延迟的缓存，在 buffSeconds 秒以内加入的 value 会缓存起来，等到超过缓存时间后 flush
// 必须用 NewXXX 函数，才能正确的 flush
type DelayableBuffer struct {
	flushFunc      func([]string)
	values         []string
	updateTime     time.Time
	lock           sync.Mutex
	timeThreshold  int // 缓存时间阈值，单位秒；在这个时间阈值以内，都继续缓存，并修改更新时间
	countThreshold int // 缓存个数阈值，如果 buffer 中的 values 超过这个阈值，flush
	flushDelay     int // 延迟多少秒后调用 flushFunc
	flushTicker    *time.Ticker
	flushChan      chan struct{}
}

func NewDelayableBuffer(countThreshold, timeThreshold int, flushDelay int, flushFunc func([]string)) *DelayableBuffer {
	if flushFunc == nil {
		return nil
	}
	b := &DelayableBuffer{
		flushFunc:      flushFunc,
		values:         []string{},
		lock:           sync.Mutex{},
		countThreshold: countThreshold,
		timeThreshold:  timeThreshold,
		flushDelay:     flushDelay,
		updateTime:     time.Now(),
		flushTicker:    time.NewTicker(time.Duration(1 * time.Second)),
		flushChan:      make(chan struct{}, 100),
	}
	go b.FlushLoop()
	return b
}

func (this *DelayableBuffer) Add(value string) {
	this.lock.Lock()
	defer this.lock.Unlock()
	zlog.Debugf("add value to delayable buffer: %s", value)
	this.values = append(this.values, value)
	this.updateTime = time.Now()
	if this.countThreshold > 0 && len(this.values) >= this.countThreshold {
		this.flushChan <- struct{}{}
	}
}

func (this *DelayableBuffer) Flush() {
	this.lock.Lock()
	defer this.lock.Unlock()

	values := this.values[:]
	if len(values) > 0 {
		time.AfterFunc(time.Duration(this.flushDelay)*time.Second, func() {
			go this.flushFunc(values)
			zlog.Infof("flushed values success: %v", values)
		})
		this.values = []string{}
	}
}

func (this *DelayableBuffer) FlushLoop() {
	for {
		select {
		case <-this.flushChan:
			this.Flush()
		case <-this.flushTicker.C:
			if time.Since(this.updateTime) > time.Duration(this.timeThreshold)*time.Second {
				this.Flush()
			}
		}
	}
}

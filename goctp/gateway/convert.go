package gateway

import (
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/wizhodl/goctp/ctp"
	"github.com/wizhodl/quanter/exchange"
)

/* 从 CTP 结构转换成 exchange 的结构 */

func convertInstrument(f *ctp.InstrumentField) *Instrument {
	var instrumentType exchange.InstrumentType
	if f.ProductClass == ctp.ProductClassFutures {
		instrumentType = exchange.USDXMarginedFutures
	} else {
		return nil
	}

	underlying := f.UnderlyingInstrID
	if underlying == "" {
		underlying = f.ProductID
	}

	i := &Instrument{
		InstrumentType: instrumentType,
		ExchangeID:     f.ExchangeID,
		Symbol:         f.InstrumentID,
		TickSize:       f.PriceTick,
		LotSize:        float64(f.MinLimitOrderVolume),
		MinSize:        float64(f.MinLimitOrderVolume),
		// MarketMinSize: f.MinMarketOrderVolume,
		ContractSize:      float64(f.VolumeMultiple),
		MarkPrice:         0,
		LastPrice:         0,
		FundingRate:       0,
		FundingRatePeriod: "1d",
		FundingTime:       time.Time{},
		MaxLeverage:       0,
		SettleCurrency:    "CNY",
		UnderlyCurrency:   underlying,
		QuoteCurrency:     "CNY",
		ContractType:      exchange.ContractTypeMonth,
		UpdateTime:        time.Now(),
	}
	return i
}

func convertInstrumentStatus(s ctp.InstrumentStatusType) exchange.InstrumentStatus {
	switch s {
	case ctp.InstrumentStatusBeforeTrading:
		return exchange.InstrumentStatusBeforeTrading
	case ctp.InstrumentStatusNoTrading:
		return exchange.InstrumentStatusNoTrading
	case ctp.InstrumentStatusContinuous:
		return exchange.InstrumentStatusContinuous
	case ctp.InstrumentStatusAuctionOrdering:
		return exchange.InstrumentStatusAuctionOrdering
	case ctp.InstrumentStatusAuctionBalance:
		return exchange.InstrumentStatusAuctionBalance
	case ctp.InstrumentStatusAuctionMatch:
		return exchange.InstrumentStatusAuctionMatch
	case ctp.InstrumentStatusClosed:
		return exchange.InstrumentStatusClosed
	}
	return exchange.UnknownInstrumentStatus
}

func convertOrderStatus(s ctp.OrderStatusType, ss ctp.OrderSubmitStatusType) (result exchange.OrderStatus) {
	switch s {
	case ctp.OrderStatusAllTraded:
		result = exchange.OrderStatusFilled
	case ctp.OrderStatusCanceled:
		result = exchange.OrderStatusCancelled
	case ctp.OrderStatusNoTradeQueueing, ctp.OrderStatusNoTradeNotQueueing, ctp.OrderStatusNotTouched:
		result = exchange.OrderStatusNew
	case ctp.OrderStatusPartTradedNotQueueing, ctp.OrderStatusPartTradedQueueing:
		result = exchange.OrderStatusPartialFilled
	case ctp.OrderStatusTouched:
		result = exchange.OrderStatusTriggered
	case ctp.OrderStatusUnknown:
		result = exchange.UnknownOrderStatus
	default:
		result = exchange.UnknownOrderStatus
	}
	if result == exchange.UnknownOrderStatus {
		switch ss {
		case ctp.OrderSubmitStatusAccepted, ctp.OrderSubmitStatusModifySubmitted, ctp.OrderSubmitStatusInsertSubmitted:
			result = exchange.OrderStatusNew
		case ctp.OrderSubmitStatusCancelRejected, ctp.OrderSubmitStatusInsertRejected, ctp.OrderSubmitStatusModifyRejected:
			result = exchange.OrderStatusRejected
		case ctp.OrderSubmitStatusCancelSubmitted:
			result = exchange.OrderStatusCancelled
		default:
			result = exchange.UnknownOrderStatus
		}
	}
	return
}

func convertOrderType(of *ctp.OrderField) exchange.OrderType {
	orderType := exchange.Limit
	if of.StopPrice != 0 {
		orderType = exchange.StopLimit
	}
	return orderType
}

func isOrderToday(of *ctp.OrderField) bool {
	if strings.Contains("SHFE/INE", of.ExchangeID) && of.OffsetFlag == ctp.OffsetFlagCloseToday {
		return true
	}
	return false
}

func isExchangeSeperateToday(exchangeID string) bool {
	if strings.Contains("SHFE/INE", exchangeID) {
		return true
	}
	return false
}

func convertOrder(of *ctp.OrderField) *Order {
	side := exchange.OrderSideBuy
	if of.Direction == ctp.DirectionSell {
		side = exchange.OrderSideSell
	}
	insertTime := ctp.ParseTimeWithString(of.InsertDate, of.InsertTime)
	ctpUpdateTime := ctp.ParseTimeWithString(of.LastTradeDate, of.LastTradeTime)
	if of.LastTradeTime == "" {
		ctpUpdateTime = insertTime
	}
	status := convertOrderStatus(of.OrderStatus, of.OrderSubmitStatus)
	orderType := convertOrderType(of)
	ctpOrderID := fmt.Sprintf("%d_%d_%s", of.FrontID, of.SessionID, of.OrderRef)
	o := &Order{
		exchange.Order{
			InstrumentType: exchange.USDXMarginedFutures,
			OrderID:        ctpOrderID,
			Symbol:         of.InstrumentID,
			Price:          of.LimitPrice,
			TriggerPrice:   of.StopPrice,
			Qty:            float64(of.VolumeTotalOriginal),
			ExecPrice:      of.TradePrice,
			ExecQty:        float64(of.VolumeTraded),
			Fee:            0,
			FeeAsset:       "CNY",
			Type:           orderType,
			Side:           side,
			Status:         status,
			TimeInForce:    exchange.GTC,
			ReduceOnly:     false, // 可能可以根据 OffsetFlagType 来判断，但是似乎没啥用
			CreateTime:     &insertTime,
			UpdateTime:     &ctpUpdateTime,
		},
		[]*SubOrder{},
		ctpUpdateTime,
	}
	return o
}

func convertPositionSide(d ctp.PosiDirectionType, qty int) exchange.PositionSide {
	switch d {
	case ctp.PosiDirectionLong:
		return exchange.PositionSideLong
	case ctp.PosiDirectionShort:
		return exchange.PositionSideShort
	case ctp.PosiDirectionNet:
		if qty > 0 {
			return exchange.PositionSideLong
		} else {
			return exchange.PositionSideShort
		}
	default:
		return exchange.UnknownPositionSide
	}
}

func (this *GatewayServer) convertPosition(pf *ctp.PositionField) (p *exchange.Position, er error) {
	if lastPrice, _, err := this.getLastPrice(pf.InstrumentID); err != nil {
		er = err
		return
	} else {
		var contractSize float64
		if in, err := this.GetInstrument(pf.InstrumentID); err != nil {
			er = err
			return
		} else {
			contractSize = in.ContractSize
		}

		position := float64(pf.Position)
		leverage := 0.0
		if pf.UseMargin != 0.0 {
			leverage = pf.OpenAmount / pf.UseMargin
		}
		entryPrice := 0.0
		if position != 0.0 {
			entryPrice = pf.OpenCost / contractSize / float64(pf.Position)
		}

		if entryPrice == math.NaN() {
			entryPrice = 0.0
		}
		p = &exchange.Position{
			InstrumentType:   exchange.USDXMarginedFutures,
			ExchangeName:     pf.ExchangeID,
			Symbol:           pf.InstrumentID,
			Qty:              position,
			Side:             convertPositionSide(pf.PositionDirection, pf.Position),
			EntryPrice:       entryPrice,
			MarkPrice:        lastPrice,
			LastPrice:        lastPrice,
			Leverage:         leverage, // 不清楚 .PositionCost 和 .OpenCost  有啥区别
			LiquidationPrice: 0,
			// UnrealisedPNL:    pf.PositionProfit, 只有当日浮盈
			Margin:     pf.ExchangeMargin,
			UpdateTime: &pf.UpdateTime,
		}
		if p.Side == exchange.PositionSideShort {
			p.Qty = -p.Qty
		}
		p.UnrealisedPNL = (lastPrice - p.EntryPrice) * p.Qty * contractSize
		return
	}
}

func convertUserMargin(account *ctp.AccountField) *exchange.UserMargin {
	userMargin := &exchange.UserMargin{}
	userMargin.WalletBalance = account.Balance - account.PositionProfit // 不包含当前持仓中的浮盈
	userMargin.AvailableMargin = account.Available                      // 包含当前持仓中的浮盈
	userMargin.MarginBalance = account.Balance
	userMargin.Currency = "CNY"
	return userMargin
}

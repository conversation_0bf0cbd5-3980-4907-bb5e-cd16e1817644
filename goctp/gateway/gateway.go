package gateway

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"math/rand"
	_ "net/http/pprof"
	"os"
	"path"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-contrib/pprof"
	"github.com/go-resty/resty/v2"
	"github.com/puzpuzpuz/xsync"
	"github.com/robfig/cron/v3"
	"github.com/wizhodl/goctp/ctp"
	"github.com/wizhodl/goctp/def"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	exchange_gateway "github.com/wizhodl/quanter/exchange/gateway"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"
	bolt "go.etcd.io/bbolt"
	"go.uber.org/atomic"
	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

/* Gateway 网关相关的代码 */

const ExtKeySubOrderID = "SubOrderID_0"
const ExtKeySubOrderIDToday = "SubOrderID_1"

type OrderSignalAction string

const UnknownOrderSignalAction OrderSignalAction = ""
const OrderSignalActionCreate OrderSignalAction = "CreateOrder"
const OrderSignalActionCancel OrderSignalAction = "CancelOrder"

type OrderSignal struct {
	// 什么类型的订单动作
	Action OrderSignalAction
	// 订单动作是否成功
	Success bool
	// 订单动作的错误原因
	Error string
	// 这里不能用指针，否则会 crash
	Order Order
}

type OrderSignalMap map[string]chan OrderSignal

func (this OrderSignalMap) add(key string) chan OrderSignal {
	this[key] = make(chan OrderSignal, 1)
	return this[key]
}

func (this OrderSignalMap) remove(key string) {
	delete(this, key)
}

type GatewayServer struct {
	sync.RWMutex
	command.BaseResponder
	ID               string
	writeLock        sync.RWMutex
	opts             *Option
	startAt          time.Time
	gin              *gin.Engine
	dbs              *xsync.MapOf[string, *bolt.DB]
	exitChan         chan struct{}
	storage          *Storage
	tickCounter      *xsync.MapOf[string, int64]     // 不同品种 onTick 的计数器，key: instrumentID, value: counter
	klineUpdateTimes *xsync.MapOf[string, time.Time] // 不同品种的上次 Kline 的更新时间，key: instrumentID, value: updateTime

	ctp *ctp.CTPController

	// K 线的 http server 会从此 channel 中读取 1min 的 K 线
	klineChan chan *def.KLine

	/* 不同品种的分钟 K 线
	key: instrumentID, value: *def.KLine
	*/
	minuteKLines *xsync.MapOf[string, *def.KLine]

	/* 分钟 K 线的第一个 tick 的成交量快照，后续的 tick 都在此基础上计算增量的 volume
	每一分钟更新一次，最新的成交量快照
	key: instrumentID, value: float64
	*/
	minuteKLinesFirstVolume *xsync.MapOf[string, float64]

	// 创建订单的 http 请求会等待 OrderSignal 结果，确认创建完成才返回
	createOrderSignals OrderSignalMap

	// 取消订单的 http 请求会等待 OrderSignal 结果，确认取消完成才返回
	cancelOrderSignals OrderSignalMap

	/* 品种数据是否就绪
	因为 ctp 品种数据的回调是异步的，每次发送一个品种回来，必须在收到所有品种后，应用才可以确保可以进行后续处理
	*/
	InstrumentsReady *atomic.Bool

	/* 品种数据
	包含 tick 的 lastPrice 更新、InstrumentStatus 的更新
	key: instrumentID, value: *Instrument
	*/
	Instruments *xsync.MapOf[string, *Instrument]

	/* 客户端 websocket 连接
	向客户端推送消息时，只需要便利 connMap 并向 msgChan 中写入 *Packet 数据即可
	key: remoteAddr, value: msgChan chan *Packet
	*/
	connMap *xsync.MapOf[string, chan *exchange.Packet]

	/* Slack 对应的 webscoket 连接，最后一个连接
	主要用于通过该地址修改 slack 连接对应的 subscribedKeys
	*/
	slackConnAddr string

	/* websocket 连接对应的订阅 keys
	key: connAddr, value: list of subscribed keys
	主要用于通过 slackConnAddr 查找和修改 subscribed keys
	*/
	connSubscribedKeys map[string][]string

	// 手工通知 websocket 推送消息
	notifyChan chan byte

	messenger        *GatewayMessenger
	commandProcessor *command.CommandProcessor // 命令处理器

	// 定时任务管理器
	cron *cron.Cron

	// 品种状态变化对应的 k 线更新缓存器，需要更新 kline 的时候，往 buffer 中写入 symbol 即可
	// 因为它需要延迟 1min flush，所以需要用一个单独的 DelayableBuffer 的实例
	instrumentChangeKlineSymbolsBuffer *DelayableBuffer
}

func NewGatewayServer(opts *Option, commitHash, buildTime string) (server *GatewayServer, er error) {
	responder := command.NewBaseResponder(opts.Debug, commitHash, buildTime, opts.SlackChannel, nil, "")
	server = &GatewayServer{
		BaseResponder:           responder,
		ID:                      opts.ID,
		opts:                    opts,
		startAt:                 time.Now(),
		dbs:                     xsync.NewMapOf[*bolt.DB](),
		exitChan:                make(chan struct{}),
		klineChan:               make(chan *def.KLine, 20000),
		minuteKLines:            xsync.NewMapOf[*def.KLine](),
		minuteKLinesFirstVolume: xsync.NewMapOf[float64](),
		createOrderSignals:      map[string]chan OrderSignal{},
		cancelOrderSignals:      map[string]chan OrderSignal{},
		Instruments:             xsync.NewMapOf[*Instrument](),
		connMap:                 xsync.NewMapOf[chan *exchange.Packet](),
		notifyChan:              make(chan byte),
		ctp:                     ctp.NewCTPController(opts.Debug),
		connSubscribedKeys:      map[string][]string{},
		InstrumentsReady:        atomic.NewBool(false),
		tickCounter:             xsync.NewMapOf[int64](),
		klineUpdateTimes:        xsync.NewMapOf[time.Time](),
	}
	server.instrumentChangeKlineSymbolsBuffer = NewDelayableBuffer(5, 5, 80, server.updateSymbolKlinesWithTianqin)
	server.messenger = NewGatewayMessenger(server)
	server.BaseResponder.Messenger = server.messenger
	server.commandProcessor = command.NewCommandProcessor(server,
		[]command.Commander{
			NewLaunchGridCommand(server),
			NewStatusGatewayCommand(server),
			NewSubscribeGatewayCommand(server),
			NewUnsubscribeGatewayCommand(server),
			NewConfigGatewayCommand(server),
			NewOrderGatewayCommand(server),
			NewPositionsGatewayCommand(server),
			NewOpenOrdersGatewayCommand(server),
			NewClosePositionCTPCommand(server),
			NewClearCTPCommand(server),
			NewCancelOrderCTPCommand(server),
			NewGetAccountGatewayCommand(server),
			NewGetInstrumentsGatewayCommand(server),
			NewOrdersCTPCommand(server),
			NewQueryOrderCTPCommand(server),
			NewPositionsCTPCommand(server),
			NewGetAccountCTPCommand(server),
			NewGetInstrumentsCTPCommand(server),
			NewGetTradesCTPCommand(server),
			NewGetInstrumentStatusCTPCommand(server),
			NewGetTicksCTPCommand(server),
			NewGetPositionDetailCTPCommand(server),
			NewGetSysOrdersCTPCommand(server),
			NewKlineStatusGatewayCommand(server),
			NewKlineUpdateGatewayCommand(server),
			cmds.NewPagerCommand(),
			NewEnableTradingGatewayCommand(server),
			NewDisableTradingGatewayCommand(server),
			NewAddProductIDGatewayCommand(server),
			NewRemoveProductIDGatewayCommand(server),
			NewPrintProductIDGatewayCommand(server),
			NewExitGatewayCommand(server),
			cmds.NewMuteCommand(),
			cmds.NewDebugCommand(),
			cmds.NewLogCommand(server.opts.Logger.Dir, server.getLogFilename()),
			cmds.NewDownloadLogCommand(server.opts.Logger.Dir, server.getLogFilename()),
			cmds.NewDownloadStorageCommand(server.opts.DataDir, fmt.Sprintf("%s.gateway_storage", server.ID)),
			cmds.NewStackTraceCommand(),
		})
	// 初始化时检查 ReleaseBinaryDir 是否可以写，以免后续 releases 相关命令每次都要检查
	if utils.CheckReleaseBinaryDirPath(server.opts.ReleaseBinaryDir) {
		server.commandProcessor.AddCommands([]command.Commander{
			cmds.NewReleaseCommand(server.opts.ReleaseBinaryDir, server.opts.ID),
			cmds.NewListVersionCommand(server.opts.ReleaseBinaryDir, server.opts.ID),
			cmds.NewUseVersionCommand(server.opts.ReleaseBinaryDir, server.opts.ID),
		})
	}
	if server.Debug {
		server.commandProcessor.AddCommands([]command.Commander{NewComplianceTestCTPCommand(server)})
	}

	if !opts.Ctp.RemoteLaunch {
		if err := server.launch("", ""); err != nil {
			zlog.Errorf("ctp launch failed, error: %s", err)
		}
	}
	if storage, err := setupStorage(server); err != nil {
		er = err
		return
	} else {
		server.storage = storage
	}

	go server.tickerLoop()
	go server.websocketLoop()
	return
}

func (this *GatewayServer) getLogFilename() string {
	return this.opts.ID
}

func TokenSignMiddleware(apiKeys APIKeys, checkSign bool) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 拷贝 context，否则如果读取过 body 后，导致 http handler 中没有数据可以读
		remoteHost := c.Request.RemoteAddr
		isLocalRequest := strings.HasPrefix(remoteHost, "127.0.0.1")
		path := c.Request.URL.Path
		checkSignWhitelist := []string{"/v1/ws", "/v1/stats"}
		inWhitelist := SliceContains(checkSignWhitelist, path)
		if !checkSign || inWhitelist || isLocalRequest {
			c.Next()
			return
		}

		apiID := c.Request.Header.Get("X-QUANTER-API-ID")
		timestampStr := c.Request.Header.Get("X-QUANTER-TIMESTAMP")
		timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
		if err != nil {
			errorJSON(c, 401, "invalid timestamp, int required")
			return
		}
		if time.Now().Unix()-timestamp > 5 {
			errorJSON(c, 401, "request timestamp exipred")
			return
		}

		signStr := c.Request.Header.Get("X-QUANTER-SIGN")
		sign, err := hex.DecodeString(signStr)
		if err != nil {
			errorJSON(c, 401, "invalid signature string, hex required")
			return
		}

		if apiID == "" {
			errorJSON(c, 401, "API ID missing")
			return
		}

		secret := ""
		for _, apiKey := range apiKeys {
			if apiKey.APIKey == apiID {
				secret = string(apiKey.APISecret)
			}
		}
		if secret == "" {
			errorJSON(c, 401, "API ID not found")
			return
		}

		body, err := ioutil.ReadAll(c.Request.Body)
		if err != nil {
			errorJSON(c, 401, fmt.Sprintf("read request body failed, error: %s", err.Error()))
			return
		}
		// 重新写入 body
		// 读取 body 后，c.Request.Body 会为空。导致后续的 http handler 拿不到数据，ctx.ShouldBindWith 报 EOF 错误。
		c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))

		combinedString := strings.Join([]string{timestampStr, c.Request.RequestURI, string(body)}, "|||||")

		h := hmac.New(sha256.New, []byte(secret))
		h.Write([]byte(combinedString))
		validSign := hmac.Equal(sign, h.Sum(nil))
		if !validSign {
			errorJSON(c, 401, "signature not match")
			return
		}

		c.Next()
	}
}

func (this *GatewayServer) Serve() {
	this.RunCron()

	opts := this.opts
	this.gin = gin.Default()
	pprof.Register(this.gin) // 注册 pprof 接口

	this.gin.Use(TokenSignMiddleware(opts.APIKeys, opts.CheckSign))
	v1 := this.gin.Group("/v1")
	{
		v1.GET("/klines/lookup", this.lookupKLineApi)
		v1.POST("/klines/update", this.updateKLineApi)
		v1.GET("/instruments", this.getInstrumentsHandler)
		v1.GET("/orders/open", this.getOpenOrdersHandler)
		v1.GET("/orders", this.getOrdersHandler)
		v1.GET("/ctp/orders", this.getCTPOrdersHandler)
		v1.GET("/instruments/price", this.getLastPriceHandler)
		v1.GET("/balances", this.getAccountBalancesHandler)
		v1.GET("/margin", this.getUserMarginHandler)
		v1.GET("/positions", this.getPositionsHandler)
		v1.GET("/ctp/positions", this.getCTPPositionsHandler)
		v1.POST("/orders/create", this.createOrderHandler)
		v1.GET("/orders/cancel", this.cancelOrderHandler)
		v1.GET("/ws", this.websocketHandler)
		v1.GET("/stats", this.getStatsHandler)
	}
	go func() {
		log.WithField("http_addr", opts.HTTPAddr).Info("start http server")
		log.Fatal(this.gin.Run(opts.HTTPAddr))
	}()
}

func (this *GatewayServer) RunCron() {
	if this.Debug {
		time.AfterFunc(10*time.Second, func() {
			this.updateKlinesWithTianqin()
		})
	}
	c := cron.New()
	c.AddFunc("0 * * * * ", this.updateKlinesWithTianqin)
	c.AddFunc("@every 1m", this.storage.SyncOrders)
	// c.AddFunc("@every 5m", this.storage.QryOrdersTest)
	c.AddFunc("0 0 * * *", this.storage.ArchiveOrders)
	c.AddFunc("0 * * * *", this.checkExpiringPosition)
	this.cron = c
	go c.Run()

	if this.Debug {
		go func() {
			time.Sleep(time.Second * 20)
			this.storage.ArchiveOrders()

			time.Sleep(time.Second * 20)
			this.checkExpiringPosition()
		}()
	}
}

func (this *GatewayServer) Exit() {
	close(this.exitChan)
	log.Infof("server exiting")
}

func (this *GatewayServer) RepairInstrumentID(instrumentID string) (repairedID string, er error) {
	if !this.InstrumentsReady.Load() {
		er = errors.New("instruments not ready")
		return
	}
	this.Instruments.Range(func(k string, instr *Instrument) bool {
		if strings.EqualFold(instr.Symbol, instrumentID) {
			repairedID = instr.Symbol
			return false
		}
		return true
	})
	return
}

func (this *GatewayServer) getLastPrice(instrumentID string) (price float64, updateTime time.Time, er error) {
	if !this.InstrumentsReady.Load() {
		er = fmt.Errorf("instrument not ready, (%s)", instrumentID)
		return
	}

	var instrument *Instrument
	this.Instruments.Range(func(k string, instr *Instrument) bool {
		if strings.EqualFold(instr.Symbol, instrumentID) {
			instrument = instr
			return false
		}
		return true
	})
	if instrument == nil {
		er = fmt.Errorf("instrument not found (%s)", instrumentID)
		return
	} else {
		price = instrument.LastPrice
		updateTime = instrument.LastPriceUpdateTime
		return
	}
}

func (this *GatewayServer) SetDebug(debug bool) {
	this.BaseResponder.Debug = debug
	var zlogger *zap.SugaredLogger
	if debug {
		zlogger = zlog.NewLogger("DEBUG", this.BaseResponder.LogPath)
	} else {
		zlogger = zlog.NewLogger("INFO", this.BaseResponder.LogPath)
	}
	zlog.SetLogger(zlogger)
}

func (this *GatewayServer) launch(mainPassword, googleAuthCode string) error {
	if this.Launched.Load() {
		return nil
	}
	if this.ctp == nil {
		return errors.New("ctp controller not initialized")
	}
	// 解密并设置 opts.Password 和 opts.AuthCode，必须在 initQuote 和 initTrade 之前调用
	if mainPassword != "" {
		if err := this.decryptSecret(mainPassword, googleAuthCode); err != nil {
			return err
		}
	}
	// 连接 ctp 服务器
	this.ctp.Connect(this.initQuote, this.initTrade)

	// 设置 ctp 的回调
	this.ctp.Quote.RegOnTick(this.onTick)
	this.ctp.Quote.RegOnRspUserLogin(this.onQuoteLogin)
	this.ctp.Trade.RegOnRspUserLogin(this.onTradeLogin)
	this.ctp.Trade.RegOnRtnInstrumentStatus(this.onInstrumentStatus)
	this.ctp.Trade.RegOnRtnInstrumentMarginRate(this.onInstrumentMarginRate)
	this.ctp.Trade.RegOnErrAction(this.onErrOrderAction)
	this.ctp.Trade.RegOnRtnOrder(this.onOrder)
	this.ctp.Trade.RegOnRtnCancel(this.onOrderCancel)
	this.ctp.Trade.RegOnErrRtnOrder(this.onErrOrder)

	go this.checkLoop()

	this.Launched.Store(true)
	return nil
}

func (this *GatewayServer) archiveKlines(symbols []string, action string) {
	type KlineArchive struct {
		KLines        exchange.KLines `json:"klines"`
		ArchiveAction string          `json:"archive_action"`
		ArchiveTime   time.Time       `json:"archive_time"`
		Symbol        string          `json:"symbol"`
	}
	respKlines := exchange.KLines{}
	for _, symbol := range symbols {
		exchangeName := ctp.GetExchangeID(symbol)
		url := fmt.Sprintf("/v1/klines/lookup?exchange=%s&instrument_id=%s&period=1day&limit=200", exchangeName, symbol)
		if err := this.sendHTTPRequest(resty.MethodGet, url, nil, &respKlines); err != nil {
			zlog.Errorf(" send kline http request failed: %s", err)
			continue
		}

		data := KlineArchive{
			KLines:        respKlines,
			ArchiveAction: action,
			ArchiveTime:   time.Now(),
			Symbol:        fmt.Sprintf("%s.%s", exchangeName, symbol),
		}
		archivePath := path.Join(this.opts.DataDir, fmt.Sprintf("kline_archive_%s.json", symbol))
		f, err := os.OpenFile(archivePath, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0755)
		if err != nil {
			zlog.Errorf("open order archive file err: %s", err)
			return
		}
		// 归档
		if data, err := json.Marshal(data); err != nil {
			zlog.Errorf("archive klines failed for (%s), marshal error: %s", symbol, err)
			return
		} else {
			f.Write(data)
			_, err := f.WriteString("\n")
			f.Close()
			if err != nil {
				zlog.Errorf("archive klines write file error: %s", err)
				return
			}
		}
	}
}

func (this *GatewayServer) initQuote(quote *ctp.Quote) {
	if quote == nil {
		return
	}
	// 连接服务器回调
	quote.RegOnFrontConnected(func() {
		now := time.Now()
		this.ctp.QuoteConnectedTime = &now
		this.ctp.QuoteConnected.Store(true)
		this.ctp.SetTradingSymbols(this.storage.TradingSymbols)
		go func() {
			quote.Login(this.opts.Ctp.InvestorID, this.opts.Ctp.Password, this.opts.Ctp.BrokerID)
		}()
	})

	// quote.RegOnFrontDisConnected(func(reason int) {
	// 	zlog.Debugf("[gateway] front disconnected, reason: %d", reason)
	// })

	// 登录回调
	quote.ReqConnect(this.opts.Ctp.QuoteFront)
}

func (this *GatewayServer) initTrade(trade *ctp.Trade) {
	if trade == nil {
		return
	}
	trade.RegOnFrontConnected(func() {
		now := time.Now()
		this.ctp.TradeConnectedTime = &now
		this.ctp.TradeConnected.Store(true)
		// 因为 Login 现在是同步调用了，如果不放到 goroutine ，会卡住其他所有请求
		go func() {
			err := trade.Login(this.opts.Ctp.InvestorID, this.opts.Ctp.Password, this.opts.Ctp.BrokerID, this.opts.Ctp.AppID, this.opts.Ctp.AuthCode)
			if err != nil {
				zlog.Errorf("gateway login failed, error: %s", err)
			}
		}()
	})

	trade.RegOnFrontDisConnected(func(reason int) {
		// 防止接口返回错误的数据
		this.InstrumentsReady.Store(false)
	})

	trade.ReqConnect(this.opts.Ctp.TradeFront)
}

// 解密本地密钥获得 CTP 的 Password 和 AuthCode
// 解密后的格式：InvestorID|||Password|||AuthCode
func (this *GatewayServer) decryptSecret(password string, authCode string) error {
	if err := secrets.CheckPassword(password, authCode); err != nil {
		return err
	} else {
		apiSecret, err := exchange_gateway.NewCTPAPISecret("", secrets.SecretString(this.opts.Ctp.EncryptedSecrets), true)
		if err != nil {
			return fmt.Errorf("decrypt ctp secret failed, error: %s", err)
		}
		if apiSecret.InvestorID != this.opts.Ctp.InvestorID {
			return fmt.Errorf("decryped investor id not match, (%s) != (%s)", apiSecret.InvestorID, this.opts.Ctp.InvestorID)
		}
		this.opts.Ctp.InvestorID = apiSecret.InvestorID
		this.opts.Ctp.Password = apiSecret.Password
		this.opts.Ctp.AuthCode = apiSecret.AuthCode

		if err := this.decryptAPIKeys(); err != nil {
			return err
		}
		return nil
	}
}

func (this *GatewayServer) decryptAPIKeys() error {
	for i, apiKey := range this.opts.APIKeys {
		if apiKey.EncryptedAPISecret != "" {
			if apiSecret, err := secrets.Decrypt(apiKey.EncryptedAPISecret); err != nil {
				return fmt.Errorf("decrypt api keys failed, api key (%s), error: %s", apiKey.APIKey, err)
			} else {
				this.opts.APIKeys[i].APISecret = apiSecret
			}
		}
	}
	return nil
}

func (this *GatewayServer) checkLoop() {
	loginTicker := time.NewTicker(5 * time.Minute)
	subscribeTicker := time.NewTicker(1 * time.Minute)
	klineTicker := time.NewTicker(10 * time.Minute)
	marginRateTicker := time.NewTicker(1 * time.Minute)
	archiveKlineTicker := time.NewTicker(5 * time.Minute)

	for {
		select {
		case <-loginTicker.C:
			zlog.Debugf("[gateway] check loop ctp login")
			if !this.Launched.Load() {
				continue
			}

			if this.ctp.TradeConnected.Load() {
				if !this.ctp.IsTradeLogin() {
					zlog.Infof("[gateway] trade connected, but not logged in, re-login")
					this.ctp.LoginTrade(this.opts.Ctp.InvestorID, this.opts.Ctp.Password, this.opts.Ctp.BrokerID, this.opts.Ctp.AppID, this.opts.Ctp.AuthCode)
				}
			} else {
				// TODO: 观察看是否需要重新连接
				// zlog.Infof("[gateway] trade not connected, re-connect")
				// this.ctp.Trade.ReqConnect(this.opts.Ctp.TradeFront)
			}
			if this.ctp.QuoteConnected.Load() {
				if !this.ctp.IsQuoteLogin() {
					zlog.Infof("[gateway] quote connected, but not logged in, re-login")
					this.ctp.LoginQuote(this.opts.Ctp.InvestorID, this.opts.Ctp.Password, this.opts.Ctp.BrokerID)
				}
			}
		case <-subscribeTicker.C:
			zlog.Debugf("[gateway] check loop tick counter")
			if !this.Launched.Load() || !this.ctp.QuoteConnected.Load() {
				continue
			}
			for _, symbol := range this.storage.KLineSymbols {
				instrument, err := this.GetInstrument(symbol)
				// 仅在交易时间检查 tickCounter
				if err != nil || instrument.Status != exchange.InstrumentStatusContinuous {
					continue
				}
				this.checkTickForSymbol(symbol)
			}
		case <-klineTicker.C:
			zlog.Debugf("[gateway] check loop kline symbols")
			this.checkKLineForSymbols()
		case <-marginRateTicker.C:
			if this.ctp.IsTradeLogin() && this.ctp.IsInstrumentsReady() {
				for _, symbol := range this.storage.TradingSymbols {
					instrument, err := this.GetInstrument(symbol)
					if err != nil {
						continue
					}
					if instrument.LongMarginRatioByMoney == 0 && instrument.ShortMarginRatioByMoney == 0 {
						this.ctp.Trade.QueryInstrumentMarginRate(symbol)
					}
				}
			}
		case <-archiveKlineTicker.C:
			this.archiveKlines(this.storage.KLineSymbols, "Periodical")
		}
	}
}

// 向某个 websocket 连接增加一个订阅的 key
func (this *GatewayServer) AddSubscribeKey(remoteAddr, key string) {
	subKeys := []string{}
	if keys, found := this.connSubscribedKeys[remoteAddr]; found {
		subKeys = keys
	} else {
		this.connSubscribedKeys[remoteAddr] = subKeys
	}
	if !SliceContains(subKeys, key) {
		subKeys = append(subKeys, key)
		this.connSubscribedKeys[remoteAddr] = subKeys
	}
}

// 从某个 websocket 连接去除一个订阅的 key
func (this *GatewayServer) RemoveSubscribeKey(remoteAddr, key string) {

	if keys, found := this.connSubscribedKeys[remoteAddr]; found {
		index := -1
		for i, k := range keys {
			if k == key {
				index = i
			}
		}
		if index > -1 {
			this.connSubscribedKeys[remoteAddr] = append(this.connSubscribedKeys[remoteAddr][:index], this.connSubscribedKeys[remoteAddr][index+1:]...)
		}
	}
}

func (this *GatewayServer) IsTrading(instrumentID string) (yes bool) {
	return SliceContains(this.storage.TradingSymbols, instrumentID)
}

// 不启用某个品种的交易，暂时去除保存该品种的原始 ticks
func (this *GatewayServer) DisbleTrading(instrumentID string) {
	index := -1
	for i, symbol := range this.storage.TradingSymbols {
		if symbol == instrumentID {
			index = i
		}
	}
	if index > -1 {
		this.storage.TradingSymbols = append(this.storage.TradingSymbols[:index], this.storage.TradingSymbols[index+1:]...)
		if this.ctp != nil {
			this.ctp.SetTradingSymbols(this.storage.TradingSymbols)
		}
		this.storage.Save()
	}
}

// 启用某个品种可以交易，暂时主要是判断是否保存该品种的原始 ticks
// 订阅某个品种后，应该允许该品种交易
func (this *GatewayServer) EnableTrading(instrumentID string) error {
	ins, err := this.GetInstrument(instrumentID)
	if err != nil {
		return err
	}
	if this.isInstrumentDelivered(instrumentID) {
		return errors.New("instrument delivered")
	}
	productID := ins.UnderlyCurrency
	if productID == "" {
		return errors.New("product id not found for instrument")
	}
	if !SliceContains(this.storage.TradingSymbols, instrumentID) {
		this.storage.TradingSymbols = append(this.storage.TradingSymbols, instrumentID)
		this.storage.Save()
		if this.ctp != nil {
			this.ctp.SetTradingSymbols(this.storage.TradingSymbols)
		}
		// 如果品种没有在 KLine 允许的产品列表中，加入列表中
		// 会自动更新 KLineSymbols
		this.storage.AddProductID(productID)

		// 如果没有 tick 要 subscribe
		this.checkTickForSymbol(instrumentID)
		// 如果 kline 不是最新，更新 kline
		this.checkKLineForSymbol(instrumentID)
	}
	return nil
}

func (this *GatewayServer) checkTickForSymbol(symbol string) {
	counter, _ := this.tickCounter.Load(symbol)
	if counter <= 1 {
		zlog.Warnf("[gateway] found tick counter <= 1, subscribe: %s", symbol)
		this.subscribe([]string{symbol})
	}
}

func (this *GatewayServer) checkKLineForSymbol(symbol string) {
	updateKLine := false
	updateTime, found := this.klineUpdateTimes.Load(symbol)
	if found {
		if time.Since(updateTime) > 65*time.Minute {
			updateKLine = true
		}
	} else {
		updateKLine = true
	}
	if updateKLine {
		// 尽量不要一起更新 K线，以免被 tianqin 那边限制
		delay := time.Duration(rand.Intn(10))
		time.AfterFunc(delay*time.Second, func() {
			this.updateSymbolKlinesWithTianqin([]string{symbol})
		})
	}
}

func (this *GatewayServer) checkKLineForSymbols() {
	symbols := []string{}
	for _, symbol := range this.storage.KLineSymbols {
		updateTime, found := this.klineUpdateTimes.Load(symbol)
		if found {
			// 开盘时间后 30 分钟内，每 5 分钟更新一次 Kline
			// 其他时间，每个小时更新一次 Kline
			nowTime := time.Now().In(time.FixedZone("CST", 8*60*60)) // 北京时间
			seconds := nowTime.Hour()*3600 + nowTime.Minute()*60 + nowTime.Second()
			// 32400 -> 09:00, 34200 -> 9:30
			// 75600 -> 21:00, 77400 -> 21:30
			isHalfHourAfterOpen := (seconds > 32400 && seconds < 34200) || (seconds > 75600 && seconds < 77400)
			delay := time.Duration(65)
			if isHalfHourAfterOpen {
				delay = time.Duration(5)
			}
			if time.Since(updateTime) > delay*time.Minute {
				symbols = append(symbols, symbol)
			}
		} else {
			symbols = append(symbols, symbol)
		}
	}
	this.updateSymbolKlinesWithTianqin(symbols)
}

func (this *GatewayServer) CheckKLineAllowed(instrumentID string) bool {
	return SliceContains(this.storage.KLineSymbols, instrumentID)
}

// 获取最近3个月的期货品种，有时候我们可能需要在更超前的月份品种上交易
// 比如，当前日期是3月29日，主力品种可能已经是5月，如果只订阅当前4月，肯定是不够的
func (this *GatewayServer) GetKLineSymbols(productID string, callback func(symbols []string)) {
	symbols := []string{}
	now := time.Now()
	month := now.Month()
	year := now.Year()
	year0, month0 := foldMonth(year, int(month+1))
	year1, month1 := foldMonth(year, int(month+2))
	year2, month2 := foldMonth(year, int(month+3))
	// TODO: 处理跨年的问题
	if strings.EqualFold(productID, "all") {
		for _, productID := range this.storage.KLineProductIDs {
			symbols = append(symbols, fmt.Sprintf("%s%s%02d", productID, fmt.Sprintf("%v", year0)[2:], month0))
			symbols = append(symbols, fmt.Sprintf("%s%s%02d", productID, fmt.Sprintf("%v", year1)[2:], month1))
			symbols = append(symbols, fmt.Sprintf("%s%s%02d", productID, fmt.Sprintf("%v", year2)[2:], month2))
		}
	} else {
		symbols = append(symbols, fmt.Sprintf("%s%s%02d", productID, fmt.Sprintf("%v", year0)[2:], month0))
		symbols = append(symbols, fmt.Sprintf("%s%s%02d", productID, fmt.Sprintf("%v", year1)[2:], month1))
		symbols = append(symbols, fmt.Sprintf("%s%s%02d", productID, fmt.Sprintf("%v", year2)[2:], month2))
	}
	go func(this *GatewayServer, symbols []string) {
		for !this.InstrumentsReady.Load() {
			time.Sleep(200 * time.Microsecond)
		}
		validSymbols := []string{}
		for _, symbol := range symbols {
			if _, err := this.GetInstrument(symbol); err != nil {
				zlog.Debugf("get kline symbols, get instrument info failed, error: %s", err)
				continue
			} else {
				validSymbols = append(validSymbols, symbol)
			}
		}
		if callback != nil {
			if len(validSymbols) > 0 {
				callback(validSymbols)
			} else {
				zlog.Errorf("get kline symbols, not a single valid symbol")
			}
		}
	}(this, symbols)
	return
}

// 在合约过期前 n 天，自动平仓持有的即将过期的仓位，避免实物交割
func (this *GatewayServer) checkExpiringPosition() {
	ctpPositions, err := this.ctp.GetPositions()
	if err != nil {
		return
	}
	ctpInstruments, err := this.ctp.GetInstruments()
	if err != nil {
		return
	}
	ctpInstrumentStatuss := this.ctp.GetInstrumentStatuss()
	ctpPositions.Range(func(k string, pf *ctp.PositionField) bool {
		if pf.Position == 0 {
			return true
		}

		ins, found := ctpInstruments.Load(pf.InstrumentID)
		if !found {
			return true
		}
		status, found := ctpInstrumentStatuss.Load(ins.ProductID)
		if !found || status.InstrumentStatus != ctp.InstrumentStatusContinuous {
			return true
		}

		if this.isInstrumentExpiredSoon(pf.InstrumentID) {
			this.SendMsgf("%s 持仓即将到期，将于 10 分钟后自动平仓。", pf.InstrumentID)
			go func() {
				time.Sleep(time.Minute * 10)
				errorMsgs := this.closePosition(pf.InstrumentID, 0)
				if len(errorMsgs) > 0 {
					this.ErrorMsgf("自动平仓出现错误，errors ： %s", SliceStringJoin(errorMsgs, "\n", false))
				}
			}()
		}
		return true
	})
}

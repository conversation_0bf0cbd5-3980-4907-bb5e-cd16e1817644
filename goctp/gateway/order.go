package gateway

import (
	"errors"
	"fmt"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/wizhodl/goctp/ctp"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

/* 订单相关的代码 */

type SubOrder struct {
	OrderID    string // 由 FrontID + SessionID + OrderRef 组成的编号
	OrderSysID string // 交易所编号，用于主动查询订单
	IsToday    bool
	IsCanceled bool // 当 ctp 挂单收盘时未成交，将被标记为取消
	CtpOrder   *ctp.OrderField
}

type Order struct {
	exchange.Order
	SubOrders     []*SubOrder
	CtpUpdateTime time.Time
}

var StatusValidChanges [][]exchange.OrderStatus = [][]exchange.OrderStatus{
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusRejected},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusTriggered},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusTriggered, exchange.OrderStatusFilled},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusTriggered, exchange.OrderStatusPartialFilled},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusTriggered, exchange.OrderStatusPartialFilled, exchange.OrderStatusFilled},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusTriggered, exchange.OrderStatusPartialFilled, exchange.OrderStatusPartialCancelled},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusTriggered, exchange.OrderStatusCancelled},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusTriggered, exchange.OrderStatusPartialCancelled},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusFilled},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusPartialFilled},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusPartialFilled, exchange.OrderStatusFilled},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusPartialFilled, exchange.OrderStatusPartialCancelled},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusCancelled},
	{exchange.UnknownOrderStatus, exchange.OrderStatusNew, exchange.OrderStatusPartialCancelled},
}

func (this *Order) ChangeStatus(newStatus exchange.OrderStatus) (er error) {
	valid := false
	validRows := [][]exchange.OrderStatus{}
	for _, row := range StatusValidChanges {
		// 如果 row 的最终状态是 newStatus
		if row[len(row)-1] == newStatus {
			validRows = append(validRows, row)
		}
	}
	for _, vRow := range validRows {
		if SliceContains(vRow, this.Status) {
			valid = true
			break
		}
	}
	if !valid {
		er = fmt.Errorf("can not change from status (%s) to status (%s)", this.Status, newStatus)
		return
	}
	zlog.Infof("order status change of (%s): (%s) -> (%s)", this.OrderID, this.Status, newStatus)
	this.Status = newStatus
	return
}

type Orders []*Order

func (this *Order) GetSubOrder(subOrderID string) (subOrder *SubOrder) {
	for _, o := range this.SubOrders {
		if o.OrderID == subOrderID {
			subOrder = o
			break
		}
	}
	return
}

func (this *Order) AddSubOrder(subOrderID string, isToday bool, ctpOrder *ctp.OrderField) error {
	if this.GetSubOrder(subOrderID) != nil {
		return fmt.Errorf("sub order already exist")
	}
	this.SubOrders = append(this.SubOrders, &SubOrder{OrderID: subOrderID, IsToday: isToday, CtpOrder: ctpOrder})
	return nil
}

func (this *Order) GetSubOrderIDs() (ids []string) {
	for _, subOrder := range this.SubOrders {
		ids = append(ids, subOrder.OrderID)
	}
	return
}

func (this *Order) IsAllSubOrderCanceled() bool {
	ctpOrderCanceledCount := 0
	for _, subOrder := range this.SubOrders {
		if subOrder.IsCanceled {
			ctpOrderCanceledCount += 1
		}
	}
	return ctpOrderCanceledCount > 0 && ctpOrderCanceledCount == len(this.SubOrders)
}

func isSubOrderID(orderID string) bool {
	return strings.Count(orderID, "_") > 0
}

func (this *GatewayServer) updateSuborders(order *Order) (updated bool, er error) {
	var foundOrders []*ctp.OrderField
	// 主订单的原始 ctp 订单更新时间
	origUpdateTime := order.CtpUpdateTime
	// 主订单的最新 ctp 订单更新时间，每次处理 suborder 时对比更新为最新的 suborder 的 ctp 更新时间
	latestUpdateTime := order.CtpUpdateTime

	// 如果没有 subOrderIDs，不需要更新 order 的状态
	if len(order.GetSubOrderIDs()) == 0 {
		return
	}

	ctpOrders, err := this.ctp.GetOrders()
	if err != nil {
		er = err
		return
	}

	for _, subOrderID := range order.GetSubOrderIDs() {
		subOrder := order.GetSubOrder(subOrderID)
		ctpOrders.Range(func(orderID string, of *ctp.OrderField) bool {
			if orderID == subOrderID {
				subOrder.CtpOrder = of
				subOrder.OrderSysID = of.OrderSysID
				foundOrders = append(foundOrders, subOrder.CtpOrder)
			}
			return true
		})

		if subOrder.CtpOrder == nil && subOrder.OrderSysID != "" {
			// 查询远程订单，如果查到将触发更新本地订单
			this.ctp.QueryOrder(order.Symbol, subOrder.OrderSysID)
		}
	}

	if len(foundOrders) == 0 {
		er = fmt.Errorf("update sub orders failed, no sub orders found")
		return
	}

	subOrders := []*Order{} // ctp 订单转换的 order

	for _, ctpOrder := range foundOrders {
		subOrders = append(subOrders, convertOrder(ctpOrder))
	}

	status := order.Status
	execAmount := 0.0
	execQty := 0.0
	cancelCount := 0
	rejectedCount := 0
	triggeredCount := 0
	openCount := 0
	for _, subOrder := range subOrders {
		// 更新最后更新的子订单时间
		if (*subOrder.UpdateTime).Sub(latestUpdateTime) > 0 {
			latestUpdateTime = subOrder.CtpUpdateTime
		}

		execQty += subOrder.ExecQty
		execAmount += subOrder.ExecPrice * subOrder.ExecQty
		if subOrder.Status == exchange.OrderStatusCancelled {
			cancelCount += 1
		} else if subOrder.Status == exchange.OrderStatusRejected {
			rejectedCount += 1
		} else if subOrder.Status == exchange.OrderStatusTriggered {
			triggeredCount += 1
		}

		if subOrder.IsOpen() {
			openCount += 1
		}
	}
	if cancelCount == len(subOrders) {
		status = exchange.OrderStatusCancelled
	} else if rejectedCount == len(subOrders) {
		status = exchange.OrderStatusRejected
	} else if triggeredCount == len(subOrders) {
		status = exchange.OrderStatusTriggered
	}

	needUpdate := false
	// 通过更新时间判断是否有状态变化，更改 order 的状态和更新时间
	// ctp 给的时间并不精确，只到秒，所以可能会有问题
	// 如市价单创建时间和最后成交时间相同，无法通过时间判断更新
	if (latestUpdateTime).Sub(origUpdateTime) > 0 {
		needUpdate = true
	} else if openCount == 0 && order.IsOpen() {
		needUpdate = true
	}

	if needUpdate {
		if execQty > 0 {
			order.ExecPrice = execAmount / execQty
			order.ExecQty = execQty

			if order.ExecQty == order.Qty {
				status = exchange.OrderStatusFilled
			} else if status == exchange.OrderStatusCancelled {
				status = exchange.OrderStatusPartialCancelled
			} else {
				status = exchange.OrderStatusPartialFilled
			}
		}

		err := order.ChangeStatus(status)
		if err != nil {
			zlog.Errorf("update suborders, change order status failed for order (%s), error: %s", order.OrderID, err)
			return
		}
		order.CtpUpdateTime = latestUpdateTime
		now := time.Now()
		order.UpdateTime = &now
		updated = true
	}

	return
}

// 条件单的触发函数
// instrument 的 LastPrice 更新后，会遍历 StopLimit 订单列表
func (this *GatewayServer) triggerOrders(instrument *Instrument) {
	if instrument == nil {
		zlog.Errorf("trigger orders for nil instrument")
		return
	}
	if this.storage.ordersLock.TryLock() {
		if orders, found := this.storage.Orders[instrument.Symbol]; found {
			openOrders := []*Order{}
			for _, order := range orders {
				if order.Status == exchange.OrderStatusNew {
					openOrders = append(openOrders, order)
				} else if order.Status == exchange.OrderStatusTriggered {
					// 如果触发但 ctp 订单都标记为 cancel，则需要再新建
					if order.IsAllSubOrderCanceled() {
						openOrders = append(openOrders, order)
					}
				}
			}
			// 等待某个 instrument 所有订单都完成再解锁
			// 因为不希望等到 CreateOrder 同步调用结束才标记 Done，所以给 CreateOrder 函数增加 reqCallback 的参数
			wg := sync.WaitGroup{}
			wg.Add(len(openOrders))
			for _, o := range openOrders {
				// 因为 CreateOrder 会 blocking，放到 goroutine 中处理
				go func(order *Order) {
					if immediately, err := this.checkOrderTriggerImmediately(instrument, order); err == nil && immediately {
						err := this.CreateOrder(order, func(ctpOrderIDs []string, err error) {
							if err == nil {
								zlog.Infof("stop limit order triggered, request create order success: %s @ %.2f  -> (%s)", order.OrderID, instrument.LastPrice, strings.Join(ctpOrderIDs, ","))
							}
							// 不用等整个订单完成，只需要请求发送完成，就可以标记该订单已经处理过了
							wg.Done()
						})
						if err != nil {
							zlog.Errorf("stop limit order triggered, create order failed: %s @ %.2f, error: %s", order.OrderID, instrument.LastPrice, err)
						}
						if this.Debug {
							this.SendMsgf("品种 [%s]， 订单 [%s] 已触发 @%.2f。", instrument.Symbol, order.OrderID, instrument.LastPrice)
						}
					} else {
						wg.Done()
						if err != nil {
							zlog.Debugf("check order trigger immediately failed for order(%s), error: %s", order.OrderID, err)
						}
					}
				}(o)
			}
			wg.Wait()
		}
		this.storage.ordersLock.Unlock()
	}
}

// 检查某个订单是否能以 lastPrice 触发
// createOrder 时不要检查，因为可能是非交易时间，价格也未知，发确定是否触发
// lastPrice 和 updateTime 必须从外面传入，因为如果在函数内部重新获取 lastPrice 可能和外部的 lastPrice 不同
func (this *GatewayServer) checkOrderTriggerImmediately(instrument *Instrument, o *Order) (result bool, er error) {
	if o.Type != exchange.StopLimit {
		return
	}
	if (o.Status != exchange.OrderStatusNew) && !(o.Status == exchange.OrderStatusTriggered && o.IsAllSubOrderCanceled()) {
		er = errors.New("order status error")
		return
	}

	// 如果不是交易时间不要触发
	if instrument.Status != exchange.InstrumentStatusContinuous {
		return
	}
	lastPrice := instrument.LastPrice
	// 如果超出涨跌停价，不要触发
	if lastPrice == 0 || lastPrice < instrument.LowerLimitPrice || lastPrice > instrument.UpperLimitPrice {
		return
	}
	// 创建订单的时候，last price 的时间检查是无法通过的
	// 如果更新时间太老，不返回错误
	exchangeTime, err := this.ctp.CurrentExchangeTime(instrument.ExchangeID)
	if err != nil {
		er = err
		return
	}
	updateSecond := instrument.GetInt(ExtKeyPriceUpdateSecond)
	exchangeSecond := ctp.TimeToSecond(exchangeTime)
	timeDelta := exchangeSecond - updateSecond
	// zlog.Debugf("check tick update time, exchange time: %s, tick time: %s, delta: %d", ctp.FormatTimeSecond(exchangeSecond), ctp.FormatTimeSecond(updateTime), timeDelta)
	if timeDelta > 2 {
		er = fmt.Errorf("last price too old, %s > %s", ctp.FormatTimeSecond(exchangeSecond), ctp.FormatTimeSecond(updateSecond))
		return
	}

	if o.TriggerDirection == exchange.TriggerDirectionHigher && lastPrice > o.TriggerPrice {
		result = true
	}
	if o.TriggerDirection == exchange.TriggerDirectionLower && lastPrice < o.TriggerPrice {
		result = true
	}
	return
}

// 根据订单对象，创建 CTP 订单；支持 Market 和 Limit 两种订单类型
// StopLimit 触发后直接挂 Limit 订单
// 创建订单后：
//
//	       填入 ctpOrderID 到 ExtKeySubOrderID 和 ExtKeySubOrderIDToday
//			  随后 onOrder 等回调时，再更新具体的订单状态
//
// reqCallback 无论是否成功都会回调
func (this *GatewayServer) CreateOrder(order *Order, reqCallback func(ctpOrderIDs []string, err error)) (er error) {
	side := ctp.DirectionBuy
	if order.Side == exchange.OrderSideSell {
		side = ctp.DirectionSell
	}

	orderQty := int(order.Qty - order.ExecQty)
	todayQty := 0 // 平今数量
	openClose := ctp.OffsetFlagOpen
	if order.ReduceOnly {
		openClose = ctp.OffsetFlagClose

		// 检查平仓数量
		position := this.getPosition(order.Symbol, order.Side.OppositeSide())
		if position == nil || position.Position < orderQty {
			er = fmt.Errorf("close qty should not greater than position qty")
			if reqCallback != nil {
				reqCallback([]string{}, er)
			}
			return
		}

		// 如果是平仓单，区分平今/昨的交易所需要根据当前持仓情况分开下单
		if isExchangeSeperateToday(ctp.GetExchangeID(order.Symbol)) {
			if orderQty > position.YdPosition {
				todayQty = orderQty - position.YdPosition
				orderQty = position.YdPosition
			}
		}
	}
	isToday := false

	triggerDirection := ctp.UnknownTriggerDirection
	triggerPrice := 0.0

	var totalCount int32 = 0
	if orderQty > 0 {
		totalCount += 1
	}
	if todayQty > 0 {
		totalCount += 1
	}

	// 已完成 reqCreateOrder 的次数
	var reqDoneCount int32 = 0
	// reqCreateOrder 返回的错误
	var reqErrors = []string{}

	// 回调函数
	var reqCallbackFunc = func(ctpOrderID string, err error) {
		if err == nil {
			if order.Type == exchange.StopLimit {
				err := order.ChangeStatus(exchange.OrderStatusTriggered)
				if err != nil {
					zlog.Errorf("create order req callback, change order status failed, (%s), error: %s", order.OrderID, err)
					return
				}
			}
			order.AddSubOrder(ctpOrderID, isToday, nil)
			this.storage.Save()
			this.storage.logOrder(order)
		} else {
			reqErrors = append(reqErrors, err.Error())
		}
		// 因为 reqCallbackFunc 可能被并发访问，需要用 atomic 操作
		atomic.AddInt32(&reqDoneCount, 1)
		if reqCallback != nil {
			if atomic.LoadInt32(&reqDoneCount) == totalCount {
				if len(reqErrors) == 0 {
					reqCallback(order.GetSubOrderIDs(), nil)
				} else {
					reqCallback(order.GetSubOrderIDs(), fmt.Errorf("%s", strings.Join(reqErrors, " | ")))
				}
			}
		}
	}

	// 如果是平仓单，则根据昨今数量可能有两笔 ctp 订单
	if orderQty > 0 {
		if order.Type == exchange.Limit || order.Type == exchange.StopLimit {
			_, er = this.ctp.CreateOrder(order.Symbol, side, openClose, order.Price, orderQty, triggerPrice, triggerDirection, reqCallbackFunc)
		} else if order.Type == exchange.Market {
			_, er = this.ctp.CreateMarketOrder(order.Symbol, side, openClose, orderQty, reqCallbackFunc)
		}
	}

	if todayQty > 0 {
		openClose = ctp.OffsetFlagCloseToday
		isToday = true
		er = nil
		if order.Type == exchange.Limit || order.Type == exchange.StopLimit {
			_, er = this.ctp.CreateOrder(order.Symbol, side, openClose, order.Price, todayQty, triggerPrice, triggerDirection, reqCallbackFunc)
		} else if order.Type == exchange.Market {
			_, er = this.ctp.CreateMarketOrder(order.Symbol, side, openClose, todayQty, reqCallbackFunc)
		}
	}

	return
}

func (this *GatewayServer) getPosition(instrumentID string, side exchange.OrderSide) (pos *ctp.PositionField) {
	key := fmt.Sprintf("%s_long", instrumentID)
	if side == exchange.OrderSideSell {
		key = fmt.Sprintf("%s_short", instrumentID)
	}
	ctpPositions, err := this.ctp.GetPositions()
	if err != nil {
		return
	}
	ctpPositions.Range(func(k string, v *ctp.PositionField) bool {
		if strings.EqualFold(k, key) {
			pos = v
			return false
		}
		return true
	})
	return
}

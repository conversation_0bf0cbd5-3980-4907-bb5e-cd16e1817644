package gateway

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path"
	"sync"
	"time"

	"github.com/wizhodl/goctp/ctp"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

type Storage struct {
	gateway         *GatewayServer
	KLineProductIDs []string // 允许跑 K线 的产品ID
	KLineSymbols    []string // 正在跑 K线 的品种，自动维护，不要手工设置
	TradingSymbols  []string // 允许交易的品种
	Orders          map[string]Orders
	logOrderLock    sync.Mutex // logOrder 函数的 lock
	lock            sync.Mutex // storage 的 lock
	ordersLock      sync.Mutex // orders 访问的 lock
}

func (this *Storage) SyncOrders() {
	ctpOrders, err := this.gateway.ctp.GetOrders()
	if err != nil {
		zlog.Errorf("sync orders get ctp orders err: %s", err)
		return
	}

	existOrderIDs := []string{}
	for _, orders := range this.Orders {
		for _, o := range orders {
			if o.IsOpen() {
				this.gateway.updateSuborders(o)
			}

			for _, subOrderID := range o.GetSubOrderIDs() {
				_, found := ctpOrders.Load(subOrderID)
				if found {
					existOrderIDs = append(existOrderIDs, subOrderID)
				}
			}
		}
	}
	this.Save()

	// 不在 storage 中的挂单报错
	unknownOrders := []*ctp.OrderField{}
	ctpOrders.Range(func(orderID string, of *ctp.OrderField) bool {
		// 仅处理 IsOpen 的订单
		if of.OrderStatus == ctp.OrderStatusAllTraded ||
			of.OrderStatus == ctp.OrderStatusPartTradedNotQueueing ||
			of.OrderStatus == ctp.OrderStatusNoTradeNotQueueing ||
			of.OrderStatus == ctp.OrderStatusCanceled ||
			of.OrderStatus == ctp.OrderStatusTouched {
			return true
		}
		if !SliceContains(existOrderIDs, orderID) {
			unknownOrders = append(unknownOrders, of)
		}
		return true
	})

	if len(unknownOrders) > 0 {
		msg := fmt.Sprintf("Gateway 本地不存在的 CTP 订单:\n```%s```", this.gateway.GetCtpOrdersSummary(unknownOrders))
		this.gateway.AlertMsgf(msg)
	}
}

// 查询远程订单状态，仅观察用
// simnow 10201 环境测试结果：
// 下午收盘后截止 6 点左右能查询到订单，但状态保存不变，如已触发仍是已触发，未触发仍是未触发；
// 到下午 6 点 ~ 晚上 8 点左右系统不可用，未登录状态无法查询
// 晚上 8 点系统可用之后，查询返回订单为空
func (this *Storage) QryOrdersTest() {
	for _, orders := range this.Orders {
		for _, order := range orders {
			for _, subOrderID := range order.GetSubOrderIDs() {
				subOrder := order.GetSubOrder(subOrderID)
				if subOrder.OrderSysID != "" {
					resOrder, err := this.gateway.ctp.QueryOrder(order.Symbol, subOrder.OrderSysID)
					if err != nil {
						zlog.Debugf("[QryOrdersTest]%s - %s err: %s", order.Symbol, subOrder.OrderSysID, err)
					} else if order == nil {
						zlog.Debugf("[QryOrdersTest]%s - %s got no err but order is nill")
					} else {
						zlog.Debugf("[QryOrdersTest]%s - %s order: %s, status: %v", order.Symbol, subOrder.OrderSysID, resOrder.GetOrderID(), resOrder.OrderStatus)
					}
				}
			}
		}
	}
}

func (this *Storage) UpdateOrder(subOrderID string, optionalInstrumentID string) (updated bool, order *Order, er error) {
	for instrumentID, orders := range this.Orders {
		if optionalInstrumentID == "" || (optionalInstrumentID != "" && instrumentID == optionalInstrumentID) {
			for _, o := range orders {
				if o.GetSubOrder(subOrderID) != nil {
					updated, er = this.gateway.updateSuborders(o)
					order = o
					if updated {
						this.Save()
					}
					return
				}
			}
		}
	}
	if order == nil {
		er = fmt.Errorf("order not found for ctp order (%s)", subOrderID)
	}
	return
}

func setupStorage(gateway *GatewayServer) (s *Storage, er error) {
	if gateway.opts == nil {
		er = fmt.Errorf("gateway option is nil, please init option before setup storage")
		return
	}
	// 检查 DirData 是否存在
	if _, err := os.Stat(gateway.opts.DataDir); os.IsNotExist(err) {
		er = err
		return
	}
	s = &Storage{
		gateway:         gateway,
		Orders:          map[string]Orders{},
		logOrderLock:    sync.Mutex{},
		lock:            sync.Mutex{},
		ordersLock:      sync.Mutex{},
		KLineProductIDs: []string{},
		KLineSymbols:    []string{},
		TradingSymbols:  []string{},
	}
	if err := s.Load(); err != nil {
		zlog.Errorf("read from storage failed (%s), error: %s", gateway.opts.DataDir, err)
	} else {
		zlog.Infof("read storage successful")
	}
	if len(s.KLineProductIDs) == 0 && len(gateway.opts.KLine.ProductIDs) > 0 {
		for _, pid := range gateway.opts.KLine.ProductIDs {
			if ctp.CheckValidProductID(pid) {
				s.AddProductID(pid)
				s.Save()
			}
		}
	}
	return
}

func (this *Storage) AddProductID(productID string) {
	index := -1
	for i, id := range this.KLineProductIDs {
		if id == productID {
			index = i
		}
	}
	if index == -1 {
		this.KLineProductIDs = append(this.KLineProductIDs, productID)
	}
	this.UpdateKLineSymbols()
}

func (this *Storage) UpdateKLineSymbols() {
	for _, productID := range this.KLineProductIDs {
		this.gateway.GetKLineSymbols(productID, func(validSymbols []string) {
			this.addKLineSymbols(validSymbols)
			this.Save()
		})
	}
}

func (this *Storage) addKLineSymbols(symbols []string) {
	for _, symbol := range symbols {
		if !SliceContains(this.KLineSymbols, symbol) {
			this.KLineSymbols = append(this.KLineSymbols, symbol)
		}
	}
}

func (this *Storage) RemoveProductID(productID string) bool {
	index := -1
	for i, id := range this.KLineProductIDs {
		if id == productID {
			index = i
		}
	}
	if index > -1 {
		this.KLineProductIDs = append(this.KLineProductIDs[:index], this.KLineProductIDs[index+1:]...)
		this.UpdateKLineSymbols()
		return true
	}
	return false
}

func (this *Storage) addOrder(order *Order) {
	this.ordersLock.Lock()
	defer this.ordersLock.Unlock()

	if _, found := this.Orders[order.Symbol]; !found {
		this.Orders[order.Symbol] = []*Order{}
	}
	this.Orders[order.Symbol] = append(this.Orders[order.Symbol], order)
	this.Save()
	this.logOrder(order)
}

func (this *Storage) setOrderCanceled(o *Order) {
	if o.IsCanceled() {
		return
	}
	nowTime := time.Now()
	newStatus := exchange.OrderStatusCancelled
	if o.ExecQty != 0 {
		newStatus = exchange.OrderStatusPartialCancelled
	}

	err := o.ChangeStatus(newStatus)
	if err != nil {
		zlog.Errorf("cancel order, change order status failed, (%s), error: %s", o.OrderID, err)
		return
	}
	o.UpdateTime = &nowTime
	this.Save()
	this.logOrder(o)
}

func (this *Storage) setSubOrderCanceled(order *Order) {
	updated := false
	for _, subOrder := range order.SubOrders {
		if subOrder.CtpOrder == nil {
			continue
		}
		ctpOrder := convertOrder(subOrder.CtpOrder)
		if ctpOrder.IsOpen() {
			subOrder.IsCanceled = true
			updated = true
		}
	}

	if !updated {
		return
	}

	nowTime := time.Now()
	order.UpdateTime = &nowTime
	this.Save()
	this.logOrder(order)
}

func (this *Storage) Load() error {
	this.lock.Lock()
	defer this.lock.Unlock()

	path := this.getPath()
	f, err := os.OpenFile(path, os.O_RDWR|os.O_CREATE, 0755)
	if err != nil {
		return err
	}
	defer f.Close()

	if content, err := io.ReadAll(f); err != nil {
		return err
	} else {
		if err := json.Unmarshal(content, this); err != nil {
			return err
		}
	}
	instrumentTypeMigrated := this.FixOrderInstrumentType()
	if instrumentTypeMigrated {
		zlog.Infof("migrate instrument type successful, save to storage")
		this.Save()
	}
	return nil
}

func (this *Storage) FixOrderInstrumentType() (migrated bool) {
	for _, orders := range this.Orders {
		for _, order := range orders {
			oldType := order.InstrumentType
			order.InstrumentType = exchange.FixInstrumentType(order.InstrumentType)
			if order.InstrumentType != oldType {
				migrated = true
			}
		}
	}
	return migrated
}

func (this *Storage) getPath() string {
	return path.Join(this.gateway.opts.DataDir, "storage.json")
}

func (this *Storage) Save() error {
	this.lock.Lock()
	defer this.lock.Unlock()

	path := this.getPath()
	if data, err := json.MarshalIndent(this, "", "    "); err != nil {
		return err
	} else {
		err := os.WriteFile(path, data, 0755)
		return err
	}
}

// 记录 Order 日志
func (this *Storage) logOrder(o *Order) {
	this.logOrderLock.Lock()
	defer this.logOrderLock.Unlock()

	if o == nil {
		zlog.Errorf("log order failed, order is nil")
		return
	}

	nowTime := time.Now()
	archivePath := fmt.Sprintf("orders_%s_%d%02d.json", o.Symbol, nowTime.Year(), nowTime.Month())
	archivePath = path.Join(this.gateway.opts.DataDir, archivePath)
	f, err := os.OpenFile(archivePath, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0755)
	if err != nil {
		zlog.Errorf("open log order file err: %s", err)
		return
	}
	defer f.Close()

	if data, err := json.Marshal(o); err != nil {
		zlog.Errorf("save order log failed, marshal error: %s", err)
		return
	} else {
		f.Write(data)
		_, err := f.WriteString("\n")
		if err != nil {
			zlog.Errorf("log order, write file error: %s", err)
		}
	}
	// 发送订单更新
	go this.gateway.sendPacket(exchange.NewOrderPacket("", &o.Order))
}

func (this *Storage) ArchiveOrders() {
	if !this.gateway.InstrumentsReady.Load() {
		return
	}

	ctpInstruments, err := this.gateway.ctp.GetInstruments()
	if err != nil {
		return
	}

	// 满足以下条件之一的订单归档
	// symbol 已过期
	// 非挂单状态 && 更新时间早于倒数第三根 K 线以前
	now := time.Now()
	oneMonthAgo := now.Add(-time.Hour * 24 * 31)
	orderIDsCanArchive := map[string][]string{}
	for symbol, orders := range this.Orders {
		symbolExpired := true
		orderExpiredTime := oneMonthAgo
		if ins, found := ctpInstruments.Load(symbol); found {
			// 查不到默认已过期
			if ins.DeliveryYear > now.Year() {
				symbolExpired = false
			}
			if ins.DeliveryYear == now.Year() && ins.DeliveryMonth > int(now.Month()) {
				symbolExpired = false
			}

			klines, err := this.gateway.getCleanedKlines(ins.ExchangeID, symbol, 100)
			if err == nil && len(klines) >= 3 {
				orderExpiredTime = time.Unix(klines[len(klines)-3].Time, 0)
			} else {
				this.gateway.AlertMsgf("Storage 归档订单，通过 Kline 获取日历出错。kline count: %d, error: %s", len(klines), err)
			}
		}

		for _, order := range orders {
			if !symbolExpired {
				if order.IsOpen() {
					break
				}
				if order.UpdateTime.After(orderExpiredTime) {
					break
				}
			}

			_, found := orderIDsCanArchive[symbol]
			if !found {
				orderIDsCanArchive[symbol] = []string{order.OrderID}
			} else {
				orderIDsCanArchive[symbol] = append(orderIDsCanArchive[symbol], order.OrderID)
			}
		}

	}

	archivePath := path.Join(this.gateway.opts.DataDir, "orders_archived.json")
	f, err := os.OpenFile(archivePath, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0755)
	if err != nil {
		zlog.Errorf("open order archive file err: %s", err)
		return
	}
	defer f.Close()

	this.ordersLock.Lock()
	defer this.ordersLock.Unlock()

	for symbol, orders := range this.Orders {
		orderIDs, found := orderIDsCanArchive[symbol]
		if !found {
			continue
		}
		for i := len(orders) - 1; i >= 0; i-- {
			order := orders[i]
			if !SliceContains(orderIDs, order.OrderID) {
				continue
			}

			// 归档
			if data, err := json.Marshal(order); err != nil {
				zlog.Errorf("archive order failed, marshal error: %s", err)
				return
			} else {
				f.Write(data)
				_, err := f.WriteString("\n")
				if err != nil {
					zlog.Errorf("archive order write file error: %s", err)
					return
				}
			}

			// storage 中删除
			this.Orders[symbol] = append(this.Orders[symbol][:i], this.Orders[symbol][i+1:]...)
		}
		this.Save()
	}
}

package gateway

import (
	"bufio"
	"context"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/wizhodl/goctp/ctp"
	"github.com/wizhodl/quanter/common/zlog"
)

func (this *GatewayServer) updateKlinesWithTianqin() {
	this.updateSymbolKlinesWithTianqin(this.storage.KLineSymbols)
}

var tianqinScriptLock = sync.Mutex{}

func (this *GatewayServer) updateSymbolKlinesWithTianqin(symbols []string) {
	if !this.opts.KLine.EnableTianqin {
		zlog.Errorf("update klines with tianqin disabled")
		return
	}

	go func() {
		if !tianqinScriptLock.TryLock() {
			zlog.Errorf("update klines with tianqin failed, another update is running")
			return
		}
		defer tianqinScriptLock.Unlock()

		for !this.InstrumentsReady.Load() {
			time.Sleep(1 * time.Second)
		}

		limit := "200"

		symbolArgs := []string{}
		for _, period := range []string{"1day", "1hour"} {
			if period == "1hour" {
				limit = "2000"
			}
			for _, instrumentID := range symbols {
				if _, err := this.GetInstrument(instrumentID); err != nil {
					zlog.Errorf("update klines with tianqian, instument id not found (%s)", instrumentID)
				} else {
					symbolArgs = append(symbolArgs, fmt.Sprintf("%s.%s:%s:%s", ctp.GetExchangeID(instrumentID), instrumentID, period, limit))
				}
			}
		}
		if len(symbolArgs) > 0 {
			pythonPath := this.opts.PythonPath
			if pythonPath == "" {
				pythonPath = "python"
			}
			commandArgs := []string{
				pythonPath,
				this.opts.Tianqin.Script,
				fmt.Sprintf("127.0.0.1%s", this.opts.HTTPAddr),
				this.opts.Tianqin.Account,
				this.opts.Tianqin.Password,
				SliceStringJoin(symbolArgs, ",", false),
			}
			if this.opts.Ctp.AppID == "simnow_client_test" && this.opts.KLine.TianqinSimnow {
				commandArgs = []string{
					pythonPath,
					this.opts.Tianqin.Script,
					fmt.Sprintf("127.0.0.1%s", this.opts.HTTPAddr),
					this.opts.Tianqin.Account,
					this.opts.Tianqin.Password,
					SliceStringJoin(symbolArgs, ",", false),
					fmt.Sprintf("%s::%s", this.opts.Ctp.InvestorID, this.opts.Ctp.Password),
				}
			}

			cmdStr := SliceStringJoin(commandArgs, " ", false)
			startUpTime := 15
			downloadTime := 4
			uploadTime := 2
			waitTime := 4
			timeout := startUpTime + len(symbols)*3*(downloadTime+uploadTime+waitTime)

			zlog.Debugf("update klines with tianqin, symbols: %s, command: %s, timeout: %d", strings.Join(symbols, ","), cmdStr, timeout)

			if err := execute(cmdStr, timeout); err != nil {
				zlog.Errorf("upload klines with tianqin failed, symbols: %s, error: %s", strings.Join(symbols, ","), err)
			} else {
				for _, instrumentID := range symbols {
					this.klineUpdateTimes.Store(instrumentID, time.Now())
				}
				this.archiveKlines(symbols, "Upload")
				zlog.Infof("upload klines with tianqin success, symbols: %s", strings.Join(symbols, ","))
			}
		} else {
			zlog.Errorf("update klines with tianqin failed, no symbol to update")
		}
	}()
}

func execute(cmd string, timeoutSecond int) (err error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeoutSecond)*time.Second)
	defer cancel()

	if cmd == "" {
		return errors.New("no command provided")
	}

	cmdArr := strings.Split(cmd, " ")
	name := cmdArr[0]

	args := []string{}
	if len(cmdArr) > 1 {
		args = cmdArr[1:]
	}

	command := exec.CommandContext(ctx, name, args...)
	command.Env = os.Environ()

	stdout, err := command.StdoutPipe()
	if err != nil {
		zlog.Errorf("failed creating command stdoutpipe: ", err)
		return err
	}
	defer stdout.Close()
	stdoutReader := bufio.NewReader(stdout)

	stderr, err := command.StderrPipe()
	if err != nil {
		zlog.Errorf("failed creating command stderrpipe: ", err)
		return err
	}
	defer stderr.Close()
	stderrReader := bufio.NewReader(stderr)

	if err := command.Start(); err != nil {
		zlog.Errorf("failed starting command: ", err)
		return err
	}

	go handleReader(stdoutReader)
	go handleReader(stderrReader)

	if err := command.Wait(); err != nil {
		if exiterr, ok := err.(*exec.ExitError); ok {
			if status, ok := exiterr.Sys().(syscall.WaitStatus); ok {
				zlog.Debugf("command exit status: %v", status.ExitStatus())
				return err
			}
		}
		return err
	}
	return nil
}

func handleReader(reader *bufio.Reader) {
	for {
		str, err := reader.ReadString('\n')
		if err != nil {
			break
		}
		zlog.Debugf(str)
	}
}

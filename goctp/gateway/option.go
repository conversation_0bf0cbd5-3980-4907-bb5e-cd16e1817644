package gateway

import (
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"time"

	"github.com/wizhodl/goctp/def"
	"github.com/wizhodl/quanter/secrets"

	"github.com/onrik/logrus/filename"
	log "github.com/sirupsen/logrus"
	yaml "gopkg.in/yaml.v2"
)

type Logger struct {
	Level string
	Skip  int
	Dir   string
}

type KLineOpt struct {
	EnableTianqin  bool `yaml:"enable_tianqin"`
	TianqinSimnow  bool `yaml:"tianqin_simnow"`
	Period         Period
	PeriodTmpl     map[string]string `yaml:"period_tmpl"`
	Dir            string
	GenPeriods     []string
	Periods        Periods
	ProductIDs     []string `yaml:"product_ids"`
	UpdateInternal int      `yaml:"update_interval"`
	BatchSize      int      `yaml:"batch_size"`
}

type Period struct {
	Exchanges []string
	exchanges map[string]bool
	WeekIndex int64 `yaml:"week_index"`
	DayIndex  int64 `yaml:"day_index"`
}

func (p Period) HasExchange(exchange string) bool {
	if p.exchanges == nil {
		p.exchanges = make(map[string]bool)
		for _, ex := range p.Exchanges {
			p.exchanges[strings.ToUpper(ex)] = true
		}
	}
	return p.exchanges[strings.ToUpper(exchange)]
}

func (p Period) Index(period string) int64 {
	period = strings.ToLower(period)
	if strings.HasSuffix(period, "week") {
		return p.WeekIndex
	} else if strings.HasSuffix(period, "day") {
		return p.DayIndex
	}
	return 0
}

type Periods []Period

// 目前K线都是根据交易所为标准的
func (ps Periods) IndexTime(exchange, period string, startAt int64, intervals ...int64) int64 {
	seconds := def.Periods[period]
	var index int64
	if seconds >= 76400 {
		for _, p := range ps {
			if p.HasExchange(exchange) {
				index = p.Index(period)
				break
			}
		}
	}
	ts := (startAt-index)/seconds*seconds + index
	for _, interval := range intervals {
		ts += interval * seconds
	}
	return ts
}

// 校准交易所时间
func (ps Periods) PoofTime(exchange, period string, timestamp int64) int64 {
	return ps.IndexTime(exchange, period, timestamp)
}

type CtpOpt struct {
	RemoteLaunch     bool   `yaml:"remote_launch"`
	QuoteFront       string `yaml:"quote_front"`
	TradeFront       string `yaml:"trade_front"`
	BrokerID         string `yaml:"broker_id"`
	InvestorID       string `yaml:"investor_id"`
	Password         string `yaml:"password"`
	AppID            string `yaml:"app_id"`
	AuthCode         string `yaml:"auth_code"`
	AuthSecret       string `yaml:"auth_secret"`
	EncryptedSecrets string `yaml:"encrypted_secrets"`
}

type WebsocketOpt struct {
	FlushInterval int `yaml:"flush_interval"`
}

type TianqinOpt struct {
	Script   string `yaml:"script"`
	Account  string `yaml:"account"`
	Password string `yaml:"password"`
}

type APIKey struct {
	APIKey             string               `yaml:"api_key"`
	EncryptedAPISecret string               `yaml:"encrypted_api_secret"`
	APISecret          secrets.SecretString `yaml:"api_secret"`
}

type APIKeys []APIKey

type Option struct {
	ID               string `yaml:"id"`
	Debug            bool   `yaml:"debug"`
	PythonPath       string `yaml:"python_path"`
	SlackChannel     string `yaml:"slack_channel"`
	CheckSign        bool   `yaml:"check_sign"`
	HTTPAddr         string `yaml:"http_addr"`
	Logger           Logger
	KLine            KLineOpt
	APIKeys          APIKeys `yaml:"api_keys"`
	Ctp              CtpOpt  `yaml:"ctp"`
	Websocket        WebsocketOpt
	DataDir          string `yaml:"data_dir"`
	Tianqin          TianqinOpt
	AutoCloseDays    int    `yaml:"auto_close_days"` // 持仓品种过期前多少天内自动平仓
	ReleaseBinaryDir string `yaml:"release_binary_dir"`
}

func NewOption() *Option {
	return &Option{
		ID:       "ctpgateway",
		Debug:    false,
		HTTPAddr: ":8204",
		DataDir:  "./data",
		Logger: Logger{
			Dir:   "./log",
			Level: "debug",
			Skip:  5,
		},
		KLine: KLineOpt{
			Dir:        "./data",
			GenPeriods: []string{"1hour", "1day"},
			PeriodTmpl: map[string]string{
				"1min":  "{exchange}/1min-{year}-{month}.dat",
				"1hour": "{exchange}/1hour-1day-{year}.dat",
				"1day":  "{exchange}/1hour-1day-{year}.dat",
			},
			UpdateInternal: 3,
			BatchSize:      500,
		},
		Websocket: WebsocketOpt{
			FlushInterval: 1000, // milliseconds
		},
	}
}

// 获取单个文件位置
func (opts *Option) PathByTime(header def.KLineHeader, timestamp int64) string {
	ktime := time.Unix(timestamp, 0)
	r := strings.NewReplacer(
		"{exchange}", header.Exchange, "{symbol}", header.Symbol, "{period}", header.Period,
		"{year}", fmt.Sprint(ktime.Year()), "{month}", fmt.Sprint(int(ktime.Month())),
		"{quarter}", fmt.Sprint(int(ktime.Month()-1)/3+1),
	)
	tmpl := opts.KLine.PeriodTmpl[header.Period]
	filename := filepath.Join(opts.KLine.Dir, r.Replace(tmpl))
	dirname := filepath.Dir(filename)
	if _, err := os.Stat(dirname); err != nil {
		os.MkdirAll(dirname, 0755)
	}
	return filename
}

// 获取一段时间范围的文件
func (opts *Option) PathByRange(header def.KLineHeader, startAt, endAt int64) (paths []string) {
	var exists = make(map[string]bool)
	startTime, endTime := time.Unix(startAt, 0), time.Unix(endAt, 0)
	endTime = endTime.AddDate(0, 1, 0).Add(-time.Minute)

	r := strings.NewReplacer("{exchange}", header.Exchange, "{symbol}", header.Symbol, "{period}", header.Period)
	for t := startTime; endTime.Sub(t) > 0; t = t.AddDate(0, 0, 10) {
		rr := strings.NewReplacer("{year}", fmt.Sprint(t.Year()), "{month}", fmt.Sprint(int(t.Month())), "{quarter}", fmt.Sprint(int(t.Month()-1)/3+1))
		tmpl := opts.KLine.PeriodTmpl[header.Period]
		filename := filepath.Join(opts.KLine.Dir, r.Replace(rr.Replace(tmpl)))

		if _, ok := exists[filename]; !ok {
			paths = append(paths, filename)
			dirname := filepath.Dir(filename)
			if _, err := os.Stat(dirname); err != nil {
				os.MkdirAll(dirname, 0755)
			}
			exists[filename] = true
		}
	}
	return
}

func (opts *Option) Load(path string) error {
	file, err := os.Open(path)
	if err != nil {
		return err
	}
	err = yaml.NewDecoder(file).Decode(opts)
	if err != nil {
		file.Close()
		return err
	}
	file.Close()

	level, err := log.ParseLevel(opts.Logger.Level)
	if err != nil {
		return err
	}
	log.SetLevel(level)
	filenameHook := filename.NewHook()
	filenameHook.Field = "source"
	filenameHook.Skip = opts.Logger.Skip
	log.AddHook(filenameHook)
	return nil
}

func (opt *Option) Print() {
	s := reflect.ValueOf(opt).Elem()
	typeOfT := s.Type()

	for i := 0; i < s.NumField(); i++ {
		f := s.Field(i)
		log.WithField(typeOfT.Field(i).Name, f.Interface()).Info("option")
	}
}

func (opt *Option) getAPISecret(apiKey string) (apiSecret secrets.SecretString) {
	for _, key := range opt.APIKeys {
		if key.APIKey == apiKey {
			return key.APISecret
		}
	}
	return ""
}

package gateway

import (
	"fmt"
	"math"
	"strings"
	"sync"
	"time"

	"github.com/wizhodl/goctp/ctp"
	"github.com/wizhodl/goctp/def"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

/* Gateway 的 CTP 回调函数 */

var onOrderLocks = map[string]*sync.Mutex{}

func (this *GatewayServer) onOrder(of *ctp.OrderField) {
	zlog.Debugf("<Order>: %#v", of)

	// 根据新的订单状态，更新条件单； 每个订单锁住，顺序处理
	// 这样做的好处是可以较长时间的保存条件单，因为 ctp.trade.Orders 中可能只有较近的订单
	// TODO: 需要做一个 AppendOnly 的 Order 归档，这样可以放心的清理 StopLimitOrders
	ctpOrderID := fmt.Sprintf("%d_%d_%s", of.FrontID, of.SessionID, of.OrderRef)
	lock := &sync.Mutex{}
	if l, found := onOrderLocks[ctpOrderID]; !found {
		onOrderLocks[ctpOrderID] = lock
	} else {
		lock = l
	}
	lock.Lock()
	defer lock.Unlock()

	this.updateOrderOnOrder(of)
}

func (this *GatewayServer) updateOrderOnOrder(of *ctp.OrderField) *Order {
	// 更新关联 CtpOrderID 的条件单的数据和状态
	updated, order, err := this.storage.UpdateOrder(of.GetOrderID(), of.InstrumentID)
	if err != nil {
		zlog.Errorf("on order, update order failed, error: %s", err)
	}
	// 此处没必要 sendPacket(order)，因为在 logOrder 处会统一 sendPacket
	if updated {
		this.storage.logOrder(order)
	}
	return order
}

func (this *GatewayServer) onOrderCancel(of *ctp.OrderField) {
	zlog.Debugf("<OrderCancel>: %#v", of)
	this.updateOrderOnOrder(of)
}

func (this *GatewayServer) onErrOrder(of *ctp.OrderField, info *ctp.RspInfoField) {
	zlog.Debugf("<ErrOrder>: %#v, %#v", of, info)
	this.updateOrderOnOrder(of)
}

// CTP 行情回调，更新 KLine 和触发条件单
// 这个函数调用会非常频繁，需要关注性能；XC 的虚拟机上的处理时间约为 500ns
func (this *GatewayServer) onTick(tick *ctp.TickField) {
	now := time.Now()
	this.ctp.LastTickTime = &now

	counter, _ := this.tickCounter.Load(tick.InstrumentID)

	// 测量 onTick 的开销
	// startTime := time.Now()
	// defer zlog.Debugf("onTick process time: %s", time.Since(startTime))

	// zlog.Debugf("<tick> %v", tick)
	if this.InstrumentsReady.Load() {
		// 只有在 storage.KLineSymbols 中的品种才可以计算 K线
		if !this.opts.KLine.EnableTianqin && this.CheckKLineAllowed(tick.InstrumentID) {
			// 计算分钟 K 线并更新
			currentKLine := this.updateMinuteKLineFromTick(tick)
			zlog.Debugf("current kline: %v", currentKLine)
		}

		// 更新 instrument 的最新价格
		// 仅更新正在交易的 Instrument
		isTrading := this.IsTrading(tick.InstrumentID)
		if isTrading {
			instrument, err := this.GetInstrument(tick.InstrumentID)
			if err == nil {
				this.UpdateInstrumentWithTick(instrument, tick)
				// 检查触发单
				this.triggerOrders(instrument)
			}
		} else if this.CheckKLineAllowed(tick.InstrumentID) {
			// 对于不是正在交易的品种，因为要判断是否展期，也需要过一段时间更新 instrument 的 volume
			// 每隔 1200次(约十分钟) onTick 更新一次 instrument，debug 约为1分钟更新一次
			var modNum int64 = 1200
			if this.opts.Debug {
				modNum = 120
			}
			if counter%modNum == 0 {
				instrument, err := this.GetInstrument(tick.InstrumentID)
				if err == nil {
					this.UpdateInstrumentWithTick(instrument, tick)
					zlog.Debugf("update instrument with tick for watchlist symbol: %s", tick.InstrumentID)
				}
			}
		}
	}
	this.tickCounter.Store(tick.InstrumentID, counter+1)
}

func (this *GatewayServer) onInstrumentStatus(status *ctp.InstrumentStatus) {
	// zlog.Debugf("<InstrumentStatus> %#v", status)
	if this.InstrumentsReady.Load() {
		this.Instruments.Range(func(key string, instrument *Instrument) bool {
			if strings.EqualFold(instrument.UnderlyCurrency, status.InstrumentID) {
				this.updateInstrumentStatus(instrument, status)
				// Status 发生变化后，实时推送 Instrument
				if this.IsTrading(status.InstrumentID) {
					packet := exchange.NewInstrumentPacket("", instrument)
					this.sendPacket(packet)
				}
			}
			return true
		})
	}
}

func (this *GatewayServer) onInstrumentMarginRate(mr *ctp.InstrumentMarginRate) {
	zlog.Debugf("<InstrumentMarginRate> %#v", mr)
	if !this.InstrumentsReady.Load() {
		return
	}
	this.Instruments.Range(func(k string, instrument *Instrument) bool {
		if instrument.Symbol == mr.InstrumentID {
			instrument.LongMarginRatioByMoney = mr.LongMarginRatioByMoney
			instrument.LongMarginRatioByVolume = mr.LongMarginRatioByVolume
			instrument.ShortMarginRatioByMoney = mr.ShortMarginRatioByMoney
			instrument.ShortMarginRatioByVolume = mr.ShortMarginRatioByVolume
			instrument.UpdateTime = time.Now()
			return false
		}
		return true
	})
}

func (this *GatewayServer) onQuoteLogin(login *ctp.RspUserLoginField, info *ctp.RspInfoField) {
	if info.ErrorID == 0 {
		now := time.Now()
		this.ctp.QuoteLoginTime = &now

		this.GetKLineSymbols("all", func(symbols []string) {
			this.subscribe(symbols)
		})
	}
	time.AfterFunc(3*time.Second, func() {
		msg := ""
		if info.ErrorID == 0 {
			msg = fmt.Sprintf("CTP Quote 启动成功。")
		} else {
			if !SliceContains([]int{7, 8}, info.ErrorID) {
				msg = fmt.Sprintf("CTP Quote 启动失败: [%d]%s", info.ErrorID, info.ErrorMsg)
			}
		}
		if msg != "" {
			this.sendPacket(exchange.NewSlackResponsePacket("", this.opts.SlackChannel, msg))
		}
	})
	// 如果是盘前还没有初始化，稍后重新初始化
	// [7]CTP:还没有初始化 , [8]CTP:前置不活跃
	if SliceContains([]int{7, 8}, info.ErrorID) && this.ctp.Quote != nil {
		time.AfterFunc(10*time.Second, func() {
			this.ctp.LoginQuote(this.opts.Ctp.InvestorID, this.opts.Ctp.Password, this.opts.Ctp.BrokerID)
		})
	}
}

func (this *GatewayServer) onTradeLogin(login *ctp.RspUserLoginField, info *ctp.RspInfoField) {
	zlog.Debugf("trade on log in: %#v", info)
	// 登录成功后需要立即缓存 instruments
	if info.ErrorID == 0 {
		go this.cacheInstruments()
	}

	// 稍后发送启动成功和失败的消息
	time.AfterFunc(3*time.Second, func() {
		if info.ErrorID != 0 {
			// [7]CTP:还没有初始化 , [8]CTP:前置不活跃
			// [3]CTP: 不合法的登录，看起来是密码错误，其实并不是这个问题，有待调查
			if !SliceContains([]int{7, 8}, info.ErrorID) {
				msg := fmt.Sprintf("CTP Trade 启动失败：[%d] %s", info.ErrorID, info.ErrorMsg)
				this.AlertMsgf(msg)
				return
			}
		}
		this.SendMsgf("CTP Trade 启动成功。")
	})

	// 如果是盘前还没有初始化，稍后重新初始化
	// [7]CTP:还没有初始化 , [8]CTP:前置不活跃
	if SliceContains([]int{7, 8}, info.ErrorID) && this.ctp.Trade != nil {
		time.AfterFunc(10*time.Second, func() {
			this.ctp.LoginTrade(this.opts.Ctp.InvestorID, this.opts.Ctp.Password, this.opts.Ctp.BrokerID, this.opts.Ctp.AppID, this.opts.Ctp.AuthCode)
		})
	}
}

func (this *GatewayServer) onErrOrderAction(orderID string, requestID int, info *ctp.RspInfoField) {
	// 取消订单失败回调时，发送“取消订单失败的信号”
	// 但是取消订单失败的原因也可能是 ”[26] CTP:报单已全部成交或已撤销，不能再撤“
	// 这种情况说明订单其实已经成功取消，需要更新订单状态，并返回“取消订单成功的信号”
	if updated, order, err := this.storage.UpdateOrder(orderID, ""); err != nil {
		zlog.Errorf("err order action, update order failed, error: %s", err)
	} else {
		if updated {
			this.storage.logOrder(order)
		}
	}
}

func (this *GatewayServer) updateMinuteKLineFromTick(tick *ctp.TickField) (result *def.KLine) {
	t := ctp.ParseTimeBeijing(fmt.Sprintf("%sT%s", tick.TradingDay, tick.UpdateTime))
	if t == nil {
		return
	}

	timestamp := t.UTC().Unix()
	timeRoundToMinute := timestamp - timestamp%60

	exchangeID := tick.ExchangeID

	// 一根新的 k 线
	freshNewKLine := &def.KLine{
		KLineHeader: def.KLineHeader{
			Exchange: exchangeID,
			Symbol:   tick.InstrumentID,
			Period:   "1min",
		},
		Open:        tick.LastPrice,
		High:        tick.LastPrice,
		Low:         tick.LastPrice,
		Volume:      0,
		QuoteVolume: 0,
		Time:        timeRoundToMinute,
	}

	key := fmt.Sprintf("%s.%s", exchangeID, tick.InstrumentID)
	if k, loaded := this.minuteKLines.Load(key); loaded {
		// 时间超过 1 分钟，将 k 线写入到 klineChan 中
		if t.Sub(time.Unix(k.Time, 0)) > time.Second*60 {
			this.klineChan <- k
			result = k
			// 将之前的 K 线发送出去后，存一个新的 KLine
			this.minuteKLines.Store(key, freshNewKLine)
			// 每隔一分钟重新设置 lastVolume
			this.minuteKLinesFirstVolume.Store(key, float64(tick.Volume))
			zlog.Debugf("new minute, store fresh kline")
		} else {
			if tick.LastPrice > k.High {
				k.High = tick.LastPrice
			}
			if tick.LastPrice < k.Low {
				k.Low = tick.LastPrice
			}
			k.Close = tick.LastPrice
			if lastVolume, ok := this.minuteKLinesFirstVolume.Load(key); ok {
				k.Volume = math.Max(float64(tick.Volume)-lastVolume, 0)
			}
			result = k
		}
		zlog.Debugf("update current minute kline value")
	} else {
		this.minuteKLines.Store(key, freshNewKLine)
		result = freshNewKLine
		this.minuteKLinesFirstVolume.Store(key, float64(tick.Volume))
		zlog.Debugf("new fresh kline")
	}
	return
}

package gateway

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

var upgrader = websocket.Upgrader{
	EnableCompression: true, // 设置为 true 时，当消息中有特殊字符时，会导致 writeJSON 报错
}

func (this *GatewayServer) websocketHandler(ctx *gin.Context) {
	ws := &WebsocketWithLock{
		gateway: this,
		Mutex:   sync.Mutex{},
	}

	conn, err := upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		zlog.Errorf("error setup websocket connection, error: %s", err)
		return
	} else {
		ws.Conn = conn
	}

	// 如果 chan 太小，发送的 Instrument 太多，可能会收不到消息
	msgChan := make(chan *exchange.Packet, 20000)
	closeChan := make(chan struct{})

	apiKey := ""
	remoteAddr := ws.RemoteAddr()
	this.connMap.Store(remoteAddr, msgChan)

	defer func() {
		close(closeChan)
		close(msgChan)
		this.connMap.Delete(remoteAddr)
		ws.Close()
	}()

	go func() {
		for {
			select {
			case <-closeChan:
				return
			case packet := <-msgChan:
				// 判断是否订阅，如果订阅了就发送数据；如果没订阅啥都不做
				if ok := ws.IsKeySubscribed(packet.Key); ok {
					// 可能有并发写导致崩溃的问题，在 sendPacket 处进行并发控制
					if isInstrument, _ := packet.CheckInstrument(); isInstrument {
						packet.SetID()
					}
					// 广播消息不自带 APIKey，必须设为客户端的 APIKey
					packet.APIKey = apiKey
					err = ws.WriteJSON(packet)

					if err != nil {
						zlog.Errorf("write packet failed, error: %s, packet: %s", err, packet.PacketHeader)
					}
				} else {
					// zlog.Debugf("packet (%s) isn't subscribed in keys: (%s)", packet.Key, SliceStringJoin(subscribedKeys, ",", false))
				}
			}
		}
	}()

	for {
		select {
		case <-closeChan:
			return
		default:
		}

		var packet = &exchange.ClientPacket{}
		if err := ws.ReadJSON(packet); err != nil {
			zlog.Errorf("read incoming response packet failed, error: %s", err)
			if strings.Contains(err.Error(), "unexpected EOF") {
				return
			} else {
				continue
			}
		}
		zlog.Debugf("received message: [%s]", packet.PacketHeader.String())

		if this.opts.CheckSign && !packet.CheckSign(this.opts.getAPISecret(packet.APIKey)) {
			zlog.Errorf("check incoming signature failed, %s", &packet.PacketHeader)
			packet := exchange.NewCheckSignErrorPacket(packet)
			ws.WriteJSON(packet)
			continue
		}

		var responsePacket *exchange.Packet
		if packet.CheckPing() {
			responsePacket = exchange.NewPongPacket(packet)
		} else if yes, key := packet.CheckSubscribe(); yes {
			// 订阅的channel
			// Order 的 key: {InstrumentID}.orders
			// Instrument 的 key: {InstrumentID}.instruments
			// Margin 的 key: margin
			// 如果需要订阅所有: all
			// 如果订阅 InstrumentID 的所有: {InstrumentID}
			// 订阅 Instrument 的 order: {InstrumentID}.orders
			// 订阅所有的 order:  orders
			if key != "" {
				// 如果 key 看起来是 instrumentID，尝试启用该 instrumentID
				instrumentID := strings.Split(key, ".")[0]
				// 生产环境必须验证 InstrumentReady
				if !this.Debug {
					if this.ctp.IsInstrumentsReady() {
						if _, err := this.GetInstrument(instrumentID); err != nil {
							responsePacket = exchange.NewSubscribeFailPacket(packet, key, fmt.Sprintf("get instrument info failed, error: %s", err))
						}
					} else {
						responsePacket = exchange.NewSubscribeFailPacket(packet, key, "instrument not ready")
					}
				}
				if responsePacket == nil && key != "margin" {
					if err := this.EnableTrading(instrumentID); err != nil {
						zlog.Errorf("error enable trading for key: %s", key)
						responsePacket = exchange.NewSubscribeFailPacket(packet, key, fmt.Sprintf("enable trading failed, error: %s", err))
					}
				}
				// 如果订阅没有失败，发送订阅成功的 packet
				if responsePacket == nil {
					responsePacket = exchange.NewSubscribeSuccessPacket(packet, key)
					this.AddSubscribeKey(remoteAddr, key)
					apiKey = packet.APIKey
				}
			} else {
				responsePacket = exchange.NewSubscribeFailPacket(packet, key, "key can not be empty")
			}
		} else if yes, key := packet.CheckSlackSubscribe(); yes {
			// 如果是 slack 适配器的订阅请求，检查配置的 slackChannel 是否一致
			// 另外，如果 ctp 服务没有启动，发送 slack 消息提示启动 ctp 服务
			if key == fmt.Sprintf("%s.slack", this.opts.SlackChannel) {
				// 关联 slackChannel 对应的 websocket 地址；后续用于查询 slackChannel 对应的 websocket 连接
				this.slackConnAddr = remoteAddr
				if this.Launched.Load() {
					// slack subscribe 可能还没有完成，需要延迟一点发送状态
					time.AfterFunc(2*time.Second, func() {
						this.commandProcessor.Process("status", []string{})
					})
				} else {
					channelName := strings.Split(key, ".")[0]
					msg := fmt.Sprintf("Build: %s, 请输入命令 `.launch Password GoogleAuthCode safe[可选]` 启动程序", this.BuildInfo())
					askForLaunchPacket := exchange.NewSlackResponsePacket(packet.APIKey, channelName, msg)
					ws.WriteJSON(askForLaunchPacket)
				}
			} else {
				responsePacket = exchange.NewSubscribeFailPacket(packet, key, "you are not allowed to subscribe this gateway")
			}
			// 如果 slack 订阅没有失败，发送订阅成功的 packet
			if responsePacket == nil {
				responsePacket = exchange.NewSubscribeSuccessPacket(packet, key)
				this.AddSubscribeKey(remoteAddr, key)
				apiKey = packet.APIKey
			}
		} else if yes, key := packet.CheckUnsubscribe(); yes {
			if key != "" {
				this.RemoveSubscribeKey(remoteAddr, key)
				// 如果是 slack 适配器取消订阅的请求，重置 slackChannel 对应的 websocket 连接
				if strings.HasSuffix(key, "slack") {
					this.slackConnAddr = ""
				}
				responsePacket = exchange.NewUnsubscribeSuccessPacket(packet, key)
			} else {
				responsePacket = exchange.NewUnsubscribeFailPacket(packet, key, "key can not be empty")
			}
		} else if yes, channelName := packet.CheckSlackRequest(); yes {
			responsePacket = exchange.NewSlackRequestAckPacket(packet, channelName)
			this.messenger.handleMessage(channelName, packet)
		} else {
			zlog.Errorf("unknown websocket command: %s", packet.Event)
			continue
		}

		if responsePacket != nil {
			err = ws.WriteJSON(responsePacket)
			if err != nil {
				zlog.Errorf("write json to websocket error: %s", err)
			}
		}
	}
}

// 手工触发消息推送
func (this *GatewayServer) notifyFlush() {
	this.notifyChan <- 1
}

// 这里处理需要定时发送的数据
func (this *GatewayServer) websocketLoop() {
	ticker := time.NewTicker(time.Duration(this.opts.Websocket.FlushInterval) * time.Millisecond)

	for i := 0; ; i++ {
		select {
		case <-this.exitChan:
			return
		case <-this.notifyChan:
		case <-ticker.C:
			// zlog.Debugf("flush interval meet, send websocket messages")
		}

		// 按 InstrumentID 单个推送；多个 instruments 推送 key 不好处理，不要那样做
		// 通过 tradingSymbols 过滤，可以大量减少 sendPacket 的量
		if this.InstrumentsReady.Load() {
			for _, instrumentID := range this.storage.TradingSymbols {
				if instrument, ok := this.Instruments.Load(instrumentID); ok {
					packet := exchange.NewInstrumentPacket("", instrument)
					this.sendPacket(packet)
				} else {
					zlog.Errorf("websocket loop instrument not found (%s)", instrumentID)
				}
			}
		}

		account, err := this.ctp.GetAccount()
		if err == nil {
			userMargin := convertUserMargin(account)
			marginPacket := exchange.NewUserMarginPacket("", userMargin)
			this.sendPacket(marginPacket)
		}
	}
}

func (this *GatewayServer) sendPacket(packet *exchange.Packet) {
	this.connMap.Range(func(k string, msgChan chan *exchange.Packet) bool {
		select {
		case msgChan <- packet:
		default:
			// zlog.Debugf("skip message for %s, %v", conn.Key, packet.PacketHeader)
		}
		return true
	})
}

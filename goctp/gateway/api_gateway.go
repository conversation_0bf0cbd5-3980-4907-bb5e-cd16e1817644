package gateway

import (
	"fmt"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/wizhodl/goctp/ctp"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

func (this *GatewayServer) getInstrumentsHandler(ctx *gin.Context) {
	var form GetInstrumentsForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args, error: %s", err)
		return
	}
	instruments := []*Instrument{}
	if !this.InstrumentsReady.Load() {
		failJSON(ctx, "instruments not ready")
		return
	}
	this.Instruments.Range(func(k string, instrument *Instrument) bool {
		if form.InstrumentID != "" {
			if strings.EqualFold(form.InstrumentID, instrument.Symbol) {
				instruments = append(instruments, instrument)
			}
		} else {
			instruments = append(instruments, instrument)
		}
		return true
	})
	okJSON(ctx, instruments)
}

func (this *GatewayServer) getOpenOrdersHandler(ctx *gin.Context) {
	var form GetOpenOrdersForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args, error: %s", err)
		return
	}

	orders := []*Order{}
	if form.OrderType == exchange.UnknownOrderType || form.OrderType == exchange.Limit || form.OrderType == exchange.StopLimit {
		// 不用单独去查询 ctp.trade.Orders
		// 因为会用 onOrder 回调更新 OrderField 的数据到 storage.Orders 中
		for instrumentID, ords := range this.storage.Orders {
			for _, order := range ords {
				if instrumentID != "" && !strings.EqualFold(instrumentID, form.InstrumentID) {
					continue
				}

				if order.IsOpen() && (order.Type == form.OrderType || form.OrderType == exchange.UnknownOrderType) {
					orders = append(orders, order)
				}
			}
		}
	} else {
		failJSON(ctx, "order type not supported (%s)", form.OrderType)
	}
	okJSON(ctx, orders)
}

func (this *GatewayServer) getOrdersHandler(ctx *gin.Context) {
	var form GetOrdersForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args, error: %s", err)
		return
	}

	ctpOrders, err := this.ctp.GetOrders()
	if err != nil {
		failJSON(ctx, "get ctp order failed, error: %s", err)
		return
	}

	orderIDs := strings.Split(form.OrderIDs, ",")
	orders := []*Order{}
	for _, orderID := range orderIDs {
		// 如果是 CTP 原始订单号，到 CTP 系统中去查
		if isSubOrderID(orderID) {
			ctpOrders.Range(func(k string, of *ctp.OrderField) bool {
				order := convertOrder(of)
				if orderID == order.OrderID {
					orders = append(orders, order)
				}
				return true
			})
		} else {
			// 因为会用 onOrder 回调更新 OrderField 的数据到 storage.Orders 中，不用单独去查询 ctp.trade.Orders
			for _, ords := range this.storage.Orders {
				for _, order := range ords {
					if orderID == order.OrderID && order.Type == form.OrderType {
						orders = append(orders, order)
					}
				}
			}
		}
	}
	okJSON(ctx, orders)
}

func (this *GatewayServer) getLastPriceHandler(ctx *gin.Context) {
	var form GetLastPriceForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args")
		return
	}
	if !this.InstrumentsReady.Load() {
		failJSON(ctx, "instruments not ready")
		return
	}

	var instrument *Instrument
	this.Instruments.Range(func(k string, instr *Instrument) bool {
		if strings.EqualFold(instr.Symbol, form.InstrumentID) {
			instrument = instr
			return false
		}
		return true
	})
	if instrument == nil {
		failJSON(ctx, "instrument not found")
		return
	}
	if instrument.Status != exchange.InstrumentStatusContinuous {
		failJSON(ctx, "instrument is not continuous trading")
		return
	}
	// timeDelta := time.Since(instrument.UpdateTime)
	// if timeDelta > 2*time.Second {
	// 	failJSON(ctx, "instrument not up to date, time delta: %s", timeDelta)
	// 	return
	// }
	okJSON(ctx, LastPriceResponse{Price: instrument.LastPrice, UpdateTime: instrument.UpdateTime})
}

func (this *GatewayServer) getAccountBalancesHandler(ctx *gin.Context) {
	account, err := this.ctp.GetAccount()
	if err != nil {
		failJSON(ctx, "account data not available, error: %s", err)
		return
	}
	balance := exchange.AccountBalance{}
	accountBalances := []*exchange.AccountBalance{&balance}
	balance.InstrumentType = exchange.USDXMarginedFutures
	balance.Currency = "CNY"
	balance.Total = account.Balance
	balance.Available = account.Available
	okJSON(ctx, accountBalances)
}

func (this *GatewayServer) getUserMarginHandler(ctx *gin.Context) {
	account, err := this.ctp.GetAccount()
	if err != nil {
		failJSON(ctx, "account data not available, error: %s", err)
		return
	}
	userMargin := convertUserMargin(account)
	okJSON(ctx, userMargin)
}

func (this *GatewayServer) getPositionsHandler(ctx *gin.Context) {
	var form GetPositionsForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args, error: %s", err)
		return
	}

	positions := []*exchange.Position{}
	errorStr := ""

	if form.NoCache {
		instrument, err := this.GetInstrument(form.InstrumentID)
		if err != nil {
			failJSON(ctx, "get instrument failed, error: %s", err)
			return
		}
		ctpPositions, err := this.ctp.QueryPositions(instrument.ExchangeID, instrument.Symbol)
		if err != nil {
			failJSON(ctx, "query positions failed, error: %s", err)
			return
		}
		for _, p := range ctpPositions {
			position, err := this.convertPosition(&p)
			if err != nil {
				failJSON(ctx, "convert position field to position failed, error: %s", err)
				return
			}
			positions = append(positions, position)
		}
		okJSON(ctx, positions)
		return
	} else {
		ctpPositions, err := this.ctp.GetPositions()
		if err != nil {
			failJSON(ctx, "ctp position data not available, error: %s", err)
		}
		ctpPositions.Range(func(k string, pf *ctp.PositionField) bool {
			longKey := fmt.Sprintf("%s_long", form.InstrumentID)
			shortKey := fmt.Sprintf("%s_short", form.InstrumentID)
			if strings.EqualFold(k, longKey) || strings.EqualFold(k, shortKey) {
				if pf.Position == 0 {
					return true
				}
				if p, err := this.convertPosition(pf); err != nil {
					errorStr = fmt.Sprintf("convert position failed (%s), error: %s", pf.InstrumentID, err)
					return false
				} else {
					if time.Since(*p.UpdateTime) < 5*time.Second {
						positions = append(positions, p)
					} else {
						errorStr = fmt.Sprintf("position update time too old ( >5s ago), ignore, instrumentID: %s, Side: %s, Qty: %f, UpdateTime: %s", p.Symbol, p.Side, p.Qty, p.UpdateTime)
						return false
					}
				}
			}
			return true
		})
		if errorStr != "" {
			zlog.Errorf(errorStr)
			failJSON(ctx, errorStr)
			return
		}
		okJSON(ctx, positions)
	}
}

// 创建订单，支持 “条件单”， “限价单”和“市价单"
func (this *GatewayServer) createOrderHandler(ctx *gin.Context) {
	args := &exchange.CreateOrderArgs{}
	if err := ctx.ShouldBindJSON(args); err != nil {
		badRequest(ctx, "invalid args, error: %s", err)
		return
	}
	if !this.ctp.IsInstrumentsReady() {
		badRequest(ctx, "instrument not ready")
		return
	}

	nowTime := time.Now()

	var instrument *Instrument
	if instr, err := this.GetInstrument(args.Symbol); err != nil {
		badRequest(ctx, "instrument not found, error: %s", err)
		return
	} else {
		instrument = instr
	}

	if instrument.Status != exchange.InstrumentStatusContinuous {
		canTrade := false
		if instrument.Status == exchange.InstrumentStatusExpiredSoon && args.ReduceOnly {
			// 如果快过期，仅允许提交平仓单
			if status, found := this.ctp.GetInstrumentStatuss().Load(instrument.UnderlyCurrency); found {
				if status.InstrumentStatus == ctp.InstrumentStatusContinuous {
					canTrade = true
				}
			}
		}
		if !canTrade {
			failJSON(ctx, "instrument is not continuous trading")
			return
		}
	}

	if args.Type != exchange.Market && args.Price == 0 {
		badRequest(ctx, "limit order price can not be 0")
		return
	}

	order := &Order{
		exchange.Order{
			InstrumentType:   exchange.USDXMarginedFutures,
			Symbol:           args.Symbol,
			OrderID:          ctp.NewRandomID(),
			Price:            instrument.RoundPrice(args.Price),
			TriggerPrice:     instrument.RoundPrice(args.TriggerPrice),
			TriggerDirection: args.TriggerDirection,
			Qty:              args.Qty,
			Type:             args.Type,
			Side:             args.Side,
			Status:           exchange.OrderStatusNew,
			TimeInForce:      args.TimeInForce,
			ReduceOnly:       args.ReduceOnly,
			CreateTime:       &nowTime,
			UpdateTime:       &nowTime,
		},
		[]*SubOrder{},
		time.Time{},
	}
	// 限价单和市价单，直接发出订单
	if SliceContains([]exchange.OrderType{exchange.Limit, exchange.Market}, order.Type) {
		this.storage.addOrder(order)
		if err := this.CreateOrder(order, nil); err != nil {
			zlog.Errorf("create order failed (%s), error: %s", order.OrderID, err)
			failJSON(ctx, err.Error())
			return
		}
	} else {
		this.storage.addOrder(order)
	}

	okJSON(ctx, order)
}

var cancelOrderLocks map[string]*sync.Mutex = map[string]*sync.Mutex{}

// 取消订单，支持 “条件单” 和 “限价单”
func (this *GatewayServer) cancelOrderHandler(ctx *gin.Context) {
	var form CancelOrderForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args, error: %s", err)
		return
	}

	ctpOrders, err := this.ctp.GetOrders()
	if err != nil {
		failJSON(ctx, "get ctp orders failed, error: %s", err)
		return
	}

	// 按 orderID 锁
	lock := &sync.Mutex{}
	if l, found := cancelOrderLocks[form.OrderID]; found {
		lock = l
	} else {
		cancelOrderLocks[form.OrderID] = lock
	}
	lock.Lock()
	defer lock.Unlock()

	// 本地主订单，或者 "ctp订单" 转换后的订单
	var order *Order

	// 取消 ctp 订单
	if isSubOrderID(form.OrderID) {
		orderSysID := ""
		ctpOrders.Range(func(oid string, of *ctp.OrderField) bool {
			if oid == form.OrderID {
				orderSysID = of.OrderSysID
				order = convertOrder(of)
				return false
			}
			return true
		})
		if order == nil {
			failJSON(ctx, "ctp order not found (%s)", form.OrderID)
			return
		}
		if order.IsCanceled() {
			okJSON(ctx, order)
			return
		} else {
			ctpOrder, err := this.ctp.CancelOrder(form.OrderID, orderSysID)

			if err != nil {
				failJSON(ctx, "cancel ctp order failed, error: %s", err)
				return
			}
			order = convertOrder(ctpOrder)
			okJSON(ctx, order)
		}
	} else { // 取消本地订单
		for _, orders := range this.storage.Orders {
			for _, o := range orders {
				if form.OrderID == o.OrderID {
					order = o
					break
				}
			}
		}
		if order == nil {
			failJSON(ctx, "order not found (%s)", form.OrderID)
			return
		}
		if order.IsCanceled() {
			okJSON(ctx, order)
			return
		} else {
			// 没有关联的子订单，直接取消本地订单并且返回结果
			if len(order.GetSubOrderIDs()) == 0 {
				if !order.IsCanceled() {
					this.storage.setOrderCanceled(order)
				}
				okJSON(ctx, order)
				return
			}
		}

		/* 取消子订单 */
		// TODO: 因为取消子订单可能失败，可能需要有固定程序扫描来警告 “主订单单已取消+子订单未取消” 的情况
		wg := sync.WaitGroup{}
		wg.Add(len(order.GetSubOrderIDs()))
		errorMsgs := []string{}
		for _, subOrder := range order.SubOrders {
			subOrderID := subOrder.OrderID
			if subOrderID != "" {
				_, err := this.ctp.CancelOrder(subOrderID, subOrder.OrderSysID)
				if err != nil {
					errorMsgs = append(errorMsgs, fmt.Sprintf("cancel ctp limit order failed, order id (%s), ctp order id (%s), error: %s", order.OrderID, subOrderID, err))
				}
				wg.Done()
			}
		}
		wg.Wait()
		// 等待 1s 让 ctpOrder 通过 onRtnOrder 同步过来
		time.Sleep(1 * time.Second)
		if len(errorMsgs) == 0 {
			if !order.IsCanceled() {
				this.storage.setOrderCanceled(order)
			}
			okJSON(ctx, order)
		} else {
			failJSON(ctx, strings.Join(errorMsgs, " | "))
		}
	}
}

type Stats struct {
	Alloc        uint64
	TotalAlloc   uint64
	Sys          uint64
	NumGC        uint32
	PauseTotalNs uint64
	UpdateTime   time.Time
}

func (this *GatewayServer) getStatsHandler(ctx *gin.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	// For info on each, see: https://golang.org/pkg/runtime/#MemStats

	s := Stats{}
	s.Alloc = b2m(m.Alloc)
	s.TotalAlloc = b2m(m.TotalAlloc)
	s.Sys = b2m(m.Sys)
	s.NumGC = m.NumGC
	s.UpdateTime = time.Now()
	s.PauseTotalNs = m.PauseTotalNs
	okJSON(ctx, s)
}

func b2m(b uint64) uint64 {
	return b / 1024 / 1024
}

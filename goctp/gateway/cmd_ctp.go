package gateway

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
	"github.com/wizhodl/goctp/ctp"
	"github.com/wizhodl/goctp/ctp/define"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

type CTPCommand struct {
	command.Command
	controller *GatewayServer
	DebugOnly  bool
}

func (this *CTPCommand) Prepare() bool {
	if !this.controller.ctp.IsTradeLogin() {
		this.ErrorMsgf("CTP 还没有启动完成，没有数据。")
		return false
	}
	if this.DebugOnly && !this.controller.Debug {
		this.ErrorMsgf("这个命令只能在 Debug 模式下执行。")
		return false
	}
	return true
}

type OrdersCTPCommand struct {
	CTPCommand
}

func NewOrdersCTPCommand(controller *GatewayServer) *OrdersCTPCommand {
	cmd := &OrdersCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "ctpOrders",
				Alias:           []string{"co"},
				Instruction:     "`.ctpOrders IDs/all summary[可选]` 查看 CTP 订单",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          2,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *OrdersCTPCommand) Do() bool {
	ctpOrders, err := this.controller.ctp.GetOrders()
	if err != nil {
		this.ErrorMsgf("获取 CTP 订单失败，error: %s", err)
		return false
	}

	ids := ""
	isSummary := false
	if len(this.Args) >= 1 {
		ids = this.Args[0]
	}
	idList := strings.Split(ids, ",")

	if len(this.Args) == 2 && strings.EqualFold(this.Args[1], "summary") {
		isSummary = true
	}

	orders := []*ctp.OrderField{}
	ctpOrders.Range(func(k string, order *ctp.OrderField) bool {
		if ids != "all" {
			if SliceContains(idList, order.GetOrderID()) {
				orders = append(orders, order)
			}
		} else {
			orders = append(orders, order)
		}
		return true
	})

	if len(orders) == 0 {
		this.SendMsgf("没有查询到订单。")
		return false
	}

	if isSummary {
		this.SendMsgf("```%s```", this.controller.GetCtpOrdersSummary(orders))
	} else {
		for _, order := range orders {
			this.SendMsgf("CTP订单 [%s]\n```%s```", order.GetOrderID(), toTable(order, true))
		}
	}
	return true
}

type PositionsCTPCommand struct {
	CTPCommand
}

func (this *GatewayServer) GetCtpOrdersSummary(orders []*ctp.OrderField) string {
	summary := "[no order summary]"
	t := exchange.NewTable()
	t.SetHeader([]string{"OrderID", "InstrumentID", "Type", "Side", "Qty", "Price", "Stop Price", "Status", "Filled Qty", "Create Time", "Update Time"})
	newOrders := []*Order{}
	for _, of := range orders {
		order := convertOrder(of)
		newOrders = append(newOrders, order)
	}
	sort.SliceStable(newOrders, func(i int, j int) bool {
		return newOrders[i].OrderID < newOrders[j].OrderID
	})

	for _, order := range newOrders {
		row := []string{}
		row = append(row, order.OrderID)
		row = append(row, order.Symbol)
		row = append(row, string(order.Type))
		if order.ReduceOnly {
			row = append(row, fmt.Sprintf("%s / Reduce", order.Side))
		} else {
			row = append(row, string(order.Side))
		}
		row = append(row, fmt.Sprintf("%.f", order.Qty))
		row = append(row, fmt.Sprintf("%.2f", order.Price))
		row = append(row, fmt.Sprintf("%.2f", order.TriggerPrice))
		row = append(row, string(order.Status))
		row = append(row, fmt.Sprintf("%.f", order.ExecQty))
		row = append(row, FormatShortTimeStr(order.CreateTime, false))
		row = append(row, FormatShortTimeStr(&order.CtpUpdateTime, false))
		t.AddRow(row)
	}
	if len(t.Rows) > 1 {
		summary = t.Render()
	}
	return summary
}

func NewPositionsCTPCommand(controller *GatewayServer) *PositionsCTPCommand {
	cmd := &PositionsCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "ctpPositions",
				Alias:           []string{"cp"},
				Instruction:     "`.ctpPositions InstrumentIDs` 查看 CTP 持仓",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *PositionsCTPCommand) Do() bool {
	ctpPositions, err := this.controller.ctp.GetPositions()
	if err != nil {
		this.ErrorMsgf("获取 CTP 持仓失败，error:%s", err)
		return false
	}

	noPosition := true
	instrumentIDs := strings.Split(this.Args[0], ",")
	ctpPositions.Range(func(positionID string, position *ctp.PositionField) bool {
		instrumentID := strings.Split(positionID, "_")[0]
		if SliceContains(instrumentIDs, instrumentID) {
			this.SendMsgf("CTP持仓 [%s]\n```%s```", positionID, toTable(position, true))
			noPosition = false
		}
		return true
	})
	if noPosition {
		this.SendMsgf("当前无持仓")
	}
	return true
}

type ClosePositionCTPCommand struct {
	CTPCommand
}

func NewClosePositionCTPCommand(controller *GatewayServer) *ClosePositionCTPCommand {
	cmd := &ClosePositionCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "ctpClose",
				Alias:           []string{"cc"},
				Instruction:     "`.ctpClose InstrumentIDs qty` 平仓 CTP 持仓, qty 可选，默认全平",
				RequiresConfirm: true,
				ArgMin:          1,
				ArgMax:          2,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *ClosePositionCTPCommand) Prepare() bool {
	ctpPositions, err := this.controller.ctp.GetPositions()
	if err != nil {
		this.ErrorMsgf("获取 CTP 持仓失败，error:%s", err)
		return false
	}

	var qty int64
	if len(this.Args) == 2 {
		qty, err = strconv.ParseInt(this.Args[1], 10, 32)
		if err != nil {
			this.ErrorMsgf("请输入正确的数量")
			return false
		}
	}

	instrumentIDs := this.Args[0]
	positions := []*exchange.Position{}
	hasError := false
	ctpPositions.Range(func(positionID string, pf *ctp.PositionField) bool {
		instrumentID := strings.Split(positionID, "_")[0]
		if strings.Contains(instrumentIDs, instrumentID) {
			if pf.Position == 0 {
				return true
			}
			if p, err := this.controller.convertPosition(pf); err != nil {
				this.ErrorMsgf("convert position failed (%s), error: %s", pf.InstrumentID, err)
				hasError = true
			} else {
				positions = append(positions, p)
			}
		}
		return true
	})
	if hasError {
		return false
	} else {
		t := exchange.NewTable()
		t.SetHeader([]string{"Symbol", "Side", "Qty"})

		for _, p := range positions {
			if math.Abs(p.Qty) < float64(qty) {
				this.ErrorMsgf("%s 持仓数量 %d 小于参数数量 %d", p.Symbol, int(math.Abs(p.Qty)), qty)
				return false
			}
			if p.Qty != 0 {
				t.AddRow([]string{p.Symbol, string(p.Side), fmt.Sprintf("%d", int(p.Qty))})
			}
		}
		this.SendMsgf("持仓：\n\n```%s```", t.Render())
	}
	return true
}

func (this *ClosePositionCTPCommand) Do() bool {
	var qty int64
	if len(this.Args) == 2 {
		qty, _ = strconv.ParseInt(this.Args[1], 10, 32)
	}
	errorMsgs := this.controller.closePosition(this.Args[0], float64(qty))
	if len(errorMsgs) > 0 {
		this.ErrorMsgf("平仓出现错误，errors ： %s", SliceStringJoin(errorMsgs, "\n", false))
		return false
	} else {
		this.SendMsgf("全部平仓成功。")
		return true
	}
}

type ClearCTPCommand struct {
	CTPCommand
}

func NewClearCTPCommand(controller *GatewayServer) *ClearCTPCommand {
	cmd := &ClearCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "clear",
				Alias:           []string{"cl"},
				Instruction:     "`.clear InstrumentIDs` 清空本地订单并平仓",
				RequiresConfirm: true,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
			DebugOnly:  true,
		},
	}
	return cmd
}

func (this *ClearCTPCommand) Do() bool {
	instrumentIDs := this.Args[0]
	// 清理 storage
	for instrumentID := range this.controller.storage.Orders {
		if strings.Contains(instrumentIDs, instrumentID) {
			delete(this.controller.storage.Orders, instrumentID)
		}
	}
	this.controller.storage.Save()

	// 平仓
	errorMsgs := this.controller.closePosition(instrumentIDs, 0)

	if len(errorMsgs) > 0 {
		this.ErrorMsgf("平仓出现错误，errors ： %s", SliceStringJoin(errorMsgs, "\n", false))
		return false
	} else {
		this.SendMsgf("清理并平仓成功。")
		return true
	}
}

func (this *GatewayServer) closePosition(instrumentIDs string, qty float64) (errorMsgs []string) {
	ctpPositions, err := this.ctp.GetPositions()
	if err != nil {
		errorMsgs = append(errorMsgs, fmt.Sprintf("获取 CTP 持仓失败，error:%s", err))
		return
	}

	ctpPositions.Range(func(k string, pf *ctp.PositionField) bool {
		if !strings.Contains(instrumentIDs, pf.InstrumentID) {
			return true
		}

		if pf.Position == 0 {
			return true
		}

		side := exchange.OrderSideBuy
		if pf.PositionDirection == ctp.PosiDirectionLong {
			side = exchange.OrderSideSell
		}

		if qty == 0 {
			qty = float64(pf.Position)
		}

		createArgs := &exchange.CreateOrderArgs{
			InstrumentType: exchange.USDXMarginedFutures,
			Symbol:         pf.InstrumentID,
			Side:           side,
			Type:           exchange.Market,
			Qty:            qty,
			ReduceOnly:     true,
		}
		order := exchange.Order{}
		if err := this.sendHTTPRequest(resty.MethodPost, "/v1/orders/create", createArgs, &order); err != nil {
			errorMsgs = append(errorMsgs, fmt.Sprintf("close position error: %s", err))
		} else {
			zlog.Debugf("close position order: %#v", order)
			// 3秒后查询成交状态
			time.Sleep(3 * time.Second)
			for _, ords := range this.storage.Orders {
				for _, o := range ords {
					if o.OrderID == order.OrderID {
						if o.Qty == o.ExecQty {
							this.SendMsgf("%s-%s 平仓完成，成交 %d, 成交价 %v", o.Symbol, o.Side, int(o.ExecQty), o.ExecPrice)
						} else {
							this.SendMsgf("%s-%s 平仓失败，成交 %d/%d。", o.Symbol, o.Side, int(o.ExecQty), int(o.Qty))
						}
						break
					}
				}
			}
		}

		return true
	})
	return
}

type CancelOrderCTPCommand struct {
	CTPCommand
}

func NewCancelOrderCTPCommand(controller *GatewayServer) *CancelOrderCTPCommand {
	cmd := &CancelOrderCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "cancelCTPOrder",
				Alias:           []string{"cco"},
				Instruction:     "`.cancelCTPOrder orderID` 撤销 CTP 订单",
				RequiresConfirm: true,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *CancelOrderCTPCommand) Prepare() bool {
	ctpOrders, err := this.controller.ctp.GetOrders()
	if err != nil {
		this.ErrorMsgf("get ctp orders err: %s", err)
		return false
	}
	var order *ctp.OrderField
	ctpOrders.Range(func(oid string, of *ctp.OrderField) bool {
		if oid == this.Args[0] {
			order = of
			return false
		}
		return true
	})
	if order == nil {
		this.ErrorMsgf("cannot find ctp order %s", this.Args[0])
		return false
	}
	return true
}

func (this *CancelOrderCTPCommand) Do() bool {
	path := fmt.Sprintf("/v1/orders/cancel?order_id=%s", this.Args[0])
	order := exchange.Order{}
	if err := this.controller.sendHTTPRequest(resty.MethodGet, path, nil, &order); err != nil {
		this.ErrorMsgf("订单取消失败: %s", err)
		return false
	}
	zlog.Debugf("cancel ctp order: %#v", order)
	this.SendMsgf("订单取消成功")
	return true
}

type GetAccountCTPCommand struct {
	CTPCommand
}

func NewGetAccountCTPCommand(controller *GatewayServer) *GetAccountCTPCommand {
	cmd := &GetAccountCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "ctpAccount",
				Alias:           []string{"ca"},
				Instruction:     "`.ctpAccount` 查看 CTP 账户",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *GetAccountCTPCommand) Do() bool {
	account, err := this.controller.ctp.GetAccount()
	if err != nil {
		this.ErrorMsgf("获取 CTP 账户数据出错，error: %s", err)
	}

	this.SendMsgf("CTP账户 [%s]\n```%s```", this.controller.opts.Ctp.InvestorID, toTable(account, true))
	return true
}

type GetInstrumentsCTPCommand struct {
	CTPCommand
}

func NewGetInstrumentsCTPCommand(controller *GatewayServer) *GetInstrumentsCTPCommand {
	cmd := &GetInstrumentsCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "ctpInstruments",
				Alias:           []string{"ci"},
				Instruction:     "`.ctpInstruments InstrumentIDs` 查看 CTP 品种",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *GetInstrumentsCTPCommand) Do() bool {
	instrumentIDs := strings.Split(this.Args[0], ",")
	if !this.controller.ctp.IsInstrumentsReady() {
		this.SendMsgf("CTP 品种数据没有就绪。")
		return false
	}

	instruments, err := this.controller.ctp.GetInstruments()
	if err != nil {
		this.ErrorMsgf("获取 CTP 品种失败，error: %s", err)
		return false
	}

	count := 0
	instruments.Range(func(instrumentID string, instrument *ctp.InstrumentField) bool {
		if SliceContains(instrumentIDs, instrumentID) {
			count += 1
			this.SendMsgf("CTP品种 [%s]\n```%s```", instrumentID, toTable(instrument, true))
		}
		return true
	})
	if count == 0 {
		this.SendMsgf("没有找到品种 %s 的数据。", instrumentIDs)
		return true
	}
	return true
}

type GetTradesCTPCommand struct {
	CTPCommand
}

func NewGetTradesCTPCommand(controller *GatewayServer) *GetTradesCTPCommand {
	cmd := &GetTradesCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "ctpTrades",
				Alias:           []string{"ct"},
				Instruction:     "`.ctpTrades OrderIDs` 查看 CTP 订单的成交",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *GetTradesCTPCommand) Do() bool {
	orderIDs := this.Args[0]
	count := 0
	this.controller.ctp.GetTrades().Range(func(tradeID string, trade *ctp.TradeField) bool {
		var order *ctp.OrderField
		if ord, err := this.controller.ctp.GetOrderByTradeID(tradeID); err != nil {
			this.SendMsgf("没有找到成交记录 (%s) 对应的订单。", tradeID)
		} else {
			order = ord
		}
		if orderIDs == "all" || (order != nil && strings.Contains(orderIDs, order.GetOrderID())) {
			count += 1
			if order != nil {
				this.SendMsgf("CTP订单 [%s] - CTP成交 [%s]\n```%s```", order.GetOrderID(), tradeID, toTable(trade, true))
			} else {
				this.SendMsgf("CTP成交 [%s]\n```%s```", tradeID, toTable(trade, true))
			}
		}
		return true
	})
	if count == 0 {
		this.SendMsgf("没有找到订单 %s 的成交数据。", orderIDs)
		return true
	}
	return true
}

type GetInstrumentStatusCTPCommand struct {
	CTPCommand
}

func NewGetInstrumentStatusCTPCommand(controller *GatewayServer) *GetInstrumentStatusCTPCommand {
	cmd := &GetInstrumentStatusCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "ctpInstrumentStatus",
				Alias:           []string{"cis"},
				Instruction:     "`.ctpInstrumentStatus productID` 查看 CTP 品种状态",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *GetInstrumentStatusCTPCommand) Do() bool {
	productID := this.Args[0]
	if len(productID) > 2 {
		this.SendMsgf("请输入产品ID，而不是品种ID。")
		return false
	}
	count := 0
	this.controller.ctp.GetInstrumentStatuss().Range(func(pID string, instrumentStatus *ctp.InstrumentStatus) bool {
		if productID == pID {
			count += 1
			this.SendMsgf("CTP产品状态 [%s]\n```%s```", productID, toTable(instrumentStatus, true))
		}
		return true
	})
	if count == 0 {
		this.SendMsgf("没有找到产品 %s 的状态数据。", productID)
		return true
	}
	return true
}

type GetTicksCTPCommand struct {
	CTPCommand
}

func NewGetTicksCTPCommand(controller *GatewayServer) *GetTicksCTPCommand {
	cmd := &GetTicksCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "ctpTicks",
				Alias:           []string{"ck"},
				Instruction:     "`.ctpTicks instrumentIDs` 查看 CTP 品种的最新报价",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *GetTicksCTPCommand) Do() bool {
	instrumentIDs := strings.Split(this.Args[0], ",")
	count := 0
	for _, instrumentID := range instrumentIDs {
		tick, err := this.controller.ctp.GetTick(instrumentID)
		if err != nil {
			this.ErrorMsgf("获取 tick 出错 (%s)，error: %s", instrumentID, err)
			continue
		}
		count += 1
		this.SendMsgf("CTP品种最新报价 [%s]\n```%s```", instrumentID, toTable(tick, true))
	}

	if count == 0 {
		this.SendMsgf("没有找到品种 %s 的报价数据。", instrumentIDs)
		return true
	}
	return true
}

type GetPositionDetailCTPCommand struct {
	CTPCommand
}

func NewGetPositionDetailCTPCommand(controller *GatewayServer) *GetPositionDetailCTPCommand {
	cmd := &GetPositionDetailCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "ctpPosiDetail",
				Alias:           []string{"cpd"},
				Instruction:     "`.ctpPosiDetail instrumentIDs` 查看 CTP 品种仓位详情",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *GetPositionDetailCTPCommand) Do() bool {
	ctpPositions, err := this.controller.ctp.GetPosiDetail()
	if err != nil {
		this.ErrorMsgf("获取 CTP 品种仓位失败，error: %s", err)
		return false
	}

	count := 0
	instrumentIDs := strings.Split(this.Args[0], ",")
	ctpPositions.Range(func(positionID string, positions []*define.CThostFtdcInvestorPositionField) bool {
		instrumentID := strings.Split(positionID, "_")[0]
		if SliceContains(instrumentIDs, instrumentID) {
			for i, position := range positions {
				count += 1
				// 不转换 byte，打印原始值，但是去掉 0 值
				// 因为 ctp.CThostFtdcInvestorPositionField 全是自定义结构，转换 byte 会导致所有的 int 都错掉
				this.SendFileWithType("CTP持仓明细", fmt.Sprintf("CTP持仓明细 [%s-%d]\n%s", positionID, i, toTable(position, false)), "javascript")
			}
		}
		return true
	})
	if count == 0 {
		this.SendMsgf("没有找到 (%s) 对应的仓位。", instrumentIDs)
	}
	return true
}

type GetSysOrdersCTPCommand struct {
	CTPCommand
}

func NewGetSysOrdersCTPCommand(controller *GatewayServer) *GetSysOrdersCTPCommand {
	cmd := &GetSysOrdersCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "ctpSysOrders",
				Alias:           []string{"cso"},
				Instruction:     "`.ctpSysOrders orderIDs` 查看 CTP 订单的SysID关联",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *GetSysOrdersCTPCommand) Do() bool {
	ctpOrders, err := this.controller.ctp.GetSysID4Order()
	if err != nil {
		this.ErrorMsgf("获取 CTP 订单的 SysID 关联失败，error: %s", err)
		return false
	}

	orderIDs := this.Args[0]
	count := 0
	ctpOrders.Range(func(sysID string, order *ctp.OrderField) bool {
		if strings.Contains(orderIDs, order.GetOrderID()) {
			count += 1
			this.SendMsgf("CTP 订单 [SysID-%s : %s]\n```%s```", sysID, order.GetOrderID(), toTable(order, true))
		}
		return true
	})
	if count == 0 {
		this.SendMsgf("没有找到 (%s) 对应的订单。", orderIDs)
	}
	return true
}

type QueryOrderCTPCommand struct {
	CTPCommand
}

func NewQueryOrderCTPCommand(controller *GatewayServer) *QueryOrderCTPCommand {
	cmd := &QueryOrderCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "ctpQueryOrder",
				Alias:           []string{"cqo"},
				Instruction:     "`.ctpQueryOrder instrumentID orderSysID` 查询 CTP 订单",
				RequiresConfirm: false,
				ArgMin:          2,
				ArgMax:          2,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *QueryOrderCTPCommand) Do() bool {
	instrumentID := this.Args[0]
	orderSysID := this.Args[1]

	ctpOrder, err := this.controller.ctp.QueryOrder(instrumentID, orderSysID)
	if err != nil {
		this.SendMsgf("查询失败: %s", err)
		return false
	}
	this.SendMsgf("CTP订单 [%s]\n```%s```", ctpOrder.GetOrderID(), toTable(ctpOrder, true))
	return true
}

// 获取基本请求 request
func (this *GatewayServer) getBaseRequest() *resty.Request {
	client := resty.New()
	client.
		SetHostURL(fmt.Sprintf("http://127.0.0.1%s", this.opts.HTTPAddr)).
		SetTimeout(10 * time.Second).     // 设置超时时间，超时无反应则返回超时错误
		SetRetryCount(0).                 // 请求失败时的重试次数
		SetRetryWaitTime(5 * time.Second) // 重试前等待时间

	req := client.R()
	req.SetHeader("Content-Type", "application/json")
	return req
}

func (this *GatewayServer) sendHTTPRequest(httpMethod, requestPath string, data, result any) (_ error) {
	req := this.getBaseRequest()

	payload := []byte("")
	if data != nil {
		payloadData, err := json.Marshal(data)
		if err != nil {
			return errors.New("sendHTTPRequest: Unable to JSON request")
		}
		payload = payloadData
		req.SetBody(payload)
	}

	resp, err := req.Execute(httpMethod, requestPath)
	if err != nil {
		zlog.Errorf("send http request(%s) err: %s", requestPath, err)
		return err
	}

	if resp.StatusCode() != 200 {
		zlog.Errorf("request(%s) error: %d - %s", requestPath, resp.StatusCode(), resp)
		msg := gjson.Parse(resp.String()).Get("message").String()
		return fmt.Errorf("http request error: %s", msg)
	}

	return json.Unmarshal(resp.Body(), result)
}

type ComplianceTestCTPCommand struct {
	CTPCommand
}

func NewComplianceTestCTPCommand(controller *GatewayServer) *ComplianceTestCTPCommand {
	cmd := &ComplianceTestCTPCommand{
		CTPCommand: CTPCommand{
			Command: command.Command{
				Name:            "ctpComplianceTest",
				Instruction:     "`.ctpComplianceTest InstrumentID fillPriceRatio[可选，默认值=1.06]` CTP 合规测试",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          2,
				AuthcodePos:     -1,
			},
			controller: controller,
			DebugOnly:  true,
		},
	}
	return cmd
}

func (this *ComplianceTestCTPCommand) Do() bool {
	instrumentID := this.Args[0]
	fillPriceRatio := 1.06
	if len(this.Args) == 2 {
		ratio, err := strconv.ParseFloat(this.Args[1], 64)
		if err != nil {
			this.ErrorMsgf("fillPriceRatio 请输入一个小数。")
			return false
		}
		fillPriceRatio = ratio
	}
	instrument, err := this.controller.GetInstrument(instrumentID)
	if err != nil {
		this.ErrorMsgf("获取品种数据出错，error: %s", err)
		return false
	}
	lastPrice := instrument.LastPrice
	if lastPrice == 0 {
		this.ErrorMsgf("%s 的最新价格为0，稍候再试。", instrumentID)
		return false
	}
	orderIDs := []string{}
	for i := 0; i < 5; i++ {
		result := "成功"
		var orderID string
		price := lastPrice*fillPriceRatio + float64(i)*instrument.TickSize
		_, err := this.controller.ctp.CreateOrder(instrumentID, ctp.DirectionBuy, ctp.OffsetFlagOpen, instrument.RoundPrice(price), 1, 0, ctp.UnknownTriggerDirection, func(ctpOrderID string, err error) {
			orderID = ctpOrderID
		})
		if err != nil {
			result = fmt.Sprintf("失败。error：%s", err)
		} else {
			orderIDs = append(orderIDs, orderID)
		}
		this.SendMsgf("发送成交的订单: %s，结果：%s", orderID, result)
	}

	for i := 0; i < 5; i++ {
		result := "成功"
		orderID := ""
		price := lastPrice*0.99 - float64(i)*instrument.TickSize
		_, err := this.controller.ctp.CreateOrder(instrumentID, ctp.DirectionBuy, ctp.OffsetFlagOpen, instrument.RoundPrice(price), 1, 0, ctp.UnknownTriggerDirection, func(ctpOrderID string, err error) {
			orderID = ctpOrderID
		})
		if err != nil {
			result = fmt.Sprintf("失败。error：%s", err)
		} else {
			orderIDs = append(orderIDs, orderID)
		}
		this.SendMsgf("发送挂单的订单: %s，结果：%s", orderID, result)
	}

	for i := 0; i < 5; i++ {
		result := "成功"
		orderID := ""
		price := lastPrice*0.99 - float64(i)*instrument.TickSize
		_, err := this.controller.ctp.CreateOrder(instrumentID, ctp.DirectionBuy, ctp.OffsetFlagOpen, instrument.RoundPrice(price), 1, 0, ctp.UnknownTriggerDirection, func(ctpOrderID string, err error) {
			orderID = ctpOrderID
		})
		if err != nil {
			result = fmt.Sprintf("失败。error：%s", err)
		} else {
			orderIDs = append(orderIDs, orderID)
		}
		this.SendMsgf("发送取消的订单: %s，结果：%s", orderID, result)

		time.Sleep(1 * time.Second)

		if _, err := this.controller.ctp.CancelOrder(orderID, ""); err == nil {
			this.SendMsgf("取消订单成功: %s", orderID)
		} else {
			this.SendMsgf("取消订单失败: %s，error: %s", orderID, err)
		}
	}

	this.SendMsgf("使用一下命令获取订单合规报告: ```.ctpOrders %s summary```", strings.Join(orderIDs, ","))
	return true
}

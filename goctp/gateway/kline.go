package gateway

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/wizhodl/goctp/def"

	cmap "github.com/orcaman/concurrent-map"
	log "github.com/sirupsen/logrus"
)

/* K 线相关的代码 */

type KLine struct {
	def.KLine
	Datetime string `json:"datetime" form:"-"`
	Updated  bool   `json:"updated,omitempty" form:"-"` // 是否更新数据，可以同步更新3m, 5m
	Changed  bool   `json:"changed,omitempty" form:"-"` // 是否允许修改降低数据
	First    bool   `json:"-" form:"-"`
}

// 获取校对的时间
type Timer interface {
	PoofTime(exchange string, period string, timestamp int64) int64
}

type KLines []KLine

// 聚合K线
func (ks KLines) Aggregate(timer Timer, period string) (newKlines KLines) {
	var tks = make(map[int64][]KLine)
	var ts []int64

	for _, k := range ks {
		time := timer.PoofTime(k.Exchange, period, k.Time)
		if _, ok := tks[time]; !ok {
			ts = append(ts, time)
		}
		tks[time] = append(tks[time], k)
	}

	for _, timestamp := range ts {
		ks := tks[timestamp]
		if len(ks) <= 0 {
			continue
		}

		var volume, quoteVolume, high, low float64
		for _, k := range ks {
			volume += k.Volume
			quoteVolume += k.QuoteVolume
			if high < k.High {
				high = k.High
			}
			if low == 0 || low > k.Low {
				low = k.Low
			}
		}

		var k KLine
		k.Exchange = ks[0].Exchange
		k.Symbol = ks[0].Symbol
		k.Period = period
		k.Open = ks[0].Open
		k.Close = ks[len(ks)-1].Close
		k.High = high
		k.Low = low
		k.Volume = volume
		k.QuoteVolume = quoteVolume
		k.Time = timestamp
		newKlines = append(newKlines, k)
	}
	return
}

// 补全K线
func (ks *KLines) Completion(seconds int64) {
	if len(*ks) <= 1 {
		return
	}

	exists := make(map[int64]*KLine)
	for idx := range *ks {
		exists[(*ks)[idx].Time] = &(*ks)[idx]
	}

	startAt := (*ks)[0].Time
	endAt := (*ks)[len(*ks)-1].Time
	var newKs []KLine
	count := 0
	for i := startAt; i <= endAt; i += seconds {
		//idx := (i - startAt) / seconds
		if exists[i] == nil && exists[i-seconds] != nil {
			count++
			lastest := exists[i-seconds]
			var k KLine
			copier.Copy(&k, lastest)
			k.Time = i
			k.Volume = 0
			k.QuoteVolume = 0
			k.Open = lastest.Close
			k.High = lastest.Close
			k.Low = lastest.Close
			newKs = append(newKs, k)
			//temp := append([]KLine{k}, (*ks)[idx:]...)
			//*ks = append((*ks)[:idx], temp...)
			exists[i] = &k
		}
	}
	if len(newKs) > 0 {
		*ks = append(*ks, newKs...)
		ks.Sort()
	}
}

func (ks *KLines) Sort() {
	sort.SliceStable(*ks, func(i, j int) bool {
		return (*ks)[i].Time < (*ks)[j].Time
	})
}

func (ks *KLines) Reverse() {
	sort.SliceStable(*ks, func(i, j int) bool {
		return (*ks)[i].Time > (*ks)[j].Time
	})
}

func (ks KLines) Unique() (newKlines KLines) {
	var exists = make(map[string]bool)
	for _, k := range ks {
		k.Exchange = strings.ToUpper(k.Exchange)
		k.Symbol = strings.ToUpper(k.Symbol)
		key := fmt.Sprintf("%s:%d", k.Bucket(), k.Time)
		if _, ok := exists[key]; !ok {
			exists[key] = true
			newKlines = append(newKlines, k)
		}
	}
	return
}

func (ks KLines) SelectPeriods(periods ...string) (newKlines KLines) {
	for _, k := range ks {
		if k.Period != "1min" && k.Period != "1hour" && k.Period != "1day" {
			continue
		}
		newKlines = append(newKlines, k)
	}
	return
}

func (this *GatewayServer) tickerLoop() {
	msgChan := this.klineChan
	klines := cmap.New()

	go func() {
		ticker := time.NewTicker(time.Second * time.Duration(this.opts.KLine.UpdateInternal))
		for range ticker.C {
			start := time.Now()
			batchSize := this.opts.KLine.BatchSize
			var tks KLines
			for item := range klines.IterBuffered() {
				var k KLine
				k.Updated = true
				ok := item.Val.(*def.KLine)
				copier.Copy(&k, ok)
				tks = append(tks, k)
				klines.Remove(item.Key)
			}
			for i := 0; i < len(tks); i += batchSize {
				kls := tks[i:min(i+batchSize, len(tks))]
				this.updateKLine(kls)
				time.Sleep(time.Millisecond * 10)
			}
			fields := log.Fields{
				"kline_len": len(tks),
				"spent":     time.Since(start),
			}
			if len(tks) > 0 {
				log.WithFields(fields).Info("update kline")
			} else {
				log.WithFields(fields).Debug("kline empty, not updated")
			}
		}
	}()

	for {
		select {
		case <-this.exitChan:
			return
		case msg := <-msgChan:
			fields := log.Fields{
				"Exchange":     msg.Exchange,
				"InstrumentID": msg.Symbol,
				"KLine":        msg,
			}
			log.WithFields(fields).Info("receive kline message")
			key := fmt.Sprintf("%s:%d", msg.Bucket(), msg.Time)
			klines.Set(key, msg)
		}
	}
}

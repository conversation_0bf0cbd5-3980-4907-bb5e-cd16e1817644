package gateway

import (
	"bytes"
	"fmt"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/wizhodl/goctp/def"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	log "github.com/sirupsen/logrus"
	bolt "go.etcd.io/bbolt"
)

func failJSON(ctx *gin.Context, format string, args ...any) {
	ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"message": fmt.Sprintf(format, args...)})
}

func errorJSON(c *gin.Context, code int, message interface{}) {
	c.AbortWithStatusJSON(code, gin.H{"message": message})
}

func okJSON(ctx *gin.Context, v any) {
	ctx.JSON(http.StatusOK, v)
}

func okString(ctx *gin.Context, v string) {
	ctx.String(http.StatusOK, v)
}

func badRequest(ctx *gin.Context, format string, args ...any) {
	ctx.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"message": fmt.Sprintf(format, args...)})
}

// 查询k线
// 1. 根据时间去对应的路径（可能存在多个）查询所有的基础K线
// 2. 根据基础K线 （1min, 1hour, 1day) 聚合成需要的K线 （15min, 2hour, 3day)等
// 3. 补全不存在的K线，（比如服务器维护，都采用上条存在的数据补全)
// 4. 排序返回
func (this *GatewayServer) lookupKLineApi(ctx *gin.Context) {
	var form LookupKLineForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args")
		return
	}
	form.Default()

	// 格式化参数
	_, ok := def.Periods[form.Period]
	if !ok {
		badRequest(ctx, "invalid periods")
		return
	}
	var startAt, endAt int64
	period, base := def.Periods.Base(form.Period)
	form.Limit *= base

	// 根据向前推的天数，计算 limit 需要调整的数量
	forwardDays := 10
	limitForward := int64(0)
	if strings.HasSuffix(form.Period, "minute") {
		limitForward = int64(forwardDays*24*60) * base
	} else if strings.HasSuffix(form.Period, "hour") {
		limitForward = int64(forwardDays*24) * base
	} else if strings.HasSuffix(form.Period, "day") || strings.HasSuffix(form.Period, "day") {
		limitForward = int64(forwardDays) * base
	}
	form.Limit += limitForward
	seconds := def.Periods[period]
	if form.Time == 0 {
		form.Time = this.opts.KLine.Periods.IndexTime(form.Exchange, period, time.Now().Add(24*time.Duration(forwardDays)*time.Hour).Unix(), base)
	}
	// 根据配置里面的周期，校验好时间
	startAt = this.opts.KLine.Periods.IndexTime(form.Exchange, period, form.Time) - form.Limit*seconds
	endAt = form.Time

	var klines = KLines{}
	var header def.KLineHeader
	var exists = make(map[int64]*KLine)
	startKey, endKey := def.ToKey(startAt), def.ToKey(endAt)

	header.Exchange = form.Exchange
	header.Symbol = form.InstrumentID
	header.Period = period

	// 获取存储路径
	paths := this.opts.PathByRange(header, startAt, endAt)
	for _, path := range paths {
		if _, err := os.Stat(path); err != nil {
			continue
		}
		db := this.openDB(path, false)
		if db == nil {
			continue
		}

		// 获取路径下面的所有K线
		err := db.View(func(tx *bolt.Tx) error {
			bkt := tx.Bucket(header.Bucket())
			if bkt != nil {
				c := bkt.Cursor()
				for k, v := c.Seek(startKey); k != nil && bytes.Compare(k, endKey) <= 0; k, v = c.Next() {
					var kline KLine
					kline.KLineHeader = header
					kline.Unpack(k, v)
					klines = append(klines, kline)
					exists[kline.Time] = &kline
				}
			}
			return nil
		})
		if err != nil {
			log.WithError(err).Info("view error")
			badRequest(ctx, "db error")
			return
		}
	}

	// 根据基础K线聚合成需要的Period的K线
	klines = klines.Aggregate(this.opts.KLine.Periods, form.Period)
	// 补全K线
	klines.Completion(def.Periods[form.Period])
	// 反序
	klines.Reverse()
	for idx := range klines {
		klines[idx].Datetime = time.Unix(klines[idx].Time, 0).Format("2006-01-02 15:04:05")
	}
	okJSON(ctx, klines)
}

func (this *GatewayServer) updateKLineApi(ctx *gin.Context) {
	var klines KLines
	if err := ctx.ShouldBindWith(&klines, binding.JSON); err != nil {
		badRequest(ctx, "invalid args")
		return
	}

	this.updateKLine(klines)

	okJSON(ctx, map[int]int{})
}

// 更新K线，有两种情况
// 1. 就是跟新一条数据(http定时更新）
// 2. 根据分钟线计算出来新增的数据，同步更新1hour, 1day的数据
func (this *GatewayServer) updateKLine(klines KLines) {
	this.writeLock.Lock()
	defer this.writeLock.Unlock()

	// 首先去重排序
	klines = klines.Unique().SelectPeriods("1min", "1hour", "1day")
	klines.Sort()
	if len(klines) == 0 {
		return
	}

	// 根据1min生成1hour, 1day的K线
	var newKlines KLines
	for idx, k := range klines {
		if k.Volume < 0 {
			continue
		}
		if k.Updated && k.Period == "1min" {
			var historyKline KLine
			this.historyKLine(&k, &historyKline)
			if historyKline.Time == k.Time {
				if !k.Changed && historyKline.Volume >= k.Volume {
					k.Volume = 0
					k.QuoteVolume = 0
					continue
				}
				k.Volume -= historyKline.Volume
				if k.QuoteVolume > 0 {
					k.QuoteVolume -= historyKline.QuoteVolume
				}
			}
			klines[idx].Updated = false
			if k.Volume >= 0 {
				startAt := k.Time
				for _, period := range this.opts.KLine.GenPeriods {
					k.Period = period
					k.Time = this.opts.KLine.Periods.IndexTime(k.Exchange, period, startAt)
					k.First = (startAt == k.Time)
					newKlines = append(newKlines, k)
				}
			}
		}
		newKlines = append(newKlines, klines[idx])
	}

	var pathKlines = make(map[string][]KLine)
	for _, kline := range newKlines {
		path := this.opts.PathByTime(kline.KLineHeader, kline.Time)
		pathKlines[path] = append(pathKlines[path], kline)
	}

	var wait sync.WaitGroup
	// 根据路径写入kline
	for path, klines := range pathKlines {
		wait.Add(1)
		go func(path string, klines []KLine) {
			defer wait.Done()
			db := this.openDB(path, true)
			if db == nil {
				return
			}
			err := db.Batch(func(tx *bolt.Tx) error {
				for _, kline := range klines {
					name := kline.Bucket()
					bkt := tx.Bucket(name)
					if bkt == nil {
						var err error
						bkt, err = tx.CreateBucket(name)
						if err != nil {
							return err
						}
					}

					if kline.Updated {
						var k KLine
						kbuf := bkt.Get(kline.Key())
						if kbuf != nil {
							k.Unpack(kline.Key(), kbuf)
						}

						// 如果不是首根K线
						if !kline.First && k.Open > 0 {
							kline.Open = k.Open
						}
						if k.High > kline.High {
							kline.High = k.High
						}
						if k.Low < kline.Low && k.Low > 0 {
							kline.Low = k.Low
						}
						kline.Volume += k.Volume
						kline.QuoteVolume += k.QuoteVolume
					}

					err := bkt.Put(kline.Key(), kline.Value())
					if err != nil {
						return err
					}
				}
				return nil
			})

			if err != nil {
				log.WithError(err).Info("batch update error")
				this.closeDB(path)
			}
		}(path, klines)
	}
	wait.Wait()
}

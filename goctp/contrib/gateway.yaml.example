
id: "ctpgateway"
slack_channel: "ctpgateway"
debug: true
check_sign: false
release_binary_dir: ""

http_addr: ":8204"

logger:
    level: "info"
    depth: 5

api_keys:
    -
        api_key: turtle
        encrypted_api_secret: ""
        api_secret: 

tianqin:
    script: /home/<USER>/workspace/goctp/scripts/tq_kline.py
    account: ""
    password: ""

ctp:
    remote_launch: false
    encrypted_secrets: ""
    trade_front: "tcp://***************:10130"
    quote_front: "tcp://***************:10131"
    broker_id: "9999"
    investor_id: "182541"
    password: "VaDfc!x7kuX2"
    app_id: "simnow_client_test"
    auth_code: "****************"

kline:
    enable_tianqin: true
    tianqin_simnow: true
    tianqin_command_timeout: 60
    product_ids: ["sc", "ag"]
    periods:
        -
            exchanges: ["SHFE", "DCE", "CZ<PERSON>", "CFF<PERSON>", "INE"]
            week_index: 345600
            day_index: -50400

    period_tmpl:
        1min: "{exchange}/1min-{year}-{month}.dat"
        1hour: "{exchange}/1hour-1day-{year}.dat"
        1day: "{exchange}/1hour-1day-{year}.dat"

    dir: "./data/kstore"

    gen_periods:
        - "1hour"
        - "1day"

    update_internal: 5
    batch_size: 500
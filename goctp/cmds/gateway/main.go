package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/wizhodl/goctp/gateway"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/secrets"
)

var cfgName string
var version bool

var commitHash string
var buildTime string

func main() {
	flag.StringVar(&cfgName, "cfg", "../../contrib/gateway.yaml", "配置文件路径")
	flag.BoolVar(&version, "version", false, "打印版本号")
	flag.Parse()

	if version {
		fmt.Printf("Build: %s/(%s)\n", commitHash, buildTime)
		os.Exit(0)
	}

	opts := gateway.NewOption()
	err := opts.Load(cfgName)
	if err != nil {
		log.Fatal(err)
	}
	opts.Print()

	logLevel := os.Getenv("CTP_GATEWAY_LOG_LEVEL")
	if logLevel == "" && opts.Debug {
		logLevel = "DEBUG"
	}
	zlog.SetLogger(zlog.NewLogger(logLevel, ""))

	if opts.AutoCloseDays > 28 {
		opts.AutoCloseDays = 28
		zlog.Warnf("can not set AutoCloseDays greater than 28")
	} else if opts.AutoCloseDays < 3 {
		opts.AutoCloseDays = 3
		zlog.Warnf("can not set AutoCloseDays smaller than 3")
	}

	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)
	if srv, err := gateway.NewGatewayServer(opts, commitHash, buildTime); err != nil {
		zlog.Errorf("start gateway server error: %s", err)
	} else {
		if !srv.Debug && !secrets.CheckEncryptSalt() {
			zlog.Warnf("encrypt salt is empty while not debugging")
		}
		srv.Serve()
		<-signalChan
		srv.Exit()
	}

}

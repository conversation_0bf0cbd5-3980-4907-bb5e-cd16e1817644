package main

import (
	"chaintools/core"
	"chaintools/utils"
	"chaintools/utils/ulog"
	"math"
	"math/big"
	"os"
	"strconv"
)

func main() {
	privateKey := os.Getenv("DEBUG_PRIVATE_KEY")
	if privateKey == "" {
		privateKey = utils.SurveyPassword("请输入私钥:")
	}

	ulog.Infof("私钥: %s...%s (%d位)", privateKey[:4], privateKey[len(privateKey)-4:], len(privateKey))

	endpoint := os.Getenv("DEBUG_ENDPOINT")
	if endpoint == "" {
		endpoint = utils.SurveyInput("请输入节点地址:")
	}

	client, err := core.NewEthClient(endpoint, privateKey)
	if err != nil {
		ulog.Errorf("Failed to create client: %v", err)
		return
	}

	balance, err := client.GetBalance(client.GetPublicAddress())
	if err != nil {
		ulog.<PERSON>rrorf("Failed to get balance: %v", err)
		return
	}
	ulog.Infof("地址余额: %.6f", float64(balance.Int64())/1e18)

	choiceEnv := os.Getenv("DEBUG_CHOICE")
	choice := -1
	if choiceEnv != "" {
		choice64, _ := strconv.ParseInt(choiceEnv, 10, 64)
		choice = int(choice64)
	} else {
		choice, _ = utils.SurveySelect("请选择交易类型:", []string{"转账", "转 Token"})
	}

	switch choice {
	case 0:
		transfer(client)
	case 1:
		transferToken(client)
	}
}

func transfer(client *core.EthClient) {
	addr := os.Getenv("DEBUG_ADDR")
	if addr == "" {
		addr = utils.SurveyInput("请输入目标地址:")
	}

	amountEnv := os.Getenv("DEBUG_AMOUNT")
	var amount float64
	if amountEnv != "" {
		amount, _ = strconv.ParseFloat(amountEnv, 64)
	} else {
		amount = utils.SurveyFloat("请输入转账金额:")
	}

	amountBig := big.NewInt(int64(amount * 1e18))

	fee, err := client.EstimateTransferFee(amountBig, addr, "")
	if err != nil {
		ulog.Errorf("Failed to estimate transfer fee: %v", err)
		return
	}
	ulog.Infof("预估手续费: %s", fee.String())

	tx, err := client.Transfer(amountBig, addr, core.TransferOptions{})
	if err != nil {
		ulog.Errorf("Failed to transfer: %v", err)
		return
	}
	ulog.Infof("Transaction hash: %s", tx)
}

func transferToken(client *core.EthClient) {
	tokenAddr := os.Getenv("DEBUG_TOKEN_ADDR")
	if tokenAddr == "" {
		tokenAddr = utils.SurveyInput("请输入 Token 地址:")
	}

	decimals, err := client.GetTokenDecimals(tokenAddr)
	if err != nil {
		ulog.Errorf("Failed to get token decimals: %v", err)
		return
	}
	ulog.Infof("Token decimals: %d", decimals)

	tokenBalance, err := client.GetTokenBalance(tokenAddr, client.GetPublicAddress())
	if err != nil {
		ulog.Errorf("Failed to get token balance: %v", err)
		return
	}
	ulog.Infof("Token balance: %.4f", float64(tokenBalance.Int64())/math.Pow10(int(decimals)))

	addr := os.Getenv("DEBUG_ADDR")
	if addr == "" {
		addr = utils.SurveyInput("请输入目标地址:")
	}

	amountEnv := os.Getenv("DEBUG_AMOUNT")
	var amount float64
	if amountEnv != "" {
		amount, _ = strconv.ParseFloat(amountEnv, 64)
	} else {
		amount = utils.SurveyFloat("请输入转账金额:")
	}

	amountBig := big.NewInt(int64(amount * float64(math.Pow10(int(decimals)))))

	fee, err := client.EstimateTransferFee(amountBig, addr, tokenAddr)
	if err != nil {
		ulog.Errorf("Failed to estimate transfer fee: %v", err)
		return
	}
	ulog.Infof("预估手续费: %s", fee.String())

	tx, err := client.TransferToken(tokenAddr, amountBig, addr, core.TransferOptions{})
	if err != nil {
		ulog.Errorf("Failed to transfer: %v", err)
		return
	}

	ulog.Infof("Transaction hash: %s", tx)
}

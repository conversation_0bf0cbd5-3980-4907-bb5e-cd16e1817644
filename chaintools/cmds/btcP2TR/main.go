package main

import (
	"bytes"
	"chaintools/utils/ulog"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"github.com/btcsuite/btcd/btcutil"
	"github.com/btcsuite/btcd/chaincfg"
	"github.com/btcsuite/btcd/chaincfg/chainhash"
	"github.com/btcsuite/btcd/txscript"
	"github.com/btcsuite/btcd/wire"
)

func P2TRTransactionTest() (string, error) {
	// var chainCfgParams *chaincfg.Params
	// if client.Network == MainNet {
	// 	chainCfgParams = &chaincfg.MainNetParams
	// } else {
	// 	chainCfgParams = &chaincfg.TestNet3Params
	// }

	var sequenceStart uint32 = 3758096384
	// var sequenceStart uint32 = 3760359660
	var sequenceEnd uint32 = 0xffffffff
	const needPrefix = "aabbcc"

	chainCfgParams := &chaincfg.TestNet3Params
	// privKey := "cSKuuShBFBXagA2tfqsKxR5pnry28UEquRKbHwDpk2kDsucMmxRc"
	// spendAddrStr := "tb1pgmk5klnduuw4elylzad2c84qu0td3lnsgcgpp83zztk7kc4zp3nspnj7ut"
	// destAddrStr := "tb1pj58ff6j3wm4zyznffps262xadh9ufxd8lw9q5fpekm57yeegr8zsksmdnd"
	privKey := "cVWirEgfn9Qb7tVomg3CdJvX2v8iu5DjW5UVrGrbkEuLnHxB3BFp"
	spendAddrStr := "tb1prrg576sr0pdhh9qy4haz0fvfx7l74hfhnng2a0qm2c2wr9a8rvfsfntdfk"
	utxoHashStr := "1a8bd729bf72146b31bf61e13c46d192ce4053eae92325e18a33b9c5f130bccb"
	utxoPosition := 1
	var utxoAmount int64 = 1000000

	outputAddrs := []string{
		"tb1p7nlw5v008jcjmur668c76fst9qm0xdlyvxzkszf0l7ryh74r4tts628tgf",
		"tb1prrg576sr0pdhh9qy4haz0fvfx7l74hfhnng2a0qm2c2wr9a8rvfsfntdfk",
	}
	outputAmounts := []int64{
		200294,
		799398,
	}

	spendAddr, err := btcutil.DecodeAddress(spendAddrStr, chainCfgParams)
	if err != nil {
		return "", err
	}

	wif, err := btcutil.DecodeWIF(privKey)
	if err != nil {
		return "", err
	}

	spenderAddrByte, err := txscript.PayToAddrScript(spendAddr)
	if err != nil {
		return "", err
	}

	utxoHash, err := chainhash.NewHashFromStr(utxoHashStr)
	if err != nil {
		return "", err
	}

	outPoint := wire.NewOutPoint(utxoHash, uint32(utxoPosition))

	redeemTx := wire.NewMsgTx(1)

	txIn := wire.NewTxIn(outPoint, nil, [][]byte{})
	redeemTx.AddTxIn(txIn)

	for i, outputAddr := range outputAddrs {
		addr, err := btcutil.DecodeAddress(outputAddr, chainCfgParams)
		if err != nil {
			return "", err
		}

		addrByte, err := txscript.PayToAddrScript(addr)
		if err != nil {
			return "", err
		}

		txOut := wire.NewTxOut(outputAmounts[i], addrByte)
		redeemTx.AddTxOut(txOut)
	}

	fetcher := txscript.NewCannedPrevOutputFetcher(spenderAddrByte, utxoAmount)
	sigHashes := txscript.NewTxSigHashes(redeemTx, fetcher)

	var sequence = sequenceStart

	ulog.Debugf("Start checking txids from sequence %d to %d", sequenceStart, sequenceEnd)
	for {
		txIn.Sequence = sequence
		signature, err := txscript.TaprootWitnessSignature(redeemTx, sigHashes, 0, utxoAmount, spenderAddrByte, txscript.SigHashAll, wif.PrivKey)
		if err != nil {
			panic(err)
		}
		redeemTx.TxIn[0].Witness = signature

		var signedTx bytes.Buffer
		redeemTx.Serialize(&signedTx)

		txHash := redeemTx.TxHash().String()
		if strings.HasPrefix(txHash, needPrefix) {
			hexSignedTx := hex.EncodeToString(signedTx.Bytes())
			ulog.Debugf("found tx: %s at sequence %d", txHash, sequence)
			ulog.Debugf("raw tx: %s", hexSignedTx)
			ulog.Debugf("with witness: %s", hex.EncodeToString(redeemTx.TxIn[0].Witness[0]))
			return txHash, nil
		}

		if sequence == sequenceEnd {
			break
		}

		if sequence%10000 == 0 {
			ulog.Debugf("Checked sequence %d", sequence)
		}

		sequence++
	}

	return "", fmt.Errorf("not found")
}

func main() {
	var now = time.Now()
	txHash, err := P2TRTransactionTest()
	if err != nil {
		ulog.Errorf("Error creating transaction: %v", err)
		return
	}
	ulog.Infof("Time used: %s", time.Since(now))
	ulog.Infof("Transaction hash: %s", txHash)
}

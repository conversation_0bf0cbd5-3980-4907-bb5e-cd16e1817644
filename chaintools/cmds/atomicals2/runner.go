package main

import (
	"bufio"
	"chaintools/utils/ulog"
	"context"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// improved version, ref: https://github.com/hack-ink/atomicalsir/blob/main/src/main.rs
// 1. switch wallet in the program
// 2. switch proxy in the program, not touching the .env file
// 3. log to file
// 4. query unconfirmed tx count, if too many, wait

type Wallet struct {
	Path    string
	Address string
}

type AtomicalWallet struct {
	Phrase  string
	Primary struct {
		Address string
		Path    string
		WIF     string `json:"WIF"`
	}
	Funding struct {
		Address string
		Path    string
		WIF     string `json:"WIF"`
	}
	Imported struct {
	}
	Path string
}

func main() {
	var num int
	var maxGas int
	var minGas int
	var timeOut int
	var ticker string
	var changeProxyRetry int
	var exitRetry int
	var gasPriceMultiplier float64
	var receiverAddress string
	var proxies string
	var checkUnconfirmedTx bool

	flag.IntVar(&num, "num", 1, "铭文个数")
	flag.IntVar(&maxGas, "max_gas", 180, "最大 gas (180)")
	flag.IntVar(&minGas, "min_gas", 90, "最小 gas (90)")
	flag.IntVar(&timeOut, "timeout", 60*60, "超时时间 (3600秒)，超过这个时间重新执行，不继续搜索")
	flag.IntVar(&changeProxyRetry, "change_proxy_retry", 0, "切换代理重试次数，0 表示不切换")
	flag.IntVar(&exitRetry, "exit_retry", 0, "重试次数，0 表示不退出")
	flag.StringVar(&ticker, "ticker", "quark", "铭文类型 (quark)")
	flag.Float64Var(&gasPriceMultiplier, "gas_price_multiplier", 1.0, "gas price 乘数 (1.00)")
	flag.StringVar(&receiverAddress, "receiver_address", "", "接收地址")
	flag.BoolVar(&checkUnconfirmedTx, "check_unconfirmed_tx", false, "检查 unconfirmed tx 数量不大于 12 个")
	flag.StringVar(&proxies, "proxy_list", "https://ep.atomicals.xyz/proxy,https://ep.nextdao.xyz/proxy", "代理列表，逗号分隔")

	flag.Parse()

	ulog.Warnf("num: %d, max_gas: %d, min_gas: %d, timeout: %d, ticker: %s, change_proxy_retry: %d, exit_retry: %d, gas_price_muliplier: %.2f", num, maxGas, minGas, timeOut, ticker, changeProxyRetry, exitRetry, gasPriceMultiplier)

	if gasPriceMultiplier > 1.15 {
		ulog.Errorf("gas_price_multiplier should be less than 1.15")
		return
	}

	proxies = strings.TrimSpace(proxies)
	proxyList := []string{}
	if proxies != "" {
		proxyList = strings.Split(proxies, ",")
	}
	runner := NewRunner(proxyList, num, maxGas, minGas, timeOut, ticker, changeProxyRetry, exitRetry, gasPriceMultiplier, receiverAddress, checkUnconfirmedTx)
	runner.Run()
}

type Runner struct {
	Num                int
	MaxGas             int
	MinGas             int
	Timeout            int
	Ticker             string
	ChangeProxyRetry   int
	ExitRetry          int
	GasPriceMultiplier float64
	ReceiverAddress    string
	CheckUnconfirmedTx bool

	ProxyEndpoint *string
	ProxyList     []string
	MintSuccess   *atomic.Bool

	Wallets       []Wallet
	CurrentWallet *Wallet
	LaunchTime    time.Time

	LogMutex sync.Mutex
}

func NewRunner(proxyList []string, num int, maxGas int, minGas int, timeout int, ticker string, changeProxyRetry int, exitRetry int, gasPriceMultiplier float64, receiverAddress string, checkUnconfirmedTx bool) *Runner {
	proxyEndpoint := os.Getenv("ELECTRUMX_PROXY_BASE_URL")
	r := &Runner{
		Num:                num,
		MaxGas:             maxGas,
		MinGas:             minGas,
		Timeout:            timeout,
		Ticker:             ticker,
		ChangeProxyRetry:   changeProxyRetry,
		ExitRetry:          exitRetry,
		GasPriceMultiplier: gasPriceMultiplier,
		ReceiverAddress:    receiverAddress,
		CheckUnconfirmedTx: checkUnconfirmedTx,

		ProxyList:     proxyList,      // 代理列表
		ProxyEndpoint: &proxyEndpoint, // 当前使用的代理，会被切换
		MintSuccess:   &atomic.Bool{}, // 每次 mint 设为 false，如果成功了，会被设置为 true

		CurrentWallet: &Wallet{},
		LaunchTime:    time.Now(),

		LogMutex: sync.Mutex{},
	}
	return r
}

func (this *Runner) Run() {
	successCount := 0

	for {
		if successCount >= this.Num {
			break
		}

		ulog.Infof("[mint] %s: %d total", this.Ticker, this.Num)

		// 从 mempool 获取最新的 gasPrice
		gasPrice, err := getGasPrice(this.GasPriceMultiplier)
		if err != nil {
			ulog.Errorf("failed getting gas price: %s, wait 5s", err)
			time.Sleep(5 * time.Second)
			continue
		}
		if gasPrice > this.MaxGas {
			ulog.Warnf("gas price too high: %d, use max gas price: %d", gasPrice, this.MaxGas)
			gasPrice = this.MaxGas
		}
		if gasPrice < this.MinGas {
			ulog.Warnf("gas price too low: %d, use min gas price: %d", gasPrice, this.MinGas)
			gasPrice = this.MinGas
		}

		ulog.Infof("minting #%d with gas price: %d", successCount+1, gasPrice)

		this.Wallets = this.LoadWallets()
		if len(this.Wallets) == 0 {
			ulog.Errorf("no wallet found, wait 5s")
			time.Sleep(5 * time.Second)
			continue
		}
		ulog.Infof("found %d wallet(s)", len(this.Wallets))

		for _, wallet := range this.Wallets {
			this.CurrentWallet = &wallet
			// 检查 unconfirmed tx 数量
			txCount, err := queryUnconfirmedCount(wallet.Address)
			if err != nil {
				ulog.Errorf("failed querying unconfirmed tx: %s, wait 5s", err)
				time.Sleep(5 * time.Second)
				continue
			}
			if txCount > 12 {
				ulog.Warnf("unconfirmed tx count too high: %d, wait 5s", txCount)
				time.Sleep(5 * time.Second)
				continue
			}

			cmdArgs := []string{
				"yarn",
				"cli",
				"mint-dft",
				"quark",
				"--satsbyte",
				fmt.Sprintf("%d", gasPrice),
			}
			if this.ReceiverAddress != "" {
				cmdArgs = append(cmdArgs, "--initialowner", this.ReceiverAddress)
			}

			envs := []string{
				fmt.Sprintf("WALLET_FILE=%s", wallet.Path),
				fmt.Sprintf("ELECTRUMX_PROXY_BASE_URL=%s", *this.ProxyEndpoint),
			}

			this.MintSuccess.Store(false)
			// 如果 execute 中成功 mint 了，就会设置 MintSuccess 为 true
			cmd, _, err := this.Execute(cmdArgs, envs)
			if err != nil {
				ulog.Errorf("failed executing command: %s", err)
				time.Sleep(5 * time.Second)
				continue
			}
			if err := cmd.Wait(); err != nil {
				ulog.Errorf("failed waiting for command: %s", err)
				time.Sleep(5 * time.Second)
				continue
			}

			if !this.MintSuccess.Load() {
				ulog.Errorf("mint failed, wait 5s")
				time.Sleep(5 * time.Second)
				continue
			}

			successCount++
			ulog.Warnf("success minted #%d", successCount)
		}
	}
}

func (this *Runner) Execute(cmdArgs []string, envs []string) (command *exec.Cmd, kill context.CancelFunc, er error) {
	var ctx context.Context
	if this.Timeout > 0 {
		ctx, kill = context.WithTimeout(context.Background(), time.Duration(this.Timeout)*time.Second)
	} else {
		ctx, kill = context.WithCancel(context.Background())
	}

	if len(cmdArgs) == 0 {
		er = errors.New("no command provided")
		return
	}

	command = exec.CommandContext(ctx, cmdArgs[0], cmdArgs[1:]...)
	command.Env = envs

	stdout, err := command.StdoutPipe()
	if err != nil {
		ulog.Debugf("failed creating command stdout pipe: %s", err)
		er = err
		return
	}
	stdoutReader := bufio.NewReader(stdout)

	stderr, err := command.StderrPipe()
	if err != nil {
		ulog.Debugf("failed creating command stderr pipe: %s", err)
		er = err
		return
	}
	stderrReader := bufio.NewReader(stderr)

	if err := command.Start(); err != nil {
		ulog.Debugf("failed starting command: %s", err)
		er = err
		return
	}

	var errCounter = atomic.Int32{}
	outputCallback := func(line string) {
		// 如果在输出中找到了 success 字符串，就认为成功了
		if strings.Contains(line, "\"success\": true,") {
			this.MintSuccess.Store(true)
		}

		// nonRecoverableErrors := []string{
		// 	"too-long-mempool-chain, too many descendants",
		// 	"insufficient fee, rejecting replacement",
		// 	"502 Bad Gateway",
		// 	"Request failed with status code 500",
		// }
		// for e := range nonRecoverableErrors {
		// 	if strings.Contains(line, e) {
		// 		kill()
		// 	}
		// }

		if strings.Contains(line, "the transaction was rejected by network rules.") {
			// 如果在输出中找到了 "the transaction was rejected by network rules." 字符串，就认为失败了
			// 连续失败5次，就切换代理
			errCounter.Add(1)
			if this.ChangeProxyRetry > 0 && (errCounter.Load() > int32(this.ChangeProxyRetry)) {
				this.ChangeProxy()
				kill()
			}
			if this.ExitRetry > 0 && (errCounter.Load() > int32(this.ExitRetry)) {
				kill()
			}
		}
	}

	go this.HandleOutput(stdoutReader, "stdout", outputCallback)
	go this.HandleOutput(stderrReader, "stderr", outputCallback)
	return
}

func (this *Runner) HandleOutput(reader *bufio.Reader, name string, callback func(line string)) {
	for {
		str, err := reader.ReadString('\n')
		if err != nil {
			break
		}
		callback(str)
		ulog.Debugf("%s >   %s", name, strings.TrimSpace(str))
		this.WriteLog(name, str)
	}
}

func (this *Runner) WriteLog(name string, str string) {
	this.LogMutex.Lock()
	defer this.LogMutex.Unlock()

	walletSuffix := this.CurrentWallet.Address[len(this.CurrentWallet.Address)-4:]
	launchTime := this.LaunchTime.Format("20060102_150405")
	file, err := os.OpenFile(fmt.Sprintf("logs/%s_%s.log", walletSuffix, launchTime), os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		ulog.Debugf("failed opening log file: %s", err)
		return
	}
	defer file.Close()

	timeStr := time.Now().Format("2006-01-02 15:04:05")
	_, err = file.WriteString(fmt.Sprintf("%s %s >  %s", timeStr, name, str))
	if err != nil {
		ulog.Debugf("failed writing log file: %s", err)
		return
	}
}

func (this *Runner) ChangeProxy() {
	proxy := *this.ProxyEndpoint
	for j, p := range this.ProxyList {
		// ulog.Debugf("checking proxy: %s with %s, %v", p, proxy, p == proxy)
		if p == proxy {
			nextIndex := (j + 1) % len(this.ProxyList)
			// ulog.Debugf("next proxy: %s, index: %d", proxyList[nextIndex], nextIndex)
			this.ProxyEndpoint = &this.ProxyList[nextIndex]
			break
		}
	}
}

func (this *Runner) LoadWallets() []Wallet {
	wallets := []Wallet{}
	// reading wallet.*.json files from wallets folder
	files, err := os.ReadDir("wallets")
	if err != nil {
		ulog.Debugf("failed reading wallets folder: %s", err)
	}
	for _, file := range files {
		if strings.HasPrefix(file.Name(), "wallet.") && strings.HasSuffix(file.Name(), ".json") {
			wallet := Wallet{}
			wallet.Path = fmt.Sprintf("wallets/%s", file.Name())
			// ulog.Debugf("wallet path: %s", wallet.Path)
			// read wallet struct from json
			file, err := os.Open(wallet.Path)
			if err != nil {
				ulog.Debugf("failed opening wallet file: %s", err)
				continue
			}
			defer file.Close()
			// read json
			// {
			// 	"phrase": "",
			// 	"primary": {
			// 	  "address": "bc1p0",
			// 	  "path": "m/44'/0'/0'/0/0",
			// 	  "WIF": ""
			// 	},
			// 	"funding": {
			// 	  "address": "bc1pu",
			// 	  "path": "m/44'/0'/0'/1/0",
			// 	  "WIF": ""
			// 	},
			// 	"imported": {}
			//   }
			aWallet := AtomicalWallet{}
			decoder := json.NewDecoder(file)
			err = decoder.Decode(&aWallet)
			if err != nil {
				ulog.Debugf("failed decoding wallet file: %s", err)
				continue
			}
			wallet.Address = aWallet.Funding.Address
			wallets = append(wallets, wallet)
		}
	}

	return wallets
}

type Response struct {
	FastestFee  int `json:"fastestFee"`
	HalfHourFee int `json:"halfHourFee"`
	HourFee     int `json:"hourFee"`
}

func getGasPrice(gasPriceMultiplier float64) (gasPrice int, er error) {
	resp, err := http.Get("https://mempool.space/api/v1/fees/recommended")
	if err != nil {
		ulog.Debugf("%s", err)
		er = err
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		ulog.Debugf("%s", err)
		er = err
		return
	}

	var r Response
	err = json.Unmarshal(body, &r)
	if err != nil {
		ulog.Debugf("%s", err)
		er = err
		return
	}

	gasPrice = int(float64(r.FastestFee) * gasPriceMultiplier) // 高一点，防止被抢
	return
}

func queryUnconfirmedCount(address string) (txCount int, er error) {
	resp, err := http.Get(fmt.Sprintf("https://api.blockcypher.com/v1/btc/main/addrs/%s?unspentOnly=true", address))
	if err != nil {
		er = fmt.Errorf("failed getting unconfirmed tx: %s", err)
		return
	}
	defer resp.Body.Close()

	type Unspent struct {
		UnconfirmedNTx int `json:"unconfirmed_n_tx"`
	}

	unspent := Unspent{}
	decoder := json.NewDecoder(resp.Body)
	err = decoder.Decode(&unspent)
	if err != nil {
		er = fmt.Errorf("failed decoding unconfirmed tx: %s", err)
		return
	}
	txCount = unspent.UnconfirmedNTx
	return
}

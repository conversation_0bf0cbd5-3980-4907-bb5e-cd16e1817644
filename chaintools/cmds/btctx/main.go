package main

import (
	"chaintools/core"
	"chaintools/utils"
	"chaintools/utils/ulog"
	"os"
	"strconv"

	"github.com/shopspring/decimal"
)

func main() {
	networkEnv := os.Getenv("DEBUG_NETWORK")
	network := core.TestNet
	if networkEnv == "" {
		idx, _ := utils.SurveySelect("请选择网络:", []string{"mainnet", "testnet"})
		if idx == 0 {
			network = core.MainNet
		}
	} else if networkEnv == "mainnet" {
		network = core.MainNet
	}

	privateKeyWIF := os.Getenv("DEBUG_PRIVATE_KEY_WIF")
	if privateKeyWIF == "" {
		privateKeyWIF = utils.SurveyPassword("请输入 WIF 私钥:")
		if privateKeyWIF == "" || len(privateKeyWIF) < 52 {
			ulog.Errorf("private key is not set, abort")
			return
		}
	}

	address := os.Getenv("DEBUG_ADDRESS")
	if address == "" {
		address = utils.SurveyInput("请输入地址:")
	}

	var amount float64
	amountInEnv := os.Getenv("DEBUG_AMOUNT")
	if amountInEnv != "" {
		// convert string to float64
		amount, _ = strconv.ParseFloat(amountInEnv, 64)
	} else {
		amount = utils.SurveyFloat("请输入转账金额（单位：BTC）:")
		if amount <= 0 {
			ulog.Errorf("amount is not set, abort")
			return
		}
	}

	satoshiMultiplier := decimal.NewFromInt(1e8)
	amountInSatoshi := decimal.NewFromFloat(amount).Mul(satoshiMultiplier)
	amountInSatoshiStr := amountInSatoshi.StringFixed(0) // 没有小数部分
	amountInSatoshiInt64, err := strconv.ParseInt(amountInSatoshiStr, 10, 64)
	if err != nil {
		ulog.Errorf("Error converting amount to int64: %v", err)
		return
	}
	ulog.Infof("amount in satoshi: %d", amountInSatoshiInt64)

	receivingAddress := os.Getenv("DEBUG_RECEIVING_ADDRESS")
	if receivingAddress == "" {
		receivingAddress = utils.SurveyInput("请输入接收地址:")
	}

	token := os.Getenv("DEBUG_TOKEN")
	if token == "" {
		token = utils.SurveyPassword("请输入 blockcypher API Token:")
	}

	client := core.NewBTCClient(
		privateKeyWIF,
		address,
		network,
		token,
	)

	priorityFees, err := client.GetMediumFeePerByte()
	if err != nil {
		ulog.Errorf("Error getting bitcoin fees: %v", err)
		return
	}
	ulog.Infof("当前推荐费率: %d sat/byte", priorityFees)

	fee := float64(priorityFees) * 141 / 1e8
	price, _ := core.GetBitcoinPrice()
	if price > 0 {
		value := fee * price
		ulog.Infof("一笔普通交易大约需要 %.8f BTC (%.2f USD)", fee, value)
	} else {
		ulog.Infof("一笔普通交易大约需要 %.8f BTC", fee)
	}

	var feePerByte int64
	feePerByteInEnv := os.Getenv("DEBUG_FEE_PER_BYTE")
	if feePerByteInEnv != "" {
		// convert string to int64
		feePerByte, _ = strconv.ParseInt(feePerByteInEnv, 10, 64)
	} else {
		feePerByte = int64(utils.SurveyInt("请输入交易费率（单位：sat/byte）:"))
		if feePerByte <= 0 {
			ulog.Errorf("feePerByte is not set, abort")
			return
		}
	}

	opReturn := os.Getenv("DEBUG_OP_RETURN")
	if opReturn == "" {
		opReturn = utils.SurveyInput("请输入 OP_RETURN 数据（可选）:")
	}

	tx, err := client.CreateTransaction(amountInSatoshiInt64, feePerByte, receivingAddress, opReturn)
	if err != nil {
		ulog.Errorf("Error creating transaction: %v", err)
		return
	}

	ulog.Infof("Raw Transaction: %s", tx)

	sendInEnv := os.Getenv("DEBUG_SEND")
	send := false
	if sendInEnv == "true" {
		send = true
	} else {
		send = utils.SurveyYes("是否发送交易？")
	}
	if send {
		txid, err := client.PushRawTransaction(tx)
		if err != nil {
			ulog.Errorf("Error sending transaction: %v", err)
			return
		}
		ulog.Infof("交易已发送，交易 ID: %s", txid)
	}
}

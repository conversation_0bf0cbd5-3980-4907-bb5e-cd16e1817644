package main

import (
	"chaintools/core"
	"chaintools/utils/ulog"
	"fmt"
	"strings"
	"time"

	"chaintools/core/frc20"
	"chaintools/utils"
)

func main() {
	var tick string
	// 每次mint的数量
	var amount int
	// mint 次数
	var times int
	var limitGasPriceGwei int64 // 单位 Gwei

	privateKey := utils.SurveyPassword("请输入从 MetaMask 导出的私钥:")
	if privateKey == "" || len(privateKey) < 10 {
		ulog.Errorf("private key is not set, abort")
		return
	}

	ulog.Infof("私钥: %s...%s (%d位)", privateKey[:4], privateKey[len(privateKey)-4:], len(privateKey))

	tick = utils.SurveyInput("请输入要 mint 币的代号:")
	tick = strings.ToLower(tick)
	if tick == "" {
		ulog.Errorf("tick is not set, abort")
		return
	}

	amount = utils.SurveyInt("请输入每次 mint 的数量（代币最小单位）:")
	if amount <= 0 {
		ulog.Errorf("amount is not set, abort")
		return
	}

	times = utils.SurveyInt("请输入 mint 的次数:")
	if times <= 0 {
		ulog.Errorf("times is not set, abort")
		return
	}

	client, err := core.NewEthClient("https://rpc.zkfair.io", privateKey)
	if err != nil {
		ulog.Errorf("Failed to create client: %v", err)
		return
	}

	currentGasPrice, err := client.GetSuggestedGasPrice()
	if err != nil {
		ulog.Errorf("Failed to suggest gas price: %v", err)
		return
	}
	currentGasPriceGwei := currentGasPrice.Int64() / 1e9

	mintFee := float64(currentGasPriceGwei*22216) / 1e9

	t := utils.NewTable()
	t.AddRow([]string{"Gas price", fmt.Sprintf("%d Gwei", currentGasPriceGwei)})
	t.AddRow([]string{"Fee", fmt.Sprintf("%.4f USDC", mintFee)})
	ulog.Infof(fmt.Sprintf("\n%s", t.Render()))

	limitFee := utils.SurveyFloat("输入最大手续费 (USDC)")
	if limitFee == 0.0 {
		limitFee = mintFee
	}
	limitGasPriceGwei = int64(limitFee * 1e9 / 22216)
	ulog.Debugf("currentGasPrice: %d Gwei", currentGasPriceGwei)
	ulog.Debugf("limitGasPrice: %d Gwei", limitGasPriceGwei)

	address := client.GetPublicAddress()
	tickHold, err := frc20.QueryHolding(address, tick)
	if err != nil {
		ulog.Errorf("Failed to query mint: %v", err)
	} else {
		ulog.Infof("当前已持有 %s: %d", tick, tickHold)
	}

	for i := 0; i < times; i++ {
		ulog.Infof("Minting %s, %d/%d", tick, i+1, times)

		// 检查 gas price
		for {
			currentGasPrice, err = client.GetSuggestedGasPrice()
			if err != nil {
				ulog.Errorf("Failed to suggest gas price: %v", err)
				time.Sleep(10 * time.Second)
				continue
			}
			currentGasPriceGwei := currentGasPrice.Int64() / 1e9
			if limitGasPriceGwei > 0 && currentGasPriceGwei > limitGasPriceGwei {
				ulog.Infof("当前 gas price %d Gwei高于限制的 %d, 等待...", currentGasPriceGwei, limitGasPriceGwei)
				time.Sleep(10 * time.Second)
				continue
			}
			break
		}

		data := fmt.Sprintf(`data:,{"p":"frc-20","op":"mint","tick":"%s","amt":"%d"}`, tick, amount)
		txHash, err := client.MintData(data)
		if err != nil {
			ulog.Errorf("Failed to mint: %v", err)
			return
		}
		ulog.Infof("Mint transaction: %s", txHash)

		time.Sleep(5 * time.Second)
		// TODO check if the transaction is successful
	}

	tickHold, err = frc20.QueryHolding(address, tick)
	if err != nil {
		ulog.Errorf("Failed to query mint: %v", err)
	} else {
		ulog.Infof("最新持有 %s: %d", tick, tickHold)
	}
}

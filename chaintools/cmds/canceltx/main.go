package main

import (
	"chaintools/core"
	"chaintools/utils"
	"chaintools/utils/ulog"
	"context"
	"os"
	"strconv"

	"github.com/ethereum/go-ethereum/common"
)

func main() {
	chain := os.Getenv("DEBUG_CHAIN")
	if chain == "" {
		_, chain = utils.SurveySelect("请选择链:", []string{"btc", "eth"})
	}
	if chain == "btc" {
		cancelBTCTx()
	}
	if chain == "eth" {
		cancelETHTx()
	}
}

func cancelBTCTx() {
	networkEnv := os.Getenv("DEBUG_NETWORK")
	network := core.TestNet
	if networkEnv == "" {
		idx, _ := utils.SurveySelect("请选择网络:", []string{"mainnet", "testnet"})
		if idx == 0 {
			network = core.MainNet
		}
	} else if networkEnv == "mainnet" {
		network = core.MainNet
	}

	privateKeyWIF := os.Getenv("DEBUG_PRIVATE_KEY_WIF")
	if privateKeyWIF == "" {
		privateKeyWIF = utils.SurveyPassword("请输入 WIF 私钥:")
		if privateKeyWIF == "" || len(privateKeyWIF) < 52 {
			ulog.Errorf("private key is not set, abort")
			return
		}
	}

	address := os.Getenv("DEBUG_ADDRESS")
	if address == "" {
		address = utils.SurveyInput("请输入地址:")
	}
	if address == "" {
		ulog.Errorf("address is not set, abort")
		return
	}

	var feePerByte int64
	feePerByteInEnv := os.Getenv("DEBUG_FEE_PER_BYTE")
	if feePerByteInEnv != "" {
		// convert string to int64
		feePerByte, _ = strconv.ParseInt(feePerByteInEnv, 10, 64)
	} else {
		feePerByte = int64(utils.SurveyInt("请输入交易费率（单位：sat/byte）:"))
		if feePerByte <= 0 {
			ulog.Errorf("feePerByte is not set, abort")
			return
		}
	}

	token := os.Getenv("DEBUG_TOKEN")
	if token == "" {
		token = utils.SurveyPassword("请输入 blockcypher API Token:")
	}

	client := core.NewBTCClient(
		privateKeyWIF,
		address,
		network,
		token,
	)

	txIDs, err := client.CancelAllUnconfirmedTransactions(address, privateKeyWIF, feePerByte)
	if err != nil {
		ulog.Errorf("Error canceling transactions: %v", err)
		return
	}
	ulog.Infof("Transactions canceled with txIDs: %v", txIDs)
}

func cancelETHTx() {
	privateKey := os.Getenv("DEBUG_PRIVATE_KEY")
	if privateKey == "" {
		privateKey = utils.SurveyPassword("请输入 ETH 的私钥:")
	}

	ulog.Infof("私钥: %s...%s (%d位)", privateKey[:4], privateKey[len(privateKey)-4:], len(privateKey))

	endpoint := os.Getenv("DEBUG_ENDPOINT")
	if endpoint == "" {
		endpoint = utils.SurveyInput("请输入以太坊节点地址:")
	}

	client, err := core.NewEthClient(endpoint, privateKey)
	if err != nil {
		ulog.Errorf("Failed to create client: %v", err)
		return
	}

	replacedTxHash := os.Getenv("DEBUG_REPLACED_TX_HASH")
	if replacedTxHash == "" {
		replacedTxHash = utils.SurveyInput("请输入要替换的交易的 hash:")
	}

	originalTx, isPending, err := client.TransactionByHash(context.Background(), common.HexToHash(replacedTxHash))
	if err != nil {
		ulog.Errorf("Failed to get transaction: %v", err)
		return
	}
	if !isPending {
		ulog.Errorf("Transaction is not pending, abort")
		return
	}

	suggestedGasPrice, err := client.GetSuggestedGasPrice()
	if err != nil {
		ulog.Errorf("Failed to suggest gas price: %v", err)
		return
	}
	currentGasPriceGwei := suggestedGasPrice.Int64() / 1e9
	ulog.Infof("当前网络推荐 gas price: %d Gwei", currentGasPriceGwei)

	originalTxGasPriceGwei := originalTx.GasPrice().Int64() / 1e9
	ulog.Infof("原交易 gas price: %d Gwei", originalTxGasPriceGwei)

	newGasPriceGweiInEnv := os.Getenv("DEBUG_NEW_GAS_PRICE_GWEI")
	var newGasPriceGwei int
	if newGasPriceGweiInEnv != "" {
		newGasPriceGwei, _ = strconv.Atoi(newGasPriceGweiInEnv)
	} else {
		newGasPriceGwei = utils.SurveyInt("请输入替换交易的 gas price（单位：Gwei）:")
	}

	if int64(newGasPriceGwei) <= originalTxGasPriceGwei {
		ulog.Errorf("newGasPriceGwei must be greater than originalTxGasPriceGwei, abort")
		return
	}

	newTxHash, err := client.CancelTransaction(replacedTxHash, float64(newGasPriceGwei))
	if err != nil {
		ulog.Errorf("Failed to cancel transaction: %v", err)
		return
	}
	ulog.Infof("New transaction hash: %s", newTxHash)
}

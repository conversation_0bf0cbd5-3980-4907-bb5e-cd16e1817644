package main

import (
	"chaintools/core/btckeygen"
	"flag"
	"fmt"
	"log"
	"strings"
)

func main() {
	compress := true // generate a compressed public key
	bip39 := flag.Bool("bip39", false, "mnemonic code for generating deterministic keys")
	pass := flag.String("pass", "", "protect bip39 mnemonic with a passphrase")
	number := flag.Int("n", 10, "set number of keys to generate")
	mnemonic := flag.String("mnemonic", "", "optional list of words to re-generate a root key")

	flag.Parse()

	if !*bip39 {
		fmt.Printf("\n%-34s %-52s %-42s %s\n", "Bitcoin Address", "WIF(Wallet Import Format)", "SegWit(bech32)", "SegWit(nested)")
		fmt.Println(strings.Repeat("-", 165))

		for i := 0; i < *number; i++ {
			wif, address, segwitBech32, segwitNested, err := btckeygen.Generate(compress)
			if err != nil {
				log.Fatal(err)
			}
			fmt.Printf("%-34s %s %s %s\n", address, wif, segwitBech32, segwitNested)
		}
		fmt.Println()
		return
	}

	km, err := btckeygen.NewKeyManager(128, *pass, *mnemonic)
	if err != nil {
		log.Fatal(err)
	}
	masterKey, err := km.GetMasterKey()
	if err != nil {
		log.Fatal(err)
	}
	passphrase := km.GetPassphrase()
	if passphrase == "" {
		passphrase = "<none>"
	}
	fmt.Printf("\n%-18s %s\n", "BIP39 Mnemonic:", km.GetMnemonic())
	fmt.Printf("%-18s %s\n", "BIP39 Passphrase:", passphrase)
	fmt.Printf("%-18s %x\n", "BIP39 Seed:", km.GetSeed())
	fmt.Printf("%-18s %s\n", "BIP32 Root Key:", masterKey.B58Serialize())

	fmt.Printf("\n%-18s %-42s %-52s\n", "Path(BIP44)", "ETH Address", "Private Key")
	fmt.Println(strings.Repeat("-", 106))
	for i := 0; i < *number; i++ {
		key, err := km.GetKey(btckeygen.PurposeBIP44, btckeygen.CoinTypeETH, 0, 0, uint32(i))
		if err != nil {
			log.Fatal(err)
		}
		privateKey, address, err := key.EncodeForETH(compress)
		if err != nil {
			log.Fatal(err)
		}

		fmt.Printf("%-18s %-34s %s\n", key.GetPath(), address, privateKey)
	}

	fmt.Printf("\n%-18s %-34s %-52s\n", "Path(BIP44)", "Bitcoin Address", "WIF(Wallet Import Format)")
	fmt.Println(strings.Repeat("-", 106))
	for i := 0; i < *number; i++ {
		key, err := km.GetKey(btckeygen.PurposeBIP44, btckeygen.CoinTypeBTC, 0, 0, uint32(i))
		if err != nil {
			log.Fatal(err)
		}
		wif, address, _, _, err := key.Encode(compress)
		if err != nil {
			log.Fatal(err)
		}

		fmt.Printf("%-18s %-34s %s\n", key.GetPath(), address, wif)
	}

	fmt.Printf("\n%-18s %-42s %s\n", "Path(BIP84)", "SegWit(bech32)", "WIF(Wallet Import Format)")
	fmt.Println(strings.Repeat("-", 114))
	for i := 0; i < *number; i++ {
		key, err := km.GetKey(btckeygen.PurposeBIP84, btckeygen.CoinTypeBTC, 0, 0, uint32(i))
		if err != nil {
			log.Fatal(err)
		}
		wif, _, segwitBech32, _, err := key.Encode(compress)
		if err != nil {
			log.Fatal(err)
		}

		fmt.Printf("%-18s %s %s\n", key.GetPath(), segwitBech32, wif)
	}
	fmt.Println()

	fmt.Printf("\n%-18s %-62s %s\n", "Path(BIP86)", "Taproot(bech32)", "WIF(Wallet Import Format)")
	fmt.Println(strings.Repeat("-", 114))
	for i := 0; i < *number; i++ {
		key, err := km.GetKey(btckeygen.PurposeBIP86, btckeygen.CoinTypeBTC, 0, 0, uint32(i))
		if err != nil {
			log.Fatal(err)
		}
		wif, taprootAddress, err := key.EncodeForTaproot(compress)
		if err != nil {
			log.Fatal(err)
		}

		fmt.Printf("%-18s %s %s\n", key.GetPath(), taprootAddress, wif)
	}
	fmt.Println()
}

package main

import (
	"chaintools/hopper"
	"chaintools/utils"
	"chaintools/utils/ulog"
	"flag"
	"fmt"
	"os"
	"path"
	"slices"
	"strings"

	"github.com/mitchellh/go-homedir"
	"github.com/spf13/cast"
)

func main() {
	ulog.DEBUG = os.Getenv("DEBUG") == "true"

	if os.Getenv("OKX_API_TEST") == "true" {
		okxTest()
		return
	}

	var sendCSV string
	var sendTX string
	var chain string
	var isForce bool
	flag.StringVar(&sendCSV, "sendcsv", "", "send tx in csv file")
	flag.StringVar(&sendTX, "sendtx", "", "send single tx")
	flag.StringVar(&chain, "chain", "", "arbitrum/ethereum/solana/dydx, input \"NA\" if specified in the tx")
	flag.BoolVar(&isForce, "force", false, "force send tx")
	flag.Parse()

	if sendTX != "" {
		sendTx(chain, sendTX, isForce)
		return
	} else if sendCSV != "" {
		promptSendTxFromFile(chain, sendCSV, isForce)
		return
	}
	manager()
}

func okxTest() {
	isMainAPI := os.Getenv("OKX_API_IS_MAIN") == "true"
	exchange, err := hopper.NewExchangeAPI("", hopper.OKX, os.Getenv("OKX_API_KEY"), os.Getenv("OKX_API_SECRET"), !isMainAPI)
	if err != nil {
		ulog.Errorf("failed to create exchange: %v", err)
		return
	}
	if ok, err := exchange.IsAvailable(); !ok {
		ulog.Errorf("API 不可用: %v", err)
		return
	}
	if total, available, err := exchange.GetBalance("USDC"); err != nil {
		ulog.Errorf("获取余额失败: %v", err)
	} else {
		ulog.Infof("OKX 余额: %f, 可用: %f", total, available)
	}
	if addres, err := exchange.GetDepositAddress("USDC", "arbitrum"); err != nil {
		ulog.Errorf("获取充值地址失败: %v", err)
	} else {
		ulog.Infof("OKX 充值地址: %s", addres)
	}
	okx, _ := exchange.Exchange.(*hopper.OKx)
	subAccount := os.Getenv("OKX_SUB_ACCOUNT")
	transferType := hopper.OKxTransferTypeSubToMainSub
	if isMainAPI && subAccount != "" {
		transferType = hopper.OKxTransferTypeSubToMain

		if total, available, err := okx.GetSubaccountBalance(subAccount, "USDC"); err != nil {
			ulog.Errorf("获取余额失败: %v", err)
		} else {
			ulog.Infof("OKX 子账号余额: %f, 可用: %f", total, available)
		}
	}
	if txId, err := okx.AssetTransfer("USDC", hopper.OKxTransferAccountFund, hopper.OKxTransferAccountFund, 1, transferType, subAccount); err != nil {
		ulog.Errorf("转账失败: %v", err)
	} else {
		ulog.Infof("转账成功: %s", txId)
	}
}

func sendTx(chainName string, sendTx string, isForce bool) {
	chain := hopper.NewChain(chainName)
	if chain == nil {
		ulog.Errorf("Chain %s 不存在", chainName)
		return
	}

	txs := hopper.NewTxsFromCSV(sendTx)
	fmt.Println(txs.FormatSendingPreview())

	if !isForce {
		input := utils.SurveyInput("请输入 SEND 开始发送交易：")
		if input != "SEND" {
			ulog.Debugf("输入错误，取消发送")
			return
		}
	}

	txs.Send(chain, nil)
}

func promptSendTxFromFile(chainName, dataPath string, isForce bool) {
	var chain *hopper.Chain
	if chainName == "" && dataPath == "" {
		chainNames := []string{}
		chains := hopper.GetChains()
		for _, chain := range chains {
			chainNames = append(chainNames, chain.Name)
		}
		_, chainName = utils.SurveySelect("选择链", chainNames)
		confirmStr := fmt.Sprintf("send txs on %s", chainName)
		confirm := utils.SurveyInput(fmt.Sprintf("输入 \"%s\" 确认：", confirmStr))
		if !strings.EqualFold(confirm, confirmStr) {
			return
		}
		chain = hopper.NewChain(chainName)
		if chain == nil {
			ulog.Errorf("Chain %s 不存在", chainName)
			return
		}
		dataPath = utils.SurveyInput("输入 csv 文件路径：")
	} else {
		if chainName != "NA" {
			chain = hopper.NewChain(chainName)
			if chain == nil {
				ulog.Errorf("Chain %s 不存在", chainName)
				return
			}
		}
	}

	if chain != nil && utils.SurveyYes("是否使用固定 Gas Price?") {
		fixedGasPrice := utils.SurveyFloat("输入固定 Gas Price (gwei)：")
		chain.FixedGasPrice = fixedGasPrice
	}

	sendTxFromCSV(chain, dataPath, isForce)
}

func sendTxFromCSV(chain *hopper.Chain, dataPath string, force bool) {
	if !strings.HasPrefix(dataPath, "/") && !strings.HasPrefix(dataPath, "~") {
		dir, _ := homedir.Expand("~/hopper")
		dataPath = path.Join(dir, dataPath)
	} else if strings.HasPrefix(dataPath, "~") {
		dataPath, _ = homedir.Expand(dataPath)
	}

	if !strings.HasSuffix(dataPath, ".csv") {
		ulog.Errorf("文件类型不支持")
		return
	}
	// read file content from csv
	data, err := os.ReadFile(dataPath)
	if err != nil {
		ulog.Errorf("Failed to read file: %v", err)
		return
	}
	txs := hopper.NewTxsFromCSV(string(data))
	fmt.Println(txs.FormatSendingPreview())

	if !force {
		input := utils.SurveyInput("请输入 SEND 开始发送交易：")
		if input != "SEND" {
			ulog.Debugf("输入错误，取消发送")
			return
		}
	}

	txs.Send(chain, nil)

	// save txs to file
	updatedCSV := txs.String()
	err = os.WriteFile(dataPath, []byte(updatedCSV), 0644)
	if err != nil {
		ulog.Errorf("Failed to write file: %v", err)
	}
}

func manager() {
	for {
		choices := []string{"1. 查看所有 hopper", "2. 创建新的 Hopper", "3. 检查 Hopper", "4. 发送 hopper 交易", "5. 修改 hopper", "6. 删除 hopper", "7. 复制 hopper", "8. 打印 hopper", "q: 退出"}
		opt := indexedSelect("选择操作", choices)
		switch opt {
		case "1":
			listHopper()
		case "2":
			createHopper()
		case "3":
			reviewHopper()
		case "4":
			sendHopperTx()
		case "5":
			modifyHopper()
		case "6":
			removeHopper()
		case "7":
			cloneHopper()
		case "8":
			printHopper()
		case "q":
			return
		}
	}
}

func indexedSelect(msg string, options []string) string {
	_, opt := utils.SurveySelect(msg, options)
	opt = strings.Split(opt, ":")[0]
	opt = strings.Split(opt, ".")[0]
	return opt
}

func printHopper() {
	options := []string{"1. 打印余额", "2. 打印 tx csv", "3. 打印 okx 提币地址", "q: 返回"}
	opt := indexedSelect("选择操作", options)
	switch opt {
	case "1":
		printBalance()
	case "2":
		printTxsCSV()
	case "3":
		printOKXWithdrawAddress()
	case "q":
		return
	}
}

func printOKXWithdrawAddress() {
	fmt.Println(hopper.GreenTextf("打印 OKX 提币地址，用于导入 OKX 免认证地址列表"))
	hopperName := utils.SurveyInput("输入 Hopper 名称：")
	h := hopper.GetHopperByName(hopperName)
	if h == nil {
		ulog.Debugf("Hopper %s 不存在", hopperName)
		return
	}

	prefix := utils.SurveyInput("输入地址前缀(自动加上W{id}后缀)：")

	t := utils.NewTable()
	t.SetHeader([]string{"Address", "AddressName(optional)"})
	for _, addr := range h.TargetWallet.Addresses {
		t.AddRow([]string{addr.Address(), fmt.Sprintf("%sW%02d", prefix, addr.Index())})
	}

	fmt.Println(t.Render())

	csvContent := ""
	for _, row := range t.Rows {
		csvContent += strings.Join(row, ",") + "\n"
	}

	saveToFile := utils.SurveyYes("是否保存到文件？")
	if saveToFile {
		defaultPath, _ := homedir.Expand(fmt.Sprintf("~/hopper/%s_okx_address.csv", prefix))
		filePath := utils.SurveyInput(fmt.Sprintf("输入文件路径：(%s)", defaultPath))
		if filePath == "" {
			filePath = defaultPath
		}
		err := os.WriteFile(filePath, []byte(csvContent), 0644)
		if err != nil {
			ulog.Errorf("Failed to write file: %v", err)
		}
		ulog.Infof("已保存到文件：%s", filePath)
	}
}

func listHopper() {
	hoppers := hopper.GetHoppers()
	fmt.Println("Hopper 列表：")
	t := utils.NewTable()
	t.SetHeader([]string{"Name", "Type", "Chain", "Amount", "Count", "Wait"})
	for _, hopper := range hoppers {
		t.AddRow([]string{hopper.Name, string(hopper.Type), hopper.Chain.String(), hopper.TokenAmount.String(), fmt.Sprintf("%d", hopper.Count), fmt.Sprintf("%d", hopper.Wait)})
	}
	fmt.Println(t.Render())
}

func createHopper() {
	h := newOrCloneHopper(nil)
	if h == nil {
		return
	}
	h.EstimateGasFee()
	h.GenerateTxs(true)
	h.Save()
}

func cloneHopper() {
	name := utils.SurveyInput("输入要复制的 Hopper 名称：")
	h := hopper.GetHopperByName(name)
	if h == nil {
		ulog.Debugf("Hopper %s 不存在", name)
		return
	}
	confirm := utils.SurveyYes("复制 hopper 用于复制原 hopper 的钱包地址，但需要重新设置其他参数，是否继续？")
	if confirm {
		hopper := newOrCloneHopper(h)
		hopper.EstimateGasFee()
		hopper.GenerateTxs(true)
		if hopper != nil {
			ulog.Infof("Hopper 复制成功，%s -> %s", h.Name, hopper.Name)
		} else {
			ulog.Infof("Hopper 复制失败，%s -> ", h.Name)
		}
	} else {
		ulog.Debugf("取消复制 Hopper")
	}
}

func newOrCloneHopper(oldHopper *hopper.Hopper) (h *hopper.Hopper) {
	name := utils.SurveyInput("输入 Hopper 名称：")
	if name == "" {
		ulog.Errorf("Hopper 名称不能为空")
		return nil
	}
	existingHopper := hopper.GetHopperByName(name)
	if existingHopper != nil {
		ulog.Debugf("Hopper %s 已存在", name)
		return nil
	}
	chainNames := []string{}
	chains := hopper.GetChains()
	for _, chain := range chains {
		chainNames = append(chainNames, chain.Name)
	}
	_, chainName := utils.SurveySelect("选择链", chainNames)
	_, hopperType := utils.SurveySelect("选择类型", []string{"deposit", "withdraw"})
	count := utils.SurveyInt("输入地址数量：")
	if count == 0 {
		ulog.Errorf("地址数量不能为 0")
		return nil
	}

	hops := utils.SurveyInt("输入跳数：")

	chain := hopper.NewChain(chainName)
	if chain == nil {
		ulog.Errorf("Chain %s 不存在", chainName)
		return nil
	}
	tokens := chain.GetTokens()
	tokens = append(tokens, "无")
	_, tokenCoin := utils.SurveySelect("选择币种", tokens)
	var amt *hopper.Amount
	if tokenCoin == "无" {
		tokenCoin = ""
	} else {
		amount := utils.SurveyFloat("输入金额：")
		randomnize := false
		rounding := -4
		if hopperType == "deposit" {
			randomnize = utils.SurveyYes("金额随机化：")
			if randomnize {
				rounding = utils.SurveyInt("输入精度（2表示精确到百位，-2表示精确到分位）：")
			}
		}
		amt = &hopper.Amount{
			Coin:       tokenCoin,
			Amount:     amount,
			Randomnize: randomnize,
			Rounding:   rounding,
		}
	}

	waitStr := "每笔存入交易"
	if hopperType == "withdraw" {
		waitStr = "每笔取款交易"
	}
	wait := utils.SurveyInt(fmt.Sprintf("%s等待时间：", waitStr))

	if oldHopper == nil {
		h = hopper.NewHopper(name, chainName, hopper.HopperType(hopperType), hops, cast.ToInt(count), wait, amt)
	} else {
		h = oldHopper.Clone(name, chainName, hopper.HopperType(hopperType), hops, cast.ToInt(count), wait, amt)
	}

	if h.Type == hopper.WithdrawHopper && tokenCoin != "" {
		gasOnly := !utils.SurveyYes(fmt.Sprintf("是否清空 %s 余额？", chain.Coin))
		if gasOnly {
			h.TransferGasOnly = true
			h.GenerateTxs(true) // save hopper automatically
		}
	}

	if h.Type == hopper.DepositHopper {
		totalAmount := utils.SurveyFloat(fmt.Sprintf("指定 %s 总额：", chain.Coin))
		h.CoinAmount = totalAmount
	}

	if h.Type == hopper.DepositHopper && utils.SurveyYes("是否使用交易所 API 充值？") {
		useExchangeAPI(h)
	}

	return h
}

func reviewHopper() {
	name := utils.SurveyInput("输入 Hopper 名称：")
	h := hopper.GetHopperByName(name)
	if h == nil {
		ulog.Errorf("Hopper %s 不存在", name)
		return
	}
	h.EstimateGasFee()
	fmt.Println(h.Format())
}

func sendHopperTx() {
	name := utils.SurveyInput("输入 Hopper 名称：")
	h := hopper.GetHopperByName(name)
	if h == nil {
		ulog.Debugf("Hopper %s 不存在", name)
		return
	}

	if utils.SurveyYes("是否使用固定 Gas Price?") {
		fixedGasPrice := utils.SurveyFloat("输入固定 Gas Price (gwei)：")
		h.Chain.FixedGasPrice = fixedGasPrice
	}

	h.EstimateGasFee()

	h.GenerateTxs(true)
	fmt.Println("交易预览：")
	fmt.Println(h.Txs.FormatSendingPreview())

	if h.Type == hopper.DepositHopper {
		if h.DepositExchange == nil {
			srcAddr := h.SourceWallet.Addresses[0].Address()
			srcKey := h.SourceWallet.PrivateKeys[0].PrivateKey()
			balance, err := h.Chain.GetBalance(srcKey, srcAddr, h.Chain.Coin)
			if err != nil {
				ulog.Errorf("Failed to get balance: %v", err)
				return
			}

			tokenCoin := h.GetTokenCoin()
			if tokenCoin != "" {
				tokenBalance, err := h.Chain.GetBalance(srcKey, srcAddr, tokenCoin)
				if err != nil {
					ulog.Errorf("Failed to get token balance with decimals: %v", err)
					return
				}

				fmt.Printf("\nSourceWallet 余额: %.8f %s, %.2f %s\n\n", balance, h.Chain.Coin, tokenBalance, tokenCoin)
			} else {
				fmt.Printf("\nSourceWallet 余额: %.8f %s\n\n", balance, h.Chain.Coin)
			}
		}

		input := utils.SurveyInput("请输入 SEND 开始发送交易：")
		if input != "SEND" {
			ulog.Debugf("输入错误，取消发送")
			return
		}
	} else {
		input := utils.SurveyInput("请输入 SEND 开始发送交易：")
		if input != "SEND" {
			ulog.Debugf("输入错误，取消发送")
			return
		}
	}

	h.SendTxs()
}

func modifyHopper() {
	name := utils.SurveyInput("输入 Hopper 名称：")
	h := hopper.GetHopperByName(name)
	if h == nil {
		ulog.Debugf("Hopper %s 不存在", name)
		return
	}

	walletType := "目标"
	extraChoice := "使用交易所 API 充值"
	if h.Type == hopper.WithdrawHopper {
		walletType = "来源"
		extraChoice = "替换提币目标地址"
	}
	choices := []string{fmt.Sprintf("1. 修改%s钱包", walletType), "2. 排除特定地址", "3. 恢复特定地址", fmt.Sprintf("4. %s", extraChoice), "q: 返回"}
	opt := indexedSelect("选择操作", choices)
	switch opt {
	case "1":
		changeWallet(h)
	case "2":
		excludeAddress(h, false)
	case "3":
		excludeAddress(h, true)
	case "4":
		if h.Type == hopper.WithdrawHopper {
			replaceWithdrawAddress(h)
		} else {
			useExchangeAPI(h)
		}
	case "q":
		return
	}
}

func changeWallet(h *hopper.Hopper) {
	walletType := "目标"
	wallet := h.TargetWallet
	if h.Type == hopper.WithdrawHopper {
		walletType = "来源"
		wallet = h.SourceWallet
	}
	mnemonic := utils.SurveyInput(fmt.Sprintf("输入新的%s钱包助记词：", walletType))
	newWallet, err := hopper.NewWalletFromMnemonic(mnemonic, h.Count)
	if err != nil {
		ulog.Debugf("创建%s钱包失败，请检查输入的助记词", walletType)
	}

	t := utils.NewTable()
	t.SetHeader([]string{"From", "To"})
	t.AddRow([]string{wallet.RedactedMnemonic(), newWallet.RedactedMnemonic()})
	for i := 0; i < h.Count; i++ {
		t.AddRow([]string{wallet.Addresses[i].String(), newWallet.Addresses[i].String()})
	}
	fmt.Println(t.Render())
	confirm := utils.SurveyYes("确认修改钱包？")
	if confirm {
		h.ChangeWallet(newWallet)
	}
}

func removeHopper() {
	name := utils.SurveyInput("输入 Hopper 名称：")
	h := hopper.GetHopperByName(name)
	if h == nil {
		ulog.Debugf("Hopper %s 不存在", name)
		return
	}
	confirm := utils.SurveyYes("确认删除 Hopper？")
	if confirm {
		hopper.RemoveHopperByName(name)
		ulog.Debugf("Hopper %s 已删除", name)
	}
}

// 排除或恢复特定地址
func excludeAddress(h *hopper.Hopper, revert bool) {
	walletType := "目标"
	if h.Type == hopper.WithdrawHopper {
		walletType = "来源"
	}
	actionFunc := h.ExcludeAddress
	action := "排除"
	if revert {
		actionFunc = h.RevertExcludeAddress
		action = "恢复"
	}
	importantAddresses := h.GetMainWallet().Addresses.Addresses()
	address := utils.SurveyInput(fmt.Sprintf("输入要%s的%s地址（可以输入, 分隔）：", action, walletType))
	if strings.Contains(address, ",") {
		addresses := utils.Split(address, ",")
		// 检查输入的地址是否在重要地址列表中
		for _, addr := range addresses {
			if !slices.Contains(importantAddresses, addr) {
				ulog.Errorf("%s地址 %s 失败，请检查输入的地址", action, address)
				return
			}
		}

		for _, addr := range addresses {
			success := actionFunc(strings.TrimSpace(addr))
			if !success {
				ulog.Errorf("%s地址 %s 失败，请检查输入的地址", action, addr)
				return
			}
			ulog.Infof("已%s: %v", action, addr)
		}
		return
	} else {
		if !slices.Contains(importantAddresses, address) {
			ulog.Errorf("%s地址 %s 失败，请检查输入的地址", action, address)
			return
		}
		success := actionFunc(address)
		if !success {
			ulog.Debugf("%s地址 %s 失败，请检查输入的地址", action, address)
			return
		}
		ulog.Infof("已%s: %v", action, address)
	}
}

func replaceWithdrawAddress(h *hopper.Hopper) {
	msg := fmt.Sprintf("输入新的目标地址的文件路径： (~/hopper/%s.address.csv)", h.Name)
	addressPath := utils.SurveyInput(msg)
	var addresses []string

	// 如果地址以 # 开头，则直接使用该地址
	if strings.HasPrefix(addressPath, "#") {
		addressStr := strings.TrimPrefix(addressPath, "#")
		addresses = strings.Split(addressStr, ",")
	} else {
		addressPath, _ = homedir.Expand(addressPath)
		if !strings.HasSuffix(addressPath, ".csv") {
			ulog.Errorf("文件类型不支持")
			return
		}
		data, err := os.ReadFile(addressPath)
		if err != nil {
			ulog.Errorf("Failed to read file: %v", err)
			return
		}
		addressesStr := string(data)
		addresses = strings.Split(addressesStr, "\n")
	}

	if len(addresses) == 0 || (len(addresses) > 1 && len(addresses) < h.Count) {
		ulog.Errorf("提币目标地址数量不匹配：要么只有一个地址，要么多于钱包地址数量")
		return
	}
	for i, addr := range addresses {
		addresses[i] = strings.TrimSpace(addr)
	}

	t := utils.NewTable()
	t.SetHeader([]string{"Index", "Address"})
	indexedAddresses := hopper.Addresses{}
	for i, addr := range addresses {
		idx := i + 1
		addr = strings.TrimSpace(addr)
		t.AddRow([]string{fmt.Sprintf("%d", idx), addr})
		indexedAddresses = append(indexedAddresses, hopper.NewIndexedAddress(idx, addr))
	}
	fmt.Println(t.Render())
	confirm := utils.SurveyYes("确认修改提币目标地址？")
	if confirm {
		h.ChangeWithdrawTargetAddresses(indexedAddresses)
		ulog.Infof("提币目标地址已更新")
	}
}

func useExchangeAPI(h *hopper.Hopper) {
	_, exchangeName := utils.SurveySelect("选择交易所", []string{"okx", "bitget"})
	apiKey := utils.SurveyInput("输入 API Key：")
	apiSecret := utils.SurveyPassword("输入 API Secret(格式为 Secret|||Password)：")

	if strings.Contains(apiKey, "/") || strings.Contains(apiKey, ",") || strings.Contains(apiSecret, "/") || strings.Contains(apiSecret, ",") {
		ulog.Errorf("API Key 和 API Secret 不能包含 / 和 ,")
		return
	}

	var exchange *hopper.ExchangeAPI
	var err error
	if exchangeName == "okx" {
		exchange, err = hopper.NewExchangeAPI("", hopper.OKX, apiKey, apiSecret, false)
	} else if exchangeName == "bitget" {
		exchange, err = hopper.NewExchangeAPI("", hopper.BITGET, apiKey, apiSecret, false)
	}
	if err != nil {
		ulog.Errorf("failed to create exchange: %v", err)
		return
	}
	if ok, err := exchange.IsAvailable(); !ok {
		ulog.Errorf("API 不可用: %v", err)
		return
	}
	fmt.Println("API 校验通过")

	confirm := utils.SurveyYes(fmt.Sprintf("确认使用 %s API 提币？请确认目标地址已经添加到免认证地址列表", exchangeName))
	if confirm {
		h.UseExchangeAPI(exchange)
		ulog.Infof("已设置使用 %s API 提币", exchangeName)
	}
}

func plainTest() {
	hopper1 := hopper.NewHopper("hopper_deposit", hopper.Arbitrum.String(), hopper.DepositHopper, 2, 20, 3*60, hopper.NewAmount("USDC", 20000, true, 2))
	fmt.Println(hopper1.Format())

	fmt.Println(strings.Join(hopper1.GenerateTxs(false), "\n"))
	hopper1.Save()

	hopper2 := hopper.NewHopper("hopper_withdraw", hopper.Arbitrum.String(), hopper.WithdrawHopper, 2, 20, 3*60, hopper.NewAmount("USDC", 200, true, 2))
	fmt.Println(hopper2.Format())
	fmt.Println(strings.Join(hopper2.GenerateTxs(false), "\n"))
	hopper2.Save()

	dataPath := utils.SurveyInput("输入 hopper 文件路径：")
	if !strings.HasPrefix(dataPath, "/") && !strings.HasPrefix(dataPath, "~") {
		dir, _ := homedir.Expand("~/hopper")
		dataPath = path.Join(dir, dataPath)
	} else if strings.HasPrefix(dataPath, "~") {
		dataPath, _ = homedir.Expand(dataPath)
	}

	hopper3 := hopper.NewHopperFromFile(dataPath)
	fmt.Println(hopper3.Format())
	fmt.Println(strings.Join(hopper3.GenerateTxs(false), "\n"))
}

func printTxsCSV() {
	hopperName := utils.SurveyInput("输入 Hopper 名称：")
	h := hopper.GetHopperByName(hopperName)
	if h == nil {
		ulog.Debugf("Hopper %s 不存在", hopperName)
		return
	}
	fmt.Println(h.Txs.String())
	saveToFile := utils.SurveyYes("是否保存到文件？")
	if saveToFile {
		defaultPath, _ := homedir.Expand(fmt.Sprintf("~/hopper/%s_txs.csv", hopperName))
		filePath := utils.SurveyInput(fmt.Sprintf("输入文件路径：(%s)", defaultPath))
		if filePath == "" {
			filePath = defaultPath
		}
		err := os.WriteFile(filePath, []byte(h.Txs.String()), 0644)
		if err != nil {
			ulog.Errorf("Failed to write file: %v", err)
		}
		ulog.Infof("已保存到文件：%s", filePath)
	}
}

func printBalance() {
	hopperName := utils.SurveyInput("输入 Hopper 名称：")
	h := hopper.GetHopperByName(hopperName)
	if h == nil {
		ulog.Debugf("Hopper %s 不存在", hopperName)
		return
	}

	coinSelect := []string{h.Chain.Coin}
	if h.GetTokenCoin() != "" {
		coinSelect = append(coinSelect, h.GetTokenCoin())
	}
	if len(coinSelect) > 1 {
		coinSelect = append(coinSelect, "ALL")
	}
	_, coin := utils.SurveySelect("选择币种", coinSelect)
	coins := []string{}
	if coin == "ALL" {
		for _, c := range coinSelect {
			if c != "ALL" {
				coins = append(coins, c)
			}
		}
	} else {
		coins = append(coins, coin)
	}

	_, wallet := utils.SurveySelect("选择钱包", []string{"main", "source", "target", "hops", "source & target", "all"})
	if wallet == "all" {
		wallet = "source,target,hops"
	}

	wallets := []*hopper.MnemonicWallet{}
	if strings.Contains(wallet, "main") {
		wallets = append(wallets, h.GetMainWallet())
	}
	if strings.Contains(wallet, "source") {
		wallets = append(wallets, h.SourceWallet)
	}
	if strings.Contains(wallet, "target") {
		wallets = append(wallets, h.TargetWallet)
	}
	if strings.Contains(wallet, "hops") {
		wallets = append(wallets, h.HopWallets...)
	}

	t := utils.NewTable()
	t.SetHeader([]string{"Hopper", "Wallet", "Address", "Balance", "Coin"})

	for i, coin := range coins {
		if i > 0 {
			t.AddRow([]string{"--------", "--------", "---------------------------------------------", "-----------", "------"})
		}
		for _, wallet := range wallets {
			for i, addr := range wallet.Addresses {
				if h.GetMainWallet().IsIndexExcluded(i) {
					continue
				}
				balanceStr := ""
				balance, err := h.Chain.GetBalance(wallet.PrivateKeys[i].PrivateKey(), addr.Address(), coin)
				if err != nil {
					ulog.Errorf("Failed to get balance: %v", err)
					balanceStr = "?"
					return
				}

				balanceStr = fmt.Sprintf("%.8f", balance)
				t.AddRow([]string{h.Name, wallet.Name, addr.String(), balanceStr, coin})
			}
		}
	}

	fmt.Println(t.Render())
}

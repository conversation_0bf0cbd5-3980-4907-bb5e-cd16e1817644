package ulog

import (
	"log"
	"os"
)

var DEBUG = true
var logger = log.New(os.Stdout, "", log.LstdFlags)

func Debugf(format string, args ...interface{}) {
	if DEBUG {
		logger.Printf(format, args...)
	}
}

func Infof(format string, args ...interface{}) {
	// print in green color
	logger.Printf("\033[32m"+format+"\033[0m", args...)
}

func Warnf(format string, args ...interface{}) {
	// print in yellow color
	logger.Printf("\033[33m"+format+"\033[0m", args...)
}

func Errorf(format string, args ...interface{}) {
	// print in red color
	logger.Printf("\033[31m"+format+"\033[0m", args...)
}

package hopper

import (
	"chaintools/core"
	"chaintools/utils/ulog"
	_ "embed"
	"errors"
	"fmt"
	"math/big"
	"os"

	"github.com/mitchellh/go-homedir"
	"gopkg.in/yaml.v2"
)

var ETH = "ETH"
var USDC = "USDC"
var USDT = "USDT"

var EthereumName = "ethereum"
var ArbitrumName = "arbitrum"
var SolanaName = "solana"
var DYDXName = "dydx"

//go:embed chains.yml
var chainConfigStr string
var chainConfig = &Chains{}

func init() {
	chainConfigPath := ""
	if os.Getenv("HOPPER_CHAINS_CONFIG") != "" {
		chainConfigPath = os.Getenv("HOPPER_CHAINS_CONFIG")
		ulog.Infof("loading chain config from $HOPPER_CHAINS_CONFIG=%s", chainConfigPath)
	}
	if chainConfigPath == "" {
		chainConfigPath = "~/hopper/chains.yaml"
		ulog.Infof("loading chain config from %s", chainConfigPath)
	}
	// try to read chain config from file
	chainConfigPath, _ = homedir.Expand(chainConfigPath)
	chainConfigFile, err := os.ReadFile(chainConfigPath)
	if err != nil {
		ulog.Errorf("read chain config error: %v, path: %s", err, chainConfigPath)
		chainConfigPath = ""
	} else {
		chainConfigStr = string(chainConfigFile)
	}

	// if load from file failed, use default chainConfigStr from embedded file
	err2 := yaml.Unmarshal([]byte(chainConfigStr), chainConfig)
	if err2 != nil {
		ulog.Errorf("unmarshal chain config error: %v", err2)
		panic("invalid chain config")
	} else {
		if chainConfigPath != "" {
			ulog.Infof("loaded chain config from: %s", chainConfigPath)
		} else {
			ulog.Infof("loaded chain config from hopper binary, set $HOPPER_CHAINS_CONFIG to load from file")
		}
	}
}

func SetChainConfig(chainConfigPath string) error {
	chainConfigPath, _ = homedir.Expand(chainConfigPath)
	chainConfigFile, err := os.ReadFile(chainConfigPath)
	if err != nil {
		ulog.Errorf("read chain config error: %v, path: %s", err, chainConfigPath)
		return err
	}
	chainConfigStr = string(chainConfigFile)

	err2 := yaml.Unmarshal([]byte(chainConfigStr), chainConfig)
	if err2 != nil {
		ulog.Errorf("unmarshal chain config error: %v", err2)
		return fmt.Errorf("unmarshal chain config error: %v", err2)
	}
	ulog.Infof("loaded chain config from: %s", chainConfigPath)
	return nil
}

type Chain struct {
	Name          string            `yaml:"name"`
	Endpoint      string            `yaml:"endpoint"`
	Coin          string            `yaml:"coin"`
	Tokens        map[string]string `yaml:"tokens"`
	FixedGasPrice float64           `yaml:"-"` // fixed gas price in gwei
}

func NewChain(name string) *Chain {
	return chainConfig.GetChain(name)
}

func (this *Chain) String() string {
	return this.Name
}

func (this *Chain) GetTokens() []string {
	tokens := []string{}
	for token := range this.Tokens {
		tokens = append(tokens, token)
	}
	return tokens
}

var Arbitrum = NewChain("arbitrum")
var Ethereum = NewChain("ethereum")
var Solana = NewChain("solana")
var DYDX = NewChain("dydx")

type Chains struct {
	Chains []*Chain `yaml:"chains"`
}

func (this *Chains) GetChain(name string) *Chain {
	for _, chain := range this.Chains {
		if chain.Name == name {
			return chain
		}
	}
	return nil
}

func GetChains() []*Chain {
	return chainConfig.Chains
}

func (this *Chain) GetTokenAddress(coin string) string {
	for token, address := range this.Tokens {
		if token == coin {
			return address
		}
	}
	ulog.Errorf("invalid coin: %s", coin)
	return ""
}

// return raw balance as big.Int, no decimals
func (this *Chain) _getTokenBalance(privateKey string, addr string, coin string) (*big.Int, error) {
	if this.Name == SolanaName || this.Name == DYDXName {
		return big.NewInt(0), nil
	}
	ethClient, err := core.NewEthClient(this.Endpoint, privateKey)
	if err != nil {
		return big.NewInt(0), err
	}

	if this.FixedGasPrice > 0 {
		ethClient.SetFixedGasPrice(this.FixedGasPrice)
	}

	if coin == this.Coin {
		return ethClient.GetBalance(addr)
	} else {
		tokenAddress := this.GetTokenAddress(coin)
		if tokenAddress == "" {
			return big.NewInt(0), fmt.Errorf("failed to get token address: %s", coin)
		}

		balance, err := ethClient.GetTokenBalance(tokenAddress, addr)
		if err != nil {
			return big.NewInt(0), fmt.Errorf("failed to get token balance: %v", err)
		}

		return balance, nil
	}
}

// return decimals as uint8
func (this *Chain) _getTokenDecimals(privateKey string, coin string) (uint8, error) {
	if this.Name != EthereumName && this.Name != ArbitrumName {
		return 0, errors.New("chain not supported: " + this.Name)
	}

	ethClient, err := core.NewEthClient(this.Endpoint, privateKey)
	if err != nil {
		return 0, err
	}

	if coin == this.Coin {
		return 18, nil
	}

	tokenAddress := this.GetTokenAddress(coin)
	if tokenAddress == "" {
		return 0, fmt.Errorf("failed to get token address: %s", coin)
	}

	return ethClient.GetTokenDecimals(tokenAddress)
}

func (this *Chain) GetBalance(privateKey string, addr string, coin string) (float64, error) {
	balance, err := this._getTokenBalance(privateKey, addr, coin)
	if err != nil {
		return 0, err
	}
	decimals, err := this._getTokenDecimals(privateKey, coin)
	if err != nil {
		return 0, err
	}
	return BigIntToFloat64(balance, int(decimals)), nil
}

func (this *Chain) estimateTransferFee(privateKey string, targetAddr string, amount *big.Int) (float64, error) {
	ethClient, err := core.NewEthClient(this.Endpoint, privateKey)
	if err != nil {
		return 0, err
	}

	if this.FixedGasPrice > 0 {
		ethClient.SetFixedGasPrice(this.FixedGasPrice)
	}

	gasTransferFee, err := ethClient.EstimateTransferFee(amount, targetAddr, "")
	if err != nil {
		return 0, err
	}

	return float64(gasTransferFee.Int64()) / 1e18, nil
}

package hopper

import (
	"chaintools/core"
	"chaintools/utils"
	"chaintools/utils/ulog"
	"context"
	"errors"
	"fmt"
	"math"
	"math/big"
	"slices"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/spf13/cast"
)

type TxStatus string

const UnknownTxStatus TxStatus = ""
const TxStatusPending TxStatus = "pending"
const TxStatusSuccess TxStatus = "success"
const TxStatusFailed TxStatus = "failed"
const TxStatusCancelled TxStatus = "cancelled"

const COIN_CHAIN_SEPARATOR string = "---"

type Txable interface {
	Send(chain *Chain, saveFunc func()) (stop bool)
	RedactedString() string
}

type Tx struct {
	ID          string   `yaml:"id" json:"id"`
	PrivateKey  string   `yaml:"private_key" json:"privateKey"`
	FromAddress string   `yaml:"from_address" json:"fromAddress"`
	ToAddress   string   `yaml:"to_address" json:"toAddress"`
	Coin        string   `yaml:"coin" json:"coin"`
	Amount      float64  `yaml:"amount" json:"amount"`
	Wait        int      `yaml:"wait" json:"wait"`
	Status      TxStatus `yaml:"status" json:"status"`
	TxHash      string   `yaml:"tx_hash" json:"txHash"`
}

func (this *Tx) RedactedString() string {
	remains := fmt.Sprintf("%s,%s,%s,%f,%d,%s,%s", this.FromAddress, this.ToAddress, this.Coin, this.Amount, this.Wait, this.Status, this.TxHash)
	return fmt.Sprintf("%s,%s,%s", this.ID, this.PrivateKey[0:4]+"..."+this.PrivateKey[len(this.PrivateKey)-4:], remains)
}

func (this *Tx) String() string {
	return fmt.Sprintf("%s,%s,%s,%s,%s,%f,%d,%s,%s", this.ID, this.PrivateKey, this.FromAddress, this.ToAddress, this.Coin, this.Amount, this.Wait, this.Status, this.TxHash)
}

func (this *Tx) IsFromExchange() bool {
	return slices.Contains([]string{string(OKX), string(BITGET)}, this.FromAddress)
}

func (this *Tx) Cancel() {
	// only cancel when tx is not sent
	if this.Status != TxStatusPending && this.Status != TxStatusFailed && this.Status != TxStatusSuccess {
		this.Status = TxStatusCancelled
	}
}

type Txs []*Tx

func (this Txs) String() string {
	return strings.Join(this.Strings(), "\n")
}

func (this Txs) Strings() []string {
	result := []string{}
	for _, tx := range this {
		result = append(result, tx.String())
	}
	return result
}

// parse txs from csv, 7 columns if no ID, 8 columns includes ID
func NewTxsFromCSV(csv string) (txs Txs) {
	lines := strings.Split(csv, "\n")
	count := 1
	for _, line := range lines {
		if strings.HasPrefix(line, "#") {
			continue
		}
		if strings.TrimSpace(line) == "" {
			continue
		}
		fields := strings.Split(line, ",")
		if len(fields) < 8 {
			ulog.Errorf("invalid csv line: %s", line)
			ulog.Infof("please check columns: ID(可选), PrivateKey, FromAddress, ToAddress, Coin, Amount, Wait, Status, TxHash")
			continue
		}
		var tx *Tx
		if len(fields) == 8 {
			tx = &Tx{
				ID:          fmt.Sprintf("TX%03d", count),
				PrivateKey:  fields[0],
				FromAddress: fields[1],
				ToAddress:   fields[2],
				Coin:        fields[3],
				Amount:      cast.ToFloat64(fields[4]),
				Wait:        cast.ToInt(fields[5]),
				Status:      TxStatus(fields[6]),
				TxHash:      fields[7],
			}
		} else if len(fields) == 9 {
			tx = &Tx{
				ID:          fields[0],
				PrivateKey:  fields[1],
				FromAddress: fields[2],
				ToAddress:   fields[3],
				Coin:        fields[4],
				Amount:      cast.ToFloat64(fields[5]),
				Wait:        cast.ToInt(fields[6]),
				Status:      TxStatus(fields[7]),
				TxHash:      fields[8],
			}
		}
		txs = append(txs, tx)
		count += 1
	}
	return
}

func (this Txs) FormatSendingPreview() string {
	result := "[No TXs]"
	t := utils.NewTable()
	t.SetHeader([]string{"ID", "From", "To", "Amount", "Coin"})
	for _, tx := range this {
		if tx.Status != TxStatusSuccess {
			t.AddRow([]string{tx.ID, tx.FromAddress, tx.ToAddress, fmt.Sprintf("%.8f", tx.Amount), tx.Coin})
		}
	}
	if len(t.Rows) > 1 {
		result = t.Render()
	}
	return result
}

func (this Txs) isExecuted() bool {
	for _, tx := range this {
		if tx.Status != UnknownTxStatus {
			return true
		}
	}
	return false
}

func speedUpTx(chain *Chain, tx *Tx) (bool, error) {
	if tx.Status != TxStatusPending {
		return false, fmt.Errorf("tx is not pending: %s", tx.ID)
	}

	privateKey := tx.PrivateKey
	ethClient, err := core.NewEthClient(chain.Endpoint, privateKey)
	if err != nil {
		return false, err
	}

	fixedGasPrice := utils.SurveyFloat("请输入加速交易的 Gas Price (gwei)：")
	txHash, err := ethClient.SpeedUpTransaction(tx.TxHash, fixedGasPrice)
	if err != nil {
		return false, err
	}

	tx.TxHash = txHash
	time.Sleep(1 * time.Second) // wait the transaction to broadcast

	txStatus := queryTx(ethClient, txHash)
	if txStatus == RawTxStatusFailed {
		ulog.Errorf("tx speed up failed after retry, abort, tx hash: %s", txHash)
		tx.Status = TxStatusFailed
		return false, nil
	} else if txStatus == RawTxStatusPending {
		ulog.Errorf("tx speed up is still pending after retry, abort, tx hash: %s", txHash)
		return false, nil
	} else {
		tx.Status = TxStatusSuccess
		ulog.Infof("tx speed up success: %s", tx.RedactedString())
		return true, nil
	}
}

// chain can be null, if chain specified in the tx
func (this Txs) Send(chain *Chain, saveFunc func()) {
	for _, tx := range this {
		for {
			ulog.Debugf("sending tx: %s", tx.ID)
			stop, err := sendTx(chain, tx, saveFunc)
			if err != nil {
				ulog.Errorf("failed to send tx: %v", err)
			}
			if stop {
				choices := []string{"resend", "skip", "abort"}
				if tx.Status == TxStatusPending && tx.TxHash != "" {
					choices = append(choices, "speed up")
				}
				_, choice := utils.SurveySelect("Do you want to resend this tx? (y/n): ", choices)
				if choice == "abort" {
					return
				} else if choice == "skip" {
					ulog.Infof("skipping tx: %s", tx.ID)
					tx.Status = TxStatusCancelled
					break
				} else if choice == "speed up" {
					ulog.Infof("speeding up tx: %s", tx.ID)
					success, err := speedUpTx(chain, tx)
					if err != nil {
						ulog.Errorf("failed to speed up tx: %v", err)
						return
					} else if success {
						ulog.Infof("tx speed up success: %s", tx.ID)
					} else {
						ulog.Errorf("tx speed up failed: %s", tx.ID)
						return
					}
					if utils.SurveyYes("Continue to send next tx? (y/N): ") {
						break
					} else {
						return
					}
				} else {
					continue
				}
			} else {
				break
			}
		}
	}
}

func (this *Tx) Send(chain *Chain, saveFunc func()) (stop bool, err error) {
	return sendTx(chain, this, saveFunc)
}

// chain can be null, if chain specified in the tx
func sendTx(chain *Chain, tx *Tx, saveFunc func()) (stop bool, er error) {
	defer func() {
		if saveFunc != nil {
			saveFunc()
		}
	}()

	if tx.Status == TxStatusSuccess {
		ulog.Infof("tx already sent: %s", tx.RedactedString())
		return
	}

	if tx.Status == TxStatusCancelled {
		ulog.Infof("tx cancelled: %s", tx.RedactedString())
		return
	}

	// wait before sending tx, in balancer application, we rely on wait to send txs in random order
	// wait after tx been sent can't guarantee the randomness
	if tx.Wait > 0 {
		ulog.Infof("waiting %d seconds to send tx: %s", tx.Wait, tx.RedactedString())
		time.Sleep(time.Duration(tx.Wait) * time.Second)
	}
	// tx status maybe canceled during wait, check again
	if tx.Status == TxStatusCancelled {
		ulog.Infof("tx cancelled: %s", tx.RedactedString())
		return
	}

	txHash := ""

	if tx.IsFromExchange() {
		ulog.Infof("\n[SENDING TX]: %s\n", tx.RedactedString())

		// 从交易所提币
		apiStr := strings.Split(tx.PrivateKey, "/")
		if len(apiStr) != 2 {
			ulog.Errorf("invalid api key: %s", tx.PrivateKey)
			tx.Status = TxStatusFailed
			stop = true
			er = fmt.Errorf("invalid api key: %s", tx.PrivateKey)
			return
		}

		apiKey := apiStr[0]
		apiSecret := apiStr[1]
		ex, err := NewExchangeAPI("", ExchangeName(tx.FromAddress), apiKey, apiSecret, false)
		if err != nil {
			ulog.Errorf("failed to create exchange: %v", err)
			tx.Status = TxStatusFailed
			stop = true
			er = fmt.Errorf("failed to create exchange: %v", err)
			return
		}
		coin := strings.Split(tx.Coin, COIN_CHAIN_SEPARATOR)[0]
		chainName := ""
		if chain != nil {
			chainName = chain.Name
		}
		// 允许在 coin 中指定 chain ，格式：{Coin}---{Chain}
		if strings.Contains(coin, COIN_CHAIN_SEPARATOR) && chainName != "" {
			// 如果已经通过参数制定了 chain，那么不使用 tx 中的 chain
			chainName = strings.Split(coin, COIN_CHAIN_SEPARATOR)[1]
		}
		if chainName == "" {
			ulog.Errorf("failed to withdraw from exchange: chain is empty")
			tx.Status = TxStatusFailed
			stop = true
			er = fmt.Errorf("failed to withdraw from exchange: chain is empty")
			return
		}

		_, balance, err := ex.GetBalance(coin)
		if err != nil {
			ulog.Errorf("failed to get balance: %v", err)
			tx.Status = TxStatusFailed
			stop = true
			er = fmt.Errorf("failed to get balance: %v", err)
			return
		}
		if balance < tx.Amount {
			ulog.Errorf("balance is less than amount: %f < %f", balance, tx.Amount)
			tx.Status = TxStatusFailed
			stop = true
			er = fmt.Errorf("balance is less than amount: %f < %f", balance, tx.Amount)
			return
		}

		wid, err := ex.Withdraw(tx.Amount, coin, tx.ToAddress, chainName)
		if err != nil {
			ulog.Errorf("failed to withdraw from exchange: %v", err)
			tx.Status = TxStatusFailed
			stop = true
			er = fmt.Errorf("failed to withdraw from exchange: %v", err)
			return
		}

		// 暂时不查询 tx hash，没有并发请求，每一笔交易至少等一分钟，很慢
		// txHash, err = queryWithdrawTxId(ex, wid)
		// if err != nil {
		// 	ulog.Errorf("failed to query withdraw txId: %v", err)
		// 	tx.Status = TxStatusFailed
		// 	stop = true
		// 	return
		// }
		// tx.TxHash = txHash
		// ulog.Infof("exchange transaction hash: %s", txHash)

		tx.Status = TxStatusSuccess
		tx.TxHash = wid
		ulog.Infof("tx success, withdraw id: %s", wid)
		time.Sleep(3 * time.Second)
	} else {
		if chain.Name != EthereumName && chain.Name != ArbitrumName {
			ulog.Errorf("unsupported chain: %s", chain.Name)
			tx.Status = TxStatusFailed
			stop = true
			er = fmt.Errorf("unsupported chain: %s", chain.Name)
			return
		}
		privateKey := tx.PrivateKey
		ethClient, err := core.NewEthClient(chain.Endpoint, privateKey)
		if err != nil {
			ulog.Errorf("failed to create eth client: %v", err)
			stop = true
			er = fmt.Errorf("failed to create eth client: %v", err)
			return
		}

		if chain.FixedGasPrice > 0 {
			ethClient.SetFixedGasPrice(chain.FixedGasPrice)
		}

		// 如果 txHash 不为空，说明之前已经发送过了，但是状态未知，需要查询状态
		// 如果状态是成功，直接跳过
		// 如果状态是失败，需要重新发送
		// 如果状态是 pending，则直接退出
		if tx.Status == TxStatusPending {
			if tx.TxHash != "" {
				status := queryTx(ethClient, tx.TxHash)
				if status == RawTxStatusSuccess {
					tx.Status = TxStatusSuccess
					ulog.Infof("tx success: %s", tx.RedactedString())
					return
				} else if status == RawTxStatusPending {
					ulog.Infof("tx is still pending: %s", tx.TxHash)
					stop = true
					er = fmt.Errorf("tx is still pending: %s", tx.TxHash)
					return
				} else {
					ulog.Errorf("tx failed: %s", tx.TxHash)
					tx.TxHash = ""
					er = fmt.Errorf("tx failed: %s", tx.TxHash)
					return
				}
			} else {
				ulog.Errorf("tx status is pending, but tx hash is empty: %s", tx.RedactedString())
				tx.Status = UnknownTxStatus
				stop = true
				er = fmt.Errorf("tx status is pending, but tx hash is empty: %s", tx.RedactedString())
				return
			}
		}

		tx.Status = TxStatusPending
		ulog.Infof("\n[SENDING TX]: %s\n", tx.RedactedString())

		// 使用私钥转账
		coinName := strings.Split(tx.Coin, COIN_CHAIN_SEPARATOR)[0]
		if coinName == chain.Coin {
			var transferAmount *big.Int
			if tx.Amount == 0 {
				balance, err := chain._getTokenBalance(tx.PrivateKey, tx.FromAddress, coinName)
				if err != nil {
					ulog.Errorf("failed to get balance: %v", err)
					stop = true
					er = fmt.Errorf("failed to get balance: %v", err)
					return
				}

				if balance.Cmp(big.NewInt(0)) == 0 {
					ulog.Errorf("balance is 0, abort")
					stop = true
					er = fmt.Errorf("balance is 0, abort")
					return
				}

				fee, err := chain.estimateTransferFee(tx.PrivateKey, tx.ToAddress, balance)
				if err != nil {
					ulog.Errorf("failed to estimate transfer fee: %v", err)
					stop = true
					er = fmt.Errorf("failed to estimate transfer fee: %v", err)
					return
				}
				// 实时预估的费用再加10%
				transferAmount = big.NewInt(balance.Int64() - int64(fee*GAS_FEE_MARGIN_RATIO*1e18))
			} else {
				transferAmount = big.NewInt(int64(tx.Amount * 1e18))
			}

			err := retryDo(func() error {
				txHash, err = ethClient.Transfer(transferAmount, tx.ToAddress, core.TransferOptions{})
				if err != nil {
					ulog.Errorf("failed to transfer: %v", err)
					return fmt.Errorf("failed to transfer: %v", err)
				}
				return nil
			}, 3, 3)
			// 多次尝试转账失败，直接退出
			if err != nil {
				stop = true
				er = fmt.Errorf("failed to transfer: %v", err)
				return
			}
		} else {
			tokenAddress := chain.GetTokenAddress(coinName)
			if tokenAddress == "" {
				ulog.Errorf("failed to get token address: %s", coinName)
				stop = true
				er = fmt.Errorf("failed to get token address: %s", coinName)
				return
			}

			var transferAmount *big.Int
			if tx.Amount == 0 {
				tokenBalance, err := chain._getTokenBalance(tx.PrivateKey, tx.FromAddress, coinName)
				if err != nil {
					ulog.Errorf("failed to get token balance: %v", err)
					stop = true
					er = fmt.Errorf("failed to get token balance: %v", err)
					return
				}

				if tokenBalance.Cmp(big.NewInt(0)) == 0 {
					ulog.Errorf("token balance is 0, abort")
					stop = true
					er = fmt.Errorf("token balance is 0, abort")
					return
				}

				transferAmount = tokenBalance
			} else {
				decimals, err := chain._getTokenDecimals(tx.PrivateKey, coinName)
				if err != nil {
					ulog.Errorf("failed to get token decimals: %v", err)
					stop = true
					er = fmt.Errorf("failed to get token decimals: %v", err)
					return
				}
				transferAmount = big.NewInt(int64(tx.Amount * math.Pow10(int(decimals))))
			}

			err2 := retryDo(func() error {
				txHash, err = ethClient.TransferToken(tokenAddress, transferAmount, tx.ToAddress, core.TransferOptions{})
				if err != nil {
					ulog.Errorf("failed to transfer token: %v", err)
					return fmt.Errorf("failed to transfer token: %v", err)
				}
				return nil
			}, 3, 3)
			if err2 != nil {
				stop = true
				er = fmt.Errorf("failed to transfer token: %v", err)
				return
			}
		}

		tx.TxHash = txHash
		ulog.Infof("raw transaction hash: %s", txHash)
		time.Sleep(1 * time.Second) // wait the transaction to broadcast

		txStatus := queryTx(ethClient, txHash)
		if txStatus == RawTxStatusFailed {
			ulog.Errorf("tx has failed after retry, abort, tx hash: %s", txHash)
			tx.Status = TxStatusFailed
			stop = true
			er = fmt.Errorf("tx has failed after retry, abort, tx hash: %s", txHash)
			return
		} else if txStatus == RawTxStatusPending {
			ulog.Errorf("tx is still pending after retry, abort, tx hash: %s", txHash)
			stop = true
			er = fmt.Errorf("tx is still pending after retry, abort, tx hash: %s", txHash)
			return
		} else {
			tx.Status = TxStatusSuccess
			ulog.Infof("tx success: %s", tx.RedactedString())
		}
	}

	time.Sleep(3 * time.Second) // safely stop the program if there is a ctrl + c
	return
}

type RawTxStatus string

const RawTxStatusPending RawTxStatus = "pending"
const RawTxStatusSuccess RawTxStatus = "success"
const RawTxStatusFailed RawTxStatus = "failed"

var ErrQueryTxByHash error = errors.New("failed to query tx by hash")
var ErrQueryTxByHashPending error = errors.New("query tx by hash, status is pending")
var ErrQueryTxReceipt error = errors.New("failed to query tx receipt")
var ErrTxReceiptFailed error = errors.New("query tx receipt result: failed")

func queryTx(ethClient *core.EthClient, txHash string) (status RawTxStatus) {
	status = RawTxStatusPending

	err := retryDo(func() error {
		_, isPending, err := ethClient.TransactionByHash(context.Background(), common.HexToHash(txHash))
		if err != nil {
			return ErrQueryTxByHash
		}

		if isPending {
			return ErrQueryTxByHashPending
		}

		receipt, err := ethClient.TransactionReceipt(context.Background(), common.HexToHash(txHash))
		if err != nil {
			return ErrQueryTxReceipt
		}

		// 只有在明确知道状态的情况下才返回 success 或 failed
		if receipt.Status == 1 {
			status = RawTxStatusSuccess
			return nil
		} else {
			status = RawTxStatusFailed
			return nil
		}
	}, 5, 10)
	if err != nil {
		ulog.Errorf("failed to query tx: %v", err)
	}
	return
}

func queryWithdrawTxId(ex *ExchangeAPI, withdrawID string) (txId string, err error) {
	err = retryDo(func() error {
		txId, err = ex.QueryWithdrawTxId(withdrawID)
		if err != nil {
			return err
		}
		if txId == "" {
			return errors.New("can not find withdraw txId")
		}

		return nil
	}, 10, 12)
	return
}

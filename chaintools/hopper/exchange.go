package hopper

import (
	"fmt"
	"time"
)

type ExchangeName string

const OKX ExchangeName = "okx"
const BITGET ExchangeName = "bitget"

type ExchangeAPI struct {
	ID           string
	Exchange     `yaml:"-"`
	ExchangeName ExchangeName
	APIKey       string
	APISecret    string
	IsTestnet    bool
	lastReqTime  time.Time
}

type Exchange interface {
	GetExchangeName() ExchangeName
	IsAvailable() (bool, error)
	GetBalance(coin string) (total, available float64, err error)
	Withdraw(amt float64, coin, address, chain string) (string, error)
	QueryWithdrawTxId(withdrawID string) (string, error)
	GetDepositAddress(coin, chain string) ([]string, error)
}

func NewExchangeAPI(id string, name ExchangeName, apiKey, apiSecret string, isTestnet bool) (*ExchangeAPI, error) {
	var ex Exchange
	switch name {
	case OKX:
		ex = NewOKx(apiKey, apiSecret, isTestnet)
	case BITGET:
		ex = NewBitget(apiKey, apiSecret, isTestnet)
	default:
		return nil, fmt.Errorf("unsupported exchange: %s", name)
	}
	if ex == nil {
		return nil, fmt.Errorf("exchange init failed: %s", name)
	}
	return &ExchangeAPI{
		ID:           id,
		Exchange:     ex,
		ExchangeName: name,
		APIKey:       apiKey,
		APISecret:    apiSecret,
		IsTestnet:    isTestnet,
	}, nil
}

func (e *ExchangeAPI) UnmarshalYAML(unmarshal func(interface{}) error) error {
	type plain ExchangeAPI
	if err := unmarshal((*plain)(e)); err != nil {
		return err
	}
	switch e.ExchangeName {
	case OKX:
		e.Exchange = NewOKx(e.APIKey, e.APISecret, e.IsTestnet)
	case BITGET:
		e.Exchange = NewBitget(e.APIKey, e.APISecret, e.IsTestnet)
	default:
		return fmt.Errorf("unsupported exchange: %s", e.ExchangeName)
	}
	return unmarshal(e.Exchange)
}

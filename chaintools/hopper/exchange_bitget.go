package hopper

import (
	"crypto"
	"crypto/hmac"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

const (
	BgTimeoutSecond = 30
	BgSignType      = ""
)

type Bitget ExchangeAPI

func NewBitget(apiKey, apiSecret string, isTestnet bool) *Bitget {
	return &Bitget{
		ExchangeName: BITGET,
		APIKey:       apiKey,
		APISecret:    apiSecret,
		IsTestnet:    isTestnet,
	}
}

func convertToBitgetCoin(coin string, chainName string) (string, string) {
	switch coin {
	case "USDT":
		switch chainName {
		case "ethereum":
			return "USDT", "ERC20"
		case "solana":
			return "USDT", "SOL"
		case "arbitrum":
			return "USDT", "ArbitrumOne"
		case "":
			return "USDT", ""
		}
	case "ETH":
		switch chainName {
		case "ethereum":
			return "ETH", "ETH"
		case "solana":
			return "ETH", "SOL"
		case "arbitrum":
			return "ETH", "ArbitrumOne"
		case "":
			return "ETH", ""
		}
	case "USDC":
		switch chainName {
		case "ethereum":
			return "USDC", "ERC20"
		case "solana":
			return "USDC", "SOL"
		case "arbitrum":
			return "USDC", "ArbitrumOne"
		case "":
			return "USDC", ""
		}
	}
	return "", ""
}

func (this *Bitget) GetExchangeName() ExchangeName {
	return this.ExchangeName
}

func (this *Bitget) IsAvailable() (bool, error) {
	_, _, err := this.GetBalance("USDC")
	if err != nil {
		return false, err
	}
	return true, nil
}

// 获取资金账户余额
func (this *Bitget) GetBalance(coin string) (total, available float64, err error) {
	// https://www.bitget.com/api-doc/spot/account/Get-Account-Assets
	// {
	// 	"code": "00000",
	// 	"message": "success",
	// 	"requestTime": *************,
	// 	"data": [
	// 		{
	// 			"coin": "usdt",
	// 			"available": "0",
	// 			"frozen": "0",
	// 			"locked": "0",
	// 			"limitAvailable": "0",
	// 			"uTime": "**********"
	// 		}
	// 	]
	// }
	api, err := NewBitgetApi(this.APIKey, this.APISecret, this.IsTestnet)
	if err != nil {
		return 0, 0, err
	}
	coin, _ = convertToBitgetCoin(coin, "")
	if coin == "" {
		return 0, 0, fmt.Errorf("unsupported coin: %s", coin)
	}
	params := map[string]string{
		"coin": coin,
	}
	result, err := api.GetAccountAssets(params)
	if err != nil {
		return 0, 0, err
	}
	for _, item := range result.Array() {
		if item.Get("coin").String() == coin {
			available = item.Get("available").Float()
			total = available + item.Get("frozen").Float() + item.Get("locked").Float()
			return total, available, nil
		}
	}
	return 0, 0, fmt.Errorf("coin not found: %s", coin)
}

// 获取子账户资金账户余额（适用于母账户）
func (this *Bitget) GetSubaccountBalance(subAcct, coin string) (total, available float64, err error) {
	// https://www.bitget.com/api-doc/spot/account/Get-Subaccount-Assets
	// [
	// {
	// 	"id": 1111,
	// 	"userId": **********,
	// 	"assetsList": [
	// 	  {
	// 		"coin": "BTC",
	// 		"available": "1.1",
	// 		"limitAvailable": "12.1",
	// 		"frozen": "0",
	// 		"locked": "1.1",
	// 		"uTime": "*************"
	// 	  }
	// 	]
	//   }
	coin, _ = convertToBitgetCoin(coin, "")
	if coin == "" {
		return 0, 0, fmt.Errorf("unsupported coin: %s", coin)
	}

	api, err := NewBitgetApi(this.APIKey, this.APISecret, this.IsTestnet)
	if err != nil {
		return 0, 0, err
	}
	params := map[string]string{
		"limit": "50",
	}
	result, err := api.GetSubaccountAssets(params)
	if err != nil {
		return 0, 0, err
	}

	subAcctID, err := cast.ToIntE(subAcct)
	if err != nil {
		return 0, 0, err
	}

	for _, item := range result.Array() {
		if item.Get("userId").Int() == int64(subAcctID) {
			for _, asset := range item.Get("assetsList").Array() {
				if asset.Get("coin").String() == coin {
					available = asset.Get("available").Float()
					total = available + asset.Get("frozen").Float() + asset.Get("locked").Float()
					return total, available, nil
				}
			}
		}
	}

	return total, available, nil
}

func (this *Bitget) Withdraw(amt float64, coin, address string, chainName string) (string, error) {
	// https://www.bitget.com/api-doc/spot/account/Wallet-Withdrawal
	// {
	// 	"code": "00000",
	// 	"msg": "success",
	// 	"requestTime": *************,
	// 	"data": {
	// 		"orderId": "123",
	// 		"clientOid": "123"
	// 	}
	// }
	api, err := NewBitgetApi(this.APIKey, this.APISecret, this.IsTestnet)
	if err != nil {
		return "", err
	}

	coinInfo, err := this.GetCoinInfo(coin, chainName)
	if err != nil {
		return "", err
	}

	coin, chainName = convertToBitgetCoin(coin, chainName)
	if coin == "" {
		return "", fmt.Errorf("unsupported coin: %s", coin)
	}

	size := amt + coinInfo.WithdrawFee

	if size < coinInfo.MinWithdrawAmount {
		return "", fmt.Errorf("amount is too small, at least %f", coinInfo.MinWithdrawAmount)
	}

	params := map[string]string{
		"coin":         coin,
		"transferType": "on_chain",
		"address":      address,
		"chain":        chainName,
		"size":         strconv.FormatFloat(size, 'f', -1, 64),
	}
	result, err := api.Withdrawal(params)
	if err != nil {
		return "", err
	}
	return result.Get("orderId").String(), nil
}

// 如果txId已经生成，则返回，否则返回""
func (this *Bitget) QueryWithdrawTxId(withdrawID string) (string, error) {
	// https://www.bitget.com/api-doc/spot/account/Get-Withdraw-Record
	// {
	// 	"code": "00000",
	// 	"msg": "success",
	// 	"requestTime": *************,
	// 	"data": [
	// 		{
	// 			"orderId": "1",
	// 			"tradeId": "1",
	// 			"coin": "USDT",
	// 			"dest": "dest",
	// 			"clientOid": "123",
	// 			"type": "withdraw",
	// 			"tag": "",
	// 			"size": "10.********",
	// 			"fee": "-1.********",
	// 			"status": "success",
	// 			"toAddress": "1",
	// 			"fromAddress": "2",
	// 			"confirm": "100",
	// 			"chain": "erc20",
	// 			"cTime": "*************",
	// 			"uTime": "*************"
	// 		}
	// 	]
	// }
	api, err := NewBitgetApi(this.APIKey, this.APISecret, this.IsTestnet)
	if err != nil {
		return "", err
	}
	params := map[string]string{
		"orderId":   withdrawID,
		"startTime": TimesStamp(time.Now().Add(-time.Hour * 24)),
		"endTime":   TimesStamp(time.Now()),
		"limit":     "100",
	}
	result, err := api.WithdrawalRecords(params)
	if err != nil {
		return "", err
	}
	for _, item := range result.Array() {
		if item.Get("orderId").String() == withdrawID {
			return item.Get("tradeId").String(), nil
		}
	}
	return "", nil
}

func (this *Bitget) GetDepositAddress(coin, chain string) ([]string, error) {
	// // https://www.bitget.com/api-doc/spot/account/Get-Deposit-Address
	// // {
	// // 	"code": "00000",
	// // 	"msg": "success",
	// // 	"requestTime": *************,
	// // 	"data": {
	// // 		"address": "xxx",
	// // 		"chain": "BTC-Bitcoin",
	// // 		"coin": "BTC",
	// // 		"tag": "",
	// // 		"url": "https://btc.com/xxx"
	// // 	}
	// // }
	// api := NewBitgetApi(this.APIKey, this.APISecret, this.IsTestnet)
	// params := map[string]string{
	// 	"coin":  coin,
	// 	"chain": convertToBitgetChain(chain),
	// }
	// result, err := api.DepositAddress(params)
	// if err != nil {
	// 	return []string{}, err
	// }
	// return []string{result.Get("address").String(), result.Get("url").String()}, nil
	return []string{}, nil
}

type BgTransferAccount string

const (
	BgTransferAccountSpot BgTransferAccount = "spot"
)

type BgTransferType string

const (
	BgTransferTypeMainToSub BgTransferType = "main_to_sub"
	BgTransferTypeSubToMain BgTransferType = "sub_to_main"
)

func (this *Bitget) AssetTransfer(coin string, from, to BgTransferAccount, amt float64, transferType BgTransferType, subAcct string) (string, error) {
	// https://www.bitget.com/api-doc/spot/account/Sub-Transfer
	// {
	// 	"code": "00000",
	// 	"msg": "success",
	// 	"requestTime": *************,
	// 	"data": {
	// 		"transferId": "123456",
	// 		"clientOid": "x123"
	// 	}
	// }

	api, err := NewBitgetApi(this.APIKey, this.APISecret, this.IsTestnet)
	if err != nil {
		return "", err
	}
	if api.IsSubAccount {
		return "", fmt.Errorf("sub account transfer is not supported for subaccount api")
	}

	params := map[string]string{
		"coin":     coin,
		"fromType": string(from),
		"toType":   string(to),
		"amount":   strconv.FormatFloat(amt, 'f', -1, 64),
	}

	if subAcct != strconv.Itoa(api.UserID) {
		if transferType == BgTransferTypeMainToSub {
			params["fromUserId"] = strconv.Itoa(api.UserID)
			params["toUserId"] = subAcct
		} else if transferType == BgTransferTypeSubToMain {
			params["fromUserId"] = subAcct
			params["toUserId"] = strconv.Itoa(api.UserID)
		}

		result, err := api.SubaccountTransfer(params)
		if err != nil {
			return "", err
		}
		return result.Get("transferId").String(), nil
	} else {
		result, err := api.Transfer(params)
		if err != nil {
			return "", err
		}
		return result.Get("transferId").String(), nil
	}
}

//	{
//		"chain": "ArbitrumOne",
//		"needTag": "false",
//		"withdrawable": "true",
//		"rechargeable": "true",
//		"withdrawFee": "0.2",
//		"extraWithdrawFee": "0",
//		"depositConfirm": "12",
//		"withdrawConfirm": "12",
//		"minDepositAmount": "0.01",
//		"minWithdrawAmount": "10",
//		"browserUrl": "https://arbiscan.io/tx/",
//		"contractAddress": "0xaf88d065e77c8cc2239327c5edb3a432268e5831",
//		"withdrawStep": "0",
//		"withdrawMinScale": "8",
//		"congestion": "normal"
//	  },
type CoinInfo struct {
	Chain             string  `json:"chain"`
	NeedTag           bool    `json:"needTag"`
	Withdrawable      bool    `json:"withdrawable"`
	Rechargeable      bool    `json:"rechargeable"`
	WithdrawFee       float64 `json:"withdrawFee"`
	ExtraWithdrawFee  float64 `json:"extraWithdrawFee"`
	DepositConfirm    int     `json:"depositConfirm"`
	WithdrawConfirm   int     `json:"withdrawConfirm"`
	MinDepositAmount  float64 `json:"minDepositAmount"`
	MinWithdrawAmount float64 `json:"minWithdrawAmount"`
	BrowserUrl        string  `json:"browserUrl"`
	ContractAddress   string  `json:"contractAddress"`
	WithdrawStep      int     `json:"withdrawStep"`
	WithdrawMinScale  int     `json:"withdrawMinScale"`
	Congestion        string  `json:"congestion"`
}

var coinInfoCache = make(map[string]CoinInfo)

func (this *Bitget) GetCoinInfo(coin string, chainName string) (coinInfo CoinInfo, err error) {
	// https://www.bitget.com/api-doc/spot/market/Get-Coin-List
	// {
	// 	"code": "00000",
	// 	"msg": "success",
	// 	"requestTime": 1695799900330,
	// 	"data": [
	// 		{
	// 			"coinId": "1",
	// 			"coin": "BTC",
	// 			"transfer": "true",
	// 			"chains": [
	// 				{
	// 					"chain": "BTC",
	// 					"needTag": "false",
	// 					"withdrawable": "true",
	// 					"rechargeable": "true",
	// 					"withdrawFee": "0.005",
	// 					"extraWithdrawFee": "0",
	// 					"depositConfirm": "1",
	// 					"withdrawConfirm": "1",
	// 					"minDepositAmount": "0.001",
	// 					"minWithdrawAmount": "0.001",
	// 					"browserUrl": "https://blockchair.com/bitcoin/testnet/transaction/",
	// 					"contractAddress": "******************************************",
	// 					"withdrawStep": "0",
	// 					"withdrawMinScale": "8",
	// 					"congestion":"normal"
	// 				}
	// 			]
	// 		}
	// 	]
	// }
	if coinInfo, ok := coinInfoCache[coin+"::"+chainName]; ok {
		return coinInfo, nil
	}
	api, err := NewBitgetApi(this.APIKey, this.APISecret, this.IsTestnet)
	if err != nil {
		return CoinInfo{}, err
	}

	bgCoin, bgChainName := convertToBitgetCoin(coin, chainName)
	if bgCoin == "" {
		return CoinInfo{}, fmt.Errorf("unsupported coin: %s", bgCoin)
	}

	params := map[string]string{
		"coin": bgCoin,
	}
	result, err := api.GetCoinInfo(params)
	if err != nil {
		return CoinInfo{}, err
	}
	for _, item := range result.Array() {
		if item.Get("coin").String() == bgCoin {
			for _, chainItem := range item.Get("chains").Array() {
				if chainItem.Get("chain").String() == bgChainName {
					coinInfo.Chain = chainItem.Get("chain").String()
					coinInfo.NeedTag = chainItem.Get("needTag").Bool()
					coinInfo.Withdrawable = chainItem.Get("withdrawable").Bool()
					coinInfo.Rechargeable = chainItem.Get("rechargeable").Bool()
					coinInfo.WithdrawFee = chainItem.Get("withdrawFee").Float()
					coinInfo.ExtraWithdrawFee = chainItem.Get("extraWithdrawFee").Float()
					coinInfo.DepositConfirm = int(chainItem.Get("depositConfirm").Int())
					coinInfo.WithdrawConfirm = int(chainItem.Get("withdrawConfirm").Int())
					coinInfo.MinDepositAmount = chainItem.Get("minDepositAmount").Float()
					coinInfo.MinWithdrawAmount = chainItem.Get("minWithdrawAmount").Float()
					coinInfo.BrowserUrl = chainItem.Get("browserUrl").String()
					coinInfo.ContractAddress = chainItem.Get("contractAddress").String()
					coinInfo.WithdrawStep = int(chainItem.Get("withdrawStep").Int())
					coinInfo.WithdrawMinScale = int(chainItem.Get("withdrawMinScale").Int())
					coinInfo.Congestion = chainItem.Get("congestion").String()
					coinInfoCache[coin+"::"+chainName] = coinInfo
					return coinInfo, nil
				}
			}
		}
	}
	return CoinInfo{}, fmt.Errorf("coin not found: %s", bgCoin)
}

/* copy from bitget sdk */

const (
	/*
	 * http headers
	 */
	BgContentType      = "Content-Type"
	BgAccessKey        = "ACCESS-KEY"
	BgAccessSign       = "ACCESS-SIGN"
	BgAccessTimestamp  = "ACCESS-TIMESTAMP"
	BgAccessPassphrase = "ACCESS-PASSPHRASE"
	BgApplicationJson  = "application/json"

	EN_US  = "en_US"
	ZH_CN  = "zh_CN"
	LOCALE = "locale="

	/*
	 * http methods
	 */
	GET  = "GET"
	POST = "POST"

	/*
	 * SignType
	 */
	RSA    = "RSA"
	SHA256 = "SHA256"
)

func TimesStampNow() string {
	return TimesStamp(time.Now())
}

func TimesStamp(t time.Time) string {
	timesStamp := t.Unix() * 1000
	return strconv.FormatInt(timesStamp, 10)
}

func TimesStampSecNow() string {
	return TimesStampSec(time.Now())
}

func TimesStampSec(t time.Time) string {
	timesStamp := t.Unix()
	return strconv.FormatInt(timesStamp, 10)
}

/**
 * get header
 */
func Headers(request *http.Request, apikey string, timestamp string, sign string, passphrase string) {
	request.Header.Add(BgContentType, BgApplicationJson)
	request.Header.Add(BgAccessKey, apikey)
	request.Header.Add(BgAccessSign, sign)
	request.Header.Add(BgAccessTimestamp, timestamp)
	request.Header.Add(BgAccessPassphrase, passphrase)
}

func BuildJsonParams(params map[string]string) (string, error) {
	if params == nil {
		return "", errors.New("illegal parameter")
	}
	data, err := json.Marshal(params)
	if err != nil {
		return "", errors.New("json convert string error")
	}
	jsonBody := string(data)
	return jsonBody, nil
}

func BuildGetParams(params map[string]string) string {
	//urlParams := url.Values{}
	//if params != nil && len(params) > 0 {
	//	for k := range params {
	//		urlParams.Add(k, params[k])
	//	}
	//}
	//return "?" + urlParams.Encode()
	if len(params) == 0 {
		return ""
	}
	return "?" + SortParams(params)
}

func SortParams(params map[string]string) string {
	keys := make([]string, len(params))
	i := 0
	for k, _ := range params {
		keys[i] = k
		i++
	}
	sort.Strings(keys)
	sorted := make([]string, len(params))
	i = 0
	for _, k := range keys {
		//sorted[i] = k + "=" + url.QueryEscape(params[k])
		sorted[i] = k + "=" + params[k]
		i++
	}
	return strings.Join(sorted, "&")
}

func JSONToMap(str string) map[string]interface{} {

	var tempMap map[string]interface{}

	err := json.Unmarshal([]byte(str), &tempMap)

	if err != nil {
		panic(err)
	}

	return tempMap
}

func NewParams() map[string]string {
	return make(map[string]string)
}

func ToJson(v interface{}) (string, error) {
	result, err := json.Marshal(v)
	if err != nil {
		return "", err
	}
	return string(result), nil
}

type BitgetClient struct {
	ApiKey       string
	ApiSecretKey string
	Passphrase   string
	BaseUrl      string
	HttpClient   http.Client
	Signer       *BgSigner
}

func NewBitgetRestClient(apiKey, apiSecretKey, passphrase, baseUrl string) *BitgetClient {
	p := &BitgetClient{
		ApiKey:       apiKey,
		ApiSecretKey: apiSecretKey,
		Passphrase:   passphrase,
		BaseUrl:      baseUrl,
	}
	p.Signer = new(BgSigner).Init(apiSecretKey)
	p.HttpClient = http.Client{
		Timeout: time.Duration(BgTimeoutSecond) * time.Second,
	}
	return p
}

func (p *BitgetClient) DoPost(uri string, params string) (string, error) {
	timesStamp := TimesStampNow()
	//body, _ := internal.BuildJsonParams(params)

	sign := p.Signer.Sign(POST, uri, params, timesStamp)
	if BgSignType == RSA {
		sign = p.Signer.SignByRSA(POST, uri, params, timesStamp)
	}
	requestUrl := p.BaseUrl + uri

	buffer := strings.NewReader(params)
	request, err := http.NewRequest(POST, requestUrl, buffer)

	Headers(request, p.ApiKey, timesStamp, sign, p.Passphrase)
	if err != nil {
		return "", err
	}
	response, err := p.HttpClient.Do(request)

	if err != nil {
		return "", err
	}

	defer response.Body.Close()

	bodyStr, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return "", err
	}

	responseBodyString := string(bodyStr)
	return responseBodyString, err
}

func (p *BitgetClient) DoGet(uri string, params map[string]string) (string, error) {
	timesStamp := TimesStampNow()
	body := BuildGetParams(params)
	//fmt.Println(body)

	sign := p.Signer.Sign(GET, uri, body, timesStamp)

	requestUrl := p.BaseUrl + uri + body

	request, err := http.NewRequest(GET, requestUrl, nil)
	if err != nil {
		return "", err
	}
	Headers(request, p.ApiKey, timesStamp, sign, p.Passphrase)

	response, err := p.HttpClient.Do(request)

	if err != nil {
		return "", err
	}

	defer response.Body.Close()

	bodyStr, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return "", err
	}

	responseBodyString := string(bodyStr)
	return responseBodyString, err
}

type BgSigner struct {
	secretKey []byte
}

func (p *BgSigner) Init(key string) *BgSigner {
	p.secretKey = []byte(key)
	return p
}

func (p *BgSigner) Sign(method string, requestPath string, body string, timesStamp string) string {
	var payload strings.Builder
	payload.WriteString(timesStamp)
	payload.WriteString(method)
	payload.WriteString(requestPath)
	if body != "" && body != "?" {
		payload.WriteString(body)
	}
	hash := hmac.New(sha256.New, p.secretKey)
	hash.Write([]byte(payload.String()))
	result := base64.StdEncoding.EncodeToString(hash.Sum(nil))
	return result
}

func (p *BgSigner) SignByRSA(method string, requestPath string, body string, timesStamp string) string {
	var payload strings.Builder
	payload.WriteString(timesStamp)
	payload.WriteString(method)
	payload.WriteString(requestPath)
	if body != "" && body != "?" {
		payload.WriteString(body)
	}

	sign, _ := RSASign([]byte(payload.String()), p.secretKey, crypto.SHA256)
	result := base64.StdEncoding.EncodeToString(sign)
	return result
}

func RSASign(src []byte, priKey []byte, hash crypto.Hash) ([]byte, error) {
	block, _ := pem.Decode(priKey)
	if block == nil {
		return nil, errors.New("key is invalid format")
	}

	var pkixPrivateKey interface{}
	var err error
	if block.Type == "RSA PRIVATE KEY" {
		pkixPrivateKey, err = x509.ParsePKCS1PrivateKey(block.Bytes)
	} else if block.Type == "PRIVATE KEY" {
		pkixPrivateKey, err = x509.ParsePKCS8PrivateKey(block.Bytes)
	}

	h := hash.New()
	_, err = h.Write(src)
	if err != nil {
		return nil, err
	}

	bytes := h.Sum(nil)
	sign, err := rsa.SignPKCS1v15(rand.Reader, pkixPrivateKey.(*rsa.PrivateKey), hash, bytes)
	if err != nil {
		return nil, err
	}

	return sign, nil
}

type BitgetSpotApi struct {
	APIKey       string
	APISecretKey string
	Passphrase   string
	BaseUrl      string
	IsTestnet    bool
	IsSubAccount bool
	UserID       int
	BitgetClient *BitgetClient
}

func NewBitgetApi(apiKey, apiSecretKey string, isTestnet bool) (*BitgetSpotApi, error) {
	baseUrl := "https://api.bitget.com"
	if isTestnet {
		baseUrl = "https://api.bitget.com"
	}
	apiSecretKey = strings.TrimSpace(apiSecretKey)
	parts := strings.Split(apiSecretKey, "|||")
	apiSecret := ""
	passphrase := ""
	userID := 0
	isSubAccount := false
	var err error
	if len(parts) == 4 {
		apiSecret = parts[0]
		passphrase = parts[1]
		userID, err = strconv.Atoi(parts[2])
		if err != nil {
			return nil, fmt.Errorf("invalid user id(int) value: %s", parts[2])
		}
		isSubAccount, err = strconv.ParseBool(parts[3])
		if err != nil {
			return nil, fmt.Errorf("invalid is sub account(bool) value: %s", parts[3])
		}
	} else {
		return nil, fmt.Errorf("invalid api secret format, need 4 parts: %s", apiSecretKey)
	}

	return &BitgetSpotApi{
		BitgetClient: NewBitgetRestClient(apiKey, apiSecret, passphrase, baseUrl),
		APIKey:       apiKey,
		APISecretKey: apiSecret,
		Passphrase:   passphrase,
		BaseUrl:      baseUrl,
		IsTestnet:    isTestnet,
		UserID:       userID,
		IsSubAccount: isSubAccount,
	}, nil
}

func parseResponse(resp string) (respResult gjson.Result, er error) {
	// {
	// 	"code": "00000",
	// 	"msg": "success",
	// 	"requestTime": *************,
	// 	"data": {}
	// }
	respResult = gjson.Parse(resp)
	if respResult.Get("code").String() != "00000" {
		return respResult, fmt.Errorf("%s: %s", respResult.Get("code").String(), respResult.Get("msg").String())
	}
	result := respResult.Get("data")
	return result, nil
}

func (p *BitgetSpotApi) GetAccountAssets(params map[string]string) (gjson.Result, error) {
	resp, err := p.BitgetClient.DoGet("/api/v2/spot/account/assets", params)
	if err != nil {
		return gjson.Result{}, err
	}
	return parseResponse(resp)
}

func (p *BitgetSpotApi) GetSubaccountAssets(params map[string]string) (gjson.Result, error) {
	resp, err := p.BitgetClient.DoGet("/api/v2/spot/account/subaccount-assets", params)
	if err != nil {
		return gjson.Result{}, err
	}
	return parseResponse(resp)
}

func (p *BitgetSpotApi) Transfer(params map[string]string) (gjson.Result, error) {
	postBody, jsonErr := ToJson(params)
	if jsonErr != nil {
		return gjson.Result{}, jsonErr
	}
	resp, err := p.BitgetClient.DoPost("/api/v2/spot/wallet/transfer", postBody)
	if err != nil {
		return gjson.Result{}, err
	}
	return parseResponse(resp)
}

func (p *BitgetSpotApi) SubaccountTransfer(params map[string]string) (gjson.Result, error) {
	postBody, jsonErr := ToJson(params)
	if jsonErr != nil {
		return gjson.Result{}, jsonErr
	}
	resp, err := p.BitgetClient.DoPost("/api/v2/spot/wallet/subaccount-transfer", postBody)
	if err != nil {
		return gjson.Result{}, err
	}
	return parseResponse(resp)
}

func (p *BitgetSpotApi) DepositAddress(params map[string]string) (gjson.Result, error) {
	resp, err := p.BitgetClient.DoGet("/api/v2/spot/wallet/deposit-address", params)
	if err != nil {
		return gjson.Result{}, err
	}
	return parseResponse(resp)
}

func (p *BitgetSpotApi) Withdrawal(params map[string]string) (gjson.Result, error) {
	postBody, jsonErr := ToJson(params)
	if jsonErr != nil {
		return gjson.Result{}, jsonErr
	}
	resp, err := p.BitgetClient.DoPost("/api/v2/spot/wallet/withdrawal", postBody)
	if err != nil {
		return gjson.Result{}, err
	}
	return parseResponse(resp)
}

func (p *BitgetSpotApi) WithdrawalRecords(params map[string]string) (gjson.Result, error) {
	resp, err := p.BitgetClient.DoGet("/api/v2/spot/wallet/withdrawal-records", params)
	if err != nil {
		return gjson.Result{}, err
	}
	return parseResponse(resp)
}

func (p *BitgetSpotApi) DepositRecords(params map[string]string) (gjson.Result, error) {
	resp, err := p.BitgetClient.DoGet("/api/v2/spot/wallet/deposit-records", params)
	if err != nil {
		return gjson.Result{}, err
	}
	return parseResponse(resp)
}

func (p *BitgetSpotApi) GetCoinInfo(params map[string]string) (gjson.Result, error) {
	resp, err := p.BitgetClient.DoGet("/api/v2/spot/public/coins", params)
	if err != nil {
		return gjson.Result{}, err
	}
	return parseResponse(resp)
}

package hopper

import (
	"chaintools/core"
	"chaintools/utils/ulog"
	"fmt"
	"math/big"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"

	"gopkg.in/yaml.v2"

	"github.com/mitchellh/go-homedir"
)

const GAS_FEE_MARGIN_RATIO = 1.1

type HopperType string

const DepositHopper HopperType = "deposit"
const WithdrawHopper HopperType = "withdraw"

type Amount struct {
	Coin       string  `yaml:"coin"`
	Amount     float64 `yaml:"amount"`
	Randomnize bool    `yaml:"randomnize"`
	Rounding   int     `yaml:"rounding"`
}

func (this *Amount) String() string {
	if this == nil {
		return ""
	}
	return fmt.Sprintf("%f %s, %v, ~%d", this.Amount, this.Coin, this.Randomnize, this.Rounding)
}

type Hopper struct {
	Type                      HopperType        `yaml:"type"`
	Name                      string            `yaml:"name"`
	Count                     int               `yaml:"count"`
	Hops                      int               `yaml:"hops"`
	ChainName                 string            `yaml:"chain"`
	Chain                     *Chain            `yaml:"-"`
	CoinAmount                float64           `yaml:"coin_amount"`  // coin amount to hop
	TokenAmount               *Amount           `yaml:"token_amount"` // Amount to hop, if amount not fully showup, don't start hop
	Wait                      int               `yaml:"wait"`         // wait seconds between each address
	TokenAmountPlan           []float64         `yaml:"token_amount_plan,flow"`
	SourceWallet              *MnemonicWallet   `yaml:"source_wallet"`
	TargetWallet              *MnemonicWallet   `yaml:"target_wallet"`
	HopWallets                []*MnemonicWallet `yaml:"hop_wallets"`
	Txs                       Txs               `yaml:"txs"`
	EstimatedTotalFeeMin      float64           `yaml:"estimated_total_fee_min"`      // desposit 预估来回总费用
	EstimatedTokenTransferFee float64           `yaml:"estimated_token_transfer_fee"` // 单笔代币交易预估费用
	EstimatedTransferFee      float64           `yaml:"estimated_transfer_fee"`       // 单笔交易预估费用
	TransferGasOnly           bool              `yaml:"transfer_gas_only"`            // 只转 gas，不清空余额
	WithdrawTargetAddresses   Addresses         `yaml:"withdraw_target_addresses"`    // 替代 withdraw 的目标地址列表
	DepositExchange           *ExchangeAPI      `yaml:"deposit_exchange"`             // deposit 时使用的 exchange api
}

func NewHopper(name string, chain string, hopperType HopperType, hops int, count int, wait int, amount *Amount) *Hopper {
	hopper := &Hopper{
		Name:        name,
		ChainName:   chain,
		Chain:       NewChain(chain),
		Type:        hopperType,
		Hops:        hops,
		Count:       count,
		Wait:        wait,
		TokenAmount: amount,
	}
	hopper.CreateWallets(hopper.Count)
	hopper.calculateAmountPlan()
	return hopper
}

func NewAmount(coin string, amount float64, randomnize bool, rounding int) *Amount {
	return &Amount{
		Coin:       coin,
		Amount:     amount,
		Randomnize: randomnize,
		Rounding:   rounding,
	}
}

func (this *Hopper) Save() {
	if this.Name == "" {
		ulog.Errorf("save hopper failed, error: name is required")
		return
	}
	mainWallet := this.GetMainWallet()
	mainWallet.UpdateCleanAddressString()
	yamlData, err := yaml.Marshal(this)
	if err != nil {
		fmt.Printf("Error: %v", err)
		os.Exit(-1)
	}

	dir, _ := homedir.Expand("~/hopper")
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		os.Mkdir(dir, 0755)
	}
	typ := "deposit_hopper"
	if this.Type == WithdrawHopper {
		typ = "withdraw_hopper"
	}
	dataPath := path.Join(dir, fmt.Sprintf("%s.%s.yaml", this.Name, typ))
	err = os.WriteFile(dataPath, yamlData, 0644)
	if err != nil {
		fmt.Printf("Error: %v", err)
		os.Exit(-1)
	}
	// ulog.Debugf("data written to file successfully: %s", dataPath)
}

func NewHopperFromFile(dataPath string) *Hopper {
	yamlData, err := os.ReadFile(dataPath)
	if err != nil {
		fmt.Printf("Error: %v", err)
		os.Exit(-1)
	}
	hopper := &Hopper{}
	err = yaml.Unmarshal(yamlData, hopper)
	if err != nil {
		fmt.Printf("Error: %v", err)
		os.Exit(-1)
	}

	hopper.Chain = NewChain(hopper.ChainName)

	// ulog.Debugf("data loaded from file successfully: %s", dataPath)
	if len(hopper.TokenAmountPlan) == 0 {
		hopper.calculateAmountPlan()
	}
	// migrate non-indexed private keys to indexed private keys
	wallets := hopper.GetWallets()
	migrated := false
	for _, wallet := range wallets {
		for i, p := range wallet.PrivateKeys {
			if !strings.Contains(p.String(), ":") {
				wallet.PrivateKeys[i] = NewIndexedPrivateKey(i+1, p.String())
				migrated = true
			}
		}
	}

	if migrated {
		hopper.Save()
	}
	if len(hopper.Txs) == 0 {
		hopper.GenerateTxs(true)
		hopper.Save()
	}
	return hopper
}

func (this *Hopper) GetWallets() []*MnemonicWallet {
	wallets := []*MnemonicWallet{}
	wallets = append(wallets, this.SourceWallet)
	wallets = append(wallets, this.HopWallets...)
	wallets = append(wallets, this.TargetWallet)
	return wallets
}

func (this *Hopper) FormatWallets() string {
	result := ""
	result += fmt.Sprintf("Source Wallet:\n%s\n", this.SourceWallet.String())
	for i, wallet := range this.HopWallets {
		result += fmt.Sprintf("Hop Wallet %d:\n%s\n", i, wallet.String())
	}
	result += fmt.Sprintf("Target Wallet:\n%s\n", this.TargetWallet.String())
	return result
}

func (this *Hopper) CreateWallets(count int) {
	if this.Type == DepositHopper {
		this.SourceWallet, _ = this.NewMnenonicWallet(1)
	} else {
		this.SourceWallet, _ = this.NewMnenonicWallet(count)
	}
	this.SourceWallet.Name = "src"
	for i := 0; i < this.Hops; i++ {
		wallet, _ := this.NewMnenonicWallet(count)
		wallet.Name = fmt.Sprintf("hop%d", i)
		this.HopWallets = append(this.HopWallets, wallet)
	}
	if this.Type == DepositHopper {
		this.TargetWallet, _ = this.NewMnenonicWallet(count)
	} else {
		this.TargetWallet, _ = this.NewMnenonicWallet(1)
	}
	this.TargetWallet.Name = "tgt"
}

func (this *Hopper) GetTokenCoin() string {
	if this.TokenAmount != nil {
		return this.TokenAmount.Coin
	}
	return ""
}

func (this *Hopper) EstimateGasFee() {
	ethClient, err := core.NewEthClient(this.Chain.Endpoint, this.SourceWallet.PrivateKeys[0].PrivateKey())
	if err != nil {
		ulog.Errorf("failed to create eth client: %v", err)
		return
	}

	if this.Chain.FixedGasPrice > 0 {
		ethClient.SetFixedGasPrice(this.Chain.FixedGasPrice)
	}

	tokenTransferFee := big.NewInt(0)

	tokenCoin := this.GetTokenCoin()
	if tokenCoin != "" {
		tokenAddress := this.Chain.GetTokenAddress(this.GetTokenCoin())
		if tokenAddress == "" {
			ulog.Errorf("failed to get token address: %s", this.GetTokenCoin())
			return
		}

		// 只能用 0 去估算，因为底层会检查是否有足够的余额
		tokenTransferFee, err = ethClient.EstimateTransferFee(big.NewInt(0), this.TargetWallet.Addresses[0].Address(), tokenAddress)
		if err != nil {
			ulog.Errorf("failed to estimate transfer fee: %v", err)
			return
		}
	}

	gasTransferFee, err := ethClient.EstimateTransferFee(big.NewInt(0), this.TargetWallet.Addresses[0].Address(), "")
	if err != nil {
		ulog.Errorf("failed to estimate gas fee: %v", err)
		return
	}

	this.EstimatedTokenTransferFee = float64(tokenTransferFee.Int64()) / 1e18 * GAS_FEE_MARGIN_RATIO // 浮动 100%
	this.EstimatedTransferFee = float64(gasTransferFee.Int64()) / 1e18 * GAS_FEE_MARGIN_RATIO        // 浮动 100%
	ulog.Debugf("Estimat token transfer fee: %.8f", this.EstimatedTokenTransferFee)
	ulog.Debugf("Estimat gas transfer fee: %.8f", this.EstimatedTransferFee)

	// 单程总费用 = (代币转账费用 + gas转账费用) * count * hops
	fee := (this.EstimatedTokenTransferFee + this.EstimatedTransferFee) * float64(this.getActualCount()) * float64(this.Hops+1)

	if this.Type == DepositHopper {
		this.EstimatedTotalFeeMin = fee * 2 // 来回费用，浮动 20%
	} else {
		this.EstimatedTotalFeeMin = fee
	}

	ulog.Debugf("Estimat total fee min: %.8f", this.EstimatedTotalFeeMin)
}

func (this *Hopper) GenerateTxs(clean bool) (txList []string) {
	// 如果已经执行过了，不再生成
	if this.Txs.isExecuted() {
		ulog.Errorf("can't generate txs for executed txs")
		return nil
	}
	if this.Type == DepositHopper {
		if !clean && this.TokenAmount != nil {
			txList = append(txList, fmt.Sprintf("# please deposit %f %s to %s", this.TokenAmount.Amount, this.TokenAmount.Coin, this.SourceWallet.Addresses[0]))
		}
	}
	txs := []*Tx{}

	// 生成交易
	// 在 getHopTxs 中处理了不同地址数量的情况，这里只需要把所有钱包串起来去生成交易即可
	wallets := this.GetWallets()

	counter := 0
	for i := 0; i < len(wallets)-1; i++ {
		hopTxs := this.getHopTxs(wallets[i], wallets[i+1], this.GetTokenCoin())

		// deposit sourcewallet 最后一笔交易的金额设为 0，尽量都转走
		if this.Type == DepositHopper && i == 0 && hopTxs[len(hopTxs)-1].Coin == this.Chain.Coin && this.DepositExchange == nil {
			ulog.Debugf("sourcewallet set last tx amount to 0")
			hopTxs[len(hopTxs)-1].Amount = 0
		}

		// 如果是第一个或者最后一个钱包，等待时间稍长
		if (this.Type == DepositHopper && i == len(wallets)-2) || (this.Type == WithdrawHopper && i == 0) {
			for _, t := range hopTxs {
				t.Wait = int(randomNumber(float64(this.Wait), 0.2))
			}
		}
		// 新生成的交易状态默认为未知，需要拷贝之前的状态到新的交易对象上
		for _, ht := range hopTxs {
			this.copyCurrentTxData(ht)
		}
		for j, t := range hopTxs {
			counter += 1
			t.ID = fmt.Sprintf("TX%03d-H%01d-%03d", counter, i+1, j+1)
		}
		txs = append(txs, hopTxs...)
	}
	this.Txs = txs
	this.Save()
	return
}

func (this *Hopper) copyCurrentTxData(t *Tx) {
	for _, tx := range this.Txs {
		if tx.FromAddress == t.FromAddress && tx.ToAddress == t.ToAddress && tx.Coin == t.Coin {
			t.Status = tx.Status
			t.TxHash = tx.TxHash
		}
	}
}

func (this *Hopper) getExcludedAddresses() []IndexedAddress {
	addresses := []IndexedAddress{}
	wallet := this.GetMainWallet()
	for _, address := range wallet.ExcludeAddresses {
		addresses = append(addresses, address)
	}
	return addresses
}

// 获取主要钱包，deposit 的话是目标钱包，withdraw 的话是来源钱包
func (this *Hopper) GetMainWallet() *MnemonicWallet {
	if this.Type == DepositHopper {
		return this.TargetWallet
	}
	return this.SourceWallet
}

func (this *Hopper) isAddressExcludedAtIndex(i int) bool {
	excludedAddresses := this.getExcludedAddresses()
	for _, address := range excludedAddresses {
		if address.Index() == i+1 {
			return true
		}
	}
	return false
}

// 保留小数位后非 0 的 n 位数
func preserveNonZeroDecimalDigits(f float64, n int) float64 {
	s := strconv.FormatFloat(f, 'f', -1, 64)
	pointIndex := strings.Index(s, ".")

	if pointIndex == -1 || pointIndex == len(s)-1 {
		return f
	}

	decimals := s[pointIndex+1:]

	firstNonZero := -1
	for i, ch := range decimals {
		if ch != '0' {
			firstNonZero = i
			break
		}
	}

	if firstNonZero == -1 {
		integerPart, _ := strconv.ParseFloat(s[:pointIndex], 64)
		return integerPart
	}

	endIndex := firstNonZero + n
	if endIndex > len(decimals) {
		endIndex = len(decimals)
	}

	result := s[:pointIndex] + "." + decimals[:endIndex]
	finalResult, err := strconv.ParseFloat(result, 64)
	if err != nil {
		ulog.Errorf("failed to parse float: %v", err)
		return 0
	}
	return finalResult
}

func (this *Hopper) getHopTxs(fromWallet *MnemonicWallet, toWallet *MnemonicWallet, coin string) []*Tx {
	txs := []*Tx{}

	if this.Type == DepositHopper && fromWallet.IsSame(this.SourceWallet) {
		// deposit src 只有一个地址
		totalCoinAmount := this.CoinAmount
		if this.CoinAmount == 0 {
			totalCoinAmount = this.EstimatedTotalFeeMin
			if this.DepositExchange == nil {
				addrBalanceBig, err := this.Chain._getTokenBalance(fromWallet.PrivateKeys[0].PrivateKey(), fromWallet.Addresses[0].Address(), this.Chain.Coin)
				if err != nil {
					ulog.Errorf("failed to get balance: %v", err)
				} else {
					addrBalance := BigIntToFloat64(addrBalanceBig, 18)
					if addrBalance > totalCoinAmount {
						// 如果余额大于预估费用，直接用余额
						totalCoinAmount = addrBalance
					}
				}
			}
		}

		amount := totalCoinAmount/float64(this.getActualCount()) - this.EstimatedTokenTransferFee - this.EstimatedTransferFee
		amount = preserveNonZeroDecimalDigits(amount, 2) // 避免都是相同的过长小数引起注意

		for i := 0; i < len(toWallet.Addresses); i++ {
			// 跳过排除的地址
			if this.isAddressExcludedAtIndex(i) {
				continue
			}

			key := fromWallet.PrivateKeys[0].PrivateKey()
			fromAddress := fromWallet.Addresses[0].Address()
			if this.DepositExchange != nil {
				fromAddress = string(this.DepositExchange.ExchangeName)
				key = fmt.Sprintf("%s/%s", this.DepositExchange.APIKey, this.DepositExchange.APISecret)
			}

			if coin != "" {
				txs = append(txs, &Tx{
					PrivateKey:  key,
					FromAddress: fromAddress,
					ToAddress:   toWallet.Addresses[i].Address(),
					Coin:        coin,
					Amount:      this.TokenAmountPlan[i],
				})
			}

			txs = append(txs, &Tx{
				PrivateKey:  key,
				FromAddress: fromAddress,
				ToAddress:   toWallet.Addresses[i].Address(),
				Coin:        this.Chain.Coin,
				Amount:      amount,
			})
		}
		return txs
	} else if this.Type == WithdrawHopper && toWallet.IsSame(this.TargetWallet) {
		// withdraw 最后一个是 tgt 只有一个地址
		for i := 0; i < len(fromWallet.Addresses); i++ {
			addressIndex := fromWallet.Addresses[i].Index()
			// 跳过排除的地址
			if this.isAddressExcludedAtIndex(i) {
				continue
			}

			toAddress := toWallet.Addresses[0].Address()

			// 如果有多个 withdraw 地址，根据 index 选择
			// 如果只有一个地址，直接用这个地址
			if len(this.WithdrawTargetAddresses) == 1 {
				toAddress = this.WithdrawTargetAddresses[0].Address()
			} else if len(this.WithdrawTargetAddresses) > 1 {
				toAddress = this.WithdrawTargetAddresses.FindByIndex(addressIndex).Address()
			}
			if toAddress == "" {
				ulog.Errorf("failed to get withdraw target address")
				return nil
			}

			if coin != "" {
				txs = append(txs, &Tx{
					PrivateKey:  fromWallet.PrivateKeys[i].PrivateKey(),
					FromAddress: fromWallet.Addresses[i].Address(),
					ToAddress:   toAddress,
					Coin:        coin,
					Amount:      this.TokenAmountPlan[i],
				})
			}

			amount := 0.0
			if this.TransferGasOnly && fromWallet.IsSame(this.SourceWallet) {
				// 如果是 withdraw 的 src，设定了只转 gas，则不清空余额
				amount = (this.EstimatedTokenTransferFee + this.EstimatedTransferFee) * float64(this.Hops)
				if this.Hops == 0 {
					continue // 无 Hops，无需转 gas
				}
			}

			txs = append(txs, &Tx{
				PrivateKey:  fromWallet.PrivateKeys[i].PrivateKey(),
				FromAddress: fromWallet.Addresses[i].Address(),
				ToAddress:   toAddress,
				Coin:        this.Chain.Coin,
				Amount:      amount,
			})
		}
		return txs
	} else if len(fromWallet.Addresses) == len(toWallet.Addresses) {
		for i := 0; i < len(fromWallet.Addresses); i++ {
			// 跳过排除的地址
			if this.isAddressExcludedAtIndex(i) {
				continue
			}
			if coin != "" {
				txs = append(txs, &Tx{
					PrivateKey:  fromWallet.PrivateKeys[i].PrivateKey(),
					FromAddress: fromWallet.Addresses[i].Address(),
					ToAddress:   toWallet.Addresses[i].Address(),
					Coin:        coin,
					Amount:      this.TokenAmountPlan[i],
				})
			}
			amount := 0.0
			if this.Type == WithdrawHopper && this.TransferGasOnly && fromWallet.IsSame(this.SourceWallet) {
				// 如果是 withdraw 的 src，设定了只转 gas，则不清空余额
				amount = (this.EstimatedTokenTransferFee + this.EstimatedTransferFee) * float64(this.Hops)
			}

			txs = append(txs, &Tx{
				PrivateKey:  fromWallet.PrivateKeys[i].PrivateKey(),
				FromAddress: fromWallet.Addresses[i].Address(),
				ToAddress:   toWallet.Addresses[i].Address(),
				Coin:        this.Chain.Coin,
				Amount:      amount,
			})
		}
		return txs
	} else {
		panic("Invalid wallet addresses, from and to wallet addresses should be the same length, or only one can be 1 length.")
	}

}

func (this *Hopper) Format() string {
	result := ""
	result += fmt.Sprintf("Name: %s\n", this.Name)
	result += fmt.Sprintf("Type: %s\n", this.Type)
	result += fmt.Sprintf("Chain: %s\n", this.Chain)
	result += fmt.Sprintf("Count: %d\n", this.Count)
	result += fmt.Sprintf("Hops: %d\n", this.Hops)
	result += fmt.Sprintf("Amount: %s\n", this.TokenAmount.String())
	result += fmt.Sprintf("TransferGasOnly: %v\n", this.TransferGasOnly)
	result += fmt.Sprintf("Wait: %d\n", this.Wait)
	result += fmt.Sprintf("AmountPlan: \n%v\n", this.TokenAmountPlan)
	result += fmt.Sprintf("Source Wallet:\n%s\n", this.SourceWallet.String())
	result += fmt.Sprintf("Target Wallet:\n%s\n", this.TargetWallet.String())
	for i, wallet := range this.HopWallets {
		result += fmt.Sprintf("Hop Wallet%d:\n%s\n", i, wallet.String())
	}
	result += fmt.Sprintf("Txs:\n%s\n\n", strings.Join(this.RedactedTxs(), "\n"))
	result += fmt.Sprintf("Estimated Gas Fee Min: %.8f\n", this.EstimatedTotalFeeMin)
	return result
}

func (this *Hopper) RedactedTxs() []string {
	redactedTxs := []string{}
	for _, tx := range this.Txs {
		redactedTxs = append(redactedTxs, tx.RedactedString())
	}
	return redactedTxs
}

func (this *Hopper) getActualCount() int {
	count := this.Count
	excludedAddresses := this.getExcludedAddresses()
	excludeCount := len(excludedAddresses)
	count -= excludeCount
	return count
}

func (this *Hopper) calculateAmountPlan() []float64 {
	if this.Txs.isExecuted() {
		return nil
	}

	if this.TokenAmount == nil {
		return nil
	}

	// 计算数量计划，排除的地址不计算在内
	count := this.getActualCount()
	amounts := make([]float64, this.Count)

	if this.Type == DepositHopper {
		avgAmount := this.TokenAmount.Amount / float64(count)
		cummulatedAmount := 0.0
		for i := 0; i < this.Count; i++ {
			if this.isAddressExcludedAtIndex(i) {
				amounts[i] = -1
				continue
			}
			if this.TokenAmount.Randomnize {
				amount := randomNumber(avgAmount, 0.1)
				if this.TokenAmount.Rounding >= 0 {
					amount = roundToNthPlace(amount, this.TokenAmount.Rounding)
				}
				// 检查是否超出总额
				left := this.TokenAmount.Amount - cummulatedAmount
				if left > amount {
					cummulatedAmount += amount
					amounts[i] = amount
				} else {
					cummulatedAmount = left
					amounts[i] = left
				}
			} else {
				cummulatedAmount += avgAmount
				amounts[i] = avgAmount
			}
		}
	} else {
		for i := 0; i < this.Count; i++ {
			if this.isAddressExcludedAtIndex(i) {
				amounts[i] = -1
				continue
			}
			if this.TokenAmount.Amount <= 0 { // 未指定提现金额，直接把所有钱包的余额都提出来
				amounts[i] = 0
			} else {
				amounts[i] = this.TokenAmount.Amount
			}
		}
	}
	this.TokenAmountPlan = amounts
	return amounts
}

func GetHoppers() (hoppers []*Hopper) {
	dir, _ := homedir.Expand("~/hopper")
	hoppers = []*Hopper{}

	files, err := os.ReadDir(dir)
	if err != nil {
		ulog.Errorf("failed to load hoppers: %v\n", err)
		return
	}

	for _, file := range files {
		if strings.HasSuffix(file.Name(), "_hopper.yaml") {
			// ulog.Debugf("Loading hopper from file: %s\n", file.Name())
			hopper := NewHopperFromFile(path.Join(dir, file.Name()))
			if len(hopper.Txs) == 0 {
				hopper.GenerateTxs(true)
			}
			if hopper == nil {
				ulog.Errorf("Failed to load hopper from file: %s\n", file.Name())
				return
			}
			hoppers = append(hoppers, hopper)
		}
	}

	return
}

func GetHopperByName(name string) *Hopper {
	hoppers := GetHoppers()
	for _, h := range hoppers {
		if h.Name == name {
			return h
		}
	}
	return nil
}

func RemoveHopperByName(name string) {
	dir, _ := homedir.Expand("~/hopper")
	err := filepath.Walk(dir, func(path string, f os.FileInfo, err error) error {
		if strings.HasSuffix(path, "_hopper.yaml") {
			hopper := NewHopperFromFile(path)
			if hopper.Name == name {
				os.Remove(path)
			}
		}
		return nil
	})

	if err != nil {
		ulog.Errorf("failed to load hoppers: %v\n", err)
		return
	}
}

func (this *Hopper) ExcludeAddress(address string) bool {
	if this.Txs.isExecuted() {
		ulog.Errorf("can't exclude address if the txs are executed")
		return false
	}
	var indexedAddress IndexedAddress
	if this.Type == DepositHopper {
		indexedAddress = this.TargetWallet.FindIndexedAddress(address)
		if indexedAddress == "" {
			return false
		}
		this.TargetWallet.ExcludeAddresses = append(this.TargetWallet.ExcludeAddresses, indexedAddress)
	} else {
		indexedAddress = this.SourceWallet.FindIndexedAddress(address)
		if indexedAddress == "" {
			return false
		}
		this.SourceWallet.ExcludeAddresses = append(this.SourceWallet.ExcludeAddresses, indexedAddress)
	}

	this.calculateAmountPlan()
	this.GenerateTxs(true)
	return true
}

func (this *Hopper) RevertExcludeAddress(address string) bool {
	if this.Txs.isExecuted() {
		ulog.Errorf("can't exclude address if the txs are executed")
		return false
	}
	var indexedAddress IndexedAddress
	if this.Type == DepositHopper {
		indexedAddress = this.TargetWallet.FindIndexedAddress(address)
		for i, addr := range this.TargetWallet.ExcludeAddresses {
			if addr.Address() == address {
				this.TargetWallet.ExcludeAddresses = append(this.TargetWallet.ExcludeAddresses[:i], this.TargetWallet.ExcludeAddresses[i+1:]...)
				break
			}
		}
	} else {
		indexedAddress = this.SourceWallet.FindIndexedAddress(address)
		for i, addr := range this.SourceWallet.ExcludeAddresses {
			if addr.Address() == address {
				this.SourceWallet.ExcludeAddresses = append(this.SourceWallet.ExcludeAddresses[:i], this.SourceWallet.ExcludeAddresses[i+1:]...)
				break
			}
		}
	}

	this.calculateAmountPlan()
	this.GenerateTxs(true)

	return string(indexedAddress) != ""
}

func (this *Hopper) ChangeWallet(newWallet *MnemonicWallet) {
	if this.Txs.isExecuted() {
		ulog.Errorf("can't exclude address if the txs are executed")
		return
	}
	if this.Type == DepositHopper {
		this.TargetWallet = newWallet
	} else {
		this.SourceWallet = newWallet
	}
	this.GenerateTxs(true)
	this.Save()
}

func (this *Hopper) ChangeWithdrawTargetAddresses(addresses Addresses) {
	if this.Txs.isExecuted() {
		ulog.Errorf("can't change withdraw target addresses if the txs are executed")
		return
	}
	this.WithdrawTargetAddresses = addresses
	this.GenerateTxs(true)
	this.Save()
}

func (this *Hopper) UseExchangeAPI(exchange *ExchangeAPI) {
	if this.Txs.isExecuted() {
		ulog.Errorf("can't use exchange api if the txs are executed")
		return
	}
	this.DepositExchange = exchange
	this.GenerateTxs(true)
	this.Save()
}

func (this *Hopper) SendTxs() {
	defer this.Save()

	this.Txs.Send(this.Chain, func() {
		this.Save()
	})
}

// 复制一个新的 Hopper，其实只是复用原来 hopper 的钱包地址，其他都需要重新配置；并且过去的 Txs 都会清空
// 如果地址数量不同，会按新的地址数量生成
// 如果跳数不同，会尽可能复用之前 hopper 的临时钱包，不够的情况下，会生成新钱包
// 典型的用途就是用于测试：比如测试一个 desposit 的 hopper 之后，需要把资金收回，那么就可以复制一个 withdraw 的 hopper
func (this *Hopper) Clone(name string, chain string, hopperType HopperType, hops int, count int, wait int, amount *Amount) *Hopper {
	hopper := &Hopper{
		Name:         name,
		ChainName:    chain,
		Chain:        NewChain(chain),
		Type:         hopperType,
		Hops:         hops,
		Count:        count,
		Wait:         wait,
		TokenAmount:  amount,
		SourceWallet: &MnemonicWallet{},
		TargetWallet: &MnemonicWallet{},
	}
	// 如果是同类型，直接复制；如果不是，source 和 target 需要调换
	sourceCount := 1
	targetCount := count
	if hopperType == WithdrawHopper {
		sourceCount = count
		targetCount = 1

		if count == len(this.WithdrawTargetAddresses) {
			hopper.WithdrawTargetAddresses = this.WithdrawTargetAddresses
		}
	}
	if hopperType == this.Type {
		var err error
		hopper.SourceWallet, err = NewWalletFromMnemonic(this.SourceWallet.Mnemonic, sourceCount)
		if err != nil {
			ulog.Errorf("failed to create source wallet: %v", err)
			return nil
		}
		hopper.TargetWallet, err = NewWalletFromMnemonic(this.TargetWallet.Mnemonic, targetCount)
		if err != nil {
			ulog.Errorf("failed to create target wallet: %v", err)
			return nil
		}
		hopper.SourceWallet.ExcludeAddresses = this.SourceWallet.ExcludeAddresses
		hopper.TargetWallet.ExcludeAddresses = this.TargetWallet.ExcludeAddresses
	} else {
		var err error
		hopper.SourceWallet, err = NewWalletFromMnemonic(this.TargetWallet.Mnemonic, sourceCount)
		if err != nil {
			ulog.Errorf("failed to create target wallet: %v", err)
			return nil
		}
		hopper.TargetWallet, err = NewWalletFromMnemonic(this.SourceWallet.Mnemonic, targetCount)
		if err != nil {
			ulog.Errorf("failed to create source wallet: %v", err)
			return nil
		}
		hopper.SourceWallet.ExcludeAddresses = this.TargetWallet.ExcludeAddresses
		hopper.TargetWallet.ExcludeAddresses = this.SourceWallet.ExcludeAddresses
	}
	hopper.SourceWallet.Name = "src"
	hopper.TargetWallet.Name = "tgt"

	for i := 0; i < hopper.Hops; i++ {
		var wallet *MnemonicWallet
		// 如果新的跳数更多，需要创建新的钱包；但还是复用之前可用的旧钱包
		if i < this.Hops {
			var err error
			wallet, err = NewWalletFromMnemonic(this.HopWallets[i].Mnemonic, count)
			if err != nil {
				ulog.Errorf("failed to create hop wallet: %v", err)
				return nil
			}
		} else {
			wallet, _ = this.NewMnenonicWallet(count)
		}

		wallet.Name = fmt.Sprintf("hop%d", i)
		hopper.HopWallets = append(hopper.HopWallets, wallet)
	}
	hopper.calculateAmountPlan()
	hopper.Save()
	hopper.GenerateTxs(true)
	hopper.Save()
	return hopper
}

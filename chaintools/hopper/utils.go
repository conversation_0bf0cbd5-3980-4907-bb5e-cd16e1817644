package hopper

import (
	"chaintools/utils/ulog"
	"fmt"
	"math"
	"math/big"
	"math/rand"
	"time"

	"github.com/avast/retry-go/v4"
)

// shift < 1 as percentage
func randomNumber(n float64, shiftPercentage float64) float64 {
	p := rand.Float64()*2*shiftPercentage - shiftPercentage // Generate a random percentage between -20% and 20%
	return float64((1.0 + p) * n)
}

func roundToNthPlace(num float64, n int) float64 {
	if n > 0 {
		p := math.Pow10(n) // computes 10^n
		return math.Round(num/p) * p
	} else {
		p := math.Pow10(n) // computes 10^n
		return math.Round(num*p) / p
	}
}

func retryDo(do func() error, maxTry int, sleep int) error {
	return retry.Do(do, retry.Attempts(uint(maxTry)), retry.Delay(time.Second*time.Duration(sleep)), retry.OnRetry(func(n uint, err error) {
		ulog.Errorf("retrying %d, error: %v", n, err)
	}))
}

// BigIntToFloat64 converts a big.Int to a float64 considering a specified number of decimal places
func BigIntToFloat64(bi *big.Int, decimals int) float64 {
	// 创建一个 big.Float 从 big.Int
	bigFloatValue := new(big.Float).SetInt(bi)

	// 计算 10 的 -decimals 次方，作为除数
	divisor := new(big.Float).SetFloat64(1).SetInt(new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(decimals)), nil))

	// 进行除法计算来移动小数点
	result := new(big.Float).Quo(bigFloatValue, divisor)

	// 将 big.Float 转换为 float64
	float64Result, _ := result.Float64()

	return float64Result
}

// 定义 ANSI 颜色代码
const (
	colorRed    = "\033[31m"
	colorGreen  = "\033[32m"
	colorYellow = "\033[33m"
	colorReset  = "\033[0m"
)

func YellowTextf(format string, args ...any) string {
	return colorYellow + fmt.Sprintf(format, args...) + colorReset
}

func GreenTextf(format string, args ...any) string {
	return colorGreen + fmt.Sprintf(format, args...) + colorReset
}

func RedTextf(format string, args ...any) string {
	return colorRed + fmt.Sprintf(format, args...) + colorReset
}

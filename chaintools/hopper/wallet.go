package hopper

import (
	"fmt"
	"slices"
	"strings"

	hdwallet "github.com/miguelmota/go-ethereum-hdwallet"
	"github.com/spf13/cast"
)

type IndexedAddress string

func (this IndexedAddress) String() string {
	return string(this)
}

func (this IndexedAddress) Address() string {
	parts := strings.Split(string(this), ": ")
	return parts[1]
}

func (this IndexedAddress) Index() int {
	parts := strings.Split(string(this), ": ")
	return cast.ToInt(parts[0])
}

func NewIndexedAddress(index int, address string) IndexedAddress {
	return IndexedAddress(fmt.Sprintf("%d: %s", index, address))
}

type Addresses []IndexedAddress

func (this Addresses) FindByIndex(idx int) IndexedAddress {
	for _, address := range this {
		if address.Index() == idx {
			return address
		}
	}
	return ""
}

func (this Addresses) Strings() []string {
	result := []string{}
	for _, address := range this {
		result = append(result, address.String())
	}
	return result
}

func (this Addresses) Indexes() []int {
	indexes := []int{}
	for _, address := range this {
		indexes = append(indexes, address.Index())
	}
	return indexes
}

func (this Addresses) Addresses() []string {
	addresses := []string{}
	for _, address := range this {
		addresses = append(addresses, address.Address())
	}
	return addresses
}

type IndexedPrivateKey string

func NewIndexedPrivateKey(index int, privateKey string) IndexedPrivateKey {
	return IndexedPrivateKey(fmt.Sprintf("%d: %s", index, privateKey))
}

func (this IndexedPrivateKey) String() string {
	return string(this)
}

func (this IndexedPrivateKey) Index() int {
	parts := strings.Split(string(this), ": ")
	return cast.ToInt(parts[0])
}

func (this IndexedPrivateKey) PrivateKey() string {
	parts := strings.Split(string(this), ": ")
	return parts[1]
}

type MnemonicWallet struct {
	Name               string              `yaml:"name"`
	Prefix             string              `yaml:"prefix"`
	Mnemonic           string              `yaml:"mnemonic"`
	Wallet             *hdwallet.Wallet    `yaml:"-"`
	Addresses          Addresses           `yaml:"addresses"`
	PrivateKeys        []IndexedPrivateKey `yaml:"private_keys"`
	ExcludeAddresses   Addresses           `yaml:"exclude_addresses"`
	CleanAddressString string              `yaml:"clean_address_string"` // 暂时仅在保存和使用时更新
}

func (this *MnemonicWallet) GetAddressAlias(address string) string {
	for _, indexedAddress := range this.Addresses {
		if indexedAddress.Address() == address {
			return fmt.Sprintf("%sW%02d", this.Prefix, indexedAddress.Index())
		}
	}
	return ""
}

func (this *MnemonicWallet) PrivateKey(index int) string {
	for _, privateKey := range this.PrivateKeys {
		if privateKey.Index() == index {
			return privateKey.PrivateKey()
		}
	}
	return ""
}
func (this *MnemonicWallet) IsSame(other *MnemonicWallet) bool {
	return this.Mnemonic == other.Mnemonic
}

func (this *MnemonicWallet) FindIndexedAddress(address string) IndexedAddress {
	for _, indexedAddress := range this.Addresses {
		if indexedAddress.Address() == address {
			return indexedAddress
		}
	}
	return ""
}

func (this *MnemonicWallet) IsIndexExcluded(i int) bool {
	return slices.Contains(this.ExcludeAddresses.Indexes(), i+1)
}

func (this *MnemonicWallet) CleanAddresses() []string {
	addresses := []string{}
	for _, addr := range this.Addresses {
		if this.IsIndexExcluded(addr.Index()) {
			continue
		}
		addresses = append(addresses, addr.String())
	}
	return addresses
}

func (this *MnemonicWallet) UpdateCleanAddressString() {
	this.CleanAddressString = strings.Join(this.CleanAddresses(), ",")
}

func (this *MnemonicWallet) String() string {
	result := "\n"
	result += fmt.Sprintf("Name: %s\n", this.Name)
	result += fmt.Sprintf("Mnemonic: %s\n", this.RedactedMnemonic())
	result += fmt.Sprintf("Excluded Addresses: \n%s\n", strings.Join(this.ExcludeAddresses.Strings(), "\n"))
	result += fmt.Sprintf("Addresses: \n%s\n", strings.Join(this.Addresses.Strings(), "\n"))
	this.UpdateCleanAddressString()
	result += fmt.Sprintf("Clean Addresses: \n%s\n", this.CleanAddressString)
	result += fmt.Sprintf("PrivateKeys: \n%v\n", strings.Join(this.RedactedPrivateKeys(), "\n"))
	return result
}

func (this *MnemonicWallet) RedactedMnemonic() string {
	parts := strings.Split(this.Mnemonic, " ")
	return strings.Join(parts[0:3], " ") + "... " + strings.Join(parts[len(parts)-3:], " ")
}

func (this *MnemonicWallet) RedactedPrivateKeys() []string {
	redactedPrivateKeys := []string{}
	for _, privateKey := range this.PrivateKeys {
		if len(privateKey) < 8 {
			redactedPrivateKeys = append(redactedPrivateKeys, privateKey.String())
			continue
		}
		redactedPrivateKeys = append(redactedPrivateKeys, privateKey.String()[0:6]+"..."+privateKey.String()[len(privateKey)-4:])
	}
	return redactedPrivateKeys
}

type WalletSummary struct {
	Name           string   `json:"name"`
	Prefix         string   `json:"prefix"`
	Addresses      []string `json:"addresses"`
	AddressAliases []string `json:"addressAliases"`
	AddressNote    string   `json:"addressNote"`
}

func (this *MnemonicWallet) GetSummary() *WalletSummary {
	aliases := []string{}
	for _, address := range this.Addresses {
		aliases = append(aliases, this.GetAddressAlias(address.Address()))
	}
	return &WalletSummary{
		Name:           this.Name,
		Prefix:         this.Prefix,
		Addresses:      this.Addresses.Addresses(),
		AddressAliases: aliases,
		AddressNote:    this.Note(),
	}
}

func (this *MnemonicWallet) Note() string {
	note := fmt.Sprintf("[wallet: %s]\n\n", this.Name)
	note += "pchrome file:\n---------------------\n"
	for _, address := range this.Addresses {
		note += fmt.Sprintf("%d: %s\n", address.Index(), address.Address())
	}
	note += "\n"
	note += "pchrome one line:\n---------------------\n"
	addressLines := []string{}
	for _, address := range this.Addresses {
		addressLines = append(addressLines, fmt.Sprintf("%d: %s", address.Index(), address.Address()))
	}
	note += strings.Join(addressLines, ",")
	note += "\n\n"
	note += "okx file:\n----------------\n"
	note += "Address,AddressName(optional)\n"
	for _, address := range this.Addresses {
		note += fmt.Sprintf("%s,%sW%02d\n", address.Address(), this.Prefix, address.Index())
	}
	note += "\n"
	note += "addresses:\n-------------\n"
	for _, address := range this.Addresses {
		note += fmt.Sprintf("%s\n", address.Address())
	}
	note += "\n"
	return note
}

func (this *Hopper) NewMnenonicWallet(addressCount int) (nwallet *MnemonicWallet, er error) {
	mnemonic, err := hdwallet.NewMnemonic(256)
	if err != nil {
		er = fmt.Errorf("failed to create new mnemonic: %s", err)
		return
	}
	wallet, err := NewWalletFromMnemonic(mnemonic, addressCount)
	if err != nil {
		er = fmt.Errorf("failed to create new wallet from mnemonic: %s", err)
	}
	nwallet = wallet
	return
}

func NewWalletFromMnemonic(mnemonic string, addressCount int) (nwallet *MnemonicWallet, er error) {
	wallet, _ := hdwallet.NewFromMnemonic(mnemonic)
	nwallet = &MnemonicWallet{
		Mnemonic: mnemonic,
		Wallet:   wallet,
	}

	// Derivation path
	for i := 0; i < addressCount; i++ {
		path := hdwallet.MustParseDerivationPath(fmt.Sprintf("m/44'/60'/0'/0/%d", i))
		account, _ := wallet.Derive(path, true)
		nwallet.Addresses = append(nwallet.Addresses, NewIndexedAddress(i+1, account.Address.Hex()))
		privateKey, err := wallet.PrivateKeyHex(account)
		if err != nil {
			er = fmt.Errorf("failed to get private key: %s", err)
			return
		}
		nwallet.PrivateKeys = append(nwallet.PrivateKeys, NewIndexedPrivateKey(i+1, privateKey))
	}
	return
}

package hopper

import (
	"chaintools/utils/ulog"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	resty "github.com/go-resty/resty/v2"
)

type OKx ExchangeAPI

func NewOKx(apiKey, apiSecret string, isTestnet bool) *OKx {
	return &OKx{
		ExchangeName: OKX,
		APIKey:       apiKey,
		APISecret:    apiSecret,
		IsTestnet:    isTestnet,
	}
}

func (this *OKx) GetExchangeName() ExchangeName {
	return this.ExchangeName
}

func (this *OKx) IsAvailable() (bool, error) {
	err := this.sendHTTPRequest("GET", "/api/v5/asset/balances", nil, nil, true, false)
	if err != nil {
		return false, fmt.Errorf("api is not available: %v", err)
	}
	return true, nil
}

// 获取资金账户余额
func (this *OKx) GetBalance(coin string) (total, available float64, err error) {
	url := fmt.Sprintf("/api/v5/asset/balances?ccy=%s", coin)
	respData := []map[string]any{}
	err = this.sendHTTPRequest("GET", url, nil, &respData, true, true)
	if err != nil {
		return 0, 0, err
	}
	if len(respData) == 0 {
		return 0, 0, fmt.Errorf("no balance found for coin: %s", coin)
	}
	if len(respData) > 1 {
		return 0, 0, fmt.Errorf("multiple balances found for coin: %s", coin)
	}

	totalBalance, ok := respData[0]["bal"].(string)
	if !ok {
		return 0, 0, fmt.Errorf("invalid total balance format for coin: %s", coin)
	}
	total, err = strconv.ParseFloat(totalBalance, 64)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to parse total balance for coin: %s, err: %v", coin, err)
	}
	balance, ok := respData[0]["availBal"].(string)
	if !ok {
		return 0, 0, fmt.Errorf("invalid balance format for coin: %s", coin)
	}
	available, err = strconv.ParseFloat(balance, 64)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to parse balance for coin: %s, err: %v", coin, err)
	}
	return total, available, nil
}

// 获取子账户资金账户余额（适用于母账户）
func (this *OKx) GetSubaccountBalance(subAcct, coin string) (total, available float64, err error) {
	url := fmt.Sprintf("/api/v5/asset/subaccount/balances?ccy=%s&subAcct=%s", coin, subAcct)
	respData := []map[string]any{}
	err = this.sendHTTPRequest("GET", url, nil, &respData, true, true)
	if err != nil {
		return 0, 0, err
	}
	if len(respData) == 0 {
		return 0, 0, fmt.Errorf("no balance found for coin: %s", coin)
	}
	if len(respData) > 1 {
		return 0, 0, fmt.Errorf("multiple balances found for coin: %s", coin)
	}

	totalBalance, ok := respData[0]["bal"].(string)
	if !ok {
		return 0, 0, fmt.Errorf("invalid total balance format for coin: %s", coin)
	}
	total, err = strconv.ParseFloat(totalBalance, 64)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to parse total balance for coin: %s, err: %v", coin, err)
	}
	balance, ok := respData[0]["availBal"].(string)
	if !ok {
		return 0, 0, fmt.Errorf("invalid balance format for coin: %s", coin)
	}
	available, err = strconv.ParseFloat(balance, 64)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to parse balance for coin: %s, err: %v", coin, err)
	}
	return total, available, nil
}

type OKxTransferType string

// 划转类型
// 0：账户内划转
// 1：母账户转子账户(仅适用于母账户APIKey)
// 2：子账户转母账户(仅适用于母账户APIKey)
// 3：子账户转母账户(仅适用于子账户APIKey)
// 4：子账户转子账户(仅适用于子账户APIKey，且目标账户需要是同一母账户下的其他子账户。子账户主动转出权限默认是关闭的，权限调整参考 设置子账户主动转出权限。)
const (
	OKxTransferTypeInternal     OKxTransferType = "0"
	OKxTransferTypeMainToSub    OKxTransferType = "1"
	OKxTransferTypeSubToMain    OKxTransferType = "2"
	OKxTransferTypeSubToMainSub OKxTransferType = "3"
	OKxTransferTypeSubToSub     OKxTransferType = "4"
)

type OKxTransferAccount string

// 划转账号
// 6：资金账户
// 18：交易账户
const (
	OKxTransferAccountFund  OKxTransferAccount = "6"
	OKxTransferAccountTrade OKxTransferAccount = "18"
)

func (this *OKx) AssetTransfer(coin string, from, to OKxTransferAccount, amt float64, transferType OKxTransferType, subAcct string) (string, error) {
	req := map[string]string{
		"type": string(transferType),
		"ccy":  coin,
		"amt":  fmt.Sprintf("%v", amt),
		"from": string(from),
		"to":   string(to),
	}
	if transferType == OKxTransferTypeMainToSub || transferType == OKxTransferTypeSubToMain || transferType == OKxTransferTypeSubToSub {
		// 当type为1/2/4时，subAcct 必填
		req["subAcct"] = subAcct
	}
	respData := []map[string]any{}
	err := this.sendHTTPRequest("POST", "/api/v5/asset/transfer", req, &respData, true, true)
	if err != nil {
		return "", err
	}
	if len(respData) == 0 {
		return "", fmt.Errorf("no transfer id found for coin: %s", coin)
	}
	if len(respData) > 1 {
		return "", fmt.Errorf("multiple transfer ids found for coin: %s", coin)
	}
	if transferId, ok := respData[0]["transId"].(string); ok {
		return transferId, nil
	} else {
		return "", fmt.Errorf("invalid transfer id format for coin: %s", coin)
	}
}

func convertChainToOKxChainSuffix(chainName string) string {
	switch chainName {
	case "arbitrum":
		return "-Arbitrum One"
	case "ethereum":
		return "-ERC20"
	case "solana":
		return "-Solana"
	default:
		return ""
	}
}

func (this *OKx) Withdraw(amt float64, coin, address string, chainName string) (string, error) {
	coinInfos, err := this.GetCoinInfos(coin)
	if err != nil {
		return "", err
	}

	chainSuffix := convertChainToOKxChainSuffix(chainName)
	if chainSuffix == "" {
		return "", fmt.Errorf("unsupported chain: %s", chainName)
	}

	var okChainName string
	var fee string
	for _, coinInfo := range coinInfos {
		if coinInfo.Chain == coin+chainSuffix {
			okChainName = coinInfo.Chain
			fee = coinInfo.MinFee

			minWd, _ := strconv.ParseFloat(coinInfo.MinWd, 64)
			if amt < minWd {
				return "", fmt.Errorf("withdraw amount(%v) is less than min withdraw amount(%v)", amt, minWd)
			}

			break
		}
	}

	req := map[string]string{
		"ccy":    coin,
		"amt":    fmt.Sprintf("%v", amt),
		"dest":   "4", // 3：内部转账 4：链上提币
		"toAddr": address,
		"chain":  okChainName,
		"fee":    fee,
	}

	ulog.Debugf("[%s] withdraw req: %#v", this.ExchangeName, req)

	respData := map[string]string{}
	err = this.sendHTTPRequest("POST", "/api/v5/asset/withdrawal", req, &respData, true, false)
	if err != nil {
		return "", err
	}

	if withdrawID, ok := respData["wdId"]; ok {
		return withdrawID, nil
	} else {
		ulog.Errorf("[%s] withdraw failed, resp: %#v", this.ExchangeName, respData)
		return "", errors.New("can not find withdraw id")
	}
}

// 如果txId已经生成，则返回，否则返回""
func (this *OKx) QueryWithdrawTxId(withdrawID string) (string, error) {
	url := fmt.Sprintf("/api/v5/asset/deposit-withdraw-status?wdId=%s", withdrawID)

	respData := map[string]string{}
	err := this.sendHTTPRequest("GET", url, nil, &respData, true, false)
	if err != nil {
		return "", err
	}

	if txId, ok := respData["txId"]; ok {
		return txId, nil
	} else {
		ulog.Errorf("[%s] query withdraw txId failed, resp: %#v", this.ExchangeName, respData)
		return "", errors.New("can not find withdraw txId")
	}
}

func (this *OKx) GetDepositAddress(coin, chain string) ([]string, error) {
	url := fmt.Sprintf("/api/v5/asset/deposit-address?ccy=%s", coin)
	respData := []map[string]any{}
	err := this.sendHTTPRequest("GET", url, nil, &respData, true, true)
	if err != nil {
		return nil, err
	}
	if len(respData) == 0 {
		return nil, fmt.Errorf("no deposit address found for coin: %s", coin)
	}
	chainSuffix := convertChainToOKxChainSuffix(chain)
	addres := make([]string, 0)
	for _, data := range respData {
		if data["chain"] == coin+chainSuffix {
			if address, ok := data["addr"].(string); ok {
				addres = append(addres, address)
			} else {
				return nil, fmt.Errorf("invalid deposit address format for coin: %s, chain: %s", coin, chain)
			}
		}
	}
	if len(addres) > 0 {
		// reverse the address list
		for i, j := 0, len(addres)-1; i < j; i, j = i+1, j-1 {
			addres[i], addres[j] = addres[j], addres[i]
		}
		return addres, nil
	}
	return nil, fmt.Errorf("no deposit address found for coin: %s, chain: %s", coin, chain)
}

// 获取基本请求 request
func (this *OKx) getBaseRequest() *resty.Request {
	client := resty.New()
	hostURL := "www.okx.com"
	if this.IsTestnet {
		hostURL = "testnet.okx.com"
	}
	client.
		SetBaseURL(fmt.Sprintf("https://%s", hostURL)).
		SetTimeout(30 * time.Second).     // 设置超时时间，超时无反应则返回超时错误
		SetRetryCount(0).                 // 请求失败时的重试次数
		SetRetryWaitTime(5 * time.Second) // 重试前等待时间

	req := client.R()
	req.SetHeader("Content-Type", "application/json")

	// for testnet
	// req.SetHeader("x-simulated-trading", "1")

	return req
}

func (this *OKx) signData(data, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

type OKxResponse struct {
	Code string `json:"code,omitempty"`
	Msg  string `json:"msg,omitempty"`
	Data []any  `json:"data,omitempty"`
}

var httpMutex sync.Mutex

func (this *OKx) sendHTTPRequest(httpMethod, requestPath string, data, result any, authenticated bool, arrayData bool) error {
	// 限制请求频率，1秒内只能请求一次
	httpMutex.Lock()
	defer httpMutex.Unlock()

	coolTime := time.Second * 1
	if !this.lastReqTime.IsZero() && time.Since(this.lastReqTime) < coolTime {
		time.Sleep(coolTime - time.Since(this.lastReqTime))
	}
	this.lastReqTime = time.Now()

	req := this.getBaseRequest()

	payload := []byte("")
	if data != nil {
		payloadData, err := json.Marshal(data)
		if err != nil {
			return errors.New("sendHTTPRequest: Unable to JSON request")
		}
		payload = payloadData
		req.SetBody(payload)
	}

	if authenticated {
		secrets := strings.Split(this.APISecret, "|||")
		if len(secrets) != 2 {
			return errors.New(`OKx API secret settings format error, please separate api secret and password with "|||"`)
		}
		apiSecret, apiPwd := secrets[0], secrets[1]

		utcTime := time.Now().UTC().Format(time.RFC3339)
		sign := this.signData(utcTime+httpMethod+requestPath+string(payload), apiSecret)

		req.SetHeader("OK-ACCESS-KEY", this.APIKey)
		req.SetHeader("OK-ACCESS-SIGN", sign)
		req.SetHeader("OK-ACCESS-TIMESTAMP", utcTime)
		req.SetHeader("OK-ACCESS-PASSPHRASE", apiPwd)
	}

	okResult := &OKxResponse{}
	resp, err := req.SetResult(okResult).Execute(httpMethod, requestPath)
	if err != nil {
		ulog.Errorf("[%s] send http request(%s) err: %s", this.ExchangeName, requestPath, err)
		return err
	}

	if resp.StatusCode() != 200 {
		ulog.Errorf("[%s] request(%s) resp err: %s %s", this.ExchangeName, requestPath, resp.Status(), resp)
		// 错误 code 类型可能不是 string，不能转换为 OKExResponse
		return fmt.Errorf("[%d]%s", resp.StatusCode(), resp)
	}

	if okResult.Code != "0" {
		msg := okResult.Msg
		if len(okResult.Data) > 0 {
			type sMsgData struct {
				SCode string `json:"sCode"`
				SMsg  string `json:"sMsg"`
			}
			var msgData sMsgData
			dataJson, _ := json.Marshal(okResult.Data[0])
			if err := json.Unmarshal(dataJson, &msgData); err == nil && msgData.SMsg != "" {
				msg = msgData.SMsg
			}
		}
		ulog.Errorf("[%s] request(%s) err: %s", this.ExchangeName, requestPath, msg)
		return fmt.Errorf("err[%v]: %v", okResult.Code, msg)
	}

	// ulog.Debugf("[%s] request(%s) resp data: %#v", o.ExchangeName, requestPath, okResult.Data)

	if len(okResult.Data) > 0 && result != nil {
		if arrayData {
			dataJson, _ := json.Marshal(okResult.Data)
			err := json.Unmarshal(dataJson, result)
			if err != nil {
				ulog.Errorf("[%s] request(%s) unmarshal data to result err, resp: %s", this.ExchangeName, requestPath, resp)
			}
			return err
		} else {
			data := okResult.Data[0]
			dataJson, _ := json.Marshal(data)
			err := json.Unmarshal(dataJson, result)
			if err != nil {
				ulog.Errorf("[%s] request(%s) unmarshal data to result err, resp: %s", this.ExchangeName, requestPath, resp)
			}
			return err
		}
	}

	return nil
}

// ccy	String	币种名称，如 BTC
// name	String	币种名称，不显示则无对应名称
// logoLink	String	币种Logo链接
// chain	String	币种链信息
// 有的币种下有多个链，必须要做区分，如USDT下有USDT-ERC20，USDT-TRC20多个链
// canDep	Boolean	当前是否可充值
// false：不可链上充值
// true：可以链上充值
// canWd	Boolean	当前是否可提币
// false：不可链上提币
// true：可以链上提币
// canInternal	Boolean	当前是否可内部转账
// false：不可内部转账
// true：可以内部转账
// minDep	String	币种单笔最小充值量
// minWd	String	币种单笔最小链上提币量
// maxWd	String	币种单笔最大链上提币量
// wdTickSz	String	提币精度,表示小数点后的位数。提币手续费精度与提币精度保持一致。
// 内部转账提币精度为小数点后8位。
// wdQuota	String	过去24小时内提币额度（包含链上提币和内部转账），单位为USD
// usedWdQuota	String	过去24小时内已用提币额度，单位为USD
// minFee	String	普通地址最小提币手续费数量
// 适用于链上提币
// maxFee	String	普通地址最大提币手续费数量
// 适用于链上提币
// minFeeForCtAddr	String	合约地址最小提币手续费数量
// 适用于链上提币
// maxFeeForCtAddr	String	合约地址最大提币手续费数量
// 适用于链上提币
// mainNet	Boolean	当前链是否为主链
// needTag	Boolean	当前链是否需要标签（tag/memo）信息，如 EOS该字段为true
type OKxCoinInfo struct {
	Ccy             string `json:"ccy,omitempty"`
	Name            string `json:"name,omitempty"`
	LogoLink        string `json:"logoLink,omitempty"`
	Chain           string `json:"chain,omitempty"`
	CanDep          bool   `json:"canDep,omitempty"`
	CanWd           bool   `json:"canWd,omitempty"`
	CanInternal     bool   `json:"canInternal,omitempty"`
	MinDep          string `json:"minDep,omitempty"`
	MinWd           string `json:"minWd,omitempty"`
	MaxWd           string `json:"maxWd,omitempty"`
	WdTickSz        string `json:"wdTickSz,omitempty"`
	WdQuota         string `json:"wdQuota,omitempty"`
	UsedWdQuota     string `json:"usedWdQuota,omitempty"`
	MinFee          string `json:"minFee,omitempty"`
	MaxFee          string `json:"maxFee,omitempty"`
	MinFeeForCtAddr string `json:"minFeeForCtAddr,omitempty"`
	MaxFeeForCtAddr string `json:"maxFeeForCtAddr,omitempty"`
	MainNet         bool   `json:"mainNet,omitempty"`
	NeedTag         bool   `json:"needTag,omitempty"`
}

func (this *OKx) GetCoinInfos(coin string) ([]OKxCoinInfo, error) {
	url := fmt.Sprintf("/api/v5/asset/currencies?ccy=%s", coin)
	coinInfos := []OKxCoinInfo{}
	err := this.sendHTTPRequest("GET", url, nil, &coinInfos, true, true)
	if err != nil {
		return nil, err
	}
	return coinInfos, nil
}

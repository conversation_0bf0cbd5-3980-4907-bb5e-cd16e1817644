package core

import (
	"chaintools/utils/ulog"
	"context"
	"crypto/ecdsa"
	"encoding/hex"
	"fmt"
	"math/big"
	"os"

	"chaintools/utils"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
)

const DefaultBaseFeeMultipler = 1.3

type EthClient struct {
	client           *ethclient.Client
	privateKey       *ecdsa.PrivateKey
	endpoint         string
	baseFeeMultipler float64
	fixedGasPrice    *big.Int
}

func NewEthClient(endpoint string, privateKeyStr string) (*EthClient, error) {
	debug := os.Getenv("DEBUG")
	ulog.DEBUG = debug == "true"

	ulog.Debugf("Dial %s...", endpoint)
	client, err := ethclient.Dial(endpoint)
	if err != nil {
		ulog.Errorf("Failed to connect to the client: %v", err)
		return nil, err
	}

	var privateKey *ecdsa.PrivateKey
	// 仅查询用允许为空
	if privateKeyStr != "" {
		privateKey, err = crypto.HexToECDSA(privateKeyStr)
		if err != nil {
			ulog.Errorf("Failed to parse private key: %v", err)
			return nil, err
		}

		publicAddress := crypto.PubkeyToAddress(privateKey.PublicKey)

		ulog.Debugf("PrivateKey to address: %s", publicAddress.Hex())
	}

	ec := &EthClient{client: client, privateKey: privateKey, endpoint: endpoint, baseFeeMultipler: DefaultBaseFeeMultipler}
	return ec, nil
}

func (c *EthClient) SetBaseFeeMultipler(multipler float64) {
	c.baseFeeMultipler = multipler
}

func (c *EthClient) SetFixedGasPrice(gwei float64) {
	c.fixedGasPrice = big.NewInt(int64(gwei * 1e9))
}

func (c *EthClient) GetPublicAddress() string {
	return crypto.PubkeyToAddress(c.privateKey.PublicKey).Hex()
}

func (c *EthClient) GetSuggestedGasPrice() (*big.Int, error) {
	return c.client.SuggestGasPrice(context.Background())
}

func (c *EthClient) TransactionByHash(ctx context.Context, hash common.Hash) (*types.Transaction, bool, error) {
	return c.client.TransactionByHash(ctx, hash)
}

func (c *EthClient) TransactionReceipt(ctx context.Context, hash common.Hash) (*types.Receipt, error) {
	return c.client.TransactionReceipt(ctx, hash)
}

func (c *EthClient) GetBalance(addressStr string) (*big.Int, error) {
	address := common.HexToAddress(addressStr)
	balance, err := c.client.BalanceAt(context.Background(), address, nil) // 使用 nil 以获取最新区块的余额
	if err != nil {
		ulog.Errorf("Failed to get balance for address %s: %v", addressStr, err)
		return nil, err
	}

	ulog.Debugf("Balance for address %s is: %s", addressStr, balance.String())
	return balance, nil
}

func (c *EthClient) GetTokenBalance(tokenAddressStr, accountAddressStr string) (*big.Int, error) {
	tokenAddress := common.HexToAddress(tokenAddressStr)
	accountAddress := common.HexToAddress(accountAddressStr)

	// 构建调用代币合约的balanceOf方法的数据
	data := common.Hex2Bytes("70a08231" + fmt.Sprintf("%064s", accountAddress.Hex()[2:]))

	msg := ethereum.CallMsg{
		To:   &tokenAddress,
		Data: data,
	}
	result, err := c.client.CallContract(context.Background(), msg, nil)
	if err != nil {
		ulog.Errorf("Failed to get token balance for address %s: %v", accountAddressStr, err)
		return nil, err
	}

	if len(result) < 32 {
		return nil, fmt.Errorf("unexpected result length: %d", len(result))
	}

	balance := new(big.Int).SetBytes(result)
	ulog.Debugf("Token balance for address %s is: %s", accountAddressStr, balance.String())
	return balance, nil
}

type TransferOptions struct {
	DataStr  string
	Nonce    *uint64
	GasLimit *uint64
}

func (c *EthClient) Transfer(amount *big.Int, toAddressStr string, options TransferOptions) (string, error) {
	ulog.Infof("Transfer %s to %s", amount.String(), toAddressStr)

	if c.privateKey == nil {
		return "", fmt.Errorf("private key is not set")
	}

	publicAddress := crypto.PubkeyToAddress(c.privateKey.PublicKey)

	var nonce uint64
	var err error
	if options.Nonce == nil {
		nonce, err = c.client.PendingNonceAt(context.Background(), publicAddress)
		if err != nil {
			ulog.Errorf("Failed to get nonce: %v", err)
			return "", err
		}
	} else {
		nonce = *options.Nonce
	}

	maxFeePerGas, maxPriorityFeePerGas, err := c.GetFeePerGas()
	if err != nil {
		ulog.Errorf("Failed to get fee per gas: %v", err)
		return "", err
	}

	var data []byte = nil
	if options.DataStr != "" {
		data = []byte(options.DataStr)
		hexData := hex.EncodeToString(data)
		ulog.Debugf("Data: %s", hexData)
	}

	toAddress := common.HexToAddress(toAddressStr)

	var gasLimit uint64
	if options.GasLimit == nil {
		// 预估 gasLimit
		gasLimit, err = c.EstimateGasLimit(ethereum.CallMsg{
			From:      publicAddress,
			To:        &toAddress,
			Value:     amount,
			GasFeeCap: maxFeePerGas,
			GasTipCap: maxPriorityFeePerGas,
			Data:      data,
		})
		if err != nil {
			ulog.Errorf("Failed to estimate gas limit: %v", err)
			return "", err
		}
	} else {
		gasLimit = *options.GasLimit
	}

	chainID, err := c.client.NetworkID(context.Background())
	if err != nil {
		ulog.Errorf("Failed to get network ID: %v", err)
		return "", err
	}

	tx := &types.DynamicFeeTx{
		ChainID:   chainID,
		Nonce:     nonce,
		To:        &toAddress,
		Value:     amount,
		Gas:       gasLimit,
		GasFeeCap: maxFeePerGas,
		GasTipCap: maxPriorityFeePerGas,
		Data:      data,
	}

	signedTx, err := types.SignNewTx(c.privateKey, types.LatestSignerForChainID(chainID), tx)
	if err != nil {
		ulog.Errorf("Failed to sign transaction: %v", err)
		return "", err
	}

	err = c.client.SendTransaction(context.Background(), signedTx)
	if err != nil {
		ulog.Errorf("Failed to send transaction: %v", err)
		return "", err
	}

	txHash := signedTx.Hash().Hex()
	ulog.Debugf("Sent transaction: %s", txHash)
	return txHash, nil
}

func (c *EthClient) MintData(dataStr string) (string, error) {
	return c.Transfer(common.Big0, c.privateKey.PublicKey.X.String(), TransferOptions{
		DataStr: dataStr,
	})
}

func (c *EthClient) CancelTransaction(txHash string, newGasPrice float64) (string, error) {
	originalTx, isPending, err := c.client.TransactionByHash(context.Background(), common.HexToHash(txHash))
	if err != nil {
		ulog.Errorf("Failed to fetch transaction: %v", err)
		return "", err
	}

	if !isPending {
		return "", fmt.Errorf("transaction is already processed and cannot be cancelled")
	}

	gasLimit := uint64(21000)
	nonce := originalTx.Nonce()
	options := TransferOptions{
		Nonce:    &nonce,
		GasLimit: &gasLimit,
	}

	c.SetFixedGasPrice(newGasPrice)

	return c.Transfer(common.Big0, c.GetPublicAddress(), options)
}

func (c *EthClient) SpeedUpTransaction(txHash string, newGasPrice float64) (string, error) {
	originalTx, isPending, err := c.client.TransactionByHash(context.Background(), common.HexToHash(txHash))
	if err != nil {
		ulog.Errorf("Failed to fetch transaction: %v", err)
		return "", err
	}

	if !isPending {
		return "", fmt.Errorf("transaction is already processed and cannot be speedup")
	}

	nonce := originalTx.Nonce()
	options := TransferOptions{
		Nonce:   &nonce,
		DataStr: hex.EncodeToString(originalTx.Data()),
	}

	c.SetFixedGasPrice(newGasPrice)

	to := originalTx.To().Hex()
	return c.Transfer(originalTx.Value(), to, options)
}

func (c *EthClient) TransferToken(tokenAddressStr string, amount *big.Int, toAddressStr string, options TransferOptions) (string, error) {
	ulog.Infof("Transfer %s of token %s to %s", amount.String(), tokenAddressStr, toAddressStr)

	tokenAddress := common.HexToAddress(tokenAddressStr)
	toAddress := common.HexToAddress(toAddressStr)

	// 方法签名的Keccak-256哈希值的前8个字符
	methodID := "a9059cbb"
	paddedAddress := fmt.Sprintf("%064s", toAddress.Hex()[2:])
	paddedAmount := fmt.Sprintf("%064s", amount.Text(16))

	// 使用 common.Hex2Bytes 来直接从十六进制字符串生成二进制数据，而不是拼接字符串
	dataStr := methodID + paddedAddress + paddedAmount
	data := common.Hex2Bytes(dataStr)

	options.DataStr = string(data)

	return c.Transfer(common.Big0, tokenAddress.Hex(), options)
}

// GetTokenDecimals 使用指定的token合约地址来获取其小数位数
func (c *EthClient) GetTokenDecimals(tokenAddressStr string) (uint8, error) {
	tokenAddress := common.HexToAddress(tokenAddressStr)

	// ERC-20 Decimals方法的签名是 "decimals()"
	// 它的Keccak-256哈希是 "313ce567"，这里我们只需要方法的前4个字节
	data := common.Hex2Bytes("313ce567")

	msg := ethereum.CallMsg{
		To:   &tokenAddress,
		Data: data,
	}
	result, err := c.client.CallContract(context.Background(), msg, nil)
	if err != nil {
		return 0, fmt.Errorf("call contract failed: %v", err)
	}

	// 解析返回的数据
	if len(result) < 32 {
		return 0, fmt.Errorf("unexpected result length: %d", len(result))
	}

	// 将结果转换为big.Int，然后转换为uint8
	decimals := new(big.Int).SetBytes(result[31:32]).Uint64()

	return uint8(decimals), nil
}

func (c *EthClient) EstimateGasLimit(msg ethereum.CallMsg) (uint64, error) {
	gas, err := c.client.EstimateGas(context.Background(), msg)
	if err != nil {
		return 0, fmt.Errorf("failed to estimate gas: %v", err)
	}
	gas = gas * 150 / 100
	ulog.Debugf("EstimateGasLimit: %d", gas)
	return gas, nil
}

func (c *EthClient) GetFeePerGas() (maxFeePerGas, maxPriorityFeePerGas *big.Int, err error) {
	maxPriorityFeePerGas, err = c.client.SuggestGasTipCap(context.Background())
	if err != nil {
		ulog.Errorf("Failed to suggest maxPriorityFeePerGas: %v", err)
		return
	}
	ulog.Debugf("MaxPriorityFeePerGas: %s", maxPriorityFeePerGas.String())

	head, err := c.client.HeaderByNumber(context.Background(), nil) // nil 代表最新的区块
	if err != nil {
		ulog.Errorf("Failed to get head block header: %v", err)
		return
	}

	baseFee := head.BaseFee
	ulog.Debugf("BaseFee: %s", baseFee.String())
	maxFeePerGas = utils.MulBitInt(head.BaseFee, c.baseFeeMultipler)
	ulog.Debugf("BaseFee after: %s, multipler: %.2f", maxFeePerGas, c.baseFeeMultipler)

	if c.fixedGasPrice == nil {
		maxFeePerGas = maxFeePerGas.Add(maxFeePerGas, maxPriorityFeePerGas)
	} else {
		maxFeePerGas = c.fixedGasPrice
		ulog.Debugf("Use fixedGasPrice: %s", maxFeePerGas.String())
	}

	ulog.Debugf("MaxFeePerGas: %s", maxFeePerGas.String())
	return
}

// EstimateTransferFee 预估发送以太币或代币的交易费用。
// tokenAddressStr 代表代币合约的地址，对于ETH转账，此参数应为空字符串。
// amount 代表转账金额，对于代币转账，确保以代币的最小单位（如wei）为单位。
// toAddressStr 代表接收方的地址。
// 返回预估的交易费用（单位：wei）和可能出现的错误。
func (c *EthClient) EstimateTransferFee(amount *big.Int, toAddressStr string, tokenAddressStr string) (*big.Int, error) {
	var data []byte
	if tokenAddressStr != "" {
		// 如果是代币转账，构建相应的数据字段
		methodID := "a9059cbb"
		paddedAddress := fmt.Sprintf("%064s", common.HexToAddress(toAddressStr).Hex()[2:])
		paddedAmount := fmt.Sprintf("%064s", amount.Text(16))
		dataStr := methodID + paddedAddress + paddedAmount
		data = common.Hex2Bytes(dataStr)
	}

	msg := ethereum.CallMsg{
		From: common.HexToAddress(c.GetPublicAddress()),
		Data: data,
	}

	if tokenAddressStr == "" {
		msg.Value = amount
		toAddress := common.HexToAddress(toAddressStr)
		msg.To = &toAddress
	} else {
		toAddress := common.HexToAddress(tokenAddressStr)
		msg.To = &toAddress
	}

	maxFeePerGas, _, err := c.GetFeePerGas()
	if err != nil {
		return nil, fmt.Errorf("failed to get fee per gas: %v", err)
	}

	gasLimit, err := c.EstimateGasLimit(msg)
	if err != nil {
		return nil, fmt.Errorf("failed to estimate gas: %v", err)
	}

	fee := new(big.Int).Mul(maxFeePerGas, big.NewInt(int64(gasLimit)))

	ulog.Infof("EstimateTransferFee: %s = %d * %s", fee.String(), gasLimit, maxFeePerGas.String())
	return fee, nil
}

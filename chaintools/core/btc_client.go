package core

import (
	"bytes"
	"chaintools/utils/ulog"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"sort"

	"github.com/btcsuite/btcd/btcec/v2"
	"github.com/btcsuite/btcd/btcutil"
	"github.com/btcsuite/btcd/chaincfg"
	"github.com/btcsuite/btcd/chaincfg/chainhash"
	"github.com/btcsuite/btcd/txscript"
	"github.com/btcsuite/btcd/wire"
	"github.com/tidwall/gjson"
)

type NetworkType int

const (
	MainNet NetworkType = iota
	TestNet
)

type BTCClient struct {
	PrivateKeyWIF string
	Address       string
	Network       NetworkType
	Blockcypher   *BlockcypherClient
}

func NewBTCClient(privateKeyWIF string, address string, networkType NetworkType, blockcypherToken string) *BTCClient {
	ulog.DEBUG = os.Getenv("DEBUG") != ""

	blockcypherNetWork := BlockcypherNetWorkTest
	if networkType == MainNet {
		blockcypherNetWork = BlockcypherNetWorkMain
	}

	return &BTCClient{
		PrivateKeyWIF: privateKeyWIF,
		Address:       address,
		Network:       networkType,
		Blockcypher:   NewBlockcypherClient(BlockcypherChainBTC, blockcypherNetWork, blockcypherToken),
	}
}

// EstimateFee 估算交易费用，基于每字节的费率
func (client *BTCClient) EstimateFee(txSize int, feePerByte int64) int64 {
	return int64(txSize) * feePerByte
}

type UTXO struct {
	TxHash     string   `json:"tx_hash"`
	TxOutputN  int      `json:"tx_output_n"`
	Value      int64    `json:"value"`
	Script     string   `json:"script"`
	ScriptType string   `json:"script_type"`
	Addresses  []string `json:"addresses"`
}

func SelectUTXOs(utxos []UTXO, amountNeeded int64, feePerByte int64) ([]UTXO, error) {
	// 将UTXOs按金额从小到大排序，优先使用小额UTXO
	sort.Slice(utxos, func(i, j int) bool {
		return utxos[i].Value < utxos[j].Value
	})

	var selectedUTXOs []UTXO
	var totalAmount int64 = 0

	// 基础交易 + 两个输出的估计大小
	var totalBytes int = 10 + 2*34

	for _, utxo := range utxos {
		selectedUTXOs = append(selectedUTXOs, utxo)
		totalAmount += utxo.Value
		totalBytes += 180 // 增加每个输入的估计大小

		// 估算交易费
		estimatedFee := int64(totalBytes) * feePerByte

		if totalAmount >= amountNeeded+estimatedFee {
			break
		}
	}

	if totalAmount <= amountNeeded {
		return nil, fmt.Errorf("insufficient funds")
	}

	return selectedUTXOs, nil
}

// CreateTransaction 创建并签名一个比特币交易
func (client *BTCClient) CreateTransaction(amountToSend int64, feePerByte int64, receivingAddress string, opReturnText string) (string, error) {
	wif, err := btcutil.DecodeWIF(client.PrivateKeyWIF)
	if err != nil {
		return "", err
	}

	var chainCfgParams *chaincfg.Params
	if client.Network == MainNet {
		chainCfgParams = &chaincfg.MainNetParams
	} else {
		chainCfgParams = &chaincfg.TestNet3Params
	}

	// pubKey := wif.PrivKey.PubKey()
	// pubKeyHash := btcutil.Hash160(pubKey.SerializeCompressed())
	// segwitAddr, err := btcutil.NewAddressWitnessPubKeyHash(pubKeyHash, chainCfgParams)
	// if err != nil {
	// 	return "", err
	// }
	// fromAddress := segwitAddr.EncodeAddress()
	fromAddress := client.Address

	ulog.Debugf("create transaction from %s to %s, amount: %.8f BTC", fromAddress, receivingAddress, float64(amountToSend)/1e8)

	UTXOs, err := client.Blockcypher.GetUTXOs(fromAddress)
	if err != nil {
		return "", err
	}

	if len(UTXOs) == 0 {
		return "", fmt.Errorf("no UTXO found")
	}

	selectedUTXOs, err := SelectUTXOs(UTXOs, amountToSend, feePerByte)
	if err != nil {
		return "", err
	}

	var totalBalance int64
	tx := wire.NewMsgTx(2)
	for _, utxo := range selectedUTXOs {
		utxoTxHash, utxoBalance, utxoIndex, _ := utxo.TxHash, utxo.Value, utxo.TxOutputN, utxo.Script
		totalBalance += utxoBalance

		// 解析UTXO的交易哈希
		txHash, err := chainhash.NewHashFromStr(utxoTxHash)
		if err != nil {
			return "", err
		}

		// 创建输入
		outPoint := wire.NewOutPoint(txHash, uint32(utxoIndex))
		txIn := wire.NewTxIn(outPoint, nil, nil)
		txIn.Sequence = 0xffffffff - 2
		tx.AddTxIn(txIn)
	}

	// 解析接收地址
	destinationAddr, err := btcutil.DecodeAddress(receivingAddress, chainCfgParams)
	if err != nil {
		return "", err
	}

	destinationAddrByte, err := txscript.PayToAddrScript(destinationAddr)
	if err != nil {
		return "", err
	}
	txOut := wire.NewTxOut(amountToSend, destinationAddrByte)
	tx.AddTxOut(txOut)

	changeAddress := fromAddress

	// 解析找零地址
	changeAddr, err := btcutil.DecodeAddress(changeAddress, chainCfgParams)
	if err != nil {
		return "", err
	}

	// 生成找零输出
	changeAddrByte, err := txscript.PayToAddrScript(changeAddr)
	if err != nil {
		return "", err
	}
	changeTxOut := wire.NewTxOut(totalBalance-amountToSend, changeAddrByte) // 先不考虑手续费用于计算交易大小估算手续费

	// 将找零输出添加到交易中
	tx.AddTxOut(changeTxOut)

	if opReturnText != "" {
		opReturnData := []byte(opReturnText)
		opReturnScript, err := txscript.NullDataScript(opReturnData)
		if err != nil {
			return "", fmt.Errorf("failed to create OP_RETURN script: %v", err)
		}
		opReturnTxOut := wire.NewTxOut(0, opReturnScript)
		tx.AddTxOut(opReturnTxOut)
	}

	fee := client.EstimateFee(tx.SerializeSize(), feePerByte)
	// 根据估算手续费设置找零
	changeTxOut.Value = totalBalance - amountToSend - fee

	// 签名后交易大小固定，重新计算手续费，然后再次签名
	signTransaction(tx, selectedUTXOs, wif.PrivKey)

	fee = client.EstimateFee(tx.SerializeSize(), feePerByte)

	if totalBalance < (amountToSend + fee) {
		return "", fmt.Errorf("not enough balance")
	}

	ulog.Debugf("estimate tx size: %d bytes, fee: %.8f BTC, change: %.8f BTC", tx.SerializeSize(), float64(fee)/1e8, float64(changeTxOut.Value)/1e8)

	changeTxOut.Value = totalBalance - amountToSend - fee

	signTransaction(tx, selectedUTXOs, wif.PrivKey)

	ulog.Debugf("tx hash: %s", tx.TxHash().String())

	var signedTx bytes.Buffer
	tx.Serialize(&signedTx)

	hexSignedTx := hex.EncodeToString(signedTx.Bytes())

	return hexSignedTx, nil
}

func signTransaction(tx *wire.MsgTx, utxos []UTXO, privKey *btcec.PrivateKey) error {
	for idx, utxo := range utxos {
		scriptPubkeyUnspent, err := hex.DecodeString(utxo.Script)
		if err != nil {
			return err
		}
		fetcher := txscript.NewCannedPrevOutputFetcher(scriptPubkeyUnspent, utxo.Value)
		sigHashes := txscript.NewTxSigHashes(tx, fetcher)

		if utxo.ScriptType == "pay-to-witness-pubkey-hash" {
			witness, err := txscript.WitnessSignature(tx, sigHashes, idx, utxo.Value, scriptPubkeyUnspent, txscript.SigHashAll, privKey, true)
			if err != nil {
				return err
			}
			tx.TxIn[idx].Witness = witness
		} else if utxo.ScriptType == "pay-to-taproot" {
			witness, err := txscript.TaprootWitnessSignature(tx, sigHashes, idx, utxo.Value, scriptPubkeyUnspent, txscript.SigHashAll, privKey)
			if err != nil {
				return err
			}
			tx.TxIn[idx].Witness = witness
		}
	}

	return nil
}

func (b *BTCClient) PushRawTransaction(rawTx string) (string, error) {
	return b.Blockcypher.PushRawTransaction(rawTx)
}

func (b *BTCClient) GetMediumFeePerByte() (int64, error) {
	return b.Blockcypher.GetMediumFeePerByte()
}

func GetBitcoinPrice() (float64, error) {
	url := "https://api.blockchain.info/ticker"
	resp, err := http.Get(url)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, err
	}

	jsonData := gjson.Parse(string(respBody))
	price := jsonData.Get("USD.last").Float()
	return price, nil
}

// ReplaceTransaction 通过 RBF 机制来替换一个未确认的交易
func (client *BTCClient) ReplaceTransaction(utxo UTXO, newFeePerByte int64) (string, error) {
	ulog.Debugf("replace utxo: %#v", utxo)

	txHash, err := chainhash.NewHashFromStr(utxo.TxHash)
	if err != nil {
		return "", err
	}

	outPoint := wire.NewOutPoint(txHash, uint32(utxo.TxOutputN))
	txIn := wire.NewTxIn(outPoint, nil, nil)

	newTx := wire.NewMsgTx(2)
	newTx.AddTxIn(txIn)

	var chainCfgParams *chaincfg.Params
	if client.Network == MainNet {
		chainCfgParams = &chaincfg.MainNetParams
	} else {
		chainCfgParams = &chaincfg.TestNet3Params
	}

	changeAddress := utxo.Addresses[0]
	changeAddr, err := btcutil.DecodeAddress(changeAddress, chainCfgParams)
	if err != nil {
		return "", err
	}

	// 生成找零输出
	changeAddrByte, err := txscript.PayToAddrScript(changeAddr)
	if err != nil {
		return "", err
	}
	changeTxOut := wire.NewTxOut(utxo.Value, changeAddrByte) // 先不考虑手续费用于计算交易大小估算手续费

	// 将找零输出添加到交易中
	newTx.AddTxOut(changeTxOut)

	fee := client.EstimateFee(newTx.SerializeSize(), newFeePerByte)

	// 根据估算手续费设置找零
	changeTxOut.Value = utxo.Value - fee

	wif, err := btcutil.DecodeWIF(client.PrivateKeyWIF)
	if err != nil {
		return "", err
	}

	signTransaction(newTx, []UTXO{utxo}, wif.PrivKey)

	fee = client.EstimateFee(newTx.SerializeSize(), newFeePerByte)

	if utxo.Value < fee {
		return "", fmt.Errorf("not enough balance")
	}

	ulog.Debugf("estimate tx size: %d bytes, fee: %.8f BTC, change: %.8f BTC", newTx.SerializeSize(), float64(fee)/1e8, float64(changeTxOut.Value)/1e8)

	changeTxOut.Value = utxo.Value - fee

	signTransaction(newTx, []UTXO{utxo}, wif.PrivKey)

	var signedTx bytes.Buffer
	newTx.Serialize(&signedTx)
	rawTx := hex.EncodeToString(signedTx.Bytes())
	ulog.Debugf("new raw tx: %s", rawTx)
	ulog.Debugf("new tx hash: %s", newTx.TxHash().String())

	// BlockcypherChain PushRawTransaction 发送 RBF 交易会失败，直接调用比特币节点的 RPC 接口可以成功
	// sentTxHash, err := client.PushRawTransaction(rawTx)

	sentTxHash, err := client.SendRawTransaction(rawTx)
	if err != nil {
		ulog.Debugf("Error sending transaction: %v", err)
		return "", err
	}

	return sentTxHash, nil
}

// IsRBFEnabled 检查交易是否启用了 RBF
func IsRBFEnabled(sequence uint32) bool {
	return sequence < 0xfffffffe
}

func (client *BTCClient) CancelAllUnconfirmedTransactions(address string, privateKeyWIF string, newFeePerByte int64) ([]string, error) {
	txs, err := client.Blockcypher.GetUnconfirmedTransactions(address)
	if err != nil {
		return nil, err
	}

	outTxs := []BlockcypherTx{}
	// 筛选出输入是 address 的交易
	for _, tx := range txs {
		for _, input := range tx.Inputs {
			for _, addr := range input.Addresses {
				if addr == address {
					outTxs = append(outTxs, tx)
					break
				}
			}
		}
	}

	txsToCancel := []BlockcypherTx{}
	// 交易的 prev_hash 对应的交易如果也未确认，则过滤
	for _, tx := range outTxs {
		prevTxUnconfirmed := false
		for _, input := range tx.Inputs {
			prevHash := input.PrevHash
			for _, outTx := range outTxs {
				if outTx.Hash == prevHash {
					prevTxUnconfirmed = true
					break
				}
			}
		}
		if !prevTxUnconfirmed {
			txsToCancel = append(txsToCancel, tx)
		}
	}

	if len(txsToCancel) == 0 {
		return nil, fmt.Errorf("no transactions to cancel")
	}

	ulog.Debugf("txs to cancel:")
	for i, tx := range txsToCancel {
		ulog.Debugf("tx[%d]: %s", i, tx.Hash)
	}

	txids := []string{}
	for _, tx := range txsToCancel {
		utxoHash := tx.Inputs[0].PrevHash
		if !IsRBFEnabled(tx.Inputs[0].Sequence) {
			ulog.Debugf("tx %s input is not RBF enabled", tx.Hash)
			continue
		}

		prevTx, err := client.Blockcypher.GetTransaction(utxoHash)
		if err != nil {
			ulog.Errorf("Error getting transaction: %v", err)
			continue
		}
		utxo := UTXO{
			TxHash: utxoHash,
		}
		for i, output := range prevTx.Outputs {
			for _, addr := range output.Addresses {
				if addr == address {
					utxo.TxOutputN = i
					utxo.Value = output.Value
					utxo.Script = output.Script
					utxo.ScriptType = output.ScriptType
					utxo.Addresses = output.Addresses
					break
				}
			}
		}

		txid, err := client.ReplaceTransaction(utxo, newFeePerByte)
		if err != nil {
			ulog.Debugf("Error cancelling transaction: %v", err)
			continue
		}
		txids = append(txids, txid)
	}

	return txids, nil
}

func (b *BTCClient) SendRawTransaction(rawTx string) (string, error) {
	// 构造请求发送到比特币节点的 RPC 接口
	payload := map[string]interface{}{
		"jsonrpc": "1.0",
		"id":      "curltext",
		"method":  "sendrawtransaction",
		"params":  []interface{}{rawTx},
	}
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}

	node := "https://bitcoin-testnet-archive.allthatnode.com"
	if b.Network == MainNet {
		node = "https://bitcoin-mainnet-archive.allthatnode.com"
	}

	req, err := http.NewRequest("POST", node, bytes.NewBuffer(jsonPayload))
	if err != nil {
		return "", err
	}

	// 设置 RPC 用户名和密码（如果有）
	req.Header.Set("Content-Type", "application/json")
	// req.SetBasicAuth(b.RpcUser, b.RpcPass)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 解析响应
	var result map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&result)
	if err != nil {
		return "", err
	}

	// 检查结果
	if result["error"] != nil {
		return "", fmt.Errorf("RPC error: %v", result["error"])
	}

	// 返回交易ID
	if txid, ok := result["result"].(string); ok {
		return txid, nil
	}

	return "", fmt.Errorf("unexpected response format")
}

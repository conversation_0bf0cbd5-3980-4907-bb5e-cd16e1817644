package frc20

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

type HoldingResponse struct {
	Code    int            `json:"code"`
	Result  map[string]int `json:"result"`
	Message string         `json:"message"`
	Type    string         `json:"type"`
}

func QueryHolding(address string, tick string) (int, error) {
	url := fmt.Sprintf("https://api.zkfair.io/inscription/mint/address?tick=%s&address=%s", tick, address)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return 0, err
	}

	// 添加必要的请求头
	req.Header.Add("Accept", "application/json, text/plain, */*")
	req.Header.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7,nb;q=0.6,pl;q=0.5,pt;q=0.4")
	req.Header.Add("Cache-Control", "no-cache")
	req.Header.Add("Origin", "https://fairinscription.org")
	req.Header.Add("Pragma", "no-cache")
	req.Header.Add("Referer", "https://fairinscription.org/")
	req.Header.Add("Sec-CH-UA", `"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"`)
	req.Header.Add("Sec-CH-UA-Mobile", "?0")
	req.Header.Add("Sec-CH-UA-Platform", `"macOS"`)
	req.Header.Add("Sec-Fetch-Dest", "empty")
	req.Header.Add("Sec-Fetch-Mode", "cors")
	req.Header.Add("Sec-Fetch-Site", "cross-site")
	req.Header.Add("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, err
	}

	var response HoldingResponse
	err = json.Unmarshal(body, &response)
	if err != nil {
		return 0, err
	}

	return response.Result[tick], nil
}

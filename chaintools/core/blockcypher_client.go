package core

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/tidwall/gjson"
)

type BlockcypherChain string

const (
	BlockcypherChainBTC BlockcypherChain = "btc"
	BlockcypherChainETH BlockcypherChain = "eth"
)

type BlockcypherNetWork string

const (
	BlockcypherNetWorkMain BlockcypherNetWork = "main"
	BlockcypherNetWorkTest BlockcypherNetWork = "test3"
)

type BlockcypherClient struct {
	Chain   BlockcypherChain
	Network BlockcypherNetWork
	Token   string
}

type BlockcypherTx struct {
	BlockHash   string               `json:"block_hash"`
	BlockHeight int                  `json:"block_height"`
	BlockIndex  int                  `json:"block_index"`
	Hash        string               `json:"hash"`
	Addresses   []string             `json:"addresses"`
	Total       int64                `json:"total"`
	Fees        int64                `json:"fees"`
	Size        int                  `json:"size"`
	VSize       int                  `json:"vsize"`
	Confirmed   string               `json:"confirmed"`
	Inputs      []BlockcypherTxInput `json:"inputs"`
	Outputs     []BlockcypherOutput  `json:"outputs"`
}

type BlockcypherTxInput struct {
	PrevHash    string   `json:"prev_hash"`
	OutputIndex int      `json:"output_index"`
	OutputValue int64    `json:"output_value"`
	Sequence    uint32   `json:"sequence"`
	Addresses   []string `json:"addresses"`
	ScriptType  string   `json:"script_type"`
	Age         int      `json:"age"`
	Witness     []string `json:"witness"`
}

type BlockcypherOutput struct {
	Value      int64    `json:"value"`
	Script     string   `json:"script"`
	Addresses  []string `json:"addresses"`
	ScriptType string   `json:"script_type"`
}

func NewBlockcypherClient(chain BlockcypherChain, networkType BlockcypherNetWork, blockcypherToken string) *BlockcypherClient {
	return &BlockcypherClient{
		Chain:   chain,
		Network: networkType,
		Token:   blockcypherToken,
	}
}

func (client *BlockcypherClient) requestAddrTxs(address string, before int, limit int) ([]BlockcypherTx, error) {
	url := fmt.Sprintf("https://api.blockcypher.com/v1/btc/%s/addrs/%s/full?limit=%d&includeScript=true", client.Network, address, limit)
	if before > 0 {
		url = fmt.Sprintf("%s&before=%d", url, before)
	}
	if client.Token != "" {
		url = fmt.Sprintf("%s&token=%s", url, client.Token)
	}
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	jsonData := gjson.Parse(string(respBody))
	txs := jsonData.Get("txs")
	if !txs.Exists() {
		return nil, fmt.Errorf("no txs found: %s", string(respBody))
	}

	var txsData []BlockcypherTx
	err = json.Unmarshal([]byte(txs.Raw), &txsData)
	if err != nil {
		return nil, err
	}

	return txsData, nil
}

func (client *BlockcypherClient) GetUTXOs(address string) (utxos []UTXO, err error) {
	url := fmt.Sprintf("https://api.blockcypher.com/v1/btc/%s/addrs/%s/full?limit=50&includeScript=true", client.Network, address)
	if client.Token != "" {
		url = fmt.Sprintf("%s&token=%s", url, client.Token)
	}

	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	jsonData := gjson.Parse(string(respBody))
	txs := jsonData.Get("txs").Array()

	if len(txs) == 0 {
		return nil, fmt.Errorf("no utxo txs found: %s", string(respBody))
	}

	for _, tx := range txs {
		hash := tx.Get("hash").String()
		outputs := tx.Get("outputs").Array()
		for index, output := range outputs {
			addresses := output.Get("addresses").Array()
			if len(addresses) == 1 {
				if addresses[0].String() == address {
					if output.Get("spent_by").Exists() {
						continue
					}
					utxo := UTXO{
						TxHash:     hash,
						TxOutputN:  index,
						Value:      output.Get("value").Int(),
						Script:     output.Get("script").String(),
						ScriptType: output.Get("script_type").String(),
					}
					utxos = append(utxos, utxo)
				}
			}
		}
	}

	return utxos, nil
}

func (client *BlockcypherClient) getAllTransactions(address string) ([]BlockcypherTx, error) {
	limit := 50
	before := 0
	var allTxs []BlockcypherTx
	for {
		txs, err := client.requestAddrTxs(address, before, limit)
		if err != nil {
			return nil, err
		}

		allTxs = append(allTxs, txs...)

		before = txs[len(txs)-1].BlockHeight
		if len(txs) < limit {
			break
		}
	}

	return allTxs, nil
}

func (client *BlockcypherClient) GetUnconfirmedTransactions(address string) ([]BlockcypherTx, error) {
	txs, err := client.getAllTransactions(address)
	if err != nil {
		return nil, err
	}

	var unconfirmedTxs []BlockcypherTx
	for _, tx := range txs {
		if tx.BlockHeight == -1 {
			unconfirmedTxs = append(unconfirmedTxs, tx)
		}
	}

	return unconfirmedTxs, nil
}

func (client *BlockcypherClient) GetTransaction(hash string) (*BlockcypherTx, error) {
	url := fmt.Sprintf("https://api.blockcypher.com/v1/btc/%s/txs/%s", client.Network, hash)
	if client.Token != "" {
		url = fmt.Sprintf("%s?token=%s", url, client.Token)
	}
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var tx *BlockcypherTx
	err = json.Unmarshal(respBody, &tx)
	if err != nil {
		return nil, err
	}

	if tx.Hash == "" {
		return nil, fmt.Errorf("no tx found: %s", string(respBody))
	}

	return tx, nil
}

func (client *BlockcypherClient) GetMediumFeePerByte() (int64, error) {
	url := fmt.Sprintf("https://api.blockcypher.com/v1/btc/%s", client.Network)
	if client.Token != "" {
		url = fmt.Sprintf("%s?token=%s", url, client.Token)
	}
	resp, err := http.Get(url)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, err
	}

	jsonData := gjson.Parse(string(respBody))
	feePerByte := jsonData.Get("medium_fee_per_kb").Int() / 1000

	return feePerByte, nil
}

func (client *BlockcypherClient) PushRawTransaction(rawTx string) (string, error) {
	url := fmt.Sprintf("https://api.blockcypher.com/v1/btc/%s/txs/push", client.Network)
	if client.Token != "" {
		url = fmt.Sprintf("%s?token=%s", url, client.Token)
	}

	body := map[string]string{
		"tx": rawTx,
	}
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return "", err
	}

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonBody))

	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	jsonData := gjson.Parse(string(respBody))
	txHash := jsonData.Get("tx.hash").String()

	if txHash == "" {
		return "", fmt.Errorf("push tx failed: %s %s", resp.Status, string(respBody))
	}

	return txHash, nil
}

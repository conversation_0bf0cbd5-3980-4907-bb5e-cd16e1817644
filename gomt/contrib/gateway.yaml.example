id: "mt5gateway"
slack_channel: "mt5_gateway"
debug: true

platform: "IB"

log_path: "./data/gomt.log"
data_dir: "./data"
adapter_script: ""
python_path: ""
terminal_exe_path: ""

check_sign: false
release_binary_dir: ""

http_addr: ":8204"
adapter_port: 8900

logger:
    level: "info"
    depth: 5

api_keys:
    -
        api_key: turtle
        encrypted_api_secret: ""
        api_secret: 


mt:
    remote_launch: false
    encrypted_secrets: ""
    server: ""
    user_id: ""
    password: ""


ib:
    remote_launch: false
    encrypted_secrets: ""
    host: "127.0.0.1"
    port: 4001
    client_id: 5678
    timeout: 10
    username: ""
    password: ""


market_time_templates:
    -
        name: default
        sessions:
            Sunday: 23:00-24:00 | 23:05-24:00
            Monday: 00:00-22:55, 23:00-24:00 | 00:00-22:55, 23:05-24:00
            Tuesday: 00:00-22:55, 23:00-24:00 | 00:00-22:55, 23:05-24:00
            Wednesday: 00:00-22:55, 23:00-24:00 | 00:00-22:55, 23:05-24:00
            Thursday: 00:00-22:55, 23:00-24:00 | 00:00-22:55, 23:05-24:00
            Friday: 00:00-22:55 | 00:00-22:55

    - 
        name: china
        sessions: 
            Monday: 03:00-10:30, 11:00-22:55 | 03:05-10:30, 11:05-22:55
            Tuesday: 03:00-10:30, 11:00-22:55 | 03:05-10:30, 11:05-22:55
            Wednesday: 03:00-10:30, 11:00-22:55 | 03:05-10:30, 11:05-22:55
            Thursday: 03:00-10:30, 11:00-22:55 | 03:05-10:30, 11:05-22:55
            Friday: 03:00-10:30, 11:00-22:55 | 03:05-10:30, 11:05-22:55

market_times:
    -
        template: china
        symbols: "#CHINA50"

    - 
        template: 
        symbols: "#US500,XAUUSD"
        sessions:
            Monday: 00:00-22:55 | 00:05-22:55
            Tuesday: 00:00-22:55 | 00:05-22:55
            Wednesday: 00:00-22:55 | 00:05-22:55
            Thursday: 00:00-22:55 | 00:05-22:55
            Friday: 00:00-22:55 | 00:05-22:55

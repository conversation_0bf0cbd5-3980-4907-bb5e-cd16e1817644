import tornado.web
from tornado.web import RequestHandler
from tornado.platform.asyncio import <PERSON><PERSON><PERSON>
 
from ib_insync import IB, util
from ib_insync.util import patchAsyncio, df
 
import pandas as pd
import asyncio
 
HTTP_PORT = 8888
GATEWAY_IP = '127.0.0.1'
GATEWAY_PORT = 4001 # 7496 tws, 4001 ib gateway
GATEWAY_CLIENT_ID = 2222

 
import logging
logging.basicConfig(level=logging.DEBUG)


class MainHandler(RequestHandler):
    def get(self):
        self.write('Hello World')
 
 
class AccountSummaryHandler(RequestHandler):
    def get(self):
        account_summary = pd.DataFrame(gateway.ib.accountSummary())
        self.write(account_summary.to_html())

 
class PositionsHandler(RequestHandler):
    def get(self):
        format = self.get_argument("format", "html")

        positions = pd.DataFrame(gateway.ib.positions())
        objs = [
            positions,
            pd.DataFrame(positions['contract'].apply(lambda x: x.dict()).values.tolist())
        ]
 
        drop_cols = ['contract', 'primaryExchange', 'includeExpired', 'secIdType', 'secId', 'comboLegsDescrip',
                     'comboLegs', 'deltaNeutralContract']
 
        positions = pd.concat(objs, axis=1).drop(drop_cols, axis=1)

        if format == "html":
            self.write(positions.to_html())
        elif format == "json":
            self.set_header("Content-Type", "application/json")
            self.write(positions.to_json(orient='records'))
 

class Gateway:

    def __init__(self, host, port, clientID, timeout):
        self._logger = logging.getLogger("")
        self.host = host
        self.port = port
        self.clientID = clientID
        self.timeout = timeout
        self.connected = False
        self.ib = IB()
        self.connect()

    def setConnected(self):
        self.connected = True

    def connect(self, wait=True):
        f = asyncio.ensure_future(self.connectAsync())
        if wait:
            util.run(f)
        self.setConnected()

    async def connectAsync(self):
        """
        Connect to a running TWS/gateway application.
        """
        if self.ib.isConnected():
            return
        self._logger.info('Trying to connect...')
        while True:
            try:
                await self.ib.connectAsync(self.host, self.port, self.clientID, self.timeout)
                if self.ib.isConnected():
                    break
            except ConnectionRefusedError:
                self._logger.error('Connect failed')
                await asyncio.sleep(5)
        self.ib.client.apiError += self._onApiError
        # self.ib.disconnectedEvent += self._onDisconnected

    def _onApiError(self, _errorMsg):
        """
        Reconnect after connection failure. By default the reconnect is
        postponed for half a minute, otherwise TWS can give back error 102
        ("Max number of tickers has been reached").
        """
        self._logger.info(f"on api error: {_errorMsg}, reconnecting...")
        self._reconnect()

    def _onDisconnected(self):
        self._logger.info(f"on disconnected, reconnecting...")
        self._reconnect()

    def _reconnect(self):
        delaySecs = 15
        self._logger.info(f'Reconnecting in {delaySecs} seconds')
        asyncio.get_event_loop().call_later(delaySecs, self.connect)

def make_app():
    urls = [
        (r'/', MainHandler),
        (r'/summary', AccountSummaryHandler),
        (r'/positions', PositionsHandler),
    ]
 
    return tornado.web.Application(handlers=urls)

if __name__ == '__main__':
    print(f'Opening Dashboard on http://localhost:{HTTP_PORT}')
    gateway = Gateway(host=GATEWAY_IP, port=GATEWAY_PORT, clientID=GATEWAY_CLIENT_ID, timeout=10)

    try: 
        patchAsyncio()
        app = make_app()
        app.listen(HTTP_PORT)
        IOLoop.instance().start()
    except KeyboardInterrupt:
        gateway.ib.disconnect()
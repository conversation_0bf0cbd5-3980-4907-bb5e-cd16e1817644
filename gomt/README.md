# gateway

### 运行服务

1. 进入工作目录，确保 gomt.yaml 和  gomt.exe 文件在同一个目录；
2. 以管理员权限运行 powershell；
3. 运行 `gomt.exe -install --platform=MT5` 注册服务
4. 在系统软件 `services` 或 `服务` 中开启服务

### MT5 设置

1. 打开 工具 -> 选项
2. 选择 EA 交易，启用 "允许算法交易"

*** 配置文件 ***

1. 如果下载的是 swissquote 专用的 mt5 软件，软件的安装位置在：`C:/Users/<USER>/AppData/Roaming/Swissquote Bank MT5 Client Terminal/terminal64.exe`
2. 将以上目录填到配置文件的 `terminal_exe_path` 中

### TWS 设置

1. 打开 Global Configuration -> API -> Settings
2. 启用 "Enable ActiveX and Socket Clients"
3. 修改 Socket port, 和程序配置 ib.port 相同, 如 4001

### IB 订阅市场数据

1. https://www.ibkr.com.cn/sso/Login 登录 IB 账号
2. 进入 设置 -> 使用者设置 -> 市场数据订阅
3. 美股订阅: 北美 NASDAQ (Network C/UTP)
4. 港股订阅: 亚太 Hong Kong Securities Exchange (Stocks, Warrants, Bonds)

### IB gateway 添加交易对

因为 IB 必须指定代码获取标的数据，所以需在 gateway 频道使用 .enableTrading 命令来添加交易对，如：

`.enableTrading TSLA`

`.enableTrading 700`

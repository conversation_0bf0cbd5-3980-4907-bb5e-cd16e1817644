module github.com/wizhodl/gomt

go 1.24

require (
	github.com/AlecAivazis/survey/v2 v2.3.7
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc
	github.com/gin-gonic/gin v1.10.0
	github.com/go-resty/resty/v2 v2.11.0
	github.com/tidwall/gjson v1.17.1
	github.com/wizhodl/quanter v0.0.0-20221128081955-c5833d6c4802
	gopkg.in/yaml.v2 v2.4.0
)

require (
	filippo.io/age v1.1.1 // indirect
	github.com/bytedance/sonic v1.11.6 // indirect
	github.com/bytedance/sonic/loader v0.1.1 // indirect
	github.com/cenkalti/backoff/v4 v4.2.1 // indirect
	github.com/cloudwego/base64x v0.1.4 // indirect
	github.com/cloudwego/iasm v0.2.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/goccy/go-json v0.10.2 // indirect
	github.com/kballard/go-shellquote v0.0.0-20180428030007-95032a82bc51 // indirect
	github.com/klauspost/cpuid/v2 v2.2.7 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mgutz/ansi v0.0.0-20200706080929-d51e80ef957d // indirect
	github.com/pelletier/go-toml/v2 v2.2.2 // indirect
	github.com/puzpuzpuz/xsync v1.3.0 // indirect
	github.com/shopspring/decimal v1.3.1 // indirect
	github.com/slack-go/slack v0.12.5 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.1 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/wizhodl/encembed v0.0.0-20220423153945-085b64d1c687 // indirect
	go.opentelemetry.io/otel v1.24.0 // indirect
	go.opentelemetry.io/otel/trace v1.24.0 // indirect
	golang.org/x/arch v0.8.0 // indirect
	golang.org/x/net v0.26.0 // indirect
	golang.org/x/term v0.21.0 // indirect
	golang.org/x/text v0.16.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

require (
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.20.0 // indirect
	github.com/gorilla/websocket v1.5.1
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/kardianos/service v1.2.2
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mitchellh/go-ps v1.0.0
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/puzpuzpuz/xsync/v2 v2.5.1
	github.com/robfig/cron/v3 v3.0.1
	github.com/spf13/cast v1.6.0 // indirect
	github.com/stevedomin/termtable v0.0.0-20150929082024-09d29f3fd628 // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	go.uber.org/atomic v1.11.0
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.27.0
	golang.org/x/crypto v0.24.0 // indirect
	golang.org/x/sys v0.21.0 // indirect
	google.golang.org/protobuf v1.34.2 // indirect
)

package gateway

import (
	"fmt"
	"os"
	"reflect"
	"strconv"
	"strings"

	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/secrets"
	yaml "gopkg.in/yaml.v2"
)

type MTOpt struct {
	RemoteLaunch     bool   `yaml:"remote_launch"`
	Server           string `yaml:"server"`
	ServerTimezone   string `yaml:"server_timezone"`
	UserID           string `yaml:"user_id"`
	Password         string `yaml:"password"`
	EncryptedSecrets string `yaml:"encrypted_secrets"`
}

type IBOpt struct {
	RemoteLaunch     bool   `yaml:"remote_launch"`
	Host             string `yaml:"host"`
	Port             int    `yaml:"port"`
	Username         string `yaml:"username"`
	Password         string `yaml:"password"`
	ClientID         int    `yaml:"client_id"`
	Timeout          int    `yaml:"timeout"`
	EncryptedSecrets string `yaml:"encrypted_secrets"`
	// 每周日几点重启，因为通过 IBC 手工重启 tws_gateway 后，IBC 配置中的重启时间会被重置为刚才手工重启的时间
	// 需要 gateway 在固定时间再重启一次，这样可以恢复设置的固定的重启时间
	AutoRestartTime                    string `yaml:"auto_restart_time"`   // "HH:MM", 对于北京时间来说，不早于 13:00，因为要保证重启时间在 01:00 US/Eastern 之后
	CommandServerPort                  int    `yaml:"command_server_port"` // IBC command server port
	RestartTerminalOnHealthCheckFailed bool   `yaml:"restart_terminal_on_health_check_failed"`
}

func (this *MTOpt) GetUserID() int {
	uid, _ := strconv.ParseInt(this.UserID, 10, 64)
	return int(uid)
}

type WebsocketOpt struct {
	FlushInterval int `yaml:"flush_interval"`
}

type APIKey struct {
	APIKey             string               `yaml:"api_key"`
	EncryptedAPISecret string               `yaml:"encrypted_api_secret"`
	APISecret          secrets.SecretString `yaml:"api_secret"`
}

type APIKeys []APIKey

type Option struct {
	ID                string   `yaml:"id"`
	Debug             bool     `yaml:"debug"`
	Platform          Platform `yaml:"platform"`
	LogPath           string   `yaml:"log_path"`
	LogMaxSize        int      `yaml:"log_max_size"`
	LogMaxBackups     int      `yaml:"log_max_backups"`
	LogMaxAge         int      `yaml:"log_max_age"`
	SlackChannel      string   `yaml:"slack_channel"`
	DataDir           string   `yaml:"data_dir"`
	PythonPath        string   `yaml:"python_path"`
	AdapterScriptPath string   `yaml:"adapter_script_path"`
	TerminalExePath   string   `yaml:"terminal_exe_path"`
	WatchdogExePath   string   `yaml:"watchdog_exe_path"`
	CheckSign         bool     `yaml:"check_sign"`
	HTTPAddr          string   `yaml:"http_addr"`
	AdapterPort       int      `yaml:"adapter_port"`
	APIKeys           APIKeys  `yaml:"api_keys"`
	MT                MTOpt    `yaml:"mt"`
	IB                IBOpt    `yaml:"ib"`
	Websocket         WebsocketOpt
	AutoCloseDays     int    `yaml:"auto_close_days"` // 持仓品种过期前多少天内自动平仓
	ReleaseBinaryDir  string `yaml:"release_binary_dir"`

	MarketTimeTemplates []*MarketTimeTemplate `yaml:"market_time_templates"`
	MarketTimes         []*MarketTime         `yaml:"market_times"`

	loadedFromPath string
}

type MarketTimeTemplate struct {
	Sessions MarketSession `yaml:"sessions"`
	Name     string        `yaml:"name"`
}

type MarketTime struct {
	Sessions MarketSession `yaml:"sessions"`
	Template string        `yaml:"template"`
	Symbols  string        `yaml:"symbols"`
}

func NewOption() *Option {
	return &Option{
		ID:       "ctpgateway",
		Debug:    false,
		HTTPAddr: ":8204",
		DataDir:  "./data",
		Websocket: WebsocketOpt{
			FlushInterval: 1000, // milliseconds
		},
	}
}

/*
检查所有交易时间的配置，并且可以获取某个品种的交易时间

检查和获取交易时间的流程：

 1. 检查配置中的 MarketTimes
    如果某个 MarketTime 是用 template 配置，则查找 template 的配置
    如果某个 MarketTime 没用 template 配置，则有单独的 MarketSession 配置

 2. 检查 Template 中必须有名为 default 的模板

 3. 对于没有在 MarketTimes 中配置的品种，使用 default 模板的配置
*/
func (opts *Option) GetInstrumentSessions(instrumentID string) (sessions Sessions, er error) {
	// 检查 MarketTimes 中的配置是否正确
	for _, marketTime := range opts.MarketTimes {
		mSessions, err := marketTime.Sessions.ValidateSessions()
		if err != nil {
			er = fmt.Errorf("check market time config failed, error: %s", err)
			return
		}
		symbols := strings.Split(marketTime.Symbols, ",")
		if marketTime.Template != "" {
			var templateFound *MarketTimeTemplate
			for _, template := range opts.MarketTimeTemplates {
				if template.Name == marketTime.Template {
					templateFound = template
				}
			}
			if templateFound == nil {
				er = fmt.Errorf("check market time config failed, template not found (%s)", marketTime.Template)
				return
			}
			tSessions, err := templateFound.Sessions.ValidateSessions()
			if err != nil {
				er = err
			}
			// 如果传入具体的 instrumentID 并且找到模板 ，帮助查找 sessions
			if instrumentID != "" && SliceContains(symbols, instrumentID) {
				sessions = tSessions
				// zlog.Debugf("found sessions from MarketTime.Template (%s)", instrumentID)
			}
		} else {
			// 如果没有模板，且没有任何单独的 sessions 配置，报错
			if len(mSessions) == 0 {
				er = fmt.Errorf("market time sessions empty, symbols: (%s)", marketTime.Symbols)
			}
			// 如果传入具体的 instrumentID 并且没有找到模板 ，帮助查找 sessions
			if instrumentID != "" && SliceContains(symbols, instrumentID) {
				sessions = mSessions
				// zlog.Debugf("found sessions from MarketTime (%s)", instrumentID)
			}
		}
	}
	// 检查：1、模板格式是否正确；2、是否有默认模板
	var defaultTemplate *MarketTimeTemplate
	var defaultSessions Sessions
	for _, template := range opts.MarketTimeTemplates {
		tSessions, err := template.Sessions.ValidateSessions()
		if err != nil {
			er = fmt.Errorf("check market time template config failed, error: %s", err)
			return
		}
		if strings.EqualFold(template.Name, "default") {
			defaultTemplate = template
			defaultSessions = tSessions
		}
	}
	if defaultTemplate == nil {
		er = fmt.Errorf("can not find market template named: default")
		return
	}
	if instrumentID != "" && len(sessions) == 0 {
		sessions = defaultSessions
		// zlog.Debugf("found sessions from default MarketTime Template (%s)", instrumentID)
	}
	return
}

func (opts *Option) Load(path string) error {
	file, err := os.Open(path)
	if err != nil {
		return err
	}
	err = yaml.NewDecoder(file).Decode(opts)
	if err != nil {
		file.Close()
		return err
	}
	file.Close()

	// 检查 MarketTime 有关的格式是否正确
	_, err = opts.GetInstrumentSessions("")
	if err != nil {
		return err
	}
	opts.loadedFromPath = path
	return nil
}

func (opts *Option) Print() {
	s := reflect.ValueOf(opts).Elem()
	typeOfT := s.Type()

	for i := 0; i < s.NumField(); i++ {
		f := s.Field(i)
		zlog.Debugf("option: %s: %v", typeOfT.Field(i).Name, f.Interface())
	}
}

func (opts *Option) getAPISecret(apiKey string) (apiSecret secrets.SecretString) {
	for _, key := range opts.APIKeys {
		if key.APIKey == apiKey {
			return key.APISecret
		}
	}
	return ""
}

func (opts *Option) IsRemoteLaunch() bool {
	remoteLaunch := false
	if opts.Platform == MT5 {
		remoteLaunch = opts.MT.RemoteLaunch
	} else if opts.Platform == IB {
		remoteLaunch = opts.IB.RemoteLaunch
	}
	return remoteLaunch
}

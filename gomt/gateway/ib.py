import logging
import tornado.web
from tornado.web import Request<PERSON><PERSON>ler
from tornado.platform.asyncio import <PERSON><PERSON><PERSON>

from ib_insync import IB, util
from ib_insync.util import patchAsyncio
from ib_insync.contract import Contract
from ib_insync.order import Order
from ib_insync.wrapper import RequestError

import pandas as pd
import asyncio
import argparse
import requests
import json
import socket


logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s.%(msecs)03d %(levelname)s %(module)s - %(funcName)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)


class BaseHandler(RequestHandler):
    def write_error(self, status_code, **kwargs):
        if "exc_info" in kwargs and len(kwargs["exc_info"]) > 1:
            exc = kwargs["exc_info"][1]
            if isinstance(exc, RequestError):
                self.json_failed(f'{exc.message}, reqId: {exc.reqId}, code: {exc.code}')
                return
            elif not gateway.ib.isConnected():
                self.json_failed('lost connection to TWS')
                return

        super(<PERSON><PERSON><PERSON><PERSON>, self).write_error(status_code, **kwargs)

    def json_ok(self, data: any = None):
        if data is None:
            self.write("{}")
            return

        format = self.get_argument("format", "json")
        if format == "html":
            self.write(data.to_html())
        elif format == "json":
            self.set_header("Content-Type", "application/json")
            if isinstance(data, pd.DataFrame):
                self.write(data.to_json(orient='records'))
            else:
                self.write(data)

    def json_failed(self, msg=""):
        self.set_status(500)
        self.write({
            "message": msg,
        })


class TerminalInfoHandler(BaseHandler):
    def get(self):
        info = {
            "connected":    gateway.ib.isConnected(),
            "name":         "ib",
            "trade_allowed": True,
        }
        self.json_ok(info)


class AccountSummaryHandler(BaseHandler):
    def get(self):
        account_summary = pd.DataFrame(gateway.ib.accountSummary())
        self.json_ok(account_summary)


class PositionsHandler(BaseHandler):
    def get(self):
        positions = pd.DataFrame(gateway.ib.positions())
        if positions.size == 0:
            self.json_ok(positions)
            return

        objs = [
            positions,
            pd.DataFrame(positions['contract'].apply(
                lambda x: x.dict()).values.tolist())
        ]

        drop_cols = ['contract', 'primaryExchange', 'includeExpired', 'secIdType', 'secId', 'comboLegsDescrip',
                     'comboLegs', 'deltaNeutralContract']

        positions = pd.concat(objs, axis=1).drop(drop_cols, axis=1)
        self.json_ok(positions)


class OpenOrdersHandler(BaseHandler):
    def get(self):
        orders = pd.DataFrame(gateway.ib.reqAllOpenOrders())  # 包含 TWS 操作的订单
        self.json_ok(orders)


# openTrades 默认只有当前客户端的挂单
# 如果先调用 reqAllOpenOrders 再调用 openTrades 就会有非当前客户端下的单
# 但是 TWS 下的单在 openTrades 出现后，通过 TWS 取消，openTrades 仍然有该订单
# 所以需要通过 reqAllOpenOrders 来过滤掉 openTrades 里的已完成订单

def getOpenTrades():
    allOpenOrders = gateway.ib.reqAllOpenOrders()
    openTrades = gateway.ib.openTrades()
    trades = []
    for trade in openTrades:
        for order in allOpenOrders:
            if order.order.permId == trade.order.permId:
                trades.append(trade)
                break
    return trades


class OpenTradesHandler(BaseHandler):
    def get(self):
        trades = getOpenTrades()
        self.json_ok(pd.DataFrame(trades))


class CompletedOrdersHandler(BaseHandler):
    def get(self):
        orders = pd.DataFrame(gateway.ib.reqCompletedOrders(False))
        # 只返回了当天的完成订单
        # 已成交订单无成交价数据
        self.json_ok(orders)


# 只有当天数据
class TradesHandler(BaseHandler):
    def get(self):
        res = pd.DataFrame(gateway.ib.trades())  # open trades + completed trades
        self.json_ok(res)


# 该接口返回的数据和 TWS Trade Log 选择的 "show trades for.." 相同，最多 7 天
# 无 Symbol、总数量等字段
class ExecutionsHandler(BaseHandler):
    def get(self):
        res = pd.DataFrame(gateway.ib.executions())
        self.json_ok(res)


# 该接口返回的数据和 TWS Trade Log 选择的 "show trades for.." 相同，最多 7 天
# 无成交价、总数量等字段
class FillsHandler(BaseHandler):
    def get(self):
        res = pd.DataFrame(gateway.ib.fills())
        self.json_ok(res)


class SymbolsHandler(BaseHandler):
    def get(self):
        pattern = self.get_argument("pattern", "")
        symbols = pd.DataFrame(gateway.ib.reqMatchingSymbols(pattern))
        self.json_ok(symbols)


class ContractDetailsHandler(BaseHandler):
    def get(self):
        symbol = self.get_argument("symbol", "")
        type = self.get_argument("type", "STK")
        currency = self.get_argument("currency", "")
        exchange = self.get_argument("exchange", "")
        # exchange = self.get_argument("exchange", "SMART") SMART 美股会自动选择交易所，港股会找不到
        contract = Contract()
        contract.symbol = symbol
        contract.secType = type
        contract.currency = currency
        contract.exchange = exchange
        details = pd.DataFrame(gateway.ib.reqContractDetails(contract))
        self.json_ok(details)


# 数据需订阅购买，如 TSLA 订阅 NASDAQ (Network C/UTP) 数据
class KlineHistoryHandler(BaseHandler):
    def get(self):
        symbol = self.get_argument("symbol", "")
        type = self.get_argument("type", "STK")
        endDateTime = self.get_argument("endDateTime", "")
        durationStr = self.get_argument("durationStr", "30 D")
        barSizeSetting = self.get_argument("barSizeSetting", "1 hour")
        whatToShow = self.get_argument("whatToShow", "ADJUSTED_LAST")
        currency = self.get_argument("currency", "USD")
        exchange = self.get_argument("exchange", "SMART")
        contract = Contract()
        contract.symbol = symbol
        contract.secType = type
        contract.currency = currency
        contract.exchange = exchange

        bars = gateway.ib.reqHistoricalData(contract, endDateTime=endDateTime, durationStr=durationStr,
                                            barSizeSetting=barSizeSetting, whatToShow=whatToShow, useRTH=True)
        self.json_ok(pd.DataFrame(bars))


class PlaceOrderHandler(BaseHandler):
    def post(self):
        args = tornado.escape.json_decode(self.request.body)
        request = {
            "symbol": args.get("symbol"),
            "secType": args.get("secType", "STK"),
            "currency": args.get("currency", "USD"),
            "exchange": args.get("exchange", "SMART"),
            "action": args.get("action", "BUY"),
            "orderType": args.get("orderType", "LMT"),
            "totalQuantity": args.get("totalQuantity", 0),
            "lmtPrice": args.get("lmtPrice", 0),
            "auxPrice": args.get("auxPrice", 0),
            "tif": args.get("tif", ""),
            "outsideRth": args.get("outsideRth", False),
        }

        # 保证以下字段是 float 类型
        floatParams = ["lmtPrice", "auxPrice"]
        for param in floatParams:
            if isinstance(request[param], int):
                request[param] = float(request[param])

        logging.info(f"place order request: {request}")

        contract = Contract()
        contract.symbol = request["symbol"]
        contract.secType = request["secType"]
        contract.currency = request["currency"]
        contract.exchange = request["exchange"]

        order = Order()
        order.action = request["action"]
        order.orderType = request["orderType"]
        order.totalQuantity = request["totalQuantity"]
        order.lmtPrice = request["lmtPrice"]
        order.auxPrice = request["auxPrice"]
        order.tif = request["tif"]
        order.outsideRth = request["outsideRth"]

        trade = gateway.ib.placeOrder(contract, order)
        if trade is None:
            self.json_failed("place order failed")
            return

        res = pd.DataFrame([trade])
        self.json_ok(res.to_json(orient='records')[1:-1])


class CancelOrderHandler(BaseHandler):
    def get(self):
        orderID = self.get_argument("orderID")
        gateway.ib.cancelOrder(Order(orderId=int(orderID)))
        # 无论是否成功返回的都是 None
        self.json_ok()


class ReqMktDataHandler(BaseHandler):
    def get(self):
        symbol = self.get_argument("symbol")
        type = self.get_argument("type", "STK")
        exchange = self.get_argument("exchange", "SMART")
        currency = self.get_argument("currency", "USD")
        contract = Contract(symbol=symbol, secType=type, exchange=exchange, currency=currency)
        gateway.ib.reqMktData(contract, genericTickList='221,233')
        gateway.ib.sleep(0) # need to be 0
        self.json_ok()


class ReqMarketRuleHandler(BaseHandler):
    def get(self):
        marketRuleId = int(self.get_argument("id"))
        priceIncrement = gateway.ib.reqMarketRule(marketRuleId)
        self.json_ok(pd.DataFrame(priceIncrement))


class TickersHandler(BaseHandler):
    # 用 reqMktData 订阅之后可以读到 tickers
    def get(self):
        tickers = gateway.ib.tickers()
        self.json_ok(pd.DataFrame(tickers))


class IBCCommandHandler(BaseHandler):

    def get(self):
        command = self.get_argument("command")
        ok, message = gateway.send_ibc_command(command)
        if ok:
            self.json_ok({'message': message})
        else:
            self.json_failed()


class Gateway:

    def __init__(self, host, port, clientID, timeout, callback_url, command_server_port):
        self._logger = logging.getLogger("")
        self.host = host
        self.port = port
        self.clientID = clientID
        self.timeout = timeout
        self.callback_url = callback_url
        self.command_server_port = command_server_port
        self.connected = False
        self.ib = IB()

        # RaiseRequestErrors 和 errorEvent 只能 2 选 1，不能同时工作
        self.ib.RaiseRequestErrors = True
        # self.ib.errorEvent += self._onErrorEvent

        self.connect()

    def callback(self, value: any):
        if self.callback == "":
            self._logger.error("callback url is empty")
            return
        try:
            resp = requests.post(self.callback_url, data=value)
        except Exception as e:
            self._logger.error(f"request call back exception: {e}")
            return

        if resp.status_code != 200:
            self._logger.error(
                f"callback request failed, error: http code: {resp.status_code}")
        else:
            self._logger.debug("callback success")

    def setConnected(self):
        self.connected = True

    def connect(self, wait=True):
        f = asyncio.ensure_future(self.connectAsync())
        if wait:
            util.run(f)
        self.setConnected()

    async def connectAsync(self):
        """
        Connect to a running TWS/gateway application.
        """
        if self.ib.isConnected():
            return
        self._logger.info('Trying to connect...')
        while True:
            try:
                await self.ib.connectAsync(self.host, self.port, self.clientID, self.timeout)
                if self.ib.isConnected():
                    break
            except:
                self._logger.error('Connect failed, retrying...')
                await asyncio.sleep(5)
        self.ib.client.apiError += self._onApiError
        # self.ib.disconnectedEvent += self._onDisconnected

    def _onApiError(self, _errorMsg):
        """
        Reconnect after connection failure. By default the reconnect is
        postponed for half a minute, otherwise TWS can give back error 102
        ("Max number of tickers has been reached").
        """
        self._logger.info(f"on api error: {_errorMsg}, reconnecting...")
        self._reconnect()

    def _onDisconnected(self):
        self._logger.info("on disconnected, reconnecting...")
        self._reconnect()

    def _reconnect(self):
        delaySecs = 15
        self._logger.info(f'Reconnecting in {delaySecs} seconds')
        asyncio.get_event_loop().call_later(delaySecs, self.connect)

    def _onErrorEvent(self, reqId: int, errorCode: int, errorString: str, contract: Contract):
        self._logger.info(f'got error event, reqId: {reqId}, errorCode: {errorCode}, errorString: {errorString}')

    def send_ibc_command(self, command: str) -> tuple[bool, str]:
        command = command.strip().upper()
        host = '127.0.0.1'
        port = self.command_server_port               # Change this if your server uses a different port
        timeout = 3            # Timeout in seconds for both connection and read

        # telnetlib will deprecated in 3.13, use raw socket instead
        # Create a socket object
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.settimeout(timeout)  # Set timeout for the connection and operations

        try:
            # Connect to the server
            client_socket.connect((host, port))

            # Send the command followed by a newline, encoded as ASCII
            client_socket.sendall((command + "\n").encode('ascii'))

            # Receive the response from the server
            response = client_socket.recv(4096).decode('ascii')  # Adjust buffer size as needed

            # Close the socket
            client_socket.close()

            return True, response
        except Exception as e:
            # In case of any exceptions, close the socket and return error
            client_socket.close()
            return False, f"send command failed, error: {e}"


def make_app():
    urls = [
        (r"/ib/terminal_info", TerminalInfoHandler),
        (r'/ib/summary', AccountSummaryHandler),
        (r'/ib/positions', PositionsHandler),
        (r'/ib/open/orders', OpenOrdersHandler),
        (r'/ib/open/trades', OpenTradesHandler),
        (r'/ib/completed/orders', CompletedOrdersHandler),
        (r'/ib/trades', TradesHandler),
        (r'/ib/executions', ExecutionsHandler),
        (r'/ib/fills', FillsHandler),
        (r'/ib/symbols', SymbolsHandler),
        (r'/ib/contract/details', ContractDetailsHandler),
        (r'/ib/kline/history', KlineHistoryHandler),
        (r'/ib/place/order', PlaceOrderHandler),
        (r'/ib/cancel/order', CancelOrderHandler),
        (r'/ib/req/market', ReqMktDataHandler),
        (r'/ib/tickers', TickersHandler),
        (r'/ib/market/rule', ReqMarketRuleHandler),
        (r'/ib/ibc/command', IBCCommandHandler),
    ]

    return tornado.web.Application(handlers=urls)


lastTrades = []


def orderCheck():
    if not gateway.connected:
        return

    trades = getOpenTrades()

    global lastTrades

    firstCheck = False
    if len(lastTrades) == 0:
        firstCheck = True

    news, updates, deletes = compareOrders(lastTrades, trades)
    lastTrades = trades

    trades = []
    for o in news:
        trades.append(o)
    for o in updates:
        trades.append(o)
    for o in deletes:
        allTrades = gateway.ib.trades()
        for trade in allTrades:
            if o.order.permId == trade.order.permId:
                trades.append(o)
                break

    if (not firstCheck) and len(trades) > 0:
        print(
            f"send callback: order.update, news: {len(news)}, updates: {len(updates)}, deletes: {len(deletes)}, total: {len(trades)}")
        data = pd.DataFrame(trades).to_json(orient="records")
        json_str = '{"channel": "order","type": "update", "data":' + data + '}'
        gateway.callback(json_str)


def compareOrders(oldTrades, newTrades):
    news = []
    updates = []
    deletes = []

    for new in newTrades:
        found = False
        for old in oldTrades:
            if new.order.permId == old.order.permId:
                if new.orderStatus.filled != old.orderStatus.filled:
                    updates.append(new)
                found = True
                break
        if not found:
            news.append(new)

    for old in oldTrades:
        found = False
        for new in newTrades:
            if new.order.permId == old.order.permId:
                found = True
                break
        if not found:
            deletes.append(old)

    return news, updates, deletes


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='IB adapter script')
    parser.add_argument('port', type=int, help='adapter http port number')
    parser.add_argument('callback_url', type=str, nargs='?', default="", help='adapter callback url')
    parser.add_argument('--gateway_host', type=str, nargs='?', default="127.0.0.1",
                        help='tws or ib gateway api host (default: 127.0.0.1)')
    parser.add_argument('--gateway_port', type=int, nargs='?', default=4001,
                        help='tws or ib gateway api port number (default: 4001)')
    parser.add_argument('--client_id', type=int, nargs='?', default=5678,
                        help='client id (default: 5678)')
    parser.add_argument('--timeout', type=int, nargs='?', default=10,
                        help='ibapi async request timeout in seconds (default: 10)')
    parser.add_argument('--command_server_port', type=int, nargs='?',
                        default=7462, help='IBC command server port number')

    args = parser.parse_args()

    print("adapter args: ", args)
    print("adapter callback url: ", args.callback_url)
    print(f'IB gateway on http://localhost:{args.port}')

    # set ib_insync logging level
    util.logToConsole(logging.INFO)

    gateway = Gateway(host=args.gateway_host, port=args.gateway_port,
                      clientID=args.client_id, timeout=args.timeout, callback_url=args.callback_url, command_server_port=args.command_server_port)

    tornado.ioloop.PeriodicCallback(orderCheck, callback_time=2000).start()

    try:
        patchAsyncio()
        app = make_app()
        app.listen(args.port)
        IOLoop.instance().start()
    except KeyboardInterrupt:
        gateway.ib.disconnect()

package gateway

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

type GatewayMessenger struct {
	gateway              *GatewayServer
	files                map[string][]byte
	commandsRequiresFile [][]string // 需要上传文件的命令列表，[[channelID, commandName],...]
}

func NewGatewayMessenger(gateway *GatewayServer) *GatewayMessenger {
	return &GatewayMessenger{
		gateway:              gateway,
		files:                map[string][]byte{},
		commandsRequiresFile: [][]string{},
	}
}

func (this *GatewayMessenger) SendRTMMessage(channelName, msg string) (bool, error) {
	packet := exchange.NewSlackResponsePacket("", channelName, msg)
	this.gateway.sendPacket(packet)
	return true, nil
}

func (this *GatewayMessenger) SendFileMessage(channelName, title, msg, comment, ftype string) {
	packet := exchange.NewSlackFileResponsePacket("", channelName, title, msg, comment, ftype)
	this.gateway.sendPacket(packet)
}

func (this *GatewayMessenger) GetFile(url string) ([]byte, error) {
	if fileContent, found := this.files[url]; found {
		return fileContent, nil
	}
	return []byte{}, errors.New("file not found")
}

func (this *GatewayMessenger) RegisterCommandRequiresFile(channelName, commandName string) {
	this.commandsRequiresFile = append(this.commandsRequiresFile, []string{channelName, commandName})
}

func (this *GatewayMessenger) DeleteMsgs(channelID string, queries []string, beforeTime time.Time, after *time.Time) (count int, err error) {
	return 0, fmt.Errorf("not implemented")
}

func (this *GatewayMessenger) GetChannelID(channelName string) string {
	return ""
}

func (this *GatewayMessenger) handleMessage(channelName string, clientPacket *exchange.ClientPacket) {
	zlog.Infof("handleMessage channelName: %s, clientPacket: %v", channelName, clientPacket.PacketHeader.String())
	if this.gateway.opts.SlackChannel == channelName {
		txt, fileURL, fileContent, err := clientPacket.SlackRequest.Decode(this.gateway.opts.getAPISecret(clientPacket.APIKey))
		if err != nil {
			this.gateway.ErrorMsgf("decode slack msg failed, error: %s", err)
			return
		}
		// 有时候客户端输入的字符容易被 markdown 转义，比如英文双引号，比如 -- ，__， ** 这些都可能被命令行自动转义
		// 因此，输入的时候可能用 code 将命令包裹起来，所以我们这里首先将 code 的包裹标签删除掉
		txt = strings.Replace(txt, "`", "", -1)
		if strings.HasPrefix(txt, ".") {
			txtArrOrig := strings.Split(txt[1:], " ")
			txtArr := []string{}
			for _, txt := range txtArrOrig {
				// 忽略参数之间多余的空格
				if txt != "" {
					txtArr = append(txtArr, txt)
				}
			}
			if len(txtArr) == 0 {
				return
			}

			command := txtArr[0]
			args := txtArr[1:]

			fileExist := false
			if fileURL != "" {
				this.files[fileURL] = fileContent
				fileExist = true
				args = append(args, fileURL)
			}

			for _, channelCommand := range this.commandsRequiresFile {
				commandName := channelCommand[1]
				if command == commandName && !fileExist {
					this.gateway.ErrorMsgf("command (%s) 需要上传文件", command)
					return
				}
			}
			this.gateway.commandProcessor.Process(command, args)
		}
	}
}

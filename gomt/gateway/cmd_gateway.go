package gateway

import (
	"encoding/json"
	"fmt"
	"os"
	"path"
	"sort"
	"strings"
	"time"

	"github.com/wizhodl/quanter/common/backscanner"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/utils"
)

type GatewayCommand struct {
	command.Command
	controller *GatewayServer
}

type LaunchGatewayCommand GatewayCommand

func NewLaunchGridCommand(controller *GatewayServer) *LaunchGatewayCommand {
	cmd := &LaunchGatewayCommand{
		Command: command.Command{
			Name:            "launch",
			Instruction:     "`.launch Password GoogleAuthCode safe[可选]` 启动程序",
			RequiresConfirm: false,
			ArgMin:          2,
			ArgMax:          3,
			AuthcodePos:     -1, // launch 命令不能在解密主密码前校验 authcode，所以把 authCode 当成是普通参数处理，跳过统一的校验流程
		},
		controller: controller,
	}
	return cmd
}

func (this *LaunchGatewayCommand) Do() bool {
	password := this.Args[0]
	authCode := this.Args[1]
	if err := this.controller.launch(password, authCode); err != nil {
		this.ErrorMsgf("启动失败，error: %s", err)
		return false
	}
	this.SendMsgf("启动成功。")
	return true
}

type RestartAdapterGatewayCommand GatewayCommand

func NewRestartAdapterGatewayCommand(controller *GatewayServer) *RestartAdapterGatewayCommand {
	cmd := &RestartAdapterGatewayCommand{
		Command: command.Command{
			Name:            "restartAdapter",
			Alias:           []string{"ra"},
			Instruction:     "`.restartAdapter authCode` 重启适配器",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1, // launch 命令不能在解密主密码前校验 authcode，所以把 authCode 当成是普通参数处理，跳过统一的校验流程
		},
		controller: controller,
	}
	return cmd
}

func (this *RestartAdapterGatewayCommand) Do() bool {
	this.SendMsgf("准备重启 %s 适配器", this.controller.opts.Platform)

	adapter := this.controller.client.GetOwnedProcess(Adapter)
	adapter.ResetAutoRecover()

	err := this.controller.client.RestartOwnedProcess(Adapter)
	if err != nil {
		this.ErrorMsgf("重启 %s 适配器出错，error: %s", this.controller.opts.Platform, err)
		return false
	}
	this.SendMsgf("重启成功。")
	return true
}

type InitAdapterGatewayCommand GatewayCommand

func NewInitAdapterGatewayCommand(controller *GatewayServer) *InitAdapterGatewayCommand {
	cmd := &InitAdapterGatewayCommand{
		Command: command.Command{
			Name:            "init",
			Instruction:     "`.init authCode` 重新初始化",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1, // launch 命令不能在解密主密码前校验 authcode，所以把 authCode 当成是普通参数处理，跳过统一的校验流程
		},
		controller: controller,
	}
	return cmd
}

func (this *InitAdapterGatewayCommand) Do() bool {
	this.SendMsgf("准备重新初始化")
	err := this.controller.client.Initialize()
	if err != nil {
		this.ErrorMsgf("重新初始化，error: %s", err)
		return false
	}
	go this.controller.cacheInstruments()
	this.SendMsgf("重新初始化成功。")
	return true
}

type ExitGatewayCommand GatewayCommand

func NewExitGatewayCommand(controller *GatewayServer) *ExitGatewayCommand {
	cmd := &ExitGatewayCommand{
		Command: command.Command{
			Name:            "restart",
			Instruction:     "`.restart GoogleAuthCode` 退出程序，重新启动",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ExitGatewayCommand) Do() bool {
	this.SendMsgf("准备退出程序...")
	os.Exit(0)
	return true
}

type SubscribeGatewayCommand GatewayCommand

func NewSubscribeGatewayCommand(controller *GatewayServer) *SubscribeGatewayCommand {
	cmd := &SubscribeGatewayCommand{
		Command: command.Command{
			Name:            "subscribe",
			Alias:           []string{"sub"},
			Instruction:     "`.subscribe key` 订阅推送",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *SubscribeGatewayCommand) Do() bool {
	if this.controller.slackConnAddr == "" {
		this.ErrorMsgf("系统错误，Slack 连接没有和 websocket 关联。")
		return false
	}
	// 直接修改 slackChannel 对应的订阅列表
	// 因为命令处理函数无法模拟 slack 适配器的 websocket 连接发送 subscribe 命令
	key := this.Args[0]
	this.controller.AddSubscribeKey(this.controller.slackConnAddr, key)
	if !strings.HasSuffix(key, "slack") {
		instrumentID := strings.Split(key, ".")[0]
		if err := this.controller.EnableTrading(instrumentID); err != nil {
			zlog.Errorf("error enable trading for key: %s", key)
		}
	}
	this.SendMsgf("订阅成功。")
	return true
}

type UnsubscribeGatewayCommand GatewayCommand

func NewUnsubscribeGatewayCommand(controller *GatewayServer) *UnsubscribeGatewayCommand {
	cmd := &UnsubscribeGatewayCommand{
		Command: command.Command{
			Name:            "unsubscribe",
			Alias:           []string{"unsub"},
			Instruction:     "`.unsubscribe key` 取消订阅推送",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *UnsubscribeGatewayCommand) Do() bool {
	if this.controller.slackConnAddr == "" {
		this.ErrorMsgf("系统错误，Slack 连接没有和 websocket 关联。")
		return false
	}
	// 直接修改 slackChannel 对应的订阅列表
	// 因为命令处理函数无法模拟 slack 适配器的 websocket 连接发送 unsubscribe 命令
	this.controller.RemoveSubscribeKey(this.controller.slackConnAddr, this.Args[0])
	this.SendMsgf("取消订阅成功。")
	return true
}

type EnableTradingGatewayCommand GatewayCommand

func NewEnableTradingGatewayCommand(controller *GatewayServer) *EnableTradingGatewayCommand {
	cmd := &EnableTradingGatewayCommand{
		Command: command.Command{
			Name:            "enableTrading",
			Alias:           []string{"et"},
			Instruction:     "`.enableTrading symbol` 启用交易对",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *EnableTradingGatewayCommand) Do() bool {
	if this.controller.slackConnAddr == "" {
		this.ErrorMsgf("系统错误，Slack 连接没有和 websocket 关联。")
		return false
	}

	symbol := this.Args[0]

	instruments, err := this.controller.client.GetInstruments(symbol)
	if err != nil {
		this.ErrorMsgf("get instrument failed, symbol: %s, error: %s", symbol, err)
		return false
	}

	for _, instrument := range instruments {
		if instrument.QuoteToSettleRate == 0 {
			continue
		}
		this.controller.Instruments.Store(instrument.Symbol, instrument)
	}

	if err := this.controller.EnableTrading(symbol); err != nil {
		this.ErrorMsgf("启用失败：%s", err)
	} else {
		this.SendMsgf("启用成功。")
	}
	return true
}

type DisbleTradingGatewayCommand GatewayCommand

func NewDisbleTradingGatewayCommand(controller *GatewayServer) *DisbleTradingGatewayCommand {
	cmd := &DisbleTradingGatewayCommand{
		Command: command.Command{
			Name:            "disbleTrading",
			Alias:           []string{"dt"},
			Instruction:     "`.disbleTrading symbol` 禁用交易对",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *DisbleTradingGatewayCommand) Do() bool {
	if this.controller.slackConnAddr == "" {
		this.ErrorMsgf("系统错误，Slack 连接没有和 websocket 关联。")
		return false
	}

	symbol := this.Args[0]
	this.controller.DisbleTrading(symbol)
	this.SendMsgf("禁用成功。")
	return true
}

type OrderGatewayCommand GatewayCommand

func NewOrderGatewayCommand(controller *GatewayServer) *OrderGatewayCommand {
	cmd := &OrderGatewayCommand{
		Command: command.Command{
			Name:            "orders",
			Alias:           []string{"o"},
			Instruction:     "`.orders IDs summary[可选]` 查询订单",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *OrderGatewayCommand) Do() bool {
	ids := strings.Split(this.Args[0], ",")

	orders, err := this.controller.client.QueryOrdersByID(ids)
	if err != nil {
		this.ErrorMsgf("获取订单失败")
		return false
	}

	if len(this.Args) == 2 && strings.EqualFold(this.Args[1], "summary") {
		this.SendMsgf("```%s```", this.controller.GetOrdersSummary(orders))
		return true
	}

	for _, order := range orders {
		this.SendMsgf("订单 [%s]\n```%s```", order.OrderID, spewToTable(order, false))
	}
	if len(orders) == 0 {
		this.SendMsgf("无订单")
	}
	return true
}

type PositionsGatewayCommand GatewayCommand

func NewPositionsGatewayCommand(controller *GatewayServer) *PositionsGatewayCommand {
	cmd := &PositionsGatewayCommand{
		Command: command.Command{
			Name:            "positions",
			Alias:           []string{"p"},
			Instruction:     "`.positions` 查看持仓",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *PositionsGatewayCommand) Do() bool {
	positions, err := this.controller.client.GetPositions("")
	if err != nil {
		this.ErrorMsgf("获取持仓失败，error: %s", err)
		return false
	}

	noPosition := true
	for _, position := range positions {
		if position.Qty != 0 {
			this.SendMsgf("持仓 [%s]\n```%s```", position.Symbol, toTable(position, false))
			noPosition = false
		}
	}
	if noPosition {
		this.SendMsgf("当前无持仓")
	}
	return true
}

type GetAccountGatewayCommand GatewayCommand

func NewGetAccountGatewayCommand(controller *GatewayServer) *GetAccountGatewayCommand {
	cmd := &GetAccountGatewayCommand{
		Command: command.Command{
			Name:            "account",
			Alias:           []string{"a"},
			Instruction:     "`.account` 查看账户",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *GetAccountGatewayCommand) Do() bool {
	userMargin, err := this.controller.client.GetUserMargin()
	if err != nil {
		this.ErrorMsgf("获取 CTP 账户数据失败，error: %s", err)
		return false
	}

	this.SendMsgf("账户 [%s]\n```%s```", this.controller.client.GetUserID(), spewToTable(userMargin, false))
	return true
}

type GetInstrumentsGatewayCommand GatewayCommand

func NewGetInstrumentsGatewayCommand(controller *GatewayServer) *GetInstrumentsGatewayCommand {
	cmd := &GetInstrumentsGatewayCommand{
		Command: command.Command{
			Name:            "instruments",
			Alias:           []string{"i"},
			Instruction:     "`.instruments InstrumentIDs` 查看品种",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *GetInstrumentsGatewayCommand) Do() bool {
	instrumentIDs := strings.Split(strings.ToUpper(this.Args[0]), ",")
	if !this.controller.InstrumentsReady.Load() {
		this.SendMsgf("品种数据没有就绪。")
		return false
	}
	count := 0
	this.controller.Instruments.Range(func(instrumentID string, instrument *Instrument) bool {
		if SliceContains(instrumentIDs, instrumentID) {
			count += 1
			this.SendFileMessage(fmt.Sprintf("品种 [%s]", instrumentID), toTable(instrument, false), "")
		}
		return true
	})
	if count == 0 {
		this.SendMsgf("没有找到品种 %s 的数据。", instrumentIDs)
		return true
	}
	return true
}

type ConfigGatewayCommand GatewayCommand

func NewConfigGatewayCommand(controller *GatewayServer) *ConfigGatewayCommand {
	cmd := &ConfigGatewayCommand{
		Command: command.Command{
			Name:            "config",
			Alias:           []string{"cfg"},
			Instruction:     "`.config` 查看配置",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ConfigGatewayCommand) Do() bool {
	t := exchange.NewTable()
	t.SetHeader([]string{"Config", "Value"})

	checkSign := fmt.Sprintf("*%v*", this.controller.opts.CheckSign)
	if this.controller.opts.CheckSign {
		checkSign = fmt.Sprintf("*[%v]*", this.controller.opts.CheckSign)
	}
	t.AddRow([]string{"CheckSign", checkSign})
	t.AddRow([]string{"UserID", this.controller.client.GetUserID()})
	t.AddRow([]string{"TradingSymbols", SliceStringJoin(this.controller.storage.TradingSymbols, ",", false)})

	this.SendMsgf("配置\n```%s```", t.Render())
	return true
}

type StatusGatewayCommand GatewayCommand

func NewStatusGatewayCommand(controller *GatewayServer) *StatusGatewayCommand {
	cmd := &StatusGatewayCommand{
		Command: command.Command{
			Name:            "status",
			Alias:           []string{"s"},
			Instruction:     "`.status` 查看运行状态",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *StatusGatewayCommand) Do() bool {
	t := exchange.NewTable()

	terminalInfo, terminalErr := this.controller.client.GetTerminalInfo()
	if terminalErr != nil {
		this.ErrorMsgf("获取终端信息失败，error: %s", terminalErr)
	}

	t.AddRow([]string{"Port", this.controller.opts.HTTPAddr})
	t.AddRow([]string{"Launched", fmt.Sprintf("%v", this.controller.Launched.Load())})
	t.AddRow([]string{"LaunchTime", utils.FormatShortTimeStr(this.controller.launchTime, false)})
	t.AddRow([]string{"InstrumentsReady", fmt.Sprintf("%v", this.controller.InstrumentsReady.Load())})
	t.AddRow([]string{"Config.FromPath", this.controller.opts.loadedFromPath})
	t.AddRow([]string{"Config.TimeZone", Timezone})
	t.AddRow([]string{"TradingSymbols", SliceStringJoin(this.controller.client.GetTradingSymbols(), ", ", false)})
	t.AddRow([]string{"", ""})
	t.AddRow([]string{"Adapter.StartTime", utils.FormatShortTimeStr(this.controller.client.GetOwnedProcess(Adapter).StartTime, false)})
	t.AddRow([]string{"Adapter.ScriptPath", this.controller.opts.AdapterScriptPath})
	t.AddRow([]string{"Adapter.Port", fmt.Sprintf("%d", this.controller.opts.AdapterPort)})
	t.AddRow([]string{"Adapter.PID", fmt.Sprintf("%v", this.controller.client.GetOwnedProcess(Adapter).PID)})

	t.AddRow([]string{"", ""})
	if terminalErr == nil {
		t.AddRow([]string{"Terminal.Name", fmt.Sprintf("%v", terminalInfo.Name)})
		t.AddRow([]string{"Terminal.Connected", fmt.Sprintf("%v", terminalInfo.Connected)})
		t.AddRow([]string{"Terminal.Ping", fmt.Sprintf("%v", terminalInfo.GetPingDisplay())})
		t.AddRow([]string{"Terminal.TradeAllowed", fmt.Sprintf("%v", terminalInfo.TradeAllowed)})
		t.AddRow([]string{"Terminal.AutoRestartTime", fmt.Sprintf("%v", terminalInfo.AutoRestartTime)})
	} else {
		t.AddRow([]string{"Terminal.Error", "get terminal info failed"})
	}

	t.AddRow([]string{"", ""})
	if this.controller.platform == IB {
		t.AddRow([]string{"IB.Username", this.controller.opts.IB.Username})
		t.AddRow([]string{"IB.TerminalPort", fmt.Sprintf("%d", this.controller.opts.IB.Port)})
		t.AddRow([]string{"IB.CommandServerPort", fmt.Sprintf("%d", this.controller.opts.IB.CommandServerPort)})
	} else if this.controller.platform == MT5 {
		t.AddRow([]string{"MT5.UserID", this.controller.opts.MT.UserID})
		t.AddRow([]string{"MT5.Server", this.controller.opts.MT.Server})
	}

	this.SendMsgf("Build: %s\n```%s```", this.controller.BuildInfo(), t.Render())

	if this.controller.launchTime != nil && time.Since(*this.controller.launchTime) > 1*time.Minute {
		status := formatOwnedProcessStatus([]*OwnedProcess{this.controller.client.GetOwnedProcess(Adapter), this.controller.client.GetOwnedProcess(Terminal), this.controller.client.GetOwnedProcess(Watchdog)})
		this.SendMsgf("进程状态: \n```%s```", status)
	} else {
		this.SendMsgf("进程状态: \n```[not checking process status in the start cooltime: %s]```", HEALTH_CHECK_COOLDOWN)
	}
	return true
}

type SwapRateGatewayCommand GatewayCommand

func NewSwapRateGatewayCommand(controller *GatewayServer) *SwapRateGatewayCommand {
	cmd := &SwapRateGatewayCommand{
		Command: command.Command{
			Name:            "swapRate",
			Instruction:     "`.swapRate symbol` 查询 Swap 资金费率历史",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *SwapRateGatewayCommand) Do() bool {
	symbol := strings.ToUpper(this.Args[0])
	archivePath := fmt.Sprintf("swap_rate_%s.json", symbol)
	archivePath = path.Join(this.controller.opts.DataDir, archivePath)
	count := 0
	limit := 100
	records := []*SwapRate{}
	err := backscanner.BackScan(archivePath, func(line []byte) bool {
		if !strings.Contains(string(line), "{") {
			return true
		}
		rate := &SwapRate{}
		err := json.Unmarshal(line, rate)
		if err != nil {
			zlog.Errorf("unmarshal swap rate line failed, error: %s", err)
			// 读取出错跳过，而不是终止
			return true
		}
		rate.LongInUSD = rate.Long * rate.QuoteToSettleRate
		rate.ShortInUSD = rate.Short * rate.QuoteToSettleRate
		records = append(records, rate)
		count += 1
		return count < limit
	})
	if err != nil {
		this.ErrorMsgf("读取 Swap 历史记录出错，error: %s", err)
		return false
	}

	if count > 0 {
		t := exchange.NewTable()
		t.SetHeader([]string{"Symbol", "Long", "Short", "SwapMode", "Base Curr.", "Quote Curr.", "Settle Curr.", "Quote Settle Rate", "Long USD", "Short USD", "Record Time"})
		for _, r := range records {
			t.AddRow([]string{
				r.Symbol,
				fmt.Sprintf("%.2f", r.Long),
				fmt.Sprintf("%.2f", r.Short),
				fmt.Sprintf("%d", r.SwapMode),
				r.BaseCurrency,
				r.QuoteCurrency,
				r.SettleCurrency,
				fmt.Sprintf("%.3f", r.QuoteToSettleRate),
				fmt.Sprintf("%.2f", r.LongInUSD),
				fmt.Sprintf("%.2f", r.ShortInUSD),
				utils.FormatShortTimeStr(ptr(r.RecordTime), false),
			})
		}
		this.SendMsgf("Swap Rate 历史记录：\n```%s```", t.Render())
		return true
	} else {
		this.SendMsgf("没有 Swap Rate 的历史记录。")
		return false
	}
}

type PositionHistoryGatewayCommand GatewayCommand

func NewPositionHistoryGatewayCommand(controller *GatewayServer) *PositionHistoryGatewayCommand {
	cmd := &PositionHistoryGatewayCommand{
		Command: command.Command{
			Name:            "positionHistory",
			Instruction:     "`.positionHistory symbol positionID[可选]` 查询持仓历史",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *PositionHistoryGatewayCommand) Do() bool {
	positionID := ""
	symbol := strings.ToUpper(this.Args[0])
	if len(this.Args) > 1 {
		positionID = this.Args[1]
	}

	archivePath := fmt.Sprintf("position_history_%s.json", symbol)
	archivePath = path.Join(this.controller.opts.DataDir, archivePath)
	count := 0
	limit := 100
	records := []*PositionHistory{}
	err := backscanner.BackScan(archivePath, func(line []byte) bool {
		if !strings.Contains(string(line), "{") {
			return true
		}
		pos := &PositionHistory{}
		err := json.Unmarshal(line, pos)
		if err != nil {
			zlog.Errorf("unmarshal position history line failed, error: %s", err)
			return false
		}
		if positionID == "" || (positionID != "" && positionID == pos.ID) {
			records = append(records, pos)
			count += 1
		}

		return count < limit
	})
	if err != nil {
		this.ErrorMsgf("读取持仓历史记录出错，error: %s", err)
		return false
	}

	if count > 0 {
		t := exchange.NewTable()
		t.SetHeader([]string{"Symbol", "ID", "Qty", "Price", "Last Price", "Unr. PNL", "Swap", "Update Time", "Record Time"})
		for _, r := range records {
			t.AddRow([]string{
				r.Symbol,
				r.ID,
				fmt.Sprintf("%.2f", r.Qty),
				fmt.Sprintf("%.5f", r.Price),
				fmt.Sprintf("%.5f", r.LastPrice),
				fmt.Sprintf("%.2f", r.UnrealizedPNL),
				fmt.Sprintf("%.2f", r.Swap),
				utils.FormatShortTimeStr(ptr(r.UpdateTime), false),
				utils.FormatShortTimeStr(ptr(r.RecordTime), false),
			})
		}
		this.SendMsgf("仓位 Swap 费用历史记录：\n```%s```", t.Render())
		return true
	} else {
		this.SendMsgf("没有持仓 Swap 费用的历史记录。")
		return false
	}
}

type OpenOrdersGatewayCommand GatewayCommand

func NewOpenOrdersGatewayCommand(controller *GatewayServer) *OpenOrdersGatewayCommand {
	cmd := &OpenOrdersGatewayCommand{
		Command: command.Command{
			Name:            "openOrders",
			Alias:           []string{"oo"},
			Instruction:     "`.openOrders InstrumentID summary[可选]` 查询挂单",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          2,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *OpenOrdersGatewayCommand) Do() bool {
	instrumentID := strings.ToUpper(this.Args[0])

	orders, err := this.controller.client.GetOpenOrders(instrumentID, exchange.UnknownOrderType)
	if err != nil {
		this.ErrorMsgf("查询挂单订单出错，error: %s", err)
		return false
	}

	if len(this.Args) == 2 && strings.EqualFold(this.Args[1], "summary") {
		this.SendMsgf("```%s```", this.controller.GetOrdersSummary(orders))
		return true
	}

	for _, order := range orders {
		this.SendMsgf("挂单 [%s]\n```%s```", order.OrderID, spewToTable(order, false))
	}
	if len(orders) == 0 {
		this.SendMsgf("本地无挂单")
	}
	return true
}

type CancelAllOrdersGatewayCommand struct {
	GatewayCommand
	Orders []*Order
}

func NewCancelAllOrdersGatewayCommand(controller *GatewayServer) *CancelAllOrdersGatewayCommand {
	cmd := &CancelAllOrdersGatewayCommand{
		GatewayCommand: GatewayCommand{
			Command: command.Command{
				Name:            "cancelAllOrders",
				Instruction:     "`.cancelAllOrders` 撤销所有订单，仅调试模式下可用",
				RequiresConfirm: true,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
		Orders: []*Order{},
	}
	return cmd
}

func (this *CancelAllOrdersGatewayCommand) Prepare() bool {
	this.Orders = []*Order{}
	if !this.controller.opts.Debug {
		this.ErrorMsgf("该功能仅调试开模式下可用")
		return false
	}
	orders, err := this.controller.client.GetOpenOrders("", exchange.UnknownOrderType)
	if err != nil {
		this.ErrorMsgf("查询挂单订单出错，error: %s", err)
		return false
	}
	this.SendMsgf("所有挂单\n\n```%s```", this.controller.GetOrdersSummary(orders))
	this.Orders = orders
	return true
}

func (this *CancelAllOrdersGatewayCommand) Do() bool {
	for _, order := range this.Orders {
		this.controller.client.CancelOrder(order.OrderID)
		this.SendMsgf("已撤销挂单: %s %s [%s]", order.Symbol, order.Side, order.OrderID)
	}
	return true
}

type ListProcessGatewayCommand GatewayCommand

func NewListProcessGatewayCommand(controller *GatewayServer) *ListProcessGatewayCommand {
	cmd := &ListProcessGatewayCommand{
		Command: command.Command{
			Name:            "listProcess",
			Alias:           []string{"lp"},
			Instruction:     "`.listProcess query/{#pid}/{:port}[可选]` 查询服务器上的进程",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *ListProcessGatewayCommand) Do() bool {
	var query string
	if len(this.Args) > 0 {
		query = this.Args[0]
	}

	processes, err := findProcess(query)
	if err != nil {
		this.ErrorMsgf("查询进程出错，error: %s", err)
		return false
	}

	t := exchange.NewTable()
	t.SetHeader([]string{"PID", "Parent PID", "Executable", "Port"})
	for _, p := range processes {
		exec := p.Executable()
		if p.Pid() == this.controller.client.GetOwnedProcess(Adapter).PID {
			exec = fmt.Sprintf("%s [Current Adapter]", exec)
		}
		row := []string{fmt.Sprintf("%d", p.Pid()), fmt.Sprintf("%d", p.PPid()), exec, p.FormatPort()}
		t.AddRow(row)
	}
	if len(t.Rows) > 1 {
		if len(t.Rows) > 15 {
			this.SendFileMessage("进程列表", t.Render(), "")
		} else {
			this.SendMsgf("进程列表:\n```%s```", t.Render())
		}
		return true
	}
	this.ErrorMsgf("没有找到进程")
	return true
}

type KillProcessGatewayCommand struct {
	GatewayCommand
	foundProcesses []*WrappedProcess
}

func NewKillProcessGatewayCommand(controller *GatewayServer) *KillProcessGatewayCommand {
	cmd := &KillProcessGatewayCommand{
		GatewayCommand: GatewayCommand{
			Command: command.Command{
				Name:            "killProcess",
				Alias:           []string{"kill"},
				Instruction:     "`.killProcess query/{#PID}/{:port}` 杀死进程",
				RequiresConfirm: true,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
	return cmd
}

func (this *KillProcessGatewayCommand) Prepare() bool {
	this.foundProcesses = []*WrappedProcess{}
	arg := this.Args[0]

	var err error
	this.foundProcesses, err = findProcess(arg)
	if err != nil {
		this.ErrorMsgf("查询进程出错，error: %s", err)
		return false
	}
	t := exchange.NewTable()
	t.SetHeader([]string{"PID", "Parent PID", "Executable", "Port"})
	for _, wp := range this.foundProcesses {
		t.AddRow([]string{fmt.Sprintf("%d", wp.Pid()), fmt.Sprintf("%d", wp.PPid()), wp.Executable(), wp.FormatPort()})
	}
	this.SendMsgf("即将杀死进程:\n```%s```\n\n杀死进程可能导致严重的系统错误，请谨慎操作。", t.Render())
	return true
}

func (this *KillProcessGatewayCommand) Do() bool {
	for _, wp := range this.foundProcesses {
		this.SendMsgf("正在停止进程: [%d]", wp.Pid())
		err := this.controller.KillProcess(wp.Pid())
		if err != nil {
			this.ErrorMsgf("停止进程时碰到错误，error: %s", err)
			return false
		}
		this.SendMsgf("进程已经停止")
	}
	return true
}

type RestartTerminalGatewayCommand GatewayCommand

func NewRestartTerminalGatewayCommand(controller *GatewayServer) *RestartTerminalGatewayCommand {
	cmd := &RestartTerminalGatewayCommand{
		Command: command.Command{
			Name:            "restartTerminal",
			Alias:           []string{"rt"},
			Instruction:     "`.restartTerminal authCode` 重启 Terminal 进程",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
		controller: controller,
	}
	return cmd
}

func (this *RestartTerminalGatewayCommand) Do() bool {
	this.SendMsgf("重启 %s Terminal", this.controller.client.GetPlatform())
	err := this.controller.client.RestartOwnedProcess(Terminal)
	if err != nil {
		this.ErrorMsgf("重启 %s Terminal 时碰到错误，error: %s", this.controller.client.GetPlatform(), err)
		return false
	}

	if this.controller.client.GetPlatform() == IB {
		this.SendMsgf("重启指令发送成功，将会在 1 分钟后重启...")
		time.Sleep(60 * time.Second)
	} else {
		this.SendMsgf("%s Terminal 已经重启", this.controller.client.GetPlatform())
	}
	return true
}

type RestartServiceGatewayCommand GatewayCommand

func NewRestartServiceGatewayCommand(controller *GatewayServer) *RestartServiceGatewayCommand {
	cmd := &RestartServiceGatewayCommand{
		Command: command.Command{
			Name:            "restartService",
			Alias:           []string{"restart"},
			Instruction:     "`.restartService authCode` 重启服务进程",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     1,
		},
		controller: controller,
	}
	return cmd
}

func (this *RestartServiceGatewayCommand) Do() bool {
	this.SendMsgf("重启 %s 服务", this.controller.client.GetPlatform())
	err := this.controller.client.RestartService()
	if err != nil {
		this.ErrorMsgf("重启 %s 服务时碰到错误，error: %s", this.controller.client.GetPlatform(), err)
		return false
	}

	time.Sleep(2 * time.Second) // 减少等待感觉
	this.SendMsgf("%s 服务进程已停止，请等待服务自动重启", this.controller.client.GetPlatform())
	return true
}

type SendTradingStatusGatewayCommand GatewayCommand

func NewSendTradingStatusGatewayCommand(controller *GatewayServer) *SendTradingStatusGatewayCommand {
	cmd := &SendTradingStatusGatewayCommand{
		Command: command.Command{
			Name:            "tradingStatus",
			Alias:           []string{"ts"},
			Instruction:     "`.tradingStatus` 打印品种交易状态",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *SendTradingStatusGatewayCommand) Do() bool {
	this.controller.SendTradingStatus()
	return true
}

func (this *GatewayServer) GetOrdersSummary(orders []*Order) string {
	summary := "[no order summary]"
	t := exchange.NewTable()
	t.SetHeader([]string{"OrderID", "Symbol", "Type", "Side", "Qty", "Price", "TriggerPrice", "Status", "ExecQty", "ExecPrice", "CreateTime", "UpdateTime"})
	sort.SliceStable(orders, func(i int, j int) bool {
		return orders[i].CreateTime.Before(*orders[j].CreateTime)
	})

	for _, order := range orders {
		row := []string{}
		row = append(row, order.OrderID)
		row = append(row, order.Symbol)
		row = append(row, string(order.Type))
		if order.ReduceOnly {
			row = append(row, fmt.Sprintf("%s / Reduce", order.Side))
		} else {
			row = append(row, string(order.Side))
		}
		row = append(row, fmt.Sprintf("%.f", order.Qty))
		row = append(row, fmt.Sprintf("%.2f", order.Price))
		row = append(row, fmt.Sprintf("%.2f", order.TriggerPrice))
		row = append(row, string(order.Status))
		row = append(row, fmt.Sprintf("%.f", order.ExecQty))
		row = append(row, fmt.Sprintf("%.f", order.ExecPrice))
		row = append(row, FormatShortTimeStr(order.CreateTime, false))
		row = append(row, FormatShortTimeStr(order.UpdateTime, false))
		t.AddRow(row)
	}
	if len(t.Rows) > 1 {
		summary = t.Render()
	}
	return summary
}

func (this *GatewayServer) SendTradingStatus() {
	status := "[no trading symbols]"
	t := exchange.NewTable()
	t.SetHeader([]string{"Symbol", "Is Trading", "LastPrice", "LastPriceTime", "Update Time"})
	for _, symbol := range this.storage.TradingSymbols {
		isTrading := ""
		updateTime := "--"
		lastPrice := "0"
		lastPriceTime := "--"
		instrument, err := this.GetInstrument(symbol)
		if err != nil {
			isTrading = fmt.Sprintf("[error!] get instrument failed, error: %s", err)
		} else {
			if instrument.Status == exchange.InstrumentStatusContinuous {
				isTrading = "Yes"
			}
			updateTime = utils.FormatShortTimeStr(&instrument.UpdateTime, false)
			if instrument.LastPrice > 0 {
				lastPrice = fmt.Sprintf("%.2f", instrument.LastPrice)
				lastPriceTime = utils.FormatShortTimeStr(&instrument.LastPriceUpdateTime, false)
			}
		}
		t.AddRow([]string{symbol, isTrading, lastPrice, lastPriceTime, updateTime})
	}
	if len(t.Rows) > 1 {
		status = t.Render()
	}
	this.SendMsgf("Trading Status:\n```%s```", status)
}

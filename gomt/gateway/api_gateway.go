package gateway

import (
	"io/ioutil"
	"runtime"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

func (this *GatewayServer) adapterCallbackHandler(ctx *gin.Context) {
	body, err := ioutil.ReadAll(ctx.Request.Body)
	if err != nil {
		failJSON(ctx, "read request body failed, error: %s", err)
		return
	}
	zlog.Debugf("adapter callback: %s", body)
	if err := this.client.HandleAdapterCallback(string(body)); err != nil {
		zlog.Errorf("handle adapter callback err: %s", err)
		failJSON(ctx, "handle callback err: %s", err)
		return
	}
	okJSON(ctx, "Success")
}

func (this *GatewayServer) getInstrumentsHandler(ctx *gin.Context) {
	var form GetInstrumentsForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args, error: %s", err)
		return
	}
	instruments := []*Instrument{}
	if !this.InstrumentsReady.Load() {
		failJSON(ctx, "instruments not ready")
		return
	}
	this.Instruments.Range(func(k string, instrument *Instrument) bool {
		if form.InstrumentID != "" {
			if strings.EqualFold(form.InstrumentID, instrument.Symbol) {
				instruments = append(instruments, instrument)
			}
		} else {
			instruments = append(instruments, instrument)
		}
		return true
	})
	okJSON(ctx, instruments)
}

func (this *GatewayServer) getOpenOrdersHandler(ctx *gin.Context) {
	var form GetOpenOrdersForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args, error: %s", err)
		return
	}
	orders, err := this.client.GetOpenOrders(form.InstrumentID, exchange.OrderType(form.OrderType))
	if err != nil {
		failJSON(ctx, "get orders failed, error: %s", err)
		return
	}
	okJSON(ctx, orders)
}

func (this *GatewayServer) getOrdersHandler(ctx *gin.Context) {
	var form GetOrdersForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args, error: %s", err)
		return
	}
	orderIDs := strings.Split(form.OrderIDs, ",")
	orders, err := this.client.QueryOrdersByID(orderIDs)
	if err != nil {
		failJSON(ctx, "query orders by ids failed, error: %s", err)
		return
	}
	okJSON(ctx, orders)
}

func (this *GatewayServer) getLastPriceHandler(ctx *gin.Context) {
	var form GetLastPriceForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args")
		return
	}
	price, updateTime, err := this.client.GetLastPrice(form.InstrumentID)
	if err != nil {
		failJSON(ctx, "get last price failed, error: %s", err)
		return
	}
	okJSON(ctx, LastPriceResponse{Price: price, UpdateTime: updateTime})
}

func (this *GatewayServer) getAccountBalancesHandler(ctx *gin.Context) {
	balances, err := this.client.GetAccountBalances()
	if err != nil {
		failJSON(ctx, "get account balance failed, error: %s", err)
		return
	}
	okJSON(ctx, balances)
}

func (this *GatewayServer) getUserMarginHandler(ctx *gin.Context) {
	margin, err := this.client.GetUserMargin()
	if err != nil {
		failJSON(ctx, "get user margin failed, error: %s", err)
		return
	}
	okJSON(ctx, margin)
}

func (this *GatewayServer) getPositionsHandler(ctx *gin.Context) {
	var form GetPositionsForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args, error: %s", err)
		return
	}

	positions, err := this.client.GetPositions(form.InstrumentID)
	if err != nil {
		failJSON(ctx, "get positions failed, error: %s", err)
		return
	}
	okJSON(ctx, positions)
}

// 创建订单，支持 “条件单”， “限价单”和“市价单"
func (this *GatewayServer) createOrderHandler(ctx *gin.Context) {
	args := &exchange.CreateOrderArgs{}
	if err := ctx.ShouldBindJSON(args); err != nil {
		badRequest(ctx, "invalid args, error: %s", err)
		return
	}

	if args.Type != exchange.Market && args.Price == 0 {
		badRequest(ctx, "limit order price can not be 0")
		return
	}

	order, err := this.client.CreateOrder(args)
	if err != nil {
		badRequest(ctx, "create order failed, error: %s", err)
		return
	}

	okJSON(ctx, order)
}

// 取消订单，支持 “条件单” 和 “限价单”
func (this *GatewayServer) cancelOrderHandler(ctx *gin.Context) {
	var form CancelOrderForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args, error: %s", err)
		return
	}

	order, err := this.client.CancelOrder(form.OrderID)
	if err != nil {
		badRequest(ctx, "cancel order failed, error: %s", err)
		return
	}
	okJSON(ctx, order)
}

type Stats struct {
	Alloc        uint64
	TotalAlloc   uint64
	Sys          uint64
	NumGC        uint32
	PauseTotalNs uint64
	UpdateTime   time.Time
}

func (this *GatewayServer) getStatsHandler(ctx *gin.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	// For info on each, see: https://golang.org/pkg/runtime/#MemStats

	s := Stats{}
	s.Alloc = b2m(m.Alloc)
	s.TotalAlloc = b2m(m.TotalAlloc)
	s.Sys = b2m(m.Sys)
	s.NumGC = m.NumGC
	s.UpdateTime = time.Now()
	s.PauseTotalNs = m.PauseTotalNs
	okJSON(ctx, s)
}

func b2m(b uint64) uint64 {
	return b / 1024 / 1024
}

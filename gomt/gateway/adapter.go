package gateway

import (
	"context"
	_ "embed"
	"fmt"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"
	"github.com/wizhodl/quanter/common/rate"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"go.uber.org/atomic"
)

// 每 20 秒检查一次，3 分钟内总共可以检查 9 次
// 如果 3 分钟内 6 次失败，重启 Terminal 和 Adapter
const HEALTH_CHECK_FAILS_LIMIT = 6
const HEALTH_CHECK_FAILS_DURATION = 3 * time.Minute
const HEALTH_CHECK_COOLDOWN = 2 * time.Minute

const ADAPTER_AUTO_RECOVER_FAILS_LIMIT = 4 // 实际是 3 次，因为第一次并没有实际重启
const ADAPTER_AUTO_RECOVER_FAILS_DURATION = 3 * time.Minute

//go:embed mt5.py
var mt5AdapterScriptStr string

//go:embed ib.py
var ibAdapterScriptStr string

type Platform string

const UnknownPlatform Platform = ""
const MT5 Platform = "MT5"
const IB Platform = "IB"

func (p Platform) ToLower() string {
	return strings.ToLower(string(p))
}

func (p Platform) String() string {
	return string(p)
}

func (p Platform) ToUpper() string {
	return strings.ToUpper(string(p))
}

type ProcessType string

const Adapter ProcessType = "adapter"
const Terminal ProcessType = "terminal"
const Watchdog ProcessType = "watchdog"

type OwnedProcess struct {
	Type    ProcessType
	CmdLine string
	Port    int // 进程占用的端口，不管是否运行，这个值都存在
	// 以下是进程运行状态
	Running    *atomic.Bool
	Cmd        *exec.Cmd
	CancelFunc context.CancelFunc
	PID        int
	StartTime  *time.Time
	// 重启进程时，防止同时重启多个进程
	RestartMutex sync.Mutex
	RestartWait  time.Duration
	// 自动恢复失败次数限制
	AutoRecoverFailsLimiter *rate.DurationLimiter
	// 自动恢复失败次数达到限制后，是否停止自动恢复
	// 如果停止自动恢复，则需要手动用 .restartAdapter 命令重启
	AutoRecoverStop bool
}

func (p *OwnedProcess) ParseCmdArgs() []string {
	if p.IsDummy() {
		return []string{}
	}
	parts := splitExecutableAndArgs(p.CmdLine)
	return parts
}

func splitExecutableAndArgs(cmdLine string) []string {
	cmdLine = strings.ReplaceAll(cmdLine, "\\", "/")
	// Find the last occurrence of "/"
	lastSlashIndex := strings.LastIndex(cmdLine, "/")

	if lastSlashIndex == -1 {
		// If no "/" found, fall back to splitting on the first space
		parts := strings.SplitN(cmdLine, " ", 2)
		if len(parts) == 1 {
			return []string{filepath.Clean(parts[0])}
		}
		newParts := []string{}
		newParts = append(newParts, filepath.Clean(parts[0]))
		newParts = append(newParts, strings.Fields(parts[1])...)
		return newParts
	}

	// Find the first space after the last "/"
	spaceIndex := strings.Index(cmdLine[lastSlashIndex:], " ")

	if spaceIndex == -1 {
		// If no space found after the last "/", the entire string is the executable
		return []string{filepath.Clean(cmdLine)}
	}

	// Split the command line
	exePath := cmdLine[:lastSlashIndex+spaceIndex]
	argsString := strings.TrimSpace(cmdLine[lastSlashIndex+spaceIndex:])

	parts := []string{filepath.Clean(exePath)}
	parts = append(parts, strings.Fields(argsString)...)
	return parts
}

func (p *OwnedProcess) ExeFilename() string {
	return filepath.Base(p.ExePath())
}

func (p *OwnedProcess) ExePath() string {
	parts := p.ParseCmdArgs()
	if len(parts) == 0 {
		return ""
	}
	return parts[0]
}

func (p *OwnedProcess) ExeDir() string {
	return filepath.Dir(p.ExePath())
}

// 是否是 dummy 进程，dummy 进程没有实际的进程，只是为了占位
func (p *OwnedProcess) IsDummy() bool {
	return p.CmdLine == "" && p.Port == 0
}

func (this *OwnedProcess) StopAutoRecover() {
	this.AutoRecoverStop = true
}

func (this *OwnedProcess) ResetAutoRecover() {
	this.AutoRecoverFailsLimiter.Reset()
	this.AutoRecoverStop = false
}
func (p *OwnedProcess) ChangePID(pid int) {
	p.PID = pid
	p.Cmd = nil
	p.CancelFunc = nil
	p.StartTime = nil
}

func (p *OwnedProcess) ReadableName() string {
	if p.Type == Terminal {
		return "终端"
	} else if p.Type == Watchdog {
		return "看门狗"
	} else if p.Type == Adapter {
		return "适配器"
	}
	return ""
}

func (p *OwnedProcess) SetRunning(running bool) {
	p.Running.Store(running)
	if running {
		if p.StartTime == nil {
			p.StartTime = ptr(time.Now())
		}
	} else {
		p.StartTime = nil
		p.PID = 0
		p.Cmd = nil
		p.CancelFunc = nil
	}
}

func formatOwnedProcessStatus(processes []*OwnedProcess) string {
	t := exchange.NewTable()
	t.SetHeader([]string{"Process Type", "Exe Path", "Port", "PID", "Start Time", "Status"})

	for _, op := range processes {
		row := []string{}
		row = append(row, string(op.Type))
		row = append(row, op.CmdLine)
		row = append(row, cast.ToString(op.Port))
		row = append(row, cast.ToString(op.PID))
		row = append(row, FormatShortTimeStr(op.StartTime, false))
		row = append(row, cast.ToString(op.Running.Load()))
		t.AddRow(row)
	}
	if len(t.Rows) > 1 {
		return t.Render()
	}
	return "[No Process]"
}

type BaseClient struct {
	Adaptable

	platform    Platform
	userID      string
	gateway     *GatewayServer
	isConnected *atomic.Bool
	http        *resty.Client
	adapter     *OwnedProcess
	terminal    *OwnedProcess
	watchdog    *OwnedProcess

	restartServiceMutex sync.Mutex

	healthCheckLimiter *rate.DurationLimiter
}

func (this *BaseClient) IsConnected() bool {
	return this.isConnected.Load()
}

func (this *BaseClient) GetPlatform() Platform {
	return this.platform
}

func (this *BaseClient) GetUserID() string {
	return this.userID
}

func (this *BaseClient) GetTradingSymbols() []string {
	return this.gateway.storage.TradingSymbols
}

func (this *BaseClient) getURL(pathFormat string, args ...any) string {
	path := fmt.Sprintf(pathFormat, args...)
	if !strings.HasPrefix(path, "/") {
		path = fmt.Sprintf("/%s", path)
	}
	return fmt.Sprintf("/%s%s", strings.ToLower(string(this.platform)), path)
}

func NewBaseClient(platform Platform, gateway *GatewayServer) *BaseClient {
	client := &BaseClient{
		platform:    platform,
		gateway:     gateway,
		isConnected: atomic.NewBool(false),
		http:        resty.New(),
	}
	if gateway.platform == IB {
		client.adapter = &OwnedProcess{
			Type:                    Adapter,
			Running:                 atomic.NewBool(false),
			Port:                    gateway.opts.AdapterPort,
			AutoRecoverFailsLimiter: rate.NewDurationLimiter(ADAPTER_AUTO_RECOVER_FAILS_LIMIT, ADAPTER_AUTO_RECOVER_FAILS_DURATION),
		}
		client.terminal = &OwnedProcess{
			Type:        Terminal,
			CmdLine:     gateway.opts.TerminalExePath,
			Port:        gateway.opts.IB.Port,
			Running:     atomic.NewBool(false),
			RestartWait: TWS_RESTART_WAIT_TIME,
		}
		client.watchdog = &OwnedProcess{
			Type:    Watchdog,
			CmdLine: gateway.opts.WatchdogExePath,
			Port:    gateway.opts.IB.CommandServerPort,
			Running: atomic.NewBool(false),
		}
	} else {
		client.adapter = &OwnedProcess{
			Type:                    Adapter,
			Running:                 atomic.NewBool(false),
			Port:                    gateway.opts.AdapterPort,
			AutoRecoverFailsLimiter: rate.NewDurationLimiter(ADAPTER_AUTO_RECOVER_FAILS_LIMIT, ADAPTER_AUTO_RECOVER_FAILS_DURATION),
		}
		client.terminal = &OwnedProcess{
			Type:        Terminal,
			CmdLine:     gateway.opts.TerminalExePath,
			Port:        0, // mt5 doesn't use tcp port to communicate
			Running:     atomic.NewBool(false),
			RestartWait: 1 * time.Minute,
		}
		// dummy watchdog
		client.watchdog = &OwnedProcess{
			Type:      Watchdog,
			CmdLine:   "",
			Port:      0,
			Running:   atomic.NewBool(true),
			StartTime: ptr(time.Now()),
		}
	}
	if gateway.opts.Debug {
		client.healthCheckLimiter = rate.NewDurationLimiter(2, 1*time.Minute)
	} else {
		client.healthCheckLimiter = rate.NewDurationLimiter(HEALTH_CHECK_FAILS_LIMIT, HEALTH_CHECK_FAILS_DURATION)
	}

	return client
}

func (this *BaseClient) GetOwnedProcess(processType ProcessType) *OwnedProcess {
	if processType == Adapter {
		return this.adapter
	} else if processType == Terminal {
		return this.terminal
	} else if processType == Watchdog {
		return this.watchdog
	}
	return nil
}

// 检查进程状态，根据 autoRestart 参数决定是否能够自动重启
// 主要还是由健康检查程序决定是否需要重启
func (this *BaseClient) checkProcessLoop() {
	if runtime.GOOS != "windows" {
		zlog.Warnf("process check loop only support windows")
		return
	}
	for {
		time.Sleep(5 * time.Second)
		if this.gateway.Launched.Load() && time.Since(*this.gateway.launchTime) > HEALTH_CHECK_COOLDOWN {
			var ownedProcesses []*OwnedProcess
			// 检查 IB 的 watchdog 进程
			// 检查 MT5 的 adapter / terminal 进程
			if this.platform == IB {
				ownedProcesses = []*OwnedProcess{this.watchdog, this.terminal, this.adapter}
			} else {
				// 如果 terminal 有问题，先重启 terminal 否则 adapter 无法启动
				ownedProcesses = []*OwnedProcess{this.terminal, this.adapter}
			}

			for _, op := range ownedProcesses {
				zlog.Debugf("checking process, type: %s, port: %d, exePath: %s", op.Type, op.Port, op.CmdLine)
				// 检查进程是否存在
				if !op.IsDummy() {
					var pid int
					var err error
					if op.Port != 0 {
						pid, err = findProcessByPort(op.Port)
						if err != nil {
							err = fmt.Errorf("find pid by port failed, error: %s", err)
							zlog.Debugf("process not found for port: %d, type: %s, error: %s", op.Port, op.Type, err)
						} else {
							zlog.Debugf("process found, type: %s, port: %d, pid: %d", op.Type, op.Port, pid)
						}
					} else {
						// 在没有配置 port 的情况下，才根据 exePath 检查
						// 适配器一定有 port，所以一定不根据 exePath 检查
						if op.Type != Adapter {
							// 如果通过 port 没有找到进程，则通过 exePath 检查
							if pid == 0 {
								if op.CmdLine != "" {
									var processes []*WrappedProcess
									processes, err = findProcess(op.ExeFilename())
									if err != nil {
										err = fmt.Errorf("find pid by path failed, error: %s", err)
										zlog.Debugf("process not found for exe name: %s, type: %s, error: %s", op.ExeFilename(), op.Type, err)
									} else {
										zlog.Debugf("process found, type: %s, exe name: %s, pid: %d", op.Type, op.ExeFilename(), processes[0].Pid())
										if len(processes) >= 1 {
											pid = processes[0].Pid()
											zlog.Debugf("process %s pid changed to %d", op.Type, pid)
										}
									}
								}
							}
						}
					}
					// 进程不存在
					if pid == 0 {
						zlog.Debugf("process not found, type: %s, error: %s", op.Type, err)
						op.SetRunning(false)
						continue
					}
					op.SetRunning(true)
					if pid != op.PID {
						op.ChangePID(pid)
					}
					// 进程存在
					zlog.Debugf("check process ok, type: %s, pid: %d", op.Type, pid)
				}
			}
			// 不用在此处理重启 adapter 和 terminal 的问题，留到 health check 处理
		}
	}
}

func (this *BaseClient) checkAdapterStatus() error {
	terminalInfo, err := this.GetTerminalInfo()
	if err != nil {
		return fmt.Errorf("read terminal info failed, error: %s", err)
	}
	if !terminalInfo.Connected {
		return fmt.Errorf("terminal not connected")
	}
	if !terminalInfo.TradeAllowed {
		return fmt.Errorf("terminal trade not allowed")
	}
	return nil
}

func (this *BaseClient) checkHealthLoop() {
	counter := 0     // 用于间隔一段时间打印警告信息
	round := 45      // 每分钟 3 次，45 次约 15 分钟；因为重启需要时间，可能稍微长一点
	alerted := false // 是否已经警告过，因为第一次需要警告时，并不一定满足 counter%round = 0 的条件
	healthCheckTicker := time.NewTicker(20 * time.Second)
	for {
		<-healthCheckTicker.C
		if this.gateway.Launched.Load() && time.Since(*this.gateway.launchTime) > 1*time.Minute {
			ok := this.checkAdapterHealth()
			counter += 1
			adapter := this.GetOwnedProcess(Adapter)
			if !ok {
				if adapter.AutoRecoverStop {
					zlog.Warnf("adapter auto recover stopped, limit: %d/%d in %s", adapter.AutoRecoverFailsLimiter.Limit, adapter.AutoRecoverFailsLimiter.GetCount(), adapter.AutoRecoverFailsLimiter.Duration)
					if !alerted || counter%round == 0 {
						this.gateway.AlertMsgf("adapter 自动恢复失败次数达到限制: %d/%d in %s，停止自动恢复", adapter.AutoRecoverFailsLimiter.GetCount(), adapter.AutoRecoverFailsLimiter.Limit, adapter.AutoRecoverFailsLimiter.Duration)
						alerted = true
					}
					continue
				}
				_, allow := adapter.AutoRecoverFailsLimiter.Allow()
				if !allow {
					zlog.Warnf("adapter auto recover fails limiter not allow")
					adapter.StopAutoRecover()
					continue
				}
				this.gateway.AlertMsgf("健康检查失败，%s 服务可能出现问题，正在尝试重启", this.platform)
				status := formatOwnedProcessStatus([]*OwnedProcess{this.GetOwnedProcess(Adapter), this.GetOwnedProcess(Terminal), this.GetOwnedProcess(Watchdog)})
				this.gateway.AlertMsgf("进程状态: \n```%s```", status)

				err := this.RestartOwnedProcess(Adapter)
				if err != nil {
					this.gateway.AlertMsgf("adapter 重启失败，error: %s", err)
				} else {
					this.gateway.AlertMsgf("adapter 已重启")
				}
			} else {
				zlog.Infof("health check ok")
				adapter.ResetAutoRecover()
				alerted = false
			}
		}
	}
}

func (this *BaseClient) RestartOwnedProcess(processType ProcessType) (er error) {
	op := this.GetOwnedProcess(processType)
	if op == nil {
		return fmt.Errorf("restart owned process failed, process is nil, type: %s", processType)
	}
	if op.IsDummy() {
		zlog.Warnf("restart process failed, process is dummy, type: %s, skip restart", op.Type)
		return
	}
	if !op.RestartMutex.TryLock() {
		zlog.Errorf("restart process failed, another restart is in progress")
		return
	}
	defer op.RestartMutex.Unlock()

	err := this.StopOwnedProcess(op.Type)
	if err != nil {
		return fmt.Errorf("stop process failed, error: %s", err)
	}

	zlog.Infof("stop process success, type: %s", op.Type)
	err = this.StartOwnedProcess(op.Type)
	if err != nil {
		return fmt.Errorf("start process failed, error: %s", err)
	}
	zlog.Infof("start process success, type: %s", op.Type)
	time.Sleep(op.RestartWait) // 等待进程完全退出
	return
}

func (this *BaseClient) StartOwnedProcess(processType ProcessType) (er error) {
	op := this.GetOwnedProcess(processType)
	if op == nil {
		return fmt.Errorf("start owned process failed, process is nil, type: %s", processType)
	}
	if op.IsDummy() {
		zlog.Warnf("start process failed, process is dummy, type: %s, skip start", op.Type)
		return
	}

	if op.Type == Adapter {
		// run adapter
		err := this.startAdapter()
		if err != nil {
			er = fmt.Errorf("start adapter failed, error: %s", err)
			return
		}
		time.Sleep(3 * time.Second) // 等待 adapter 启动
		err = this.Initialize()
		if err != nil {
			er = fmt.Errorf("adapter initialize failed, error: %s", err)
		} else {
			this.gateway.SendMsgf("adapter started for user %s, PID: #%d", this.userID, this.adapter.PID)
		}
		go this.gateway.cacheInstruments()
	} else {
		// parse exePath
		cmdArgs := op.ParseCmdArgs()
		if len(cmdArgs) == 0 {
			zlog.Warnf("start process failed, type: %s, cmd args is empty", op.Type)
			return
		}
		zlog.Debugf("start process %s, args: [%s]", op.Type, strings.Join(cmdArgs, ", ")) // 可能泄露机密信息
		cmd, cancelFunc, err := execute(cmdArgs, 0)
		if err != nil {
			er = fmt.Errorf("start process failed, type: %s, error: %s", op.Type, err)
			return
		}
		op.CancelFunc = cancelFunc
		op.PID = cmd.Process.Pid
		op.Cmd = cmd
		op.Running.Store(true)
		this.gateway.SendMsgf("start process success, type: %s, PID: #%d", op.Type, op.PID)
		return
	}
	return
}

func (this *BaseClient) checkAdapterHealth() (ok bool) {
	err := this.checkAdapterStatus()
	if err == nil {
		zlog.Infof("adapter status is ok")
		return true
	}

	_, ok = this.healthCheckLimiter.Allow()
	if !ok {
		zlog.Infof("adapter health check limiter not allow")
	}
	return
}

func (this *BaseClient) RestartService() (er error) {
	if !this.restartServiceMutex.TryLock() {
		zlog.Errorf("restart service failed, another restart is in progress")
		return
	}
	defer this.restartServiceMutex.Unlock()

	parts := strings.Split(this.gateway.opts.HTTPAddr, ":")
	if len(parts) != 2 {
		return fmt.Errorf("invalid http addr, format error")
	}
	port, err := cast.ToIntE(parts[1])
	if err != nil {
		return fmt.Errorf("parse port from http addr failed, error: %s", err)
	}
	err = killProcessOnPort(port)
	return fmt.Errorf("kill service process failed, error: %s", err)
}

func (this *BaseClient) StopOwnedProcess(processType ProcessType) (er error) {
	op := this.GetOwnedProcess(processType)
	if op == nil {
		return fmt.Errorf("kill owned process failed, process is nil, type: %s", processType)
	}
	if op.IsDummy() {
		zlog.Infof("process %s is dummy, skip kill", op.Type)
		return
	}

	if op.CancelFunc != nil {
		op.CancelFunc()
		zlog.Infof("last %s killed, PID: #%d", op.Type, op.PID)
	} else {
		if op.PID > 0 {
			err := this.gateway.KillProcess(op.PID)
			if err != nil {
				zlog.Errorf("kill process failed, type: %s, PID: #%d, error: %s", op.Type, op.PID, err)
			} else {
				zlog.Infof("process killed, type: %s, PID: #%d", op.Type, op.PID)
			}
		}
	}

	if op.Port != 0 {
		// as safe guard, try to kill process occupied port
		if err := killProcessOnPort(op.Port); err != nil {
			zlog.Infof("try to kill process on port failed, maybe already killed by cancelFunc: %s", err)
		}
	}

	op.Running.Store(false)
	op.PID = 0
	op.StartTime = nil
	op.Cmd = nil
	op.CancelFunc = nil
	return
}

func (this *BaseClient) startAdapter() (er error) {
	// 测试是否有安装 python
	realPythonPath, err := tryPython(this.gateway.opts.PythonPath)
	if err != nil {
		er = fmt.Errorf("python command not found, please set env, error: %s", err)
		return
	}

	adapterScriptStr := mt5AdapterScriptStr
	if this.platform == IB {
		adapterScriptStr = ibAdapterScriptStr
	}

	// 内嵌脚本模式
	commandArgs := []string{
		realPythonPath,
		"-c",
		adapterScriptStr,
	}

	// 调试模式下，如果设置了 adapter 脚本路径，优先使用外部脚本
	if this.gateway.opts.Debug && this.gateway.opts.AdapterScriptPath != "" && IsPathExist(this.gateway.opts.AdapterScriptPath) {
		commandArgs = []string{
			realPythonPath,
			this.gateway.opts.AdapterScriptPath,
		}
	}

	// 添加 adapter 附加参数
	extraArgs := this.GetAdapterScriptArgs()
	commandArgs = append(commandArgs, extraArgs...)

	commandStr := strings.Join(commandArgs, " ")
	// limit commandStr to max 200 chars
	if len(commandStr) > 200 {
		commandStr = commandStr[:200] + "..."
	}

	zlog.Infof("running adapter with args: %s", commandStr)

	// 执行脚本
	cmd, cancelFunc, err := execute(commandArgs, 0)
	if err != nil {
		er = fmt.Errorf("run adapter failed, error: %s", err)
		zlog.Errorf("%s", er)
		this.adapter.Running.Store(false)
	} else {
		zlog.Infof("run adapter success")
		this.adapter.StartTime = ptr(time.Now())
		this.adapter.CancelFunc = cancelFunc
		this.adapter.Running.Store(true)
	}
	// 运行出错的进程也记录 PID，方便后续杀死
	this.adapter.PID = cmd.Process.Pid
	this.adapter.Cmd = cmd
	// 保存在 storage 中，防止没有正常退出，下次启动自动杀死
	this.gateway.storage.AdapterPID = this.adapter.PID
	this.gateway.storage.Save()

	return
}

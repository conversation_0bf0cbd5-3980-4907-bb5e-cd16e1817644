package gateway

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

type MT5InitializeArgs struct {
	Path     string `json:"path"`
	Login    int    `json:"login"`
	Password string `json:"password"`
	Server   string `json:"server"`
	Timeout  int    `json:"timeout"`
	Portable bool   `json:"portable"`
}

type MT5TerminalInfo struct {
	Connected    bool   `json:"connected"`
	Name         string `json:"name"`
	PingLast     int    `json:"ping_last"`
	TradeAllowed bool   `json:"trade_allowed"`
}

type MT5AccountInfo struct {
	Login      int     `json:"login"`
	Balance    float64 `json:"balance"`
	Margin     float64 `json:"margin"`
	MarginFree float64 `json:"margin_free"`
	Equity     float64 `json:"equity"`
	Assets     float64 `json:"assets"`
	Currency   string  `json:"currency"`
	MarginMode int     `json:"margin_mode"`
	TradeMode  int     `json:"trade_mode"`
}

type AccountType string

const UnknownAccountType AccountType = ""
const AccountTypeDemo AccountType = "Demo"
const AccountTypeReal AccountType = "Real"
const AccountTypeContest AccountType = "Contest"

func (this *MT5AccountInfo) getAccountType() AccountType {
	switch this.TradeMode {
	case 0:
		return AccountTypeDemo
	case 1:
		return AccountTypeContest
	case 2:
		return AccountTypeReal
	}
	return UnknownAccountType
}

func (this *MT5AccountInfo) isHedging() bool {
	return this.MarginMode == 2
}

func (this *MT5AccountInfo) toAccountBalance() *exchange.AccountBalance {
	return &exchange.AccountBalance{
		InstrumentType: exchange.USDXMarginedFutures,
		Total:          this.Balance,
		Available:      this.MarginFree,
		Currency:       this.Currency,
	}
}

func (this *MT5AccountInfo) toUserMargin() *exchange.UserMargin {
	return &exchange.UserMargin{
		WalletBalance:   this.Equity,
		MarginBalance:   this.Balance,
		AvailableMargin: this.MarginFree,
		Currency:        this.Currency,
	}
}

type MT5SymbolInfo struct {
	Name              string  `json:"name"`
	TradeTickSize     float64 `json:"trade_tick_size"`
	TradeContractSize float64 `json:"trade_contract_size"`
	VolumeMin         float64 `json:"volume_min"`
	VolumeStep        float64 `json:"volume_step"`
	CurrencyBase      string  `json:"currency_base"`
	CurrencyProfit    string  `json:"currency_profit"`
	CurrencyMargin    string  `json:"currency_margin"`
	TradeTickValue    float64 `json:"trade_tick_value"`
	SessionVolume     float64 `json:"session_volume"`
	SessionInterest   float64 `json:"session_interest"`
	Last              float64 `json:"last"`
	Ask               float64 `json:"ask"`
	Bid               float64 `json:"bid"`
	TradeMode         int     `json:"trade_mode"`
	SwapMode          int     `json:"swap_mode"`
	SwapLong          float64 `json:"swap_long"`
	SwapShort         float64 `json:"swap_short"`
}

func (this *MT5SymbolInfo) toInstrument() *exchange.Instrument {
	nowTime := time.Now()
	if this.TradeTickValue == 0 {
		zlog.Errorf("TradeTickValue is 0, SymbolInfo: %#v", this)
	}
	return &exchange.Instrument{
		InstrumentType:      exchange.USDXMarginedFutures,
		Symbol:              this.Name,
		TickSize:            this.TradeTickSize,
		ContractSize:        this.TradeContractSize,
		MinSize:             this.VolumeMin,
		LotSize:             this.VolumeStep,
		SettleCurrency:      "USD",
		UnderlyCurrency:     this.CurrencyBase,
		QuoteCurrency:       this.CurrencyProfit,
		QuoteToSettleRate:   this.TradeTickValue / (this.TradeTickSize * this.TradeContractSize),
		Volume:              this.SessionVolume,
		OpenInterest:        this.SessionInterest,
		LastPrice:           (this.Ask + this.Bid) / 2.0, // this.Last 上看起来始终没有值，用 ask 和 bid 的平均值代替
		LastPriceUpdateTime: nowTime,
		// 默认返回已休市的状态，后续由更上层更新为正确的状态
		// 因为 python API 中没法获取到交易时间，单独有配置来处理交易时间的问题
		Status:     exchange.InstrumentStatusClosed,
		UpdateTime: nowTime,
	}
}

type MT5SymbolInfos []*MT5SymbolInfo

func (this *MT5SymbolInfos) toInstruments() (instruments []*exchange.Instrument) {
	instruments = []*exchange.Instrument{}
	for _, symbolInfo := range *this {
		instrument := symbolInfo.toInstrument()
		instruments = append(instruments, instrument)
	}
	return
}

type MT5SymbolInfoTick struct {
	TimeMsc    int64   `json:"time_msc"`
	Bid        float64 `json:"bid"`
	Ask        float64 `json:"ask"`
	Last       float64 `json:"last"`
	Volume     float64 `json:"volume"`
	Flags      int     `json:"flags"`
	VolumeReal float64 `json:"volume_real"`
}

func (this *MT5SymbolInfoTick) GetLastPrice() float64 {
	return (this.Ask + this.Bid) / 2.0
}

func (this *MT5SymbolInfoTick) GetUpdateTime() time.Time {
	return ExchangeTimeToUTC(time.UnixMilli(this.TimeMsc))
}

type MT5Position struct {
	Symbol string `json:"symbol"`
	/*
		Position ticket. Unique number assigned to each newly opened position. It usually matches the ticket of an order used
		to open the position except when the ticket is changed as a result of service operations on the server, for example,
		when charging swaps with position re-opening. To find an order used to open a position, apply the POSITION_IDENTIFIER property.
	*/
	Ticket        int64 `json:"ticket"`
	TimeMsc       int64 `json:"time_msc"`
	TimeUpdateMsc int64 `json:"time_update_msc"`
	Type          int   `json:"type"`
	Magic         int64 `json:"magic"`
	/*
		Position identifier is a unique number assigned to each re-opened position. It does not change throughout its life cycle and corresponds to the ticket of an order used to open a position.
		Position identifier is specified in each order (ORDER_POSITION_ID) and deal (DEAL_POSITION_ID) used to open, modify, or close it. Use this property to search for orders and deals related to the position.
		When reversing a position in netting mode (using a single in/out trade), POSITION_IDENTIFIER does not change. However, POSITION_TICKET is replaced with the ticket of the order that led to the reversal. Position reversal is not provided in hedging mode.
	*/
	Identifier   int64   `json:"identifier"`
	Volume       float64 `json:"volume"`
	PriceOpen    float64 `json:"price_open"`
	SL           float64 `json:"sl"`
	TP           float64 `json:"tp"`
	PriceCurrent float64 `json:"price_current"`
	Swap         float64 `json:"swap"`
	Profit       float64 `json:"profit"`
	Comment      string  `json:"comment"`
	ExternalID   string  `json:"external_id"`
}

func (this *MT5Position) GetUpdateTime() time.Time {
	return ExchangeTimeToUTC(time.UnixMilli(this.TimeUpdateMsc))
}

func (this *MT5Position) toPosition() (position *exchange.Position) {
	side := exchange.UnknownPositionSide
	switch this.Type {
	case 0:
		side = exchange.PositionSideLong
	case 1:
		side = exchange.PositionSideShort
	}
	position = &exchange.Position{
		InstrumentType: exchange.USDXMarginedFutures,
		Symbol:         this.Symbol,
		Qty:            this.Volume,
		Side:           side,
		EntryPrice:     this.PriceOpen,
		MarkPrice:      this.PriceCurrent,
		LastPrice:      this.PriceCurrent,
		UnrealisedPNL:  this.Profit,
		UpdateTime:     ptr(ExchangeTimeToUTC(time.UnixMilli(this.TimeUpdateMsc))),
	}
	return
}

type MT5Positions []*MT5Position

func (this MT5Positions) toPositions() (positions []*exchange.Position) {
	positions = []*exchange.Position{}
	for _, p := range this {
		positions = append(positions, p.toPosition())
	}
	return
}

type MT5Order struct {
	Symbol         string         `json:"symbol"`
	Ticket         int64          `json:"ticket"`
	TimeSetup      int64          `json:"time_setup"`
	TimeSetupMsc   int64          `json:"time_setup_msc"`
	TimeDoneMsc    int64          `json:"time_done_msc"`
	Type           MT5_ORDER_TYPE `json:"type"`
	TypeTime       int            `json:"type_time"`
	TypeFilling    int            `json:"type_filling"`
	State          int            `json:"state"`
	Magic          int64          `json:"magic"`
	PositionID     int64          `json:"position_id"`
	PositionByID   int64          `json:"position_by_id"`
	Reason         int            `json:"reason"`
	VolumeInitial  float64        `json:"volume_initial"`
	VolumeCurrent  float64        `json:"volume_current"`
	PriceOpen      float64        `json:"price_open"`
	SL             float64        `json:"sl"`
	TP             float64        `json:"tp"`
	PriceCurrent   float64        `json:"price_current"`
	PriceStopLimit float64        `json:"price_stoplimit"`
	Comment        string         `json:"comment"`
	ExternalID     string         `json:"external_id"`
	ExecPrice      float64        `json:"-"` // 从 deal 中查询的
}

func (this *MT5Order) getOrderSideAndType() (side exchange.OrderSide, typ exchange.OrderType) {
	side = exchange.UnknownOrderSide
	typ = exchange.UnknownOrderType
	switch this.Type {
	case MT5_ORDER_TYPE_BUY, MT5_ORDER_TYPE_BUY_LIMIT, MT5_ORDER_TYPE_BUY_STOP, MT5_ORDER_TYPE_BUY_STOP_LIMIT:
		side = exchange.OrderSideBuy
	case MT5_ORDER_TYPE_SELL, MT5_ORDER_TYPE_SELL_LIMIT, MT5_ORDER_TYPE_SELL_STOP, MT5_ORDER_TYPE_SELL_STOP_LIMIT:
		side = exchange.OrderSideSell
	case 8:
		// TODO: check
	}

	switch this.Type {
	case MT5_ORDER_TYPE_BUY, MT5_ORDER_TYPE_SELL:
		typ = exchange.Market
	case MT5_ORDER_TYPE_BUY_LIMIT, MT5_ORDER_TYPE_SELL_LIMIT:
		typ = exchange.Limit
	case MT5_ORDER_TYPE_BUY_STOP, MT5_ORDER_TYPE_SELL_STOP:
		typ = exchange.StopMarket
	case MT5_ORDER_TYPE_BUY_STOP_LIMIT, MT5_ORDER_TYPE_SELL_STOP_LIMIT:
		typ = exchange.StopLimit
	}
	return
}

func (this *MT5Order) getOrderStatus() exchange.OrderStatus {
	switch this.State {
	case 0, 1, 7, 8:
		return exchange.OrderStatusNew
	case 2:
		if this.partialFilled() {
			return exchange.OrderStatusPartialCancelled
		}
		return exchange.OrderStatusCancelled
	case 3:
		return exchange.OrderStatusPartialFilled
	case 4:
		return exchange.OrderStatusFilled
	case 5:
		return exchange.OrderStatusRejected
	case 6:
		if this.partialFilled() {
			return exchange.OrderStatusPartialCancelled
		}
		return exchange.OrderStatusCancelled
	case 9:
		return exchange.OrderStatusCancelled
	}
	return exchange.UnknownOrderStatus
}

func (this *MT5Order) partialFilled() bool {
	return this.VolumeCurrent != this.VolumeInitial
}

func (this *MT5Order) toOrder() *Order {
	side, typ := this.getOrderSideAndType()
	tif := exchange.GTC
	switch this.TypeTime {
	case 0:
		tif = exchange.GTC
	case 1:
		tif = exchange.GTD
	case 2:
		tif = exchange.GTE
	case 3:
		tif = exchange.UnknownTimeInForce
	}
	order := &Order{
		InstrumentType: exchange.USDXMarginedFutures,
		OrderID:        fmt.Sprintf("%d", this.Ticket),
		Symbol:         this.Symbol,
		Price:          this.PriceOpen,
		LastPrice:      this.PriceCurrent,
		Qty:            this.VolumeInitial,
		ExecQty:        this.VolumeInitial - this.VolumeCurrent,
		ExecPrice:      this.ExecPrice,
		Type:           typ,
		Side:           side,
		Status:         this.getOrderStatus(),
		TimeInForce:    tif,
		CreateTime:     ptr(ExchangeTimeToUTC(time.UnixMilli(this.TimeSetupMsc))),
	}

	if this.PriceStopLimit != 0 {
		// 未触发时 PriceStopLimit 是限价，PriceOpen 是触发价
		// 触发后 PriceStopLimit 为 0，PriceOpen 为原 PriceStopLimit 值
		order.Price = this.PriceStopLimit
		order.TriggerPrice = this.PriceOpen
	}

	isStopLimitOrder, stopPrice := isStopLimitOrderComment(this.Comment)
	if isStopLimitOrder {
		order.Type = exchange.StopLimit
		order.TriggerPrice = stopPrice
	}

	order.UpdateTime = order.CreateTime
	if this.TimeDoneMsc > 0 {
		order.UpdateTime = ptr(ExchangeTimeToUTC(time.UnixMilli(this.TimeDoneMsc)))
	}
	return order
}

func getLocationZoneOffset() int {
	loc, err := time.LoadLocation(Timezone)
	if err != nil {
		zlog.Errorf("load location %s error: %s", Timezone, err)
		return 0
	}
	zNow := time.Now().In(loc)
	_, offset := zNow.Zone()
	return offset / 3600
}

func ExchangeTimeToUTC(t time.Time) time.Time {
	offset := getLocationZoneOffset()
	newTime := t.Add(-time.Hour * time.Duration(offset))
	return newTime
}

func ExchangeTimeNow() time.Time {
	location, err := time.LoadLocation(Timezone)
	if err != nil {
		zlog.Errorf("load location %s error: %s", location, err)
		return time.Now()
	} else {
		return time.Now().In(location)
	}
}

type MT5Orders []*MT5Order

func (this MT5Orders) toOrders() (orders []*Order) {
	orders = []*Order{}
	for _, order := range this {
		orders = append(orders, order.toOrder())
	}
	return
}

func setStopLimitOrderComment(stopPrice float64) string {
	return fmt.Sprintf("SL|%v", stopPrice)
}

func isStopLimitOrderComment(comment string) (bool, float64) {
	strs := strings.Split(comment, "|")
	if len(strs) == 2 && strs[0] == "SL" {
		stopPrice, err := strconv.ParseFloat(strs[1], 64)
		if err == nil && stopPrice > 0 {
			return true, stopPrice
		}
	}
	return false, 0
}

type MT5Deal struct {
	Symbol     string  `json:"symbol"`
	Ticket     int64   `json:"ticket"`
	Order      int64   `json:"order"`
	Time       int64   `json:"time"`
	TimeMsc    int64   `json:"time_msc"`
	Type       int     `json:"type"`
	Entry      int     `json:"entry"`
	Magic      int64   `json:"magic"`
	PositionID int64   `json:"position_id"`
	Reason     int     `json:"reason"`
	Volume     float64 `json:"volume"`
	Price      float64 `json:"price"`
	Commission float64 `json:"commission"`
	Swap       float64 `json:"swap"`
	Profit     float64 `json:"profit"`
	Fee        float64 `json:"fee"`
	Comment    string  `json:"comment"`
	ExternalID string  `json:"external_id"`
}

type MT5Deals []*MT5Deal

type MT5_TRADE_ACTION int

const (
	MT5_TRADE_ACTION_DEAL     MT5_TRADE_ACTION = 1  // Place a trade order for an immediate execution with the specified parameters (market order)
	MT5_TRADE_ACTION_PENDING  MT5_TRADE_ACTION = 5  // Place a trade order for the execution under specified conditions (pending order)
	MT5_TRADE_ACTION_SLTP     MT5_TRADE_ACTION = 6  // Modify Stop Loss and Take Profit values of an opened position
	MT5_TRADE_ACTION_MODIFY   MT5_TRADE_ACTION = 7  // Modify the parameters of the order placed previously
	MT5_TRADE_ACTION_REMOVE   MT5_TRADE_ACTION = 8  // Delete the pending order placed previously
	MT5_TRADE_ACTION_CLOSE_BY MT5_TRADE_ACTION = 10 // Close a position by an opposite one
)

type MT5_ORDER_TYPE int

const (
	MT5_ORDER_TYPE_BUY             MT5_ORDER_TYPE = 0 // Market Buy order
	MT5_ORDER_TYPE_SELL            MT5_ORDER_TYPE = 1 // Market Sell order
	MT5_ORDER_TYPE_BUY_LIMIT       MT5_ORDER_TYPE = 2 // Buy Limit pending order
	MT5_ORDER_TYPE_SELL_LIMIT      MT5_ORDER_TYPE = 3 // Sell Limit pending order
	MT5_ORDER_TYPE_BUY_STOP        MT5_ORDER_TYPE = 4 // Buy Stop pending order
	MT5_ORDER_TYPE_SELL_STOP       MT5_ORDER_TYPE = 5 // Sell Stop pending order
	MT5_ORDER_TYPE_BUY_STOP_LIMIT  MT5_ORDER_TYPE = 6 // Upon reaching the order price, a pending Buy Limit order is placed at the StopLimit price
	MT5_ORDER_TYPE_SELL_STOP_LIMIT MT5_ORDER_TYPE = 7 // Upon reaching the order price, a pending Sell Limit order is placed at the StopLimit price
	MT5_ORDER_TYPE_CLOSE_BY        MT5_ORDER_TYPE = 8 // Order to close a position by an opposite one
)

type MT5_ORDER_TYPE_FILLING int

const (
	MT5_ORDER_FILLING_FOK    MT5_ORDER_TYPE_FILLING = 0
	MT5_ORDER_FILLING_IOC    MT5_ORDER_TYPE_FILLING = 1
	MT5_ORDER_FILLING_RETURN MT5_ORDER_TYPE_FILLING = 2
)

type MT5_ORDER_TYPE_TIME int

const (
	MT5_ORDER_TIME_GTC           MT5_ORDER_TYPE_TIME = 0 // Good till cancel order
	MT5_ORDER_TIME_DAY           MT5_ORDER_TYPE_TIME = 1 // Good till current trade day order
	MT5_ORDER_TIME_SPECIFIED     MT5_ORDER_TYPE_TIME = 2 // Good till expired order
	MT5_ORDER_TIME_SPECIFIED_DAY MT5_ORDER_TYPE_TIME = 3 // The order will be effective till 23:59:59 of the specified day. If this time is outside a trading session,
)

type MT5OrderSendRequest struct {
	Action      MT5_TRADE_ACTION       `json:"action"`
	Magic       int64                  `json:"magic"`
	Order       int64                  `json:"order"`
	Symbol      string                 `json:"symbol"`
	Volume      float64                `json:"volume"`
	Price       float64                `json:"price"`
	StopLimit   float64                `json:"stoplimit"`
	Sl          float64                `json:"sl"`
	Tp          float64                `json:"tp"`
	Deviation   int                    `json:"deviation"`
	Type        MT5_ORDER_TYPE         `json:"type"`
	TypeFilling MT5_ORDER_TYPE_FILLING `json:"type_filling"`
	TypeTime    MT5_ORDER_TYPE_TIME    `json:"type_time"`
	Expiration  int64                  `json:"expiration"`
	Comment     string                 `json:"comment"`
	Position    float64                `json:"position"`
	PositionBy  float64                `json:"position_by"`
}

type MT5TradeResult struct {
	RetCode         int     `json:"retcode"`          // Operation return code
	Deal            int64   `json:"deal"`             // Deal ticket, if it is performed
	Order           int64   `json:"order"`            // Order ticket, if it is placed
	Volume          float64 `json:"volume"`           // Deal volume, confirmed by broker
	Price           float64 `json:"price"`            // Deal price, confirmed by broker
	Bid             float64 `json:"bid"`              // Current Bid price
	Ask             float64 `json:"ask"`              // Current Ask price
	Comment         string  `json:"comment"`          // Broker comment to operation (by default it is filled by description of trade server return code)
	RequestID       int64   `json:"request_id"`       // Request ID set by the terminal during the dispatch
	RetcodeExternal int     `json:"retcode_external"` // Return code of an external trading system
}

type MT5_TIME_FRAME int

const MT5_TIME_FRAME_M1 MT5_TIME_FRAME = 1
const MT5_TIME_FRAME_M2 MT5_TIME_FRAME = 2
const MT5_TIME_FRAME_M3 MT5_TIME_FRAME = 3
const MT5_TIME_FRAME_M4 MT5_TIME_FRAME = 4
const MT5_TIME_FRAME_M5 MT5_TIME_FRAME = 5
const MT5_TIME_FRAME_M6 MT5_TIME_FRAME = 6
const MT5_TIME_FRAME_M10 MT5_TIME_FRAME = 10
const MT5_TIME_FRAME_M12 MT5_TIME_FRAME = 12
const MT5_TIME_FRAME_M15 MT5_TIME_FRAME = 15
const MT5_TIME_FRAME_M20 MT5_TIME_FRAME = 20
const MT5_TIME_FRAME_M30 MT5_TIME_FRAME = 30
const MT5_TIME_FRAME_H1 MT5_TIME_FRAME = 1 | 0x4000
const MT5_TIME_FRAME_H2 MT5_TIME_FRAME = 2 | 0x4000
const MT5_TIME_FRAME_H4 MT5_TIME_FRAME = 4 | 0x4000
const MT5_TIME_FRAME_H3 MT5_TIME_FRAME = 3 | 0x4000
const MT5_TIME_FRAME_H6 MT5_TIME_FRAME = 6 | 0x4000
const MT5_TIME_FRAME_H8 MT5_TIME_FRAME = 8 | 0x4000
const MT5_TIME_FRAME_H12 MT5_TIME_FRAME = 12 | 0x4000
const MT5_TIME_FRAME_D1 MT5_TIME_FRAME = 24 | 0x4000
const MT5_TIME_FRAME_W1 MT5_TIME_FRAME = 1 | 0x8000
const MT5_TIME_FRAME_MN1 MT5_TIME_FRAME = 1 | 0xC000

package gateway

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

type IBPosition struct {
	SecType  IBContractType `json:"secType,omitempty"`
	Account  string         `json:"account,omitempty"`
	Position float64        `json:"position,omitempty"`
	AvgCost  float64        `json:"avgCost,omitempty"`
	Symbol   string         `json:"symbol,omitempty"`
}

func (this *IBPosition) toPosition() (position *exchange.Position) {
	side := exchange.PositionSideLong
	if this.Position < 0 {
		side = exchange.PositionSideShort
	}
	instrumentType := exchange.Spot
	if this.SecType != IBStocks {
		instrumentType = exchange.UnknownInstrumentType
	}
	position = &exchange.Position{
		InstrumentType: instrumentType,
		Side:           side,
		Symbol:         this.Symbol,
		Qty:            this.Position,
		EntryPrice:     this.AvgCost,
		UpdateTime:     ptr(time.Now()),
	}
	return
}

type IBPositions []*IBPosition

func (this IBPositions) toPositions() (positions []*exchange.Position) {
	positions = []*exchange.Position{}
	for _, p := range this {
		positions = append(positions, p.toPosition())
	}
	return
}

type IBSummary struct {
	Account   string
	Tag       string
	Value     string
	Currency  string
	ModelCode string
}

type IBContractType string

const UnknownIBContractType IBContractType = ""
const IBStocks IBContractType = "STK"
const IBFxPairs IBContractType = "CASH"
const IBCrypto IBContractType = "CRYPTO"
const IBIndexes IBContractType = "IND"
const IBCFDs IBContractType = "CFD"
const IBFutures IBContractType = "FUT"
const IBOptions IBContractType = "OPT"
const IBFuturesOptions IBContractType = "FOP"
const IBBonds IBContractType = "BOND"
const IBFunds IBContractType = "FUND"
const IBCommodities IBContractType = "CMDTY"
const IBWarrants IBContractType = "WAR"
const IBDutchWarrantsandStructuredProducts IBContractType = "IOPT"

type IBContract struct {
	ConId                        int64
	Currency                     string
	Exchange                     string
	LastTradeDateOrContractMonth string
	Multiplier                   string
	PrimaryExchange              string
	SecId                        string
	SecType                      IBContractType
	Symbol                       string
}

type IBContractDetails struct {
	Contract               IBContract
	MarketName             string
	MinTick                float64
	OrderTypes             string
	ValidExchanges         string
	PriceMagnifier         float64
	UnderConId             int64
	LongName               string
	ContractMonth          string
	Industry               string
	Category               string
	Subcategory            string
	TimeZoneId             string
	TradingHours           string
	LiquidHours            string
	evRule                 string
	EvMultiplier           float64
	MdSizeMultiplier       float64
	AggGroup               float64
	UnderSymbol            string
	UnderSecType           string
	MarketRuleIds          string
	RealExpirationDate     string
	LastTradeTime          string
	StockType              string
	MinSize                float64
	SizeIncrement          float64
	SuggestedSizeIncrement float64
	Cusip                  string
	Ratings                string
	DescAppend             string
	BondType               string
	CouponType             string
	Callable               bool
	Putable                bool
	Coupon                 float64
	Convertible            bool
	Maturity               string
	IssueDate              string
	NextOptionDate         string
	NextOptionType         string
	NextOptionPartial      bool
	Notes                  string
}

func (this *IBContractDetails) getInstrumentStatus() (exchange.InstrumentStatus, error) {
	status := exchange.InstrumentStatusClosed
	loc, err := time.LoadLocation(this.TimeZoneId)
	if err != nil {
		return "", fmt.Errorf("load contract timezone err: %s", err)
	}
	now := time.Now().In(loc)

	liquidHours := strings.Split(this.LiquidHours, ";")
	for _, hour := range liquidHours {
		if strings.Contains(hour, "CLOSED") {
			continue
		}

		ranges := strings.Split(hour, "-")
		if len(ranges) != 2 {
			continue
		}
		fromTime, err := time.ParseInLocation("20060102:1504", ranges[0], loc)
		if err != nil {
			continue
		}
		toTime, err := time.ParseInLocation("20060102:1504", ranges[1], loc)
		if err != nil {
			continue
		}
		if now.After(fromTime) && now.Before(toTime) {
			status = exchange.InstrumentStatusContinuous
			break
		}
	}

	return status, nil
}

func (this *IBContractDetails) toInstrument() (*exchange.Instrument, error) {
	nowTime := time.Now()
	if this.Contract.SecType != IBStocks { // 暂只支持股票类型
		return nil, fmt.Errorf("unsupported type: %s", this.Contract.SecType)
	}
	ins := &exchange.Instrument{
		InstrumentType: exchange.Spot,
		Symbol:         this.Contract.Symbol,
		ExchangeID:     this.Contract.Exchange,
		MinSize:        this.MinSize,
		// LotSize:           this.SizeIncrement,
		LotSize:           1, // 不能通过 API 下达小数股委托单
		SettleCurrency:    this.Contract.Currency,
		QuoteCurrency:     this.Contract.Currency,
		QuoteToSettleRate: 1,
		UpdateTime:        nowTime,
	}
	status, err := this.getInstrumentStatus()
	if err != nil {
		return nil, err
	}
	ins.Status = status
	return ins, nil
}

type IBTicker struct {
	Contract  IBContract
	Time      int64
	MinTick   float64
	Bid       float64
	BidSize   float64
	Ask       float64
	AskSize   float64
	Last      float64
	LastSize  float64
	Volume    float64
	Open      float64
	High      float64
	Low       float64
	Close     float64
	MarkPrice float64
}

type IBKLine struct {
	Date   int64
	Open   float64
	High   float64
	Low    float64
	Close  float64
	Volume float64
}

const IBActionBuy string = "BUY"
const IBActionSell string = "SELL"

type IBOrderType string

const IBOrderTypeLimit IBOrderType = "LMT"

/*
A Stop-Limit order is an instruction to submit a buy or sell limit order when the user-specified stop trigger price is attained or penetrated. The order has two basic components: the stop price and the limit price. When a trade has occurred at or through the stop price, the order becomes executable and enters the market as a limit order, which is an order to buy or sell at a specified price or better.
*/
const IBOrderTypeStopLimit IBOrderType = "STP LMT"

/*
A Limit if Touched is an order to buy (or sell) a contract at a specified price or better, below (or above) the market. This order is held in the system until the trigger price is touched. An LIT order is similar to a stop limit order, except that an LIT sell order is placed above the current market price, and a stop limit sell order is placed below.
*/
const IBOrderTypeLimitIfTouched IBOrderType = "LIT"

const IBOrderTypeMarket IBOrderType = "MKT"

type IBPlaceOrderRequest struct {
	Symbol        string         `json:"symbol"`
	SecType       IBContractType `json:"secType"`
	Currency      string         `json:"currency"`
	Exchange      string         `json:"exchange"`
	Action        string         `json:"action"`
	OrderType     IBOrderType    `json:"orderType"`
	TotalQuantity float64        `json:"totalQuantity"`
	LmtPrice      float64        `json:"lmtPrice"`
	AuxPrice      float64        `json:"auxPrice"`
	Tif           IBTimeInForce  `json:"tif"`
	OutsideRth    bool           `json:"outsideRth"`
}

type IBTimeInForce string

const IBTimeInForceDAY IBTimeInForce = "DAY" // 默认值
const IBTimeInForceGTC IBTimeInForce = "GTC"
const IBTimeInForceOPG IBTimeInForce = "OPG"
const IBTimeInForceIOC IBTimeInForce = "IOC"
const IBTimeInForceGTD IBTimeInForce = "GTD"
const IBTimeInForceGTT IBTimeInForce = "GTT"
const IBTimeInForceAUC IBTimeInForce = "AUC"
const IBTimeInForceFOK IBTimeInForce = "FOK"
const IBTimeInForceGTX IBTimeInForce = "GTX"
const IBTimeInForceDTC IBTimeInForce = "DTC"

type IBOrder struct {
	OrderId         int64 // api 下单时指定的 ID
	ClientId        int64 // api 连接 ID 固定值
	PermId          int64 // tws 唯一标识
	Action          string
	TotalQuantity   float64     // 成交后是 0
	FilledQuantity  json.Number // 无成交时是无限大
	OrderType       IBOrderType
	LmtPrice        float64
	AuxPrice        float64
	Tif             IBTimeInForce
	ActiveStartTime string
	ActiveStopTime  string
	OcaGroup        string
	OcaType         int
	OrderRef        string
	Transmit        bool
	parentId        int64
	BlockOrder      bool
	SweepToFill     bool
	OutsideRth      bool
	Hidden          bool
	GoodAfterTime   string
	GoodTillDate    string
	Rule80A         string
	AllOrNone       bool
	Duration        int64
}

type IBOrderStatus struct {
	OrderId       int64
	Status        string
	Filled        float64
	Remaining     float64
	AvgFillPrice  float64
	PermId        int64
	ParentId      int64
	LastFillPrice float64
	ClientId      int64
}

type IBExecution struct {
	ExecId        string
	Time          int64
	AcctNumber    string
	Exchange      string
	Side          string
	Shares        float64
	Price         float64
	PermId        int64
	ClientId      int64
	OrderId       int64
	Liquidation   float64
	CumQty        float64
	AvgPrice      float64
	OrderRef      string
	EvRule        string
	EvMultiplier  float64
	ModelCode     string
	LastLiquidity float64
}

type IBCommission struct {
	ExecId      string
	Commission  float64
	Currency    string
	RealizedPNL float64
}

type IBLog struct {
	Time      int64
	Status    string
	Message   string
	ErrorCode int
}

type IBTrade struct {
	Contract      IBContract
	Order         IBOrder
	OrderStatus   IBOrderStatus
	Fills         [][]interface{}
	Log           []IBLog
	AdvancedError string
}

// log 里的时间并不是订单时间，而且 adpater 获取时的时间
// func (i *IBTrade) getCreateTime() *time.Time {
// 	if len(i.Log) > 0 {
// 		return ptr(time.Unix(i.Log[0].Time/1000, 0))
// 	}
// 	return ptr(time.Now())
// }

// func (i *IBTrade) getUpdateTime() *time.Time {
// 	if len(i.Log) > 0 {
// 		return ptr(time.Unix(i.Log[len(i.Log)-1].Time/1000, 0))
// 	}
// 	return ptr(time.Now())
// }

func (i *IBTrade) getOrderStatus() exchange.OrderStatus {
	switch i.OrderStatus.Status {
	case "Submitted", "PendingSubmit", "ApiPending", "PreSubmitted":
		if i.OrderStatus.Filled > 0 {
			return exchange.OrderStatusPartialFilled
		}
		return exchange.OrderStatusNew
	case "Cancelled":
		if i.OrderStatus.Filled > 0 {
			return exchange.OrderStatusPartialCancelled
		}
		return exchange.OrderStatusCancelled
	case "Filled":
		return exchange.OrderStatusFilled
	case "Inactive":
		return exchange.OrderStatusRejected
	default:
		return exchange.UnknownOrderStatus
	}
}

func (i *IBTrade) getOrderType() exchange.OrderType {
	switch i.Order.OrderType {
	case IBOrderTypeLimit:
		return exchange.Limit
	case IBOrderTypeMarket:
		return exchange.Market
	case IBOrderTypeStopLimit, IBOrderTypeLimitIfTouched:
		return exchange.StopLimit
	default:
		return exchange.UnknownOrderType
	}
}

func (i *IBTrade) toOrder() (order *exchange.Order) {
	order = &Order{
		InstrumentType: exchange.Spot,
		OrderID:        fmt.Sprintf("%d", i.Order.PermId),
		Symbol:         i.Contract.Symbol,
		Status:         i.getOrderStatus(),
		Type:           i.getOrderType(),
		Price:          i.Order.LmtPrice,
		TriggerPrice:   i.Order.AuxPrice,
		Qty:            i.Order.TotalQuantity,
		ExecQty:        i.OrderStatus.Filled,
		ExecPrice:      i.OrderStatus.AvgFillPrice,
	}

	if order.Qty == 0 {
		order.Qty, _ = i.Order.FilledQuantity.Float64()
	}

	// OrderStatus 里的成交数据在程序重启后就没有了，需从 Fills 获取
	execQty := 0.0
	execPrice := 0.0
	commission := 0.0
	commissionCurrency := ""
	for _, fillArray := range i.Fills {
		if len(fillArray) >= 3 {
			jsonStr, err := json.Marshal(fillArray[1])
			if err != nil {
				zlog.Errorf("marshal fills err: %s", err)
				continue
			}

			var exec IBExecution
			if err := json.Unmarshal(jsonStr, &exec); err != nil {
				zlog.Errorf("unmarshal fills err: %s", err)
				continue
			}

			if exec.CumQty > execQty {
				execQty = exec.CumQty
				execPrice = exec.AvgPrice
				if exec.Time != 0 {
					order.UpdateTime = ptr(time.Unix(exec.Time/1000, 0))
				}
			}

			jsonStr, err = json.Marshal(fillArray[2])
			if err != nil {
				zlog.Errorf("marshal fills err: %s", err)
				continue
			}

			var com IBCommission
			if err := json.Unmarshal(jsonStr, &com); err != nil {
				zlog.Errorf("unmarshal fills err: %s", err)
				continue
			}

			if commissionCurrency == "" {
				commissionCurrency = com.Currency
			}
			if commissionCurrency == com.Currency {
				commission += com.Commission
			}
		}
	}

	if order.ExecPrice == 0 {
		order.ExecPrice = execPrice
		order.ExecQty = execQty
	}
	order.FeeAsset = commissionCurrency
	order.Fee = commission

	if i.Order.Action == IBActionBuy {
		order.Side = exchange.OrderSideBuy
	} else {
		order.Side = exchange.OrderSideSell
	}

	order.TimeInForce = exchange.UnknownTimeInForce
	switch i.Order.Tif {
	case IBTimeInForceGTC:
		order.TimeInForce = exchange.GTC
	case IBTimeInForceIOC:
		order.TimeInForce = exchange.IOC
	case IBTimeInForceGTD:
		order.TimeInForce = exchange.GTD
	}

	if order.UpdateTime == nil {
		order.UpdateTime = ptr(time.Now())
	}

	return order
}

type IBPriceIncrement struct {
	LowEdge   float64
	Increment float64
}

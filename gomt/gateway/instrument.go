package gateway

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

/* 品种相关的代码 */

const ExtKeyPriceUpdateSecond = "PriceUpdateSecond"

type Instrument = exchange.Instrument

func (this *GatewayServer) isInstrumentExpiredSoon(instrumentID string) bool {
	_, found := this.Instruments.Load(instrumentID)
	if !found {
		return false
	}
	// ins := i.(*gomt.InstrumentField)
	// dliveryDate := time.Date(ins.DeliveryYear, time.Month(ins.DeliveryMonth), 1, 0, 0, 0, 0, time.UTC).Add(-time.Hour * 8)
	// now := time.Now()
	// // zlog.Debugf("%s dliveryDate %s", instrumentID, dliveryDate)
	// return !now.After(dliveryDate) && now.Add(time.Hour*24*time.Duration(this.opts.AutoCloseDays)).After(dliveryDate)
	return false
}

func (this *GatewayServer) isInstrumentDelivered(instrumentID string) bool {
	if this.platform == IB {
		return false
	}
	_, found := this.Instruments.Load(instrumentID)
	return !found
}

func (this *GatewayServer) subscribe(instrumentIDs []string) {
	go func() {
		for !this.client.IsConnected() {
			time.Sleep(1 * time.Second)
		}
		for _, instrumentID := range instrumentIDs {
			zlog.Debugf("subscribe %s", instrumentID)
			err := this.client.Subscribe(instrumentID)
			if err != nil {
				zlog.Errorf("subscribe failed, (%s), error: %s", instrumentID, err)
			} else {
				zlog.Infof("subscribe success, (%s)", instrumentID)
			}
		}
	}()
}

func (this *GatewayServer) IsInstrumentTrading(instrumentID string) bool {
	sessions, err := this.opts.GetInstrumentSessions(instrumentID)
	if err != nil {
		zlog.Errorf("check instrument is trading failed, error: %s", err)
		return false
	}
	return sessions.IsTrading()
}

// 缓存品种数据，blocking
// TODO: 根据 tradingSymbols 来频繁更新对应 instruments 的 LastPrice 数据
func (this *GatewayServer) cacheInstruments() {
	if !this.cacheInstrumentMutex.TryLock() {
		return
	}
	defer this.cacheInstrumentMutex.Unlock()

	for !this.Launched.Load() {
		time.Sleep(1 * time.Second)
	}

	for {
		instruments, err := this.client.GetInstruments("")
		if err != nil {
			zlog.Errorf("cache instruments failed, get %s instruments failed, error: %s", this.platform, err)
			time.Sleep(1 * time.Second)
			continue
		}

		for _, instrument := range instruments {
			if instrument.QuoteToSettleRate == 0 {
				continue
			}
			this.Instruments.Store(instrument.Symbol, instrument)
		}

		this.InstrumentsReady.Store(true)
		zlog.Debugf("cache instrument, instrument ready set to true")
		this.client.QueryInstrumentMarginRates()
		this.clearDeliveredSymbol()
		break
	}
}

func (this *GatewayServer) clearDeliveredSymbol() {
	tradingSymbols := this.storage.TradingSymbols
	for _, symbol := range tradingSymbols {
		if this.isInstrumentDelivered(symbol) {
			this.DisbleTrading(symbol)
		}
	}
}

func (this *GatewayServer) GetInstrument(instrumentID string) (result *Instrument, er error) {
	if !this.InstrumentsReady.Load() {
		return nil, errors.New("instruments not ready")
	}
	this.Instruments.Range(func(key string, value *exchange.Instrument) bool {
		if strings.EqualFold(key, instrumentID) {
			result = value
			return false
		}
		return true
	})
	if result == nil {
		return nil, fmt.Errorf("get instrument failed, instrument not found, instrument: %s", instrumentID)
	}
	return
}

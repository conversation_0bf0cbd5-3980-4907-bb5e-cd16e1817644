package gateway

import (
	"bufio"
	"context"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/mitchellh/go-ps"
	"github.com/spf13/cast"
	"github.com/wizhodl/quanter/common/zlog"
)

type WrappedProcess struct {
	ps.Process
	Port int
}

func NewWrappedProcess(p ps.Process, port int) *WrappedProcess {
	return &WrappedProcess{
		Process: p,
		Port:    port,
	}
}

func (this *WrappedProcess) FormatPort() string {
	if this.Port == 0 {
		return ""
	}
	return fmt.Sprintf(":%d", this.Port)
}

func killProcessOnPort(port int) error {
	pid, err := findProcessByPort(port)
	if err != nil {
		return fmt.Errorf("find process by port failed, error: %s", err)
	}

	// 终止进程
	killCmd := exec.Command("taskkill", "/F", "/PID", strconv.Itoa(pid))
	err = killCmd.Run()
	if err != nil {
		return fmt.Errorf("terminate process failed, pid: %d, error: %v", pid, err)
	}

	zlog.Infof("kill process success for port: %d, (PID: %d)\n", port, pid)
	return nil
}

func findProcessByPort(port int) (pid int, er error) {
	// return error for non-windows system
	if runtime.GOOS != "windows" {
		return 0, fmt.Errorf("not implemented on non-windows system")
	}
	// 查找占用指定端口的进程
	cmd := exec.Command("netstat", "-ano", "-p", "tcp")
	output, err := cmd.Output()
	if err != nil {
		return 0, fmt.Errorf("execute netstat command failed, error: %v", err)
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, fmt.Sprintf(":%d", port)) {
			fields := strings.Fields(line)
			if len(fields) > 4 {
				pid, err = strconv.Atoi(fields[len(fields)-1])
				if err != nil {
					return 0, fmt.Errorf("parse PID from netstat output failed, error: %v", err)
				}
				break
			}
		}
	}
	if pid == 0 {
		return 0, fmt.Errorf("process occupied port not found, port: %d", port)
	}
	return pid, nil
}

func tryPython(pythonPath string) (realPythonPath string, er error) {
	if pythonPath == "" {
		pythonPath = "python"
	} else {
		if _, err := os.Stat(pythonPath); os.IsNotExist(err) {
			er = fmt.Errorf("python path is not exist, (%s)", pythonPath)
			return
		}
	}
	if _, cancelFunc, err := execute([]string{pythonPath, "-V"}, 0); err != nil {
		er = fmt.Errorf("test python command failed, error: %s", err)
		return
	} else {
		zlog.Infof("test python success")
		realPythonPath = pythonPath
		if cancelFunc != nil {
			cancelFunc()
		}
	}
	return
}

func execute(cmdArgs []string, timeoutSecond int) (command *exec.Cmd, cancel context.CancelFunc, er error) {
	var ctx context.Context
	if timeoutSecond > 0 {
		ctx, cancel = context.WithTimeout(context.Background(), time.Duration(timeoutSecond)*time.Second)
		defer cancel()
	} else {
		ctx, cancel = context.WithCancel(context.Background())
	}

	if len(cmdArgs) == 0 {
		er = errors.New("no command provided")
		return
	}

	exeName := filepath.Base(cmdArgs[0])
	command = exec.CommandContext(ctx, cmdArgs[0], cmdArgs[1:]...)
	command.Env = os.Environ()

	stdout, err := command.StdoutPipe()
	if err != nil {
		zlog.Errorf("failed creating command stdout pipe: ", err)
		er = err
		return
	}
	stdoutReader := bufio.NewReader(stdout)

	stderr, err := command.StderrPipe()
	if err != nil {
		zlog.Errorf("failed creating command stderr pipe: ", err)
		er = err
		return
	}
	stderrReader := bufio.NewReader(stderr)

	if err := command.Start(); err != nil {
		zlog.Errorf("failed starting command: ", err)
		er = err
		return
	}

	go handleReader(stdoutReader, exeName, "stdout")
	go handleReader(stderrReader, exeName, "stderr")
	return
}

func handleReader(reader *bufio.Reader, exeName, outputType string) {
	for {
		str, err := reader.ReadString('\n')
		if err != nil {
			break
		}
		zlog.Debugf("[%s] %s >   %s", exeName, outputType, strings.TrimSpace(str))
	}
}

func findProcess(query string) (foundProcesses []*WrappedProcess, er error) {
	processes, err := ps.Processes()
	if err != nil {
		return nil, fmt.Errorf("query processes failed, error: %s", err)
	}

	// 如果 query 是 :port 的形式，则查询 port 对应的进程
	pidForPort := 0
	var port int
	if strings.HasPrefix(query, ":") {
		var err error
		port, err = cast.ToIntE(query[1:])
		if err != nil {
			return nil, fmt.Errorf("port must be a number")
		}
		pidForPort, err = findProcessByPort(int(port))
		if err != nil {
			return nil, fmt.Errorf("query port failed, port: %d, error: %s", port, err)
		}
		zlog.Debugf("pid for port: %d, pid: %d", port, pidForPort)
	}

	pidInQuery := 0
	if strings.HasPrefix(query, "#") {
		var err error
		pidInQuery, err = cast.ToIntE(query[1:])
		if err != nil {
			return nil, fmt.Errorf("pid must be a number")
		}
		zlog.Debugf("pid in query: %d", pidInQuery)
	}

	blockList := []string{"svchost", "Dell."} // 不显示一些常见的系统进程
	for _, p := range processes {
		wp := NewWrappedProcess(p, 0)
		if pidForPort != 0 && wp.Pid() == pidForPort {
			wp.Port = port
			foundProcesses = append(foundProcesses, wp)
			break
		}
		if pidInQuery != 0 && wp.Pid() == pidInQuery {
			foundProcesses = append(foundProcesses, wp)
			break
		}
		if query != "" && !strings.Contains(wp.Executable(), query) {
			continue
		}
		inBlockList := false
		for _, b := range blockList {
			if strings.Contains(wp.Executable(), b) {
				inBlockList = true
				break
			}
		}
		if inBlockList {
			continue
		}
		foundProcesses = append(foundProcesses, wp)
	}
	// 进程名称按字母排序
	sort.SliceStable(foundProcesses, func(i, j int) bool {
		exec1 := foundProcesses[i].Executable()
		exec2 := foundProcesses[j].Executable()
		fileName1 := strings.ToLower(filepath.Base(exec1))
		fileName2 := strings.ToLower(filepath.Base(exec2))
		return fileName1 < fileName2
	})

	if len(foundProcesses) == 0 {
		return nil, fmt.Errorf("no process found")
	}
	return
}

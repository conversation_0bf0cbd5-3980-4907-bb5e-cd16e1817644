package gateway

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	_ "net/http/pprof"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/puzpuzpuz/xsync/v2"
	"github.com/robfig/cron/v3"
	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/base/cmds"
	"github.com/wizhodl/quanter/common/command"
	"github.com/wizhodl/quanter/common/ginmw/ginzap"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	exchange_gateway "github.com/wizhodl/quanter/exchange/gateway"
	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"
	"go.uber.org/atomic"
	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
)

/* Gateway 网关相关的代码 */

type GatewayServer struct {
	sync.RWMutex
	command.BaseResponder
	platform   Platform
	launchTime *time.Time
	ID         string
	opts       *Option
	startAt    time.Time
	gin        *gin.Engine
	exitChan   chan struct{}
	storage    *Storage

	lastAdapterPID int

	client Adaptable

	/* 品种数据是否就绪
	因为 ctp 品种数据的回调是异步的，每次发送一个品种回来，必须在收到所有品种后，应用才可以确保可以进行后续处理
	*/
	InstrumentsReady *atomic.Bool

	/* 品种数据
	包含 tick 的 lastPrice 更新、InstrumentStatus 的更新
	key: instrumentID, value: *Instrument
	*/
	Instruments *xsync.MapOf[string, *Instrument]

	/* 客户端 websocket 连接
	向客户端推送消息时，只需要便利 connMap 并向 msgChan 中写入 *Packet 数据即可
	key: remoteAddr, value: msgChan chan *Packet
	*/
	connMap *xsync.MapOf[string, chan *exchange.Packet]

	/* Slack 对应的 webscoket 连接，最后一个连接
	主要用于通过该地址修改 slack 连接对应的 subscribedKeys
	*/
	slackConnAddr string

	/* websocket 连接对应的订阅 keys
	key: connAddr, value: list of subscribed keys
	主要用于通过 slackConnAddr 查找和修改 subscribed keys
	*/
	connSubscribedKeys map[string][]string

	// 手工通知 websocket 推送消息
	notifyChan chan byte

	messenger        *GatewayMessenger
	commandProcessor *command.CommandProcessor // 命令处理器

	// 定时任务管理器
	cron *cron.Cron

	// 缓存品种的锁
	cacheInstrumentMutex sync.Mutex

	SyncingOrderExecutions *atomic.Bool
}

func NewGatewayServer(platform Platform, opts *Option, commitHash, buildTime string) (server *GatewayServer, er error) {
	optPlatform := opts.Platform
	// 如果未指定平台，使用配置文件的平台设置
	// 如果已经指定平台，需要和配置文件中的平台设置相同
	if platform == UnknownPlatform {
		if optPlatform == UnknownPlatform {
			er = fmt.Errorf("need at least one of option.Platform and --platform command line argument")
			return
		} else {
			platform = optPlatform
		}
	} else if platform != optPlatform {
		er = fmt.Errorf("option.Platform not match with program args --platform")
		return
	}

	// 设置全局 Timezone 为配置的值，并且检查时区是否正确
	if platform == MT5 {
		if opts.MT.ServerTimezone != "" {
			Timezone = opts.MT.ServerTimezone
		}
		_, err := time.LoadLocation(Timezone)
		if err != nil {
			zlog.Errorf("validate server timezone failed, %s, error: %s", Timezone, err)
			er = fmt.Errorf("invalid server timezone: %s", Timezone)
			return
		}
	}

	baseResponder := command.NewBaseResponder(opts.Debug, commitHash, buildTime, opts.SlackChannel, nil, opts.LogPath)

	server = &GatewayServer{
		BaseResponder: baseResponder,
		ID:            opts.ID,
		platform:      platform,
		opts:          opts,
		startAt:       time.Now(),

		exitChan:               make(chan struct{}),
		Instruments:            xsync.NewMapOf[*Instrument](),
		connMap:                xsync.NewMapOf[chan *exchange.Packet](),
		notifyChan:             make(chan byte),
		connSubscribedKeys:     map[string][]string{},
		InstrumentsReady:       atomic.NewBool(false),
		SyncingOrderExecutions: atomic.NewBool(false),
		cacheInstrumentMutex:   sync.Mutex{},
	}
	server.messenger = NewGatewayMessenger(server)
	server.BaseResponder.Messenger = server.messenger
	server.commandProcessor = command.NewCommandProcessor(server,
		[]command.Commander{
			NewLaunchGridCommand(server),
			NewStatusGatewayCommand(server),
			NewSubscribeGatewayCommand(server),
			NewUnsubscribeGatewayCommand(server),
			NewEnableTradingGatewayCommand(server),
			NewDisbleTradingGatewayCommand(server),
			NewConfigGatewayCommand(server),
			NewRestartAdapterGatewayCommand(server),
			NewInitAdapterGatewayCommand(server),
			NewListProcessGatewayCommand(server),
			NewRestartTerminalGatewayCommand(server),
			NewRestartServiceGatewayCommand(server),
			NewKillProcessGatewayCommand(server),
			NewSendTradingStatusGatewayCommand(server),
			// 数据查询命令
			NewOrderGatewayCommand(server),
			NewPositionsGatewayCommand(server),
			NewOpenOrdersGatewayCommand(server),
			NewGetAccountGatewayCommand(server),
			NewGetInstrumentsGatewayCommand(server),
			NewCancelAllOrdersGatewayCommand(server),
		})

	// 初始化 mt 客户端
	if server.platform == MT5 {
		server.client = NewMT5Client(server)
		server.commandProcessor.AddCommands(
			// MT5 命令
			[]command.Commander{
				NewMT5TerminalInfoGatewayCommand(server),
				NewMT5AccountInfoGatewayCommand(server),
				NewMT5InstrumentsGatewayCommand(server),
				NewMT5OpenOrdersGatewayCommand(server),
				NewMT5PositionsGatewayCommand(server),
				NewMT5HistoryOrdersGatewayCommand(server),
				NewSwapRateGatewayCommand(server),
				NewPositionHistoryGatewayCommand(server),
			})
	} else if server.platform == IB { // 初始化 IB 客户端
		server.client = NewIBClient(server)
		// server.commandProcessor.AddCommands(
		// 	// IB 命令
		// 	[]command.Commander{})
	}

	logDir, logFilename := utils.ParseDirAndFilename(opts.LogPath, true)
	zlog.Infof("logDir: %s, logFilename: %s", logDir, logFilename)

	server.commandProcessor.AddCommands(
		[]command.Commander{
			cmds.NewPagerCommand(),
			NewExitGatewayCommand(server),
			cmds.NewMuteCommand(),
			cmds.NewDebugCommand(),
			cmds.NewLogCommand(logDir, logFilename),
			cmds.NewDownloadLogCommand(logDir, logFilename),
			cmds.NewDownloadStorageCommand(server.opts.DataDir, fmt.Sprintf("%s.gateway_storage", server.ID)),
			cmds.NewStackTraceCommand(),
		})
	// 初始化时检查 ReleaseBinaryDir 是否可以写，以免后续 releases 相关命令每次都要检查
	if utils.CheckReleaseBinaryDirPath(server.opts.ReleaseBinaryDir) {
		server.commandProcessor.AddCommands([]command.Commander{
			cmds.NewReleaseCommand(server.opts.ReleaseBinaryDir, server.opts.ID),
			cmds.NewListVersionCommand(server.opts.ReleaseBinaryDir, server.opts.ID),
			cmds.NewUseVersionCommand(server.opts.ReleaseBinaryDir, server.opts.ID),
		})
	}

	if storage, err := setupStorage(server); err != nil {
		er = err
		return
	} else {
		server.storage = storage

		if storage.AdapterPID > 0 {
			server.lastAdapterPID = storage.AdapterPID
			err := server.KillProcess(server.lastAdapterPID)
			if err != nil {
				zlog.Errorf("init gateway,kill last adapter process failed, error: %s", err)
			} else {
				zlog.Infof("init gateway,kill last adapter process success, PID: %d", server.lastAdapterPID)
			}
		}
	}

	if !opts.IsRemoteLaunch() {

		if err := server.launch("", ""); err != nil {
			zlog.Errorf("mt gateway launch failed, error: %s", err)
		}
	}

	go server.websocketLoop()
	return
}

func (this *GatewayServer) GetMTClient() *MT5Client {
	if this.client.GetPlatform() == MT5 {
		return this.client.(*MT5Client)
	}
	return nil
}

func (this *GatewayServer) KillProcess(pid int) (er error) {
	if pid > 0 {
		process, err := os.FindProcess(pid)
		if err != nil {
			er = fmt.Errorf("find process failed, error: %s", err)
			return
		}

		err2 := process.Kill()
		if err2 != nil {
			er = fmt.Errorf("kill process failed, error: %s", err2)
			return
		}
	}
	return
}

func (this *GatewayServer) getLogFilename() string {
	return this.opts.ID
}

func TokenSignMiddleware(apiKeys APIKeys, checkSign bool) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path

		// 拷贝 context，否则如果读取过 body 后，导致 http handler 中没有数据可以读
		remoteHost := c.Request.RemoteAddr
		// zlog.Debugf("checking sign of %s, remote address: %s", path, remoteHost)
		isLocalRequest := strings.HasPrefix(remoteHost, "127.0.0.1") || strings.HasPrefix(remoteHost, "[::1]:")

		checkSignWhitelist := []string{"/v1/ws", "/v1/stats"}
		inWhitelist := SliceContains(checkSignWhitelist, path)
		if !checkSign || inWhitelist || isLocalRequest {
			c.Next()
			return
		}

		apiID := c.Request.Header.Get("X-QUANTER-API-ID")
		timestampStr := c.Request.Header.Get("X-QUANTER-TIMESTAMP")
		timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
		if err != nil {
			errorJSON(c, 401, "invalid timestamp, int required")
			return
		}
		if time.Now().Unix()-timestamp > 5 {
			errorJSON(c, 401, "request timestamp exipred")
			return
		}

		signStr := c.Request.Header.Get("X-QUANTER-SIGN")
		sign, err := hex.DecodeString(signStr)
		if err != nil {
			errorJSON(c, 401, "invalid signature string, hex required")
			return
		}

		if apiID == "" {
			errorJSON(c, 401, "API ID missing")
			return
		}

		secret := ""
		for _, apiKey := range apiKeys {
			if apiKey.APIKey == apiID {
				secret = string(apiKey.APISecret)
			}
		}
		if secret == "" {
			errorJSON(c, 401, "API ID not found")
			return
		}

		body, err := ioutil.ReadAll(c.Request.Body)
		if err != nil {
			errorJSON(c, 401, fmt.Sprintf("read request body failed, error: %s", err.Error()))
			return
		}
		// 重新写入 body
		// 读取 body 后，c.Request.Body 会为空。导致后续的 http handler 拿不到数据，ctx.ShouldBindWith 报 EOF 错误。
		c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))

		combinedString := strings.Join([]string{timestampStr, c.Request.RequestURI, string(body)}, "|||||")

		h := hmac.New(sha256.New, []byte(secret))
		h.Write([]byte(combinedString))
		validSign := hmac.Equal(sign, h.Sum(nil))
		if !validSign {
			errorJSON(c, 401, "signature not match")
			return
		}

		c.Next()
	}
}

func (this *GatewayServer) Serve() {
	this.RunCron()

	opts := this.opts
	if this.opts.Debug {
		gin.SetMode("debug")
	}
	this.gin = gin.New()
	sugaredLogger := zlog.GetLogger()

	if sugaredLogger != nil {
		// TODO: 检查 SetDebug 是否会导致 logger 为 nil，进而导致崩溃
		logger := sugaredLogger.Desugar()
		// Add a ginzap middleware, which:
		//   - Logs all requests, like a combined access and error log.
		//   - Logs to stdout.
		this.gin.Use(ginzap.Ginzap(logger, time.RFC3339, true))

		// Logs all panic to error log
		//   - stack means whether output the stack info.
		this.gin.Use(ginzap.RecoveryWithZap(logger, true))
	} else {
		this.gin.Use(gin.Logger(), gin.Recovery())
	}

	this.gin.Use(TokenSignMiddleware(opts.APIKeys, opts.CheckSign))
	v1 := this.gin.Group("/v1")
	{
		v1.GET("/instruments", this.getInstrumentsHandler)
		v1.GET("/orders/open", this.getOpenOrdersHandler)
		v1.GET("/orders", this.getOrdersHandler)
		v1.GET("/instruments/price", this.getLastPriceHandler)
		v1.GET("/balances", this.getAccountBalancesHandler)
		v1.GET("/margin", this.getUserMarginHandler)
		v1.GET("/positions", this.getPositionsHandler)
		v1.POST("/orders/create", this.createOrderHandler)
		v1.GET("/orders/cancel", this.cancelOrderHandler)
		v1.GET("/ws", this.websocketHandler)
		v1.GET("/stats", this.getStatsHandler)
		v1.GET("/klines/lookup", this.getKlinesHandler)
		v1.POST("/callback", this.adapterCallbackHandler)
	}
	go func() {
		zlog.Infof("start http server, http_addr: %s", opts.HTTPAddr)
		err := this.gin.Run(opts.HTTPAddr)
		zlog.Panicf("gin run failed, error: %s", err)
	}()
}

func (this *GatewayServer) RunCron() {
	c := cron.New()
	// 每天记录一次 PositionHistory
	c.AddFunc("0 1 * * *", this.recordPositionHistory)
	// 每天记录一次 SwapRate
	c.AddFunc("0 1 * * *", this.recordSwapRate)
	// c.AddFunc("2 20,21,22 * * *", this.SendTradingStatus)
	// 每分钟记录一次 swap 的数据，调试时打开
	// c.AddFunc("* * * * *", this.recordSwapRate)

	// 每小时同步当天订单到本地
	c.AddFunc("0 * * * *", this.syncOrders)
	// 开盘前同步历史成交
	c.AddFunc("CRON_TZ=Asia/Shanghai 0 8,20 * * *", this.syncOrderExecutions)

	this.cron = c
	go c.Run()

	if this.Debug {
		go func() {
			time.Sleep(time.Second * 20)
			// this.checkExpiringPosition()

			this.syncOrders()
			this.syncOrderExecutions()
		}()
	}
}

func (this *GatewayServer) Exit() {
	cancelFunc := this.client.GetOwnedProcess(Adapter).CancelFunc
	if cancelFunc != nil {
		cancelFunc()
	} else {
		err := this.KillProcess(this.storage.AdapterPID)
		if err != nil {
			zlog.Errorf("exit, kill adapter process failed, error: %s", err)
		}
	}
	close(this.exitChan)
	zlog.Infof("server exiting")
}

func (this *GatewayServer) RepairInstrumentID(instrumentID string) (repairedID string, er error) {
	if !this.InstrumentsReady.Load() {
		er = errors.New("instruments not ready")
		return
	}
	this.Instruments.Range(func(k string, instr *Instrument) bool {
		if strings.EqualFold(instr.Symbol, instrumentID) {
			repairedID = instr.Symbol
			return false
		}
		return true
	})
	return
}

func (this *GatewayServer) SetDebug(debug bool) {
	this.BaseResponder.Debug = debug
	logRoation := &zlog.LogRotation{
		MaxSize:    this.opts.LogMaxSize,
		MaxBackups: this.opts.LogMaxBackups,
		MaxAge:     this.opts.LogMaxAge,
	}
	var zlogger *zap.SugaredLogger
	if debug {
		zlogger = zlog.NewRotateLogger("DEBUG", this.BaseResponder.LogPath, logRoation)
	} else {
		zlogger = zlog.NewRotateLogger("INFO", this.BaseResponder.LogPath, logRoation)
	}
	zlog.SetLogger(zlogger)
}

func (this *GatewayServer) launch(mainPassword, googleAuthCode string) error {
	if this.Launched.Load() {
		return nil
	}
	if this.client == nil {
		return errors.New("adapter client not initialized")
	}
	// 解密并设置 opts.Password 和 opts.AuthCode，必须在 initQuote 和 initTrade 之前调用
	if mainPassword != "" {
		if err := this.decryptSecret(mainPassword, googleAuthCode); err != nil {
			return err
		}
	}

	// 启动 mt 适配器，出错不返回错误，因为后续可以自行恢复
	err := this.client.StartOwnedProcess(Adapter)
	if err != nil {
		this.AlertMsgf("start adapter failed, error: %s", err)
	}

	go this.checkTradingStatusLoop()
	this.Launched.Store(true)
	this.launchTime = ptr(time.Now())
	return nil
}

// 解密本地密钥获得 CTP 的 Password 和 AuthCode
// 解密后的格式：InvestorID|||Password|||AuthCode
func (this *GatewayServer) decryptSecret(password string, authCode string) error {
	err := secrets.CheckPassword(password, authCode)
	if err != nil {
		return errors.New("password wrong or authcode")
	}
	if this.platform == MT5 {
		apiSecret, err := exchange_gateway.NewMetaTraderAPISecret("", secrets.SecretString(this.opts.MT.EncryptedSecrets), true)
		if err != nil {
			return fmt.Errorf("decrypt mt api secret failed, error: %s", err)
		}
		this.opts.MT.Password = apiSecret.Password
		if apiSecret.UserID != this.opts.MT.UserID {
			return fmt.Errorf("decryped investor id not match, (%s) != (%s)", apiSecret.UserID, this.opts.MT.UserID)
		}
		this.opts.MT.UserID = apiSecret.UserID
	}
	if err := this.decryptAPIKeys(); err != nil {
		return err
	}
	return nil
}

// 用于对外部系统调用请求进行认证
func (this *GatewayServer) decryptAPIKeys() error {
	for i, apiKey := range this.opts.APIKeys {
		if apiKey.EncryptedAPISecret != "" {
			apiSecret, err := secrets.Decrypt(apiKey.EncryptedAPISecret)
			if err != nil {
				return fmt.Errorf("decrypt api keys failed, api key (%s), error: %s", apiKey.APIKey, err)
			} else {
				this.opts.APIKeys[i].APISecret = secrets.SecretString(apiSecret)
			}
		}
	}
	return nil
}

func (this *GatewayServer) checkTradingStatusLoop() {
	if this.platform == IB {
		// IB 从 instrument 可以获取交易状态，无需单独判断
		return
	}

	ticker := time.NewTicker(1 * time.Second)
	for {
		<-ticker.C
		zlog.Debugf("check instrument trading status")
		nowTime := time.Now()
		this.Instruments.Range(func(symbol string, instrument *Instrument) bool {
			// 对于不在 TradingSymbols 中的品种，每隔 5 秒钟检查一次
			if !SliceContains(this.storage.TradingSymbols, symbol) && nowTime.Second()%5 != 0 {
				return true
			}
			isTrading := this.IsInstrumentTrading(symbol)
			oldStatus := instrument.Status
			if isTrading && instrument.Status != exchange.InstrumentStatusContinuous {
				instrument.Status = exchange.InstrumentStatusContinuous
			}
			if !isTrading && instrument.Status == exchange.InstrumentStatusContinuous {
				instrument.Status = exchange.InstrumentStatusClosed
			}
			// 状态变化后实时推送 instrument
			// TODO: 后续可以做得更加精细一些，比如单独推送单个 instrument
			if instrument.Status != oldStatus {
				nowTime := time.Now()
				instrument.UpdateTime = nowTime
				this.notifyFlush()
				location, _ := time.LoadLocation("")
				nowUTC := nowTime.In(location)
				zlog.Infof("[%d] trading status changed (%s): %s -> %s", nowUTC.Unix(), instrument.Symbol, oldStatus, instrument.Status)
			}
			return true
		})
	}
}

// 向某个 websocket 连接增加一个订阅的 key
func (this *GatewayServer) AddSubscribeKey(remoteAddr, key string) {
	subKeys := []string{}
	if keys, found := this.connSubscribedKeys[remoteAddr]; found {
		subKeys = keys
	} else {
		this.connSubscribedKeys[remoteAddr] = subKeys
	}
	if !SliceContains(subKeys, key) {
		subKeys = append(subKeys, key)
		this.connSubscribedKeys[remoteAddr] = subKeys
	}
}

// 从某个 websocket 连接去除一个订阅的 key
func (this *GatewayServer) RemoveSubscribeKey(remoteAddr, key string) {
	if keys, found := this.connSubscribedKeys[remoteAddr]; found {
		index := -1
		for i, k := range keys {
			if k == key {
				index = i
			}
		}
		if index > -1 {
			this.connSubscribedKeys[remoteAddr] = append(this.connSubscribedKeys[remoteAddr][:index], this.connSubscribedKeys[remoteAddr][index+1:]...)
		}
	}
}

func (this *GatewayServer) IsTrading(instrumentID string) (yes bool) {
	return SliceContainsEqualFold(this.storage.TradingSymbols, instrumentID)
}

// 不启用某个品种的交易，暂时去除保存该品种的原始 ticks
func (this *GatewayServer) DisbleTrading(instrumentID string) {
	index := -1
	for i, symbol := range this.storage.TradingSymbols {
		if strings.EqualFold(symbol, instrumentID) {
			index = i
		}
	}
	if index > -1 {
		this.storage.TradingSymbols = append(this.storage.TradingSymbols[:index], this.storage.TradingSymbols[index+1:]...)
		this.storage.Save()
	}
}

// 启用某个品种可以交易，暂时主要是判断是否保存该品种的原始 ticks
// 订阅某个品种后，应该允许该品种交易
func (this *GatewayServer) EnableTrading(instrumentID string) error {
	ins, err := this.GetInstrument(instrumentID)
	if err != nil {
		return fmt.Errorf("enable trading failed, get instrument failed, instrument: %s, error: %s", instrumentID, err)
	}
	if this.isInstrumentDelivered(instrumentID) {
		return fmt.Errorf("enable trading failed, instrument delivered, instrument: %s", instrumentID)
	}
	productID := ins.UnderlyCurrency
	if productID == "" && this.platform == MT5 {
		return fmt.Errorf("enable trading failed, product id not found for instrument: %s", instrumentID)
	}
	if !SliceContainsEqualFold(this.storage.TradingSymbols, instrumentID) {
		this.storage.TradingSymbols = append(this.storage.TradingSymbols, instrumentID)
		this.storage.Save()
	}
	return nil
}

func (this *GatewayServer) CheckKLineAllowed(instrumentID string) bool {
	return true
}

// 在合约过期前 n 天，自动平仓持有的即将过期的仓位，避免实物交割
// func (this *GatewayServer) checkExpiringPosition() {
// 	ctpPositions, err := this.mt.GetPositions()
// 	if err != nil {
// 		return
// 	}
// 	ctpInstruments, err := this.mt.GetInstruments()
// 	if err != nil {
// 		return
// 	}
// 	ctpPositions.Range(func(k, v any) bool {
// 		pf := v.(*gomt.PositionField)
// 		if pf.Position == 0 {
// 			return true
// 		}

// 		i, found := ctpInstruments.Load(pf.InstrumentID)
// 		if !found {
// 			return true
// 		}
// 		ins := i.(*gomt.InstrumentField)
// 		st, found := ctpInstrumentStatuss.Load(ins.ProductID)
// 		status := st.(*gomt.InstrumentStatus)
// 		if !found || status.InstrumentStatus != gomt.InstrumentStatusContinuous {
// 			return true
// 		}

// 		if this.isInstrumentExpiredSoon(pf.InstrumentID) {
// 			this.SendMsgf("%s 持仓即将到期，将于 10 分钟后自动平仓。", pf.InstrumentID)
// 			go func() {
// 				time.Sleep(time.Minute * 10)
// 				errorMsgs := this.closePosition(pf.InstrumentID, 0)
// 				if len(errorMsgs) > 0 {
// 					this.ErrorMsgf("自动平仓出现错误，errors ： %s", SliceStringJoin(errorMsgs, "\n", false))
// 				}
// 			}()
// 		}
// 		return true
// 	})
// }

// 获取基本请求 request
func (this *GatewayServer) getBaseRequest() *resty.Request {
	client := resty.New()
	client.
		SetBaseURL(fmt.Sprintf("http://127.0.0.1%s", this.opts.HTTPAddr)).
		SetTimeout(10 * time.Second).     // 设置超时时间，超时无反应则返回超时错误
		SetRetryCount(0).                 // 请求失败时的重试次数
		SetRetryWaitTime(5 * time.Second) // 重试前等待时间

	req := client.R()
	req.SetHeader("Content-Type", "application/json")
	return req
}

func (this *GatewayServer) sendHTTPRequest(httpMethod, requestPath string, data, result any) (_ error) {
	req := this.getBaseRequest()

	if data != nil {
		payloadData, err := json.Marshal(data)
		if err != nil {
			return errors.New("sendHTTPRequest: Unable to JSON request")
		}
		req.SetBody(payloadData)
	}

	resp, err := req.Execute(httpMethod, requestPath)
	if err != nil {
		zlog.Errorf("send http request(%s) err: %s", requestPath, err)
		return err
	}

	if resp.StatusCode() != 200 {
		zlog.Errorf("request(%s) error: %d - %s", requestPath, resp.StatusCode(), resp)
		msg := gjson.Parse(resp.String()).Get("message").String()
		return fmt.Errorf("http request error: %s", msg)
	}

	return json.Unmarshal(resp.Body(), result)
}

func (this *GatewayServer) recordSwapRate() {
	if this.client.GetPlatform() == MT5 {
		if !this.Launched.Load() {
			zlog.Errorf("record swap failed, gateway not launched")
			return
		}
		if !this.client.GetOwnedProcess(Adapter).Running.Load() {
			zlog.Errorf("record swap failed, adapter not running")
			return
		}
		allSymbols, err := this.GetMTClient().mtGetSymbols("")
		if err != nil {
			zlog.Errorf("record swap get symbols from mt failed, error: %s", err)
		}

		for _, s := range allSymbols {
			if SliceContains(this.storage.TradingSymbols, s.Name) {
				this.storage.logSwapRate(s)
			}
		}
	}
}

func (this *GatewayServer) recordPositionHistory() {
	if this.client.GetPlatform() == MT5 {
		allPositions, err := this.GetMTClient().mtGetPositions("")
		if err != nil {
			zlog.Errorf("record swap get positions from mt failed, error: %s", err)
		}
		for _, p := range allPositions {
			this.storage.logPositionSwap(p)
		}
	}
}

func (this *GatewayServer) syncOrders() {
	if this.client.GetPlatform() != IB {
		return
	}

	ib := this.client.(*IBClient)
	trades, er := ib.ibAllTrades()
	if er != nil {
		zlog.Errorf("ib get trades error: %s", er)
		return
	}
	for _, trade := range trades {
		if trade.Order.PermId != 0 {
			order := trade.toOrder()
			this.storage.saveOrder(order)
		}
	}

	this.storage.clearUpOrders()
}

// IB 只能获取到挂单和当天完成的订单
// 历史完成订单需要查询历史成交情况，最多查 7 天，但数据不完全，需结合本地数据更新
// 程序运行正常的情况下，syncOrders 会同步当天完成的订单到本地
// 程序异常没有同步成功，syncOrderExecutions 通过本地订单查历史成交更新
func (this *GatewayServer) syncOrderExecutions() {
	if this.client.GetPlatform() != IB {
		return
	}

	this.SyncingOrderExecutions.Store(true)
	defer this.SyncingOrderExecutions.Store(false)

	ib := this.client.(*IBClient)
	executions, er := ib.ibGetExecutions()
	if er != nil {
		zlog.Errorf("ib get executions error: %s", er)
		return
	}

	openOrders, err := ib.GetOpenOrders("", exchange.UnknownOrderType)
	if err != nil {
		zlog.Errorf("ib get open orders error: %s", err)
		return
	}
	openOrderIDs := []string{}
	for _, order := range openOrders {
		openOrderIDs = append(openOrderIDs, order.OrderID)
	}

	for _, order := range this.storage.Orders {
		if !order.IsOpen() {
			continue
		}

		if SliceContains(openOrderIDs, order.OrderID) {
			continue
		}

		execQty := 0.0
		execPrice := 0.0
		var execTime int64
		for _, exec := range executions {
			if fmt.Sprintf("%d", exec.PermId) == order.OrderID && exec.CumQty > execQty {
				execQty = exec.CumQty
				execPrice = exec.AvgPrice
				execTime = exec.Time
			}
		}

		if execQty > order.ExecQty {
			order.ExecQty = execQty
			order.ExecPrice = execPrice
			order.UpdateTime = ptr(time.Unix(execTime/1000, 0))

			if order.ExecQty == order.Qty {
				order.Status = exchange.OrderStatusFilled
			} else {
				order.Status = exchange.OrderStatusPartialCancelled
			}

			this.storage.Save()
		}

		if order.ExecQty == 0 {
			order.Status = exchange.OrderStatusCancelled
			this.storage.Save()
		}
	}
}

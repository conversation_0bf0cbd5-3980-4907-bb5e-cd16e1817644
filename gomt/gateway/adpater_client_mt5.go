package gateway

import (
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"go.uber.org/atomic"
)

type MT5Client struct {
	BaseClient
	sysErrorDemoInProduction *atomic.Bool
	sysErrorIsHedging        *atomic.Bool
}

func NewMT5Client(gateway *GatewayServer) *MT5Client {
	client := &MT5Client{
		BaseClient:               *NewBaseClient(gateway.platform, gateway),
		sysErrorDemoInProduction: atomic.NewBool(false),
		sysErrorIsHedging:        atomic.NewBool(false),
	}
	client.userID = gateway.opts.MT.UserID
	client.Adaptable = client
	go client.checkProcessLoop()
	go client.checkHealthLoop()
	return client
}

// 处理订单更新、价格更新、合约更新等
func (this *MT5Client) HandleAdapterCallback(jsonData string) error {
	gRes := gjson.Parse(jsonData)
	channel := gRes.Get("channel").String()
	if channel == "" {
		return fmt.Errorf("channel cannot be empty")
	}
	typ := gRes.Get("type").String()
	if channel == "order" && typ == "update" {
		orderData := gRes.Get("data").String()
		orders := &MT5Orders{}
		if err := json.Unmarshal([]byte(orderData), orders); err == nil {
			exOrders := orders.toOrders()
			for _, order := range exOrders {
				packet := exchange.NewOrderPacket("", order)
				this.gateway.sendPacket(packet)
			}
		} else {
			return err
		}
	}
	return nil
}

func (this *MT5Client) GetAdapterScriptArgs() []string {
	host := this.gateway.opts.HTTPAddr
	if strings.HasPrefix(this.gateway.opts.HTTPAddr, ":") {
		host = fmt.Sprintf("localhost%s", this.gateway.opts.HTTPAddr)
	}
	return []string{
		fmt.Sprintf("%d", this.gateway.opts.AdapterPort),
		fmt.Sprintf("http://%s/v1/callback", host),
	}
}

func (this *MT5Client) Initialize() (er error) {
	url := this.getURL("/initialize")
	exePath := ""
	if this.terminal.ExePath() != "" {
		exePath = this.terminal.ExePath()
	}
	initArgs := &MT5InitializeArgs{
		Path:     exePath,
		Login:    this.gateway.opts.MT.GetUserID(),
		Password: this.gateway.opts.MT.Password,
		Server:   this.gateway.opts.MT.Server,
		Timeout:  10,
		Portable: false,
	}
	if initArgs.Login == 0 || initArgs.Password == "" || initArgs.Server == "" {
		er = fmt.Errorf("MetaTrader credentials error, please check: userID, password and server is not empty")
		return
	}
	terminalInfo := &MT5TerminalInfo{}
	er = this.sendHTTPRequest(resty.MethodPost, url, initArgs, terminalInfo)
	if er == nil {
		if this.platform == MT5 && !slices.Contains([]string{"MetaTrader 5", "Swissquote Bank MT5 Client Terminal"}, terminalInfo.Name) {
			er = fmt.Errorf("terminal platform not match, required: %s, terminal: %s", this.platform, terminalInfo.Name)
		}
		if !terminalInfo.TradeAllowed {
			er = fmt.Errorf("trading disabled by mt terminal")
		}
	}
	return
}

func (this *MT5Client) mtGetTerminalInfo() (terminalInfo *MT5TerminalInfo, er error) {
	url := this.getURL("/terminal_info")
	terminalInfo = &MT5TerminalInfo{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, terminalInfo)
	return
}

func (this *MT5Client) GetTerminalInfo() (terminalInfo *TerminalInfo, er error) {
	inf, err := this.mtGetTerminalInfo()
	er = err
	terminalInfo = &TerminalInfo{
		Connected:    inf.Connected,
		Name:         inf.Name,
		PingLast:     inf.PingLast,
		TradeAllowed: inf.TradeAllowed,
	}
	return
}

func (this *MT5Client) mtGetAccountInfo() (accountInfo *MT5AccountInfo, er error) {
	url := this.getURL("/account_info")
	accountInfo = &MT5AccountInfo{}
	// 如果 platform 是 MT4，并且 MT4 的 AccountInfo 差别较大，可以判断后赋值为 MT4AccountInfo
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, accountInfo)
	if er == nil {
		if !this.gateway.Debug && accountInfo.getAccountType() != AccountTypeReal {
			er = fmt.Errorf("can not use demo account in production, current account type: %s, #%d", accountInfo.getAccountType(), accountInfo.Login)
			this.sysErrorDemoInProduction.Store(true)
		} else {
			this.sysErrorDemoInProduction.Store(false)
		}
		// 要求账号必须是 Non-Hedge 模式
		// 临时测试如果有问题，可以注释掉
		if accountInfo.isHedging() {
			er = fmt.Errorf("hedge mode not supported, account #%d", accountInfo.Login)
			this.sysErrorIsHedging.Store(true)
		} else {
			this.sysErrorIsHedging.Store(false)
		}
	}
	return
}

func (this *MT5Client) GetAccountBalances() (balances []*exchange.AccountBalance, er error) {
	accountInfo, err := this.mtGetAccountInfo()
	er = err
	if er == nil {
		balances = []*exchange.AccountBalance{accountInfo.toAccountBalance()}
	}
	return
}

func (this *MT5Client) GetUserMargin() (margin *exchange.UserMargin, er error) {
	accountInfo, err := this.mtGetAccountInfo()
	er = err
	if er == nil {
		margin = accountInfo.toUserMargin()
	}
	return
}

func (this *MT5Client) mtGetPositions(instrumentID string) (positions MT5Positions, er error) {
	url := this.getURL(fmt.Sprintf("/positions?symbol=%s", instrumentID))
	positions = MT5Positions{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &positions)
	return
}

func (this *MT5Client) GetPositions(instrumentID string) (positions []*exchange.Position, er error) {
	positions = []*exchange.Position{}
	mtPositions, err := this.mtGetPositions(instrumentID)
	er = err
	if er == nil {
		positions = mtPositions.toPositions()
	}
	return
}

func (this *MT5Client) mtSymbolSelect(symbol string, enable bool) (er error) {
	url := this.getURL("/symbol_select")
	request := map[string]interface{}{
		"symbol": symbol,
		"enable": enable,
	}
	er = this.sendHTTPRequest(resty.MethodPost, url, request, &map[string]string{})
	return
}

func (this *MT5Client) mtGetLastPrice(instrumentID string) (symbolInfoTick *MT5SymbolInfoTick, er error) {
	url := this.getURL("/symbol_info_tick?symbol=%s", instrumentID)
	symbolInfoTick = &MT5SymbolInfoTick{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, symbolInfoTick)
	if er != nil && strings.Contains(er.Error(), "Not found") {
		// 添加后再试一次
		if err := this.mtSymbolSelect(instrumentID, true); err == nil {
			zlog.Infof("symbol %s selected", instrumentID)
			time.Sleep(time.Millisecond * 500) // 稍等再获取，否则拿到的是 0
			er = this.sendHTTPRequest(resty.MethodGet, url, nil, symbolInfoTick)
		}
	}
	if er == nil && symbolInfoTick.Ask == 0 && symbolInfoTick.Bid == 0 {
		er = errors.New("ask & bid is 0")
	}
	return
}

func (this *MT5Client) GetLastPrice(instrumentID string) (price float64, updateTime time.Time, er error) {
	symbolInfoTick, err := this.mtGetLastPrice(instrumentID)
	er = err
	if er == nil {
		price = symbolInfoTick.GetLastPrice()
		updateTime = symbolInfoTick.GetUpdateTime()
		// 顺便更新 instrument 上的上次成交价
		if instrument, found := this.gateway.Instruments.Load(instrumentID); found {
			instrument.LastPrice = price
			instrument.LastPriceUpdateTime = updateTime
			instrument.UpdateTime = updateTime
			this.gateway.Instruments.Store(instrumentID, instrument)
		}
	}
	return
}

func (this *MT5Client) mtGetKlines(symbol string, period MT5_TIME_FRAME, dateFrom, dateTo int64) (klines exchange.KLines, er error) {
	path := fmt.Sprintf("/klines?symbol=%s&timeframe=%d&date_from=%d&date_to=%d", symbol, period, dateFrom, dateTo)
	url := this.getURL(path)
	klines = exchange.KLines{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &klines)
	return
}

func (this *MT5Client) GetKlines(symbol string, period string, dateFrom, dateTo int64) (klines exchange.KLines, er error) {
	return this.mtGetKlines(symbol, MT5_TIME_FRAME_D1, dateFrom, dateTo)
}

func (this *MT5Client) mtGetSymbols(group string) (symbols MT5SymbolInfos, er error) {
	path := "/symbols"
	if group != "" {
		path = fmt.Sprintf("%s?group=%s", path, group)
	}
	url := this.getURL(path)
	symbols = MT5SymbolInfos{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &symbols)
	return
}

func (this *MT5Client) GetInstruments(symbols string) (instruments []*exchange.Instrument, er error) {
	symbolInfos, err := this.mtGetSymbols(symbols)
	er = err
	if er == nil {
		instruments = symbolInfos.toInstruments()
	}
	// 但从 mt 获取不了交易状态，必须单独更新
	for _, instrument := range instruments {
		isTrading := this.gateway.IsInstrumentTrading(instrument.Symbol)
		if isTrading {
			instrument.Status = exchange.InstrumentStatusContinuous
		}
	}
	return
}

func (this *MT5Client) mtGetOpenOrders(instrumentID string) (orders MT5Orders, er error) {
	url := this.getURL("/orders?symbol=%s", instrumentID)
	orders = MT5Orders{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &orders)
	return
}

func (this *MT5Client) mtGetHistoryOrders(orderID string) (orders MT5Orders, er error) {
	url := this.getURL("/history_orders?ticket=%s", orderID)
	orders = MT5Orders{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &orders)
	if er == nil {
		for _, o := range orders {
			if o.PositionID == 0 {
				continue
			}
			deals, err := this.mtGetHistoryDeals(o.PositionID, 0)
			if err != nil {
				zlog.Errorf("get deals err: %s", err)
				continue
			}
			for _, deal := range deals {
				if deal.Order == o.Ticket {
					o.ExecPrice = deal.Price
					break
				}
			}
		}
	}

	return
}

func (this *MT5Client) mtGetHistoryDeals(positionID int64, ticket int64) (deals MT5Deals, er error) {
	var url string
	if positionID != 0 {
		url = this.getURL("/history_deals?position=%v", positionID)
	} else if ticket != 0 {
		url = this.getURL("/history_deals?ticket=%v", ticket)
	} else {
		return nil, errors.New("invalid args")
	}
	deals = MT5Deals{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &deals)
	return
}

func (this *MT5Client) mtOrderSend(request *MT5OrderSendRequest) (res *MT5TradeResult, er error) {
	url := this.getURL("/order_send")
	res = &MT5TradeResult{}
	er = this.sendHTTPRequest(resty.MethodPost, url, request, res)
	return
}

func (this *MT5Client) CreateOrder(args *exchange.CreateOrderArgs) (order *Order, er error) {
	/*
		特殊的返回错误：
		非交易时间：10018 - Market closed
	*/
	request := &MT5OrderSendRequest{
		Symbol:      args.Symbol,
		Volume:      args.Qty,
		Price:       args.Price,
		TypeFilling: MT5_ORDER_FILLING_RETURN,
		TypeTime:    MT5_ORDER_TIME_GTC,
	}
	nowTime := time.Now()

	if args.TimeInForce == exchange.IOC {
		request.TypeFilling = MT5_ORDER_FILLING_IOC
	} else if args.TimeInForce == exchange.GTD {
		request.TypeTime = MT5_ORDER_TIME_DAY
	} else if args.TimeInForce == exchange.GTE {
		request.TypeTime = MT5_ORDER_TIME_SPECIFIED
	}

	if args.Type == exchange.Market {
		request.Action = MT5_TRADE_ACTION_DEAL
		if args.Side == exchange.OrderSideBuy {
			request.Type = MT5_ORDER_TYPE_BUY
		} else {
			request.Type = MT5_ORDER_TYPE_SELL
		}
	} else if args.Type == exchange.Limit {
		request.Action = MT5_TRADE_ACTION_PENDING
		if args.Side == exchange.OrderSideBuy {
			request.Type = MT5_ORDER_TYPE_BUY_LIMIT
		} else {
			request.Type = MT5_ORDER_TYPE_SELL_LIMIT
		}
	} else if args.Type == exchange.StopLimit {
		// MT5 StopLimit 挂单规则：
		// - BUY_STOP_LIMIT 触发价需高于卖一价[ask]，limit 价[stoplimit]需低于触发价
		// - SELL_STOP_LIMIT 触发价需低于买一价[bid]，limit 价需高于触发价
		// - STOP_LIMIT 单触发后，订单 ID 不会变，但 type 会变成 limit，price 值为 stoplimit，stoplimit 为 0
		// - BUY_STOP 触发价需高于卖一价，触发后市价成交
		// - SELL_STOP 触发价需低于买一价，触发后市价成交
		// - STOP 单触发后 type 不会变
		request.Action = MT5_TRADE_ACTION_PENDING
		request.Comment = setStopLimitOrderComment(args.TriggerPrice)

		tick, err := this.mtGetLastPrice(args.Symbol)
		if err != nil {
			return nil, err
		}

		if args.Side == exchange.OrderSideBuy {
			if args.TriggerPrice > tick.Ask && args.Price <= args.TriggerPrice {
				request.Type = MT5_ORDER_TYPE_BUY_STOP_LIMIT
				request.Price = args.TriggerPrice
				request.StopLimit = args.Price
			} else if args.TriggerPrice > tick.Ask && args.Price > args.TriggerPrice {
				request.Type = MT5_ORDER_TYPE_BUY_STOP
				request.Price = args.TriggerPrice
			} else {
				request.Type = MT5_ORDER_TYPE_BUY_LIMIT
				request.Price = args.Price
			}
		} else {
			if args.TriggerPrice < tick.Bid && args.Price >= args.TriggerPrice {
				request.Type = MT5_ORDER_TYPE_SELL_STOP_LIMIT
				request.Price = args.TriggerPrice
				request.StopLimit = args.Price
			} else if args.TriggerPrice < tick.Bid && args.Price < args.TriggerPrice {
				request.Type = MT5_ORDER_TYPE_SELL_STOP
				request.Price = args.TriggerPrice
			} else {
				request.Type = MT5_ORDER_TYPE_SELL_LIMIT
				request.Price = args.Price
			}
		}

	} else {
		er = fmt.Errorf("order type not supported")
		return
	}

	retryTimes := 0
	var orderID, dealID int64
	for {
		zlog.Debugf("order send request: %#v", request)
		result, err := this.mtOrderSend(request)
		if err != nil {
			er = fmt.Errorf("order send error: %s", err)
			return
		}

		if args.Type == exchange.Market && retryTimes < 5 && result.RetCode == 10004 && result.Order == 0 {
			// 市价单遇到报价错误可以重试
			retryTimes += 1
			zlog.Debugf("order send got 'Requote' err, retry %d...", retryTimes)
			continue
		}

		if result.Order == 0 {
			er = fmt.Errorf("order send failed, %v - %s", result.RetCode, result.Comment)
			return
		}

		orderID = result.Order
		dealID = result.Deal
		break
	}

	order = &Order{
		InstrumentType:   exchange.USDXMarginedFutures,
		Symbol:           args.Symbol,
		OrderID:          fmt.Sprintf("%v", orderID),
		Price:            args.Price,
		TriggerPrice:     args.TriggerPrice,
		TriggerDirection: args.TriggerDirection,
		Qty:              args.Qty,
		Type:             args.Type,
		Side:             args.Side,
		Status:           exchange.OrderStatusNew,
		TimeInForce:      args.TimeInForce,
		ReduceOnly:       args.ReduceOnly,
		CreateTime:       &nowTime,
		UpdateTime:       &nowTime,
	}

	if dealID != 0 {
		deals, err := this.mtGetHistoryDeals(0, dealID)
		if err == nil {
			for _, deal := range deals {
				order.ExecPrice = deal.Price
				order.ExecQty = deal.Volume
				if order.ExecQty == order.Qty {
					order.Status = exchange.OrderStatusFilled
				} else {
					order.Status = exchange.OrderStatusPartialFilled
				}
			}
		} else {
			zlog.Errorf("get deals err: %s", err)
		}
	}
	return
}

func (this *MT5Client) CancelOrder(orderID string) (order *Order, er error) {
	oID, err := strconv.ParseInt(orderID, 10, 64)
	if err != nil {
		er = fmt.Errorf("invalid orderID format, need a int: (%s)", orderID)
		return
	}
	// 获取 Open orders 开销较小
	_, err2 := this.GetOpenOrderByID(fmt.Sprintf("%d", oID))
	if err2 != nil {
		er = fmt.Errorf("get open order by id failed, error: %s", err2)
		return
	}

	request := &MT5OrderSendRequest{
		Action: MT5_TRADE_ACTION_REMOVE,
		Order:  oID,
	}

	result, err := this.mtOrderSend(request)
	if err != nil {
		er = fmt.Errorf("order send error: %s", err)
		return
	}

	if result.Order == 0 {
		er = fmt.Errorf("order send failed, %v - %s", result.RetCode, result.Comment)
		return
	}
	orderAfterCancel, err := this.GetOrderByID(orderID)
	if err != nil {
		er = fmt.Errorf("order canceled, but get new order failed, error: %s", err)
		return
	}
	order = orderAfterCancel
	return
}

func (this *MT5Client) GetOpenOrders(instrumentID string, orderType exchange.OrderType) (orders []*Order, er error) {
	orders = []*Order{}
	mtOrders, err := this.mtGetOpenOrders(instrumentID)
	er = err
	if er == nil {
		allOrders := mtOrders.toOrders()
		if orderType == exchange.UnknownOrderType {
			orders = allOrders
		} else {
			for _, o := range allOrders {
				if o.Type == orderType {
					orders = append(orders, o)
				}
			}
		}
	}
	return
}

func (this *MT5Client) GetOpenOrderByID(orderID string) (order *Order, er error) {
	orders, err := this.GetOpenOrders("", exchange.UnknownOrderType)
	if err != nil {
		er = fmt.Errorf("get open order by id failed, error: %s", err)
		return
	}
	for _, o := range orders {
		if o.OrderID == orderID {
			order = o
			break
		}
	}
	if order == nil {
		er = fmt.Errorf("order not found")
	}
	return
}

func (this *MT5Client) QueryOrdersByID(orderIDs []string) (orders []*Order, er error) {
	orders = []*Order{}
	openOrders := []*Order{}
	mtOrders, err := this.mtGetOpenOrders("")
	er = err
	if er == nil {
		openOrders = append(openOrders, mtOrders.toOrders()...)
	}
	orderIDsNotFound := []string{} // 在 OpenOrders 中没找到的订单
	for _, orderID := range orderIDs {
		var orderFound *Order
		for _, o := range openOrders {
			if o.OrderID == orderID {
				orderFound = o
			}
		}
		if orderFound != nil {
			orders = append(orders, orderFound)
		} else {
			orderIDsNotFound = append(orderIDsNotFound, orderID)
		}
	}
	// 在历史订单中查找在 OpenOrders 中没找到的订单
	for _, orderID := range orderIDsNotFound {
		mtHisOrders, err := this.mtGetHistoryOrders(orderID)
		hisOrders := mtHisOrders.toOrders()
		// TODO: 如果是没找到，可以忽略错误
		er = err
		for _, o := range hisOrders {
			if o.OrderID == orderID {
				orders = append(orders, o)
			}
		}
	}
	return
}

func (this *MT5Client) GetOrderByID(orderID string) (order *Order, er error) {
	orders, err := this.QueryOrdersByID([]string{orderID})

	if err == nil {
		for _, o := range orders {
			if o.OrderID == orderID {
				order = o
				break
			}
		}
	}
	if order == nil {
		er = fmt.Errorf("order not found")
		return
	}
	return
}

func (this *MT5Client) Subscribe(instrumentID string) error {
	return exchange.ErrNotImplemented
}

func (this *MT5Client) QueryInstrumentMarginRates() error {
	return exchange.ErrNotImplemented
}

func (this *MT5Client) getBaseRequest() *resty.Request {
	this.http.SetBaseURL(fmt.Sprintf("http://127.0.0.1:%d", this.gateway.opts.AdapterPort)).
		SetTimeout(10 * time.Second).     // 设置超时时间，超时无反应则返回超时错误
		SetRetryCount(0).                 // 请求失败时的重试次数
		SetRetryWaitTime(5 * time.Second) // 重试前等待时间
	req := this.http.R()
	req.SetHeader("Content-Type", "application/json")
	return req
}

func (this *MT5Client) sendHTTPRequest(httpMethod, requestPath string, data, result any) (er error) {
	defer func() {
		if er != nil {
			zlog.Errorf("send http request failed, path: %s, error: %s", requestPath, er)
		}
	}()
	if !this.adapter.Running.Load() {
		er = fmt.Errorf("adapter is not running")
		return
	}

	// 如果有系统错误，阻止 API 操作
	if !(strings.Contains(requestPath, "/initialize") || strings.Contains(requestPath, "/account_info")) {
		if this.sysErrorDemoInProduction.Load() {
			er = fmt.Errorf("can not use demo account in production, account #%d, http request aborted", this.gateway.opts.MT.GetUserID())
			return
		}

		if this.sysErrorIsHedging.Load() {
			er = fmt.Errorf("hedge mode not supported, account #%d, http request aborted", this.gateway.opts.MT.GetUserID())
			return
		}
	}

	req := this.getBaseRequest()

	if data != nil {
		payloadData, err := json.Marshal(data)
		if err != nil {
			er = errors.New("unable to marshal JSON request")
			return
		}
		req.SetBody(payloadData)
	}

	resp, err := req.Execute(httpMethod, requestPath)
	if err != nil {
		er = fmt.Errorf("http execute failed, error: %s", err)
		return
	}

	if resp.StatusCode() != 200 {
		zlog.Debugf("error response: %s", resp.String())

		msg := gjson.Parse(resp.String()).Get("message").String()
		er = fmt.Errorf("http status is code: %d, error_message: %s", resp.StatusCode(), msg)
		// 如果是 -10001 错误，表示和 terminal 通讯出错，可以自动重启 terminal
		// 注意：通过此方法重启 terminal 并不会启动 UI
		// [-10001]
		// [9999] not initialized
		// [-10003] IPC initialize failed, Process create failed '\"C:/Users/<USER>/AppData/Roaming/Swissquote Bank MT5 Client Terminal/terminal64.exe\"'
		// [-10004] No IPC connection 有可能也需要处理
		// [-10005] IPC timeout
		// 注意有可能启动 terminal 后，算法交易的开关被重置为不允许算法交易，这个问题对平台稳定运行影响较大，需要关注
		if strings.HasPrefix(msg, "[-10001]") || strings.HasPrefix(msg, "[9999]") || strings.HasPrefix(msg, "[-10004]") {
			if !strings.Contains(requestPath, "/initialize") {
				this.Initialize()
				zlog.Errorf("terminal IPC failed, reinitialize")
			}
		}
		return
	}

	if result != nil {
		err = json.Unmarshal(resp.Body(), result)
		if err != nil {
			er = fmt.Errorf("unmarshal result json failed, error: %s", err)
		}
	}
	return
}

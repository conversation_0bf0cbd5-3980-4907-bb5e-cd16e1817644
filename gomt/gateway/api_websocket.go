package gateway

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

var upgrader = websocket.Upgrader{
	EnableCompression: true, // 设置为 true 时，当消息中有特殊字符时，会导致 writeJSON 报错
}

func (this *GatewayServer) websocketHandler(ctx *gin.Context) {
	ws := &WebsocketWithLock{
		gateway: this,
		Mutex:   sync.Mutex{},
	}

	conn, err := upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		zlog.Errorf("error setup websocket connection, error: %s", err)
		return
	} else {
		ws.Conn = conn
	}

	// 如果 chan 太小，发送的 Instrument 太多，可能会收不到消息
	msgChan := make(chan *exchange.Packet, 20000)
	closeChan := make(chan struct{})

	apiKey := ""
	remoteAddr := ws.RemoteAddr()
	this.connMap.Store(remoteAddr, msgChan)

	defer func() {
		close(closeChan)
		this.connMap.Delete(remoteAddr)
		close(msgChan)
		ws.Close()
	}()

	go func() {
		for {
			select {
			case <-closeChan:
				return
			case packet := <-msgChan:
				// 判断是否订阅，如果订阅了就发送数据；如果没订阅啥都不做
				if ok := ws.IsKeySubscribed(packet.Key); ok {
					// 可能有并发写导致崩溃的问题，在 sendPacket 处进行并发控制
					if isInstrument, _ := packet.CheckInstrument(); isInstrument {
						packet.SetID()
					}
					// 广播消息不自带 APIKey，必须设为客户端的 APIKey
					packet.APIKey = apiKey
					err = ws.WriteJSON(packet)

					if err != nil {
						zlog.Errorf("write packet failed, error: %s, packet: %s", err, packet.PacketHeader)
					}
				}
				// zlog.Debugf("packet (%s) isn't subscribed in keys: (%s)", packet.Key, SliceStringJoin(subscribedKeys, ",", false))
			}
		}
	}()

	for {
		select {
		case <-closeChan:
			return
		default:
		}

		clientPacket := &exchange.ClientPacket{}
		if err := ws.ReadJSON(clientPacket); err != nil {
			// 可能碰到以下一些错误：
			// 1. read tcp ***************:5006->************:33996: wsarecv: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
			// 2. unexpected EOF
			// 3. An established connection was aborted by the software in your host machine.
			// 4. read: connection reset by peer
			// 看哪些错误可以
			connectionGone := strings.Contains(err.Error(), "unexpected EOF") ||
				strings.Contains(err.Error(), "An established connection was aborted by the software in your host machine.") ||
				strings.Contains(err.Error(), "read: connection reset by peer")
			if connectionGone {
				zlog.Errorf("read incoming response packet failed, unexpected close, error: %s", err)
				return
			} else {
				zlog.Debugf("read incoming response packet failed, error: %s", err)
				time.Sleep(100 * time.Millisecond)
				continue
			}
		}
		// zlog.Debugf("received message: ID(%s), Event(%s), Key(%s)", clientPacket.ID, clientPacket.Event, clientPacket.Key)

		if this.opts.CheckSign && !clientPacket.CheckSign(this.opts.getAPISecret(clientPacket.APIKey)) {
			zlog.Errorf("check incoming signature failed, packet: %s", clientPacket.PacketHeader)
			packet := exchange.NewCheckSignErrorPacket(clientPacket)
			ws.WriteJSON(packet)
			continue
		}

		var responsePacket *exchange.Packet
		if clientPacket.CheckPing() {
			responsePacket = exchange.NewPongPacket(clientPacket)
		} else if yes, key := clientPacket.CheckSubscribe(); yes {
			// 订阅的channel
			// Order 的 key: {InstrumentID}.orders
			// Instrument 的 key: {InstrumentID}.instruments
			// Margin 的 key: margin
			// 如果需要订阅所有: all
			// 如果订阅 InstrumentID 的所有: {InstrumentID}
			// 订阅 Instrument 的 order: {InstrumentID}.orders
			// 订阅所有的 order:  orders
			if key != "" {
				// 如果 key 看起来是 instrumentID，尝试启用该 instrumentID
				instrumentID := strings.Split(key, ".")[0]
				// 生产环境必须验证 InstrumentReady
				if !this.Debug {
					if this.InstrumentsReady.Load() {
						if _, err := this.GetInstrument(instrumentID); err != nil {
							responsePacket = exchange.NewSubscribeFailPacket(clientPacket, key, fmt.Sprintf("get instrument failed, error: %s", err))
						}
					} else {
						responsePacket = exchange.NewSubscribeFailPacket(clientPacket, key, "instrument not ready")
					}
				}
				if responsePacket == nil && key != "margin" {
					if err := this.EnableTrading(instrumentID); err != nil {
						zlog.Errorf("error enable trading for key: %s, error: %s", key, err)
						responsePacket = exchange.NewSubscribeFailPacket(clientPacket, key, fmt.Sprintf("enable trading failed, error: %s", err))
					}
				}
				// 如果订阅没有失败，发送订阅成功的 packet
				if responsePacket == nil {
					responsePacket = exchange.NewSubscribeSuccessPacket(clientPacket, key)
					this.AddSubscribeKey(remoteAddr, key)
					apiKey = clientPacket.APIKey
				}
			} else {
				responsePacket = exchange.NewSubscribeFailPacket(clientPacket, key, "key can not be empty")
			}
		} else if yes, key := clientPacket.CheckSlackSubscribe(); yes {
			// 如果是 slack 适配器的订阅请求，检查配置的 slackChannel 是否一致
			// 另外，如果 ctp 服务没有启动，发送 slack 消息提示启动 ctp 服务
			if key == fmt.Sprintf("%s.slack", this.opts.SlackChannel) {
				// 关联 slackChannel 对应的 websocket 地址；后续用于查询 slackChannel 对应的 websocket 连接
				this.slackConnAddr = remoteAddr
				if this.Launched.Load() {
					// slack subscribe 可能还没有完成，需要延迟一点发送状态
					time.AfterFunc(2*time.Second, func() {
						this.commandProcessor.Process("status", []string{})
					})
				} else {
					channelName := strings.Split(key, ".")[0]
					msg := fmt.Sprintf("Build: %s, 请输入命令 `.launch Password GoogleAuthCode safe[可选]` 启动程序", this.BuildInfo())
					askForLaunchPacket := exchange.NewSlackResponsePacket(clientPacket.APIKey, channelName, msg)
					ws.WriteJSON(askForLaunchPacket)
				}
			} else {
				responsePacket = exchange.NewSubscribeFailPacket(clientPacket, key, "you are not allowed to subscribe this gateway")
			}
			// 如果 slack 订阅没有失败，发送订阅成功的 packet
			if responsePacket == nil {
				responsePacket = exchange.NewSubscribeSuccessPacket(clientPacket, key)
				this.AddSubscribeKey(remoteAddr, key)
				apiKey = clientPacket.APIKey
			}
		} else if yes, key := clientPacket.CheckUnsubscribe(); yes {
			if key != "" {
				this.RemoveSubscribeKey(remoteAddr, key)
				// 如果是 slack 适配器取消订阅的请求，重置 slackChannel 对应的 websocket 连接
				if strings.HasSuffix(key, "slack") {
					this.slackConnAddr = ""
				}
				responsePacket = exchange.NewUnsubscribeSuccessPacket(clientPacket, key)
			} else {
				responsePacket = exchange.NewUnsubscribeFailPacket(clientPacket, key, "key can not be empty")
			}
		} else if yes, channelName := clientPacket.CheckSlackRequest(); yes {
			responsePacket = exchange.NewSlackRequestAckPacket(clientPacket, channelName)
			go this.messenger.handleMessage(channelName, clientPacket)
		} else {
			zlog.Errorf("unknown websocket command: %s", clientPacket.Event)
			continue
		}

		if responsePacket != nil {
			err = ws.WriteJSON(responsePacket)
			if err != nil {
				zlog.Errorf("write json to websocket error: %s", err)
			}
		}
	}
}

// 手工触发消息推送
func (this *GatewayServer) notifyFlush() {
	this.notifyChan <- 1
}

// 这里处理需要定时发送的数据
func (this *GatewayServer) websocketLoop() {
	ticker := time.NewTicker(time.Duration(this.opts.Websocket.FlushInterval) * time.Millisecond)
	getAllInstrumentsTicker := time.NewTicker(60 * time.Second)

	for i := 0; ; i++ {
		select {
		case <-this.exitChan:
			return
		case <-this.notifyChan:
		case <-ticker.C:
			// zlog.Debugf("flush interval meet, send websocket messages")

			// 按 InstrumentID 单个推送；多个 instruments 推送 key 不好处理，不要那样做
			// 通过 tradingSymbols 过滤，可以大量减少 sendPacket 的量
			if this.InstrumentsReady.Load() {
				for _, instrumentID := range this.storage.TradingSymbols {
					instruments, err := this.client.GetInstruments(instrumentID)
					if err != nil {
						zlog.Errorf("get instrument failed, instrumentID: %s, error: %s", instrumentID, err)
						continue
					}

					for _, instrument := range instruments {
						if instrument.QuoteToSettleRate == 0 {
							continue
						}
						this.Instruments.Store(instrument.Symbol, instrument)
						packet := exchange.NewInstrumentPacket("", instrument)
						this.sendPacket(packet)
					}
				}
			}

			userMargin, err := this.client.GetUserMargin()
			if err == nil {
				marginPacket := exchange.NewUserMarginPacket("", userMargin)
				this.sendPacket(marginPacket)
			}

		case <-getAllInstrumentsTicker.C:
			if this.InstrumentsReady.Load() {
				instruments, err := this.client.GetInstruments("") // 推所有 Symbols
				if err == nil {
					for _, instrument := range instruments {
						if instrument.QuoteToSettleRate == 0 {
							continue
						}
						this.Instruments.Store(instrument.Symbol, instrument)
						packet := exchange.NewInstrumentPacket("", instrument)
						this.sendPacket(packet)
					}
				}
			}
		}
	}
}

func (this *GatewayServer) sendPacket(packet *exchange.Packet) {
	this.connMap.Range(func(k string, msgChan chan *exchange.Packet) bool {
		select {
		case msgChan <- packet:
		default:
			// zlog.Debugf("skip message for %s, %v", conn.Key, packet.PacketHeader)
		}
		return true
	})
}

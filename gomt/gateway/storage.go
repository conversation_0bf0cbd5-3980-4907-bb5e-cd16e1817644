package gateway

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path"
	"sync"
	"time"

	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
)

type Storage struct {
	gateway        *GatewayServer
	TradingSymbols []string // 允许交易的品种
	lock           *sync.Mutex
	AdapterPID     int // MT 适配器的 PID，下次运行检查
	ordersLock     *sync.Mutex
	Orders         []*Order
}

func setupStorage(gateway *GatewayServer) (s *Storage, er error) {
	if gateway.opts == nil {
		er = fmt.Errorf("gateway option is nil, please init option before setup storage")
		return
	}
	// 检查 DirData 是否存在
	if _, err := os.Stat(gateway.opts.DataDir); os.IsNotExist(err) {
		er = err
		return
	}
	s = &Storage{
		gateway:        gateway,
		TradingSymbols: []string{},
		lock:           &sync.Mutex{},
		ordersLock:     &sync.Mutex{},
	}
	if err := s.Load(); err != nil {
		zlog.Errorf("read from storage failed (%s), error: %s", gateway.opts.DataDir, err)
	} else {
		zlog.Infof("read storage successful")
	}
	return
}

func (this *Storage) Load() error {
	this.lock.Lock()
	defer this.lock.Unlock()

	path := this.getPath()
	f, err := os.OpenFile(path, os.O_RDWR|os.O_CREATE, 0755)
	if err != nil {
		return err
	}
	defer f.Close()

	if content, err := io.ReadAll(f); err != nil {
		return err
	} else {
		if err := json.Unmarshal(content, this); err != nil {
			return err
		}
	}
	instrumentTypeMigrated := this.FixOrderInstrumentType()
	if instrumentTypeMigrated {
		zlog.Infof("migrate instrument type successful, save to storage")
		this.Save()
	}
	return nil
}

func (this *Storage) FixOrderInstrumentType() (migrated bool) {
	for _, order := range this.Orders {
		oldType := order.InstrumentType
		order.InstrumentType = exchange.FixInstrumentType(order.InstrumentType)
		if order.InstrumentType != oldType {
			migrated = true
		}
	}
	return migrated
}

func (this *Storage) getPath() string {
	return path.Join(this.gateway.opts.DataDir, fmt.Sprintf("%s_storage.json", this.gateway.opts.Platform.ToLower()))
}

func (this *Storage) Save() error {
	this.lock.Lock()
	defer this.lock.Unlock()

	path := this.getPath()
	if data, err := json.MarshalIndent(this, "", "    "); err != nil {
		return err
	} else {
		err := os.WriteFile(path, data, 0755)
		return err
	}
}

type SwapRate struct {
	Symbol            string
	Long              float64
	Short             float64
	SwapMode          int
	BaseCurrency      string
	QuoteCurrency     string
	SettleCurrency    string
	QuoteToSettleRate float64
	LongInUSD         float64
	ShortInUSD        float64
	RecordTime        time.Time
}

type PositionHistory struct {
	Symbol        string
	ID            string
	Qty           float64
	Price         float64
	LastPrice     float64
	UnrealizedPNL float64
	Swap          float64
	UpdateTime    time.Time
	RecordTime    time.Time
}

func (this *Storage) logSwapRate(symbol *MT5SymbolInfo) {
	if symbol == nil {
		zlog.Errorf("log swap rate failed, symbol is nil")
		return
	}

	quoteToSettleRate := symbol.TradeTickValue / (symbol.TradeTickSize * symbol.TradeContractSize)
	rate := &SwapRate{
		Symbol:            symbol.Name,
		Long:              symbol.SwapLong,
		Short:             symbol.SwapShort,
		LongInUSD:         symbol.SwapLong,
		ShortInUSD:        symbol.SwapShort,
		SwapMode:          symbol.SwapMode,
		BaseCurrency:      symbol.CurrencyBase,
		QuoteCurrency:     symbol.CurrencyProfit,
		SettleCurrency:    "USD",
		QuoteToSettleRate: quoteToSettleRate,
		RecordTime:        time.Now(),
	}
	archivePath := fmt.Sprintf("swap_rate_%s.json", rate.Symbol)
	archivePath = path.Join(this.gateway.opts.DataDir, archivePath)
	f, err := os.OpenFile(archivePath, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0755)
	if err != nil {
		zlog.Errorf("open log order file err: %s", err)
		return
	}
	defer f.Close()

	if data, err := json.Marshal(rate); err != nil {
		zlog.Errorf("save order log failed, marshal error: %s", err)
		return
	} else {
		f.Write(data)
		_, err := f.WriteString("\n")
		if err != nil {
			zlog.Errorf("log order, write file error: %s", err)
		}
	}
}

func (this *Storage) logPositionSwap(position *MT5Position) {
	if position == nil {
		zlog.Errorf("log swap rate failed, swap rate is nil")
		return
	}
	volume := position.Volume
	if position.Type == 1 {
		volume = -volume
	}
	swap := &PositionHistory{
		Symbol:        position.Symbol,
		ID:            fmt.Sprintf("%d", position.Ticket),
		Qty:           volume,
		Price:         position.PriceOpen,
		LastPrice:     position.PriceCurrent,
		UnrealizedPNL: position.Profit,
		Swap:          position.Swap,
		UpdateTime:    position.GetUpdateTime(),
		RecordTime:    time.Now(),
	}

	archivePath := fmt.Sprintf("position_history_%s.json", swap.Symbol)
	archivePath = path.Join(this.gateway.opts.DataDir, archivePath)
	f, err := os.OpenFile(archivePath, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0755)
	if err != nil {
		zlog.Errorf("open log order file err: %s", err)
		return
	}
	defer f.Close()

	if data, err := json.Marshal(swap); err != nil {
		zlog.Errorf("save order log failed, marshal error: %s", err)
		return
	} else {
		f.Write(data)
		_, err := f.WriteString("\n")
		if err != nil {
			zlog.Errorf("log order, write file error: %s", err)
		}
	}
}

func (this *Storage) saveOrder(order *Order) {
	this.ordersLock.Lock()
	defer this.ordersLock.Unlock()

	found := false
	for i, sOrder := range this.Orders {
		if sOrder.OrderID == order.OrderID {
			found = true
			this.Orders[i] = order
			break
		}
	}
	if !found {
		this.Orders = append(this.Orders, order)
	}
	this.Save()
}

// 删除 30 天前的已完成订单
func (this *Storage) clearUpOrders() {
	this.ordersLock.Lock()
	defer this.ordersLock.Unlock()

	newOrders := []*Order{}
	daysAgo := time.Now().Add(-time.Hour * 24 * 30)
	for _, order := range this.Orders {
		if !order.IsOpen() && order.UpdateTime != nil && order.UpdateTime.Before(daysAgo) {
			continue
		}
		newOrders = append(newOrders, order)
	}
	this.Orders = newOrders
	this.Save()
}

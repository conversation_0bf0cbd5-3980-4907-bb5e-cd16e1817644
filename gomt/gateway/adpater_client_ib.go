package gateway

import (
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/puzpuzpuz/xsync/v2"
	"github.com/tidwall/gjson"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/exchange"
	"github.com/wizhodl/quanter/exchange/gateway"
	"github.com/wizhodl/quanter/utils"
)

const TWS_RESTART_WAIT_TIME = 3 * time.Minute

type IBClient struct {
	BaseClient
	lastRestartTime time.Time
	lastPriceCache  *xsync.MapOf[string, *PriceCache]
}

type PriceCache struct {
	Price      float64
	UpdateTime time.Time
}

type OKMessage struct {
	Message string `json:"message"`
}

func NewIBClient(gateway *GatewayServer) *IBClient {
	client := &IBClient{
		BaseClient:     *NewBaseClient(gateway.platform, gateway),
		lastPriceCache: xsync.NewMapOf[*PriceCache](),
	}
	client.userID = gateway.opts.IB.Username
	client.Adaptable = client
	go client.checkProcessLoop()
	go client.checkHealthLoop()
	go client.autoRestartTerminalLoop()
	return client
}

func (this *IBClient) autoRestartTerminalLoop() {
	// AutoRestartTime is "HH:MM"
	// 实际重启在 1 分钟以后，每 5 秒钟检查一次，下一次检查的时候，已经是下一分钟了，不会导致重复重启
	restartTicker := time.NewTicker(5 * time.Second)
	for {
		<-restartTicker.C
		if this.gateway.opts.IB.AutoRestartTime == "" {
			continue
		}
		now := time.Now()
		if now.Format("15:04") == this.gateway.opts.IB.AutoRestartTime {
			// 1 分钟内不重复重启
			err := this.RestartOwnedProcess(Terminal)
			if err != nil {
				this.gateway.ErrorMsgf("auto restart terminal failed, error: %s", err)
			} else {
				this.lastRestartTime = now
				this.gateway.SendMsgf("auto restarted terminal at %s", now.Format("15:04"))
			}
		}
	}
}

// 处理订单更新、价格更新、合约更新等
func (this *IBClient) HandleAdapterCallback(jsonData string) error {
	gRes := gjson.Parse(jsonData)
	channel := gRes.Get("channel").String()
	if channel == "" {
		return fmt.Errorf("channel cannot be empty")
	}
	typ := gRes.Get("type").String()
	if channel == "order" && typ == "update" {
		orderData := gRes.Get("data").String()
		trades := []*IBTrade{}
		if err := json.Unmarshal([]byte(orderData), &trades); err == nil {
			for _, trade := range trades {
				packet := exchange.NewOrderPacket("", trade.toOrder())
				this.gateway.sendPacket(packet)
			}
		} else {
			return err
		}
	}
	return nil
}

func (this *IBClient) GetAdapterScriptArgs() []string {
	host := this.gateway.opts.HTTPAddr
	if strings.HasPrefix(this.gateway.opts.HTTPAddr, ":") {
		host = fmt.Sprintf("localhost%s", this.gateway.opts.HTTPAddr)
	}
	args := []string{
		fmt.Sprintf("%d", this.gateway.opts.AdapterPort),
		fmt.Sprintf("http://%s/v1/callback", host),
	}
	if this.gateway.opts.IB.Host != "" {
		args = append(args, []string{
			"--gateway_host",
			this.gateway.opts.IB.Host,
		}...)
	}
	if this.gateway.opts.IB.Port != 0 {
		args = append(args, []string{
			"--gateway_port",
			fmt.Sprintf("%d", this.gateway.opts.IB.Port),
		}...)
	}
	if this.gateway.opts.IB.ClientID > -1 {
		args = append(args, []string{
			"--client_id",
			fmt.Sprintf("%d", this.gateway.opts.IB.ClientID),
		}...)
	}
	if this.gateway.opts.IB.Timeout > 0 {
		args = append(args, []string{
			"--timeout",
			fmt.Sprintf("%d", this.gateway.opts.IB.Timeout),
		}...)
	}
	if this.gateway.opts.IB.CommandServerPort > 0 {
		args = append(args, []string{
			"--command_server_port",
			fmt.Sprintf("%d", this.gateway.opts.IB.CommandServerPort),
		}...)
	}
	return args
}

func (this *IBClient) Initialize() (er error) {
	return
}

func (this *IBClient) GetTerminalInfo() (terminalInfo *TerminalInfo, er error) {
	url := this.getURL("/terminal_info")
	terminalInfo = &TerminalInfo{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, terminalInfo)
	terminalInfo.AutoRestartTime = this.gateway.opts.IB.AutoRestartTime
	return
}

func (this *IBClient) GetAccountBalances() (balances []*exchange.AccountBalance, er error) {
	url := this.getURL("/summary")
	summaries := []*IBSummary{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &summaries)
	balancesByCurrency := map[string]*exchange.AccountBalance{}
	for _, summary := range summaries {
		value, _ := strconv.ParseFloat(summary.Value, 64)
		if summary.Tag == "TotalCashBalance" && summary.Currency != "BASE" {
			balance, found := balancesByCurrency[summary.Currency]
			if !found {
				balance = &exchange.AccountBalance{}
				balance.InstrumentType = exchange.Spot
				balance.Currency = summary.Currency
			}
			balance.Total = value
			balance.Available = value
			balancesByCurrency[summary.Currency] = balance
		}
	}
	for _, balance := range balancesByCurrency {
		balances = append(balances, balance)
	}

	// 股票持仓也是现货余额
	positions, err := this.ibGetPositions()
	if err != nil {
		return nil, err
	}
	for _, pos := range positions {
		if pos.SecType != IBStocks {
			continue
		}
		balances = append(balances, &exchange.AccountBalance{
			InstrumentType: exchange.Spot,
			Currency:       pos.Symbol,
			Total:          pos.Position,
			Available:      pos.Position,
		})
	}

	return
}

func (this *IBClient) GetUserMargin() (margin *exchange.UserMargin, er error) {
	url := this.getURL("/summary")
	summaries := []*IBSummary{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &summaries)
	margin = &exchange.UserMargin{Currency: "USD"}
	for _, summary := range summaries {
		value, _ := strconv.ParseFloat(summary.Value, 64)
		// TODO 不确认多个账号的是不是多个值
		if summary.Tag == "NetLiquidationByCurrency" && summary.Currency == "BASE" {
			margin.MarginBalance = value
		} else if summary.Tag == "TotalCashBalance" && summary.Currency == "BASE" {
			margin.AvailableMargin = value
		}
	}
	for _, summary := range summaries {
		if summary.Tag == "UnrealizedPnL" && summary.Currency == "BASE" {
			value, _ := strconv.ParseFloat(summary.Value, 64)
			margin.WalletBalance = margin.MarginBalance - value
		}
	}
	return
}

func (this *IBClient) ibGetPositions() (positions IBPositions, er error) {
	url := this.getURL("/positions")
	positions = IBPositions{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &positions)
	return
}

func (this *IBClient) GetPositions(instrumentID string) (positions []*exchange.Position, er error) {
	positions = []*exchange.Position{}
	ibPositions, err := this.ibGetPositions()
	er = err
	if er == nil {
		for _, p := range ibPositions {
			if p.SecType != IBStocks { // 暂只支持股票类型
				continue
			}

			if p.Symbol == instrumentID || instrumentID == "" {
				positions = append(positions, p.toPosition())
			}
		}
	}
	return
}

// 需要先订阅才能从 tickers 获取到最新价格
func (this *IBClient) ibReqMarket(symbol, exchange, currency string) (er error) {
	path := fmt.Sprintf("/req/market?symbol=%s&exchange=%s&currency=%s", symbol, exchange, currency)
	url := this.getURL(path)
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, nil)
	return
}

func (this *IBClient) ibGetTickers() (tickers []*IBTicker, er error) {
	url := this.getURL("/tickers")
	tickers = []*IBTicker{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &tickers)
	return
}

func (this *IBClient) subscribeTicker(symbol string) error {
	zlog.Infof("ib subscribing ticker for %s", symbol)
	var exchange, currency string
	if contract, err := this.querySymbol(symbol); err != nil {
		return fmt.Errorf("subscribe ticker failed, query symbol failed, error: %v", err)
	} else {
		exchange = contract.Exchange
		currency = contract.Currency
	}
	return this.ibReqMarket(symbol, exchange, currency)
}

func (this *IBClient) getTickerPrice(symbol string) (price float64, updateTime time.Time, er error) {
	tickers, err := this.ibGetTickers()
	if err != nil {
		er = fmt.Errorf("get tickers failed, error: %v", err)
		return
	}
	for _, tick := range tickers {
		if tick.Contract.Symbol == symbol {
			if tick.Last > 0 {
				price = tick.Last
			} else if tick.Close > 0 {
				price = tick.Close
			} else {
				continue
			}
			updateTime = time.Unix(tick.Time/1000, 0)
			return
		}
	}
	er = fmt.Errorf("ticker not found for symbol %s", symbol)
	return
}

func (this *IBClient) GetLastPrice(symbol string) (price float64, updateTime time.Time, er error) {
	price, updateTime, er = this.getTickerPrice(symbol)
	if er != nil && strings.Contains(er.Error(), "ticker not found") {
		// 没找到，订阅后稍等再次尝试
		this.subscribeTicker(symbol)
		time.Sleep(3 * time.Second)
		price, updateTime, er = this.getTickerPrice(symbol)
	}
	if price != 0 {
		this.lastPriceCache.Store(symbol, &PriceCache{Price: price, UpdateTime: updateTime})
	} else {
		if cache, found := this.lastPriceCache.Load(symbol); found && cache.Price != 0 {
			zlog.Debugf("get last price from cache: %#v", cache)
			price = cache.Price
			updateTime = cache.UpdateTime
			return price, updateTime, nil
		}
	}
	return
}

func (this *IBClient) querySymbol(symbol string) (contract *IBContract, er error) {
	if contracts, err := this.ibGetContractDetails(symbol, ""); err != nil {
		return nil, fmt.Errorf("get contract details error: %v", err)
	} else {
		for _, contract := range contracts {
			return &contract.Contract, nil
		}
	}
	return nil, fmt.Errorf("cannot found contract for symbol: %s", symbol)
}

func (this *IBClient) ibGetKlines(symbol, period, duration string) (klines []*IBKLine, er error) {
	var exchange, currency string
	if contract, err := this.querySymbol(symbol); err != nil {
		return nil, fmt.Errorf("query symbol error: %v", err)
	} else {
		exchange = contract.Exchange
		currency = contract.Currency
	}

	query := fmt.Sprintf("symbol=%s&exchange=%s&currency=%s&barSizeSetting=%s&whatToShow=ADJUSTED_LAST&durationStr=%s",
		symbol,
		exchange,
		currency,
		url.QueryEscape(period),
		url.QueryEscape(duration),
	)
	path := fmt.Sprintf("/kline/history?%s", query)
	url := this.getURL(path)
	klines = []*IBKLine{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &klines)
	return
}

func (this *IBClient) GetKlines(symbol string, period string, dateFrom, dateTo int64) (klines exchange.KLines, er error) {
	ibKlines, err := this.ibGetKlines(symbol, "1 day", "13 W")
	if err != nil {
		return nil, err
	}
	for _, line := range ibKlines {
		klines = append(klines, &exchange.KLine{
			Open:  line.Open,
			Close: line.Close,
			High:  line.High,
			Low:   line.Low,
			Time:  line.Date / 1000,
		})
	}
	return
}

func (this *IBClient) ibGetContractDetails(symbol string, currency string) (contracts []*IBContractDetails, er error) {
	path := fmt.Sprintf("/contract/details?symbol=%s&currency=%s", symbol, currency)
	url := this.getURL(path)
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &contracts)
	return
}

func (this *IBClient) ibGetMarketRule(ruleID string) (priceIncrements []*IBPriceIncrement, er error) {
	url := this.getURL(fmt.Sprintf("/market/rule?id=%s", ruleID))
	priceIncrements = []*IBPriceIncrement{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &priceIncrements)
	return
}

func (this *IBClient) getTickSize(details *IBContractDetails, lastPrice float64) (tickSize float64, er error) {
	ids := strings.Split(details.MarketRuleIds, ",")
	rules := []string{}
	for _, id := range ids {
		// id 可能重复，无需再获取
		if SliceContains(rules, id) {
			continue
		}
		rules = append(rules, id)

		priceIncrements, err := this.ibGetMarketRule(id)
		if err != nil {
			return 0, err
		}
		for _, priceIncrement := range priceIncrements {
			if priceIncrement.LowEdge <= lastPrice {
				tickSize = priceIncrement.Increment
			} else {
				break
			}
		}
	}
	if tickSize == 0 {
		tickSize = details.MinTick // 如果从 Rule 拿不到就用这个
	}
	return
}

func (this *IBClient) GetInstruments(symbol string) (instruments []*exchange.Instrument, er error) {
	symbols := []string{symbol}
	if symbol == "" {
		symbols = this.gateway.storage.TradingSymbols
	}
	for _, symbol := range symbols {
		details, err := this.ibGetContractDetails(symbol, "")
		if err != nil {
			return nil, err
		}
		for _, detail := range details {
			instrument, err := detail.toInstrument()
			if err != nil {
				zlog.Errorf("convert contract to instrument error: %v", err)
				continue
			}

			lastPrice, updateTime, err := this.GetLastPrice(instrument.Symbol)
			// 允许 lastPrice == 0 的时候添加 instrument，因为可能还没来得及从行情服务器获取到价格
			// 由上层应用自己处理 lastPrice == 0 的情况
			if lastPrice == 0 {
				zlog.Warnf("get last price failed: zero price, symbol: %s, error: %v", instrument.Symbol, err)
			} else {
				instrument.LastPrice = lastPrice
				instrument.LastPriceUpdateTime = updateTime
				instrument.UpdateTime = updateTime
			}

			tickSize, err := this.getTickSize(detail, lastPrice)
			if err != nil {
				return nil, err
			}
			instrument.TickSize = tickSize

			instruments = append(instruments, instrument)
			// 可能会查到多个，首选第一个
			break
		}
	}
	return
}

func (this *IBClient) ibPlaceOrder(request *IBPlaceOrderRequest) (res *IBTrade, er error) {
	url := this.getURL("/place/order")
	res = &IBTrade{}
	er = this.sendHTTPRequest(resty.MethodPost, url, request, res)
	return
}

func (this *IBClient) CreateOrder(args *exchange.CreateOrderArgs) (order *Order, er error) {
	contract, err := this.querySymbol(args.Symbol)
	if err != nil {
		return nil, fmt.Errorf("query symbol error: %v", err)
	}

	req := &IBPlaceOrderRequest{
		Symbol:        args.Symbol,
		SecType:       contract.SecType,
		Currency:      contract.Currency,
		Exchange:      contract.Exchange,
		TotalQuantity: args.Qty,
		LmtPrice:      args.Price,
		AuxPrice:      args.TriggerPrice,
	}

	if args.TimeInForce == exchange.GTC {
		req.Tif = IBTimeInForceGTC
	} else if args.TimeInForce == exchange.IOC {
		req.Tif = IBTimeInForceIOC
	} else if args.TimeInForce == exchange.GTD {
		req.Tif = IBTimeInForceGTD
	}

	if args.Side == exchange.OrderSideBuy {
		req.Action = IBActionBuy
	} else {
		req.Action = IBActionSell
	}

	if args.Type == exchange.Limit {
		req.OrderType = IBOrderTypeLimit
	} else if args.Type == exchange.Market {
		req.OrderType = IBOrderTypeMarket
	} else if args.Type == exchange.StopLimit {
		req.OrderType = IBOrderTypeStopLimit
	} else {
		return nil, fmt.Errorf("unsupported order type %s", args.Type)
	}

	if args.GetBool(gateway.ExtKeyOutsideRth) {
		req.OutsideRth = true
	}

	ibTrade, err := this.ibPlaceOrder(req)
	if err != nil {
		return nil, err
	}

	// 返回的 PermId 为 0，需稍等再次查询
	for i := 0; i < 10; i++ {
		time.Sleep(200 * time.Millisecond)
		ibTrade, err = this.queryTradeByCliendOrder(ibTrade.Order.ClientId, ibTrade.Order.OrderId)
		if err != nil {
			return nil, err
		}
		order = ibTrade.toOrder()
		if order.OrderID == "0" {
			if order.Status == exchange.OrderStatusCancelled { // 这种能确定是下单失败被取消
				msg := "order cancelled"
				if len(ibTrade.Log) > 0 && ibTrade.Log[len(ibTrade.Log)-1].Message != "" {
					msg = ibTrade.Log[len(ibTrade.Log)-1].Message
				}
				return nil, fmt.Errorf("%s", msg)
			}
			continue
		}

		order.CreateTime = ptr(time.Now())
		this.gateway.storage.saveOrder(order)
		return
	}
	zlog.Errorf("place order no permId: %#v", ibTrade)
	er = fmt.Errorf("place order success but can not get orderID")
	return
}

func (this *IBClient) CancelOrder(orderID string) (order *Order, er error) {
	// 需要根据 IBOrder.permId 查到 IBOrder.orderId 来取消订单
	var ibOrderID int64
	isOpen := false
	openTrades, err := this.ibOpenOrders()
	if err != nil {
		return nil, err
	}
	for _, trade := range openTrades {
		if fmt.Sprintf("%d", trade.Order.PermId) == orderID {
			ibOrderID = trade.Order.OrderId
			isOpen = true
			break
		}
	}
	if !isOpen {
		return nil, errors.New("can not found order in open orders")
	}

	path := fmt.Sprintf("/cancel/order?orderID=%d", ibOrderID)
	url := this.getURL(path)
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, nil)
	if er != nil {
		return nil, er
	}

	// 需再次查询确认已取消
	for i := 0; i < 3; i++ {
		// 可能需要稍等一会
		time.Sleep(100 * time.Millisecond)
		completedTrades, err := this.ibCompletedOrders()
		if err != nil {
			return nil, err
		}
		for _, trade := range completedTrades {
			if fmt.Sprintf("%d", trade.Order.PermId) == orderID {
				return trade.toOrder(), nil
			}
		}
	}

	return nil, fmt.Errorf("can not found in completed orders")
}

func (this *IBClient) ibOpenOrders() (ibTrades []*IBTrade, er error) {
	url := this.getURL("/open/trades")
	ibTrades = []*IBTrade{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &ibTrades)
	return
}

// 该接口获取到的成交订单无成交价等数据
func (this *IBClient) ibCompletedOrders() (ibTrades []*IBTrade, er error) {
	url := this.getURL("/completed/orders")
	ibTrades = []*IBTrade{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &ibTrades)
	return
}

// 只有挂单和当天完成的订单
func (this *IBClient) ibAllTrades() (ibTrades []*IBTrade, er error) {
	url := this.getURL("/trades")
	ibTrades = []*IBTrade{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &ibTrades)
	return
}

// 有最多 7 天（根据 TWS Trade Log 的设置）的成交记录
func (this *IBClient) ibGetExecutions() (ibExecutions []*IBExecution, er error) {
	url := this.getURL("/executions")
	ibExecutions = []*IBExecution{}
	er = this.sendHTTPRequest(resty.MethodGet, url, nil, &ibExecutions)
	return
}

func (this *IBClient) queryTradeByCliendOrder(cliendID, orderID int64) (*IBTrade, error) {
	allTrades, err := this.ibAllTrades()
	if err != nil {
		return nil, err
	}
	for _, trade := range allTrades {
		if trade.Order.ClientId == cliendID && trade.Order.OrderId == orderID {
			return trade, nil
		}
	}
	return nil, fmt.Errorf("order not found: %v", orderID)
}

func (this *IBClient) GetOpenOrders(symbol string, orderType exchange.OrderType) (orders []*Order, er error) {
	ibTrades, er := this.ibOpenOrders()
	if er != nil {
		return nil, er
	}
	orders = []*Order{}
	for _, trade := range ibTrades {
		if symbol != "" && symbol != trade.Contract.Symbol {
			continue
		}
		if orderType != exchange.UnknownOrderType && orderType != trade.getOrderType() {
			continue
		}
		if trade.OrderStatus.PermId == 0 { // 可能还在 PendingSubmit 状态，没有 TWS ID
			continue
		}
		if trade.OrderStatus.Status == "Inactive" {
			continue
		}
		orders = append(orders, trade.toOrder())
	}
	return
}

func (this *IBClient) QueryOrdersByID(orderIDs []string) (orders []*Order, er error) {
	orders = []*Order{}
	trades, er := this.ibAllTrades()
	if er != nil {
		return nil, er
	}
	foundIDs := []string{}
	for _, trade := range trades {
		if SliceContains(orderIDs, fmt.Sprintf("%v", trade.Order.PermId)) {
			order := trade.toOrder()
			orders = append(orders, order)
			foundIDs = append(foundIDs, order.OrderID)
		}
	}

	// 没找到的订单在本地查
	for _, orderID := range orderIDs {
		if SliceContains(foundIDs, orderID) {
			continue
		}
		if this.gateway.SyncingOrderExecutions.Load() {
			er = fmt.Errorf("synching orders now, try later")
			return
		}
		for _, order := range this.gateway.storage.Orders {
			if order.OrderID == orderID {
				orders = append(orders, order)
				break
			}
		}
	}
	return
}

func (this *IBClient) Subscribe(instrumentID string) error {
	return exchange.ErrNotImplemented
}

func (this *IBClient) QueryInstrumentMarginRates() error {
	return exchange.ErrNotImplemented
}

func (this *IBClient) getBaseRequest() *resty.Request {
	this.http.SetBaseURL(fmt.Sprintf("http://127.0.0.1:%d", this.gateway.opts.AdapterPort)).
		SetTimeout(10 * time.Second).     // 设置超时时间，超时无反应则返回超时错误
		SetRetryCount(0).                 // 请求失败时的重试次数
		SetRetryWaitTime(5 * time.Second) // 重试前等待时间
	req := this.http.R()
	req.SetHeader("Content-Type", "application/json")
	return req
}

func (this *IBClient) sendHTTPRequest(httpMethod, requestPath string, data, result any) (er error) {
	defer func() {
		if er != nil {
			zlog.Errorf("send http request failed, path: %s, error: %s", requestPath, er)
		}
	}()

	if !this.adapter.Running.Load() {
		er = fmt.Errorf("adapter is not running")
		return
	}

	req := this.getBaseRequest()

	if data != nil {
		payloadData, err := json.Marshal(data)
		if err != nil {
			er = errors.New("unable to marshal JSON request")
			return
		}
		req.SetBody(payloadData)
	}

	resp, err := req.Execute(httpMethod, requestPath)
	if err != nil {
		er = fmt.Errorf("http execute failed, error: %s", err)
		return
	}

	if resp.StatusCode() != 200 {
		zlog.Debugf("error response: %s", resp.String())

		msg := gjson.Parse(resp.String()).Get("message").String()
		er = fmt.Errorf("http status is code: %d, error_message: %s", resp.StatusCode(), msg)
		return
	}

	if result != nil {
		err = json.Unmarshal(resp.Body(), result)
		if err != nil {
			zlog.Errorf("unmarshal result json, resp: %s", resp.String())
			er = fmt.Errorf("unmarshal result json failed, error: %s", err)
		}
	}
	return
}

// 简单防止重复调用重启
var softResetMutex = sync.Mutex{}

func (this *IBClient) RestartOwnedProcess(processType ProcessType) (er error) {
	if processType == Terminal {
		if !softResetMutex.TryLock() {
			er = fmt.Errorf("soft reset terminal is in progress")
			return
		}
		defer softResetMutex.Unlock()
		er = this.SendCommand("RESTART")
		if er == nil {
			time.Sleep(this.GetOwnedProcess(Terminal).RestartWait)
		}
	} else {
		er = this.BaseClient.RestartOwnedProcess(processType)
	}
	return
}

// ref: https://github.com/IbcAlpha/IBC/blob/master/userguide.md
func (this *IBClient) SendCommand(command string) (er error) {
	command = strings.TrimSpace(strings.ToUpper(command))
	validCommands := strings.Split("RESTART/STOP/ENABLEAPI/RECONNECTDATA/RECONNECTACCOUNT/EXIT", "/")
	if !utils.SliceContainsEqualFold(validCommands, command) {
		return fmt.Errorf("invalid command: %s", command)
	}

	host := "127.0.0.1"
	port := this.gateway.opts.IB.CommandServerPort // Assuming this is defined in the IBGateway struct
	timeout := 3 * time.Second

	addr := fmt.Sprintf("%s:%d", host, port)
	conn, err := net.DialTimeout("tcp", addr, timeout)
	if err != nil {
		return fmt.Errorf("send command failed, dail error: %v", err)
	}
	defer conn.Close()

	conn.SetDeadline(time.Now().Add(timeout))

	command += "\n"
	_, err = conn.Write([]byte(command))
	if err != nil {
		return fmt.Errorf("send command failed, write error: %v", err)
	}

	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return fmt.Errorf("send command failed, read error: %v", err)
	}

	response := string(buffer[:n])
	zlog.Infof("send command response: %s", response)
	return
}

// override, 因为 IB 有自己的重启 terminal 的方法
func (this *IBClient) checkHealthLoop() {
	counter := 0     // 用于间隔一段时间打印警告信息
	round := 45      // 每分钟 3 次，45 次约 15 分钟；因为重启需要时间，可能稍微长一点
	alerted := false // 是否已经警告过，因为第一次需要警告时，并不一定满足 counter%round = 0 的条件
	healthCheckTicker := time.NewTicker(20 * time.Second)
	for {
		<-healthCheckTicker.C
		if this.gateway.Launched.Load() && time.Since(*this.gateway.launchTime) > HEALTH_CHECK_COOLDOWN {
			ok := this.checkAdapterHealth()
			counter += 1
			adapter := this.GetOwnedProcess(Adapter)
			if !ok {
				if adapter.AutoRecoverStop {
					zlog.Warnf("adapter auto recover stopped, limit: %d/%d in %s", adapter.AutoRecoverFailsLimiter.Limit, adapter.AutoRecoverFailsLimiter.GetCount(), adapter.AutoRecoverFailsLimiter.Duration)
					if !alerted || counter%round == 0 {
						this.gateway.AlertMsgf("adapter 自动恢复失败次数达到限制: %d/%d in %s，停止自动恢复", adapter.AutoRecoverFailsLimiter.GetCount(), adapter.AutoRecoverFailsLimiter.Limit, adapter.AutoRecoverFailsLimiter.Duration)
						alerted = true
					}
					continue
				}
				_, allow := adapter.AutoRecoverFailsLimiter.Allow()
				if !allow {
					zlog.Warnf("adapter auto recover fails limiter not allow")
					adapter.StopAutoRecover()
					continue
				}

				this.gateway.AlertMsgf("健康检查失败，%s 服务可能出现问题，正在尝试重启", this.platform)
				status := formatOwnedProcessStatus([]*OwnedProcess{this.GetOwnedProcess(Adapter), this.GetOwnedProcess(Terminal), this.GetOwnedProcess(Watchdog)})
				this.gateway.AlertMsgf("进程状态: \n```%s```", status)
				if this.gateway.opts.IB.RestartTerminalOnHealthCheckFailed {
					err := this.RestartOwnedProcess(Terminal) // 主要是因为需要调用这个 override 的方法，无法在 BaseClient 中去实现
					if err != nil {
						this.gateway.AlertMsgf("terminal 重启失败, error: %s", err)
					} else {
						this.gateway.AlertMsgf("terminal 已重启，等待 %s，稍后自动重启 adapter", TWS_RESTART_WAIT_TIME)
					}
				}

				err2 := this.RestartOwnedProcess(Adapter)
				if err2 != nil {
					this.gateway.AlertMsgf("adapter 重启失败，error: %s", err2)
				} else {
					this.gateway.AlertMsgf("adapter 已重启")
				}
			} else {
				zlog.Infof("adapter health check ok")
				adapter.ResetAutoRecover()
				alerted = false
			}
		}
	}
}

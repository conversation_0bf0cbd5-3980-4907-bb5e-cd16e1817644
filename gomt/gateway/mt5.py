import json

import MetaTrader5 as mt5
import tornado.ioloop
import tornado.web
import argparse
import requests


Initialized = False


class BaseHandler(tornado.web.RequestHandler):
    def prepare(self):
        pass

    def on_finish(self):
        pass

    def set_default_headers(self):
        self.set_header("Content-Type", "application/json")

    def json_ok(self, data=None):
        if data is None:
            data = {}
        self.write(json.dumps(data))

    def json_failed(self, msg=""):
        if msg == "":
            msg = getMT5ErrorMsg()
            if not Initialized:
                msg = "[9999] not initialized"

        self.set_status(500)
        self.write({
            "message": msg,
        })


class InitializeHandler(BaseHandler):
    def post(self):
        args = tornado.escape.json_decode(self.request.body)
        ok = mt5.initialize(
            path=args.get("path", ""),
            login=args.get("login"),
            password=args.get("password"),
            server=args.get("server"),
            timeout=args.get("timeout", 10),
            portable=args.get("portable", False),
        )
        if ok:
            terminal_info_dict = mt5.terminal_info()._asdict()
            if terminal_info_dict.get("trade_allowed", False) == False:
                self.json_failed("[9998] trading disabled by client terminal")
                return

            global Initialized
            Initialized = True
            self.json_ok(terminal_info_dict)
        else:
            self.json_failed(getMT5ErrorMsg())


class TerminalInfoHandler(BaseHandler):
    def get(self):
        global Initialized
        if Initialized:
            terminal_info_dict = mt5.terminal_info()._asdict()
            self.json_ok(terminal_info_dict)
        else:
            self.json_failed("[9999] not initialized")


class ShutdownHandler(BaseHandler):
    def get(self):
        mt5.shutdown()
        global Initialized
        Initialized = False
        self.json_ok()


def getMT5ErrorMsg():
    err = mt5.last_error()
    return "[{}] {}".format(err[0], err[1])


class LastErrorHandler(BaseHandler):
    def get(self):
        self.json_ok({"message": getMT5ErrorMsg()})


class AccountInfoHandler(BaseHandler):
    def get(self):
        account_info = mt5.account_info()
        if account_info == None:
            self.json_failed()
            return
        self.json_ok(account_info._asdict())


class SymbolsHandler(BaseHandler):
    def get(self):
        symbols = mt5.symbols_get(group=self.get_argument("group", ""))
        if symbols == None:
            self.json_failed()
            return
        symbolObjs = []
        for s in symbols:
            symbolObjs.append(s._asdict())
        self.json_ok(symbolObjs)


class SymbolInfoTickHandler(BaseHandler):
    def get(self):
        tick = mt5.symbol_info_tick(self.get_argument("symbol"))
        if tick == None:
            self.json_failed()
            return
        self.json_ok(tick._asdict())


class SymbolSelectHandler(BaseHandler):
    def post(self):
        args = tornado.escape.json_decode(self.request.body)
        ok = mt5.symbol_select(args.get("symbol"), args.get("enable", False))
        if not ok:
            self.json_failed()
            return
        self.json_ok()


class OrdersHandler(BaseHandler):
    def get(self):
        kwargs = {}
        symbol = self.get_argument("symbol", "")
        if symbol != "":
            kwargs["symbol"] = symbol
        group = self.get_argument("group", "")
        if group != "":
            kwargs["group"] = group
        ticket = self.get_argument("ticket", "")
        if ticket != "":
            kwargs["ticket"] = int(ticket)
        orders = mt5.orders_get(**kwargs)
        if orders == None:
            self.json_failed()
            return
        orderObjs = []
        for o in orders:
            orderObjs.append(o._asdict())
        self.json_ok(orderObjs)


class HistoryOrdersHandler(BaseHandler):
    def get(self):
        try:
            ticket = self.get_argument("ticket", "")
            position = self.get_argument("position", "")
            if ticket != "":
                orders = mt5.history_orders_get(
                    ticket=int(ticket)
                )
            elif position != "":
                orders = mt5.history_orders_get(
                    position=int(position)
                )
            else:
                kwargs = {}
                group = self.get_argument("group", "")
                if group != "":
                    kwargs["group"] = group
                orders = mt5.history_orders_get(
                    int(self.get_argument("date_from")),
                    int(self.get_argument("date_to")),
                    **kwargs,
                )

            if orders == None:
                self.json_failed()
                return
            orderObjs = []
            for o in orders:
                orderObjs.append(o._asdict())
            self.json_ok(orderObjs)
        except Exception as e:
            self.json_failed("{}".format(e))


class HistoryDealsHandler(BaseHandler):
    def get(self):
        ticket = self.get_argument("ticket", "")
        position = self.get_argument("position", "")
        if ticket != "":
            deals = mt5.history_deals_get(
                ticket=int(ticket)
            )
        elif position != "":
            deals = mt5.history_deals_get(
                position=int(position)
            )
        else:
            kwargs = {}
            group = self.get_argument("group", "")
            if group != "":
                kwargs["group"] = group
            deals = mt5.history_deals_get(
                int(self.get_argument("date_from")),
                int(self.get_argument("date_to")),
                **kwargs,
            )

        if deals == None:
            self.json_failed()
            return
        dealObjs = []
        for d in deals:
            dealObjs.append(d._asdict())
        self.json_ok(dealObjs)


class OrderCalcMarginHandler(BaseHandler):
    def post(self):
        args = tornado.escape.json_decode(self.request.body)
        margin = mt5.order_calc_margin(
            args.get("action"),
            args.get("symbol"),
            args.get("volume"),
            args.get("price"),
        )
        if margin == None:
            self.json_failed()
            return
        self.json_ok({"margin": margin})


class PositionsHandler(BaseHandler):
    def get(self):
        kwargs = {}
        symbol = self.get_argument("symbol", "")
        if symbol != "":
            kwargs["symbol"] = symbol
        group = self.get_argument("group", "")
        if group != "":
            kwargs["group"] = group
        ticket = self.get_argument("ticket", "")
        if ticket != "":
            kwargs["ticket"] = int(ticket)

        positions = mt5.positions_get(**kwargs)
        if positions == None:
            self.json_failed()
            return
        positionObjs = []
        for p in positions:
            positionObjs.append(p._asdict())
        self.json_ok(positionObjs)


class OrderSendHandler(BaseHandler):
    def post(self):
        args = tornado.escape.json_decode(self.request.body)
        request = {
            "action": args.get("action"),
            "magic": args.get("magic", 0),
            "order": args.get("order", 0),
            "symbol": args.get("symbol", ""),
            "volume": args.get("volume"),
            "price": args.get("price", 0),
            "stoplimit": args.get("stoplimit", 0),
            "sl": args.get("sl", 0),
            "tp": args.get("tp", 0),
            "deviation": args.get("deviation", 0),
            "type": args.get("type"),
            "type_filling": args.get("type_filling"),
            "type_time": args.get("type_time"),
            "expiration": args.get("expiration", 0),
            "comment": args.get("comment", ""),
            "position": args.get("position", 0),
            "position_by": args.get("position_by", 0),
        }

        # 保证以下字段是 float 类型
        floatParams = ["volume", "price", "stoplimit", "sl", "tp"]
        for param in floatParams:
            if type(request[param]) == int:
                request[param] = float(request[param])

        if request["magic"] == 0:
            del request["magic"]
        if request["order"] == 0:
            del request["order"]
        if request["symbol"] == "":
            del request["symbol"]
        if request["price"] == 0:
            del request["price"]
        if request["stoplimit"] == 0:
            del request["stoplimit"]
        if request["sl"] == 0:
            del request["sl"]
        if request["tp"] == 0:
            del request["tp"]
        if request["deviation"] == 0:
            del request["deviation"]
        if request["expiration"] == 0:
            del request["expiration"]
        if request["position"] == 0:
            del request["position"]
        if request["position_by"] == 0:
            del request["position_by"]

        if request["action"] == mt5.TRADE_ACTION_DEAL:
            # 市价单，需用 tick 报价
            tick = mt5.symbol_info_tick(request["symbol"])
            if tick is None:
                self.json_failed()
                return
            if request["type"] == mt5.ORDER_TYPE_BUY:
                request["price"] = tick.ask
            else:
                request["price"] = tick.bid
        elif request["action"] == mt5.TRADE_ACTION_REMOVE:
            del request["volume"]

        print("order send request", request)
        result = mt5.order_send(request)
        if result is None:
            self.json_failed()
            return
        self.json_ok(result._asdict())


class KlinesHandler(BaseHandler):
    def get(self):
        klines = mt5.copy_rates_range(
            self.get_argument("symbol"),
            int(self.get_argument("timeframe")),
            int(self.get_argument("date_from")),
            int(self.get_argument("date_to")),
        )
        if klines is None:
            self.json_failed()
            return
        klineObjs = []
        for k in klines:
            klineObjs.append({
                "time": int(k[0]),
                "open": k[1],
                "high": k[2],
                "low": k[3],
                "close": k[4],
                "tick_volume": int(k[5]),
                "spread": int(k[6]),
                "real_volume": int(k[7]),
            })
        self.json_ok(klineObjs)


def make_app():
    return tornado.web.Application([
        (r"/mt5/initialize", InitializeHandler),
        (r"/mt5/terminal_info", TerminalInfoHandler),
        (r"/mt5/shutdown", ShutdownHandler),
        (r"/mt5/last_error", LastErrorHandler),
        (r"/mt5/account_info", AccountInfoHandler),
        (r"/mt5/symbols", SymbolsHandler),
        (r"/mt5/symbol_info_tick", SymbolInfoTickHandler),
        (r"/mt5/symbol_select", SymbolSelectHandler),
        (r"/mt5/orders", OrdersHandler),
        (r"/mt5/history_orders", HistoryOrdersHandler),
        (r"/mt5/history_deals", HistoryDealsHandler),
        (r"/mt5/order_calc_margin", OrderCalcMarginHandler),
        (r"/mt5/positions", PositionsHandler),
        (r"/mt5/order_send", OrderSendHandler),
        (r"/mt5/klines", KlinesHandler),
    ])


def serve(port):
    tornado.ioloop.PeriodicCallback(orderCheck, callback_time=1000).start()

    app = make_app()
    app.listen(port)
    tornado.ioloop.IOLoop.current().start()


callback_url = ""


def callback(value: any):
    try:
        global callback_url
        resp = requests.post(callback_url, json=value)
    except Exception as e:
        print(f"request call back exception: {e}")
        return

    if resp.status_code != 200:
        print(
            f"callback request failed, error: http code: {resp.status_code}")
    else:
        print(f"callback success")


def compareOrders(oldOrders, newOrders):
    news = []
    updates = []
    deletes = []

    for new in newOrders:
        found = False
        for old in oldOrders:
            if new.ticket == old.ticket:
                if new.volume_initial != old.volume_initial or new.volume_current != old.volume_current:
                    updates.append(new)
                found = True
                break
        if not found:
            news.append(new)

    for old in oldOrders:
        found = False
        for new in newOrders:
            if new.ticket == old.ticket:
                found = True
                break
        if not found:
            deletes.append(old)

    return news, updates, deletes


lastOrders = []


def orderCheck():
    orders = mt5.orders_get()
    if orders == None:
        return
    
    global lastOrders

    firstCheck = False
    if len(lastOrders) == 0:
        firstCheck = True
    
    news, updates, deletes = compareOrders(lastOrders, orders)
    lastOrders = orders

    orders = []
    for o in news:
        orders.append(o._asdict())
    for o in updates:
        orders.append(o._asdict())
    for o in deletes:
        hisOrders = mt5.history_orders_get(ticket=o.ticket)
        if hisOrders != None:
            for h in hisOrders:
                orders.append(h._asdict())

    if (not firstCheck) and len(orders) > 0:
        print(f"send callback: order.update, news: {len(news)}, updates: {len(updates)}, deletes: {len(deletes)}, total: {len(orders)}")
        callback({"channel": "order", "type": "update", "data": orders})


if __name__ == "__main__":
    # Instantiate the parser
    parser = argparse.ArgumentParser(description='MetaTrader5 adapter script')
    parser.add_argument('port', type=int, help='adapter http port number')
    parser.add_argument('callback_url', type=str, help='adapter callback url')
    args = parser.parse_args()

    callback_url = args.callback_url

    print("mt5 service started at localhost:{}".format(args.port))
    serve(args.port)

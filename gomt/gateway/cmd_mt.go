package gateway

import (
	"strings"

	"github.com/wizhodl/quanter/common/command"
)

type MT5TerminalInfoGatewayCommand GatewayCommand

func NewMT5TerminalInfoGatewayCommand(controller *GatewayServer) *MT5TerminalInfoGatewayCommand {
	cmd := &MT5TerminalInfoGatewayCommand{
		Command: command.Command{
			Name:            "mtTerminalInfo",
			Alias:           []string{"mti"},
			Instruction:     "`.mtTerminalInfo` 打印 MT 终端信息",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *MT5TerminalInfoGatewayCommand) Do() bool {
	terminalInfo, err := this.controller.GetMTClient().mtGetTerminalInfo()
	if err != nil {
		this.ErrorMsgf("获取 MT 终端信息出错，error: %s", err)
		return false
	}
	this.SendMsgf("终端信息\n```%s```", toTable(terminalInfo, false))
	return true
}

type MT5AccountInfoGatewayCommand GatewayCommand

func NewMT5AccountInfoGatewayCommand(controller *GatewayServer) *MT5AccountInfoGatewayCommand {
	cmd := &MT5AccountInfoGatewayCommand{
		Command: command.Command{
			Name:            "mtAccount",
			Alias:           []string{"ma"},
			Instruction:     "`.mtAccount` 打印 MT 账户信息",
			RequiresConfirm: false,
			ArgMin:          0,
			ArgMax:          0,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *MT5AccountInfoGatewayCommand) Do() bool {
	accountInfo, err := this.controller.GetMTClient().mtGetAccountInfo()
	if err != nil {
		this.ErrorMsgf("获取 MT 账户信息出错，error: %s", err)
		return false
	}
	this.SendMsgf("账户信息\n```%s```", toTable(accountInfo, false))
	return true
}

type MT5PositionsGatewayCommand GatewayCommand

func NewMT5PositionsGatewayCommand(controller *GatewayServer) *MT5PositionsGatewayCommand {
	cmd := &MT5PositionsGatewayCommand{
		Command: command.Command{
			Name:            "mtPositions",
			Alias:           []string{"mp"},
			Instruction:     "`.mtPositions instrumentID` 打印 MT 持仓",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *MT5PositionsGatewayCommand) Do() bool {
	instrumentID := strings.ToUpper(this.Args[0])
	positions, err := this.controller.GetMTClient().mtGetPositions(instrumentID)
	if err != nil {
		this.ErrorMsgf("获取 MT 持仓出错，error: %s", err)
		return false
	}
	for _, position := range positions {
		this.SendMsgf("持仓 [%s]\n```%s```", instrumentID, toTable(position, false))
	}
	return true
}

type MT5InstrumentsGatewayCommand GatewayCommand

func NewMT5InstrumentsGatewayCommand(controller *GatewayServer) *MT5InstrumentsGatewayCommand {
	cmd := &MT5InstrumentsGatewayCommand{
		Command: command.Command{
			Name:            "mtInstruments",
			Alias:           []string{"mi"},
			Instruction:     "`.mtInstruments InstrumentIDs` 查看 MT 品种",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *MT5InstrumentsGatewayCommand) Do() bool {
	count := 0
	symbols, err := this.controller.GetMTClient().mtGetSymbols(strings.ToUpper(this.Args[0]))
	if err != nil {
		this.ErrorMsgf("获取品种信息出错，error: %s", err)
		return false
	}
	for _, symbol := range symbols {
		count += 1
		this.SendMsgf("品种 [%s]\n```%s```", symbol.Name, toTable(symbol, false))
	}
	if count == 0 {
		this.SendMsgf("没有找到品种 %s 的数据。", this.Args[0])
		return true
	}
	return true
}

type MT5OpenOrdersGatewayCommand GatewayCommand

func NewMT5OpenOrdersGatewayCommand(controller *GatewayServer) *MT5OpenOrdersGatewayCommand {
	cmd := &MT5OpenOrdersGatewayCommand{
		Command: command.Command{
			Name:            "mtOpenOrders",
			Alias:           []string{"moo"},
			Instruction:     "`.mtOpenOrders InstrumentID` 查看 MT 挂单",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *MT5OpenOrdersGatewayCommand) Do() bool {
	instrumentID := strings.ToUpper(this.Args[0])
	count := 0
	orders, err := this.controller.GetMTClient().mtGetOpenOrders(instrumentID)
	if err != nil {
		this.ErrorMsgf("获取挂单出错，error: %s", err)
		return false
	}
	for _, order := range orders {
		if order.Symbol == instrumentID {
			count += 1
			this.SendMsgf("挂单 [%d]\n```%s```", order.Ticket, toTable(order, false))
		}
	}
	if count == 0 {
		this.SendMsgf("没有找到品种 %s 的挂单。", instrumentID)
	}
	return true
}

type MT5HistoryOrdersGatewayCommand GatewayCommand

func NewMT5HistoryOrdersGatewayCommand(controller *GatewayServer) *MT5HistoryOrdersGatewayCommand {
	cmd := &MT5HistoryOrdersGatewayCommand{
		Command: command.Command{
			Name:            "mtHistoryOrders",
			Alias:           []string{"mho"},
			Instruction:     "`.mtHistoryOrders OrderIDs` 查询 MT 历史订单",
			RequiresConfirm: false,
			ArgMin:          1,
			ArgMax:          1,
			AuthcodePos:     -1,
		},
		controller: controller,
	}
	return cmd
}

func (this *MT5HistoryOrdersGatewayCommand) Do() bool {
	orderIDs := strings.Split(this.Args[0], ",")
	count := 0
	for _, orderID := range orderIDs {
		orders, err := this.controller.GetMTClient().mtGetHistoryOrders(orderID)
		if err != nil {
			this.ErrorMsgf("查找历史订单 [%s] 出错，error: %s", orderID, err)
			return false
		}
		if len(orders) != 1 {
			this.ErrorMsgf("没找到历史订单 %s", orderID)
			continue
		}

		order := orders[0]
		count += 1
		this.SendMsgf("历史订单 [%s]\n```%s```", orderID, toTable(order, false))
	}

	if count == 0 {
		this.SendMsgf("没有找到 %s 的历史订单。", orderIDs)
	}
	return true
}

package gateway

import (
	"fmt"
	"strings"
	"time"
)

type SessionType string

const SessionTypeQuote SessionType = "Quote"
const SessionTypeTrade SessionType = "Trade"

// 某个工作日的一段交易时间，最终用这里的时间检查是否可以交易
type Session struct {
	WeekDay     time.Weekday
	SessionType SessionType
	StartTime   int64 // second of the day
	EndTime     int64 // second of the day
}

func NewSession(sessionStr string, weekDay time.Weekday, sessionType SessionType) (s *Session, er error) {
	// sessionStr 的格式为：HH:MM-HH:MM
	parts := strings.Split(sessionStr, "-")
	startStr := strings.TrimSpace(parts[0])
	endStr := strings.TrimSpace(parts[1])
	// ParseTimeSeond 认为 xx:yy 是 MM:SS，统一加上 :00 让它变为 HH:MM:SS
	startTime, err := ParseTimeSecond(fmt.Sprintf("%s:00", startStr))
	if err != nil {
		er = err
		return
	}
	endTime, err2 := ParseTimeSecond(fmt.Sprintf("%s:00", endStr))
	if err2 != nil {
		er = err2
		return
	}
	s = &Session{
		WeekDay:     weekDay,
		SessionType: sessionType,
		StartTime:   int64(startTime),
		EndTime:     int64(endTime),
	}
	return
}

// 一组交易时段，一般是用来保存同一个品种的所有交易时段
type Sessions []*Session

// 检查当前时间是否在交易中
func (this *Sessions) IsTrading() bool {
	sessionType := SessionTypeTrade
	nowTime := ExchangeTimeNow()
	// 不能用 nowTime.Unix() 直接获取秒数，因为这个时间是 UTC 时间，而不是交易所所在时区的时间
	secondInDay := int64(nowTime.Hour()*3600 + nowTime.Minute()*60 + nowTime.Second())
	for _, session := range *this {
		if session.SessionType == sessionType && session.WeekDay == nowTime.Weekday() {
			if secondInDay > session.StartTime && secondInDay < session.EndTime {
				return true
			}
		}
	}
	return false
}

// 用于配置每周的所有交易时段，也是某个品种每周的所有交易时段配置
type MarketSession struct {
	Sunday    string `yaml:"Sunday"`
	Monday    string `yaml:"Monday"`
	Tuesday   string `yaml:"Tuesday"`
	Wednesday string `yaml:"Wednesday"`
	Thursday  string `yaml:"Thursday"`
	Friday    string `yaml:"Friday"`
	Saturday  string `yaml:"Saturday"`
}

// 检查数据并且返回所有交易时段的列表
func (this *MarketSession) ValidateSessions() (sessions Sessions, er error) {
	sessions = Sessions{}
	sessionFields := []string{this.Sunday, this.Monday, this.Tuesday, this.Wednesday, this.Thursday, this.Friday, this.Saturday}
	for i, session := range sessionFields {
		if session == "" {
			continue
		}
		// session 的格式为：HH:MM-HH:MM, HH:MM-HH:MM | HH:MM-HH:MM, HH:MM-HH:MM
		parts := strings.Split(session, "|")
		if len(parts) != 2 {
			er = fmt.Errorf("invalid quote | trade format (%s)", session)
			return
		}
		quoteStr := strings.TrimSpace(parts[0])
		tradeStr := strings.TrimSpace(parts[1])
		quoteParts := strings.Split(quoteStr, ",")
		tradeParts := strings.Split(tradeStr, ",")
		for _, quoteStr := range quoteParts {
			// quoteStr 的格式是：HH:MM-HH:MM
			quoteSession, err := NewSession(quoteStr, time.Weekday(i), SessionTypeQuote)
			if err != nil {
				er = err
				return
			}
			sessions = append(sessions, quoteSession)
		}
		for _, tradeStr := range tradeParts {
			// tradeStr 的格式是：HH:MM-HH:MM
			tradeSession, err2 := NewSession(tradeStr, time.Weekday(i), SessionTypeTrade)
			if err2 != nil {
				er = err2
				return
			}
			sessions = append(sessions, tradeSession)
		}
	}
	return
}

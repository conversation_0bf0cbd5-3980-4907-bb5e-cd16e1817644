package gateway

import (
	"fmt"
	"time"

	"github.com/wizhodl/quanter/exchange"
)

type Adaptable interface {
	// 控制相关
	Initialize() error
	RestartService() (er error)
	IsConnected() bool
	GetOwnedProcess(processType ProcessType) *OwnedProcess
	StartOwnedProcess(processType ProcessType) (er error)
	StopOwnedProcess(processType ProcessType) (er error)
	RestartOwnedProcess(processType ProcessType) (er error)
	GetPlatform() Platform
	GetUserID() string
	GetAdapterScriptArgs() []string

	// 业务相关
	GetTradingSymbols() []string
	GetOpenOrders(instrumentID string, orderType exchange.OrderType) (orders []*Order, er error)
	QueryOrdersByID(orderIDs []string) (orders []*Order, er error)
	GetLastPrice(instrumentID string) (price float64, updateTime time.Time, er error)
	GetAccountBalances() (balance []*exchange.AccountBalance, er error)
	GetUserMargin() (margin *exchange.UserMargin, er error)
	GetPositions(instrumentID string) (positions []*exchange.Position, er error)
	CreateOrder(args *exchange.CreateOrderArgs) (order *Order, er error)
	CancelOrder(orderID string) (order *Order, er error)
	GetKlines(symbol string, period string, dateFrom, dateTo int64) (klines exchange.KLines, er error)
	GetInstruments(symbols string) (instruments []*exchange.Instrument, er error)
	GetTerminalInfo() (terminalInfo *TerminalInfo, er error)
	QueryInstrumentMarginRates() error
	Subscribe(instrumentID string) error

	// 处理 adapter 回调数据
	HandleAdapterCallback(jsonData string) error
}

type TerminalInfo struct {
	Connected       bool   `json:"connected"`
	Name            string `json:"name"`
	PingLast        int    `json:"ping_last"`
	TradeAllowed    bool   `json:"trade_allowed"`
	AutoRestartTime string `json:"auto_restart_time"`
}

func (this *TerminalInfo) GetPingDisplay() string {
	return fmt.Sprintf("%d ms", int(this.PingLast/1000))
}

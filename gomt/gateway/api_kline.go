package gateway

import (
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

func (this *GatewayServer) getKlinesHandler(ctx *gin.Context) {
	var form LookupKLineForm
	if err := ctx.ShouldBindWith(&form, binding.Query); err != nil {
		badRequest(ctx, "invalid args, error: %s", err)
		return
	}

	if form.Period != "1day" {
		badRequest(ctx, "invalid args, period not supported")
		return
	}

	fromTs := form.Time - 24*3600*form.Limit
	klines, err := this.client.GetKlines(form.InstrumentID, "1D", fromTs, form.Time)
	if err != nil {
		failJSON(ctx, "get klines failed, error: %s", err)
		return
	}
	okJSON(ctx, klines)
}

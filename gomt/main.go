package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"
	_ "time/tzdata"

	"github.com/kardianos/service"
	"github.com/mitchellh/go-homedir"
	"github.com/wizhodl/gomt/gateway"
	"github.com/wizhodl/quanter/common/stack"
	"github.com/wizhodl/quanter/common/zlog"
	"github.com/wizhodl/quanter/secrets"
)

var cfgName string
var platform string
var flagVersion bool
var flagService bool
var flagInstall bool
var flagUninstall bool

var commitHash string
var buildTime string

type Program struct {
	platform gateway.Platform
	cfgPath  string
	gateway  *gateway.GatewayServer
	Service  service.Service
}

func NewProgram(platform string, cfgPath string) (program *Program, er error) {
	if !gateway.SliceContainsEqualFold([]string{"MT5", "IB"}, platform) {
		er = fmt.Errorf("platform must be MT5 or IB")
		return
	}

	program = &Program{
		platform: gateway.Platform(strings.ToUpper(platform)),
	}

	var err error
	program.cfgPath, err = program.expandPath(cfgPath)
	if err != nil {
		er = fmt.Errorf("expand config path failed, error: %s", err)
		return
	}

	// 没有加载成功 config 文件前，zlog 无法打印到文件，可以临时打开这个文件打印到文件
	logPath, err := program.logPathAuto()
	if err == nil {
		zlog.SetLogger(zlog.NewLogger("DEUBG", logPath))
	} else {
		zlog.SetLogger(zlog.NewLogger("DEUBG", ""))
	}

	args := []string{"-service", "--platform", platform}
	if cfgPath != "" {
		args = append(args, "--cfg", cfgPath)
	}
	svcConfig := &service.Config{
		Name:        fmt.Sprintf("go%s", program.platform.ToLower()),
		DisplayName: fmt.Sprintf("go%s", program.platform.ToLower()),
		Description: fmt.Sprintf("Go %s gateway", platform),
		Arguments:   args,
	}
	program.Service, er = service.New(program, svcConfig)
	if er != nil {
		er = fmt.Errorf("create new service failed, error: %s", er)
		return
	}

	return
}

func (p *Program) Start(s service.Service) error {
	go p.run()
	return nil
}

func (p *Program) run() {
	p.runGateway(p.platform, func(gateway *gateway.GatewayServer) {
		p.gateway = gateway
	})
}

func (p *Program) Stop(s service.Service) error {
	if p.gateway != nil {
		p.gateway.Exit()
	}
	return nil
}

func (p *Program) loadGatewayOptions() (opts *gateway.Option, pathFound string, er error) {
	opts = gateway.NewOption()
	cfgRoot := ""
	cfgEnv := os.Getenv(fmt.Sprintf("GO%s_CFG", p.platform.ToUpper()))
	cfgSameDir, err := p.configPathInSameDir()
	if err != nil {
		zlog.Debugf("search config in the same dir failed, error: %s", err)
	}
	paths := []string{p.cfgPath, cfgEnv, cfgSameDir}
	zlog.Debugf("try to load config from: %s", strings.Join(paths, ",  "))
	for _, p := range paths {
		if _, err := os.Stat(p); err != nil {
			zlog.Debugf("config not found at: %s, error: %s, is not exist: %v", p, err, os.IsNotExist(err))
			continue
		}
		pathFound = p
		zlog.Debugf("config file found: %s", p)
		break
	}
	if pathFound != "" {
		err = opts.Load(pathFound)
		if err != nil {
			er = err
			return
		}
	} else {
		er = fmt.Errorf("config not found at:  flag:(%s), env:(%s), same dir:(%s), root:(%s)", cfgName, cfgEnv, cfgSameDir, cfgRoot)
	}

	return
}

func currDir() string {
	exePath, _ := os.Executable()
	exeDir, _ := filepath.Abs(filepath.Dir(exePath))
	return exeDir
}

func (p *Program) runGateway(platform gateway.Platform, successCallback func(gateway *gateway.GatewayServer)) {
	opts, pathFound, err := p.loadGatewayOptions()
	if err != nil {
		zlog.Panicf("config not found or invalid, error: %s", err)
		return
	}
	zlog.Debugf("loading config from: %s", pathFound)
	logLevel := os.Getenv("GOMT_LOG_LEVEL")
	if logLevel == "" && opts.Debug {
		logLevel = "DEBUG"
	}
	logRoation := &zlog.LogRotation{
		MaxSize:    opts.LogMaxSize,
		MaxBackups: opts.LogMaxBackups,
		MaxAge:     opts.LogMaxAge,
	}
	stack.StartStorage(opts.ID, currDir(), 15)
	zlog.SetLogger(zlog.NewRotateLogger(logLevel, opts.LogPath, logRoation))

	if opts.AutoCloseDays > 28 {
		opts.AutoCloseDays = 28
		zlog.Warnf("can not set AutoCloseDays greater than 28")
	} else if opts.AutoCloseDays < 3 {
		opts.AutoCloseDays = 3
		zlog.Warnf("can not set AutoCloseDays smaller than 3")
	}

	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)
	if srv, err := gateway.NewGatewayServer(platform, opts, commitHash, buildTime); err != nil {
		zlog.Errorf("start gateway server error: %s", err)
	} else {
		if !srv.Debug && !secrets.CheckEncryptSalt() {
			zlog.Warnf("encrypt salt is empty while not debugging")
		}
		if successCallback != nil {
			successCallback(srv)
		}
		srv.Serve()
		<-signalChan
		srv.Exit()
	}
}

func (p *Program) expandPath(path string) (newPath string, er error) {
	if path == "" {
		return "", nil
	}

	if strings.HasPrefix(path, "~") {
		newPath, er = homedir.Expand(path)
		return
	}
	if !filepath.IsAbs(path) {
		newPath, er = filepath.Abs(path)
		return
	}
	newPath = path
	return
}

func (p *Program) configPathInSameDir() (cfgPath string, er error) {
	var s string
	var err error
	s, err = os.Executable()
	if err != nil {
		return "", fmt.Errorf("get current executable failed, error: %s", err)
	}
	dir := filepath.Dir(s)
	cfgPath = filepath.Join(dir, fmt.Sprintf("go%s.yaml", p.platform.ToLower()))
	return cfgPath, nil
}

func (p *Program) logPathAuto() (logPath string, er error) {
	var s string
	var err error
	s, err = os.Executable()
	if err != nil {
		return "", fmt.Errorf("get current executable failed, error: %s", err)
	}
	dir := filepath.Dir(s)
	logDir := filepath.Join(dir, "logs")
	os.MkdirAll(logDir, 0755)
	logPath = filepath.Join(logDir, fmt.Sprintf("go%s.log", p.platform.ToLower()))
	return logPath, nil
}

func uninstallService(name string) {
	svcConfig := &service.Config{
		Name: name,
	}
	svc, err := service.New(nil, svcConfig)
	if err != nil {
		zlog.Errorf("create new service failed, error: %s", err)
		return
	}
	err = svc.Uninstall()
	if err != nil {
		zlog.Errorf("uninstall service failed, error: %s", err)
		return
	}
	zlog.Infof("uninstall service success")
}

func main() {
	flag.StringVar(&cfgName, "cfg", "", "配置文件路径")
	flag.BoolVar(&flagVersion, "version", false, "打印版本号")
	flag.BoolVar(&flagService, "service", false, "以服务方式运行")
	flag.BoolVar(&flagInstall, "install", false, "安装服务")
	flag.BoolVar(&flagUninstall, "uninstall", false, "卸载服务")
	flag.StringVar(&platform, "platform", "", "交易平台，-install 和 -service 要指定")
	flag.Parse()

	if flagVersion {
		fmt.Printf("Build: %s/(%s)\n", commitHash, buildTime)
		os.Exit(0)
	}

	program, err := NewProgram(platform, cfgName)
	if err != nil {
		zlog.Panicf("create new program failed, error: %s", err)
		return
	}

	if flagService {
		err = program.Service.Run()
		if err != nil {
			zlog.Errorf("run service error: %s", err)
		} else {
			zlog.Infof("run service success")
		}
	} else if flagInstall {
		zlog.Infof("installing service")
		isAdmin := gateway.SurveyYes("Are you running this program as administrator？")
		if isAdmin {
			err := program.Service.Install()
			if err != nil {
				zlog.Errorf("install service failed, error: %s", err)
			} else {
				zlog.Infof("install service success")
			}
		} else {
			zlog.Errorf("Please run this program as administrator")
		}
	} else if flagUninstall {
		zlog.Infof("uninstalling service")
		isAdmin := gateway.SurveyYes("Are you running this program as administrator？")
		if isAdmin {
			err := program.Service.Uninstall()
			if err != nil {
				zlog.Errorf("uninstall service failed, error: %s", err)
			} else {
				zlog.Infof("uninstall service success")
			}
		} else {
			zlog.Errorf("Please run this program as administrator")
		}
	} else {
		program.run()
	}
}
